# レンタル商品テスト手順

**最終更新日**: 2025/05/17

## 目次

1. [概要](#概要)
2. [テスト環境の準備](#テスト環境の準備)
3. [テストシナリオ](#テストシナリオ)
4. [テスト実行手順](#テスト実行手順)
5. [テスト結果の確認](#テスト結果の確認)
6. [トラブルシューティング](#トラブルシューティング)

## 概要

本ドキュメントでは、レンタル商品の予約システムをテストするための手順を説明します。様々なレンタル状況（仮予約、確定予約、休日を挟んだレンタルなど）を網羅的にテストし、システムが正しく機能することを確認します。

## テスト環境の準備

### テスト用商品の準備

テスト用商品は以下のスクリプトを使用して作成できます：

```bash
# 総合テスト用商品の作成
node scripts/comprehensive-test-products.js

# 正しい在庫管理のテスト用商品の作成
node scripts/correct-inventory-test.js
```

これらのスクリプトは、以下のような様々なレンタル状況をカバーするテスト用商品を作成します：

1. 仮予約状態の商品
2. 確定予約状態の商品
3. 休日を挟んだレンタル日程
4. 長期レンタル（8日以上）
5. 短期レンタル（1-2日）
6. 複数の予約が連続している商品
7. 予約と予約の間に空きがある商品
8. メンテナンス中の商品
9. 破損状態の商品
10. 利用不可状態の商品
11. 複数台ある同一商品で異なる予約状況

### テスト環境の確認

テストを開始する前に、以下の環境が正しく設定されていることを確認してください：

1. **Shopify環境**：
   - テストストア：peaces-test-block.myshopify.com
   - 管理者アクセス権限があること

2. **アプリ環境**：
   - アプリURL：https://app.shopify-app-test.xyz
   - 開発サーバーが起動していること

3. **データベース環境**：
   - Neonデータベースに接続できること
   - Prismaクライアントが生成されていること

## テストシナリオ

以下のテストシナリオを実行して、レンタル予約システムの機能を検証します：

### 1. 基本予約フロー

#### 1.1 商品ページでの日付選択

- **目的**：カレンダーUIが正しく表示され、日付選択が機能することを確認
- **手順**：
  1. テスト商品ページにアクセス
  2. カレンダーから日付を選択
  3. 「カートに追加」ボタンをクリック
- **期待結果**：
  - カレンダーが正しく表示される
  - 利用可能な日付と予約済みの日付が区別されて表示される
  - 選択した日付に基づいて料金が正しく計算される
  - 商品がカートに追加される

#### 1.2 カート処理と決済

- **目的**：カートから決済までのフローが正しく機能することを確認
- **手順**：
  1. カートページで商品と日程を確認
  2. 「レジに進む」ボタンをクリック
  3. 顧客情報を入力
  4. 支払い方法を選択
  5. 注文を確定
- **期待結果**：
  - カートページに商品と日程が正しく表示される
  - 料金が正しく計算される
  - 注文が正常に処理される
  - 予約情報がデータベースに保存される

### 2. 特殊ケーステスト

#### 2.1 仮予約と確定予約

- **目的**：仮予約と確定予約の処理が正しく機能することを確認
- **手順**：
  1. 仮予約として商品を予約
  2. 管理画面で仮予約を確認
  3. 仮予約を確定予約に変更
- **期待結果**：
  - 仮予約が正しく作成される
  - 仮予約が確定予約に正しく変更される
  - カレンダーに予約状態が正しく反映される

#### 2.2 休日を挟んだレンタル

- **目的**：休日を挟んだレンタル期間の処理が正しく機能することを確認
- **手順**：
  1. 休日を挟む期間を選択
  2. 料金計算を確認
  3. 予約を完了
- **期待結果**：
  - 休日が料金計算から除外される
  - 予約期間に休日が含まれていることが表示される
  - 予約が正常に処理される

#### 2.3 長期レンタル

- **目的**：8日以上の長期レンタルの料金計算が正しく機能することを確認
- **手順**：
  1. 8日以上の期間を選択
  2. 料金計算を確認
  3. 予約を完了
- **期待結果**：
  - 8日目以降の料金が基本料金の10%として計算される
  - 総額が正しく表示される
  - 予約が正常に処理される

#### 2.4 複数予約の連続

- **目的**：連続した予約の処理が正しく機能することを確認
- **手順**：
  1. 既に予約がある商品を選択
  2. 既存の予約の直後の期間を選択
  3. 予約を完了
- **期待結果**：
  - 既存の予約期間がブロックされている
  - 新しい予約が既存の予約と連続して作成される
  - カレンダーに両方の予約が表示される

#### 2.5 メンテナンス状態の商品

- **目的**：メンテナンス中の商品の予約処理が正しく機能することを確認
- **手順**：
  1. メンテナンス状態の商品を選択
  2. 予約を試みる
- **期待結果**：
  - メンテナンス中であることが表示される
  - 予約ができないようにブロックされている

## テスト実行手順

### 1. テスト用商品の作成

```bash
# 総合テスト用商品の作成
node scripts/comprehensive-test-products.js

# 正しい在庫管理のテスト用商品の作成
node scripts/correct-inventory-test.js
```

### 2. テスト用商品の確認

```bash
# テスト用商品の確認
node scripts/check-comprehensive-test.js
node scripts/check-correct-inventory.js
```

### 3. 基本予約フローのテスト

1. ブラウザでテストストアにアクセス
2. テスト用商品ページに移動
3. 日付を選択してカートに追加
4. カート処理と決済フローを実行

### 4. 特殊ケーステスト

各特殊ケースについて、上記のテストシナリオに従ってテストを実行します。

### 5. 自動テストの実行

```bash
# 自動テストの実行
node scripts/test-booking-flow.js
```

## テスト結果の確認

### 1. データベースでの確認

```bash
# Prisma Studioの起動
npx prisma studio
```

Prisma Studioで以下のテーブルを確認します：

- `Booking`：予約情報
- `Product`：商品情報
- `InventoryItem`：在庫アイテム情報

### 2. Shopify管理画面での確認

1. Shopify管理画面にログイン
2. 「商品」セクションでテスト用商品を確認
3. 「注文」セクションでテスト注文を確認
4. メタフィールドが正しく設定されていることを確認

### 3. アプリ管理画面での確認

1. アプリ管理画面にアクセス
2. 「予約管理」セクションで予約情報を確認
3. 「カレンダー」セクションで予約状況を確認

## トラブルシューティング

### よくある問題と解決策

1. **カレンダー表示の問題**：
   - メタフィールドが正しく設定されているか確認
   - ブラウザのコンソールでエラーを確認
   - アプリのログを確認

2. **予約処理のエラー**：
   - データベース接続を確認
   - APIレスポンスを確認
   - Webhookが正しく設定されているか確認

3. **料金計算の問題**：
   - 料金設定メタフィールドを確認
   - 計算ロジックを確認
   - 休日設定を確認

4. **在庫状態の問題**：
   - 在庫アイテム情報メタフィールドを確認
   - 在庫数設定を確認
   - 予約情報メタフィールドを確認

### ログの確認

問題が発生した場合は、以下のログを確認してください：

1. **アプリログ**：
   ```bash
   # アプリログの確認
   tail -f logs/app.log
   ```

2. **Shopify Webhookログ**：
   ```bash
   # Webhookログの確認
   tail -f logs/webhook.log
   ```

3. **ブラウザコンソールログ**：
   - ブラウザの開発者ツールを開く
   - コンソールタブでエラーを確認

### サポート情報

テスト中に問題が解決しない場合は、以下の方法でサポートを受けることができます：

1. **開発者サポート**：
   - 開発者チームに問い合わせ
   - GitHub Issuesで問題を報告

2. **Shopifyサポート**：
   - Shopifyヘルプセンターで情報を検索
   - Shopifyサポートに問い合わせ
