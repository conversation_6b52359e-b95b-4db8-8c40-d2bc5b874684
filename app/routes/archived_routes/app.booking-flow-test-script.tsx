/**
 * 予約フローテストスクリプトページ
 *
 * フロントエンドから予約フローをテストするためのスクリプトを提供します。
 * このページでは、予約の作成、確認、キャンセルなどの一連の流れをテストできます。
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  BlockStack,
  Text,
  Button,
  Banner,
  InlineStack,
  Box,
  Divider,
  Frame,
  Modal,
  ButtonGroup,
  Tabs,
  Link,
  TextField,
  Select,
  Spinner,
  Icon
} from "@shopify/polaris";
// アイコンのインポートを削除
import { useState, useCallback, useEffect, useRef } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";

/**
 * ローダー関数
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // 商品データを取得
    const products = await prisma.product.findMany({
      where: { shop },
      select: {
        id: true,
        title: true,
        sku: true,
        status: true,
        price: true,
        shopifyId: true
      },
      orderBy: { title: 'asc' },
    });

    // Shopifyから商品情報を取得
    const shopifyProductsResponse = await admin.graphql(`
      query getProducts {
        products(first: 10) {
          edges {
            node {
              id
              title
              handle
              status
              variants(first: 1) {
                edges {
                  node {
                    id
                    title
                    sku
                    price
                  }
                }
              }
            }
          }
        }
      }
    `);

    const shopifyProductsData = await shopifyProductsResponse.json();
    const shopifyProducts = shopifyProductsData.data?.products?.edges?.map(edge => ({
      id: edge.node.id,
      title: edge.node.title,
      handle: edge.node.handle,
      status: edge.node.status,
      variant: edge.node.variants.edges[0]?.node || null
    })) || [];

    return json({
      products,
      shopifyProducts,
      shop,
      apiEndpoints: {
        createBooking: "/api.bookings.create",
        cancelBooking: "/api.bookings.cancel",
        provisionalToConfirmed: "/api.bookings.provisional-to-confirmed",
        availability: "/api.availability",
        addToCart: "/api.add-to-cart"
      }
    });
  } catch (error) {
    console.error("データ取得エラー:", error);
    return json({
      products: [],
      shopifyProducts: [],
      shop: session.shop,
      error: "データの取得中にエラーが発生しました",
      apiEndpoints: {}
    });
  }
}

export default function BookingFlowTestScript() {
  const { products, shopifyProducts, shop, apiEndpoints, error } = useLoaderData<typeof loader>();

  const [selectedTab, setSelectedTab] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [errorMessage, setErrorMessage] = useState(error || "");
  const [successMessage, setSuccessMessage] = useState("");
  const [testConfig, setTestConfig] = useState({
    productId: products[0]?.id || "",
    variantId: "",
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    customerEmail: "<EMAIL>",
    customerName: "テストユーザー",
    notes: "テスト予約です",
    basePrice: "5000"
  });

  const [testResults, setTestResults] = useState({
    bookingId: "",
    draftOrderId: "",
    draftOrderUrl: "",
    totalPrice: 0,
    status: ""
  });

  const logsEndRef = useRef<HTMLDivElement>(null);

  // タブの設定
  const tabs = [
    {
      id: "test-script",
      content: "テストスクリプト",
      accessibilityLabel: "テストスクリプトタブ",
      panelID: "test-script-panel",
    },
    {
      id: "test-config",
      content: "テスト設定",
      accessibilityLabel: "テスト設定タブ",
      panelID: "test-config-panel",
    }
  ];

  // ログをスクロールして最新の内容を表示
  useEffect(() => {
    if (logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [logs]);

  // 商品選択時の処理
  const handleProductChange = useCallback((value: string) => {
    setTestConfig(prev => ({
      ...prev,
      productId: value
    }));

    // 選択された商品のShopify IDを取得
    const selectedProduct = products.find(p => p.id === value);
    if (selectedProduct && selectedProduct.shopifyId) {
      // 対応するShopify商品を検索
      const shopifyProduct = shopifyProducts.find(p =>
        p.id === `gid://shopify/Product/${selectedProduct.shopifyId}`
      );

      if (shopifyProduct && shopifyProduct.variant) {
        setTestConfig(prev => ({
          ...prev,
          variantId: shopifyProduct.variant.id.replace("gid://shopify/ProductVariant/", "")
        }));
      }
    }
  }, [products, shopifyProducts]);

  // ログの追加
  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  }, []);

  // テストの実行
  const runTest = useCallback(async () => {
    setIsRunning(true);
    setIsPaused(false);
    setLogs([]);
    setCurrentStep(0);
    setErrorMessage("");
    setSuccessMessage("");
    setTestResults({
      bookingId: "",
      draftOrderId: "",
      draftOrderUrl: "",
      totalPrice: 0,
      status: ""
    });

    addLog("テストを開始します...");

    try {
      // ステップ1: 利用可能状況の確認
      setCurrentStep(1);
      addLog("ステップ1: 利用可能状況の確認");

      const availabilityUrl = `${apiEndpoints.availability}?productId=${testConfig.productId}&startDate=${testConfig.startDate}&endDate=${testConfig.endDate}&basePrice=${testConfig.basePrice}`;
      addLog(`APIリクエスト: GET ${availabilityUrl}`);

      const availabilityResponse = await fetch(availabilityUrl);
      const availabilityData = await availabilityResponse.json();

      addLog(`APIレスポンス: ${JSON.stringify(availabilityData, null, 2)}`);

      if (!availabilityData.success) {
        throw new Error(`利用可能状況の確認に失敗しました: ${availabilityData.error}`);
      }

      if (!availabilityData.available) {
        throw new Error("選択された期間は予約できません");
      }

      addLog(`利用可能状況: 予約可能`);
      addLog(`計算された料金: ${availabilityData.pricing.totalPrice}円`);

      // ステップ2: 予約の作成
      setCurrentStep(2);
      addLog("ステップ2: 予約の作成");

      const createBookingData = {
        productId: testConfig.productId,
        variantId: testConfig.variantId,
        startDate: testConfig.startDate,
        endDate: testConfig.endDate,
        customerEmail: testConfig.customerEmail,
        customerName: testConfig.customerName,
        notes: testConfig.notes,
        basePrice: parseInt(testConfig.basePrice)
      };

      addLog(`APIリクエスト: POST ${apiEndpoints.createBooking}`);
      addLog(`リクエストデータ: ${JSON.stringify(createBookingData, null, 2)}`);

      const createBookingResponse = await fetch(apiEndpoints.createBooking, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(createBookingData)
      });

      const createBookingResult = await createBookingResponse.json();

      addLog(`APIレスポンス: ${JSON.stringify(createBookingResult, null, 2)}`);

      if (!createBookingResult.success) {
        throw new Error(`予約の作成に失敗しました: ${createBookingResult.error}`);
      }

      addLog(`予約が作成されました: ID=${createBookingResult.booking.bookingId}`);

      setTestResults(prev => ({
        ...prev,
        bookingId: createBookingResult.booking.id,
        totalPrice: createBookingResult.booking.totalAmount,
        status: createBookingResult.booking.status
      }));

      // ステップ3: 仮予約から本予約への変更
      setCurrentStep(3);
      addLog("ステップ3: 仮予約から本予約への変更");

      const provisionalToConfirmedData = {
        bookingId: createBookingResult.booking.id,
        notes: "テスト: 仮予約から本予約への変更"
      };

      addLog(`APIリクエスト: POST ${apiEndpoints.provisionalToConfirmed}`);
      addLog(`リクエストデータ: ${JSON.stringify(provisionalToConfirmedData, null, 2)}`);

      const provisionalToConfirmedResponse = await fetch(apiEndpoints.provisionalToConfirmed, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(provisionalToConfirmedData)
      });

      const provisionalToConfirmedResult = await provisionalToConfirmedResponse.json();

      addLog(`APIレスポンス: ${JSON.stringify(provisionalToConfirmedResult, null, 2)}`);

      if (!provisionalToConfirmedResult.success) {
        throw new Error(`仮予約から本予約への変更に失敗しました: ${provisionalToConfirmedResult.error}`);
      }

      addLog(`仮予約が本予約に変更されました`);
      addLog(`ドラフトオーダー: ${provisionalToConfirmedResult.draftOrder.name}`);
      addLog(`請求書URL: ${provisionalToConfirmedResult.draftOrder.invoiceUrl}`);

      setTestResults(prev => ({
        ...prev,
        draftOrderId: provisionalToConfirmedResult.draftOrder.name,
        draftOrderUrl: provisionalToConfirmedResult.draftOrder.invoiceUrl,
        status: "CONFIRMED"
      }));

      // ステップ4: 予約のキャンセル
      setCurrentStep(4);
      addLog("ステップ4: 予約のキャンセル");

      const cancelBookingData = {
        bookingId: createBookingResult.booking.id,
        cancelReason: "テストキャンセル",
        notes: "テスト: 予約のキャンセル"
      };

      addLog(`APIリクエスト: POST ${apiEndpoints.cancelBooking}`);
      addLog(`リクエストデータ: ${JSON.stringify(cancelBookingData, null, 2)}`);

      const cancelBookingResponse = await fetch(apiEndpoints.cancelBooking, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(cancelBookingData)
      });

      const cancelBookingResult = await cancelBookingResponse.json();

      addLog(`APIレスポンス: ${JSON.stringify(cancelBookingResult, null, 2)}`);

      if (!cancelBookingResult.success) {
        throw new Error(`予約のキャンセルに失敗しました: ${cancelBookingResult.error}`);
      }

      addLog(`予約がキャンセルされました`);

      if (cancelBookingResult.cancellationFee > 0) {
        addLog(`キャンセル料: ${cancelBookingResult.cancellationFee}円 (${cancelBookingResult.cancellationFeeDescription})`);
      } else {
        addLog(`キャンセル料: なし`);
      }

      setTestResults(prev => ({
        ...prev,
        status: "CANCELLED"
      }));

      // テスト完了
      setCurrentStep(5);
      addLog("テスト完了: すべてのステップが正常に完了しました");
      setSuccessMessage("テストが正常に完了しました");
    } catch (error) {
      addLog(`エラー: ${error instanceof Error ? error.message : String(error)}`);
      setErrorMessage(error instanceof Error ? error.message : String(error));
    } finally {
      setIsRunning(false);
    }
  }, [apiEndpoints, testConfig, addLog]);

  // テストの一時停止/再開
  const togglePause = useCallback(() => {
    setIsPaused(!isPaused);
  }, [isPaused]);

  // テストのリセット
  const resetTest = useCallback(() => {
    setIsRunning(false);
    setIsPaused(false);
    setLogs([]);
    setCurrentStep(0);
    setErrorMessage("");
    setSuccessMessage("");
    setTestResults({
      bookingId: "",
      draftOrderId: "",
      draftOrderUrl: "",
      totalPrice: 0,
      status: ""
    });
  }, []);

  return (
    <Frame>
      <Page
        title="予約フローテストスクリプト"
        backAction={{ content: "戻る", url: "/app" }}
        primaryAction={{
          content: isRunning ? (isPaused ? "再開" : "一時停止") : "テスト実行",
          // アイコンを削除
          onAction: isRunning ? togglePause : runTest,
          disabled: isRunning && !isPaused && currentStep >= 5
        }}
        secondaryActions={[
          {
            content: "リセット",
            // アイコンを削除
            onAction: resetTest,
            disabled: isRunning && !isPaused
          }
        ]}
      >
        {errorMessage && (
          <Banner status="critical" onDismiss={() => setErrorMessage("")}>
            <p>{errorMessage}</p>
          </Banner>
        )}

        {successMessage && (
          <Banner status="success" onDismiss={() => setSuccessMessage("")}>
            <p>{successMessage}</p>
          </Banner>
        )}

        <Layout>
          <Layout.Section>
            <Tabs
              tabs={tabs}
              selected={selectedTab}
              onSelect={(index) => setSelectedTab(index)}
            />

            {selectedTab === 0 && (
              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">テスト進行状況</Text>

                  <BlockStack gap="200">
                    <InlineStack gap="200" align="center">
                      <Box
                        background={currentStep >= 1 ? "bg-success-strong" : "bg-surface-neutral"}
                        padding="200"
                        borderRadius="200"
                      >
                        <Text variant="bodyMd" fontWeight="bold" color={currentStep >= 1 ? "text-on-color" : "text-subdued"}>1</Text>
                      </Box>
                      <Text variant="bodyMd" color={currentStep >= 1 ? "text-success" : "text-subdued"}>利用可能状況の確認</Text>
                      {currentStep === 1 && isRunning && !isPaused && <Spinner size="small" />}
                    </InlineStack>

                    <InlineStack gap="200" align="center">
                      <Box
                        background={currentStep >= 2 ? "bg-success-strong" : "bg-surface-neutral"}
                        padding="200"
                        borderRadius="200"
                      >
                        <Text variant="bodyMd" fontWeight="bold" color={currentStep >= 2 ? "text-on-color" : "text-subdued"}>2</Text>
                      </Box>
                      <Text variant="bodyMd" color={currentStep >= 2 ? "text-success" : "text-subdued"}>予約の作成</Text>
                      {currentStep === 2 && isRunning && !isPaused && <Spinner size="small" />}
                    </InlineStack>

                    <InlineStack gap="200" align="center">
                      <Box
                        background={currentStep >= 3 ? "bg-success-strong" : "bg-surface-neutral"}
                        padding="200"
                        borderRadius="200"
                      >
                        <Text variant="bodyMd" fontWeight="bold" color={currentStep >= 3 ? "text-on-color" : "text-subdued"}>3</Text>
                      </Box>
                      <Text variant="bodyMd" color={currentStep >= 3 ? "text-success" : "text-subdued"}>仮予約から本予約への変更</Text>
                      {currentStep === 3 && isRunning && !isPaused && <Spinner size="small" />}
                    </InlineStack>

                    <InlineStack gap="200" align="center">
                      <Box
                        background={currentStep >= 4 ? "bg-success-strong" : "bg-surface-neutral"}
                        padding="200"
                        borderRadius="200"
                      >
                        <Text variant="bodyMd" fontWeight="bold" color={currentStep >= 4 ? "text-on-color" : "text-subdued"}>4</Text>
                      </Box>
                      <Text variant="bodyMd" color={currentStep >= 4 ? "text-success" : "text-subdued"}>予約のキャンセル</Text>
                      {currentStep === 4 && isRunning && !isPaused && <Spinner size="small" />}
                    </InlineStack>

                    <InlineStack gap="200" align="center">
                      <Box
                        background={currentStep >= 5 ? "bg-success-strong" : "bg-surface-neutral"}
                        padding="200"
                        borderRadius="200"
                      >
                        <Text variant="bodyMd" fontWeight="bold" color={currentStep >= 5 ? "text-on-color" : "text-subdued"}>5</Text>
                      </Box>
                      <Text variant="bodyMd" color={currentStep >= 5 ? "text-success" : "text-subdued"}>テスト完了</Text>
                      {currentStep === 5 && isRunning && !isPaused && <Spinner size="small" />}
                    </InlineStack>
                  </BlockStack>

                  <Divider />

                  <Text as="h2" variant="headingMd">テスト結果</Text>

                  {testResults.bookingId ? (
                    <BlockStack gap="200">
                      <Text variant="bodyMd">予約ID: {testResults.bookingId}</Text>
                      <Text variant="bodyMd">合計金額: {testResults.totalPrice}円</Text>
                      <Text variant="bodyMd">ステータス: {testResults.status}</Text>
                      {testResults.draftOrderId && (
                        <>
                          <Text variant="bodyMd">ドラフトオーダー: {testResults.draftOrderId}</Text>
                          {testResults.draftOrderUrl && (
                            <Link url={testResults.draftOrderUrl} external>
                              請求書を表示
                            </Link>
                          )}
                        </>
                      )}
                    </BlockStack>
                  ) : (
                    <Text variant="bodyMd" color="text-subdued">テストを実行すると結果がここに表示されます</Text>
                  )}

                  <Divider />

                  <Text as="h2" variant="headingMd">ログ</Text>

                  <Box
                    padding="400"
                    background="bg-surface-secondary"
                    borderRadius="200"
                    overflowY="scroll"
                    maxHeight="400px"
                  >
                    {logs.length > 0 ? (
                      <BlockStack gap="200">
                        {logs.map((log, index) => (
                          <Text key={index} variant="bodyMd" as="pre" fontFamily="monospace">
                            {log}
                          </Text>
                        ))}
                        <div ref={logsEndRef} />
                      </BlockStack>
                    ) : (
                      <Text variant="bodyMd" color="text-subdued">テストを実行するとログがここに表示されます</Text>
                    )}
                  </Box>
                </BlockStack>
              </Card>
            )}

            {selectedTab === 1 && (
              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">テスト設定</Text>

                  <Select
                    label="商品"
                    options={products.map(p => ({ label: p.title || p.id, value: p.id }))}
                    value={testConfig.productId}
                    onChange={handleProductChange}
                    disabled={isRunning}
                  />

                  <TextField
                    label="バリアントID"
                    value={testConfig.variantId}
                    onChange={(value) => setTestConfig({...testConfig, variantId: value})}
                    disabled={isRunning}
                  />

                  <InlineStack gap="200">
                    <TextField
                      label="開始日"
                      type="date"
                      value={testConfig.startDate}
                      onChange={(value) => setTestConfig({...testConfig, startDate: value})}
                      disabled={isRunning}
                    />

                    <TextField
                      label="終了日"
                      type="date"
                      value={testConfig.endDate}
                      onChange={(value) => setTestConfig({...testConfig, endDate: value})}
                      disabled={isRunning}
                    />
                  </InlineStack>

                  <TextField
                    label="基本料金"
                    type="number"
                    value={testConfig.basePrice}
                    onChange={(value) => setTestConfig({...testConfig, basePrice: value})}
                    disabled={isRunning}
                  />

                  <TextField
                    label="メールアドレス"
                    type="email"
                    value={testConfig.customerEmail}
                    onChange={(value) => setTestConfig({...testConfig, customerEmail: value})}
                    disabled={isRunning}
                  />

                  <TextField
                    label="氏名"
                    value={testConfig.customerName}
                    onChange={(value) => setTestConfig({...testConfig, customerName: value})}
                    disabled={isRunning}
                  />

                  <TextField
                    label="備考"
                    value={testConfig.notes}
                    onChange={(value) => setTestConfig({...testConfig, notes: value})}
                    multiline={3}
                    disabled={isRunning}
                  />

                  <InlineStack gap="200">
                    <Button
                      primary
                      onClick={runTest}
                      disabled={isRunning}
                    >
                      テスト実行
                    </Button>

                    <Button
                      onClick={resetTest}
                      disabled={isRunning && !isPaused}
                    >
                      リセット
                    </Button>
                  </InlineStack>
                </BlockStack>
              </Card>
            )}
          </Layout.Section>
        </Layout>
      </Page>
    </Frame>
  );
}
