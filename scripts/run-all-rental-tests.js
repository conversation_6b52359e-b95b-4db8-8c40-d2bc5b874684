/**
 * レンタル予約システム包括的テストスクリプト
 *
 * このスクリプトは以下のテストを実行します：
 * 1. 祝日判定ロジックテスト
 * 2. 祝日を挟んだ予約の料金計算テスト
 * 3. 休日を挟んだレンタルテスト
 * 4. 長期・短期レンタルテスト
 */

import { execSync } from 'child_process';

// テスト設定
const config = {
  // 実行するテストスクリプト
  testScripts: [
    {
      name: '祝日判定ロジックテスト',
      path: 'scripts/test-holiday-detection.js'
    },
    {
      name: '祝日を挟んだ予約の料金計算テスト',
      path: 'scripts/test-holiday-pricing.js'
    },
    {
      name: '休日を挟んだレンタルテスト',
      path: 'scripts/test-holiday-rental.js'
    },
    {
      name: '長期・短期レンタルテスト',
      path: 'scripts/test-rental-duration.js'
    }
  ]
};

// ユーティリティ関数
function executeCommand(command, options = {}) {
  console.log(`実行: ${command}`);
  try {
    const output = execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options
    });
    return { success: true, output: output ? output.toString() : '' };
  } catch (error) {
    console.error(`エラー: コマンド実行中にエラーが発生しました: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// テスト実行関数
function runTests() {
  console.log('===== レンタル予約システム包括的テスト開始 =====');

  let passedTests = 0;
  let failedTests = 0;

  for (const testScript of config.testScripts) {
    console.log(`\n----- ${testScript.name} (${testScript.path}) -----`);

    const result = executeCommand(`node ${testScript.path}`, { silent: false });

    if (result.success) {
      console.log(`✅ ${testScript.name}が成功しました。`);
      passedTests++;
    } else {
      console.log(`❌ ${testScript.name}が失敗しました。`);
      failedTests++;
    }
  }

  // 総合結果
  console.log('\n===== テスト結果 =====');
  console.log(`合計: ${config.testScripts.length}件`);
  console.log(`成功: ${passedTests}件`);
  console.log(`失敗: ${failedTests}件`);

  return passedTests === config.testScripts.length;
}

// メイン実行関数
function main() {
  const success = runTests();

  if (success) {
    console.log('\n✅ 全てのテストが成功しました！');
    console.log('予約システムの本番環境への展開を進めることができます。');
  } else {
    console.log('\n❌ 一部のテストが失敗しました。');
    console.log('失敗したテストを修正してから、再度テストを実行してください。');
    process.exit(1);
  }
}

// スクリプト実行
main();
