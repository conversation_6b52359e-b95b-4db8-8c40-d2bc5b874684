/**
 * 商品バリエーション方式での予約管理検証スクリプト
 *
 * このスクリプトでは、商品バリエーション方式での予約管理がPrismaで可能かどうかを検証します。
 * 以下の機能をテストします：
 * 1. バリエーションを持つテスト商品の作成
 * 2. 予約データの作成と保存
 * 3. 日付範囲での予約状況の確認
 * 4. 予約の重複チェック
 * 5. 予約データの更新と削除
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数を読み込む
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// GraphQLクエリとミューテーション
const GET_PRODUCT = gql`
  query getProduct($id: ID!) {
    product(id: $id) {
      id
      title
      handle
      variants(first: 10) {
        edges {
          node {
            id
            title
            price
            inventoryQuantity
            inventoryItem {
              id
            }
          }
        }
      }
    }
  }
`;

const UPDATE_INVENTORY = gql`
  mutation inventoryAdjustQuantity($input: InventoryAdjustQuantityInput!) {
    inventoryAdjustQuantity(input: $input) {
      inventoryLevel {
        available
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * テスト1: バリエーションを持つテスト商品の情報を取得
 */
async function getTestProduct() {
  console.log("=== テスト1: バリエーションを持つテスト商品の情報を取得 ===\n");

  try {
    // テスト商品のIDを指定（Shopify管理画面から取得したIDを使用）
    const productId = "gid://shopify/Product/8973654458536"; // テスト商品A（家具）のID

    // 商品情報を取得
    const result = await shopifyClient.request(GET_PRODUCT, {
      id: productId
    });

    const product = result.product;
    console.log(`商品名: ${product.title}`);
    console.log(`商品ID: ${product.id}`);
    console.log(`バリエーション数: ${product.variants.edges.length}`);

    // 各バリエーションの情報を表示
    console.log("\nバリエーション一覧:");
    product.variants.edges.forEach((edge, index) => {
      const variant = edge.node;
      console.log(`${index + 1}. ${variant.title} (ID: ${variant.id})`);
      console.log(`   価格: ${variant.price}`);
      console.log(`   在庫数: ${variant.inventoryQuantity}`);
      console.log(`   在庫アイテムID: ${variant.inventoryItem.id}`);
      console.log('');
    });

    return product;
  } catch (error) {
    console.error("商品情報の取得中にエラーが発生しました:", error);
    return null;
  }
}

/**
 * テスト2: 予約データの作成と保存
 */
async function createBooking(product) {
  console.log("\n=== テスト2: 予約データの作成と保存 ===\n");

  if (!product) {
    console.error("商品情報がありません");
    return null;
  }

  try {
    // Prismaデータベースから商品情報を取得
    const productId = product.id.replace('gid://shopify/Product/', '');
    let dbProduct = await prisma.product.findFirst({
      where: {
        shopifyId: productId
      }
    });

    // 商品が見つからない場合は新しく作成
    if (!dbProduct) {
      console.log(`商品ID ${productId} がデータベースに見つかりません。新しく作成します。`);

      dbProduct = await prisma.product.create({
        data: {
          title: product.title,
          sku: `TEST-${productId}`,
          price: parseFloat(product.variants.edges[0].node.price),
          description: "テスト用商品",
          status: "AVAILABLE",
          shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
          shopifyId: productId,
          basicInfo: JSON.stringify({
            productCode: `TEST-${productId}`,
            status: 'available',
            location: 'NY'
          })
        }
      });

      console.log(`データベースに商品を作成しました: ${dbProduct.title} (ID: ${dbProduct.id})`);
    }

    console.log(`データベース内の商品情報: ${dbProduct.title} (ID: ${dbProduct.id})`);

    // 予約期間を設定
    const now = new Date();
    const startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 7); // 1週間後
    const endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 10); // 10日後

    // 選択されたバリエーション（例: 4日レンタル）
    const selectedVariant = product.variants.edges[3].node;
    const variantId = selectedVariant.id.replace('gid://shopify/ProductVariant/', '');

    // 予約データを作成
    const booking = await prisma.booking.create({
      data: {
        productId: dbProduct.id,
        variantId: variantId,
        startDate: startDate,
        endDate: endDate,
        status: "CONFIRMED",
        bookingId: `TEST-BOOK-${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
        customerName: "テストユーザー",
        customerEmail: "<EMAIL>",
        totalAmount: selectedVariant.price,
        depositAmount: (parseFloat(selectedVariant.price) * 0.1).toString(),
        shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
        bookingType: "CONFIRMED"
      }
    });

    console.log("予約データを作成しました:");
    console.log(`予約ID: ${booking.bookingId}`);
    console.log(`商品ID: ${booking.productId}`);
    console.log(`バリアントID: ${booking.variantId}`);
    console.log(`開始日: ${booking.startDate.toISOString().split('T')[0]}`);
    console.log(`終了日: ${booking.endDate.toISOString().split('T')[0]}`);
    console.log(`ステータス: ${booking.status}`);

    return booking;
  } catch (error) {
    console.error("予約データの作成中にエラーが発生しました:", error);
    return null;
  }
}

/**
 * テスト3: 日付範囲での予約状況の確認
 */
async function checkAvailability(product, startDate, endDate) {
  console.log("\n=== テスト3: 日付範囲での予約状況の確認 ===\n");

  if (!product) {
    console.error("商品情報がありません");
    return false;
  }

  try {
    // Prismaデータベースから商品情報を取得
    const productId = product.id.replace('gid://shopify/Product/', '');
    let dbProduct = await prisma.product.findFirst({
      where: {
        shopifyId: productId
      }
    });

    // 商品が見つからない場合は新しく作成
    if (!dbProduct) {
      console.log(`商品ID ${productId} がデータベースに見つかりません。新しく作成します。`);

      dbProduct = await prisma.product.create({
        data: {
          title: product.title,
          sku: `TEST-${productId}`,
          price: parseFloat(product.variants.edges[0].node.price),
          description: "テスト用商品",
          status: "AVAILABLE",
          shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
          shopifyId: productId,
          basicInfo: JSON.stringify({
            productCode: `TEST-${productId}`,
            status: 'available',
            location: 'NY'
          })
        }
      });

      console.log(`データベースに商品を作成しました: ${dbProduct.title} (ID: ${dbProduct.id})`);
    }

    // 指定された日付範囲で予約が存在するか確認
    const existingBookings = await prisma.booking.findMany({
      where: {
        productId: dbProduct.id,
        status: { in: ['CONFIRMED', 'PROVISIONAL'] },
        OR: [
          {
            // 開始日が予約期間内にある
            startDate: { gte: startDate, lte: endDate }
          },
          {
            // 終了日が予約期間内にある
            endDate: { gte: startDate, lte: endDate }
          },
          {
            // 予約期間が既存の予約を包含
            AND: [
              { startDate: { lte: startDate } },
              { endDate: { gte: endDate } }
            ]
          }
        ]
      }
    });

    const isAvailable = existingBookings.length === 0;

    console.log(`日付範囲: ${startDate.toISOString().split('T')[0]} 〜 ${endDate.toISOString().split('T')[0]}`);
    console.log(`予約可能: ${isAvailable ? 'はい' : 'いいえ'}`);

    if (!isAvailable) {
      console.log(`${existingBookings.length}件の予約が見つかりました:`);
      existingBookings.forEach((booking, index) => {
        console.log(`${index + 1}. 予約ID: ${booking.bookingId}`);
        console.log(`   期間: ${booking.startDate.toISOString().split('T')[0]} 〜 ${booking.endDate.toISOString().split('T')[0]}`);
        console.log(`   ステータス: ${booking.status}`);
      });
    }

    return isAvailable;
  } catch (error) {
    console.error("予約状況の確認中にエラーが発生しました:", error);
    return false;
  }
}

/**
 * テスト4: 在庫状態の更新
 */
async function updateInventory(product, available) {
  console.log(`\n=== テスト4: 在庫状態の更新 (${available ? '利用可能' : '利用不可'}) ===\n`);

  if (!product) {
    console.error("商品情報がありません");
    return false;
  }

  try {
    // 全バリエーションの在庫を更新
    for (const edge of product.variants.edges) {
      const variant = edge.node;
      const inventoryItemId = variant.inventoryItem.id;

      // 在庫を更新
      const result = await shopifyClient.request(UPDATE_INVENTORY, {
        input: {
          inventoryItemId: inventoryItemId,
          availableDelta: available ? 1 : -1
        }
      });

      if (result.inventoryAdjustQuantity.userErrors.length > 0) {
        console.error(`バリアント ${variant.title} の在庫更新中にエラーが発生しました:`, result.inventoryAdjustQuantity.userErrors);
        continue;
      }

      console.log(`バリアント ${variant.title} の在庫を更新しました: ${result.inventoryAdjustQuantity.inventoryLevel.available}個`);
    }

    return true;
  } catch (error) {
    console.error("在庫状態の更新中にエラーが発生しました:", error);
    return false;
  }
}

/**
 * テスト5: 予約データの更新と削除
 */
async function updateAndDeleteBooking(bookingId) {
  console.log("\n=== テスト5: 予約データの更新と削除 ===\n");

  if (!bookingId) {
    console.error("予約IDがありません");
    return false;
  }

  try {
    // 予約データを更新
    const updatedBooking = await prisma.booking.update({
      where: {
        bookingId: bookingId
      },
      data: {
        status: "CANCELLED"
      }
    });

    console.log("予約データを更新しました:");
    console.log(`予約ID: ${updatedBooking.bookingId}`);
    console.log(`新しいステータス: ${updatedBooking.status}`);

    // 予約データを削除
    const deletedBooking = await prisma.booking.delete({
      where: {
        bookingId: bookingId
      }
    });

    console.log("\n予約データを削除しました:");
    console.log(`予約ID: ${deletedBooking.bookingId}`);

    return true;
  } catch (error) {
    console.error("予約データの更新・削除中にエラーが発生しました:", error);
    return false;
  }
}

// メイン処理
async function main() {
  try {
    console.log("商品バリエーション方式での予約管理検証を開始します\n");

    // テスト1: バリエーションを持つテスト商品の情報を取得
    const product = await getTestProduct();
    if (!product) {
      throw new Error("テスト商品の取得に失敗しました");
    }

    // テスト2: 予約データの作成と保存
    const booking = await createBooking(product);
    if (!booking) {
      throw new Error("予約データの作成に失敗しました");
    }

    // テスト3: 日付範囲での予約状況の確認
    // 予約済みの期間をチェック（利用不可のはず）
    const now = new Date();
    const bookedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 7);
    const bookedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 10);
    await checkAvailability(product, bookedStartDate, bookedEndDate);

    // 別の期間をチェック（利用可能のはず）
    const availableStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 20);
    const availableEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 25);
    await checkAvailability(product, availableStartDate, availableEndDate);

    // テスト4: 在庫状態の更新
    // 在庫を0に設定（利用不可）
    await updateInventory(product, false);

    // テスト5: 予約データの更新と削除
    await updateAndDeleteBooking(booking.bookingId);

    // 在庫を1に戻す（利用可能）
    await updateInventory(product, true);

    console.log("\n商品バリエーション方式での予約管理検証が完了しました");
  } catch (error) {
    console.error("\n検証中にエラーが発生しました:", error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
