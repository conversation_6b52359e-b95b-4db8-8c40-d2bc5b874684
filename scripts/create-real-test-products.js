import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品作成のミューテーション
const CREATE_PRODUCT_MUTATION = gql`
  mutation productCreate($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// テスト用商品データ
const testProducts = [
  {
    title: "花器 シルバー 穴開きボトル型",
    descriptionHtml: "花器 シルバー 穴開きボトル型 - テスト用商品",
    vendor: "テスト",
    productType: "花器",
    tags: ["テスト", "花器", "シルバー"],
    status: "ACTIVE",
    options: ["レンタル日数"],
    variants: [
      {
        price: "1500",
        sku: "212-05-023",
        options: ["1日レンタル"],
        inventoryManagement: "SHOPIFY"
      }
    ]
  },
  {
    title: "オブジェ コネクトサークル ゴールド×クリスタル",
    descriptionHtml: "オブジェ コネクトサークル ゴールド×クリスタル - テスト用商品",
    vendor: "テスト",
    productType: "オブジェ",
    tags: ["テスト", "オブジェ", "ゴールド"],
    status: "ACTIVE",
    options: ["レンタル日数"],
    variants: [
      {
        price: "1600",
        sku: "211-12-952",
        options: ["1日レンタル"],
        inventoryManagement: "SHOPIFY"
      }
    ]
  },
  {
    title: "U字ボウル ゴールド",
    descriptionHtml: "U字ボウル ゴールド - テスト用商品",
    vendor: "テスト",
    productType: "食器",
    tags: ["テスト", "食器", "ゴールド"],
    status: "ACTIVE",
    options: ["レンタル日数"],
    variants: [
      {
        price: "1400",
        sku: "201-06-555",
        options: ["1日レンタル"],
        inventoryManagement: "SHOPIFY"
      }
    ]
  }
];

// 商品を作成する関数
async function createProduct(productData) {
  try {
    const variables = {
      input: productData
    };
    
    const result = await graphQLClient.request(CREATE_PRODUCT_MUTATION, variables);
    
    if (result.productCreate.userErrors.length > 0) {
      console.error('商品作成エラー:', result.productCreate.userErrors);
      return null;
    }
    
    return result.productCreate.product;
  } catch (error) {
    console.error('商品作成中にエラーが発生しました:', error);
    return null;
  }
}

// メイン処理
async function createTestProducts() {
  console.log('テスト用商品を作成しています...');
  
  const createdProducts = [];
  
  for (const productData of testProducts) {
    console.log(`商品を作成中: ${productData.title}`);
    const product = await createProduct(productData);
    
    if (product) {
      console.log(`商品を作成しました: ${product.title} (ID: ${product.id})`);
      createdProducts.push(product);
    } else {
      console.error(`商品の作成に失敗しました: ${productData.title}`);
    }
  }
  
  console.log('\n=== 作成完了 ===');
  console.log(`${createdProducts.length}件の商品を作成しました`);
  
  if (createdProducts.length > 0) {
    console.log('\n作成された商品:');
    createdProducts.forEach(product => {
      console.log(`- ${product.title} (ID: ${product.id})`);
    });
    
    console.log('\nShopify管理画面で商品を確認: https://admin.shopify.com/store/peaces-test-block/products');
  }
}

// 実行
createTestProducts();
