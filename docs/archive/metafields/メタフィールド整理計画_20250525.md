# メタフィールド整理計画と在庫管理確認レポート

**作成日**: 2025年5月25日  
**目的**: メタフィールドの整理と在庫管理の問題確認

## 🔍 現状分析

### 定義されているメタフィールド（23個）

#### **rental名前空間（20個）**
1. **basic_info** (JSON) - 商品基本情報 ✅
2. **pricing** (JSON) - 料金設定 ✅
3. **inventory_items** (JSON) - 在庫アイテム情報 ✅
4. **reservation_info** (JSON) - 予約情報 ✅
5. **variant_mapping** (JSON) - バリアントマッピング ✅
6. **status** - レンタル状態 ✅
7. **location** - 在庫場所 ✅
8. **color** - 色情報 ✅
9. **material** - 素材情報 ✅
10. **maintenance_notes** - メンテナンス備考 ⚠️
11. **manufacturer** - メーカー ⚠️
12. **purchase_place** - 購入場所 ❌
13. **purchase_date** - 購入日 ❌
14. **purchase_price** - 購入価格 ❌
15. **is_disposed** - 廃棄済み ❌
16. **disposal_date** - 廃棄日 ❌
17. **disposal_reason** - 廃棄理由 ❌
18. **booking_notes** - 予約備考 ⚠️
19. **booking_type** - 予約タイプ ⚠️
20. **variation_type** - バリエーションタイプ ✅

#### **product名前空間（3個）**
1. **height** - 高さ ✅
2. **width** - 幅 ✅
3. **depth** - 奥行き ✅

## 🚨 発見された問題

### 1. **グループ管理機能が未実装**
- `group_info`メタフィールドが定義されていない
- 同じ形の商品やカラー違いをグループ化する仕組みが不足
- 現在は`basic_info`内にgroupCodeを含めることは可能だが、専用フィールドがない

### 2. **重複・不要なメタフィールド**
- **購入管理系（削除推奨）**：
  - purchase_place
  - purchase_date
  - purchase_price
  - → `basic_info`または`metadata`に統合可能

- **廃棄管理系（削除推奨）**：
  - is_disposed
  - disposal_date
  - disposal_reason
  - → 商品ステータスで管理すべき

- **曖昧な用途**：
  - booking_notes vs maintenance_notes
  - booking_type（全商品で同じ値）

### 3. **在庫管理の問題**
- `inventory_items`は定義されているが、Shopifyの在庫管理との連携が不明確
- 各バリアント（1日、2日...）の在庫数が全て1に設定されている想定
- 実際の在庫管理は`inventoryManagement: 'SHOPIFY'`で行われている

## 📋 整理案

### **必須メタフィールド（13個に削減）**

```json
{
  "rental": {
    "basic_info": "商品基本情報（コード、詳細、カナ、場所）",
    "pricing": "料金設定",
    "group_info": "グループ管理情報【新規追加】",
    "inventory_status": "在庫・予約状態【統合】",
    "variant_mapping": "バリアントマッピング",
    "status": "商品ステータス",
    "location": "在庫場所",
    "color": "色情報",
    "material": "素材情報",
    "variation_type": "バリエーションタイプ（rental_period固定）"
  },
  "product": {
    "height": "高さ",
    "width": "幅",
    "depth": "奥行き"
  }
}
```

### **新規追加：group_info構造**
```json
{
  "groupCode": "BASIC-SOFA-1S",
  "groupName": "ベーシックソファ 1シーター",
  "groupType": "color", // color, size, material など
  "relatedProducts": [
    {
      "productId": "gid://shopify/Product/xxx",
      "detailCode": "1",
      "variation": "オフホワイト"
    },
    {
      "productId": "gid://shopify/Product/yyy",
      "detailCode": "2", 
      "variation": "ベージュ"
    }
  ]
}
```

### **統合案：inventory_status**
```json
{
  "availability": "available", // available, maintenance, reserved
  "totalQuantity": 1, // 実在庫数
  "reservations": [], // 予約情報
  "maintenanceSchedule": [], // メンテナンス予定
  "notes": "特記事項があればここに記載"
}
```

## 🔧 在庫管理の改善案

### **現在の問題**
1. 各バリアント（1日、2日...）が別々の在庫として管理されている
2. 実際は1つの商品を日数違いでレンタルするだけ
3. 在庫数は常に1（ユニーク商品）

### **解決策**
1. **商品レベルで在庫管理**
   - 全バリアントの在庫を1に統一（現状維持）
   - `inventory_status`で実際の利用可否を管理

2. **予約による在庫制御**
   - Bookingテーブルで期間管理
   - InventoryCalendarで可視化
   - 予約が入ったら他の日数バリアントも予約不可に

3. **メタフィールドでの補助管理**
   - `inventory_status.availability`で即座に状態確認
   - 予約情報はPrismaで管理、メタフィールドはキャッシュ的使用

## 📝 実装手順

### Phase 1: メタフィールド整理
1. 不要なメタフィールドの削除スクリプト作成
2. `group_info`メタフィールド定義の追加
3. `inventory_status`への統合スクリプト作成

### Phase 2: 既存商品の移行
1. 購入情報を`basic_info`に移行
2. 予約・メンテナンス情報を`inventory_status`に統合
3. グループ情報の設定

### Phase 3: 新規商品登録の更新
1. `create-product-with-group-management.ts`の調整
2. メタフィールド設定の最適化
3. 在庫管理ロジックの確認

## 🎯 期待効果

1. **メタフィールド数を23個から13個に削減（43%削減）**
2. **グループ管理による商品関連性の明確化**
3. **在庫管理の一元化と簡素化**
4. **データ構造の明確化による保守性向上**

この整理により、メタフィールドがより効率的で管理しやすくなります。