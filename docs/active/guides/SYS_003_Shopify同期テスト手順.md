# Shopify同期テスト手順

**作成日**: 2025-05-28
**作成者**: システム開発チーム
**最終更新日**: 2025-05-28

## 概要

本ドキュメントでは、Shopifyとの同期問題を解決するためのテスト手順について説明します。特に、予約データがPrismaデータベースに保存されるものの、Shopifyのメタフィールドに同期されない問題に焦点を当てています。

## テストスクリプト

以下の2つのテストスクリプトを作成しました：

1. **予約メタフィールド同期テスト** (`scripts/test-booking-metafield-sync.ts`)
   - テスト用の予約を作成し、Shopifyメタフィールドに同期されるかをテスト
   - 同期前後の商品情報を表示
   - テスト予約を自動的に削除

2. **Shopifyメタフィールド確認** (`scripts/check-shopify-metafields.ts`)
   - Shopifyの商品メタフィールドを確認
   - 予約メタフィールドの詳細を表示

## テスト手順

### 1. 予約メタフィールド同期テスト

このスクリプトは、テスト用の予約を作成し、Shopifyメタフィールドに同期されるかをテストします。

#### 実行方法

```bash
# 商品IDを指定して実行
npx tsx scripts/test-booking-metafield-sync.ts [商品ID]
```

#### 期待される結果

- テスト予約が作成される
- Shopifyメタフィールドに同期される
- テスト予約が削除される

#### 成功例

```
予約メタフィールド同期テストを開始します...
商品ID 123456789 のテスト予約を作成します...
テスト予約が作成されました
予約ID: clxyz123-abcd-1234-5678-abcdef123456
開始日: 2025/05/28
終了日: 2025/05/31

----- 同期前の商品情報 -----
----- 商品情報 -----
ID: abcd1234-5678-90ab-cdef-123456789012
Shopify ID: 123456789
タイトル: テスト商品
SKU: TEST-123
価格: 5000
ステータス: AVAILABLE

----- 予約情報 -----
予約 1:
  ID: clxyz123-abcd-1234-5678-abcdef123456
  予約ID: TEST-BOOK-1234567
  開始日: 2025/05/28
  終了日: 2025/05/31
  ステータス: PROVISIONAL
  顧客名: テスト顧客
  顧客メール: <EMAIL>
  顧客ID: 12345678
---

----- メタフィールド同期を実行中... -----
メタフィールド同期が成功しました
メッセージ: {"success":true,"updated":true,"metafield":{"id":"gid://shopify/Metafield/12345678901234"}}

----- 同期後の商品情報 -----
----- 商品情報 -----
ID: abcd1234-5678-90ab-cdef-123456789012
Shopify ID: 123456789
タイトル: テスト商品
SKU: TEST-123
価格: 5000
ステータス: AVAILABLE

----- 予約情報 -----
予約 1:
  ID: clxyz123-abcd-1234-5678-abcdef123456
  予約ID: TEST-BOOK-1234567
  開始日: 2025/05/28
  終了日: 2025/05/31
  ステータス: PROVISIONAL
  顧客名: テスト顧客
  顧客メール: <EMAIL>
  顧客ID: 12345678
---

予約ID clxyz123-abcd-1234-5678-abcdef123456 を削除します...
テスト予約が削除されました

===== テスト結果サマリー =====
テスト予約作成: 成功
メタフィールド同期テスト: 成功
テスト予約削除: 成功

テストが成功しました！
```

### 2. Shopifyメタフィールド確認

このスクリプトは、Shopifyの商品メタフィールドを確認します。

#### 実行方法

```bash
# 商品IDを指定して実行
npx tsx scripts/check-shopify-metafields.ts [商品ID]
```

#### 期待される結果

- Shopifyの商品メタフィールドが表示される
- 予約メタフィールドの詳細が表示される

#### 成功例

```
Shopifyメタフィールド確認を開始します...
商品情報を取得しました: テスト商品 (ID: abcd1234-5678-90ab-cdef-123456789012, Shopify ID: 123456789)
Shopify商品ID 123456789 のメタフィールドを取得します...
----- 商品情報 -----
ID: gid://shopify/Product/123456789
タイトル: テスト商品

----- メタフィールド一覧 -----
メタフィールド 1:
  ID: gid://shopify/Metafield/12345678901234
  名前空間: rental
  キー: bookings
  タイプ: json
  値:
{
  "bookings": [
    {
      "id": "TEST-BOOK-1234567",
      "startDate": "2025-05-28",
      "endDate": "2025-05-31",
      "status": "PROVISIONAL",
      "customerName": "テスト顧客",
      "customerEmail": "<EMAIL>",
      "variantId": null
    }
  ],
  "status": "available",
  "lastUpdated": "2025-05-28 18:30:00",
  "variants": {
    "default": {
      "status": "available",
      "reservations": [
        {
          "id": "TEST-BOOK-1234567",
          "startDate": "2025-05-28",
          "endDate": "2025-05-31",
          "status": "PROVISIONAL",
          "type": "PROVISIONAL",
          "customerName": "テスト顧客",
          "customerEmail": "<EMAIL>"
        }
      ]
    }
  },
  "availability": {
    "rentalStatus": "available",
    "startDate": "2025-05-28",
    "maintenanceDates": [],
    "blockedDates": []
  }
}
---

----- 予約メタフィールド詳細 -----
予約一覧:
予約 1:
  ID: TEST-BOOK-1234567
  開始日: 2025-05-28
  終了日: 2025-05-31
  ステータス: PROVISIONAL
  顧客名: テスト顧客
  顧客メール: <EMAIL>
  バリアントID: なし
---

バリアント情報:
バリアントID: default
  ステータス: available
  予約:
    予約 1:
      ID: TEST-BOOK-1234567
      タイプ: PROVISIONAL
      開始日: 2025-05-28
      終了日: 2025-05-31
      ステータス: PROVISIONAL
    ---
---

利用可能状態:
  レンタル状態: available
  開始日: 2025-05-28

全体のステータス: available
最終更新日時: 2025-05-28 18:30:00

Shopifyメタフィールド確認が完了しました
```

## 問題解決の手順

Shopifyとの同期問題を解決するために、以下の手順を実施します：

### 1. 現状の確認

まず、現在の状態を確認します：

```bash
# 商品IDを指定してShopifyメタフィールドを確認
npx tsx scripts/check-shopify-metafields.ts [商品ID]
```

### 2. テスト予約の作成と同期テスト

次に、テスト予約を作成し、Shopifyメタフィールドに同期されるかをテストします：

```bash
# 商品IDを指定して予約メタフィールド同期テストを実行
npx tsx scripts/test-booking-metafield-sync.ts [商品ID]
```

### 3. 実際の予約作成と同期確認

テストが成功したら、実際の予約を作成し、Shopifyメタフィールドに同期されるかを確認します：

1. 管理画面から予約を作成
2. 予約が作成されたことを確認
3. Shopifyメタフィールドを確認

```bash
# 商品IDを指定してShopifyメタフィールドを確認
npx tsx scripts/check-shopify-metafields.ts [商品ID]
```

## トラブルシューティング

### 1. 同期エラーの確認

同期に失敗した場合は、以下のログを確認します：

- サーバーログ（コンソール出力）
- テストスクリプトのエラーメッセージ

### 2. 認証情報の確認

Shopify APIの認証情報が正しく設定されているか確認します：

- `.env`ファイルの`SHOPIFY_ADMIN_API_ACCESS_TOKEN`が正しく設定されているか
- `SHOPIFY_SHOP`が正しく設定されているか

### 3. APIアクセス権限の確認

Shopify APIのアクセス権限が正しく設定されているか確認します：

- Shopify管理画面のアプリ設定でAPIアクセス権限を確認
- 必要なスコープ（`write_products`など）が付与されているか

## 参考資料

- [Shopify Admin API Documentation](https://shopify.dev/api/admin)
- [Shopify Metafield API Documentation](https://shopify.dev/api/admin-rest/2023-01/resources/metafield)
- [Shopify App Remix Documentation](https://shopify.dev/apps/tools/app-bridge/getting-started/remix)
