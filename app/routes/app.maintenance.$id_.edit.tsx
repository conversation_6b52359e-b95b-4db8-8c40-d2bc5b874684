import React, { useState } from 'react';
import { LoaderFunctionArgs, ActionFunctionArgs, json, redirect } from '@remix-run/node';
import { useLoaderData, Form } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import {
  Page,
  LegacyCard,
  FormLayout,
  TextField,
  Select,
  Button,
  ButtonGroup,
  Text
} from '@shopify/polaris';
import { prisma } from '../db.server';
import { MaintenanceStatus, MaintenanceType } from '@prisma/client';
import { InventoryCalendarService } from '../services/inventory-calendar.service';

export async function loader({ request, params }: LoaderFunctionArgs) {
  await authenticate.admin(request);

  const { id } = params;
  if (!id) {
    throw new Response('メンテナンスIDが指定されていません', { status: 400 });
  }

  const maintenance = await prisma.maintenance.findUnique({
    where: { id },
    include: {
      product: true
    }
  });

  if (!maintenance) {
    throw new Response('メンテナンス記録が見つかりません', { status: 404 });
  }

  return json({
    maintenance: {
      id: maintenance.id,
      productId: maintenance.productId,
      productTitle: maintenance.product.title,
      productSku: maintenance.product.sku,
      shopifyProductId: maintenance.shopifyProductId,
      status: maintenance.status,
      type: maintenance.type,
      startDate: maintenance.startDate.toISOString().split('T')[0],
      endDate: maintenance.endDate?.toISOString().split('T')[0] || '',
      notes: maintenance.notes || ''
    }
  });
}

export async function action({ request, params }: ActionFunctionArgs) {
  await authenticate.admin(request);

  const { id } = params;
  if (!id) {
    throw new Response('メンテナンスIDが指定されていません', { status: 400 });
  }

  const formData = await request.formData();
  const status = formData.get('status') as MaintenanceStatus;
  const type = formData.get('type') as MaintenanceType;
  const startDate = formData.get('startDate') as string;
  const endDate = formData.get('endDate') as string;
  const notes = formData.get('notes') as string;

  try {
    const startDateObj = new Date(startDate);
    const endDateObj = endDate ? new Date(endDate) : null;

    // メンテナンス期間中に既存の予約があるかチェック
    if (status === 'IN_PROGRESS' || status === 'SCHEDULED') {
      const conflictingBookings = await prisma.booking.findMany({
        where: {
          productId: formData.get('productId') as string,
          status: { in: ['PROVISIONAL', 'CONFIRMED'] },
          AND: [
            {
              startDate: { lte: endDateObj || new Date('2099-12-31') },
              endDate: { gte: startDateObj }
            }
          ]
        }
      });

      if (conflictingBookings.length > 0) {
        console.error('メンテナンス期間中に予約が存在:', conflictingBookings);
        throw new Response(
          `メンテナンス期間中に${conflictingBookings.length}件の予約があります。先に予約をキャンセルまたは変更してください。`,
          { status: 400 }
        );
      }
    }

    // メンテナンス記録を更新
    const updatedMaintenance = await prisma.maintenance.update({
      where: { id },
      data: {
        status,
        type,
        startDate: startDateObj,
        endDate: endDateObj,
        notes: notes || null,
        updatedAt: new Date()
      },
      include: {
        product: true
      }
    });

    console.log('メンテナンス更新完了:', {
      id: updatedMaintenance.id,
      status: updatedMaintenance.status,
      productId: updatedMaintenance.productId,
      shopifyProductId: updatedMaintenance.shopifyProductId
    });

    // 在庫カレンダーサービスを初期化
    const inventoryService = new InventoryCalendarService();

    // 商品の在庫カレンダーを更新
    await inventoryService.updateCalendarForProduct(updatedMaintenance.productId);

    console.log('在庫カレンダー更新完了:', {
      productId: updatedMaintenance.productId,
      status: updatedMaintenance.status
    });

    return redirect(`/app/maintenance/${id}?shop=peaces-test-block.myshopify.com`);
  } catch (error) {
    console.error('メンテナンス更新エラー:', error);
    throw new Response('メンテナンス記録の更新に失敗しました', { status: 500 });
  }
}

export default function MaintenanceEdit() {
  const { maintenance } = useLoaderData<typeof loader>();
  const [formData, setFormData] = useState({
    status: maintenance.status,
    type: maintenance.type,
    startDate: maintenance.startDate,
    endDate: maintenance.endDate,
    notes: maintenance.notes
  });

  const statusOptions = [
    { label: '予定', value: 'SCHEDULED' },
    { label: '進行中', value: 'IN_PROGRESS' },
    { label: '完了', value: 'COMPLETED' },
    { label: 'キャンセル', value: 'CANCELLED' }
  ];

  const typeOptions = [
    { label: '定期メンテナンス', value: 'ROUTINE' },
    { label: '修理', value: 'REPAIR' },
    { label: '点検', value: 'INSPECTION' },
    { label: '清掃', value: 'CLEANING' },
    { label: 'その他', value: 'OTHER' }
  ];

  const handleCancel = () => {
    window.location.href = `/app/maintenance/${maintenance.id}?shop=peaces-test-block.myshopify.com`;
  };

  const handleBack = () => {
    window.location.href = `/app/maintenance?shop=peaces-test-block.myshopify.com`;
  };

  return (
    <Page
      title={`メンテナンス編集: ${maintenance.productTitle}`}
      backAction={{
        content: '詳細に戻る',
        onAction: handleCancel
      }}
    >
      <LegacyCard>
        <div style={{ padding: '20px' }}>
          {/* 商品情報（読み取り専用） */}
          <div style={{ marginBottom: '20px' }}>
            <Text as="h3" variant="headingMd" fontWeight="bold">
              商品情報
            </Text>
            <div style={{ marginTop: '12px' }}>
              <Text as="p" variant="bodyMd">
                <strong>商品名:</strong> {maintenance.productTitle}
              </Text>
              <Text as="p" variant="bodyMd">
                <strong>SKU:</strong> {maintenance.productSku}
              </Text>
            </div>
          </div>

          {/* 編集フォーム */}
          <Form method="post">
            <FormLayout>
              <Select
                label="ステータス"
                options={statusOptions}
                value={formData.status}
                onChange={(value) => setFormData({ ...formData, status: value as MaintenanceStatus })}
                name="status"
              />

              <Select
                label="メンテナンス種別"
                options={typeOptions}
                value={formData.type}
                onChange={(value) => setFormData({ ...formData, type: value as MaintenanceType })}
                name="type"
              />

              <TextField
                label="開始日"
                type="date"
                value={formData.startDate}
                onChange={(value) => setFormData({ ...formData, startDate: value })}
                name="startDate"
                autoComplete="off"
              />

              <TextField
                label="終了日"
                type="date"
                value={formData.endDate}
                onChange={(value) => setFormData({ ...formData, endDate: value })}
                name="endDate"
                autoComplete="off"
                helpText="終了日が未定の場合は空白にしてください"
              />

              <TextField
                label="備考"
                value={formData.notes}
                onChange={(value) => setFormData({ ...formData, notes: value })}
                name="notes"
                multiline={4}
                autoComplete="off"
                helpText="メンテナンスに関する詳細情報や注意事項を入力してください"
              />

              <ButtonGroup>
                <Button submit variant="primary">
                  保存
                </Button>
                <Button onClick={handleCancel}>
                  キャンセル
                </Button>
                <Button onClick={handleBack} variant="plain">
                  一覧に戻る
                </Button>
              </ButtonGroup>
            </FormLayout>
          </Form>
        </div>
      </LegacyCard>
    </Page>
  );
}
