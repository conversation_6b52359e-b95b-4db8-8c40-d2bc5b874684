/**
 * カテゴリマスタシステムのテストページ
 */

import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import {
  Page, Card, BlockStack, Text, DataTable, Badge
} from '@shopify/polaris';
import { CategoryMasterService } from '../services/category-master.service';

export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  
  const categoryService = new CategoryMasterService();
  
  try {
    // カテゴリマスタ一覧を取得
    const categories = await categoryService.getActiveCategoriesByShop(shop);
    
    // テスト用のSKU解析
    const testSKUs = ['212-05-023', '201-10-001', '211-12-001', '104-10-001'];
    const skuTests = await Promise.all(
      testSKUs.map(async (sku) => {
        const skuStructure = categoryService.parseSKU(sku);
        let categoryName = '';
        let subCategoryName = '';
        let isValid = false;
        
        if (skuStructure) {
          const validation = await categoryService.validateSKU(shop, sku);
          isValid = validation.isValid;
          
          if (validation.categoryExists) {
            const category = await categoryService.getCategoryByCode(shop, skuStructure.categoryCode);
            if (category) {
              categoryName = category.name;
            }
          }
          
          if (validation.subCategoryExists) {
            const subCategory = await categoryService.getSubCategoryByCode(
              shop, 
              skuStructure.categoryCode, 
              skuStructure.subCategoryCode
            );
            if (subCategory) {
              subCategoryName = subCategory.name;
            }
          }
        }
        
        return {
          sku,
          categoryCode: skuStructure?.categoryCode || '',
          subCategoryCode: skuStructure?.subCategoryCode || '',
          serialNumber: skuStructure?.serialNumber || '',
          categoryName,
          subCategoryName,
          isValid
        };
      })
    );
    
    // SKU生成テスト
    const generateTests = [];
    const testCategoryCodes = ['212', '201', '211'];
    
    for (const categoryCode of testCategoryCodes) {
      const category = await categoryService.getCategoryByCode(shop, categoryCode);
      if (category && category.subCategories && category.subCategories.length > 0) {
        const subCategory = category.subCategories[0];
        try {
          const newSKU = await categoryService.generateNextSKU(shop, categoryCode, subCategory.code);
          generateTests.push({
            categoryCode,
            categoryName: category.name,
            subCategoryCode: subCategory.code,
            subCategoryName: subCategory.name,
            generatedSKU: newSKU,
            success: true
          });
        } catch (error) {
          generateTests.push({
            categoryCode,
            categoryName: category.name,
            subCategoryCode: subCategory.code,
            subCategoryName: subCategory.name,
            generatedSKU: '',
            success: false,
            error: error instanceof Error ? error.message : '不明なエラー'
          });
        }
      }
    }
    
    return json({
      categories,
      skuTests,
      generateTests
    });
    
  } catch (error) {
    console.error('カテゴリテストエラー:', error);
    return json({
      categories: [],
      skuTests: [],
      generateTests: [],
      error: error instanceof Error ? error.message : '不明なエラーが発生しました'
    });
  }
}

export default function TestCategories() {
  const { categories, skuTests, generateTests, error } = useLoaderData<typeof loader>();
  
  // カテゴリ一覧テーブル
  const categoryRows = categories.map(category => [
    category.code,
    category.name,
    category.level.toString(),
    category.isActive ? '有効' : '無効',
    category.subCategories?.length.toString() || '0'
  ]);
  
  // SKU解析テストテーブル
  const skuTestRows = skuTests.map(test => [
    test.sku,
    test.categoryCode,
    test.subCategoryCode,
    test.serialNumber,
    test.categoryName,
    test.subCategoryName,
    test.isValid ? '✅' : '❌'
  ]);
  
  // SKU生成テストテーブル
  const generateTestRows = generateTests.map(test => [
    test.categoryCode,
    test.categoryName,
    test.subCategoryCode,
    test.subCategoryName,
    test.generatedSKU,
    test.success ? '✅' : '❌',
    test.error || ''
  ]);
  
  return (
    <Page
      title="カテゴリマスタシステムテスト"
      subtitle="カテゴリマスタとSKU管理システムの動作確認"
      backAction={{ content: 'ホーム', url: '/app' }}
    >
      <BlockStack gap="400">
        {error && (
          <Card>
            <Text as="p" tone="critical">
              エラー: {error}
            </Text>
          </Card>
        )}
        
        <Card>
          <BlockStack gap="300">
            <Text as="h2" variant="headingMd">
              カテゴリマスタ一覧 ({categories.length}件)
            </Text>
            <DataTable
              columnContentTypes={['text', 'text', 'text', 'text', 'text']}
              headings={[
                'コード',
                'カテゴリ名',
                'レベル',
                'ステータス',
                'サブカテゴリ数'
              ]}
              rows={categoryRows}
            />
          </BlockStack>
        </Card>
        
        <Card>
          <BlockStack gap="300">
            <Text as="h2" variant="headingMd">
              SKU解析テスト
            </Text>
            <DataTable
              columnContentTypes={['text', 'text', 'text', 'text', 'text', 'text', 'text']}
              headings={[
                'SKU',
                'カテゴリコード',
                'サブカテゴリコード',
                '連番',
                'カテゴリ名',
                'サブカテゴリ名',
                '有効性'
              ]}
              rows={skuTestRows}
            />
          </BlockStack>
        </Card>
        
        <Card>
          <BlockStack gap="300">
            <Text as="h2" variant="headingMd">
              SKU生成テスト
            </Text>
            <DataTable
              columnContentTypes={['text', 'text', 'text', 'text', 'text', 'text', 'text']}
              headings={[
                'カテゴリコード',
                'カテゴリ名',
                'サブカテゴリコード',
                'サブカテゴリ名',
                '生成されたSKU',
                '成功',
                'エラー'
              ]}
              rows={generateTestRows}
            />
          </BlockStack>
        </Card>
        
        <Card>
          <BlockStack gap="300">
            <Text as="h2" variant="headingMd">
              システム統計
            </Text>
            <BlockStack gap="200">
              <Text as="p">
                <Badge tone="info">総カテゴリ数: {categories.length}</Badge>
              </Text>
              <Text as="p">
                <Badge tone="success">
                  総サブカテゴリ数: {categories.reduce((sum, cat) => sum + (cat.subCategories?.length || 0), 0)}
                </Badge>
              </Text>
              <Text as="p">
                <Badge tone="warning">
                  SKUテスト成功率: {skuTests.filter(t => t.isValid).length}/{skuTests.length}
                </Badge>
              </Text>
              <Text as="p">
                <Badge tone="attention">
                  SKU生成成功率: {generateTests.filter(t => t.success).length}/{generateTests.length}
                </Badge>
              </Text>
            </BlockStack>
          </BlockStack>
        </Card>
      </BlockStack>
    </Page>
  );
}
