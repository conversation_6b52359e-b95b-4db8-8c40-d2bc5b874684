/**
 * 料金整合性チェック & 自動修正スクリプト
 *
 * 全商品の料金整合性をチェックし、必要に応じて自動修正を行います
 */

import { PrismaClient } from '@prisma/client';
import { PricingValidationService } from '../app/services/pricing/pricing-validation.service';
import { UnifiedPricingService } from '../app/services/pricing/unified-pricing.service';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

interface ProductValidationResult {
  productId: string;
  title: string;
  isValid: boolean;
  issues: string[];
  fixedIssues: string[];
}

class PricingValidator {
  private validationService: PricingValidationService;
  private pricingService: UnifiedPricingService;
  private results: ProductValidationResult[] = [];

  constructor() {
    this.validationService = PricingValidationService.getInstance();
    this.pricingService = UnifiedPricingService.getInstance();
  }

  /**
   * 全商品の料金整合性をチェック
   */
  async validateAllProducts(autoFix: boolean = false): Promise<void> {
    console.log('=== 全商品料金整合性チェック開始 ===\n');

    try {
      // 全商品を取得
      const products = await prisma.product.findMany({
        where: {
          status: 'AVAILABLE'
        },
        select: {
          id: true,
          title: true,
          price: true,
          pricing: true,
          sku: true
        }
      });

      console.log(`${products.length}件の商品をチェックします\n`);

      for (const product of products) {
        await this.validateSingleProduct(product, autoFix);
      }

      this.generateSummaryReport();

    } catch (error) {
      console.error('料金整合性チェック中にエラーが発生しました:', error);
    } finally {
      await prisma.$disconnect();
    }
  }

  /**
   * 単一商品の料金整合性をチェック
   */
  private async validateSingleProduct(product: any, autoFix: boolean): Promise<void> {
    console.log(`--- ${product.title} ---`);

    const result: ProductValidationResult = {
      productId: product.id,
      title: product.title,
      isValid: true,
      issues: [],
      fixedIssues: []
    };

    try {
      // 基本価格を取得
      const basePrice = typeof product.price === 'object' ? Number(product.price) : product.price;
      console.log(`基本価格: ${this.pricingService.formatPrice(basePrice)}`);

      // バリエーション価格を解析（pricingフィールドから）
      const variantPrices = this.parseVariantPricesFromPricing(product.pricing);
      console.log(`バリエーション数: ${variantPrices.length}`);

      // 整合性チェック
      const validation = this.validationService.validatePricing(basePrice, variantPrices);

      if (!validation.isValid) {
        result.isValid = false;

        // 不整合の詳細を記録
        validation.discrepancies.forEach(disc => {
          const issue = `${disc.days}日: バリエーション=${disc.variantPrice}円 vs 計算=${disc.calculatedPrice}円 (差額: ${disc.difference}円)`;
          result.issues.push(issue);
          console.log(`❌ ${issue}`);
        });

        validation.missingVariants.forEach(days => {
          const issue = `${days}日バリエーションが不足`;
          result.issues.push(issue);
          console.log(`❌ ${issue}`);
        });

        // 自動修正を実行
        if (autoFix) {
          await this.fixProductPricing(product, basePrice);
          result.fixedIssues = [...result.issues];
          console.log('✅ 自動修正を実行しました');
        }
      } else {
        console.log('✅ 料金整合性: 正常');
      }

      // 整合性レポートを生成
      const report = this.validationService.generateValidationReport(
        product.title,
        basePrice,
        variantPrices
      );

      if (!validation.isValid) {
        console.log('\n' + report);
      }

    } catch (error) {
      result.isValid = false;
      result.issues.push(`チェック中にエラーが発生: ${error.message}`);
      console.error(`❌ エラー: ${error.message}`);
    }

    this.results.push(result);
    console.log('');
  }

  /**
   * pricingフィールドからバリエーション価格を解析
   */
  private parseVariantPricesFromPricing(pricing: any): Array<{ days: number; price: number; variantId: string; title: string }> {
    const variantPrices: Array<{ days: number; price: number; variantId: string; title: string }> = [];

    if (!pricing || typeof pricing !== 'object') {
      return variantPrices;
    }

    // pricingフィールドの構造に応じて解析
    // 例: { "1day": 3000, "2day": 3600, ... } または { "variants": [...] }
    if (pricing.variants && Array.isArray(pricing.variants)) {
      pricing.variants.forEach((variant: any) => {
        const title = variant.title || '';
        const price = typeof variant.price === 'object' ? Number(variant.price) : variant.price;

        // タイトルから日数を抽出
        const dayMatch = title.match(/(\d+)日/);
        if (dayMatch) {
          const days = parseInt(dayMatch[1]);
          variantPrices.push({
            days,
            price,
            variantId: variant.id || `${days}day`,
            title
          });
        }
      });
    } else {
      // 直接的な日数キーの場合
      Object.keys(pricing).forEach(key => {
        const dayMatch = key.match(/(\d+)day/);
        if (dayMatch) {
          const days = parseInt(dayMatch[1]);
          const price = typeof pricing[key] === 'object' ? Number(pricing[key]) : pricing[key];
          variantPrices.push({
            days,
            price,
            variantId: key,
            title: `${days}日レンタル`
          });
        }
      });
    }

    return variantPrices.sort((a, b) => a.days - b.days);
  }

  /**
   * 商品の料金を自動修正
   */
  private async fixProductPricing(product: any, basePrice: number): Promise<void> {
    try {
      // 正しいバリエーション価格を生成
      const correctPrices = this.validationService.generateCorrectVariantPrices(basePrice);

      // データベースのバリエーション価格を更新
      for (const correctPrice of correctPrices) {
        if (correctPrice.days <= 7) {
          // 対応するバリエーションを検索
          const existingVariant = product.variants?.find((v: any) => {
            const title = v.title || '';
            return title.includes(`${correctPrice.days}日`);
          });

          if (existingVariant) {
            // 既存バリエーションの価格を更新
            await prisma.productVariant.update({
              where: { id: existingVariant.id },
              data: { price: correctPrice.price }
            });
            console.log(`  ${correctPrice.days}日バリエーション価格を${correctPrice.price}円に更新`);
          } else {
            // 新しいバリエーションを作成
            await prisma.productVariant.create({
              data: {
                productId: product.id,
                title: correctPrice.title,
                price: correctPrice.price,
                sku: `${product.sku}-${correctPrice.days}D`,
                inventoryQuantity: 1
              }
            });
            console.log(`  ${correctPrice.days}日バリエーションを新規作成: ${correctPrice.price}円`);
          }
        }
      }

    } catch (error) {
      console.error(`商品 ${product.title} の自動修正中にエラーが発生:`, error);
    }
  }

  /**
   * サマリーレポートを生成
   */
  private generateSummaryReport(): void {
    console.log('=== 料金整合性チェック サマリー ===');

    const totalProducts = this.results.length;
    const validProducts = this.results.filter(r => r.isValid).length;
    const invalidProducts = totalProducts - validProducts;
    const fixedProducts = this.results.filter(r => r.fixedIssues.length > 0).length;

    console.log(`総商品数: ${totalProducts}`);
    console.log(`正常: ${validProducts} (${((validProducts / totalProducts) * 100).toFixed(1)}%)`);
    console.log(`問題あり: ${invalidProducts} (${((invalidProducts / totalProducts) * 100).toFixed(1)}%)`);
    console.log(`自動修正済み: ${fixedProducts}`);

    if (invalidProducts > 0) {
      console.log('\n問題のある商品:');
      this.results.filter(r => !r.isValid).forEach(result => {
        console.log(`- ${result.title}`);
        result.issues.forEach(issue => {
          console.log(`  ${issue}`);
        });
      });
    }

    console.log('\n=== チェック完了 ===');
  }

  /**
   * 特定商品の詳細チェック
   */
  async validateSpecificProduct(productId: string): Promise<void> {
    try {
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: { variants: true }
      });

      if (!product) {
        console.log('商品が見つかりません');
        return;
      }

      await this.validateSingleProduct(product, false);
    } catch (error) {
      console.error('商品チェック中にエラーが発生:', error);
    } finally {
      await prisma.$disconnect();
    }
  }
}

// メイン実行
async function main() {
  const validator = new PricingValidator();

  const args = process.argv.slice(2);
  const command = args[0];
  const autoFix = args.includes('--fix');

  switch (command) {
    case 'all':
      await validator.validateAllProducts(autoFix);
      break;
    case 'product':
      const productId = args[1];
      if (!productId) {
        console.log('使用方法: npm run validate-pricing product <productId>');
        return;
      }
      await validator.validateSpecificProduct(productId);
      break;
    default:
      console.log('使用方法:');
      console.log('  npm run validate-pricing all [--fix]     # 全商品をチェック');
      console.log('  npm run validate-pricing product <id>    # 特定商品をチェック');
      break;
  }
}

if (import.meta.url === new URL(import.meta.url).href) {
  main().catch(console.error);
}

export { PricingValidator };
