/**
 * 実際の在庫状況確認スクリプト
 */

import { authenticate } from '../app/shopify.server';

async function checkActualInventory() {
  console.log('=== 実際の在庫状況確認開始 ===');

  try {
    const { admin } = await authenticate.admin(new Request('http://localhost'));

    // 最近作成された商品を取得（詳細な在庫情報付き）
    const response = await admin.graphql(`
      query getRecentProductsWithInventory {
        products(first: 3, reverse: true) {
          edges {
            node {
              id
              title
              handle
              createdAt
              variants(first: 20) {
                edges {
                  node {
                    id
                    title
                    sku
                    price
                    inventoryItem {
                      id
                      tracked
                      requiresShipping
                      inventoryLevels(first: 10) {
                        edges {
                          node {
                            id
                            available
                            committed
                            incoming
                            onHand
                            location {
                              id
                              name
                              address {
                                formatted
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    `);

    const data = await response.json();
    
    if (data.errors) {
      throw new Error(`GraphQL エラー: ${JSON.stringify(data.errors)}`);
    }

    const products = data.data.products.edges;

    console.log(`\n📦 確認対象商品数: ${products.length}`);

    products.forEach((productEdge: any, index: number) => {
      const product = productEdge.node;
      console.log(`\n=== 商品 ${index + 1}: ${product.title} ===`);
      console.log(`ID: ${product.id}`);
      console.log(`Handle: ${product.handle}`);
      console.log(`作成日時: ${product.createdAt}`);

      const variants = product.variants.edges;
      console.log(`\n🔧 バリエーション数: ${variants.length}`);
      
      variants.forEach((variantEdge: any, variantIndex: number) => {
        const variant = variantEdge.node;
        console.log(`\n  ${variantIndex + 1}. ${variant.title}`);
        console.log(`     SKU: ${variant.sku}`);
        console.log(`     価格: ¥${variant.price}`);
        console.log(`     InventoryItem ID: ${variant.inventoryItem.id}`);
        console.log(`     在庫追跡: ${variant.inventoryItem.tracked ? 'ON' : 'OFF'}`);
        console.log(`     配送必要: ${variant.inventoryItem.requiresShipping ? 'YES' : 'NO'}`);
        
        // 在庫レベル詳細
        const inventoryLevels = variant.inventoryItem.inventoryLevels.edges;
        console.log(`     📦 在庫レベル数: ${inventoryLevels.length}`);
        
        if (inventoryLevels.length === 0) {
          console.log(`     ⚠️ 在庫レベルが設定されていません`);
        } else {
          inventoryLevels.forEach((levelEdge: any) => {
            const level = levelEdge.node;
            console.log(`     📍 ロケーション: ${level.location.name}`);
            console.log(`        - 利用可能: ${level.available}個`);
            console.log(`        - 予約済み: ${level.committed}個`);
            console.log(`        - 入荷予定: ${level.incoming}個`);
            console.log(`        - 手持ち: ${level.onHand}個`);
            console.log(`        - ロケーションID: ${level.location.id}`);
            if (level.location.address?.formatted) {
              console.log(`        - 住所: ${level.location.address.formatted}`);
            }
          });
        }
      });
    });

    // ロケーション一覧も確認
    console.log('\n=== 利用可能なロケーション一覧 ===');
    const locationResponse = await admin.graphql(`
      query getLocations {
        locations(first: 10) {
          edges {
            node {
              id
              name
              address {
                formatted
              }
              fulfillmentService {
                id
                serviceName
              }
            }
          }
        }
      }
    `);

    const locationData = await locationResponse.json();
    if (locationData.errors) {
      console.error('ロケーション取得エラー:', locationData.errors);
    } else {
      const locations = locationData.data.locations.edges;
      locations.forEach((locationEdge: any) => {
        const location = locationEdge.node;
        console.log(`📍 ${location.name}: ${location.id}`);
        if (location.address?.formatted) {
          console.log(`   住所: ${location.address.formatted}`);
        }
        if (location.fulfillmentService) {
          console.log(`   配送サービス: ${location.fulfillmentService.serviceName}`);
        }
      });
    }

  } catch (error) {
    console.error('❌ エラー:', error);
  }

  console.log('\n=== 実際の在庫状況確認完了 ===');
}

// スクリプト実行
if (import.meta.url === new URL(import.meta.url).href) {
  checkActualInventory().catch(console.error);
}

export { checkActualInventory };
