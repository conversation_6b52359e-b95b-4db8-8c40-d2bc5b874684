/**
 * 休日を挟んだレンタルテストスクリプト
 *
 * このスクリプトは以下の機能をテストします：
 * 1. 日曜日を含むレンタル期間の料金計算
 * 2. 祝日を含むレンタル期間の料金計算
 * 3. 年末年始を含むレンタル期間の料金計算
 * 4. 複数の休日を含むレンタル期間の料金計算
 */

// ESモジュール形式で実行

// テスト設定
const config = {
  // APIエンドポイント
  apiBaseUrl: 'https://app.shopify-app-test.xyz',
  // テスト用商品ID
  productId: '8873137176744', // ソファテスト
  // テスト用バリアントID
  variantId: '47368928911544',
  // 基本料金
  basePrice: 1000,
  // テストケース
  testCases: [
    // 1. 日曜日を含むテストケース
    {
      name: 'テスト1: 日曜日を含む期間',
      startDate: '2025-06-06', // 金曜日
      endDate: '2025-06-09',   // 月曜日
      expectedDays: 3,         // 日曜日を除く
      expectedPrice: 1000 + (1000 * 0.2 * 2) // 1日目: 1000円, 2-3日目: 各200円
    },
    {
      name: 'テスト2: 複数の日曜日を含む期間',
      startDate: '2025-06-02', // 月曜日
      endDate: '2025-06-16',   // 月曜日
      expectedDays: 13,        // 2つの日曜日を除く
      expectedPrice: 1000 + (1000 * 0.2 * 5) + (1000 * 0.1 * 7) // 1日目: 1000円, 2-6日目: 各200円, 7-13日目: 各100円
    },

    // 2. 祝日を含むテストケース
    {
      name: 'テスト3: 祝日を含む期間',
      startDate: '2025-07-14', // 月曜日
      endDate: '2025-07-22',   // 火曜日
      expectedDays: 7,         // 日曜日と祝日（7/21 海の日）を除く
      expectedPrice: 1000 + (1000 * 0.2 * 5) + (1000 * 0.1 * 1) // 1日目: 1000円, 2-6日目: 各200円, 7日目: 100円
    },
    {
      name: 'テスト4: 複数の祝日を含む期間',
      startDate: '2025-05-01', // 木曜日
      endDate: '2025-05-10',   // 土曜日
      expectedDays: 6,         // 日曜日と祝日（5/3 憲法記念日, 5/4 みどりの日, 5/5 こどもの日, 5/6 振替休日）を除く
      expectedPrice: 1000 + (1000 * 0.2 * 5) // 1日目: 1000円, 2-6日目: 各200円
    },

    // 3. 年末年始を含むテストケース
    {
      name: 'テスト5: 年末年始を含む期間',
      startDate: '2025-12-28', // 日曜日
      endDate: '2026-01-05',   // 月曜日
      expectedDays: 1,         // 1/5のみ (日曜、祝日、年末年始を除く)
      expectedPrice: 1000      // 1日目: 1000円
    },

    // 4. 複合的なテストケース
    {
      name: 'テスト6: 祝日と日曜日が重なる期間',
      startDate: '2025-11-02', // 日曜日
      endDate: '2025-11-04',   // 火曜日（11/3は文化の日）
      expectedDays: 1,         // 11/4のみ (日曜と祝日を除く)
      expectedPrice: 1000      // 1日目: 1000円
    },
    {
      name: 'テスト7: 長期間で複数の休日を含む期間',
      startDate: '2025-04-25', // 金曜日
      endDate: '2025-05-10',   // 土曜日
      expectedDays: 10,        // 日曜日と祝日（4/29 昭和の日, 5/3 憲法記念日, 5/4 みどりの日, 5/5 こどもの日, 5/6 振替休日）を除く
      expectedPrice: 1000 + (1000 * 0.2 * 5) + (1000 * 0.1 * 4) // 1日目: 1000円, 2-6日目: 各200円, 7-10日目: 各100円
    }
  ]
};

// ユーティリティ関数
function formatDate(date) {
  return new Date(date).toLocaleDateString('ja-JP');
}

// 日付が日曜日かどうかを判定
function isSunday(date) {
  return new Date(date).getDay() === 0;
}

// 日付が祝日かどうかを判定（簡易版）
function isJapaneseHoliday(date) {
  // 簡易的な祝日判定（実際にはライブラリを使用）
  const holidays = [
    '2025-01-01', // 元日
    '2025-01-13', // 成人の日
    '2025-02-11', // 建国記念の日
    '2025-02-23', // 天皇誕生日
    '2025-02-24', // 振替休日
    '2025-03-20', // 春分の日
    '2025-04-29', // 昭和の日
    '2025-05-03', // 憲法記念日
    '2025-05-04', // みどりの日
    '2025-05-05', // こどもの日
    '2025-05-06', // 振替休日
    '2025-07-21', // 海の日
    '2025-08-11', // 山の日
    '2025-09-15', // 敬老の日
    '2025-09-23', // 秋分の日
    '2025-10-13', // スポーツの日
    '2025-11-03', // 文化の日
    '2025-11-23', // 勤労感謝の日
    '2025-11-24', // 振替休日
    '2025-12-23', // 天皇誕生日
  ];

  const dateStr = date.toISOString().split('T')[0];
  return holidays.includes(dateStr);
}

// 日付が年末年始かどうかを判定
function isNewYearHoliday(date) {
  const month = date.getMonth() + 1;
  const day = date.getDate();

  // 12月29日から1月3日までを年末年始とする
  return (month === 12 && day >= 29) || (month === 1 && day <= 3);
}

// 営業日数を計算
function calculateBusinessDays(startDate, endDate) {
  let businessDays = 0;
  const start = new Date(startDate);
  const end = new Date(endDate);
  const current = new Date(start);

  while (current <= end) {
    // 日曜日・祝日・年末年始以外の日をカウント
    if (!isSunday(current) && !isJapaneseHoliday(current) && !isNewYearHoliday(current)) {
      businessDays++;
    }
    current.setDate(current.getDate() + 1);
  }

  return businessDays;
}

// 料金計算
function calculateRentalPrice(startDate, endDate, basePrice) {
  // 営業日数を計算
  const rentalDays = calculateBusinessDays(startDate, endDate);

  // 総日数（開始日から終了日までの日数）
  const totalDays = Math.floor((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24)) + 1;

  let totalPrice = 0;

  if (rentalDays >= 1) {
    // 1日目は基本料金
    totalPrice += basePrice;

    if (rentalDays > 1) {
      // 2-6日目は基本料金の20%
      const day2to6 = Math.min(5, rentalDays - 1);
      totalPrice += basePrice * 0.2 * day2to6;

      if (rentalDays > 6) {
        // 7日目以降は基本料金の10%
        const day7plus = rentalDays - 6;
        totalPrice += basePrice * 0.1 * day7plus;
      }
    }
  }

  // 整数に丸める
  return {
    totalDays,
    rentalDays,
    totalPrice: Math.round(totalPrice)
  };
}

// APIで料金計算をテスト
async function testPriceCalculation(testCase) {
  try {
    // APIリクエスト
    const url = `${config.apiBaseUrl}/api/calculate/rental?productId=${config.productId}&startDate=${testCase.startDate}&endDate=${testCase.endDate}`;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`APIエラー: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`APIリクエスト中にエラーが発生しました: ${error.message}`);
    return null;
  }
}

// メインテスト実行関数
async function runTests() {
  console.log('===== 休日を挟んだレンタルテスト開始 =====');

  let passedCount = 0;
  let failedCount = 0;

  for (const testCase of config.testCases) {
    console.log(`\n----- ${testCase.name} -----`);
    console.log(`期間: ${formatDate(testCase.startDate)} 〜 ${formatDate(testCase.endDate)}`);

    // ローカル計算
    const localResult = calculateRentalPrice(testCase.startDate, testCase.endDate, config.basePrice);
    console.log(`ローカル計算結果: 総日数=${localResult.totalDays}日, 営業日数=${localResult.rentalDays}日, ¥${localResult.totalPrice}`);

    // 期待値との比較
    const localTestPassed = localResult.rentalDays === testCase.expectedDays && localResult.totalPrice === testCase.expectedPrice;
    console.log(`ローカルテスト: ${localTestPassed ? '成功' : '失敗'}`);

    if (!localTestPassed) {
      console.log(`  期待値: 営業日数=${testCase.expectedDays}日, 料金=¥${testCase.expectedPrice}`);
      console.log(`  実際値: 営業日数=${localResult.rentalDays}日, 料金=¥${localResult.totalPrice}`);
    }

    // API計算（オプション）
    try {
      const apiResult = await testPriceCalculation(testCase);
      if (apiResult) {
        console.log(`API計算結果: 営業日数=${apiResult.rentalDays}日, ¥${apiResult.totalPrice}`);

        // APIテスト結果の検証
        const apiTestPassed = apiResult.rentalDays === testCase.expectedDays &&
                             Math.abs(apiResult.totalPrice - testCase.expectedPrice) <= 10; // 小数点の丸め誤差を許容

        console.log(`APIテスト: ${apiTestPassed ? '成功' : '失敗'}`);

        if (!apiTestPassed) {
          console.log(`  期待値: 営業日数=${testCase.expectedDays}日, 料金=¥${testCase.expectedPrice}`);
          console.log(`  実際値: 営業日数=${apiResult.rentalDays}日, 料金=¥${apiResult.totalPrice}`);
        }
      }
    } catch (error) {
      console.log(`APIテスト: スキップ (${error.message})`);
    }

    // テスト結果のカウント
    if (localTestPassed) {
      passedCount++;
    } else {
      failedCount++;
    }
  }

  // 総合結果
  console.log('\n===== テスト結果 =====');
  console.log(`合計: ${config.testCases.length}件`);
  console.log(`成功: ${passedCount}件`);
  console.log(`失敗: ${failedCount}件`);

  return passedCount === config.testCases.length;
}

// テスト実行
runTests().then(success => {
  if (success) {
    console.log('\n✅ 全てのテストが成功しました！');
  } else {
    console.log('\n❌ 一部のテストが失敗しました。');
  }
}).catch(error => {
  console.error('\nテスト実行中にエラーが発生しました:', error);
});
