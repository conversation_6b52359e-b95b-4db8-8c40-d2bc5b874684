/**
 * メンテナンス中商品の予約防止テストスクリプト
 *
 * このスクリプトは以下をテストします：
 * 1. Shopify商品をメンテナンス中に設定
 * 2. メンテナンス中の商品が予約できないことを確認
 * 3. 在庫カレンダーが正しく更新されているかチェック
 * 4. メンテナンス完了後に予約可能になることを確認
 *
 * 実行方法: node scripts/test-maintenance-booking-prevention.js [商品SKU]
 */

import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import { v4 as uuidv4 } from 'uuid';
import { addDays, format, parseISO } from 'date-fns';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品情報を取得するクエリ
const GET_PRODUCT = gql`
  query getProduct($id: ID!) {
    product(id: $id) {
      id
      title
      status
      variants(first: 5) {
        edges {
          node {
            id
            title
            sku
            inventoryQuantity
            inventoryPolicy
          }
        }
      }
      metafields(first: 20) {
        edges {
          node {
            id
            namespace
            key
            value
          }
        }
      }
    }
  }
`;

/**
 * 商品情報を取得する関数
 */
async function getProductInfo(shopifyId) {
  try {
    console.log(`🔍 商品情報を取得中... (Shopify ID: ${shopifyId})`);

    // Shopifyから商品情報を取得
    const shopifyGid = `gid://shopify/Product/${shopifyId}`;
    const result = await shopifyClient.request(GET_PRODUCT, {
      id: shopifyGid
    });

    if (!result.product) {
      throw new Error(`Shopify商品ID ${shopifyId} が見つかりません`);
    }

    // データベースから商品情報を取得
    const dbProduct = await prisma.product.findFirst({
      where: { shopifyId: shopifyId }
    });

    if (!dbProduct) {
      // データベースに商品が存在しない場合は作成
      console.log(`⚠️ データベースに商品が存在しないため、作成します...`);
      const createdProduct = await prisma.product.create({
        data: {
          id: uuidv4(),
          shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
          shopifyId: shopifyId,
          title: result.product.title,
          sku: result.product.variants.edges[0]?.node.sku || `SKU-${shopifyId}`,
          price: 10000,
          status: 'AVAILABLE',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      console.log(`✅ 商品をデータベースに作成: ${createdProduct.id}`);
      return { shopifyProduct: result.product, dbProduct: createdProduct };
    }

    console.log(`✅ 商品情報を取得: ${result.product.title}`);
    return { shopifyProduct: result.product, dbProduct };
  } catch (error) {
    console.error('❌ 商品情報の取得中にエラー:', error);
    throw error;
  }
}

/**
 * メンテナンス記録を作成する関数
 */
async function createMaintenanceRecord(productId, shopifyProductId, startDate, endDate) {
  try {
    console.log(`🔧 メンテナンス記録を作成中...`);

    const maintenance = await prisma.maintenance.create({
      data: {
        id: uuidv4(),
        shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
        productId: productId,
        shopifyProductId: shopifyProductId,
        status: 'IN_PROGRESS',
        type: 'REPAIR',
        startDate: startDate,
        endDate: endDate,
        notes: 'テスト用メンテナンス - 予約防止テスト',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log(`✅ メンテナンス記録を作成: ${maintenance.id}`);
    console.log(`   期間: ${format(startDate, 'yyyy-MM-dd')} 〜 ${format(endDate, 'yyyy-MM-dd')}`);

    return maintenance;
  } catch (error) {
    console.error('❌ メンテナンス記録の作成中にエラー:', error);
    throw error;
  }
}

/**
 * 在庫カレンダーを更新する関数
 */
async function updateInventoryCalendar(productId, shopifyProductId, startDate, endDate, maintenanceId) {
  try {
    console.log(`📅 在庫カレンダーを更新中...`);

    const dates = [];
    const currentDate = new Date(startDate);
    const finalDate = new Date(endDate);

    while (currentDate <= finalDate) {
      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    console.log(`   更新対象日数: ${dates.length}日`);

    for (const date of dates) {
      await prisma.inventoryCalendar.upsert({
        where: {
          shop_productId_date: {
            shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
            productId: productId,
            date: date
          }
        },
        update: {
          isAvailable: false,
          unavailableReason: 'MAINTENANCE',
          maintenanceId: maintenanceId,
          updatedAt: new Date()
        },
        create: {
          shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
          productId: productId,
          shopifyProductId: shopifyProductId,
          date: date,
          isAvailable: false,
          unavailableReason: 'MAINTENANCE',
          maintenanceId: maintenanceId,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }

    console.log(`✅ 在庫カレンダーを更新完了`);
  } catch (error) {
    console.error('❌ 在庫カレンダーの更新中にエラー:', error);
    throw error;
  }
}

/**
 * 予約作成を試行する関数
 */
async function attemptBookingCreation(product, startDate, endDate) {
  try {
    console.log(`📝 予約作成を試行中...`);
    console.log(`   期間: ${format(startDate, 'yyyy-MM-dd')} 〜 ${format(endDate, 'yyyy-MM-dd')}`);

    // 在庫カレンダーをチェック
    const unavailableDates = await prisma.inventoryCalendar.findMany({
      where: {
        productId: product.dbProduct.id,
        date: {
          gte: startDate,
          lte: endDate
        },
        isAvailable: false
      }
    });

    if (unavailableDates.length > 0) {
      console.log(`❌ 予約不可: ${unavailableDates.length}日間が利用不可`);
      unavailableDates.forEach(date => {
        console.log(`   ${format(date.date, 'yyyy-MM-dd')}: ${date.unavailableReason}`);
      });
      return { success: false, reason: 'MAINTENANCE_PERIOD', unavailableDates };
    }

    // 予約を作成（実際には作成しない、チェックのみ）
    console.log(`✅ 予約可能: 在庫カレンダーに問題なし`);
    return { success: true, reason: 'AVAILABLE' };

  } catch (error) {
    console.error('❌ 予約作成試行中にエラー:', error);
    return { success: false, reason: 'ERROR', error };
  }
}

/**
 * メンテナンス完了処理
 */
async function completeMaintenanceRecord(maintenanceId) {
  try {
    console.log(`🔧 メンテナンス完了処理中...`);

    const maintenance = await prisma.maintenance.update({
      where: { id: maintenanceId },
      data: {
        status: 'COMPLETED',
        completionDate: new Date(),
        updatedAt: new Date()
      }
    });

    // 在庫カレンダーを更新（利用可能に戻す）
    await prisma.inventoryCalendar.updateMany({
      where: {
        maintenanceId: maintenanceId,
        unavailableReason: 'MAINTENANCE'
      },
      data: {
        isAvailable: true,
        unavailableReason: null,
        maintenanceId: null,
        updatedAt: new Date()
      }
    });

    console.log(`✅ メンテナンス完了: ${maintenance.id}`);
    return maintenance;
  } catch (error) {
    console.error('❌ メンテナンス完了処理中にエラー:', error);
    throw error;
  }
}

/**
 * テストクリーンアップ
 */
async function cleanupTestData(maintenanceId) {
  try {
    console.log(`🧹 テストデータをクリーンアップ中...`);

    // 在庫カレンダーを削除
    await prisma.inventoryCalendar.deleteMany({
      where: { maintenanceId: maintenanceId }
    });

    // メンテナンス記録を削除
    await prisma.maintenance.delete({
      where: { id: maintenanceId }
    });

    console.log(`✅ テストデータをクリーンアップ完了`);
  } catch (error) {
    console.error('❌ クリーンアップ中にエラー:', error);
  }
}

/**
 * メイン関数
 */
async function main() {
  let maintenanceId = null;

  try {
    // コマンドライン引数からShopify商品IDを取得
    const shopifyId = process.argv[2] || '8977061544104';

    console.log(`🚀 メンテナンス中商品の予約防止テストを開始`);
    console.log(`📦 対象商品Shopify ID: ${shopifyId}`);
    console.log(`📅 テスト日時: ${format(new Date(), 'yyyy-MM-dd HH:mm:ss')}`);
    console.log('');

    // ステップ1: 商品情報を取得
    console.log('=== ステップ1: 商品情報取得 ===');
    const product = await getProductInfo(shopifyId);
    console.log(`商品ID: ${product.dbProduct.id}`);
    console.log(`Shopify ID: ${product.dbProduct.shopifyId}`);
    console.log(`商品名: ${product.shopifyProduct.title}`);
    console.log('');

    // ステップ2: メンテナンス期間を設定（今日から7日間）
    console.log('=== ステップ2: メンテナンス記録作成 ===');
    const today = new Date();
    const maintenanceStart = new Date(today);
    const maintenanceEnd = addDays(today, 7);

    const maintenance = await createMaintenanceRecord(
      product.dbProduct.id,
      product.dbProduct.shopifyId,
      maintenanceStart,
      maintenanceEnd
    );
    maintenanceId = maintenance.id;
    console.log('');

    // ステップ3: 在庫カレンダーを更新
    console.log('=== ステップ3: 在庫カレンダー更新 ===');
    await updateInventoryCalendar(
      product.dbProduct.id,
      product.dbProduct.shopifyId,
      maintenanceStart,
      maintenanceEnd,
      maintenance.id
    );
    console.log('');

    // ステップ4: メンテナンス期間中の予約を試行（失敗するはず）
    console.log('=== ステップ4: メンテナンス期間中の予約試行 ===');
    const bookingStart = addDays(today, 2);
    const bookingEnd = addDays(today, 4);

    const bookingResult1 = await attemptBookingCreation(product, bookingStart, bookingEnd);

    if (!bookingResult1.success && bookingResult1.reason === 'MAINTENANCE_PERIOD') {
      console.log('✅ 期待通り: メンテナンス期間中は予約不可');
    } else {
      console.log('❌ 問題: メンテナンス期間中なのに予約可能');
    }
    console.log('');

    // ステップ5: メンテナンス完了後の期間で予約を試行（成功するはず）
    console.log('=== ステップ5: メンテナンス完了後の予約試行 ===');
    const futureBookingStart = addDays(today, 10);
    const futureBookingEnd = addDays(today, 12);

    const bookingResult2 = await attemptBookingCreation(product, futureBookingStart, futureBookingEnd);

    if (bookingResult2.success) {
      console.log('✅ 期待通り: メンテナンス期間外は予約可能');
    } else {
      console.log('❌ 問題: メンテナンス期間外なのに予約不可');
      console.log(`理由: ${bookingResult2.reason}`);
    }
    console.log('');

    // ステップ6: メンテナンス完了処理
    console.log('=== ステップ6: メンテナンス完了処理 ===');
    await completeMaintenanceRecord(maintenance.id);
    console.log('');

    // ステップ7: メンテナンス完了後の予約を再試行
    console.log('=== ステップ7: メンテナンス完了後の予約再試行 ===');
    const bookingResult3 = await attemptBookingCreation(product, bookingStart, bookingEnd);

    if (bookingResult3.success) {
      console.log('✅ 期待通り: メンテナンス完了後は予約可能');
    } else {
      console.log('❌ 問題: メンテナンス完了後なのに予約不可');
      console.log(`理由: ${bookingResult3.reason}`);
    }
    console.log('');

    // テスト結果サマリー
    console.log('=== テスト結果サマリー ===');
    console.log(`商品: ${product.shopifyProduct.title} (${shopifyId})`);
    console.log(`メンテナンス期間: ${format(maintenanceStart, 'yyyy-MM-dd')} 〜 ${format(maintenanceEnd, 'yyyy-MM-dd')}`);
    console.log(`メンテナンス中の予約: ${!bookingResult1.success ? '✅ 正常に拒否' : '❌ 異常に許可'}`);
    console.log(`メンテナンス期間外の予約: ${bookingResult2.success ? '✅ 正常に許可' : '❌ 異常に拒否'}`);
    console.log(`メンテナンス完了後の予約: ${bookingResult3.success ? '✅ 正常に許可' : '❌ 異常に拒否'}`);

    const allTestsPassed = !bookingResult1.success && bookingResult2.success && bookingResult3.success;
    console.log(`総合結果: ${allTestsPassed ? '✅ 全テスト合格' : '❌ テスト失敗'}`);

  } catch (error) {
    console.error('❌ テスト実行中にエラーが発生:', error);
  } finally {
    // クリーンアップ
    if (maintenanceId) {
      console.log('');
      console.log('=== クリーンアップ ===');
      await cleanupTestData(maintenanceId);
    }

    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
