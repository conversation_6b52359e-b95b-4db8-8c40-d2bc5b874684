import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2025-01';

// GraphQLクライアントの設定
const client = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品情報
const OLD_PRODUCT_ID = "gid://shopify/Product/8982368747688";
const PRODUCT_DATA = {
  title: "14_●ﾊｰｳﾞｪｲﾁｪｱ (ﾎﾜｲﾄｼｰﾄ) 本体のみ [１]",
  handle: "10301009-001-chair",
  vendor: "IZIZ RENTAL",
  productType: "椅子",
  tags: ["商品番号:10301009", "場所:NY", "未指定Re;_20080328", "椅子", "詳細:001"],
  productCode: "10301009",
  detailCode: "001",
  categoryName: "椅子",
  kana: "ハーヴェイチェア",
  basePrice: 700,
  extraDayRate: 350,
  quantity: 1,
  width: 450,
  depth: 580,
  height: 940,
  purchasePrice: 30000
};

const BASE_SKU = "10301009-001";

// バリアントマッピング定義
const VARIANT_MAPPING = {
  "1D": { price: 700, deposit: 0 },
  "2D": { price: 1050, deposit: 0 },
  "3D": { price: 1400, deposit: 0 },
  "4D": { price: 1750, deposit: 0 },
  "5D": { price: 2100, deposit: 0 },
  "6D": { price: 2450, deposit: 0 },
  "7D": { price: 2800, deposit: 0 },
  "8D+": { price: 2800, deposit: 0 },
  "PROV": { price: 70, deposit: 0 }
};

// レンタル期間のバリアント定義
const RENTAL_VARIANTS = [
  { title: "1日レンタル", suffix: "1D", days: 1 },
  { title: "2日レンタル", suffix: "2D", days: 2 },
  { title: "3日レンタル", suffix: "3D", days: 3 },
  { title: "4日レンタル", suffix: "4D", days: 4 },
  { title: "5日レンタル", suffix: "5D", days: 5 },
  { title: "6日レンタル", suffix: "6D", days: 6 },
  { title: "7日レンタル", suffix: "7D", days: 7 },
  { title: "8日以上レンタル", suffix: "8D+", days: 8 },
  { title: "仮予約", suffix: "PROV", days: 0 }
];

// ロケーションを取得
async function getLocations() {
  const query = gql`
    query GetLocations {
      locations(first: 10) {
        edges {
          node {
            id
            name
            isActive
          }
        }
      }
    }
  `;

  const data = await client.request(query);
  return data.locations.edges.map(edge => edge.node).filter(loc => loc.isActive);
}

async function deleteAndRecreateProduct() {
  try {
    console.log('=== 商品の削除と再作成開始 ===\n');

    // 1. 既存の商品を削除
    console.log('1. 既存の商品を削除中...');
    const deleteProductMutation = gql`
      mutation deleteProduct($input: ProductDeleteInput!) {
        productDelete(input: $input) {
          deletedProductId
          userErrors {
            field
            message
          }
        }
      }
    `;

    const deleteResult = await client.request(deleteProductMutation, {
      input: { id: OLD_PRODUCT_ID }
    });

    if (deleteResult.productDelete.userErrors.length > 0) {
      console.error('商品削除エラー:', deleteResult.productDelete.userErrors);
    } else {
      console.log('✓ 商品削除完了');
    }

    // 少し待機
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 2. ロケーションを取得
    console.log('\n2. ロケーション情報を取得中...');
    const locations = await getLocations();
    const nyLocation = locations.find(loc => loc.name === 'NY');
    
    if (!nyLocation) {
      throw new Error('NYロケーションが見つかりません');
    }
    console.log(`✓ NYロケーション: ${nyLocation.id}`);

    // 3. 新しい商品を作成
    console.log('\n3. 新しい商品を作成中...');
    const createProductMutation = gql`
      mutation createProduct($input: ProductInput!) {
        productCreate(input: $input) {
          product {
            id
            title
            handle
            variants(first: 100) {
              edges {
                node {
                  id
                  title
                  sku
                  price
                  inventoryItem {
                    id
                  }
                }
              }
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    // バリアント情報を準備
    const variants = RENTAL_VARIANTS.map(variant => ({
      sku: `${BASE_SKU}-${variant.suffix}`,
      price: VARIANT_MAPPING[variant.suffix].price,
      options: [variant.title],
      inventoryPolicy: "DENY",
      inventoryQuantities: [{
        availableQuantity: PRODUCT_DATA.quantity,
        locationId: nyLocation.id
      }]
    }));

    const productInput = {
      title: PRODUCT_DATA.title,
      handle: PRODUCT_DATA.handle,
      vendor: PRODUCT_DATA.vendor,
      productType: PRODUCT_DATA.productType,
      tags: PRODUCT_DATA.tags,
      status: "ACTIVE",
      productOptions: [{ name: "レンタル期間", values: RENTAL_VARIANTS.map(v => v.title) }],
      variants: variants
    };

    const createResult = await client.request(createProductMutation, { input: productInput });

    if (createResult.productCreate.userErrors.length > 0) {
      console.error('商品作成エラー:', createResult.productCreate.userErrors);
      return;
    }

    const newProduct = createResult.productCreate.product;
    console.log(`✓ 商品作成完了: ${newProduct.id}`);
    console.log(`  タイトル: ${newProduct.title}`);
    console.log(`  ハンドル: ${newProduct.handle}`);
    console.log(`  バリアント数: ${newProduct.variants.edges.length}`);

    // バリアントIDをマッピング
    const variantIds = {};
    newProduct.variants.edges.forEach(edge => {
      const variant = edge.node;
      const suffix = variant.sku.split('-').pop();
      variantIds[suffix] = variant.id;
      console.log(`  - ${variant.title}: ¥${variant.price} (SKU: ${variant.sku})`);
    });

    // 4. メタフィールドを設定
    console.log('\n4. メタフィールドを設定中...');
    const metafieldsSetMutation = gql`
      mutation setMetafields($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          metafields {
            id
            namespace
            key
            value
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const metafields = [
      {
        ownerId: newProduct.id,
        namespace: "rental",
        key: "basic_info",
        value: JSON.stringify({
          product_code: PRODUCT_DATA.productCode,
          detail_code: PRODUCT_DATA.detailCode,
          category_name: PRODUCT_DATA.categoryName,
          kana: PRODUCT_DATA.kana
        }),
        type: "json"
      },
      {
        ownerId: newProduct.id,
        namespace: "rental",
        key: "pricing",
        value: JSON.stringify({
          base_price: PRODUCT_DATA.basePrice,
          extra_day_rate: PRODUCT_DATA.extraDayRate
        }),
        type: "json"
      },
      {
        ownerId: newProduct.id,
        namespace: "rental",
        key: "variant_mapping",
        value: JSON.stringify(Object.entries(variantIds).map(([suffix, id]) => ({
          variant_id: id,
          rental_period: suffix,
          days: RENTAL_VARIANTS.find(v => v.suffix === suffix)?.days || 0
        }))),
        type: "json"
      },
      {
        ownerId: newProduct.id,
        namespace: "rental",
        key: "status",
        value: JSON.stringify({
          enabled: true,
          maintenance_notes: ""
        }),
        type: "json"
      },
      {
        ownerId: newProduct.id,
        namespace: "custom",
        key: "width",
        value: PRODUCT_DATA.width.toString(),
        type: "single_line_text_field"
      },
      {
        ownerId: newProduct.id,
        namespace: "custom",
        key: "depth",
        value: PRODUCT_DATA.depth.toString(),
        type: "single_line_text_field"
      },
      {
        ownerId: newProduct.id,
        namespace: "custom",
        key: "height",
        value: PRODUCT_DATA.height.toString(),
        type: "single_line_text_field"
      },
      {
        ownerId: newProduct.id,
        namespace: "custom",
        key: "purchase_price",
        value: PRODUCT_DATA.purchasePrice.toString(),
        type: "single_line_text_field"
      },
      {
        ownerId: newProduct.id,
        namespace: "custom",
        key: "variation_type",
        value: "rental_period",
        type: "single_line_text_field"
      },
      {
        ownerId: newProduct.id,
        namespace: "custom",
        key: "booking_type",
        value: "日単位",
        type: "single_line_text_field"
      },
      {
        ownerId: newProduct.id,
        namespace: "custom",
        key: "booking_enabled",
        value: "true",
        type: "single_line_text_field"
      }
    ];

    const metafieldsResult = await client.request(metafieldsSetMutation, { metafields });
    
    if (metafieldsResult.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', metafieldsResult.metafieldsSet.userErrors);
    } else {
      console.log('✓ メタフィールド設定完了');
      console.log(`  設定されたメタフィールド数: ${metafieldsResult.metafieldsSet.metafields.length}`);
    }

    // 5. 在庫追跡を有効化
    console.log('\n5. 在庫追跡を有効化中...');
    for (const edge of newProduct.variants.edges) {
      const variant = edge.node;
      const inventoryItemId = variant.inventoryItem.id;

      const enableInventoryMutation = gql`
        mutation enableInventory($inventoryItemId: ID!, $input: InventoryItemInput!) {
          inventoryItemUpdate(id: $inventoryItemId, input: $input) {
            inventoryItem {
              id
              tracked
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      await client.request(enableInventoryMutation, {
        inventoryItemId: inventoryItemId,
        input: { tracked: true }
      });
    }
    console.log('✓ 在庫追跡有効化完了');

    console.log('\n=== 商品の削除と再作成完了 ===');
    console.log(`新しい商品ID: ${newProduct.id}`);
    console.log(`商品URL: https://admin.shopify.com/store/peaces-test-block/products/${newProduct.id.split('/').pop()}`);
    
  } catch (error) {
    console.error('エラーが発生しました:', error);
    if (error.response && error.response.errors) {
      console.error('GraphQLエラー:', JSON.stringify(error.response.errors, null, 2));
    }
  }
}

deleteAndRecreateProduct();