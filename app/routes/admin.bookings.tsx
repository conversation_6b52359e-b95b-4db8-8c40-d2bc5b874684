/**
 * 予約管理画面
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, Outlet, useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Tabs,
  LegacyStack,
  Button
} from "@shopify/polaris";
import { useState, useCallback } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import BookingList from "../components/BookingList";

/**
 * ローダー関数
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin } = await authenticate.admin(request);

  // 処理待ちの変更リクエスト数を取得
  const pendingUpgradeRequestsCount = await prisma.bookingUpgradeRequest.count({
    where: { status: 'PENDING' }
  });

  return json({
    shop: admin.shop,
    pendingUpgradeRequestsCount
  });
}

/**
 * 予約管理画面コンポーネント
 */
export default function BookingsPage() {
  const { pendingUpgradeRequestsCount } = useLoaderData<typeof loader>();
  const [selected, setSelected] = useState(0);

  const handleTabChange = useCallback(
    (selectedTabIndex: number) => setSelected(selectedTabIndex),
    [],
  );

  const tabs = [
    {
      id: 'all-bookings',
      content: '全ての予約',
      accessibilityLabel: '全ての予約',
      panelID: 'all-bookings-panel',
    },
    {
      id: 'provisional-bookings',
      content: '仮予約',
      accessibilityLabel: '仮予約',
      panelID: 'provisional-bookings-panel',
    },
    {
      id: 'confirmed-bookings',
      content: '本予約',
      accessibilityLabel: '本予約',
      panelID: 'confirmed-bookings-panel',
    },
  ];

  return (
    <Page
      title="予約管理"
      primaryAction={{ content: '新規予約', url: '/admin/bookings/new' }}
    >
      <Layout>
        <Layout.Section>
          <LegacyStack distribution="trailing">
            <Button
              url="/admin/bookings/upgrade-requests"
              monochrome
              outline
            >
              変更リクエスト
              {pendingUpgradeRequestsCount > 0 && (
                <span style={{
                  marginLeft: '0.5rem',
                  background: '#bf0711',
                  color: 'white',
                  borderRadius: '50%',
                  padding: '0.2rem 0.5rem'
                }}>
                  {pendingUpgradeRequestsCount}
                </span>
              )}
            </Button>
          </LegacyStack>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <Tabs tabs={tabs} selected={selected} onSelect={handleTabChange}>
              <Card.Section>
                {selected === 0 && (
                  <BookingList type="all" />
                )}
                {selected === 1 && (
                  <BookingList type="provisional" />
                )}
                {selected === 2 && (
                  <BookingList type="confirmed" />
                )}
              </Card.Section>
            </Tabs>
          </Card>
        </Layout.Section>
      </Layout>

      <Outlet />
    </Page>
  );
}
