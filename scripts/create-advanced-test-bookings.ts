/**
 * 高度なテストケース用の予約データ作成スクリプト
 *
 * このスクリプトは、以下の高度なテストケース用のデータを作成します：
 * 1. 長期間の予約（30日以上）
 * 2. 予約の日程変更（変更履歴付き）
 * 3. 複数商品の同時予約（同一顧客）
 * 4. 在庫状態の変更（メンテナンス中の商品）
 * 5. 予約の延長処理
 *
 * 使用方法:
 * npx tsx scripts/create-advanced-test-bookings.ts
 */

import { PrismaClient, BookingStatus, BookingType, PaymentMethod, PaymentStatus } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// ショップドメイン
const SHOP = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';

// 現在の日付
const NOW = new Date();

// 日付ユーティリティ関数
const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

const subtractDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() - days);
  return result;
};

// 商品を取得するクエリ
const GET_PRODUCTS = gql`
  query getProducts($first: Int!) {
    products(first: $first) {
      edges {
        node {
          id
          title
          variants(first: 10) {
            edges {
              node {
                id
                title
                price
                inventoryQuantity
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
              }
            }
          }
        }
      }
    }
  }
`;

// 商品のメタフィールドを更新するミューテーション
const UPDATE_PRODUCT_METAFIELD = gql`
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 顧客を取得するクエリ
const GET_CUSTOMERS = gql`
  query getCustomers($first: Int!) {
    customers(first: $first) {
      edges {
        node {
          id
          firstName
          lastName
          email
          phone
        }
      }
    }
  }
`;

/**
 * 商品データを取得する
 */
async function getProducts() {
  console.log('商品データを取得中...');

  try {
    const result = await client.request(GET_PRODUCTS, {
      first: 10
    });

    const products = result.products.edges.map((edge: any) => {
      const product = edge.node;
      const variants = product.variants.edges.map((variantEdge: any) => variantEdge.node);

      // メタフィールドを解析
      const metafields: any = {};
      product.metafields.edges.forEach((metafieldEdge: any) => {
        const metafield = metafieldEdge.node;
        if (!metafields[metafield.namespace]) {
          metafields[metafield.namespace] = {};
        }

        try {
          metafields[metafield.namespace][metafield.key] = JSON.parse(metafield.value);
        } catch (e) {
          metafields[metafield.namespace][metafield.key] = metafield.value;
        }
      });

      return {
        ...product,
        variants,
        metafields
      };
    });

    if (products.length === 0) {
      throw new Error('商品データが見つかりません。先に商品データを作成してください。');
    }

    console.log(`${products.length}件の商品が見つかりました`);
    return products;
  } catch (error) {
    console.error('商品データの取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 顧客データを取得する
 */
async function getCustomers() {
  console.log('顧客データを取得中...');

  try {
    const result = await client.request(GET_CUSTOMERS, {
      first: 10
    });

    const customers = result.customers.edges.map((edge: any) => edge.node);

    if (customers.length === 0) {
      throw new Error('顧客データが見つかりません。先に顧客データを作成してください。');
    }

    console.log(`${customers.length}件の顧客が見つかりました`);
    return customers;
  } catch (error) {
    console.error('顧客データの取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 商品のステータスを更新する
 */
async function updateProductStatus(product: any, status: string) {
  try {
    console.log(`商品「${product.title}」のステータスを「${status}」に更新中...`);

    const result = await client.request(UPDATE_PRODUCT_METAFIELD, {
      metafields: [
        {
          ownerId: product.id,
          namespace: "rental",
          key: "status",
          value: status,
          type: "single_line_text_field"
        }
      ]
    });

    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('商品ステータス更新中にエラーが発生しました:', result.metafieldsSet.userErrors);
      return false;
    }

    console.log(`商品「${product.title}」のステータスを「${status}」に更新しました`);
    return true;
  } catch (error) {
    console.error('商品ステータス更新中にエラーが発生しました:', error);
    return false;
  }
}

/**
 * Prismaに予約データを作成する
 */
async function createPrismaBooking(
  product: any,
  customer: any,
  startDate: Date,
  endDate: Date,
  status: BookingStatus,
  bookingType: BookingType,
  notes: string = '',
  changeHistory: string = ''
) {
  try {
    const customerName = `${customer.firstName} ${customer.lastName}`;
    const customerId = customer.id.replace('gid://shopify/Customer/', '');

    // レンタル日数を計算
    const days = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    // 基本料金（1日目）
    const basePrice = product.variants[0].price ? parseFloat(product.variants[0].price) : 5000;

    // 料金計算
    let totalAmount = Math.min(basePrice, 5000); // 最大5000円に制限
    if (days > 1) {
      const day2to6Count = Math.min(days - 1, 5);
      totalAmount += totalAmount * 0.2 * day2to6Count;
    }
    if (days > 6) {
      const day7PlusCount = days - 6;
      totalAmount += totalAmount * 0.1 * day7PlusCount;
    }

    // デポジット金額（10%）
    const depositAmount = Math.round(totalAmount * 0.1);

    // 変更履歴をmetadataに保存
    const metadata = changeHistory ? { changeHistory: JSON.parse(changeHistory) } : undefined;

    // 予約データを作成
    const booking = await prisma.booking.create({
      data: {
        productId: product.id,
        startDate,
        endDate,
        customerId,
        customerEmail: customer.email,
        customerName,
        customerPhone: customer.phone || '',
        totalAmount: totalAmount.toString(),
        depositAmount: depositAmount.toString(),
        depositPaid: status !== BookingStatus.CANCELLED,
        bookingId: `BK-${uuidv4().substring(0, 8).toUpperCase()}`,
        shop: SHOP,
        bookingType,
        status,
        paymentStatus: status === BookingStatus.CANCELLED ? PaymentStatus.REFUNDED :
                      (status === BookingStatus.PROVISIONAL ? PaymentStatus.PARTIALLY_PAID : PaymentStatus.COMPLETED),
        paymentMethod: status === BookingStatus.CANCELLED ? null : PaymentMethod.CREDIT_CARD,
        paymentDate: status === BookingStatus.CANCELLED ? null : new Date(),
        notes: notes || `${customerName}様の${bookingType === BookingType.PROVISIONAL ? '仮予約' : '本予約'}`,
        metadata,
        orderId: null,
        orderName: null,
      }
    });

    console.log(`予約を作成しました: ${booking.bookingId} (${status})`);
    return booking;
  } catch (error) {
    console.error('Prisma予約作成中にエラーが発生しました:', error);
    return null;
  }
}

/**
 * メインの実行関数
 */
async function main() {
  try {
    console.log('高度なテストケース用の予約データの作成を開始します...');

    // 1. 商品データを取得
    const products = await getProducts();

    // 2. 顧客データを取得
    const customers = await getCustomers();

    // 3. 既存の高度なテスト予約データを削除
    const deletedBookings = await prisma.booking.deleteMany({
      where: {
        shop: SHOP,
        notes: {
          contains: '[高度なテスト]'
        }
      }
    });
    console.log(`${deletedBookings.count}件の既存高度なテスト予約を削除しました`);

    // 4. 高度なテスト予約データを作成
    const bookings = [];

    // 4.1 長期間の予約（40日間）
    const longTermStartDate = addDays(NOW, 60);
    const longTermEndDate = addDays(NOW, 100); // 40日間

    const longTermBooking = await createPrismaBooking(
      products[0],
      customers[0],
      longTermStartDate,
      longTermEndDate,
      BookingStatus.CONFIRMED,
      BookingType.CONFIRMED,
      `[高度なテスト] ${customers[0].firstName} ${customers[0].lastName}様の長期予約（40日間）`
    );
    bookings.push(longTermBooking);

    // 4.2 予約の日程変更（変更履歴付き）
    const originalStartDate = addDays(NOW, 30);
    const originalEndDate = addDays(NOW, 33);
    const newStartDate = addDays(NOW, 35);
    const newEndDate = addDays(NOW, 38);

    const changeHistory = JSON.stringify([
      {
        date: subtractDays(NOW, 5).toISOString(),
        type: 'DATE_CHANGE',
        oldValue: {
          startDate: originalStartDate.toISOString(),
          endDate: originalEndDate.toISOString()
        },
        newValue: {
          startDate: newStartDate.toISOString(),
          endDate: newEndDate.toISOString()
        },
        reason: '顧客の都合による日程変更',
        operator: 'admin'
      }
    ]);

    const dateChangedBooking = await createPrismaBooking(
      products[1],
      customers[1],
      newStartDate,
      newEndDate,
      BookingStatus.CONFIRMED,
      BookingType.CONFIRMED,
      `[高度なテスト] ${customers[1].firstName} ${customers[1].lastName}様の日程変更予約`,
      changeHistory
    );
    bookings.push(dateChangedBooking);

    // 4.3 複数商品の同時予約（同一顧客）
    const multipleItemsStartDate = addDays(NOW, 45);
    const multipleItemsEndDate = addDays(NOW, 48);

    // 1つ目の商品予約
    const multipleItems1Booking = await createPrismaBooking(
      products[2],
      customers[2],
      multipleItemsStartDate,
      multipleItemsEndDate,
      BookingStatus.CONFIRMED,
      BookingType.CONFIRMED,
      `[高度なテスト] ${customers[2].firstName} ${customers[2].lastName}様の複数商品予約（商品1）`
    );
    bookings.push(multipleItems1Booking);

    // 2つ目の商品予約（同じ顧客、同じ期間）
    const multipleItems2Booking = await createPrismaBooking(
      products[3],
      customers[2],
      multipleItemsStartDate,
      multipleItemsEndDate,
      BookingStatus.CONFIRMED,
      BookingType.CONFIRMED,
      `[高度なテスト] ${customers[2].firstName} ${customers[2].lastName}様の複数商品予約（商品2）`
    );
    bookings.push(multipleItems2Booking);

    // 4.4 在庫状態の変更（メンテナンス中の商品）
    // 商品のステータスを「maintenance」に更新
    await updateProductStatus(products[4], 'maintenance');

    const maintenanceItemBooking = await createPrismaBooking(
      products[4],
      customers[0],
      addDays(NOW, 50),
      addDays(NOW, 52),
      BookingStatus.CONFIRMED,
      BookingType.CONFIRMED,
      `[高度なテスト] メンテナンス中の商品の予約（この予約は表示されるべきだが、新規予約はできないはず）`
    );
    bookings.push(maintenanceItemBooking);

    // 4.5 予約の延長処理
    const originalExtensionStartDate = addDays(NOW, 20);
    const originalExtensionEndDate = addDays(NOW, 22);
    const newExtensionEndDate = addDays(NOW, 25); // 3日延長

    const extensionHistory = JSON.stringify([
      {
        date: subtractDays(NOW, 2).toISOString(),
        type: 'EXTENSION',
        oldValue: {
          endDate: originalExtensionEndDate.toISOString()
        },
        newValue: {
          endDate: newExtensionEndDate.toISOString()
        },
        additionalFee: '3000', // 追加料金
        reason: '顧客の要望による延長',
        operator: 'admin'
      }
    ]);

    const extensionBooking = await createPrismaBooking(
      products[5],
      customers[0],
      originalExtensionStartDate,
      newExtensionEndDate,
      BookingStatus.CONFIRMED,
      BookingType.CONFIRMED,
      `[高度なテスト] ${customers[0].firstName} ${customers[0].lastName}様の予約延長（3日間延長）`,
      extensionHistory
    );
    bookings.push(extensionBooking);

    console.log('\n=== 高度なテスト予約データ作成結果 ===');
    console.log(`作成した予約: ${bookings.filter(b => b !== null).length}件`);
    console.log('=============================\n');

    console.log('高度なテスト予約データの作成が完了しました');
  } catch (error) {
    console.error('テスト予約データ作成中にエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('予期しないエラーが発生しました:', error);
    process.exit(1);
  });
