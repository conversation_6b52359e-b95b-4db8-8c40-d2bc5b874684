#!/usr/bin/env node

/**
 * カテゴリシステムの動作確認スクリプト
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testCategorySystem() {
  console.log('=== カテゴリシステム動作確認 ===');
  
  const shop = 'ease-next-temp.myshopify.com';
  
  try {
    // 1. カテゴリマスタ件数確認
    const categoryCount = await prisma.categoryMaster.count({
      where: { shop }
    });
    console.log(`カテゴリマスタ件数: ${categoryCount}件`);
    
    // 2. サブカテゴリマスタ件数確認
    const subCategoryCount = await prisma.subCategoryMaster.count({
      where: { shop }
    });
    console.log(`サブカテゴリマスタ件数: ${subCategoryCount}件`);
    
    // 3. 上位5カテゴリを表示
    const topCategories = await prisma.categoryMaster.findMany({
      where: { shop },
      take: 5,
      orderBy: { code: 'asc' },
      include: {
        subCategories: {
          take: 3,
          orderBy: { code: 'asc' }
        }
      }
    });
    
    console.log('\n=== 上位5カテゴリ ===');
    topCategories.forEach(category => {
      console.log(`${category.code}: ${category.name}`);
      category.subCategories.forEach(sub => {
        console.log(`  └ ${sub.code}: ${sub.name}`);
      });
    });
    
    // 4. SKU形式のテスト
    console.log('\n=== SKU形式テスト ===');
    const testSKUs = ['212-05-023', '201-10-001', '999-99-999'];
    
    for (const sku of testSKUs) {
      const pattern = /^(\d{3})-(\d{2})-(\d{3})$/;
      const match = sku.match(pattern);
      
      if (match) {
        const [, categoryCode, subCategoryCode] = match;
        
        const category = await prisma.categoryMaster.findUnique({
          where: { shop_code: { shop, code: categoryCode } }
        });
        
        const subCategory = await prisma.subCategoryMaster.findUnique({
          where: {
            shop_code_parentCategoryCode: {
              shop,
              code: subCategoryCode,
              parentCategoryCode: categoryCode
            }
          }
        });
        
        console.log(`SKU: ${sku}`);
        console.log(`  カテゴリ: ${category ? category.name : '見つからない'}`);
        console.log(`  サブカテゴリ: ${subCategory ? subCategory.name : '見つからない'}`);
      } else {
        console.log(`SKU: ${sku} - 形式エラー`);
      }
    }
    
    console.log('\n=== 動作確認完了 ===');
    
  } catch (error) {
    console.error('エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testCategorySystem();
