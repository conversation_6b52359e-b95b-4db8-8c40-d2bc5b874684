{"timestamp": "2025-05-19T07:23:59.194Z", "duration": 2.683, "totalTests": 3, "successCount": 0, "failureCount": 3, "tests": [{"testName": "予約リクエスト #0", "success": false, "timestamp": "2025-05-19T07:24:01.878Z", "message": "予約リクエストが失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-05-26", "duration": 1021, "details": {"requestId": 0, "bookingId": ""}}, {"testName": "予約リクエスト #1", "success": false, "timestamp": "2025-05-19T07:24:01.878Z", "message": "予約リクエストが失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-05, 2025-06-06, 2025-06-07", "duration": 1533, "details": {"requestId": 1, "bookingId": ""}}, {"testName": "予約リクエスト #2", "success": false, "timestamp": "2025-05-19T07:24:01.878Z", "message": "予約リクエストが失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-15, 2025-06-16, 2025-06-17", "duration": 1547, "details": {"requestId": 2, "bookingId": ""}}], "environment": {"concurrentRequests": 3, "nodeVersion": "v20.18.1", "os": "darwin arm64", "cpuCores": 10}, "performance": {"averageTestDuration": 1367, "slowestTest": {"name": "リクエスト #2", "duration": 1547}, "fastestTest": {"name": "リクエスト #0", "duration": 1021}, "throughput": 1.1181513231457325, "successRate": 0}}