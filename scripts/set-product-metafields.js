const dotenv = require('dotenv');
const { GraphQLClient, gql } = require('graphql-request');

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品検索のクエリ
const GET_PRODUCTS_QUERY = gql`
  query getProducts($query: String!, $first: Int!, $after: String) {
    products(first: $first, after: $after, query: $query) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          handle
          vendor
          productType
          tags
          variants(first: 50) {
            edges {
              node {
                id
                title
                sku
                price
                inventoryQuantity
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    }
  }
`;

// メタフィールドの設定
async function setJsonMetafield(productId, namespace, key, jsonValue) {
  const mutation = gql`
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    metafields: [
      {
        ownerId: productId,
        namespace,
        key,
        value: JSON.stringify(jsonValue),
        type: 'json',
      }
    ]
  };

  try {
    const result = await graphQLClient.request(mutation, variables);
    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', result.metafieldsSet.userErrors);
      return false;
    }
    return true;
  } catch (error) {
    console.error(`メタフィールド ${namespace}.${key} 設定エラー:`, error);
    return false;
  }
}

// 商品を検索する関数
async function getProducts(query = '', limit = 50) {
  const products = [];
  let hasNextPage = true;
  let cursor = null;

  while (hasNextPage) {
    try {
      const variables = {
        query,
        first: limit,
        after: cursor
      };

      const result = await graphQLClient.request(GET_PRODUCTS_QUERY, variables);

      const edges = result.products.edges;
      products.push(...edges.map(edge => edge.node));

      hasNextPage = result.products.pageInfo.hasNextPage;
      cursor = result.products.pageInfo.endCursor;
    } catch (error) {
      console.error('商品検索エラー:', error);
      hasNextPage = false;
    }
  }

  return products;
}

// CSVファイルを読み込む関数は使用しないため削除

// SKUから商品コードと詳細コードを抽出する関数
function extractCodesFromSKU(sku) {
  // 例: 10101007-001 -> { productCode: "10101007", detailCode: "001" }
  const match = sku.match(/^(\d+)-(\d+)/);
  if (match) {
    return {
      productCode: match[1],
      detailCode: match[2]
    };
  }
  return { productCode: "", detailCode: "" };
}

// 商品タイトルからカナを抽出する関数
function extractKana(title) {
  // 半角カタカナを全角カタカナに変換
  const hankakuToZenkaku = {
    'ｱ': 'ア', 'ｲ': 'イ', 'ｳ': 'ウ', 'ｴ': 'エ', 'ｵ': 'オ',
    'ｶ': 'カ', 'ｷ': 'キ', 'ｸ': 'ク', 'ｹ': 'ケ', 'ｺ': 'コ',
    'ｻ': 'サ', 'ｼ': 'シ', 'ｽ': 'ス', 'ｾ': 'セ', 'ｿ': 'ソ',
    'ﾀ': 'タ', 'ﾁ': 'チ', 'ﾂ': 'ツ', 'ﾃ': 'テ', 'ﾄ': 'ト',
    'ﾅ': 'ナ', 'ﾆ': 'ニ', 'ﾇ': 'ヌ', 'ﾈ': 'ネ', 'ﾉ': 'ノ',
    'ﾊ': 'ハ', 'ﾋ': 'ヒ', 'ﾌ': 'フ', 'ﾍ': 'ヘ', 'ﾎ': 'ホ',
    'ﾏ': 'マ', 'ﾐ': 'ミ', 'ﾑ': 'ム', 'ﾒ': 'メ', 'ﾓ': 'モ',
    'ﾔ': 'ヤ', 'ﾕ': 'ユ', 'ﾖ': 'ヨ',
    'ﾗ': 'ラ', 'ﾘ': 'リ', 'ﾙ': 'ル', 'ﾚ': 'レ', 'ﾛ': 'ロ',
    'ﾜ': 'ワ', 'ｦ': 'ヲ', 'ﾝ': 'ン',
    'ｧ': 'ァ', 'ｨ': 'ィ', 'ｩ': 'ゥ', 'ｪ': 'ェ', 'ｫ': 'ォ',
    'ｯ': 'ッ', 'ｬ': 'ャ', 'ｭ': 'ュ', 'ｮ': 'ョ',
    'ｰ': 'ー', '･': '・', '､': '、', '｡': '。',
    '｢': '「', '｣': '」', '(': '（', ')': '）',
    '!': '！', '?': '？', '[': '［', ']': '］'
  };

  // タイトルから半角カタカナを抽出
  const kanaPattern = /[ｱ-ﾝｧ-ｮｰ]+/g;
  const matches = title.match(kanaPattern);

  if (!matches) {
    // 半角カタカナがない場合は、タイトルをそのまま返す
    return title.replace(/[【】]/g, '').trim();
  }

  // 半角カタカナを全角カタカナに変換
  let kana = '';
  for (const match of matches) {
    let converted = '';
    for (let i = 0; i < match.length; i++) {
      const char = match[i];
      converted += hankakuToZenkaku[char] || char;
    }
    kana += converted;
  }

  // スペースを削除
  return kana.replace(/\s+/g, '');
}

// 商品の基本情報メタフィールドを生成する関数
function generateBasicInfo(product, variant) {
  const sku = variant.sku;
  const { productCode, detailCode } = extractCodesFromSKU(sku);
  const kana = extractKana(product.title);

  // 商品タイプからサイズ情報を抽出
  const dimensions = { width: 0, depth: 0, height: 0 };
  const seatDimensions = { width: 0, depth: 0, height: 0 };

  // タイトルから色を抽出
  let color = '';
  const colorMatch = product.title.match(/(オフホワイト|ベージュ|ブラック|ブラウン|グリーン|アンティークグリーン|モケットグリーン|レッド|ブルー|イエロー|グレー|ゴールド|シルバー)/);
  if (colorMatch) {
    color = colorMatch[1];
  }

  // タイトルから素材を抽出
  let material = '';
  const materialMatch = product.title.match(/(ファブリック|レザー|モケット|ウッド|メタル)/);
  if (materialMatch) {
    material = materialMatch[1];
  }

  return {
    productCode,
    detailCode,
    kana,
    dimensions,
    seatDimensions,
    material,
    color,
    maker: product.vendor || '不明',
    campaign: '通常商品',
    notes: ''
  };
}

// 商品の料金設定メタフィールドを生成する関数
function generatePricing(product, variant) {
  const basePrice = parseInt(variant.price);

  return {
    basePrice,
    depositRate: 0.1,
    discountRules: {
      day2_6_rate: 0.2,
      day7_plus_rate: 0.1
    },
    minimumDays: 1,
    maximumDays: 30
  };
}

// 商品の在庫アイテム情報メタフィールドを生成する関数
function generateInventoryItems(product, variants) {
  const inventoryItems = [];

  // 商品タイトルから状況を判断
  const title = product.title.toLowerCase();

  // 基本バリアントを取得
  const baseVariant = variants.find(v => v.title.includes('1日') || v.title === 'Default Title');
  if (!baseVariant) return inventoryItems;

  const sku = baseVariant.sku;
  const { productCode, detailCode } = extractCodesFromSKU(sku);

  // 商品ごとに異なる在庫アイテムを生成
  if (title.includes('ベーシックソファ')) {
    // 2台の在庫アイテム（1台目は利用可能、2台目はメンテナンス中）
    inventoryItems.push({
      id: `item-${productCode}-${detailCode}-1`,
      sku: `${sku}-1`,
      status: 'available',
      location: 'NY',
      notes: '背面向かって右側うっすら黒いしみ'
    });

    inventoryItems.push({
      id: `item-${productCode}-${detailCode}-2`,
      sku: `${sku}-2`,
      status: 'maintenance',
      location: 'NY',
      notes: '向かって左アーム手前と正面左側に黄色い輪染み有'
    });
  } else if (title.includes('ウイングソファ')) {
    // 1台の在庫アイテム
    inventoryItems.push({
      id: `item-${productCode}-${detailCode}-1`,
      sku: `${sku}-1`,
      status: 'available',
      location: 'NY',
      notes: '特になし'
    });
  } else if (title.includes('カリモクソファ')) {
    // 2台の在庫アイテム（1台目は利用可能、2台目は破損状態）
    inventoryItems.push({
      id: `item-${productCode}-${detailCode}-1`,
      sku: `${sku}-1`,
      status: 'available',
      location: 'NY',
      notes: '特になし'
    });

    inventoryItems.push({
      id: `item-${productCode}-${detailCode}-2`,
      sku: `${sku}-2`,
      status: 'damaged',
      location: 'NY',
      notes: '座面に傷あり'
    });
  } else if (title.includes('プレーンソファ')) {
    // 3台の在庫アイテム（1台目は利用可能、2台目は利用可能、3台目は利用不可）
    inventoryItems.push({
      id: `item-${productCode}-${detailCode}-1`,
      sku: `${sku}-1`,
      status: 'available',
      location: 'NY',
      notes: '特になし'
    });

    inventoryItems.push({
      id: `item-${productCode}-${detailCode}-2`,
      sku: `${sku}-2`,
      status: 'available',
      location: 'NY',
      notes: '特になし'
    });

    inventoryItems.push({
      id: `item-${productCode}-${detailCode}-3`,
      sku: `${sku}-3`,
      status: 'unavailable',
      location: 'PR',
      notes: '長期貸出中'
    });
  } else {
    // その他の商品は1台の在庫アイテム
    inventoryItems.push({
      id: `item-${productCode}-${detailCode}-1`,
      sku: `${sku}-1`,
      status: 'available',
      location: 'NY',
      notes: ''
    });
  }

  return inventoryItems;
}

// 商品の予約情報メタフィールドを生成する関数
function generateReservationInfo(inventoryItems) {
  return inventoryItems.map(item => ({
    itemId: item.id,
    reservations: []
  }));
}

// メタフィールドを設定する関数
async function setProductMetafields(product) {
  const productId = product.id;
  const variants = product.variants.edges.map(edge => edge.node);

  // 1日レンタルまたはDefault Titleのバリアントを取得
  const baseVariant = variants.find(v =>
    v.title.includes('1日') || v.title === 'Default Title'
  );

  if (!baseVariant) {
    console.error(`商品 ${product.title} に基本バリアントが見つかりません`);
    return false;
  }

  // 基本情報メタフィールドを生成
  const basicInfo = generateBasicInfo(product, baseVariant);

  // 料金設定メタフィールドを生成
  const pricing = generatePricing(product, baseVariant);

  // 在庫アイテム情報メタフィールドを生成
  const inventoryItems = generateInventoryItems(product, variants);

  // 予約情報メタフィールドを生成
  const reservationInfo = generateReservationInfo(inventoryItems);

  // メタフィールドを設定
  console.log(`商品 ${product.title} のメタフィールドを設定中...`);

  // 基本情報を設定
  const basicInfoResult = await setJsonMetafield(
    productId,
    'rental',
    'basic_info',
    basicInfo
  );

  if (!basicInfoResult) {
    console.error(`商品 ${product.title} の基本情報メタフィールドの設定に失敗しました`);
  }

  // 料金設定を設定
  const pricingResult = await setJsonMetafield(
    productId,
    'rental',
    'pricing',
    pricing
  );

  if (!pricingResult) {
    console.error(`商品 ${product.title} の料金設定メタフィールドの設定に失敗しました`);
  }

  // 在庫アイテム情報を設定
  const inventoryItemsResult = await setJsonMetafield(
    productId,
    'rental',
    'inventory_items',
    inventoryItems
  );

  if (!inventoryItemsResult) {
    console.error(`商品 ${product.title} の在庫アイテム情報メタフィールドの設定に失敗しました`);
  }

  // 予約情報を設定
  const reservationInfoResult = await setJsonMetafield(
    productId,
    'rental',
    'reservation_info',
    reservationInfo
  );

  if (!reservationInfoResult) {
    console.error(`商品 ${product.title} の予約情報メタフィールドの設定に失敗しました`);
  }

  return basicInfoResult && pricingResult && inventoryItemsResult && reservationInfoResult;
}

// メイン処理
async function main() {
  try {
    // 商品を検索
    console.log('商品を検索中...');
    const products = await getProducts('title:【テスト】', 10);
    console.log(`${products.length}件の商品が見つかりました`);

    // 各商品のメタフィールドを設定
    let successCount = 0;
    for (const product of products) {
      console.log(`\n商品 ${product.title} の処理を開始...`);

      // メタフィールドが既に設定されているかチェック
      const hasInventoryItems = product.metafields.edges.some(
        edge => edge.node.namespace === 'rental' && edge.node.key === 'inventory_items'
      );

      if (hasInventoryItems) {
        console.log(`商品 ${product.title} には既に在庫アイテム情報が設定されています。スキップします。`);
        continue;
      }

      // メタフィールドを設定
      const success = await setProductMetafields(product);

      if (success) {
        console.log(`商品 ${product.title} のメタフィールド設定が完了しました`);
        successCount++;
      } else {
        console.error(`商品 ${product.title} のメタフィールド設定に失敗しました`);
      }
    }

    console.log(`\n処理が完了しました。${successCount}/${products.length}件の商品のメタフィールドを設定しました。`);
  } catch (error) {
    console.error('エラーが発生しました:', error);
  }
}

// 実行
main();
