#!/usr/bin/env node

/**
 * 既存システムのデータをShopifyとプリズマデータベースにインポートするスクリプト
 * 
 * 使用方法:
 * ```
 * node import-legacy-data.js --products=shopify-products.csv --metafields=shopify-metafields.csv
 * ```
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const { PrismaClient } = require('@prisma/client');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const { GraphQLClient, gql } = require('graphql-request');
const dotenv = require('dotenv');
const axios = require('axios');

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// コマンドライン引数を解析
const argv = yargs(hideBin(process.argv))
  .option('products', {
    alias: 'p',
    description: 'Shopify商品CSVファイルのパス',
    type: 'string',
    demandOption: true
  })
  .option('metafields', {
    alias: 'm',
    description: 'Shopifyメタフィールドのパス',
    type: 'string',
    demandOption: true
  })
  .option('strategy', {
    alias: 's',
    description: 'インポート戦略（shopify-only, db-only, both）',
    type: 'string',
    default: 'both'
  })
  .option('batch-size', {
    alias: 'b',
    description: '一度に処理する件数',
    type: 'number',
    default: 10
  })
  .help()
  .alias('help', 'h')
  .argv;

// Shopify APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2023-10/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// CSVファイルのパス
const productsFile = argv.products;
const metafieldsFile = argv.metafields;
const importStrategy = argv.strategy;
const batchSize = argv.batchSize;

// インポート進捗追跡用の変数
let totalProducts = 0;
let importedToShopify = 0;
let importedToDb = 0;
let errors = [];

/**
 * インポート処理のメイン関数
 */
async function importLegacyData() {
  console.log('データインポートを開始します');
  console.log(`商品ファイル: ${productsFile}`);
  console.log(`メタフィールドファイル: ${metafieldsFile}`);
  console.log(`インポート戦略: ${importStrategy}`);
  
  try {
    // CSVファイルが存在するか確認
    if (!fs.existsSync(productsFile)) {
      throw new Error(`商品ファイルが見つかりません: ${productsFile}`);
    }
    
    if (!fs.existsSync(metafieldsFile)) {
      throw new Error(`メタフィールドファイルが見つかりません: ${metafieldsFile}`);
    }
    
    // CSVからデータを読み込み
    const products = await readCsvFile(productsFile);
    const metafields = await readCsvFile(metafieldsFile);
    
    totalProducts = products.length;
    
    console.log(`商品データ: ${products.length}件`);
    console.log(`メタフィールドデータ: ${metafields.length}件`);
    
    // メタフィールドをハンドルごとにグループ化
    const metafieldsByHandle = groupMetafieldsByHandle(metafields);
    
    // バッチ処理用に商品を分割
    const batches = [];
    for (let i = 0; i < products.length; i += batchSize) {
      batches.push(products.slice(i, i + batchSize));
    }
    
    console.log(`${batches.length}バッチに分割して処理します（1バッチ${batchSize}件）`);
    
    // 各バッチを処理
    for (let i = 0; i < batches.length; i++) {
      console.log(`バッチ ${i + 1}/${batches.length} を処理中...`);
      
      const batch = batches[i];
      
      // バッチ内の商品を処理
      for (const product of batch) {
        const handle = product['Handle'];
        const productMetafields = metafieldsByHandle[handle] || [];
        
        try {
          // インポート戦略に基づいて処理
          if (importStrategy === 'both' || importStrategy === 'shopify-only') {
            const shopifyProduct = await importProductToShopify(product, productMetafields);
            if (shopifyProduct && shopifyProduct.id) {
              importedToShopify++;
              console.log(`Shopifyに商品をインポートしました: ${handle}`);
              
              // メタフィールドをインポート
              if (productMetafields.length > 0) {
                await importMetafieldsToShopify(shopifyProduct.id, productMetafields);
                console.log(`メタフィールドをインポートしました: ${handle}`);
              }
            }
          }
          
          if (importStrategy === 'both' || importStrategy === 'db-only') {
            // データベースにも商品情報を登録
            await importProductToDatabase(product, productMetafields);
            importedToDb++;
            console.log(`データベースに商品を登録しました: ${handle}`);
          }
        } catch (err) {
          console.error(`商品 ${handle} のインポート中にエラーが発生:`, err);
          errors.push({
            handle,
            error: err.message
          });
        }
      }
      
      // バッチ間にスリープを入れてAPIレート制限を回避
      if (i < batches.length - 1) {
        console.log('APIレート制限を回避するため5秒待機します...');
        await sleep(5000);
      }
    }
    
    console.log('\nインポート完了');
    console.log(`合計: ${totalProducts}件`);
    console.log(`Shopifyにインポート: ${importedToShopify}件`);
    console.log(`データベースに登録: ${importedToDb}件`);
    console.log(`エラー: ${errors.length}件`);
    
    if (errors.length > 0) {
      console.log('\nエラー一覧:');
      errors.forEach((err, index) => {
        console.log(`${index + 1}. ${err.handle}: ${err.error}`);
      });
      
      // エラーログを出力
      fs.writeFileSync(
        'import-errors.json',
        JSON.stringify(errors, null, 2),
        'utf8'
      );
      console.log('エラーログを import-errors.json に出力しました');
    }
  } catch (err) {
    console.error('インポート処理中にエラーが発生しました:', err);
    process.exit(1);
  } finally {
    // Prismaクライアントを終了
    await prisma.$disconnect();
  }
}

/**
 * CSVファイルを読み込み
 */
async function readCsvFile(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (err) => reject(err));
  });
}

/**
 * メタフィールドをハンドルごとにグループ化
 */
function groupMetafieldsByHandle(metafields) {
  const grouped = {};
  
  metafields.forEach(metafield => {
    const handle = metafield['Handle'];
    
    if (!grouped[handle]) {
      grouped[handle] = [];
    }
    
    grouped[handle].push(metafield);
  });
  
  return grouped;
}

/**
 * 商品をShopifyにインポート
 */
async function importProductToShopify(product, metafields) {
  const title = product['Title'];
  const handle = product['Handle'];
  const bodyHtml = product['Body (HTML)'];
  const vendor = product['Vendor'];
  const productType = product['Type'];
  const tags = product['Tags'];
  const status = product['Status'] || 'active';
  
  // バリアント情報
  const variantSku = product['Variant SKU'];
  const variantPrice = product['Variant Price'];
  const variantInventoryQty = parseInt(product['Variant Inventory Qty'] || '0', 10);
  const variantBarcode = product['Variant Barcode'];
  
  // 既存の商品か確認
  const existingProduct = await findShopifyProductByHandle(handle);
  
  if (existingProduct) {
    // 既存商品を更新
    return await updateShopifyProduct(existingProduct.id, {
      title,
      bodyHtml,
      vendor,
      productType,
      tags,
      status,
      variants: [
        {
          id: existingProduct.variants.edges[0]?.node.id,
          price: variantPrice,
          sku: variantSku,
          barcode: variantBarcode,
          inventoryQuantity: variantInventoryQty
        }
      ]
    });
  } else {
    // 新規商品を作成
    return await createShopifyProduct({
      title,
      handle,
      bodyHtml,
      vendor,
      productType,
      tags,
      status,
      variants: [
        {
          price: variantPrice,
          sku: variantSku,
          barcode: variantBarcode,
          inventoryQuantity: variantInventoryQty,
          inventoryManagement: 'SHOPIFY'
        }
      ]
    });
  }
}

/**
 * ハンドルから商品を検索
 */
async function findShopifyProductByHandle(handle) {
  try {
    const query = gql`
      query GetProductByHandle($handle: String!) {
        productByHandle(handle: $handle) {
          id
          title
          variants(first: 1) {
            edges {
              node {
                id
              }
            }
          }
        }
      }
    `;
    
    const response = await shopifyClient.request(query, { handle });
    return response.productByHandle;
  } catch (err) {
    console.error(`商品検索エラー (${handle}):`, err.message);
    return null;
  }
}

/**
 * 新規商品を作成
 */
async function createShopifyProduct(productData) {
  try {
    const mutation = gql`
      mutation ProductCreate($input: ProductInput!) {
        productCreate(input: $input) {
          product {
            id
            title
            handle
          }
          userErrors {
            field
            message
          }
        }
      }
    `;
    
    const response = await shopifyClient.request(mutation, {
      input: productData
    });
    
    if (response.productCreate.userErrors.length > 0) {
      throw new Error(response.productCreate.userErrors[0].message);
    }
    
    return response.productCreate.product;
  } catch (err) {
    throw new Error(`Shopify商品作成エラー: ${err.message}`);
  }
}

/**
 * 既存商品を更新
 */
async function updateShopifyProduct(productId, productData) {
  try {
    const mutation = gql`
      mutation ProductUpdate($input: ProductInput!) {
        productUpdate(input: $input) {
          product {
            id
            title
            handle
          }
          userErrors {
            field
            message
          }
        }
      }
    `;
    
    const response = await shopifyClient.request(mutation, {
      input: {
        id: productId,
        ...productData
      }
    });
    
    if (response.productUpdate.userErrors.length > 0) {
      throw new Error(response.productUpdate.userErrors[0].message);
    }
    
    return response.productUpdate.product;
  } catch (err) {
    throw new Error(`Shopify商品更新エラー: ${err.message}`);
  }
}

/**
 * メタフィールドをShopifyにインポート
 */
async function importMetafieldsToShopify(productId, metafields) {
  try {
    for (const metafield of metafields) {
      // メタフィールドのキーを分解
      const metafieldKeys = Object.keys(metafield).filter(key => 
        key.startsWith('Metafield:') && metafield[key]
      );
      
      for (const key of metafieldKeys) {
        // キーから情報を抽出（例: "Metafield: rental.basic_info [json]"）
        const matches = key.match(/Metafield: ([^[]+) \[([^\]]+)\]/);
        if (!matches) continue;
        
        const [_, namespace_key, type] = matches;
        const [namespace, metafieldKey] = namespace_key.split('.');
        const value = metafield[key];
        
        // メタフィールドの型に基づいて処理
        let metafieldType;
        switch(type.toLowerCase()) {
          case 'json':
            metafieldType = 'json_string';
            break;
          case 'string':
            metafieldType = 'single_line_text_field';
            break;
          case 'integer':
            metafieldType = 'number_integer';
            break;
          case 'float':
          case 'decimal':
            metafieldType = 'number_decimal';
            break;
          case 'boolean':
            metafieldType = 'boolean';
            break;
          case 'date':
            metafieldType = 'date';
            break;
          default:
            metafieldType = 'single_line_text_field';
        }
        
        // メタフィールドの作成または更新
        await createOrUpdateMetafield(
          productId,
          namespace,
          metafieldKey,
          value,
          metafieldType
        );
      }
    }
  } catch (err) {
    throw new Error(`メタフィールドインポートエラー: ${err.message}`);
  }
}

/**
 * メタフィールドを作成または更新
 */
async function createOrUpdateMetafield(ownerId, namespace, key, value, type) {
  try {
    const mutation = gql`
      mutation MetafieldSet($metafield: MetafieldsSetInput!) {
        metafieldsSet(metafields: $metafield) {
          metafields {
            id
            namespace
            key
          }
          userErrors {
            field
            message
          }
        }
      }
    `;
    
    const response = await shopifyClient.request(mutation, {
      metafield: {
        ownerId,
        namespace,
        key,
        value,
        type
      }
    });
    
    if (response.metafieldsSet.userErrors.length > 0) {
      throw new Error(response.metafieldsSet.userErrors[0].message);
    }
    
    return response.metafieldsSet.metafields[0];
  } catch (err) {
    throw new Error(`メタフィールド設定エラー (${namespace}.${key}): ${err.message}`);
  }
}

/**
 * 商品をデータベースにインポート
 */
async function importProductToDatabase(product, metafields) {
  try {
    const title = product['Title'];
    const handle = product['Handle'];
    const variantSku = product['Variant SKU'];
    
    // メタフィールドからデータを抽出
    const metadata = {};
    
    for (const metafield of metafields) {
      // メタフィールドのキーを分解
      const metafieldKeys = Object.keys(metafield).filter(key => 
        key.startsWith('Metafield:') && metafield[key]
      );
      
      for (const key of metafieldKeys) {
        // キーから情報を抽出（例: "Metafield: rental.basic_info [json]"）
        const matches = key.match(/Metafield: ([^[]+) \[([^\]]+)\]/);
        if (!matches) continue;
        
        const [_, namespace_key, type] = matches;
        const [namespace, metafieldKey] = namespace_key.split('.');
        const value = metafield[key];
        
        // JSON型の場合はパース
        if (type.toLowerCase() === 'json' && value) {
          try {
            // 各種メタフィールドをメタデータに追加
            if (namespace === 'rental') {
              if (!metadata[metafieldKey]) {
                metadata[metafieldKey] = JSON.parse(value);
              }
            }
          } catch (parseErr) {
            console.error(`JSONパースエラー (${namespace}.${metafieldKey}):`, parseErr);
          }
        }
      }
    }
    
    // ShopifyからIDを取得
    const shopifyProduct = await findShopifyProductByHandle(handle);
    const shopifyId = shopifyProduct?.id?.split('/').pop() || '';
    
    // 既存の商品をチェック
    const existingProduct = await prisma.product.findFirst({
      where: {
        OR: [
          { shopifyId },
          { sku: variantSku }
        ]
      }
    });
    
    if (existingProduct) {
      // 既存商品を更新
      return await prisma.product.update({
        where: { id: existingProduct.id },
        data: {
          title,
          sku: variantSku,
          metadata,
          updatedAt: new Date()
        }
      });
    } else {
      // 新規商品を作成
      return await prisma.product.create({
        data: {
          shop: process.env.SHOPIFY_SHOP,
          shopifyId,
          title,
          sku: variantSku,
          metadata,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }
  } catch (err) {
    throw new Error(`データベース登録エラー: ${err.message}`);
  }
}

/**
 * 指定ミリ秒スリープする
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// インポート処理を実行
importLegacyData().catch(err => {
  console.error('インポート処理中に致命的なエラーが発生しました:', err);
  process.exit(1);
});