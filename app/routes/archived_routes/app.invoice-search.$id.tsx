import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useNavigate, useParams } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  BlockStack,
  InlineStack,
  Badge,
  Button,
  ButtonGroup,
  Divider,
  Banner,
  List,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { useState } from "react";

interface Invoice {
  id: string;
  invoiceNo: string;
  status: string;
  customerName: string;
  companyName: string;
  customerEmail: string;
  customerPhone: string;
  customerAddress: string;
  rentalDate: string;
  returnDate: string;
  paymentMethod: string;
  amount: number;
  tax: number;
  deposit: number;
  orderId?: string;
  items: {
    id: string;
    productName: string;
    sku: string;
    quantity: number;
    unitPrice: number;
    subtotal: number;
  }[];
  notes: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * 伝票詳細画面
 */
export async function loader({ request, params }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  const { id } = params;

  // 実際の実装では、以下のようにデータベースから伝票情報を取得します
  /*
  const invoice = await prisma.invoice.findUnique({
    where: { id },
    include: {
      items: true,
    },
  });

  if (!invoice) {
    throw new Response("伝票が見つかりません", { status: 404 });
  }
  */

  // モックデータ
  const mockInvoice: Invoice = {
    id: id || "1",
    invoiceNo: "INV-2023-001",
    status: "unpaid",
    customerName: "山田太郎",
    companyName: "株式会社サンプル",
    customerEmail: "<EMAIL>",
    customerPhone: "03-1234-5678",
    customerAddress: "東京都渋谷区渋谷1-1-1",
    rentalDate: "2023/10/01",
    returnDate: "2023/10/05",
    paymentMethod: "クレジット",
    amount: 15000,
    tax: 1500,
    deposit: 5000,
    orderId: "gid://shopify/Order/1234567890",
    items: [
      {
        id: "item1",
        productName: "カメラ Canon EOS R5",
        sku: "CAM-001",
        quantity: 1,
        unitPrice: 10000,
        subtotal: 10000,
      },
      {
        id: "item2",
        productName: "三脚 Manfrotto",
        sku: "TRI-001",
        quantity: 1,
        unitPrice: 5000,
        subtotal: 5000,
      },
    ],
    notes: "初回利用のお客様です。丁寧な対応をお願いします。",
    createdAt: "2023/10/01 10:30:00",
    updatedAt: "2023/10/01 10:30:00",
  };

  return json({ invoice: mockInvoice });
}

export default function InvoiceDetail() {
  const { invoice } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [isUpdating, setIsUpdating] = useState(false);

  // ステータスに応じたバッジの色を返す
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "unpaid":
        return <Badge status="warning">未支払い</Badge>;
      case "paid":
        return <Badge status="success">支払い済み</Badge>;
      case "canceled":
        return <Badge status="critical">キャンセル</Badge>;
      case "returned":
        return <Badge status="info">返却済み</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // 金額のフォーマット
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("ja-JP", {
      style: "currency",
      currency: "JPY",
    }).format(amount);
  };

  // ステータス更新ハンドラ
  const handleUpdateStatus = async (newStatus: string) => {
    setIsUpdating(true);
    try {
      // 実際の実装では、APIを呼び出してステータスを更新します
      console.log(`伝票 ${invoice.id} のステータスを ${newStatus} に更新します`);
      
      // 更新が完了したら、画面をリロードするか、状態を更新します
      setTimeout(() => {
        setIsUpdating(false);
        // 実際の実装では、ここでリロードするか状態を更新します
      }, 1000);
    } catch (error) {
      console.error("ステータス更新エラー:", error);
      setIsUpdating(false);
    }
  };

  return (
    <Page
      title={`伝票詳細: ${invoice.invoiceNo}`}
      backAction={{ content: "伝票一覧に戻る", url: "/app/invoice-search" }}
    >
      <TitleBar title={`伝票詳細: ${invoice.invoiceNo}`} />
      
      <Layout>
        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                <InlineStack align="space-between">
                  <BlockStack gap="200">
                    <Text variant="headingLg" as="h2">
                      {invoice.invoiceNo}
                    </Text>
                    <Text variant="bodyMd" color="subdued">
                      作成日: {invoice.createdAt}
                    </Text>
                  </BlockStack>
                  <InlineStack gap="200">
                    {getStatusBadge(invoice.status)}
                    {invoice.orderId && (
                      <Button
                        url={`https://${process.env.SHOPIFY_SHOP}/admin/orders/${invoice.orderId.replace("gid://shopify/Order/", "")}`}
                        external
                      >
                        Shopify注文を表示
                      </Button>
                    )}
                  </InlineStack>
                </InlineStack>
                
                <Divider />
                
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h3">
                    顧客情報
                  </Text>
                  
                  <InlineStack gap="800">
                    <BlockStack gap="200">
                      <Text variant="bodyMd" fontWeight="bold">
                        顧客名
                      </Text>
                      <Text variant="bodyMd">
                        {invoice.customerName}
                      </Text>
                    </BlockStack>
                    
                    <BlockStack gap="200">
                      <Text variant="bodyMd" fontWeight="bold">
                        会社名
                      </Text>
                      <Text variant="bodyMd">
                        {invoice.companyName}
                      </Text>
                    </BlockStack>
                  </InlineStack>
                  
                  <InlineStack gap="800">
                    <BlockStack gap="200">
                      <Text variant="bodyMd" fontWeight="bold">
                        メールアドレス
                      </Text>
                      <Text variant="bodyMd">
                        {invoice.customerEmail}
                      </Text>
                    </BlockStack>
                    
                    <BlockStack gap="200">
                      <Text variant="bodyMd" fontWeight="bold">
                        電話番号
                      </Text>
                      <Text variant="bodyMd">
                        {invoice.customerPhone}
                      </Text>
                    </BlockStack>
                  </InlineStack>
                  
                  <BlockStack gap="200">
                    <Text variant="bodyMd" fontWeight="bold">
                      住所
                    </Text>
                    <Text variant="bodyMd">
                      {invoice.customerAddress}
                    </Text>
                  </BlockStack>
                </BlockStack>
                
                <Divider />
                
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h3">
                    レンタル情報
                  </Text>
                  
                  <InlineStack gap="800">
                    <BlockStack gap="200">
                      <Text variant="bodyMd" fontWeight="bold">
                        貸出日
                      </Text>
                      <Text variant="bodyMd">
                        {invoice.rentalDate}
                      </Text>
                    </BlockStack>
                    
                    <BlockStack gap="200">
                      <Text variant="bodyMd" fontWeight="bold">
                        返却日
                      </Text>
                      <Text variant="bodyMd">
                        {invoice.returnDate}
                      </Text>
                    </BlockStack>
                    
                    <BlockStack gap="200">
                      <Text variant="bodyMd" fontWeight="bold">
                        支払方法
                      </Text>
                      <Text variant="bodyMd">
                        {invoice.paymentMethod}
                      </Text>
                    </BlockStack>
                  </InlineStack>
                </BlockStack>
                
                <Divider />
                
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h3">
                    商品情報
                  </Text>
                  
                  <Box paddingBlockStart="200" paddingBlockEnd="200">
                    <table style={{ width: "100%", borderCollapse: "collapse" }}>
                      <thead>
                        <tr style={{ borderBottom: "1px solid #ddd" }}>
                          <th style={{ textAlign: "left", padding: "8px" }}>商品名</th>
                          <th style={{ textAlign: "left", padding: "8px" }}>SKU</th>
                          <th style={{ textAlign: "right", padding: "8px" }}>数量</th>
                          <th style={{ textAlign: "right", padding: "8px" }}>単価</th>
                          <th style={{ textAlign: "right", padding: "8px" }}>小計</th>
                        </tr>
                      </thead>
                      <tbody>
                        {invoice.items.map((item) => (
                          <tr key={item.id} style={{ borderBottom: "1px solid #ddd" }}>
                            <td style={{ padding: "8px" }}>{item.productName}</td>
                            <td style={{ padding: "8px" }}>{item.sku}</td>
                            <td style={{ textAlign: "right", padding: "8px" }}>{item.quantity}</td>
                            <td style={{ textAlign: "right", padding: "8px" }}>{formatCurrency(item.unitPrice)}</td>
                            <td style={{ textAlign: "right", padding: "8px" }}>{formatCurrency(item.subtotal)}</td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colSpan={3}></td>
                          <td style={{ textAlign: "right", padding: "8px", fontWeight: "bold" }}>小計:</td>
                          <td style={{ textAlign: "right", padding: "8px" }}>{formatCurrency(invoice.amount - invoice.tax)}</td>
                        </tr>
                        <tr>
                          <td colSpan={3}></td>
                          <td style={{ textAlign: "right", padding: "8px", fontWeight: "bold" }}>消費税:</td>
                          <td style={{ textAlign: "right", padding: "8px" }}>{formatCurrency(invoice.tax)}</td>
                        </tr>
                        <tr>
                          <td colSpan={3}></td>
                          <td style={{ textAlign: "right", padding: "8px", fontWeight: "bold" }}>合計:</td>
                          <td style={{ textAlign: "right", padding: "8px", fontWeight: "bold" }}>{formatCurrency(invoice.amount)}</td>
                        </tr>
                        <tr>
                          <td colSpan={3}></td>
                          <td style={{ textAlign: "right", padding: "8px", fontWeight: "bold" }}>デポジット:</td>
                          <td style={{ textAlign: "right", padding: "8px" }}>{formatCurrency(invoice.deposit)}</td>
                        </tr>
                      </tfoot>
                    </table>
                  </Box>
                </BlockStack>
                
                {invoice.notes && (
                  <>
                    <Divider />
                    <BlockStack gap="400">
                      <Text variant="headingMd" as="h3">
                        備考
                      </Text>
                      <Box background="bg-surface-secondary" padding="300" borderRadius="100">
                        <Text variant="bodyMd">
                          {invoice.notes}
                        </Text>
                      </Box>
                    </BlockStack>
                  </>
                )}
                
                <Divider />
                
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h3">
                    ステータス管理
                  </Text>
                  
                  <ButtonGroup>
                    <Button
                      onClick={() => handleUpdateStatus("paid")}
                      disabled={invoice.status === "paid" || isUpdating}
                      loading={isUpdating && invoice.status !== "paid"}
                    >
                      支払い済みにする
                    </Button>
                    <Button
                      onClick={() => handleUpdateStatus("returned")}
                      disabled={invoice.status === "returned" || isUpdating}
                      loading={isUpdating && invoice.status !== "returned"}
                    >
                      返却済みにする
                    </Button>
                    <Button
                      onClick={() => handleUpdateStatus("canceled")}
                      disabled={invoice.status === "canceled" || isUpdating}
                      loading={isUpdating && invoice.status !== "canceled"}
                      destructive
                    >
                      キャンセルする
                    </Button>
                  </ButtonGroup>
                  
                  {invoice.status === "canceled" && (
                    <Banner status="critical">
                      <p>この伝票はキャンセルされています。</p>
                    </Banner>
                  )}
                </BlockStack>
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
