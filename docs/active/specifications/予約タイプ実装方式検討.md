# 予約タイプ実装方式検討

## 概要

本ドキュメントは、レンタル商品の予約タイプ（本予約/仮予約）と日数バリエーションの実装方式について検討した結果をまとめたものです。現状の課題を分析し、複数の実装方式を比較検討した上で、最適な実装方法を提案します。

## 現状と課題

現在の実装では、商品の日数バリエーション（1日、2日、...、8日以上）と仮予約バリエーションを同じバリエーション選択肢として混在させています。このアプローチには以下の課題があります：

1. **概念的な混乱**：日数（期間）と予約タイプ（仮/本）という異なる性質の選択肢が混在しています
2. **ユーザー体験の低下**：選択の流れが直感的でなく、ユーザーが混乱する可能性があります
3. **将来の拡張性の制限**：予約タイプや条件が増えると管理が複雑化します
4. **在庫管理の複雑さ**：同じ物理商品に対して複数のバリエーションの在庫を管理する必要があります

## 検討した実装方式の比較

### 選択肢1: 現状維持（日数+仮予約をバリエーションとして管理）

```
バリエーション選択肢: 1日, 2日, 3日, ... 8日以上, 仮予約
```

#### メリット
- 既に実装済みで追加開発工数が不要
- Shopifyの標準バリエーション機能を使用している
- 価格設定が直接的（価格調整API不要）

#### デメリット
- 概念的に不自然（日数と予約タイプの混在）
- ユーザーにとって選択が直感的でない
- 拡張性に制限がある（選択肢が増えると管理が複雑に）
- 「2日間の仮予約」のような組み合わせができない

### 選択肢2: 予約タイプをバリエーションとして管理

```
バリエーション選択肢: 本予約, 仮予約
日数選択: カスタムUI（別途実装）
```

#### メリット
- ユーザーにとって意味のある選択肢を提示できる
- 在庫管理が直接的（バリエーションごとに同じ在庫を参照）
- 予約タイプによる価格差（仮予約は基本料金の10%）を明示的に設定できる

#### デメリット
- 日数に基づく料金計算をカスタム実装する必要がある
- 価格調整APIの実装に工数が必要（4-6日程度）
- ドラフトオーダーとの連携が複雑になる可能性がある
- 「日数×予約タイプ」の組み合わせを管理する必要がある

### 選択肢3: 予約タイプをメタフィールドとして管理（推奨）

```
バリエーション選択肢: 1日, 2日, 3日, ... 8日以上（現状通り）
メタフィールド: 予約タイプ（本予約/仮予約）
```

#### メリット
- 概念的に明確（予約タイプはステータス情報として扱う）
- 既存の日数バリエーション構造を維持できる
- ユーザー体験向上（日数と予約タイプを分離して選択）
- 将来的な拡張が容易（予約タイプの追加が容易）
- テーマでのメタフィールド表示が比較的容易

#### デメリット
- 価格調整APIの実装が必要（仮予約時は本予約の10%に調整）
- テーマのカスタマイズが必要

## 推奨実装方式

**選択肢3: 予約タイプをメタフィールドとして管理**を推奨します。

理由：
1. **概念的に明確**：予約タイプはステータス情報であり、メタフィールドとして管理するのが適切です
2. **ユーザー体験向上**：日数と予約タイプの選択を分けることでより直感的なUIを実現できます
3. **既存コードとの互換性**：日数バリエーションの構造は維持されるため、既存コードの変更が最小限で済みます
4. **拡張性**：将来的に予約タイプが増えても対応しやすい設計です
5. **メタフィールド設計の改善提案との整合性**：SYS_001_メタフィールド設計改善.mdで提案されている構造化JSONメタフィールドと相性が良いです

## 実装詳細

### メタフィールド定義

```javascript
// rental.booking_type メタフィールドの定義
{
  "namespace": "rental",
  "key": "booking_type",
  "value": "regular", // "regular" または "provisional"
  "type": "single_line_text_field",
  "description": "予約タイプ（本予約/仮予約）"
}
```

### テーマでの表示例

```liquid
<div class="booking-type-selector">
  <h3>予約タイプ</h3>
  <div class="radio-options">
    <label>
      <input type="radio" name="booking_type" value="regular" checked>
      <span>本予約</span>
    </label>
    <label>
      <input type="radio" name="booking_type" value="provisional">
      <span>仮予約（基本料金の10%）</span>
    </label>
  </div>
</div>

<script>
  // 予約タイプが変更されたときの処理
  $('input[name="booking_type"]').change(function() {
    const bookingType = $(this).val();
    updatePrice(bookingType);
  });
  
  // 価格更新処理
  function updatePrice(bookingType) {
    let price = {{ product.selected_or_first_available_variant.price }};
    
    if (bookingType === 'provisional') {
      // 仮予約の場合は10%の価格を表示
      price = price * 0.1;
    }
    
    // 価格表示を更新
    $('.product-price').text(`¥${price.toLocaleString()}`);
    
    // hidden フィールドに予約タイプを設定
    $('input[name="properties[予約タイプ]"]').val(
      bookingType === 'regular' ? '本予約' : '仮予約'
    );
  }
</script>
```

### カートへの追加処理

```javascript
// カートに追加する際のコード例
const addToCart = async () => {
  const bookingType = $('input[name="booking_type"]:checked').val();
  const variant = product.variants.find(v => v.title === selectedDays);
  
  await fetch('/cart/add.js', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      items: [{
        id: variant.id,
        quantity: 1,
        properties: {
          '開始日': selectedDates.start,
          '終了日': selectedDates.end,
          'レンタル日数': calculatedDays,
          '予約タイプ': bookingType === 'regular' ? '本予約' : '仮予約',
          '計算価格': bookingType === 'regular' 
            ? variant.price 
            : Math.round(variant.price * 0.1)
        }
      }]
    })
  });
};
```

### 注文後の価格調整処理

```javascript
// 注文作成Webhookハンドラー
app.post('/webhooks/orders/create', async (req, res) => {
  const order = req.body;
  
  // 注文の各商品をチェック
  for (const item of order.line_items) {
    // 予約タイプをチェック
    const bookingType = item.properties.find(p => p.name === '予約タイプ')?.value;
    
    if (bookingType === '仮予約') {
      try {
        // 注文編集セッションを開始
        const response = await shopifyClient.graphql(`
          mutation orderEditBegin($id: ID!) {
            orderEditBegin(id: $id) {
              calculatedOrder {
                id
              }
              userErrors {
                field
                message
              }
            }
          }
        `, {
          variables: {
            id: `gid://shopify/Order/${order.id}`
          }
        });
        
        const editId = response.data.orderEditBegin.calculatedOrder.id;
        
        // 商品の価格を10%に設定
        const newPrice = Math.round(item.price * 0.1);
        
        // 価格を更新
        await shopifyClient.graphql(`
          mutation orderEditSetQuantity($id: ID!, $lineItemId: ID!, $quantity: Int!, $price: MoneyInput!) {
            orderEditSetLineItemPrice(
              id: $id,
              lineItemId: $lineItemId,
              price: $price
            ) {
              calculatedLineItem {
                id
              }
              calculatedOrder {
                id
              }
              userErrors {
                field
                message
              }
            }
          }
        `, {
          variables: {
            id: editId,
            lineItemId: `gid://shopify/LineItem/${item.id}`,
            price: { 
              amount: newPrice, 
              currencyCode: order.currency 
            }
          }
        });
        
        // 注文編集を確定
        await shopifyClient.graphql(`
          mutation orderEditCommit($id: ID!) {
            orderEditCommit(id: $id) {
              order {
                id
              }
              userErrors {
                field
                message
              }
            }
          }
        `, {
          variables: {
            id: editId
          }
        });
        
        console.log(`仮予約注文 ${order.id} の価格を ${newPrice} に調整しました`);
      } catch (error) {
        console.error(`注文 ${order.id} の価格調整に失敗しました:`, error);
      }
    }
  }
  
  res.status(200).send('OK');
});
```

## 段階的実装アプローチ

工数の懸念を考慮し、以下の段階的アプローチを提案します：

### フェーズ1: 基本実装（1-2日）

- メタフィールド定義の作成（予約タイプ）
- テーマでの予約タイプ選択UI実装
- フロントエンドでの価格表示調整（仮予約は10%として表示）
- Line Item Propertiesに予約タイプと計算価格を保存
- 注文メモに「仮予約: 基本料金の10%のみ請求」などの注記を追加

### フェーズ2: 管理機能（2-3日）

- 管理画面での仮予約識別機能
- 簡易的な価格調整機能（管理者用）
- 仮予約注文の一覧表示
- 料金調整済みフラグの管理

### フェーズ3: 完全自動化（オプション、3-4日）

- Webhook処理による自動価格調整API実装
- エラーハンドリングとリトライ機能
- 監視とログ機能
- 自動メール通知機能

この段階的アプローチにより、初期の機能をすぐにリリースしながら、徐々に自動化を進めることができます。最初は管理者の手動処理を組み合わせることで、全体の工数を分散させることができます。

## 注意点と留意事項

1. **価格表示と実際の請求額**:
   - フロントエンドでの表示価格と実際の請求額に一時的な不一致が生じる可能性があります
   - ユーザーへの明確な説明が必要です（「仮予約は基本料金の10%のみ請求されます」など）

2. **在庫管理**:
   - 本予約と仮予約で同じ在庫を使用するため、予約期間の重複チェックが重要です
   - 仮予約から本予約への変更プロセスも考慮する必要があります

3. **エラー処理**:
   - 価格調整APIの失敗に対する対応策が必要です
   - 失敗した場合の再試行メカニズムやアラート機能を検討してください

4. **テストの重要性**:
   - 実際の注文フローでの動作確認が必要です
   - 様々なシナリオ（割引併用、複数商品注文など）でのテストを実施してください

## まとめ

予約タイプをメタフィールドとして管理し、日数はバリエーションとして維持する方式が、概念的明確さ、ユーザー体験、拡張性の観点から最適です。段階的な実装アプローチを採用することで、開発リソースの制約にも対応できます。

この実装方式により、ユーザーにとって直感的な予約プロセスを提供しつつ、システムの保守性と拡張性を向上させることができます。また、SYS_001_メタフィールド設計改善.mdで提案されているメタフィールド設計の改善とも整合性が高く、長期的な視点でも優れた選択と言えます。