/**
 * 顧客一覧を表示するスクリプト
 *
 * このスクリプトは、Shopifyに登録されている顧客情報を一覧表示します。
 *
 * 使用方法: npx tsx scripts/list-customers.ts [--limit N]
 * 
 * オプション:
 * --limit: 表示する顧客の最大数（デフォルト: 10）
 */

import dotenv from 'dotenv';
import { parseArgs } from 'node:util';
import { authenticate } from '../app/shopify.server';

// 環境変数の読み込み
dotenv.config();

/**
 * Shopify Admin APIを初期化する関数
 */
async function initializeShopifyAdmin() {
  // 環境変数からShopify情報を取得
  const shopifyShop = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';
  const shopifyAdminApiAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
  
  if (!shopifyAdminApiAccessToken) {
    console.error('SHOPIFY_ADMIN_API_ACCESS_TOKEN環境変数が設定されていません');
    process.exit(1);
  }
  
  console.log('Shopify情報:');
  console.log('- Shop:', shopifyShop);
  console.log('- Access Token:', shopifyAdminApiAccessToken.substring(0, 5) + '...');
  
  // リクエストオブジェクトを作成（ダミー）
  const headers = new Headers();
  headers.append('Content-Type', 'application/json');
  headers.append('X-Shopify-Access-Token', shopifyAdminApiAccessToken);
  
  const request = new Request(`https://${shopifyShop}/admin/api/2025-01/graphql.json`, {
    method: 'GET',
    headers: headers
  });
  
  try {
    // authenticate.adminを呼び出す
    const { admin } = await authenticate.admin(request);
    console.log('Shopify Admin APIの初期化が完了しました');
    
    // adminオブジェクトを返す
    return admin;
  } catch (authError) {
    console.error('Shopify Admin API認証エラー:', authError);
    
    // 代替手段として、直接GraphQLクライアントを作成
    console.log('代替手段としてGraphQLクライアントを直接作成します...');
    
    // 簡易的なGraphQLクライアントを作成
    const admin = {
      graphql: async (query: string, options: any) => {
        const response = await fetch(`https://${shopifyShop}/admin/api/2025-01/graphql.json`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': shopifyAdminApiAccessToken
          },
          body: JSON.stringify({
            query,
            variables: options.variables
          })
        });
        
        return response;
      }
    };
    
    console.log('代替GraphQLクライアントの作成が完了しました');
    return admin;
  }
}

/**
 * 顧客一覧を取得する関数
 */
async function listCustomers(admin: any, limit: number = 10) {
  try {
    console.log('顧客一覧を取得しています...');
    
    // GraphQLクエリを実行
    const CUSTOMERS_QUERY = `
      query getCustomers($first: Int!) {
        customers(first: $first) {
          edges {
            node {
              id
              firstName
              lastName
              email
              phone
              createdAt
              defaultAddress {
                address1
                city
                province
                zip
                country
              }
            }
          }
        }
      }
    `;
    
    const response = await admin.graphql(
      CUSTOMERS_QUERY,
      {
        variables: {
          first: limit
        }
      }
    );
    
    const result = await response.json();
    
    if (result.errors) {
      console.error('GraphQLエラー:', result.errors);
      return;
    }
    
    const customers = result.data?.customers?.edges?.map((edge: any) => edge.node) || [];
    
    if (customers.length === 0) {
      console.log('顧客情報が見つかりません');
      return;
    }
    
    console.log(`${customers.length}件の顧客が見つかりました\n`);
    
    // 顧客情報を表示
    customers.forEach((customer: any, index: number) => {
      console.log(`----- 顧客 ${index + 1} -----`);
      console.log('顧客ID:', customer.id);
      console.log('名前:', `${customer.firstName} ${customer.lastName}`);
      console.log('メール:', customer.email);
      console.log('電話番号:', customer.phone || 'なし');
      console.log('作成日時:', customer.createdAt);
      
      if (customer.defaultAddress) {
        console.log('住所:');
        console.log('  住所1:', customer.defaultAddress.address1);
        console.log('  市区町村:', customer.defaultAddress.city);
        console.log('  都道府県:', customer.defaultAddress.province);
        console.log('  郵便番号:', customer.defaultAddress.zip);
        console.log('  国:', customer.defaultAddress.country);
      }
      
      console.log('');
    });
    
    return customers;
  } catch (error) {
    console.error('顧客情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

// メイン処理
async function main() {
  try {
    // コマンドライン引数を解析
    const args = parseArgs({
      options: {
        limit: { type: 'string' }
      },
      strict: false
    });
    
    const limit = args.values.limit ? parseInt(args.values.limit, 10) : 10;
    
    // Shopify Admin APIを初期化
    const admin = await initializeShopifyAdmin();
    
    // 顧客一覧を表示
    await listCustomers(admin, limit);
    
    console.log('処理が完了しました');
    process.exit(0);
  } catch (error) {
    console.error('エラーが発生しました:', error);
    process.exit(1);
  }
}

// スクリプトを実行
main();
