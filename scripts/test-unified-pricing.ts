/**
 * 統一料金計算サービスのテストスクリプト
 */

import { UnifiedPricingService } from '../app/services/pricing/unified-pricing.service';

interface TestCase {
  name: string;
  startDate: Date;
  endDate: Date;
  basePrice: number;
  expectedPrice: number;
  config?: any;
}

class UnifiedPricingTester {
  private pricingService: UnifiedPricingService;
  private results: Array<{ name: string; success: boolean; message: string }> = [];

  constructor() {
    this.pricingService = UnifiedPricingService.getInstance();
  }

  /**
   * テストケースを実行
   */
  async runTests(): Promise<void> {
    console.log('=== 統一料金計算サービステスト開始 ===\n');

    const testCases: TestCase[] = [
      {
        name: '1日間レンタル',
        startDate: new Date('2025-05-24'),
        endDate: new Date('2025-05-24'),
        basePrice: 1800,
        expectedPrice: 1800,
      },
      {
        name: '2日間レンタル',
        startDate: new Date('2025-05-24'),
        endDate: new Date('2025-05-25'),
        basePrice: 1800,
        expectedPrice: 2160, // 1800 + (1800 * 0.2)
      },
      {
        name: '3日間レンタル',
        startDate: new Date('2025-05-24'),
        endDate: new Date('2025-05-26'),
        basePrice: 1800,
        expectedPrice: 2520, // 1800 + (1800 * 0.2 * 2)
      },
      {
        name: '7日間レンタル',
        startDate: new Date('2025-05-24'),
        endDate: new Date('2025-05-30'),
        basePrice: 1800,
        expectedPrice: 3780, // 1800 + (1800 * 0.2 * 5) + (1800 * 0.1 * 1) = 1800 + 1800 + 180
      },
      {
        name: '10日間レンタル',
        startDate: new Date('2025-05-24'),
        endDate: new Date('2025-06-02'),
        basePrice: 1800,
        expectedPrice: 4320, // 1800 + (1800 * 0.2 * 5) + (1800 * 0.1 * 4) = 1800 + 1800 + 720
      },
    ];

    for (const testCase of testCases) {
      await this.runSingleTest(testCase);
    }

    this.displayResults();
  }

  /**
   * 単一テストケースを実行
   */
  private async runSingleTest(testCase: TestCase): Promise<void> {
    try {
      console.log(`\n--- ${testCase.name} ---`);
      console.log(`期間: ${testCase.startDate.toISOString().split('T')[0]} 〜 ${testCase.endDate.toISOString().split('T')[0]}`);
      console.log(`基本料金: ${testCase.basePrice}円`);
      console.log(`期待値: ${testCase.expectedPrice}円`);

      const result = this.pricingService.calculatePrice(
        testCase.startDate,
        testCase.endDate,
        testCase.basePrice,
        testCase.config
      );

      console.log(`計算結果: ${result.totalPrice}円`);
      console.log(`レンタル日数: ${result.rentalDays}日`);

      // 料金内訳を表示
      console.log('料金内訳:');
      result.priceBreakdown.forEach(item => {
        console.log(`  ${item.day}日目: ${item.price}円 (${item.description})`);
      });

      // 結果の検証
      const tolerance = 10; // 許容誤差
      const isSuccess = Math.abs(result.totalPrice - testCase.expectedPrice) <= tolerance;

      if (isSuccess) {
        console.log('✅ テスト成功');
        this.results.push({
          name: testCase.name,
          success: true,
          message: `計算結果: ${result.totalPrice}円 (期待値: ${testCase.expectedPrice}円)`
        });
      } else {
        console.log('❌ テスト失敗');
        this.results.push({
          name: testCase.name,
          success: false,
          message: `計算結果: ${result.totalPrice}円 (期待値: ${testCase.expectedPrice}円) - 差額: ${Math.abs(result.totalPrice - testCase.expectedPrice)}円`
        });
      }

    } catch (error) {
      console.log('❌ テストエラー:', error.message);
      this.results.push({
        name: testCase.name,
        success: false,
        message: `エラー: ${error.message}`
      });
    }
  }

  /**
   * テスト結果を表示
   */
  private displayResults(): void {
    console.log('\n=== テスト結果サマリー ===');

    const successCount = this.results.filter(r => r.success).length;
    const totalCount = this.results.length;

    console.log(`成功: ${successCount}/${totalCount}`);
    console.log(`失敗: ${totalCount - successCount}/${totalCount}`);

    if (totalCount - successCount > 0) {
      console.log('\n失敗したテスト:');
      this.results.filter(r => !r.success).forEach(result => {
        console.log(`❌ ${result.name}: ${result.message}`);
      });
    }

    console.log('\n=== 統一料金計算サービステスト終了 ===');
  }

  /**
   * 複数商品のテスト
   */
  async testMultipleProducts(): Promise<void> {
    console.log('\n=== 複数商品料金計算テスト ===');

    try {
      const products = [
        {
          startDate: new Date('2025-05-24'),
          endDate: new Date('2025-05-26'),
          basePrice: 1800,
        },
        {
          startDate: new Date('2025-05-24'),
          endDate: new Date('2025-05-25'),
          basePrice: 2500,
        },
      ];

      const result = this.pricingService.calculateMultipleProducts(products);

      console.log('複数商品の計算結果:');
      console.log(`合計金額: ${result.totalPrice}円`);
      console.log(`税込金額: ${result.taxInclusivePrice}円`);
      console.log(`デポジット: ${result.depositAmount}円`);

      result.products.forEach((product, index) => {
        console.log(`\n商品${index + 1}:`);
        console.log(`  期間: ${product.metadata.startDate} 〜 ${product.metadata.endDate}`);
        console.log(`  基本料金: ${product.metadata.basePrice}円`);
        console.log(`  計算結果: ${product.totalPrice}円`);
      });

      console.log('✅ 複数商品テスト成功');
    } catch (error) {
      console.log('❌ 複数商品テストエラー:', error.message);
    }
  }

  /**
   * 仮予約料金計算のテスト
   */
  async testProvisionalBooking(): Promise<void> {
    console.log('\n=== 仮予約料金計算テスト ===');

    try {
      const basePrice = 1800;
      const startDate = new Date('2025-05-24');
      const endDate = new Date('2025-05-26'); // 3日間

      // 正規予約の計算
      const confirmedResult = this.pricingService.calculateConfirmedPrice(startDate, endDate, basePrice);
      console.log(`正規予約料金: ${this.pricingService.formatPrice(confirmedResult.totalPrice)}`);

      // 仮予約の計算
      const provisionalResult = this.pricingService.calculateProvisionalPrice(startDate, endDate, basePrice);
      console.log(`仮予約料金: ${this.pricingService.formatPrice(provisionalResult.totalPrice)}`);

      // 差額計算
      const upgradeInfo = this.pricingService.calculateUpgradePrice(startDate, endDate, basePrice);
      console.log(`差額: ${this.pricingService.formatPrice(upgradeInfo.upgradeAmount)}`);

      // 検証
      const expectedConfirmed = 2520; // 1800 + (1800 * 0.2 * 2) = 2520
      const expectedProvisional = 252; // 2520 * 0.1 = 252
      const expectedUpgrade = 2268; // 2520 - 252 = 2268

      const confirmedCorrect = Math.abs(confirmedResult.totalPrice - expectedConfirmed) <= 10;
      const provisionalCorrect = Math.abs(provisionalResult.totalPrice - expectedProvisional) <= 10;
      const upgradeCorrect = Math.abs(upgradeInfo.upgradeAmount - expectedUpgrade) <= 10;

      console.log(`正規予約検証: ${confirmedCorrect ? '✅' : '❌'} (期待値: ${expectedConfirmed}円)`);
      console.log(`仮予約検証: ${provisionalCorrect ? '✅' : '❌'} (期待値: ${expectedProvisional}円)`);
      console.log(`差額検証: ${upgradeCorrect ? '✅' : '❌'} (期待値: ${expectedUpgrade}円)`);

      // 仮予約の詳細情報を表示
      console.log('\n仮予約詳細:');
      console.log(`  正規料金: ${this.pricingService.formatPrice(provisionalResult.fullPrice || 0)}`);
      console.log(`  仮予約料金: ${this.pricingService.formatPrice(provisionalResult.provisionalPrice || 0)}`);
      console.log(`  仮予約フラグ: ${provisionalResult.isProvisional}`);

      if (confirmedCorrect && provisionalCorrect && upgradeCorrect) {
        console.log('✅ 仮予約料金計算テスト成功');
      } else {
        console.log('❌ 仮予約料金計算テスト失敗');
      }

    } catch (error) {
      console.log('❌ 仮予約料金計算テストエラー:', error.message);
    }
  }

  /**
   * 複数日数での仮予約テスト
   */
  async testProvisionalBookingMultipleDays(): Promise<void> {
    console.log('\n=== 複数日数仮予約テスト ===');

    const testCases = [
      { days: 1, startDate: new Date('2025-05-24'), endDate: new Date('2025-05-24') },
      { days: 3, startDate: new Date('2025-05-24'), endDate: new Date('2025-05-26') },
      { days: 7, startDate: new Date('2025-05-24'), endDate: new Date('2025-05-30') },
      { days: 10, startDate: new Date('2025-05-24'), endDate: new Date('2025-06-02') },
    ];

    const basePrice = 1800;

    for (const testCase of testCases) {
      console.log(`\n--- ${testCase.days}日間 ---`);

      const confirmed = this.pricingService.calculateConfirmedPrice(testCase.startDate, testCase.endDate, basePrice);
      const provisional = this.pricingService.calculateProvisionalPrice(testCase.startDate, testCase.endDate, basePrice);

      console.log(`正規: ${this.pricingService.formatPrice(confirmed.totalPrice)}`);
      console.log(`仮予約: ${this.pricingService.formatPrice(provisional.totalPrice)}`);
      console.log(`割合: ${((provisional.totalPrice / confirmed.totalPrice) * 100).toFixed(1)}%`);

      // 10%になっているかチェック
      const expectedProvisional = Math.round(confirmed.totalPrice * 0.1);
      const isCorrect = Math.abs(provisional.totalPrice - expectedProvisional) <= 1;
      console.log(`検証: ${isCorrect ? '✅' : '❌'} (期待値: ${expectedProvisional}円)`);
    }
  }

  /**
   * フォーマット機能のテスト
   */
  testFormatting(): void {
    console.log('\n=== フォーマット機能テスト ===');

    try {
      const result = this.pricingService.calculatePrice(
        new Date('2025-05-24'),
        new Date('2025-05-26'),
        1800
      );

      console.log('フォーマット結果:');
      console.log(`料金: ${this.pricingService.formatPrice(result.totalPrice)}`);
      console.log(`税込: ${this.pricingService.formatPrice(result.taxInclusivePrice)}`);
      console.log(`デポジット: ${this.pricingService.formatPrice(result.depositAmount)}`);

      console.log('\nサマリー:');
      console.log(this.pricingService.generatePricingSummary(result));

      console.log('✅ フォーマット機能テスト成功');
    } catch (error) {
      console.log('❌ フォーマット機能テストエラー:', error.message);
    }
  }
}

// メイン実行
async function main() {
  const tester = new UnifiedPricingTester();

  await tester.runTests();
  await tester.testMultipleProducts();
  await tester.testProvisionalBooking();
  await tester.testProvisionalBookingMultipleDays();
  tester.testFormatting();
}

if (import.meta.url === new URL(import.meta.url).href) {
  main().catch(console.error);
}

export { UnifiedPricingTester };
