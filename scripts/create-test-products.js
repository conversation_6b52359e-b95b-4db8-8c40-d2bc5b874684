const fetch = require('node-fetch');
require('dotenv').config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLリクエストを送信する関数
async function sendGraphQLRequest(query, variables = {}) {
  const url = `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify({
        query,
        variables
      })
    });
    
    const data = await response.json();
    
    if (data.errors) {
      console.error('GraphQLエラー:', data.errors);
      return null;
    }
    
    return data.data;
  } catch (error) {
    console.error('リクエストエラー:', error);
    return null;
  }
}

// 商品を作成する関数
async function createProduct(title, variants, metafields = []) {
  const query = `
    mutation productCreate($input: ProductInput!) {
      productCreate(input: $input) {
        product {
          id
          title
          handle
        }
        userErrors {
          field
          message
        }
      }
    }
  `;
  
  const variables = {
    input: {
      title,
      variants,
      tags: "テスト,レンタル商品",
      productType: "レンタル商品",
      vendor: "テスト",
      metafields
    }
  };
  
  const result = await sendGraphQLRequest(query, variables);
  
  if (result && result.productCreate && result.productCreate.product) {
    console.log(`商品 ${title} を作成しました。ID: ${result.productCreate.product.id}`);
    return result.productCreate.product;
  } else {
    console.error(`商品 ${title} の作成に失敗しました。`);
    return null;
  }
}

// メタフィールドを設定する関数
async function setMetafields(productId, metafields) {
  const query = `
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
        }
        userErrors {
          field
          message
        }
      }
    }
  `;
  
  const variables = {
    metafields: metafields.map(metafield => ({
      ...metafield,
      ownerId: productId
    }))
  };
  
  const result = await sendGraphQLRequest(query, variables);
  
  if (result && result.metafieldsSet && result.metafieldsSet.metafields) {
    console.log(`商品 ${productId} のメタフィールドを設定しました。`);
    return true;
  } else {
    console.error(`商品 ${productId} のメタフィールド設定に失敗しました。`);
    return false;
  }
}

// テスト用商品を作成する関数
async function createTestProducts() {
  // ベーシックソファのテスト商品
  const basicSofaVariants = [
    { price: 8000, title: "1日レンタル", sku: "10101007-001-001" },
    { price: 9600, title: "2日レンタル", sku: "10101007-001-002" },
    { price: 11200, title: "3日レンタル", sku: "10101007-001-003" },
    { price: 12800, title: "4日レンタル", sku: "10101007-001-004" },
    { price: 14400, title: "5日レンタル", sku: "10101007-001-005" },
    { price: 16000, title: "6日レンタル", sku: "10101007-001-006" },
    { price: 17600, title: "7日レンタル", sku: "10101007-001-007" },
    { price: 18400, title: "8日以上レンタル", sku: "10101007-001-008" }
  ];
  
  const basicSofaProduct = await createProduct(
    "【テスト】ベーシックソファ オフホワイト 1シーター",
    basicSofaVariants
  );
  
  if (basicSofaProduct) {
    // 基本情報メタフィールド
    const basicInfo = {
      productCode: "10101007",
      detailCode: "001",
      kana: "ベーシックソファオフホワイト1シーター",
      dimensions: {
        width: 87,
        depth: 74,
        height: 76
      },
      seatDimensions: {
        width: 52,
        depth: 54,
        height: 40
      },
      material: "ファブリック",
      color: "オフホワイト",
      maker: "ヤマナリ",
      campaign: "通常商品",
      notes: "クリーニング済みですが全体的に黄ばみ発生（H30.12.15)"
    };
    
    // 料金設定メタフィールド
    const pricing = {
      basePrice: 8000,
      depositRate: 0.1,
      discountRules: {
        day2_6_rate: 0.2,
        day7_plus_rate: 0.1
      },
      minimumDays: 1,
      maximumDays: 30
    };
    
    // 在庫アイテム情報メタフィールド
    const inventoryItems = [
      {
        id: "item-10101007-001-1",
        sku: "10101007-001-1",
        status: "available",
        location: "NY",
        notes: "背面向かって右側うっすら黒いしみ"
      },
      {
        id: "item-10101007-001-2",
        sku: "10101007-001-2",
        status: "maintenance",
        location: "NY",
        notes: "向かって左アーム手前と正面左側に黄色い輪染み有"
      }
    ];
    
    // 予約情報メタフィールド
    const reservationInfo = [
      {
        itemId: "item-10101007-001-1",
        reservations: [
          {
            id: "reservation-001",
            startDate: "2025-05-22",
            endDate: "2025-05-25",
            status: "confirmed",
            customerName: "山田太郎",
            customerEmail: "<EMAIL>",
            orderId: "gid://shopify/Order/1001",
            orderLineItemId: "gid://shopify/LineItem/1001",
            notes: "特になし"
          }
        ]
      },
      {
        itemId: "item-10101007-001-2",
        reservations: []
      }
    ];
    
    // メタフィールドを設定
    await setMetafields(basicSofaProduct.id, [
      {
        namespace: "rental",
        key: "basic_info",
        value: JSON.stringify(basicInfo),
        type: "json"
      },
      {
        namespace: "rental",
        key: "pricing",
        value: JSON.stringify(pricing),
        type: "json"
      },
      {
        namespace: "rental",
        key: "inventory_items",
        value: JSON.stringify(inventoryItems),
        type: "json"
      },
      {
        namespace: "rental",
        key: "reservation_info",
        value: JSON.stringify(reservationInfo),
        type: "json"
      }
    ]);
  }
  
  console.log("テスト商品の作成が完了しました。");
}

// 実行
createTestProducts();
