const { PrismaClient } = require("@prisma/client");
const { shopify } = require("../app/shopify.server");
const { authenticate } = require("../app/shopify.server");

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト結果を保存する配列
const testResults = [];

/**
 * テスト結果を記録する関数
 */
function logTestResult(testName, success, details = {}) {
  const result = {
    testName,
    success,
    timestamp: new Date().toISOString(),
    ...details
  };
  
  testResults.push(result);
  
  // コンソールにも出力
  console.log(`[${success ? 'SUCCESS' : 'FAILURE'}] ${testName}`);
  if (details.message) {
    console.log(`  ${details.message}`);
  }
  if (details.error) {
    console.error(`  Error: ${details.error}`);
  }
}

/**
 * Shopify商品を作成する関数
 */
async function createShopifyProduct(session, productData) {
  try {
    const client = new shopify.api.clients.Graphql({ session });
    
    const response = await client.query({
      data: {
        query: `
          mutation productCreate($input: ProductInput!) {
            productCreate(input: $input) {
              product {
                id
                title
                handle
                variants(first: 1) {
                  edges {
                    node {
                      id
                      price
                      inventoryQuantity
                    }
                  }
                }
              }
              userErrors {
                field
                message
              }
            }
          }
        `,
        variables: {
          input: productData
        }
      }
    });
    
    if (response.body.data.productCreate.userErrors.length > 0) {
      throw new Error(JSON.stringify(response.body.data.productCreate.userErrors));
    }
    
    return response.body.data.productCreate.product;
  } catch (error) {
    console.error("商品作成エラー:", error);
    throw error;
  }
}

/**
 * 商品のメタフィールドを設定する関数
 */
async function setProductMetafields(session, productId, metafields) {
  try {
    const client = new shopify.api.clients.Graphql({ session });
    
    for (const metafield of metafields) {
      const response = await client.query({
        data: {
          query: `
            mutation metafieldSet($metafield: MetafieldsSetInput!) {
              metafieldsSet(metafields: $metafield) {
                metafields {
                  id
                  namespace
                  key
                }
                userErrors {
                  field
                  message
                }
              }
            }
          `,
          variables: {
            metafield: {
              ownerId: productId,
              metafields: [metafield]
            }
          }
        }
      });
      
      if (response.body.data.metafieldsSet.userErrors.length > 0) {
        throw new Error(JSON.stringify(response.body.data.metafieldsSet.userErrors));
      }
    }
    
    return true;
  } catch (error) {
    console.error("メタフィールド設定エラー:", error);
    throw error;
  }
}

/**
 * 予約を作成する関数
 */
async function createBooking(productId, startDate, endDate, customerEmail, status = "CONFIRMED") {
  try {
    const booking = await prisma.booking.create({
      data: {
        shopifyProductId: productId,
        shopifyOrderId: `order-${Date.now()}`,
        customerId: customerEmail,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        status: status,
        shop: "peaces-test-block.myshopify.com",
        bookingId: `BOOK-${Math.random().toString(36).substring(2, 8).toUpperCase()}`
      }
    });
    
    // 在庫カレンダーも更新
    const start = new Date(startDate);
    const end = new Date(endDate);
    const dateRange = [];
    
    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      dateRange.push(new Date(date));
    }
    
    for (const date of dateRange) {
      await prisma.inventoryCalendar.upsert({
        where: {
          shop_shopifyProductId_date: {
            shop: "peaces-test-block.myshopify.com",
            shopifyProductId: productId,
            date: date
          }
        },
        update: {
          isAvailable: false,
          unavailableReason: "reserved",
          bookingId: booking.id
        },
        create: {
          shop: "peaces-test-block.myshopify.com",
          shopifyProductId: productId,
          date: date,
          isAvailable: false,
          unavailableReason: "reserved",
          bookingId: booking.id
        }
      });
    }
    
    return booking;
  } catch (error) {
    console.error("予約作成エラー:", error);
    throw error;
  }
}

/**
 * 予約の重複をチェックする関数
 */
async function checkBookingOverlap(productId, startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  const overlappingDates = await prisma.inventoryCalendar.findMany({
    where: {
      shopifyProductId: productId,
      date: {
        gte: start,
        lte: end
      },
      isAvailable: false
    }
  });
  
  return overlappingDates.length > 0;
}

/**
 * メイン関数
 */
async function runTests() {
  try {
    console.log("予約システムの自動テストを開始します...");
    
    // Shopifyセッションを取得
    const session = await authenticate.admin();
    
    // テスト用の商品を作成
    console.log("テスト用の商品を作成しています...");
    
    const product1 = await createShopifyProduct(session, {
      title: "テスト商品A",
      productType: "レンタル商品",
      vendor: "テストベンダー",
      variants: [{ price: "5000" }],
      status: "ACTIVE"
    });
    
    const product2 = await createShopifyProduct(session, {
      title: "テスト商品B",
      productType: "レンタル商品",
      vendor: "テストベンダー",
      variants: [{ price: "10000" }],
      status: "ACTIVE"
    });
    
    const product3 = await createShopifyProduct(session, {
      title: "テスト商品C",
      productType: "レンタル商品",
      vendor: "テストベンダー",
      variants: [{ price: "15000" }],
      status: "ACTIVE"
    });
    
    logTestResult("商品作成", true, {
      message: `3つのテスト商品を作成しました: ${product1.title}, ${product2.title}, ${product3.title}`
    });
    
    // 商品のメタフィールドを設定
    console.log("商品のメタフィールドを設定しています...");
    
    const basicInfoMetafield = {
      namespace: "rental",
      key: "basic_info",
      value: JSON.stringify({
        productCode: "TEST-001",
        status: "good",
        location: "NY"
      }),
      type: "json"
    };
    
    const pricingMetafield = {
      namespace: "rental",
      key: "pricing",
      value: JSON.stringify({
        basePrice: 5000,
        depositRate: 0.1
      }),
      type: "json"
    };
    
    await setProductMetafields(session, product1.id, [basicInfoMetafield, pricingMetafield]);
    await setProductMetafields(session, product2.id, [basicInfoMetafield, pricingMetafield]);
    await setProductMetafields(session, product3.id, [basicInfoMetafield, pricingMetafield]);
    
    logTestResult("メタフィールド設定", true, {
      message: "すべての商品にメタフィールドを設定しました"
    });
    
    // テストケース1: 単一商品の予約
    console.log("テストケース1: 単一商品の予約をテストしています...");
    
    const booking1 = await createBooking(
      product1.id.replace("gid://shopify/Product/", ""),
      "2025-06-01",
      "2025-06-03",
      "<EMAIL>"
    );
    
    logTestResult("単一商品の予約", true, {
      message: `予約ID: ${booking1.id} が作成されました`,
      bookingId: booking1.id
    });
    
    // テストケース2: 同じ商品の別の日程での予約
    console.log("テストケース2: 同じ商品の別の日程での予約をテストしています...");
    
    const booking2 = await createBooking(
      product1.id.replace("gid://shopify/Product/", ""),
      "2025-06-10",
      "2025-06-12",
      "<EMAIL>"
    );
    
    logTestResult("同じ商品の別の日程での予約", true, {
      message: `予約ID: ${booking2.id} が作成されました`,
      bookingId: booking2.id
    });
    
    // テストケース3: 複数商品の同じ日程での予約
    console.log("テストケース3: 複数商品の同じ日程での予約をテストしています...");
    
    const booking3 = await createBooking(
      product2.id.replace("gid://shopify/Product/", ""),
      "2025-06-01",
      "2025-06-03",
      "<EMAIL>"
    );
    
    logTestResult("複数商品の同じ日程での予約", true, {
      message: `予約ID: ${booking3.id} が作成されました`,
      bookingId: booking3.id
    });
    
    // テストケース4: 予約の重複チェック
    console.log("テストケース4: 予約の重複チェックをテストしています...");
    
    const hasOverlap = await checkBookingOverlap(
      product1.id.replace("gid://shopify/Product/", ""),
      "2025-06-01",
      "2025-06-03"
    );
    
    logTestResult("予約の重複チェック", hasOverlap, {
      message: hasOverlap 
        ? "正しく重複を検出しました" 
        : "重複を検出できませんでした"
    });
    
    // テストケース5: 連続した予約
    console.log("テストケース5: 連続した予約をテストしています...");
    
    const booking4 = await createBooking(
      product3.id.replace("gid://shopify/Product/", ""),
      "2025-07-01",
      "2025-07-03",
      "<EMAIL>"
    );
    
    const booking5 = await createBooking(
      product3.id.replace("gid://shopify/Product/", ""),
      "2025-07-04",
      "2025-07-06",
      "<EMAIL>"
    );
    
    logTestResult("連続した予約", true, {
      message: `連続した2つの予約 (${booking4.id}, ${booking5.id}) が作成されました`,
      bookingIds: [booking4.id, booking5.id]
    });
    
    // テストケース6: 長期間の予約
    console.log("テストケース6: 長期間の予約をテストしています...");
    
    const booking6 = await createBooking(
      product2.id.replace("gid://shopify/Product/", ""),
      "2025-08-01",
      "2025-08-14",
      "<EMAIL>"
    );
    
    logTestResult("長期間の予約", true, {
      message: `14日間の予約 (${booking6.id}) が作成されました`,
      bookingId: booking6.id
    });
    
    // テストケース7: 仮予約から確定予約への変更
    console.log("テストケース7: 仮予約から確定予約への変更をテストしています...");
    
    const booking7 = await createBooking(
      product3.id.replace("gid://shopify/Product/", ""),
      "2025-09-01",
      "2025-09-03",
      "<EMAIL>",
      "PROVISIONAL"
    );
    
    const updatedBooking7 = await prisma.booking.update({
      where: { id: booking7.id },
      data: { status: "CONFIRMED" }
    });
    
    logTestResult("仮予約から確定予約への変更", updatedBooking7.status === "CONFIRMED", {
      message: `予約ID: ${updatedBooking7.id} のステータスが ${updatedBooking7.status} に更新されました`,
      bookingId: updatedBooking7.id
    });
    
    // テストケース8: 予約のキャンセル
    console.log("テストケース8: 予約のキャンセルをテストしています...");
    
    const booking8 = await createBooking(
      product1.id.replace("gid://shopify/Product/", ""),
      "2025-10-01",
      "2025-10-03",
      "<EMAIL>"
    );
    
    const cancelledBooking8 = await prisma.booking.update({
      where: { id: booking8.id },
      data: { status: "CANCELLED" }
    });
    
    // キャンセルされた予約の在庫カレンダーを更新
    await prisma.inventoryCalendar.updateMany({
      where: { bookingId: booking8.id },
      data: {
        isAvailable: true,
        unavailableReason: null,
        bookingId: null
      }
    });
    
    logTestResult("予約のキャンセル", cancelledBooking8.status === "CANCELLED", {
      message: `予約ID: ${cancelledBooking8.id} がキャンセルされました`,
      bookingId: cancelledBooking8.id
    });
    
    // テストケース9: 在庫カレンダーの確認
    console.log("テストケース9: 在庫カレンダーの確認をテストしています...");
    
    const inventoryCalendar = await prisma.inventoryCalendar.findMany({
      where: {
        shopifyProductId: product1.id.replace("gid://shopify/Product/", ""),
        date: {
          gte: new Date("2025-06-01"),
          lte: new Date("2025-06-03")
        }
      }
    });
    
    logTestResult("在庫カレンダーの確認", inventoryCalendar.length > 0, {
      message: `${inventoryCalendar.length}件の在庫カレンダーレコードが見つかりました`,
      calendarCount: inventoryCalendar.length
    });
    
    // テストケース10: 予約検索
    console.log("テストケース10: 予約検索をテストしています...");
    
    const bookings = await prisma.booking.findMany({
      where: {
        customerId: "<EMAIL>"
      }
    });
    
    logTestResult("予約検索", bookings.length > 0, {
      message: `${bookings.length}件の予約が見つかりました`,
      bookingCount: bookings.length
    });
    
    // テスト結果のサマリーを表示
    console.log("\n===== テスト結果サマリー =====");
    const successCount = testResults.filter(r => r.success).length;
    const failureCount = testResults.length - successCount;
    
    console.log(`実行したテスト数: ${testResults.length}`);
    console.log(`成功: ${successCount}`);
    console.log(`失敗: ${failureCount}`);
    console.log("============================\n");
    
    // 詳細なテスト結果を表示
    console.log("詳細なテスト結果:");
    testResults.forEach((result, index) => {
      console.log(`${index + 1}. [${result.success ? 'SUCCESS' : 'FAILURE'}] ${result.testName}`);
      if (result.message) {
        console.log(`   ${result.message}`);
      }
    });
    
  } catch (error) {
    console.error("テスト実行中にエラーが発生しました:", error);
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// テストを実行
runTests();
