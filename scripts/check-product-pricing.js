/**
 * 商品価格設定チェックスクリプト
 */

import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品詳細を取得するクエリ
const GET_PRODUCT_DETAILS = gql`
  query getProduct($id: ID!) {
    product(id: $id) {
      id
      title
      status
      variants(first: 10) {
        edges {
          node {
            id
            title
            sku
            price
            compareAtPrice
            inventoryQuantity
          }
        }
      }
    }
  }
`;

async function checkProductPricing() {
  const sku = process.argv[2] || '201-07-107';
  
  console.log(`🔍 商品 ${sku} の価格設定チェックを開始...\n`);

  try {
    // データベースから商品情報を取得
    const dbProduct = await prisma.product.findFirst({
      where: { sku: sku }
    });

    if (!dbProduct) {
      console.log(`❌ SKU ${sku} の商品がデータベースに見つかりません`);
      return;
    }

    console.log('=== データベースの商品情報 ===');
    console.log(`ID: ${dbProduct.id}`);
    console.log(`タイトル: ${dbProduct.title}`);
    console.log(`SKU: ${dbProduct.sku}`);
    console.log(`価格: ¥${dbProduct.price ? (dbProduct.price / 100).toLocaleString() : '未設定'}`);
    console.log(`価格（生値）: ${dbProduct.price}`);
    console.log(`Shopify ID: ${dbProduct.shopifyId}`);
    console.log('');

    // Shopifyから商品情報を取得
    const shopifyId = `gid://shopify/Product/${dbProduct.shopifyId}`;
    const shopifyResult = await shopifyClient.request(GET_PRODUCT_DETAILS, {
      id: shopifyId
    });

    if (!shopifyResult.product) {
      console.log(`❌ Shopify商品ID ${dbProduct.shopifyId} が見つかりません`);
      return;
    }

    console.log('=== Shopifyの商品情報 ===');
    console.log(`タイトル: ${shopifyResult.product.title}`);
    console.log(`ステータス: ${shopifyResult.product.status}`);
    console.log('');

    console.log('=== Shopifyバリアント情報 ===');
    shopifyResult.product.variants.edges.forEach((edge, index) => {
      const variant = edge.node;
      console.log(`${index + 1}. ${variant.title}`);
      console.log(`   SKU: ${variant.sku}`);
      console.log(`   価格: ¥${parseFloat(variant.price).toLocaleString()}`);
      console.log(`   価格（生値）: ${variant.price}`);
      if (variant.compareAtPrice) {
        console.log(`   比較価格: ¥${parseFloat(variant.compareAtPrice).toLocaleString()}`);
      }
      console.log(`   在庫: ${variant.inventoryQuantity}`);
      console.log('');
    });

    // 価格計算ロジックをテスト
    console.log('=== 価格計算テスト ===');
    const startDate = new Date('2025-05-23');
    const endDate = new Date('2025-05-26');
    const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
    
    console.log(`レンタル期間: ${days}日間`);
    console.log(`開始日: ${startDate.toISOString().split('T')[0]}`);
    console.log(`終了日: ${endDate.toISOString().split('T')[0]}`);
    console.log('');

    // 基本価格（1日料金）を取得
    const basePrice = dbProduct.price || 0;
    console.log(`基本価格（DB）: ¥${(basePrice / 100).toLocaleString()}`);

    // Shopifyの1日レンタル価格を取得
    const oneDayVariant = shopifyResult.product.variants.edges.find(edge => 
      edge.node.sku && edge.node.sku.includes('-1D')
    );
    
    if (oneDayVariant) {
      const shopifyBasePrice = parseFloat(oneDayVariant.node.price);
      console.log(`Shopify 1日価格: ¥${shopifyBasePrice.toLocaleString()}`);
      console.log(`Shopify 1日価格（セント）: ${shopifyBasePrice * 100}`);
    }

    // 価格計算ロジック（レンタル期間に応じた割引）
    let totalPrice = 0;
    if (days === 1) {
      totalPrice = basePrice; // 1日: 100%
    } else if (days >= 2 && days <= 7) {
      totalPrice = basePrice + (basePrice * 0.2 * (days - 1)); // 2-7日: 20%/日
    } else if (days >= 8) {
      totalPrice = basePrice + (basePrice * 0.2 * 6) + (basePrice * 0.1 * (days - 7)); // 8日以上: 10%/日
    }

    console.log('');
    console.log('=== 計算結果 ===');
    console.log(`${days}日間レンタルの場合:`);
    console.log(`計算価格: ¥${(totalPrice / 100).toLocaleString()}`);
    console.log(`計算価格（セント）: ${totalPrice}`);

    // 正しい価格を計算（4,200円になるべき）
    console.log('');
    console.log('=== 期待される価格 ===');
    console.log(`期待価格: ¥4,200`);
    console.log(`期待価格（セント）: 420,000`);

    // 逆算して正しい基本価格を計算
    const expectedTotal = 420000; // 4,200円をセント単位
    let correctBasePrice = 0;
    
    if (days === 1) {
      correctBasePrice = expectedTotal;
    } else if (days >= 2 && days <= 7) {
      // totalPrice = basePrice + (basePrice * 0.2 * (days - 1))
      // totalPrice = basePrice * (1 + 0.2 * (days - 1))
      correctBasePrice = expectedTotal / (1 + 0.2 * (days - 1));
    } else if (days >= 8) {
      // totalPrice = basePrice + (basePrice * 0.2 * 6) + (basePrice * 0.1 * (days - 7))
      // totalPrice = basePrice * (1 + 0.2 * 6 + 0.1 * (days - 7))
      correctBasePrice = expectedTotal / (1 + 0.2 * 6 + 0.1 * (days - 7));
    }

    console.log(`${days}日間で¥4,200にするための基本価格: ¥${(correctBasePrice / 100).toLocaleString()}`);
    console.log(`${days}日間で¥4,200にするための基本価格（セント）: ${Math.round(correctBasePrice)}`);

    // 実際の予約データを確認
    console.log('');
    console.log('=== 実際の予約データ確認 ===');
    const recentBookings = await prisma.booking.findMany({
      where: {
        productId: dbProduct.id
      },
      orderBy: { createdAt: 'desc' },
      take: 3
    });

    if (recentBookings.length > 0) {
      console.log(`最近の予約: ${recentBookings.length}件`);
      recentBookings.forEach((booking, index) => {
        const bookingDays = Math.ceil((booking.endDate - booking.startDate) / (1000 * 60 * 60 * 24));
        console.log(`${index + 1}. ${booking.bookingId}`);
        console.log(`   期間: ${booking.startDate.toISOString().split('T')[0]} 〜 ${booking.endDate.toISOString().split('T')[0]} (${bookingDays}日)`);
        console.log(`   金額: ¥${booking.totalAmount ? (booking.totalAmount / 100).toLocaleString() : '未設定'}`);
        console.log(`   金額（セント）: ${booking.totalAmount}`);
      });
    } else {
      console.log('予約データなし');
    }

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
checkProductPricing();
