{"timestamp": "2025-05-19T03:42:24.895Z", "duration": 5.473, "totalTests": 4, "successCount": 3, "failureCount": 1, "tests": [{"testName": "商品情報取得エラーのユニットテスト", "success": false, "timestamp": "2025-05-19T03:42:28.032Z", "error": "テスト実行中にエラーが発生しました", "details": {"duration": 3.131, "output": "\n RUN  v1.6.1 /Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp\n\n ❯ app/tests/calendar/product-fetch-error.test.ts  (0 test)\n\n Test Files  1 failed (1)\n      Tests  no tests\n   Start at  12:42:27\n   Duration  329ms (transform 81ms, setup 0ms, collect 0ms, tests 0ms, environment 0ms, prepare 190ms)\n\n"}}, {"testName": "商品情報取得エラーのE2Eテスト", "success": true, "timestamp": "2025-05-19T03:42:28.588Z", "message": "テストが正常に完了しました (0.56秒)", "details": {"duration": 0.556}}, {"testName": "統一カレンダーのテスト", "success": true, "timestamp": "2025-05-19T03:42:29.340Z", "message": "テストが正常に完了しました (0.75秒)", "details": {"duration": 0.751}}, {"testName": "チェックアウト統合テスト", "success": true, "timestamp": "2025-05-19T03:42:30.368Z", "message": "テストが正常に完了しました (1.03秒)", "details": {"duration": 1.026}}]}