// scripts/fix-booking-styles.js
// 予約スタイル適用関数を修正するスクリプト

// 予約スタイル適用関数を修正する
const fixBookingStyles = () => {
  console.log('予約スタイル適用関数を修正します');

  // 元の関数を保存
  const originalApplyBookingStyles = window.applyBookingStyles;

  // 新しい関数を定義
  window.applyBookingStyles = (bookings) => {
    console.log('修正された予約スタイル適用関数を実行します:', bookings.length, '件');

    // カレンダーの読み込み完了を確認するための遅延
    setTimeout(() => {
      // 現在表示されている月を取得
      const currentMonthElement = document.querySelector('.Polaris-DatePicker__Title');
      const currentMonthText = currentMonthElement ? currentMonthElement.textContent : '';
      console.log('現在表示されている月:', currentMonthText);

      if (!currentMonthText) {
        console.log('月情報が取得できませんでした');
        return;
      }

      // 月の比較用に月名と年を取得
      const displayedMonthEn = currentMonthText.split(' ')[0]; // "June" from "June 2025"
      const displayedYear = parseInt(currentMonthText.split(' ')[1] || '0'); // "2025" from "June 2025"
      console.log(`表示月=${displayedMonthEn} ${displayedYear}`);

      // 各予約に対して処理
      bookings.forEach(booking => {
        const startDate = new Date(booking.startDate);
        const endDate = new Date(booking.endDate);
        const bookingType = booking.bookingType;

        // 予約期間の各日付にスタイルを適用
        const currentDate = new Date(startDate);
        while (currentDate <= endDate) {
          const dateStr = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD
          
          // 予約の月と表示月が異なる場合はスキップ
          const bookingMonthEn = currentDate.toLocaleString('en-US', { month: 'long' });
          const bookingYear = currentDate.getFullYear();
          
          console.log(`比較: 予約日=${dateStr}, 予約月=${bookingMonthEn} ${bookingYear}, 表示月=${displayedMonthEn} ${displayedYear}`);
          
          if (bookingMonthEn !== displayedMonthEn || bookingYear !== displayedYear) {
            console.log('月が異なるためスキップします:', dateStr);
            // 次の日付に進む
            currentDate.setDate(currentDate.getDate() + 1);
            continue;
          }

          // 日付に対応する要素を探す
          const day = currentDate.getDate(); // 日付の数字
          
          // aria-labelに日付情報が含まれている要素を探す
          const dateElements = document.querySelectorAll(`.Polaris-DatePicker__Day[aria-label*="${bookingMonthEn} ${day}, ${bookingYear}"]`);
          console.log(`日付要素を探します: ${bookingMonthEn} ${day}, ${bookingYear}`, dateElements.length, '個見つかりました');

          dateElements.forEach(element => {
            const spanElement = element.querySelector('span');
            if (spanElement) {
              // 予約タイプに応じたスタイルを適用
              if (bookingType === 'PROVISIONAL') {
                spanElement.style.color = '#d82c0d';
                spanElement.style.position = 'relative';
                spanElement.style.textDecoration = 'line-through';

                // 「仮」のマークを追加
                const afterElement = document.createElement('span');
                afterElement.textContent = '仮';
                afterElement.style.position = 'absolute';
                afterElement.style.bottom = '-11px';
                afterElement.style.left = '50%';
                afterElement.style.transform = 'translateX(-50%)';
                afterElement.style.fontSize = '0.6rem';
                afterElement.style.color = '#d82c0d';
                afterElement.style.fontWeight = 'bold';

                // 既存のマークがあれば削除
                const existingAfter = spanElement.querySelector('.booking-mark');
                if (existingAfter) {
                  existingAfter.remove();
                }

                afterElement.classList.add('booking-mark');
                spanElement.appendChild(afterElement);

                // データ属性を追加
                element.setAttribute('data-booking-type', 'provisional');
                element.setAttribute('data-booked', 'true');

                console.log(`仮予約スタイルを適用しました: ${dateStr}`);
              } else if (bookingType === 'CONFIRMED') {
                spanElement.style.color = '#00848e';
                spanElement.style.position = 'relative';
                spanElement.style.textDecoration = 'line-through';

                // 「予」のマークを追加
                const afterElement = document.createElement('span');
                afterElement.textContent = '予';
                afterElement.style.position = 'absolute';
                afterElement.style.bottom = '-11px';
                afterElement.style.left = '50%';
                afterElement.style.transform = 'translateX(-50%)';
                afterElement.style.fontSize = '0.6rem';
                afterElement.style.color = '#00848e';
                afterElement.style.fontWeight = 'bold';

                // 既存のマークがあれば削除
                const existingAfter = spanElement.querySelector('.booking-mark');
                if (existingAfter) {
                  existingAfter.remove();
                }

                afterElement.classList.add('booking-mark');
                spanElement.appendChild(afterElement);

                // データ属性を追加
                element.setAttribute('data-booking-type', 'confirmed');
                element.setAttribute('data-booked', 'true');

                console.log(`確定予約スタイルを適用しました: ${dateStr}`);
              }
            }
          });

          // 次の日付に進む
          currentDate.setDate(currentDate.getDate() + 1);
        }
      });
    }, 1000);
  };

  console.log('予約スタイル適用関数を修正しました');
};

// ページ読み込み時に実行
document.addEventListener('DOMContentLoaded', () => {
  console.log('予約スタイル適用関数の修正スクリプトを実行します');
  setTimeout(fixBookingStyles, 1000);
});
