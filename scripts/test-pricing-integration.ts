/**
 * 料金計算統合テストスクリプト
 *
 * バリエーション価格と統一計算ロジックの整合性を包括的にテストします
 */

import { PricingValidationService } from '../app/services/pricing/pricing-validation.service';
import { UnifiedPricingService } from '../app/services/pricing/unified-pricing.service';

interface TestCase {
  name: string;
  basePrice: number;
  variantPrices: Array<{ days: number; price: number; title: string }>;
  expectedResults: Array<{ days: number; expectedPrice: number }>;
}

class PricingIntegrationTester {
  private validationService: PricingValidationService;
  private pricingService: UnifiedPricingService;
  private results: Array<{ testName: string; success: boolean; details: string }> = [];

  constructor() {
    this.validationService = PricingValidationService.getInstance();
    this.pricingService = UnifiedPricingService.getInstance();
  }

  /**
   * 統合テストを実行
   */
  async runIntegrationTests(): Promise<void> {
    console.log('=== 料金計算統合テスト開始 ===\n');

    const testCases: TestCase[] = [
      {
        name: '基本料金3000円の商品',
        basePrice: 3000,
        variantPrices: [
          { days: 1, price: 3000, title: '1日レンタル' },
          { days: 2, price: 3600, title: '2日レンタル' },
          { days: 3, price: 4200, title: '3日レンタル' },
          { days: 4, price: 4800, title: '4日レンタル' },
          { days: 5, price: 5400, title: '5日レンタル' },
          { days: 6, price: 6000, title: '6日レンタル' },
          { days: 7, price: 6300, title: '7日レンタル' }, // 実際の画像の価格（統一計算とは異なる）
        ],
        expectedResults: [
          { days: 1, expectedPrice: 3000 },
          { days: 2, expectedPrice: 3600 },
          { days: 3, expectedPrice: 4200 },
          { days: 4, expectedPrice: 4800 },
          { days: 5, expectedPrice: 5400 },
          { days: 6, expectedPrice: 6000 },
          { days: 7, expectedPrice: 6600 }, // 3000 + (3000 * 0.2 * 6) = 3000 + 3600 = 6600
          { days: 8, expectedPrice: 6900 }, // 3000 + (3000 * 0.2 * 6) + (3000 * 0.1 * 1) = 6600 + 300 = 6900
          { days: 10, expectedPrice: 7500 }, // 3000 + (3000 * 0.2 * 6) + (3000 * 0.1 * 3) = 6600 + 900 = 7500
        ]
      },
      {
        name: '基本料金1800円の商品（テスト用）',
        basePrice: 1800,
        variantPrices: [
          { days: 1, price: 1800, title: '1日レンタル' },
          { days: 2, price: 2160, title: '2日レンタル' },
          { days: 3, price: 2520, title: '3日レンタル' },
          { days: 4, price: 2880, title: '4日レンタル' },
          { days: 5, price: 3240, title: '5日レンタル' },
          { days: 6, price: 3600, title: '6日レンタル' },
          { days: 7, price: 3960, title: '7日レンタル' },
        ],
        expectedResults: [
          { days: 1, expectedPrice: 1800 },
          { days: 2, expectedPrice: 2160 },
          { days: 3, expectedPrice: 2520 },
          { days: 4, expectedPrice: 2880 },
          { days: 5, expectedPrice: 3240 },
          { days: 6, expectedPrice: 3600 },
          { days: 7, expectedPrice: 3960 },
          { days: 8, expectedPrice: 4140 },
          { days: 10, expectedPrice: 4500 },
        ]
      },
      {
        name: '仮予約テストケース（基本料金3000円）',
        basePrice: 3000,
        variantPrices: [
          { days: 1, price: 300, title: '1日仮予約' }, // 3000 * 0.1 = 300
          { days: 3, price: 420, title: '3日仮予約' }, // 4200 * 0.1 = 420
          { days: 7, price: 660, title: '7日仮予約' }, // 6600 * 0.1 = 660
        ],
        expectedResults: [
          { days: 1, expectedPrice: 300 }, // 仮予約: 3000 * 0.1
          { days: 3, expectedPrice: 420 }, // 仮予約: 4200 * 0.1
          { days: 7, expectedPrice: 660 }, // 仮予約: 6600 * 0.1
        ]
      }
    ];

    for (const testCase of testCases) {
      await this.runSingleTest(testCase);
    }

    this.displayResults();
  }

  /**
   * 単一テストケースを実行
   */
  private async runSingleTest(testCase: TestCase): Promise<void> {
    console.log(`\n--- ${testCase.name} ---`);

    try {
      // 1. 整合性チェック
      console.log('1. バリエーション価格の整合性チェック');
      const validation = this.validationService.validatePricing(
        testCase.basePrice,
        testCase.variantPrices
      );

      console.log(`整合性: ${validation.isValid ? '✅ 正常' : '❌ 不整合あり'}`);

      if (!validation.isValid) {
        validation.discrepancies.forEach(disc => {
          console.log(`  ${disc.days}日: バリエーション=${disc.variantPrice}円 vs 計算=${disc.calculatedPrice}円 (差額: ${disc.difference}円)`);
        });
      }

      // 2. 最適価格ソース決定テスト
      console.log('\n2. 最適価格ソース決定テスト');
      for (const expected of testCase.expectedResults) {
        const result = this.validationService.determineBestPricingSource(
          expected.days,
          testCase.basePrice,
          testCase.variantPrices
        );

        const isCorrect = Math.abs(result.price - expected.expectedPrice) <= 10; // 10円の許容誤差
        console.log(`  ${expected.days}日: ${result.price}円 (${result.source.type}) ${isCorrect ? '✅' : '❌'}`);
        console.log(`    理由: ${result.source.reason}`);

        if (!isCorrect) {
          console.log(`    期待値: ${expected.expectedPrice}円, 実際: ${result.price}円`);
        }
      }

      // 3. 正しいバリエーション価格生成テスト
      console.log('\n3. 正しいバリエーション価格生成テスト');
      const correctPrices = this.validationService.generateCorrectVariantPrices(testCase.basePrice);

      console.log('生成されたバリエーション価格:');
      correctPrices.forEach(price => {
        if (price.days <= 7) {
          const expected = testCase.expectedResults.find(e => e.days === price.days);
          const isCorrect = expected && Math.abs(price.price - expected.expectedPrice) <= 10;
          console.log(`  ${price.days}日: ${price.price}円 ${isCorrect ? '✅' : '❌'}`);
        }
      });

      // 4. 8日以上の計算テスト
      console.log('\n4. 8日以上の計算テスト');
      for (const expected of testCase.expectedResults.filter(e => e.days >= 8)) {
        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(startDate.getDate() + expected.days - 1);

        const result = this.pricingService.calculatePrice(startDate, endDate, testCase.basePrice);
        const isCorrect = Math.abs(result.totalPrice - expected.expectedPrice) <= 10;

        console.log(`  ${expected.days}日: ${result.totalPrice}円 ${isCorrect ? '✅' : '❌'}`);

        if (!isCorrect) {
          console.log(`    期待値: ${expected.expectedPrice}円, 実際: ${result.totalPrice}円`);
        }
      }

      // 5. 整合性レポート生成テスト
      console.log('\n5. 整合性レポート生成');
      const report = this.validationService.generateValidationReport(
        testCase.name,
        testCase.basePrice,
        testCase.variantPrices
      );

      console.log('--- レポート ---');
      console.log(report);
      console.log('--- レポート終了 ---');

      this.results.push({
        testName: testCase.name,
        success: validation.isValid,
        details: `不整合: ${validation.discrepancies.length}件, 不足バリエーション: ${validation.missingVariants.length}件`
      });

    } catch (error) {
      console.error(`❌ テストエラー: ${error.message}`);
      this.results.push({
        testName: testCase.name,
        success: false,
        details: `エラー: ${error.message}`
      });
    }
  }

  /**
   * テスト結果を表示
   */
  private displayResults(): void {
    console.log('\n=== 統合テスト結果サマリー ===');

    const successCount = this.results.filter(r => r.success).length;
    const totalCount = this.results.length;

    console.log(`成功: ${successCount}/${totalCount}`);
    console.log(`失敗: ${totalCount - successCount}/${totalCount}`);

    this.results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${result.testName}: ${result.details}`);
    });

    console.log('\n=== 統合テスト終了 ===');
  }

  /**
   * 仮予約専用テスト
   */
  async runProvisionalBookingTest(): Promise<void> {
    console.log('\n=== 仮予約専用テスト ===');

    try {
      const basePrice = 3000;
      const testCases = [
        { days: 1, startDate: new Date('2025-05-24'), endDate: new Date('2025-05-24') },
        { days: 3, startDate: new Date('2025-05-24'), endDate: new Date('2025-05-26') },
        { days: 7, startDate: new Date('2025-05-24'), endDate: new Date('2025-05-30') },
        { days: 10, startDate: new Date('2025-05-24'), endDate: new Date('2025-06-02') },
      ];

      for (const testCase of testCases) {
        console.log(`\n--- ${testCase.days}日間仮予約テスト ---`);

        // 正規予約計算
        const confirmed = this.pricingService.calculateConfirmedPrice(
          testCase.startDate,
          testCase.endDate,
          basePrice
        );

        // 仮予約計算
        const provisional = this.pricingService.calculateProvisionalPrice(
          testCase.startDate,
          testCase.endDate,
          basePrice
        );

        // 差額計算
        const upgrade = this.pricingService.calculateUpgradePrice(
          testCase.startDate,
          testCase.endDate,
          basePrice
        );

        console.log(`正規予約: ${this.pricingService.formatPrice(confirmed.totalPrice)}`);
        console.log(`仮予約: ${this.pricingService.formatPrice(provisional.totalPrice)}`);
        console.log(`差額: ${this.pricingService.formatPrice(upgrade.upgradeAmount)}`);

        // 検証
        const expectedProvisional = Math.round(confirmed.totalPrice * 0.1);
        const isCorrect = Math.abs(provisional.totalPrice - expectedProvisional) <= 1;

        console.log(`検証: ${isCorrect ? '✅' : '❌'} (期待値: ${expectedProvisional}円)`);
        console.log(`仮予約フラグ: ${provisional.isProvisional ? '✅' : '❌'}`);
        console.log(`正規料金保持: ${provisional.fullPrice === confirmed.totalPrice ? '✅' : '❌'}`);

        if (!isCorrect) {
          console.log(`  実際: ${provisional.totalPrice}円, 期待: ${expectedProvisional}円, 差: ${Math.abs(provisional.totalPrice - expectedProvisional)}円`);
        }
      }

      console.log('\n=== 仮予約専用テスト終了 ===');

    } catch (error) {
      console.error('❌ 仮予約専用テストエラー:', error.message);
    }
  }

  /**
   * パフォーマンステスト
   */
  async runPerformanceTest(): Promise<void> {
    console.log('\n=== パフォーマンステスト ===');

    const iterations = 1000;
    const basePrice = 3000;
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(startDate.getDate() + 6); // 7日間

    console.log(`${iterations}回の料金計算を実行...`);

    const startTime = Date.now();

    for (let i = 0; i < iterations; i++) {
      this.pricingService.calculatePrice(startDate, endDate, basePrice);
    }

    const endTime = Date.now();
    const totalTime = endTime - startTime;
    const avgTime = totalTime / iterations;

    console.log(`総実行時間: ${totalTime}ms`);
    console.log(`平均実行時間: ${avgTime.toFixed(2)}ms`);
    console.log(`1秒あたりの処理数: ${Math.round(1000 / avgTime)}回`);

    if (avgTime < 1) {
      console.log('✅ パフォーマンス: 良好');
    } else if (avgTime < 5) {
      console.log('⚠️ パフォーマンス: 普通');
    } else {
      console.log('❌ パフォーマンス: 改善が必要');
    }
  }
}

// メイン実行
async function main() {
  const tester = new PricingIntegrationTester();

  await tester.runIntegrationTests();
  await tester.runProvisionalBookingTest();
  await tester.runPerformanceTest();
}

if (import.meta.url === new URL(import.meta.url).href) {
  main().catch(console.error);
}

export { PricingIntegrationTester };
