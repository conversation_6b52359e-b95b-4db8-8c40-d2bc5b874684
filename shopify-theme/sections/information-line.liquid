{% capture style_base_html %}
    <style>
        :root {
            {% if section.settings.type == '1' %}
                --information-line-type-1-title-size: var(--h6-size);
                --information-line-type-1-title-line-height: var(--h6-line-height);
                --information-line-type-1-title-letter-spacing: var(--h6-letter-spacing);
                --information-line-type-1-title-weight: var(--h6-weight);
            {% elsif section.settings.type == '2' %}
                --information-line-type-2-title-size: var(--h4-size);
                --information-line-type-2-title-line-height: var(--h4-line-height);
                --information-line-type-2-title-letter-spacing: var(--h4-letter-spacing);
                --information-line-type-2-title-weight: var(--h4-weight);
            {% elsif section.settings.type == '3' %}
                --information-line-type-1-paragraph-size: var(--body-size);
                --information-line-type-1-paragraph-line-height: var(--body-line-height);
                --information-line-type-1-paragraph-letter-spacing: var(--body-letter-spacing);
                --information-line-type-1-paragraph-weight: var(--body-weight);
            {% elsif section.settings.type == '4' %}
                --information-line-type-2-paragraph-size: var(--body-size);
                --information-line-type-2-paragraph-line-height: var(--body-line-height);
                --information-line-type-2-paragraph-letter-spacing: var(--body-letter-spacing);
                --information-line-type-2-paragraph-weight: var(--body-weight);
            {% endif %}

            {% if section.settings.style == '1' %}
                --info-line-style-1-bg: var(--theme-body);
                --info-line-style-1-bd-top: solid 1px var(--theme5);
                --info-line-style-1-bd-bottom: solid 1px var(--theme5);
                --info-line-style-1-icon-c: var(--theme-primary);
                --info-line-style-1-title-c: var(--theme-head-c);
                --info-line-style-1-paragraph-c: var(--theme-c);
            {% elsif section.settings.style == '2' %}
                --info-line-style-2-bg: var(--theme-body);
                --info-line-style-2-bd-top: solid 1px var(--theme5);
                --info-line-style-2-bd-bottom: none;
                --info-line-style-2-icon-c: var(--theme-primary);
                --info-line-style-2-title-c: var(--theme-head-c);
                --info-line-style-2-paragraph-c: var(--theme-c);
            {% elsif section.settings.style == '3' %}
                --info-line-style-3-bg: var(--theme-body);
                --info-line-style-3-bd-top: none;
                --info-line-style-3-bd-bottom: none;
                --info-line-style-3-icon-c: var(--theme-primary);
                --info-line-style-3-title-c: var(--theme-head-c);
                --info-line-style-3-paragraph-c: var(--theme-c);
            {% elsif section.settings.style == '4' %}
                --info-line-style-4-bg: var(--theme);
                --info-line-style-4-bd-top: none;
                --info-line-style-4-bd-bottom: none;
                --info-line-style-4-icon-c: var(--theme-body);
                --info-line-style-4-title-c: var(--theme-body);
                --info-line-style-4-paragraph-c: var(--theme-c);
            {% elsif section.settings.style == '5' %}
                --info-line-style-5-bg: var(--theme-primary);
                --info-line-style-5-bd-top: none;
                --info-line-style-5-bd-bottom: none;
                --info-line-style-5-icon-c: var(--theme-body);
                --info-line-style-5-title-c: var(--theme-body);
                --info-line-style-5-paragraph-c: var(--theme-body);
            {% endif %}

            {% if settings.layout_settings_file == 'skin-5' %}
                --info-line-style-1-icon-c: #1278A5;
                --info-line-style-2-icon-c: #1278A5;
                --info-line-style-3-icon-c: #1278A5;
            {% elsif settings.layout_settings_file == 'skin-15' %}
                --information-line-type-2-paragraph-size: var(--body-lg-size);
                --information-line-type-2-paragraph-line-height: var(--body-lg-line-height);
                --information-line-type-2-paragraph-letter-spacing: var(--body-lg-letter-spacing);
                --information-line-type-2-paragraph-weight: var(--body-lg-weight);
                --info-line-style-2-title-c: var(--theme-primary);
            {% endif %}
        }

        .information-line--style-{{ section.settings.style }} {
            background-color: var(--info-line-style-{{ section.settings.style }}-bg);
            border-top: var(--info-line-style-{{ section.settings.style }}-bd-top);
            border-bottom: var(--info-line-style-{{ section.settings.style }}-bd-bottom);
        }
        .information-line--style-{{ section.settings.style }} .icon {
            fill: var(--info-line-style-{{ section.settings.style }}-icon-c);
        }
        .information-line--style-{{ section.settings.style }} h6 {
            color: var(--info-line-style-{{ section.settings.style }}-title-c);
        }
        .information-line--style-{{ section.settings.style }} p {
            color: var(--info-line-style-{{ section.settings.style }}-paragraph-c);
        }
        .information-line--type-{{ section.settings.type }} p {
            font-size: var(--information-line-type-{{ section.settings.style }}-paragraph-size);
            line-height: var(--information-line-type-{{ section.settings.style }}-paragraph-line-height);
            letter-spacing: var(--information-line-type-{{ section.settings.style }}-paragraph-letter-spacing);
            font-weight: var(--information-line-type-{{ section.settings.style }}-paragraph-weight);
        }
        .information-line--type-{{ section.settings.type }} .information-line__title {
            font-size: var(--information-line-type-{{ section.settings.style }}-title-size);
            line-height: var(--information-line-type-{{ section.settings.style }}-title-line-height);
            letter-spacing: var(--information-line-type-{{ section.settings.style }}-title-letter-spacing);
            font-weight: var(--information-line-type-{{ section.settings.style }}-title-weight);
        }
    </style>
{% endcapture %}
{%- if style_base_html -%}
    {%- if request.design_mode -%}
        {{ style_base_html }}
    {%- else -%}
        <template class="js-loader-inline-style" data-key="{{ style_base_html | hmac_sha1: 'secret_key' }}">{{ style_base_html }}</template>
    {%- endif -%}
{%- endif -%}
{%- if section.blocks.size > 0 -%}
    {% include 'global-variables' %}
    <div data-section-id="{{ section.id }}" data-section-type="information-line">
        {%- if section.settings.title != blank -%}
            <h2 class="h4 information-line-title home-section-title mb-30 text-center">{{ section.settings.title }}</h2>
        {%- endif -%}
        <div class="information-line information-line--type-{{ section.settings.type }} information-line--style-{{ section.settings.style }}">
            <div class="container">
                <div class="row py-{{ information_line_padding_v }}">
                    {%- for block in section.blocks -%}
                        <{% if block.settings.url != blank %}a href="{{ block.settings.url }}"{% else %}div{% endif %} class="col-12 col-lg-{{ 12 | divided_by: section.blocks.size }}">
                            {%- if section.settings.type == '1' -%}
                                <div class="d-flex align-items-center justify-content-start justify-content-lg-start py-10">
                                    <i class="mr-15">{% include 'load-icon' with name: block.settings.icon %}</i>
                                    <div>
                                        {%- if block.settings.title != blank -%}
                                            <h6 class="information-line__title d-inline">{{ block.settings.title }} </h6>
                                        {%- endif -%}
                                        {%- if block.settings.paragraph != blank -%}
                                            <p class="d-inline">{{ block.settings.paragraph }}</p>
                                        {%- endif -%}
                                    </div>
                                </div>
                            {%- else -%}
                                <div class="d-flex align-items-start justify-content-start py-10">
                                    <i class="mr-15">{% include 'load-icon' with name: block.settings.icon %}</i>
                                    <div>
                                        {%- if block.settings.title != blank -%}
                                            <h6 class="information-line__title mb-0">{{ block.settings.title }} </h6>
                                        {%- endif -%}
                                        {%- if block.settings.paragraph != blank -%}
                                            <p class="d-inline">{{ block.settings.paragraph }}</p>
                                        {%- endif -%}
                                    </div>
                                </div>
                            {%- endif -%}
                        </{% if block.settings.url != blank %}a{% else %}div{% endif %}>
                    {%- endfor -%}
                </div>
            </div>
        </div>
    </div>
{%- else -%}
    {% render 'no-blocks' %}
{%- endif -%}


{% schema %}
{
    "name": "t:sections.information_line.name",
    "settings": [
        {
            "type": "textarea",
            "id": "title",
            "label": "t:sections.information_line.settings.title.label"
        },
        {
            "type": "select",
            "id": "type",
            "label": "t:sections.information_line.settings.type.label",
            "default": "1",
            "options": [
                {
                    "value": "1",
                    "label": "t:sections.information_line.settings.type.option__1.label"
                },
                {
                    "value": "2",
                    "label": "t:sections.information_line.settings.type.option__2.label"
                }
            ]
        },
        {
            "type": "select",
            "id": "style",
            "label": "t:sections.information_line.settings.style.label",
            "default": "1",
            "options": [
                {
                    "value": "1",
                    "label": "t:sections.information_line.settings.style.option__1.label"
                },
                {
                    "value": "2",
                    "label": "t:sections.information_line.settings.style.option__2.label"
                },
                {
                    "value": "3",
                    "label": "t:sections.information_line.settings.style.option__3.label"
                },
                {
                    "value": "4",
                    "label": "t:sections.information_line.settings.style.option__4.label"
                },
                {
                    "value": "5",
                    "label": "t:sections.information_line.settings.style.option__5.label"
                }
            ]
        },
        {
            "type": "header",
            "content": "t:sections.information_line.settings.header__1.content"
        },
        {
            "type": "paragraph",
            "content": "t:sections.information_line.settings.paragraph__1.content"
        },
        {
            "type": "paragraph",
            "content": "t:sections.information_line.settings.paragraph__2.content"
        }
    ],
    "blocks": [
        {
            "type": "content",
            "name": "t:sections.information_line.blocks.content.name",
            "limit": 4,
            "settings": [
                {
                    "type": "text",
                    "id": "icon",
                    "label": "t:sections.information_line.blocks.content.settings.icon.label",
                    "default": "theme-116"
                },
                {
                    "type": "text",
                    "id": "title",
                    "label": "t:sections.information_line.blocks.content.settings.title.label",
                    "default": "タイトルテキスト"  // "Title text."
                },
                {
                    "type": "text",
                    "id": "paragraph",
                    "label": "t:sections.information_line.blocks.content.settings.paragraph.label",
                    "default": "パラグラフ文章のサンプル" // "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Culpa error iste provident."
                },
                {
                    "type": "url",
                    "id": "url",
                    "label": "t:sections.information_line.blocks.content.settings.url.label"
                }
            ]
        }
    ],
    "presets": [
        {
            "name": { "en": "Information line", "ja": "インフォーメーションライン"},
            "category": "3) Static content",
            "blocks": [
                {
                    "type": "content",
                    "settings": {
                        "icon": "theme-116",
                        "title": "送料無料",
                        "paragraph": "ご注文合計金額が5,000円以上で送料無料。"
                    }
                },
                {
                    "type": "content",
                    "settings": {
                        "icon": "theme-125",
                        "title": "お支払い方法",
                        "paragraph": "クレジットカード: Visa, MasterCard, Maestro, American Express."
                    }
                },
                {
                    "type": "content",
                    "settings": {
                        "icon": "theme-009",
                        "title": "返品について",
                        "paragraph": "商品到着日時より7日以内の返品を受け付けております。"
                    }
                }
            ]
        }
    ]
}
{% endschema %}