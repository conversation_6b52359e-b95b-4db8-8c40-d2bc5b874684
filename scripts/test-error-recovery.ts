/**
 * エラー回復とロバスト性のテスト
 *
 * このスクリプトは、API障害やネットワーク切断時の回復メカニズム、
 * 一時的なエラー発生時の自動リトライ機能、データ不整合発生時の自動修復機能をテストします。
 *
 * 実行方法: npx tsx scripts/test-error-recovery.ts
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';
import { addDays, format, differenceInMilliseconds } from 'date-fns';
import { ja } from 'date-fns/locale';
import { CartService } from '../app/services/cart.service';
import { BookingService } from '../app/services/booking.service';
import * as fs from 'fs';
import * as path from 'path';
import fetch from 'node-fetch';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト結果の型定義
interface TestResult {
  testName: string;
  success: boolean;
  timestamp: string;
  message?: string;
  error?: string;
  details?: Record<string, any>;
  duration?: number; // ミリ秒単位
}

// テスト結果セットの型定義
interface TestResultSet {
  timestamp: string;
  duration: number;
  totalTests: number;
  successCount: number;
  failureCount: number;
  tests: TestResult[];
  environment: {
    nodeVersion: string;
    os: string;
  };
  performance: {
    averageTestDuration: number;
    slowestTest: {
      name: string;
      duration: number;
    };
    fastestTest: {
      name: string;
      duration: number;
    };
  };
}

// テスト設定
const config = {
  // テストする商品のShopify ID
  productId: process.argv[2] || '123456789', // コマンドライン引数から取得、または既定値を使用

  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',

  // テスト用の顧客情報
  customer: {
    email: '<EMAIL>',
    name: 'テストユーザー',
    phone: '090-1234-5678',
    address: '東京都渋谷区'
  },

  // 予約日数（今日から何日後に予約するか）
  startDaysFromNow: 7,
  durationDays: 3,

  // レポート出力設定
  outputDir: path.join(process.cwd(), 'docs/test-results'),

  // リトライ設定
  maxRetries: 3,
  retryDelay: 1000, // ミリ秒

  // タイムアウト設定
  timeout: 5000 // ミリ秒
};

/**
 * 商品情報を取得する関数
 */
async function getProductInfo(shopifyProductId: string) {
  try {
    // Prismaから商品情報を取得
    const product = await prisma.product.findFirst({
      where: { shopifyId: shopifyProductId }
    });

    if (!product) {
      console.error(`商品ID ${shopifyProductId} が見つかりません`);
      return null;
    }

    return product;
  } catch (error) {
    console.error('商品情報取得エラー:', error);
    return null;
  }
}

/**
 * リトライ機能付きのfetch関数
 */
async function fetchWithRetry(url: string, options: any = {}, maxRetries = config.maxRetries, delay = config.retryDelay) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), config.timeout);

      options.signal = controller.signal;
      const response = await fetch(url, options);

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      lastError = error;
      console.warn(`Attempt ${attempt} failed: ${error.message}`);

      if (attempt < maxRetries) {
        // 指数バックオフ（徐々に待機時間を増やす）
        const waitTime = delay * Math.pow(2, attempt - 1);
        console.log(`Retrying in ${waitTime}ms...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }

  throw lastError;
}

/**
 * API障害シミュレーション用の関数
 */
async function simulateAPIFailure(url: string, options: any = {}, failureCount = 2) {
  let attempts = 0;

  // 元のfetch関数を保存
  const originalFetch = global.fetch;

  // fetchをモック化
  global.fetch = async function mockedFetch(input: RequestInfo | URL, init?: RequestInit) {
    attempts++;

    if (attempts <= failureCount) {
      // 最初のfailureCount回は失敗させる
      throw new Error(`Simulated API failure (attempt ${attempts})`);
    }

    // それ以降は元のfetch関数を呼び出す
    return originalFetch(input, init);
  } as any;

  try {
    // リトライ機能付きのfetchで呼び出し
    return await fetchWithRetry(url, options);
  } finally {
    // 元のfetch関数を復元
    global.fetch = originalFetch;
  }
}

/**
 * ネットワーク切断シミュレーション用の関数
 */
async function simulateNetworkDisconnection(url: string, options: any = {}, disconnectionCount = 2) {
  let attempts = 0;

  // 元のfetch関数を保存
  const originalFetch = global.fetch;

  // fetchをモック化
  global.fetch = async function mockedFetch(input: RequestInfo | URL, init?: RequestInit) {
    attempts++;

    if (attempts <= disconnectionCount) {
      // 最初のdisconnectionCount回はネットワークエラーを発生させる
      throw new Error(`Simulated network disconnection (attempt ${attempts})`);
    }

    // それ以降は元のfetch関数を呼び出す
    return originalFetch(input, init);
  } as any;

  try {
    // リトライ機能付きのfetchで呼び出し
    return await fetchWithRetry(url, options);
  } finally {
    // 元のfetch関数を復元
    global.fetch = originalFetch;
  }
}

/**
 * データ不整合シミュレーション用の関数
 */
async function simulateDataInconsistency(productId: string) {
  try {
    // 商品情報を取得
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!product) {
      throw new Error(`Product with ID ${productId} not found`);
    }

    // 在庫カレンダーを取得
    const today = new Date();
    const startDate = addDays(today, config.startDaysFromNow);
    const endDate = addDays(startDate, config.durationDays - 1);

    // 在庫カレンダーに不整合データを作成（同じ日に複数の在庫レコードを作成）
    for (let date = new Date(startDate); date <= endDate; date = addDays(date, 1)) {
      // 商品情報を取得して必要なフィールドを取得
      const productInfo = await prisma.product.findUnique({
        where: { id: productId }
      });

      if (!productInfo) {
        throw new Error(`商品ID ${productId} が見つかりません`);
      }

      await prisma.inventoryCalendar.createMany({
        data: [
          {
            productId,
            date,
            isAvailable: true,
            shop: productInfo.shop,
            shopifyProductId: productInfo.shopifyId
          },
          {
            productId,
            date,
            isAvailable: false,
            shop: productInfo.shop,
            shopifyProductId: productInfo.shopifyId
          }
        ],
        skipDuplicates: true
      });
    }

    console.log(`データ不整合を作成しました: 商品ID ${productId} の在庫カレンダーに重複レコード`);

    // 不整合を修復
    return await repairDataInconsistency(productId, startDate, endDate);
  } catch (error) {
    console.error('データ不整合シミュレーションエラー:', error);
    return false;
  }
}

/**
 * データ不整合を修復する関数
 */
async function repairDataInconsistency(productId: string, startDate: Date, endDate: Date) {
  try {
    // 各日付について処理
    for (let date = new Date(startDate); date <= endDate; date = addDays(date, 1)) {
      // その日の在庫レコードをすべて取得
      const inventoryRecords = await prisma.inventoryCalendar.findMany({
        where: {
          productId,
          date
        }
      });

      if (inventoryRecords.length <= 1) {
        // 重複がなければスキップ
        continue;
      }

      console.log(`日付 ${format(date, 'yyyy-MM-dd')} の重複レコードを修復します (${inventoryRecords.length} レコード)`);

      // 最新のレコード以外を削除
      const sortedRecords = inventoryRecords.sort((a, b) =>
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      );

      const latestRecord = sortedRecords[0];

      // 最新以外のレコードを削除
      for (let i = 1; i < sortedRecords.length; i++) {
        await prisma.inventoryCalendar.delete({
          where: { id: sortedRecords[i].id }
        });
      }

      console.log(`日付 ${format(date, 'yyyy-MM-dd')} の重複レコードを修復しました。残りのレコード: ID=${latestRecord.id}, isAvailable=${latestRecord.isAvailable}`);
    }

    return true;
  } catch (error) {
    console.error('データ不整合修復エラー:', error);
    return false;
  }
}

/**
 * テスト結果をJSONファイルに保存する関数
 */
function saveTestResults(results: TestResultSet, filePath: string): void {
  try {
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(filePath, JSON.stringify(results, null, 2));
    console.log(`テスト結果JSONファイルを生成しました: ${filePath}`);
  } catch (error) {
    console.error(`テスト結果の保存に失敗しました: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * テスト結果をMarkdownレポートとして生成する関数
 */
function generateMarkdownReport(results: TestResultSet, outputPath: string): string {
  try {
    let markdown = `# エラー回復とロバスト性のテストレポート

## テスト概要

このレポートは、API障害やネットワーク切断時の回復メカニズム、一時的なエラー発生時の自動リトライ機能、データ不整合発生時の自動修復機能のテスト結果をまとめたものです。

テスト実行日時: ${new Date(results.timestamp).toLocaleString('ja-JP')}
実行時間: ${results.duration.toFixed(2)}秒

## テスト環境

- Node.js バージョン: ${results.environment.nodeVersion}
- OS: ${results.environment.os}

## テスト結果

- 合計テスト数: ${results.totalTests}
- 成功: ${results.successCount}
- 失敗: ${results.failureCount}
- 成功率: ${Math.round((results.successCount / results.totalTests) * 100)}%

## パフォーマンス分析

- 平均テスト実行時間: ${results.performance.averageTestDuration.toFixed(2)}ミリ秒
- 最も遅いテスト: ${results.performance.slowestTest.name} (${results.performance.slowestTest.duration.toFixed(2)}ミリ秒)
- 最も速いテスト: ${results.performance.fastestTest.name} (${results.performance.fastestTest.duration.toFixed(2)}ミリ秒)

## 詳細結果

`;

    // 各テストケースの詳細結果を追加
    results.tests.forEach((result, index) => {
      markdown += `### テストケース${index + 1}: ${result.testName}

- 結果: ${result.success ? '✅ 成功' : '❌ 失敗'}
${result.message ? `- メッセージ: ${result.message}` : ''}
${result.error ? `- エラー: ${result.error}` : ''}
- 実行時間: ${result.duration ? `${result.duration.toFixed(2)}ミリ秒` : '不明'}
`;

      if (result.details) {
        markdown += `- 詳細:\n\`\`\`json\n${JSON.stringify(result.details, null, 2)}\n\`\`\`\n\n`;
      }
    });

    // レポートファイルに書き込み
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(outputPath, markdown);
    console.log(`テスト結果レポートを生成しました: ${outputPath}`);

    return outputPath;
  } catch (error) {
    console.error(`Markdownレポートの生成に失敗しました: ${error instanceof Error ? error.message : String(error)}`);
    return '';
  }
}

/**
 * テスト結果を分析する関数
 */
function analyzeTestResults(results: TestResult[]): {
  averageTestDuration: number;
  slowestTest: { name: string; duration: number };
  fastestTest: { name: string; duration: number };
} {
  // 実行時間が記録されているテストのみを対象にする
  const testsWithDuration = results.filter(test => test.duration !== undefined) as (TestResult & { duration: number })[];

  if (testsWithDuration.length === 0) {
    return {
      averageTestDuration: 0,
      slowestTest: { name: '不明', duration: 0 },
      fastestTest: { name: '不明', duration: 0 }
    };
  }

  // 平均実行時間を計算
  const totalDuration = testsWithDuration.reduce((sum, test) => sum + test.duration, 0);
  const averageTestDuration = totalDuration / testsWithDuration.length;

  // 最も遅いテストを特定
  const slowestTest = testsWithDuration.reduce((slowest, test) =>
    test.duration > slowest.duration ? { name: test.testName, duration: test.duration } : slowest,
    { name: '', duration: -1 }
  );

  // 最も速いテストを特定
  const fastestTest = testsWithDuration.reduce((fastest, test) =>
    test.duration < fastest.duration || fastest.duration === -1 ? { name: test.testName, duration: test.duration } : fastest,
    { name: '', duration: -1 }
  );

  return {
    averageTestDuration,
    slowestTest,
    fastestTest
  };
}

/**
 * エラー回復とロバスト性をテストする関数
 */
async function testErrorRecovery() {
  try {
    console.log('エラー回復とロバスト性のテストを開始します...');

    // 開始時刻を記録
    const startTime = new Date();

    // テスト結果を格納する配列
    const testResults: TestResult[] = [];

    // テスト1: API障害からの回復テスト
    console.log('\n----- テスト1: API障害からの回復テスト -----');
    const test1StartTime = new Date();
    let test1Result = false;
    let test1Error = '';

    try {
      // API障害をシミュレート
      const apiUrl = 'https://jsonplaceholder.typicode.com/todos/1';
      const result = await simulateAPIFailure(apiUrl, {}, 2);

      console.log('API障害からの回復テスト結果:', result);
      test1Result = true;
    } catch (error) {
      test1Error = error.message || String(error);
      console.error('API障害からの回復テストエラー:', error);
    }

    const test1EndTime = new Date();
    const test1Duration = differenceInMilliseconds(test1EndTime, test1StartTime);

    testResults.push({
      testName: 'API障害からの回復テスト',
      success: test1Result,
      timestamp: new Date().toISOString(),
      message: test1Result ? 'API障害から正常に回復しました' : 'API障害からの回復に失敗しました',
      error: test1Error,
      duration: test1Duration
    });

    // テスト2: ネットワーク切断からの回復テスト
    console.log('\n----- テスト2: ネットワーク切断からの回復テスト -----');
    const test2StartTime = new Date();
    let test2Result = false;
    let test2Error = '';

    try {
      // ネットワーク切断をシミュレート
      const apiUrl = 'https://jsonplaceholder.typicode.com/todos/2';
      const result = await simulateNetworkDisconnection(apiUrl, {}, 2);

      console.log('ネットワーク切断からの回復テスト結果:', result);
      test2Result = true;
    } catch (error) {
      test2Error = error.message || String(error);
      console.error('ネットワーク切断からの回復テストエラー:', error);
    }

    const test2EndTime = new Date();
    const test2Duration = differenceInMilliseconds(test2EndTime, test2StartTime);

    testResults.push({
      testName: 'ネットワーク切断からの回復テスト',
      success: test2Result,
      timestamp: new Date().toISOString(),
      message: test2Result ? 'ネットワーク切断から正常に回復しました' : 'ネットワーク切断からの回復に失敗しました',
      error: test2Error,
      duration: test2Duration
    });

    // テスト3: データ不整合の自動修復テスト
    console.log('\n----- テスト3: データ不整合の自動修復テスト -----');
    const test3StartTime = new Date();
    let test3Result = false;
    let test3Error = '';

    try {
      // 商品情報を取得
      const product = await getProductInfo(config.productId);

      if (!product) {
        throw new Error(`商品ID ${config.productId} が見つかりません`);
      }

      // データ不整合をシミュレートして修復
      test3Result = await simulateDataInconsistency(product.id);
    } catch (error) {
      test3Error = error.message || String(error);
      console.error('データ不整合の自動修復テストエラー:', error);
    }

    const test3EndTime = new Date();
    const test3Duration = differenceInMilliseconds(test3EndTime, test3StartTime);

    testResults.push({
      testName: 'データ不整合の自動修復テスト',
      success: test3Result,
      timestamp: new Date().toISOString(),
      message: test3Result ? 'データ不整合を正常に修復しました' : 'データ不整合の修復に失敗しました',
      error: test3Error,
      duration: test3Duration
    });

    // テスト結果のサマリー
    console.log('\n===== テスト結果サマリー =====');
    testResults.forEach(result => {
      console.log(`${result.testName}: ${result.success ? '成功' : '失敗'} (${result.duration}ミリ秒)`);
    });

    // 終了時刻を記録
    const endTime = new Date();
    const totalDuration = differenceInMilliseconds(endTime, startTime) / 1000; // 秒単位

    // テスト結果を分析
    const performanceAnalysis = analyzeTestResults(testResults);

    // テスト結果セットを作成
    const resultSet: TestResultSet = {
      timestamp: startTime.toISOString(),
      duration: totalDuration,
      totalTests: testResults.length,
      successCount: testResults.filter(r => r.success).length,
      failureCount: testResults.filter(r => !r.success).length,
      tests: testResults,
      environment: {
        nodeVersion: process.version,
        os: `${process.platform} ${process.arch}`
      },
      performance: performanceAnalysis
    };

    return resultSet;
  } catch (error) {
    console.error('エラー回復とロバスト性のテストエラー:', error);
    throw error;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('エラー回復とロバスト性のテストを開始します...');

    // テストを実行
    const testResults = await testErrorRecovery();

    // 結果ディレクトリが存在しない場合は作成
    if (!fs.existsSync(config.outputDir)) {
      fs.mkdirSync(config.outputDir, { recursive: true });
    }

    // タイムスタンプを生成
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

    // JSONファイルに保存
    const jsonFilePath = path.join(config.outputDir, `error-recovery-test-${timestamp}.json`);
    saveTestResults(testResults, jsonFilePath);

    // Markdownレポートを生成
    const markdownFilePath = path.join(config.outputDir, `error-recovery-test-${timestamp}.md`);
    generateMarkdownReport(testResults, markdownFilePath);

    // 成功/失敗の集計
    const successCount = testResults.tests.filter(r => r.success).length;
    const failureCount = testResults.tests.length - successCount;

    console.log('\n===== テスト完了 =====');
    console.log(`合計テスト数: ${testResults.tests.length}`);
    console.log(`成功: ${successCount}`);
    console.log(`失敗: ${failureCount}`);
    console.log(`成功率: ${Math.round((successCount / testResults.tests.length) * 100)}%`);

    if (failureCount === 0) {
      console.log('\nすべてのテストが成功しました！');
    } else {
      console.error('\n一部のテストが失敗しました。詳細はレポートを確認してください。');
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
