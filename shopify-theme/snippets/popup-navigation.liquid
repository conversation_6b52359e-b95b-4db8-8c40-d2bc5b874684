<div class="popup-navigation js-popup-navigation" data-popup-content>
    <div class="popup-navigation__head pt-20 pb-10 px-10 d-lg-none">
        <div class="container">
            <div class="popup-navigation__button d-flex align-items-center" data-js-popup-navigation-button="close">
                <i class="popup-navigation__close cursor-pointer" data-js-popup-close data-button-content="close">{% render 'icon-theme-164' %}</i>
                <i class="popup-navigation__back cursor-pointer d-lg-none" data-button-content="back">{% render 'icon-theme-012' %}</i>
            </div>
        </div>
    </div>
    {%- if settings.search_show -%}
        <div class="popup-navigation__search search pt-lg-25 pb-lg-35 px-10 px-lg-0" data-js-max-products="6">
            <div class="container">
                <div class="d-none d-lg-flex align-items-lg-center mb-5 mb-lg-10">
                    <p class="m-0">{{ 'general.search.title' | t }}</p>
                    <i class="search__close ml-auto cursor-pointer" data-js-popup-close>{% render 'icon-theme-164' %}</i>
                </div>
                {%- if settings.search_predictive_enabled -%}
                    <predictive-search class="search-modal__form" data-loading-text="{{ 'accessibility.loading' | t }}">
                {%- else -%}
                    <search-form class="search-modal__form">
                {%- endif -%}
                    <form action="{{ routes.search_url }}" method="get" role="search" class="m-0">
                        <div class="position-relative d-flex align-items-center pb-5 pb-lg-15 mb-0 border-bottom">
                            <input type="search" 
                                class="border-0 p-0 mb-0" 
                                name="q" 
                                id="Search" 
                                value="{{ search.terms | escape }}" 
                                placeholder="{{ 'general.search.placeholder' | t }}"
                                {%- if settings.search_predictive_enabled -%}
                                role="combobox"
                                aria-expanded="false"
                                aria-owns="predictive-search-results"
                                aria-controls="predictive-search-results"
                                aria-haspopup="listbox"
                                aria-autocomplete="list"
                                autocorrect="off"
                                autocomplete="off"
                                autocapitalize="off"
                                spellcheck="false"
                                {%- endif -%}
                            >
                            <input type="hidden" name="options[prefix]" value="last"/>
                            <button type="reset" class="d-none" aria-label="{{ 'general.search.reset' | t }}">{{ 'general.search.reset' | t }}</button>
                            <button type="submit" class="search__button btn--unstyled position-absolute right-0 mb-0 mr-0 m-lg-0 cursor-pointer" aria-label="{{ 'general.search.search' | t }}"><i>{% render 'icon-theme-168' %}</i></button>
                        </div>
                        {%- if settings.search_predictive_enabled -%}
                            <div class="search__content position-relative" tabindex="-1">
                                <div class="search__result px-10 py-20 p-lg-0 mt-20 mt-lg-40 d-none-important" data-predictive-search></div>
                                <div class="search__view-all pb-20 pb-lg-0 mt-20 mt-lg-10 d-none-important">
                                    <a href="{{ routes.search_url }}" class="btn-link">{{ 'general.popups.search.view_all_products' | t }}</a>
                                </div>
                            </div>
                            <span class="predictive-search-status d-none" role="status" aria-hidden="true"></span>
                        {%- endif -%}
                        <p class="search__empty pb-20 pb-lg-0 mt-20 mt-lg-30 mb-0 d-none-important"><a href="{{ routes.search_url }}" class="btn-link">{{ 'general.popups.search.empty_html' | t }}</a></p>
                    </form>
                {%- if settings.search_predictive_enabled -%}
                    </predictive-search>
                {%- else -%}
                    </search-form>
                {%- endif -%}
            </div>
        </div>
    {%- endif -%}
    <div class="popup-navigation__menu d-lg-none px-10" data-js-menu-mobile>
        <div class="container" data-js-position-mobile="vertical-menu"></div>
    </div>
    <div class="popup-navigation__menu d-lg-none{% if settings.search_show %} pt-25{% endif %} pb-25 px-10" data-js-menu-mobile>
        <div class="container" data-js-position-mobile="menu"></div>
    </div>
    <div class="popup-navigation__currency d-lg-none px-10 mt-25" data-js-position-mobile="currencies"></div>
    <div class="popup-navigation__currency d-lg-none px-10 mt-25" data-js-position-mobile="languages"></div>
</div>