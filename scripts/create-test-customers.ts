/**
 * Shopifyにテスト顧客データを作成するスクリプト
 * 
 * このスクリプトは、Shopify Admin APIを使用して、テスト顧客データを作成します。
 * 実際のフローに近いデータを作成するために使用します。
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';

// 環境変数の読み込み
dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// 顧客作成のGraphQLミューテーション
const CREATE_CUSTOMER = gql`
  mutation customerCreate($input: CustomerInput!) {
    customerCreate(input: $input) {
      customer {
        id
        firstName
        lastName
        email
        phone
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// テスト顧客データ
const testCustomers = [
  {
    firstName: '山田',
    lastName: '太郎',
    email: `yamada.taro.${uuidv4().substring(0, 8)}@example.com`,
    phone: '090-1234-5678',
    addresses: [
      {
        address1: '東京都渋谷区神宮前1-1-1',
        city: '渋谷区',
        province: '東京都',
        zip: '150-0001',
        country: 'Japan',
        firstName: '山田',
        lastName: '太郎',
      }
    ]
  },
  {
    firstName: '佐藤',
    lastName: '花子',
    email: `sato.hanako.${uuidv4().substring(0, 8)}@example.com`,
    phone: '090-2345-6789',
    addresses: [
      {
        address1: '大阪府大阪市北区梅田2-2-2',
        city: '大阪市北区',
        province: '大阪府',
        zip: '530-0001',
        country: 'Japan',
        firstName: '佐藤',
        lastName: '花子',
      }
    ]
  },
  {
    firstName: '鈴木',
    lastName: '一郎',
    email: `suzuki.ichiro.${uuidv4().substring(0, 8)}@example.com`,
    phone: '090-3456-7890',
    addresses: [
      {
        address1: '福岡県福岡市博多区博多駅前3-3-3',
        city: '福岡市博多区',
        province: '福岡県',
        zip: '812-0011',
        country: 'Japan',
        firstName: '鈴木',
        lastName: '一郎',
      }
    ]
  },
  {
    firstName: '田中',
    lastName: '美咲',
    email: `tanaka.misaki.${uuidv4().substring(0, 8)}@example.com`,
    phone: '090-4567-8901',
    addresses: [
      {
        address1: '北海道札幌市中央区北4条西4-4-4',
        city: '札幌市中央区',
        province: '北海道',
        zip: '060-0004',
        country: 'Japan',
        firstName: '田中',
        lastName: '美咲',
      }
    ]
  },
  {
    firstName: '高橋',
    lastName: '健太',
    email: `takahashi.kenta.${uuidv4().substring(0, 8)}@example.com`,
    phone: '090-5678-9012',
    addresses: [
      {
        address1: '愛知県名古屋市中区栄5-5-5',
        city: '名古屋市中区',
        province: '愛知県',
        zip: '460-0008',
        country: 'Japan',
        firstName: '高橋',
        lastName: '健太',
      }
    ]
  }
];

/**
 * メインの実行関数
 */
async function main() {
  console.log('テスト顧客データの作成を開始します...');

  const createdCustomers = [];

  for (const customer of testCustomers) {
    try {
      console.log(`顧客を作成中: ${customer.firstName} ${customer.lastName} (${customer.email})`);
      
      const response = await client.request(CREATE_CUSTOMER, {
        input: customer
      });

      if (response.customerCreate.userErrors.length > 0) {
        console.error('顧客作成エラー:', response.customerCreate.userErrors);
        continue;
      }

      const createdCustomer = response.customerCreate.customer;
      console.log(`顧客を作成しました: ID=${createdCustomer.id}`);
      createdCustomers.push(createdCustomer);

      // API制限を考慮して少し待機
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error('顧客作成中にエラーが発生しました:', error);
    }
  }

  console.log('\n=== 作成された顧客 ===');
  createdCustomers.forEach((customer, index) => {
    console.log(`${index + 1}. ${customer.firstName} ${customer.lastName} (${customer.email}) - ID: ${customer.id}`);
  });
  console.log('======================\n');

  return createdCustomers;
}

// スクリプトを実行
main()
  .then(createdCustomers => {
    console.log(`${createdCustomers.length}件の顧客データを作成しました`);
    process.exit(0);
  })
  .catch(error => {
    console.error('予期しないエラーが発生しました:', error);
    process.exit(1);
  });
