#!/usr/bin/env node

/**
 * データベーステーブル確認スクリプト
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkTables() {
  console.log('=== データベーステーブル確認 ===');
  
  try {
    await prisma.$connect();
    console.log('✅ データベース接続成功');
    
    const shop = 'ease-next-temp.myshopify.com';
    
    // 各テーブルを個別にチェック
    const tables = [
      'product',
      'booking', 
      'customer',
      'deliverySchedule',
      'pickingRegistration',
      'returnProcessing',
      'categoryMaster',
      'subCategoryMaster'
    ];
    
    console.log('\n📊 テーブル存在確認:');
    
    for (const tableName of tables) {
      try {
        let count = 0;
        
        switch (tableName) {
          case 'product':
            count = await prisma.product.count({ where: { shop } });
            break;
          case 'booking':
            count = await prisma.booking.count({ where: { shop } });
            break;
          case 'customer':
            count = await prisma.customer.count({ where: { shop } });
            break;
          case 'deliverySchedule':
            try {
              count = await prisma.deliverySchedule.count({ where: { shop } });
            } catch {
              count = 'テーブル不存在';
            }
            break;
          case 'pickingRegistration':
            try {
              count = await prisma.pickingRegistration.count({ where: { shop } });
            } catch {
              count = 'テーブル不存在';
            }
            break;
          case 'returnProcessing':
            try {
              count = await prisma.returnProcessing.count({ where: { shop } });
            } catch {
              count = 'テーブル不存在';
            }
            break;
          case 'categoryMaster':
            count = await prisma.categoryMaster.count({ where: { shop } });
            break;
          case 'subCategoryMaster':
            count = await prisma.subCategoryMaster.count({ where: { shop } });
            break;
        }
        
        console.log(`  ${tableName}: ${count}件`);
        
      } catch (error) {
        console.log(`  ${tableName}: ❌ エラー (${error.message})`);
      }
    }
    
    // 実際に存在するテーブルのみで削除処理を実行
    console.log('\n=== 安全な削除処理 ===');
    
    try {
      // 予約データを削除
      const deletedBookings = await prisma.booking.deleteMany({
        where: { shop }
      });
      console.log(`✅ 予約削除: ${deletedBookings.count}件`);
    } catch (error) {
      console.log(`⚠️ 予約削除スキップ: ${error.message}`);
    }
    
    try {
      // 商品データを削除
      const deletedProducts = await prisma.product.deleteMany({
        where: { shop }
      });
      console.log(`✅ 商品削除: ${deletedProducts.count}件`);
    } catch (error) {
      console.log(`⚠️ 商品削除スキップ: ${error.message}`);
    }
    
    try {
      // 顧客データを削除
      const deletedCustomers = await prisma.customer.deleteMany({
        where: { shop }
      });
      console.log(`✅ 顧客削除: ${deletedCustomers.count}件`);
    } catch (error) {
      console.log(`⚠️ 顧客削除スキップ: ${error.message}`);
    }
    
    console.log('\n✅ 削除処理完了');
    
  } catch (error) {
    console.error('❌ エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkTables();
