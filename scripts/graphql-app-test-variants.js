/**
 * GraphQL App用 バリエーション作成テストコード
 *
 * このコードをShopify GraphQL Appで実行して、
 * 正しいmutation名でバリエーション作成をテストします。
 */

// 1. 商品バリエーション一括作成のテスト
const TEST_PRODUCT_VARIANTS_BULK_CREATE = `
mutation productVariantsBulkCreate($productId: ID!, $variants: [ProductVariantsBulkInput!]!) {
  productVariantsBulkCreate(productId: $productId, variants: $variants) {
    productVariants {
      id
      title
      price
      inventoryItem {
        sku
        id
      }
    }
    userErrors {
      field
      message
      code
    }
  }
}
`;

// テスト用変数（実際の商品IDに変更してください）
const PRODUCT_VARIANTS_VARIABLES = {
  "productId": "gid://shopify/Product/YOUR_PRODUCT_ID_HERE",
  "variants": [
    {
      "price": "300",
      "optionValues": [{
        "optionName": "Title",
        "name": "1日レンタル"
      }],
      "inventoryItem": {
        "sku": "1D",
        "tracked": true
      },
      "inventoryPolicy": "DENY"
    },
    {
      "price": "360",
      "optionValues": [{
        "optionName": "Title",
        "name": "2日レンタル"
      }],
      "inventoryItem": {
        "sku": "2D",
        "tracked": true
      },
      "inventoryPolicy": "DENY"
    },
    {
      "price": "420",
      "optionValues": [{
        "optionName": "Title",
        "name": "3日レンタル"
      }],
      "inventoryItem": {
        "sku": "3D",
        "tracked": true
      },
      "inventoryPolicy": "DENY"
    }
  ]
};

// 2. 在庫調整のテスト
const TEST_INVENTORY_ADJUST_QUANTITIES = `
mutation inventoryAdjustQuantities($input: InventoryAdjustQuantitiesInput!) {
  inventoryAdjustQuantities(input: $input) {
    inventoryAdjustmentGroup {
      id
      createdAt
      reason
      changes {
        name
        delta
        quantityAfterChange
      }
    }
    userErrors {
      field
      message
      code
    }
  }
}
`;

// テスト用変数（実際のInventoryItemIDとLocationIDに変更してください）
const INVENTORY_ADJUST_VARIABLES = {
  "input": {
    "name": "available",
    "reason": "correction",
    "referenceDocumentUri": "product-setup-test",
    "changes": [
      {
        "inventoryItemId": "gid://shopify/InventoryItem/YOUR_INVENTORY_ITEM_ID_HERE",
        "locationId": "gid://shopify/Location/YOUR_LOCATION_ID_HERE",
        "delta": 1
      }
    ]
  }
};

// 3. 商品メタフィールド設定のテスト
const TEST_PRODUCT_UPDATE_METAFIELDS = `
mutation productUpdate($input: ProductInput!) {
  productUpdate(input: $input) {
    product {
      id
      metafields(first: 10) {
        edges {
          node {
            namespace
            key
            value
            type
          }
        }
      }
    }
    userErrors {
      field
      message
    }
  }
}
`;

// テスト用変数（実際の商品IDに変更してください）
const METAFIELDS_VARIABLES = {
  "input": {
    "id": "gid://shopify/Product/YOUR_PRODUCT_ID_HERE",
    "metafields": [
      {
        "namespace": "rental",
        "key": "basic_info",
        "value": JSON.stringify({
          "productCode": "20101001",
          "detailCode": "001",
          "kana": "",
          "location": "NY",
          "status": "available"
        }),
        "type": "json"
      },
      {
        "namespace": "rental",
        "key": "pricing",
        "value": JSON.stringify({
          "basePrice": 300,
          "depositRate": 0.1,
          "discountRules": {
            "day2_6_rate": 0.2,
            "day7_plus_rate": 0.1
          },
          "minimumDays": 1,
          "maximumDays": 30
        }),
        "type": "json"
      },
      {
        "namespace": "rental",
        "key": "status",
        "value": "available",
        "type": "single_line_text_field"
      },
      {
        "namespace": "rental",
        "key": "location",
        "value": "NY",
        "type": "single_line_text_field"
      }
    ]
  }
};

// 4. 商品とバリエーション情報取得のテスト
const TEST_GET_PRODUCT_INFO = `
query getProductInfo($id: ID!) {
  product(id: $id) {
    id
    title
    handle
    variants(first: 50) {
      edges {
        node {
          id
          title
          price
          sku
          inventoryItem {
            id
          }
        }
      }
    }
    metafields(first: 10) {
      edges {
        node {
          namespace
          key
          value
          type
        }
      }
    }
  }
}
`;

// テスト用変数
const GET_PRODUCT_VARIABLES = {
  "id": "gid://shopify/Product/YOUR_PRODUCT_ID_HERE"
};

// 5. ロケーション情報取得のテスト
const TEST_GET_LOCATIONS = `
query getLocations {
  locations(first: 10) {
    edges {
      node {
        id
        name
        address {
          formatted
        }
      }
    }
  }
}
`;

/**
 * 使用方法:
 *
 * 1. Shopify GraphQL Appを開く
 * 2. 上記のクエリとバリエーションをコピー&ペースト
 * 3. YOUR_PRODUCT_ID_HERE, YOUR_INVENTORY_ITEM_ID_HERE, YOUR_LOCATION_ID_HERE を実際のIDに変更
 * 4. 各テストを順番に実行
 *
 * テスト順序:
 * 1. TEST_GET_LOCATIONS でロケーションIDを取得
 * 2. TEST_GET_PRODUCT_INFO で商品情報を確認
 * 3. TEST_PRODUCT_VARIANTS_BULK_CREATE でバリエーション作成
 * 4. TEST_PRODUCT_UPDATE_METAFIELDS でメタフィールド設定
 * 5. TEST_INVENTORY_ADJUST_QUANTITIES で在庫調整
 */

console.log('GraphQL App用テストコードが準備されました');
console.log('上記のクエリをShopify GraphQL Appで実行してください');
