{% comment %}
  商品詳細ページに表示するレンタルカレンダー
  
  使用方法:
  {% render 'product-rental-calendar', product: product %}
{% endcomment %}

<div id="rental-calendar-container" class="product-rental-calendar">
  <div class="product-rental-calendar__loading">
    カレンダーを読み込み中...
  </div>
</div>

{{ 'rental-calendar.css' | asset_url | stylesheet_tag }}
{{ 'rental-calendar.js' | asset_url | script_tag }}

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 商品情報の取得
    const productId = "{{ product.id }}";
    const variantId = "{{ product.selected_or_first_available_variant.id }}";
    const basePrice = {{ product.price | divided_by: 100.0 }};
    
    // カレンダーの初期化
    const rentalCalendar = new RentalCalendar({
      productId: '',
      shopifyProductId: productId,
      variantId: variantId,
      basePrice: basePrice,
      productTitle: "{{ product.title | escape }}",
      variantTitle: "{{ product.selected_or_first_available_variant.title | escape }}",
      productImage: "{{ product.featured_image | img_url: '100x100' }}",
      containerSelector: '#rental-calendar-container'
    });
    
    // カートに追加ボタンのカスタマイズ
    const addToCartButton = document.querySelector('form[action="/cart/add"] [type="submit"]');
    if (addToCartButton) {
      // 元のボタンを非表示
      addToCartButton.style.display = 'none';
      
      // カスタムボタンを作成
      const customButton = document.createElement('button');
      customButton.type = 'button';
      customButton.className = addToCartButton.className;
      customButton.id = 'rental-add-to-cart';
      customButton.textContent = 'レンタル期間を選択してカートに追加';
      
      // ボタンを追加
      addToCartButton.parentNode.insertBefore(customButton, addToCartButton.nextSibling);
      
      // イベントリスナーを設定
      customButton.addEventListener('click', function() {
        // 日付が選択されているかチェック
        if (!rentalCalendar.state.startDate || !rentalCalendar.state.endDate) {
          alert('レンタル期間を選択してください');
          return;
        }
        
        // カートに追加
        addToCart(
          variantId, 
          rentalCalendar.state.startDate, 
          rentalCalendar.state.endDate, 
          rentalCalendar.state.rentalDays
        );
      });
    }
    
    // カートに追加する関数
    async function addToCart(variantId, startDate, endDate, rentalDays) {
      try {
        // 日付をフォーマット
        const formatDate = (date) => {
          const d = new Date(date);
          return d.toISOString().split('T')[0]; // YYYY-MM-DD形式
        };
        
        const formattedStartDate = formatDate(startDate);
        const formattedEndDate = formatDate(endDate);
        
        // カートに追加するデータ
        const formData = {
          'items': [{
            'id': variantId,
            'quantity': 1,
            'properties': {
              'レンタル開始日': formattedStartDate,
              'レンタル終了日': formattedEndDate,
              'レンタル日数': rentalDays,
              '予約タイプ': 'CONFIRMED',
              'デポジット': `¥${rentalCalendar.state.depositAmount.toLocaleString()}`
            }
          }]
        };
        
        // カートに追加
        const response = await fetch('/cart/add.js', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formData)
        });
        
        if (!response.ok) {
          throw new Error('カートへの追加に失敗しました');
        }
        
        const data = await response.json();
        
        // 成功メッセージを表示
        alert('商品がカートに追加されました');
        
        // カートページに遷移するかどうかを確認
        if (confirm('カートページに移動しますか？')) {
          window.location.href = '/cart';
        }
      } catch (error) {
        console.error('カート追加エラー:', error);
        alert('カートへの追加中にエラーが発生しました。もう一度お試しください。');
      }
    }
  });
</script>
