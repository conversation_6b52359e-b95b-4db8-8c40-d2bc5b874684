import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config({ path: '/Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp/.env' });

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2025-01';

// GraphQLクライアントの設定
const client = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品詳細取得クエリ
const PRODUCT_QUERY = gql`
  query getProduct($id: ID!) {
    product(id: $id) {
      id
      title
      handle
      vendor
      productType
      tags
      status
      totalInventory
      options {
        name
        values
      }
      variants(first: 100) {
        edges {
          node {
            id
            title
            sku
            price
            inventoryQuantity
            selectedOptions {
              name
              value
            }
            inventoryItem {
              id
              tracked
            }
          }
        }
      }
      metafields(first: 100) {
        edges {
          node {
            id
            namespace
            key
            value
            type
          }
        }
      }
    }
  }
`;

async function checkProduct() {
  const productId = "gid://shopify/Product/8982388146344";
  
  try {
    console.log('=== 商品ID 8982368747688 の詳細情報 ===\n');
    
    const result = await client.request(PRODUCT_QUERY, { id: productId });
    const product = result.product;
    
    console.log('【基本情報】');
    console.log(`タイトル: ${product.title}`);
    console.log(`ハンドル: ${product.handle}`);
    console.log(`ベンダー: ${product.vendor || '未設定'}`);
    console.log(`商品タイプ: ${product.productType || '未設定'}`);
    console.log(`タグ: ${product.tags.join(', ') || 'なし'}`);
    console.log(`ステータス: ${product.status}`);
    console.log(`総在庫数: ${product.totalInventory}`);
    
    console.log('\n【オプション】');
    product.options.forEach(option => {
      console.log(`${option.name}: ${option.values.join(', ')}`);
    });
    
    console.log('\n【バリアント情報】');
    console.log(`バリアント数: ${product.variants.edges.length}`);
    
    if (product.variants.edges.length === 0) {
      console.log('⚠️  バリアントが登録されていません！');
    } else {
      product.variants.edges.forEach((edge, index) => {
        const variant = edge.node;
        console.log(`\n--- バリアント ${index + 1} ---`);
        console.log(`  タイトル: ${variant.title}`);
        console.log(`  SKU: ${variant.sku || '❌ 未設定'}`);
        console.log(`  価格: ¥${variant.price}`);
        console.log(`  在庫数: ${variant.inventoryQuantity}`);
        console.log(`  在庫追跡: ${variant.inventoryItem.tracked ? '有効' : '無効'}`);
        variant.selectedOptions.forEach(opt => {
          console.log(`  ${opt.name}: ${opt.value}`);
        });
      });
    }
    
    console.log('\n【メタフィールド情報】');
    console.log(`メタフィールド数: ${product.metafields.edges.length}`);
    
    if (product.metafields.edges.length === 0) {
      console.log('⚠️  メタフィールドが登録されていません！');
    } else {
      const metafieldsByNamespace = {};
      product.metafields.edges.forEach(edge => {
        const mf = edge.node;
        if (!metafieldsByNamespace[mf.namespace]) {
          metafieldsByNamespace[mf.namespace] = [];
        }
        metafieldsByNamespace[mf.namespace].push({
          key: mf.key,
          value: mf.value,
          type: mf.type
        });
      });
      
      Object.entries(metafieldsByNamespace).forEach(([namespace, fields]) => {
        console.log(`\n[${namespace}]`);
        fields.forEach(field => {
          console.log(`  ${field.key}: ${field.value.substring(0, 100)}${field.value.length > 100 ? '...' : ''} (${field.type})`);
        });
      });
    }
    
    // 問題の診断
    console.log('\n【診断結果】');
    const issues = [];
    
    if (product.variants.edges.length === 0) {
      issues.push('❌ バリアントが作成されていない');
    } else if (product.variants.edges.length === 1 && product.variants.edges[0].node.title === 'Default Title') {
      issues.push('❌ デフォルトバリアントのみで、レンタル期間バリアントが作成されていない');
    }
    
    const hasSku = product.variants.edges.some(e => e.node.sku);
    if (!hasSku) {
      issues.push('❌ SKUが設定されていない');
    }
    
    if (product.totalInventory === 0) {
      issues.push('❌ 在庫が設定されていない');
    }
    
    if (product.metafields.edges.length === 0) {
      issues.push('❌ メタフィールドが設定されていない');
    }
    
    if (issues.length > 0) {
      console.log('\n発見された問題:');
      issues.forEach(issue => console.log(issue));
    } else {
      console.log('✅ 正常に登録されています');
    }
    
  } catch (error) {
    console.error('エラーが発生しました:', error);
  }
}

checkProduct();