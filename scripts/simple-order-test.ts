/**
 * シンプルな注文作成テストスクリプト
 *
 * このスクリプトは、修正された注文作成ロジックを単純にテストします。
 *
 * 実行方法: npx tsx scripts/simple-order-test.ts
 */

import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';
import { createOrderFromBooking } from '../app/utils/booking/order-creator';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// メイン実行関数
async function main() {
  try {
    console.log('シンプルな注文作成テストを開始します...');

    // セッションを取得
    const session = await prisma.session.findUnique({
      where: {
        id: `offline_${process.env.SHOPIFY_SHOP}`
      }
    });

    if (!session) {
      throw new Error('セッションが見つかりません。アプリケーションが正しく認証されていることを確認してください。');
    }

    console.log(`セッション情報: Shop=${session.shop}`);

    // 最新の予約を取得
    const latestBooking = await prisma.booking.findFirst({
      where: {
        shop: process.env.SHOPIFY_SHOP
      },
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        product: true
      }
    });

    if (!latestBooking) {
      throw new Error('予約が見つかりません。予約を作成してください。');
    }

    console.log('最新の予約情報:');
    console.log(`- 予約ID: ${latestBooking.id}`);
    console.log(`- 予約番号: ${latestBooking.bookingId}`);
    console.log(`- 商品: ${latestBooking.product.title}`);
    console.log(`- バリアントID: ${latestBooking.variantId}`);
    console.log(`- 注文ID: ${latestBooking.shopifyOrderId || 'なし'}`);

    // 予約情報を更新（テスト用）
    if (latestBooking.shopifyOrderId) {
      console.log('この予約には既に注文が関連付けられています。テスト用に注文IDをクリアします。');

      await prisma.booking.update({
        where: { id: latestBooking.id },
        data: {
          shopifyOrderId: null,
          shopifyOrderName: null
        }
      });

      console.log('予約情報を更新しました（注文IDをクリア）');
    }

    // 注文作成ロジックを使用

    // モックAdminApiを作成
    const mockAdminApi = {
      graphql: async (query: string, options?: any) => {
        console.log('GraphQLリクエスト:', {
          query: query.substring(0, 100) + '...',
          variables: JSON.stringify(options?.variables || {}).substring(0, 200) + '...'
        });

        // 成功レスポンスをモック
        if (query.includes('draftOrderCreate')) {
          return {
            json: async () => ({
              data: {
                draftOrderCreate: {
                  draftOrder: {
                    id: 'gid://shopify/DraftOrder/mock-draft-order-id',
                    name: '#D1234',
                    totalPrice: '1500.00'
                  },
                  userErrors: []
                }
              }
            })
          };
        } else if (query.includes('draftOrderComplete')) {
          return {
            json: async () => ({
              data: {
                draftOrderComplete: {
                  draftOrder: {
                    id: 'gid://shopify/DraftOrder/mock-draft-order-id',
                    name: '#D1234'
                  },
                  userErrors: []
                }
              }
            })
          };
        } else {
          return {
            json: async () => ({
              data: {
                orderCreate: {
                  order: {
                    id: 'gid://shopify/Order/mock-order-id',
                    name: '#1234',
                    totalPrice: '1500.00'
                  },
                  userErrors: []
                }
              }
            })
          };
        }
      }
    };

    // 注文作成を実行
    console.log('注文作成を実行します...');

    const result = await createOrderFromBooking(
      prisma,
      mockAdminApi as any,
      latestBooking.id,
      3 // リトライ回数
    );

    console.log('注文作成結果:');
    console.log(JSON.stringify(result, null, 2));

    // 更新された予約情報を取得
    const updatedBooking = await prisma.booking.findUnique({
      where: { id: latestBooking.id },
      select: {
        id: true,
        bookingId: true,
        orderId: true,
        orderName: true
      }
    });

    console.log('更新後の予約情報:');
    console.log(JSON.stringify(updatedBooking, null, 2));

    // 同じ予約IDで再度注文作成を試みる
    console.log('\n同じ予約IDで再度注文作成を試みます...');

    const secondResult = await createOrderFromBooking(
      prisma,
      mockAdminApi as any,
      latestBooking.id,
      3 // リトライ回数
    );

    console.log('2回目の注文作成結果:');
    console.log(JSON.stringify(secondResult, null, 2));

    // 重複注文が発生したかどうかを確認
    if (secondResult.isExisting) {
      console.log('\n✅ 重複注文は検出されませんでした: 既存の注文が正しく検出されました');
    } else {
      console.log('\n⚠️ 重複注文が検出されました: 同じ予約に対して複数の注文が作成されました');
    }

    return {
      firstResult: result,
      secondResult
    };
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    throw error;
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// スクリプトを実行
console.log('シンプルなテストスクリプトを開始します...');
main()
  .then((results) => {
    console.log('テストが完了しました');
    process.exit(0);
  })
  .catch((error) => {
    console.error('テストが失敗しました:', error);
    console.error('エラーの詳細:', error.stack);
    process.exit(1);
  });
