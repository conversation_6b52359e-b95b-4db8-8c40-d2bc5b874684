/**
 * 商品データ表示テスト結果のレポート生成スクリプト
 *
 * このスクリプトは、商品データ表示テストの結果を分析し、HTMLレポートを生成します。
 * 
 * 実行方法: npx tsx app/tests/product-display/generate-report.ts [結果JSONファイルのパス]
 */

import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

// 現在のファイルのディレクトリパスを取得
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 結果保存用ディレクトリ
const RESULTS_DIR = path.join(__dirname, 'results');

/**
 * HTMLレポートを生成する関数
 * @param results テスト結果
 * @param outputPath 出力先のファイルパス
 * @returns 生成されたファイルのパス
 */
function generateHtmlReport(results: any, outputPath: string): string {
  // 結果の分析
  const dbResults = results.database || {};
  const apiResults = results.api || {};
  const consistencyResults = results.consistency || {};
  
  // 問題のある予約を特定
  const problemBookings = [];
  
  if (consistencyResults.details) {
    for (const detail of consistencyResults.details) {
      if (!detail.isConsistent) {
        problemBookings.push(detail);
      }
    }
  }
  
  // HTMLの生成
  let html = `<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>商品データ表示テスト結果</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #2c3e50;
    }
    .summary {
      background-color: #f8f9fa;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 20px;
    }
    .success {
      color: #28a745;
    }
    .warning {
      color: #ffc107;
    }
    .danger {
      color: #dc3545;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 8px 12px;
      border: 1px solid #dee2e6;
      text-align: left;
    }
    th {
      background-color: #f8f9fa;
    }
    tr:nth-child(even) {
      background-color: #f8f9fa;
    }
    .problem-details {
      background-color: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 10px 15px;
      margin-bottom: 10px;
    }
    .recommendations {
      background-color: #d1ecf1;
      border-radius: 5px;
      padding: 15px;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <h1>商品データ表示テスト結果</h1>
  <p>テスト実行日時: ${new Date(results.timestamp).toLocaleString('ja-JP')}</p>
  
  <div class="summary">
    <h2>テスト結果サマリー</h2>
    <p><strong>データベース検証:</strong> 
      ${dbResults.validProductCount || 0}/${dbResults.totalBookings || 0} 件の有効な商品データ 
      (${dbResults.missingProductCount || 0} 件の欠損, ${dbResults.invalidProductCount || 0} 件の無効データ)
    </p>
    <p><strong>API検証:</strong> 
      ${apiResults.validProductCount || 0}/${apiResults.totalBookings || 0} 件の有効な商品データ 
      (${apiResults.missingProductCount || 0} 件の欠損, ${apiResults.invalidProductCount || 0} 件の無効データ)
    </p>
    <p><strong>整合性検証:</strong> 
      ${consistencyResults.consistentCount || 0}/${consistencyResults.matchedBookings || 0} 件の整合性のあるデータ 
      (${consistencyResults.inconsistentCount || 0} 件の不整合)
    </p>
    
    <h3>総合評価</h3>`;
  
  // 総合評価の判定
  const dbSuccessRate = dbResults.validProductCount / (dbResults.totalBookings || 1);
  const apiSuccessRate = apiResults.validProductCount / (apiResults.totalBookings || 1);
  const consistencyRate = consistencyResults.consistentCount / (consistencyResults.matchedBookings || 1);
  
  if (dbSuccessRate > 0.9 && apiSuccessRate > 0.9 && consistencyRate > 0.9) {
    html += `
    <p class="success">✅ <strong>良好</strong> - 商品データの表示は概ね正常に機能しています。</p>`;
  } else if (dbSuccessRate > 0.7 && apiSuccessRate > 0.7 && consistencyRate > 0.7) {
    html += `
    <p class="warning">⚠️ <strong>注意</strong> - 商品データの表示に一部問題があります。</p>`;
  } else {
    html += `
    <p class="danger">❌ <strong>問題あり</strong> - 商品データの表示に重大な問題があります。</p>`;
  }
  
  html += `
  </div>
  
  <h2>問題のある予約</h2>`;
  
  if (problemBookings.length > 0) {
    html += `
    <p>${problemBookings.length}件の予約で商品データの不整合が検出されました。</p>
    
    <table>
      <thead>
        <tr>
          <th>予約ID</th>
          <th>DB商品名</th>
          <th>DB商品SKU</th>
          <th>API商品名</th>
          <th>API商品SKU</th>
        </tr>
      </thead>
      <tbody>`;
    
    for (const booking of problemBookings) {
      html += `
        <tr>
          <td>${booking.id}</td>
          <td>${booking.db.title || '-'}</td>
          <td>${booking.db.sku || '-'}</td>
          <td>${booking.api.title || '-'}</td>
          <td>${booking.api.sku || '-'}</td>
        </tr>`;
    }
    
    html += `
      </tbody>
    </table>`;
  } else {
    html += `
    <p class="success">✅ 不整合のある予約は検出されませんでした。</p>`;
  }
  
  // 問題の詳細分析
  html += `
  <h2>問題の詳細分析</h2>`;
  
  // データベース側の問題
  if (dbResults.missingProductCount > 0 || dbResults.invalidProductCount > 0) {
    html += `
    <div class="problem-details">
      <h3>データベースの問題</h3>
      <ul>`;
    
    if (dbResults.missingProductCount > 0) {
      html += `
        <li class="danger">❌ <strong>${dbResults.missingProductCount}件</strong>の予約で商品データが見つかりません。これは、商品が削除されたか、予約と商品の関連付けが失われている可能性があります。</li>`;
    }
    
    if (dbResults.invalidProductCount > 0) {
      html += `
        <li class="warning">⚠️ <strong>${dbResults.invalidProductCount}件</strong>の予約で商品データが無効です。必須フィールドが欠けているか、データ形式が正しくない可能性があります。</li>`;
    }
    
    html += `
      </ul>
    </div>`;
  }
  
  // API側の問題
  if (apiResults.missingProductCount > 0 || apiResults.invalidProductCount > 0) {
    html += `
    <div class="problem-details">
      <h3>API応答の問題</h3>
      <ul>`;
    
    if (apiResults.missingProductCount > 0) {
      html += `
        <li class="danger">❌ <strong>${apiResults.missingProductCount}件</strong>の予約でAPI応答に商品データが含まれていません。これは、APIのデータ取得ロジックに問題がある可能性があります。</li>`;
    }
    
    if (apiResults.invalidProductCount > 0) {
      html += `
        <li class="warning">⚠️ <strong>${apiResults.invalidProductCount}件</strong>の予約でAPI応答の商品データが無効です。必須フィールドが欠けているか、データ形式が正しくない可能性があります。</li>`;
    }
    
    html += `
      </ul>
    </div>`;
  }
  
  // 整合性の問題
  if (consistencyResults.inconsistentCount > 0) {
    html += `
    <div class="problem-details">
      <h3>データ整合性の問題</h3>
      <p class="warning">⚠️ <strong>${consistencyResults.inconsistentCount}件</strong>の予約でデータベースとAPI応答の間に不整合があります。これは、キャッシュの問題やデータ同期の問題が原因である可能性があります。</p>
    </div>`;
  }
  
  // 推奨される対応策
  html += `
  <div class="recommendations">
    <h2>推奨される対応策</h2>
    <ol>`;
  
  if (dbResults.missingProductCount > 0) {
    html += `
      <li><strong>商品と予約の関連付けを修復する</strong> - 商品データが欠落している予約を特定し、正しい商品と関連付けます。</li>`;
  }
  
  if (apiResults.missingProductCount > 0) {
    html += `
      <li><strong>API応答の商品データ取得ロジックを改善する</strong> - 予約データを取得する際に、関連する商品データも確実に取得されるようにします。</li>`;
  }
  
  if (consistencyResults.inconsistentCount > 0) {
    html += `
      <li><strong>データ同期メカニズムを見直す</strong> - データベースとAPI応答の間で商品データの整合性を確保するために、同期メカニズムを改善します。</li>`;
  }
  
  if (dbResults.invalidProductCount > 0 || apiResults.invalidProductCount > 0) {
    html += `
      <li><strong>データバリデーションを強化する</strong> - 商品データの保存と取得時に、必須フィールドが確実に含まれるようにバリデーションを追加します。</li>`;
  }
  
  html += `
      <li><strong>エラーハンドリングを改善する</strong> - 商品データが見つからない場合や無効な場合に、適切なフォールバック処理を実装します。</li>
      <li><strong>キャッシュ機構を最適化する</strong> - 商品データのキャッシュ機構を見直し、最新のデータが確実に表示されるようにします。</li>
    </ol>
  </div>
  
  <footer>
    <p><small>このレポートは ${new Date().toLocaleString('ja-JP')} に生成されました。</small></p>
  </footer>
</body>
</html>`;
  
  // HTMLファイルに書き込み
  const dir = path.dirname(outputPath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  fs.writeFileSync(outputPath, html);
  
  return outputPath;
}

/**
 * メイン実行関数
 */
async function main() {
  try {
    // コマンドライン引数からJSONファイルのパスを取得
    const args = process.argv.slice(2);
    let jsonFilePath = args[0];
    
    // 引数がない場合は最新のJSONファイルを使用
    if (!jsonFilePath) {
      // 結果ディレクトリが存在するか確認
      if (!fs.existsSync(RESULTS_DIR)) {
        console.error('結果ディレクトリが見つかりません:', RESULTS_DIR);
        process.exit(1);
      }
      
      // 最新のJSONファイルを取得
      const files = fs.readdirSync(RESULTS_DIR)
        .filter(file => file.endsWith('.json'))
        .map(file => ({
          name: file,
          path: path.join(RESULTS_DIR, file),
          mtime: fs.statSync(path.join(RESULTS_DIR, file)).mtime
        }))
        .sort((a, b) => b.mtime.getTime() - a.mtime.getTime());
      
      if (files.length === 0) {
        console.error('JSONファイルが見つかりません');
        process.exit(1);
      }
      
      jsonFilePath = files[0].path;
      console.log('最新のJSONファイルを使用します:', jsonFilePath);
    }
    
    // JSONファイルを読み込む
    if (!fs.existsSync(jsonFilePath)) {
      console.error('指定されたJSONファイルが見つかりません:', jsonFilePath);
      process.exit(1);
    }
    
    const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));
    
    // HTMLレポートの出力先
    const htmlFilePath = jsonFilePath.replace('.json', '.html');
    
    // HTMLレポートを生成
    const reportPath = generateHtmlReport(jsonData, htmlFilePath);
    console.log('HTMLレポートを生成しました:', reportPath);
    
    // ブラウザでレポートを開く
    const platform = process.platform;
    if (platform === 'darwin') {
      // macOS
      require('child_process').execSync(`open "${reportPath}"`);
    } else if (platform === 'win32') {
      // Windows
      require('child_process').execSync(`start "" "${reportPath}"`);
    } else if (platform === 'linux') {
      // Linux
      require('child_process').execSync(`xdg-open "${reportPath}"`);
    } else {
      console.log('ブラウザでレポートを開くことができません。手動で開いてください:', reportPath);
    }
  } catch (error) {
    console.error('エラー:', error);
    process.exit(1);
  }
}

// スクリプトを実行
main();
