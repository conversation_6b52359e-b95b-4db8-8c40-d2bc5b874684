{% include 'global-variables' %}
{% include 'product-cart-res-variables' %}
{%- assign image_size = '100x' -%}
{%- assign show_title = show_title | default: true -%}
{%- assign show_variant = show_variant | default: true -%}
{%- assign show_price = show_price | default: true -%}
{%- assign show_button_remove = show_button_remove | default: true -%}
<div class="product-store-lists d-flex flex-row align-items-start mb-20" data-js-product data-product-handle="{{ product.handle }}" data-product-variant-id="{{ id }}">
    <div class="product-store-lists__image mr-15">
        <a href="{{ url }}" class="d-block">
            <img>
        </a>
    </div>
    <div class="product-store-lists__content d-flex flex-column align-items-start">
        {%- if show_title -%}
            <div class="product-store-lists__title mb-3">
                <h3 class="h6 m-0">
                    <a href="{{ url }}">{{ title }}</a>
                </h3>
            </div>
        {%- endif -%}
        {%- if show_variant -%}
            {% capture default_variant_title %}{{ 'products.product.default_variant_title' | t }}{% endcapture %}
            <div class="product-store-lists__variant{% if variant_title == 'Default Title' %} d-none{% endif %}">{{ variant_title | replace: 'Default Title', default_variant_title }}</div>
        {%- endif -%}
        {%- if show_price -%}
            <div class="product-store-lists__price mt-10 mb-10">
                {% include 'product-get-price' %}
            </div>
        {%- endif -%}
        {%- if show_button_remove -%}
            <span class="product-store-lists__remove btn-link js-store-lists-remove-{{ namespace }}">{{ 'products.product.remove' | t }}</span>
        {%- endif -%}
    </div>
</div>