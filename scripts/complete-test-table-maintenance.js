/**
 * TEST-TABLE-001のメンテナンス完了スクリプト
 */

import { PrismaClient } from '@prisma/client';
import { format } from 'date-fns';

const prisma = new PrismaClient();

async function completeTestTableMaintenance() {
  console.log('🔧 TEST-TABLE-001のメンテナンス完了処理を開始...\n');

  try {
    // TEST-TABLE-001を取得
    const product = await prisma.product.findFirst({
      where: { sku: 'TEST-TABLE-001' }
    });

    if (!product) {
      console.log('❌ TEST-TABLE-001が見つかりません');
      return;
    }

    console.log(`📦 商品: ${product.title} (${product.sku})`);

    // 進行中のメンテナンスを取得
    const activeMaintenances = await prisma.maintenance.findMany({
      where: {
        productId: product.id,
        status: 'IN_PROGRESS'
      }
    });

    console.log(`🔧 進行中のメンテナンス: ${activeMaintenances.length}件`);

    if (activeMaintenances.length === 0) {
      console.log('✅ 進行中のメンテナンスはありません');
      return;
    }

    for (const maintenance of activeMaintenances) {
      console.log(`\n📋 メンテナンス詳細:`);
      console.log(`   ID: ${maintenance.id}`);
      console.log(`   期間: ${format(maintenance.startDate, 'yyyy-MM-dd')} 〜 ${maintenance.endDate ? format(maintenance.endDate, 'yyyy-MM-dd') : '未設定'}`);
      console.log(`   タイプ: ${maintenance.type}`);
      console.log(`   備考: ${maintenance.notes || 'なし'}`);

      // メンテナンス期間が過去の場合は完了にする
      const today = new Date();
      const endDate = maintenance.endDate ? new Date(maintenance.endDate) : null;

      if (endDate && endDate < today) {
        console.log(`\n✅ メンテナンス期間が終了しているため、完了処理を実行`);

        // メンテナンスを完了状態に更新
        await prisma.maintenance.update({
          where: { id: maintenance.id },
          data: {
            status: 'COMPLETED',
            completionDate: endDate,
            notes: `${maintenance.notes || ''}\n[自動完了] 期間終了により自動完了 (${format(today, 'yyyy-MM-dd')})`
          }
        });

        console.log(`   📝 メンテナンスステータスを COMPLETED に更新`);

        // 在庫カレンダーを更新（メンテナンス理由の利用不可を解除）
        const deletedCount = await prisma.inventoryCalendar.deleteMany({
          where: {
            productId: product.id,
            maintenanceId: maintenance.id,
            unavailableReason: 'MAINTENANCE'
          }
        });

        console.log(`   📅 在庫カレンダーを更新: ${deletedCount.count}件のメンテナンス期間を削除`);

      } else {
        console.log(`\n⚠️ メンテナンス期間がまだ終了していません`);
        console.log(`   現在日時: ${format(today, 'yyyy-MM-dd')}`);
        console.log(`   終了予定: ${endDate ? format(endDate, 'yyyy-MM-dd') : '未設定'}`);

        // 手動で完了させるかユーザーに確認
        console.log(`\n🔄 手動完了オプション:`);
        console.log(`   1. 今日で完了させる`);
        console.log(`   2. そのまま継続`);

        // 自動で今日完了させる
        console.log(`\n✅ 自動選択: 今日で完了させる`);

        await prisma.maintenance.update({
          where: { id: maintenance.id },
          data: {
            status: 'COMPLETED',
            endDate: today,
            completionDate: today,
            notes: `${maintenance.notes || ''}\n[手動完了] 予約競合解消のため手動完了 (${format(today, 'yyyy-MM-dd')})`
          }
        });

        console.log(`   📝 メンテナンスステータスを COMPLETED に更新`);

        // 在庫カレンダーを更新
        const deletedCount = await prisma.inventoryCalendar.deleteMany({
          where: {
            productId: product.id,
            maintenanceId: maintenance.id,
            unavailableReason: 'MAINTENANCE'
          }
        });

        console.log(`   📅 在庫カレンダーを更新: ${deletedCount.count}件のメンテナンス期間を削除`);
      }
    }

    // 完了後の状況確認
    console.log('\n=== 完了後の状況確認 ===');

    const remainingMaintenances = await prisma.maintenance.findMany({
      where: {
        productId: product.id,
        status: 'IN_PROGRESS'
      }
    });

    console.log(`🔧 残りの進行中メンテナンス: ${remainingMaintenances.length}件`);

    const bookings = await prisma.booking.findMany({
      where: {
        productId: product.id,
        status: { in: ['PROVISIONAL', 'CONFIRMED'] },
        endDate: { gte: new Date() }
      },
      orderBy: { startDate: 'asc' }
    });

    console.log(`📅 今後の予約: ${bookings.length}件`);
    bookings.forEach((booking, index) => {
      console.log(`  ${index + 1}. ${booking.bookingId} (${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')})`);
      console.log(`     顧客: ${booking.customerName}, ステータス: ${booking.status}`);
    });

    // 在庫カレンダーの状況
    const today = new Date();
    const futureDate = new Date();
    futureDate.setDate(today.getDate() + 7);

    const inventoryEntries = await prisma.inventoryCalendar.findMany({
      where: {
        productId: product.id,
        date: { gte: today, lte: futureDate }
      },
      orderBy: { date: 'asc' }
    });

    console.log(`\n📊 在庫カレンダー（今後7日間）: ${inventoryEntries.length}件`);
    inventoryEntries.forEach(entry => {
      const dateStr = format(entry.date, 'yyyy-MM-dd');
      const status = entry.isAvailable ? '✅ 利用可能' : '❌ 利用不可';
      const reason = entry.unavailableReason ? ` (${entry.unavailableReason})` : '';
      console.log(`  ${dateStr}: ${status}${reason}`);
    });

    if (remainingMaintenances.length === 0) {
      console.log('\n✅ TEST-TABLE-001のメンテナンス完了処理が正常に完了しました');
    } else {
      console.log('\n⚠️ まだ進行中のメンテナンスが残っています');
    }

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
completeTestTableMaintenance();
