/**
 * レンタル商品カート表示カスタマイズ
 * 
 * このスクリプトは、カートページでレンタル商品の表示をカスタマイズします。
 * - レンタル期間の表示
 * - 複数バリエーションの統合表示
 * - 8日以上のレンタルの追加料金表示
 */

document.addEventListener('DOMContentLoaded', function() {
  console.log('Rental cart script loaded');
  
  // カート内の商品を取得
  const cartItems = document.querySelectorAll('.cart-item');
  
  if (cartItems.length === 0) {
    console.log('No cart items found');
    return;
  }
  
  console.log('Found cart items:', cartItems.length);
  
  // 各商品をカスタマイズ
  cartItems.forEach(function(item) {
    // レンタル情報を持つ商品かどうかを確認
    const rentalStartDate = getItemProperty(item, 'レンタル開始日');
    const rentalEndDate = getItemProperty(item, 'レンタル終了日');
    
    if (!rentalStartDate || !rentalEndDate) {
      console.log('Not a rental item');
      return;
    }
    
    console.log('Processing rental item');
    
    // レンタル情報を表示
    displayRentalInfo(item, rentalStartDate, rentalEndDate);
    
    // 価格表示を調整
    adjustPriceDisplay(item);
  });
  
  /**
   * 商品のプロパティを取得
   * @param {Element} item カート商品要素
   * @param {string} propertyName プロパティ名
   * @returns {string|null} プロパティ値
   */
  function getItemProperty(item, propertyName) {
    const propertyElements = item.querySelectorAll('.cart-item__property');
    
    for (const element of propertyElements) {
      if (element.textContent.includes(propertyName + ':')) {
        return element.textContent.split(':')[1].trim();
      }
    }
    
    return null;
  }
  
  /**
   * レンタル情報を表示
   * @param {Element} item カート商品要素
   * @param {string} startDate 開始日
   * @param {string} endDate 終了日
   */
  function displayRentalInfo(item, startDate, endDate) {
    // レンタル期間の表示要素を作成
    const rentalPeriodElement = document.createElement('div');
    rentalPeriodElement.className = 'rental-period';
    rentalPeriodElement.innerHTML = `
      <strong>レンタル期間:</strong> ${formatDate(startDate)} 〜 ${formatDate(endDate)}
    `;
    
    // レンタル日数を取得
    const rentalDays = getItemProperty(item, 'レンタル日数');
    if (rentalDays) {
      const daysElement = document.createElement('div');
      daysElement.className = 'rental-days';
      daysElement.innerHTML = `<strong>レンタル日数:</strong> ${rentalDays}日`;
      rentalPeriodElement.appendChild(daysElement);
    }
    
    // 商品情報の前に挿入
    const itemDetails = item.querySelector('.cart-item__details');
    if (itemDetails) {
      itemDetails.insertBefore(rentalPeriodElement, itemDetails.firstChild);
    }
  }
  
  /**
   * 価格表示を調整
   * @param {Element} item カート商品要素
   */
  function adjustPriceDisplay(item) {
    // レンタル料金を取得
    const rentalPrice = getItemProperty(item, 'レンタル料金');
    
    if (rentalPrice) {
      // 価格表示要素を取得
      const priceElement = item.querySelector('.cart-item__price');
      
      if (priceElement) {
        // 元の価格を保存
        const originalPrice = priceElement.textContent;
        
        // 価格表示を調整
        priceElement.innerHTML = `
          <div class="original-price" style="text-decoration: line-through;">${originalPrice}</div>
          <div class="calculated-price" style="font-weight: bold; color: #4CAF50;">${rentalPrice}</div>
          <div class="price-note" style="font-size: 0.8em; color: #666;">※レンタル料金</div>
        `;
      }
    }
  }
  
  /**
   * 日付をフォーマット
   * @param {string} dateString YYYY-MM-DD形式の日付文字列
   * @returns {string} フォーマットされた日付
   */
  function formatDate(dateString) {
    if (!dateString) return '';
    
    const parts = dateString.split('-');
    if (parts.length !== 3) return dateString;
    
    return `${parts[0]}年${parseInt(parts[1])}月${parseInt(parts[2])}日`;
  }
});
