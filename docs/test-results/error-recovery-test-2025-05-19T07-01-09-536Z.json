{"timestamp": "2025-05-19T07:01:08.569Z", "duration": 0.967, "totalTests": 3, "successCount": 2, "failureCount": 1, "tests": [{"testName": "API障害からの回復テスト", "success": true, "timestamp": "2025-05-19T07:01:08.631Z", "message": "API障害から正常に回復しました", "error": "", "duration": 62}, {"testName": "ネットワーク切断からの回復テスト", "success": true, "timestamp": "2025-05-19T07:01:08.644Z", "message": "ネットワーク切断から正常に回復しました", "error": "", "duration": 12}, {"testName": "データ不整合の自動修復テスト", "success": false, "timestamp": "2025-05-19T07:01:09.536Z", "message": "データ不整合の修復に失敗しました", "error": "", "duration": 892}], "environment": {"nodeVersion": "v20.18.1", "os": "darwin arm64"}, "performance": {"averageTestDuration": 322, "slowestTest": {"name": "データ不整合の自動修復テスト", "duration": 892}, "fastestTest": {"name": "ネットワーク切断からの回復テスト", "duration": 12}}}