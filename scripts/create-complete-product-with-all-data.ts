import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();

// Shopify API設定
const config = {
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block',
  apiSecretKey: process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
};

// 設定の検証
if (!config.apiSecretKey) {
  console.error('エラー: SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません。');
  process.exit(1);
}

// ショップ名を正規化
const shopName = config.shop.replace('.myshopify.com', '');

// Shopify GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${shopName}.myshopify.com/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': config.apiSecretKey,
      'Content-Type': 'application/json',
    },
  }
);

// バリアントマッピング定義
const VARIANT_MAPPING = {
  "1D": { price: 1500, deposit: 0 },
  "2D": { price: 2250, deposit: 0 },
  "3D": { price: 3000, deposit: 0 },
  "4D": { price: 3750, deposit: 0 },
  "5D": { price: 4500, deposit: 0 },
  "6D": { price: 5250, deposit: 0 },
  "7D": { price: 6000, deposit: 0 },
  "8D+": { price: 6000, deposit: 0 },
  "PROV": { price: 0, deposit: 0 }
};

// レンタル期間のバリアント定義
const RENTAL_VARIANTS = [
  { title: "1日レンタル", suffix: "1D", days: 1 },
  { title: "2日レンタル", suffix: "2D", days: 2 },
  { title: "3日レンタル", suffix: "3D", days: 3 },
  { title: "4日レンタル", suffix: "4D", days: 4 },
  { title: "5日レンタル", suffix: "5D", days: 5 },
  { title: "6日レンタル", suffix: "6D", days: 6 },
  { title: "7日レンタル", suffix: "7D", days: 7 },
  { title: "8日以上レンタル", suffix: "8D+", days: 8 },
  { title: "仮予約", suffix: "PROV", days: 0 }
];

// 商品データインターフェース
interface ProductData {
  productCode: string;
  detailCode: string;
  categoryName: string;
  title: string;
  kana: string;
  description: string;
  basePrice: number;
  quantity: number;
  location: string;
  width: number;
  depth: number;
  height: number;
  purchasePrice: number;
  status: string;
  maintenanceNotes: string;
}

// ロケーションを取得
async function getLocations() {
  const query = gql`
    query GetLocations {
      locations(first: 10) {
        edges {
          node {
            id
            name
            isActive
          }
        }
      }
    }
  `;

  const data = await client.request(query);
  return data.locations.edges.map((edge: any) => edge.node).filter((loc: any) => loc.isActive);
}

// 商品を作成
async function createProduct(productData: ProductData) {
  console.log(`\n=== 商品作成開始: ${productData.title} ===`);

  try {
    // 1. 商品を作成（バリアントなしで）
    const createProductMutation = gql`
      mutation createProduct($input: ProductInput!) {
        productCreate(input: $input) {
          product {
            id
            title
            handle
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const baseSku = `${productData.productCode}-${productData.detailCode.padStart(3, '0')}`;
    const handle = `${productData.productCode}-${productData.detailCode}`;

    const productInput = {
      title: productData.title,
      descriptionHtml: `<p>${productData.description}</p><p>${productData.maintenanceNotes}</p>`,
      vendor: "PEACES",
      productType: productData.categoryName,
      handle: handle,
      status: "ACTIVE",
      tags: [productData.categoryName, `場所:${productData.location}`, `状態:${productData.status}`],
      metafields: [
        {
          namespace: "custom",
          key: "variant_mapping",
          value: JSON.stringify(VARIANT_MAPPING),
          type: "json"
        },
        {
          namespace: "custom",
          key: "pricing",
          value: JSON.stringify({
            base_price: productData.basePrice,
            daily_rate: Math.round(productData.basePrice * 0.5),
            weekly_rate: Math.round(productData.basePrice * 3),
            monthly_rate: Math.round(productData.basePrice * 10),
            deposit: 0,
            purchase_price: productData.purchasePrice
          }),
          type: "json"
        },
        {
          namespace: "custom",
          key: "product_group",
          value: productData.productCode,
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "detail_code",
          value: productData.detailCode,
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "location",
          value: productData.location,
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "status",
          value: productData.status,
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "item_quantity",
          value: productData.quantity.toString(),
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "kana",
          value: productData.kana,
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "width",
          value: productData.width.toString(),
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "depth",
          value: productData.depth.toString(),
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "height",
          value: productData.height.toString(),
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "general_notes",
          value: productData.maintenanceNotes,
          type: "multi_line_text_field"
        },
        {
          namespace: "custom",
          key: "purchase_price",
          value: productData.purchasePrice.toString(),
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "variation_type",
          value: "rental_period",
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "booking_type",
          value: "日単位",
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "booking_enabled",
          value: "true",
          type: "single_line_text_field"
        }
      ]
    };

    const createResult = await client.request(createProductMutation, { input: productInput });

    if (createResult.productCreate.userErrors.length > 0) {
      console.error('商品作成エラー:', createResult.productCreate.userErrors);
      return null;
    }

    const product = createResult.productCreate.product;
    console.log(`✓ 商品作成成功: ${product.id}`);

    // 2. バリアントを作成
    console.log('\nバリアント作成中...');
    const variantIds: Record<string, string> = {};

    for (const variant of RENTAL_VARIANTS) {
      const variantSku = `${baseSku}-${variant.suffix}`;
      const variantPrice = VARIANT_MAPPING[variant.suffix].price;

      const createVariantMutation = gql`
        mutation createVariant($input: ProductVariantsBulkInput!) {
          productVariantsBulkCreate(productId: "${product.id}", variants: [$input]) {
            productVariants {
              id
              title
              sku
              price
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variantInput = {
        optionValues: [{ optionName: "レンタル期間", name: variant.title }],
        price: variantPrice.toString(),
        inventoryPolicy: "DENY",
        inventoryQuantities: []
      };

      try {
        const variantResult = await client.request(createVariantMutation, { input: variantInput });
        
        if (variantResult.productVariantsBulkCreate.userErrors.length > 0) {
          console.error(`バリアントエラー (${variant.title}):`, variantResult.productVariantsBulkCreate.userErrors);
        } else {
          const createdVariant = variantResult.productVariantsBulkCreate.productVariants[0];
          variantIds[variant.suffix] = createdVariant.id;
          console.log(`  ✓ ${variant.title}: ¥${variantPrice} (SKU: ${variantSku})`);
        }
      } catch (error) {
        console.error(`バリアント作成エラー (${variant.title}):`, error);
      }
    }

    // 3. ロケーションを取得して在庫を設定
    console.log('\n在庫設定中...');
    const locations = await getLocations();
    const targetLocation = locations.find((loc: any) => loc.name === productData.location) || locations[0];

    if (!targetLocation) {
      console.error('ロケーションが見つかりません');
      return product;
    }

    // 各バリアントの在庫を設定
    for (const [suffix, variantId] of Object.entries(variantIds)) {
      // まずInventoryItemを取得
      const getInventoryItemQuery = gql`
        query getInventoryItem($id: ID!) {
          productVariant(id: $id) {
            inventoryItem {
              id
            }
          }
        }
      `;

      const inventoryItemData = await client.request(getInventoryItemQuery, { id: variantId });
      const inventoryItemId = inventoryItemData.productVariant.inventoryItem.id;

      // 在庫を設定
      const setInventoryMutation = gql`
        mutation setInventory($input: InventorySetQuantitiesInput!) {
          inventorySetQuantities(input: $input) {
            inventoryAdjustmentGroup {
              reason
              changes {
                name
                delta
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const inventoryInput = {
        reason: "correction",
        name: "available",
        ignoreCompareQuantity: true,
        quantities: [{
          inventoryItemId: inventoryItemId,
          locationId: targetLocation.id,
          quantity: productData.quantity
        }]
      };

      try {
        const inventoryResult = await client.request(setInventoryMutation, { input: inventoryInput });
        
        if (inventoryResult.inventorySetQuantities?.userErrors?.length > 0) {
          console.error(`在庫設定エラー (${suffix}):`, inventoryResult.inventorySetQuantities.userErrors);
        } else {
          console.log(`  ✓ ${suffix}: 在庫${productData.quantity}個 (${targetLocation.name})`);
        }
      } catch (error) {
        console.error(`在庫設定エラー (${suffix}):`, error);
      }
    }

    // 4. デフォルトバリアントを削除
    console.log('\nデフォルトバリアントを削除中...');
    const getDefaultVariantQuery = gql`
      query getDefaultVariant($productId: ID!) {
        product(id: $productId) {
          variants(first: 20) {
            edges {
              node {
                id
                title
                sku
              }
            }
          }
        }
      }
    `;

    const variantsData = await client.request(getDefaultVariantQuery, { productId: product.id });
    const defaultVariant = variantsData.product.variants.edges.find((v: any) => 
      v.node.title === "Default Title" || !v.node.sku || v.node.sku === ""
    );

    if (defaultVariant) {
      const deleteVariantMutation = gql`
        mutation deleteVariant($id: ID!) {
          productVariantDelete(id: $id) {
            deletedProductVariantId
            userErrors {
              field
              message
            }
          }
        }
      `;

      try {
        await client.request(deleteVariantMutation, { id: defaultVariant.node.id });
        console.log('✓ デフォルトバリアント削除完了');
      } catch (error) {
        console.log('デフォルトバリアント削除をスキップ');
      }
    }

    console.log(`\n✅ 商品作成完了: ${product.title}`);
    console.log(`   URL: https://${shopName}.myshopify.com/admin/products/${product.id.split('/').pop()}`);
    
    return product;

  } catch (error) {
    console.error('商品作成エラー:', error);
    return null;
  }
}

// メイン実行関数
async function main() {
  console.log('=== 完全商品登録スクリプト開始 ===\n');

  // テスト用商品データ
  const testProducts: ProductData[] = [
    {
      productCode: "10101031",
      detailCode: "001",
      categoryName: "ソファ",
      title: "ベーシックソファ グレー 2シーター",
      kana: "ベーシックソファグレー2シーター",
      description: "シンプルで使いやすい2人掛けソファ。どんなインテリアにも合わせやすいグレーカラー。",
      basePrice: 12000,
      quantity: 1,
      location: "PR",
      width: 160,
      depth: 85,
      height: 78,
      purchasePrice: 45000,
      status: "available",
      maintenanceNotes: "2025年1月メンテナンス済み。状態良好。"
    },
    {
      productCode: "10201015",
      detailCode: "001",
      categoryName: "テーブル",
      title: "ウッドダイニングテーブル 4人用",
      kana: "ウッドダイニングテーブル4ニンヨウ",
      description: "天然木を使用した温かみのあるダイニングテーブル。4人でゆったり使用できます。",
      basePrice: 8000,
      quantity: 1,
      location: "NY",
      width: 120,
      depth: 80,
      height: 72,
      purchasePrice: 35000,
      status: "available",
      maintenanceNotes: "天板に小傷あり。使用には問題なし。"
    }
  ];

  // 商品を作成
  for (const productData of testProducts) {
    await createProduct(productData);
    
    // API制限対策
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  console.log('\n=== 全商品登録完了 ===');
  await prisma.$disconnect();
}

// スクリプト実行
main().catch((error) => {
  console.error('実行エラー:', error);
  process.exit(1);
});