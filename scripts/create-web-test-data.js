import { PrismaClient, ProductStatus, BookingStatus, PaymentMethod, PaymentStatus } from '@prisma/client';

const prisma = new PrismaClient();
const SHOP = 'peaces-test-block.myshopify.com';

// 日付ユーティリティ関数
function addDays(date, days) {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

// 日付フォーマット関数
function formatDate(date) {
  return date.toISOString().split('T')[0];
}

// テスト用の商品データを作成
async function createTestProducts() {
  console.log('テスト用の商品データを作成しています...');

  const products = [
    {
      title: 'テスト商品1 - Webカメラ',
      description: 'テスト用のWebカメラです。高解像度で会議に最適。',
      price: 5000,
      sku: '101-01-001',
      shopifyId: 'gid://shopify/Product/1001',
      status: ProductStatus.AVAILABLE,
      basicInfo: {
        productCode: '101-01-001',
        brand: 'TestBrand',
        color: 'Black',
        weight: '200g',
      },
    },
    {
      title: 'テスト商品2 - Webマイク',
      description: 'テスト用のWebマイクです。ノイズキャンセリング機能付き。',
      price: 3000,
      sku: '101-02-001',
      shopifyId: 'gid://shopify/Product/1002',
      status: ProductStatus.AVAILABLE,
      basicInfo: {
        productCode: '101-02-001',
        brand: 'TestBrand',
        color: 'Silver',
        weight: '150g',
      },
    },
    {
      title: 'テスト商品3 - Webスピーカー',
      description: 'テスト用のWebスピーカーです。クリアな音質でオンライン会議に最適。',
      price: 4000,
      sku: '101-03-001',
      shopifyId: 'gid://shopify/Product/1003',
      status: ProductStatus.AVAILABLE,
      basicInfo: {
        productCode: '101-03-001',
        brand: 'TestBrand',
        color: 'White',
        weight: '300g',
      },
    },
    {
      title: '高級カメラ - プロ仕様',
      description: 'プロフェッショナル向けの高級カメラです。',
      price: 15000,
      sku: '202-01-001',
      shopifyId: 'gid://shopify/Product/2001',
      status: ProductStatus.AVAILABLE,
      basicInfo: {
        productCode: '202-01-001',
        brand: 'ProBrand',
        color: 'Black',
        weight: '800g',
      },
    },
    {
      title: 'ポータブルプロジェクター',
      description: '持ち運びに便利なコンパクトプロジェクターです。',
      price: 8000,
      sku: '303-01-001',
      shopifyId: 'gid://shopify/Product/3001',
      status: ProductStatus.AVAILABLE,
      basicInfo: {
        productCode: '303-01-001',
        brand: 'TechBrand',
        color: 'Gray',
        weight: '500g',
      },
    },
  ];

  const createdProducts = [];

  for (const product of products) {
    const createdProduct = await prisma.product.create({
      data: {
        title: product.title,
        description: product.description,
        price: product.price,
        sku: product.sku,
        shopifyId: product.shopifyId,
        shop: SHOP,
        status: product.status,
        basicInfo: product.basicInfo,
      },
    });
    createdProducts.push(createdProduct);
    console.log(`商品を作成しました: ${product.title} (ID: ${createdProduct.id})`);
  }

  return createdProducts;
}

// テスト用の予約データを作成
async function createTestBookings(products) {
  console.log('テスト用の予約データを作成しています...');

  const today = new Date();
  const bookings = [];

  // 各商品に対して予約を作成
  for (let i = 0; i < products.length; i++) {
    const product = products[i];

    // 過去の予約
    const pastStartDate = addDays(today, -10 - i);
    const pastEndDate = addDays(pastStartDate, 3);

    const pastBooking = await prisma.booking.create({
      data: {
        productId: product.id,
        startDate: pastStartDate,
        endDate: pastEndDate,
        customerName: `テスト顧客${i + 1}`,
        customerEmail: `test${i + 1}@example.com`,
        totalAmount: product.price,
        bookingId: `PAST-${formatDate(new Date())}-${1000 + i}`,
        shop: SHOP,
        status: BookingStatus.COMPLETED,
        paymentMethod: PaymentMethod.CASH,
        paymentStatus: PaymentStatus.PAID,
        metadata: {
          notes: '過去の予約テストデータ',
        },
      },
    });
    bookings.push(pastBooking);
    console.log(`過去の予約を作成しました: ${pastBooking.bookingId}`);

    // 現在の予約
    const currentStartDate = addDays(today, -1);
    const currentEndDate = addDays(today, 2);

    const currentBooking = await prisma.booking.create({
      data: {
        productId: product.id,
        startDate: currentStartDate,
        endDate: currentEndDate,
        customerName: `現在顧客${i + 1}`,
        customerEmail: `current${i + 1}@example.com`,
        totalAmount: product.price,
        bookingId: `CURRENT-${formatDate(new Date())}-${2000 + i}`,
        shop: SHOP,
        status: BookingStatus.CONFIRMED,
        paymentMethod: PaymentMethod.CREDIT_CARD,
        paymentStatus: PaymentStatus.PAID,
        metadata: {
          notes: '現在の予約テストデータ',
        },
      },
    });
    bookings.push(currentBooking);
    console.log(`現在の予約を作成しました: ${currentBooking.bookingId}`);

    // 未来の予約
    const futureStartDate = addDays(today, 5 + i);
    const futureEndDate = addDays(futureStartDate, 3);

    const futureBooking = await prisma.booking.create({
      data: {
        productId: product.id,
        startDate: futureStartDate,
        endDate: futureEndDate,
        customerName: `未来顧客${i + 1}`,
        customerEmail: `future${i + 1}@example.com`,
        totalAmount: product.price,
        bookingId: `FUTURE-${formatDate(new Date())}-${3000 + i}`,
        shop: SHOP,
        status: BookingStatus.CONFIRMED,
        paymentMethod: PaymentMethod.BANK_TRANSFER,
        paymentStatus: PaymentStatus.PENDING,
        metadata: {
          notes: '未来の予約テストデータ',
        },
      },
    });
    bookings.push(futureBooking);
    console.log(`未来の予約を作成しました: ${futureBooking.bookingId}`);
  }

  return bookings;
}

// メイン関数
async function createTestData() {
  try {
    console.log('テストデータの作成を開始します...');

    // 商品データを作成
    const products = await createTestProducts();

    // 予約データを作成
    await createTestBookings(products);

    console.log('テストデータの作成が完了しました！');
  } catch (error) {
    console.error('テストデータの作成中にエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestData();
