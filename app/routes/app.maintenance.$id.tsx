import React, { useState } from 'react';
import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import {
  Page,
  LegacyCard,
  Text,
  Badge,
  Button,
  BlockStack,
  InlineStack,
  Divider,
  Modal,
  Select,
  Toast,
  Frame
} from '@shopify/polaris';
import { prisma } from '../db.server';
import { MaintenanceStatus } from '@prisma/client';

// ステータスに応じたバッジ設定
const statusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  SCHEDULED: { tone: 'attention', label: '予定' },
  IN_PROGRESS: { tone: 'warning', label: '進行中' },
  COMPLETED: { tone: 'success', label: '完了' },
  CANCELLED: { tone: 'critical', label: 'キャンセル' }
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  await authenticate.admin(request);

  const { id } = params;

  if (!id) {
    throw new Response('メンテナンスIDが指定されていません', { status: 400 });
  }

  // メンテナンス詳細を取得
  const maintenance = await prisma.maintenance.findUnique({
    where: { id },
    include: {
      product: true
    }
  });

  if (!maintenance) {
    throw new Response('メンテナンス記録が見つかりません', { status: 404 });
  }

  return json({
    maintenance: {
      id: maintenance.id,
      productId: maintenance.productId,
      productTitle: maintenance.product.title,
      productSku: maintenance.product.sku,
      shopifyProductId: maintenance.shopifyProductId,
      status: maintenance.status,
      type: maintenance.type,
      startDate: maintenance.startDate.toISOString(),
      endDate: maintenance.endDate?.toISOString(),
      completionDate: maintenance.completionDate?.toISOString(),
      notes: maintenance.notes || '',
      createdAt: maintenance.createdAt.toISOString(),
      updatedAt: maintenance.updatedAt.toISOString()
    }
  });
}

export default function MaintenanceDetail() {
  const { maintenance } = useLoaderData<typeof loader>();
  const navigate = useNavigate();

  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(maintenance.status);
  const [isUpdating, setIsUpdating] = useState(false);
  const [toastMessage, setToastMessage] = useState('');

  // 日付をフォーマット
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 日付のみをフォーマット
  const formatDateOnly = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const badge = statusBadgeMap[maintenance.status] || { tone: 'new', label: maintenance.status };

  // メンテナンス種別の日本語表示
  const typeLabels: Record<string, string> = {
    REGULAR_INSPECTION: '定期点検',
    REPAIR: '修理',
    CLEANING: 'クリーニング',
    OTHER: 'その他'
  };

  // ステータス変更のオプション
  const statusOptions = [
    { label: '予定', value: 'SCHEDULED' },
    { label: '進行中', value: 'IN_PROGRESS' },
    { label: '完了', value: 'COMPLETED' },
    { label: 'キャンセル', value: 'CANCELLED' }
  ];

  // ステータス変更処理
  const handleStatusChange = async () => {
    if (selectedStatus === maintenance.status) {
      setIsStatusModalOpen(false);
      return;
    }

    setIsUpdating(true);
    try {
      const response = await fetch('/api/maintenance/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: maintenance.id,
          status: selectedStatus
        })
      });

      if (response.ok) {
        setToastMessage('ステータスを更新しました');
        setIsStatusModalOpen(false);
        // ページをリロードして最新データを表示
        window.location.reload();
      } else {
        throw new Error('ステータス更新に失敗しました');
      }
    } catch (error) {
      console.error('ステータス更新エラー:', error);
      setToastMessage('ステータス更新に失敗しました');
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <Page
      title={`メンテナンス詳細 - ${maintenance.productTitle}`}
      backAction={{
        content: 'メンテナンス一覧に戻る',
        onAction: () => navigate('/app/maintenance')
      }}
      primaryAction={{
        content: '編集',
        onAction: () => navigate(`/app/maintenance/${maintenance.id}/edit`)
      }}
      secondaryActions={[
        {
          content: 'ステータス変更',
          onAction: () => setIsStatusModalOpen(true)
        }
      ]}
    >
      <BlockStack gap="500">
        {/* 基本情報 */}
        <LegacyCard title="基本情報">
          <div style={{ padding: '16px' }}>
            <BlockStack gap="400">
              <InlineStack align="space-between">
                <Text as="h3" variant="headingMd">
                  商品情報
                </Text>
                <Badge tone={badge.tone}>{badge.label}</Badge>
              </InlineStack>

              <BlockStack gap="200">
                <InlineStack gap="400">
                  <Text as="span" variant="bodyMd" fontWeight="bold">商品名:</Text>
                  <Text as="span" variant="bodyMd">{maintenance.productTitle}</Text>
                </InlineStack>

                <InlineStack gap="400">
                  <Text as="span" variant="bodyMd" fontWeight="bold">SKU:</Text>
                  <Text as="span" variant="bodyMd">{maintenance.productSku}</Text>
                </InlineStack>

                <InlineStack gap="400">
                  <Text as="span" variant="bodyMd" fontWeight="bold">Shopify商品ID:</Text>
                  <Text as="span" variant="bodyMd">{maintenance.shopifyProductId}</Text>
                </InlineStack>
              </BlockStack>
            </BlockStack>
          </div>
        </LegacyCard>

        {/* メンテナンス情報 */}
        <LegacyCard title="メンテナンス情報">
          <div style={{ padding: '16px' }}>
            <BlockStack gap="400">
              <InlineStack gap="400">
                <Text as="span" variant="bodyMd" fontWeight="bold">種別:</Text>
                <Text as="span" variant="bodyMd">{typeLabels[maintenance.type] || maintenance.type}</Text>
              </InlineStack>

              <InlineStack gap="400">
                <Text as="span" variant="bodyMd" fontWeight="bold">開始日:</Text>
                <Text as="span" variant="bodyMd">{formatDateOnly(maintenance.startDate)}</Text>
              </InlineStack>

              {maintenance.endDate && (
                <InlineStack gap="400">
                  <Text as="span" variant="bodyMd" fontWeight="bold">終了予定日:</Text>
                  <Text as="span" variant="bodyMd">{formatDateOnly(maintenance.endDate)}</Text>
                </InlineStack>
              )}

              {maintenance.completionDate && (
                <InlineStack gap="400">
                  <Text as="span" variant="bodyMd" fontWeight="bold">完了日:</Text>
                  <Text as="span" variant="bodyMd">{formatDateOnly(maintenance.completionDate)}</Text>
                </InlineStack>
              )}

              {maintenance.notes && (
                <BlockStack gap="200">
                  <Text as="span" variant="bodyMd" fontWeight="bold">備考:</Text>
                  <Text as="p" variant="bodyMd">{maintenance.notes}</Text>
                </BlockStack>
              )}
            </BlockStack>
          </div>
        </LegacyCard>

        {/* システム情報 */}
        <LegacyCard title="システム情報">
          <div style={{ padding: '16px' }}>
            <BlockStack gap="200">
              <InlineStack gap="400">
                <Text as="span" variant="bodySm" fontWeight="bold">作成日時:</Text>
                <Text as="span" variant="bodySm">{formatDate(maintenance.createdAt)}</Text>
              </InlineStack>

              <InlineStack gap="400">
                <Text as="span" variant="bodySm" fontWeight="bold">更新日時:</Text>
                <Text as="span" variant="bodySm">{formatDate(maintenance.updatedAt)}</Text>
              </InlineStack>

              <InlineStack gap="400">
                <Text as="span" variant="bodySm" fontWeight="bold">メンテナンスID:</Text>
                <Text as="span" variant="bodySm">{maintenance.id}</Text>
              </InlineStack>
            </BlockStack>
          </div>
        </LegacyCard>
      </BlockStack>

      {/* ステータス変更モーダル */}
      <Modal
        open={isStatusModalOpen}
        onClose={() => setIsStatusModalOpen(false)}
        title="ステータス変更"
        primaryAction={{
          content: '更新',
          onAction: handleStatusChange,
          loading: isUpdating
        }}
        secondaryActions={[
          {
            content: 'キャンセル',
            onAction: () => setIsStatusModalOpen(false)
          }
        ]}
      >
        <Modal.Section>
          <Select
            label="新しいステータス"
            options={statusOptions}
            value={selectedStatus}
            onChange={setSelectedStatus}
          />
        </Modal.Section>
      </Modal>

      {/* トースト */}
      {toastMessage && (
        <Frame>
          <Toast
            content={toastMessage}
            onDismiss={() => setToastMessage('')}
          />
        </Frame>
      )}
    </Page>
  );
}
