# Shopify-Prisma同期改善計画

このドキュメントでは、ShopifyとPrismaデータベース間のデータ同期を改善するための具体的な実装計画を説明します。

## 目次

1. [現状の問題点](#現状の問題点)
2. [改善目標](#改善目標)
3. [実装計画](#実装計画)
4. [テスト計画](#テスト計画)
5. [スケジュール](#スケジュール)

## 現状の問題点

現在のShopify-Prisma同期には以下の問題があります：

1. **同期の信頼性**: Shopifyで作成した商品がPrismaに正しく同期されない場合がある
2. **SKU管理**: ShopifyでSKUが変更された場合、Prismaでの照合が困難
3. **商品削除の検出**: 商品削除時の処理が未実装
4. **エラー処理**: 同期エラー時の再試行と通知が不十分
5. **同期状態の可視化**: 同期状態を確認する手段が限られている

## 改善目標

以下の目標を達成することで、同期の信頼性と効率を向上させます：

1. **100%の同期率**: すべてのShopify商品がPrismaに正確に同期される
2. **堅牢なSKU管理**: SKUが変更されても商品を正確に照合できる
3. **完全なライフサイクル管理**: 商品の作成から削除までのすべての状態変化を追跡
4. **効率的なエラー処理**: エラーの自動検出、再試行、通知
5. **透明性の向上**: 同期状態の可視化と問題の早期発見

## 実装計画

### フェーズ1: 基盤強化（優先度: 高）

#### 1.1 元のSKU保存機能の実装

```typescript
// app/services/sync/db-sync.service.ts に追加

// 商品データを作成または更新
if (product) {
  // 既存商品を更新
  product = await prisma.product.update({
    where: { id: product.id },
    data: {
      title: shopifyProduct.title,
      sku: sku,
      originalSku: product.originalSku || product.sku, // 元のSKUを保持
      // ... 他のフィールド
    }
  });
} else {
  // 新規商品を作成
  product = await prisma.product.create({
    data: {
      shopifyId: shopifyProductId,
      title: shopifyProduct.title,
      sku: sku,
      originalSku: sku, // 元のSKUを保存
      // ... 他のフィールド
    }
  });
}
```

#### 1.2 商品照合ロジックの強化

```typescript
// app/services/sync/db-sync.service.ts に追加

// 商品を検索する関数を強化
async findProductByShopifyData(shopifyProduct) {
  // 1. ShopifyIDで検索
  let product = await prisma.product.findFirst({
    where: { shopifyId: shopifyProduct.id }
  });
  
  if (product) return product;
  
  // 2. SKUで検索
  const sku = this.extractSku(shopifyProduct);
  if (sku) {
    product = await prisma.product.findFirst({
      where: {
        OR: [
          { sku },
          { originalSku: sku }
        ]
      }
    });
    
    if (product) return product;
  }
  
  // 3. タイトルで検索（最終手段）
  product = await prisma.product.findFirst({
    where: { 
      title: shopifyProduct.title,
      shop: this.shop
    }
  });
  
  return product;
}
```

#### 1.3 同期ステータスの拡張

```typescript
// prisma/schema.prisma に追加

enum SyncStatus {
  PENDING
  PROCESSING
  SYNCED
  ERROR
  DELETED
}

model Product {
  // ... 既存のフィールド
  syncStatus      SyncStatus     @default(PENDING)
  syncErrorCount  Int            @default(0)
  syncErrorMessage String?
  lastSyncAttempt DateTime?
  lastSyncedAt    DateTime?
}
```

### フェーズ2: 同期機能拡張（優先度: 中）

#### 2.1 商品削除Webhookの実装

```typescript
// app/routes/webhooks.products.delete.tsx を新規作成

import { json, type ActionFunctionArgs } from "@remix-run/node";
import { prisma } from "~/db.server";
import { authenticate } from "~/shopify.server";

export async function action({ request }: ActionFunctionArgs) {
  try {
    // Shopify認証
    const { admin, session, payload } = await authenticate.webhook(request);

    console.log("商品削除webhook受信:", payload.id);

    // 商品IDの取得
    const productId = payload.id;
    const shop = session.shop;

    // 商品を検索
    const product = await prisma.product.findFirst({
      where: { shopifyId: productId, shop }
    });

    if (product) {
      // 商品を論理削除
      await prisma.product.update({
        where: { id: product.id },
        data: {
          syncStatus: 'DELETED',
          lastSyncedAt: new Date(),
          metadata: {
            ...product.metadata,
            deletedAt: new Date().toISOString(),
            deletedVia: 'webhook'
          }
        }
      });

      console.log(`商品ID ${productId} を論理削除しました`);
    } else {
      console.log(`商品ID ${productId} が見つかりません`);
    }

    return json({ success: true });
  } catch (error) {
    console.error("商品削除webhook処理エラー:", error);
    return json({ error: "内部サーバーエラー" }, { status: 500 });
  }
}
```

#### 2.2 定期的な完全同期の強化

```typescript
// app/jobs/syncProducts.server.ts に追加

// 削除された商品を検出する関数
async function detectDeletedProducts() {
  try {
    // Prismaに存在するすべての商品のShopifyIDを取得
    const products = await prisma.product.findMany({
      where: { 
        syncStatus: { not: 'DELETED' },
        shopifyId: { not: null }
      },
      select: { id: true, shopifyId: true }
    });
    
    // ShopifyIDのリストを作成
    const shopifyIds = products.map(p => p.shopifyId.replace('gid://shopify/Product/', ''));
    
    // バッチ処理（100件ずつ）
    for (let i = 0; i < shopifyIds.length; i += 100) {
      const batch = shopifyIds.slice(i, i + 100);
      
      // GraphQL APIで商品の存在を確認
      const query = `
        query {
          nodes(ids: [${batch.map(id => `"gid://shopify/Product/${id}"`).join(', ')}]) {
            id
          }
        }
      `;
      
      const response = await admin.graphql(query);
      const data = await response.json();
      
      // 存在しない商品を特定
      const existingIds = data.data.nodes
        .filter(node => node !== null)
        .map(node => node.id);
      
      const deletedIds = batch
        .map(id => `gid://shopify/Product/${id}`)
        .filter(id => !existingIds.includes(id));
      
      // 削除された商品を論理削除
      for (const deletedId of deletedIds) {
        const product = products.find(p => p.shopifyId === deletedId);
        if (product) {
          await prisma.product.update({
            where: { id: product.id },
            data: {
              syncStatus: 'DELETED',
              lastSyncedAt: new Date(),
              metadata: {
                ...product.metadata,
                deletedAt: new Date().toISOString(),
                deletedVia: 'scheduled_sync'
              }
            }
          });
          
          console.log(`商品ID ${deletedId} を論理削除しました（定期同期）`);
        }
      }
    }
  } catch (error) {
    console.error("削除商品検出エラー:", error);
  }
}
```

### フェーズ3: エラー処理と通知（優先度: 中）

#### 3.1 再試行メカニズムの強化

```typescript
// app/services/sync/retry.service.ts を新規作成

export class RetryService {
  // 指数バックオフによる再試行
  static async withExponentialBackoff(
    fn: () => Promise<any>,
    maxRetries = 3,
    initialDelay = 1000
  ) {
    let lastError;
    
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        console.warn(`操作に失敗しました (${attempt + 1}/${maxRetries}):`, error);
        
        if (attempt < maxRetries - 1) {
          const delay = initialDelay * Math.pow(2, attempt);
          console.log(`${delay}ms後に再試行します...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError;
  }
  
  // 同期エラーを記録
  static async logSyncError(
    prisma,
    entityType,
    entityId,
    shopifyId,
    error
  ) {
    try {
      await prisma.syncLog.create({
        data: {
          entityType,
          entityId,
          operation: `${entityType}_sync`,
          status: 'error',
          shopifyId,
          errorMessage: error.message,
          metadata: {
            stack: error.stack,
            timestamp: new Date().toISOString()
          }
        }
      });
      
      // エンティティの同期ステータスを更新
      if (entityType === 'product' && entityId) {
        await prisma.product.update({
          where: { id: entityId },
          data: {
            syncStatus: 'ERROR',
            syncErrorCount: { increment: 1 },
            syncErrorMessage: error.message,
            lastSyncAttempt: new Date()
          }
        });
      }
    } catch (logError) {
      console.error("同期エラーのログ記録に失敗:", logError);
    }
  }
}
```

#### 3.2 同期エラー通知システム

```typescript
// app/services/notification.service.ts を新規作成

export class NotificationService {
  // エラー通知を送信
  static async sendSyncErrorNotification(
    entityType,
    entityId,
    shopifyId,
    error
  ) {
    try {
      // 環境変数から通知設定を取得
      const notifyEmail = process.env.SYNC_ERROR_NOTIFY_EMAIL;
      const notifySlack = process.env.SYNC_ERROR_NOTIFY_SLACK;
      
      if (!notifyEmail && !notifySlack) {
        console.log("通知先が設定されていないため、通知はスキップされます");
        return;
      }
      
      const errorMessage = `
        同期エラーが発生しました:
        - タイプ: ${entityType}
        - ID: ${entityId}
        - ShopifyID: ${shopifyId}
        - エラー: ${error.message}
        - 発生時刻: ${new Date().toISOString()}
      `;
      
      // メール通知
      if (notifyEmail) {
        // メール送信ロジック（実装省略）
        console.log(`エラー通知メールを送信: ${notifyEmail}`);
      }
      
      // Slack通知
      if (notifySlack) {
        // Slack通知ロジック（実装省略）
        console.log(`Slack通知を送信: ${notifySlack}`);
      }
    } catch (notifyError) {
      console.error("通知送信エラー:", notifyError);
    }
  }
}
```

### フェーズ4: 管理UI（優先度: 低）

#### 4.1 同期ステータスダッシュボード

```typescript
// app/routes/app.sync-dashboard.tsx を新規作成

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { prisma } from "~/db.server";
import { authenticate } from "~/shopify.server";

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  
  // 同期ステータスの集計
  const statusCounts = await prisma.product.groupBy({
    by: ['syncStatus'],
    _count: { id: true }
  });
  
  // 最近の同期エラー
  const recentErrors = await prisma.product.findMany({
    where: { syncStatus: 'ERROR' },
    orderBy: { lastSyncAttempt: 'desc' },
    take: 10,
    select: {
      id: true,
      title: true,
      shopifyId: true,
      syncErrorMessage: true,
      syncErrorCount: true,
      lastSyncAttempt: true
    }
  });
  
  // 同期ログの集計
  const syncLogs = await prisma.syncLog.groupBy({
    by: ['status'],
    _count: { id: true }
  });
  
  return json({
    statusCounts,
    recentErrors,
    syncLogs
  });
}

// UIコンポーネント（実装省略）
```

## テスト計画

### 1. 単体テスト

- **商品照合ロジックのテスト**: 様々なシナリオでの商品照合テスト
- **同期ステータス管理のテスト**: 同期ステータスの更新と管理のテスト
- **再試行メカニズムのテスト**: エラー時の再試行動作のテスト

### 2. 統合テスト

- **Webhook処理フローのテスト**: Webhookの受信から処理完了までのフロー
- **商品削除検出のテスト**: 削除された商品の検出と処理
- **完全同期のテスト**: すべての商品の完全同期プロセス

### 3. エンドツーエンドテスト

- **商品ライフサイクルテスト**: 商品の作成→更新→削除の全フロー
- **エラー回復テスト**: 同期エラーからの回復プロセス
- **通知システムテスト**: エラー通知の送信と受信

## スケジュール

| フェーズ | タスク | 優先度 | 見積工数 |
|---------|------|--------|---------|
| 1.1 | 元のSKU保存機能の実装 | 高 | 1日 |
| 1.2 | 商品照合ロジックの強化 | 高 | 2日 |
| 1.3 | 同期ステータスの拡張 | 高 | 1日 |
| 2.1 | 商品削除Webhookの実装 | 中 | 2日 |
| 2.2 | 定期的な完全同期の強化 | 中 | 3日 |
| 3.1 | 再試行メカニズムの強化 | 中 | 2日 |
| 3.2 | 同期エラー通知システム | 中 | 2日 |
| 4.1 | 同期ステータスダッシュボード | 低 | 3日 |

**合計見積工数**: 16日

**注意**: 実際の工数は、既存コードの複雑さやテスト範囲によって変動する可能性があります。
