/**
 * 実際のCSVデータを使用したテストデータ作成スクリプト
 *
 * このスクリプトは、rental_items_20250509_153839.csvから商品データを読み込み、
 * Shopifyに商品を登録します。各商品には1〜8日のレンタル期間バリアントと
 * 仮予約オプションが設定されます。
 */

import { GraphQLClient, gql } from 'graphql-request';
import fs from 'fs';
import { parse } from 'csv-parse/sync';
import dotenv from 'dotenv';

dotenv.config();

// 設定
const INPUT_PATH = 'master-data-csv/rental_items_20250509_153839.csv';
const MAX_PRODUCTS = 20; // 作成する商品の最大数

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品作成のGraphQLミューテーション
const CREATE_PRODUCT = gql`
  mutation productCreate($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールド設定のGraphQLミューテーション
const SET_METAFIELDS = gql`
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 商品バリアント作成のGraphQLミューテーション
const CREATE_VARIANT = gql`
  mutation productVariantCreate($input: ProductVariantInput!) {
    productVariantCreate(input: $input) {
      productVariant {
        id
        title
        sku
        price
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// CSVファイルを読み込む関数
function readCsvFile(filePath) {
  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const records = parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
      relax_quotes: true,
      relax_column_count: true
    });
    return records;
  } catch (error) {
    console.error(`CSVファイル ${filePath} の読み込み中にエラーが発生しました:`, error);
    return [];
  }
}

// 商品データを準備する関数
function prepareProductData(record) {
  // カテゴリー情報を取得
  const categories = [];
  if (record['家具小道具メニュー']) categories.push(record['家具小道具メニュー']);
  if (record['家具小道具大カテゴリー']) {
    const bigCategories = record['家具小道具大カテゴリー'].split(';');
    categories.push(...bigCategories);
  }
  if (record['家具小道具小カテゴリー']) {
    const smallCategories = record['家具小道具小カテゴリー'].split(';');
    categories.push(...smallCategories);
  }

  // 色情報を取得
  const colors = [];
  if (record['色']) {
    const colorValues = record['色'].split(';');
    colors.push(...colorValues.filter(color => color.trim() !== ''));
  }

  // 素材情報を取得
  const materials = [];
  if (record['素材']) {
    const materialValues = record['素材'].split(';');
    materials.push(...materialValues.filter(material => material.trim() !== ''));
  }

  // タグ情報を取得
  const tags = [];
  if (record['キャンペーン']) {
    const campaignValues = record['キャンペーン'].split(';');
    tags.push(...campaignValues.filter(tag => tag.trim() !== ''));
  }

  // カテゴリーもタグに追加
  tags.push(...categories);

  // 在庫場所（デフォルトはNY、PRが含まれていればPR）
  const inventoryLocation = record['在庫場所'] || 'NY';

  // ステータス（デフォルトはavailable、非公開ならunavailable）
  let status = 'available';
  if (record['公開ステータス(公開/非公開)'] === '非公開') {
    status = 'unavailable';
  }

  // 基本料金（1日レンタル料金）
  const basePrice = parseFloat(record['レンタル料金1日']) || 1000;

  // SKUの作成（型番をベースに）
  const sku = record['型番'] || `SKU-${Math.floor(Math.random() * 10000)}`;

  // バリアント情報の作成
  // 料金計算関数
  function calculatePrice(days, basePrice) {
    let totalPrice = basePrice; // 1日目は基本料金

    if (days > 1) {
      const day2to6Count = Math.min(days - 1, 5); // 最大5日間（2日目〜6日目）
      totalPrice += basePrice * 0.2 * day2to6Count;
    }

    if (days > 6) {
      const day7PlusCount = days - 6;
      totalPrice += basePrice * 0.1 * day7PlusCount;
    }

    return Math.round(totalPrice);
  }

  const variants = [
    { title: '1日レンタル', price: record['レンタル料金1日'] || basePrice.toString(), sku: `${sku}-1D`, inventoryQuantity: parseInt(record['在庫数']) || 1 },
    { title: '2日レンタル', price: record['レンタル料金(1泊2日)'] || calculatePrice(2, basePrice).toString(), sku: `${sku}-2D`, inventoryQuantity: parseInt(record['在庫数']) || 1 },
    { title: '3日レンタル', price: record['レンタル料金(2泊3日)'] || calculatePrice(3, basePrice).toString(), sku: `${sku}-3D`, inventoryQuantity: parseInt(record['在庫数']) || 1 },
    { title: '4日レンタル', price: record['レンタル料金(3泊4日)'] || calculatePrice(4, basePrice).toString(), sku: `${sku}-4D`, inventoryQuantity: parseInt(record['在庫数']) || 1 },
    { title: '5日レンタル', price: calculatePrice(5, basePrice).toString(), sku: `${sku}-5D`, inventoryQuantity: parseInt(record['在庫数']) || 1 },
    { title: '6日レンタル', price: calculatePrice(6, basePrice).toString(), sku: `${sku}-6D`, inventoryQuantity: parseInt(record['在庫数']) || 1 },
    { title: '7日レンタル', price: calculatePrice(7, basePrice).toString(), sku: `${sku}-7D`, inventoryQuantity: parseInt(record['在庫数']) || 1 },
    { title: '8日以上レンタル', price: calculatePrice(8, basePrice).toString(), sku: `${sku}-8D+`, inventoryQuantity: parseInt(record['在庫数']) || 1 },
    { title: '仮予約', price: Math.round(basePrice * 0.1).toString(), sku: `${sku}-PROV`, inventoryQuantity: parseInt(record['在庫数']) || 1 }
  ];

  // 商品情報の作成
  return {
    title: record['名前'] || 'No Name',
    description: record['その他'] || '',
    productType: categories[0] || 'その他',
    vendor: 'IZIZ',
    tags,
    variants,
    metafields: {
      basic_info: {
        product_code: sku,
        detail_code: record['型番'] || '',
        status,
        inventory_location: inventoryLocation,
        size: {
          width: record['サイズW'] || '0',
          depth: record['サイズD'] || '0',
          height: record['サイズH'] || '0',
          seat_height: record['サイズSH'] || '0',
          other: record['サイズ(その他)'] || ''
        },
        color: colors.join(';'),
        color_other: record['色(その他)'] || '',
        material: materials.join(';'),
        other_info: record['その他'] || ''
      },
      pricing: {
        base_price: basePrice,
        discount_rules: {
          '2-7': 0.8,
          '8+': 0.9
        }
      }
    }
  };
}

// 商品を作成する関数
async function createProduct(productData) {
  try {
    console.log(`商品「${productData.title}」を作成中...`);

    // 基本的な商品情報を作成
    const result = await client.request(CREATE_PRODUCT, {
      input: {
        title: productData.title,
        descriptionHtml: productData.description,
        productType: productData.productType,
        vendor: productData.vendor,
        tags: productData.tags,
        options: ['レンタル期間'],
        variants: [
          {
            price: productData.variants[0].price,
            sku: productData.variants[0].sku,
            inventoryManagement: 'SHOPIFY',
            inventoryPolicy: 'DENY',
            options: [productData.variants[0].title]
          }
        ]
      }
    });

    if (result.productCreate.userErrors.length > 0) {
      console.error(`商品「${productData.title}」の作成中にエラーが発生しました:`, result.productCreate.userErrors);
      return null;
    }

    const productId = result.productCreate.product.id;
    console.log(`商品「${productData.title}」を作成しました。ID: ${productId}`);

    // メタフィールドを設定
    if (productData.metafields) {
      const metafields = [];

      for (const namespace in productData.metafields) {
        const value = JSON.stringify(productData.metafields[namespace]);

        metafields.push({
          ownerId: productId,
          namespace,
          key: namespace,
          value,
          type: 'json'
        });
      }

      const metafieldsResult = await client.request(SET_METAFIELDS, {
        metafields
      });

      if (metafieldsResult.metafieldsSet.userErrors.length > 0) {
        console.error(`商品「${productData.title}」のメタフィールド設定中にエラーが発生しました:`, metafieldsResult.metafieldsSet.userErrors);
      } else {
        console.log(`商品「${productData.title}」のメタフィールドを設定しました。`);
      }
    }

    // 追加のバリアントを作成
    for (let i = 1; i < productData.variants.length; i++) {
      const variant = productData.variants[i];

      const variantResult = await client.request(CREATE_VARIANT, {
        input: {
          productId,
          price: variant.price,
          sku: variant.sku,
          inventoryManagement: 'SHOPIFY',
          inventoryPolicy: 'DENY',
          options: [variant.title]
        }
      });

      if (variantResult.productVariantCreate.userErrors.length > 0) {
        console.error(`バリアント「${variant.title}」の作成中にエラーが発生しました:`, variantResult.productVariantCreate.userErrors);
      } else {
        console.log(`バリアント「${variant.title}」を作成しました。`);
      }

      // APIレート制限対策
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    return productId;
  } catch (error) {
    console.error(`商品「${productData.title}」の作成中にエラーが発生しました:`, error);
    return null;
  }
}

// メイン処理
async function main() {
  console.log('実際のCSVデータを使用したテストデータ作成を開始します...');

  // CSVファイルを読み込む
  console.log(`${INPUT_PATH} を読み込んでいます...`);
  const records = readCsvFile(INPUT_PATH);
  console.log(`${records.length}件の商品データを読み込みました。`);

  // 商品データを作成
  console.log('商品データを作成しています...');
  const productIds = [];

  // 最大MAX_PRODUCTS件の商品を作成
  const selectedRecords = records.slice(0, MAX_PRODUCTS);

  for (const record of selectedRecords) {
    const productData = prepareProductData(record);
    const productId = await createProduct(productData);
    if (productId) {
      productIds.push(productId);
    }

    // APIレート制限対策
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('テストデータ作成が完了しました。');
  console.log(`作成した商品: ${productIds.length}件`);
}

// スクリプト実行
main().catch(error => {
  console.error('スクリプト実行中にエラーが発生しました:', error);
  process.exit(1);
});
