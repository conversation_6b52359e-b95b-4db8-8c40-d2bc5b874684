/**
 * テストレンタル商品作成スクリプト
 *
 * このスクリプトは、レンタル予約システムのテスト用の商品を作成し、
 * 必要なメタフィールドを設定します。
 */

import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品作成のGraphQLミューテーション
const CREATE_PRODUCT = gql`
  mutation productCreate($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// バリアント作成のGraphQLミューテーション
const CREATE_VARIANT = gql`
  mutation productVariantCreate($input: ProductVariantInput!) {
    productVariantCreate(input: $input) {
      productVariant {
        id
        title
        sku
        price
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールド設定のGraphQLミューテーション
const SET_METAFIELDS = gql`
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 商品を作成する関数
async function createProduct(title, productType, vendor, tags) {
  try {
    const result = await client.request(CREATE_PRODUCT, {
      input: {
        title,
        productType,
        vendor,
        tags,
        options: ['レンタル日数']
      }
    });

    if (result.productCreate.userErrors.length > 0) {
      console.error('商品作成中にエラーが発生しました:', result.productCreate.userErrors);
      return null;
    }

    console.log(`商品 ${title} を作成しました。`);
    return result.productCreate.product;
  } catch (error) {
    console.error('商品作成中にエラーが発生しました:', error);
    return null;
  }
}

// バリアントを作成する関数
async function createVariant(productId, title, price, sku, options) {
  try {
    const result = await client.request(CREATE_VARIANT, {
      input: {
        productId,
        title,
        price,
        sku,
        options
      }
    });

    if (result.productVariantCreate.userErrors.length > 0) {
      console.error(`バリアント ${title} 作成中にエラーが発生しました:`, result.productVariantCreate.userErrors);
      return null;
    }

    console.log(`バリアント ${title} を作成しました。`);
    return result.productVariantCreate.productVariant;
  } catch (error) {
    console.error(`バリアント ${title} 作成中にエラーが発生しました:`, error);
    return null;
  }
}

// メタフィールドを設定する関数
async function setMetafields(ownerId, metafields) {
  try {
    const metafieldsInput = metafields.map(metafield => ({
      ownerId,
      namespace: metafield.namespace,
      key: metafield.key,
      value: JSON.stringify(metafield.value),
      type: 'json'
    }));

    const result = await client.request(SET_METAFIELDS, {
      metafields: metafieldsInput
    });

    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定中にエラーが発生しました:', result.metafieldsSet.userErrors);
      return false;
    }

    console.log('メタフィールドを設定しました。');
    return true;
  } catch (error) {
    console.error('メタフィールド設定中にエラーが発生しました:', error);
    return false;
  }
}

// メインの実行関数
async function createTestProduct() {
  console.log('テスト商品の作成を開始します...');

  // 商品を作成
  const product = await createProduct(
    '【テスト】ベーシックソファ オフホワイト 1シーター',
    'レンタル商品',
    'テスト',
    'テスト,レンタル商品'
  );

  if (!product) {
    console.error('テスト商品の作成に失敗しました。');
    return;
  }

  // バリアントを作成
  const variants = [
    { title: '1日レンタル', price: '8000', sku: '10101007-001-001' },
    { title: '2日レンタル', price: '9600', sku: '10101007-001-002' },
    { title: '3日レンタル', price: '11200', sku: '10101007-001-003' },
    { title: '4日レンタル', price: '12800', sku: '10101007-001-004' },
    { title: '5日レンタル', price: '14400', sku: '10101007-001-005' },
    { title: '6日レンタル', price: '16000', sku: '10101007-001-006' },
    { title: '7日レンタル', price: '17600', sku: '10101007-001-007' },
    { title: '8日以上レンタル', price: '18400', sku: '10101007-001-008' }
  ];

  for (const variant of variants) {
    await createVariant(
      product.id,
      variant.title,
      variant.price,
      variant.sku,
      [variant.title]
    );
  }

  // メタフィールドを設定
  const metafields = [
    {
      namespace: 'rental',
      key: 'basic_info',
      value: {
        productCode: '10101007',
        detailCode: '001',
        kana: 'ベーシックソファオフホワイト1シーター',
        dimensions: {
          width: 87,
          depth: 74,
          height: 76
        },
        seatDimensions: {
          width: 52,
          depth: 54,
          height: 40
        },
        material: 'ファブリック',
        color: 'オフホワイト',
        maker: 'ヤマナリ',
        campaign: '通常商品',
        notes: 'クリーニング済みですが全体的に黄ばみ発生（H30.12.15)'
      }
    },
    {
      namespace: 'rental',
      key: 'pricing',
      value: {
        basePrice: 8000,
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30
      }
    },
    {
      namespace: 'rental',
      key: 'inventory_items',
      value: [
        {
          id: 'item-10101007-001-1',
          sku: '10101007-001-1',
          status: 'available',
          location: 'NY',
          notes: '背面向かって右側うっすら黒いしみ'
        },
        {
          id: 'item-10101007-001-2',
          sku: '10101007-001-2',
          status: 'maintenance',
          location: 'NY',
          notes: '向かって左アーム手前と正面左側に黄色い輪染み有'
        }
      ]
    },
    {
      namespace: 'rental',
      key: 'reservation_info',
      value: [
        {
          itemId: 'item-10101007-001-1',
          reservations: [
            {
              id: 'reservation-001',
              startDate: '2025-05-22',
              endDate: '2025-05-25',
              status: 'confirmed',
              customerName: '山田太郎',
              customerEmail: '<EMAIL>',
              orderId: 'gid://shopify/Order/1001',
              orderLineItemId: 'gid://shopify/LineItem/1001',
              notes: '特になし'
            }
          ]
        },
        {
          itemId: 'item-10101007-001-2',
          reservations: []
        }
      ]
    }
  ];

  await setMetafields(product.id, metafields);

  console.log('テスト商品の作成が完了しました。');
}

// スクリプト実行
createTestProduct().catch(error => {
  console.error('スクリプト実行中にエラーが発生しました:', error);
  process.exit(1);
});
