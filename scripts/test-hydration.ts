/**
 * Reactハイドレーションエラーのテストスクリプト
 * 
 * このスクリプトは、ハイドレーションエラーの原因を特定し、修正するためのものです。
 * 
 * 実行方法: npx tsx scripts/test-hydration.ts
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// 検索するファイルの拡張子
const extensions = ['.tsx', '.jsx', '.ts', '.js'];

// 検索するディレクトリ
const searchDirs = ['app/components', 'app/routes'];

// 問題となる可能性のあるパターン
const patterns = [
  {
    name: 'クライアントサイド専用のAPIの使用',
    regex: /typeof\s+window\s+!==\s+['"]undefined['"]/g,
    description: 'window オブジェクトへの参照がサーバーサイドレンダリング中に undefined になる可能性があります。',
    solution: 'useEffect フックを使用して、クライアントサイドでのみ実行されるようにしてください。'
  },
  {
    name: '条件付きレンダリング',
    regex: /\{(.*?)\s*\?\s*(.*?)\s*:\s*(.*?)\}/g,
    description: '条件付きレンダリングがサーバーとクライアントで異なる結果になる可能性があります。',
    solution: 'データの初期状態を明示的に設定し、サーバーとクライアントで同じ条件になるようにしてください。'
  },
  {
    name: 'useEffect内でのステート更新',
    regex: /useEffect\(\s*\(\)\s*=>\s*\{[^}]*set[A-Z]/g,
    description: 'useEffect内でのステート更新がハイドレーションの不一致を引き起こす可能性があります。',
    solution: 'useEffect の依存配列を適切に設定し、必要な場合のみステートを更新するようにしてください。'
  },
  {
    name: '非ブール属性にブール値を使用',
    regex: /(external|textAlign)\s*=\s*\{true\}/g,
    description: '非ブール属性にブール値を使用すると警告が発生します。',
    solution: 'ブール値を文字列に変換してください: external={true.toString()} または external="true"'
  },
  {
    name: 'DOM要素に無効なプロップを渡す',
    regex: /<(div|span|a|button|input|textarea|select|form|img|ul|li|ol|h[1-6]|p|section|article|nav|header|footer|main|aside)\s+[^>]*?(textAlign|className|style|onClick|onChange|onSubmit|onBlur|onFocus|onKeyDown|onKeyUp|onKeyPress|onMouseDown|onMouseUp|onMouseMove|onMouseOver|onMouseOut|onMouseEnter|onMouseLeave|onTouchStart|onTouchEnd|onTouchMove|onTouchCancel|onScroll|onWheel|onDrag|onDragStart|onDragEnd|onDragEnter|onDragLeave|onDragOver|onDrop)[^>]*?>/g,
    description: 'DOM要素に無効なプロップを渡している可能性があります。',
    solution: 'DOM要素に渡すプロップが有効かどうか確認してください。textAlignなどのプロップはstyleオブジェクト内で使用してください。'
  }
];

/**
 * ファイルを検索して問題のあるパターンを見つける
 */
function findProblematicPatterns() {
  const results: {
    file: string;
    line: number;
    pattern: string;
    description: string;
    solution: string;
    code: string;
  }[] = [];

  // 各ディレクトリを検索
  for (const dir of searchDirs) {
    const files = findFilesRecursively(dir, extensions);
    
    // 各ファイルを検査
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf-8');
      const lines = content.split('\n');
      
      // 各パターンをチェック
      for (const pattern of patterns) {
        const regex = pattern.regex;
        let match;
        
        // ファイル全体で正規表現を検索
        while ((match = regex.exec(content)) !== null) {
          // マッチした行番号を見つける
          const matchedText = match[0];
          const index = match.index;
          let lineNumber = 1;
          let currentIndex = 0;
          
          for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (currentIndex + line.length >= index) {
              lineNumber = i + 1;
              break;
            }
            currentIndex += line.length + 1; // +1 for the newline character
          }
          
          // コードスニペットを取得（前後の行を含む）
          const startLine = Math.max(0, lineNumber - 3);
          const endLine = Math.min(lines.length, lineNumber + 3);
          const codeSnippet = lines.slice(startLine, endLine).join('\n');
          
          results.push({
            file,
            line: lineNumber,
            pattern: pattern.name,
            description: pattern.description,
            solution: pattern.solution,
            code: codeSnippet
          });
        }
      }
    }
  }
  
  return results;
}

/**
 * 再帰的にファイルを検索
 */
function findFilesRecursively(dir: string, extensions: string[]): string[] {
  const files: string[] = [];
  
  if (!fs.existsSync(dir)) {
    return files;
  }
  
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    
    if (entry.isDirectory()) {
      files.push(...findFilesRecursively(fullPath, extensions));
    } else if (entry.isFile() && extensions.includes(path.extname(entry.name))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * ShopifyCustomerDisplayコンポーネントを特別にチェック
 */
function checkShopifyCustomerDisplay() {
  const filePath = 'app/components/ShopifyCustomerDisplay.tsx';
  
  if (!fs.existsSync(filePath)) {
    console.log('ShopifyCustomerDisplayコンポーネントが見つかりません。');
    return null;
  }
  
  const content = fs.readFileSync(filePath, 'utf-8');
  
  // サーバーサイドとクライアントサイドの処理の違いをチェック
  const serverClientCheck = content.includes('typeof window !== "undefined"') || 
                           content.includes("typeof window !== 'undefined'");
  
  // useEffectの使用をチェック
  const useEffectCheck = content.includes('useEffect');
  
  // 条件付きレンダリングをチェック
  const conditionalRenderingCheck = /\{.*\?.*:.*\}/.test(content);
  
  return {
    serverClientCheck,
    useEffectCheck,
    conditionalRenderingCheck,
    content
  };
}

/**
 * BookingsNewPageコンポーネントをチェック
 */
function checkBookingsNewPage() {
  const filePath = 'app/routes/app.bookings.new.tsx';
  
  if (!fs.existsSync(filePath)) {
    console.log('BookingsNewPageコンポーネントが見つかりません。');
    return null;
  }
  
  const content = fs.readFileSync(filePath, 'utf-8');
  
  // textAlignプロップの使用をチェック
  const textAlignCheck = content.includes('textAlign={');
  
  // 行番号539付近のコードを取得
  const lines = content.split('\n');
  const lineNumber = 539;
  const startLine = Math.max(0, lineNumber - 5);
  const endLine = Math.min(lines.length, lineNumber + 5);
  const codeSnippet = lines.slice(startLine, endLine).join('\n');
  
  return {
    textAlignCheck,
    codeSnippet
  };
}

/**
 * ShopifyCustomerSearchコンポーネントをチェック
 */
function checkShopifyCustomerSearch() {
  const filePath = 'app/components/ShopifyCustomerSearch.tsx';
  
  if (!fs.existsSync(filePath)) {
    console.log('ShopifyCustomerSearchコンポーネントが見つかりません。');
    return null;
  }
  
  const content = fs.readFileSync(filePath, 'utf-8');
  
  // externalプロップの使用をチェック
  const externalCheck = content.includes('external={true}');
  
  // 行番号28付近のコードを取得
  const lines = content.split('\n');
  const lineNumber = 28;
  const startLine = Math.max(0, lineNumber - 5);
  const endLine = Math.min(lines.length, lineNumber + 5);
  const codeSnippet = lines.slice(startLine, endLine).join('\n');
  
  return {
    externalCheck,
    codeSnippet
  };
}

/**
 * メイン処理
 */
async function main() {
  console.log('Reactハイドレーションエラーのテストを開始します...');
  
  // 問題のあるパターンを検索
  console.log('\n=== 問題のあるパターンの検索 ===');
  const results = findProblematicPatterns();
  
  if (results.length > 0) {
    console.log(`${results.length}件の潜在的な問題が見つかりました。`);
    
    for (const result of results) {
      console.log(`\nファイル: ${result.file}`);
      console.log(`行番号: ${result.line}`);
      console.log(`パターン: ${result.pattern}`);
      console.log(`説明: ${result.description}`);
      console.log(`解決策: ${result.solution}`);
      console.log('コード:');
      console.log('```');
      console.log(result.code);
      console.log('```');
    }
  } else {
    console.log('問題のあるパターンは見つかりませんでした。');
  }
  
  // ShopifyCustomerDisplayコンポーネントをチェック
  console.log('\n=== ShopifyCustomerDisplayコンポーネントのチェック ===');
  const customerDisplayCheck = checkShopifyCustomerDisplay();
  
  if (customerDisplayCheck) {
    console.log(`サーバー/クライアント分岐: ${customerDisplayCheck.serverClientCheck ? 'あり' : 'なし'}`);
    console.log(`useEffect使用: ${customerDisplayCheck.useEffectCheck ? 'あり' : 'なし'}`);
    console.log(`条件付きレンダリング: ${customerDisplayCheck.conditionalRenderingCheck ? 'あり' : 'なし'}`);
  }
  
  // BookingsNewPageコンポーネントをチェック
  console.log('\n=== BookingsNewPageコンポーネントのチェック ===');
  const bookingsNewCheck = checkBookingsNewPage();
  
  if (bookingsNewCheck) {
    console.log(`textAlignプロップ使用: ${bookingsNewCheck.textAlignCheck ? 'あり' : 'なし'}`);
    console.log('問題の可能性がある行付近のコード:');
    console.log('```');
    console.log(bookingsNewCheck.codeSnippet);
    console.log('```');
  }
  
  // ShopifyCustomerSearchコンポーネントをチェック
  console.log('\n=== ShopifyCustomerSearchコンポーネントのチェック ===');
  const customerSearchCheck = checkShopifyCustomerSearch();
  
  if (customerSearchCheck) {
    console.log(`externalプロップ使用: ${customerSearchCheck.externalCheck ? 'あり' : 'なし'}`);
    console.log('問題の可能性がある行付近のコード:');
    console.log('```');
    console.log(customerSearchCheck.codeSnippet);
    console.log('```');
  }
  
  // 修正案の提示
  console.log('\n=== 修正案 ===');
  console.log('1. ShopifyCustomerDisplayコンポーネント:');
  console.log('   - useEffectフックを使用して、クライアントサイドでのみデータを取得するようにする');
  console.log('   - 初期状態を明示的に設定し、サーバーとクライアントで同じ初期レンダリングになるようにする');
  
  console.log('\n2. BookingsNewPageコンポーネント:');
  console.log('   - textAlignプロップをstyleオブジェクトに移動する');
  console.log('   - 例: textAlign={center} → style={{ textAlign: "center" }}');
  
  console.log('\n3. ShopifyCustomerSearchコンポーネント:');
  console.log('   - externalプロップをブール値から文字列に変更する');
  console.log('   - 例: external={true} → external="true"');
  
  console.log('\n修正を適用した後、アプリケーションを再起動して問題が解決したか確認してください。');
}

// スクリプトを実行
main();
