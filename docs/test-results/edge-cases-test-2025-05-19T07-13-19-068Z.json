{"timestamp": "2025-05-19T07:13:19.068Z", "duration": 25.077, "totalTests": 10, "successCount": 7, "failureCount": 3, "tests": [{"testName": "年末年始の予約テスト", "success": true, "timestamp": "2025-05-19T07:13:24.253Z", "message": "年末年始の予約が正常に作成されました", "error": "", "duration": 4467, "details": {"bookingId": "954763e0-bf5b-459b-ae36-a2baf07e9dea", "period": "年末年始", "startDate": "2025-12-29", "endDate": "2026-01-03"}}, {"testName": "予約期間の最小値テスト", "success": true, "timestamp": "2025-05-19T07:13:25.230Z", "message": "最小予約期間（1日）の予約が正常に作成されました", "error": "", "duration": 977, "details": {"bookingId": "c5a33d37-620b-4896-9af7-660b8e98aef7", "durationDays": 1}}, {"testName": "予約期間の最大値テスト", "success": true, "timestamp": "2025-05-19T07:13:39.416Z", "message": "最大予約期間（30日）の予約が正常に作成されました", "error": "", "duration": 14186, "details": {"bookingId": "fb17c07e-fd58-4393-be45-55693dfeba19", "durationDays": 30}}, {"testName": "過去の日付での予約テスト", "success": true, "timestamp": "2025-05-19T07:13:42.767Z", "message": "過去の日付での予約は正しくエラーになりました", "error": "過去の日付での予約がエラーにならずに成功してしまいました", "duration": 2321, "details": {"expectedError": true}}, {"testName": "終了日が開始日より前の予約テスト", "success": true, "timestamp": "2025-05-19T07:13:43.234Z", "message": "終了日が開始日より前の予約は正しくエラーになりました", "error": "終了日が開始日より前の予約がエラーにならずに成功してしまいました", "duration": 467, "details": {"expectedError": true}}, {"testName": "無効なメールアドレスでの予約テスト", "success": true, "timestamp": "2025-05-19T07:13:43.459Z", "message": "無効なメールアドレスでの予約は正しくエラーになりました", "error": "", "duration": 225, "details": {"expectedError": true, "invalidEmail": "invalid-email"}}, {"testName": "在庫をゼロにするテスト", "success": false, "timestamp": "2025-05-19T07:13:43.693Z", "message": "在庫をゼロにすることに失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-08, 2025-06-09, 2025-06-10, 2025-06-11", "duration": 233, "details": {"bookingId": ""}}, {"testName": "在庫がゼロの状態で予約を試みるテスト", "success": true, "timestamp": "2025-05-19T07:13:43.919Z", "message": "在庫がゼロの状態での予約は正しくエラーになりました", "error": "", "duration": 226, "details": {"expectedError": true}}, {"testName": "予約をキャンセルして在庫を回復するテスト", "success": false, "timestamp": "2025-05-19T07:13:43.919Z", "message": "予約をキャンセルして在庫を回復することに失敗しました", "error": "テスト1で作成した予約IDがありません", "duration": 0, "details": {"bookingId": ""}}, {"testName": "在庫が回復した状態で予約を試みるテスト", "success": false, "timestamp": "2025-05-19T07:13:44.145Z", "message": "在庫が回復した状態での予約に失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-08, 2025-06-09, 2025-06-10, 2025-06-11", "duration": 226, "details": {"bookingId": ""}}], "environment": {"nodeVersion": "v20.18.1", "os": "darwin arm64"}, "performance": {"averageTestDuration": 2332.8, "slowestTest": {"name": "予約期間の最大値テスト", "duration": 14186}, "fastestTest": {"name": "予約をキャンセルして在庫を回復するテスト", "duration": 0}}}