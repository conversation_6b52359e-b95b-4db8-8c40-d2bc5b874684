/**
 * 特殊ケーステストページ
 *
 * このページは通常とは異なるケースでの動作確認を行います。
 * メンテナンス中の商品、予約キャンセル、予約変更、エラーハンドリングなどをテストできます。
 */

import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, useSubmit, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  BlockStack,
  Text,
  Select,
  TextField,
  Button,
  Banner,
  InlineStack,
  Box,
  Divider,
  Frame,
  Modal,
  ButtonGroup,
  Tabs,
  DatePicker
} from "@shopify/polaris";
import { useState, useCallback, useEffect } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { addDays, format, parseISO } from "date-fns";
import { ja } from "date-fns/locale";

/**
 * ローダー関数
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // 商品データを取得
    const products = await prisma.product.findMany({
      where: { shop },
      select: {
        id: true,
        title: true,
        sku: true,
        status: true,
        price: true,
        shopifyId: true
      },
      orderBy: { title: 'asc' },
    });

    // 予約データを取得
    const bookings = await prisma.booking.findMany({
      where: { shop },
      select: {
        id: true,
        bookingId: true,
        productId: true,
        startDate: true,
        endDate: true,
        status: true,
        bookingType: true,
        customerEmail: true,
        customerName: true,
        product: {
          select: {
            id: true,
            title: true,
            status: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    // メンテナンス情報を取得
    const maintenances = await prisma.maintenance.findMany({
      where: { shop },
      select: {
        id: true,
        productId: true,
        startDate: true,
        endDate: true,
        status: true,
        type: true,
        notes: true,
        product: {
          select: {
            id: true,
            title: true
          }
        }
      },
      orderBy: { startDate: 'desc' },
      take: 10
    });

    // Date型を文字列に変換
    const serializedBookings = bookings.map(b => ({
      ...b,
      startDate: b.startDate.toISOString(),
      endDate: b.endDate.toISOString()
    }));

    const serializedMaintenances = maintenances.map(m => ({
      ...m,
      startDate: m.startDate.toISOString(),
      endDate: m.endDate ? m.endDate.toISOString() : null
    }));

    return json({
      products,
      bookings: serializedBookings,
      maintenances: serializedMaintenances,
      shop
    });
  } catch (error) {
    console.error("データ取得エラー:", error);
    return json({
      products: [],
      bookings: [],
      maintenances: [],
      shop: session.shop,
      error: "データの取得中にエラーが発生しました"
    });
  }
}

/**
 * アクション関数
 */
export async function action({ request }: ActionFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    const formData = await request.formData();
    const intent = formData.get("intent") as string;

    if (intent === "cancel-booking") {
      const bookingId = formData.get("bookingId") as string;
      
      if (!bookingId) {
        return json({
          success: false,
          error: "予約IDが指定されていません"
        });
      }

      // 予約をキャンセル状態に更新
      const booking = await prisma.booking.update({
        where: { id: bookingId },
        data: {
          status: 'CANCELLED',
          updatedAt: new Date()
        }
      });

      return json({
        success: true,
        message: "予約をキャンセルしました",
        booking
      });
    }

    if (intent === "update-booking-dates") {
      const bookingId = formData.get("bookingId") as string;
      const startDate = formData.get("startDate") as string;
      const endDate = formData.get("endDate") as string;
      
      if (!bookingId || !startDate || !endDate) {
        return json({
          success: false,
          error: "必須項目が入力されていません"
        });
      }

      // 予約の日程を更新
      const booking = await prisma.booking.update({
        where: { id: bookingId },
        data: {
          startDate: new Date(startDate),
          endDate: new Date(endDate),
          updatedAt: new Date()
        }
      });

      return json({
        success: true,
        message: "予約日程を更新しました",
        booking
      });
    }

    if (intent === "create-maintenance") {
      const productId = formData.get("productId") as string;
      const startDate = formData.get("startDate") as string;
      const endDate = formData.get("endDate") as string;
      const type = formData.get("type") as string;
      const notes = formData.get("notes") as string;
      
      if (!productId || !startDate || !type) {
        return json({
          success: false,
          error: "必須項目が入力されていません"
        });
      }

      // 商品情報を取得
      const product = await prisma.product.findFirst({
        where: { id: productId },
        select: { shopifyId: true }
      });

      if (!product) {
        return json({
          success: false,
          error: "商品が見つかりません"
        });
      }

      // メンテナンス情報を作成
      const maintenance = await prisma.maintenance.create({
        data: {
          shop,
          productId,
          shopifyProductId: product.shopifyId,
          startDate: new Date(startDate),
          endDate: endDate ? new Date(endDate) : null,
          type: type,
          status: 'SCHEDULED',
          notes: notes || null,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      return json({
        success: true,
        message: "メンテナンス情報を登録しました",
        maintenance
      });
    }

    if (intent === "update-product-status") {
      const productId = formData.get("productId") as string;
      const status = formData.get("status") as string;
      
      if (!productId || !status) {
        return json({
          success: false,
          error: "必須項目が入力されていません"
        });
      }

      // 商品ステータスを更新
      const product = await prisma.product.update({
        where: { id: productId },
        data: {
          status: status,
          updatedAt: new Date()
        }
      });

      return json({
        success: true,
        message: "商品ステータスを更新しました",
        product
      });
    }

    return json({
      success: false,
      error: "不明な操作が指定されました"
    });
  } catch (error) {
    console.error("アクションエラー:", error);
    return json({
      success: false,
      error: "処理中にエラーが発生しました: " + (error instanceof Error ? error.message : String(error))
    });
  }
}

export default function SpecialCases() {
  const { products, bookings, maintenances, shop, error } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();

  const [selectedTab, setSelectedTab] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(error || "");
  const [successMessage, setSuccessMessage] = useState("");

  // モーダル状態
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);

  // メンテナンス作成フォーム
  const [maintenanceForm, setMaintenanceForm] = useState({
    productId: "",
    startDate: new Date().toISOString().split('T')[0],
    endDate: addDays(new Date(), 7).toISOString().split('T')[0],
    type: "REGULAR",
    notes: ""
  });

  // 商品ステータス更新フォーム
  const [statusForm, setStatusForm] = useState({
    productId: "",
    status: "AVAILABLE"
  });

  // 予約日程更新フォーム
  const [updateForm, setUpdateForm] = useState({
    startDate: new Date().toISOString().split('T')[0],
    endDate: addDays(new Date(), 3).toISOString().split('T')[0]
  });

  // タブの設定
  const tabs = [
    {
      id: "booking-management",
      content: "予約管理",
      accessibilityLabel: "予約管理タブ",
      panelID: "booking-management-panel",
    },
    {
      id: "maintenance",
      content: "メンテナンス",
      accessibilityLabel: "メンテナンスタブ",
      panelID: "maintenance-panel",
    },
    {
      id: "product-status",
      content: "商品ステータス",
      accessibilityLabel: "商品ステータスタブ",
      panelID: "product-status-panel",
    },
  ];

  // アクションデータの処理
  useEffect(() => {
    if (actionData) {
      setIsLoading(false);
      
      if (actionData.success) {
        setSuccessMessage(actionData.message || "操作が成功しました");
        setErrorMessage("");
        
        // モーダルを閉じる
        setShowCancelModal(false);
        setShowUpdateModal(false);
        setSelectedBooking(null);
        
        // フォームをリセット
        if (actionData.maintenance) {
          setMaintenanceForm({
            productId: "",
            startDate: new Date().toISOString().split('T')[0],
            endDate: addDays(new Date(), 7).toISOString().split('T')[0],
            type: "REGULAR",
            notes: ""
          });
        }
        
        if (actionData.product) {
          setStatusForm({
            productId: "",
            status: "AVAILABLE"
          });
        }
      } else if (actionData.error) {
        setErrorMessage(actionData.error);
        setSuccessMessage("");
      }
    }
  }, [actionData]);

  // 予約キャンセル処理
  const handleCancelBooking = useCallback(() => {
    if (!selectedBooking) return;
    
    setIsLoading(true);
    setErrorMessage("");
    setSuccessMessage("");
    
    const formData = new FormData();
    formData.append("intent", "cancel-booking");
    formData.append("bookingId", selectedBooking.id);
    
    submit(formData, { method: "post" });
  }, [selectedBooking, submit]);

  // 予約日程更新処理
  const handleUpdateBookingDates = useCallback(() => {
    if (!selectedBooking) return;
    
    setIsLoading(true);
    setErrorMessage("");
    setSuccessMessage("");
    
    const formData = new FormData();
    formData.append("intent", "update-booking-dates");
    formData.append("bookingId", selectedBooking.id);
    formData.append("startDate", updateForm.startDate);
    formData.append("endDate", updateForm.endDate);
    
    submit(formData, { method: "post" });
  }, [selectedBooking, updateForm, submit]);

  // メンテナンス作成処理
  const handleCreateMaintenance = useCallback(() => {
    setIsLoading(true);
    setErrorMessage("");
    setSuccessMessage("");
    
    const formData = new FormData();
    formData.append("intent", "create-maintenance");
    formData.append("productId", maintenanceForm.productId);
    formData.append("startDate", maintenanceForm.startDate);
    formData.append("endDate", maintenanceForm.endDate);
    formData.append("type", maintenanceForm.type);
    formData.append("notes", maintenanceForm.notes);
    
    submit(formData, { method: "post" });
  }, [maintenanceForm, submit]);

  // 商品ステータス更新処理
  const handleUpdateProductStatus = useCallback(() => {
    setIsLoading(true);
    setErrorMessage("");
    setSuccessMessage("");
    
    const formData = new FormData();
    formData.append("intent", "update-product-status");
    formData.append("productId", statusForm.productId);
    formData.append("status", statusForm.status);
    
    submit(formData, { method: "post" });
  }, [statusForm, submit]);

  // 予約キャンセルモーダルを開く
  const openCancelModal = useCallback((booking) => {
    setSelectedBooking(booking);
    setShowCancelModal(true);
  }, []);

  // 予約更新モーダルを開く
  const openUpdateModal = useCallback((booking) => {
    setSelectedBooking(booking);
    setUpdateForm({
      startDate: new Date(booking.startDate).toISOString().split('T')[0],
      endDate: new Date(booking.endDate).toISOString().split('T')[0]
    });
    setShowUpdateModal(true);
  }, []);

  return (
    <Frame>
      <Page
        title="特殊ケーステスト"
        backAction={{ content: "戻る", url: "/app" }}
      >
        {errorMessage && (
          <Banner status="critical" onDismiss={() => setErrorMessage("")}>
            <p>{errorMessage}</p>
          </Banner>
        )}
        
        {successMessage && (
          <Banner status="success" onDismiss={() => setSuccessMessage("")}>
            <p>{successMessage}</p>
          </Banner>
        )}
        
        <Layout>
          <Layout.Section>
            <Tabs
              tabs={tabs}
              selected={selectedTab}
              onSelect={(index) => setSelectedTab(index)}
            />
            
            <Card>
              {selectedTab === 0 && (
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">予約管理</Text>
                  <Text>予約のキャンセルや日程変更をテストします</Text>
                  
                  {bookings.length > 0 ? (
                    bookings.map(booking => (
                      <Card key={booking.id}>
                        <BlockStack gap="200">
                          <Text variant="headingSm">予約ID: {booking.bookingId}</Text>
                          <Text>商品: {booking.product?.title || "不明な商品"}</Text>
                          <Text>期間: {new Date(booking.startDate).toLocaleDateString()} 〜 {new Date(booking.endDate).toLocaleDateString()}</Text>
                          <Text>ステータス: {booking.status}</Text>
                          <Text>顧客: {booking.customerName || "名前なし"} ({booking.customerEmail || "メールなし"})</Text>
                          
                          <InlineStack gap="200">
                            <Button
                              onClick={() => openUpdateModal(booking)}
                              disabled={booking.status === 'CANCELLED'}
                            >
                              日程変更
                            </Button>
                            
                            <Button
                              tone="critical"
                              onClick={() => openCancelModal(booking)}
                              disabled={booking.status === 'CANCELLED'}
                            >
                              キャンセル
                            </Button>
                          </InlineStack>
                        </BlockStack>
                      </Card>
                    ))
                  ) : (
                    <Banner>予約データがありません</Banner>
                  )}
                </BlockStack>
              )}
              
              {selectedTab === 1 && (
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">メンテナンス</Text>
                  
                  <Card>
                    <BlockStack gap="400">
                      <Text as="h3" variant="headingMd">新規メンテナンス登録</Text>
                      
                      <Select
                        label="商品"
                        options={[
                          { label: "商品を選択してください", value: "" },
                          ...products.map(p => ({ label: p.title, value: p.id }))
                        ]}
                        value={maintenanceForm.productId}
                        onChange={(value) => setMaintenanceForm({...maintenanceForm, productId: value})}
                      />
                      
                      <TextField
                        label="開始日"
                        type="date"
                        value={maintenanceForm.startDate}
                        onChange={(value) => setMaintenanceForm({...maintenanceForm, startDate: value})}
                      />
                      
                      <TextField
                        label="終了日"
                        type="date"
                        value={maintenanceForm.endDate}
                        onChange={(value) => setMaintenanceForm({...maintenanceForm, endDate: value})}
                      />
                      
                      <Select
                        label="メンテナンスタイプ"
                        options={[
                          { label: "定期メンテナンス", value: "REGULAR" },
                          { label: "緊急メンテナンス", value: "EMERGENCY" },
                          { label: "修理", value: "REPAIR" }
                        ]}
                        value={maintenanceForm.type}
                        onChange={(value) => setMaintenanceForm({...maintenanceForm, type: value})}
                      />
                      
                      <TextField
                        label="メモ"
                        multiline={3}
                        value={maintenanceForm.notes}
                        onChange={(value) => setMaintenanceForm({...maintenanceForm, notes: value})}
                        autoComplete="off"
                      />
                      
                      <Button
                        primary
                        loading={isLoading}
                        onClick={handleCreateMaintenance}
                      >
                        メンテナンス登録
                      </Button>
                    </BlockStack>
                  </Card>
                  
                  <Divider />
                  
                  <Text variant="headingMd">メンテナンス一覧</Text>
                  
                  {maintenances.length > 0 ? (
                    maintenances.map(maintenance => (
                      <Card key={maintenance.id}>
                        <BlockStack gap="200">
                          <Text variant="headingSm">商品: {maintenance.product?.title || "不明な商品"}</Text>
                          <Text>期間: {new Date(maintenance.startDate).toLocaleDateString()} 〜 {maintenance.endDate ? new Date(maintenance.endDate).toLocaleDateString() : "未定"}</Text>
                          <Text>タイプ: {maintenance.type}</Text>
                          <Text>ステータス: {maintenance.status}</Text>
                          {maintenance.notes && <Text>メモ: {maintenance.notes}</Text>}
                        </BlockStack>
                      </Card>
                    ))
                  ) : (
                    <Banner>メンテナンスデータがありません</Banner>
                  )}
                </BlockStack>
              )}
              
              {selectedTab === 2 && (
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">商品ステータス</Text>
                  <Text>商品のステータスを変更してテストします</Text>
                  
                  <Card>
                    <BlockStack gap="400">
                      <Select
                        label="商品"
                        options={[
                          { label: "商品を選択してください", value: "" },
                          ...products.map(p => ({ label: `${p.title} (${p.status})`, value: p.id }))
                        ]}
                        value={statusForm.productId}
                        onChange={(value) => setStatusForm({...statusForm, productId: value})}
                      />
                      
                      <Select
                        label="ステータス"
                        options={[
                          { label: "利用可能", value: "AVAILABLE" },
                          { label: "メンテナンス中", value: "MAINTENANCE" },
                          { label: "破損", value: "DAMAGED" },
                          { label: "利用不可", value: "UNAVAILABLE" }
                        ]}
                        value={statusForm.status}
                        onChange={(value) => setStatusForm({...statusForm, status: value})}
                      />
                      
                      <Button
                        primary
                        loading={isLoading}
                        onClick={handleUpdateProductStatus}
                      >
                        ステータス更新
                      </Button>
                    </BlockStack>
                  </Card>
                  
                  <Divider />
                  
                  <Text variant="headingMd">商品一覧</Text>
                  
                  {products.length > 0 ? (
                    products.map(product => (
                      <Card key={product.id}>
                        <BlockStack gap="200">
                          <Text variant="headingSm">{product.title}</Text>
                          <Text>SKU: {product.sku}</Text>
                          <Text>ステータス: {product.status}</Text>
                          <Text>価格: {product.price}</Text>
                        </BlockStack>
                      </Card>
                    ))
                  ) : (
                    <Banner>商品データがありません</Banner>
                  )}
                </BlockStack>
              )}
            </Card>
          </Layout.Section>
        </Layout>
        
        {/* 予約キャンセルモーダル */}
        <Modal
          open={showCancelModal}
          onClose={() => setShowCancelModal(false)}
          title="予約をキャンセル"
          primaryAction={{
            content: "キャンセルする",
            destructive: true,
            loading: isLoading,
            onAction: handleCancelBooking
          }}
          secondaryActions={[
            {
              content: "閉じる",
              onAction: () => setShowCancelModal(false)
            }
          ]}
        >
          <Modal.Section>
            <BlockStack gap="400">
              <Text>以下の予約をキャンセルしますか？</Text>
              
              {selectedBooking && (
                <BlockStack gap="200">
                  <Text variant="headingSm">予約ID: {selectedBooking.bookingId}</Text>
                  <Text>商品: {selectedBooking.product?.title || "不明な商品"}</Text>
                  <Text>期間: {new Date(selectedBooking.startDate).toLocaleDateString()} 〜 {new Date(selectedBooking.endDate).toLocaleDateString()}</Text>
                  <Text>顧客: {selectedBooking.customerName || "名前なし"} ({selectedBooking.customerEmail || "メールなし"})</Text>
                </BlockStack>
              )}
              
              <Banner tone="warning">
                この操作は元に戻せません。
              </Banner>
            </BlockStack>
          </Modal.Section>
        </Modal>
        
        {/* 予約日程更新モーダル */}
        <Modal
          open={showUpdateModal}
          onClose={() => setShowUpdateModal(false)}
          title="予約日程を変更"
          primaryAction={{
            content: "更新する",
            loading: isLoading,
            onAction: handleUpdateBookingDates
          }}
          secondaryActions={[
            {
              content: "キャンセル",
              onAction: () => setShowUpdateModal(false)
            }
          ]}
        >
          <Modal.Section>
            <BlockStack gap="400">
              {selectedBooking && (
                <BlockStack gap="200">
                  <Text variant="headingSm">予約ID: {selectedBooking.bookingId}</Text>
                  <Text>商品: {selectedBooking.product?.title || "不明な商品"}</Text>
                  <Text>現在の期間: {new Date(selectedBooking.startDate).toLocaleDateString()} 〜 {new Date(selectedBooking.endDate).toLocaleDateString()}</Text>
                  
                  <TextField
                    label="新しい開始日"
                    type="date"
                    value={updateForm.startDate}
                    onChange={(value) => setUpdateForm({...updateForm, startDate: value})}
                  />
                  
                  <TextField
                    label="新しい終了日"
                    type="date"
                    value={updateForm.endDate}
                    onChange={(value) => setUpdateForm({...updateForm, endDate: value})}
                  />
                </BlockStack>
              )}
            </BlockStack>
          </Modal.Section>
        </Modal>
      </Page>
    </Frame>
  );
}
