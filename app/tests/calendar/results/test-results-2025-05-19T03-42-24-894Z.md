# テスト結果レポート

## テスト概要

このレポートは、テスト実行の結果をまとめたものです。

テスト実行日時: 2025/5/19 12:42:24
実行時間: 5.47秒

## テスト結果

- 合計テスト数: 4
- 成功: 3
- 失敗: 1
- 成功率: 75%

## 詳細結果

### テストケース1: 商品情報取得エラーのユニットテスト

- 結果: ❌ 失敗

- エラー: テスト実行中にエラーが発生しました
- 詳細:
```json
{
  "duration": 3.131,
  "output": "\n RUN  v1.6.1 /Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp\n\n ❯ app/tests/calendar/product-fetch-error.test.ts  (0 test)\n\n Test Files  1 failed (1)\n      Tests  no tests\n   Start at  12:42:27\n   Duration  329ms (transform 81ms, setup 0ms, collect 0ms, tests 0ms, environment 0ms, prepare 190ms)\n\n"
}
```

### テストケース2: 商品情報取得エラーのE2Eテスト

- 結果: ✅ 成功
- メッセージ: テストが正常に完了しました (0.56秒)

- 詳細:
```json
{
  "duration": 0.556
}
```

### テストケース3: 統一カレンダーのテスト

- 結果: ✅ 成功
- メッセージ: テストが正常に完了しました (0.75秒)

- 詳細:
```json
{
  "duration": 0.751
}
```

### テストケース4: チェックアウト統合テスト

- 結果: ✅ 成功
- メッセージ: テストが正常に完了しました (1.03秒)

- 詳細:
```json
{
  "duration": 1.026
}
```

