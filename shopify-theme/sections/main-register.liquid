{% include 'breadcrumbs' %}
<div class="register pb-60">
  <div class="container">
    <h1 class="h3 mt-30 mb-40 text-center">{{ 'customer.register.title' | t }}</h1>
    {% form 'create_customer' %}
      {% if shop.locale == 'ja' %}
        <label for="LastName" class="label-required">{{ 'customer.register.last_name_title' | t }}</label>
        <input type="text"
               name="customer[last_name]"
               id="LastName"
               placeholder="{{ 'customer.register.last_name_placeholder' | t }}" 
               required="required"
               autofocus
               {% if form.last_name %}value="{{ form.last_name }}"{% endif %}>
        <label for="FirstName" class="label-required">{{ 'customer.register.first_name_title' | t }}</label>
        <input type="text"
             name="customer[first_name]"
             id="FirstName"
             placeholder="{{ 'customer.register.first_name_placeholder' | t }}"
             required="required"
             {% if form.first_name %}value="{{ form.first_name }}"{% endif %}>
      {% else %}
      <label for="FirstName" class="label-required">{{ 'customer.register.first_name_title' | t }}</label>
      <input type="text"
             name="customer[first_name]"
             id="FirstName"
             placeholder="{{ 'customer.register.first_name_placeholder' | t }}"
             required="required"
             {% if form.first_name %}value="{{ form.first_name }}"{% endif %}>
      <label for="LastName" class="label-required">{{ 'customer.register.last_name_title' | t }}</label>
      <input type="text"
             name="customer[last_name]"
             id="LastName"
             placeholder="{{ 'customer.register.last_name_placeholder' | t }}" 
             required="required"
             {% if form.last_name %}value="{{ form.last_name }}"{% endif %}>
      {% endif %}
      <label for="Email" class="label-required">{{ 'customer.register.email_title' | t }}</label>
      <input type="email"
             name="customer[email]"
             id="Email"
             class="{% if form.errors contains 'email' %}input-error{% endif %}"
             placeholder="{{ 'customer.register.email_placeholder' | t }}"
             value="{% if form.email %}{{ form.email }}{% endif %}"
             spellcheck="false"
             autocomplete="off"
             autocapitalize="off"
             required="required">
      <label for="CreatePassword" class="label-required">{{ 'customer.register.password_title' | t }}</label>
      <input type="password"
             name="customer[password]"
             id="CreatePassword"
             class="{% if form.errors contains 'password' %}input-error{% endif %}"
             placeholder="{{ 'customer.register.password_placeholder' | t }}"
             required="required">
      {% include 'form-get-message' %}
      <div class="text-center">
        <input type="submit" value="{{ 'customer.register.submit' | t }}" class="btn btn--full btn--secondary">
        <a href="{{ shop.url }}" class="h6 btn-link mt-20 mb-0">{{ 'customer.register.cancel' | t }}</a>
      </div>
    {% endform %}
  </div>
</div>


{% schema %}
{
    "name": "t:sections.register.name",
    "settings": []
}
{% endschema %}