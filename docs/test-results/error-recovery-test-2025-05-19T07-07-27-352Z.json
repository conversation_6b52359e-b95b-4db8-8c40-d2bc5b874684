{"timestamp": "2025-05-19T07:07:24.844Z", "duration": 2.508, "totalTests": 3, "successCount": 3, "failureCount": 0, "tests": [{"testName": "API障害からの回復テスト", "success": true, "timestamp": "2025-05-19T07:07:25.134Z", "message": "API障害から正常に回復しました", "error": "", "duration": 290}, {"testName": "ネットワーク切断からの回復テスト", "success": true, "timestamp": "2025-05-19T07:07:25.149Z", "message": "ネットワーク切断から正常に回復しました", "error": "", "duration": 15}, {"testName": "データ不整合の自動修復テスト", "success": true, "timestamp": "2025-05-19T07:07:27.351Z", "message": "データ不整合を正常に修復しました", "error": "", "duration": 2202}], "environment": {"nodeVersion": "v20.18.1", "os": "darwin arm64"}, "performance": {"averageTestDuration": 835.6666666666666, "slowestTest": {"name": "データ不整合の自動修復テスト", "duration": 2202}, "fastestTest": {"name": "ネットワーク切断からの回復テスト", "duration": 15}}}