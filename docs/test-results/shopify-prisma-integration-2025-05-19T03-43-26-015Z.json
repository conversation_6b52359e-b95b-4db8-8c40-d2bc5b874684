{"timestamp": "2025-05-19T03:43:26.015Z", "duration": 0, "totalTests": 7, "successCount": 4, "failureCount": 3, "testMode": "basic", "environment": {"shop": "peaces-test-block.myshopify.com", "productId": "8597856903456", "customer": {"email": "<EMAIL>", "name": "テスト顧客"}, "bookingDays": 3, "args": ["--local"], "nodeVersion": "v20.18.1", "os": "darwin arm64", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tests": [{"testName": "商品同期", "success": true, "timestamp": "2025-05-19T03:43:26.015Z", "message": "商品データが正常に同期されました"}, {"testName": "在庫カレンダー更新", "success": true, "timestamp": "2025-05-19T03:43:26.015Z", "message": "在庫カレンダーが正常に更新されました"}, {"testName": "予約作成", "success": true, "timestamp": "2025-05-19T03:43:26.015Z", "message": "予約が正常に作成されました"}, {"testName": "注文連携", "success": true, "timestamp": "2025-05-19T03:43:26.015Z", "message": "注文が予約と正常に関連付けられました"}, {"testName": "商品バリエーション", "success": false, "timestamp": "2025-05-19T03:43:26.015Z", "message": "バリエーション商品のテストに失敗しました"}, {"testName": "エラーケース", "success": false, "timestamp": "2025-05-19T03:43:26.015Z", "message": "エラーケースのテストに失敗しました"}, {"testName": "テストデータのクリーンアップ", "success": false, "timestamp": "2025-05-19T03:43:26.015Z", "message": "テストデータのクリーンアップに失敗しました"}]}