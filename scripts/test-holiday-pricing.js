/**
 * 祝日を挟んだ予約の料金計算テストスクリプト
 *
 * このスクリプトは以下の機能をテストします：
 * 1. 祝日を含む期間の営業日数計算
 * 2. 祝日を含む期間の料金計算
 * 3. 連続する祝日を含む期間の料金計算
 * 4. 祝日と日曜日が重なる期間の料金計算
 */

// ESモジュール形式で実行

// 日本の祝日（2025年）
const JAPANESE_HOLIDAYS_2025 = [
  '2025-01-01', // 元日
  '2025-01-13', // 成人の日
  '2025-02-11', // 建国記念の日
  '2025-02-23', // 天皇誕生日
  '2025-02-24', // 振替休日
  '2025-03-20', // 春分の日
  '2025-04-29', // 昭和の日
  '2025-05-03', // 憲法記念日
  '2025-05-04', // みどりの日
  '2025-05-05', // こどもの日
  '2025-05-06', // 振替休日
  '2025-07-21', // 海の日
  '2025-08-11', // 山の日
  '2025-09-15', // 敬老の日
  '2025-09-23', // 秋分の日
  '2025-10-13', // スポーツの日
  '2025-11-03', // 文化の日
  '2025-11-23', // 勤労感謝の日
  '2025-11-24', // 振替休日
  '2025-12-23', // 天皇誕生日
];

// テストケース
const testCases = [
  // 1. 単一の祝日を含む期間
  {
    name: '単一の祝日を含む期間',
    startDate: '2025-11-01', // 土曜日
    endDate: '2025-11-05',   // 水曜日
    expectedDays: 3,         // 11/1, 11/4, 11/5 (日曜日と文化の日を除く)
    basePrice: 1000,
    expectedPrice: 1000 + (1000 * 0.2 * 2) // 1日目: 1000円, 2-3日目: 各200円
  },

  // 2. 連続する祝日を含む期間（ゴールデンウィーク）
  {
    name: '連続する祝日を含む期間（ゴールデンウィーク）',
    startDate: '2025-04-28', // 月曜日
    endDate: '2025-05-07',   // 水曜日
    expectedDays: 5,         // 4/28, 4/30, 5/1, 5/2, 5/7 (日曜日と祝日を除く)
    basePrice: 1000,
    expectedPrice: 1000 + (1000 * 0.2 * 4) // 1日目: 1000円, 2-5日目: 各200円
  },

  // 3. 年末年始を含む期間
  {
    name: '年末年始を含む期間',
    startDate: '2024-12-27', // 金曜日
    endDate: '2025-01-06',   // 月曜日
    expectedDays: 4,         // 12/27, 12/28, 1/4, 1/6 (日曜日と年末年始を除く)
    basePrice: 1000,
    expectedPrice: 1000 + (1000 * 0.2 * 3) // 1日目: 1000円, 2-4日目: 各200円
  },

  // 4. 祝日と日曜日が重なる期間
  {
    name: '祝日と日曜日が重なる期間',
    startDate: '2025-11-21', // 金曜日
    endDate: '2025-11-25',   // 火曜日
    expectedDays: 3,         // 11/21, 11/22, 11/25 (日曜日と勤労感謝の日と振替休日を除く)
    basePrice: 1000,
    expectedPrice: 1000 + (1000 * 0.2 * 2) // 1日目: 1000円, 2-3日目: 各200円
  },

  // 5. 祝日が多い月をまたぐ期間
  {
    name: '祝日が多い月をまたぐ期間',
    startDate: '2025-04-25', // 金曜日
    endDate: '2025-05-10',   // 土曜日
    expectedDays: 10,        // 4/25, 4/28, 4/30, 5/1, 5/2, 5/7, 5/8, 5/9, 5/10 (日曜日と祝日を除く)
    basePrice: 1000,
    expectedPrice: 1000 + (1000 * 0.2 * 5) + (1000 * 0.1 * 4) // 1日目: 1000円, 2-6日目: 各200円, 7-10日目: 各100円
  },

  // 6. 短期間（祝日なし）
  {
    name: '短期間（祝日なし）',
    startDate: '2025-06-02', // 月曜日
    endDate: '2025-06-04',   // 水曜日
    expectedDays: 3,         // 6/2, 6/3, 6/4
    basePrice: 1000,
    expectedPrice: 1000 + (1000 * 0.2 * 2) // 1日目: 1000円, 2-3日目: 各200円
  },

  // 7. 短期間（祝日あり）
  {
    name: '短期間（祝日あり）',
    startDate: '2025-07-19', // 土曜日
    endDate: '2025-07-22',   // 火曜日
    expectedDays: 2,         // 7/19, 7/22 (日曜日と海の日を除く)
    basePrice: 1000,
    expectedPrice: 1000 + (1000 * 0.2) // 1日目: 1000円, 2日目: 200円
  },

  // 8. 長期間（祝日複数）
  {
    name: '長期間（祝日複数）',
    startDate: '2025-09-10', // 水曜日
    endDate: '2025-09-25',   // 木曜日
    expectedDays: 12,        // 9/10-9/14, 9/16-9/22, 9/24-9/25 (日曜日と敬老の日と秋分の日を除く)
    basePrice: 1000,
    expectedPrice: 1000 + (1000 * 0.2 * 5) + (1000 * 0.1 * 6) // 1日目: 1000円, 2-6日目: 各200円, 7-12日目: 各100円
  }
];

// 日付が日曜日かどうかを判定
function isSunday(date) {
  return new Date(date).getDay() === 0;
}

// 日付が祝日かどうかを判定
function isJapaneseHoliday(date) {
  const dateStr = new Date(date).toISOString().split('T')[0];
  return JAPANESE_HOLIDAYS_2025.includes(dateStr);
}

// 日付が年末年始かどうかを判定
function isNewYearHoliday(date) {
  const d = new Date(date);
  const month = d.getMonth() + 1; // 0-indexed to 1-indexed
  const day = d.getDate();

  // 12月29日〜12月31日
  if (month === 12 && day >= 29 && day <= 31) {
    return true;
  }

  // 1月1日〜1月3日
  if (month === 1 && day >= 1 && day <= 3) {
    return true;
  }

  return false;
}

// 休業日かどうかを判定（日曜日・祝日・年末年始）
function isClosedDay(date) {
  return isSunday(date) || isJapaneseHoliday(date) || isNewYearHoliday(date);
}

// 営業日数を計算
function calculateBusinessDays(startDate, endDate) {
  let businessDays = 0;
  const start = new Date(startDate);
  const end = new Date(endDate);
  const current = new Date(start);

  while (current <= end) {
    // 日曜日・祝日・年末年始以外の日をカウント
    if (!isClosedDay(current)) {
      businessDays++;
    }
    current.setDate(current.getDate() + 1);
  }

  return businessDays;
}

// 料金計算
function calculateRentalPrice(startDate, endDate, basePrice) {
  // 営業日数を計算
  const rentalDays = calculateBusinessDays(startDate, endDate);

  // 総日数（開始日から終了日までの日数）
  const totalDays = Math.floor((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24)) + 1;

  let totalPrice = 0;

  if (rentalDays >= 1) {
    // 1日目は基本料金
    totalPrice += basePrice;

    if (rentalDays > 1) {
      // 2-6日目は基本料金の20%
      const day2to6 = Math.min(5, rentalDays - 1);
      totalPrice += basePrice * 0.2 * day2to6;

      if (rentalDays > 6) {
        // 7日目以降は基本料金の10%
        const day7plus = rentalDays - 6;
        totalPrice += basePrice * 0.1 * day7plus;
      }
    }
  }

  // 整数に丸める
  return {
    totalDays,
    rentalDays,
    totalPrice: Math.round(totalPrice)
  };
}

// 日付の範囲内の休業日を表示
function getClosedDaysInRange(startDate, endDate) {
  const closedDays = [];
  const start = new Date(startDate);
  const end = new Date(endDate);
  const current = new Date(start);

  while (current <= end) {
    if (isClosedDay(current)) {
      let reason = [];
      if (isSunday(current)) reason.push('日曜日');
      if (isJapaneseHoliday(current)) reason.push('祝日');
      if (isNewYearHoliday(current)) reason.push('年末年始');

      closedDays.push({
        date: new Date(current).toISOString().split('T')[0],
        reason: reason.join('・')
      });
    }
    current.setDate(current.getDate() + 1);
  }

  return closedDays;
}

// テスト実行関数
function runTests() {
  console.log('===== 祝日を挟んだ予約の料金計算テスト開始 =====');

  let passedTests = 0;

  for (const testCase of testCases) {
    console.log(`\n----- ${testCase.name} -----`);
    console.log(`期間: ${testCase.startDate} 〜 ${testCase.endDate}`);

    // 休業日の表示
    const closedDays = getClosedDaysInRange(testCase.startDate, testCase.endDate);
    console.log(`休業日: ${closedDays.length}日`);
    closedDays.forEach(day => {
      console.log(`  ${day.date} (${day.reason})`);
    });

    // 料金計算
    const result = calculateRentalPrice(testCase.startDate, testCase.endDate, testCase.basePrice);
    console.log(`営業日数: ${result.rentalDays}日 (総日数: ${result.totalDays}日)`);
    console.log(`料金: ¥${result.totalPrice}`);

    // テスト結果の検証
    const daysMatch = result.rentalDays === testCase.expectedDays;
    const priceMatch = result.totalPrice === testCase.expectedPrice;
    const passed = daysMatch && priceMatch;

    console.log(`テスト結果: ${passed ? '成功' : '失敗'}`);

    if (!daysMatch) {
      console.log(`  営業日数: 期待値=${testCase.expectedDays}日, 実際値=${result.rentalDays}日`);
    }

    if (!priceMatch) {
      console.log(`  料金: 期待値=¥${testCase.expectedPrice}, 実際値=¥${result.totalPrice}`);
    }

    if (passed) {
      passedTests++;
    }
  }

  // 総合結果
  console.log('\n===== テスト結果 =====');
  console.log(`合計: ${testCases.length}件`);
  console.log(`成功: ${passedTests}件`);
  console.log(`失敗: ${testCases.length - passedTests}件`);

  return passedTests === testCases.length;
}

// テスト実行
const success = runTests();
if (success) {
  console.log('\n✅ 全てのテストが成功しました！');
} else {
  console.log('\n❌ 一部のテストが失敗しました。');
}
