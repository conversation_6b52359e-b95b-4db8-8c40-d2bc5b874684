/**
 * 高橋顧客用テスト予約作成スクリプト
 *
 * このスクリプトは、高橋健太顧客用のテスト予約を作成します。
 *
 * 使用方法:
 * npx tsx scripts/create-test-booking-for-takahashi.ts [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';
import { addDays, format } from 'date-fns';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// 高橋顧客のID
const TAKAHASHI_CUSTOMER_ID = 'gid://shopify/Customer/8420600250536';
const TAKAHASHI_CUSTOMER_NAME = '高橋 健太';
const TAKAHASHI_CUSTOMER_EMAIL = '<EMAIL>';

/**
 * テスト予約を作成する関数
 * @param productId 商品ID
 * @returns 作成された予約
 */
async function createTestBooking(productId: string) {
  try {
    console.log(`商品ID ${productId} のテスト予約を作成します...`);

    // 商品情報を取得
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!product) {
      throw new Error(`商品ID ${productId} が見つかりません`);
    }

    console.log(`商品情報: ${product.title} (${product.sku})`);

    // 予約期間を設定（今日から3日間）
    const startDate = new Date();
    const endDate = addDays(startDate, 3);

    console.log(`予約期間: ${format(startDate, 'yyyy-MM-dd')} 〜 ${format(endDate, 'yyyy-MM-dd')}`);

    // 予約データを作成
    const booking = await prisma.booking.create({
      data: {
        id: uuidv4(),
        bookingId: `TEST-TAKAHASHI-${uuidv4().substring(0, 8).toUpperCase()}`,
        shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
        productId: product.id,
        startDate,
        endDate,
        customerName: TAKAHASHI_CUSTOMER_NAME,
        customerEmail: TAKAHASHI_CUSTOMER_EMAIL,
        customerId: TAKAHASHI_CUSTOMER_ID,
        bookingType: 'PROVISIONAL',
        status: 'PROVISIONAL',
        paymentStatus: 'PENDING',
        totalAmount: product.price?.toString() || '10000',
        depositAmount: (Number(product.price || 10000) * 0.1).toString(),
        priority: 1,
        notes: 'テスト予約（高橋健太様用）',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('予約を作成しました:');
    console.log(`予約ID: ${booking.id}`);
    console.log(`予約番号: ${booking.bookingId}`);
    console.log(`顧客名: ${booking.customerName}`);
    console.log(`顧客ID: ${booking.customerId}`);
    console.log(`期間: ${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')}`);

    return booking;
  } catch (error) {
    console.error('予約作成中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    // コマンドライン引数から商品IDを取得
    const productId = process.argv[2];

    if (!productId) {
      // 商品IDが指定されていない場合は、最初の商品を使用
      const firstProduct = await prisma.product.findFirst({
        where: {
          status: 'AVAILABLE'
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      if (!firstProduct) {
        throw new Error('商品が見つかりません。商品IDを指定するか、商品を作成してください。');
      }

      console.log(`商品IDが指定されていないため、最初の商品 ${firstProduct.title} (ID: ${firstProduct.id}) を使用します。`);
      await createTestBooking(firstProduct.id);
    } else {
      await createTestBooking(productId);
    }

    console.log('テスト予約の作成が完了しました');
  } catch (error) {
    console.error('エラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
