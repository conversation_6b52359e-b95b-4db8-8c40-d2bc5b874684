# Claude CodeでShopify開発支援を活用する方法

**作成日**: 2025年5月25日  
**目的**: <PERSON> CodeでShopify Dev MCPの機能を代替する方法

## 現状について

Claude Codeは現在、カスタムMCPサーバーの追加をサポートしていません。
しかし、Shopify Dev MCP Serverが提供する機能は、以下の方法で代替できます。

## 代替方法

### 1. Shopify GraphQL Admin APIスキーマの活用

Admin APIのスキーマファイルをローカルに保存して参照：

```bash
# スキーマをダウンロード
cd /Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp
node scripts/download-schema.ts
```

スキーマファイルは `data/admin_schema_2025-01.json.gz` に保存されています。

### 2. GraphQLクエリの作成支援

以下のようなヘルパースクリプトを作成：

```javascript
// scripts/graphql-helper.js
const fs = require('fs');
const zlib = require('zlib');

// スキーマを読み込む
const schemaBuffer = fs.readFileSync('./data/admin_schema_2025-01.json.gz');
const schemaString = zlib.gunzipSync(schemaBuffer).toString();
const schema = JSON.parse(schemaString);

// 型情報を検索
function findType(typeName) {
  return schema.data.__schema.types.find(t => t.name === typeName);
}

// 使用例
const productType = findType('Product');
console.log(JSON.stringify(productType, null, 2));
```

### 3. Shopifyドキュメントの検索

Webブラウザで直接アクセス：
- [Shopify Dev Docs](https://shopify.dev)
- [GraphQL Admin API Reference](https://shopify.dev/docs/api/admin-graphql)

または、以下のコマンドでドキュメントを開く：

```bash
open "https://shopify.dev/docs/api/admin-graphql/latest/queries/product"
```

## 推奨ワークフロー

### 1. GraphQLクエリ作成時

```bash
# 1. スキーマを確認
cat data/admin_schema_2025-01.json.gz | gunzip | jq '.data.__schema.types[] | select(.name == "Product")' | less

# 2. 既存のクエリを参考にする
grep -r "query.*Product" app/services/ scripts/

# 3. GraphQL Playgroundでテスト
# Shopify管理画面 > Apps > Manage private apps > GraphQL Playground
```

### 2. メタフィールド操作時

```bash
# 既存のメタフィールドツールを活用
node scripts/check-product-metafields-usage.ts
node scripts/create-complete-product-from-data.ts
```

### 3. API操作の実装時

プロジェクト内の既存実装を参考に：
- `app/shopify.server.ts` - GraphQLクライアントの実装
- `app/services/*.service.ts` - 各種API操作の実装例
- `scripts/` - スタンドアロンのAPI操作例

## よく使うコマンド集

```bash
# 商品情報の取得
node scripts/get-product-info.js

# メタフィールドの確認
node scripts/check-product-metafields.js [productId]

# GraphQLクエリのテスト
node scripts/run-graphql-query.js

# 商品の検索
node scripts/find-product.js [検索キーワード]
```

## まとめ

Claude Codeでは直接MCPサーバーを使用できませんが、以下の方法で効率的に開発できます：

1. **ローカルスキーマファイルの活用** - 型情報の確認
2. **既存スクリプトの活用** - 各種操作の自動化
3. **プロジェクト内の実装例** - コードパターンの参照

これらのツールとリソースを組み合わせることで、Shopify開発を効率的に行えます。

---

本ドキュメントは代替手段として作成されました。
最終更新: 2025年5月25日