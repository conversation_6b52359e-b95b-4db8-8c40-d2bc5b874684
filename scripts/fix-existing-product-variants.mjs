import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2025-01';

// GraphQLクライアントの設定
const client = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品情報
const PRODUCT_ID = "gid://shopify/Product/8982388146344";
const BASE_SKU = "10301009-001";
const PRODUCT_DATA = {
  productCode: "10301009",
  detailCode: "001",
  categoryName: "椅子",
  kana: "ハーヴェイチェア",
  basePrice: 700,
  extraDayRate: 350,
  quantity: 1,
  width: 450,
  depth: 580,
  height: 940,
  purchasePrice: 30000
};

// レンタル期間のバリアント定義
const RENTAL_VARIANTS = [
  { title: "1日レンタル", suffix: "1D", days: 1, price: 700 },
  { title: "2日レンタル", suffix: "2D", days: 2, price: 1050 },
  { title: "3日レンタル", suffix: "3D", days: 3, price: 1400 },
  { title: "4日レンタル", suffix: "4D", days: 4, price: 1750 },
  { title: "5日レンタル", suffix: "5D", days: 5, price: 2100 },
  { title: "6日レンタル", suffix: "6D", days: 6, price: 2450 },
  { title: "7日レンタル", suffix: "7D", days: 7, price: 2800 },
  { title: "8日以上レンタル", suffix: "8D+", days: 8, price: 2800 },
  { title: "仮予約", suffix: "PROV", days: 0, price: 70 }
];

// ロケーションを取得
async function getLocations() {
  const query = gql`
    query GetLocations {
      locations(first: 10) {
        edges {
          node {
            id
            name
            isActive
          }
        }
      }
    }
  `;

  const data = await client.request(query);
  return data.locations.edges.map(edge => edge.node).filter(loc => loc.isActive);
}

async function fixProduct() {
  try {
    console.log('=== 既存商品の修正開始 ===');
    console.log(`商品ID: ${PRODUCT_ID}\n`);

    // 1. ロケーションを取得
    console.log('1. ロケーション情報を取得中...');
    const locations = await getLocations();
    const nyLocation = locations.find(loc => loc.name === 'NY');
    
    if (!nyLocation) {
      throw new Error('NYロケーションが見つかりません');
    }
    console.log(`✓ NYロケーション: ${nyLocation.id}`);

    // 2. 既存のデフォルトバリアントを削除
    console.log('\n2. 既存のバリアントを確認中...');
    const getVariantsQuery = gql`
      query getVariants($id: ID!) {
        product(id: $id) {
          variants(first: 100) {
            edges {
              node {
                id
                title
                sku
              }
            }
          }
        }
      }
    `;

    const variantsResult = await client.request(getVariantsQuery, { id: PRODUCT_ID });
    const existingVariants = variantsResult.product.variants.edges;

    // デフォルトバリアントを削除
    for (const edge of existingVariants) {
      if (edge.node.title === 'Default Title' || !edge.node.sku) {
        console.log(`デフォルトバリアント削除中: ${edge.node.id}`);
        const deleteVariantMutation = gql`
          mutation deleteVariant($id: ID!) {
            productVariantDelete(id: $id) {
              deletedProductVariantId
              userErrors {
                field
                message
              }
            }
          }
        `;
        
        const deleteResult = await client.request(deleteVariantMutation, { id: edge.node.id });
        if (deleteResult.productVariantDelete.userErrors.length > 0) {
          console.error('バリアント削除エラー:', deleteResult.productVariantDelete.userErrors);
        } else {
          console.log('✓ デフォルトバリアント削除完了');
        }
      }
    }

    // 3. バリアントを一括作成
    console.log('\n3. レンタル期間別バリアントを作成中...');
    const createVariantsMutation = gql`
      mutation createVariants($productId: ID!, $variants: [ProductVariantsBulkInput!]!) {
        productVariantsBulkCreate(productId: $productId, variants: $variants) {
          productVariants {
            id
            title
            price
            inventoryItem {
              id
              sku
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const variantsInput = RENTAL_VARIANTS.map(variant => ({
      optionValues: [{ optionName: "レンタル期間", name: variant.title }],
      price: variant.price.toString(),
      inventoryItem: {
        sku: `${BASE_SKU}-${variant.suffix}`,
        tracked: true
      },
      inventoryPolicy: "DENY",
      inventoryQuantities: [{
        availableQuantity: PRODUCT_DATA.quantity,
        locationId: nyLocation.id
      }]
    }));

    const createResult = await client.request(createVariantsMutation, {
      productId: PRODUCT_ID,
      variants: variantsInput
    });

    if (createResult.productVariantsBulkCreate.userErrors.length > 0) {
      console.error('バリアント作成エラー:', createResult.productVariantsBulkCreate.userErrors);
      return;
    }

    console.log('✓ バリアント作成完了');
    const variantIds = {};
    createResult.productVariantsBulkCreate.productVariants.forEach(v => {
      console.log(`  - ${v.title}: ¥${v.price} (SKU: ${v.inventoryItem.sku})`);
      const suffix = v.inventoryItem.sku.split('-').pop();
      variantIds[suffix] = v.id;
    });

    // 4. メタフィールドを設定
    console.log('\n4. メタフィールドを設定中...');
    const metafieldsSetMutation = gql`
      mutation setMetafields($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          metafields {
            id
            namespace
            key
            value
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const metafields = [
      {
        ownerId: PRODUCT_ID,
        namespace: "rental",
        key: "basic_info",
        value: JSON.stringify({
          product_code: PRODUCT_DATA.productCode,
          detail_code: PRODUCT_DATA.detailCode,
          category_name: PRODUCT_DATA.categoryName,
          kana: PRODUCT_DATA.kana
        }),
        type: "json"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "rental",
        key: "pricing",
        value: JSON.stringify({
          base_price: PRODUCT_DATA.basePrice,
          extra_day_rate: PRODUCT_DATA.extraDayRate
        }),
        type: "json"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "rental",
        key: "variant_mapping",
        value: JSON.stringify(Object.entries(variantIds).map(([suffix, id]) => ({
          variant_id: id,
          rental_period: suffix,
          days: RENTAL_VARIANTS.find(v => v.suffix === suffix)?.days || 0
        }))),
        type: "json"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "rental",
        key: "status",
        value: JSON.stringify({
          enabled: true,
          maintenance_notes: ""
        }),
        type: "json"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "width",
        value: PRODUCT_DATA.width.toString(),
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "depth",
        value: PRODUCT_DATA.depth.toString(),
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "height",
        value: PRODUCT_DATA.height.toString(),
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "purchase_price",
        value: PRODUCT_DATA.purchasePrice.toString(),
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "variation_type",
        value: "rental_period",
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "booking_type",
        value: "日単位",
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "booking_enabled",
        value: "true",
        type: "single_line_text_field"
      }
    ];

    const metafieldsResult = await client.request(metafieldsSetMutation, { metafields });
    
    if (metafieldsResult.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', metafieldsResult.metafieldsSet.userErrors);
    } else {
      console.log('✓ メタフィールド設定完了');
      console.log(`  設定されたメタフィールド数: ${metafieldsResult.metafieldsSet.metafields.length}`);
    }

    // 5. 在庫追跡を有効化
    console.log('\n5. 在庫追跡を有効化中...');
    for (const variant of createResult.productVariantsBulkCreate.productVariants) {
      const inventoryItemId = variant.inventoryItem.id;

      const enableInventoryMutation = gql`
        mutation enableInventory($inventoryItemId: ID!, $input: InventoryItemInput!) {
          inventoryItemUpdate(id: $inventoryItemId, input: $input) {
            inventoryItem {
              id
              tracked
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      await client.request(enableInventoryMutation, {
        inventoryItemId: inventoryItemId,
        input: { tracked: true }
      });
    }
    console.log('✓ 在庫追跡有効化完了');

    console.log('\n=== 商品修正完了 ===');
    console.log(`商品ID: ${PRODUCT_ID}`);
    console.log(`商品URL: https://admin.shopify.com/store/peaces-test-block/products/${PRODUCT_ID.split('/').pop()}`);
    
  } catch (error) {
    console.error('エラーが発生しました:', error);
    if (error.response && error.response.errors) {
      console.error('GraphQLエラー:', JSON.stringify(error.response.errors, null, 2));
    }
  }
}

fixProduct();