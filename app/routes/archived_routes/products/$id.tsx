import { LoaderFunction<PERSON>rgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate, Link, useParams } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import { RentalCalendar } from '../components/Booking/RentalCalendar';
import type { Booking, Product as ProductModel } from '@prisma/client';
import {
  Page,
  LegacyCard,
  Badge,
  Button,
  Text,
  ButtonGroup,
  Layout,
  DescriptionList,
  Modal,
  Banner,
  LegacyStack,
  Tabs,
  BlockStack,
  Box
} from '@shopify/polaris';
import { PrismaClient, Prisma } from '@prisma/client';
import { formatLocalDate } from '../utils/date/date-utils';
import { useState, useCallback } from 'react';

const prisma = new PrismaClient();

// ステータスに応じたバッジ設定
const statusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  available: { tone: 'success', label: '利用可能' },
  maintenance: { tone: 'warning', label: 'メンテナンス中' },
  damaged: { tone: 'critical', label: '損傷中' },
  unavailable: { tone: 'critical', label: '利用不可' }
};

// 予約ステータスに応じたバッジ設定
const bookingStatusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  DRAFT: { tone: 'attention', label: '下書き' },
  PROVISIONAL: { tone: 'warning', label: '仮予約' },
  CONFIRMED: { tone: 'success', label: '本予約' },
  COMPLETED: { tone: 'info', label: '完了' },
  CANCELLED: { tone: 'critical', label: 'キャンセル' }
};


// JSONデータを安全にパースする関数
const safeParseJson = (jsonValue: Prisma.JsonValue | null | undefined): Record<string, any> => {
  if (jsonValue && typeof jsonValue === 'object' && !Array.isArray(jsonValue)) {
    return jsonValue as Record<string, any>;
  }
  if (typeof jsonValue === 'string') {
    try {
      const parsed = JSON.parse(jsonValue);
      if (typeof parsed === 'object' && parsed !== null && !Array.isArray(parsed)) {
        return parsed;
      }
    } catch (e) {
      console.warn("Failed to parse JSON string:", jsonValue, e);
    }
  }
  return {};
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  console.log("[loader] Start loading product detail for ID:", params.id);
  // authenticate.admin を実行し、admin オブジェクトを取得
  const { admin, session } = await authenticate.admin(request);

  const { id } = params; // URLから取得するIDはShopifyの商品ID (文字列)

  if (!id) {
    throw new Response('商品IDが指定されていません', { status: 400 });
  }

  // shopifyId を使って商品を検索 (shop ドメインも条件に加える)
  const product: ProductModel | null = await prisma.product.findUnique({
    // where: { id } // 内部IDでの検索をやめる
    where: {
      shop_shopifyId: { // 複合ユニークキーを使用
        shop: session.shop, // 現在のショップドメイン
        shopifyId: id       // URLから取得したShopify商品ID
      }
    }
  });

  console.log("[loader] Product found using shopifyId:", product);

  if (!product) {
    console.error(`[loader] Product with Shopify ID ${id} for shop ${session.shop} not found in DB.`);
    // 404エラーではなく、エラーメッセージを表示するページにリダイレクトするか、
    // エラーコンポーネントを表示する方が親切かもしれない
    throw new Response(`商品 (Shopify ID: ${id}) が見つかりません`, { status: 404 });
  }
  console.log("[loader] Found product:", product.title);

  // 商品が見つかった場合のみ、その商品の内部IDを使って予約を検索
  const currentBookings = await prisma.booking.findMany({
    where: {
      productId: product.id, // データベースの内部IDを使用
      status: { in: ['DRAFT', 'PROVISIONAL', 'CONFIRMED'] },
      endDate: { gte: new Date() }
    },
    orderBy: [
      { status: 'asc' },
      { startDate: 'asc' }
    ]
  });
  console.log("[loader] Current bookings:", currentBookings.length);

  const pastBookings = await prisma.booking.findMany({
    where: {
      productId: product.id, // データベースの内部IDを使用
      OR: [
        { status: { in: ['COMPLETED', 'CANCELLED'] } },
        { endDate: { lt: new Date() } }
      ]
    },
    orderBy: { endDate: 'desc' },
    take: 10
  });
  console.log("[loader] Past bookings:", pastBookings.length);

  const basicInfo = safeParseJson(product.basicInfo);
  const maintenanceInfo = safeParseJson(product.maintenanceInfo);
  const purchaseInfo = safeParseJson(product.purchaseInfo);
  const disposalInfo = safeParseJson(product.disposalInfo);

  return json({
    product: {
      id: product.id, // DBの内部ID
      shopifyId: product.shopifyId, // Shopify ID
      title: product.title,
      sku: product.sku || '',
      price: product.price.toString(),
      productCode: basicInfo.productCode || '',
      detailCode: basicInfo.detailCode || '',
      kana: basicInfo.kana || '',
      location: basicInfo.location || '',
      status: basicInfo.status || 'available',
      createdAt: product.createdAt.toISOString(),
      updatedAt: product.updatedAt.toISOString()
    },
    metadata: {
      basicInfo,
      maintenanceInfo,
      purchaseInfo,
      disposalInfo
    },
    currentBookings: currentBookings.map((r: Booking) => ({
      id: r.id,
      status: r.status,
      startDate: r.startDate.toISOString(),
      endDate: r.endDate.toISOString(),
      customerName: r.customerName,
      customerEmail: r.customerEmail,
      priority: r.priority,
      orderId: r.orderId
    })),
    pastBookings: pastBookings.map((r: Booking) => ({
      id: r.id,
      status: r.status,
      startDate: r.startDate.toISOString(),
      endDate: r.endDate.toISOString(),
      customerName: r.customerName,
      customerEmail: r.customerEmail,
      orderId: r.orderId
    }))
  });
}

export default function ProductDetail() {
  console.log("[ProductDetail] Rendering component...");
  const { product, metadata, currentBookings, pastBookings } = useLoaderData<typeof loader>();
  const navigate = useNavigate();

  const [selectedTab, setSelectedTab] = useState(0);
  const [showMaintenanceModal, setShowMaintenanceModal] = useState(false);
  const [selectedStartDate, setSelectedStartDate] = useState<Date | null>(null);
  const [selectedEndDate, setSelectedEndDate] = useState<Date | null>(null);
  const [calculatedPrice, setCalculatedPrice] = useState<any | null>(null);

  const badge = statusBadgeMap[product.status] || { tone: 'new', label: product.status };

  const maintenanceStatus = metadata.maintenanceInfo?.maintenanceStatus;
  const lastMaintenanceDate = metadata.maintenanceInfo?.lastMaintenanceDate;
  const nextMaintenanceDate = metadata.maintenanceInfo?.nextMaintenanceDate;
  const maintenanceNotes = metadata.maintenanceInfo?.maintenanceNotes;

  const tabs = [
    { id: 'details', content: '基本情報' },
    { id: 'reservations', content: '予約情報' },
    { id: 'maintenance', content: 'メンテナンス' },
    { id: 'purchase', content: '購入/廃棄情報' }
  ];

  const getStatusBadge = (status: string) => {
    const badgeInfo = bookingStatusBadgeMap[status] || { tone: 'new', label: status };
    return <Badge tone={badgeInfo.tone}>{badgeInfo.label}</Badge>;
  };

  const handleCalendarDateChange = useCallback((startDate: Date | null, endDate: Date | null) => {
    setSelectedStartDate(startDate);
    setSelectedEndDate(endDate);
    console.log("Selected Dates:", startDate, endDate);
  }, []);

  const handleCalendarPriceChange = useCallback((priceResult: any | null) => {
    setCalculatedPrice(priceResult);
    console.log("Calculated Price:", priceResult);
  }, []);

  return (
    <Page
      title="商品詳細"
      backAction={{ content: '商品一覧', url: '/app/products' }}
      titleMetadata={<Badge tone={badge.tone}>{badge.label}</Badge>}
      // subtitle を DBのIDに変更
      subtitle={`DB ID: ${product.id} / Shopify ID: ${product.shopifyId}`}
      primaryAction={
        <ButtonGroup>
          <Button url={`https://${process.env.SHOPIFY_SHOP}/admin/products/${product.shopifyId}`} external>
            Shopifyで表示
          </Button>
        </ButtonGroup>
      }
      secondaryActions={[
        {
          content: 'メンテナンス登録',
          onAction: () => setShowMaintenanceModal(true)
        }
      ]}
    >
      <Box paddingBlockEnd="400"><Tabs tabs={tabs} selected={selectedTab} onSelect={setSelectedTab} /></Box>

      <div style={{ marginTop: '16px' }}>
        {/* 基本情報タブ */}
        {selectedTab === 0 && (
          <Layout>
            <Layout.Section>
              <LegacyCard title="商品基本情報" sectioned>
                <DescriptionList
                  items={[
                    { term: '商品名', description: product.title },
                    { term: 'SKU', description: product.sku || '未設定' },
                    { term: '商品コード', description: product.productCode || '未設定' },
                    { term: '詳細コード', description: product.detailCode || '未設定' },
                    { term: 'フリガナ', description: product.kana || '未設定' },
                    { term: '在庫場所', description: product.location || '未設定' },
                    { term: '商品ステータス', description: (<Badge tone={badge.tone}>{badge.label}</Badge>) }
                  ]}
                />
              </LegacyCard>
            </Layout.Section>

            <Layout.Section>
              <LegacyCard title="システム情報" sectioned>
                <DescriptionList
                  items={[
                    { term: 'Shopify ID', description: product.shopifyId },
                    { term: 'DB ID', description: product.id }, // DB IDも表示
                    { term: '登録日', description: formatLocalDate(product.createdAt) },
                    { term: '最終更新日', description: formatLocalDate(product.updatedAt) }
                  ]}
                />
              </LegacyCard>

              <LegacyCard title="操作" sectioned>
                <BlockStack gap="100">
                  {/* productId には DBの内部IDを渡す */}
                  <Button url={`/app/bookings/new?productId=${product.id}`}>新規予約を作成</Button>
                </BlockStack>
              </LegacyCard>
            </Layout.Section>
          </Layout>
        )}

        {/* 予約情報タブ */}
        {selectedTab === 1 && (
          <Layout>
            <Layout.Section>
              <LegacyCard title="現在の予約" sectioned>
                {currentBookings.length === 0 ? (
                  <Banner title="情報" tone="info">現在の予約はありません</Banner>
                ) : (
                  <div>
                    {currentBookings.map((booking) => (
                      <div key={booking.id} style={{ marginBottom: '16px', borderBottom: '1px solid #ddd', paddingBottom: '16px' }}>
                        <LegacyStack>
                          <LegacyStack.Item fill>
                              <Text as="span" variant="headingMd">{booking.customerName || booking.customerEmail || '顧客情報なし'}</Text>
                            <Text as="p" variant="bodySm">
                              期間: {formatLocalDate(booking.startDate)} 〜 {formatLocalDate(booking.endDate)}
                            </Text>
                            {booking.orderId && (
                              <Text as="p" variant="bodySm">
                                注文ID: {booking.orderId}
                              </Text>
                            )}
                          </LegacyStack.Item>
                          <LegacyStack.Item>
                            <LegacyStack vertical spacing="tight">
                              {getStatusBadge(booking.status)}
                              {booking.status === 'PROVISIONAL' && booking.priority > 1 && (
                                <Badge tone="warning">{`第${booking.priority}優先`}</Badge>
                              )}
                            </LegacyStack>
                          </LegacyStack.Item>
                        </LegacyStack>
                      </div>
                    ))}
                  </div>
                )}
              </LegacyCard>

              <LegacyCard title="予約カレンダー" sectioned>
                <RentalCalendar
                  productId={product.id} // DBの内部IDを渡す
                  basePrice={parseFloat(product.price || '0')}
                  reservations={currentBookings.map(b => ({ startDate: b.startDate, endDate: b.endDate }))}
                  holidays={[]}
                  onDateChange={handleCalendarDateChange}
                  onPriceChange={handleCalendarPriceChange}
                />
              </LegacyCard>

              <LegacyCard title="過去の予約履歴" sectioned>
                {pastBookings.length === 0 ? (
                  <Banner title="情報" tone="info">過去の予約履歴はありません</Banner>
                ) : (
                  <div>
                    {pastBookings.map((booking) => (
                      <div key={booking.id} style={{ marginBottom: '16px', borderBottom: '1px solid #ddd', paddingBottom: '16px' }}>
                        <LegacyStack>
                          <LegacyStack.Item fill>
                              <Text as="span" variant="bodyMd">{booking.customerName || booking.customerEmail || '顧客情報なし'}</Text>
                            <Text as="p" variant="bodySm">
                              期間: {formatLocalDate(booking.startDate)} 〜 {formatLocalDate(booking.endDate)}
                            </Text>
                          </LegacyStack.Item>
                          <LegacyStack.Item>
                            {getStatusBadge(booking.status)}
                          </LegacyStack.Item>
                        </LegacyStack>
                      </div>
                    ))}

                    {pastBookings.length >= 10 && (
                      <div style={{ textAlign: 'center', marginTop: '16px' }}>
                      </div>
                    )}
                  </div>
                )}
              </LegacyCard>
            </Layout.Section>

            <Layout.Section>
              <LegacyCard title="予約管理" sectioned>
                <BlockStack gap="100">
                  {/* productId には DBの内部IDを渡す */}
                  <Button variant="primary" url={`/app/bookings/new?productId=${product.id}`}>新規予約を作成</Button>
                </BlockStack>
              </LegacyCard>
            </Layout.Section>
          </Layout>
        )}

        {/* メンテナンスタブ */}
        {selectedTab === 2 && (
          <Layout>
            <Layout.Section>
              <LegacyCard title="メンテナンス情報" sectioned>
                {!maintenanceStatus && !lastMaintenanceDate ? (
                  <Banner title="情報" tone="info">メンテナンス情報がありません</Banner>
                ) : (
                  <DescriptionList
                    items={[
                      { term: 'メンテナンス状態', description: maintenanceStatus || '未設定' },
                      { term: '最終メンテナンス日', description: lastMaintenanceDate ? formatLocalDate(lastMaintenanceDate) : '未設定' },
                      { term: '次回メンテナンス予定日', description: nextMaintenanceDate ? formatLocalDate(nextMaintenanceDate) : '未設定' },
                      { term: '備考', description: maintenanceNotes || '特になし' }
                    ]}
                  />
                )}
              </LegacyCard>

              <LegacyCard title="メンテナンス履歴" sectioned>
                <Banner title="情報" tone="info">メンテナンス履歴機能は開発中です</Banner>
              </LegacyCard>
            </Layout.Section>

            <Layout.Section>
              <LegacyCard title="操作" sectioned>
                <BlockStack gap="100">
                  <Button onClick={() => setShowMaintenanceModal(true)}>メンテナンス登録</Button>
                </BlockStack>
              </LegacyCard>
            </Layout.Section>
          </Layout>
        )}

        {/* 購入/廃棄情報タブ */}
        {selectedTab === 3 && (
          <Layout>
            <Layout.Section>
              <LegacyCard title="購入情報" sectioned>
                {!metadata.purchaseInfo || Object.keys(metadata.purchaseInfo).length === 0 ? (
                  <Banner title="情報" tone="info">購入情報がありません</Banner>
                ) : (
                  <DescriptionList
                    items={[
                      { term: 'メーカー', description: metadata.purchaseInfo?.manufacturer || '未設定' },
                      { term: '購入場所', description: metadata.purchaseInfo?.purchasePlace || '未設定' },
                      { term: '購入日', description: metadata.purchaseInfo?.purchaseDate ? formatLocalDate(metadata.purchaseInfo.purchaseDate) : '未設定' },
                      { term: '購入金額', description: metadata.purchaseInfo?.purchasePrice ? `${parseInt(metadata.purchaseInfo.purchasePrice).toLocaleString()}円` : '未設定' },
                      { term: '備考', description: metadata.purchaseInfo?.purchaseNotes || '特になし' }
                    ]}
                  />
                )}
              </LegacyCard>

              <LegacyCard title="廃棄情報" sectioned>
                {!metadata.disposalInfo || !metadata.disposalInfo.isDisposed ? (
                  <Banner title="情報" tone="info">廃棄情報がありません</Banner>
                ) : (
                  <DescriptionList
                    items={[
                      { term: '廃棄状態', description: metadata.disposalInfo.isDisposed ? (<Badge tone="critical">廃棄済み</Badge>) : '未廃棄' },
                      { term: '廃棄日', description: metadata.disposalInfo.disposalDate ? formatLocalDate(metadata.disposalInfo.disposalDate) : '未設定' },
                      { term: '廃棄理由', description: metadata.disposalInfo.disposalReason || '未設定' },
                      { term: '廃棄方法', description: metadata.disposalInfo.disposalMethod || '未設定' },
                      { term: '備考', description: metadata.disposalInfo.disposalNotes || '特になし' }
                    ]}
                  />
                )}
              </LegacyCard>
            </Layout.Section>

            <Layout.Section>
              <LegacyCard title="操作" sectioned>
                <BlockStack gap="100">
                  {metadata.disposalInfo?.isDisposed ? (
                    <Button disabled>廃棄済み</Button>
                  ) : (
                    <Button tone="critical" disabled>廃棄登録 (未実装)</Button>
                  )}
                </BlockStack>
              </LegacyCard>
            </Layout.Section>
          </Layout>
        )}
      </div>

      {/* メンテナンス登録モーダル */}
      <Modal
        open={showMaintenanceModal}
        onClose={() => setShowMaintenanceModal(false)}
        title="メンテナンス登録"
        primaryAction={{
          content: '保存',
          onAction: () => {
            setShowMaintenanceModal(false);
            alert('メンテナンス情報の保存機能は開発中です');
          }
        }}
        secondaryActions={[
          {
            content: 'キャンセル',
            onAction: () => setShowMaintenanceModal(false)
          }
        ]}
      >
        <Modal.Section>
          <Banner title="情報" tone="info">メンテナンス登録機能は開発中です</Banner>
        </Modal.Section>
      </Modal>
    </Page>
  );
}