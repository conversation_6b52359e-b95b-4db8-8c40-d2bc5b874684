// app/routes/app.scopes.tsx
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { authenticate } from "../shopify.server";
import {
  Page,
  Layout,
  Text,
  Card,
  BlockStack,
  Box,
  List,
} from "@shopify/polaris";
import type { LoaderFunctionArgs } from "@remix-run/node";

const REQUIRED_SCOPES = [
  "write_products",
  "read_products",
  "write_inventory",
  "read_inventory",
  "write_orders",
  "read_orders",
  "write_draft_orders",
  "read_draft_orders",
  "write_metaobjects",
  "read_metaobjects",
  "write_metaobject_definitions",
  "read_metaobject_definitions",
  "write_customers",
  "read_customers",
  "write_reports",
  "read_reports",
  "write_price_rules",
  "read_price_rules",
  "write_shipping",
  "read_shipping",
  "read_shopify_payments_accounts",
  "read_shopify_payments_disputes",
  "read_shopify_payments_payouts",
];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const currentScopes = session?.scope?.split(",") || [];

  const scopeStatus = REQUIRED_SCOPES.map((scope) => ({
    scope,
    granted: currentScopes.includes(scope),
  }));

  return json({
    currentScopes,
    requiredScopes: REQUIRED_SCOPES,
    scopeStatus,
  });
};

export default function Scopes() {
  const { scopeStatus } = useLoaderData<typeof loader>();

  return (
    <Page title="アプリケーションスコープ">
      <Layout>
        <Layout.Section>
          <BlockStack gap="400">
            <Card>
              <BlockStack gap="200">
                <Text as="p">以下のスコープを追加してください：</Text>
                <List>
                  {scopeStatus.map(({ scope, granted }) => (
                    <List.Item key={scope}>
                      <Box
                        padding="300"
                        background={granted ? "bg-surface-success" : "bg-surface-critical"}
                        borderRadius="200"
                      >
                        <Text as="span" tone={granted ? "success" : "critical"}>
                          {granted ? "✓" : "✗"} {scope}
                        </Text>
                      </Box>
                    </List.Item>
                  ))}
                </List>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">設定方法</Text>
                <List>
                  <List.Item>
                    1. Shopifyパートナーダッシュボードで、アプリの設定を開きます
                  </List.Item>
                  <List.Item>
                    2. "App setup" &gt; "Configuration" タブを選択します
                  </List.Item>
                  <List.Item>
                    3. "Admin API access scopes" セクションで必要なスコープを追加します
                  </List.Item>
                  <List.Item>
                    4. 変更を保存し、アプリを再インストールします
                  </List.Item>
                </List>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>

        <Layout.Section>
          <BlockStack gap="400">
            <Card>
              <BlockStack gap="200">
                <Text as="p">スコープの確認中にエラーが発生しました。</Text>
                <Text as="p">アプリが正しくインストールされているか確認してください。</Text>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
    </Page>
  );
}