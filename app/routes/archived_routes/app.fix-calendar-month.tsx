import { json } from "@remix-run/node";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { Page, Layout, Card, Text, BlockStack, Button, Banner, Box } from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { useEffect } from "react";

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  return json({
    message: "カレンダーの月表示問題を修正します"
  });
}

export default function FixCalendarMonth() {
  const { message } = useLoaderData<typeof loader>();

  useEffect(() => {
    // カレンダーの月表示問題を修正する関数
    const fixCalendarMonthIssue = () => {
      console.log('カレンダーの月表示問題を修正します');

      // 現在表示されている月を取得
      const currentMonthElement = document.querySelector('.Polaris-DatePicker__Title');
      const currentMonthText = currentMonthElement ? currentMonthElement.textContent : '';
      console.log('現在表示されている月:', currentMonthText);

      if (currentMonthText) {
        // 月の比較用に月名を取得
        const displayedMonthEn = currentMonthText.split(' ')[0]; // "June" from "June 2025"
        const displayedYear = parseInt(currentMonthText.split(' ')[1] || '0'); // "2025" from "June 2025"
        console.log(`表示月=${displayedMonthEn} ${displayedYear}`);

        // すべての日付要素を取得
        const dateElements = document.querySelectorAll('.Polaris-DatePicker__Day');
        console.log(`日付要素を ${dateElements.length} 個見つけました`);

        // 各要素のデータ属性を確認
        dateElements.forEach((element) => {
          const spanElement = element.querySelector('span');
          if (!spanElement) return;

          // 日付の数字を取得
          const dayNum = spanElement.textContent ? parseInt(spanElement.textContent.trim()) : 0;
          if (!dayNum) return;

          // aria-labelから日付情報を取得
          const ariaLabel = element.getAttribute('aria-label') || '';
          console.log(`日付要素: ${dayNum}, aria-label: ${ariaLabel}`);

          // aria-labelに現在の月が含まれているか確認
          const isCurrentMonth = ariaLabel.includes(displayedMonthEn);
          
          // 予約マークを取得
          const bookingMark = spanElement.querySelector('.booking-mark');
          
          // 現在の月に属さない日付の予約マークを削除
          if (!isCurrentMonth && bookingMark) {
            bookingMark.remove();
            console.log(`${dayNum}日の予約マークを削除しました（別の月の日付）`);
            
            // スタイルもリセット
            const htmlElement = spanElement as HTMLElement;
            if (htmlElement.style) {
              htmlElement.style.color = '';
              htmlElement.style.position = '';
              htmlElement.style.textDecoration = '';
              console.log(`${dayNum}日のスタイルをリセットしました（別の月の日付）`);
            }
          }
        });
      }

      console.log('カレンダーの月表示問題を修正しました');
    };

    // ページ読み込み時に修正を実行
    setTimeout(fixCalendarMonthIssue, 1000);

    // グローバルオブジェクトに関数を追加
    window.fixCalendarMonthIssue = fixCalendarMonthIssue;
  }, []);

  const handleFixCalendarMonth = () => {
    // @ts-ignore
    window.fixCalendarMonthIssue();
  };

  return (
    <Page title="カレンダー月表示修正" backAction={{ content: "戻る", url: "/app" }}>
      <TitleBar title="カレンダー月表示修正" />
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="p">{message}</Text>
              <Text as="p">このページでは、カレンダーの月表示問題を修正するための機能を提供します。</Text>
              <Text as="p">4月の予約データが6月のカレンダーにも表示されてしまう問題を修正します。</Text>
              
              <Banner tone="warning">
                <Text as="p">この問題は、日付要素を特定する際に月を考慮していないことが原因です。</Text>
                <Text as="p">修正後は、異なる月の同じ日付（例：4月19日と6月19日）が別々に扱われるようになります。</Text>
              </Banner>
              
              <Box paddingBlockStart="300">
                <BlockStack gap="200">
                  <Button onClick={handleFixCalendarMonth} primary>
                    カレンダーの月表示問題を修正
                  </Button>
                  <Button url="/app/booking-test">
                    予約テストページに戻る
                  </Button>
                </BlockStack>
              </Box>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
