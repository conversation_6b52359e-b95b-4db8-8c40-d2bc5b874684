import { PrismaClient } from '@prisma/client';
import { addDays, format } from 'date-fns';

const prisma = new PrismaClient();

const SHOP = 'peaces-test-block.myshopify.com';

// テスト用スタッフデータ
const TEST_STAFF = [
  {
    code: 'STAFF001',
    name: '田中 太郎',
    nameKana: 'タナカ タロウ',
    department: '配送部',
    role: '配送担当',
    email: '<EMAIL>',
    phone: '090-1234-5678',
    isActive: true,
  },
  {
    code: 'STAFF002',
    name: '佐藤 花子',
    nameKana: 'サトウ ハナコ',
    department: 'ピッキング部',
    role: 'ピッキング担当',
    email: '<EMAIL>',
    phone: '090-8765-4321',
    isActive: true,
  },
  {
    code: 'STAFF003',
    name: '山田 次郎',
    nameKana: 'ヤマダ ジロウ',
    department: '配送部',
    role: '配送リーダー',
    email: '<EMAIL>',
    phone: '090-2345-6789',
    isActive: true,
  },
];

// テスト用配送業者データ
const TEST_CARRIERS = [
  {
    code: 'YAMATO',
    name: 'ヤマト運輸',
    phoneNumber: '0120-01-9625',
    address: '東京都中央区銀座2-16-10',
    contactPerson: '営業担当者',
    email: '<EMAIL>',
    notes: '大型家具配送対応',
  },
  {
    code: 'SAGAWA',
    name: '佐川急便',
    phoneNumber: '0120-33-7224',
    address: '京都府京都市南区上鳥羽角田町68',
    contactPerson: '配送担当者',
    email: '<EMAIL>',
    notes: '重量物配送対応',
  },
  {
    code: 'FUKUTSU',
    name: '福山通運',
    phoneNumber: '************',
    address: '広島県福山市東深津町4-20-1',
    contactPerson: '物流担当者',
    email: '<EMAIL>',
    notes: '家具専門配送',
  },
];

// テスト用配送料金データ
const TEST_SHIPPING_FEES = [
  {
    name: '基本配送料（小型）',
    shippingType: 'BASIC',
    regions: '関東',
    fee: 3000,
    minHeight: 0,
    maxHeight: 100,
    minWidth: 0,
    maxWidth: 100,
    minDepth: 0,
    maxDepth: 100,
    minWeight: 0,
    maxWeight: 30,
    isActive: true,
  },
  {
    name: '基本配送料（中型）',
    shippingType: 'BASIC',
    regions: '関東',
    fee: 5000,
    minHeight: 101,
    maxHeight: 150,
    minWidth: 101,
    maxWidth: 150,
    minDepth: 101,
    maxDepth: 150,
    minWeight: 31,
    maxWeight: 50,
    isActive: true,
  },
  {
    name: 'トラック配送料（大型）',
    shippingType: 'TRUCK',
    regions: '関東',
    fee: 10000,
    minHeight: 151,
    maxHeight: 300,
    minWidth: 151,
    maxWidth: 300,
    minDepth: 151,
    maxDepth: 300,
    minWeight: 51,
    maxWeight: 200,
    isActive: true,
  },
];

async function createDeliveryTestData() {
  try {
    console.log('配送管理のテストデータ作成を開始します...');

    // 既存のテストデータをクリア
    console.log('既存のテストデータをクリアしています...');
    await prisma.deliverySchedule.deleteMany({ where: { shop: SHOP } });
    await prisma.shippingFee.deleteMany({ where: { shop: SHOP } });
    await prisma.shippingCarrier.deleteMany({ where: { shop: SHOP } });
    await prisma.staff.deleteMany({ where: { shop: SHOP } });

    // スタッフデータを作成
    console.log('スタッフデータを作成しています...');
    const staffData = await Promise.all(
      TEST_STAFF.map(staff =>
        prisma.staff.create({
          data: {
            ...staff,
            shop: SHOP,
          },
        })
      )
    );
    console.log(`${staffData.length}件のスタッフデータを作成しました`);

    // 配送業者データを作成
    console.log('配送業者データを作成しています...');
    const carrierData = await Promise.all(
      TEST_CARRIERS.map(carrier =>
        prisma.shippingCarrier.create({
          data: {
            ...carrier,
            shop: SHOP,
          },
        })
      )
    );
    console.log(`${carrierData.length}件の配送業者データを作成しました`);

    // 配送料金データを作成
    console.log('配送料金データを作成しています...');
    const shippingFeeData = await Promise.all(
      TEST_SHIPPING_FEES.map(fee =>
        prisma.shippingFee.create({
          data: {
            ...fee,
            shop: SHOP,
          },
        })
      )
    );
    console.log(`${shippingFeeData.length}件の配送料金データを作成しました`);

    // 既存の予約データを取得
    console.log('既存の予約データを確認しています...');
    const existingBookings = await prisma.booking.findMany({
      where: { shop: SHOP },
      take: 10,
    });

    if (existingBookings.length === 0) {
      console.log('予約データが見つかりません。配送スケジュールの作成をスキップします。');
      console.log('先に予約データを作成してから、再度このスクリプトを実行してください。');
      return;
    }

    // 配送スケジュールデータを作成
    console.log('配送スケジュールデータを作成しています...');
    const today = new Date();
    const deliverySchedules = [];

    for (let i = 0; i < Math.min(existingBookings.length, 5); i++) {
      const booking = existingBookings[i];
      const deliveryDate = addDays(today, i + 1);
      const staff = staffData[i % staffData.length];
      const carrier = carrierData[i % carrierData.length];
      const shippingFee = shippingFeeData[i % shippingFeeData.length];

      // 配送スケジュール
      const deliverySchedule = await prisma.deliverySchedule.create({
        data: {
          shop: SHOP,
          bookingId: booking.id,
          deliveryDate: deliveryDate,
          deliveryType: 'delivery',
          timeFrom: '09:00',
          timeTo: '12:00',
          destination: `配送先${i + 1}`,
          staffId: staff.id,
          address: `東京都渋谷区渋谷${i + 1}-${i + 1}-${i + 1}`,
          carrierId: carrier.id,
          notes: `配送メモ${i + 1}`,
          pickingStatus: 'pending',
          pickingLocation: 'NY',
          pickingStaffId: staffData[1].id, // ピッキング担当者
          shippingFeeId: shippingFee.id,
          shippingFeeAmount: shippingFee.fee,
        },
      });

      // 返却スケジュール
      const returnDate = addDays(deliveryDate, 7);
      const returnSchedule = await prisma.deliverySchedule.create({
        data: {
          shop: SHOP,
          bookingId: booking.id,
          deliveryDate: returnDate,
          deliveryType: 'pickup',
          timeFrom: '14:00',
          timeTo: '17:00',
          destination: `返却先${i + 1}`,
          staffId: staff.id,
          address: `東京都渋谷区渋谷${i + 1}-${i + 1}-${i + 1}`,
          carrierId: carrier.id,
          notes: `返却メモ${i + 1}`,
          returnStatus: 'pending',
          shippingFeeId: shippingFee.id,
          shippingFeeAmount: shippingFee.fee,
        },
      });

      deliverySchedules.push(deliverySchedule, returnSchedule);
    }

    console.log(`${deliverySchedules.length}件の配送スケジュールデータを作成しました`);

    // 作成結果を表示
    console.log('\n=== 作成されたテストデータ ===');
    console.log(`スタッフ: ${staffData.length}件`);
    console.log(`配送業者: ${carrierData.length}件`);
    console.log(`配送料金: ${shippingFeeData.length}件`);
    console.log(`配送スケジュール: ${deliverySchedules.length}件`);

    console.log('\n配送管理のテストデータ作成が完了しました！');

  } catch (error) {
    console.error('テストデータ作成中にエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  createDeliveryTestData();
}

export { createDeliveryTestData };
