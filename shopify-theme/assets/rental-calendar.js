/**
 * レンタル商品カレンダー用JavaScript
 *
 * このスクリプトは、商品詳細ページでの予約カレンダー表示、
 * 日付選択、予約状況表示などの機能を提供します。
 */

class RentalCalendar {
  constructor(options = {}) {
    // 設定を読み込んでから初期化
    this.loadConfig().then(() => {
      // オプションの設定
      this.options = {
        productId: options.productId || '',
        shopifyProductId: options.shopifyProductId || '',
        basePrice: parseFloat(options.basePrice || 0),
        containerSelector: options.containerSelector || '#rental-calendar-container',
        apiEndpoint: `/apps/ease-next-temp/get-product-bookings`,
        ...options
      };

      // 状態の初期化
      this.state = {
        startDate: null,
        endDate: null,
        bookings: [],
        holidays: [],
        newYearDates: [],
        rentalDays: 0,
        totalPrice: 0,
        depositAmount: 0,
        isAvailable: false,
        isLoading: false,
        errorMessage: ''
      };

      // DOM要素の参照
      this.elements = {
        container: document.querySelector(this.options.containerSelector),
        calendarContainer: null,
        startDateInput: null,
        endDateInput: null,
        rentalDaysElement: null,
        totalPriceElement: null,
        depositElement: null,
        errorMessageElement: null,
        loadingIndicator: null
      };

      // カレンダーの初期化
      this.initCalendar();
    }).catch(error => {
      console.error("Failed to load config:", error);
      // フォールバック設定を使用して初期化
      this.initWithFallbackConfig(options);
    });
  }

  /**
   * フォールバック設定で初期化
   */
  initWithFallbackConfig(options) {
    // フォールバック設定
    this.config = {
      appUrl: 'https://app.shopify-app-test.xyz',
      environment: 'development'
    };

    // オプションの設定
    this.options = {
      productId: options.productId || '',
      shopifyProductId: options.shopifyProductId || '',
      basePrice: parseFloat(options.basePrice || 0),
      containerSelector: options.containerSelector || '#rental-calendar-container',
      apiEndpoint: `/apps/ease-next-temp/get-product-bookings`,
      ...options
    };

    // 状態の初期化
    this.state = {
      startDate: null,
      endDate: null,
      bookings: [],
      holidays: [],
      newYearDates: [],
      rentalDays: 0,
      totalPrice: 0,
      depositAmount: 0,
      isAvailable: false,
      isLoading: false,
      errorMessage: ''
    };

    // DOM要素の参照
    this.elements = {
      container: document.querySelector(this.options.containerSelector),
      calendarContainer: null,
      startDateInput: null,
      endDateInput: null,
      rentalDaysElement: null,
      totalPriceElement: null,
      depositElement: null,
      errorMessageElement: null,
      loadingIndicator: null
    };

    // カレンダーの初期化
    this.initCalendar();
  }

  /**
   * 設定を読み込む
   */
  async loadConfig() {
    try {
      // テーマから環境設定を取得
      if (window.shopConfig) {
        this.config = window.shopConfig;
        console.log("Config loaded from theme:", this.config);
        return;
      }

      // テーマから設定が取得できない場合はフォールバック設定を使用
      this.config = {
        appUrl: 'https://app.shopify-app-test.xyz',
        environment: 'development'
      };
      console.log("Using fallback config:", this.config);
    } catch (error) {
      console.error("Error loading config:", error);
      // フォールバック設定
      this.config = {
        appUrl: 'https://app.shopify-app-test.xyz',
        environment: 'development'
      };
      console.log("Using fallback config:", this.config);
    }
  }

  /**
   * カレンダーの初期化
   */
  async initCalendar() {
    if (!this.elements.container) {
      console.error('カレンダーコンテナが見つかりません:', this.options.containerSelector);
      return;
    }

    // カレンダーUIの作成
    this.createCalendarUI();

    // 予約データの取得
    this.state.isLoading = true;
    this.updateUI();

    try {
      // 予約データを取得
      await this.fetchBookings();

      // カレンダーに予約データを表示
      this.renderBookingsOnCalendar();
    } catch (error) {
      console.error('カレンダー初期化エラー:', error);
      this.state.errorMessage = 'データの取得に失敗しました';
    } finally {
      this.state.isLoading = false;
      this.updateUI();
    }
  }

  /**
   * カレンダーUIの作成
   */
  createCalendarUI() {
    // コンテナ内のHTMLを作成
    this.elements.container.innerHTML = `
      <div class="rental-calendar">
        <div class="rental-calendar__loading" style="display: none;">
          <div class="rental-calendar__loading-spinner"></div>
          <p>読み込み中...</p>
        </div>

        <div class="rental-calendar__error" style="display: none; color: red;"></div>

        <div class="rental-calendar__date-selection">
          <div class="rental-calendar__date-field">
            <label for="rental-start-date">レンタル開始日</label>
            <input type="date" id="rental-start-date" class="rental-calendar__date-input">
          </div>

          <div class="rental-calendar__date-field">
            <label for="rental-end-date">レンタル終了日</label>
            <input type="date" id="rental-end-date" class="rental-calendar__date-input">
          </div>
        </div>

        <div class="rental-calendar__calendar-container"></div>

        <div class="rental-calendar__info" style="display: none;">
          <div class="rental-calendar__rental-days">
            <span>レンタル日数:</span>
            <span id="rental-days-count">0</span>日
          </div>

          <div class="rental-calendar__price">
            <span>レンタル料金:</span>
            <span id="rental-price-amount">0</span>円
          </div>

          <div class="rental-calendar__deposit">
            <span>デポジット:</span>
            <span id="deposit-amount">0</span>円
          </div>
        </div>

        <div class="rental-calendar__legend">
          <div class="rental-calendar__legend-item">
            <span class="rental-calendar__legend-mark rental-calendar__legend-mark--reserved">予</span>
            <span>予約済み</span>
          </div>

          <div class="rental-calendar__legend-item">
            <span class="rental-calendar__legend-mark rental-calendar__legend-mark--provisional">仮</span>
            <span>仮予約</span>
          </div>

          <div class="rental-calendar__legend-item">
            <span class="rental-calendar__legend-mark rental-calendar__legend-mark--holiday"></span>
            <span>休業日（日曜・祝日・年末年始）</span>
          </div>
        </div>
      </div>
    `;

    // DOM要素の参照を更新
    this.elements.calendarContainer = this.elements.container.querySelector('.rental-calendar__calendar-container');
    this.elements.startDateInput = this.elements.container.querySelector('#rental-start-date');
    this.elements.endDateInput = this.elements.container.querySelector('#rental-end-date');
    this.elements.rentalDaysElement = this.elements.container.querySelector('#rental-days-count');
    this.elements.totalPriceElement = this.elements.container.querySelector('#rental-price-amount');
    this.elements.depositElement = this.elements.container.querySelector('#deposit-amount');
    this.elements.errorMessageElement = this.elements.container.querySelector('.rental-calendar__error');
    this.elements.loadingIndicator = this.elements.container.querySelector('.rental-calendar__loading');
    this.elements.infoContainer = this.elements.container.querySelector('.rental-calendar__info');

    // イベントリスナーの設定
    this.setupEventListeners();
  }

  /**
   * イベントリスナーの設定
   */
  setupEventListeners() {
    // 日付入力のイベントリスナー
    if (this.elements.startDateInput && this.elements.endDateInput) {
      this.elements.startDateInput.addEventListener('change', () => this.handleDateChange());
      this.elements.endDateInput.addEventListener('change', () => this.handleDateChange());
    }
  }

  /**
   * 予約データの取得
   */
  async fetchBookings() {
    try {
      // APIエンドポイントの構築
      const url = new URL(this.options.apiEndpoint, window.location.origin);

      // クエリパラメータの追加
      if (this.options.productId) {
        url.searchParams.append('productId', this.options.productId);
      }

      if (this.options.shopifyProductId) {
        url.searchParams.append('shopifyProductId', this.options.shopifyProductId);
      }

      // APIリクエスト
      const response = await fetch(url.toString());

      if (!response.ok) {
        throw new Error(`APIエラー: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'データの取得に失敗しました');
      }

      // 状態を更新
      this.state.bookings = data.bookings || [];
      this.state.holidays = data.holidays || [];
      this.state.newYearDates = data.newYearDates || [];

      return data;
    } catch (error) {
      console.error('予約データ取得エラー:', error);
      this.state.errorMessage = '予約データの取得に失敗しました';
      throw error;
    }
  }

  /**
   * カレンダーに予約データを表示
   */
  renderBookingsOnCalendar() {
    // カレンダーの実装（次のステップで実装）
  }

  /**
   * 日付変更時の処理
   */
  handleDateChange() {
    // 入力値の取得
    const startDateStr = this.elements.startDateInput.value;
    const endDateStr = this.elements.endDateInput.value;

    // 日付のバリデーション
    if (!startDateStr || !endDateStr) {
      this.updateState({
        startDate: startDateStr ? new Date(startDateStr) : null,
        endDate: endDateStr ? new Date(endDateStr) : null,
        rentalDays: 0,
        totalPrice: 0,
        depositAmount: 0,
        isAvailable: false,
        errorMessage: '開始日と終了日を選択してください'
      });
      this.updateUI();
      return;
    }

    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);

    // 終了日が開始日より前の場合
    if (endDate < startDate) {
      this.updateState({
        startDate: startDate,
        endDate: null,
        rentalDays: 0,
        totalPrice: 0,
        depositAmount: 0,
        isAvailable: false,
        errorMessage: '終了日は開始日より後の日付を選択してください'
      });
      this.updateUI();
      return;
    }

    // レンタル日数の計算
    const rentalDays = this.calculateRentalDays(startDate, endDate);

    // 料金の計算
    const { totalPrice, depositAmount } = this.calculatePrice(rentalDays);

    // 状態の更新
    this.updateState({
      startDate: startDate,
      endDate: endDate,
      rentalDays: rentalDays,
      totalPrice: totalPrice,
      depositAmount: depositAmount,
      isAvailable: true,
      errorMessage: ''
    });

    // 在庫状況の確認
    this.checkAvailability();
  }

  /**
   * レンタル日数の計算
   */
  calculateRentalDays(startDate, endDate) {
    // 日曜日と祝日を除外した営業日数を計算
    // 簡易版: 日数の差を計算
    const diffTime = Math.abs(endDate - startDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // 初日も含める
    return diffDays;
  }

  /**
   * 料金の計算
   */
  calculatePrice(days) {
    if (days <= 0 || !this.options.basePrice) {
      return { totalPrice: 0, depositAmount: 0 };
    }

    let totalPrice = 0;

    // 1日目: 100%
    totalPrice += this.options.basePrice;

    // 2-6日目: 20%
    const days2to6 = Math.min(days - 1, 5);
    if (days2to6 > 0) {
      totalPrice += this.options.basePrice * 0.2 * days2to6;
    }

    // 7日目以降: 10%
    const days7plus = Math.max(days - 6, 0);
    if (days7plus > 0) {
      totalPrice += this.options.basePrice * 0.1 * days7plus;
    }

    // デポジット: 基本料金の10%
    const depositAmount = Math.round(this.options.basePrice * 0.1);

    return {
      totalPrice: Math.round(totalPrice),
      depositAmount: depositAmount
    };
  }

  /**
   * 在庫状況の確認
   */
  async checkAvailability() {
    if (!this.state.startDate || !this.state.endDate) {
      return;
    }

    this.state.isLoading = true;
    this.updateUI();

    try {
      // 日付をフォーマット
      const formatDate = (date) => {
        const d = new Date(date);
        return d.toISOString().split('T')[0]; // YYYY-MM-DD形式
      };

      const startDateStr = formatDate(this.state.startDate);
      const endDateStr = formatDate(this.state.endDate);

      // APIエンドポイントの構築
      const url = new URL('/apps/ease-next-temp/check-product-availability', window.location.origin);

      // クエリパラメータの追加
      if (this.options.productId) {
        url.searchParams.append('productId', this.options.productId);
      }

      if (this.options.shopifyProductId) {
        url.searchParams.append('shopifyProductId', this.options.shopifyProductId);
      }

      url.searchParams.append('startDate', startDateStr);
      url.searchParams.append('endDate', endDateStr);

      // APIリクエスト
      const response = await fetch(url.toString());

      if (!response.ok) {
        throw new Error(`APIエラー: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || '在庫確認に失敗しました');
      }

      // 状態を更新
      this.updateState({
        isAvailable: data.available,
        errorMessage: data.available ? '' : '選択した期間は予約できません'
      });

      return data.available;
    } catch (error) {
      console.error('在庫確認エラー:', error);
      this.updateState({
        isAvailable: false,
        errorMessage: '在庫確認中にエラーが発生しました'
      });
      return false;
    } finally {
      this.state.isLoading = false;
      this.updateUI();
    }
  }

  /**
   * 状態の更新
   */
  updateState(newState) {
    this.state = { ...this.state, ...newState };
  }

  /**
   * UIの更新
   */
  updateUI() {
    // ローディング表示
    if (this.elements.loadingIndicator) {
      this.elements.loadingIndicator.style.display = this.state.isLoading ? 'flex' : 'none';
    }

    // エラーメッセージ
    if (this.elements.errorMessageElement) {
      this.elements.errorMessageElement.textContent = this.state.errorMessage;
      this.elements.errorMessageElement.style.display = this.state.errorMessage ? 'block' : 'none';
    }

    // 情報表示
    if (this.elements.infoContainer) {
      this.elements.infoContainer.style.display = this.state.startDate && this.state.endDate ? 'block' : 'none';
    }

    // レンタル日数
    if (this.elements.rentalDaysElement) {
      this.elements.rentalDaysElement.textContent = this.state.rentalDays;
    }

    // 料金
    if (this.elements.totalPriceElement) {
      this.elements.totalPriceElement.textContent = this.state.totalPrice.toLocaleString();
    }

    // デポジット
    if (this.elements.depositElement) {
      this.elements.depositElement.textContent = this.state.depositAmount.toLocaleString();
    }
  }
}

// グローバルオブジェクトとして初期化
window.RentalCalendar = RentalCalendar;
