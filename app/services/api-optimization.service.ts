/**
 * API最適化サービス
 * 
 * このサービスは、Shopify API呼び出しを最適化するための機能を提供します。
 * - APIリクエストのバッチ処理
 * - レスポンスのキャッシュ
 * - レート制限の管理
 */

import { LRUCache } from 'lru-cache';

// キャッシュの設定
const CACHE_OPTIONS = {
  max: 500, // 最大エントリ数
  ttl: 1000 * 60 * 5, // 有効期限（5分）
  allowStale: false, // 期限切れのエントリを返さない
  updateAgeOnGet: true, // 取得時に有効期限をリセット
  updateAgeOnHas: false // hasメソッド呼び出し時に有効期限をリセットしない
};

// APIリクエストのキュー
interface ApiRequest {
  id: string;
  url: string;
  method: string;
  headers: Record<string, string>;
  body?: any;
  priority: number;
  timestamp: number;
  resolve: (value: any) => void;
  reject: (reason: any) => void;
}

export class ApiOptimizationService {
  private static instance: ApiOptimizationService;
  private cache: LRUCache<string, any>;
  private requestQueue: ApiRequest[] = [];
  private processingQueue = false;
  private rateLimitRemaining = 1000; // デフォルト値
  private rateLimitResetTime = 0;
  private lastRequestTime = 0;
  private minRequestInterval = 100; // ミリ秒単位の最小リクエスト間隔

  private constructor() {
    this.cache = new LRUCache(CACHE_OPTIONS);
    
    // 定期的にキューを処理
    setInterval(() => this.processQueue(), 500);
  }

  /**
   * シングルトンインスタンスを取得
   */
  public static getInstance(): ApiOptimizationService {
    if (!ApiOptimizationService.instance) {
      ApiOptimizationService.instance = new ApiOptimizationService();
    }
    return ApiOptimizationService.instance;
  }

  /**
   * APIリクエストを実行（キャッシュとバッチ処理を使用）
   */
  public async request(url: string, options: RequestInit, useCache = true, priority = 1): Promise<any> {
    const method = options.method || 'GET';
    const cacheKey = this.generateCacheKey(url, options);
    
    // GETリクエストでキャッシュが有効な場合、キャッシュをチェック
    if (useCache && method === 'GET' && this.cache.has(cacheKey)) {
      console.log(`[API] キャッシュヒット: ${url}`);
      return this.cache.get(cacheKey);
    }
    
    // リクエストをキューに追加
    return new Promise((resolve, reject) => {
      const request: ApiRequest = {
        id: `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        url,
        method,
        headers: options.headers as Record<string, string> || {},
        body: options.body,
        priority,
        timestamp: Date.now(),
        resolve,
        reject
      };
      
      this.requestQueue.push(request);
      this.requestQueue.sort((a, b) => b.priority - a.priority || a.timestamp - b.timestamp);
      
      // キュー処理を開始
      if (!this.processingQueue) {
        this.processQueue();
      }
    });
  }

  /**
   * キャッシュキーを生成
   */
  private generateCacheKey(url: string, options: RequestInit): string {
    const method = options.method || 'GET';
    const body = options.body ? JSON.stringify(options.body) : '';
    return `${method}:${url}:${body}`;
  }

  /**
   * リクエストキューを処理
   */
  private async processQueue() {
    if (this.processingQueue || this.requestQueue.length === 0) {
      return;
    }
    
    this.processingQueue = true;
    
    try {
      // レート制限をチェック
      if (this.rateLimitRemaining <= 0) {
        const now = Date.now();
        if (now < this.rateLimitResetTime) {
          // レート制限がリセットされるまで待機
          console.log(`[API] レート制限に達しました。${Math.ceil((this.rateLimitResetTime - now) / 1000)}秒後に再試行します。`);
          setTimeout(() => this.processQueue(), this.rateLimitResetTime - now + 100);
          this.processingQueue = false;
          return;
        }
      }
      
      // リクエスト間隔を確保
      const now = Date.now();
      const timeSinceLastRequest = now - this.lastRequestTime;
      if (timeSinceLastRequest < this.minRequestInterval) {
        await new Promise(resolve => setTimeout(resolve, this.minRequestInterval - timeSinceLastRequest));
      }
      
      // キューから次のリクエストを取得
      const request = this.requestQueue.shift();
      if (!request) {
        this.processingQueue = false;
        return;
      }
      
      // リクエストを実行
      try {
        console.log(`[API] リクエスト実行: ${request.method} ${request.url}`);
        this.lastRequestTime = Date.now();
        
        const response = await fetch(request.url, {
          method: request.method,
          headers: request.headers,
          body: request.body
        });
        
        // レート制限情報を更新
        this.updateRateLimits(response);
        
        if (!response.ok) {
          throw new Error(`API エラー: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // GETリクエストの場合、結果をキャッシュ
        if (request.method === 'GET') {
          const cacheKey = this.generateCacheKey(request.url, {
            method: request.method,
            headers: request.headers,
            body: request.body
          });
          this.cache.set(cacheKey, data);
        }
        
        request.resolve(data);
      } catch (error) {
        console.error(`[API] リクエストエラー: ${error.message}`);
        request.reject(error);
      }
    } finally {
      this.processingQueue = false;
      
      // キューに残りがあれば続けて処理
      if (this.requestQueue.length > 0) {
        setTimeout(() => this.processQueue(), this.minRequestInterval);
      }
    }
  }

  /**
   * レスポンスヘッダーからレート制限情報を更新
   */
  private updateRateLimits(response: Response) {
    const remaining = response.headers.get('X-Shopify-Shop-Api-Call-Limit');
    if (remaining) {
      const [used, limit] = remaining.split('/').map(Number);
      this.rateLimitRemaining = limit - used;
      
      // リセット時間を推定（通常は1分間隔）
      this.rateLimitResetTime = Date.now() + 60 * 1000;
      
      console.log(`[API] レート制限: ${used}/${limit} (残り: ${this.rateLimitRemaining})`);
    }
  }

  /**
   * キャッシュを無効化
   */
  public invalidateCache(url: string, options: RequestInit = {}) {
    const cacheKey = this.generateCacheKey(url, options);
    this.cache.delete(cacheKey);
  }

  /**
   * 特定のパターンに一致するキャッシュをすべて無効化
   */
  public invalidateCacheByPattern(pattern: RegExp) {
    const keys = Array.from(this.cache.keys());
    let count = 0;
    
    for (const key of keys) {
      if (pattern.test(key)) {
        this.cache.delete(key);
        count++;
      }
    }
    
    console.log(`[API] ${count}件のキャッシュエントリを無効化しました`);
    return count;
  }

  /**
   * キャッシュの統計情報を取得
   */
  public getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: CACHE_OPTIONS.max,
      hitRate: this.cache.stats?.getHitRate() || 0,
      hits: this.cache.stats?.hits || 0,
      misses: this.cache.stats?.misses || 0
    };
  }

  /**
   * キューの状態を取得
   */
  public getQueueStats() {
    return {
      queueLength: this.requestQueue.length,
      processingQueue: this.processingQueue,
      rateLimitRemaining: this.rateLimitRemaining,
      rateLimitResetTime: new Date(this.rateLimitResetTime).toISOString()
    };
  }
}
