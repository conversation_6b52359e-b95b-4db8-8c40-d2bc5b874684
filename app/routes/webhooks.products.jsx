/**
 * 商品作成・更新時のWebhookハンドラー
 *
 * このファイルは、Shopifyの商品作成・更新イベントのWebhookを処理し、
 * 必要なメタフィールドを自動的に設定します。
 * また、DbSyncServiceを使用してデータベースとの同期も行います。
 */
import { json } from "@remix-run/node";
import { GraphQLClient, gql } from 'graphql-request';
import { prisma } from "~/db.server";
import { DbSyncService } from "~/services/sync/db-sync.service";
import { authenticate } from "~/shopify.server";

/**
 * Webhookリクエストを処理するアクションハンドラー
 */
export async function action({ request }) {
  try {
    console.log("========== Webhook受信開始 ==========");

    // リクエストヘッダーを確認
    const headers = Object.fromEntries(request.headers.entries());
    console.log("Webhookヘッダー:", JSON.stringify(headers, null, 2));

    // リクエストボディを取得（認証前に取得）
    const rawBody = await request.text();
    const payload = JSON.parse(rawBody);
    console.log("Webhookペイロード:", JSON.stringify(payload, null, 2));

    // Shopify認証
    // 新しいリクエストオブジェクトを作成して認証に使用
    const hmac = headers["x-shopify-hmac-sha256"];
    const shop = headers["x-shopify-shop-domain"];
    const topic = headers["x-shopify-topic"];

    if (!hmac || !shop || !topic) {
      console.error("必要なヘッダーが不足しています");
      return json({ error: "必要なヘッダーが不足しています" }, { status: 401 });
    }

    console.log(`Webhook処理: Shop ${shop}, Topic ${topic}`);

    // セッションを取得（オフラインセッション）
    const sessionId = `offline_${shop}`;
    const session = await prisma.session.findUnique({
      where: { id: sessionId }
    });

    if (!session) {
      console.error(`セッションが見つかりません: ${sessionId}`);
      return json({ error: "セッションが見つかりません" }, { status: 401 });
    }

    console.log("セッション取得成功:", session.shop);

    // 商品情報を取得
    const productId = payload.id;
    const shopifyGid = `gid://shopify/Product/${productId}`;

    console.log(`Webhook処理: 商品ID ${productId} (${payload.title || '不明'})`);

    // GraphQL APIクライアントの設定
    const graphqlClient = new GraphQLClient(
      `https://${session.shop}/admin/api/2024-01/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': session.accessToken,
          'Content-Type': 'application/json',
        },
      }
    );

    // 商品の詳細情報を取得
    const productDetails = await fetchProductDetails(graphqlClient, shopifyGid);

    // Prismaデータベースに商品情報を保存または更新
    const dbProduct = await saveProductToPrisma(productDetails, session);

    // メタフィールドを設定
    await setProductMetafields(graphqlClient, productDetails, dbProduct);

    // DbSyncServiceを使用して商品データを同期
    try {
      // 認証情報を取得
      const { admin } = await authenticate.admin(request);

      // DbSyncServiceのインスタンスを作成
      const dbSyncService = new DbSyncService(request);

      // 商品データを同期
      const syncResult = await dbSyncService.syncProductFromShopify(productId);

      if (syncResult.success) {
        console.log(`商品同期成功: ${syncResult.message}`);
      } else {
        console.warn(`商品同期警告: ${syncResult.message}`);
      }
    } catch (syncError) {
      console.error("商品同期エラー:", syncError);
      // 同期エラーがあっても処理を続行
    }

    console.log("========== Webhook処理完了 ==========");
    return json({ success: true });
  } catch (error) {
    console.error("========== Webhook処理エラー ==========");
    console.error("エラー詳細:", error);
    console.error("エラーメッセージ:", error.message);
    console.error("エラースタック:", error.stack);
    return json({ error: error.message }, { status: 500 });
  }
}

/**
 * 商品の詳細情報を取得する関数
 * @param {GraphQLClient} client - GraphQLクライアント
 * @param {string} productId - 商品ID
 * @returns {Promise<Object>} - 商品の詳細情報
 */
async function fetchProductDetails(client, productId) {
  const GET_PRODUCT = gql`
    query getProduct($id: ID!) {
      product(id: $id) {
        id
        title
        handle
        variants(first: 20) {
          edges {
            node {
              id
              title
              sku
              price
            }
          }
        }
      }
    }
  `;

  const result = await client.request(GET_PRODUCT, { id: productId });
  return result.product;
}

/**
 * Prismaデータベースに商品情報を保存する関数
 * @param {Object} product - 商品情報
 * @param {Object} session - Shopifyセッション情報
 * @returns {Promise<Object>} - 保存された商品情報
 */
async function saveProductToPrisma(product, session) {
  const shopifyId = product.id.replace('gid://shopify/Product/', '');
  const variants = product.variants.edges.map(edge => edge.node);

  // SKUから商品コードと詳細コードを抽出
  let productCode = '';
  let detailCode = '';

  if (variants.length > 0 && variants[0].sku) {
    const skuParts = variants[0].sku.split('-');
    if (skuParts.length >= 2) {
      productCode = skuParts.slice(0, -1).join('-');
      detailCode = skuParts[skuParts.length - 1];
    }
  }

  // 基本価格を取得（1日レンタルのバリエーション）
  const oneDayVariant = variants.find(v =>
    v.title.toLowerCase().includes('1日') || v.title.toLowerCase().includes('1day')
  );
  const basePrice = oneDayVariant ? parseFloat(oneDayVariant.price) : 10000;

  // Prismaデータベースに商品を保存または更新
  const dbProduct = await prisma.product.upsert({
    where: {
      shop_shopifyId: {
        shop: session.shop,
        shopifyId: shopifyId
      }
    },
    update: {
      title: product.title,
      sku: variants.length > 0 ? variants[0].sku : '',
      price: basePrice,
      basicInfo: JSON.stringify({
        productCode,
        detailCode,
        kana: product.title,
        location: 'NY',
        status: 'available'
      }),
      pricing: JSON.stringify({
        basePrice,
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30
      }),
      updatedAt: new Date()
    },
    create: {
      shopifyId,
      title: product.title,
      sku: variants.length > 0 ? variants[0].sku : '',
      price: basePrice,
      status: 'AVAILABLE',
      shop: session.shop, // process.env.SHOPIFY_SHOPの代わりにsession.shopを使用
      basicInfo: JSON.stringify({
        productCode,
        detailCode,
        kana: product.title,
        location: 'NY',
        status: 'available'
      }),
      pricing: JSON.stringify({
        basePrice,
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30
      }),
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });

  return dbProduct;
}

/**
 * メタフィールドを設定する関数
 * @param {GraphQLClient} client - GraphQLクライアント
 * @param {Object} product - 商品情報
 * @param {Object} dbProduct - データベースの商品情報
 */
async function setProductMetafields(client, product, dbProduct) {
  const variants = product.variants.edges.map(edge => edge.node);

  // バリエーションマッピングを作成
  const mapping = {};

  for (const variant of variants) {
    const title = variant.title.toLowerCase();

    if (title.includes('1日') || title.includes('1day')) {
      mapping['1day'] = variant.id;
    } else if (title.includes('2日') || title.includes('2day')) {
      mapping['2day'] = variant.id;
    } else if (title.includes('3日') || title.includes('3day')) {
      mapping['3day'] = variant.id;
    } else if (title.includes('4日') || title.includes('4day')) {
      mapping['4day'] = variant.id;
    } else if (title.includes('5日') || title.includes('5day')) {
      mapping['5day'] = variant.id;
    } else if (title.includes('6日') || title.includes('6day')) {
      mapping['6day'] = variant.id;
    } else if (title.includes('7日') || title.includes('7day')) {
      mapping['7day'] = variant.id;
    } else if (title.includes('8日以上') || title.includes('8plus') || title.includes('8day')) {
      mapping['8plus'] = variant.id;
    }
  }

  // マッピングが空の場合はスキップ
  if (Object.keys(mapping).length === 0) {
    console.log(`商品 ${product.title} にはレンタル日数バリエーションがありません`);
    return;
  }

  // 料金計算
  const basePrice = dbProduct.price;
  const variantPrices = {
    "1day": basePrice,
    "2day": Math.round(basePrice + basePrice * 0.2),
    "3day": Math.round(basePrice + basePrice * 0.2 * 2),
    "4day": Math.round(basePrice + basePrice * 0.2 * 3),
    "5day": Math.round(basePrice + basePrice * 0.2 * 4),
    "6day": Math.round(basePrice + basePrice * 0.2 * 5),
    "7day": Math.round(basePrice + basePrice * 0.2 * 6),
    "8plus": Math.round(basePrice + basePrice * 0.2 * 6 + basePrice * 0.1) // 8日目は基本料金の10%
  };

  // メタフィールドを設定するGraphQLクエリ
  const SET_METAFIELD = gql`
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  // pricingメタフィールドを更新
  const pricingData = JSON.parse(dbProduct.pricing);
  pricingData.variantPrices = variantPrices;

  // すべてのメタフィールドを一度に設定
  await client.request(SET_METAFIELD, {
    metafields: [
      {
        ownerId: product.id,
        namespace: 'rental',
        key: 'variant_mapping',
        value: JSON.stringify(mapping),
        type: 'json'
      },
      {
        ownerId: product.id,
        namespace: 'rental',
        key: 'pricing',
        value: JSON.stringify(pricingData),
        type: 'json'
      },
      {
        ownerId: product.id,
        namespace: 'rental',
        key: 'basic_info',
        value: dbProduct.basicInfo,
        type: 'json'
      },
      {
        ownerId: product.id,
        namespace: 'rental',
        key: 'variation_type',
        value: 'レンタル日数',
        type: 'single_line_text_field'
      },
      {
        ownerId: product.id,
        namespace: 'rental',
        key: 'maintenance_status',
        value: '良好',
        type: 'single_line_text_field'
      }
    ]
  });
}

/**
 * GETリクエストを処理するローダーハンドラー
 */
export function loader() {
  return json({ message: "Method not allowed" }, { status: 405 });
}
