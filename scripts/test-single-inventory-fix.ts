/**
 * 単一商品での在庫設定修正テスト
 */

import { authenticate } from '../app/shopify.server';
import { VariantAutoCreatorService } from '../app/services/shopify/variant-auto-creator.service';

async function testSingleInventoryFix() {
  console.log('=== 単一商品在庫設定修正テスト開始 ===');

  try {
    const { admin } = await authenticate.admin(new Request('http://localhost'));
    const variantService = new VariantAutoCreatorService();

    // テスト商品データ
    const testProduct = {
      title: 'テスト商品 在庫設定修正確認',
      handle: 'test-inventory-fix-single',
      productType: 'グラス',
      vendor: 'テストベンダー',
      tags: ['テスト', 'レンタル'],
      status: 'ACTIVE',
      variants: [{
        title: 'デフォルト',
        price: '1200',
        sku: 'TEST-INV-FIX-001',
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY'
      }]
    };

    // 1. 商品作成
    console.log('1. テスト商品を作成中...');
    const createResponse = await admin.graphql(`
      mutation productCreate($input: ProductInput!) {
        productCreate(input: $input) {
          product {
            id
            title
            handle
          }
          userErrors {
            field
            message
          }
        }
      }
    `, {
      input: testProduct
    });

    const createData = await createResponse.json();
    
    if (createData.errors || createData.data.productCreate.userErrors.length > 0) {
      throw new Error(`商品作成エラー: ${JSON.stringify(createData.errors || createData.data.productCreate.userErrors)}`);
    }

    const productId = createData.data.productCreate.product.id;
    const shopifyProductId = productId.split('/').pop();
    console.log(`✅ 商品作成成功: ${shopifyProductId}`);

    // 2. バリエーション自動作成（在庫設定含む）
    console.log('\n2. バリエーション自動作成と在庫設定をテスト中...');
    
    const result = await variantService.createMissingVariants(admin, shopifyProductId, {
      basePrice: 1200,
      productStatus: 'available',
      location: 'NY',
      createProvisionalVariants: false
    });

    console.log('\n=== バリエーション作成結果 ===');
    console.log(`作成されたバリエーション数: ${result.createdVariants.length}`);
    console.log(`在庫設定: ${result.inventoryUpdated ? '✅ 成功' : '❌ 失敗'}`);
    console.log(`全体の成功: ${result.success ? '✅ 成功' : '❌ 失敗'}`);
    
    if (result.errors.length > 0) {
      console.log('\nエラー:');
      result.errors.forEach(error => console.log(`  - ${error}`));
    }

    // 3. 在庫状況の詳細確認
    console.log('\n3. 在庫状況の詳細確認中...');
    const inventoryResponse = await admin.graphql(`
      query getProductInventoryDetails($id: ID!) {
        product(id: $id) {
          title
          variants(first: 20) {
            edges {
              node {
                id
                title
                sku
                inventoryItem {
                  id
                  tracked
                  requiresShipping
                  inventoryLevels(first: 5) {
                    edges {
                      node {
                        id
                        available
                        committed
                        incoming
                        onHand
                        location {
                          id
                          name
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    `, {
      id: productId
    });

    const inventoryData = await inventoryResponse.json();
    
    if (inventoryData.errors) {
      throw new Error(`在庫確認エラー: ${JSON.stringify(inventoryData.errors)}`);
    }

    const product = inventoryData.data.product;
    console.log(`\n📦 商品: ${product.title}`);
    console.log(`総バリエーション数: ${product.variants.edges.length}`);
    
    let totalInventorySet = 0;
    let trackedVariants = 0;
    
    product.variants.edges.forEach((variantEdge: any, index: number) => {
      const variant = variantEdge.node;
      console.log(`\n  ${index + 1}. ${variant.title} (SKU: ${variant.sku})`);
      console.log(`     InventoryItem ID: ${variant.inventoryItem.id}`);
      console.log(`     在庫追跡: ${variant.inventoryItem.tracked ? '✅ ON' : '❌ OFF'}`);
      console.log(`     配送必要: ${variant.inventoryItem.requiresShipping ? 'YES' : 'NO'}`);
      
      if (variant.inventoryItem.tracked) {
        trackedVariants++;
      }
      
      const inventoryLevels = variant.inventoryItem.inventoryLevels.edges;
      console.log(`     在庫レベル数: ${inventoryLevels.length}`);
      
      if (inventoryLevels.length === 0) {
        console.log(`     ⚠️ 在庫レベルが設定されていません`);
      } else {
        inventoryLevels.forEach((levelEdge: any) => {
          const level = levelEdge.node;
          console.log(`     📍 ${level.location.name}:`);
          console.log(`        - 利用可能: ${level.available}個`);
          console.log(`        - 予約済み: ${level.committed}個`);
          console.log(`        - 入荷予定: ${level.incoming}個`);
          console.log(`        - 手持ち: ${level.onHand}個`);
          
          if (level.available > 0) {
            totalInventorySet++;
          }
        });
      }
    });

    // 4. 結果評価
    console.log('\n=== テスト結果評価 ===');
    const expectedVariants = 8; // 1D, 2D, 3D, 4D, 5D, 6D, 7D, 8D+
    const actualVariants = product.variants.edges.length;
    
    console.log(`バリエーション作成: ${actualVariants}/${expectedVariants} ${actualVariants === expectedVariants ? '✅' : '❌'}`);
    console.log(`在庫追跡有効: ${trackedVariants}/${actualVariants} ${trackedVariants === actualVariants ? '✅' : '❌'}`);
    console.log(`在庫設定済み: ${totalInventorySet}個のバリエーション ${totalInventorySet > 0 ? '✅' : '❌'}`);

    if (actualVariants === expectedVariants && trackedVariants === actualVariants && totalInventorySet > 0) {
      console.log('\n🎉 すべてのテストが成功しました！');
      console.log('✅ バリエーション作成: 完璧');
      console.log('✅ 在庫追跡: 完璧');
      console.log('✅ 在庫設定: 完璧');
    } else {
      console.log('\n⚠️ 一部のテストに問題があります');
      if (trackedVariants < actualVariants) {
        console.log('❌ 在庫追跡が無効なバリエーションがあります');
      }
      if (totalInventorySet === 0) {
        console.log('❌ 在庫が設定されていません');
      }
    }

    // 5. クリーンアップ
    console.log('\n5. テスト商品を削除中...');
    await admin.graphql(`
      mutation productDelete($input: ProductDeleteInput!) {
        productDelete(input: $input) {
          deletedProductId
          userErrors {
            field
            message
          }
        }
      }
    `, {
      input: {
        id: productId
      }
    });

    console.log('✅ テスト商品削除完了');

  } catch (error) {
    console.error('❌ テストエラー:', error);
  }

  console.log('\n=== 単一商品在庫設定修正テスト完了 ===');
}

// スクリプト実行
if (import.meta.url === new URL(import.meta.url).href) {
  testSingleInventoryFix().catch(console.error);
}

export { testSingleInventoryFix };
