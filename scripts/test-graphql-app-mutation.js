/**
 * Shopify GraphQL Appでテストするためのコード
 * 
 * このスクリプトは、Shopify Admin APIを使用して在庫を更新するテストコードです。
 * GraphQL Appでコピー&ペーストして実行することができます。
 */

// 在庫を更新するミューテーション
// GraphQL Appで実行するためのクエリ
const updateInventoryMutation = `
  mutation inventoryAdjustQuantities($input: InventoryAdjustQuantitiesInput!) {
    inventoryAdjustQuantities(input: $input) {
      inventoryAdjustmentGroup {
        id
        createdAt
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 在庫を1に設定するための変数
// 商品のバリアントIDとInventoryItemIDを適宜変更してください
const variables = {
  "input": {
    "reason": "在庫調整",
    "name": "テスト用在庫設定",
    "changes": [
      {
        "inventoryItemId": "gid://shopify/InventoryItem/47887775465640",
        "availableDelta": 1,
        "locationId": "gid://shopify/Location/1"
      },
      {
        "inventoryItemId": "gid://shopify/InventoryItem/47887775498408",
        "availableDelta": 1,
        "locationId": "gid://shopify/Location/1"
      },
      {
        "inventoryItemId": "gid://shopify/InventoryItem/47887775531176",
        "availableDelta": 1,
        "locationId": "gid://shopify/Location/1"
      }
      // 必要に応じて他のバリアントも追加
    ]
  }
};

// 商品を取得するクエリ
// GraphQL Appで実行するためのクエリ
const getProductsQuery = `
  query getProducts($first: Int!) {
    products(first: $first) {
      edges {
        node {
          id
          title
          variants(first: 10) {
            edges {
              node {
                id
                title
                inventoryQuantity
                inventoryItem {
                  id
                }
              }
            }
          }
        }
      }
    }
  }
`;

// 商品を取得するための変数
const getProductsVariables = {
  "first": 5
};

// 商品の在庫を確認するクエリ
// GraphQL Appで実行するためのクエリ
const checkInventoryQuery = `
  query getInventoryLevels($inventoryItemIds: [ID!]!) {
    inventoryItems(first: 10, query: "id:") {
      edges {
        node {
          id
          inventoryLevels(first: 1) {
            edges {
              node {
                id
                available
                location {
                  id
                  name
                }
              }
            }
          }
        }
      }
    }
  }
`;

// 在庫を確認するための変数
const checkInventoryVariables = {
  "inventoryItemIds": [
    "gid://shopify/InventoryItem/47887775465640",
    "gid://shopify/InventoryItem/47887775498408",
    "gid://shopify/InventoryItem/47887775531176"
  ]
};

// 特定の商品の在庫を更新するミューテーション
// GraphQL Appで実行するためのクエリ
const updateSingleProductInventoryMutation = `
  mutation inventoryAdjustQuantities($input: InventoryAdjustQuantitiesInput!) {
    inventoryAdjustQuantities(input: $input) {
      inventoryAdjustmentGroup {
        id
        createdAt
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 特定の商品の在庫を1に設定するための変数
// 商品のInventoryItemIDを適宜変更してください
const updateSingleProductVariables = {
  "input": {
    "reason": "在庫調整",
    "name": "単一商品テスト用在庫設定",
    "changes": [
      {
        "inventoryItemId": "gid://shopify/InventoryItem/47887775465640",
        "availableDelta": 1,
        "locationId": "gid://shopify/Location/1"
      }
    ]
  }
};

// 商品のメタフィールドを更新するミューテーション
// GraphQL Appで実行するためのクエリ
const updateMetafieldsMutation = `
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 商品のメタフィールドを更新するための変数
// 商品IDを適宜変更してください
const updateMetafieldsVariables = {
  "metafields": [
    {
      "ownerId": "gid://shopify/Product/8346431971496",
      "namespace": "rental",
      "key": "status",
      "value": "available",
      "type": "single_line_text_field"
    },
    {
      "ownerId": "gid://shopify/Product/8346431971496",
      "namespace": "rental",
      "key": "location",
      "value": "NY",
      "type": "single_line_text_field"
    }
  ]
};

// 商品のメタフィールドを取得するクエリ
// GraphQL Appで実行するためのクエリ
const getMetafieldsQuery = `
  query getProductMetafields($productId: ID!) {
    product(id: $productId) {
      id
      title
      metafields(first: 20) {
        edges {
          node {
            id
            namespace
            key
            value
          }
        }
      }
    }
  }
`;

// 商品のメタフィールドを取得するための変数
// 商品IDを適宜変更してください
const getMetafieldsVariables = {
  "productId": "gid://shopify/Product/8346431971496"
};

// 8日以上のレンタル料金計算のテスト関数
// JavaScriptコンソールで実行できます
function calculateRentalPrice(basePrice, days) {
  let totalPrice = basePrice; // 1日目は基本料金
  
  if (days > 1) {
    const day2to6Count = Math.min(days - 1, 5); // 最大5日間（2日目〜6日目）
    totalPrice += basePrice * 0.2 * day2to6Count;
  }
  
  if (days > 6) {
    const day7PlusCount = days - 6;
    totalPrice += basePrice * 0.1 * day7PlusCount;
  }
  
  return Math.round(totalPrice);
}

// 料金計算のテスト例
// JavaScriptコンソールで実行できます
const testPriceCalculation = () => {
  const basePrice = 10000;
  
  console.log(`基本料金: ${basePrice}円`);
  console.log(`1日間レンタル: ${calculateRentalPrice(basePrice, 1)}円`); // 10000円
  console.log(`2日間レンタル: ${calculateRentalPrice(basePrice, 2)}円`); // 12000円
  console.log(`3日間レンタル: ${calculateRentalPrice(basePrice, 3)}円`); // 14000円
  console.log(`7日間レンタル: ${calculateRentalPrice(basePrice, 7)}円`); // 21000円
  console.log(`8日間レンタル: ${calculateRentalPrice(basePrice, 8)}円`); // 22000円
  console.log(`9日間レンタル: ${calculateRentalPrice(basePrice, 9)}円`); // 23000円
  console.log(`10日間レンタル: ${calculateRentalPrice(basePrice, 10)}円`); // 24000円
};

// 使用方法
// 1. Shopify管理画面 > Apps > GraphiQL App を開く
// 2. 上記のクエリとミューテーションをコピー&ペーストして実行
// 3. 必要に応じて変数を変更して実行

// 実行順序の例
// 1. getProductsQueryを実行して商品とInventoryItemIDを取得
// 2. updateInventoryMutationを実行して在庫を更新
// 3. checkInventoryQueryを実行して在庫が更新されたことを確認
// 4. getMetafieldsQueryを実行して商品のメタフィールドを確認
// 5. updateMetafieldsMutationを実行してメタフィールドを更新
// 6. JavaScriptコンソールでtestPriceCalculation()を実行して料金計算をテスト
