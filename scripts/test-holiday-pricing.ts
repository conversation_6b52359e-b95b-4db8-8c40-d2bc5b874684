/**
 * 休日・特別日の料金計算テスト
 *
 * このスクリプトは、休日や特別日の料金計算が正しく行われることをテストします。
 * 実行方法: npx tsx scripts/test-holiday-pricing.ts [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { addDays, format, parseISO } from 'date-fns';
import { ja } from 'date-fns/locale';
import { calculateRentalPrice } from '../app/utils/pricing';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト設定
const config = {
  // テストする商品のShopify ID
  productId: process.argv[2] || '123456789', // コマンドライン引数から取得、または既定値を使用
  
  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
  
  // 基本料金
  basePrice: 10000,
  
  // 休日リスト（テスト用）
  holidays: [
    '2025-01-01', // 元日
    '2025-01-13', // 成人の日
    '2025-02-11', // 建国記念の日
    '2025-02-23', // 天皇誕生日
    '2025-03-21', // 春分の日
    '2025-04-29', // 昭和の日
    '2025-05-03', // 憲法記念日
    '2025-05-04', // みどりの日
    '2025-05-05', // こどもの日
    '2025-07-21', // 海の日
    '2025-08-11', // 山の日
    '2025-09-15', // 敬老の日
    '2025-09-23', // 秋分の日
    '2025-10-13', // スポーツの日
    '2025-11-03', // 文化の日
    '2025-11-23', // 勤労感謝の日
    '2025-12-23', // 天皇誕生日
    '2025-12-29', // 年末休業
    '2025-12-30', // 年末休業
    '2025-12-31'  // 年末休業
  ],
  
  // 特別料金日リスト（テスト用）
  specialDays: [
    { date: '2025-05-01', rate: 1.5, name: 'ゴールデンウィーク' },
    { date: '2025-05-02', rate: 1.5, name: 'ゴールデンウィーク' },
    { date: '2025-08-13', rate: 2.0, name: 'お盆' },
    { date: '2025-08-14', rate: 2.0, name: 'お盆' },
    { date: '2025-08-15', rate: 2.0, name: 'お盆' },
    { date: '2025-12-24', rate: 1.5, name: 'クリスマス' },
    { date: '2025-12-25', rate: 1.5, name: 'クリスマス' }
  ]
};

/**
 * 商品情報を取得する関数
 */
async function getProductInfo(shopifyProductId: string) {
  try {
    // Prismaから商品情報を取得
    const product = await prisma.product.findFirst({
      where: { shopifyId: shopifyProductId }
    });
    
    if (!product) {
      console.error(`商品ID ${shopifyProductId} が見つかりません`);
      return null;
    }
    
    return product;
  } catch (error) {
    console.error('商品情報取得エラー:', error);
    return null;
  }
}

/**
 * 料金計算をテストする関数
 */
async function testPricing(shopifyProductId: string) {
  try {
    console.log(`商品ID ${shopifyProductId} の料金計算テストを実行します...`);
    
    // 商品情報を取得
    const product = await getProductInfo(shopifyProductId);
    
    if (!product) {
      return false;
    }
    
    // 料金情報を取得
    let pricingInfo = null;
    try {
      if (product.pricing) {
        pricingInfo = typeof product.pricing === 'string' 
          ? JSON.parse(product.pricing as string) 
          : product.pricing;
      }
    } catch (error) {
      console.warn(`料金情報解析エラー (ID: ${shopifyProductId}):`, error);
      // エラーがあっても処理を続行
    }
    
    // 基本料金の設定
    const basePrice = product.price || config.basePrice;
    
    console.log(`\n商品名: ${product.title}`);
    console.log(`基本料金: ${basePrice}円`);
    
    // テストケースを定義
    const testCases = [
      {
        name: '通常日の1日レンタル',
        startDate: new Date('2025-06-10'),
        endDate: new Date('2025-06-10'),
        expectedDays: 1
      },
      {
        name: '通常日の3日レンタル',
        startDate: new Date('2025-06-10'),
        endDate: new Date('2025-06-12'),
        expectedDays: 3
      },
      {
        name: '週末を含む3日レンタル',
        startDate: new Date('2025-06-13'), // 金曜日
        endDate: new Date('2025-06-15'), // 日曜日
        expectedDays: 3
      },
      {
        name: '祝日を含む3日レンタル',
        startDate: new Date('2025-05-02'),
        endDate: new Date('2025-05-04'), // 憲法記念日、みどりの日
        expectedDays: 3
      },
      {
        name: '特別料金日を含む3日レンタル',
        startDate: new Date('2025-08-12'),
        endDate: new Date('2025-08-14'), // お盆期間
        expectedDays: 3
      },
      {
        name: '年末年始を含む7日レンタル',
        startDate: new Date('2025-12-28'),
        endDate: new Date('2026-01-03'), // 年末年始
        expectedDays: 7
      },
      {
        name: '長期レンタル（10日間）',
        startDate: new Date('2025-06-10'),
        endDate: new Date('2025-06-19'),
        expectedDays: 10
      }
    ];
    
    // 各テストケースを実行
    console.log('\n===== 料金計算テスト =====');
    
    for (const testCase of testCases) {
      console.log(`\n----- ${testCase.name} -----`);
      console.log(`期間: ${format(testCase.startDate, 'yyyy/MM/dd(E)', { locale: ja })} 〜 ${format(testCase.endDate, 'yyyy/MM/dd(E)', { locale: ja })}`);
      console.log(`予想日数: ${testCase.expectedDays}日`);
      
      // 休日情報を設定
      const holidayConfig = {
        holidays: config.holidays.map(date => parseISO(date)),
        specialDays: config.specialDays.map(day => ({
          date: parseISO(day.date),
          rate: day.rate,
          name: day.name
        }))
      };
      
      // 料金を計算
      const price = calculateRentalPrice(
        testCase.startDate,
        testCase.endDate,
        basePrice,
        pricingInfo,
        holidayConfig
      );
      
      console.log(`計算結果: ${price.toLocaleString()}円`);
      
      // 日ごとの料金内訳を表示
      let currentDate = new Date(testCase.startDate);
      const endDate = new Date(testCase.endDate);
      
      console.log('\n日ごとの料金内訳:');
      while (currentDate <= endDate) {
        const dateStr = format(currentDate, 'yyyy/MM/dd(E)', { locale: ja });
        const isHoliday = config.holidays.includes(format(currentDate, 'yyyy-MM-dd'));
        const specialDay = config.specialDays.find(day => day.date === format(currentDate, 'yyyy-MM-dd'));
        
        let dayType = '通常日';
        let rateInfo = '';
        
        if (isHoliday) {
          dayType = '休日';
          rateInfo = '(休日料金)';
        } else if (specialDay) {
          dayType = '特別日';
          rateInfo = `(${specialDay.name}: ${specialDay.rate}倍)`;
        } else if (currentDate.getDay() === 0 || currentDate.getDay() === 6) {
          dayType = '週末';
          rateInfo = '(週末料金)';
        }
        
        console.log(`${dateStr}: ${dayType} ${rateInfo}`);
        
        // 次の日に進む
        currentDate.setDate(currentDate.getDate() + 1);
      }
    }
    
    return true;
  } catch (error) {
    console.error('料金計算テストエラー:', error);
    return false;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('休日・特別日の料金計算テストを開始します...');
    
    // コマンドライン引数から商品IDを取得
    const shopifyProductId = config.productId;
    
    // 料金計算をテスト
    const testResult = await testPricing(shopifyProductId);
    
    // テスト結果のサマリー
    console.log('\n===== テスト結果サマリー =====');
    console.log(`料金計算テスト: ${testResult ? '成功' : '失敗'}`);
    
    if (testResult) {
      console.log('\nテストが成功しました！');
    } else {
      console.error('\nテストが失敗しました。');
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
