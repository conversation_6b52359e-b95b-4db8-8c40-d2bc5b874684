/**
 * 予約可能性チェックスクリプト
 * 
 * 特定商品の予約状況と在庫カレンダーを詳細チェック
 */

import { PrismaClient } from '@prisma/client';
import { format, addDays } from 'date-fns';

const prisma = new PrismaClient();

async function checkBookingAvailability() {
  const sku = process.argv[2] || '201-07-107';
  
  console.log(`🔍 商品 ${sku} の予約可能性チェックを開始...\n`);

  try {
    // 商品情報を取得
    const product = await prisma.product.findFirst({
      where: { sku: sku }
    });

    if (!product) {
      console.log(`❌ SKU ${sku} の商品が見つかりません`);
      return;
    }

    console.log('=== 商品情報 ===');
    console.log(`ID: ${product.id}`);
    console.log(`タイトル: ${product.title}`);
    console.log(`SKU: ${product.sku}`);
    console.log(`ステータス: ${product.status}`);
    console.log(`Shopify ID: ${product.shopifyId}`);
    console.log('');

    // 予約状況を確認
    console.log('=== 予約状況 ===');
    const bookings = await prisma.booking.findMany({
      where: {
        productId: product.id
      },
      orderBy: { startDate: 'asc' }
    });

    console.log(`総予約件数: ${bookings.length}件`);
    
    if (bookings.length === 0) {
      console.log('✅ 予約はありません');
    } else {
      bookings.forEach((booking, index) => {
        console.log(`${index + 1}. ${booking.bookingId}`);
        console.log(`   期間: ${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')}`);
        console.log(`   ステータス: ${booking.status}`);
        console.log(`   顧客: ${booking.customerName}`);
        console.log(`   作成日: ${format(booking.createdAt, 'yyyy-MM-dd HH:mm:ss')}`);
      });
    }

    // 現在有効な予約（PROVISIONAL, CONFIRMED）
    const activeBookings = bookings.filter(b => 
      ['PROVISIONAL', 'CONFIRMED'].includes(b.status) && 
      new Date(b.endDate) >= new Date()
    );

    console.log(`\n現在有効な予約: ${activeBookings.length}件`);
    activeBookings.forEach((booking, index) => {
      console.log(`${index + 1}. ${booking.bookingId} (${booking.status})`);
      console.log(`   期間: ${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')}`);
    });

    console.log('\n=== メンテナンス状況 ===');
    const maintenances = await prisma.maintenance.findMany({
      where: {
        productId: product.id
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`メンテナンス記録: ${maintenances.length}件`);
    
    if (maintenances.length === 0) {
      console.log('✅ メンテナンス記録はありません');
    } else {
      maintenances.forEach((maintenance, index) => {
        console.log(`${index + 1}. ${maintenance.id}`);
        console.log(`   ステータス: ${maintenance.status}`);
        console.log(`   期間: ${format(maintenance.startDate, 'yyyy-MM-dd')} 〜 ${maintenance.endDate ? format(maintenance.endDate, 'yyyy-MM-dd') : '未設定'}`);
        console.log(`   タイプ: ${maintenance.type}`);
        console.log(`   備考: ${maintenance.notes || 'なし'}`);
      });
    }

    // 進行中のメンテナンス
    const activeMaintenances = maintenances.filter(m => m.status === 'IN_PROGRESS');
    console.log(`\n進行中のメンテナンス: ${activeMaintenances.length}件`);

    console.log('\n=== 在庫カレンダー状況 ===');
    const today = new Date();
    const futureDate = addDays(today, 14);

    const inventoryEntries = await prisma.inventoryCalendar.findMany({
      where: {
        productId: product.id,
        date: { gte: today, lte: futureDate }
      },
      orderBy: { date: 'asc' }
    });

    console.log(`在庫カレンダー（今後14日間）: ${inventoryEntries.length}件`);
    
    if (inventoryEntries.length === 0) {
      console.log('✅ 在庫カレンダーエントリはありません（すべて利用可能）');
    } else {
      inventoryEntries.forEach(entry => {
        const dateStr = format(entry.date, 'yyyy-MM-dd');
        const status = entry.isAvailable ? '✅ 利用可能' : '❌ 利用不可';
        const reason = entry.unavailableReason ? ` (${entry.unavailableReason})` : '';
        const maintenanceId = entry.maintenanceId ? ` [M:${entry.maintenanceId.slice(-8)}]` : '';
        console.log(`  ${dateStr}: ${status}${reason}${maintenanceId}`);
      });
    }

    // 利用不可の日数をカウント
    const unavailableDates = inventoryEntries.filter(entry => !entry.isAvailable);
    console.log(`\n利用不可日数: ${unavailableDates.length}日`);

    // 理由別の集計
    const reasonCounts = {};
    unavailableDates.forEach(entry => {
      const reason = entry.unavailableReason || 'UNKNOWN';
      reasonCounts[reason] = (reasonCounts[reason] || 0) + 1;
    });

    if (Object.keys(reasonCounts).length > 0) {
      console.log('利用不可理由別:');
      Object.entries(reasonCounts).forEach(([reason, count]) => {
        console.log(`  ${reason}: ${count}日`);
      });
    }

    console.log('\n=== 予約可能性分析 ===');
    
    // 今日から3日後の期間で予約可能かチェック
    const testStartDate = addDays(today, 1);
    const testEndDate = addDays(today, 3);
    
    console.log(`テスト期間: ${format(testStartDate, 'yyyy-MM-dd')} 〜 ${format(testEndDate, 'yyyy-MM-dd')}`);

    // 該当期間の在庫カレンダーをチェック
    const testPeriodInventory = await prisma.inventoryCalendar.findMany({
      where: {
        productId: product.id,
        date: { gte: testStartDate, lte: testEndDate }
      }
    });

    const unavailableInTestPeriod = testPeriodInventory.filter(entry => !entry.isAvailable);
    
    if (unavailableInTestPeriod.length > 0) {
      console.log(`❌ テスト期間中に${unavailableInTestPeriod.length}日間利用不可`);
      unavailableInTestPeriod.forEach(entry => {
        console.log(`  ${format(entry.date, 'yyyy-MM-dd')}: ${entry.unavailableReason}`);
      });
    } else {
      console.log(`✅ テスト期間中は利用可能`);
    }

    // 予約競合チェック
    const conflictingBookings = activeBookings.filter(booking => {
      const bookingStart = new Date(booking.startDate);
      const bookingEnd = new Date(booking.endDate);
      return (
        (bookingStart <= testEndDate && bookingEnd >= testStartDate)
      );
    });

    if (conflictingBookings.length > 0) {
      console.log(`❌ テスト期間中に${conflictingBookings.length}件の予約競合`);
      conflictingBookings.forEach(booking => {
        console.log(`  ${booking.bookingId}: ${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')}`);
      });
    } else {
      console.log(`✅ テスト期間中に予約競合なし`);
    }

    // メンテナンス競合チェック
    const conflictingMaintenances = activeMaintenances.filter(maintenance => {
      const maintenanceStart = new Date(maintenance.startDate);
      const maintenanceEnd = maintenance.endDate ? new Date(maintenance.endDate) : new Date('2099-12-31');
      return (
        (maintenanceStart <= testEndDate && maintenanceEnd >= testStartDate)
      );
    });

    if (conflictingMaintenances.length > 0) {
      console.log(`❌ テスト期間中に${conflictingMaintenances.length}件のメンテナンス競合`);
      conflictingMaintenances.forEach(maintenance => {
        console.log(`  ${maintenance.id}: ${format(maintenance.startDate, 'yyyy-MM-dd')} 〜 ${maintenance.endDate ? format(maintenance.endDate, 'yyyy-MM-dd') : '未設定'}`);
      });
    } else {
      console.log(`✅ テスト期間中にメンテナンス競合なし`);
    }

    // 総合判定
    console.log('\n=== 総合判定 ===');
    const canBook = (
      unavailableInTestPeriod.length === 0 &&
      conflictingBookings.length === 0 &&
      conflictingMaintenances.length === 0 &&
      product.status !== 'ARCHIVED' &&
      product.status !== 'DELETED'
    );

    if (canBook) {
      console.log('✅ この商品は予約可能です');
    } else {
      console.log('❌ この商品は予約不可です');
      console.log('理由:');
      if (unavailableInTestPeriod.length > 0) {
        console.log(`  - 在庫カレンダーで利用不可: ${unavailableInTestPeriod.length}日`);
      }
      if (conflictingBookings.length > 0) {
        console.log(`  - 予約競合: ${conflictingBookings.length}件`);
      }
      if (conflictingMaintenances.length > 0) {
        console.log(`  - メンテナンス競合: ${conflictingMaintenances.length}件`);
      }
      if (product.status === 'ARCHIVED' || product.status === 'DELETED') {
        console.log(`  - 商品ステータス: ${product.status}`);
      }
    }

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
checkBookingAvailability();
