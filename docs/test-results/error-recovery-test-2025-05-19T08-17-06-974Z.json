{"timestamp": "2025-05-19T08:17:04.383Z", "duration": 2.591, "totalTests": 3, "successCount": 3, "failureCount": 0, "tests": [{"testName": "API障害からの回復テスト", "success": true, "timestamp": "2025-05-19T08:17:04.783Z", "message": "API障害から正常に回復しました", "error": "", "duration": 400}, {"testName": "ネットワーク切断からの回復テスト", "success": true, "timestamp": "2025-05-19T08:17:04.798Z", "message": "ネットワーク切断から正常に回復しました", "error": "", "duration": 14}, {"testName": "データ不整合の自動修復テスト", "success": true, "timestamp": "2025-05-19T08:17:06.972Z", "message": "データ不整合を正常に修復しました", "error": "", "duration": 2174}], "environment": {"nodeVersion": "v20.18.1", "os": "darwin arm64"}, "performance": {"averageTestDuration": 862.6666666666666, "slowestTest": {"name": "データ不整合の自動修復テスト", "duration": 2174}, "fastestTest": {"name": "ネットワーク切断からの回復テスト", "duration": 14}}}