/**
 * 商品詳細ページ
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { 
  Page, 
  Layout, 
  Card, 
  Text, 
  Badge, 
  Tabs,
  LegacyStack,
  Button
} from "@shopify/polaris";
import { useState, useCallback } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import BookingUpgradeRequestForm from "../components/BookingUpgradeRequestForm";
import { format } from "date-fns";
import { ja } from "date-fns/locale";

/**
 * ローダー関数
 */
export async function loader({ request, params }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  
  const { id } = params;
  
  if (!id) {
    throw new Response("商品IDが指定されていません", { status: 400 });
  }
  
  // 商品情報を取得
  const product = await prisma.product.findUnique({
    where: { id },
    include: {
      bookings: {
        where: {
          status: {
            in: ['CONFIRMED', 'PROVISIONAL']
          },
          endDate: {
            gte: new Date()
          }
        },
        orderBy: {
          startDate: 'asc'
        }
      },
      maintenances: {
        where: {
          endDate: {
            gte: new Date()
          }
        },
        orderBy: {
          startDate: 'asc'
        }
      }
    }
  });
  
  if (!product) {
    throw new Response("商品が見つかりません", { status: 404 });
  }
  
  // 仮予約の数を取得
  const provisionalBookingsCount = product.bookings.filter(
    booking => booking.bookingType === 'PROVISIONAL'
  ).length;
  
  return json({
    product: {
      id: product.id,
      shopifyId: product.shopifyId,
      title: product.title,
      description: product.description,
      sku: product.sku,
      status: product.status,
      price: product.price?.toString() || '',
      locationId: product.locationId,
      basicInfo: product.basicInfo,
      maintenanceInfo: product.maintenanceInfo,
      createdAt: product.createdAt.toISOString(),
      updatedAt: product.updatedAt.toISOString()
    },
    bookings: product.bookings.map(booking => ({
      id: booking.id,
      bookingId: booking.bookingId,
      startDate: booking.startDate.toISOString(),
      endDate: booking.endDate.toISOString(),
      customerName: booking.customerName,
      customerEmail: booking.customerEmail,
      bookingType: booking.bookingType,
      status: booking.status
    })),
    maintenances: product.maintenances.map(maintenance => ({
      id: maintenance.id,
      startDate: maintenance.startDate.toISOString(),
      endDate: maintenance.endDate?.toISOString() || null,
      type: maintenance.type,
      status: maintenance.status,
      notes: maintenance.notes
    })),
    provisionalBookingsCount
  });
}

/**
 * 商品詳細ページコンポーネント
 */
export default function ProductDetail() {
  const { product, bookings, maintenances, provisionalBookingsCount } = useLoaderData<typeof loader>();
  const [selected, setSelected] = useState(0);

  const handleTabChange = useCallback(
    (selectedTabIndex: number) => setSelected(selectedTabIndex),
    [],
  );

  const tabs = [
    {
      id: 'details',
      content: '商品詳細',
      accessibilityLabel: '商品詳細',
      panelID: 'details-panel',
    },
    {
      id: 'bookings',
      content: `予約 (${bookings.length})`,
      accessibilityLabel: '予約',
      panelID: 'bookings-panel',
    },
    {
      id: 'maintenances',
      content: `メンテナンス (${maintenances.length})`,
      accessibilityLabel: 'メンテナンス',
      panelID: 'maintenances-panel',
    },
  ];

  // 商品ステータスに応じたバッジを取得
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return <Badge status="success">利用可能</Badge>;
      case 'MAINTENANCE':
        return <Badge status="attention">メンテナンス中</Badge>;
      case 'DAMAGED':
        return <Badge status="warning">破損</Badge>;
      case 'UNAVAILABLE':
        return <Badge status="critical">利用不可</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // 予約タイプに応じたバッジを取得
  const getBookingTypeBadge = (type: string) => {
    switch (type) {
      case 'PROVISIONAL':
        return <Badge status="attention">仮予約</Badge>;
      case 'CONFIRMED':
        return <Badge status="success">本予約</Badge>;
      default:
        return <Badge>{type}</Badge>;
    }
  };

  // 日付をフォーマット
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'yyyy年MM月dd日', { locale: ja });
  };

  return (
    <Page
      title={product.title}
      backAction={{ content: '商品一覧へ戻る', url: '/app/products' }}
      primaryAction={{ content: '編集', url: `/app/products/edit/${product.id}` }}
      secondaryActions={[
        {
          content: 'メンテナンス登録',
          url: `/app/maintenance/new?productId=${product.id}`
        }
      ]}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Tabs tabs={tabs} selected={selected} onSelect={handleTabChange}>
              <Card.Section>
                {selected === 0 && (
                  <div>
                    <LegacyStack vertical spacing="loose">
                      <LegacyStack distribution="equalSpacing">
                        <Text variant="headingMd">基本情報</Text>
                        {getStatusBadge(product.status)}
                      </LegacyStack>
                      
                      <LegacyStack>
                        <LegacyStack.Item fill>
                          <Text variant="bodyMd" fontWeight="bold">SKU</Text>
                          <Text variant="bodyMd">{product.sku}</Text>
                        </LegacyStack.Item>
                        <LegacyStack.Item fill>
                          <Text variant="bodyMd" fontWeight="bold">価格</Text>
                          <Text variant="bodyMd">¥{parseInt(product.price).toLocaleString()}</Text>
                        </LegacyStack.Item>
                        <LegacyStack.Item fill>
                          <Text variant="bodyMd" fontWeight="bold">場所</Text>
                          <Text variant="bodyMd">{product.locationId || '未設定'}</Text>
                        </LegacyStack.Item>
                      </LegacyStack>
                      
                      {product.description && (
                        <div>
                          <Text variant="bodyMd" fontWeight="bold">説明</Text>
                          <Text variant="bodyMd">{product.description}</Text>
                        </div>
                      )}
                    </LegacyStack>
                  </div>
                )}
                
                {selected === 1 && (
                  <div>
                    {bookings.length === 0 ? (
                      <Text variant="bodyMd">予約はありません</Text>
                    ) : (
                      <div>
                        {bookings.map((booking) => (
                          <div key={booking.id} style={{ marginBottom: '1rem', padding: '1rem', border: '1px solid #ddd', borderRadius: '4px' }}>
                            <LegacyStack distribution="equalSpacing">
                              <Text variant="bodyMd" fontWeight="bold">予約番号: {booking.bookingId}</Text>
                              {getBookingTypeBadge(booking.bookingType)}
                            </LegacyStack>
                            <div style={{ marginTop: '0.5rem' }}>
                              <Text variant="bodyMd">期間: {formatDate(booking.startDate)} 〜 {formatDate(booking.endDate)}</Text>
                            </div>
                            <div style={{ marginTop: '0.5rem' }}>
                              <Text variant="bodyMd">顧客: {booking.customerName || '未設定'} ({booking.customerEmail || '未設定'})</Text>
                            </div>
                            
                            {booking.bookingType === 'PROVISIONAL' && (
                              <div style={{ marginTop: '1rem' }}>
                                <BookingUpgradeRequestForm
                                  booking={booking}
                                  product={{
                                    id: product.id,
                                    title: product.title,
                                    price: parseInt(product.price)
                                  }}
                                />
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
                
                {selected === 2 && (
                  <div>
                    {maintenances.length === 0 ? (
                      <Text variant="bodyMd">メンテナンス予定はありません</Text>
                    ) : (
                      <div>
                        {maintenances.map((maintenance) => (
                          <div key={maintenance.id} style={{ marginBottom: '1rem', padding: '1rem', border: '1px solid #ddd', borderRadius: '4px' }}>
                            <LegacyStack distribution="equalSpacing">
                              <Text variant="bodyMd" fontWeight="bold">
                                {maintenance.type === 'REGULAR' ? '定期メンテナンス' : '緊急メンテナンス'}
                              </Text>
                              <Badge status={maintenance.status === 'SCHEDULED' ? 'attention' : 'success'}>
                                {maintenance.status === 'SCHEDULED' ? '予定' : '完了'}
                              </Badge>
                            </LegacyStack>
                            <div style={{ marginTop: '0.5rem' }}>
                              <Text variant="bodyMd">
                                期間: {formatDate(maintenance.startDate)} 〜 {maintenance.endDate ? formatDate(maintenance.endDate) : '未定'}
                              </Text>
                            </div>
                            {maintenance.notes && (
                              <div style={{ marginTop: '0.5rem' }}>
                                <Text variant="bodyMd">備考: {maintenance.notes}</Text>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </Card.Section>
            </Tabs>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
