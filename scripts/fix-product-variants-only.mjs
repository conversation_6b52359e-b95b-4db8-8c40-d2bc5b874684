import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2025-01';

// GraphQLクライアントの設定
const client = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品情報
const PRODUCT_ID = "gid://shopify/Product/8982368747688";
const BASE_SKU = "10301009-001";
const BASE_PRICE = 700; // 基本価格
const INVENTORY_QUANTITY = 1; // 在庫数

// バリアントマッピング定義
const VARIANT_MAPPING = {
  "1D": { price: 700, deposit: 0 },
  "2D": { price: 1050, deposit: 0 },
  "3D": { price: 1400, deposit: 0 },
  "4D": { price: 1750, deposit: 0 },
  "5D": { price: 2100, deposit: 0 },
  "6D": { price: 2450, deposit: 0 },
  "7D": { price: 2800, deposit: 0 },
  "8D+": { price: 2800, deposit: 0 },
  "PROV": { price: 70, deposit: 0 } // 仮予約は10%
};

// レンタル期間のバリアント定義
const RENTAL_VARIANTS = [
  { title: "1日レンタル", suffix: "1D", days: 1 },
  { title: "2日レンタル", suffix: "2D", days: 2 },
  { title: "3日レンタル", suffix: "3D", days: 3 },
  { title: "4日レンタル", suffix: "4D", days: 4 },
  { title: "5日レンタル", suffix: "5D", days: 5 },
  { title: "6日レンタル", suffix: "6D", days: 6 },
  { title: "7日レンタル", suffix: "7D", days: 7 },
  { title: "8日以上レンタル", suffix: "8D+", days: 8 },
  { title: "仮予約", suffix: "PROV", days: 0 }
];

async function fixProduct() {
  try {
    console.log('=== 商品修正開始（簡易版） ===');
    console.log(`商品ID: ${PRODUCT_ID}`);
    console.log(`基本SKU: ${BASE_SKU}`);
    console.log(`基本価格: ¥${BASE_PRICE}`);
    console.log(`在庫数: ${INVENTORY_QUANTITY}\n`);

    // 1. 既存のデフォルトバリアントを更新
    console.log('1. 既存のバリアントを取得中...');
    const getVariantQuery = gql`
      query getVariant($id: ID!) {
        product(id: $id) {
          variants(first: 1) {
            edges {
              node {
                id
                title
                inventoryItem {
                  id
                }
              }
            }
          }
        }
      }
    `;

    const variantResult = await client.request(getVariantQuery, { id: PRODUCT_ID });
    const defaultVariant = variantResult.product.variants.edges[0]?.node;

    if (defaultVariant) {
      console.log(`デフォルトバリアントを更新: ${defaultVariant.id}`);
      
      // 最初のバリアントに更新
      const updateVariantMutation = gql`
        mutation updateVariant($input: ProductVariantInput!) {
          productVariantUpdate(input: $input) {
            productVariant {
              id
              title
              sku
              price
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const firstVariantInput = {
        id: defaultVariant.id,
        sku: `${BASE_SKU}-1D`,
        price: VARIANT_MAPPING["1D"].price.toString()
      };

      await client.request(updateVariantMutation, { input: firstVariantInput });
      console.log('✓ 最初のバリアント更新完了');

      // 在庫を有効化
      const enableInventoryMutation = gql`
        mutation enableInventory($inventoryItemId: ID!, $input: InventoryItemInput!) {
          inventoryItemUpdate(id: $inventoryItemId, input: $input) {
            inventoryItem {
              id
              tracked
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      await client.request(enableInventoryMutation, {
        inventoryItemId: defaultVariant.inventoryItem.id,
        input: { tracked: true }
      });

      // 在庫数を設定
      const setQuantityMutation = gql`
        mutation setQuantity($input: InventorySetQuantitiesInput!) {
          inventorySetQuantities(input: $input) {
            userErrors {
              field
              message
            }
          }
        }
      `;

      const quantityInput = {
        reason: "correction",
        name: "available",
        quantities: [{
          inventoryItemId: defaultVariant.inventoryItem.id,
          locationId: "gid://shopify/Location/78875197608", // NYロケーション
          quantity: INVENTORY_QUANTITY
        }]
      };

      await client.request(setQuantityMutation, { input: quantityInput });
      console.log('✓ 在庫設定完了');
    }

    // 2. 追加のバリアントを作成（2日以降）
    console.log('\n2. 追加バリアントを作成中...');
    const variantIds = { "1D": defaultVariant?.id };

    for (let i = 1; i < RENTAL_VARIANTS.length; i++) {
      const variant = RENTAL_VARIANTS[i];
      const createVariantMutation = gql`
        mutation createVariant($productId: ID!, $input: ProductVariantInput!) {
          productVariantCreate(productId: $productId, input: $input) {
            productVariant {
              id
              title
              sku
              price
              inventoryItem {
                id
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variantInput = {
        sku: `${BASE_SKU}-${variant.suffix}`,
        price: VARIANT_MAPPING[variant.suffix].price.toString(),
        inventoryPolicy: "DENY"
      };

      try {
        const result = await client.request(createVariantMutation, {
          productId: PRODUCT_ID,
          input: variantInput
        });

        if (result.productVariantCreate.userErrors.length > 0) {
          console.error(`エラー (${variant.title}):`, result.productVariantCreate.userErrors);
        } else {
          const createdVariant = result.productVariantCreate.productVariant;
          variantIds[variant.suffix] = createdVariant.id;
          console.log(`✓ ${variant.title}: ¥${VARIANT_MAPPING[variant.suffix].price} (SKU: ${createdVariant.sku})`);

          // 在庫を設定
          await client.request(setQuantityMutation, {
            input: {
              reason: "correction",
              name: "available",
              quantities: [{
                inventoryItemId: createdVariant.inventoryItem.id,
                locationId: "gid://shopify/Location/78875197608",
                quantity: INVENTORY_QUANTITY
              }]
            }
          });
        }
      } catch (error) {
        console.error(`バリアント作成エラー (${variant.title}):`, error.message);
      }
    }

    // 3. メタフィールドを設定
    console.log('\n3. メタフィールドを設定中...');
    const metafieldsSetMutation = gql`
      mutation setMetafields($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          metafields {
            id
            namespace
            key
            value
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const metafields = [
      {
        ownerId: PRODUCT_ID,
        namespace: "rental",
        key: "basic_info",
        value: JSON.stringify({
          product_code: "10301009",
          detail_code: "001",
          category_name: "椅子",
          kana: "ハーヴェイチェア"
        }),
        type: "json"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "rental",
        key: "pricing",
        value: JSON.stringify({
          base_price: BASE_PRICE,
          extra_day_rate: 350
        }),
        type: "json"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "rental",
        key: "variant_mapping",
        value: JSON.stringify(Object.entries(variantIds).map(([suffix, id]) => ({
          variant_id: id,
          rental_period: suffix,
          days: RENTAL_VARIANTS.find(v => v.suffix === suffix)?.days || 0
        }))),
        type: "json"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "rental",
        key: "status",
        value: JSON.stringify({
          enabled: true,
          maintenance_notes: ""
        }),
        type: "json"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "width",
        value: "450",
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "depth",
        value: "580",
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "height",
        value: "940",
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "variation_type",
        value: "rental_period",
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "booking_type",
        value: "日単位",
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "booking_enabled",
        value: "true",
        type: "single_line_text_field"
      }
    ];

    const metafieldsResult = await client.request(metafieldsSetMutation, { metafields });
    
    if (metafieldsResult.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', metafieldsResult.metafieldsSet.userErrors);
    } else {
      console.log('✓ メタフィールド設定完了');
      console.log(`  設定されたメタフィールド数: ${metafieldsResult.metafieldsSet.metafields.length}`);
    }

    console.log('\n=== 商品修正完了 ===');
    console.log('商品URL: https://admin.shopify.com/store/peaces-test-block/products/8982368747688');
    
  } catch (error) {
    console.error('エラーが発生しました:', error);
    if (error.response && error.response.errors) {
      console.error('GraphQLエラー:', JSON.stringify(error.response.errors, null, 2));
    }
  }
}

fixProduct();