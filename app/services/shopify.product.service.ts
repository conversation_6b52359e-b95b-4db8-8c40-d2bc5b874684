import { Session } from "@shopify/shopify-api";
import { shopifyApi } from "@shopify/shopify-api";
import { prisma } from "~/db.server";

/**
 * Shopify商品データ取得サービス
 */
export class ShopifyProductService {
  private session: Session;
  private client: any;

  constructor(session: Session) {
    this.session = session;
    // 新しいGraphQLクライアントの初期化方法
    this.client = {
      query: async ({ data }: { data: any }) => {
        const client = new shopifyApi.clients.Graphql({ session });
        return await client.query({ data });
      }
    };
  }

  /**
   * Shopifyから商品データを取得
   * @param limit 取得する商品数
   * @returns 商品データ
   */
  async fetchProducts(limit = 50) {
    try {
      const query = `
        {
          products(first: ${limit}) {
            edges {
              node {
                id
                title
                handle
                description
                productType
                tags
                status
                images(first: 1) {
                  edges {
                    node {
                      url
                    }
                  }
                }
                variants(first: 10) {
                  edges {
                    node {
                      id
                      title
                      price
                      sku
                      inventoryQuantity
                      inventoryItem {
                        id
                        inventoryLevel(locationId: "") {
                          available
                          location {
                            id
                            name
                          }
                        }
                      }
                    }
                  }
                }
                metafields(first: 20, namespace: "rental") {
                  edges {
                    node {
                      id
                      namespace
                      key
                      value
                      type
                    }
                  }
                }
              }
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      `;

      const response = await this.client.query({
        data: query,
      });

      if (response.body.data) {
        return response.body.data.products.edges.map((edge: any) => edge.node);
      }

      return [];
    } catch (error) {
      console.error("Shopify商品データ取得エラー:", error);
      throw error;
    }
  }

  /**
   * 商品メタフィールドを更新
   * @param productId 商品ID
   * @param metafields メタフィールド
   */
  async updateProductMetafields(productId: string, metafields: any[]) {
    try {
      const metafieldInputs = metafields.map((metafield) => {
        return {
          namespace: metafield.namespace || "rental",
          key: metafield.key,
          value: typeof metafield.value === "string"
            ? metafield.value
            : JSON.stringify(metafield.value),
          type: metafield.type || "string",
        };
      });

      const mutation = `
        mutation productUpdate($input: ProductInput!) {
          productUpdate(input: $input) {
            product {
              id
              metafields(first: 20, namespace: "rental") {
                edges {
                  node {
                    id
                    namespace
                    key
                    value
                  }
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const response = await this.client.query({
        data: {
          query: mutation,
          variables: {
            input: {
              id: productId,
              metafields: metafieldInputs,
            },
          },
        },
      });

      if (response.body.data.productUpdate.userErrors.length > 0) {
        throw new Error(response.body.data.productUpdate.userErrors[0].message);
      }

      return response.body.data.productUpdate.product;
    } catch (error) {
      console.error("商品メタフィールド更新エラー:", error);
      throw error;
    }
  }

  /**
   * 商品在庫を更新
   * @param inventoryItemId 在庫アイテムID
   * @param locationId ロケーションID
   * @param quantity 数量
   */
  async updateInventoryLevel(inventoryItemId: string, locationId: string, quantity: number) {
    try {
      const mutation = `
        mutation inventoryAdjustQuantity($input: InventoryAdjustQuantityInput!) {
          inventoryAdjustQuantity(input: $input) {
            inventoryLevel {
              available
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const response = await this.client.query({
        data: {
          query: mutation,
          variables: {
            input: {
              inventoryItemId,
              locationId,
              availableDelta: quantity,
            },
          },
        },
      });

      if (response.body.data.inventoryAdjustQuantity.userErrors.length > 0) {
        throw new Error(response.body.data.inventoryAdjustQuantity.userErrors[0].message);
      }

      return response.body.data.inventoryAdjustQuantity.inventoryLevel;
    } catch (error) {
      console.error("在庫更新エラー:", error);
      throw error;
    }
  }

  /**
   * Shopify商品データをPrismaデータベースと同期
   * @param shop ショップドメイン
   */
  async syncProductsWithDatabase(shop: string) {
    try {
      // Shopifyから商品データを取得
      const shopifyProducts = await this.fetchProducts();

      // 同期結果を記録
      const syncResults = {
        created: 0,
        updated: 0,
        skipped: 0,
        errors: 0,
      };

      // 各商品を処理
      for (const product of shopifyProducts) {
        try {
          // Shopify IDからGID部分を抽出
          const shopifyId = product.id.replace("gid://shopify/Product/", "");

          // データベースに既存の商品があるか確認
          const existingProduct = await prisma.product.findFirst({
            where: {
              shop,
              shopifyId,
            },
          });

          // メタフィールドを解析
          const metafields = product.metafields.edges.map((edge: any) => ({
            key: edge.node.key,
            value: edge.node.value,
            namespace: edge.node.namespace,
          }));

          // レンタル状態を取得
          const statusMetafield = metafields.find((m: any) => m.key === "status");
          const status = statusMetafield ? statusMetafield.value : "available";

          // 基本情報を取得
          const basicInfoMetafield = metafields.find((m: any) => m.key === "basic_info");
          const basicInfo = basicInfoMetafield ? JSON.parse(basicInfoMetafield.value) : {};

          // 在庫情報を取得
          const defaultVariant = product.variants.edges[0]?.node;
          const sku = defaultVariant?.sku || "";
          const price = parseFloat(defaultVariant?.price || "0");

          // 画像URLを取得
          const imageUrl = product.images.edges[0]?.node.url || "";

          if (existingProduct) {
            // 既存の商品を更新
            await prisma.product.update({
              where: {
                id: existingProduct.id,
              },
              data: {
                title: product.title,
                handle: product.handle,
                description: product.description,
                productType: product.productType,
                tags: product.tags,
                status,
                sku,
                price,
                imageUrl,
                metafields: metafields,
                basicInfo,
                updatedAt: new Date(),
              },
            });
            syncResults.updated++;
          } else {
            // 新規商品を作成
            await prisma.product.create({
              data: {
                shop,
                shopifyId,
                title: product.title,
                handle: product.handle,
                description: product.description,
                productType: product.productType,
                tags: product.tags,
                status,
                sku,
                price,
                imageUrl,
                metafields: metafields,
                basicInfo,
              },
            });
            syncResults.created++;
          }
        } catch (error) {
          console.error(`商品同期エラー (${product.title}):`, error);
          syncResults.errors++;
        }
      }

      return syncResults;
    } catch (error) {
      console.error("商品同期エラー:", error);
      throw error;
    }
  }
}
