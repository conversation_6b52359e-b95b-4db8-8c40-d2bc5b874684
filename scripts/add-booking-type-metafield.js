/**
 * 予約タイプメタフィールド追加スクリプト
 *
 * このスクリプトは、予約タイプ（仮予約・本予約）を管理するためのメタフィールドを追加します。
 * 「予約タイプ実装方式検討.md」で推奨されている方式に基づいています。
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// メタフィールド定義作成のGraphQLミューテーション
const CREATE_METAFIELD_DEFINITION = gql`
  mutation metafieldDefinitionCreate($definition: MetafieldDefinitionInput!) {
    metafieldDefinitionCreate(definition: $definition) {
      createdDefinition {
        id
        name
        namespace
        key
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールド定義取得のGraphQLクエリ
const GET_METAFIELD_DEFINITIONS = gql`
  query getMetafieldDefinitions($namespace: String!, $key: String!) {
    metafieldDefinitions(first: 1, ownerType: PRODUCT, namespace: $namespace, key: $key) {
      edges {
        node {
          id
          name
          namespace
          key
        }
      }
    }
  }
`;

// 追加する予約タイプメタフィールド定義
const BOOKING_TYPE_METAFIELD = {
  name: '予約タイプ',
  namespace: 'rental',
  key: 'booking_type',
  description: '予約タイプ（仮予約または本予約）',
  type: 'single_line_text_field',
  ownerType: 'PRODUCT',
  // single_line_text_fieldではallowed_valuesバリデーションがサポートされていないため削除
  visibleToStorefrontApi: true
};

// 注文用の予約タイプメタフィールド定義
const ORDER_BOOKING_TYPE_METAFIELD = {
  name: '予約タイプ',
  namespace: 'rental',
  key: 'reservation_type',
  description: '予約タイプ（仮予約または本予約）',
  type: 'single_line_text_field',
  ownerType: 'ORDER',
  // single_line_text_fieldではallowed_valuesバリデーションがサポートされていないため削除
  visibleToStorefrontApi: true
};

// メタフィールド定義を作成する関数
async function createMetafieldDefinition(definition) {
  try {
    console.log(`メタフィールド定義を作成: ${definition.namespace}.${definition.key}`);

    // 既存のメタフィールド定義を確認
    const existingResult = await client.request(GET_METAFIELD_DEFINITIONS, {
      namespace: definition.namespace,
      key: definition.key
    });

    const existingDefinitions = existingResult.metafieldDefinitions.edges;

    if (existingDefinitions.length > 0) {
      console.log(`メタフィールド定義 ${definition.namespace}.${definition.key} は既に存在します。スキップします。`);
      return;
    }

    // メタフィールド定義を作成
    const result = await client.request(CREATE_METAFIELD_DEFINITION, {
      definition
    });

    if (result.metafieldDefinitionCreate.userErrors.length > 0) {
      console.error(`エラー: ${JSON.stringify(result.metafieldDefinitionCreate.userErrors, null, 2)}`);
    } else {
      console.log(`成功: ${definition.namespace}.${definition.key} を作成しました`);
    }
  } catch (error) {
    console.error(`メタフィールド定義の作成中にエラーが発生しました: ${error.message}`);
  }
}

// メインの実行関数
async function addBookingTypeMetafield() {
  console.log('予約タイプメタフィールドの追加を開始します...');

  // 商品用の予約タイプメタフィールドを作成
  await createMetafieldDefinition(BOOKING_TYPE_METAFIELD);

  // API制限を回避するための短い待機
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 注文用の予約タイプメタフィールドを作成
  await createMetafieldDefinition(ORDER_BOOKING_TYPE_METAFIELD);

  console.log('予約タイプメタフィールドの追加が完了しました');
}

// スクリプト実行
addBookingTypeMetafield().catch(error => {
  console.error('スクリプト実行中にエラーが発生しました:', error);
  process.exit(1);
});
