/**
 * 予約一覧を表示するスクリプト
 *
 * このスクリプトは、データベースに登録されている予約情報を一覧表示します。
 *
 * 実行方法: npx tsx scripts/list-bookings.ts
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * 予約一覧を表示する関数
 */
async function listBookings() {
  try {
    console.log('予約一覧を取得しています...');

    // 予約情報を取得
    const bookings = await prisma.booking.findMany({
      include: {
        product: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (bookings.length === 0) {
      console.log('予約情報が見つかりません');
      return;
    }

    console.log(`${bookings.length}件の予約が見つかりました\n`);

    // 予約情報を表示
    bookings.forEach((booking, index) => {
      console.log(`----- 予約 ${index + 1} -----`);
      console.log('予約ID:', booking.id);
      console.log('予約番号:', booking.bookingId);
      console.log('予約タイプ:', booking.bookingType);
      console.log('開始日:', format(booking.startDate, 'yyyy/MM/dd', { locale: ja }));
      console.log('終了日:', format(booking.endDate, 'yyyy/MM/dd', { locale: ja }));
      console.log('顧客名:', booking.customerName);
      console.log('顧客メール:', booking.customerEmail);
      console.log('顧客ID:', booking.customerId);

      if (booking.product) {
        console.log('商品名:', booking.product.title);
        console.log('商品ID:', booking.product.id);
        console.log('Shopify商品ID:', booking.product.shopifyId);
      } else {
        console.log('商品名: 不明（商品データなし）');
        console.log('商品ID: 不明');
        console.log('Shopify商品ID: 不明');
      }

      console.log('合計金額:', booking.totalAmount);
      console.log('注文ID:', booking.orderId || 'なし');
      console.log('注文番号:', booking.orderName || 'なし');
      console.log('作成日時:', format(booking.createdAt, 'yyyy/MM/dd HH:mm:ss', { locale: ja }));
      console.log('更新日時:', format(booking.updatedAt, 'yyyy/MM/dd HH:mm:ss', { locale: ja }));
      console.log('');
    });

    return bookings;
  } catch (error) {
    console.error('予約情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

// メイン処理
async function main() {
  try {
    // 予約一覧を表示
    await listBookings();

    console.log('処理が完了しました');
    process.exit(0);
  } catch (error) {
    console.error('エラーが発生しました:', error);
    process.exit(1);
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
