#!/usr/bin/env node

/**
 * 統合カテゴリマスタを作成するスクリプト
 * 
 * 使用方法:
 * node scripts/category-management/02_create_unified_master.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 入力ファイルのパス
const CATEGORY_ANALYSIS_CSV = path.join(__dirname, '../../master-data-csv/generated/category_analysis.csv');
const SUBCATEGORY_ANALYSIS_CSV = path.join(__dirname, '../../master-data-csv/generated/subcategory_analysis.csv');
const EXISTING_CATEGORY_CSV = path.join(__dirname, '../../master-data-csv/other-master-data/商品カテゴリマスタ.csv');

// 出力ファイルのパス
const OUTPUT_DIR = path.join(__dirname, '../../master-data-csv/generated');
const UNIFIED_CATEGORY_CSV = path.join(OUTPUT_DIR, 'unified_category_master.csv');
const UNIFIED_SUBCATEGORY_CSV = path.join(OUTPUT_DIR, 'unified_subcategory_master.csv');

/**
 * CSVファイルを読み込んでパースする
 */
function parseCSV(filePath) {
  if (!fs.existsSync(filePath)) {
    console.warn(`ファイルが見つかりません: ${filePath}`);
    return [];
  }
  
  const content = fs.readFileSync(filePath, 'utf-8');
  const lines = content.split('\n').filter(line => line.trim());
  const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
  
  return lines.slice(1).map(line => {
    const values = parseCSVLine(line);
    const obj = {};
    headers.forEach((header, index) => {
      obj[header] = values[index] || '';
    });
    return obj;
  });
}

/**
 * CSV行をパースする（クォート対応）
 */
function parseCSVLine(line) {
  const result = [];
  let current = '';
  let inQuotes = false;
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i];
    
    if (char === '"') {
      inQuotes = !inQuotes;
    } else if (char === ',' && !inQuotes) {
      result.push(current.trim());
      current = '';
    } else {
      current += char;
    }
  }
  
  result.push(current.trim());
  return result;
}

/**
 * カテゴリ名を推定する
 */
function estimateCategoryName(code, majorCategories, existingMaster) {
  // 既存マスタから検索
  const existing = existingMaster.find(item => item['商品カテゴリコード'] === code);
  if (existing) {
    return existing['商品カテゴリ名称'];
  }
  
  // 大カテゴリから推定
  const majors = majorCategories.split(';').filter(cat => cat.trim());
  if (majors.length > 0) {
    // 最も一般的なカテゴリ名を使用
    return majors[0].trim();
  }
  
  // デフォルト名
  return `カテゴリ${code}`;
}

/**
 * サブカテゴリ名を推定する
 */
function estimateSubCategoryName(categoryCode, subCategoryCode, majorCategories, minorCategories) {
  const majors = majorCategories.split(';').filter(cat => cat.trim());
  const minors = minorCategories.split(';').filter(cat => cat.trim());
  
  // 小カテゴリがある場合は優先
  if (minors.length > 0) {
    return minors[0].trim();
  }
  
  // 大カテゴリから推定
  if (majors.length > 0) {
    return majors[0].trim();
  }
  
  // デフォルト名
  return `サブカテゴリ${subCategoryCode}`;
}

/**
 * 統合カテゴリマスタを作成
 */
function createUnifiedCategoryMaster() {
  console.log('=== 統合カテゴリマスタ作成開始 ===');
  
  // 分析結果を読み込み
  const categoryAnalysis = parseCSV(CATEGORY_ANALYSIS_CSV);
  const existingMaster = parseCSV(EXISTING_CATEGORY_CSV);
  
  console.log(`カテゴリ分析データ: ${categoryAnalysis.length}件`);
  console.log(`既存マスタデータ: ${existingMaster.length}件`);
  
  // 統合カテゴリマスタを作成
  const unifiedCategories = [];
  let displayOrder = 1;
  
  // 分析結果からカテゴリを作成
  categoryAnalysis.forEach(item => {
    const code = item['カテゴリコード'];
    const count = parseInt(item['商品数']) || 0;
    const majorCategories = item['大カテゴリ'] || '';
    
    const categoryName = estimateCategoryName(code, majorCategories, existingMaster);
    
    unifiedCategories.push({
      code: code,
      name: categoryName,
      parentCode: '',
      level: 1,
      displayOrder: displayOrder++,
      isActive: count > 0 ? 'true' : 'false',
      description: `商品数: ${count}件, 大カテゴリ: ${majorCategories}`,
      productCount: count
    });
  });
  
  // 既存マスタで不足しているカテゴリを追加
  existingMaster.forEach(existing => {
    const code = existing['商品カテゴリコード'];
    const name = existing['商品カテゴリ名称'];
    
    if (!unifiedCategories.find(cat => cat.code === code)) {
      unifiedCategories.push({
        code: code,
        name: name,
        parentCode: '',
        level: 1,
        displayOrder: displayOrder++,
        isActive: 'true',
        description: '既存マスタより',
        productCount: 0
      });
    }
  });
  
  // コード順でソート
  unifiedCategories.sort((a, b) => a.code.localeCompare(b.code));
  
  return unifiedCategories;
}

/**
 * 統合サブカテゴリマスタを作成
 */
function createUnifiedSubCategoryMaster() {
  console.log('=== 統合サブカテゴリマスタ作成開始 ===');
  
  // 分析結果を読み込み
  const subCategoryAnalysis = parseCSV(SUBCATEGORY_ANALYSIS_CSV);
  
  console.log(`サブカテゴリ分析データ: ${subCategoryAnalysis.length}件`);
  
  // 統合サブカテゴリマスタを作成
  const unifiedSubCategories = [];
  let displayOrder = 1;
  
  subCategoryAnalysis.forEach(item => {
    const categoryCode = item['カテゴリコード'];
    const subCategoryCode = item['サブカテゴリコード'];
    const count = parseInt(item['商品数']) || 0;
    const majorCategories = item['大カテゴリ'] || '';
    const minorCategories = item['小カテゴリ'] || '';
    
    const subCategoryName = estimateSubCategoryName(
      categoryCode, 
      subCategoryCode, 
      majorCategories, 
      minorCategories
    );
    
    unifiedSubCategories.push({
      code: subCategoryCode,
      name: subCategoryName,
      parentCategoryCode: categoryCode,
      isActive: count > 0 ? 'true' : 'false',
      displayOrder: displayOrder++,
      description: `商品数: ${count}件`,
      productCount: count,
      majorCategories: majorCategories,
      minorCategories: minorCategories
    });
  });
  
  // カテゴリコード、サブカテゴリコード順でソート
  unifiedSubCategories.sort((a, b) => {
    const categoryCompare = a.parentCategoryCode.localeCompare(b.parentCategoryCode);
    if (categoryCompare !== 0) return categoryCompare;
    return a.code.localeCompare(b.code);
  });
  
  return unifiedSubCategories;
}

/**
 * CSVファイルに出力
 */
function outputToCSV(categories, subCategories) {
  // 出力ディレクトリを作成
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }
  
  // カテゴリマスタCSVを出力
  const categoryCSV = [
    'code,name,parentCode,level,displayOrder,isActive,description,productCount'
  ];
  
  categories.forEach(cat => {
    categoryCSV.push(
      `${cat.code},"${cat.name}","${cat.parentCode}",${cat.level},${cat.displayOrder},${cat.isActive},"${cat.description}",${cat.productCount}`
    );
  });
  
  fs.writeFileSync(UNIFIED_CATEGORY_CSV, categoryCSV.join('\n'), 'utf-8');
  console.log(`統合カテゴリマスタを出力: ${UNIFIED_CATEGORY_CSV}`);
  
  // サブカテゴリマスタCSVを出力
  const subCategoryCSV = [
    'code,name,parentCategoryCode,isActive,displayOrder,description,productCount,majorCategories,minorCategories'
  ];
  
  subCategories.forEach(sub => {
    subCategoryCSV.push(
      `${sub.code},"${sub.name}",${sub.parentCategoryCode},${sub.isActive},${sub.displayOrder},"${sub.description}",${sub.productCount},"${sub.majorCategories}","${sub.minorCategories}"`
    );
  });
  
  fs.writeFileSync(UNIFIED_SUBCATEGORY_CSV, subCategoryCSV.join('\n'), 'utf-8');
  console.log(`統合サブカテゴリマスタを出力: ${UNIFIED_SUBCATEGORY_CSV}`);
}

/**
 * 統計情報を出力
 */
function outputStatistics(categories, subCategories) {
  const stats = [
    '# 統合カテゴリマスタ統計',
    '',
    `作成日時: ${new Date().toLocaleString('ja-JP')}`,
    '',
    '## 基本統計',
    `- 総カテゴリ数: ${categories.length}個`,
    `- 総サブカテゴリ数: ${subCategories.length}個`,
    `- アクティブカテゴリ数: ${categories.filter(c => c.isActive === 'true').length}個`,
    `- アクティブサブカテゴリ数: ${subCategories.filter(s => s.isActive === 'true').length}個`,
    '',
    '## カテゴリ別商品数',
    ''
  ];
  
  categories
    .filter(cat => cat.productCount > 0)
    .sort((a, b) => b.productCount - a.productCount)
    .forEach(cat => {
      stats.push(`- ${cat.code} (${cat.name}): ${cat.productCount}件`);
    });
  
  stats.push('');
  stats.push('## サブカテゴリ別商品数（上位20件）');
  stats.push('');
  
  subCategories
    .filter(sub => sub.productCount > 0)
    .sort((a, b) => b.productCount - a.productCount)
    .slice(0, 20)
    .forEach(sub => {
      stats.push(`- ${sub.parentCategoryCode}-${sub.code} (${sub.name}): ${sub.productCount}件`);
    });
  
  const statsPath = path.join(OUTPUT_DIR, 'unified_master_statistics.md');
  fs.writeFileSync(statsPath, stats.join('\n'), 'utf-8');
  console.log(`統計情報を出力: ${statsPath}`);
}

/**
 * メイン処理
 */
function main() {
  try {
    // 前提条件チェック
    if (!fs.existsSync(CATEGORY_ANALYSIS_CSV)) {
      console.error('カテゴリ分析ファイルが見つかりません。先に01_analyze_excel_categories.jsを実行してください。');
      process.exit(1);
    }
    
    const categories = createUnifiedCategoryMaster();
    const subCategories = createUnifiedSubCategoryMaster();
    
    outputToCSV(categories, subCategories);
    outputStatistics(categories, subCategories);
    
    console.log('\n=== 統合マスタ作成完了 ===');
    console.log('生成されたファイル:');
    console.log('- master-data-csv/generated/unified_category_master.csv');
    console.log('- master-data-csv/generated/unified_subcategory_master.csv');
    console.log('- master-data-csv/generated/unified_master_statistics.md');
    
  } catch (error) {
    console.error('エラーが発生しました:', error);
    process.exit(1);
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
