import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  BlockStack,
  DataTable,
  Button,
  Badge,
  Pagination,
  EmptyState,
  TextField,
  ButtonGroup,
  Filters,
  InlineStack,
  Icon,
  Tooltip,
  Banner
} from "@shopify/polaris";
import { SearchIcon, PlusIcon } from "@shopify/polaris-icons";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { useState } from "react";
import { buildSearchCondition, filterItemsByName } from "../utils/search-utils";

/**
 * 顧客管理ページ（インデックスページ）
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  // URLからパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get("page") || "1", 10);
  const pageSize = 20; // 表示件数
  const searchQuery = url.searchParams.get("q") || "";
  const sortField = url.searchParams.get("sort") || "createdAt";
  const sortOrder = url.searchParams.get("order") || "desc";

  try {
    // 検索条件を構築
    let whereCondition: any = { shop };

    // 検索クエリがある場合
    if (searchQuery) {
      // 共通ユーティリティ関数を使用して検索条件を構築
      const searchFields = ['name', 'email', 'phone'];
      const searchCondition = buildSearchCondition(searchQuery, searchFields);

      // 最終的な検索条件を設定
      whereCondition = { ...whereCondition, ...searchCondition };

      // 検索条件をログに出力（デバッグ用）
      console.log('顧客検索条件:', JSON.stringify(whereCondition, null, 2));
    }

    // 並び順を設定
    const orderBy: any = {};
    orderBy[sortField] = sortOrder;

    // 顧客データを取得
    const customers = await prisma.customer.findMany({
      where: whereCondition,
      orderBy,
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    // 総顧客数を取得
    const totalCustomers = await prisma.customer.count({
      where: whereCondition,
    });

    // 顧客データをフォーマット
    const formattedCustomers = customers.map(customer => {
      return {
        id: customer.id,
        name: customer.name || "",
        email: customer.email || "",
        phone: customer.phone || "",
        address: customer.address || "",
        bookingsCount: 0, // 予約数は現在取得していないため0を設定
        createdAt: customer.createdAt ? new Date(customer.createdAt).toLocaleString() : "",
      };
    });

    return json({
      customers: formattedCustomers,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalCustomers / pageSize),
        totalItems: totalCustomers,
      },
      filters: {
        query: searchQuery,
        sort: sortField,
        order: sortOrder,
      },
    });
  } catch (error) {
    console.error("顧客データ取得エラー:", error);
    return json({
      customers: [],
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
      },
      filters: {
        query: searchQuery,
        sort: sortField,
        order: sortOrder,
      },
      error: "顧客データの取得中にエラーが発生しました"
    });
  }
}

export default function CustomersIndexPage() {
  const { customers, pagination, filters, error } = useLoaderData<typeof loader>();
  const [searchValue, setSearchValue] = useState(filters.query);

  // 検索フォームの送信処理
  const handleSearch = (query?: string) => {
    const url = new URL(window.location.href);
    const searchTerm = query !== undefined ? query : searchValue;

    if (searchTerm && searchTerm !== "") {
      url.searchParams.set("q", searchTerm);
    } else {
      url.searchParams.delete("q");
    }

    url.searchParams.set("page", "1"); // 検索時は1ページ目に戻る
    window.location.href = url.toString();
  };

  // ページネーションの処理
  const handlePageChange = (page: number) => {
    const url = new URL(window.location.href);
    url.searchParams.set("page", page.toString());
    window.location.href = url.toString();
  };

  // 並び替えの変更処理
  const handleSortChange = (field: string) => {
    const url = new URL(window.location.href);
    const currentSort = url.searchParams.get("sort") || "createdAt";
    const currentOrder = url.searchParams.get("order") || "desc";

    // 同じフィールドをクリックした場合は並び順を反転
    if (field === currentSort) {
      url.searchParams.set("order", currentOrder === "asc" ? "desc" : "asc");
    } else {
      url.searchParams.set("sort", field);
      url.searchParams.set("order", "asc"); // 新しいフィールドの場合は昇順から
    }

    window.location.href = url.toString();
  };

  // データテーブルの行を生成
  const rows = customers.map(customer => [
    <Link key={`link-${customer.id}`} to={`/app/customers/${customer.id}`}>{customer.name}</Link>,
    customer.email,
    customer.phone,
    customer.address,
    customer.bookingsCount,
    customer.createdAt,
  ]);

  return (
    <Page
      title="顧客一覧"
      subtitle="顧客情報の管理と確認"
      backAction={{ content: "ホーム", url: "/app" }}
      primaryAction={{
        content: "新規顧客",
        url: "/app/customers/new",
        icon: PlusIcon,
      }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                {error && (
                  <Text as="p" tone="critical">{error}</Text>
                )}

                {/* 検索セクション */}
                <Box paddingBlockEnd="400">
                  <InlineStack gap="400" align="start" blockAlign="center">
                    <div style={{ flex: 1 }}>
                      <form
                        onSubmit={(e) => {
                          e.preventDefault();
                          handleSearch(searchValue);
                        }}
                        style={{ display: 'flex' }}
                      >
                        <div style={{ flex: 1 }}>
                          <TextField
                            label="検索"
                            type="search"
                            value={searchValue}
                            placeholder="顧客名、メール、電話番号で検索"
                            prefix={<Icon source={SearchIcon} />}
                            onClearButtonClick={() => {
                              setSearchValue("");
                              handleSearch("");
                            }}
                            onChange={(value) => {
                              setSearchValue(value);
                            }}
                            autoComplete="off"
                          />
                        </div>
                        <div style={{ marginLeft: '8px', marginTop: '24px' }}>
                          <Button onClick={() => handleSearch(searchValue)} type="submit">検索</Button>
                        </div>
                      </form>
                    </div>
                  </InlineStack>
                </Box>

                {customers.length === 0 ? (
                  <EmptyState
                    heading="顧客データがありません"
                    image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                  >
                    <p>検索条件に一致する顧客データが見つかりません。</p>
                  </EmptyState>
                ) : (
                  <DataTable
                    columnContentTypes={[
                      "text",
                      "text",
                      "text",
                      "text",
                      "numeric",
                      "text",
                    ]}
                    headings={[
                      <Button plain monochrome onClick={() => handleSortChange("name")}>顧客名</Button>,
                      <Button plain monochrome onClick={() => handleSortChange("email")}>メールアドレス</Button>,
                      <Button plain monochrome onClick={() => handleSortChange("phone")}>電話番号</Button>,
                      <Button plain monochrome onClick={() => handleSortChange("address")}>住所</Button>,
                      <Button plain monochrome onClick={() => handleSortChange("bookingsCount")}>予約数</Button>,
                      <Button plain monochrome onClick={() => handleSortChange("createdAt")}>登録日時</Button>,
                    ]}
                    rows={rows}
                    hoverable
                    increasedTableDensity
                  />
                )}

                {pagination.totalPages > 1 && (
                  <Box paddingBlockStart="400">
                    <Pagination
                      hasPrevious={pagination.currentPage > 1}
                      onPrevious={() => handlePageChange(pagination.currentPage - 1)}
                      hasNext={pagination.currentPage < pagination.totalPages}
                      onNext={() => handlePageChange(pagination.currentPage + 1)}
                    />
                    <div style={{ textAlign: "center", marginTop: "10px" }}>
                      <Text as="span">全{pagination.totalItems}件中 {(pagination.currentPage - 1) * 20 + 1}-{Math.min(pagination.currentPage * 20, pagination.totalItems)}件を表示</Text>
                    </div>
                  </Box>
                )}
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
