/**
 * カテゴリ連携実装スクリプト
 *
 * SKU番号体系とカテゴリマスタを活用して、
 * Shopifyのタグ・コレクション・タイプを自動設定
 */

import { PrismaClient } from '@prisma/client';
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { parse } from 'csv-parse/sync';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();
const SHOPIFY_SHOP = process.env.SHOPIFY_SHOP;
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

/**
 * Shopify GraphQL APIを呼び出し
 */
async function callShopifyGraphQL(query, variables = {}) {
  const response = await fetch(`https://${SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN
    },
    body: JSON.stringify({ query, variables })
  });

  const data = await response.json();
  if (data.errors) {
    throw new Error(`Shopify API エラー: ${JSON.stringify(data.errors)}`);
  }
  return data.data;
}

/**
 * カテゴリマスタデータを読み込み
 */
function loadCategoryMaster() {
  const categoryMasterPath = path.join(process.cwd(), 'master-data-csv/other-master-data/商品カテゴリマスタ.csv');

  if (!fs.existsSync(categoryMasterPath)) {
    console.log('⚠️ カテゴリマスタファイルが見つかりません');
    return {};
  }

  const csvContent = fs.readFileSync(categoryMasterPath, 'utf-8');
  const records = parse(csvContent, {
    columns: true,
    skip_empty_lines: true
  });

  const categoryMap = {};
  records.forEach(record => {
    categoryMap[record['商品カテゴリコード']] = record['商品カテゴリ名称'];
  });

  return categoryMap;
}

/**
 * SKUからカテゴリ情報を抽出
 */
function extractCategoryFromSku(sku) {
  if (!sku) return null;

  // ハイフンを除去して数字のみにする
  const numericSku = sku.replace(/-/g, '');

  if (numericSku.length >= 8) {
    const categoryCode = numericSku.substring(0, 3); // 例: "101"
    const subCategoryCode = numericSku.substring(3, 5); // 例: "01"
    const itemCode = numericSku.substring(5, 8); // 例: "008"

    return {
      categoryCode,
      subCategoryCode,
      itemCode,
      fullCategoryCode: `${categoryCode}-${subCategoryCode}`
    };
  }

  return null;
}

/**
 * カテゴリコードからShopifyタグを生成
 */
function generateShopifyTags(categoryCode, subCategoryCode, categoryMaster) {
  const tags = [];

  // カテゴリマッピング（SKU番号体系に基づく）
  const categoryMapping = {
    '101': 'ソファ',
    '102': 'チェア',
    '103': 'テーブル',
    '201': 'ソファ',
    '202': 'チェア',
    '203': 'テーブル',
    '208': '布',
    '209': 'キッズ',
    '210': '本',
    '211': 'オブジェ',
    '212': '花器',
    '213': 'クリスマス',
    '214': '箱',
    '215': 'ステーショナリー',
    '216': 'ボディ',
    '217': 'トランク',
    '301': 'グリーン',
    '401': 'アート',
    '901': 'デリバリー',
    '902': 'キャンセル料',
    '903': '他社レンタル',
    '904': '制作',
    '999': 'EASE'
  };

  const subCategoryMapping = {
    '01': '1シーター',
    '02': '2シーター',
    '03': '3シーター',
    '06': 'ソファ',
    '07': 'アクセサリー'
  };

  // メインカテゴリタグ
  if (categoryMapping[categoryCode]) {
    tags.push(categoryMapping[categoryCode]);
  }

  // サブカテゴリタグ
  if (subCategoryMapping[subCategoryCode]) {
    tags.push(subCategoryMapping[subCategoryCode]);
  }

  // カテゴリマスタからのタグ
  if (categoryMaster[categoryCode]) {
    tags.push(categoryMaster[categoryCode]);
  }

  // システム用タグ
  tags.push(`CAT-${categoryCode}`);
  tags.push(`SUBCAT-${subCategoryCode}`);
  tags.push('レンタル家具');

  return [...new Set(tags)]; // 重複除去
}

/**
 * SKUからShopifyコレクションを決定
 */
function determineShopifyCollection(categoryCode) {
  const collectionMapping = {
    '101': 'ソファコレクション',
    '102': 'チェアコレクション',
    '103': 'テーブルコレクション',
    '201': 'ソファコレクション',
    '202': 'チェアコレクション',
    '203': 'テーブルコレクション',
    '208': 'ファブリックコレクション',
    '209': 'キッズコレクション',
    '301': 'グリーンコレクション',
    '401': 'アートコレクション'
  };

  return collectionMapping[categoryCode] || 'その他コレクション';
}

/**
 * SKUからShopify商品タイプを決定
 */
function determineShopifyProductType(categoryCode, subCategoryCode) {
  if (categoryCode === '101' || categoryCode === '201') {
    if (subCategoryCode === '01') return 'ソファ - 1シーター';
    if (subCategoryCode === '02') return 'ソファ - 2シーター';
    if (subCategoryCode === '03') return 'ソファ - 3シーター';
    return 'ソファ';
  }

  if (categoryCode === '102' || categoryCode === '202') {
    return 'チェア';
  }

  if (categoryCode === '103' || categoryCode === '203') {
    return 'テーブル';
  }

  return 'レンタル家具';
}

/**
 * 商品のカテゴリ情報を更新
 */
async function updateProductCategoryInfo(productId, tags, productType) {
  const updateMutation = `
    mutation productUpdate($input: ProductInput!) {
      productUpdate(input: $input) {
        product {
          id
          tags
          productType
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const updateInput = {
    id: productId,
    tags: tags,
    productType: productType
  };

  return await callShopifyGraphQL(updateMutation, { input: updateInput });
}

/**
 * 全商品のカテゴリ連携を実行
 */
async function implementCategoryIntegration() {
  console.log('🏷️ === カテゴリ連携実装開始 ===');
  console.log(`対象ショップ: ${SHOPIFY_SHOP}`);
  console.log(`実行時刻: ${new Date().toLocaleString('ja-JP')}`);
  console.log('');

  try {
    // 1. カテゴリマスタを読み込み
    console.log('1. カテゴリマスタ読み込み中...');
    const categoryMaster = loadCategoryMaster();
    console.log(`✅ カテゴリマスタ: ${Object.keys(categoryMaster).length}件`);

    Object.entries(categoryMaster).forEach(([code, name]) => {
      console.log(`   ${code}: ${name}`);
    });
    console.log('');

    // 2. Prismaから商品データを取得
    console.log('2. 商品データ取得中...');
    const products = await prisma.product.findMany({
      where: {
        shop: SHOPIFY_SHOP
      },
      select: {
        id: true,
        shopifyId: true,
        title: true,
        sku: true
      }
    });

    console.log(`✅ 対象商品: ${products.length}件`);
    console.log('');

    // 3. 各商品のカテゴリ情報を分析・更新
    console.log('3. カテゴリ情報分析・更新中...');
    let successCount = 0;
    let errorCount = 0;

    for (const product of products) {
      try {
        console.log(`処理中: ${product.sku} - ${product.title}`);

        // SKUからカテゴリ情報を抽出
        const categoryInfo = extractCategoryFromSku(product.sku);

        if (!categoryInfo) {
          console.log(`   ⚠️ SKUからカテゴリ情報を抽出できません: ${product.sku}`);
          continue;
        }

        console.log(`   📊 カテゴリ: ${categoryInfo.categoryCode}-${categoryInfo.subCategoryCode}`);

        // タグを生成
        const tags = generateShopifyTags(
          categoryInfo.categoryCode,
          categoryInfo.subCategoryCode,
          categoryMaster
        );

        // 商品タイプを決定
        const productType = determineShopifyProductType(
          categoryInfo.categoryCode,
          categoryInfo.subCategoryCode
        );

        // コレクションを決定
        const collection = determineShopifyCollection(categoryInfo.categoryCode);

        console.log(`   🏷️ タグ: ${tags.join(', ')}`);
        console.log(`   📦 商品タイプ: ${productType}`);
        console.log(`   📚 コレクション: ${collection}`);

        // Shopify IDをGlobal ID形式に変換
        let globalId = product.shopifyId;
        if (!globalId.startsWith('gid://shopify/Product/')) {
          globalId = `gid://shopify/Product/${globalId}`;
        }

        // Shopifyで商品情報を更新
        const result = await updateProductCategoryInfo(
          globalId,
          tags,
          productType
        );

        if (result.productUpdate.userErrors.length > 0) {
          console.log(`   ❌ 更新エラー: ${JSON.stringify(result.productUpdate.userErrors)}`);
          errorCount++;
        } else {
          console.log(`   ✅ 更新成功`);
          successCount++;
        }

      } catch (error) {
        console.log(`   ❌ 処理エラー: ${error.message}`);
        errorCount++;
      }

      console.log('');
    }

    // 4. 結果サマリー
    console.log('🎯 === カテゴリ連携結果 ===');
    console.log(`✅ 成功: ${successCount}件`);
    console.log(`❌ エラー: ${errorCount}件`);
    console.log(`📊 成功率: ${Math.round((successCount / (successCount + errorCount)) * 100)}%`);

    if (successCount > 0) {
      console.log('');
      console.log('🎉 カテゴリ連携が完了しました！');
      console.log('');
      console.log('📋 次のステップ:');
      console.log('1. Shopify管理画面でタグ・コレクション・商品タイプを確認');
      console.log('2. ストアフロントでのカテゴリ検索機能を実装');
      console.log('3. 商品登録時の自動カテゴリ設定機能を実装');
    }

  } catch (error) {
    console.error('❌ カテゴリ連携エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * 特定商品のカテゴリ情報を分析
 */
async function analyzeSingleProduct(productSku) {
  console.log(`🔍 === 商品カテゴリ分析: ${productSku} ===`);

  try {
    const categoryMaster = loadCategoryMaster();
    const categoryInfo = extractCategoryFromSku(productSku);

    if (!categoryInfo) {
      console.log('❌ SKUからカテゴリ情報を抽出できません');
      return;
    }

    console.log('📊 抽出されたカテゴリ情報:');
    console.log(`   カテゴリコード: ${categoryInfo.categoryCode}`);
    console.log(`   サブカテゴリコード: ${categoryInfo.subCategoryCode}`);
    console.log(`   アイテムコード: ${categoryInfo.itemCode}`);
    console.log('');

    const tags = generateShopifyTags(
      categoryInfo.categoryCode,
      categoryInfo.subCategoryCode,
      categoryMaster
    );

    const productType = determineShopifyProductType(
      categoryInfo.categoryCode,
      categoryInfo.subCategoryCode
    );

    const collection = determineShopifyCollection(categoryInfo.categoryCode);

    console.log('🏷️ 生成される情報:');
    console.log(`   タグ: ${tags.join(', ')}`);
    console.log(`   商品タイプ: ${productType}`);
    console.log(`   コレクション: ${collection}`);

  } catch (error) {
    console.error('❌ 分析エラー:', error);
  }
}

// コマンドライン引数の処理
const args = process.argv.slice(2);
const command = args[0];
const productSku = args[1];

async function main() {
  try {
    switch (command) {
      case 'implement':
        await implementCategoryIntegration();
        break;
      case 'analyze':
        if (!productSku) {
          console.error('使用方法: node category-integration.js analyze [SKU]');
          process.exit(1);
        }
        await analyzeSingleProduct(productSku);
        break;
      default:
        console.log('🏷️ カテゴリ連携ツール');
        console.log('');
        console.log('使用方法:');
        console.log('  node category-integration.js implement        # 全商品のカテゴリ連携実行');
        console.log('  node category-integration.js analyze [SKU]    # 特定商品のカテゴリ分析');
        console.log('');
        console.log('デフォルトで分析を実行します...');
        await analyzeSingleProduct('10101008');
        break;
    }
  } catch (error) {
    console.error('実行エラー:', error);
    process.exit(1);
  }
}

// スクリプト実行
main().catch(console.error);
