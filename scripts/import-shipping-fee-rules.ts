import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';

const prisma = new PrismaClient();

interface ShippingFeeRuleData {
  category: string;
  type: string;
  areaType: string;
  areaDetail?: string;
  notes?: string;
  basePrice: number;
}

async function importShippingFeeRules() {
  try {
    console.log('配送料金ルールのインポートを開始します...');

    // CSVファイルのパス
    const csvFilePath = path.join(process.cwd(), 'master-data-csv/other-master-data/配送料金マスター.csv');
    
    if (!fs.existsSync(csvFilePath)) {
      console.error(`CSVファイルが見つかりません: ${csvFilePath}`);
      return;
    }

    const rules: ShippingFeeRuleData[] = [];

    // CSVファイルを読み込み
    await new Promise<void>((resolve, reject) => {
      fs.createReadStream(csvFilePath)
        .pipe(csv())
        .on('data', (row) => {
          const rule: ShippingFeeRuleData = {
            category: row['配送カテゴリ'] || '',
            type: row['配送種類'] || '',
            areaType: row['エリア/詳細'] || '',
            areaDetail: undefined,
            notes: row['備考'] || undefined,
            basePrice: parseFloat(row['配送料金']) || 0
          };

          // エリア/詳細を分割
          if (rule.areaType.includes('／')) {
            const parts = rule.areaType.split('／');
            rule.areaType = parts[0];
            rule.areaDetail = parts.slice(1).join('／');
          }

          // 必須項目がある場合のみ追加
          if (rule.category && rule.type && rule.basePrice >= 0) {
            rules.push(rule);
          }
        })
        .on('end', resolve)
        .on('error', reject);
    });

    console.log(`${rules.length}件の配送料金ルールを読み込みました`);

    const shop = 'development.myshopify.com'; // 開発環境用

    // 既存データをクリア（開発環境のみ）
    if (process.env.NODE_ENV === 'development') {
      await prisma.shippingFeeRule.deleteMany({
        where: { shop }
      });
      console.log('既存の配送料金ルールをクリアしました');
    }

    // データをインポート
    let importedCount = 0;
    for (const rule of rules) {
      try {
        await prisma.shippingFeeRule.create({
          data: {
            shop: shop,
            category: rule.category,
            type: rule.type,
            areaType: rule.areaType,
            areaDetail: rule.areaDetail,
            basePrice: rule.basePrice,
            isActive: true,
          },
        });
        importedCount++;
      } catch (error) {
        console.error(`配送料金ルール ${rule.category}-${rule.type} のインポートでエラー:`, error);
      }
    }

    console.log(`${importedCount}件の配送料金ルールをインポートしました`);

    // インポート結果を確認
    const importedRules = await prisma.shippingFeeRule.findMany({
      where: { shop },
      orderBy: [
        { category: 'asc' },
        { type: 'asc' },
        { areaType: 'asc' }
      ]
    });

    console.log('\n=== インポート結果 ===');
    
    // カテゴリ別に集計
    const categoryStats = importedRules.reduce((acc, rule) => {
      if (!acc[rule.category]) {
        acc[rule.category] = [];
      }
      acc[rule.category].push(rule);
      return acc;
    }, {} as Record<string, typeof importedRules>);

    Object.entries(categoryStats).forEach(([category, categoryRules]) => {
      console.log(`\n【${category}】 (${categoryRules.length}件)`);
      categoryRules.forEach((rule, index) => {
        const areaInfo = rule.areaDetail 
          ? `${rule.areaType}（${rule.areaDetail}）`
          : rule.areaType;
        console.log(`  ${index + 1}. ${rule.type} - ${areaInfo}: ¥${rule.basePrice.toLocaleString()}`);
      });
    });

    // 統計情報
    const totalCount = await prisma.shippingFeeRule.count({ where: { shop } });
    const avgPrice = await prisma.shippingFeeRule.aggregate({
      where: { shop, basePrice: { gt: 0 } },
      _avg: { basePrice: true }
    });

    console.log('\n=== 統計情報 ===');
    console.log(`総ルール数: ${totalCount}件`);
    console.log(`平均料金: ¥${Math.round(avgPrice._avg.basePrice || 0).toLocaleString()}`);
    
    // カテゴリ別件数
    console.log('\nカテゴリ別件数:');
    Object.entries(categoryStats).forEach(([category, rules]) => {
      console.log(`  ${category}: ${rules.length}件`);
    });

  } catch (error) {
    console.error('配送料金ルールのインポートでエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  importShippingFeeRules();
}

export { importShippingFeeRules };
