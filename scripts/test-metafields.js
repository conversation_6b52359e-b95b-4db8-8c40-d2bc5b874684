// メタフィールドテスト用スクリプト
import { authenticate } from "../app/shopify.server.js";
import dotenv from "dotenv";

dotenv.config();

/**
 * 商品のメタフィールドを取得するテスト関数
 * @param {string} productId - Shopify商品ID（gid://shopify/Product/XXXXX形式）
 */
async function testProductMetafields(productId) {
  try {
    // 認証情報を取得
    const shop = process.env.SHOPIFY_SHOP;
    const accessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
    
    if (!shop || !accessToken) {
      console.error("環境変数が設定されていません。.envファイルを確認してください。");
      return;
    }
    
    // GraphQL APIを使用してメタフィールドを取得
    const response = await fetch(`https://${shop}/admin/api/2023-01/graphql.json`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": accessToken,
      },
      body: JSON.stringify({
        query: `
          query {
            product(id: "${productId}") {
              id
              title
              metafields(first: 20) {
                edges {
                  node {
                    id
                    namespace
                    key
                    value
                    type
                  }
                }
              }
            }
          }
        `
      }),
    });
    
    const data = await response.json();
    
    if (data.errors) {
      console.error("GraphQLエラー:", data.errors);
      return;
    }
    
    // 商品情報を表示
    console.log("商品情報:");
    console.log(`ID: ${data.data.product.id}`);
    console.log(`タイトル: ${data.data.product.title}`);
    
    // メタフィールドを表示
    console.log("\nメタフィールド:");
    const metafields = data.data.product.metafields.edges;
    
    if (metafields.length === 0) {
      console.log("メタフィールドが見つかりませんでした。");
      return;
    }
    
    // 必要なメタフィールドをチェック
    const requiredMetafields = [
      "rental.variant_mapping",
      "rental.pricing",
      "rental.basic_info",
      "rental.variation_type",
      "rental.status"
    ];
    
    const foundMetafields = {};
    
    metafields.forEach(({ node }) => {
      const fullKey = `${node.namespace}.${node.key}`;
      console.log(`${fullKey}: ${node.type}`);
      console.log(node.value);
      console.log("---");
      
      if (requiredMetafields.includes(fullKey)) {
        foundMetafields[fullKey] = true;
      }
    });
    
    // 必要なメタフィールドが存在するか確認
    console.log("\n必要なメタフィールドのチェック:");
    requiredMetafields.forEach(key => {
      console.log(`${key}: ${foundMetafields[key] ? "✅ 存在します" : "❌ 存在しません"}`);
    });
    
  } catch (error) {
    console.error("エラーが発生しました:", error);
  }
}

// コマンドライン引数から商品IDを取得
const productId = process.argv[2];

if (!productId) {
  console.error("使用方法: node scripts/test-metafields.js gid://shopify/Product/XXXXX");
  process.exit(1);
}

// テスト実行
testProductMetafields(productId);
