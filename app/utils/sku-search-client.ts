/**
 * SKU検索ユーティリティ（クライアントサイド用）
 * 
 * フロントエンドでの商品検索に使用する関数群
 */

/**
 * 基本SKU形式かどうかを判定（XXX-XX-XXX形式）
 * @param sku SKU文字列
 * @returns 基本SKU形式かどうか
 */
function isBasicSkuFormat(sku: string): boolean {
  const basicSkuPattern = /^\d{3}-\d{2}-\d{3}$/;
  return basicSkuPattern.test(sku);
}

/**
 * 数字のみの文字列かどうかを判定
 * @param str 文字列
 * @returns 数字のみかどうか
 */
function isNumericOnly(str: string): boolean {
  return /^\d+$/.test(str);
}

/**
 * 数字のみの文字列を基本SKU形式にフォーマット
 * @param numericStr 数字文字列（例: "20107107"）
 * @returns フォーマットされたSKU（例: "201-07-107"）または null
 */
function formatNumericToSku(numericStr: string): string | null {
  if (numericStr.length === 8) {
    return `${numericStr.substring(0, 3)}-${numericStr.substring(3, 5)}-${numericStr.substring(5, 8)}`;
  }
  
  if (numericStr.length >= 8) {
    const first8 = numericStr.substring(0, 8);
    return `${first8.substring(0, 3)}-${first8.substring(3, 5)}-${first8.substring(5, 8)}`;
  }
  
  return null;
}

/**
 * 商品がSKU検索条件にマッチするかどうかを判定
 * @param product 商品オブジェクト
 * @param searchQuery 検索クエリ
 * @returns マッチするかどうか
 */
export function matchesSkuSearch(product: { sku: string; title: string }, searchQuery: string): boolean {
  if (!searchQuery || searchQuery.trim() === '') {
    return true;
  }

  const trimmedQuery = searchQuery.trim().toLowerCase();
  const productSku = product.sku.toLowerCase();
  const productTitle = product.title.toLowerCase();

  // タイトル検索
  if (productTitle.includes(trimmedQuery)) {
    return true;
  }

  // 1. 完全一致検索
  if (productSku === trimmedQuery) {
    return true;
  }

  // 2. 部分一致検索
  if (productSku.includes(trimmedQuery)) {
    return true;
  }

  // 3. 基本SKU形式の検索（201-07-107 形式）
  if (isBasicSkuFormat(trimmedQuery)) {
    // 基本SKUから始まるバリアントを検索
    if (productSku.startsWith(trimmedQuery + '-')) {
      return true;
    }
  }

  // 4. ハイフンなし形式の検索（20107107 → 201-07-107）
  if (isNumericOnly(trimmedQuery) && trimmedQuery.length >= 8) {
    const formattedSku = formatNumericToSku(trimmedQuery);
    if (formattedSku) {
      const formattedSkuLower = formattedSku.toLowerCase();
      // フォーマットされたSKUでの検索
      if (productSku === formattedSkuLower || productSku.startsWith(formattedSkuLower + '-')) {
        return true;
      }
    }
  }

  // 5. 前方10文字での検索（201-07-107-1D → 201-07-107）
  if (trimmedQuery.length > 10) {
    const prefix = trimmedQuery.substring(0, 10);
    if (isBasicSkuFormat(prefix)) {
      if (productSku.startsWith(prefix.toLowerCase())) {
        return true;
      }
    }
  }

  return false;
}

/**
 * 商品リストをSKU検索でフィルタリング
 * @param products 商品リスト
 * @param searchQuery 検索クエリ
 * @returns フィルタリングされた商品リスト
 */
export function filterProductsBySkuSearch<T extends { sku: string; title: string }>(
  products: T[], 
  searchQuery: string
): T[] {
  if (!searchQuery || searchQuery.trim() === '') {
    return products;
  }

  return products.filter(product => matchesSkuSearch(product, searchQuery));
}

/**
 * SKU検索の説明を生成（デバッグ用）
 * @param searchQuery 検索クエリ
 * @returns 検索条件の説明
 */
export function explainSkuSearchClient(searchQuery: string): string[] {
  const explanations = [];
  
  if (!searchQuery || searchQuery.trim() === '') {
    return ['検索クエリが空です'];
  }

  const trimmedQuery = searchQuery.trim();
  explanations.push(`検索クエリ: "${trimmedQuery}"`);

  // 検索条件を説明
  explanations.push('適用される検索条件:');
  explanations.push('  1. タイトル部分一致');
  explanations.push('  2. SKU完全一致');
  explanations.push('  3. SKU部分一致');

  if (isBasicSkuFormat(trimmedQuery)) {
    explanations.push('  4. 基本SKU形式 → バリアント検索');
  }

  if (isNumericOnly(trimmedQuery) && trimmedQuery.length >= 8) {
    const formattedSku = formatNumericToSku(trimmedQuery);
    if (formattedSku) {
      explanations.push(`  5. 数字形式 → フォーマット: "${formattedSku}"`);
    }
  }

  if (trimmedQuery.length > 10) {
    const prefix = trimmedQuery.substring(0, 10);
    if (isBasicSkuFormat(prefix)) {
      explanations.push(`  6. 前方10文字: "${prefix}"`);
    }
  }

  return explanations;
}

/**
 * SKU検索のテストケース（クライアントサイド用）
 */
export const SKU_SEARCH_CLIENT_TEST_CASES = [
  {
    input: '201-07-107',
    description: '基本SKU形式',
    testProducts: [
      { sku: '201-07-107-1D', title: 'テスト商品1', shouldMatch: true },
      { sku: '201-07-107-bk', title: 'テスト商品2', shouldMatch: true },
      { sku: '212-05-023-1D', title: 'テスト商品3', shouldMatch: false },
    ]
  },
  {
    input: '20107107',
    description: 'ハイフンなし数字',
    testProducts: [
      { sku: '201-07-107-1D', title: 'テスト商品1', shouldMatch: true },
      { sku: '201-07-107-2D', title: 'テスト商品2', shouldMatch: true },
      { sku: '212-05-023-1D', title: 'テスト商品3', shouldMatch: false },
    ]
  },
  {
    input: '201',
    description: '部分SKU',
    testProducts: [
      { sku: '201-07-107-1D', title: 'テスト商品1', shouldMatch: true },
      { sku: '201-06-555-1D', title: 'テスト商品2', shouldMatch: true },
      { sku: '212-05-023-1D', title: 'テスト商品3', shouldMatch: false },
    ]
  }
];
