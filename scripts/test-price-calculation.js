/**
 * 価格計算テストスクリプト
 */

// 修正後の価格計算ロジックをテスト
function calculateRentalPrice(basePrice, rentalDays) {
  let productTotal = basePrice; // 1日目は基本料金
  
  if (rentalDays > 1) {
    // 2-7日目: 基本料金の20%
    const day2to7Count = Math.min(rentalDays - 1, 6); // 最大6日間（2日目〜7日目）
    productTotal += basePrice * 0.2 * day2to7Count;
    
    // 8日目以降: 基本料金の10%
    if (rentalDays > 7) {
      const day8PlusCount = rentalDays - 7;
      productTotal += basePrice * 0.1 * day8PlusCount;
    }
  }
  
  return Math.round(productTotal);
}

// 通貨フォーマット関数
function formatCurrency(amount) {
  const yenAmount = Math.round(amount / 100);
  return new Intl.NumberFormat('ja-JP', {
    style: 'currency',
    currency: 'JPY'
  }).format(yenAmount);
}

console.log('🧮 価格計算テストを開始...\n');

// テストケース
const testCases = [
  { basePrice: 300000, days: 1, expected: 3000 }, // 1日: ¥3,000
  { basePrice: 300000, days: 2, expected: 3600 }, // 2日: ¥3,000 + ¥600 = ¥3,600
  { basePrice: 300000, days: 3, expected: 4200 }, // 3日: ¥3,000 + ¥1,200 = ¥4,200
  { basePrice: 300000, days: 4, expected: 4800 }, // 4日: ¥3,000 + ¥1,800 = ¥4,800
  { basePrice: 300000, days: 7, expected: 6600 }, // 7日: ¥3,000 + ¥3,600 = ¥6,600
  { basePrice: 300000, days: 8, expected: 6900 }, // 8日: ¥3,000 + ¥3,600 + ¥300 = ¥6,900
  { basePrice: 300000, days: 10, expected: 7500 }, // 10日: ¥3,000 + ¥3,600 + ¥900 = ¥7,500
];

console.log('=== 価格計算テスト結果 ===');
testCases.forEach((testCase, index) => {
  const { basePrice, days, expected } = testCase;
  const calculated = calculateRentalPrice(basePrice, days);
  const isCorrect = calculated === expected;
  
  console.log(`${index + 1}. ${days}日間レンタル:`);
  console.log(`   基本価格: ${formatCurrency(basePrice)}`);
  console.log(`   計算結果: ${formatCurrency(calculated)}`);
  console.log(`   期待値: ${formatCurrency(expected)}`);
  console.log(`   判定: ${isCorrect ? '✅ 正しい' : '❌ 間違い'}`);
  
  if (!isCorrect) {
    console.log(`   差額: ${formatCurrency(Math.abs(calculated - expected))}`);
  }
  
  console.log('');
});

// 実際の商品での計算例
console.log('=== 実際の商品での計算例 ===');
console.log('商品: シルバートレー　オーバル　フリルフレーム　持ち手付');
console.log('基本価格: ¥3,000（300,000セント）');
console.log('');

const realTestCases = [
  { days: 1, description: '1日レンタル' },
  { days: 3, description: '3日レンタル（実際の予約）' },
  { days: 7, description: '1週間レンタル' },
];

realTestCases.forEach(({ days, description }) => {
  const basePrice = 300000; // ¥3,000をセント単位
  const total = calculateRentalPrice(basePrice, days);
  
  console.log(`${description}:`);
  console.log(`  期間: ${days}日間`);
  console.log(`  料金: ${formatCurrency(total)}`);
  
  // 内訳を表示
  if (days === 1) {
    console.log(`  内訳: 基本料金 ${formatCurrency(basePrice)}`);
  } else if (days <= 7) {
    const day1Price = basePrice;
    const additionalDays = days - 1;
    const additionalPrice = basePrice * 0.2 * additionalDays;
    console.log(`  内訳: 基本料金 ${formatCurrency(day1Price)} + 追加${additionalDays}日 ${formatCurrency(additionalPrice)}`);
  } else {
    const day1Price = basePrice;
    const day2to7Price = basePrice * 0.2 * 6;
    const day8PlusPrice = basePrice * 0.1 * (days - 7);
    console.log(`  内訳: 基本料金 ${formatCurrency(day1Price)} + 2-7日目 ${formatCurrency(day2to7Price)} + 8日目以降 ${formatCurrency(day8PlusPrice)}`);
  }
  
  console.log('');
});

console.log('✅ 価格計算テスト完了');
