{%- if customer and customer.metafields.wishlist -%}
    {%- for item in customer.metafields.wishlist -%}
        {%- assign wishlist_id = item[0] | abs -%}
        {%- assign wishlist_handle = item[1] -%}
        {%- if current_variant_id == wishlist_id and product.handle == wishlist_handle -%}
            {%- assign present_in_wishlist = true -%}
        {%- endif -%}
    {%- endfor -%}
{%- endif -%}
<a href="/account" class="btn btn--text btn--status px-md-0 js-store-lists-add-wishlist"
    {% if present_in_wishlist %} data-button-status="added"{% endif %}>
    <span class="d-flex flex-center"><i class="mr-5 mb-1">{% render 'icon-theme-180' %}</i>{{ 'products.product.add_to_widhlist.title' | t }}</span>
    <span class="d-flex flex-center" data-button-content="added"><i class="mr-5 mb-1">{% render 'icon-theme-181' %}</i>{{ 'products.product.add_to_widhlist.added' | t }}</span>
</a>
{%- assign present_in_wishlist = false -%}
