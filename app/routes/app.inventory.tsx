import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit, useActionData, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  LegacyCard,
  Button,
  Text,
  BlockStack,
  Banner,
  FormLayout,
  TextField,
  Select,
  DataTable,
  Badge,
  Tabs,
  InlineStack,
  Spinner,
  EmptyState
} from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import { PrismaClient } from "@prisma/client";
import { useState } from "react";
import { InventorySyncService } from "../services/sync/inventory-sync.service";
import { InventoryCalendarService } from "../services/inventory-calendar.service";
import { addDays, format, parseISO } from "date-fns";
import { ja } from "date-fns/locale";
import { formatSkuWithHyphens } from "../utils/sku-formatter.js";

const prisma = new PrismaClient();

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);

  const url = new URL(request.url);
  const locationId = url.searchParams.get("location") || "";
  const status = url.searchParams.get("status") || "";
  const q = url.searchParams.get("q") || "";
  const page = parseInt(url.searchParams.get("page") || "1", 10);
  const pageSize = 20;

  // 検索条件を構築
  const where: any = {};

  if (locationId) {
    where.locationId = locationId;
  }

  if (status) {
    where.status = status;
  }

  if (q) {
    where.OR = [
      { title: { contains: q, mode: 'insensitive' } },
      { sku: { contains: q, mode: 'insensitive' } }
    ];
  }

  // 商品データを取得
  const products = await prisma.product.findMany({
    where,
    select: {
      id: true,
      shopifyId: true,
      title: true,
      sku: true,
      status: true,
      price: true,
      locationId: true,
      metadata: true,
      updatedAt: true,
      bookings: {
        where: {
          status: { in: ['PROVISIONAL', 'CONFIRMED'] },
          startDate: { lte: addDays(new Date(), 30) },
          endDate: { gte: new Date() }
        },
        orderBy: { startDate: 'asc' },
        take: 1
      },
      maintenances: {
        where: {
          endDate: { gte: new Date() }
        },
        orderBy: { startDate: 'asc' },
        take: 1
      }
    },
    orderBy: { updatedAt: 'desc' },
    skip: (page - 1) * pageSize,
    take: pageSize
  });

  // 商品の総数を取得
  const totalCount = await prisma.product.count({ where });

  // ロケーション情報を取得
  const locations = await prisma.location.findMany({
    where: { isActive: true },
    orderBy: { name: 'asc' }
  });

  // 在庫カレンダー情報を取得（今日から7日分）
  const today = new Date();
  const nextWeek = addDays(today, 7);

  const inventoryCalendar = await prisma.inventoryCalendar.findMany({
    where: {
      productId: { in: products.map(p => p.id) },
      date: {
        gte: today,
        lte: nextWeek
      }
    }
  });

  // 商品データを整形
  const formattedProducts = products.map(product => {
    // 次の予約情報
    const nextBooking = product.bookings[0];
    const nextBookingDate = nextBooking
      ? format(new Date(nextBooking.startDate), 'yyyy/MM/dd', { locale: ja })
      : '';

    // 次のメンテナンス情報
    const nextMaintenance = product.maintenances[0];
    const nextMaintenanceDate = nextMaintenance
      ? format(new Date(nextMaintenance.startDate), 'yyyy/MM/dd', { locale: ja })
      : '';

    // 在庫カレンダー情報を整形
    const calendar: { [date: string]: { available: boolean, reason?: string } } = {};

    // 7日分の日付を生成
    for (let i = 0; i < 7; i++) {
      const date = addDays(today, i);
      const dateStr = format(date, 'yyyy-MM-dd');

      // デフォルトは利用可能
      calendar[dateStr] = { available: true };
    }

    // 在庫カレンダー情報を反映
    inventoryCalendar
      .filter(entry => entry.productId === product.id)
      .forEach(entry => {
        const dateStr = format(new Date(entry.date), 'yyyy-MM-dd');
        if (calendar[dateStr]) {
          calendar[dateStr] = {
            available: entry.isAvailable,
            reason: entry.unavailableReason || undefined
          };
        }
      });

    // 在庫ロケーション
    const location = locations.find(loc => loc.id === product.locationId);

    return {
      id: product.id,
      shopifyId: product.shopifyId,
      title: product.title,
      sku: product.sku,
      status: product.status,
      price: product.price?.toString() || '',
      location: location?.name || '',
      nextBooking: nextBookingDate,
      nextMaintenance: nextMaintenanceDate,
      calendar,
      updatedAt: format(new Date(product.updatedAt), 'yyyy/MM/dd HH:mm', { locale: ja })
    };
  });

  return json({
    products: formattedProducts,
    pagination: {
      currentPage: page,
      totalPages: Math.ceil(totalCount / pageSize),
      totalCount
    },
    locations: locations.map(loc => ({
      label: loc.name,
      value: loc.id
    })),
    filters: {
      location: locationId,
      status,
      q
    }
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const { session } = await authenticate.admin(request);

  const formData = await request.formData();
  const action = formData.get("action") as string;
  const productId = formData.get("productId") as string;
  const shopifyProductId = formData.get("shopifyProductId") as string;

  try {
    if (action === "sync_inventory") {
      // 在庫同期
      const inventorySyncService = new InventorySyncService(request);
      const result = await inventorySyncService.syncInventoryFromShopify(shopifyProductId);
      return json({ success: true, result });
    } else if (action === "update_calendar") {
      // 在庫カレンダー更新
      const inventoryCalendarService = new InventoryCalendarService();
      await inventoryCalendarService.updateInventoryCalendar(productId);
      return json({ success: true, message: "在庫カレンダーを更新しました" });
    }

    return json({ success: false, error: "無効なアクション" });
  } catch (error) {
    return json({ success: false, error: String(error) });
  }
}

export default function InventoryPage() {
  const { products, pagination, locations, filters } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);

  // 在庫同期
  const handleSyncInventory = (shopifyProductId: string) => {
    setIsSubmitting(true);
    const formData = new FormData();
    formData.append("action", "sync_inventory");
    formData.append("shopifyProductId", shopifyProductId);
    submit(formData, { method: "post" });
  };

  // 在庫カレンダー更新
  const handleUpdateCalendar = (productId: string) => {
    setIsSubmitting(true);
    const formData = new FormData();
    formData.append("action", "update_calendar");
    formData.append("productId", productId);
    submit(formData, { method: "post" });
  };

  // 送信完了時の処理
  if (actionData && isSubmitting) {
    setIsSubmitting(false);
  }

  // 在庫状態に応じたバッジを生成
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return <Badge tone="success">利用可能</Badge>;
      case 'MAINTENANCE':
        return <Badge tone="warning">メンテナンス中</Badge>;
      case 'DAMAGED':
        return <Badge tone="critical">破損</Badge>;
      case 'UNAVAILABLE':
        return <Badge tone="critical">利用不可</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // 在庫カレンダーのセルを生成
  const getCalendarCell = (calendar: any, dateStr: string) => {
    const entry = calendar[dateStr];
    if (!entry) return <Text>-</Text>;

    if (entry.available) {
      return <Badge tone="success">○</Badge>;
    } else {
      switch (entry.reason) {
        case 'BOOKED':
          return <Badge tone="attention">予</Badge>;
        case 'MAINTENANCE':
          return <Badge tone="warning">メ</Badge>;
        default:
          return <Badge tone="critical">×</Badge>;
      }
    }
  };

  // タブの設定
  const tabs = [
    {
      id: 'list',
      content: 'リスト表示',
      accessibilityLabel: '商品リスト',
      panelID: 'list-panel',
    },
    {
      id: 'calendar',
      content: 'カレンダー表示',
      accessibilityLabel: '在庫カレンダー',
      panelID: 'calendar-panel',
    },
  ];

  // 日付ヘッダーを生成
  const dateHeaders = [];
  for (let i = 0; i < 7; i++) {
    const date = addDays(new Date(), i);
    dateHeaders.push(format(date, 'MM/dd(E)', { locale: ja }));
  }

  // データテーブルの行を生成
  const rows = products.map(product => [
    formatSkuWithHyphens(product.sku),
    product.title,
    getStatusBadge(product.status),
    product.location,
    product.nextBooking || '-',
    product.nextMaintenance || '-',
    <InlineStack gap="200">
      <Button
        size="slim"
        onClick={() => handleSyncInventory(product.shopifyId)}
        loading={isSubmitting}
      >
        在庫同期
      </Button>
      <Button
        size="slim"
        onClick={() => handleUpdateCalendar(product.id)}
        loading={isSubmitting}
      >
        カレンダー更新
      </Button>
    </InlineStack>
  ]);

  // カレンダー表示の行を生成
  const calendarRows = products.map(product => {
    const cells = [formatSkuWithHyphens(product.sku), product.title];

    // 7日分の在庫状態を追加
    for (let i = 0; i < 7; i++) {
      const date = addDays(new Date(), i);
      const dateStr = format(date, 'yyyy-MM-dd');
      cells.push(getCalendarCell(product.calendar, dateStr));
    }

    return cells;
  });

  return (
    <Page
      title="在庫管理"
      subtitle="商品の在庫状態と予約状況"
      backAction={{ content: "ホーム", url: "/app" }}
    >
      <Layout>
        <Layout.Section>
          {actionData?.success === false && (
            <Banner status="critical">
              <p>{actionData.error}</p>
            </Banner>
          )}

          {actionData?.success === true && (
            <Banner status="success">
              <p>{actionData.message || "操作が完了しました"}</p>
            </Banner>
          )}
        </Layout.Section>

        <Layout.Section>
          <Tabs tabs={tabs} selected={selectedTab} onSelect={setSelectedTab} />

          {selectedTab === 0 && (
            <LegacyCard>
              <LegacyCard.Section>
                <DataTable
                  columnContentTypes={['text', 'text', 'text', 'text', 'text', 'text', 'text']}
                  headings={['SKU', '商品名', 'ステータス', 'ロケーション', '次回予約', '次回メンテナンス', '操作']}
                  rows={rows}
                  footerContent={`全${pagination.totalCount}件中 ${(pagination.currentPage - 1) * 20 + 1}〜${Math.min(pagination.currentPage * 20, pagination.totalCount)}件を表示`}
                />
              </LegacyCard.Section>
            </LegacyCard>
          )}

          {selectedTab === 1 && (
            <LegacyCard>
              <LegacyCard.Section>
                <DataTable
                  columnContentTypes={['text', 'text', 'text', 'text', 'text', 'text', 'text', 'text', 'text']}
                  headings={['SKU', '商品名', ...dateHeaders]}
                  rows={calendarRows}
                  footerContent={
                    <BlockStack gap="200">
                      <Text>○: 利用可能 / 予: 予約中 / メ: メンテナンス中 / ×: 利用不可</Text>
                      <Text>{`全${pagination.totalCount}件中 ${(pagination.currentPage - 1) * 20 + 1}〜${Math.min(pagination.currentPage * 20, pagination.totalCount)}件を表示`}</Text>
                    </BlockStack>
                  }
                />
              </LegacyCard.Section>
            </LegacyCard>
          )}
        </Layout.Section>
      </Layout>
    </Page>
  );
}
