import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品検索のクエリ
const GET_PRODUCTS_QUERY = gql`
  query getProducts($query: String!, $first: Int!, $after: String) {
    products(first: $first, after: $after, query: $query) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          handle
          vendor
          productType
          tags
          variants(first: 50) {
            edges {
              node {
                id
                title
                sku
                price
                inventoryQuantity
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    }
  }
`;

// 商品を検索する関数
async function getProducts(query = '', limit = 50) {
  const products = [];
  let hasNextPage = true;
  let cursor = null;

  while (hasNextPage) {
    try {
      const variables = {
        query,
        first: limit,
        after: cursor
      };

      const result = await graphQLClient.request(GET_PRODUCTS_QUERY, variables);

      const edges = result.products.edges;
      products.push(...edges.map(edge => edge.node));

      hasNextPage = result.products.pageInfo.hasNextPage;
      cursor = result.products.pageInfo.endCursor;
    } catch (error) {
      console.error('商品検索エラー:', error);
      hasNextPage = false;
    }
  }

  return products;
}

// メタフィールドの状態を確認する関数
function checkMetafields(product) {
  const metafields = product.metafields.edges.map(edge => edge.node);

  // 必要なメタフィールド
  const requiredMetafields = [
    { namespace: 'rental', key: 'basic_info' },
    { namespace: 'rental', key: 'pricing' },
    { namespace: 'rental', key: 'inventory_items' },
    { namespace: 'rental', key: 'reservation_info' }
  ];

  // 各メタフィールドの状態を確認
  const metafieldStatus = {};

  for (const required of requiredMetafields) {
    const metafield = metafields.find(
      m => m.namespace === required.namespace && m.key === required.key
    );

    metafieldStatus[`${required.namespace}.${required.key}`] = {
      exists: !!metafield,
      value: metafield ? metafield.value : null
    };
  }

  return metafieldStatus;
}

// バリアントSKUの状態を確認する関数
function checkVariantSKUs(product) {
  const variants = product.variants.edges.map(edge => edge.node);

  // SKUパターンをチェック
  const skuPatterns = {
    valid: 0,
    invalid: 0,
    empty: 0
  };

  const skuDetails = [];

  for (const variant of variants) {
    const sku = variant.sku;

    if (!sku) {
      skuPatterns.empty++;
      skuDetails.push({
        title: variant.title,
        sku,
        status: 'empty'
      });
      continue;
    }

    // 正規化されたSKUパターン: 商品コード-詳細コード-バリエーション
    const isValid = sku.match(/^[0-9]+-[0-9]+-[0-9]+$/);

    if (isValid) {
      skuPatterns.valid++;
      skuDetails.push({
        title: variant.title,
        sku,
        status: 'valid'
      });
    } else {
      skuPatterns.invalid++;
      skuDetails.push({
        title: variant.title,
        sku,
        status: 'invalid'
      });
    }
  }

  return {
    patterns: skuPatterns,
    details: skuDetails
  };
}

// メイン処理
async function main() {
  try {
    // 商品を検索
    console.log('商品を検索中...');
    const products = await getProducts('', 10);
    console.log(`${products.length}件の商品が見つかりました`);

    // 各商品のメタフィールドとSKUを確認
    const results = [];

    for (const product of products) {
      console.log(`\n商品 ${product.title} の確認中...`);

      // メタフィールドの状態を確認
      const metafieldStatus = checkMetafields(product);

      // バリアントSKUの状態を確認
      const skuStatus = checkVariantSKUs(product);

      // 結果を保存
      results.push({
        id: product.id,
        title: product.title,
        metafields: metafieldStatus,
        skus: skuStatus
      });

      // 結果を表示
      console.log(`商品 ${product.title} の確認結果:`);
      console.log('メタフィールド:');
      for (const [key, status] of Object.entries(metafieldStatus)) {
        console.log(`  ${key}: ${status.exists ? '存在します' : '存在しません'}`);
      }

      console.log('SKU:');
      console.log(`  有効: ${skuStatus.patterns.valid}件`);
      console.log(`  無効: ${skuStatus.patterns.invalid}件`);
      console.log(`  空: ${skuStatus.patterns.empty}件`);
    }

    // 全体の統計を表示
    console.log('\n=== 全体の統計 ===');

    // メタフィールドの統計
    const metafieldStats = {
      'rental.basic_info': { exists: 0, missing: 0 },
      'rental.pricing': { exists: 0, missing: 0 },
      'rental.inventory_items': { exists: 0, missing: 0 },
      'rental.reservation_info': { exists: 0, missing: 0 }
    };

    // SKUの統計
    const skuStats = {
      valid: 0,
      invalid: 0,
      empty: 0
    };

    // 統計を集計
    for (const result of results) {
      for (const [key, status] of Object.entries(result.metafields)) {
        if (status.exists) {
          metafieldStats[key].exists++;
        } else {
          metafieldStats[key].missing++;
        }
      }

      skuStats.valid += result.skus.patterns.valid;
      skuStats.invalid += result.skus.patterns.invalid;
      skuStats.empty += result.skus.patterns.empty;
    }

    // 統計を表示
    console.log('メタフィールドの統計:');
    for (const [key, stats] of Object.entries(metafieldStats)) {
      console.log(`  ${key}: 存在=${stats.exists}件, 欠落=${stats.missing}件`);
    }

    console.log('SKUの統計:');
    console.log(`  有効: ${skuStats.valid}件`);
    console.log(`  無効: ${skuStats.invalid}件`);
    console.log(`  空: ${skuStats.empty}件`);

    console.log('\n処理が完了しました。');
  } catch (error) {
    console.error('エラーが発生しました:', error);
  }
}

// 実行
main();
