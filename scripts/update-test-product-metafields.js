/**
 * テスト商品のメタフィールドを更新するスクリプト
 * 
 * このスクリプトは、Shopifyに登録されたテスト商品のメタフィールドを更新します。
 * 特に、basic_info、pricing、status、locationなどのメタフィールドを設定します。
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品を取得するクエリ
const GET_PRODUCTS = gql`
  query getProducts($first: Int!, $after: String) {
    products(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          handle
          variants(first: 10) {
            edges {
              node {
                id
                title
                sku
                price
                inventoryQuantity
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
              }
            }
          }
        }
      }
    }
  }
`;

// メタフィールドを設定するミューテーション
const SET_METAFIELDS = gql`
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 商品の在庫を更新するミューテーション
const UPDATE_INVENTORY = gql`
  mutation inventoryBulkAdjustQuantityAtLocation($inventoryItemAdjustments: [InventoryAdjustItemInput!]!) {
    inventoryBulkAdjustQuantityAtLocation(
      inventoryItemAdjustments: $inventoryItemAdjustments,
      locationId: "gid://shopify/Location/1"
    ) {
      inventoryLevels {
        available
        item {
          id
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 商品のバリアントを取得するクエリ
const GET_VARIANT_WITH_INVENTORY = gql`
  query getVariantWithInventory($id: ID!) {
    productVariant(id: $id) {
      id
      inventoryItem {
        id
        inventoryLevels(first: 1) {
          edges {
            node {
              id
              available
            }
          }
        }
      }
    }
  }
`;

// SKUから商品コードと詳細コードを抽出する関数
function extractCodesFromSKU(sku) {
  if (!sku) return { productCode: '', detailCode: '' };
  
  const parts = sku.split('-');
  if (parts.length >= 2) {
    // 最後の部分を詳細コード、それ以外を商品コードとする
    const detailCode = parts.pop();
    const productCode = parts.join('-');
    return { productCode, detailCode };
  }
  
  return { productCode: sku, detailCode: '' };
}

// 商品の基本情報メタフィールドを生成する関数
function generateBasicInfo(product, baseVariant) {
  const sku = baseVariant?.sku || '';
  const { productCode, detailCode } = extractCodesFromSKU(sku);
  
  // 商品タイトルから情報を抽出
  const title = product.title;
  
  // 寸法情報を抽出（タイトルから推測）
  const dimensions = {
    width: 0,
    depth: 0,
    height: 0,
    seatHeight: 0
  };
  
  // 色情報を抽出（タイトルから推測）
  let color = '';
  if (title.includes('ホワイト')) color = 'ホワイト';
  else if (title.includes('ブラック')) color = 'ブラック';
  else if (title.includes('レッド')) color = 'レッド';
  else if (title.includes('ブルー')) color = 'ブルー';
  else if (title.includes('グリーン')) color = 'グリーン';
  else if (title.includes('イエロー')) color = 'イエロー';
  else if (title.includes('ピンク')) color = 'ピンク';
  else if (title.includes('パープル')) color = 'パープル';
  else if (title.includes('オレンジ')) color = 'オレンジ';
  else if (title.includes('ブラウン')) color = 'ブラウン';
  else if (title.includes('グレー')) color = 'グレー';
  else if (title.includes('ゴールド')) color = 'ゴールド';
  else if (title.includes('シルバー')) color = 'シルバー';
  else color = '不明';
  
  // 素材情報を抽出（タイトルから推測）
  let material = '';
  if (title.includes('ファブリック')) material = 'ファブリック';
  else if (title.includes('レザー')) material = 'レザー';
  else if (title.includes('ウッド')) material = 'ウッド';
  else if (title.includes('ガラス')) material = 'ガラス';
  else if (title.includes('スチール')) material = 'スチール';
  else if (title.includes('プラスチック')) material = 'プラスチック';
  else material = '不明';
  
  return {
    productCode,
    detailCode,
    kana: title,
    dimensions,
    color,
    material,
    notes: ''
  };
}

// 商品の料金設定メタフィールドを生成する関数
function generatePricing(product, baseVariant) {
  const basePrice = parseFloat(baseVariant?.price) || 0;
  
  return {
    basePrice,
    depositRate: 0.1,
    discountRules: {
      day2_6_rate: 0.2,
      day7_plus_rate: 0.1
    },
    minimumDays: 1,
    maximumDays: 30
  };
}

// 商品のメタフィールドを設定する関数
async function setProductMetafields(product) {
  try {
    const variants = product.variants.edges.map(edge => edge.node);
    if (variants.length === 0) {
      console.error(`商品 ${product.title} にバリアントがありません`);
      return false;
    }
    
    // 基本バリアントを取得（1日レンタルまたはDefault Title）
    const baseVariant = variants.find(v => v.title.includes('1日') || v.title === 'Default Title') || variants[0];
    
    // 商品ID
    const productId = product.id;
    
    // 基本情報メタフィールドを生成
    const basicInfo = generateBasicInfo(product, baseVariant);
    
    // 料金設定メタフィールドを生成
    const pricing = generatePricing(product, baseVariant);
    
    // 在庫場所（ランダムにNYまたはPR）
    const location = Math.random() > 0.3 ? 'NY' : 'PR';
    
    // ステータス（ほとんどはavailable、一部はmaintenanceまたはunavailable）
    let status = 'available';
    if (product.title.toLowerCase().includes('メンテナンス')) {
      status = 'maintenance';
    } else if (product.title.toLowerCase().includes('廃棄')) {
      status = 'unavailable';
    }
    
    // メタフィールドを設定
    console.log(`商品 ${product.title} のメタフィールドを設定中...`);
    
    const metafields = [
      {
        ownerId: productId,
        namespace: 'rental',
        key: 'basic_info',
        value: JSON.stringify(basicInfo),
        type: 'json'
      },
      {
        ownerId: productId,
        namespace: 'rental',
        key: 'pricing',
        value: JSON.stringify(pricing),
        type: 'json'
      },
      {
        ownerId: productId,
        namespace: 'rental',
        key: 'location',
        value: location,
        type: 'single_line_text_field'
      },
      {
        ownerId: productId,
        namespace: 'rental',
        key: 'status',
        value: status,
        type: 'single_line_text_field'
      }
    ];
    
    const result = await client.request(SET_METAFIELDS, { metafields });
    
    if (result.metafieldsSet.userErrors.length > 0) {
      console.error(`商品 ${product.title} のメタフィールド設定中にエラーが発生しました:`, result.metafieldsSet.userErrors);
      return false;
    }
    
    console.log(`商品 ${product.title} のメタフィールドを設定しました`);
    
    // 在庫数を更新（メンテナンス中や廃棄予定の商品は在庫0、それ以外は在庫1）
    if (status !== 'available') {
      // 各バリアントの在庫を0に設定
      for (const variant of variants) {
        const variantResult = await client.request(GET_VARIANT_WITH_INVENTORY, { id: variant.id });
        const inventoryItemId = variantResult.productVariant.inventoryItem.id;
        
        await client.request(UPDATE_INVENTORY, {
          inventoryItemAdjustments: [
            {
              inventoryItemId,
              availableDelta: -variant.inventoryQuantity
            }
          ]
        });
        
        console.log(`バリアント ${variant.title} の在庫を0に設定しました`);
      }
    }
    
    return true;
  } catch (error) {
    console.error(`商品 ${product.title} のメタフィールド設定中にエラーが発生しました:`, error);
    return false;
  }
}

// すべての商品を取得して処理する関数
async function processAllProducts() {
  try {
    console.log('商品データを取得しています...');
    
    let hasNextPage = true;
    let cursor = null;
    let allProducts = [];
    
    // ページネーションを使用してすべての商品を取得
    while (hasNextPage) {
      const result = await client.request(GET_PRODUCTS, {
        first: 50,
        after: cursor
      });
      
      const products = result.products.edges.map(edge => edge.node);
      allProducts = allProducts.concat(products);
      
      hasNextPage = result.products.pageInfo.hasNextPage;
      cursor = result.products.pageInfo.endCursor;
      
      console.log(`${products.length}件の商品を取得しました。合計: ${allProducts.length}件`);
      
      if (hasNextPage) {
        // APIレート制限対策
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log(`合計${allProducts.length}件の商品を処理します...`);
    
    // 各商品のメタフィールドを設定
    let successCount = 0;
    for (const product of allProducts) {
      console.log(`\n商品 ${product.title} の処理を開始...`);
      
      // メタフィールドが既に設定されているかチェック
      const hasBasicInfo = product.metafields.edges.some(
        edge => edge.node.namespace === 'rental' && edge.node.key === 'basic_info'
      );
      
      if (hasBasicInfo) {
        console.log(`商品 ${product.title} には既に基本情報が設定されています。更新します。`);
      }
      
      // メタフィールドを設定
      const success = await setProductMetafields(product);
      
      if (success) {
        console.log(`商品 ${product.title} のメタフィールド設定が完了しました`);
        successCount++;
      } else {
        console.error(`商品 ${product.title} のメタフィールド設定に失敗しました`);
      }
      
      // APIレート制限対策
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log(`\n処理が完了しました。${successCount}/${allProducts.length}件の商品のメタフィールドを設定しました。`);
  } catch (error) {
    console.error('エラーが発生しました:', error);
  }
}

// スクリプト実行
processAllProducts();
