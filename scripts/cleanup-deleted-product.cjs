const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function cleanupDeletedProduct() {
  try {
    const product = await prisma.product.findFirst({
      where: { shopifyId: '8977334337704' },
      include: {
        bookings: {
          where: {
            status: { in: ['PROVISIONAL', 'CONFIRMED'] }
          }
        }
      }
    });
    
    if (!product) {
      console.log('商品が見つかりません');
      return;
    }
    
    console.log('商品情報:');
    console.log('- タイトル:', product.title);
    console.log('- 有効な予約数:', product.bookings.length);
    
    if (product.bookings.length > 0) {
      console.log('有効な予約があるため、商品を無効化します');
      
      const updated = await prisma.product.update({
        where: { id: product.id },
        data: {
          status: 'UNAVAILABLE',
          syncStatus: 'DELETED_FROM_SHOPIFY',
          lastSyncedAt: new Date()
        }
      });
      
      console.log('商品を無効化しました');
    } else {
      console.log('有効な予約がないため、商品を削除します');
      
      await prisma.product.delete({
        where: { id: product.id }
      });
      
      console.log('商品を削除しました');
    }
  } catch (error) {
    console.error('エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupDeletedProduct();
