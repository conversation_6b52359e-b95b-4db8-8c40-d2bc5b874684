/**
 * 在庫状況詳細調査スクリプト
 *
 * 商品の在庫状況を詳しく調査し、2日レンタル以降の在庫0の原因を特定
 */

import { PrismaClient } from '@prisma/client';
import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();
const SHOPIFY_SHOP = process.env.SHOPIFY_SHOP;
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

/**
 * Shopify GraphQL APIを呼び出し
 */
async function callShopifyGraphQL(query, variables = {}) {
  const response = await fetch(`https://${SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN
    },
    body: JSON.stringify({ query, variables })
  });

  const data = await response.json();
  if (data.errors) {
    throw new Error(`Shopify API エラー: ${JSON.stringify(data.errors)}`);
  }
  return data.data;
}

/**
 * 商品の詳細在庫情報を取得
 */
async function getProductInventoryDetails(productId) {
  const query = `
    query getProduct($id: ID!) {
      product(id: $id) {
        id
        title
        handle
        status
        variants(first: 20) {
          edges {
            node {
              id
              title
              sku
              price
              inventoryQuantity
              inventoryPolicy
              inventoryItem {
                id
                tracked
                requiresShipping
                inventoryLevels(first: 10) {
                  edges {
                    node {
                      id
                      quantities(names: ["available"]) {
                        name
                        quantity
                      }
                      location {
                        id
                        name
                      }
                    }
                  }
                }
              }
            }
          }
        }
        metafields(first: 20) {
          edges {
            node {
              namespace
              key
              value
              type
            }
          }
        }
      }
    }
  `;

  return await callShopifyGraphQL(query, { id: productId });
}

/**
 * Prismaデータベースの商品情報を取得
 */
async function getPrismaProductInfo(shopifyProductId) {
  return await prisma.product.findFirst({
    where: {
      shopifyId: shopifyProductId
    },
    include: {
      bookings: {
        where: {
          status: {
            in: ['CONFIRMED', 'PROVISIONAL']
          }
        },
        orderBy: {
          startDate: 'asc'
        }
      }
    }
  });
}

/**
 * 在庫状況を分析
 */
async function analyzeInventoryStatus(productId) {
  console.log('🔍 === 在庫状況詳細分析 ===');
  console.log(`対象商品ID: ${productId}`);
  console.log(`実行時刻: ${new Date().toLocaleString('ja-JP')}`);
  console.log('');

  try {
    // 1. Shopifyから商品情報を取得
    console.log('1. Shopify商品情報取得中...');
    const shopifyData = await getProductInventoryDetails(productId);
    const product = shopifyData.product;

    console.log(`✅ 商品名: ${product.title}`);
    console.log(`📦 ステータス: ${product.status}`);
    console.log(`🔗 ハンドル: ${product.handle}`);
    console.log('');

    // 2. バリアント在庫詳細
    console.log('=== 📊 バリアント在庫詳細 ===');
    product.variants.edges.forEach((edge, index) => {
      const variant = edge.node;
      console.log(`${index + 1}. ${variant.title}`);
      console.log(`   SKU: ${variant.sku}`);
      console.log(`   価格: ¥${variant.price}`);
      console.log(`   在庫数: ${variant.inventoryQuantity}`);
      console.log(`   在庫ポリシー: ${variant.inventoryPolicy}`);
      console.log(`   在庫追跡: ${variant.inventoryItem.tracked ? 'Yes' : 'No'}`);

      if (variant.inventoryItem.inventoryLevels.edges.length > 0) {
        console.log('   📍 ロケーション別在庫:');
        variant.inventoryItem.inventoryLevels.edges.forEach(levelEdge => {
          const level = levelEdge.node;
          const availableQty = level.quantities.find(q => q.name === 'available')?.quantity || 0;
          console.log(`     - ${level.location.name}: ${availableQty}個`);
        });
      } else {
        console.log('   📍 ロケーション別在庫: なし');
      }
      console.log('');
    });

    // 3. メタフィールド情報
    console.log('=== 🏷️ メタフィールド情報 ===');
    if (product.metafields.edges.length > 0) {
      product.metafields.edges.forEach(edge => {
        const meta = edge.node;
        console.log(`${meta.namespace}.${meta.key} (${meta.type}):`);

        // JSONの場合は整形して表示
        if (meta.type === 'json') {
          try {
            const jsonValue = JSON.parse(meta.value);
            console.log(JSON.stringify(jsonValue, null, 2));
          } catch {
            console.log(meta.value);
          }
        } else {
          console.log(meta.value);
        }
        console.log('');
      });
    } else {
      console.log('メタフィールドなし');
    }

    // 4. Prismaデータベース情報
    console.log('=== 💾 Prismaデータベース情報 ===');
    const prismaProduct = await getPrismaProductInfo(productId);

    if (prismaProduct) {
      console.log(`✅ Prisma商品: ${prismaProduct.title}`);
      console.log(`📦 SKU: ${prismaProduct.sku}`);
      console.log(`💰 価格: ¥${prismaProduct.price}`);
      console.log(`📍 ロケーション: ${prismaProduct.locationId || '未設定'}`);
      console.log(`📊 ステータス: ${prismaProduct.status}`);
      console.log(`📅 予約数: ${prismaProduct.bookings.length}件`);

      if (prismaProduct.bookings.length > 0) {
        console.log('');
        console.log('📅 予約一覧:');
        prismaProduct.bookings.forEach((booking, index) => {
          console.log(`  ${index + 1}. ${booking.bookingId}`);
          console.log(`     期間: ${booking.startDate.toLocaleDateString()} - ${booking.endDate.toLocaleDateString()}`);
          console.log(`     ステータス: ${booking.status}`);
          console.log(`     タイプ: ${booking.bookingType}`);
        });
      }
    } else {
      console.log('❌ Prismaデータベースに商品が見つかりません');
    }

    // 5. 在庫問題の分析
    console.log('');
    console.log('=== 🔍 在庫問題分析 ===');

    const variants = product.variants.edges.map(edge => edge.node);
    const zeroInventoryVariants = variants.filter(v => v.inventoryQuantity === 0);
    const positiveInventoryVariants = variants.filter(v => v.inventoryQuantity > 0);

    console.log(`📊 総バリアント数: ${variants.length}`);
    console.log(`❌ 在庫0のバリアント: ${zeroInventoryVariants.length}件`);
    console.log(`✅ 在庫ありのバリアント: ${positiveInventoryVariants.length}件`);

    if (zeroInventoryVariants.length > 0) {
      console.log('');
      console.log('❌ 在庫0のバリアント詳細:');
      zeroInventoryVariants.forEach(variant => {
        console.log(`  - ${variant.title} (SKU: ${variant.sku})`);
        console.log(`    在庫ポリシー: ${variant.inventoryPolicy}`);
        console.log(`    在庫追跡: ${variant.inventoryItem.tracked ? 'Yes' : 'No'}`);
      });
    }

    if (positiveInventoryVariants.length > 0) {
      console.log('');
      console.log('✅ 在庫ありのバリアント詳細:');
      positiveInventoryVariants.forEach(variant => {
        console.log(`  - ${variant.title} (SKU: ${variant.sku}): ${variant.inventoryQuantity}個`);
      });
    }

    // 6. 推定原因と対策
    console.log('');
    console.log('=== 💡 推定原因と対策 ===');

    if (zeroInventoryVariants.length > 0) {
      console.log('🔍 可能な原因:');
      console.log('1. レンタル商品の在庫管理設定が不適切');
      console.log('2. バリアント作成時の初期在庫設定が0');
      console.log('3. 在庫追跡が有効になっているが在庫補充されていない');
      console.log('4. 予約システムとShopify在庫の同期問題');

      console.log('');
      console.log('🛠️ 推奨対策:');
      console.log('1. レンタル商品は在庫追跡を無効にする');
      console.log('2. 在庫ポリシーを"continue"に設定');
      console.log('3. バリアント在庫を1に設定');
      console.log('4. 予約システムでの可用性管理を優先');
    } else {
      console.log('✅ 在庫設定に問題は見つかりませんでした');
    }

  } catch (error) {
    console.error('❌ 分析エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * 在庫修正スクリプト
 */
async function fixInventoryIssues(productId) {
  console.log('🛠️ === 在庫問題修正開始 ===');

  try {
    // 商品情報を取得
    const shopifyData = await getProductInventoryDetails(productId);
    const product = shopifyData.product;

    console.log(`対象商品: ${product.title}`);

    // 在庫0のバリアントを修正
    const variants = product.variants.edges.map(edge => edge.node);
    const zeroInventoryVariants = variants.filter(v => v.inventoryQuantity === 0);

    if (zeroInventoryVariants.length > 0) {
      console.log(`修正対象バリアント: ${zeroInventoryVariants.length}件`);

      for (const variant of zeroInventoryVariants) {
        console.log(`修正中: ${variant.title}`);

        // バリアント更新のGraphQL mutation
        const updateMutation = `
          mutation productVariantsBulkUpdate($productId: ID!, $variants: [ProductVariantsBulkInput!]!) {
            productVariantsBulkUpdate(productId: $productId, variants: $variants) {
              productVariants {
                id
                inventoryQuantity
                inventoryPolicy
              }
              userErrors {
                field
                message
              }
            }
          }
        `;

        const updateInput = {
          productId: product.id,
          variants: [{
            id: variant.id,
            inventoryPolicy: 'CONTINUE' // 在庫切れでも販売継続
          }]
        };

        try {
          const result = await callShopifyGraphQL(updateMutation, updateInput);

          if (result.productVariantsBulkUpdate.userErrors.length > 0) {
            console.log(`❌ 更新エラー: ${JSON.stringify(result.productVariantsBulkUpdate.userErrors)}`);
          } else {
            console.log(`✅ 更新成功: ${variant.title}`);
          }
        } catch (error) {
          console.log(`❌ API呼び出しエラー: ${error.message}`);
        }
      }
    } else {
      console.log('✅ 修正が必要なバリアントはありません');
    }

  } catch (error) {
    console.error('❌ 修正エラー:', error);
  }
}

// コマンドライン引数の処理
const args = process.argv.slice(2);
const command = args[0];
const productId = args[1] || 'gid://shopify/Product/8981694021800';

async function main() {
  try {
    switch (command) {
      case 'analyze':
        await analyzeInventoryStatus(productId);
        break;
      case 'fix':
        await fixInventoryIssues(productId);
        break;
      default:
        console.log('🔍 在庫状況調査ツール');
        console.log('');
        console.log('使用方法:');
        console.log('  node inventory-analysis.js analyze [商品ID] # 在庫状況分析');
        console.log('  node inventory-analysis.js fix [商品ID]     # 在庫問題修正');
        console.log('');
        console.log('デフォルトで分析を実行します...');
        await analyzeInventoryStatus(productId);
        break;
    }
  } catch (error) {
    console.error('実行エラー:', error);
    process.exit(1);
  }
}

// スクリプト実行
main().catch(console.error);
