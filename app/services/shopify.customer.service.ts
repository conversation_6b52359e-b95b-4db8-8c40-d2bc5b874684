import { Session } from "@shopify/shopify-api";
import { shopifyApi } from "@shopify/shopify-api";
import { prisma } from "~/db.server";

/**
 * Shopify顧客データ取得サービス
 */
export class ShopifyCustomerService {
  private session: Session;
  private client: any;

  constructor(session: Session) {
    this.session = session;
    // 新しいGraphQLクライアントの初期化方法
    this.client = {
      query: async ({ data }: { data: any }) => {
        const client = new shopifyApi.clients.Graphql({ session });
        return await client.query({ data });
      }
    };
  }

  /**
   * Shopifyから顧客データを取得
   * @param limit 取得する顧客数
   * @returns 顧客データ
   */
  async fetchCustomers(limit = 50) {
    try {
      const query = `
        {
          customers(first: ${limit}, sortKey: CREATED_AT, reverse: true) {
            edges {
              node {
                id
                firstName
                lastName
                email
                phone
                createdAt
                updatedAt
                defaultAddress {
                  address1
                  address2
                  city
                  province
                  zip
                  country
                  phone
                }
                addresses {
                  address1
                  address2
                  city
                  province
                  zip
                  country
                  phone
                }
                orders(first: 5) {
                  edges {
                    node {
                      id
                      name
                      createdAt
                      displayFinancialStatus
                    }
                  }
                }
                metafields(first: 10, namespace: "rental") {
                  edges {
                    node {
                      id
                      namespace
                      key
                      value
                    }
                  }
                }
              }
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      `;

      const response = await this.client.query({
        data: query,
      });

      if (response.body.data) {
        return response.body.data.customers.edges.map((edge: any) => edge.node);
      }

      return [];
    } catch (error) {
      console.error("Shopify顧客データ取得エラー:", error);
      throw error;
    }
  }

  /**
   * 顧客メタフィールドを更新
   * @param customerId 顧客ID
   * @param metafields メタフィールド
   */
  async updateCustomerMetafields(customerId: string, metafields: any[]) {
    try {
      const metafieldInputs = metafields.map((metafield) => {
        return {
          namespace: metafield.namespace || "rental",
          key: metafield.key,
          value: typeof metafield.value === "string"
            ? metafield.value
            : JSON.stringify(metafield.value),
          type: metafield.type || "string",
        };
      });

      const mutation = `
        mutation customerUpdate($input: CustomerInput!) {
          customerUpdate(input: $input) {
            customer {
              id
              metafields(first: 10, namespace: "rental") {
                edges {
                  node {
                    id
                    namespace
                    key
                    value
                  }
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const response = await this.client.query({
        data: {
          query: mutation,
          variables: {
            input: {
              id: customerId,
              metafields: metafieldInputs,
            },
          },
        },
      });

      if (response.body.data.customerUpdate.userErrors.length > 0) {
        throw new Error(response.body.data.customerUpdate.userErrors[0].message);
      }

      return response.body.data.customerUpdate.customer;
    } catch (error) {
      console.error("顧客メタフィールド更新エラー:", error);
      throw error;
    }
  }

  /**
   * Shopify顧客データをPrismaデータベースと同期
   * @param shop ショップドメイン
   */
  async syncCustomersWithDatabase(shop: string) {
    try {
      // Shopifyから顧客データを取得
      const shopifyCustomers = await this.fetchCustomers();

      // 同期結果を記録
      const syncResults = {
        created: 0,
        updated: 0,
        skipped: 0,
        errors: 0,
      };

      // 各顧客を処理
      for (const customer of shopifyCustomers) {
        try {
          // Shopify IDからGID部分を抽出
          const shopifyId = customer.id.replace("gid://shopify/Customer/", "");

          // 顧客名を生成
          const name = `${customer.firstName || ''} ${customer.lastName || ''}`.trim();

          // メールアドレスを取得
          const email = customer.email || '';

          // 電話番号を取得
          const phone = customer.phone || customer.defaultAddress?.phone || '';

          // 住所を取得
          let address = '';
          if (customer.defaultAddress) {
            const addr = customer.defaultAddress;
            address = [
              addr.address1,
              addr.address2,
              addr.city,
              addr.province,
              addr.zip,
              addr.country
            ].filter(Boolean).join(' ');
          }

          // メタフィールドを解析
          const metafields = customer.metafields?.edges.map((edge: any) => ({
            key: edge.node.key,
            value: edge.node.value,
            namespace: edge.node.namespace,
          })) || [];

          // 備考を取得
          const notesMetafield = metafields.find((m: any) => m.key === "notes");
          const notes = notesMetafield ? notesMetafield.value : '';

          // データベースに既存の顧客があるか確認
          const existingCustomer = await prisma.customer.findFirst({
            where: {
              shop,
              OR: [
                { shopifyId },
                { email: { equals: email, mode: 'insensitive' } }
              ]
            },
          });

          if (existingCustomer) {
            // 既存の顧客を更新
            await prisma.customer.update({
              where: {
                id: existingCustomer.id,
              },
              data: {
                shopifyId,
                name,
                email,
                phone,
                address,
                notes,
                updatedAt: new Date(),
              },
            });
            syncResults.updated++;
          } else {
            // 新規顧客を作成
            await prisma.customer.create({
              data: {
                shop,
                shopifyId,
                name,
                email,
                phone,
                address,
                notes,
              },
            });
            syncResults.created++;
          }
        } catch (error) {
          console.error(`顧客同期エラー (${customer.email}):`, error);
          syncResults.errors++;
        }
      }

      return syncResults;
    } catch (error) {
      console.error("顧客同期エラー:", error);
      throw error;
    }
  }
}
