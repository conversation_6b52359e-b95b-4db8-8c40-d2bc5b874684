/**
 * 在庫状況確認スクリプト
 */

import { authenticate } from '../app/shopify.server';

async function checkInventoryStatus() {
  console.log('=== 在庫状況確認開始 ===');

  try {
    const { admin } = await authenticate.admin(new Request('http://localhost'));

    // 最近作成された商品を取得
    const response = await admin.graphql(`
      query getRecentProducts {
        products(first: 5, reverse: true) {
          edges {
            node {
              id
              title
              handle
              variants(first: 20) {
                edges {
                  node {
                    id
                    title
                    sku
                    inventoryItem {
                      id
                      tracked
                      inventoryLevels(first: 10) {
                        edges {
                          node {
                            id
                            available
                            location {
                              id
                              name
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
              metafields(first: 10, namespace: "rental") {
                edges {
                  node {
                    key
                    value
                  }
                }
              }
            }
          }
        }
      }
    `);

    const data = await response.json();
    
    if (data.errors) {
      throw new Error(`GraphQL エラー: ${JSON.stringify(data.errors)}`);
    }

    const products = data.data.products.edges;

    console.log(`\n📦 商品数: ${products.length}`);

    products.forEach((productEdge: any, index: number) => {
      const product = productEdge.node;
      console.log(`\n--- 商品 ${index + 1}: ${product.title} ---`);
      console.log(`ID: ${product.id}`);
      console.log(`Handle: ${product.handle}`);

      // メタフィールド確認
      const metafields = product.metafields.edges;
      console.log(`\n📋 メタフィールド (${metafields.length}個):`);
      metafields.forEach((metafieldEdge: any) => {
        const metafield = metafieldEdge.node;
        console.log(`  ${metafield.key}: ${metafield.value}`);
      });

      // バリエーション確認
      const variants = product.variants.edges;
      console.log(`\n🔧 バリエーション (${variants.length}個):`);
      
      variants.forEach((variantEdge: any, variantIndex: number) => {
        const variant = variantEdge.node;
        console.log(`  ${variantIndex + 1}. ${variant.title} (SKU: ${variant.sku})`);
        console.log(`     ID: ${variant.id}`);
        console.log(`     Tracked: ${variant.inventoryItem.tracked}`);
        
        // 在庫レベル確認
        const inventoryLevels = variant.inventoryItem.inventoryLevels.edges;
        console.log(`     📦 在庫レベル (${inventoryLevels.length}個):`);
        
        if (inventoryLevels.length === 0) {
          console.log(`       ⚠️ 在庫レベルが設定されていません`);
        } else {
          inventoryLevels.forEach((levelEdge: any) => {
            const level = levelEdge.node;
            console.log(`       ${level.location.name}: ${level.available}個 (ID: ${level.location.id})`);
          });
        }
      });
    });

    // ロケーション一覧も取得
    console.log('\n=== ロケーション一覧 ===');
    const locationResponse = await admin.graphql(`
      query getLocations {
        locations(first: 10) {
          edges {
            node {
              id
              name
              address {
                formatted
              }
            }
          }
        }
      }
    `);

    const locationData = await locationResponse.json();
    const locations = locationData.data.locations.edges;

    locations.forEach((locationEdge: any) => {
      const location = locationEdge.node;
      console.log(`📍 ${location.name}: ${location.id}`);
      if (location.address?.formatted) {
        console.log(`   住所: ${location.address.formatted}`);
      }
    });

  } catch (error) {
    console.error('❌ エラー:', error);
  }

  console.log('\n=== 在庫状況確認完了 ===');
}

// スクリプト実行
if (import.meta.url === new URL(import.meta.url).href) {
  checkInventoryStatus().catch(console.error);
}

export { checkInventoryStatus };
