import { json, redirect } from "@remix-run/node";
import type { ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";

export async function action({ request }: ActionFunctionArgs) {
  const { session } = await authenticate.admin(request);
  
  try {
    // すべての予約を削除
    const { count } = await prisma.booking.deleteMany({
      where: { shop: session.shop },
    });
    
    console.log(`${count}件の予約が削除されました`);
    
    return redirect("/app/booking-test");
  } catch (error) {
    console.error("予約削除エラー:", error);
    return json({ error: "予約の削除に失敗しました" });
  }
}

export default function DeleteAllBookings() {
  return (
    <div>
      <p>予約を削除中...</p>
    </div>
  );
}
