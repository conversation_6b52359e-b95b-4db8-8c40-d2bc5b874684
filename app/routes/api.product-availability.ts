import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { prisma } from "~/db.server";
import { authenticate } from "~/shopify.server";

/**
 * 商品の予約状況とメンテナンス期間を取得するAPIエンドポイント
 * GET /api/product-availability?productId={productId}
 */
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Shopify認証（必要に応じて）
    const { session } = await authenticate.public.appProxy(request);
    
    const url = new URL(request.url);
    const productId = url.searchParams.get('productId');
    
    if (!productId) {
      return json(
        { 
          success: false, 
          error: 'productId parameter is required',
          bookings: [],
          maintenance: []
        },
        { status: 400 }
      );
    }

    // 現在の日付から3ヶ月先までの予約データを取得
    const now = new Date();
    const threeMonthsLater = new Date();
    threeMonthsLater.setMonth(now.getMonth() + 3);

    // 予約データを取得
    const bookings = await prisma.booking.findMany({
      where: {
        productId: productId,
        // 終了日が現在日時以降の予約のみ取得
        endDate: {
          gte: now
        },
        // 開始日が3ヶ月以内の予約のみ取得
        startDate: {
          lte: threeMonthsLater
        }
      },
      select: {
        id: true,
        bookingId: true,
        startDate: true,
        endDate: true,
        status: true,
        bookingType: true,
        customerName: true,
        totalAmount: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        startDate: 'asc'
      }
    });

    // 商品のメンテナンス期間を取得（basic_infoのJSONから）
    const product = await prisma.product.findUnique({
      where: {
        shopifyProductId: productId
      },
      select: {
        id: true,
        shopifyProductId: true,
        basic_info: true,
        status: true
      }
    });

    let maintenancePeriods: any[] = [];
    
    if (product?.basic_info) {
      try {
        const basicInfo = typeof product.basic_info === 'string' 
          ? JSON.parse(product.basic_info) 
          : product.basic_info;
        
        // メンテナンス期間の情報があれば取得
        if (basicInfo.maintenance_periods && Array.isArray(basicInfo.maintenance_periods)) {
          maintenancePeriods = basicInfo.maintenance_periods.map((period: any) => ({
            id: period.id || `maintenance_${Date.now()}`,
            startDate: period.startDate,
            endDate: period.endDate,
            reason: period.reason || 'メンテナンス',
            type: 'maintenance'
          }));
        }

        // 商品ステータスがメンテナンス中の場合、現在から1ヶ月間をメンテナンス期間として追加
        if (basicInfo.status === 'maintenance') {
          const maintenanceEnd = new Date();
          maintenanceEnd.setMonth(maintenanceEnd.getMonth() + 1);
          
          maintenancePeriods.push({
            id: `status_maintenance_${product.id}`,
            startDate: now.toISOString(),
            endDate: maintenanceEnd.toISOString(),
            reason: 'メンテナンス中',
            type: 'status_maintenance'
          });
        }
      } catch (error) {
        console.error('Error parsing basic_info JSON:', error);
      }
    }

    // レスポンス用にデータを整形
    const formattedBookings = bookings.map(booking => ({
      id: booking.id,
      bookingId: booking.bookingId,
      startDate: booking.startDate.toISOString(),
      endDate: booking.endDate.toISOString(),
      status: booking.status,
      bookingType: booking.bookingType,
      type: booking.bookingType === 'CONFIRMED' ? 'confirmed' : 'provisional',
      customerName: booking.customerName,
      totalAmount: booking.totalAmount,
      createdAt: booking.createdAt.toISOString(),
      updatedAt: booking.updatedAt.toISOString()
    }));

    return json({
      success: true,
      productId: productId,
      bookings: formattedBookings,
      maintenance: maintenancePeriods,
      product: product ? {
        id: product.id,
        shopifyProductId: product.shopifyProductId,
        status: product.status
      } : null,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching product availability:', error);
    
    return json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        bookings: [],
        maintenance: []
      },
      { status: 500 }
    );
  }
}

/**
 * CORS対応のためのOPTIONSメソッド
 */
export async function options() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
