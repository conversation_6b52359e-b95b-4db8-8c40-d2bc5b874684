{"name": "ease-next-temp", "private": true, "scripts": {"test:variant-booking": "node --loader ts-node/esm scripts/test-variant-booking-management.js", "setup:variant-test-env": "node --loader ts-node/esm scripts/setup-variant-test-environment.js", "setup:variant-test-products": "node --loader ts-node/esm scripts/test-products-setup.js", "setup:product-variants": "node --loader ts-node/esm scripts/setup-product-variants.js", "create:test-bookings": "node --loader ts-node/esm scripts/create-test-bookings.js", "update:variant-metafields": "node --loader ts-node/esm scripts/update-variant-metafields.js", "create:variant-mapping": "node --loader ts-node/esm scripts/create-variant-mapping-metafield.js", "restore:original-metafields": "node --loader ts-node/esm scripts/restore-original-metafields.js", "generate:test-products": "tsx scripts/generate-test-products.ts", "clear:test-data": "tsx scripts/clear-test-data.ts", "create:valid-test-data": "node scripts/generate-valid-test-data.js", "reset:test-data": "npm run create:valid-test-data", "build": "remix vite:build", "dev": "SHOPIFY_APP_URL=https://app.shopify-app-test.xyz vite dev --port 8002", "dev:tunnel": "shopify app dev --tunnel", "dev:fixed-tunnel": "SHOPIFY_APP_URL=https://app.shopify-app-test.xyz vite dev --port 8002", "dev:alt-port": "vite dev --port 9294", "dev:remix": "remix vite:dev --port 9294", "dev:fast": "vite dev --port 8002", "dev:fast-tunnel": "SHOPIFY_APP_URL=https://app.shopify-app-test.xyz vite dev --port 8002", "dev:clean": "rm -rf node_modules/.vite .cache && shopify app dev --theme-app-extension-port=9294", "dev:no-migrations": "PRISMA_SCHEMA_DISABLE_MIGRATIONS=true shopify app dev", "dev:fixed-tunnel:no-migrations": "PRISMA_SCHEMA_DISABLE_MIGRATIONS=true SHOPIFY_APP_URL=https://app.shopify-app-test.xyz vite dev --port 8002", "dev:vite-only": "npx prisma generate && vite dev --port 8002", "config:link": "shopify app config link", "generate": "shopify app generate", "deploy": "shopify app deploy", "deploy:auto-update": "node scripts/deploy-auto-update.js", "update:cloudflare-url": "bash scripts/update-cloudflare-url.sh", "config:use": "shopify app config use", "env": "shopify app env", "start": "remix-serve ./build/server/index.js", "docker-start": "npm run setup && npm run start", "setup": "prisma generate && prisma migrate deploy", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "shopify": "shopify", "prisma": "prisma", "graphql-codegen": "graphql-codegen", "vite": "vite", "download-schema": "node scripts/download-schema.cjs", "codegen": "graphql-codegen --config codegen.ts", "codegen:watch": "graphql-codegen --config codegen.ts --watch", "graphql:generate": "npm run download-schema && npm run codegen", "generate:booking-test-data": "tsx scripts/migration/generate-booking-test-data.ts", "build:scripts": "node app/scripts/build-scripts.js", "clear:calendar-data": "tsx app/scripts/clear-calendar-data.ts", "create:calendar-test-data": "tsx app/scripts/create-calendar-test-data.ts", "reset:calendar-data": "npm run clear:calendar-data && npm run create:calendar-test-data", "test": "vitest run", "test:watch": "vitest", "test:staff": "vitest run app/tests/staff/*.test.ts", "test:master": "vitest run app/tests/master/*.test.ts", "test:shipping": "vitest run app/tests/shipping/*.test.ts", "test:booking": "vitest run app/tests/booking/*.test.ts", "test:dashboard": "vitest run app/tests/dashboard/*.test.ts", "test:integration": "vitest run app/tests/integration/*.test.ts", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:calendar": "playwright test app/tests/e2e/calendar.spec.ts --headed", "test:e2e:admin": "playwright test app/tests/e2e/admin-dashboard.spec.ts --headed", "test:e2e:reservation": "playwright test app/tests/e2e/reservation-flow.spec.ts --headed", "test:e2e:bulk-selection": "playwright test app/tests/e2e/bulk-product-selection.spec.ts --headed", "test:e2e:run": "./app/tests/e2e/run-tests.sh", "test:e2e:run-reservation": "./app/tests/e2e/run-reservation-tests.sh", "test:e2e:run-bulk-selection": "./app/tests/e2e/run-bulk-selection-tests.sh", "test:e2e:functional": "./app/tests/e2e/run-functional-tests.sh", "test:e2e:screen": "./app/tests/e2e/run-screen-verification.sh", "test:e2e:crud": "./app/tests/e2e/run-crud-test.sh", "generate:test-data": "node app/tests/scripts/generate-test-data.js", "test:calendar": "vitest run app/tests/calendar/unified-calendar.test.ts", "test:calendar:watch": "vitest app/tests/calendar/unified-calendar.test.ts", "test:checkout": "vitest run app/tests/calendar/test-checkout-integration.test.ts", "test:checkout:watch": "vitest app/tests/calendar/test-checkout-integration.test.ts", "test:error-handling": "vitest run app/tests/calendar/product-fetch-error.test.ts", "test:error-handling:watch": "vitest app/tests/calendar/product-fetch-error.test.ts", "test:error-e2e": "tsx app/tests/calendar/product-fetch-error-e2e.ts", "test:run-all": "tsx app/tests/run-all-tests.ts", "test:generate-report": "tsx app/tests/calendar/generate-report.ts", "test:auto": "tsx app/tests/calendar/auto-test.ts", "test:comprehensive-booking": "node scripts/comprehensive-booking-test.mjs", "test:booking-report": "node scripts/generate-booking-test-report.mjs", "test:create-booking-data": "node scripts/create-booking-test-data.mjs", "import:shipping-destinations": "tsx scripts/import-shipping-destinations.ts", "test:booking-crud": "tsx scripts/test-booking-crud.ts", "debug:booking-creation": "tsx scripts/debug-booking-creation.ts", "test:sync-inventory": "tsx scripts/test-sync-inventory.ts", "test:booking-inventory": "tsx scripts/test-booking-inventory.ts", "test:cart-integration": "tsx scripts/test-cart-integration.ts", "test:date-conflict": "tsx scripts/test-booking-date-conflict.ts", "test:holiday-pricing": "tsx scripts/test-holiday-pricing.ts", "test:metafield-sync": "tsx scripts/test-metafield-sync.ts", "test:multi-product-booking": "tsx scripts/test-multi-product-booking.ts", "test:shopify-prisma-integration": "tsx scripts/test-shopify-prisma-integration.ts", "test:all": "npm run test:shopify-prisma-integration && npm run test:shopify-prisma-integration -- --error-cases && npm run test:shopify-prisma-integration -- --variants", "test:local": "npm run test:shopify-prisma-integration -- --local && npm run test:shopify-prisma-integration -- --error-cases --local && npm run test:shopify-prisma-integration -- --variants --local", "test:ci": "npm run test:local && npm run test:shopify-prisma-integration -- --cleanup --local", "test:cleanup": "tsx scripts/cleanup-test-data.ts", "test:report": "tsx scripts/generate-test-report.ts", "test:provisional-variant": "tsx scripts/test-provisional-variant.ts", "test:validate-results": "tsx scripts/validate-test-results.ts", "test:auto-validate": "npm run test:shopify-prisma-integration && npm run test:validate-results docs/test-results/shopify-prisma-integration-*.json scripts/expected-results/shopify-prisma-integration.json", "test:multi-product-validate": "npm run test:multi-product-booking && npm run test:validate-results docs/test-results/multi-product-booking-*.json scripts/expected-results/multi-product-booking.json", "test:error-recovery": "tsx scripts/test-error-recovery.ts", "test:load-concurrency": "tsx scripts/test-load-concurrency.ts", "test:edge-cases": "tsx scripts/test-edge-cases.ts", "cleanup:test-bookings": "tsx scripts/cleanup-test-bookings.ts", "reset:inventory-calendar": "tsx scripts/reset-inventory-calendar.ts", "prepare:test-environment": "tsx scripts/prepare-test-environment.ts", "create:test-products": "tsx scripts/create-test-products-for-booking.ts", "create:expired-provisional": "tsx scripts/create-expired-provisional-booking.ts", "check:provisional-expiration": "tsx scripts/check-provisional-expiration.ts", "notify:provisional-expiration": "tsx scripts/notify-provisional-expiration.ts", "cleanup:inventory": "tsx scripts/cleanup-inventory-calendar.ts", "setup:variants": "node scripts/setup-product-variants.js", "setup:variants-with-provisional": "node scripts/setup-product-variants-with-provisional.js", "migrate:customer-ids": "tsx scripts/migrate-customer-ids.ts", "create:realistic-data": "tsx scripts/create-realistic-test-data.ts", "test:customer-api": "tsx scripts/test-customer-api.ts", "test:booking-search": "tsx scripts/test-booking-search.ts", "test:shopify-customer-search": "tsx scripts/test-shopify-customer-search.ts", "test:customer-reference": "tsx scripts/test-customer-reference.ts", "test:product-display": "tsx app/tests/product-display/product-display-test.ts", "test:product-display:report": "tsx app/tests/product-display/generate-report.ts", "fix:product-display": "tsx scripts/fix-product-display-issues.ts", "validate-pricing": "tsx scripts/validate-and-fix-pricing.ts", "validate-pricing:fix": "tsx scripts/validate-and-fix-pricing.ts all --fix", "create-variants": "tsx scripts/create-missing-variants.ts", "create-variants:all": "tsx scripts/create-missing-variants.ts all", "test-product-registration": "tsx scripts/test-product-registration-with-variants.ts", "check-inventory": "tsx scripts/check-inventory-status.ts", "test-single-inventory": "tsx scripts/test-single-product-inventory.ts", "test-inventory-fix": "tsx scripts/test-inventory-fix.ts", "test-variant-creation": "tsx scripts/test-single-variant-creation.ts", "test-inventory-permissions": "tsx scripts/test-inventory-with-permissions.ts", "check-actual-inventory": "tsx scripts/check-actual-inventory.ts", "test-single-inventory-fix": "tsx scripts/test-single-inventory-fix.ts", "test-ny-location": "tsx scripts/test-ny-location-only.ts", "test-sku-format": "tsx scripts/test-sku-format-fix.ts", "test-complete-products": "tsx scripts/test-complete-product-creation.ts", "test-metafield-prisma": "tsx scripts/test-metafield-prisma-integration.ts", "debug-inventory": "tsx scripts/debug-inventory-issue.ts"}, "type": "module", "engines": {"node": "^18.20 || ^20.10 || >=21.0.0"}, "prisma": {"schema": "prisma/schema.prisma", "seed": "node prisma/seed.js"}, "dependencies": {"@holiday-jp/holiday_jp": "^2.4.0", "@mastra/core": "^0.8.3", "@neondatabase/mcp-server-neon": "^0.3.4", "@prisma/client": "^6.5.0", "@remix-run/dev": "^2.16.1", "@remix-run/fs-routes": "^2.16.1", "@remix-run/node": "^2.16.1", "@remix-run/react": "^2.16.1", "@remix-run/serve": "^2.16.1", "@shopify/admin-api-client": "^1.0.7", "@shopify/app-bridge-react": "^4.1.6", "@shopify/dev-mcp": "^1.0.2", "@shopify/polaris": "^12.0.0", "@shopify/polaris-icons": "^9.3.1", "@shopify/shopify-app-remix": "^3.7.0", "@shopify/shopify-app-session-storage-prisma": "^6.0.0", "@types/node-cron": "^3.0.11", "@types/pdfkit": "^0.13.9", "@types/react-datepicker": "^6.2.0", "@uvarov.frontend/vanilla-calendar": "^2.8.6", "bcryptjs": "^3.0.2", "csv-parse": "^5.6.0", "csv-parser": "^3.2.0", "csv-stringify": "^6.5.2", "date-fns": "^4.1.0", "graphql-request": "^7.1.2", "isbot": "^5.1.0", "lru-cache": "^11.1.0", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "pdfkit": "^0.17.0", "prisma": "^6.5.0", "react": "^18.2.0", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "task-master-ai": "^0.12.1", "uuid": "^11.1.0", "vanilla-calendar-pro": "^3.0.4", "vite-tsconfig-paths": "^5.0.1"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.5", "@graphql-codegen/schema-ast": "^4.1.0", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.0", "@graphql-codegen/typescript-react-apollo": "^4.3.2", "@playwright/test": "^1.52.0", "@remix-run/eslint-config": "^2.16.1", "@remix-run/route-config": "^2.16.1", "@shopify/api-codegen-preset": "^1.1.1", "@tailwindcss/forms": "^0.5.10", "@types/eslint": "^9.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.13.14", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "dotenv": "^16.5.0", "eslint": "^8.42.0", "eslint-config-prettier": "^10.0.1", "graphql": "^16.11.0", "jest": "^29.7.0", "postcss": "^8.5.3", "prettier": "^3.2.4", "tailwindcss": "^3.4.17", "ts-jest": "^29.3.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.3", "typescript": "^5.8.2", "vite": "6.2.3", "vitest": "^1.6.1"}, "workspaces": ["extensions/*"], "trustedDependencies": ["@shopify/plugin-cloudflare"], "resolutions": {"@graphql-tools/url-loader": "8.0.16"}, "overrides": {"@graphql-tools/url-loader": "8.0.16"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}