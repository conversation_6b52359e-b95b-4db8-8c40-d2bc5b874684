// Shopify Draft Order APIのテストスクリプト
// このスクリプトは、Shopify Admin APIを使用してドラフトオーダーを作成し、
// 正しい請求書URLを確認するためのものです。

import 'dotenv/config';
import fetch from 'node-fetch';

// Shopify API設定
const SHOP = process.env.SHOP_DOMAIN || process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';
const API_VERSION = process.env.SHOPIFY_API_VERSION || '2025-01';
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

// APIエンドポイント
const API_URL = `https://${SHOP}/admin/api/${API_VERSION}`;

// ヘッダー設定
const headers = {
  'Content-Type': 'application/json',
  'X-Shopify-Access-Token': ACCESS_TOKEN
};

// ドラフトオーダーを作成する関数
async function createDraftOrder() {
  try {
    console.log('ドラフトオーダーを作成中...');

    // ドラフトオーダーのデータ
    const draftOrderData = {
      draft_order: {
        line_items: [
          {
            title: 'テスト商品',
            price: '1000.00',
            quantity: 1,
            requires_shipping: true,
            taxable: true,
            properties: [
              { name: 'レンタル開始日', value: '2023-06-20' },
              { name: 'レンタル終了日', value: '2023-06-22' },
              { name: 'レンタル日数', value: '3' }
            ]
          }
        ],
        note: 'テスト用ドラフトオーダー'
      }
    };

    // APIリクエスト
    const response = await fetch(`${API_URL}/draft_orders.json`, {
      method: 'POST',
      headers,
      body: JSON.stringify(draftOrderData)
    });

    // レスポンスをJSON形式で取得
    const data = await response.json();

    // レスポンスを表示
    console.log('APIレスポンス:', JSON.stringify(data, null, 2));

    if (data.draft_order) {
      console.log('\n=== ドラフトオーダー情報 ===');
      console.log('ID:', data.draft_order.id);
      console.log('名前:', data.draft_order.name);
      console.log('ステータス:', data.draft_order.status);

      // 請求書URL
      if (data.draft_order.invoice_url) {
        console.log('請求書URL:', data.draft_order.invoice_url);
      } else {
        console.log('請求書URLが見つかりません');
      }

      // 管理画面URL
      const adminUrl = `https://${SHOP}/admin/draft_orders/${data.draft_order.id}`;
      console.log('管理画面URL:', adminUrl);

      // 請求書を送信
      await sendInvoice(data.draft_order.id);
    } else {
      console.error('ドラフトオーダーの作成に失敗しました:', data.errors || 'Unknown error');
    }
  } catch (error) {
    console.error('エラーが発生しました:', error);
  }
}

// 請求書を送信する関数
async function sendInvoice(draftOrderId) {
  try {
    console.log(`\n請求書を送信中... (ID: ${draftOrderId})`);

    // APIリクエスト
    const response = await fetch(`${API_URL}/draft_orders/${draftOrderId}/send_invoice.json`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        draft_order_invoice: {
          to: '<EMAIL>',
          subject: 'テスト請求書',
          custom_message: 'これはテスト用の請求書です。'
        }
      })
    });

    // レスポンスをJSON形式で取得
    const data = await response.json();

    // レスポンスを表示
    console.log('請求書送信レスポンス:', JSON.stringify(data, null, 2));

    if (data.draft_order_invoice) {
      console.log('\n=== 請求書情報 ===');
      console.log('送信先:', data.draft_order_invoice.to);
      console.log('件名:', data.draft_order_invoice.subject);

      // 請求書URL
      if (data.draft_order_invoice.invoice_url) {
        console.log('請求書URL:', data.draft_order_invoice.invoice_url);
      } else {
        console.log('請求書URLが見つかりません');
      }
    } else {
      console.error('請求書の送信に失敗しました:', data.errors || 'Unknown error');
    }
  } catch (error) {
    console.error('エラーが発生しました:', error);
  }
}

// スクリプトを実行
createDraftOrder();
