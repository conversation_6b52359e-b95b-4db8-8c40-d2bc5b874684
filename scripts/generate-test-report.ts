/**
 * テスト結果レポート生成スクリプト
 * 
 * このスクリプトは、テスト結果を詳細なレポートとして生成します。
 * - テスト結果の概要
 * - 各テストケースの詳細結果
 * - パフォーマンス指標
 * - エラーログ
 * - 改善提案
 * 
 * 実行方法: npx tsx scripts/generate-test-report.ts [レポートファイルパス]
 */

import fs from 'fs';
import path from 'path';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';

// レポートの種類
enum ReportType {
  BASIC = 'basic',
  ERROR_CASES = 'error-cases',
  VARIANTS = 'variants',
  COMPREHENSIVE = 'comprehensive'
}

// テスト結果の型定義
interface TestResult {
  testCase: string;
  success: boolean;
  details?: string;
  duration?: number;
  error?: any;
}

// テスト環境情報の型定義
interface TestEnvironment {
  shop: string;
  productId: string;
  customer: {
    email: string;
    name: string;
  };
  bookingDays: number;
  useRealApi: boolean;
  useLocalMode: boolean;
  nodeVersion: string;
  os: string;
  timestamp: string;
}

// テスト結果レポートの型定義
interface TestReport {
  type: ReportType;
  environment: TestEnvironment;
  results: {
    basic?: TestResult[];
    errorCases?: TestResult[];
    variants?: TestResult[];
    cleanup?: TestResult[];
  };
  performance: {
    totalDuration: number;
    apiCalls: number;
    dbQueries: number;
    cacheHits?: number;
    cacheMisses?: number;
  };
  errors: {
    count: number;
    details: any[];
  };
  improvements: string[];
}

/**
 * テスト結果レポートを生成する関数
 */
function generateReport(results: any, type: ReportType = ReportType.BASIC): string {
  // テスト環境情報を取得
  const environment: TestEnvironment = {
    shop: results.environment?.shop || 'unknown',
    productId: results.environment?.productId || 'unknown',
    customer: results.environment?.customer || { email: 'unknown', name: 'unknown' },
    bookingDays: results.environment?.bookingDays || 0,
    useRealApi: results.environment?.useRealApi || false,
    useLocalMode: results.environment?.useLocalMode || false,
    nodeVersion: process.version,
    os: `${process.platform} ${process.arch}`,
    timestamp: new Date().toISOString()
  };
  
  // テスト結果を集計
  const testResults = {
    basic: results.basic || [],
    errorCases: results.errorCases || [],
    variants: results.variants || [],
    cleanup: results.cleanup || []
  };
  
  // 成功率を計算
  const calculateSuccessRate = (results: TestResult[]) => {
    if (results.length === 0) return 0;
    const successCount = results.filter(r => r.success).length;
    return Math.round((successCount / results.length) * 100);
  };
  
  // 基本テストの成功率
  const basicSuccessRate = calculateSuccessRate(testResults.basic);
  
  // エラーケーステストの成功率
  const errorCasesSuccessRate = calculateSuccessRate(testResults.errorCases);
  
  // バリエーションテストの成功率
  const variantsSuccessRate = calculateSuccessRate(testResults.variants);
  
  // クリーンアップの成功率
  const cleanupSuccessRate = calculateSuccessRate(testResults.cleanup);
  
  // 全体の成功率
  const allResults = [
    ...testResults.basic,
    ...testResults.errorCases,
    ...testResults.variants,
    ...testResults.cleanup
  ];
  const overallSuccessRate = calculateSuccessRate(allResults);
  
  // パフォーマンス指標
  const performance = results.performance || {
    totalDuration: 0,
    apiCalls: 0,
    dbQueries: 0,
    cacheHits: 0,
    cacheMisses: 0
  };
  
  // エラー情報
  const errors = results.errors || {
    count: 0,
    details: []
  };
  
  // 改善提案
  const improvements = results.improvements || [];
  
  // 自動生成の改善提案
  if (improvements.length === 0) {
    if (overallSuccessRate < 100) {
      improvements.push('テスト失敗の原因を調査し、修正してください。');
    }
    
    if (type === ReportType.ERROR_CASES && errorCasesSuccessRate < 100) {
      improvements.push('エラーケースのバリデーションを強化してください。');
    }
    
    if (type === ReportType.VARIANTS && variantsSuccessRate < 100) {
      improvements.push('バリエーション商品の処理を改善してください。');
    }
    
    improvements.push('テストカバレッジを向上させるために、より多くのエッジケースをテストに追加してください。');
    improvements.push('パフォーマンスを最適化するために、API呼び出しの回数を減らしてください。');
    improvements.push('ユーザーエクスペリエンスを向上させるために、エラーメッセージをより分かりやすくしてください。');
  }
  
  // マークダウンレポートを生成
  let markdown = `# Shopify と Prisma の連携テストレポート

## テスト概要

このレポートは、Shopify と Prisma の連携テストの結果をまとめたものです。

テスト実行日時: ${format(new Date(), 'yyyy/MM/dd HH:mm:ss', { locale: ja })}
テストタイプ: ${type === ReportType.BASIC ? '基本テスト' : type === ReportType.ERROR_CASES ? 'エラーケーステスト' : type === ReportType.VARIANTS ? 'バリエーションテスト' : '総合テスト'}
テスト実行環境: ${environment.nodeVersion} (${environment.os})

## テスト環境

- ショップ: ${environment.shop}
- テスト商品ID: ${environment.productId}
- テスト顧客: ${environment.customer.name} (${environment.customer.email})
- 予約日数: ${environment.bookingDays}日間
- 実際のAPI使用: ${environment.useRealApi ? 'はい' : 'いいえ'}
- ローカルモード: ${environment.useLocalMode ? 'はい' : 'いいえ'}

## テスト結果サマリー

`;
  
  // テスト結果サマリーを追加
  markdown += `### 全体の結果

- 合計テスト数: ${allResults.length}
- 成功: ${allResults.filter(r => r.success).length}
- 失敗: ${allResults.filter(r => !r.success).length}
- 成功率: ${overallSuccessRate}%

`;
  
  // 各テストタイプの結果を追加
  if (testResults.basic.length > 0) {
    markdown += `### 基本テスト

- テスト数: ${testResults.basic.length}
- 成功: ${testResults.basic.filter(r => r.success).length}
- 失敗: ${testResults.basic.filter(r => !r.success).length}
- 成功率: ${basicSuccessRate}%

`;
  }
  
  if (testResults.errorCases.length > 0) {
    markdown += `### エラーケーステスト

- テスト数: ${testResults.errorCases.length}
- 成功: ${testResults.errorCases.filter(r => r.success).length}
- 失敗: ${testResults.errorCases.filter(r => !r.success).length}
- 成功率: ${errorCasesSuccessRate}%

`;
  }
  
  if (testResults.variants.length > 0) {
    markdown += `### バリエーションテスト

- テスト数: ${testResults.variants.length}
- 成功: ${testResults.variants.filter(r => r.success).length}
- 失敗: ${testResults.variants.filter(r => !r.success).length}
- 成功率: ${variantsSuccessRate}%

`;
  }
  
  if (testResults.cleanup.length > 0) {
    markdown += `### クリーンアップ

- テスト数: ${testResults.cleanup.length}
- 成功: ${testResults.cleanup.filter(r => r.success).length}
- 失敗: ${testResults.cleanup.filter(r => !r.success).length}
- 成功率: ${cleanupSuccessRate}%

`;
  }
  
  // 詳細結果を追加
  markdown += `## 詳細結果

`;
  
  // 基本テストの詳細結果
  if (testResults.basic.length > 0) {
    markdown += `### 基本テスト

`;
    
    testResults.basic.forEach((result, index) => {
      markdown += `#### テストケース${index + 1}: ${result.testCase}

- 結果: ${result.success ? '✅ 成功' : '❌ 失敗'}
${result.details ? `- 詳細: ${result.details}` : ''}
${result.duration ? `- 実行時間: ${result.duration}ms` : ''}

`;
    });
  }
  
  // エラーケーステストの詳細結果
  if (testResults.errorCases.length > 0) {
    markdown += `### エラーケーステスト

`;
    
    testResults.errorCases.forEach((result, index) => {
      markdown += `#### テストケース${index + 1}: ${result.testCase}

- 結果: ${result.success ? '✅ 成功' : '❌ 失敗'}
${result.details ? `- 詳細: ${result.details}` : ''}
${result.duration ? `- 実行時間: ${result.duration}ms` : ''}

`;
    });
  }
  
  // バリエーションテストの詳細結果
  if (testResults.variants.length > 0) {
    markdown += `### バリエーションテスト

`;
    
    testResults.variants.forEach((result, index) => {
      markdown += `#### テストケース${index + 1}: ${result.testCase}

- 結果: ${result.success ? '✅ 成功' : '❌ 失敗'}
${result.details ? `- 詳細: ${result.details}` : ''}
${result.duration ? `- 実行時間: ${result.duration}ms` : ''}

`;
    });
  }
  
  // パフォーマンス指標を追加
  markdown += `## パフォーマンス指標

- 総実行時間: ${performance.totalDuration}ms
- API呼び出し回数: ${performance.apiCalls}回
- データベースクエリ回数: ${performance.dbQueries}回
${performance.cacheHits !== undefined ? `- キャッシュヒット: ${performance.cacheHits}回` : ''}
${performance.cacheMisses !== undefined ? `- キャッシュミス: ${performance.cacheMisses}回` : ''}
${performance.cacheHits !== undefined && performance.cacheMisses !== undefined ? `- キャッシュヒット率: ${Math.round((performance.cacheHits / (performance.cacheHits + performance.cacheMisses)) * 100)}%` : ''}

`;
  
  // エラー情報を追加
  if (errors.count > 0) {
    markdown += `## エラー情報

- エラー総数: ${errors.count}

`;
    
    errors.details.forEach((error, index) => {
      markdown += `### エラー${index + 1}

\`\`\`
${JSON.stringify(error, null, 2)}
\`\`\`

`;
    });
  }
  
  // 改善提案を追加
  markdown += `## 改善提案

`;
  
  improvements.forEach((improvement, index) => {
    markdown += `${index + 1}. ${improvement}\n`;
  });
  
  return markdown;
}

/**
 * メイン関数
 */
async function main() {
  try {
    // コマンドライン引数からレポートファイルパスを取得
    const reportPath = process.argv[2] || path.join(process.cwd(), 'docs/shopify-prisma-integration-test-report.md');
    
    // テスト結果のサンプルデータ
    const sampleResults = {
      environment: {
        shop: 'example.myshopify.com',
        productId: '123456789',
        customer: {
          email: '<EMAIL>',
          name: 'テスト顧客'
        },
        bookingDays: 3,
        useRealApi: false,
        useLocalMode: true
      },
      basic: [
        { testCase: '商品同期', success: true, details: '商品データが正常に同期されました', duration: 1250 },
        { testCase: '在庫カレンダー更新', success: true, details: '在庫カレンダーが正常に更新されました', duration: 850 },
        { testCase: '予約作成', success: true, details: '予約が正常に作成されました', duration: 950 },
        { testCase: '注文連携', success: true, details: '注文が予約と正常に関連付けられました', duration: 1050 }
      ],
      errorCases: [
        { testCase: '無効な商品IDでの同期', success: true, details: 'フォールバックが正常に機能しました', duration: 750 },
        { testCase: '無効な日付での予約作成', success: true, details: 'バリデーションが正常に機能しました', duration: 650 },
        { testCase: '重複予約の作成', success: true, details: 'バリデーションが正常に機能しました', duration: 700 }
      ],
      variants: [
        { testCase: 'バリエーション商品の作成', success: true, details: 'バリエーション商品が正常に作成されました', duration: 1150 },
        { testCase: 'バリエーション商品の予約', success: true, details: 'バリエーション商品の予約が正常に作成されました', duration: 950 }
      ],
      cleanup: [
        { testCase: 'テストデータのクリーンアップ', success: true, details: 'テストデータが正常にクリーンアップされました', duration: 850 }
      ],
      performance: {
        totalDuration: 9150,
        apiCalls: 12,
        dbQueries: 45,
        cacheHits: 8,
        cacheMisses: 4
      },
      errors: {
        count: 0,
        details: []
      },
      improvements: [
        'テストカバレッジを向上させるために、より多くのエッジケースをテストに追加してください。',
        'パフォーマンスを最適化するために、API呼び出しの回数を減らしてください。',
        'ユーザーエクスペリエンスを向上させるために、エラーメッセージをより分かりやすくしてください。',
        'CI/CDパイプラインにテストを統合して、自動化を強化してください。'
      ]
    };
    
    // レポートを生成
    const report = generateReport(sampleResults, ReportType.COMPREHENSIVE);
    
    // レポートをファイルに書き込み
    fs.writeFileSync(reportPath, report);
    
    console.log(`テスト結果レポートを生成しました: ${reportPath}`);
  } catch (error) {
    console.error('レポート生成エラー:', error);
    process.exit(1);
  }
}

// スクリプトが直接実行された場合のみメイン関数を実行
if (require.main === module) {
  main().catch(e => {
    console.error('レポート生成中に予期せぬエラーが発生しました:', e);
    process.exit(1);
  });
}

// モジュールとしてエクスポート
export { generateReport, ReportType };
