import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit, useActionData } from "@remix-run/react";
import {
  <PERSON>,
  Layout,
  <PERSON><PERSON>,
  Card,
  Text,
  BlockStack,
  Box,
  Banner,
} from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import { useState, useEffect } from "react";
import { ActionFunctionArgs } from "@remix-run/node";

// メタフィールド定義の型
interface MetafieldDefinition {
  namespace: string;
  key: string;
  name: string;
  description: string;
  type: string;
  validations: {
    value?: {
      type: string;
      required?: string[];
      properties?: Record<string, any>;
      items?: {
        type: string;
        required?: string[];
        properties?: Record<string, any>;
      };
    };
  };
}

interface MetafieldError {
  field?: string;
  message: string;
}

interface ActionResult {
  status: "success" | "error";
  message: string;
}

// メタフィールド定義のレスポンス型
interface MetafieldDefinitionResponse {
  id: string;
  name: string;
  key: string;
  namespace: string;
  ownerType: string;
  type: {
    name: string;
  };
  description: string;
  validations: Array<{
    name: string;
    value: string;
  }>;
}

// 商品メタフィールドの定義
const PRODUCT_METAFIELDS: MetafieldDefinition[] = [
  {
    namespace: "rental",
    key: "basic_info",
    name: "基本情報",
    description:
      "商品の基本情報（商品コード、詳細コード、フリガナ、在庫場所、ステータス）",
    type: "json",
    validations: {
      value: {
        type: "object",
        required: ["productCode", "detailCode", "kana", "location", "status"],
        properties: {
          productCode: { type: "string" },
          detailCode: { type: "string" },
          kana: { type: "string" },
          location: { type: "string" },
          status: {
            type: "string",
            enum: ["available", "maintenance", "disposed"],
          },
        },
      },
    },
  },
  {
    namespace: "rental",
    key: "maintenance_info",
    name: "メンテナンス情報",
    description:
      "商品のメンテナンス情報（最終メンテナンス日、次回メンテナンス日、状態、備考）",
    type: "json",
    validations: {
      value: {
        type: "object",
        required: [
          "lastMaintenanceDate",
          "nextMaintenanceDate",
          "maintenanceStatus",
        ],
        properties: {
          lastMaintenanceDate: { type: "string" },
          nextMaintenanceDate: { type: "string" },
          maintenanceStatus: { type: "string" },
          maintenanceNotes: { type: "string" },
        },
      },
    },
  },
  {
    namespace: "rental",
    key: "purchase_info",
    name: "購入情報",
    description: "商品の購入情報（メーカー、購入場所、購入日、購入価格、備考）",
    type: "json",
    validations: {
      value: {
        type: "object",
        required: ["manufacturer", "purchasePlace", "purchaseDate"],
        properties: {
          manufacturer: { type: "string" },
          purchasePlace: { type: "string" },
          purchaseDate: { type: "string" },
          purchasePrice: { type: "number" },
          purchaseNotes: { type: "string" },
        },
      },
    },
  },
  {
    namespace: "rental",
    key: "rental_dates",
    name: "レンタル期間",
    description: "商品のレンタル可能期間（開始日、終了日）",
    type: "json",
    validations: {
      value: {
        type: "object",
        properties: {
          startDate: { type: "string" },
          endDate: { type: "string" },
        },
      },
    },
  },
  {
    namespace: "rental",
    key: "bookings",
    name: "予約情報",
    description:
      "商品の予約情報（予約ID、注文ID、ステータス、開始日、終了日、顧客メール、優先度、作成日時）",
    type: "json",
    validations: {
      value: {
        type: "object",
        required: ["bookings"],
        properties: {
          bookings: {
            type: "array",
            items: {
              type: "object",
              required: [
                "id",
                "orderId",
                "status",
                "startDate",
                "endDate",
                "customerEmail",
                "priority",
                "createdAt",
              ],
              properties: {
                id: { type: "string" },
                orderId: { type: "string" },
                status: {
                  type: "string",
                  enum: ["draft", "reserved", "cancelled"],
                },
                startDate: { type: "string" },
                endDate: { type: "string" },
                customerEmail: { type: "string" },
                priority: { type: "number" },
                createdAt: { type: "string" },
              },
            },
          },
        },
      },
    },
  },
  {
    namespace: "rental",
    key: "disposal_info",
    name: "廃棄情報",
    description:
      "商品の廃棄情報（廃棄フラグ、廃棄日、廃棄理由、廃棄方法、備考）",
    type: "json",
    validations: {
      value: {
        type: "object",
        required: ["isDisposed"],
        properties: {
          isDisposed: { type: "boolean" },
          disposalDate: { type: "string" },
          disposalReason: { type: "string" },
          disposalMethod: { type: "string" },
          disposalNotes: { type: "string" },
        },
      },
    },
  },
];

export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);

  // Get existing metafield definitions with more specific query
  const response = await admin.graphql(
    `query {
      metafieldDefinitions(
        first: 100,
        ownerType: PRODUCT,
        namespace: "rental"
      ) {
        nodes {
          id
          name
          key
          namespace
          ownerType
          type {
            name
          }
          description
          validations {
            name
            value
          }
        }
      }
    }`,
  );

  const data = await response.json();
  console.log("Loader response:", JSON.stringify(data, null, 2));

  return json({
    definitions: data.data.metafieldDefinitions.nodes,
    shop: session.shop,
  });
}

const CREATE_METAFIELD_DEFINITION = `
  mutation metafieldDefinitionCreate($definition: MetafieldDefinitionInput!) {
    metafieldDefinitionCreate(definition: $definition) {
      createdDefinition {
        id
        name
        namespace
        key
      }
      userErrors {
        field
        message
        code
      }
    }
  }
`;

export async function action({ request }: ActionFunctionArgs) {
  const { admin } = await authenticate.admin(request);
  const results: ActionResult[] = [];

  // Create new metafield definitions
  for (const metafield of PRODUCT_METAFIELDS) {
    try {
      const response = await admin.graphql(CREATE_METAFIELD_DEFINITION, {
        variables: {
          definition: {
            name: metafield.name,
            namespace: metafield.namespace,
            key: metafield.key,
            description: metafield.description,
            type: metafield.type,
            ownerType: "PRODUCT",
            // JSONタイプのメタフィールドの場合、validationsは空配列を指定
            validations: [],
          },
        },
      });

      const result = await response.json();
      console.log("GraphQL Response:", JSON.stringify(result, null, 2));

      if (result.data?.metafieldDefinitionCreate?.createdDefinition) {
        results.push({
          status: "success",
          message: `メタフィールド "${metafield.name}" が正常に作成されました。`,
        });
      } else if (
        result.data?.metafieldDefinitionCreate?.userErrors?.length > 0
      ) {
        const errors = result.data.metafieldDefinitionCreate.userErrors;
        results.push({
          status: "error",
          message: `メタフィールド "${metafield.name}" の作成に失敗しました: ${errors.map((e: MetafieldError) => e.message).join(", ")}`,
        });
      }
    } catch (error) {
      console.error("Error creating metafield:", error);
      results.push({
        status: "error",
        message: `メタフィールド "${metafield.name}" の作成中にエラーが発生しました。`,
      });
    }
  }

  return json({ results });
}

export default function MetafieldsPage() {
  const { definitions, shop } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const [isCreating, setIsCreating] = useState(false);
  const submit = useSubmit();

  useEffect(() => {
    if (actionData?.results) {
      setIsCreating(false);
    }
  }, [actionData]);

  const handleCreateMetafields = () => {
    setIsCreating(true);
    submit({}, { method: "POST" });
  };

  // Log existing definitions for debugging
  console.log("Existing metafield definitions:", definitions);

  return (
    <Page>
      <Layout>
        <Layout.Section>
          <BlockStack gap="500">
            {actionData?.results && (
              <Card>
                <BlockStack gap="400">
                  <Box padding="400">
                    <BlockStack gap="400">
                      {actionData.results.map((result, index) => (
                        <Banner
                          key={index}
                          title={`メタフィールドの作成${result.status === "success" ? "成功" : "エラー"}`}
                          tone={
                            result.status === "success" ? "success" : "critical"
                          }
                        >
                          <p>{result.message}</p>
                        </Banner>
                      ))}
                    </BlockStack>
                  </Box>
                </BlockStack>
              </Card>
            )}

            <Card>
              <BlockStack gap="400">
                <Box padding="400">
                  <BlockStack gap="200">
                    <Text as="h2" variant="headingMd">
                      注意
                    </Text>
                    <Text as="p" variant="bodyMd">
                      このページでは、{shop}の商品メタフィールドを設定します。
                      既に存在するメタフィールドは上書きされません。
                    </Text>
                  </BlockStack>
                </Box>
                <Box padding="400">
                  <BlockStack gap="400">
                    {PRODUCT_METAFIELDS.map((metafield, index) => (
                      <Box
                        key={index}
                        padding="400"
                        borderWidth="025"
                        borderColor="border"
                        borderStyle="solid"
                      >
                        <BlockStack gap="200">
                          <Text as="h3" variant="headingMd">
                            {metafield.name}
                          </Text>
                          <BlockStack gap="200">
                            <Text as="p" variant="bodyMd">
                              キー: {metafield.namespace}.{metafield.key}
                            </Text>
                            <Text as="p" variant="bodyMd">
                              説明: {metafield.description}
                            </Text>
                            <Text as="p" variant="bodyMd">
                              タイプ: {metafield.type}
                            </Text>
                            {definitions.some(
                              (d: MetafieldDefinitionResponse) =>
                                d.namespace === metafield.namespace &&
                                d.key === metafield.key,
                            ) && (
                              <Banner tone="info">
                                このメタフィールドは既に作成されています
                              </Banner>
                            )}
                          </BlockStack>
                        </BlockStack>
                      </Box>
                    ))}
                  </BlockStack>
                </Box>
                <Box padding="400">
                  <Button
                    variant="primary"
                    onClick={handleCreateMetafields}
                    loading={isCreating}
                    disabled={isCreating}
                  >
                    メタフィールドを作成
                  </Button>
                </Box>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
