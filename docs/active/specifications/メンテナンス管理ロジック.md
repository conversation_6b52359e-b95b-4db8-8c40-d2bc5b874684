# レンタル商品メンテナンス管理ロジック

## 概要

このドキュメントでは、レンタル商品ECシステムにおけるメンテナンス管理ロジックについて詳細に説明します。メンテナンス状態の管理、メンテナンス履歴の記録、およびメンテナンス予定の管理について解説します。

## メンテナンス状態の定義

レンタル商品のメンテナンス状態は以下のように定義されます：

1. **良好**: 通常使用可能な状態
2. **点検中**: 定期点検または臨時点検中の状態
3. **修理中**: 故障や損傷により修理中の状態
4. **清掃中**: クリーニングや清掃中の状態
5. **廃棄予定**: 使用不可で廃棄予定の状態

## メンテナンス情報の管理

### メタフィールドによる管理

メンテナンス情報は以下のメタフィールドで管理します：

1. **`rental.maintenance_status`**: メンテナンス状態
   - タイプ: `single_line_text_field`
   - 許容値: `良好`, `点検中`, `修理中`, `清掃中`, `廃棄予定`

2. **`rental.last_maintenance_date`**: 最終メンテナンス日
   - タイプ: `date`

3. **`rental.next_maintenance_date`**: 次回メンテナンス予定日
   - タイプ: `date`

4. **`rental.maintenance_notes`**: メンテナンス備考
   - タイプ: `multi_line_text_field`

### データベースによる管理

メンテナンス履歴と予定は以下のデータベースモデルで管理します：

```typescript
// メンテナンス履歴
model MaintenanceHistory {
  id                String          @id @default(cuid())
  shop              String
  productId         String
  status            String          // メンテナンス状態
  type              String          // メンテナンスタイプ
  startDate         DateTime        // 開始日
  endDate           DateTime?       // 終了日
  notes             String?         // 備考
  staffId           String?         // 担当者ID
  cost              Decimal?        @db.Decimal(10, 2) // メンテナンス費用
  
  // リレーション
  product           Product         @relation(fields: [productId], references: [id])
  staff             Staff?          @relation(fields: [staffId], references: [id])
  
  // タイムスタンプ
  createdAt         DateTime        @default(now()) @map("created_at")
  updatedAt         DateTime        @updatedAt @map("updated_at")
  
  @@index([productId])
  @@index([staffId])
  @@map("maintenance_history")
}

// メンテナンス予定
model MaintenanceSchedule {
  id                String          @id @default(cuid())
  shop              String
  productId         String
  type              String          // メンテナンスタイプ
  scheduledDate     DateTime        // 予定日
  status            String          // 状態（予定、完了、キャンセルなど）
  notes             String?         // 備考
  staffId           String?         // 担当者ID
  estimatedCost     Decimal?        @db.Decimal(10, 2) // 見積費用
  
  // リレーション
  product           Product         @relation(fields: [productId], references: [id])
  staff             Staff?          @relation(fields: [staffId], references: [id])
  
  // タイムスタンプ
  createdAt         DateTime        @default(now()) @map("created_at")
  updatedAt         DateTime        @updatedAt @map("updated_at")
  
  @@index([productId])
  @@index([staffId])
  @@index([scheduledDate])
  @@map("maintenance_schedules")
}
```

## メンテナンス管理機能

### メンテナンス状態の更新

メンテナンス状態を更新する関数：

```typescript
/**
 * メンテナンス状態を更新する関数
 * @param productId 商品ID
 * @param status メンテナンス状態
 * @param startDate 開始日
 * @param endDate 終了日（オプション）
 * @param notes 備考（オプション）
 * @param staffId 担当者ID（オプション）
 * @param type メンテナンスタイプ（オプション）
 * @returns 更新結果
 */
export async function updateMaintenanceStatus(
  productId: string,
  status: string,
  startDate: Date,
  endDate?: Date,
  notes?: string,
  staffId?: string,
  type?: string
): Promise<{ success: boolean; message?: string }> {
  try {
    // メタフィールドを更新
    await updateMaintenanceMetafields(productId, status, startDate, endDate, notes);
    
    // メンテナンス履歴を記録
    await createMaintenanceHistory(
      productId,
      status,
      startDate,
      endDate,
      notes,
      staffId,
      type || '定期点検'
    );
    
    // 在庫状態を更新
    await updateInventoryForMaintenance(productId, status, startDate, endDate);
    
    return { success: true };
  } catch (error) {
    console.error('Failed to update maintenance status:', error);
    return { 
      success: false, 
      message: `Failed to update maintenance status: ${error.message}` 
    };
  }
}

/**
 * メンテナンスメタフィールドを更新する関数
 */
async function updateMaintenanceMetafields(
  productId: string,
  status: string,
  startDate: Date,
  endDate?: Date,
  notes?: string
): Promise<void> {
  // メタフィールド更新ロジック
  // ...
}

/**
 * メンテナンス履歴を作成する関数
 */
async function createMaintenanceHistory(
  productId: string,
  status: string,
  startDate: Date,
  endDate?: Date,
  notes?: string,
  staffId?: string,
  type?: string
): Promise<MaintenanceHistory> {
  return prisma.maintenanceHistory.create({
    data: {
      productId,
      status,
      type,
      startDate,
      endDate,
      notes,
      staffId
    }
  });
}

/**
 * メンテナンスによる在庫状態を更新する関数
 */
async function updateInventoryForMaintenance(
  productId: string,
  status: string,
  startDate: Date,
  endDate?: Date
): Promise<void> {
  // 在庫更新ロジック
  // ...
}
```

### メンテナンス履歴の取得

商品のメンテナンス履歴を取得する関数：

```typescript
/**
 * メンテナンス履歴を取得する関数
 * @param productId 商品ID
 * @param options 取得オプション
 * @returns メンテナンス履歴リスト
 */
export async function getMaintenanceHistory(
  productId: string,
  options: {
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    startDate?: Date;
    endDate?: Date;
    status?: string;
    type?: string;
  } = {}
): Promise<MaintenanceHistory[]> {
  const {
    limit = 10,
    offset = 0,
    sortBy = 'startDate',
    sortOrder = 'desc',
    startDate,
    endDate,
    status,
    type
  } = options;
  
  // 検索条件を構築
  const where: any = { productId };
  
  if (startDate) {
    where.startDate = { gte: startDate };
  }
  
  if (endDate) {
    where.endDate = { lte: endDate };
  }
  
  if (status) {
    where.status = status;
  }
  
  if (type) {
    where.type = type;
  }
  
  // メンテナンス履歴を取得
  return prisma.maintenanceHistory.findMany({
    where,
    orderBy: { [sortBy]: sortOrder },
    skip: offset,
    take: limit,
    include: {
      staff: true
    }
  });
}
```

### メンテナンス予定の管理

メンテナンス予定を作成・更新・取得する関数：

```typescript
/**
 * メンテナンス予定を作成する関数
 * @param data メンテナンス予定データ
 * @returns 作成されたメンテナンス予定
 */
export async function createMaintenanceSchedule(
  data: {
    productId: string;
    type: string;
    scheduledDate: Date;
    status: string;
    notes?: string;
    staffId?: string;
    estimatedCost?: number;
  }
): Promise<MaintenanceSchedule> {
  return prisma.maintenanceSchedule.create({
    data: {
      productId: data.productId,
      type: data.type,
      scheduledDate: data.scheduledDate,
      status: data.status,
      notes: data.notes,
      staffId: data.staffId,
      estimatedCost: data.estimatedCost
    }
  });
}

/**
 * メンテナンス予定を更新する関数
 * @param id メンテナンス予定ID
 * @param data 更新データ
 * @returns 更新されたメンテナンス予定
 */
export async function updateMaintenanceSchedule(
  id: string,
  data: {
    type?: string;
    scheduledDate?: Date;
    status?: string;
    notes?: string;
    staffId?: string;
    estimatedCost?: number;
  }
): Promise<MaintenanceSchedule> {
  return prisma.maintenanceSchedule.update({
    where: { id },
    data
  });
}

/**
 * メンテナンス予定を取得する関数
 * @param options 取得オプション
 * @returns メンテナンス予定リスト
 */
export async function getMaintenanceSchedules(
  options: {
    productId?: string;
    staffId?: string;
    status?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  } = {}
): Promise<MaintenanceSchedule[]> {
  const {
    productId,
    staffId,
    status,
    startDate,
    endDate,
    limit = 10,
    offset = 0
  } = options;
  
  // 検索条件を構築
  const where: any = {};
  
  if (productId) {
    where.productId = productId;
  }
  
  if (staffId) {
    where.staffId = staffId;
  }
  
  if (status) {
    where.status = status;
  }
  
  if (startDate || endDate) {
    where.scheduledDate = {};
    
    if (startDate) {
      where.scheduledDate.gte = startDate;
    }
    
    if (endDate) {
      where.scheduledDate.lte = endDate;
    }
  }
  
  // メンテナンス予定を取得
  return prisma.maintenanceSchedule.findMany({
    where,
    orderBy: { scheduledDate: 'asc' },
    skip: offset,
    take: limit,
    include: {
      product: true,
      staff: true
    }
  });
}
```

### メンテナンス完了処理

メンテナンス予定を完了処理する関数：

```typescript
/**
 * メンテナンス予定を完了処理する関数
 * @param scheduleId メンテナンス予定ID
 * @param completionData 完了データ
 * @returns 処理結果
 */
export async function completeMaintenanceSchedule(
  scheduleId: string,
  completionData: {
    completionDate: Date;
    status: string;
    notes?: string;
    actualCost?: number;
  }
): Promise<{ success: boolean; message?: string }> {
  try {
    // トランザクション開始
    return await prisma.$transaction(async (tx) => {
      // メンテナンス予定を取得
      const schedule = await tx.maintenanceSchedule.findUnique({
        where: { id: scheduleId },
        include: { product: true }
      });
      
      if (!schedule) {
        return { 
          success: false, 
          message: 'Maintenance schedule not found' 
        };
      }
      
      // メンテナンス予定を完了状態に更新
      await tx.maintenanceSchedule.update({
        where: { id: scheduleId },
        data: {
          status: 'COMPLETED',
          notes: completionData.notes 
            ? `${schedule.notes || ''}\n\n完了備考: ${completionData.notes}`
            : schedule.notes
        }
      });
      
      // メンテナンス履歴を作成
      await tx.maintenanceHistory.create({
        data: {
          productId: schedule.productId,
          status: completionData.status,
          type: schedule.type,
          startDate: schedule.scheduledDate,
          endDate: completionData.completionDate,
          notes: completionData.notes,
          staffId: schedule.staffId,
          cost: completionData.actualCost
        }
      });
      
      // 商品のメンテナンス状態を更新
      await updateMaintenanceStatus(
        schedule.productId,
        completionData.status,
        schedule.scheduledDate,
        completionData.completionDate,
        completionData.notes,
        schedule.staffId,
        schedule.type
      );
      
      return { success: true };
    });
  } catch (error) {
    console.error('Failed to complete maintenance schedule:', error);
    return { 
      success: false, 
      message: `Failed to complete maintenance schedule: ${error.message}` 
    };
  }
}
```

## メンテナンス管理UI

### メンテナンス状態表示

商品詳細画面でのメンテナンス状態表示：

```tsx
// メンテナンス状態表示コンポーネント
function MaintenanceStatusDisplay({ product }) {
  const { maintenanceStatus, lastMaintenanceDate, nextMaintenanceDate, maintenanceNotes } = product;
  
  // メンテナンス状態に応じたバッジを表示
  const getStatusBadge = () => {
    switch (maintenanceStatus) {
      case '良好':
        return <Badge tone="success">良好</Badge>;
      case '点検中':
        return <Badge tone="attention">点検中</Badge>;
      case '修理中':
        return <Badge tone="warning">修理中</Badge>;
      case '清掃中':
        return <Badge tone="info">清掃中</Badge>;
      case '廃棄予定':
        return <Badge tone="critical">廃棄予定</Badge>;
      default:
        return <Badge tone="new">{maintenanceStatus}</Badge>;
    }
  };
  
  return (
    <Card title="メンテナンス情報">
      <Card.Section>
        <BlockStack gap="200">
          <InlineStack align="space-between">
            <Text>状態:</Text>
            {getStatusBadge()}
          </InlineStack>
          
          {lastMaintenanceDate && (
            <InlineStack align="space-between">
              <Text>最終メンテナンス日:</Text>
              <Text>{formatDate(lastMaintenanceDate)}</Text>
            </InlineStack>
          )}
          
          {nextMaintenanceDate && (
            <InlineStack align="space-between">
              <Text>次回メンテナンス予定日:</Text>
              <Text>{formatDate(nextMaintenanceDate)}</Text>
            </InlineStack>
          )}
          
          {maintenanceNotes && (
            <BlockStack>
              <Text>備考:</Text>
              <Box paddingBlock="200" paddingInline="300" background="bg-surface-secondary">
                <Text>{maintenanceNotes}</Text>
              </Box>
            </BlockStack>
          )}
        </BlockStack>
      </Card.Section>
      
      <Card.Section>
        <ButtonGroup>
          <Button onClick={() => openMaintenanceHistoryModal()}>
            メンテナンス履歴を表示
          </Button>
          <Button onClick={() => openMaintenanceScheduleModal()}>
            メンテナンス予定を表示
          </Button>
        </ButtonGroup>
      </Card.Section>
    </Card>
  );
}
```

### メンテナンス履歴表示

メンテナンス履歴を表示するモーダル：

```tsx
// メンテナンス履歴モーダル
function MaintenanceHistoryModal({ productId, isOpen, onClose }) {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    if (isOpen) {
      fetchMaintenanceHistory();
    }
  }, [isOpen, productId]);
  
  const fetchMaintenanceHistory = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/maintenance/history?productId=${productId}`);
      const data = await response.json();
      
      if (data.success) {
        setHistory(data.history);
      } else {
        console.error('Failed to fetch maintenance history:', data.message);
      }
    } catch (error) {
      console.error('Error fetching maintenance history:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Modal
      open={isOpen}
      onClose={onClose}
      title="メンテナンス履歴"
      primaryAction={{
        content: '閉じる',
        onAction: onClose
      }}
    >
      <Modal.Section>
        {loading ? (
          <Spinner accessibilityLabel="Loading maintenance history" />
        ) : history.length === 0 ? (
          <Banner title="情報" tone="info">
            メンテナンス履歴がありません
          </Banner>
        ) : (
          <DataTable
            columnContentTypes={['text', 'text', 'text', 'text', 'text']}
            headings={['日付', '状態', 'タイプ', '担当者', '備考']}
            rows={history.map(item => [
              formatDate(item.startDate),
              item.status,
              item.type,
              item.staff?.name || '-',
              item.notes || '-'
            ])}
          />
        )}
      </Modal.Section>
    </Modal>
  );
}
```

### メンテナンス予定管理

メンテナンス予定を管理するモーダル：

```tsx
// メンテナンス予定モーダル
function MaintenanceScheduleModal({ productId, isOpen, onClose }) {
  const [schedules, setSchedules] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    if (isOpen) {
      fetchMaintenanceSchedules();
    }
  }, [isOpen, productId]);
  
  const fetchMaintenanceSchedules = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/maintenance/schedules?productId=${productId}`);
      const data = await response.json();
      
      if (data.success) {
        setSchedules(data.schedules);
      } else {
        console.error('Failed to fetch maintenance schedules:', data.message);
      }
    } catch (error) {
      console.error('Error fetching maintenance schedules:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleComplete = async (scheduleId) => {
    // メンテナンス完了処理
    // ...
  };
  
  const handleCancel = async (scheduleId) => {
    // メンテナンスキャンセル処理
    // ...
  };
  
  return (
    <Modal
      open={isOpen}
      onClose={onClose}
      title="メンテナンス予定"
      primaryAction={{
        content: '新規予定を追加',
        onAction: () => openAddScheduleModal()
      }}
      secondaryActions={[
        {
          content: '閉じる',
          onAction: onClose
        }
      ]}
    >
      <Modal.Section>
        {loading ? (
          <Spinner accessibilityLabel="Loading maintenance schedules" />
        ) : schedules.length === 0 ? (
          <Banner title="情報" tone="info">
            メンテナンス予定がありません
          </Banner>
        ) : (
          <DataTable
            columnContentTypes={['text', 'text', 'text', 'text', 'text']}
            headings={['予定日', 'タイプ', '状態', '担当者', 'アクション']}
            rows={schedules.map(item => [
              formatDate(item.scheduledDate),
              item.type,
              item.status,
              item.staff?.name || '-',
              <ButtonGroup>
                <Button size="slim" onClick={() => handleComplete(item.id)}>
                  完了
                </Button>
                <Button size="slim" tone="critical" onClick={() => handleCancel(item.id)}>
                  キャンセル
                </Button>
              </ButtonGroup>
            ])}
          />
        )}
      </Modal.Section>
    </Modal>
  );
}
```

## メンテナンス分析

### メンテナンス統計

メンテナンス統計を取得する関数：

```typescript
/**
 * メンテナンス統計を取得する関数
 * @param options 取得オプション
 * @returns メンテナンス統計
 */
export async function getMaintenanceStatistics(
  options: {
    shop: string;
    startDate?: Date;
    endDate?: Date;
    productId?: string;
    groupBy?: 'month' | 'type' | 'status' | 'staff';
  }
): Promise<any> {
  const { shop, startDate, endDate, productId, groupBy = 'month' } = options;
  
  // 検索条件を構築
  const where: any = { shop };
  
  if (productId) {
    where.productId = productId;
  }
  
  if (startDate || endDate) {
    where.startDate = {};
    
    if (startDate) {
      where.startDate.gte = startDate;
    }
    
    if (endDate) {
      where.startDate.lte = endDate;
    }
  }
  
  // グループ化に基づいて統計を取得
  switch (groupBy) {
    case 'month':
      // 月ごとの統計
      // ...
      break;
    
    case 'type':
      // タイプごとの統計
      // ...
      break;
    
    case 'status':
      // 状態ごとの統計
      // ...
      break;
    
    case 'staff':
      // 担当者ごとの統計
      // ...
      break;
  }
  
  // 統計データを返す
  // ...
}
```

### メンテナンスコスト分析

メンテナンスコストを分析する関数：

```typescript
/**
 * メンテナンスコスト分析を取得する関数
 * @param options 取得オプション
 * @returns メンテナンスコスト分析
 */
export async function getMaintenanceCostAnalysis(
  options: {
    shop: string;
    startDate?: Date;
    endDate?: Date;
    productId?: string;
    groupBy?: 'month' | 'type' | 'product';
  }
): Promise<any> {
  // コスト分析ロジック
  // ...
}
```

## まとめ

このドキュメントでは、レンタル商品ECシステムにおけるメンテナンス管理ロジックについて詳細に説明しました。メンテナンス状態の管理、メンテナンス履歴の記録、およびメンテナンス予定の管理について解説しました。

メンテナンス管理は、レンタル商品の品質と可用性を維持するために重要な機能です。本システムでは、メタフィールドとデータベースを組み合わせることで、柔軟かつ詳細なメンテナンス管理を実現しています。

今後の拡張として、メンテナンスコスト分析、メンテナンス効率の最適化、および予防的メンテナンスの予測を計画しています。これにより、より効率的なメンテナンス管理と運用コストの削減が可能になります。
