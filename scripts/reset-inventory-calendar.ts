/**
 * 在庫カレンダーリセットスクリプト
 *
 * このスクリプトは、テスト商品の在庫カレンダーをリセットします。
 * 実行方法: npx tsx scripts/reset-inventory-calendar.ts [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { addDays, format } from 'date-fns';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * 在庫カレンダーをリセットする関数
 */
async function resetInventoryCalendar() {
  try {
    console.log('在庫カレンダーのリセットを開始します...');

    // 商品IDをコマンドライン引数から取得
    const shopifyProductId = process.argv[2] || '3333333333';

    // 商品情報を取得
    const product = await prisma.product.findFirst({
      where: { shopifyId: shopifyProductId }
    });

    if (!product) {
      console.error(`商品ID ${shopifyProductId} が見つかりません`);
      return;
    }

    console.log(`商品情報を取得しました: ${product.title} (ID: ${product.id})`);

    // 在庫カレンダーを削除
    const deleteResult = await prisma.inventoryCalendar.deleteMany({
      where: { productId: product.id }
    });

    console.log(`${deleteResult.count}件の在庫カレンダーを削除しました`);

    // 今日から1年分の在庫カレンダーを作成
    const today = new Date();
    const endDate = addDays(today, 365);

    console.log(`${format(today, 'yyyy-MM-dd')}から${format(endDate, 'yyyy-MM-dd')}までの在庫カレンダーを作成します`);

    // 日付ごとに在庫カレンダーを作成
    for (let date = new Date(today); date <= endDate; date = addDays(date, 1)) {
      await prisma.inventoryCalendar.create({
        data: {
          productId: product.id,
          date,
          isAvailable: true,
          shop: product.shop,
          shopifyProductId: product.shopifyId
        }
      });
    }

    console.log(`在庫カレンダーの作成が完了しました`);
    console.log('在庫カレンダーのリセットが完了しました');
  } catch (error) {
    console.error('在庫カレンダーのリセット中にエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
resetInventoryCalendar();
