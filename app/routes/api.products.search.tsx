/**
 * 商品検索API
 * 商品名やSKUで商品を検索し、その商品の予約状況を返す
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { buildImprovedSkuSearch } from "../utils/sku-search-improved";

export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  const url = new URL(request.url);
  const searchQuery = url.searchParams.get("q") || "";

  try {
    if (!searchQuery) {
      return json({ products: [], message: "検索クエリが必要です" });
    }

    console.log(`商品検索API: "${searchQuery}"`);

    // 改善されたSKU検索条件を生成
    const { searchConditions } = buildImprovedSkuSearch(searchQuery);
    
    // 商品を検索
    const products = await prisma.product.findMany({
      where: {
        shop,
        OR: [
          { title: { contains: searchQuery.trim(), mode: 'insensitive' } },
          // 改善されたSKU検索条件を追加
          ...searchConditions
        ]
      },
      select: {
        id: true,
        title: true,
        sku: true,
        status: true,
        shopifyId: true,
        basicInfo: true,
      },
      take: 20 // 最大20件まで
    });

    console.log(`商品検索結果: ${products.length}件の商品が見つかりました`);

    if (products.length === 0) {
      return json({ 
        products: [], 
        message: `「${searchQuery}」に一致する商品が見つかりませんでした` 
      });
    }

    // 各商品の予約状況を取得
    const productsWithBookings = await Promise.all(
      products.map(async (product) => {
        const bookings = await prisma.booking.findMany({
          where: {
            shop,
            productId: product.id
          },
          select: {
            id: true,
            startDate: true,
            endDate: true,
            status: true,
            bookingType: true,
            customerName: true,
            customerEmail: true,
            totalAmount: true,
            createdAt: true,
            bookingId: true,
          },
          orderBy: {
            startDate: 'asc'
          }
        });

        // 日付をフォーマット
        const formatDate = (date: Date | null | undefined) => {
          if (!date) return "";
          const d = new Date(date);
          return `${d.getFullYear()}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getDate().toString().padStart(2, '0')}`;
        };

        // レンタル日数を計算
        const calculateRentalDays = (start: Date, end: Date) => {
          const startDate = new Date(start);
          const endDate = new Date(end);
          const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return diffDays + 1; // 開始日と終了日を含める
        };

        const formattedBookings = bookings.map(booking => ({
          id: booking.id,
          bookingId: booking.bookingId || "",
          startDate: formatDate(booking.startDate),
          endDate: formatDate(booking.endDate),
          rentalDays: booking.startDate && booking.endDate 
            ? calculateRentalDays(booking.startDate, booking.endDate)
            : 0,
          status: booking.status,
          bookingType: booking.bookingType,
          customerName: booking.customerName || "",
          customerEmail: booking.customerEmail || "",
          totalAmount: booking.totalAmount ? `¥${booking.totalAmount.toLocaleString()}` : "",
          createdAt: booking.createdAt ? new Date(booking.createdAt).toLocaleString() : "",
        }));

        return {
          id: product.id,
          title: product.title,
          sku: product.sku,
          status: product.status,
          shopifyId: product.shopifyId,
          basicInfo: product.basicInfo,
          bookings: formattedBookings,
          bookingCount: formattedBookings.length
        };
      })
    );

    return json({
      products: productsWithBookings,
      message: `${products.length}件の商品が見つかりました`,
      searchQuery
    });

  } catch (error) {
    console.error("商品検索エラー:", error);
    return json({
      products: [],
      message: "商品検索中にエラーが発生しました",
      error: String(error)
    }, { status: 500 });
  }
}
