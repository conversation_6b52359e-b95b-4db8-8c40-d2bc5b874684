/**
 * 予約Webhookハンドラー
 *
 * このファイルは、Shopifyの注文作成・更新・支払い完了時のWebhookを処理し、
 * 予約ステータスを更新します。
 */
import { json } from "@remix-run/node";
import { prisma } from "~/db.server";
import { BookingService } from "~/services/booking.service";
import { logger } from "~/utils/logger";

/**
 * Webhookリクエストを処理するアクションハンドラー
 */
export async function action({ request }) {
  try {
    console.log("========== 予約Webhook受信開始 ==========");

    // リクエストヘッダーを確認
    const headers = Object.fromEntries(request.headers.entries());
    console.log("Webhookヘッダー:", JSON.stringify(headers, null, 2));

    // リクエストボディを取得（認証前に取得）
    const rawBody = await request.text();
    const payload = JSON.parse(rawBody);
    console.log("Webhookペイロード:", JSON.stringify(payload, null, 2));

    // Shopify認証
    const hmac = headers["x-shopify-hmac-sha256"];
    const shop = headers["x-shopify-shop-domain"];
    const topic = headers["x-shopify-topic"];

    if (!hmac || !shop || !topic) {
      console.error("必要なヘッダーが不足しています");
      return json({ error: "必要なヘッダーが不足しています" }, { status: 401 });
    }

    console.log(`Webhook処理: Shop ${shop}, Topic ${topic}`);

    // セッションを取得（オフラインセッション）
    const sessionId = `offline_${shop}`;
    const session = await prisma.session.findUnique({
      where: { id: sessionId }
    });

    if (!session) {
      console.error(`セッションが見つかりません: ${sessionId}`);
      return json({ error: "セッションが見つかりません" }, { status: 401 });
    }

    console.log("セッション取得成功:", session.shop);

    // 注文情報を取得
    const orderId = payload.id;
    const orderName = payload.name;
    const financialStatus = payload.financial_status;
    const fulfillmentStatus = payload.fulfillment_status;
    
    console.log(`Webhook処理: 注文ID ${orderId} (${orderName || '不明'})`);
    console.log(`支払い状態: ${financialStatus}, 配送状態: ${fulfillmentStatus}`);

    // 予約IDを抽出
    let bookingId = null;
    
    // 注文のラインアイテムを確認
    const lineItems = payload.line_items || [];
    
    // 各ラインアイテムのプロパティを確認
    for (const item of lineItems) {
      const properties = item.properties || [];
      
      // プロパティから予約IDを検索
      for (const prop of properties) {
        if (prop.name === '_booking_id') {
          bookingId = prop.value;
          break;
        }
      }
      
      if (bookingId) break;
    }
    
    if (!bookingId) {
      console.log("予約IDが見つかりません。通常の注文として処理します。");
      return json({ success: true, message: "予約IDなし" });
    }
    
    console.log(`予約ID ${bookingId} の処理を開始します。`);
    
    // 予約情報を取得
    const booking = await prisma.booking.findFirst({
      where: { bookingId }
    });
    
    if (!booking) {
      console.warn(`予約ID ${bookingId} が見つかりません。`);
      return json({ success: false, error: "予約が見つかりません" });
    }
    
    // BookingServiceを初期化
    const bookingService = new BookingService();
    
    // トピックに応じた処理
    if (topic === 'orders/create') {
      // 注文作成時の処理
      await bookingService.updateBookingStatus(booking.id, 'CONFIRMED', {
        orderId: orderId.toString(),
        orderName,
        notes: `注文作成: ${orderName}`
      });
      
      console.log(`予約ID ${bookingId} を確定状態に更新しました。`);
    } else if (topic === 'orders/updated') {
      // 注文更新時の処理
      if (financialStatus === 'paid') {
        // 支払い完了の場合
        await bookingService.updatePaymentStatus(booking.id, 'PAID', {
          notes: `支払い完了: ${orderName}`,
          paymentMethod: 'CREDIT_CARD'
        });
        
        console.log(`予約ID ${bookingId} の支払い状態を完了に更新しました。`);
      } else if (financialStatus === 'refunded' || financialStatus === 'voided') {
        // 返金または無効化の場合
        await bookingService.cancelBooking(booking.id, {
          cancelReason: `注文${financialStatus === 'refunded' ? '返金' : '無効化'}`,
          notes: `注文${financialStatus === 'refunded' ? '返金' : '無効化'}: ${orderName}`
        });
        
        console.log(`予約ID ${bookingId} をキャンセル状態に更新しました。`);
      } else {
        // その他の状態変更
        console.log(`予約ID ${bookingId} の注文状態が ${financialStatus} に変更されました。`);
      }
    } else if (topic === 'orders/cancelled') {
      // 注文キャンセル時の処理
      await bookingService.cancelBooking(booking.id, {
        cancelReason: '注文キャンセル',
        notes: `注文キャンセル: ${orderName}`
      });
      
      console.log(`予約ID ${bookingId} をキャンセル状態に更新しました。`);
    } else if (topic === 'orders/fulfilled') {
      // 注文配送完了時の処理
      await bookingService.updateBookingStatus(booking.id, 'IN_PROGRESS', {
        notes: `配送完了: ${orderName}`
      });
      
      console.log(`予約ID ${bookingId} を進行中状態に更新しました。`);
    } else if (topic === 'orders/paid') {
      // 注文支払い完了時の処理
      await bookingService.updatePaymentStatus(booking.id, 'PAID', {
        notes: `支払い完了: ${orderName}`,
        paymentMethod: 'CREDIT_CARD'
      });
      
      console.log(`予約ID ${bookingId} の支払い状態を完了に更新しました。`);
    }

    console.log("========== 予約Webhook処理完了 ==========");
    return json({ success: true });
  } catch (error) {
    console.error("========== 予約Webhook処理エラー ==========");
    console.error("エラー詳細:", error);
    console.error("エラーメッセージ:", error.message);
    console.error("エラースタック:", error.stack);
    return json({ error: error.message }, { status: 500 });
  }
}

/**
 * GETリクエストを処理するローダーハンドラー
 */
export function loader() {
  return json({ message: "Method not allowed" }, { status: 405 });
}
