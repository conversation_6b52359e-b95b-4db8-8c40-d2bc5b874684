/**
 * 統一カレンダーのデモページ
 *
 * 統一カレンダーコンポーネントの使用例を示すページです。
 * バックエンドとフロントエンドで共通して使用できるカレンダー機能を実装しています。
 */

import type { BookingStatus, BookingType } from "@prisma/client";
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { TitleBar } from "@shopify/app-bridge-react";
import {
    Banner,
    BlockStack,
    Box,
    Card,
    Layout,
    Page,
    Select,
    Text
} from "@shopify/polaris";
import { useCallback, useState } from "react";
import { UnifiedCalendarWithErrorBoundary } from "../components/Calendar/UnifiedCalendar";
import { prisma } from "../db.server";
import { formatJapaneseDateWithWeekday, type CalendarBooking } from "../utils/calendar/unified-calendar";
import { authenticate } from "../shopify.server";

// ローダーデータの型定義
interface LoaderData {
  products: Array<{
    id: string;
    title: string;
    sku: string;
    status: string;
  }>;
  bookings: Array<{
    id: string;
    productId: string;
    startDate: string;
    endDate: string;
    status: string;
    bookingType: string;
    priority: number;
    customerEmail: string | null;
    customerName: string | null;
    product: {
      id: string;
      title: string;
      sku: string;
    };
  }>;
}

// ローダー関数
export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);

  try {
    // 商品データを取得
    const products = await prisma.product.findMany({
      where: {
        shop: session.shop,
      },
      select: {
        id: true,
        title: true,
        sku: true,
        status: true,
      },
    });

    // 予約データを取得
    const bookings = await prisma.booking.findMany({
      where: {
        shop: session.shop,
      },
      select: {
        id: true,
        productId: true,
        startDate: true,
        endDate: true,
        status: true,
        bookingType: true,
        priority: true,
        customerEmail: true,
        customerName: true,
        product: {
          select: {
            id: true,
            title: true,
            sku: true,
          },
        },
      },
    });

    // Date型を文字列に変換してLoaderDataの型に合わせる
    const serializedBookings = bookings.map((b) => ({
      id: b.id,
      productId: b.productId,
      startDate: b.startDate.toISOString(),
      endDate: b.endDate.toISOString(),
      status: b.status,
      bookingType: b.bookingType,
      priority: b.priority,
      customerEmail: b.customerEmail,
      customerName: b.customerName,
      product: b.product || { id: '', title: '', sku: '' }, // nullの場合は空のオブジェクトを提供
    }));

    return json({
      products,
      bookings: serializedBookings,
    });
  } catch (error) {
    console.error('統一カレンダーデータの読み込み中にエラーが発生しました:', error);
    return json({
      products: [],
      bookings: [],
      error: '統一カレンダーデータの読み込み中にエラーが発生しました'
    });
  }
}

// メインコンポーネント
export default function CalendarUnified() {
  const { products, bookings, error } = useLoaderData<LoaderData & { error?: string }>();

  // エラーがある場合はエラーメッセージを表示
  if (error) {
    return (
      <Page title="統一カレンダー" backAction={{ content: "戻る", url: "/app" }}>
        <TitleBar title="統一カレンダー" />
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Banner tone="critical">
                  <Text variant="headingMd" as="h2">エラーが発生しました</Text>
                  <Text as="p">{error}</Text>
                  <Text as="p">ページをリロードするか、管理者にお問い合わせください。</Text>
                </Banner>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>
      </Page>
    );
  }

  // 文字列型の日付を Date オブジェクトに変換し、必要なフィールドのみで CalendarBooking 型の配列を作成
  const calendarBookings: CalendarBooking[] = bookings.map((b) => ({
    id: b.id,
    productId: b.productId,
    startDate: new Date(b.startDate),
    endDate: new Date(b.endDate),
    status: b.status as BookingStatus,
    bookingType: b.bookingType as BookingType,
    priority: b.priority,
    customerEmail: b.customerEmail,
    customerName: b.customerName,
  }));

  // 選択された商品ID
  const [selectedProductId, setSelectedProductId] = useState<string>("");

  // 選択された日付範囲
  const [selectedRange, setSelectedRange] = useState<{
    startDate: Date | null;
    endDate: Date | null;
  }>({
    startDate: null,
    endDate: null,
  });

  // 商品選択ハンドラー
  const handleProductChange = useCallback((value: string) => {
    setSelectedProductId(value);
    setSelectedRange({ startDate: null, endDate: null });
  }, []);

  // 日付範囲選択ハンドラー
  const handleRangeSelect = useCallback((startDate: Date, endDate: Date) => {
    setSelectedRange({ startDate, endDate });
  }, []);

  // 選択された商品に関連する予約のみをフィルタリング
  const filteredBookings = selectedProductId
    ? calendarBookings.filter((booking) => booking.productId === selectedProductId)
    : [];

  // 商品選択オプション
  const productOptions = [
    { label: "商品を選択", value: "" },
    ...products.map((product) => ({
      label: product.title,
      value: product.id,
    })),
  ];

  return (
    <Page title="統一カレンダー" backAction={{ content: "戻る", url: "/app" }}>
      <TitleBar title="統一カレンダー" />

      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text variant="headingMd" as="h2">
                統一カレンダーデモ
              </Text>

              <Text as="p">
                このページでは、バックエンドとフロントエンドで共通して使用できる統一カレンダーコンポーネントのデモを表示しています。
                日付選択、予約管理、カレンダー表示に関するすべてのロジックが一元管理されています。
              </Text>

              <Select
                label="商品を選択"
                options={productOptions}
                value={selectedProductId}
                onChange={handleProductChange}
                placeholder="商品を選択してください"
              />
            </BlockStack>
          </Card>
        </Layout.Section>

        {selectedProductId && (
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">
                  予約カレンダー
                </Text>

                <UnifiedCalendarWithErrorBoundary
                  bookings={filteredBookings}
                  settings={{
                    excludeSundays: true,
                    excludeHolidays: true,
                    excludeNewYear: true,
                    minRentalDays: 1,
                    maxRentalDays: 30,
                  }}
                  minDate={new Date()}
                  maxDate={new Date(new Date().setFullYear(new Date().getFullYear() + 1))}
                  onRangeSelect={handleRangeSelect}
                  showSelectedDates={true}
                  showLegend={true}
                  showValidationErrors={true}
                  numberOfMonths={2}
                />
              </BlockStack>
            </Card>
          </Layout.Section>
        )}

        {selectedRange.startDate && selectedRange.endDate && (
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">
                  選択された日付範囲
                </Text>

                <Box background="bg-surface-success" padding="300" borderRadius="100">
                  <BlockStack gap="200">
                    <Text as="p">
                      開始日: {formatJapaneseDateWithWeekday(selectedRange.startDate)}
                    </Text>
                    <Text as="p">
                      終了日: {formatJapaneseDateWithWeekday(selectedRange.endDate)}
                    </Text>
                    <Text as="p">
                      期間: {Math.round((selectedRange.endDate.getTime() - selectedRange.startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1}日間
                    </Text>
                  </BlockStack>
                </Box>

                <Banner tone="info">
                  <Text as="p">
                    選択された日付範囲は、バックエンドとフロントエンドで共通して使用できます。
                    この情報を使用して、予約の作成や更新を行うことができます。
                  </Text>
                </Banner>
              </BlockStack>
            </Card>
          </Layout.Section>
        )}

        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text variant="headingMd" as="h2">
                使用方法
              </Text>

              <Text as="p">
                統一カレンダーコンポーネントは、以下のファイルで実装されています：
              </Text>

              <Box background="bg-surface-secondary" padding="300" borderRadius="100">
                <BlockStack gap="200">
                  <Text as="p" fontWeight="bold">
                    ユーティリティ関数:
                  </Text>
                  <Text as="p" fontWeight="medium">
                    app/utils/calendar/unified-calendar.ts
                  </Text>
                  <Text as="p">
                    日付処理、予約管理、カレンダー表示に関するすべてのロジックを一元管理しています。
                    バックエンドとフロントエンドの両方で使用できます。
                  </Text>
                </BlockStack>
              </Box>

              <Box background="bg-surface-secondary" padding="300" borderRadius="100">
                <BlockStack gap="200">
                  <Text as="p" fontWeight="bold">
                    コンポーネント:
                  </Text>
                  <Text as="p" fontWeight="medium">
                    app/components/Calendar/UnifiedCalendar.tsx
                  </Text>
                  <Text as="p">
                    Shopify Polarisの DatePicker をベースにしたカスタマイズ可能なカレンダーコンポーネントです。
                    予約データの表示、日付選択、バリデーションなどの機能を提供します。
                  </Text>
                </BlockStack>
              </Box>

              <Box background="bg-surface-secondary" padding="300" borderRadius="100">
                <BlockStack gap="200">
                  <Text as="p" fontWeight="bold">
                    使用例:
                  </Text>
                  <Text as="p" fontWeight="medium">
                    app/routes/app.calendar-unified.tsx
                  </Text>
                  <Text as="p">
                    統一カレンダーコンポーネントの使用例を示すページです。
                    商品選択、予約データの表示、日付範囲の選択などの機能を実装しています。
                  </Text>
                </BlockStack>
              </Box>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
