import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import {
  Page, Card, Tabs, TextField, Select, Checkbox, Button, DataTable, DatePicker, Collapsible, LegacyStack, BlockStack, Icon
} from '@shopify/polaris';
import {
  SearchIcon, XIcon, ProductIcon, CategoriesIcon
} from '@shopify/polaris-icons';
import React, { useCallback, useState } from 'react';
import { prisma } from '../db.server';
import { formatLocalDate } from '../utils/date/date-utils';

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  
  // クエリパラメータを取得
  const url = new URL(request.url);
  const startDate = url.searchParams.get('startDate') || undefined;
  const endDate = url.searchParams.get('endDate') || undefined;
  const productCode = url.searchParams.get('productCode') || undefined;
  const tag = url.searchParams.get('tag') || undefined;
  const note = url.searchParams.get('note') || undefined;
  const onlyAvailable = url.searchParams.get('onlyAvailable') === 'true';
  
  // 検索条件を構築
  const where: any = {};
  
  if (startDate && endDate) {
    where.OR = [
      {
        startDate: { lte: new Date(endDate) },
        endDate: { gte: new Date(startDate) }
      }
    ];
  }
  
  // 商品関連の検索条件
  const productWhere: any = {};
  
  if (productCode) {
    productWhere.sku = { startsWith: productCode };
  }
  
  if (tag || note) {
    productWhere.OR = [];
    
    if (tag) {
      productWhere.OR.push({ 
        basicInfo: { 
          path: ['tags'],
          array_contains: tag 
        } 
      });
    }
    
    if (note) {
      productWhere.OR.push({ description: { contains: note } });
    }
  }
  
  // 予約データを取得
  const bookings = await prisma.booking.findMany({
    where,
    include: {
      product: true
    }
  });
  
  // 利用可能な予約のみをフィルタリング
  const filteredBookings = onlyAvailable 
    ? bookings.filter(booking => booking.status !== 'CONFIRMED' && booking.status !== 'COMPLETED')
    : bookings;
  
  // 商品データを取得
  const products = await prisma.product.findMany({
    where: productWhere
  });
  
  // カテゴリ一覧を取得（basicInfoからカテゴリを抽出）
  const allProducts = await prisma.product.findMany({
    select: {
      basicInfo: true
    }
  });
  
  // basicInfoからカテゴリを抽出
  const categories = new Set<string>();
  allProducts.forEach(product => {
    if (product.basicInfo && typeof product.basicInfo === 'object') {
      const basicInfo = product.basicInfo as any;
      if (basicInfo.category) {
        categories.add(basicInfo.category);
      }
    }
  });
  
  return json({
    bookings: filteredBookings.map(booking => ({
      id: booking.id,
      productId: booking.productId,
      productTitle: booking.product.title,
      productSku: booking.product.sku,
      productCategory: booking.product.basicInfo && typeof booking.product.basicInfo === 'object' 
        ? (booking.product.basicInfo as any).category || '' 
        : '',
      startDate: booking.startDate.toISOString(),
      endDate: booking.endDate.toISOString(),
      status: booking.status
    })),
    products,
    categories: Array.from(categories)
  });
}

export default function BookingAggregate() {
  const { bookings, categories } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  
  // タブ状態
  const [selectedTab, setSelectedTab] = useState(0);
  const handleTabChange = useCallback(
    (selectedTabIndex: number) => setSelectedTab(selectedTabIndex),
    [],
  );

  // 検索条件パネルの開閉状態
  const [searchOpen, setSearchOpen] = useState(true);
  const toggleSearchOpen = useCallback(() => setSearchOpen((open) => !open), []);

  // 検索条件の状態管理
  const [dateRange, setDateRange] = useState<{start: Date, end: Date}>({start: new Date(), end: new Date()});
  const [productCode, setProductCode] = useState('');
  const [category, setCategory] = useState('');
  const [tag, setTag] = useState('');
  const [note, setNote] = useState('');
  const [onlyAvailable, setOnlyAvailable] = useState(false);

  // カテゴリ選択肢の生成
  const categoryOptions = [
    {label: '--', value: ''},
    ...categories.map(c => ({ label: c, value: c }))
  ];

  // 検索結果の行データを生成
  const rows = bookings.map(booking => [
    booking.productSku,
    booking.productTitle,
    booking.productCategory,
    booking.status === 'CONFIRMED' ? '予約中' : 
    booking.status === 'COMPLETED' ? '貸出中' : 
    booking.status === 'CANCELLED' ? '利用可' : '利用可',
    `${formatLocalDate(booking.startDate)} 〜 ${formatLocalDate(booking.endDate)}`
  ]);

  const handleSearch = () => {
    // 検索条件をURLパラメータに変換
    const params = new URLSearchParams();
    
    params.set('startDate', dateRange.start.toISOString().split('T')[0]);
    params.set('endDate', dateRange.end.toISOString().split('T')[0]);
    
    if (productCode) {
      params.set('productCode', productCode);
    }
    
    if (category) {
      params.set('category', category);
    }
    
    if (tag) {
      params.set('tag', tag);
    }
    
    if (note) {
      params.set('note', note);
    }
    
    if (onlyAvailable) {
      params.set('onlyAvailable', 'true');
    }
    
    navigate(`/bookings/aggregate?${params.toString()}`);
  };

  const handleClear = () => {
    setDateRange({start: new Date(), end: new Date()});
    setProductCode('');
    setCategory('');
    setTag('');
    setNote('');
    setOnlyAvailable(false);
    navigate('/bookings/aggregate');
  };

  const tabs = [
    {id: 'category-search', content: 'カテゴリ検索', accessibilityLabel: 'カテゴリ検索', panelID: 'category-search-content', icon: <Icon source={CategoriesIcon} />},
    {id: 'product-code-search', content: '商品コード検索', accessibilityLabel: '商品コード検索', panelID: 'product-code-search-content', icon: <Icon source={ProductIcon} />},
  ];

  return (
    <Page 
      title="予約状況一括照会"
      secondaryActions={[
        {
          content: searchOpen ? '検索条件を閉じる' : '検索条件を開く',
          onAction: toggleSearchOpen,
        },
      ]}
    >
      <Card>
        <Tabs tabs={tabs} selected={selectedTab} onSelect={handleTabChange} fitted>
          <BlockStack gap="400">
            <Collapsible
              open={searchOpen}
              id="search-collapsible"
              transition={{duration: '300ms', timingFunction: 'ease-in-out'}}
            >
              <div style={{marginTop: '16px', padding: '16px'}}> {/* Card.Sectionの代わりにpaddingを追加 */}
                <LegacyStack vertical>
                  <LegacyStack distribution="fillEvenly">
                    <DatePicker
                      month={dateRange.start.getMonth()}
                      year={dateRange.start.getFullYear()}
                      onChange={setDateRange} // 直接setDateRangeを渡す
                      selected={dateRange} // {start: Date, end: Date}形式
                      allowRange
                      multiMonth
                    />
                  </LegacyStack>
                  <TextField
                    label="商品コード（前方一致）"
                    value={productCode}
                    onChange={setProductCode}
                    placeholder="例: CAMERA-001"
                    disabled={selectedTab === 0} // カテゴリ検索時は非活性
                    autoComplete="off"
                  />
                  {selectedTab === 0 && (
                    <Select
                      label="カテゴリ"
                      options={categoryOptions}
                      onChange={setCategory}
                      value={category}
                    />
                  )}
                  <TextField
                    label="タグ"
                    value={tag}
                    onChange={setTag}
                    placeholder="例: 初心者向け"
                    autoComplete="off"
                  />
                  <TextField
                    label="備考"
                    value={note}
                    onChange={setNote}
                    placeholder="例: 特記事項あり"
                    multiline={3}
                    autoComplete="off"
                  />
                  <Checkbox
                    label="貸出可能なもののみ表示する"
                    checked={onlyAvailable}
                    onChange={(newChecked) => setOnlyAvailable(newChecked)}
                  />
                  <LegacyStack distribution="trailing">
                    <Button onClick={handleClear} icon={<Icon source={XIcon} />}>クリア</Button>
                    <Button onClick={handleSearch} variant="primary" icon={<Icon source={SearchIcon} />}>検索</Button>
                  </LegacyStack>
                </LegacyStack>
              </div>
            </Collapsible>
          </BlockStack>
        </Tabs>
        <DataTable
          columnContentTypes={[
            'text',
            'text',
            'text',
            'text',
            'text'
          ]}
          headings={[
            '商品コード',
            '商品名',
            'カテゴリ',
            '貸出可否',
            '予約期間'
          ]}
          rows={rows}
          footerContent={`全 ${rows.length} 件`}
        />
      </Card>
    </Page>
  );
}
