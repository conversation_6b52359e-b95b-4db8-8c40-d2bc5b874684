<div data-section-id="{{ section.id }}" data-section-type="contact-form">
    <div class="container">
        <div class="contact-form">
            {%- if section.settings.title != blank -%}
                <h4 class="mb-35 text-center">{{ section.settings.title }}</h4>
            {%- endif -%}
            <div class="row">
                {%- if section.settings.show_info_column or section.settings.contact_map_code != blank -%}
                    <div class="col-12 col-md-4 mb-30">
                        {%- if section.settings.show_info_column -%}
                            {%- if section.settings.page_content != blank -%}
                                <div class="rte">
                                    {% include 'parse-page-html-content' with default_page: 'include-contact-form' page_content: section.settings.page_content %}
                                </div>
                                {%- assign has_content = true -%}
                            {%- elsif page.content -%}
                                <div class="rte">
                                    {% include 'parse-for-icons' content: page.content %}
                                </div>
                                {%- assign has_content = true -%}
                            {%- endif -%}
                        {%- endif -%}
                        <div{% if has_content %} class="mt-30"{% endif %}>
                            {%- if section.settings.contact_map_code != blank -%}
                                <div class="contact-map position-relative">
                                    {{ section.settings.contact_map_code }}
                                </div>
                            {%- endif -%}
                        </div>
                    </div>
                {%- endif -%}
                <div class="col-12 col-md-8 mx-auto">
                    <h3>{{ 'contact.form.title' | t }}</h3>
                    <p class="fs-lg">{{ 'contact.form.paragraph' | t }}</p>
                    {% form 'contact' %}
                        {%- if form.posted_successfully? -%}
                            <p class="form-success">{{ 'contact.form.post_success' | t }}</p>
                        {%- endif -%}
                        {%- if section.settings.show_input_label -%}
                            <label for="ContactFormName" class="label-required">{{ 'contact.form.name_title' | t }}</label>
                        {%- endif -%}
                        <input type="text"
                               name="contact[name]"
                               id="ContactFormName"
                               placeholder="{{ 'contact.form.name_placeholder' | t }}"
                               value="{% if form[name] %}{{ form[name] }}{% elsif customer %}{{ customer.name }}{% endif %}"
                               required="required">
                        {%- if section.settings.show_input_label -%}
                            <label for="ContactFormEmail" class="label-required">{{ 'contact.form.email_title' | t }}</label>
                        {%- endif -%}
                        <input type="email"
                               name="contact[email]"
                               id="ContactFormEmail"
                               placeholder="{{ 'contact.form.email_placeholder' | t }}"
                               value="{% if form.email %}{{ form.email }}{% elsif customer %}{{ customer.email }}{% endif %}"
                               spellcheck="false"
                               autocomplete="off"
                               autocapitalize="off"
                               required="required">
                        {%- if section.settings.show_input_label -%}
                            <label for="ContactFormPhone">{{ 'contact.form.phone_title' | t }}</label>
                        {%- endif -%}
                        <input type="tel"
                               class="form-control"
                               name="contact[phone]"
                               id="ContactFormPhone"
                               placeholder="{{ 'contact.form.phone_placeholder' | t }}"
                               value="{% if form[phone] %}{{ form[phone] }}{% elsif customer %}{{ customer.phone }}{% endif %}">
                        {%- if section.settings.show_input_label -%}
                            <label for="ContactFormMessage" class="label-required">{{ 'contact.form.message_title' | t }}</label>
                        {%- endif -%}
                        <textarea rows="8"
                                  name="contact[body]"
                                  id="ContactFormMessage"
                                  placeholder="{{ 'contact.form.message_placeholder' | t }}"
                                  required="required">
                          {%- if form.body -%}
                              {{ form.body }}
                          {%- endif -%}
                        </textarea>
                        {% include 'form-get-message' %}
                        <div class="pt-10">
                            <button type="submit" class="btn{% if section.settings.button_type == 'default' %} btn--secondary{% endif %}"><i class="mr-5">{% render 'icon-theme-196' %}</i>{{ 'contact.form.send' | t }}</button>
                        </div>
                    {% endform %}
                </div>
            </div>
        </div>
    </div>
</div>


{% schema %}
{
    "name": { "en": "Contact form", "ja": "お問い合わせフォーム"},
    "settings": [
        {
            "type": "textarea",
            "id": "title",
            "label": { "en": "Title", "ja": "タイトル"}
        },
        {
            "type": "checkbox",
            "id": "show_input_label",
            "label": { "en": "Show input label", "ja": "インプットラベルを表示"},
            "default": true
        },
        {
            "type": "select",
            "id": "button_type",
            "label": { "en": "Button type", "ja": "ボタンのタイプ"},
            "default": "default",
            "options": [
              {
                "value": "default",
                "label": { "en": "Default", "ja": "デフォルト"}
              },
              {
                "value": "invert",
                "label": { "en": "Invert", "ja": "反転"}
              }
            ]
        },
        {
            "type": "header",
            "content": { "en": "Content", "ja": "コンテンツ"}
        },
        {
            "type": "checkbox",
            "id": "show_info_column",
            "label": { "en": "Show info column", "ja": "インフォーメーションコラムを表示"},
            "default": true
        },
        {
            "type": "page",
            "id": "page_content",
            "label": { "en": "Page content", "ja": "ページコンテンツ"},
            "info": { "en": "Default page: 'contact'", "ja": "デフォルトでは 'contact'のページが適用されます。"}
        },
        {
            "type": "textarea",
            "id": "contact_map_code",
            "label": { "en": "Contact Map Code", "ja": "マップコード"},
            "default": "<iframe width=\"600\" height=\"450\" frameborder=\"0\" style=\"border:0\" src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3239.6531405426076!2d139.80894350150703!3d35.7101523358941!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188ed0d12f9adf%3A0x7d1d4fb31f43f72a!2sTokyo%20Skytree!5e0!3m2!1sen!2sus!4v1649915336912!5m2!1sen!2sus\" allowfullscreen=\"\"></iframe>"
        },
        {
"type": "header",
"content": { "en": "Support", "ja": "ビデオチュートリアルとユーザーマニュアル"}
},
        {
            "type": "paragraph",
            "content": { "en": "[Misell YouTube](https:\/\/www.youtube.com\/channel\/UChCFvSvr3YgbYDcumJ_OqzQ)", "ja": "[MISEル YouTube](https:\/\/www.youtube.com\/channel\/UChCFvSvr3YgbYDcumJ_OqzQ)"}
        },
        {
            "type": "paragraph",
            "content": { "en": "[Contact Form](https://misell-manual.wraptas.site/sections/contact-form)", "ja": "[お問い合わせフォーム](https://misell-manual.wraptas.site/sections/contact-form)"}
        }
    ],
    "presets": [
        {
            "name": { "en": "Contact form", "ja": "お問い合わせフォーム"},
            "category": "3) Static content"
        }
    ]
}
{% endschema %}