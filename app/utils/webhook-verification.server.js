/**
 * Shopify Webhookの検証ユーティリティ
 */
import crypto from 'crypto';

/**
 * Webhookリクエストの署名を検証する
 * @param {Request} request - Webhookリクエスト
 * @returns {Promise<boolean>} - 検証結果
 */
export async function verifyWebhook(request) {
  const hmacHeader = request.headers.get('X-Shopify-Hmac-Sha256');
  if (!hmacHeader) {
    return false;
  }
  
  const body = await request.clone().text();
  const calculatedHmac = crypto
    .createHmac('sha256', process.env.SHOPIFY_API_SECRET)
    .update(body, 'utf8')
    .digest('base64');
  
  return crypto.timingSafeEqual(
    Buffer.from(hmacHeader),
    Buffer.from(calculatedHmac)
  );
}
