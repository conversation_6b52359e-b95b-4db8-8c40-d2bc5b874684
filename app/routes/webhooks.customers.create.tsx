import { ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { PrismaClient } from "@prisma/client";
import { logger } from "../services/logger.service";

const prisma = new PrismaClient();

export const action = async ({ request }: ActionFunctionArgs) => {
  const { topic, shop, webhookId } = await authenticate.webhook(request);

  if (topic !== "CUSTOMERS_CREATE") {
    return new Response(`Webhook topic ${topic} not supported`, { status: 400 });
  }

  try {
    const payload = await request.json();
    logger.info(`Received customer create webhook for shop ${shop}`, {
      webhookId,
      customerId: payload.id,
      customerEmail: payload.email
    });

    // 顧客データをPrismaに保存
    await prisma.customer.upsert({
      where: {
        id: payload.id.toString()
      },
      update: {
        email: payload.email,
        firstName: payload.first_name,
        lastName: payload.last_name,
        name: `${payload.first_name} ${payload.last_name}`.trim(),
        phone: payload.phone,
        shop,
        shopifyId: payload.id.toString(),
        shopifyData: payload,
        updatedAt: new Date()
      },
      create: {
        id: payload.id.toString(),
        email: payload.email,
        firstName: payload.first_name,
        lastName: payload.last_name,
        name: `${payload.first_name} ${payload.last_name}`.trim(),
        phone: payload.phone,
        shop,
        shopifyId: payload.id.toString(),
        shopifyData: payload,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    logger.info(`Customer ${payload.id} synchronized to database`, {
      webhookId,
      customerId: payload.id
    });

    return new Response("Customer created in database", { status: 200 });
  } catch (error) {
    logger.error(`Error processing customer create webhook: ${error}`, {
      webhookId,
      error: error instanceof Error ? error.message : String(error)
    });
    return new Response("Error processing webhook", { status: 500 });
  }
};
