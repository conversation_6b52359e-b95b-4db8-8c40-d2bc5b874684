# レンタル商品メタフィールド構造

## 概要

このドキュメントでは、レンタル商品ECシステムで使用しているShopifyメタフィールドの構造と使用方法について説明します。メタフィールドは商品の追加情報を管理するために使用され、レンタル固有の情報を保存します。

**重要**: このドキュメントは、Shopifyとneonデータベースの連携を考慮した最適なメタフィールド構造を定義しています。AIが間違えないよう、また開発者が正確に実装できるよう、詳細に記述しています。

## 設計方針

以下の方針に基づいてメタフィールド構造を設計しています：

1. **シンプルさ優先**: 必要最小限のメタフィールドのみを定義
2. **人の管理が必要な項目**: 独立したシンプルなメタフィールドとして定義
3. **システム管理の項目**: JSONまたはNeonデータベースで管理
4. **重複の排除**: 同じ情報を複数の場所で管理しない
5. **責任の分離**:
   - Shopify: 商品の静的情報と基本的な状態
   - Neon: 動的な情報、履歴、予約情報

## メタフィールド構造

### 1. 人が直接管理する項目（独立したメタフィールド）

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| rental | status | 単一行のテキスト | 商品の状態 | 商品の物理的状態を管理（available、maintenance、damaged、unavailable） |
| rental | location | 単一行のテキスト | 商品の保管場所 | 在庫の物理的な保管場所を管理（NY、PRなど） |
| rental | maintenance_notes | 複数行のテキスト | メンテナンスに関する備考 | メンテナンス作業の詳細や注意点を記録 |
| rental | booking_notes | 複数行のテキスト | 予約に関する備考 | 手動調整が必要な予約情報を記録 |
| rental | booking_type | 単一行のテキスト | 予約タイプ | 予約タイプ（provisional/confirmed）を管理 |

### 2. システムで管理する項目（JSON形式）

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| rental | basic_info | JSON | レンタル商品の基本情報 | 商品コード、フリガナなどの基本データを管理 |
| rental | pricing | JSON | レンタル料金設定 | 基本料金、割引率などの料金計算用データを管理 |

### 3. 寸法情報（独立したメタフィールド）

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| product | width | 整数 | 商品の幅 | 寸法情報の管理（mm単位） |
| product | depth | 整数 | 商品の奥行き | 寸法情報の管理（mm単位） |
| product | height | 整数 | 商品の高さ | 寸法情報の管理（mm単位） |

### 4. 購入・廃棄情報（独立したメタフィールド）

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| rental | purchase_price | 小数 | 商品の購入価格 | 原価管理に使用 |
| rental | purchase_date | 日付 | 商品の購入日 | 資産管理に使用 |
| rental | purchase_place | 単一行のテキスト | 商品の購入場所 | 購入履歴の管理に使用 |
| rental | manufacturer | 単一行のテキスト | 商品のメーカー | メーカー情報の管理に使用 |
| rental | is_disposed | boolean | 廃棄済みフラグ | 廃棄済み商品の管理に使用 |
| rental | disposal_date | 日付 | 商品の廃棄日 | 廃棄履歴の管理に使用 |
| rental | disposal_reason | 複数行のテキスト | 商品を廃棄した理由 | 廃棄理由の記録に使用 |

### 5. 商品属性情報（独立したメタフィールド）

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| rental | material | 単一行のテキスト | 素材情報 | 商品の素材を管理 |
| rental | color | 単一行のテキスト | 色情報 | 商品の色を管理 |
| rental | designer | 単一行のテキスト | デザイナー情報 | 商品のデザイナーを管理 |

### 6. バリエーション管理（独立したメタフィールド）

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| rental | variation_group | 単一行のテキスト | バリエーショングループID | 関連商品のグループ化に使用 |
| rental | variation_type | 単一行のテキスト | バリエーションタイプ | バリエーションの種類（色、サイズなど）を管理 |

## メタフィールドの詳細

### 1. 人が直接管理する項目

#### rental.status

商品の状態を管理するメタフィールドです。

- **タイプ**: 単一行のテキスト
- **プリセット値**:
  ```
  available
  maintenance
  damaged
  unavailable
  ```
- **各値の意味**:
  - `available`: 利用可能な状態
  - `maintenance`: メンテナンス中の状態
  - `damaged`: 破損している状態
  - `unavailable`: 利用不可の状態

#### rental.location

商品の保管場所を管理するメタフィールドです。

- **タイプ**: 単一行のテキスト
- **例**: 「東京倉庫」、「大阪倉庫」など

#### rental.maintenance_notes

メンテナンスに関する備考を記録するメタフィールドです。

- **タイプ**: 複数行のテキスト
- **例**: 「2024/4/1: 脚部のネジを締め直した。次回は全体の清掃が必要。」

#### rental.booking_notes

予約に関する特別な備考や調整事項を記録するメタフィールドです。

- **タイプ**: 複数行のテキスト
- **例**: 「4/20午前返却予定。同日午後から次の予約可能。」

#### rental.booking_type

予約タイプを管理するメタフィールドです。

- **タイプ**: 単一行のテキスト
- **プリセット値**:
  ```
  provisional
  confirmed
  ```
- **各値の意味**:
  - `provisional`: 仮予約（デポジットのみ支払い、料金の10%）
  - `confirmed`: 本予約（料金の全額支払い）

#### rental.last_maintenance_date

最後にメンテナンスを行った日付を記録するメタフィールドです。

- **タイプ**: 日付
- **形式**: YYYY-MM-DD

### 2. システムで管理する項目

#### rental.basic_info (JSON)

レンタル商品の基本情報を保存するJSONフィールドです。

```json
{
  "productCode": "CHAIR-001",
  "detailCode": "A-BLK",
  "kana": "デザイナーズチェア"
}
```

**注意**: `location`と`status`は独立したメタフィールドとして管理するため、basic_infoには含まれません。

#### rental.pricing (JSON)

レンタル料金の設定を保存するJSONフィールドです。

```json
{
  "basePrice": 5000,
  "depositRate": 0.1,
  "discountRules": {
    "day2_6_rate": 0.2,
    "day7_plus_rate": 0.1
  },
  "minimumDays": 1,
  "maximumDays": 30
}
```

### 3. 寸法情報

#### product.width, product.depth, product.height

商品の寸法情報を管理するメタフィールドです。

- **タイプ**: 整数
- **単位**: mm（ミリメートル）
- **バリデーション**: 0〜10000の範囲内

### 4. 商品属性情報

#### rental.material, rental.color, rental.designer

商品の属性情報を管理するメタフィールドです。

- **タイプ**: 単一行のテキスト
- **例**:
  - material: 「木材、スチール」
  - color: 「ブラック」
  - designer: 「ジョン・スミス」

### 5. バリエーション管理

#### rental.variation_group, rental.variation_type

関連する商品をグループ化するためのメタフィールドです。

- **タイプ**: 単一行のテキスト
- **例**:
  - variation_group: 「CHAIR-A」（同じデザインの椅子のグループ）
  - variation_type: 「色」（バリエーションの種類）

## バリエーション管理

商品のバリエーションは、Shopifyの標準的なバリエーション機能ではなく、個別のSKUを持つ独立した商品として管理しています。これにより、各バリエーションを独立した在庫として扱うことができます。

例えば、同じデザインの椅子でも色違いの場合：
- デザイナーズチェア（ブラック）: SKU: CHAIR-A-BLK
- デザイナーズチェア（ホワイト）: SKU: CHAIR-A-WHT

これらは別々の商品として登録し、`variation_group`メタフィールドで関連付けます。

## Shopifyとneonデータベースの連携

### 責任分担

**Shopify側**
- 商品の静的情報（基本情報、仕様、画像など）
- 商品の物理的状態（良好、修理中など）

**Neon側**
- 動的な情報（予約状況、在庫カレンダー）
- 時系列データ（メンテナンス履歴、予約履歴）
- 複雑な関係性を持つデータ

### データ同期メカニズム

1. **Shopify → Neon の同期**
   - 商品の物理的状態が変更された場合（例：`rental.status`が`maintenance`に変更）
   - Webhookを使用して変更を検知
   - Neonデータベースに適切なレコードを作成（例：新しいメンテナンスレコード）

2. **Neon → Shopify の同期**
   - メンテナンスが完了した場合
   - Shopifyの商品メタフィールドを更新

## メタフィールドの使用方法

### 商品登録時

新しい商品を登録する際は、以下の手順でメタフィールドを設定します：

1. Shopify管理画面で商品を作成
2. 基本情報（名前、説明、価格、画像など）を入力
3. 「メタフィールド」セクションで必要なメタフィールドを設定
   - 基本情報（JSON）
   - 料金設定（JSON）
   - 寸法情報
   - 保管場所
   - 商品状態（デフォルトは"available"）

### 商品状態の更新

商品状態を更新する際は、以下のメタフィールドを更新します：

1. `rental.status`: 新しい商品状態
2. `rental.maintenance_notes`: メンテナンスに関する備考（メンテナンス中の場合）
3. `rental.booking_notes`: 予約に関する備考（手動調整が必要な場合）
4. `rental.last_maintenance_date`: メンテナンス完了時に更新

### プログラムからのアクセス

アプリケーションからメタフィールドにアクセスする方法：

```typescript
// Shopify Admin APIを使用してメタフィールドを取得
const getProductMetafields = async (productId: string) => {
  const client = new Shopify.Clients.Rest(shop, accessToken);

  const response = await client.get({
    path: `products/${productId}/metafields`,
  });

  return response.body.metafields;
};

// メタフィールドを更新
const updateProductMetafield = async (
  productId: string,
  namespace: string,
  key: string,
  value: string,
  type: string
) => {
  const client = new Shopify.Clients.Rest(shop, accessToken);

  await client.post({
    path: `products/${productId}/metafields`,
    data: {
      metafield: {
        namespace,
        key,
        value,
        type
      },
    },
  });
};

// 商品状態を更新する例
const updateProductStatus = async (
  productId: string,
  status: string,
  notes?: string,
  isBookingNote: boolean = false
) => {
  // 商品状態を更新
  await updateProductMetafield(
    productId,
    'rental',
    'status',
    status,
    'single_line_text_field'
  );

  // 備考があれば更新
  if (notes) {
    const noteField = isBookingNote ? 'booking_notes' : 'maintenance_notes';
    await updateProductMetafield(
      productId,
      'rental',
      noteField,
      notes,
      'multi_line_text_field'
    );
  }

  // 状態が"available"に変更された場合は最終メンテナンス日を更新
  if (status === 'available' && !isBookingNote) {
    const today = new Date().toISOString().split('T')[0];
    await updateProductMetafield(
      productId,
      'rental',
      'last_maintenance_date',
      today,
      'date'
    );
  }

  // Neonデータベースも更新（別途実装）
  await updateNeonMaintenanceStatus(productId, status, notes);
};
```

## 注文メタフィールド

注文に関連するメタフィールドは以下の通りです：

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| rental | start_date | 日付 | レンタル開始日 | レンタル期間の開始日を管理 |
| rental | end_date | 日付 | レンタル終了日 | レンタル期間の終了日を管理 |
| rental | reservation_type | 単一行のテキスト | 予約タイプ | 予約タイプ（provisional/confirmed）を管理 |

### 注文メタフィールドの詳細

#### rental.start_date

レンタル開始日を管理するメタフィールドです。

- **タイプ**: 日付
- **形式**: YYYY-MM-DD

#### rental.end_date

レンタル終了日を管理するメタフィールドです。

- **タイプ**: 日付
- **形式**: YYYY-MM-DD

#### rental.reservation_type

注文の予約タイプを管理するメタフィールドです。

- **タイプ**: 単一行のテキスト
- **プリセット値**:
  ```
  provisional
  confirmed
  ```
- **各値の意味**:
  - `provisional`: 仮予約（デポジットのみ支払い、料金の10%）
  - `confirmed`: 本予約（料金の全額支払い）

## 実装上の注意点

1. **メタフィールドの一貫性**:
   - 同じ情報を複数の場所で管理しないこと
   - 特に`rental.basic_info`と独立したメタフィールドの間で重複がないようにすること

2. **API呼び出しの最適化**:
   - メタフィールドの更新は必要な場合のみ行うこと
   - バッチ処理を活用して複数のメタフィールドを一度に更新すること

3. **エラーハンドリング**:
   - メタフィールドの取得・更新時のエラーを適切に処理すること
   - 特にAPI制限エラー（429）に対応すること

4. **データ同期**:
   - ShopifyとNeonデータベース間の同期を確実に行うこと
   - 同期エラーが発生した場合のリカバリー処理を実装すること

## まとめ

このドキュメントでは、レンタル商品ECシステムで使用しているShopifyメタフィールドの構造と使用方法について説明しました。シンプルさを優先し、人の管理が必要な項目は独立したメタフィールドとして定義し、システム管理の項目はJSONまたはNeonデータベースで管理する方針を採用しています。

特に重要なのは、ShopifyとNeonデータベースの責任分担を明確にし、適切なデータ同期メカニズムを実装することです。これにより、レンタル商品の管理が効率化され、ユーザー体験の向上につながります。
