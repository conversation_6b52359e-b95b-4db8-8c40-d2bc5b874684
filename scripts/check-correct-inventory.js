import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品検索のクエリ
const GET_PRODUCT_BY_TITLE = gql`
  query getProductByTitle($query: String!) {
    products(first: 1, query: $query) {
      edges {
        node {
          id
          title
          metafields(first: 20) {
            edges {
              node {
                namespace
                key
                value
                type
              }
            }
          }
          variants(first: 50) {
            edges {
              node {
                id
                sku
                title
                price
                inventoryQuantity
              }
            }
          }
        }
      }
    }
  }
`;

// タイトルで商品を検索（完全一致）
async function getProductByTitle(title) {
  try {
    const result = await graphQLClient.request(GET_PRODUCT_BY_TITLE, {
      query: `title:"${title}"`,
    });

    if (result.products.edges.length === 0) {
      console.error(`タイトル "${title}" の商品が見つかりませんでした`);
      return null;
    }

    // 完全一致を確認
    const matchedProduct = result.products.edges.find(edge => edge.node.title === title);
    if (!matchedProduct) {
      console.error(`タイトル "${title}" の商品が完全一致しませんでした`);
      return null;
    }

    return matchedProduct.node;
  } catch (error) {
    console.error('商品検索エラー:', error);
    return null;
  }
}

// 日付をフォーマット
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
}

// 予約状況を確認
async function checkInventoryItems(productTitle) {
  console.log(`\n=== ${productTitle} の在庫アイテム情報を確認 ===`);
  
  // 商品を検索
  const product = await getProductByTitle(productTitle);
  if (!product) {
    console.error(`${productTitle} の商品が見つかりませんでした。`);
    return;
  }
  
  console.log(`商品を見つけました: ${product.title} (${product.id})`);
  
  // メタフィールドから情報を取得
  const metafields = product.metafields.edges.map(edge => edge.node);
  
  // 基本情報を取得
  const basicInfoMetafield = metafields.find(
    meta => meta.namespace === 'rental' && meta.key === 'basic_info'
  );
  
  if (basicInfoMetafield) {
    const basicInfo = JSON.parse(basicInfoMetafield.value);
    console.log('\n基本情報:');
    console.log(`商品コード: ${basicInfo.productCode}-${basicInfo.detailCode}`);
    console.log(`商品名カナ: ${basicInfo.kana}`);
    console.log(`サイズ: W${basicInfo.dimensions.width} × D${basicInfo.dimensions.depth} × H${basicInfo.dimensions.height} cm`);
    console.log(`座面サイズ: W${basicInfo.seatDimensions.width} × D${basicInfo.seatDimensions.depth} × H${basicInfo.seatDimensions.height} cm`);
    console.log(`素材: ${basicInfo.material}`);
    console.log(`色: ${basicInfo.color}`);
    console.log(`メーカー: ${basicInfo.maker}`);
    console.log(`キャンペーン: ${basicInfo.campaign}`);
    console.log(`備考: ${basicInfo.notes}`);
  } else {
    console.log('基本情報が見つかりませんでした。');
  }
  
  // 在庫アイテム情報を取得
  const inventoryItemsMetafield = metafields.find(
    meta => meta.namespace === 'rental' && meta.key === 'inventory_items'
  );
  
  if (!inventoryItemsMetafield) {
    console.log('在庫アイテム情報が見つかりませんでした。');
    return;
  }
  
  // 在庫アイテム情報をパース
  const inventoryItems = JSON.parse(inventoryItemsMetafield.value);
  
  // 予約情報を取得
  const reservationMetafield = metafields.find(
    meta => meta.namespace === 'rental' && meta.key === 'reservation_info'
  );
  
  if (!reservationMetafield) {
    console.log('予約情報が見つかりませんでした。');
    return;
  }
  
  // 予約情報をパース
  const reservations = JSON.parse(reservationMetafield.value);
  
  // 各在庫アイテムの情報を表示
  console.log('\n在庫アイテム一覧:');
  for (const item of inventoryItems) {
    console.log(`\n- アイテムID: ${item.id}`);
    console.log(`  SKU: ${item.sku}`);
    console.log(`  ステータス: ${item.status}`);
    console.log(`  在庫場所: ${item.location}`);
    console.log(`  備考: ${item.notes}`);
    
    // 対応する予約情報を検索
    const itemReservation = reservations.find(r => r.itemId === item.id);
    if (itemReservation && itemReservation.reservations.length > 0) {
      console.log('\n  予約一覧:');
      for (const reservation of itemReservation.reservations) {
        console.log(`  - 予約ID: ${reservation.id}`);
        console.log(`    期間: ${formatDate(reservation.startDate)} 〜 ${formatDate(reservation.endDate)}`);
        console.log(`    状態: ${reservation.status === 'confirmed' ? '確定予約' : '仮予約'}`);
        console.log(`    顧客: ${reservation.customerName} (${reservation.customerEmail})`);
        console.log(`    注文ID: ${reservation.orderId}`);
        console.log(`    備考: ${reservation.notes || 'なし'}`);
      }
    } else {
      console.log('\n  予約はありません。');
    }
  }
  
  // バリアント情報を表示
  console.log('\nバリアント一覧:');
  const variants = product.variants.edges.map(edge => edge.node);
  for (const variant of variants) {
    console.log(`- ${variant.title} (${variant.sku}): ${variant.price}円 (在庫: ${variant.inventoryQuantity || 0})`);
  }
}

// 特定の期間の予約状況を確認
async function checkPeriodAvailability(productTitle, startDate, endDate) {
  console.log(`\n=== ${productTitle} の ${startDate} 〜 ${endDate} の予約状況を確認 ===`);
  
  // 商品を検索
  const product = await getProductByTitle(productTitle);
  if (!product) {
    console.error(`${productTitle} の商品が見つかりませんでした。`);
    return;
  }
  
  // メタフィールドから情報を取得
  const metafields = product.metafields.edges.map(edge => edge.node);
  
  // 在庫アイテム情報を取得
  const inventoryItemsMetafield = metafields.find(
    meta => meta.namespace === 'rental' && meta.key === 'inventory_items'
  );
  
  if (!inventoryItemsMetafield) {
    console.log('在庫アイテム情報が見つかりませんでした。');
    return;
  }
  
  // 在庫アイテム情報をパース
  const inventoryItems = JSON.parse(inventoryItemsMetafield.value);
  
  // 予約情報を取得
  const reservationMetafield = metafields.find(
    meta => meta.namespace === 'rental' && meta.key === 'reservation_info'
  );
  
  if (!reservationMetafield) {
    console.log('予約情報が見つかりませんでした。');
    return;
  }
  
  // 予約情報をパース
  const reservations = JSON.parse(reservationMetafield.value);
  
  // 期間内の各日付をチェック
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  // 各在庫アイテムごとに予約状況を確認
  for (const item of inventoryItems) {
    console.log(`\n在庫アイテム: ${item.id} (${item.sku})`);
    console.log(`ステータス: ${item.status}`);
    
    // ステータスが利用可能でない場合はスキップ
    if (item.status !== 'available') {
      console.log(`この在庫アイテムは現在 ${item.status} 状態のため予約できません。`);
      continue;
    }
    
    // 対応する予約情報を検索
    const itemReservation = reservations.find(r => r.itemId === item.id);
    if (!itemReservation) {
      console.log('予約情報が見つかりませんでした。');
      continue;
    }
    
    let isAvailable = true;
    const conflictingReservations = [];
    
    // 期間内の各日付をチェック
    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      const dateStr = date.toISOString().split('T')[0];
      
      // 予約済みかチェック
      for (const reservation of itemReservation.reservations) {
        const reservationStart = new Date(reservation.startDate);
        const reservationEnd = new Date(reservation.endDate);
        
        if (date >= reservationStart && date <= reservationEnd) {
          isAvailable = false;
          if (!conflictingReservations.includes(reservation)) {
            conflictingReservations.push(reservation);
          }
        }
      }
    }
    
    if (isAvailable) {
      console.log(`${startDate} 〜 ${endDate} の期間は予約可能です。`);
    } else {
      console.log(`${startDate} 〜 ${endDate} の期間は予約できません。`);
      console.log('競合する予約:');
      
      for (const reservation of conflictingReservations) {
        console.log(`- 予約ID: ${reservation.id}`);
        console.log(`  期間: ${formatDate(reservation.startDate)} 〜 ${formatDate(reservation.endDate)}`);
        console.log(`  状態: ${reservation.status === 'confirmed' ? '確定予約' : '仮予約'}`);
        console.log(`  顧客: ${reservation.customerName}`);
      }
    }
  }
}

// メイン処理
async function main() {
  const testProducts = [
    "【正しい実装】ベーシックソファ オフホワイト 1シーター",
    "【正しい実装】カリモクソファ モケットグリーン 1シーター"
  ];
  
  // 各商品の在庫アイテム情報を確認
  for (const productTitle of testProducts) {
    await checkInventoryItems(productTitle);
  }
  
  // 特定の期間の予約状況を確認
  const startDate = new Date();
  startDate.setDate(startDate.getDate() + 5);
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + 10);
  
  for (const productTitle of testProducts) {
    await checkPeriodAvailability(
      productTitle,
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0]
    );
  }
}

// 実行
main();
