import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit, useActionData, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  LegacyCard,
  Button,
  Text,
  BlockStack,
  Banner,
  FormLayout,
  TextField,
  Select,
  DataTable,
  Spinner,
  InlineStack,
  Badge
} from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import { DbSyncService } from "../services/sync/db-sync.service";
import { PrismaClient } from "@prisma/client";
import { useState } from "react";

const prisma = new PrismaClient();

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  
  // 同期ログを取得
  const syncLogs = await prisma.syncLog.findMany({
    orderBy: { createdAt: 'desc' },
    take: 20
  });
  
  // 商品数を取得
  const productsCount = await prisma.product.count();
  
  // 予約数を取得
  const bookingsCount = await prisma.booking.count();
  
  // 注文数を取得
  const ordersCount = await prisma.order.count();
  
  return json({ 
    syncLogs,
    stats: {
      productsCount,
      bookingsCount,
      ordersCount
    }
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const { session } = await authenticate.admin(request);
  
  const formData = await request.formData();
  const action = formData.get("action") as string;
  const productId = formData.get("productId") as string;
  const shopifyProductId = formData.get("shopifyProductId") as string;
  const orderId = formData.get("orderId") as string;
  const shopifyOrderId = formData.get("shopifyOrderId") as string;
  
  const dbSyncService = new DbSyncService(request);
  
  try {
    if (action === "sync_product") {
      // 特定の商品を同期
      const result = await dbSyncService.syncProductFromShopify(shopifyProductId);
      return json({ success: true, result });
    } else if (action === "sync_all_products") {
      // すべての商品を同期
      const result = await dbSyncService.syncAllProducts();
      return json({ success: true, result });
    } else if (action === "sync_bookings") {
      // 特定の商品の予約を同期
      const result = await dbSyncService.syncBookings(productId);
      return json({ success: true, result });
    } else if (action === "sync_order") {
      // 特定の注文を同期
      const result = await dbSyncService.syncOrderFromShopify(shopifyOrderId);
      return json({ success: true, result });
    } else if (action === "schedule_sync") {
      // 同期スケジュールを実行
      const result = await dbSyncService.scheduleSync();
      return json({ success: true, result });
    }
    
    return json({ success: false, error: "無効なアクション" });
  } catch (error) {
    return json({ success: false, error: String(error) });
  }
}

export default function SyncPage() {
  const { syncLogs, stats } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [syncType, setSyncType] = useState('product');
  
  // 特定の商品を同期
  const handleSyncProduct = (shopifyProductId: string) => {
    setIsSubmitting(true);
    const formData = new FormData();
    formData.append("action", "sync_product");
    formData.append("shopifyProductId", shopifyProductId);
    submit(formData, { method: "post" });
  };
  
  // すべての商品を同期
  const handleSyncAllProducts = () => {
    setIsSubmitting(true);
    const formData = new FormData();
    formData.append("action", "sync_all_products");
    submit(formData, { method: "post" });
  };
  
  // 特定の商品の予約を同期
  const handleSyncBookings = (productId: string) => {
    setIsSubmitting(true);
    const formData = new FormData();
    formData.append("action", "sync_bookings");
    formData.append("productId", productId);
    submit(formData, { method: "post" });
  };
  
  // 特定の注文を同期
  const handleSyncOrder = (shopifyOrderId: string) => {
    setIsSubmitting(true);
    const formData = new FormData();
    formData.append("action", "sync_order");
    formData.append("shopifyOrderId", shopifyOrderId);
    submit(formData, { method: "post" });
  };
  
  // 同期スケジュールを実行
  const handleScheduleSync = () => {
    setIsSubmitting(true);
    const formData = new FormData();
    formData.append("action", "schedule_sync");
    submit(formData, { method: "post" });
  };
  
  // 送信完了時の処理
  if (actionData && isSubmitting) {
    setIsSubmitting(false);
  }
  
  // 同期ログのテーブル行を生成
  const syncLogRows = syncLogs.map(log => [
    new Date(log.createdAt).toLocaleString(),
    log.entityType,
    log.operation,
    <Badge tone={log.status === 'success' ? 'success' : 'critical'}>
      {log.status}
    </Badge>,
    log.errorMessage || "成功"
  ]);
  
  return (
    <Page
      title="データ同期"
      subtitle="ShopifyとPrismaデータベース間のデータ同期"
      backAction={{ content: "ホーム", url: "/app" }}
    >
      <Layout>
        <Layout.Section>
          <LegacyCard title="データ統計">
            <LegacyCard.Section>
              <BlockStack gap="400">
                <Text>商品数: {stats.productsCount}</Text>
                <Text>予約数: {stats.bookingsCount}</Text>
                <Text>注文数: {stats.ordersCount}</Text>
              </BlockStack>
            </LegacyCard.Section>
          </LegacyCard>
        </Layout.Section>
        
        <Layout.Section>
          <LegacyCard title="同期操作">
            <LegacyCard.Section>
              <BlockStack gap="400">
                {actionData?.success === false && (
                  <Banner status="critical">
                    <p>{actionData.error}</p>
                  </Banner>
                )}
                
                {actionData?.success === true && (
                  <Banner status="success">
                    <p>同期が完了しました</p>
                    {actionData.result?.message && (
                      <p>{actionData.result.message}</p>
                    )}
                  </Banner>
                )}
                
                <Form method="post">
                  <FormLayout>
                    <Select
                      label="同期タイプ"
                      options={[
                        {label: '商品同期', value: 'product'},
                        {label: '予約同期', value: 'booking'},
                        {label: '注文同期', value: 'order'}
                      ]}
                      onChange={setSyncType}
                      value={syncType}
                    />
                    
                    {syncType === 'product' && (
                      <>
                        <TextField
                          label="Shopify商品ID"
                          placeholder="数字のみ（例: 1234567890）"
                          id="shopifyProductId"
                          name="shopifyProductId"
                          autoComplete="off"
                        />
                        
                        <InlineStack gap="200">
                          <Button
                            onClick={() => handleSyncProduct(
                              (document.getElementById("shopifyProductId") as HTMLInputElement).value
                            )}
                            loading={isSubmitting}
                          >
                            商品を同期
                          </Button>
                          
                          <Button
                            onClick={handleSyncAllProducts}
                            loading={isSubmitting}
                          >
                            すべての商品を同期
                          </Button>
                        </InlineStack>
                      </>
                    )}
                    
                    {syncType === 'booking' && (
                      <>
                        <TextField
                          label="商品ID"
                          placeholder="Prisma商品ID"
                          id="productId"
                          name="productId"
                          autoComplete="off"
                        />
                        
                        <Button
                          onClick={() => handleSyncBookings(
                            (document.getElementById("productId") as HTMLInputElement).value
                          )}
                          loading={isSubmitting}
                        >
                          予約を同期
                        </Button>
                      </>
                    )}
                    
                    {syncType === 'order' && (
                      <>
                        <TextField
                          label="Shopify注文ID"
                          placeholder="数字のみ（例: 1234567890）"
                          id="shopifyOrderId"
                          name="shopifyOrderId"
                          autoComplete="off"
                        />
                        
                        <Button
                          onClick={() => handleSyncOrder(
                            (document.getElementById("shopifyOrderId") as HTMLInputElement).value
                          )}
                          loading={isSubmitting}
                        >
                          注文を同期
                        </Button>
                      </>
                    )}
                    
                    <Button
                      onClick={handleScheduleSync}
                      loading={isSubmitting}
                    >
                      同期スケジュールを実行
                    </Button>
                  </FormLayout>
                </Form>
              </BlockStack>
            </LegacyCard.Section>
          </LegacyCard>
        </Layout.Section>
        
        <Layout.Section>
          <LegacyCard title="同期ログ">
            <LegacyCard.Section>
              <BlockStack gap="400">
                {syncLogs.length === 0 ? (
                  <Text>同期ログはありません</Text>
                ) : (
                  <DataTable
                    columnContentTypes={['text', 'text', 'text', 'text', 'text']}
                    headings={['日時', 'タイプ', '操作', 'ステータス', 'メッセージ']}
                    rows={syncLogRows}
                  />
                )}
              </BlockStack>
            </LegacyCard.Section>
          </LegacyCard>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
