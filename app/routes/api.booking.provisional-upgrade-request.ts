/**
 * 仮予約から本予約への変更リクエストAPI
 * 
 * 仮予約から本予約への変更リクエストを処理するAPIエンドポイント
 * POST /api/booking/provisional-upgrade-request
 */

import { json, type ActionFunctionArgs } from "@remix-run/node";
import { prisma } from "~/db.server";
import { logger } from "~/utils/logger";
import { sendEmail } from "~/utils/email";
import { formatErrorMessage } from "~/utils/error-utils";
import { format } from "date-fns";
import { ja } from "date-fns/locale";

/**
 * 仮予約から本予約への変更リクエストを処理するアクション
 */
export async function action({ request }: ActionFunctionArgs) {
  try {
    // リクエストボディを取得
    const body = await request.json();
    const { 
      bookingId, 
      requesterId, 
      requesterName, 
      requesterEmail, 
      requesterPhone, 
      requesterNotes 
    } = body;

    // 必須パラメータのチェック
    if (!bookingId) {
      return json({ success: false, error: "予約IDは必須です" }, { status: 400 });
    }

    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId }
    });

    if (!booking) {
      return json({ success: false, error: "予約が見つかりません" }, { status: 404 });
    }

    // 仮予約かどうかをチェック
    if (booking.bookingType !== "PROVISIONAL") {
      return json({ 
        success: false, 
        error: "指定された予約は仮予約ではありません",
        booking
      }, { status: 400 });
    }

    // 予約ステータスをチェック
    if (booking.status !== "PROVISIONAL") {
      return json({ 
        success: false, 
        error: `予約のステータスが無効です: ${booking.status}`,
        booking
      }, { status: 400 });
    }

    // 商品情報を取得
    const product = await prisma.product.findUnique({
      where: { id: booking.productId }
    });

    if (!product) {
      return json({ success: false, error: "商品が見つかりません" }, { status: 404 });
    }

    // 変更リクエスト情報を保存
    const upgradeRequest = await prisma.bookingUpgradeRequest.create({
      data: {
        bookingId: booking.id,
        requesterId: requesterId || null,
        requesterName: requesterName || "匿名",
        requesterEmail: requesterEmail || null,
        requesterPhone: requesterPhone || null,
        requesterNotes: requesterNotes || null,
        status: "PENDING",
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    // 予約情報を更新（メタデータに変更リクエスト情報を追加）
    const currentMetadata = booking.metadata || {};
    await prisma.booking.update({
      where: { id: booking.id },
      data: {
        metadata: {
          ...currentMetadata,
          upgradeRequests: [
            ...(currentMetadata.upgradeRequests || []),
            {
              id: upgradeRequest.id,
              requesterId,
              requesterName,
              requesterEmail,
              requesterPhone,
              requesterNotes,
              status: "PENDING",
              createdAt: new Date().toISOString()
            }
          ]
        }
      }
    });

    // 仮予約している顧客にメール通知
    try {
      if (booking.customerEmail) {
        const emailResult = await sendEmail({
          to: booking.customerEmail,
          subject: `【重要】仮予約中の商品に本予約リクエストがありました（予約番号: ${booking.bookingId}）`,
          text: `
${booking.customerName || "お客様"}

いつもご利用ありがとうございます。

あなたが仮予約中の商品「${product.title}」（予約番号: ${booking.bookingId}）に、
別のお客様から本予約のリクエストが入りました。

■ 予約情報
予約番号: ${booking.bookingId}
商品名: ${product.title}
予約期間: ${format(new Date(booking.startDate), 'yyyy年MM月dd日', { locale: ja })} 〜 ${format(new Date(booking.endDate), 'yyyy年MM月dd日', { locale: ja })}

仮予約は${format(new Date(booking.expiresAt || new Date(booking.startDate)), 'yyyy年MM月dd日', { locale: ja })}まで有効ですが、
本予約への変更をご希望の場合は、お早めにご連絡ください。

ご不明な点がございましたら、お気軽にお問い合わせください。

よろしくお願いいたします。
          `,
          html: `
<p>${booking.customerName || "お客様"}</p>

<p>いつもご利用ありがとうございます。</p>

<p>あなたが仮予約中の商品「${product.title}」（予約番号: ${booking.bookingId}）に、<br>
別のお客様から本予約のリクエストが入りました。</p>

<h3>予約情報</h3>
<ul>
  <li>予約番号: ${booking.bookingId}</li>
  <li>商品名: ${product.title}</li>
  <li>予約期間: ${format(new Date(booking.startDate), 'yyyy年MM月dd日', { locale: ja })} 〜 ${format(new Date(booking.endDate), 'yyyy年MM月dd日', { locale: ja })}</li>
</ul>

<p>仮予約は${format(new Date(booking.expiresAt || new Date(booking.startDate)), 'yyyy年MM月dd日', { locale: ja })}まで有効ですが、<br>
本予約への変更をご希望の場合は、お早めにご連絡ください。</p>

<p>ご不明な点がございましたら、お気軽にお問い合わせください。</p>

<p>よろしくお願いいたします。</p>
          `
        });

        logger.info(`仮予約顧客への通知メール送信: ${booking.customerEmail}`, emailResult);
      }
    } catch (emailError) {
      logger.error("仮予約顧客への通知メール送信エラー:", emailError);
      // メール送信エラーは処理を続行
    }

    // 管理者にメール通知
    try {
      const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";
      const emailResult = await sendEmail({
        to: adminEmail,
        subject: `【通知】仮予約から本予約への変更リクエストがありました（予約番号: ${booking.bookingId}）`,
        text: `
仮予約から本予約への変更リクエストがありました。

■ 予約情報
予約番号: ${booking.bookingId}
商品名: ${product.title}
予約期間: ${format(new Date(booking.startDate), 'yyyy年MM月dd日', { locale: ja })} 〜 ${format(new Date(booking.endDate), 'yyyy年MM月dd日', { locale: ja })}

■ 仮予約顧客情報
名前: ${booking.customerName || "未設定"}
メール: ${booking.customerEmail || "未設定"}
電話: ${booking.customerPhone || "未設定"}

■ リクエスト顧客情報
名前: ${requesterName || "未設定"}
メール: ${requesterEmail || "未設定"}
電話: ${requesterPhone || "未設定"}
備考: ${requesterNotes || "なし"}

管理画面から対応をお願いします。
        `,
        html: `
<h2>仮予約から本予約への変更リクエストがありました。</h2>

<h3>予約情報</h3>
<ul>
  <li>予約番号: ${booking.bookingId}</li>
  <li>商品名: ${product.title}</li>
  <li>予約期間: ${format(new Date(booking.startDate), 'yyyy年MM月dd日', { locale: ja })} 〜 ${format(new Date(booking.endDate), 'yyyy年MM月dd日', { locale: ja })}</li>
</ul>

<h3>仮予約顧客情報</h3>
<ul>
  <li>名前: ${booking.customerName || "未設定"}</li>
  <li>メール: ${booking.customerEmail || "未設定"}</li>
  <li>電話: ${booking.customerPhone || "未設定"}</li>
</ul>

<h3>リクエスト顧客情報</h3>
<ul>
  <li>名前: ${requesterName || "未設定"}</li>
  <li>メール: ${requesterEmail || "未設定"}</li>
  <li>電話: ${requesterPhone || "未設定"}</li>
  <li>備考: ${requesterNotes || "なし"}</li>
</ul>

<p>管理画面から対応をお願いします。</p>
        `
      });

      logger.info(`管理者への通知メール送信: ${adminEmail}`, emailResult);
    } catch (emailError) {
      logger.error("管理者への通知メール送信エラー:", emailError);
      // メール送信エラーは処理を続行
    }

    return json({ 
      success: true, 
      message: "仮予約から本予約への変更リクエストを受け付けました",
      upgradeRequest
    });
  } catch (error) {
    logger.error("仮予約から本予約への変更リクエスト処理エラー:", error);
    return json({ 
      success: false, 
      error: formatErrorMessage(error) 
    }, { status: 500 });
  }
}
