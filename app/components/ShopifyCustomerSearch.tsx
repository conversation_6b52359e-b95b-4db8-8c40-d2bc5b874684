import { useState, useCallback } from "react";
import {
  Resource<PERSON>ist,
  TextField,
  But<PERSON>,
  Spinner,
  Text,
  Banner,
  EmptyState,
  Card,
  Link
} from "@shopify/polaris";

interface ShopifyCustomer {
  id: string;
  displayName: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
}

interface ShopifyCustomerSearchProps {
  onSelect: (customer: ShopifyCustomer) => void;
  shopifyShop?: string;
}

export function ShopifyCustomerSearch({ onSelect, shopifyShop }: ShopifyCustomerSearchProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [customers, setCustomers] = useState<ShopifyCustomer[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [searched, setSearched] = useState(false);

  const handleSearch = useCallback(async () => {
    if (!searchTerm) return;

    setLoading(true);
    setError("");

    try {
      const response = await fetch(`/api/customers/search?query=${encodeURIComponent(searchTerm)}`);
      const data = await response.json();

      if (data.error) {
        setError(data.error);
        return;
      }

      console.log('顧客検索結果:', data.customers);
      setCustomers(data.customers || []);
      setSearched(true);
    } catch (error) {
      console.error("顧客検索エラー:", error);
      setError("顧客の検索中にエラーが発生しました");
    } finally {
      setLoading(false);
    }
  }, [searchTerm]);

  // Shopify管理画面の顧客作成URLを生成
  const getShopifyCustomerUrl = () => {
    if (!shopifyShop) return "https://admin.shopify.com/store";

    // shopifyShopからストア名を抽出（例: example.myshopify.com → example）
    const storeName = shopifyShop.split('.')[0];
    return `https://${shopifyShop}/admin/customers/new`;
  };

  return (
    <div>
      <Text variant="bodyMd" as="p" color="subdued">
        顧客情報はShopifyから選択してください。新規顧客の場合は「Shopifyで新規顧客登録」ボタンをクリックしてください。
      </Text>

      <form
        onSubmit={(e) => {
          e.preventDefault();
          handleSearch();
        }}
        style={{ display: "flex", marginBottom: "16px", marginTop: "16px" }}
      >
        <div style={{ flex: 1 }}>
          <TextField
            label="顧客検索"
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder="名前、メール、電話番号で検索"
            autoComplete="off"
          />
        </div>
        <div style={{ marginLeft: "8px", marginTop: "24px" }}>
          <Button onClick={handleSearch} disabled={loading} type="submit">
            {loading ? <Spinner size="small" /> : "検索"}
          </Button>
        </div>
      </form>

      <div style={{ marginBottom: "16px" }}>
        <Button
          url={getShopifyCustomerUrl()}
          external="true"
          target="_blank"
        >
          Shopifyで新規顧客登録
        </Button>
      </div>

      {error && (
        <Banner tone="critical">
          <p>{error}</p>
        </Banner>
      )}

      {customers.length > 0 ? (
        <Card>
          <ResourceList
            items={customers}
            renderItem={(customer) => (
              <ResourceList.Item
                id={customer.id}
                onClick={() => onSelect(customer)}
              >
                <div>
                  <Text variant="bodyMd" fontWeight="bold">{customer.displayName}</Text>
                  {customer.email && <div>{customer.email}</div>}
                  {customer.phone && <div>{customer.phone}</div>}
                </div>
              </ResourceList.Item>
            )}
          />
        </Card>
      ) : searched && !loading ? (
        <EmptyState
          heading="顧客が見つかりません"
          image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
        >
          <p>検索条件を変更して再度お試しください。または、新規顧客を登録してください。</p>
        </EmptyState>
      ) : null}
    </div>
  );
}
