<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>テスト結果レポート</title>
  <style>
    body {
      font-family: 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #2c3e50;
    }
    .summary {
      background-color: #f8f9fa;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .summary-item {
      margin: 5px 15px;
    }
    .test-list {
      margin-top: 20px;
    }
    .test-item {
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 15px;
      position: relative;
    }
    .test-name {
      font-weight: bold;
      margin-bottom: 10px;
      padding-right: 80px;
    }
    .test-status {
      position: absolute;
      top: 15px;
      right: 15px;
      padding: 3px 10px;
      border-radius: 3px;
      font-size: 14px;
      font-weight: bold;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    .failure {
      background-color: #f8d7da;
      color: #721c24;
    }
    .test-details {
      background-color: #f8f9fa;
      border-radius: 3px;
      padding: 10px;
      margin-top: 10px;
      font-family: monospace;
      white-space: pre-wrap;
      overflow-x: auto;
    }
    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 14px;
      color: #6c757d;
    }
    .validation-section {
      margin-top: 30px;
      border-top: 1px solid #ddd;
      padding-top: 20px;
    }
    .validation-item {
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 15px;
    }
    .validation-message {
      font-weight: bold;
      margin-bottom: 10px;
    }
    .validation-details {
      background-color: #f8f9fa;
      border-radius: 3px;
      padding: 10px;
      margin-top: 10px;
      font-family: monospace;
      white-space: pre-wrap;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>テスト結果レポート</h1>
  
  <div class="summary">
    <div class="summary-item">
      <strong>実行日時:</strong> 2025/5/19 12:43:26
    </div>
    <div class="summary-item">
      <strong>実行時間:</strong> 0.00秒
    </div>
    <div class="summary-item">
      <strong>合計テスト数:</strong> 7
    </div>
    <div class="summary-item">
      <strong>成功:</strong> 4
    </div>
    <div class="summary-item">
      <strong>失敗:</strong> 3
    </div>
  </div>
  
  <h2>テスト結果詳細</h2>
  <div class="test-list">
    <div class="test-item success">
  <div class="test-name">
    商品同期
    <span class="test-status success">成功</span>
  </div>
  <div class="test-details">メッセージ: 商品データが正常に同期されました</div>
</div><div class="test-item success">
  <div class="test-name">
    在庫カレンダー更新
    <span class="test-status success">成功</span>
  </div>
  <div class="test-details">メッセージ: 在庫カレンダーが正常に更新されました</div>
</div><div class="test-item success">
  <div class="test-name">
    予約作成
    <span class="test-status success">成功</span>
  </div>
  <div class="test-details">メッセージ: 予約が正常に作成されました</div>
</div><div class="test-item success">
  <div class="test-name">
    注文連携
    <span class="test-status success">成功</span>
  </div>
  <div class="test-details">メッセージ: 注文が予約と正常に関連付けられました</div>
</div><div class="test-item failure">
  <div class="test-name">
    商品バリエーション
    <span class="test-status failure">失敗</span>
  </div>
  <div class="test-details">メッセージ: バリエーション商品のテストに失敗しました</div>
</div><div class="test-item failure">
  <div class="test-name">
    エラーケース
    <span class="test-status failure">失敗</span>
  </div>
  <div class="test-details">メッセージ: エラーケースのテストに失敗しました</div>
</div><div class="test-item failure">
  <div class="test-name">
    テストデータのクリーンアップ
    <span class="test-status failure">失敗</span>
  </div>
  <div class="test-details">メッセージ: テストデータのクリーンアップに失敗しました</div>
</div>
  </div>
  
  <div class="validation-section">
  <h2>検証結果</h2>
  <div class="summary">
    <div class="summary-item">
      <strong>合計検証数:</strong> 8
    </div>
    <div class="summary-item">
      <strong>成功:</strong> 4
    </div>
    <div class="summary-item">
      <strong>失敗:</strong> 4
    </div>
  </div>
  <div class="test-list">
    <div class="validation-item failure">
  <div class="validation-message">
    テスト結果の成功/失敗数が期待と異なります。
    <span class="test-status failure">失敗</span>
  </div>
  <div class="validation-details"><pre>{
  "expectedSuccess": 4,
  "actualSuccess": 4,
  "expectedFailure": 0,
  "actualFailure": 3
}</pre></div>
</div><div class="validation-item success">
  <div class="validation-message">
    テスト "商品同期" は期待通りの結果です。
    <span class="test-status success">成功</span>
  </div>
  
</div><div class="validation-item success">
  <div class="validation-message">
    テスト "在庫カレンダー更新" は期待通りの結果です。
    <span class="test-status success">成功</span>
  </div>
  
</div><div class="validation-item success">
  <div class="validation-message">
    テスト "予約作成" は期待通りの結果です。
    <span class="test-status success">成功</span>
  </div>
  
</div><div class="validation-item success">
  <div class="validation-message">
    テスト "注文連携" は期待通りの結果です。
    <span class="test-status success">成功</span>
  </div>
  
</div><div class="validation-item failure">
  <div class="validation-message">
    テスト "商品バリエーション" の期待結果が定義されていません。
    <span class="test-status failure">失敗</span>
  </div>
  <div class="validation-details"><pre>{
  "actual": {
    "testName": "商品バリエーション",
    "success": false,
    "timestamp": "2025-05-19T03:43:26.015Z",
    "message": "バリエーション商品のテストに失敗しました"
  }
}</pre></div>
</div><div class="validation-item failure">
  <div class="validation-message">
    テスト "エラーケース" の期待結果が定義されていません。
    <span class="test-status failure">失敗</span>
  </div>
  <div class="validation-details"><pre>{
  "actual": {
    "testName": "エラーケース",
    "success": false,
    "timestamp": "2025-05-19T03:43:26.015Z",
    "message": "エラーケースのテストに失敗しました"
  }
}</pre></div>
</div><div class="validation-item failure">
  <div class="validation-message">
    テスト "テストデータのクリーンアップ" の期待結果が定義されていません。
    <span class="test-status failure">失敗</span>
  </div>
  <div class="validation-details"><pre>{
  "actual": {
    "testName": "テストデータのクリーンアップ",
    "success": false,
    "timestamp": "2025-05-19T03:43:26.015Z",
    "message": "テストデータのクリーンアップに失敗しました"
  }
}</pre></div>
</div>
  </div>
</div>
  
  <div class="footer">
    <p>レポート生成日時: 2025/5/19 12:43:33</p>
  </div>
</body>
</html>