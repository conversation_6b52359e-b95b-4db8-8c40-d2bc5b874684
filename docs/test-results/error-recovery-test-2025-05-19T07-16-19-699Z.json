{"timestamp": "2025-05-19T07:16:17.448Z", "duration": 2.251, "totalTests": 3, "successCount": 3, "failureCount": 0, "tests": [{"testName": "API障害からの回復テスト", "success": true, "timestamp": "2025-05-19T07:16:17.523Z", "message": "API障害から正常に回復しました", "error": "", "duration": 75}, {"testName": "ネットワーク切断からの回復テスト", "success": true, "timestamp": "2025-05-19T07:16:17.542Z", "message": "ネットワーク切断から正常に回復しました", "error": "", "duration": 19}, {"testName": "データ不整合の自動修復テスト", "success": true, "timestamp": "2025-05-19T07:16:19.699Z", "message": "データ不整合を正常に修復しました", "error": "", "duration": 2157}], "environment": {"nodeVersion": "v20.18.1", "os": "darwin arm64"}, "performance": {"averageTestDuration": 750.3333333333334, "slowestTest": {"name": "データ不整合の自動修復テスト", "duration": 2157}, "fastestTest": {"name": "ネットワーク切断からの回復テスト", "duration": 19}}}