# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

**重要**: このプロジェクトは日本市場向けのアプリケーションです。コメント、説明、エラーメッセージは日本語で記述してください。

## Development Commands

### Starting Development Server

**Always use Cloudflare Tunnel for development** (required for Shopify webhooks):

```bash
# 1. Start the persistent tunnel first (in one terminal)
cloudflared tunnel run shopify-app-tunnel

# 2. Start the dev server (in another terminal)
npm run dev:fixed-tunnel
```

The app will be available at `https://app.shopify-app-test.xyz`

### Testing Commands

```bash
# Run all tests
npm test

# Test specific areas
npm run test:booking      # Booking system tests
npm run test:calendar     # Calendar functionality
npm run test:integration  # Integration tests

# Run single test file
npx vitest run path/to/test.ts

# E2E tests
npm run test:e2e          # All E2E tests
npm run test:e2e:calendar # Calendar E2E tests only
```

### Database Commands

```bash
# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate dev

# View database
npx prisma studio
```

### Build & Deployment

```bash
npm run build              # Build for production
npm run deploy            # Deploy to Shopify
npm run lint              # Run linting
```

### Shopify GraphQL Development Tools

**Local Admin API Schema**:
```bash
# View the downloaded Admin API schema
ls -la data/admin_schema_2025-01.json.gz

# Search for specific types in the schema
zcat data/admin_schema_2025-01.json.gz | jq '.data.__schema.types[] | select(.name == "Product")' | less
zcat data/admin_schema_2025-01.json.gz | jq '.data.__schema.types[] | select(.name == "Order")' | less
zcat data/admin_schema_2025-01.json.gz | jq '.data.__schema.types[] | select(.name == "Customer")' | less

# Find available fields for a type
zcat data/admin_schema_2025-01.json.gz | jq '.data.__schema.types[] | select(.name == "Product") | .fields[].name' | sort
```

**Utility Scripts for Shopify Operations**:
```bash
# Check product metafields
node scripts/check-product-metafields-usage.ts

# Create products with complete metafield setup
node scripts/create-complete-product-from-data.ts

# Run arbitrary GraphQL queries
node scripts/run-graphql-query.js

# Find products by search term
node scripts/find-product.js "ソファ"

# Get product information
node scripts/get-product-info.js
```

## Architecture Overview

This is a **Shopify rental booking application** built with Remix, designed for the Japanese market. It enables merchants to rent products with complex booking rules.

### Core Business Logic

**Booking System**: Two-tier rental process
- **Provisional Booking**: Customer pays 10% deposit, inventory is reserved
- **Confirmed Booking**: Full payment converts provisional to confirmed
- Provisional bookings auto-expire after 48 hours if not confirmed
- Complex date-based pricing with holiday surcharges

**Inventory Management**: Calendar-based availability tracking
- `InventoryCalendar` tracks availability by date and variant
- Prevents double-booking with database constraints
- Maintenance periods block inventory
- Real-time sync with Shopify inventory levels

### Service Architecture

The application uses a **Service-Oriented Architecture**:

1. **Route Layer** (`app/routes/`): Handles HTTP requests
   - `api.*.tsx` - AJAX endpoints
   - `app.*.tsx` - Admin UI pages
   - `webhooks.*.tsx` - Shopify webhook handlers

2. **Service Layer** (`app/services/`): Business logic
   - `booking.service.ts` - Booking lifecycle
   - `inventory-calendar.service.ts` - Availability management
   - `shopify.*.service.ts` - Shopify API integration
   - Services use LRU caching for performance

3. **Data Layer** (Prisma + PostgreSQL on Neon)
   - Complex relationships between bookings, products, inventory
   - Optimized queries with proper indexes
   - Transaction support for data consistency

### Key Integrations

**Shopify Integration**:
- Products, customers, and orders sync bidirectionally
- Metafields store rental-specific data (pricing, availability)
- Draft orders created for checkout
- Webhooks keep data synchronized

**Japanese Market Features**:
- Full Japanese localization
- Furigana support for product search
- Japanese business day calculations
- Holiday pricing adjustments
- Freee accounting integration ready

### Critical Files to Understand

1. **`prisma/schema.prisma`**: Database schema - understand data relationships
2. **`app/shopify.server.ts`**: Shopify app configuration and authentication
3. **`app/services/booking.service.ts`**: Core booking business logic
4. **`app/services/inventory-calendar.service.ts`**: Availability calculations
5. **`app/routes/api.bookings.create.tsx`**: Booking creation flow
6. **`app/components/ProductBookingCalendar.tsx`**: Main booking UI component

### Metafield Structure

The app uses Shopify metafields extensively:
- `rental.basic_info` - Product codes and categories
- `rental.pricing` - Base price and extra day rates
- `rental.variant_mapping` - Maps variants to rental periods
- `rental.status` - Product availability status
- See `docs/active/specifications/メタフィールド定義_最新版_20250525.md` for details

### Common Development Patterns

**API Routes**: Always authenticate with Shopify
```typescript
export async function action({ request }: ActionFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  // ... business logic
}
```

**Service Usage**: Services handle business logic
```typescript
const bookingService = new BookingService();
const booking = await bookingService.createBooking(data);
```

**Error Handling**: Use structured error responses
```typescript
return json({ 
  success: false, 
  error: { code: 'INVENTORY_UNAVAILABLE', message: '...' } 
}, { status: 400 });
```

**Database Queries**: Use Prisma with proper error handling
```typescript
try {
  const result = await prisma.$transaction(async (tx) => {
    // Multiple operations
  });
} catch (error) {
  // Handle database errors
}
```

**GraphQL Query Examples**: Reference implementations
```typescript
// From app/shopify.server.ts - authenticated client
const { admin } = await authenticate.admin(request);
const response = await admin.graphql(
  `#graphql
    query getProduct($id: ID!) {
      product(id: $id) {
        title
        metafields(first: 10) {
          edges {
            node {
              namespace
              key
              value
            }
          }
        }
      }
    }`,
  { variables: { id: productId } }
);

// From scripts/ - standalone GraphQL execution
// See: scripts/run-graphql-query.js
// See: scripts/check-product-metafields.js
// See: scripts/create-complete-product-from-data.ts
```