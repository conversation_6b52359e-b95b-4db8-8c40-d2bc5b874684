import { PrismaClient } from "@prisma/client";
import { writeFileSync } from "fs";

const prisma = new PrismaClient();

async function exportData() {
  try {
    console.log("データのエクスポートを開始します...");

    // 各テーブルのデータをエクスポート
    const data = {
      sessions: await prisma.session.findMany(),
      products: await prisma.product.findMany(),
      bookings: await prisma.booking.findMany(),
      customers: await prisma.customer.findMany(),
      syncStatuses: await prisma.syncStatus.findMany(),
      priceSettings: await prisma.priceSettings.findMany(),
      notificationTemplates: await prisma.notificationTemplate.findMany(),
      holidays: await prisma.holiday.findMany(),
      searchHistories: await prisma.searchHistory.findMany(),
      notifications: await prisma.notification.findMany(),
    };

    // JSONファイルとして保存
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const backupPath = `backup_${timestamp}.json`;

    writeFileSync(backupPath, JSON.stringify(data, null, 2));

    console.log("データのエクスポートが完了しました");
    console.log("バックアップファイル:", backupPath);

    // データ件数の表示
    Object.entries(data).forEach(([table, records]) => {
      console.log(`${table}: ${records.length}件`);
    });
  } catch (error) {
    console.error("エクスポート中にエラーが発生しました:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトの実行
exportData();
