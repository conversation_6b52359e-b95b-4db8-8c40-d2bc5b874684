/**
 * すべての商品の在庫数を1に設定するスクリプト
 *
 * このスクリプトは、Shopifyの商品在庫数を1に設定します。
 * メンテナンス中や廃棄予定の商品は除外します。
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品を取得するクエリ
const GET_PRODUCTS = gql`
  query getProducts($first: Int!, $after: String) {
    products(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          status
          variants(first: 10) {
            edges {
              node {
                id
                title
                inventoryQuantity
                inventoryItem {
                  id
                }
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
              }
            }
          }
        }
      }
    }
  }
`;

// 在庫を更新するミューテーション
const UPDATE_INVENTORY = gql`
  mutation inventoryAdjustQuantities($input: InventoryAdjustQuantitiesInput!) {
    inventoryAdjustQuantities(input: $input) {
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールドから値を取得する関数
function getMetafieldValue(metafields, namespace, key) {
  const metafield = metafields.find(
    m => m.node.namespace === namespace && m.node.key === key
  );

  if (!metafield) return null;

  try {
    // JSON形式の場合はパースする
    return JSON.parse(metafield.node.value);
  } catch (e) {
    // 通常の文字列の場合はそのまま返す
    return metafield.node.value;
  }
}

// 商品の在庫を更新する関数
async function updateProductInventory(product) {
  try {
    const metafields = product.metafields.edges;
    const variants = product.variants.edges.map(edge => edge.node);

    // メタフィールドからステータスを取得
    const statusMetafield = getMetafieldValue(metafields, 'rental', 'status');

    // メンテナンス中や廃棄予定の商品は除外
    if (statusMetafield === 'maintenance' || statusMetafield === 'damaged' || statusMetafield === 'unavailable') {
      console.log(`商品「${product.title}」はステータスが ${statusMetafield} のため、在庫を更新しません。`);
      return true;
    }

    // 在庫数を1に設定
    const targetInventory = 1;

    console.log(`商品「${product.title}」の在庫を${targetInventory}に設定します...`);

    // 各バリアントの在庫を更新
    const inventoryItemAdjustments = [];

    for (const variant of variants) {
      const inventoryItemId = variant.inventoryItem.id;
      const currentInventory = variant.inventoryQuantity || 0;
      const delta = targetInventory - currentInventory;

      if (delta !== 0) {
        inventoryItemAdjustments.push({
          inventoryItemId,
          availableDelta: delta
        });
      }
    }

    if (inventoryItemAdjustments.length === 0) {
      console.log(`商品「${product.title}」の在庫は既に${targetInventory}です。スキップします。`);
      return true;
    }

    // 在庫を更新
    const result = await client.request(UPDATE_INVENTORY, {
      input: {
        reason: "在庫調整",
        name: "テスト用在庫設定",
        changes: inventoryItemAdjustments.map(item => ({
          inventoryItemId: item.inventoryItemId,
          availableDelta: item.availableDelta,
          locationId: "gid://shopify/Location/1"
        }))
      }
    });

    if (result.inventoryAdjustQuantities.userErrors.length > 0) {
      console.error(`商品「${product.title}」の在庫更新中にエラーが発生しました:`, result.inventoryAdjustQuantities.userErrors);
      return false;
    }

    console.log(`商品「${product.title}」の在庫を${targetInventory}に更新しました`);
    return true;
  } catch (error) {
    console.error(`商品「${product.title}」の在庫更新中にエラーが発生しました:`, error);
    return false;
  }
}

// すべての商品の在庫を更新する関数
async function updateAllProductsInventory() {
  try {
    console.log('商品データを取得しています...');

    let hasNextPage = true;
    let cursor = null;
    let allProducts = [];

    // ページネーションを使用してすべての商品を取得
    while (hasNextPage) {
      try {
        console.log(`GraphQLリクエストを実行します... cursor: ${cursor}`);
        const result = await client.request(GET_PRODUCTS, {
          first: 50,
          after: cursor
        });

        const products = result.products.edges.map(edge => edge.node);
        allProducts = allProducts.concat(products);

        hasNextPage = result.products.pageInfo.hasNextPage;
        cursor = result.products.pageInfo.endCursor;

        console.log(`${products.length}件の商品を取得しました。合計: ${allProducts.length}件`);

        if (hasNextPage) {
          // APIレート制限対策
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        console.error('商品データの取得中にエラーが発生しました:', error);
        hasNextPage = false;
      }
    }

    console.log(`合計${allProducts.length}件の商品の在庫を更新します...`);

    // 各商品の在庫を更新
    let successCount = 0;
    for (const product of allProducts) {
      console.log(`\n商品「${product.title}」の処理を開始...`);

      try {
        const success = await updateProductInventory(product);

        if (success) {
          console.log(`商品「${product.title}」の在庫更新が完了しました`);
          successCount++;
        } else {
          console.error(`商品「${product.title}」の在庫更新に失敗しました`);
        }
      } catch (error) {
        console.error(`商品「${product.title}」の処理中にエラーが発生しました:`, error);
      }

      // APIレート制限対策
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log(`\n在庫更新が完了しました。${successCount}/${allProducts.length}件の商品の在庫を更新しました。`);
  } catch (error) {
    console.error('エラーが発生しました:', error);
  }
}

// スクリプト実行
updateAllProductsInventory();
