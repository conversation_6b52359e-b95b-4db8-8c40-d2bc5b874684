# テストスクリプト目録

このドキュメントでは、プロジェクトで使用されているテストスクリプトの一覧と説明を提供します。

## 目次

1. [同期確認スクリプト](#同期確認スクリプト)
2. [テストデータ作成スクリプト](#テストデータ作成スクリプト)
3. [予約テストスクリプト](#予約テストスクリプト)
4. [Webhook関連スクリプト](#webhook関連スクリプト)
5. [クリーンアップスクリプト](#クリーンアップスクリプト)
6. [その他のユーティリティスクリプト](#その他のユーティリティスクリプト)

## 同期確認スクリプト

Shopifyとprismaデータベース間の同期状況を確認するためのスクリプト。

| スクリプト名 | 説明 | 使用方法 |
|------------|------|---------|
| `check-product-sync.ts` | 商品データの同期状況を確認する | `npx tsx scripts/check-product-sync.ts [検索キーワード]` |
| `check-customer-sync.ts` | 顧客データの同期状況を確認する | `npx tsx scripts/check-customer-sync.ts [検索キーワード]` |
| `check-booking-sync.ts` | 予約データの同期状況を確認する | `npx tsx scripts/check-booking-sync.ts [検索キーワード]` |
| `check-webhooks.ts` | Webhookの登録状況を確認する | `npx tsx scripts/check-webhooks.ts` |
| `sync-product-to-db.ts` | 商品データをShopifyからprismaに同期する | `npx tsx scripts/sync-product-to-db.ts [検索キーワード]` |

## テストデータ作成スクリプト

テスト用のデータを作成するためのスクリプト。

| スクリプト名 | 説明 | 使用方法 |
|------------|------|---------|
| `create-variant-based-bookings.ts` | 商品バリエーション方式に対応したテスト予約データを作成する | `npx tsx scripts/create-variant-based-bookings.ts` |
| `create-booking-cancellation-tests.ts` | キャンセルパターンを含むテスト予約データを作成する | `npx tsx scripts/create-booking-cancellation-tests.ts` |
| `create-advanced-test-bookings.ts` | 高度なテストケース用の予約データを作成する | `npx tsx scripts/create-advanced-test-bookings.ts` |
| `create-comprehensive-bookings.ts` | 包括的なテスト予約データを作成する | `npx tsx scripts/create-comprehensive-bookings.ts` |
| `create-realistic-test-data.ts` | 現実的なテストデータを作成する | `npx tsx scripts/create-realistic-test-data.ts` |
| `create-test-customers.ts` | テスト顧客データを作成する | `npx tsx scripts/create-test-customers.ts` |
| `create-test-products-for-booking.ts` | 予約テスト用の商品データを作成する | `npx tsx scripts/create-test-products-for-booking.ts` |
| `create-expired-provisional-booking.ts` | 期限切れの仮予約データを作成する | `npx tsx scripts/create-expired-provisional-booking.ts` |

## 予約テストスクリプト

予約機能をテストするためのスクリプト。

| スクリプト名 | 説明 | 使用方法 |
|------------|------|---------|
| `test-booking-date-conflict.ts` | 予約日程の競合をテストする | `npx tsx scripts/test-booking-date-conflict.ts` |
| `test-booking-inventory.ts` | 予約と在庫管理の連携をテストする | `npx tsx scripts/test-booking-inventory.ts` |
| `test-booking-search.ts` | 予約検索機能をテストする | `npx tsx scripts/test-booking-search.ts` |
| `test-cart-integration.ts` | カート連携機能をテストする | `npx tsx scripts/test-cart-integration.ts` |
| `test-holiday-pricing.ts` | 休日料金計算をテストする | `npx tsx scripts/test-holiday-pricing.ts` |
| `test-multi-product-booking.ts` | 複数商品の同時予約をテストする | `npx tsx scripts/test-multi-product-booking.ts` |
| `test-provisional-variant.ts` | 仮予約バリエーションをテストする | `npx tsx scripts/test-provisional-variant.ts` |

## Webhook関連スクリプト

Webhookの登録と管理に関するスクリプト。

| スクリプト名 | 説明 | 使用方法 |
|------------|------|---------|
| `register-webhooks.ts` | Webhookを登録する | `npx tsx scripts/register-webhooks.ts` |
| `register-order-webhooks.ts` | 注文関連のWebhookを登録する | `npx tsx scripts/register-order-webhooks.ts` |

## クリーンアップスクリプト

テストデータをクリーンアップするためのスクリプト。

| スクリプト名 | 説明 | 使用方法 |
|------------|------|---------|
| `cleanup-test-bookings.ts` | テスト予約データを削除する | `npx tsx scripts/cleanup-test-bookings.ts` |
| `cleanup-test-data.ts` | テストデータを削除する | `npx tsx scripts/cleanup-test-data.ts` |
| `cleanup-inventory-calendar.ts` | 在庫カレンダーデータをクリーンアップする | `npx tsx scripts/cleanup-inventory-calendar.ts` |
| `reset-database.ts` | データベースをリセットする | `npx tsx scripts/reset-database.ts` |
| `reset-inventory-calendar.ts` | 在庫カレンダーをリセットする | `npx tsx scripts/reset-inventory-calendar.ts` |

## その他のユーティリティスクリプト

その他の便利なスクリプト。

| スクリプト名 | 説明 | 使用方法 |
|------------|------|---------|
| `check-provisional-expiration.ts` | 仮予約の期限切れを確認する | `npx tsx scripts/check-provisional-expiration.ts` |
| `notify-provisional-expiration.ts` | 仮予約の期限切れを通知する | `npx tsx scripts/notify-provisional-expiration.ts` |
| `prepare-test-environment.ts` | テスト環境を準備する | `npx tsx scripts/prepare-test-environment.ts` |
| `generate-test-report.ts` | テストレポートを生成する | `npx tsx scripts/generate-test-report.ts` |
| `validate-test-results.ts` | テスト結果を検証する | `npx tsx scripts/validate-test-results.ts` |
| `test-shopify-prisma-integration.ts` | ShopifyとPrismaの連携をテストする | `npx tsx scripts/test-shopify-prisma-integration.ts` |
| `test-edge-cases.ts` | エッジケースをテストする | `npx tsx scripts/test-edge-cases.ts` |
| `test-error-recovery.ts` | エラー回復をテストする | `npx tsx scripts/test-error-recovery.ts` |
| `test-load-concurrency.ts` | 負荷と同時実行をテストする | `npx tsx scripts/test-load-concurrency.ts` |
| `test-customer-api.ts` | 顧客APIをテストする | `npx tsx scripts/test-customer-api.ts` |
| `test-customer-reference.ts` | 顧客参照をテストする | `npx tsx scripts/test-customer-reference.ts` |
| `test-shopify-customer-search.ts` | Shopify顧客検索をテストする | `npx tsx scripts/test-shopify-customer-search.ts` |
| `test-metafield-sync.ts` | メタフィールド同期をテストする | `npx tsx scripts/test-metafield-sync.ts` |
| `test-sync-inventory.ts` | 在庫同期をテストする | `npx tsx scripts/test-sync-inventory.ts` |
| `fix-product-display-issues.ts` | 商品表示の問題を修正する | `npx tsx scripts/fix-product-display-issues.ts` |
| `migrate-customer-ids.ts` | 顧客IDを移行する | `npx tsx scripts/migrate-customer-ids.ts` |

## アーカイブされたスクリプト

使用されなくなったスクリプトは `scripts/archive` フォルダに移動されています。これらのスクリプトは参照用に保存されていますが、現在のシステムでは使用されていません。

主なアーカイブされたスクリプトのカテゴリ：

1. 古いドラフトオーダー方式に関連するスクリプト
2. 古いテスト方式に関連するスクリプト
3. 30日以上前に更新されたJavaScriptファイル
4. 重複するスクリプト（同じ機能を持つJSとTSファイル）

アーカイブされたスクリプトを参照する必要がある場合は、`scripts/archive`フォルダを確認してください。
