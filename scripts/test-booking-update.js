/**
 * 予約更新テストスクリプト
 *
 * このスクリプトは、予約の更新機能をテストします。
 * 実行方法: node scripts/test-booking-update.js [商品ID] [予約ID] [更新タイプ]
 * 更新タイプ: date（日付変更）, status（ステータス変更）, info（情報変更）
 */

import { PrismaClient } from '@prisma/client';
import { addDays, format } from 'date-fns';
import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品情報を取得するクエリ
const GET_PRODUCT = gql`
  query getProduct($id: ID!) {
    product(id: $id) {
      id
      title
      status
      variants(first: 5) {
        edges {
          node {
            id
            title
            sku
            inventoryQuantity
            inventoryPolicy
          }
        }
      }
      metafields(first: 10) {
        edges {
          node {
            namespace
            key
            value
          }
        }
      }
    }
  }
`;

// メタフィールドを更新するミューテーション
const UPDATE_METAFIELD = gql`
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * 商品情報を取得する関数
 * @param {string} productId 商品ID
 * @returns {Promise<object>} 商品情報
 */
async function getProductInfo(productId) {
  try {
    // Shopify IDを正規化
    const shopifyId = productId.startsWith('gid://shopify/Product/')
      ? productId
      : `gid://shopify/Product/${productId}`;

    console.log(`商品情報を取得中... (Shopify ID: ${shopifyId})`);

    // Shopifyから商品情報を取得
    const result = await shopifyClient.request(GET_PRODUCT, {
      id: shopifyId
    });

    if (!result.product) {
      throw new Error(`商品ID ${productId} が見つかりません`);
    }

    const product = result.product;
    console.log(`商品情報を取得しました: ${product.title}`);

    // データベースから商品情報を取得
    const dbProduct = await prisma.product.findFirst({
      where: {
        shopifyId: productId.replace('gid://shopify/Product/', '')
      }
    });

    if (!dbProduct) {
      throw new Error(`商品ID ${productId} がデータベースに存在しません`);
    }

    return { shopifyProduct: product, dbProduct };
  } catch (error) {
    console.error('商品情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 予約情報を取得する関数
 * @param {string} bookingId 予約ID
 * @returns {Promise<object>} 予約情報
 */
async function getBookingInfo(bookingId) {
  try {
    // データベースから予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: {
        id: bookingId
      }
    });

    if (!booking) {
      throw new Error(`予約ID ${bookingId} が見つかりません`);
    }

    console.log(`予約情報を取得しました: ${booking.bookingId}`);
    return booking;
  } catch (error) {
    console.error('予約情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 予約情報をメタフィールド用のフォーマットに変換する関数
 * @param {Array} bookings 予約情報
 * @returns {object} メタフィールド用の予約情報
 */
function formatBookingsForMetafield(bookings) {
  // 予約情報をメタフィールド用のフォーマットに変換
  const formattedBookings = bookings.map(booking => ({
    id: booking.bookingId,
    startDate: format(booking.startDate, 'yyyy-MM-dd'),
    endDate: format(booking.endDate, 'yyyy-MM-dd'),
    status: booking.status,
    customerName: booking.customerName,
    customerEmail: booking.customerEmail
  }));

  // メタフィールド用のデータ構造
  const metafieldData = {
    bookings: formattedBookings,
    availability: {
      rentalStatus: 'available',
      startDate: format(new Date(), 'yyyy-MM-dd'),
      maintenanceDates: [],
      blockedDates: []
    }
  };

  return metafieldData;
}

/**
 * 予約情報をShopifyメタフィールドに更新する関数
 * @param {string} productId 商品ID
 * @param {Array} bookings 予約情報
 * @returns {Promise<object>} 更新結果
 */
async function updateBookingMetafield(productId, bookings) {
  try {
    console.log('予約情報メタフィールドを更新中...');

    // Shopify IDを正規化
    const shopifyId = productId.startsWith('gid://shopify/Product/')
      ? productId
      : `gid://shopify/Product/${productId}`;

    // 予約情報をメタフィールド用のフォーマットに変換
    const metafieldData = formatBookingsForMetafield(bookings);

    // メタフィールドの設定
    const result = await shopifyClient.request(UPDATE_METAFIELD, {
      metafields: [
        {
          ownerId: shopifyId,
          namespace: 'rental',
          key: 'bookings',
          value: JSON.stringify(metafieldData),
          type: 'json'
        }
      ]
    });

    if (result.metafieldsSet.userErrors.length > 0) {
      throw new Error(`メタフィールドの設定中にエラーが発生しました: ${JSON.stringify(result.metafieldsSet.userErrors)}`);
    }

    console.log('予約情報メタフィールドを更新しました');
    return result.metafieldsSet.metafields[0];
  } catch (error) {
    console.error('予約情報メタフィールドの更新中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 予約の日付を更新する関数
 * @param {string} bookingId 予約ID
 * @param {Date} newStartDate 新しい開始日
 * @param {Date} newEndDate 新しい終了日
 * @returns {Promise<object>} 更新結果
 */
async function updateBookingDates(bookingId, newStartDate, newEndDate) {
  try {
    console.log(`予約ID ${bookingId} の日付を更新中...`);
    console.log(`新しい期間: ${format(newStartDate, 'yyyy-MM-dd')} 〜 ${format(newEndDate, 'yyyy-MM-dd')}`);

    // 予約情報を取得
    const booking = await getBookingInfo(bookingId);

    // 日付を更新
    const updatedBooking = await prisma.booking.update({
      where: {
        id: bookingId
      },
      data: {
        startDate: newStartDate,
        endDate: newEndDate,
        updatedAt: new Date()
      }
    });

    console.log('予約日程を更新しました:');
    console.log(`予約ID: ${updatedBooking.bookingId}`);
    console.log(`旧期間: ${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')}`);
    console.log(`新期間: ${format(updatedBooking.startDate, 'yyyy-MM-dd')} 〜 ${format(updatedBooking.endDate, 'yyyy-MM-dd')}`);

    // 商品の全予約情報を取得
    const allBookings = await prisma.booking.findMany({
      where: {
        productId: booking.productId,
        status: {
          in: ['PROVISIONAL', 'CONFIRMED']
        }
      }
    });

    // Shopifyメタフィールドを更新
    try {
      // 商品情報を取得
      const product = await prisma.product.findUnique({
        where: {
          id: booking.productId
        }
      });

      if (product) {
        await updateBookingMetafield(`gid://shopify/Product/${product.shopifyId}`, allBookings);
        console.log('Shopifyメタフィールドを更新しました');
      }
    } catch (metafieldError) {
      console.error('Shopifyメタフィールドの更新中にエラーが発生しました:', metafieldError);
      // メタフィールドの更新に失敗しても、予約の更新自体は成功とする
    }

    return { success: true, booking: updatedBooking };
  } catch (error) {
    console.error('予約日程の更新中にエラーが発生しました:', error);
    return { success: false, error };
  }
}

/**
 * 予約のステータスを更新する関数
 * @param {string} bookingId 予約ID
 * @param {string} newStatus 新しいステータス
 * @returns {Promise<object>} 更新結果
 */
async function updateBookingStatus(bookingId, newStatus) {
  try {
    console.log(`予約ID ${bookingId} のステータスを更新中...`);
    console.log(`新しいステータス: ${newStatus}`);

    // 予約情報を取得
    const booking = await getBookingInfo(bookingId);

    // ステータスを更新
    const updatedBooking = await prisma.booking.update({
      where: {
        id: bookingId
      },
      data: {
        status: newStatus,
        bookingType: newStatus,
        updatedAt: new Date()
      }
    });

    console.log('予約ステータスを更新しました:');
    console.log(`予約ID: ${updatedBooking.bookingId}`);
    console.log(`旧ステータス: ${booking.status}`);
    console.log(`新ステータス: ${updatedBooking.status}`);

    // 商品の全予約情報を取得
    const allBookings = await prisma.booking.findMany({
      where: {
        productId: booking.productId,
        status: {
          in: ['PROVISIONAL', 'CONFIRMED']
        }
      }
    });

    // Shopifyメタフィールドを更新
    try {
      // 商品情報を取得
      const product = await prisma.product.findUnique({
        where: {
          id: booking.productId
        }
      });

      if (product) {
        await updateBookingMetafield(`gid://shopify/Product/${product.shopifyId}`, allBookings);
        console.log('Shopifyメタフィールドを更新しました');
      }
    } catch (metafieldError) {
      console.error('Shopifyメタフィールドの更新中にエラーが発生しました:', metafieldError);
      // メタフィールドの更新に失敗しても、予約の更新自体は成功とする
    }

    return { success: true, booking: updatedBooking };
  } catch (error) {
    console.error('予約ステータスの更新中にエラーが発生しました:', error);
    return { success: false, error };
  }
}

/**
 * 予約の顧客情報を更新する関数
 * @param {string} bookingId 予約ID
 * @param {object} customerInfo 新しい顧客情報
 * @returns {Promise<object>} 更新結果
 */
async function updateBookingCustomerInfo(bookingId, customerInfo) {
  try {
    console.log(`予約ID ${bookingId} の顧客情報を更新中...`);

    // 予約情報を取得
    const booking = await getBookingInfo(bookingId);

    // 顧客情報を更新
    const updatedBooking = await prisma.booking.update({
      where: {
        id: bookingId
      },
      data: {
        customerName: customerInfo.name || booking.customerName,
        customerEmail: customerInfo.email || booking.customerEmail,
        updatedAt: new Date()
      }
    });

    console.log('予約顧客情報を更新しました:');
    console.log(`予約ID: ${updatedBooking.bookingId}`);
    console.log(`旧顧客名: ${booking.customerName}`);
    console.log(`新顧客名: ${updatedBooking.customerName}`);
    console.log(`旧メールアドレス: ${booking.customerEmail}`);
    console.log(`新メールアドレス: ${updatedBooking.customerEmail}`);

    // 商品の全予約情報を取得
    const allBookings = await prisma.booking.findMany({
      where: {
        productId: booking.productId,
        status: {
          in: ['PROVISIONAL', 'CONFIRMED']
        }
      }
    });

    // Shopifyメタフィールドを更新
    try {
      // 商品情報を取得
      const product = await prisma.product.findUnique({
        where: {
          id: booking.productId
        }
      });

      if (product) {
        await updateBookingMetafield(`gid://shopify/Product/${product.shopifyId}`, allBookings);
        console.log('Shopifyメタフィールドを更新しました');
      }
    } catch (metafieldError) {
      console.error('Shopifyメタフィールドの更新中にエラーが発生しました:', metafieldError);
      // メタフィールドの更新に失敗しても、予約の更新自体は成功とする
    }

    return { success: true, booking: updatedBooking };
  } catch (error) {
    console.error('予約顧客情報の更新中にエラーが発生しました:', error);
    return { success: false, error };
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    // コマンドライン引数から商品ID、予約ID、更新タイプを取得
    const productId = process.argv[2];
    const bookingId = process.argv[3];
    const updateType = process.argv[4] || 'date';

    if (!productId || !bookingId) {
      console.error('商品IDまたは予約IDが指定されていません。使用方法: node scripts/test-booking-update.js [商品ID] [予約ID] [更新タイプ]');
      process.exit(1);
    }

    console.log(`商品ID ${productId} の予約ID ${bookingId} を更新します...`);
    console.log(`更新タイプ: ${updateType}`);

    // 商品情報を取得
    const product = await getProductInfo(productId);

    // 更新タイプに応じたテストを実行
    let testResult;
    switch (updateType) {
      case 'date':
        // 現在の日付から7日後と10日後に変更
        const newStartDate = addDays(new Date(), 7);
        const newEndDate = addDays(new Date(), 10);
        testResult = await updateBookingDates(bookingId, newStartDate, newEndDate);
        break;
      case 'status':
        // 予約ステータスを「CONFIRMED」に変更
        testResult = await updateBookingStatus(bookingId, 'CONFIRMED');
        break;
      case 'info':
        // 顧客情報を更新
        testResult = await updateBookingCustomerInfo(bookingId, {
          name: '更新テストユーザー',
          email: '<EMAIL>'
        });
        break;
      default:
        console.error(`未知の更新タイプです: ${updateType}`);
        process.exit(1);
    }

    if (testResult.success) {
      console.log('予約更新テストが成功しました！');
    } else {
      console.error('予約更新テストが失敗しました:', testResult.error);
      process.exit(1);
    }
  } catch (error) {
    console.error('予約更新テスト中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
