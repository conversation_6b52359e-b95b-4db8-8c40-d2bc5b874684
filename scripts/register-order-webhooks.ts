/**
 * 注文関連のWebhookを登録するスクリプト
 *
 * このスクリプトは、Shopify Admin APIを使用して注文関連のWebhookを登録します。
 *
 * 使用方法:
 * npx tsx scripts/register-order-webhooks.ts
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// Webhookサブスクリプションを作成するミューテーション
const CREATE_WEBHOOK_SUBSCRIPTION = gql`
  mutation webhookSubscriptionCreate($topic: WebhookSubscriptionTopic!, $webhookSubscription: WebhookSubscriptionInput!) {
    webhookSubscriptionCreate(topic: $topic, webhookSubscription: $webhookSubscription) {
      webhookSubscription {
        id
        topic
        endpoint {
          __typename
          ... on WebhookHttpEndpoint {
            callbackUrl
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * Webhookを登録する
 */
async function registerWebhook(topic: string, callbackUrl: string) {
  try {
    console.log(`「${topic}」のWebhookを登録中...`);

    const result = await client.request(CREATE_WEBHOOK_SUBSCRIPTION, {
      topic,
      webhookSubscription: {
        callbackUrl,
        format: "JSON"
      }
    });

    if (result.webhookSubscriptionCreate.userErrors.length > 0) {
      console.error(`「${topic}」のWebhook登録中にエラーが発生しました:`, result.webhookSubscriptionCreate.userErrors);
      return false;
    }

    console.log(`「${topic}」のWebhookを登録しました: ${result.webhookSubscriptionCreate.webhookSubscription.id}`);
    return true;
  } catch (error) {
    console.error(`「${topic}」のWebhook登録中にエラーが発生しました:`, error);
    return false;
  }
}

/**
 * メインの実行関数
 */
async function main() {
  try {
    console.log('注文関連Webhookの登録を開始します...');

    // アプリのホスト名を取得
    const appHost = process.env.HOST || 'https://shopify-app-test.xyz';

    // 登録するWebhookのトピックとURLのマッピング
    const webhooks = [
      // 注文関連（正しいトピック名）
      { topic: 'ORDERS_CREATE', path: '/webhooks/orders/create' },
      { topic: 'ORDERS_PAID', path: '/webhooks/orders/paid' },
      { topic: 'ORDERS_FULFILLED', path: '/webhooks/orders/fulfilled' },
      { topic: 'ORDERS_PARTIALLY_FULFILLED', path: '/webhooks/orders/partially-fulfilled' },
      { topic: 'ORDERS_CANCELLED', path: '/webhooks/orders/cancelled' },
      { topic: 'ORDERS_EDITED', path: '/webhooks/orders/edited' },
      { topic: 'ORDERS_UPDATED', path: '/webhooks/orders/update' }
    ];

    // Webhookを登録
    const results = [];

    for (const webhook of webhooks) {
      const callbackUrl = `${appHost}${webhook.path}`;
      const success = await registerWebhook(webhook.topic, callbackUrl);
      results.push({ topic: webhook.topic, success });
    }

    // 結果の表示
    console.log('\n=== Webhook登録結果 ===');

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    console.log(`成功: ${successCount}件`);
    console.log(`失敗: ${failureCount}件`);

    if (failureCount > 0) {
      console.log('\n失敗したWebhook:');
      results.filter(r => !r.success).forEach(r => {
        console.log(`- ${r.topic}`);
      });
    }

    console.log('\n注文関連Webhookの登録が完了しました');
  } catch (error) {
    console.error('Webhook登録中にエラーが発生しました:', error);
  }
}

// スクリプトを実行
main()
  .then(() => console.log('\nスクリプトの実行が完了しました'))
  .catch((error) => console.error('予期しないエラーが発生しました:', error));
