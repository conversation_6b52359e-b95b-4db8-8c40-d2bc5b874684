import React, { useState, useEffect } from 'react';
import type { ActionFunctionArgs, LoaderFunctionArgs } from '@remix-run/node';
import { json, redirect } from '@remix-run/node';
import { useLoaderData, useSubmit, Form } from '@remix-run/react';
import {
  Page,
  Layout,
  Card,
  Text,
  BlockStack,
  Banner,
  Button,
  InlineStack,
  FormLayout,
  Select,
} from '@shopify/polaris';
import { TitleBar } from '@shopify/app-bridge-react';
import { SimpleCalendar } from '../components/SimpleCalendar';
import { authenticate } from '../shopify.server';
import { prisma } from '../db.server';

interface BookedDate {
  startDate: string;
  endDate: string;
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin } = await authenticate.admin(request);
  
  try {
    // Shopifyから商品を取得
    const response = await admin.graphql(
      `#graphql
        query getProducts {
          products(first: 20) {
            edges {
              node {
                id
                title
                status
              }
            }
          }
        }`
    );
    
    const responseJson = await response.json();
    const shopifyProducts = responseJson.data.products.edges.map(
      ({ node }: any) => ({
        id: node.id,
        title: node.title,
        status: node.status
      })
    );
    
    // Prismaからすべての予約を取得
    const bookings = await prisma.booking.findMany({
      where: {
        status: { in: ['reserved', 'completed'] }
      },
      select: {
        id: true,
        startDate: true,
        endDate: true,
        productId: true,
        status: true
      }
    });
    
    return json({ 
      products: shopifyProducts,
      bookings: bookings,
      errorMessage: null
    });
  } catch (error) {
    console.error('データの取得エラー:', error);
    return json({ 
      products: [],
      bookings: [],
      errorMessage: 'データの取得中にエラーが発生しました'
    });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin } = await authenticate.admin(request);
  const formData = await request.formData();
  
  const productId = formData.get('productId') as string;
  const startDate = formData.get('startDate') as string;
  const endDate = formData.get('endDate') as string;
  
  if (!productId || !startDate || !endDate) {
    return json({
      success: false,
      errorMessage: '必要なデータが不足しています'
    });
  }
  
  try {
    // 予約データを仮作成
    const newBooking = await prisma.booking.create({
      data: {
        bookingId: `BOOK-${Date.now()}`,
        status: 'draft',
        productId,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        priority: 1
      }
    });
    
    return redirect('/app/bookings');
  } catch (error) {
    console.error('予約作成エラー:', error);
    return json({
      success: false,
      errorMessage: '予約の作成中にエラーが発生しました'
    });
  }
};

export default function BookProduct() {
  const { products, bookings, errorMessage } = useLoaderData<typeof loader>();
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [bookedDates, setBookedDates] = useState<BookedDate[]>([]);
  const submit = useSubmit();
  
  // 商品選択オプションの作成
  const productOptions = products.map((product: any) => ({
    label: product.title,
    value: product.id
  }));
  
  // 商品選択時の処理
  useEffect(() => {
    if (selectedProductId) {
      // 選択した商品の予約情報を取得
      const productBookings = bookings.filter(
        (booking: any) => booking.productId === selectedProductId
      );
      
      // 予約日程を設定
      const bookedRanges = productBookings.map((booking: any) => ({
        startDate: booking.startDate,
        endDate: booking.endDate
      }));
      
      setBookedDates(bookedRanges);
    } else {
      setBookedDates([]);
    }
  }, [selectedProductId, bookings]);
  
  const handleRangeSelect = (start: Date | null, end: Date | null) => {
    setStartDate(start);
    setEndDate(end);
  };
  
  const handleSubmitBooking = () => {
    if (!selectedProductId || !startDate || !endDate) return;
    
    const formData = new FormData();
    formData.append('productId', selectedProductId);
    formData.append('startDate', startDate.toISOString());
    formData.append('endDate', endDate.toISOString());
    
    submit(formData, { method: 'post' });
  };
  
  if (errorMessage) {
    return (
      <Page>
        <TitleBar title="商品予約" />
        <Banner status="critical">
          <p>{errorMessage}</p>
        </Banner>
      </Page>
    );
  }
  
  return (
    <Page
      title="商品予約"
      backAction={{ content: '商品一覧に戻る', url: '/app/products' }}
    >
      <TitleBar title="商品予約" />
      
      <Layout>
        <Layout.Section>
          <BlockStack gap="400">
            <Card>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">予約する商品を選択</Text>
                <FormLayout>
                  <Select
                    label="商品"
                    options={productOptions}
                    onChange={setSelectedProductId}
                    value={selectedProductId}
                  />
                </FormLayout>
              </BlockStack>
            </Card>
            
            {selectedProductId && (
              <SimpleCalendar
                bookedDates={bookedDates}
                minDays={1}
                maxDays={30}
                onRangeSelect={handleRangeSelect}
              />
            )}
            
            {selectedProductId && startDate && endDate && (
              <Card>
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2">予約内容の確認</Text>
                  <BlockStack gap="200">
                    <Text variant="bodyMd" as="p">
                      商品: {products.find((p: any) => p.id === selectedProductId)?.title}
                    </Text>
                    <Text variant="bodyMd" as="p">
                      レンタル期間: {startDate.toLocaleDateString()} 〜 {endDate.toLocaleDateString()}
                    </Text>
                    <Text variant="bodyMd" as="p">
                      レンタル日数: {Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1}日間
                    </Text>
                  </BlockStack>
                  <InlineStack gap="200">
                    <Button onClick={() => {
                      setStartDate(null);
                      setEndDate(null);
                    }}>
                      キャンセル
                    </Button>
                    <Button primary onClick={handleSubmitBooking}>
                      予約を確定する
                    </Button>
                  </InlineStack>
                </BlockStack>
              </Card>
            )}
          </BlockStack>
        </Layout.Section>
        
        <Layout.Section variant="oneThird">
          <Card>
            <BlockStack gap="400">
              <Text variant="headingMd" as="h2">予約状況</Text>
              {bookedDates.length === 0 ? (
                <Text variant="bodyMd" as="p">現在予約はありません</Text>
              ) : (
                <BlockStack gap="200">
                  <Text variant="bodyMd" as="p">予約済み期間:</Text>
                  {bookedDates.map((booking, index) => (
                    <Banner key={index} status="info">
                      <Text variant="bodyMd" as="p">
                        {new Date(booking.startDate).toLocaleDateString()} 〜 {new Date(booking.endDate).toLocaleDateString()}
                      </Text>
                    </Banner>
                  ))}
                </BlockStack>
              )}
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}