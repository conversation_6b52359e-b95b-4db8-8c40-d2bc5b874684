import { json } from "@remix-run/node";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import { authenticate } from "../shopify.server";
import { Page, Layout, Card, Button, Text, Banner } from "@shopify/polaris";

/**
 * Webhookセットアップページ
 * GET: Webhookの設定状況を表示
 * POST: Webhookを設定
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);

  try {
    // 現在のWebhook設定を取得
    const response = await admin.graphql(`
      query getWebhooks {
        webhookSubscriptions(first: 100) {
          edges {
            node {
              id
              topic
              endpoint {
                __typename
                ... on WebhookHttpEndpoint {
                  callbackUrl
                }
              }
            }
          }
        }
      }
    `);

    const responseJson = await response.json();
    const webhooks = responseJson.data.webhookSubscriptions.edges.map((edge: any) => ({
      id: edge.node.id,
      topic: edge.node.topic,
      callbackUrl: edge.node.endpoint.__typename === "WebhookHttpEndpoint" ? edge.node.endpoint.callbackUrl : null
    }));

    return json({
      webhooks,
      appUrl: process.env.SHOPIFY_APP_URL || request.url
    });
  } catch (error) {
    console.error("Webhook取得エラー:", error);
    return json({
      webhooks: [],
      appUrl: process.env.SHOPIFY_APP_URL || request.url,
      error: "Webhookの取得中にエラーが発生しました"
    });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "setup_webhooks") {
    try {
      // アプリのURLを取得
      const appUrl = process.env.SHOPIFY_APP_URL || request.url.split("/app")[0];
      
      // ドラフトオーダー完了Webhookを設定
      const draftOrderCompletedResponse = await admin.graphql(`
        mutation createWebhook($topic: WebhookSubscriptionTopic!, $callbackUrl: URL!) {
          webhookSubscriptionCreate(
            topic: $topic,
            webhookSubscription: {
              callbackUrl: $callbackUrl,
              format: JSON
            }
          ) {
            webhookSubscription {
              id
            }
            userErrors {
              field
              message
            }
          }
        }
      `, {
        variables: {
          topic: "DRAFT_ORDERS_CREATE",
          callbackUrl: `${appUrl}/api/webhooks/draft-order-completed`
        }
      });

      const draftOrderCompletedData = await draftOrderCompletedResponse.json();
      
      // 注文作成Webhookを設定
      const orderCreateResponse = await admin.graphql(`
        mutation createWebhook($topic: WebhookSubscriptionTopic!, $callbackUrl: URL!) {
          webhookSubscriptionCreate(
            topic: $topic,
            webhookSubscription: {
              callbackUrl: $callbackUrl,
              format: JSON
            }
          ) {
            webhookSubscription {
              id
            }
            userErrors {
              field
              message
            }
          }
        }
      `, {
        variables: {
          topic: "ORDERS_CREATE",
          callbackUrl: `${appUrl}/api/webhooks/order-created`
        }
      });

      const orderCreateData = await orderCreateResponse.json();

      return json({
        success: true,
        draftOrderWebhook: draftOrderCompletedData.data.webhookSubscriptionCreate,
        orderWebhook: orderCreateData.data.webhookSubscriptionCreate
      });
    } catch (error) {
      console.error("Webhook設定エラー:", error);
      return json({
        success: false,
        error: "Webhookの設定中にエラーが発生しました"
      });
    }
  }

  return json({ success: false, error: "不明なアクション" });
}

export default function WebhooksSetup() {
  const { webhooks, appUrl, error } = useLoaderData<typeof loader>();
  const submit = useSubmit();

  const handleSetupWebhooks = () => {
    submit({ action: "setup_webhooks" }, { method: "post" });
  };

  const draftOrderWebhook = webhooks.find((webhook: any) => 
    webhook.topic === "DRAFT_ORDERS_CREATE" && 
    webhook.callbackUrl?.includes("/api/webhooks/draft-order-completed")
  );

  const orderWebhook = webhooks.find((webhook: any) => 
    webhook.topic === "ORDERS_CREATE" && 
    webhook.callbackUrl?.includes("/api/webhooks/order-created")
  );

  return (
    <Page title="Webhook設定">
      <Layout>
        {error && (
          <Layout.Section>
            <Banner status="critical">{error}</Banner>
          </Layout.Section>
        )}
        
        <Layout.Section>
          <Card>
            <Card.Section>
              <Text variant="headingMd">Webhookの設定状況</Text>
              <Text>アプリURL: {appUrl}</Text>
            </Card.Section>
            
            <Card.Section title="ドラフトオーダー完了Webhook">
              {draftOrderWebhook ? (
                <Banner status="success">
                  設定済み: {draftOrderWebhook.callbackUrl}
                </Banner>
              ) : (
                <Banner status="warning">
                  未設定: ドラフトオーダー完了通知を受け取るためにWebhookを設定してください。
                </Banner>
              )}
            </Card.Section>
            
            <Card.Section title="注文作成Webhook">
              {orderWebhook ? (
                <Banner status="success">
                  設定済み: {orderWebhook.callbackUrl}
                </Banner>
              ) : (
                <Banner status="warning">
                  未設定: 注文作成通知を受け取るためにWebhookを設定してください。
                </Banner>
              )}
            </Card.Section>
            
            <Card.Section>
              <Button primary onClick={handleSetupWebhooks}>
                Webhookを設定する
              </Button>
            </Card.Section>
          </Card>
        </Layout.Section>
        
        <Layout.Section>
          <Card title="登録済みWebhook一覧">
            <Card.Section>
              {webhooks.length > 0 ? (
                <ul style={{ listStyleType: "none", padding: 0 }}>
                  {webhooks.map((webhook: any) => (
                    <li key={webhook.id} style={{ marginBottom: "10px", padding: "10px", border: "1px solid #ddd", borderRadius: "4px" }}>
                      <Text variant="bodyMd" fontWeight="bold">{webhook.topic}</Text>
                      <Text variant="bodyMd">{webhook.callbackUrl}</Text>
                    </li>
                  ))}
                </ul>
              ) : (
                <Text>登録済みのWebhookはありません。</Text>
              )}
            </Card.Section>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
