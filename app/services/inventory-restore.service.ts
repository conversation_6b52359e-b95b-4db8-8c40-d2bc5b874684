import { prisma } from "~/db.server";
import { logger } from "~/utils/logger";

/**
 * 在庫復元サービス
 * 
 * 注文キャンセルや予約キャンセル時の在庫復元処理を管理
 */
export class InventoryRestoreService {

  /**
   * 予約キャンセル時の在庫復元
   */
  async restoreInventoryForBooking(
    bookingId: string,
    options: {
      reason?: string;
      notes?: string;
    } = {}
  ) {
    try {
      logger.info("予約キャンセル時の在庫復元開始", { 
        bookingId,
        reason: options.reason 
      });

      // 予約情報を取得
      const booking = await prisma.booking.findUnique({
        where: { id: bookingId },
        include: {
          product: {
            select: {
              id: true,
              title: true,
              sku: true,
              shopifyId: true
            }
          }
        }
      });

      if (!booking) {
        throw new Error(`予約が見つかりません: ${bookingId}`);
      }

      // 在庫カレンダーから予約期間を削除
      await this.removeBookingFromInventoryCalendar(booking);

      // Shopify在庫を復元
      await this.restoreShopifyInventory(booking);

      // 在庫復元ログを記録
      await this.logInventoryRestore(booking, options);

      logger.info("予約キャンセル時の在庫復元完了", {
        bookingId: booking.bookingId,
        productId: booking.productId,
        startDate: booking.startDate,
        endDate: booking.endDate
      });

      return { success: true, message: "在庫復元完了" };
    } catch (error) {
      logger.error("予約キャンセル時の在庫復元エラー", {
        bookingId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 注文キャンセル時の在庫復元
   */
  async restoreInventoryForOrder(
    orderId: string,
    shop: string,
    options: {
      reason?: string;
      notes?: string;
    } = {}
  ) {
    try {
      logger.info("注文キャンセル時の在庫復元開始", { 
        orderId,
        shop,
        reason: options.reason 
      });

      // 関連する予約を検索
      const relatedBookings = await prisma.booking.findMany({
        where: {
          OR: [
            { shopifyOrderId: orderId },
            { orderId: orderId }
          ],
          shop,
          status: {
            not: "CANCELLED"
          }
        },
        include: {
          product: {
            select: {
              id: true,
              title: true,
              sku: true,
              shopifyId: true
            }
          }
        }
      });

      if (relatedBookings.length === 0) {
        logger.warn("関連する予約が見つかりません", { orderId, shop });
        return { success: true, message: "関連予約なし" };
      }

      // 各予約の在庫を復元
      for (const booking of relatedBookings) {
        await this.restoreInventoryForBooking(booking.id, options);
      }

      logger.info("注文キャンセル時の在庫復元完了", {
        orderId,
        shop,
        bookingsRestored: relatedBookings.length
      });

      return { 
        success: true, 
        message: "在庫復元完了",
        bookingsRestored: relatedBookings.length
      };
    } catch (error) {
      logger.error("注文キャンセル時の在庫復元エラー", {
        orderId,
        shop,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 在庫カレンダーから予約期間を削除
   */
  private async removeBookingFromInventoryCalendar(booking: any) {
    try {
      const startDate = new Date(booking.startDate);
      const endDate = new Date(booking.endDate);
      
      // 予約期間の各日付について在庫カレンダーから削除
      const deletedRecords = await prisma.inventoryCalendar.deleteMany({
        where: {
          productId: booking.productId,
          bookingId: booking.bookingId,
          date: {
            gte: startDate,
            lte: endDate
          }
        }
      });

      logger.info("在庫カレンダーから予約期間を削除", {
        bookingId: booking.bookingId,
        productId: booking.productId,
        startDate: booking.startDate,
        endDate: booking.endDate,
        deletedRecords: deletedRecords.count
      });

      return deletedRecords.count;
    } catch (error) {
      logger.error("在庫カレンダー削除エラー", {
        bookingId: booking.bookingId,
        productId: booking.productId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Shopify在庫を復元
   */
  private async restoreShopifyInventory(booking: any) {
    try {
      // 商品のShopify IDが存在する場合のみ処理
      if (!booking.product?.shopifyId) {
        logger.warn("Shopify商品IDが設定されていません", {
          bookingId: booking.bookingId,
          productId: booking.productId
        });
        return;
      }

      // レンタル商品の場合、在庫は通常1個なので、
      // キャンセル時は在庫を1に戻す
      await this.updateShopifyProductInventory(
        booking.product.shopifyId,
        booking.variantId,
        1 // 在庫を1に復元
      );

      logger.info("Shopify在庫復元完了", {
        bookingId: booking.bookingId,
        shopifyProductId: booking.product.shopifyId,
        variantId: booking.variantId
      });
    } catch (error) {
      logger.error("Shopify在庫復元エラー", {
        bookingId: booking.bookingId,
        shopifyProductId: booking.product?.shopifyId,
        error: error instanceof Error ? error.message : String(error)
      });
      // Shopify在庫復元エラーは警告レベルとして処理を続行
    }
  }

  /**
   * Shopify商品の在庫を更新
   */
  private async updateShopifyProductInventory(
    shopifyProductId: string,
    variantId: string | null,
    quantity: number
  ) {
    try {
      // 実際のShopify API呼び出しは、認証情報が必要なため
      // ここではログ出力のみ行い、実装は後で追加
      logger.info("Shopify在庫更新要求", {
        shopifyProductId,
        variantId,
        quantity
      });

      // TODO: Shopify Admin APIを使用して在庫を更新
      // const mutation = `
      //   mutation inventoryAdjustQuantity($input: InventoryAdjustQuantityInput!) {
      //     inventoryAdjustQuantity(input: $input) {
      //       inventoryLevel {
      //         id
      //         available
      //       }
      //       userErrors {
      //         field
      //         message
      //       }
      //     }
      //   }
      // `;

    } catch (error) {
      logger.error("Shopify在庫更新エラー", {
        shopifyProductId,
        variantId,
        quantity,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 在庫復元ログを記録
   */
  private async logInventoryRestore(
    booking: any,
    options: {
      reason?: string;
      notes?: string;
    }
  ) {
    try {
      // 在庫復元ログをメタデータに記録
      const restoreLog = {
        restoredAt: new Date().toISOString(),
        reason: options.reason || "予約キャンセル",
        notes: options.notes,
        productId: booking.productId,
        productTitle: booking.product?.title,
        startDate: booking.startDate,
        endDate: booking.endDate
      };

      // 予約のメタデータを更新
      const currentMetadata = booking.metadata || {};
      const updatedMetadata = {
        ...currentMetadata,
        inventoryRestore: restoreLog
      };

      await prisma.booking.update({
        where: { id: booking.id },
        data: {
          metadata: updatedMetadata,
          updatedAt: new Date()
        }
      });

      logger.info("在庫復元ログ記録完了", {
        bookingId: booking.bookingId,
        restoreLog
      });
    } catch (error) {
      logger.error("在庫復元ログ記録エラー", {
        bookingId: booking.bookingId,
        error: error instanceof Error ? error.message : String(error)
      });
      // ログ記録エラーは警告レベルとして処理を続行
    }
  }

  /**
   * 在庫復元状況を確認
   */
  async checkInventoryRestoreStatus(shop: string, days: number = 7) {
    try {
      const since = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

      // 最近キャンセルされた予約を検索
      const cancelledBookings = await prisma.booking.findMany({
        where: {
          shop,
          status: "CANCELLED",
          updatedAt: {
            gte: since
          }
        },
        include: {
          product: {
            select: {
              id: true,
              title: true,
              sku: true
            }
          }
        },
        orderBy: {
          updatedAt: 'desc'
        }
      });

      // 在庫復元が記録されている予約をカウント
      const restoredBookings = cancelledBookings.filter(booking => 
        booking.metadata && 
        typeof booking.metadata === 'object' && 
        'inventoryRestore' in booking.metadata
      );

      logger.info("在庫復元状況確認", {
        shop,
        days,
        totalCancelledBookings: cancelledBookings.length,
        restoredBookings: restoredBookings.length,
        restoreRate: cancelledBookings.length > 0 
          ? (restoredBookings.length / cancelledBookings.length * 100).toFixed(1) + '%'
          : '0%'
      });

      return {
        totalCancelledBookings: cancelledBookings.length,
        restoredBookings: restoredBookings.length,
        restoreRate: cancelledBookings.length > 0 
          ? restoredBookings.length / cancelledBookings.length
          : 0,
        cancelledBookings: cancelledBookings.map(booking => ({
          bookingId: booking.bookingId,
          productTitle: booking.product?.title,
          cancelledAt: booking.updatedAt,
          hasRestoreLog: !!(booking.metadata && 
            typeof booking.metadata === 'object' && 
            'inventoryRestore' in booking.metadata)
        }))
      };
    } catch (error) {
      logger.error("在庫復元状況確認エラー", {
        shop,
        days,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }
}
