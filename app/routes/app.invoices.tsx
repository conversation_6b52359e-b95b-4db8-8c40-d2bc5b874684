import React, { useState } from 'react';
import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import { 
  Page, 
  LegacyCard, 
  ResourceList, 
  ResourceItem, 
  Filters, 
  Badge, 
  EmptyState,
  Text,
  Pagination,
  // Button, // 未使用のため削除
} from '@shopify/polaris';
import { prisma } from '../db.server';
import { PaymentStatus } from '@prisma/client'; // PaymentStatus のインポート元を修正

// 請求書ステータスに応じたバッジ設定
const invoiceStatusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  [PaymentStatus.PENDING]: { tone: 'attention', label: '保留中' }, // DRAFT, SENT, OVERDUE を PENDING で代用
  [PaymentStatus.COMPLETED]: { tone: 'success', label: '完了' }, // PAID を COMPLETED で代用
  [PaymentStatus.REFUNDED]: { tone: 'info', label: '返金済み' },
  [PaymentStatus.FAILED]: { tone: 'critical', label: '失敗' },
  // CANCELLED は PaymentStatus enum に存在しないため、必要であればenumに追加するか別途対応
};

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  
  // クエリパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  const limit = parseInt(url.searchParams.get('limit') || '50', 10);
  const status = url.searchParams.get('status') || undefined;
  const query = url.searchParams.get('query') || undefined;
  const startDate = url.searchParams.get('startDate') || undefined;
  const endDate = url.searchParams.get('endDate') || undefined;
  
  // 検索条件を構築
  const where: any = {};
  
  if (status) {
    where.paymentStatus = status; // status を paymentStatus に変更
  }
  
  if (query) {
    where.OR = [
      { invoiceNumber: { contains: query } },
      // { customerName: { contains: query, mode: 'insensitive' } }, // customerInfo (JSON) のため一旦コメントアウト
      // { customerEmail: { contains: query, mode: 'insensitive' } }, // customerInfo (JSON) のため一旦コメントアウト
      { notes: { contains: query, mode: 'insensitive' } },
      {
        bookings: {
          some: {
            OR: [
              { bookingId: { contains: query } },
              { 
                product: {
                  title: { contains: query, mode: 'insensitive' }
                }
              },
              { 
                product: {
                  sku: { contains: query }
                }
              }
            ]
          }
        }
      }
    ];
  }
  
  if (startDate) {
    where.createdAt = { gte: new Date(startDate) }; // issueDate を createdAt に変更
  }
  
  if (endDate) {
    where.createdAt = { // issueDate を createdAt に変更
      ...(where.createdAt || {}),
      lte: new Date(endDate)
    };
  }
  
  // 請求書データを取得
  const invoices = await prisma.invoice.findMany({
    where,
    select: { // include から select に変更し、必要なフィールドを明示
      id: true,
      invoiceNumber: true,
      paymentStatus: true, // status から paymentStatus
      customerInfo: true,  // customerName, customerEmail のため
      paymentMethod: true,
      notes: true,
      createdAt: true,     // issueDate のため
      paymentDate: true,   // paidDate のため
      billingInfo: true,   // dueDate のため (Json)
      bookings: {
        select: {
          id: true,
          bookingId: true,
          totalAmount: true,
          startDate: true,
          endDate: true,
          product: {
            select: { title: true, sku: true }
          }
        }
      }
    },
    orderBy: [
      { paymentStatus: 'asc' }, // status を paymentStatus に変更
      { createdAt: 'desc' }     // issueDate を createdAt に変更
    ],
    skip: (page - 1) * limit,
    take: limit
  });
  
  // 総件数を取得
  const total = await prisma.invoice.count({ where });
  
  return json({
    invoices: invoices.map(invoice => ({
      id: invoice.id,
      invoiceNumber: invoice.invoiceNumber,
      status: invoice.paymentStatus, // status を paymentStatus に変更
      customerName: (invoice.customerInfo as any)?.name || '', // customerInfo から取得
      customerEmail: (invoice.customerInfo as any)?.email || '', // customerInfo から取得
      totalAmount: invoice.bookings.reduce((sum, b) => sum + Number(b.totalAmount || 0), 0).toString(), // bookings から合計を計算
      issueDate: invoice.createdAt.toISOString(), // issueDate を createdAt に変更
      dueDate: (invoice.billingInfo as any)?.dueDate ? new Date((invoice.billingInfo as any).dueDate).toISOString() : null, // billingInfo から取得 (存在する場合)
      paidDate: invoice.paymentDate?.toISOString() || null, // paidDate を paymentDate に変更
      paymentMethod: invoice.paymentMethod || '',
      notes: invoice.notes || '',
      bookingsCount: invoice.bookings?.length || 0, // オプショナルチェイニング追加
      bookings: invoice.bookings?.map(booking => ({ // オプショナルチェイニング追加
        id: booking.id,
        bookingId: booking.bookingId,
        productTitle: booking.product.title,
        productSku: booking.product.sku,
        startDate: booking.startDate?.toISOString() || null,
        endDate: booking.endDate?.toISOString() || null,
        amount: booking.totalAmount?.toString() || '0'
      })) || [] // bookings がない場合は空配列
    })),
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}

export default function InvoicesIndex() {
  const { invoices, pagination } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  
  // 請求書詳細ページへ移動
  const handleInvoiceClick = (invoiceId: string) => {
    navigate(`/app/invoices/${invoiceId}`);
  };
  
  // ステータスフィルターを適用
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    const params = new URLSearchParams(window.location.search);
    
    if (value) {
      params.set('status', value);
    } else {
      params.delete('status');
    }
    
    navigate(`/app/invoices?${params.toString()}`);
  };
  
  // 検索クエリを適用
  const handleSearchSubmit = () => {
    const params = new URLSearchParams(window.location.search);
    
    if (searchQuery) {
      params.set('query', searchQuery);
    } else {
      params.delete('query');
    }
    
    navigate(`/app/invoices?${params.toString()}`);
  };
  
  // ページネーション処理
  const handlePaginationChange = (newPage: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set('page', newPage.toString());
    navigate(`/app/invoices?${params.toString()}`);
  };
  
  // 日付をフォーマット
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };
  
  // 金額をフォーマット
  const formatAmount = (amount: string) => {
    return `¥${parseInt(amount).toLocaleString()}`;
  };
  
  // フィルターオプションを生成
  const filters = [
    {
      key: 'status',
      label: 'ステータス',
      filter: (
        <select
          value={statusFilter}
          onChange={(e) => handleStatusFilterChange(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="">すべて</option>
          <option value={PaymentStatus.PENDING}>保留中</option>
          <option value={PaymentStatus.COMPLETED}>完了</option>
          <option value={PaymentStatus.REFUNDED}>返金済み</option>
          <option value={PaymentStatus.FAILED}>失敗</option>
        </select>
      ),
      shortcut: true,
    },
    {
      key: 'date',
      label: '発行日',
      filter: (
        <div>
          <input
            type="date"
            onChange={(e) => {
              const params = new URLSearchParams(window.location.search);
              if (e.target.value) {
                params.set('startDate', e.target.value);
              } else {
                params.delete('startDate');
              }
              navigate(`/app/invoices?${params.toString()}`);
            }}
            style={{ width: '100%', padding: '8px' }}
          />
        </div>
      ),
    }
  ];
  
  return (
    <Page
      title="請求書管理"
      primaryAction={{
        content: "新規請求書",
        url: "/app/invoices/new"
      }}
    >
      <LegacyCard>
        <ResourceList
          resourceName={{ singular: '請求書', plural: '請求書' }}
          items={invoices}
          renderItem={(invoice) => {
            const { 
              id, 
              invoiceNumber, 
              status, 
              customerName, 
              totalAmount, 
              issueDate, 
              dueDate, 
              paidDate, 
              bookingsCount 
            } = invoice;
            
            const badge = invoiceStatusBadgeMap[status] || { tone: 'new', label: status };
            
            const shortcutActions = [
              {
                content: '詳細',
                url: `/app/invoices/${id}`,
              },
              {
                content: '編集',
                url: `/app/invoices/edit/${id}`,
              }
            ];
            
            // 'DRAFT' に相当するステータスがないため、一旦 PENDING で代用、または条件を見直す
            if (status === PaymentStatus.PENDING) {
              shortcutActions.push({
                content: '送信', // ラベルは仮
                url: `/app/invoices/send/${id}`,
              });
            }
            
            // 'SENT' や 'OVERDUE' に相当するステータスがないため、一旦 PENDING で代用、または条件を見直す
            if (status === PaymentStatus.PENDING) {
              shortcutActions.push({
                content: '支払記録', // ラベルは仮
                url: `/app/invoices/mark-paid/${id}`,
              });
            }
            
            return (
              <ResourceItem
                id={id}
                onClick={() => handleInvoiceClick(id)}
                shortcutActions={shortcutActions}
                persistActions
              >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <Text as="h3" variant="bodyMd" fontWeight="bold">
                      請求書番号: {invoiceNumber}
                    </Text>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodyMd">
                        {customerName} 様
                      </Text>
                    </div>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodyMd" fontWeight="bold">
                        {formatAmount(totalAmount)}
                      </Text>
                    </div>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        発行日: {formatDate(issueDate)}
                        {dueDate && ` / 支払期限: ${formatDate(dueDate)}`}
                        {paidDate && ` / 支払日: ${formatDate(paidDate)}`}
                      </Text>
                    </div>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        予約数: {bookingsCount}件
                      </Text>
                    </div>
                  </div>
                  <div>
                    <Badge tone={badge.tone}>{badge.label}</Badge>
                  </div>
                </div>
              </ResourceItem>
            );
          }}
          filterControl={
            <Filters
              queryValue={searchQuery}
              filters={filters}
              onQueryChange={setSearchQuery}
              onQueryClear={() => setSearchQuery('')}
              onClearAll={() => {
                setSearchQuery('');
                setStatusFilter('');
                navigate('/app/invoices');
              }}
              onQueryBlur={handleSearchSubmit}
              hideQueryField={false}
            />
          }
          emptyState={
            <EmptyState
              heading="請求書がありません"
              image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
            >
              <p>請求書を作成して、レンタル料金の請求を管理しましょう。</p>
            </EmptyState>
          }
        />
        {pagination.totalPages > 1 && (
          <div style={{ padding: '16px', display: 'flex', justifyContent: 'center' }}>
            <Pagination
              hasPrevious={pagination.page > 1}
              onPrevious={() => handlePaginationChange(pagination.page - 1)}
              hasNext={pagination.page < pagination.totalPages}
              onNext={() => handlePaginationChange(pagination.page + 1)}
            />
          </div>
        )}
      </LegacyCard>
      <div style={{ marginTop: '16px', textAlign: 'right' }}>
        <Text as="p" variant="bodySm" tone="subdued">
          合計 {pagination.total} 件の請求書 ( {pagination.page} / {pagination.totalPages} ページ )
        </Text>
      </div>
    </Page>
  );
}
