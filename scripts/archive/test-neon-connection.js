/**
 * Neonデータベース接続テスト
 * 
 * このスクリプトでは以下をテストします：
 * 1. Neonデータベースへの接続
 * 2. 基本的なCRUD操作
 */

import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

// テスト結果を保存する配列
const testResults = [];

/**
 * テスト結果を記録する関数
 */
function logTestResult(testName, success, details = {}) {
  const result = {
    testName,
    success,
    timestamp: new Date().toISOString(),
    ...details
  };
  
  testResults.push(result);
  
  // コンソールにも出力
  console.log(`[${success ? 'SUCCESS' : 'FAILURE'}] ${testName}`);
  if (details.message) {
    console.log(`  ${details.message}`);
  }
  if (details.error) {
    console.error(`  Error: ${details.error}`);
  }
}

/**
 * データベース接続テスト
 */
async function testDatabaseConnection() {
  console.log("\nテストケース1: データベース接続テスト");
  
  try {
    // 接続テスト
    await prisma.$connect();
    
    logTestResult("データベース接続", true, {
      message: "Neonデータベースに正常に接続できました"
    });
    
    return true;
  } catch (error) {
    logTestResult("データベース接続", false, {
      error: error.message
    });
    
    return false;
  }
}

/**
 * 商品テーブルのテスト
 */
async function testProductTable() {
  console.log("\nテストケース2: 商品テーブルのテスト");
  
  try {
    // 商品数を取得
    const productCount = await prisma.product.count();
    
    // 最新の商品を取得
    const latestProducts = await prisma.product.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    console.log(`商品テーブルには${productCount}件のレコードがあります`);
    console.log("最新の商品:");
    latestProducts.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.title} (ID: ${product.id})`);
    });
    
    logTestResult("商品テーブル", productCount > 0, {
      message: `商品テーブルには${productCount}件のレコードがあります`
    });
    
    return productCount > 0;
  } catch (error) {
    logTestResult("商品テーブル", false, {
      error: error.message
    });
    
    return false;
  }
}

/**
 * 予約テーブルのテスト
 */
async function testBookingTable() {
  console.log("\nテストケース3: 予約テーブルのテスト");
  
  try {
    // 予約数を取得
    const bookingCount = await prisma.booking.count();
    
    // 最新の予約を取得
    const latestBookings = await prisma.booking.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        product: true
      }
    });
    
    console.log(`予約テーブルには${bookingCount}件のレコードがあります`);
    console.log("最新の予約:");
    latestBookings.forEach((booking, index) => {
      console.log(`  ${index + 1}. 予約ID: ${booking.bookingId}`);
      console.log(`     商品: ${booking.product?.title || '不明'}`);
      console.log(`     期間: ${booking.startDate.toISOString().split('T')[0]} 〜 ${booking.endDate.toISOString().split('T')[0]}`);
      console.log(`     ステータス: ${booking.status}`);
    });
    
    logTestResult("予約テーブル", bookingCount > 0, {
      message: `予約テーブルには${bookingCount}件のレコードがあります`
    });
    
    return bookingCount > 0;
  } catch (error) {
    logTestResult("予約テーブル", false, {
      error: error.message
    });
    
    return false;
  }
}

/**
 * 在庫カレンダーテーブルのテスト
 */
async function testInventoryCalendarTable() {
  console.log("\nテストケース4: 在庫カレンダーテーブルのテスト");
  
  try {
    // 在庫カレンダー数を取得
    const calendarCount = await prisma.inventoryCalendar.count();
    
    // 予約済みの在庫カレンダーを取得
    const reservedCalendars = await prisma.inventoryCalendar.findMany({
      where: {
        isAvailable: false
      },
      take: 5,
      orderBy: {
        date: 'asc'
      },
      include: {
        product: true
      }
    });
    
    console.log(`在庫カレンダーテーブルには${calendarCount}件のレコードがあります`);
    console.log("予約済みの在庫カレンダー:");
    reservedCalendars.forEach((calendar, index) => {
      console.log(`  ${index + 1}. 日付: ${calendar.date.toISOString().split('T')[0]}`);
      console.log(`     商品: ${calendar.product?.title || '不明'}`);
      console.log(`     利用不可理由: ${calendar.unavailableReason || '不明'}`);
    });
    
    logTestResult("在庫カレンダーテーブル", calendarCount > 0, {
      message: `在庫カレンダーテーブルには${calendarCount}件のレコードがあります`
    });
    
    return calendarCount > 0;
  } catch (error) {
    logTestResult("在庫カレンダーテーブル", false, {
      error: error.message
    });
    
    return false;
  }
}

/**
 * 予約と在庫カレンダーの整合性テスト
 */
async function testBookingCalendarConsistency() {
  console.log("\nテストケース5: 予約と在庫カレンダーの整合性テスト");
  
  try {
    // 最新の予約を取得
    const latestBooking = await prisma.booking.findFirst({
      orderBy: {
        createdAt: 'desc'
      },
      where: {
        status: 'CONFIRMED'
      }
    });
    
    if (!latestBooking) {
      console.log("確定済みの予約が見つかりませんでした");
      
      logTestResult("予約と在庫カレンダーの整合性", false, {
        message: "確定済みの予約が見つかりませんでした"
      });
      
      return false;
    }
    
    console.log(`最新の予約: ID=${latestBooking.id}, 予約ID=${latestBooking.bookingId}`);
    console.log(`期間: ${latestBooking.startDate.toISOString().split('T')[0]} 〜 ${latestBooking.endDate.toISOString().split('T')[0]}`);
    
    // 予約期間の在庫カレンダーを取得
    const calendars = await prisma.inventoryCalendar.findMany({
      where: {
        productId: latestBooking.productId,
        date: {
          gte: latestBooking.startDate,
          lte: latestBooking.endDate
        }
      }
    });
    
    console.log(`予約期間の在庫カレンダー: ${calendars.length}件`);
    
    // 予約期間中のすべての日が予約済みになっているか確認
    const allDaysReserved = calendars.every(calendar => !calendar.isAvailable);
    
    // 予約IDが正しく設定されているか確認
    const allCalendarsHaveBookingId = calendars.every(calendar => calendar.bookingId === latestBooking.id);
    
    const success = allDaysReserved && allCalendarsHaveBookingId;
    
    logTestResult("予約と在庫カレンダーの整合性", success, {
      message: `予約期間中のすべての日が予約済み: ${allDaysReserved}, 予約IDが正しく設定されている: ${allCalendarsHaveBookingId}`
    });
    
    return success;
  } catch (error) {
    logTestResult("予約と在庫カレンダーの整合性", false, {
      error: error.message
    });
    
    return false;
  }
}

/**
 * テスト用の予約データを作成
 */
async function createTestBooking() {
  console.log("\nテストケース6: テスト用の予約データを作成");
  
  try {
    // テスト用の商品を取得
    const testProduct = await prisma.product.findFirst({
      where: {
        sku: {
          startsWith: 'TEST-'
        }
      }
    });
    
    if (!testProduct) {
      console.log("テスト用の商品が見つかりませんでした");
      
      logTestResult("テスト用の予約データ作成", false, {
        message: "テスト用の商品が見つかりませんでした"
      });
      
      return null;
    }
    
    // 予約期間を設定（来月の1日から3日）
    const today = new Date();
    const nextMonth = today.getMonth() + 1;
    const year = today.getFullYear() + (nextMonth > 11 ? 1 : 0);
    const month = nextMonth > 11 ? 0 : nextMonth;
    
    const startDate = new Date(year, month, 1);
    const endDate = new Date(year, month, 3);
    
    // 予約IDを生成
    const bookingId = `TEST-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
    
    // 予約データを作成
    const booking = await prisma.booking.create({
      data: {
        productId: testProduct.id,
        startDate,
        endDate,
        status: 'CONFIRMED',
        bookingType: 'CONFIRMED',
        customerEmail: '<EMAIL>',
        customerName: 'テストユーザー',
        shop: 'peaces-test-block.myshopify.com',
        bookingId
      }
    });
    
    console.log(`テスト用の予約を作成しました: ID=${booking.id}, 予約ID=${booking.bookingId}`);
    console.log(`商品: ${testProduct.title}`);
    console.log(`期間: ${startDate.toISOString().split('T')[0]} 〜 ${endDate.toISOString().split('T')[0]}`);
    
    // 在庫カレンダーも更新
    const dateRange = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      dateRange.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    for (const date of dateRange) {
      await prisma.inventoryCalendar.upsert({
        where: {
          shop_productId_date: {
            shop: 'peaces-test-block.myshopify.com',
            productId: testProduct.id,
            date
          }
        },
        update: {
          isAvailable: false,
          unavailableReason: 'reserved',
          bookingId: booking.id
        },
        create: {
          shop: 'peaces-test-block.myshopify.com',
          productId: testProduct.id,
          shopifyProductId: testProduct.id,
          date,
          isAvailable: false,
          unavailableReason: 'reserved',
          bookingId: booking.id
        }
      });
    }
    
    console.log(`在庫カレンダーを更新しました: ${dateRange.length}日分`);
    
    logTestResult("テスト用の予約データ作成", true, {
      message: `テスト用の予約データを作成しました: ID=${booking.id}, 予約ID=${booking.bookingId}`
    });
    
    return booking;
  } catch (error) {
    logTestResult("テスト用の予約データ作成", false, {
      error: error.message
    });
    
    return null;
  }
}

/**
 * メイン関数
 */
async function runTests() {
  console.log("=== Neonデータベース接続テスト ===\n");
  
  try {
    // データベース接続テスト
    const connectionSuccess = await testDatabaseConnection();
    
    if (!connectionSuccess) {
      console.error("データベース接続に失敗しました。テストを中止します。");
      return;
    }
    
    // 各テーブルのテスト
    await testProductTable();
    await testBookingTable();
    await testInventoryCalendarTable();
    
    // 予約と在庫カレンダーの整合性テスト
    await testBookingCalendarConsistency();
    
    // テスト用の予約データを作成
    const testBooking = await createTestBooking();
    
    // テスト結果のサマリーを表示
    console.log("\n=== テスト結果サマリー ===");
    const successCount = testResults.filter(r => r.success).length;
    const failureCount = testResults.length - successCount;
    
    console.log(`実行したテスト数: ${testResults.length}`);
    console.log(`成功: ${successCount}`);
    console.log(`失敗: ${failureCount}`);
    console.log("============================\n");
    
    // 詳細なテスト結果を表示
    console.log("詳細なテスト結果:");
    testResults.forEach((result, index) => {
      console.log(`${index + 1}. [${result.success ? 'SUCCESS' : 'FAILURE'}] ${result.testName}`);
      if (result.message) {
        console.log(`   ${result.message}`);
      }
    });
    
  } catch (error) {
    console.error("テスト実行中にエラーが発生しました:", error);
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// テストを実行
runTests();
