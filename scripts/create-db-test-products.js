import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品作成のミューテーション
const CREATE_PRODUCT_MUTATION = gql`
  mutation productCreate($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// バリアント作成のミューテーション
const CREATE_VARIANT_MUTATION = gql`
  mutation productVariantCreate($input: ProductVariantInput!) {
    productVariantCreate(input: $input) {
      productVariant {
        id
        title
        price
        sku
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// テスト用商品データ
const testProducts = [
  {
    title: "ベーシックソファ オフホワイト 1シーター",
    descriptionHtml: "ベーシックソファ オフホワイト 1シーター - テスト用商品",
    vendor: "テスト",
    productType: "ソファ",
    tags: ["テスト", "ソファ", "オフホワイト"],
    status: "ACTIVE",
    options: ["レンタル日数"],
    variants: [
      {
        price: "8000",
        sku: "10101007-001",
        options: ["1日レンタル"],
        inventoryManagement: "SHOPIFY"
      }
    ]
  },
  {
    title: "クレーリーソファ ベージュ 1シーター",
    descriptionHtml: "クレーリーソファ ベージュ 1シーター - テスト用商品",
    vendor: "テスト",
    productType: "ソファ",
    tags: ["テスト", "ソファ", "ベージュ"],
    status: "ACTIVE",
    options: ["レンタル日数"],
    variants: [
      {
        price: "9000",
        sku: "10101008-001",
        options: ["1日レンタル"],
        inventoryManagement: "SHOPIFY"
      }
    ]
  },
  {
    title: "ウイングソファ アンティークグリーン 1シーター",
    descriptionHtml: "ウイングソファ アンティークグリーン 1シーター - テスト用商品",
    vendor: "テスト",
    productType: "ソファ",
    tags: ["テスト", "ソファ", "アンティークグリーン"],
    status: "ACTIVE",
    options: ["レンタル日数"],
    variants: [
      {
        price: "10000",
        sku: "10101009-001",
        options: ["1日レンタル"],
        inventoryManagement: "SHOPIFY"
      }
    ]
  }
];

// 追加バリアントデータ
const additionalVariants = [
  {
    productIndex: 0,
    variants: [
      { title: "2日レンタル", price: "9600", sku: "10101007-002" },
      { title: "3日レンタル", price: "11200", sku: "10101007-003" },
      { title: "4日レンタル", price: "12800", sku: "10101007-004" },
      { title: "5日レンタル", price: "14400", sku: "10101007-005" },
      { title: "6日レンタル", price: "16000", sku: "10101007-006" },
      { title: "7日レンタル", price: "17600", sku: "10101007-007" },
      { title: "8日以上レンタル", price: "18400", sku: "10101007-008" }
    ]
  },
  {
    productIndex: 1,
    variants: [
      { title: "2日レンタル", price: "10800", sku: "10101008-002" },
      { title: "3日レンタル", price: "12600", sku: "10101008-003" },
      { title: "4日レンタル", price: "14400", sku: "10101008-004" },
      { title: "5日レンタル", price: "16200", sku: "10101008-005" },
      { title: "6日レンタル", price: "18000", sku: "10101008-006" },
      { title: "7日レンタル", price: "19800", sku: "10101008-007" },
      { title: "8日以上レンタル", price: "20700", sku: "10101008-008" }
    ]
  },
  {
    productIndex: 2,
    variants: [
      { title: "2日レンタル", price: "12000", sku: "10101009-002" },
      { title: "3日レンタル", price: "14000", sku: "10101009-003" },
      { title: "4日レンタル", price: "16000", sku: "10101009-004" },
      { title: "5日レンタル", price: "18000", sku: "10101009-005" },
      { title: "6日レンタル", price: "20000", sku: "10101009-006" },
      { title: "7日レンタル", price: "22000", sku: "10101009-007" },
      { title: "8日以上レンタル", price: "23000", sku: "10101009-008" }
    ]
  }
];

// 商品を作成する関数
async function createProduct(productData) {
  try {
    const variables = {
      input: productData
    };
    
    const result = await graphQLClient.request(CREATE_PRODUCT_MUTATION, variables);
    
    if (result.productCreate.userErrors.length > 0) {
      console.error('商品作成エラー:', result.productCreate.userErrors);
      return null;
    }
    
    return result.productCreate.product;
  } catch (error) {
    console.error('商品作成中にエラーが発生しました:', error);
    return null;
  }
}

// バリアントを作成する関数
async function createVariant(productId, variantData) {
  try {
    const variables = {
      input: {
        productId,
        ...variantData,
        options: [variantData.title]
      }
    };
    
    const result = await graphQLClient.request(CREATE_VARIANT_MUTATION, variables);
    
    if (result.productVariantCreate.userErrors.length > 0) {
      console.error('バリアント作成エラー:', result.productVariantCreate.userErrors);
      return null;
    }
    
    return result.productVariantCreate.productVariant;
  } catch (error) {
    console.error('バリアント作成中にエラーが発生しました:', error);
    return null;
  }
}

// メイン処理
async function createTestProducts() {
  console.log('テスト用商品を作成しています...');
  
  const createdProducts = [];
  
  for (const productData of testProducts) {
    console.log(`商品を作成中: ${productData.title}`);
    const product = await createProduct(productData);
    
    if (product) {
      console.log(`商品を作成しました: ${product.title} (ID: ${product.id})`);
      createdProducts.push(product);
    } else {
      console.error(`商品の作成に失敗しました: ${productData.title}`);
    }
  }
  
  // 追加バリアントの作成
  for (const { productIndex, variants } of additionalVariants) {
    if (productIndex < createdProducts.length) {
      const product = createdProducts[productIndex];
      console.log(`${product.title} の追加バリアントを作成中...`);
      
      for (const variantData of variants) {
        console.log(`バリアントを作成中: ${variantData.title}`);
        const variant = await createVariant(product.id, variantData);
        
        if (variant) {
          console.log(`バリアントを作成しました: ${variant.title} (ID: ${variant.id})`);
        } else {
          console.error(`バリアントの作成に失敗しました: ${variantData.title}`);
        }
      }
    }
  }
  
  console.log('\n=== 作成完了 ===');
  console.log(`${createdProducts.length}件の商品を作成しました`);
  
  if (createdProducts.length > 0) {
    console.log('\n作成された商品:');
    createdProducts.forEach(product => {
      console.log(`- ${product.title} (ID: ${product.id})`);
    });
    
    console.log('\nShopify管理画面で商品を確認: https://admin.shopify.com/store/peaces-test-block/products');
  }
}

// 実行
createTestProducts();
