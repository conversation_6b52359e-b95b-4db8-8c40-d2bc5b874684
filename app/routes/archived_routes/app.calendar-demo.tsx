import React, { useState } from 'react';
import { Page, Card, Text, Button, BlockStack } from '@shopify/polaris';
import { SimpleCalendar } from '../components/Calendar/SimpleCalendar';

// 日付に日数を追加する関数
function addDays(date: Date, days: number): Date {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

export default function CalendarDemo() {
  // サンプル予約データ
  const [bookings, setBookings] = useState([
    {
      id: '1',
      startDate: addDays(new Date(), 3),
      endDate: addDays(new Date(), 5),
      status: 'PROVISIONAL' as const
    },
    {
      id: '2',
      startDate: addDays(new Date(), 10),
      endDate: addDays(new Date(), 12),
      status: 'CONFIRMED' as const
    }
  ]);

  // 選択された日付
  const [selectedDates, setSelectedDates] = useState<{ start: Date; end: Date } | undefined>(undefined);

  // 日付選択ハンドラー
  const handleDateSelect = (dates: { start: Date; end: Date }) => {
    setSelectedDates(dates);
  };

  // 予約追加ハンドラー
  const handleAddBooking = () => {
    if (!selectedDates) return;

    const newBooking = {
      id: Math.random().toString(36).substring(7),
      startDate: selectedDates.start,
      endDate: selectedDates.end || selectedDates.start,
      status: 'PROVISIONAL' as const
    };

    setBookings([...bookings, newBooking]);
    setSelectedDates(undefined);
  };

  // 予約確定ハンドラー
  const handleConfirmBooking = (id: string) => {
    const updatedBookings = bookings.map(booking =>
      booking.id === id
        ? { ...booking, status: 'CONFIRMED' as const }
        : booking
    );

    setBookings(updatedBookings);
  };

  // 予約キャンセルハンドラー
  const handleCancelBooking = (id: string) => {
    const updatedBookings = bookings.filter(booking => booking.id !== id);
    setBookings(updatedBookings);
  };

  return (
    <Page title="カレンダーデモ">
      <BlockStack gap="4">
        <SimpleCalendar
          bookings={bookings}
          selectedDates={selectedDates}
          onSelect={handleDateSelect}
          disableDatesBefore={new Date()}
          title="予約カレンダー"
          description="このカレンダーでは、仮予約（黄色）、本予約（緑色）、休日（赤色）の表示を確認できます。日付を選択して「予約追加」ボタンをクリックすると、仮予約として追加されます。"
          showLegend={true}
        />

        {selectedDates && (
          <Card>
            <BlockStack gap="4">
              <Text as="h2" variant="headingMd">選択された日付</Text>
              <Text as="p">
                開始日: {selectedDates.start.toLocaleDateString()}
              </Text>
              {selectedDates.end && selectedDates.end.getTime() !== selectedDates.start.getTime() && (
                <Text as="p">
                  終了日: {selectedDates.end.toLocaleDateString()}
                </Text>
              )}
              <Button primary onClick={handleAddBooking}>予約追加</Button>
            </BlockStack>
          </Card>
        )}

        <Card>
          <BlockStack gap="4">
            <Text as="h2" variant="headingMd">予約一覧</Text>

            {bookings.length === 0 ? (
              <Text as="p">予約はありません</Text>
            ) : (
              <BlockStack gap="4">
                {bookings.map(booking => (
                  <Card key={booking.id}>
                    <BlockStack gap="2">
                      <Text as="p">
                        予約ID: {booking.id}
                      </Text>
                      <Text as="p">
                        期間: {booking.startDate.toLocaleDateString()} 〜 {booking.endDate.toLocaleDateString()}
                      </Text>
                      <Text as="p">
                        ステータス: {booking.status === 'PROVISIONAL' ? '仮予約' : '本予約'}
                      </Text>
                      <BlockStack gap="2" distribution="leading">
                        {booking.status === 'PROVISIONAL' && (
                          <Button onClick={() => handleConfirmBooking(booking.id)}>予約確定</Button>
                        )}
                        <Button destructive onClick={() => handleCancelBooking(booking.id)}>予約キャンセル</Button>
                      </BlockStack>
                    </BlockStack>
                  </Card>
                ))}
              </BlockStack>
            )}
          </BlockStack>
        </Card>
      </BlockStack>
    </Page>
  );
}
