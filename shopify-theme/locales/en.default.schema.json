{"settings_schema": {"quick_start": {"name": "Quick Start", "settings": {"header__1": {"content": "Thank you!"}, "paragraph__1": {"content": "Thank you for using the MISELL theme. It has a lot of features and options. MISELL gives you the power to configure your store in a countless number of ways. With great power comes great responsibility. Be ready to spend a bit more time to become familiar with MISELL power."}, "header__2": {"content": "How to find an option?"}, "paragraph__2": {"content": "Start at Theme settings. You are currently at Theme settings, review it below."}, "paragraph__3": {"content": "Continue with Sections. There are multiple types of sections."}, "paragraph__4": {"content": "a) Dynamic sections - you can add or remove sections for the Home page, e.g.: Builder, Product carousel, Collections, One product. If you need any additional section, please go to Sections -> Add section."}, "paragraph__5": {"content": "b) Static sections - they are store-wide, e.g.: <PERSON>er and <PERSON>er. Go to the Sections tab."}, "paragraph__6": {"content": "c) Static sections per page - unique features per page type, e.g.: 'Product Page' and 'Related product carousel' sections for product pages or 'Sidebar' and 'Head' sections for collection pages. !!! _Navigate to page_ !!! and check the Sections tab."}, "paragraph__7": {"content": "Please note, the list of sections in the Sections tab will be different depending on the current page. You can navigate to a needed page via store preview or using Shopify page type switcher. Please check the [Sections and theme settings](https://help.shopify.com/en/manual/using-themes/change-the-layout/theme-settings/sections-and-settings) Shopify help page for more details."}, "header__3": {"content": "Change text"}, "paragraph__8": {"content": "If you want to change a text of any button, checkbox, etc, go to Theme actions -> Edit languages."}, "header__4": {"content": "Resources"}, "paragraph__9": {"content": "[Shopify help pages](https://help.shopify.com/en/manual/intro-to-shopify/initial-setup/setup-getting-started)"}, "paragraph__10": {"content": "[MISELL FAQ](https://misell-manual.wraptas.site/)"}, "paragraph__11": {"content": "[Watch video tutorials](https://www.youtube.com/@shopifymise1354)"}, "paragraph__12": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}, "header__5": {"content": "Support"}, "paragraph__13": {"content": "[Create a new ticket](https://shop.misell-theme.com/)"}, "paragraph__14": {"content": "Please note, the response time can be up to 1 business day. Theme support does not include Customization services, Installation services."}, "header__6": {"content": "Do You Like MISELL?"}, "paragraph__15": {"content": "If you like our theme, please support us with a five stars review at [Misell](https://shop.misell-theme.com/). Thank you :)"}}}, "license": {"name": "License", "settings": {"purchase_code": {"label": "Purchase code", "info": "You can find a purchase code at misell-theme  [download](https://shop.misell-theme.com/)."}, "presentation_enable": {"label": "Enable a gear wheel popup (Presentation mode)", "info": "Untick this option in real store. This option is only for the demo store."}, "header__1": {"content": "Info"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "logo_and_favicon": {"name": "Logo and Favicon", "settings": {"paragraph__1": {"content": "Select 'Image' if you want to use and download images for logo. Or if you use 'SVG', replace the code in the logo file with yours."}, "logo_types": {"label": "Logo types", "option__1": {"label": "SVG"}, "option__2": {"label": "Image"}}, "header__1": {"content": "Main logo"}, "logo_svg_file": {"label": "Logo file name", "info": "Path: snippets/[svg-file-name].liquid"}, "logo_image": {"label": "Logo image", "info": "Recommended size 64x27 pixels"}, "logo_width": {"label": "Logo width"}, "header__2": {"content": "Mobile header logo"}, "logo_mobile_svg_file": {"label": "Mobile logo file name", "info": "Path: snippets/[svg-file-name].liquid"}, "logo_mobile_image": {"label": "Mobile logo image", "info": "Recommended size 34x27 pixels"}, "logo_mobile_width": {"label": "Mobile logo width"}, "header__3": {"content": "Sticky header logo"}, "logo_sticky_svg_file": {"label": "Sticky logo file name", "info": "Path: snippets/[svg-file-name].liquid"}, "logo_sticky_image": {"label": "Sticky logo image", "info": "Recommended size 34x27 pixels"}, "logo_sticky_width": {"label": "Sticky logo width"}, "header__4": {"content": "Transparent header logo"}, "logo_transparent_svg_file": {"label": "Transparent header logo file name", "info": "Path: snippets/[svg-file-name].liquid"}, "logo_transparent_image": {"label": "Transparent header logo image", "info": "Recommended size 34x27 pixels"}, "logo_transparent_width": {"label": "Transparent header logo width"}, "header__5": {"content": "Footer logo"}, "logo_footer_svg_file": {"label": "Footer logo file name", "info": "Path: snippets/[svg-file-name].liquid"}, "logo_footer_image": {"label": "Footer logo image", "info": "Recommended size 64x27 pixels"}, "logo_footer_width": {"label": "Footer logo width"}, "header__6": {"content": "Mobile footer logo"}, "logo_footer_mobile_svg_file": {"label": "Mobile footer logo file name", "info": "Path: snippets/[svg-file-name].liquid"}, "logo_footer_mobile_image": {"label": "Mobile footer logo image", "info": "Recommended size 64x27 pixels"}, "logo_footer_mobile_width": {"label": "Mobile footer width"}, "header__7": {"content": "Favicon"}, "favicon": {"label": "Favicon image", "info": "Recommended size 32x32 pixels"}, "header__8": {"content": "Support"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "layout": {"name": "Layout", "settings": {"layout_container": {"label": "Default content width", "info": "Applies to header, footer and home page sections. You can overwrite this option at section options.", "option__1": {"label": "Fullwidth"}, "option__2": {"label": "Boxed"}}, "layout_show_breadcrumbs": {"label": "Show breadcrumbs"}, "layout_pagination_type": {"label": "Pagination type", "info": "Applies only to collection page! Infinite scroll - loads next page content when you scroll to the end of the page. Load more - shows load more button at the end of the page. Classic - shows the list of pages with previous and next links.", "option__1": {"label": "Classic"}, "option__2": {"label": "Centered classic"}, "option__3": {"label": "Button 'Load more'"}, "option__4": {"label": "Infinite scroll"}}, "layout_enable_rtl": {"label": "Enable RTL", "info": "Enable right to left text mode. You may need to switch language at Theme actions -> Edit languages."}, "layout_images_lazyload": {"label": "Enable images lazyload", "info": "Highly recommended option for page speed optimization."}, "layout_settings_file": {"label": "Skin", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Home v16"}, "option__3": {"label": "Home v18"}, "option__4": {"label": "Skin #1"}, "option__5": {"label": "Skin #2"}, "option__6": {"label": "Skin #3"}, "option__7": {"label": "Skin #4"}, "option__8": {"label": "Skin #5"}, "option__9": {"label": "Skin #6"}, "option__10": {"label": "Skin #7"}, "option__11": {"label": "Skin #8"}, "option__12": {"label": "Skin #9"}, "option__13": {"label": "Skin #10"}, "option__14": {"label": "Skin #11"}, "option__15": {"label": "Skin #12"}, "option__16": {"label": "Skin #13"}, "option__17": {"label": "Skin #14"}, "option__18": {"label": "Skin #15"}, "option__19": {"label": "Skin #16"}, "option__20": {"label": "Skin #17"}, "option__21": {"label": "Skin #18"}, "option__22": {"label": "Custom"}}, "header__1": {"content": "Age confirmation"}, "layout_age_confirmation": {"label": "Enable age confirmation popup"}, "layout_age_confirmation_image": {"label": "Background image"}, "layout_age_confirmation_image_width": {"label": "Background image width"}, "layout_age_confirmation_checkbox": {"label": "Enable confirmation checkbox"}, "header__2": {"content": "Support"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "colors": {"name": "Colors", "settings": {"header__1": {"content": "Global"}, "color_theme_primary": {"label": "Primary"}, "color_theme_body": {"label": "Body background"}, "color_theme_c": {"label": "Text"}, "color_theme_head_c": {"label": "Heading"}, "color_theme_link_c": {"label": "Link"}, "color_theme_popups_bg": {"label": "Popup background"}, "header__2": {"content": "Buttons"}, "color_button_default_bg": {"label": "Default background"}, "color_button_default_bd": {"label": "Default border"}, "color_button_default_c": {"label": "Default text"}, "color_button_default_act_bg": {"label": "Default active background"}, "color_button_default_act_bd": {"label": "Default active border"}, "color_button_default_act_c": {"label": "Default active text"}, "color_button_invert_bg": {"label": "Invert background"}, "color_button_invert_bd": {"label": "Invert border"}, "color_button_invert_c": {"label": "Invert text"}, "color_button_invert_act_bg": {"label": "Invert active background"}, "color_button_invert_act_bd": {"label": "Invert active border"}, "color_button_invert_act_c": {"label": "Invert active text"}, "color_button_secondary_bg": {"label": "Secondary background"}, "color_button_secondary_bd": {"label": "Secondary border"}, "color_button_secondary_c": {"label": "Secondary text"}, "color_button_secondary_act_bg": {"label": "Secondary active background"}, "color_button_secondary_act_bd": {"label": "Secondary active border"}, "color_button_secondary_act_c": {"label": "Secondary active text"}, "color_button_clean_bg": {"label": "Clean background"}, "color_button_clean_bd": {"label": "Clean border"}, "color_button_clean_c": {"label": "Clean text"}, "color_button_clean_act_bg": {"label": "Clean active background"}, "color_button_clean_act_bd": {"label": "Clean active border"}, "color_button_clean_act_c": {"label": "Clean active text"}, "color_collection_item_addtocart_btn_type": {"label": "Collection item Add To Cart button type", "option__1": {"label": "Auto"}, "option__2": {"label": "<PERSON><PERSON><PERSON>"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Secondary"}, "option__5": {"label": "Clean"}}, "color_product_page_addtocart_btn_type": {"label": "Product page Add To Cart button type", "option__1": {"label": "Auto"}, "option__2": {"label": "<PERSON><PERSON><PERSON>"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Secondary"}, "option__5": {"label": "Clean"}}, "color_product_page_wishlist_btn_type": {"label": "Product page Wishlist button type", "option__1": {"label": "Auto"}, "option__2": {"label": "<PERSON><PERSON><PERSON>"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Secondary"}, "option__5": {"label": "Clean"}}, "color_product_page_compare_btn_type": {"label": "Product page Compare button type", "option__1": {"label": "Auto"}, "option__2": {"label": "<PERSON><PERSON><PERSON>"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Secondary"}, "option__5": {"label": "Clean"}}, "header__3": {"content": "Paint palette"}, "paragraph__1": {"content": "All other colors inherit this palette"}, "color_theme": {"label": "1) Base"}, "color_theme2": {"label": "2) Base background"}, "color_theme3": {"label": "3) Text"}, "color_theme4": {"label": "4) Additional background"}, "color_theme5": {"label": "5) Border"}, "color_theme6": {"label": "6) Icon"}, "color_theme7": {"label": "7) Success, In stock"}, "color_theme8": {"label": "8) <PERSON><PERSON><PERSON>, Sale"}, "color_theme9": {"label": "9) Label 'New'"}, "color_theme10": {"label": "10) Label 'Hot'"}, "color_theme11": {"label": "11) Dynamic checkout btn"}, "color_theme12": {"label": "12) Label 'Pre-order'"}, "color_theme13": {"label": "13) Input background"}, "header__4": {"content": "Support"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "typography": {"name": "Typography", "settings": {"header__1": {"content": "Body"}, "font_base": {"label": "Base font family"}, "font_base_type": {"label": "Load additional style", "info": "For 'Base' font family", "option__1": {"label": "None"}, "option__2": {"label": "Semi Bold"}, "option__3": {"label": "Bold"}, "option__4": {"label": "Medium"}, "option__5": {"label": "Italic"}, "option__6": {"label": "Semi Bold and italic"}, "option__7": {"label": "Bold and italic"}, "option__8": {"label": "Bold italic"}, "option__9": {"label": "All"}}, "font_size_body": {"label": "Base font size"}, "header__2": {"content": "Heading"}, "font_heading": {"label": "<PERSON>ont family"}, "font_heading_type": {"label": "Load additional style", "info": "For 'Heading' font family", "option__1": {"label": "None"}, "option__2": {"label": "Semi Bold"}, "option__3": {"label": "Bold"}, "option__4": {"label": "Italic"}, "option__5": {"label": "Semi Bold and italic"}, "option__6": {"label": "Bold and italic"}, "option__7": {"label": "Bold italic"}, "option__8": {"label": "All"}}, "font_size_h1": {"label": "Promo box large text size, h1"}, "font_size_h2": {"label": "Promo box text size, h2"}, "font_size_h3": {"label": "Page title size, h3"}, "font_size_h4": {"label": "Section title size, h4"}, "font_size_h5": {"label": "Menus title size, h5"}, "font_size_h6": {"label": "Highlighted text size, h6"}, "header__3": {"content": "Button and input"}, "font_button": {"label": "Button font family"}, "font_size_button": {"label": "Button font size"}, "font_size_input": {"label": "Input font size"}, "header__4": {"content": "<PERSON><PERSON>"}, "font_menu": {"label": "<PERSON>ont family"}, "font_size_menu": {"label": "First level font size"}, "font_size_menu_list": {"label": "List font size", "info": "Dropdown, mega menu"}, "font_size_menu_title": {"label": "Title font size", "info": "Mega menu"}, "font_size_menu_mobile": {"label": "Mobile menu font size"}, "header__5": {"content": "Alternative font family"}, "paragraph__1": {"content": "You can add the class 'ff-alternative' to any element to apply this font family"}, "font_load_alternative": {"label": "Enable font family"}, "font_alternative": {"label": "<PERSON>ont family"}, "font_alternative_type": {"label": "Load additional style", "info": "For 'Alternative' font family", "option__1": {"label": "None"}, "option__2": {"label": "Bold"}, "option__3": {"label": "Italic"}, "option__4": {"label": "Bold and italic"}, "option__5": {"label": "Bold italic"}, "option__6": {"label": "All"}}, "header__6": {"content": "Support"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "collection_and_search_page": {"name": "Collection and Search Page", "settings": {"header__1": {"content": "Sidebar"}, "collection_show_sidebar": {"label": "Sidebar visibility", "option__1": {"label": "Desktop and mobile"}, "option__2": {"label": "Only desktop"}, "option__3": {"label": "<PERSON>de"}}, "collection_sidebar_position": {"label": "Sidebar position on desktop", "info": "Configure sidebar position to left, right, top or show the 'Filter' button with slide-out filters popup.", "option__1": {"label": "Left"}, "option__2": {"label": "Right"}, "option__3": {"label": "Top and vertical filters"}, "option__4": {"label": "Popup and 'Filter' button"}}, "collection_enable_sticky": {"label": "Enable sticky sidebar", "info": "Show sidebar on scroll"}, "header__2": {"content": "Other"}, "collection_enable_ajax": {"label": "Show filter result on the fly without page reloading (AJAX)"}, "header__3": {"content": "Support"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "product_general": {"name": "Product -> General", "settings": {"paragraph__1": {"content": "Storewide product options"}, "product_format_pjpg": {"label": "Enable format 'PJPG'", "info": "Don't work with transparent images. Enable it for better page speed performance."}, "product_cursor": {"label": "Cursor image", "info": "Replace the mouse cursor with the image on the product hover on the collection page."}, "header__1": {"content": "Support"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "product_variants": {"name": "Product -> Variants", "settings": {"paragraph__1": {"content": "Configure the product variant design. You can show product options as 'Text', 'Large Text', 'Circle color', 'Circle image', 'Square color', 'Square image', or 'Select' - a dropdown list."}, "header__1": {"content": "General"}, "product_show_custom_options": {"label": "Enable advanced design for variants"}, "product_enable_variants_section": {"label": "Enable 'Product variants' global section for fine-tuning options"}, "product_hide_options_without_availability_variants": {"label": "Hide options without availability variants", "info": "Hide out of stock variants. Or show product option as unavailable."}, "product_variant_auto_select": {"label": "Auto select variant", "option__1": {"label": "Enable"}, "option__2": {"label": "Only first option selected"}, "option__3": {"label": "First and second options selected"}, "option__4": {"label": "Disable"}}, "product_auto_selected_options": {"label": "Auto selected options", "info": "Separator is '|'. Example: Color|Size"}, "product_collection_set_first_image_by_variant": {"label": "Set first image by variant on listing"}, "product_info_set_first_image_by_variant": {"label": "Set first image by variant on product page"}, "product_options_listing_visibility": {"label": "Visibility on listing", "info": "Set number of options to show on collection page. 'Add to cart' button transforms to 'select options' button.", "option__1": {"label": "Show all options"}, "option__2": {"label": "Show only first option"}, "option__3": {"label": "Show first and second options"}}, "product_variant_change_on_hover": {"label": "Change variants on hover", "info": "Change product variant on the mouse over. Only for collection item"}, "header__2": {"content": "Option types"}, "paragraph__2": {"content": "Default custom options type is 'Text'"}, "product_options_type_circle_color": {"label": "Options type 'Circle color'", "info": "Separator is '|'. Show product option as circle with color background. Option name should have a [CSS color value](https://www.w3schools.com/cssref/css_colors.asp). You can configure custom values at Sections -> Product Swatches"}, "product_options_type_circle_image": {"label": "Options type 'Circle image'", "info": "Separator is '|'. Show product option as circle with image. You should configure images per each product variant."}, "product_options_type_square_color": {"label": "Options type 'Square color'", "info": "Separator is '|'. Show product option as square with color background."}, "product_options_type_square_image": {"label": "Options type 'Square image'", "info": "Separator is '|'. Show product option as square with image."}, "product_options_type_select": {"label": "Options type 'Select'", "info": "Separator is '|'. Show product option as drop-down list."}, "product_options_type_large_text": {"label": "Options type 'Large text'", "info": "Separator is '|'. Show option as bigger text."}, "header__3": {"content": "Support"}, "paragraph__3": {"content": "[Watch video tutorials](https://www.youtube.com/@shopifymise1354)"}, "paragraph__4": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "product_collection_page": {"name": "Product -> Collection Page", "settings": {"header__1": {"content": "Image"}, "product_collection_image_height_percent": {"label": "Image height (in % of the width)", "info": "100 - square image. Less than 100 - landscape. More than 100 - portrait. Doesn't work for 'Image resize = Auto'."}, "product_collection_image_size": {"label": "Image resize", "option__1": {"label": "Auto"}, "option__2": {"label": "Contain"}, "option__3": {"label": "Cover"}, "option__4": {"label": "Stretch by width"}, "option__5": {"label": "Stretch by height"}}, "paragraph__1": {"content": "Auto - if you choose this option, ignore the previous option of the image height (in % of the width). The image container and the image size are generated based on source image dimensions."}, "paragraph__2": {"content": "Contain - CSS object-fit = contain. If you choose this option, the image will look like: [object-fit demo](https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit)."}, "paragraph__3": {"content": "Cover - CSS object-fit = cover. If you choose this option, the image will look like [object-fit demo](https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit)."}, "paragraph__4": {"content": "Stretch by width - in this option, the image width fits the container width. It is centered vertically. If the image height is more than the container, cut the image from the top and the bottom. If the image height is less than the container, add white space at the top and the bottom of the image."}, "paragraph__5": {"content": "Stretch by height - in this option, the image height fits the container height. The image is centered horizontally."}, "product_collection_image_border_radius": {"label": "Image border-radius", "info": "Round image corners."}, "product_replace_images_hover": {"label": "Show second images on hover", "info": "Show second product image on mouse over"}, "header__2": {"content": "Labels"}, "product_collection_show_label_in_stock": {"label": "Show label 'In stock'"}, "product_collection_show_label_pre_order": {"label": "Show label 'Pre order'"}, "product_collection_show_label_out_stock": {"label": "Show label 'Out of stock'"}, "product_collection_show_label_sale": {"label": "Show label 'Sale'"}, "product_collection_show_label_new": {"label": "Show label 'New'"}, "product_collection_show_label_hot": {"label": "Show label 'Hot'"}, "header__3": {"content": "Elements"}, "product_collection_centered_info": {"label": "Centered information", "info": "Center product information under the image."}, "product_collection_more_info_type": {"label": "Show product type, vendor or collections before title", "option__1": {"label": "Product type"}, "option__2": {"label": "<PERSON><PERSON><PERSON>"}, "option__3": {"label": "Collections"}, "option__4": {"label": "<PERSON>de"}}, "product_collection_show_title": {"label": "Show the product title"}, "product_collection_title_trim": {"label": "Trim title to one line"}, "product_collection_show_price": {"label": "Show the product price"}, "product_collection_show_button_quick_view": {"label": "Show button 'Quick view'"}, "product_collection_show_countdown": {"label": "Show countdown"}, "product_collection_show_images_navigation": {"label": "Show images navigation"}, "product_collection_show_options": {"label": "Show options (variants)"}, "product_collection_show_quantity": {"label": "Show quantity"}, "product_collection_show_button_add_to_cart": {"label": "Show button 'Add to cart'"}, "product_collection_show_button_add_to_wishlist": {"label": "Show button 'Add to wishlist'"}, "product_collection_show_button_add_to_compare": {"label": "Show button 'Add to compare'"}, "header__4": {"content": "Elements for list mode"}, "product_collection_show_sku": {"label": "Show SKU"}, "product_collection_show_barcode": {"label": "Show product barcode"}, "product_collection_show_availability": {"label": "Show product stock status"}, "product_collection_show_type": {"label": "Show product type"}, "product_collection_show_vendor": {"label": "Show product vendor"}, "product_collection_show_description": {"label": "Show description"}, "product_collection_list_show_quantity": {"label": "Show quantity", "info": "Only for list design #2"}, "product_collection_show_metafield_sub_description": {"title": "Using product metafield", "label": "Show sub_description metafield", "info": "This is an advanced store setup using the product metafield. For more information, see the manual."}, "header__5": {"content": "Support"}, "paragraph__6": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "product_page_and_quick_view": {"name": "Product Page and Quick View", "settings": {"product_info_layout": {"label": "Layout", "info": "'Sheet gallery' and 'Gallery by grid' layouts automatically activate sticky block with information. You can also choose a layout by adding a tag with a layout number to the product as \"gallery-layout-2\".", "option__1": {"label": "#1 Gallery with side thumbnails"}, "option__2": {"label": "#2 Gallery with thumbnails below"}, "option__3": {"label": "#3 Sheet gallery"}, "option__4": {"label": "#4 Gallery by grid"}, "option__5": {"label": "#5 Centered gallery and information"}}, "header__1": {"content": "Gallery"}, "product_info_show_mobile_thumbnails": {"label": "Show thumbnails on mobile"}, "product_info_enable_fullscreen_popup": {"label": "Enable fullscreen gallery popup", "info": "Opens by clicking on the gallery"}, "product_info_show_btn_fullscreen": {"label": "Show button 'Fullscreen gallery'"}, "product_info_show_btn_video": {"label": "Show button 'Video'"}, "product_info_video_autoplay": {"label": "Enable autoplay video"}, "product_info_enable_zoom": {"label": "Enable zoom with mouse"}, "product_info_zoom_scale_coef": {"label": "Zoom scale coefficient"}, "product_info_gallery_height_and_size_path": {"label": "Set image height and size from", "info": "This option is for easier or more flexible to customize the image on the collection page and product page. By default for both pages the parameters are taken from the setting Product -> Collection Page -> Image", "option__1": {"label": "Extend Collection Page settings"}, "option__2": {"label": "Set this section settings"}}, "product_info_gallery_height_percent": {"label": "Gallery images height", "info": "100 - square image. Less than 100 - landscape. More than 100 - portrait. Doesn't work for 'Image resize = Auto'."}, "product_info_gallery_image_size": {"label": "Image resize", "option__1": {"label": "Auto"}, "option__2": {"label": "Contain"}, "option__3": {"label": "Cover"}, "option__4": {"label": "Stretch by width"}, "option__5": {"label": "Stretch by height"}}, "paragraph__1": {"content": "Details for this option described in Product -> Collection Page -> Image"}, "product_info_gallery_image_size_mobile": {"label": "Image resize on mobile", "option__1": {"label": "Auto"}, "option__2": {"label": "Use settings like on desktop"}}, "product_info_gallery_mobile_zoom_type": {"label": "Mobile zoom type", "option__1": {"label": "Theme zoom"}, "option__2": {"label": "Native browser zoom"}}, "product_info_gallery_grouped": {"label": "Group gallery by options", "option__1": {"label": "Disable"}, "option__2": {"label": "Enable"}, "option__3": {"label": "Activate with tag 'group-gallery'"}}, "product_info_enable_sticky_gallery": {"label": "Enable sticky gallery"}, "header__2": {"content": "Labels"}, "product_info_show_label_in_stock": {"label": "Show label 'In stock'"}, "product_info_show_label_pre_order": {"label": "Show label 'Pre order'"}, "product_info_show_label_out_stock": {"label": "Show label 'Out of stock'"}, "product_info_show_label_sale": {"label": "Show label 'Sale'"}, "product_info_show_label_new": {"label": "Show label 'New'"}, "product_info_show_label_hot": {"label": "Show label 'Hot'"}, "header__3": {"content": "Product details"}, "product_info_show_sku": {"label": "Show product SKU"}, "product_info_show_barcode": {"label": "Show product barcode"}, "product_info_show_availability": {"label": "Show product stock status"}, "product_info_show_type": {"label": "Show product type"}, "product_info_show_vendor": {"label": "Show product vendor"}, "header__4": {"content": "Price"}, "product_info_show_sale_details": {"label": "Show sale price details"}, "product_info_show_tax_included": {"label": "Display the text (including tax) next to the price.", "info": "Display the text (including tax) next to the price. This is just a feature to display the tax included text on the theme side. Please configure your Shopify store to reflect the actual amount of tax included."}, "product_info_show_taxes": {"label": "Show taxes text"}, "product_info_payment_terms": {"label": "Show payment terms"}, "header__5": {"content": "Selling plan"}, "paragraph__2": {"content": "Located in the \"Buttons and quantity\" block"}, "product_info_show_selling_plan": {"label": "Show selling plan select"}, "header__6": {"content": "Stock countdown"}, "product_info_show_stock_countdown_range": {"label": "Show range of stock countdown"}, "product_stock_countdown_min": {"label": "Stock countdown min value", "info": "Set the boundary value for 'Hurry! Only XX Left in Stock!'"}, "header__7": {"content": "Delivery countdown"}, "paragraph__3": {"content": "Configure options for 'Order in the next XX hours to get it by YY date' block"}, "product_delivery_countdown_reset_time": {"label": "Hours of the day to reset delivery countdown"}, "product_delivery_countdown_delivery_time": {"label": "Average delivery time of delivery countdown (days)"}, "product_delivery_countdown_delivery_format": {"label": "Format of delivery countdown date", "info": "Example: Day DD/MM/YYYY"}, "product_delivery_countdown_delivery_excludes": {"label": "Exclude days of the week", "info": "Example (excluded weekend): Saturday, Sunday"}, "header__8": {"content": "Popups"}, "paragraph__4": {"content": "Located in the \"Details buttons\" block"}, "product_info_show_size_guide": {"label": "Show button 'Size guide'"}, "product_info_size_guide_page_content": {"label": "Content for 'Size guide'", "info": "Default page: 'Include Popup Size Guide'"}, "product_info_show_delivery_return": {"label": "Show button 'Delivery return'"}, "product_info_delivery_return_page_content": {"label": "Content for 'Delivery return'", "info": "Default page: 'Include Popup Delivery Return'"}, "product_info_show_message": {"label": "Show button 'Message'"}, "header__9": {"content": "Buttons and inputs"}, "product_info_show_quantity": {"label": "Show quantity"}, "product_info_button_layout": {"label": "Buttons layout", "option__1": {"label": "1"}, "option__2": {"label": "2"}, "option__3": {"label": "3"}, "option__4": {"label": "4"}}, "product_info_show_button_add_to_cart": {"label": "Show button 'Add to cart'"}, "product_info_button_add_to_cart_size": {"label": "Button 'Add to cart' size", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Large"}}, "product_info_show_button_dynamic_checkout": {"label": "Show dynamic checkout button"}, "product_info_show_dynamic_checkout_confirmation": {"label": "Show dynamic checkout confirmation checkbox"}, "product_info_styled_dynamic_checkout": {"label": "Custom style for dynamic checkout button"}, "product_info_show_gift_card_recipient": {"label": "Show recipient information form for gift card products", "info": "Gift card products can optionally be sent direct to a recipient on a scheduled date along with a personal message. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}, "product_info_show_button_add_to_wishlist": {"label": "Show button 'Add to wishlist'"}, "product_info_show_button_add_to_compare": {"label": "Show button 'Add to compare'"}, "header__10": {"content": "Pickup availability"}, "product_info_show_unavailable_pickup_available": {"label": "Show unavailable pickup availability"}, "header__11": {"content": "Complementary products"}, "product_info_complementary_products_limit": {"label": "Limit"}, "product_info_complementary_products_add_to_cart": {"label": "Show add to cart instead of quick view"}, "header__12": {"content": "Social share buttons"}, "product_info_social_share_buttons_type": {"label": "Social share buttons type", "option__1": {"label": "Small"}, "option__2": {"label": "Large"}}, "header__13": {"content": "Footbar"}, "product_footbar_enable": {"label": "Enable footbar"}, "product_footbar_show_title": {"label": "Show the product title"}, "product_footbar_show_price": {"label": "Show the product price"}, "product_footbar_show_options": {"label": "Show options (variants)"}, "product_footbar_options_type": {"label": "Options type (Only desktop)", "info": "Type 'Select' - is always on mobile", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Select"}}, "product_footbar_show_quantity": {"label": "Show quantity"}, "product_footbar_show_button_add_to_cart": {"label": "Show button 'Add to cart'"}, "header__14": {"content": "Quick view only"}, "product_quick_view_layout": {"label": "Layout", "info": "This option inherits by default the layout's global setting at the beginning of the \"Product Page and Quick View\" section. you can set a different value for the quick view popup. 'Sheet gallery' and 'Gallery by grid' layouts automatically activate sticky block with information.", "option__1": {"label": "Inherit layout's global setting"}, "option__2": {"label": "#1 Gallery with side thumbnails"}, "option__3": {"label": "#2 Gallery with thumbnails below"}, "option__4": {"label": "#3 Sheet gallery"}, "option__5": {"label": "#4 Gallery by grid"}, "option__6": {"label": "#5 Centered gallery and information"}}, "product_quick_view_show_full_details": {"label": "Show full details button"}, "header__15": {"content": "Support"}, "paragraph__5": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "search": {"name": "Search", "settings": {"search_show": {"label": "Show search in header and navigation popup"}, "search_predictive_enabled": {"label": "Enable product suggestions", "info": "Default search does not support all languages. Turn off this option to switch to a simplified search type. [List of supported languages](https://shopify.dev/docs/themes/ajax-api/reference/predictive-search)"}, "header__1": {"content": "Support"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "currency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"header__1": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "paragraph__1": {"content": "By default theme shows currencies configured at [Shopify Payments](https://help.shopify.com/en/manual/payments/shopify-payments/multi-currency). If you want to show unsupported currencies by Shopify Payments - enable the info currency conversion below."}, "price_show_sale_separator": {"label": "Show sale price separator 'from'. Example: 120$ from 100$"}, "header__2": {"content": "Info currency"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}, "show_multiple_currencies": {"label": "Enable legacy method of the currency conversion", "info": "Even though prices are displayed in different currencies, orders will still be processed in your store's currency."}, "currency_format": {"label": "Format", "option__1": {"label": "Without currency ($10.00)"}, "option__2": {"label": "With currency ($10.00 CAD)"}}, "supported_currencies": {"label": "Supported", "info": "Use the country's ISO [currency code](http://www.xe.com/iso4217.php). Separate your currency codes with a space."}, "supported_currencies_short_names": {"label": "Short names", "info": "Separator is '|'"}, "supported_currencies_full_names": {"label": "Full names", "info": "Separator is '|'"}, "default_currency": {"label": "<PERSON><PERSON><PERSON>"}, "header__3": {"content": "Support"}, "paragraph__3": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "cart": {"name": "<PERSON><PERSON>", "settings": {"header__1": {"content": "General"}, "cart_show_header_button": {"label": "Show header button"}, "cart_ajax": {"label": "Add to cart and stay on the page (AJAX)", "info": "Untick to redirect to the cart page."}, "cart_icon": {"label": "Icon for a 'Cart' button", "info": "default: icon-theme-109"}, "cart_popup_show_checkout_confirmation_checkbox": {"label": "Show checkout confirmation checkbox"}, "header__2": {"content": "Popup"}, "cart_popup_enable": {"label": "Enable popup"}, "cart_popup_type": {"label": "Popup position", "option__1": {"label": "Side of the screen"}, "option__2": {"label": "Center of the screen"}}, "cart_popup_show_quantity_input": {"label": "Show quantity input"}, "cart_popup_show_taxes_and_shipping_information": {"label": "Show taxes and shipping information"}, "cart_popup_show_link_to_cart_page": {"label": "show link to cart page"}, "header__3": {"content": "Cart page"}, "cart_show_vendor": {"label": "Show product vendor"}, "cart_show_notes": {"label": "Show notes on sidebar"}, "header__4": {"content": "Free shipping progress bar"}, "cart_show_free_shipping": {"label": "Enable bar"}, "cart_free_shipping_value": {"label": "Free shipping value", "info": "Set a free shipping limit in cents. If you offer free shipping on orders more than 50 USD, set 5000 here. If you want to set values for different currencies use the separator \"|\""}, "header__5": {"content": "Support"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "wishlist_and_compare": {"name": "Wishlist and Compare", "settings": {"header__1": {"content": "Wishlist"}, "wishlist_type": {"label": "Wishlist type", "option__1": {"label": "Disable"}, "option__2": {"label": "Default (Wishlist app)"}}, "header__2": {"content": "Compare"}, "compare_type": {"label": "Compare type", "option__1": {"label": "Disable"}, "option__2": {"label": "Default (Wishlist app)"}}, "header__3": {"content": "Customer Meta app settings"}, "storelists_price_multiplier": {"label": "Store price format", "info": " ", "option__1": {"label": "With decimals (99.99)"}, "option__2": {"label": "Without decimals (99)"}}, "header__4": {"content": "Compare - Customer Meta app settings"}, "paragraph__1": {"content": "Configure options for product compare popup."}, "compare_show_description": {"label": "Show descriptions"}, "compare_show_collection": {"label": "Show collection"}, "compare_show_availability": {"label": "Show product stock status"}, "compare_show_product_type": {"label": "Show product’s type"}, "compare_show_vendor": {"label": "Show product vendor"}, "compare_show_sku": {"label": "Show SKU "}, "compare_show_barcode": {"label": "Show product barcode"}, "compare_show_options": {"label": "Show options"}, "compare_description_max_size": {"label": "Maximum number of characters in the description"}, "header__5": {"content": "Support"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "payments": {"name": "Payments", "settings": {"payment_design": {"label": "Icons design", "option__1": {"label": "Theme icons"}, "option__2": {"label": "Shopify icons"}, "content": "Icons not provided by Shopify cannot be displayed,please select Misell Icons"}, "header__1": {"content": "Sequence settings"}, "paragraph__1": {"content": "List the icons to display. Clear the field to show store defaults"}, "paragraph__2": {"content": "Separator is ','. Example and complete list of icons:"}, "paragraph__3": {"content": "afterpay,american_express,apple_pay,amazon_pay,bitcoin,dankort,diners_club,discover,dogecoin,dwolla,forbrugsforeningen,google_pay,ideal,jcb,litecoin,maestro,master,paypal,rakuten_pay,shopify_pay,sofort,visa,alipay,au_pay,au,d_barai,docomo,family_mart,lawson,line_pay,merupay,mini_stop,paypay,rakuten_pay,seven_eleven,softbank,union_pay,custom_1,custom_2,custom_3,custom_4,custom_5"}, "payment_sequence_product": {"label": "Product page and Quick view"}, "payment_sequence_footer": {"label": "Footer"}, "header__2": {"content": "Icon Images"}, "payment_images": {"label": "Images shortcode", "info": "You can add a image instead of an icon using the format: afterpay[image_url], visa[image_url]"}, "header__3": {"content": "Support"}, "paragraph__4": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "social_media": {"name": "Social Media", "settings": {"header__1": {"content": "Social sharing"}, "social_share_image": {"label": "Image", "info": "Shown when sharing a link on social media. [Learn more](https://help.shopify.com/manual/using-themes/troubleshooting/showing-social-media-thumbnail-images/) about image thumbnails."}, "header__2": {"content": "Social accounts"}, "social_facebook": {"label": "Facebook link"}, "social_twitter": {"label": "Twitter link"}, "social_instagram": {"label": "Instagram link"}, "social_pinterest": {"label": "Pinterest link"}, "social_youtube": {"label": "YouTube link"}, "social_behance": {"label": "Behance link"}, "social_skype": {"label": "Skype link"}, "social_tiktok": {"label": "TikTok link"}, "header__3": {"content": "Social share buttons"}, "paragraph__1": {"content": "Turn on the option 'Show social share buttons' in the 'Theme settings -> Product Page and Quick View' to show the buttons on the product page."}, "social_share_enable_facebook": {"label": "Show Facebook share button"}, "social_share_enable_twitter": {"label": "Show Twitter share button"}, "social_share_enable_pinterest": {"label": "Show Pinterest share button"}, "social_share_enable_linkedin": {"label": "Show Linkedin share button"}, "social_share_enable_buffer": {"label": "Show Buffer share button"}, "social_share_enable_reddit": {"label": "Show Reddit share button"}, "social_share_enable_line": {"label": "Show Line share button"}, "social_share_enable_hatena": {"label": "Show Hatena share button"}, "social_share_enable_pocket": {"label": "Show Pocket share button"}, "header__4": {"content": "Support"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "news_subscription": {"name": "News Subscription", "settings": {"subscription_method": {"label": "Subscription method", "info": "Shopify (default) subscribers will be added to your “accepts marketing” [customer list.](/admin/customers?query=&accepts_marketing=1)", "option__1": {"label": "Shopify (default)"}, "option__2": {"label": "MailChimp"}}, "subscription_mailchimp_form_action": {"label": "MailChimp URL", "info": "[Where to get MailChimp form action URL?](https://mailchimp.com/)"}, "subscription_show_confirmation_checkbox": {"label": "Show confirmation checkbox"}, "header__1": {"content": "Subscription popup"}, "subscription_popup_enable": {"label": "Enable subscription popup"}, "subscription_popup_layout": {"label": "Layout", "option__1": {"label": "1"}, "option__2": {"label": "2"}, "option__3": {"label": "3"}, "option__4": {"label": "4"}, "option__5": {"label": "5"}}, "subscription_popup_show_icon": {"label": "Show icon"}, "paragraph__1": {"content": "Note! Remove text from the field to hide the block"}, "subscription_popup_text_line_01": {"label": "Text line #1"}, "subscription_popup_text_line_02": {"label": "Text line #2"}, "subscription_popup_text_line_03": {"label": "Text line #3"}, "subscription_popup_text_line_04": {"label": "Text line #4"}, "subscription_popup_show_form": {"label": "Show form"}, "subscription_popup_text_input_placeholder": {"label": "Input placeholder"}, "subscription_popup_text_submit_button_text": {"label": "<PERSON><PERSON> submit text"}, "subscription_popup_text_button": {"label": "Link button text"}, "subscription_popup_text_dont_show_again": {"label": "Text 'Don't show again'"}, "subscription_popup_text_close": {"label": "Text 'Close popup'"}, "subscription_popup_image": {"label": "Background image"}, "subscription_popup_image_width": {"label": "Background image width"}, "subscription_popup_image_format_pjpg": {"label": "Enable format 'PJPG'", "info": "Don't work with transparent images"}, "subscription_popup_image_mobile": {"label": "Mobile background image", "info": "Only for layout #4"}, "subscription_popup_insert_image": {"label": "Insert image"}, "subscription_popup_insert_image_width": {"label": "Insert image width"}, "subscription_popup_insert_image_format_pjpg": {"label": "Enable insert image format 'PJPG'", "info": "Don't work with transparent images"}, "subscription_popup_link": {"label": "Link"}, "subscription_popup_show_once": {"label": "Show once?", "option__1": {"label": "Unless user select 'don't show'"}, "option__2": {"label": "Once"}}, "subscription_popup_delay": {"label": "Delay (sec)"}, "subscription_popup_cookies_life": {"label": "Cookies life", "option__1": {"label": "1 day"}, "option__2": {"label": "3 days"}, "option__3": {"label": "1 week"}, "option__4": {"label": "1 month"}, "option__5": {"label": "1 year"}}, "header__2": {"content": "Subscription confirmation popup"}, "subscription_confirmation_popup_enable": {"label": "Enable subscription confirmation popup"}, "subscription_confirmation_popup_success_message": {"label": "Success message"}, "subscription_confirmation_popup_success_button": {"label": "Success button text"}, "header__3": {"content": "Support"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "blog_and_article_pages": {"name": "Blog and Article Pages", "settings": {"header__1": {"content": "Blog sidebar"}, "blog_show_sidebar": {"label": "Sidebar visibility", "option__1": {"label": "Desktop and mobile"}, "option__2": {"label": "Only desktop"}, "option__3": {"label": "<PERSON>de"}}, "blog_sidebar_position": {"label": "Sidebar position", "option__1": {"label": "Left"}, "option__2": {"label": "Right"}, "option__3": {"label": "Dropdown"}}, "blog_enable_sticky": {"label": "Enable sticky sidebar", "info": "Show sidebar on scroll"}, "header__2": {"content": "Article sidebar"}, "article_show_sidebar": {"label": "Sidebar visibility", "option__1": {"label": "Desktop and mobile"}, "option__2": {"label": "Only desktop"}, "option__3": {"label": "<PERSON>de"}}, "article_sidebar_position": {"label": "Sidebar position", "option__1": {"label": "Left"}, "option__2": {"label": "Right"}, "option__3": {"label": "Dropdown"}}, "article_enable_sticky": {"label": "Enable sticky sidebar", "info": "Show sidebar on scroll"}, "header__3": {"content": "Support"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "account": {"name": "Account", "settings": {"header__1": {"content": "General"}, "account_show_header_button": {"label": "Show header button"}, "header__2": {"content": "Popup"}, "account_popup_enable": {"label": "Enable popup"}, "account_popup_sign_up_info_page_content": {"label": "Content for popup", "info": "Default page: 'Include Popup Account'"}, "account_popup_show_subscription": {"label": "Show subscription", "info": "Visible only after user authorization"}, "header__3": {"content": "Login page"}, "account_page_sing_up_page_content": {"label": "'Sign up' content on login page"}, "header__4": {"content": "Support"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "services": {"name": "Services", "settings": {"header__1": {"content": "General"}, "services_show_header_button": {"label": "Show header button"}, "header__2": {"content": "Popup"}, "services_popup_enable": {"label": "Enable popup"}, "services_header_button_link": {"label": "Header button's link url", "info": "if you don't use popup, set link url. Otherwise, the link go to pages/customer-services as default"}, "services_popup_page_content": {"label": "Content for popup", "info": "Default page: 'Include Popup Services'"}}}, "apps_and_language": {"name": "Apps and Language", "settings": {"header__1": {"content": "Language"}, "app_language": {"label": "Language app", "option__1": {"label": "Store languages (Default)"}, "option__2": {"label": "Weglot"}, "option__3": {"label": "Links list (Select the menu below)"}, "option__4": {"label": "None"}}, "app_language_link_list": {"label": "Links list for language dropdown"}, "header__2": {"content": "Support"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "button_input_and_promo_box": {"name": "Button, Input and Promo Box", "settings": {"header__1": {"content": "Button and Input"}, "button_border_radius": {"label": "Button border-radius"}, "input_border_radius": {"label": "Input border-radius"}, "header__2": {"content": "Promo Box"}, "promobox_image_border_radius": {"label": "Image border-radius"}, "promobox_format_pjpg": {"label": "Enable format 'PJPG'", "info": "Don't work with transparent images"}, "header__3": {"content": "Support"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "notifications": {"name": "Notifications", "settings": {"header__1": {"content": "Cookie notification"}, "notification_cookies_enable": {"label": "Enable"}, "notification_cookies_button_information_link": {"label": "Button information link"}, "notification_cookies_delay": {"label": "Delay (sec)"}, "notification_cookies_show_once": {"label": "Show once?", "info": "If user close notification"}, "notification_cookies_cookies_life": {"label": "Cookies life", "option__1": {"label": "1 day"}, "option__2": {"label": "3 days"}, "option__3": {"label": "1 week"}, "option__4": {"label": "1 month"}, "option__5": {"label": "1 year"}}, "header__2": {"content": "Support"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "pre-loader": {"name": "Pre-loader", "settings": {"layout_preloader_image": {"label": "Custom pre-loader image"}, "layout_preloader_image_size": {"label": "Custom pre-loader image size"}, "layout_preloader_opacity": {"label": "Curtain background opacity"}, "layout_enable_page_preloader": {"label": "Enable page pre-loader"}, "header__1": {"content": "Support"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "animations": {"name": "Animations", "settings": {"header__1": {"content": "Popup"}, "popup_enable_bg_blur": {"label": "Enable blur effect for background"}, "header__2": {"content": "Promo box"}, "promobox_animation_duration": {"label": "Promo box effects (sec)"}, "header__3": {"content": "Tooltips"}, "tooltips_enable": {"label": "Enable tooltips on whole store"}, "tooltips_animation_type": {"label": "Animation style", "option__1": {"label": "Fade"}, "option__2": {"label": "Scale"}, "option__3": {"label": "Shift-toward"}, "option__4": {"label": "Shift-away"}, "option__5": {"label": "Perspective"}}, "header__4": {"content": "Header"}, "header_animation_sticky_opacity": {"label": "Sticky header opacity"}, "header_tape_animation_duration": {"label": "Header tape slide duration (sec)"}, "header__5": {"content": "Product"}, "product_hover_animation_type": {"label": "Animation style for content on product image hover (Collection Page)", "info": "Arrows, Quick View, Labels, Countdown", "option__1": {"label": "Toggle"}, "option__2": {"label": "Fade"}, "option__3": {"label": "Emersion horizontal"}, "option__4": {"label": "Emersion vertical"}, "option__5": {"label": "Emersion all"}, "option__6": {"label": "Scale"}}, "buttons_animation_icon_enable": {"label": "Enable animation for icons on the 'Add to cart',  'Add to compare', and 'Add to wishlist' buttons hover"}, "button_add_to_cart_animation_enable": {"label": "Enable 'Shake' animation for the 'Add to cart' button on the Product Page"}, "header__6": {"content": "Support"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "developer": {"name": "Developer", "settings": {"paragraph__1": {"content": "Our recommendations: 1) track all your custom changes which you apply to the theme. It will help you greatly in the future theme update. (We recommend GIT), 2) keep your changes as separately as you can. Next two options is a good example how to keep CSS and JS code separately."}, "dev_enable_custom_css": {"label": "Enable custom CSS file", "info": "Add your custom CSS style to a separate file. Rename custom-example.css file to custom.css."}, "dev_enable_custom_js": {"label": "Enable custom JavaScript file", "info": "Add your custom JavaScript code to a separate file. Rename custom-example.js file to custom.js"}}}}, "sections": {"article_carousel": {"name": "Article carousel", "settings": {"title": {"label": "Title"}, "header__1": {"content": "General"}, "blog": {"label": "Blog"}, "layout": {"label": "Layout", "option__1": {"label": "Row"}, "option__2": {"label": "Column"}, "option__3": {"label": "Centered in the column"}}, "max_symbols_count": {"label": "Max length of the description"}, "show_details": {"label": "Show the post details"}, "button_text": {"label": "Button text", "info": "For the 'Column' layout"}, "size_of_columns": {"label": "Size of the columns", "option__1": {"label": "2 items in the row"}, "option__2": {"label": "3 items in the row"}, "option__3": {"label": "4 items in the row"}}, "max_items_count": {"label": "Max items count"}, "header__2": {"content": "Slider settings"}, "autoplay": {"label": "Autoplay"}, "speed": {"label": "Autoplay speed (sec)"}, "infinite": {"label": "Is infinite"}, "arrows": {"label": "Arrows"}, "bullets": {"label": "Bullets"}, "header__3": {"content": "Support"}, "paragraph__1": {"content": "[Watch video tutorials](https://www.youtube.com/@shopifymise1354)"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "brand_carousel": {"name": "Brand carousel", "settings": {"title": {"label": "Title"}, "header__1": {"content": "General"}, "layout": {"label": "Layout", "option__1": {"label": "Slide<PERSON>"}, "option__2": {"label": "Grid"}}, "size_of_columns": {"label": "Size of the columns", "info": " Only for <PERSON><PERSON><PERSON>", "option__1": {"label": "4 items in the row"}, "option__2": {"label": "5 items in the row"}, "option__3": {"label": "6 items in the row"}, "option__4": {"label": "7 items in the row"}, "option__5": {"label": "8 items in the row"}}, "image_max_width": {"label": "Image max width", "info": "0 - is auto width"}, "bordered_links": {"label": "Bordered links"}, "border_radius": {"label": "Border-radius (px, %)", "info": "Example: 5px or 50%"}, "header__2": {"content": "Slider settings"}, "autoplay": {"label": "Autoplay"}, "speed": {"label": "Autoplay speed (sec)"}, "infinite": {"label": "Is infinite"}, "arrows": {"label": "Arrows"}, "bullets": {"label": "Bullets"}, "header__3": {"content": "Support"}, "paragraph__1": {"content": "[Watch video tutorials](https://www.youtube.com/@shopifymise1354)"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}, "blocks": {"brand": {"name": "Brand", "settings": {"image": {"label": "Image"}, "link": {"label": "Link", "info": "You can also add a link to the collection + filter by brand. You need to set up filters in the Navigation -> [Filters](/admin/menus) for link filtering to work. Link format: {collection link}?filter.p.vendor={Brand})"}}}}}, "product_carousel": {"name": "Product carousel", "settings": {"show_title": {"label": "Show title"}, "header__1": {"content": "General"}, "container": {"label": "Content width", "option__1": {"label": "Fullwidth"}, "option__2": {"label": "Boxed"}}, "size_of_columns": {"label": "Size of the columns", "option__1": {"label": "2 items in the row"}, "option__2": {"label": "3 items in the row"}, "option__3": {"label": "4 items in the row"}}, "max_count": {"label": "Max count"}, "header__2": {"content": "Slider settings"}, "autoplay": {"label": "Autoplay"}, "speed": {"label": "Autoplay speed (sec)"}, "infinite": {"label": "Is infinite"}, "arrows": {"label": "Arrows"}, "bullets": {"label": "Bullets"}, "header__3": {"content": "Support"}, "paragraph__1": {"content": "[Watch video tutorials](https://www.youtube.com/@shopifymise1354)"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}, "blocks": {"collection": {"name": "Collection", "settings": {"title": {"label": "Title"}, "collection": {"label": "Collection"}}}}}, "related_product_carousel": {"name": "Related product carousel", "settings": {"show_section": {"label": "Show related products"}, "type": {"label": "Type", "option__1": {"label": "Related"}, "option__2": {"label": "Recommendations"}}, "size_of_columns": {"label": "Size of the columns", "option__1": {"label": "2 items in the row"}, "option__2": {"label": "3 items in the row"}, "option__3": {"label": "4 items in the row"}}, "max_count": {"label": "Max count"}, "autoplay": {"label": "Autoplay"}, "speed": {"label": "Autoplay speed (sec)"}, "infinite": {"label": "Is infinite"}, "arrows": {"label": "Arrows"}, "bullets": {"label": "Bullets"}}}, "review_carousel": {"name": "Review carousel", "settings": {"title": {"label": "Title"}, "header__1": {"content": "General"}, "type": {"label": "Type", "option__1": {"label": "1"}, "option__2": {"label": "2"}}, "style": {"label": "Colorize style", "option__1": {"label": "Style #1"}, "option__2": {"label": "Style #2"}, "option__3": {"label": "Style #3"}}, "size_of_columns": {"label": "Size of the columns", "option__1": {"label": "1 item in the row"}, "option__2": {"label": "2 items in the row"}, "option__3": {"label": "3 items in the row"}, "option__4": {"label": "4 items in the row"}}, "autoplay": {"label": "Autoplay"}, "speed": {"label": "Autoplay speed (sec)"}, "arrows": {"label": "Arrows"}}, "blocks": {"slide": {"name": "Slide", "settings": {"title": {"label": "Title"}, "stars": {"label": "Stars"}, "paragraph": {"label": "Paragraph"}, "user_image": {"label": "User image"}, "user_text_line_1": {"label": "User text line #1"}, "user_text_line_2": {"label": "User text line #2"}, "header__1": {"content": "Support"}, "paragraph__1": {"content": "[Watch video tutorials](https://www.youtube.com/@shopifymise1354)"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}}}, "custom_liquid": {"name": "Custom Liquid", "settings": {"use_container": {"label": "Use container"}, "custom_liquid": {"label": "Custom Liquid", "info": "Add app snippets or other Liquid code to create advanced customizations."}}}, "footer": {"name": "Footer", "settings": {"type": {"label": "Type", "option__1": {"label": "Type #1"}, "option__2": {"label": "Type #2"}, "option__3": {"label": "Type #3"}, "option__4": {"label": "Type #4"}, "option__5": {"label": "Type #5"}, "option__6": {"label": "Type #6"}, "option__7": {"label": "Type #7"}}, "style": {"label": "Colorize style", "info": "You can colorize the section in detail. To do this, add a block to \"Add block -> Colorize\"", "option__1": {"label": "Style #1"}, "option__2": {"label": "Style #2"}}, "show_logo": {"label": "Show logo"}, "show_button_back_to_top": {"label": "Show button 'Back to top'"}, "enable_fixed": {"label": "Enable fixed footer"}, "header__1": {"content": "Follow on Shop"}, "undefined": {"info": "To allow customers to follow your store on the Shop app from your storefront, Shop Pay must be enabled. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "enable_follow_on_shop": {"label": "Enable Follow on Shop"}, "header__2": {"content": "Support"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}, "blocks": {"menu": {"name": "<PERSON><PERSON>", "settings": {"title": {"label": "Title"}, "show_title_on_desktop": {"label": "Show title on the desktop"}, "menu": {"label": "<PERSON><PERSON>"}, "size": {"label": "Column size", "info": "0 - is auto size"}, "rows_limit": {"label": "Rows limit", "info": "0 - disable"}}}, "custom_html": {"name": "Custom html", "settings": {"title": {"label": "Title"}, "show_title_on_desktop": {"label": "Show title on the desktop"}, "page_content": {"label": "Page content", "info": "Default page: 'Include Footer Custom HTML'"}}}, "social_media": {"name": "Social media", "settings": {"title": {"label": "Title"}, "show_facebook": {"label": "Show facebook"}, "show_twitter": {"label": "Show twitter"}, "show_instagram": {"label": "Show instagram"}, "show_pinterest": {"label": "Show pinterest"}, "show_youtube": {"label": "Show youtube"}, "show_behance": {"label": "Show behance"}, "show_skype": {"label": "Show skype"}, "show_tiktok": {"label": "Show tiktok"}}}, "subscription": {"name": "Subscription", "settings": {"title": {"label": "Title"}, "paragraph": {"label": "Paragraph"}, "placeholder": {"label": "Placeholder"}, "button_text": {"label": "Button text"}}}, "copyright": {"name": "Copyright", "settings": {"page_content": {"label": "Page content", "info": "Default page: 'Include Footer Copyright'"}}}, "payments": {"name": "Payments"}, "colorize": {"name": "Colorize", "settings": {"bg": {"label": "Background"}, "bd": {"label": "Border"}, "c": {"label": "Text"}, "subscription_btn_type": {"label": "Search button type", "option__1": {"label": "Auto"}, "option__2": {"label": "<PERSON><PERSON><PERSON>"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Secondary"}, "option__5": {"label": "Clean"}}, "links_c": {"label": "Links"}, "links_h_c": {"label": "Hover links"}, "title_c": {"label": "Title"}, "icons_c": {"label": "Icons"}, "icons_h_c": {"label": "Hover icons"}}}}}, "header": {"name": "Header", "settings": {"type": {"label": "Header type", "option__1": {"label": "Type #1"}, "option__2": {"label": "Type #2"}, "option__3": {"label": "Type #3"}, "option__4": {"label": "Type #4"}, "option__5": {"label": "Type #5 (Has vertical menu)"}, "option__6": {"label": "Type #6"}, "option__7": {"label": "Type #7 (Has vertical menu)"}}, "style": {"label": "Colorize style", "info": "You can colorize the section in detail. To do this, add a block to \"Add block -> Colorize\"", "option__1": {"label": "Style #1"}, "option__2": {"label": "Style #2"}, "option__3": {"label": "Style #3"}, "option__4": {"label": "Style #4"}}, "transparent_bg": {"label": "Transparent background", "option__1": {"label": "Disable"}, "option__2": {"label": "Transparent"}, "option__3": {"label": "Transparent and colorize on hover"}}, "logo_mobile_centered": {"label": "Centered logo on mobile", "info": "Check your icons on mobile to make sure they don't overlap with the centered logo as centering doesn't check the position of the icons"}, "height": {"label": "Top line height (only desktop)", "info": "0 - auto"}, "items_padding": {"label": "Horizontal padding of items", "info": "0 - auto"}, "header__1": {"content": "Main menu"}, "menu": {"label": "Select menu"}, "show_menu_arrows": {"label": "Show arrows on first levels"}, "show_menu_hover_underline": {"label": "Show underline on item hover"}, "header__2": {"content": "Vertical menu"}, "paragraph__1": {"content": "Header types #5, #7"}, "vertical_menu": {"label": "Select vertical menu"}, "fixed_vertical_menu": {"label": "Fixed vertical menu on home page", "info": "If the option is enabled, you can add 'Space for vertical menu' block in the 'Builder' section"}, "header__3": {"content": "Content"}, "paragraph__2": {"content": "Header types #5, #7"}, "additional_line_info_page_content": {"label": "Top line content", "info": "Default page: 'Include Header Info Line #4'"}, "header__4": {"content": "Country/region selector"}, "undefined": {"info": "To add a country/region, go to your [payment settings.](/admin/settings/payments)"}, "enable_country_selector": {"label": "Enable country/region selector"}, "show_country_name": {"label": "Show country name"}, "country_selector_max_width": {"label": "Select maximum width", "info": "0 - disable width limit"}, "header__5": {"content": "Sticky header"}, "sticky": {"label": "Sticky state", "option__1": {"label": "Disable"}, "option__2": {"label": "Desktop and mobile"}, "option__3": {"label": "Only desktop"}, "option__4": {"label": "Only mobile"}}, "desktop_sticky_type": {"label": "Sticky type (Desktop)", "option__1": {"label": "Wide (Full header)"}, "option__2": {"label": "<PERSON> (Menu line)"}}, "hide_sticky_when_scroll_down": {"label": "Hide sticks header when scrolling down"}, "header__6": {"content": "Support"}, "paragraph__3": {"content": "[Watch video tutorials](https://www.youtube.com/@shopifymise1354)"}, "paragraph__4": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}, "blocks": {"megamenu_title_image": {"name": "Title images", "settings": {"paragraph__1": {"content": "Only for second level!"}, "for_item": {"label": "For item #1", "info": "Example: Women's+Clothing"}, "image": {"label": "Image #1"}, "image_size": {"label": "Image size #1", "info": "These are the fields for image quality. If images lazy loading is on, the option is ignored"}, "for_item_2": {"label": "For item #2"}, "image_2": {"label": "Image #2"}, "image_size_2": {"label": "Image size #2"}, "for_item_3": {"label": "For item #3"}, "image_3": {"label": "Image #3"}, "image_size_3": {"label": "Image size #3"}, "for_item_4": {"label": "For item #4"}, "image_4": {"label": "Image #4"}, "image_size_4": {"label": "Image size #4"}, "for_item_5": {"label": "For item #5"}, "image_5": {"label": "Image #5"}, "image_size_5": {"label": "Image size #5"}}}, "megamenu_label": {"name": "Labels", "settings": {"type": {"label": "Type", "option__1": {"label": "Hot"}, "option__2": {"label": "Sale"}, "option__3": {"label": "New"}}, "text": {"label": "Text"}, "for_item": {"label": "For item #1", "info": "Example: Men's+Shoes+Flats"}, "for_item_2": {"label": "For item #2"}, "for_item_3": {"label": "For item #3"}, "for_item_4": {"label": "For item #4"}, "for_item_5": {"label": "For item #5"}, "for_item_6": {"label": "For item #6"}, "for_item_7": {"label": "For item #7"}, "for_item_8": {"label": "For item #8"}, "for_item_9": {"label": "For item #9"}, "for_item_10": {"label": "For item #10"}, "for_item_11": {"label": "For item #11"}, "for_item_12": {"label": "For item #12"}, "for_item_13": {"label": "For item #13"}, "for_item_14": {"label": "For item #14"}, "for_item_15": {"label": "For item #15"}}}, "icon": {"name": "Icons (For first level)", "settings": {"paragraph__1": {"content": "The example for 'Icon #' fields is 'icon-theme-050'."}, "for_vertical": {"label": "For vertical menu"}, "for_item_1": {"label": "For item #1", "info": "Example: 'Men's'"}, "icon_1": {"label": "Icon #1", "info": "Example: 'icon-theme-050'"}, "for_item_2": {"label": "For item #2"}, "icon_2": {"label": "Icon #2"}, "for_item_3": {"label": "For item #3"}, "icon_3": {"label": "Icon #3"}, "for_item_4": {"label": "For item #4"}, "icon_4": {"label": "Icon #4"}, "for_item_5": {"label": "For item #5"}, "icon_5": {"label": "Icon #5"}, "for_item_6": {"label": "For item #6"}, "icon_6": {"label": "Icon #6"}, "for_item_7": {"label": "For item #7"}, "icon_7": {"label": "Icon #7"}, "for_item_8": {"label": "For item #8"}, "icon_8": {"label": "Icon #8"}, "for_item_9": {"label": "For item #9"}, "icon_9": {"label": "Icon #9"}, "for_item_10": {"label": "For item #10"}, "icon_10": {"label": "Icon #10"}, "for_item_11": {"label": "For item #11"}, "icon_11": {"label": "Icon #11"}, "for_item_12": {"label": "For item #12"}, "icon_12": {"label": "Icon #12"}, "for_item_13": {"label": "For item #13"}, "icon_13": {"label": "Icon #13"}, "for_item_14": {"label": "For item #14"}, "icon_14": {"label": "Icon #14"}, "for_item_15": {"label": "For item #15"}, "icon_15": {"label": "Icon #15"}, "for_item_16": {"label": "For item #16"}, "icon_16": {"label": "Icon #16"}, "for_item_17": {"label": "For item #17"}, "icon_17": {"label": "Icon #17"}, "for_item_18": {"label": "For item #18"}, "icon_18": {"label": "Icon #18"}, "for_item_19": {"label": "For item #19"}, "icon_19": {"label": "Icon #19"}, "for_item_20": {"label": "For item #20"}, "icon_20": {"label": "Icon #20"}}}, "info_line": {"name": "Info line", "settings": {"show_on_mobile": {"label": "Show on mobile"}, "style": {"label": "Colorize style", "option__1": {"label": "Style #1"}, "option__2": {"label": "Style #2"}}, "transparent_bg": {"label": "Enable transparent background"}, "page_content": {"label": "Page content", "info": "Default page: 'Include Header Info Line'"}, "show_social_media": {"label": "Show social media"}, "header__1": {"content": "Media settings"}, "show_facebook": {"label": "Show facebook"}, "show_twitter": {"label": "Show twitter"}, "show_instagram": {"label": "Show instagram"}, "show_pinterest": {"label": "Show pinterest"}, "show_youtube": {"label": "Show youtube"}, "show_behance": {"label": "Show behance"}, "show_skype": {"label": "Show skype"}, "show_line": {"label": "LINE"}, "show_tiktok": {"label": "Show tiktok"}}}, "tape": {"name": "Announcement bar", "settings": {"delay": {"label": "Delayed appearance"}, "style": {"label": "Colorize style", "option__1": {"label": "Style #1"}, "option__2": {"label": "Style #2"}, "option__3": {"label": "Style #3"}}, "transparent_bg": {"label": "Enable transparent background"}, "content": {"label": "Text or HTML content"}}}, "megamenu": {"name": "Mega menu", "settings": {"header__1": {"content": "General"}, "for_item": {"label": "For item"}, "grid": {"label": "Links column size"}, "second_level_column": {"label": "Second level is a column"}, "second_level_column_size": {"label": "Column lines limit"}, "wrap": {"label": "Wrap menu columns to grid"}, "wrapper_grid": {"label": "Wrapper column size"}, "header__2": {"content": "Additional content on the left"}, "column_left_type": {"label": "Content type", "option__1": {"label": "None"}, "option__2": {"label": "1 item in the row"}, "option__3": {"label": "1 item in 2 rows"}, "option__4": {"label": "2 items in the row"}, "option__5": {"label": "2 items in 2 rows"}, "option__6": {"label": "3 items in the row"}, "option__7": {"label": "3 items in 2 rows"}, "option__8": {"label": "4 items in the row"}, "option__9": {"label": "4 items in 2 rows"}, "option__10": {"label": "2/3 & 1/3 columns"}}, "column_left_size": {"label": "Column size", "info": "0 - auto"}, "header__3": {"content": "Additional content on the right"}, "column_right_type": {"label": "Content type", "option__1": {"label": "None"}, "option__2": {"label": "1 item in the row"}, "option__3": {"label": "1 item in 2 rows"}, "option__4": {"label": "2 items in the row"}, "option__5": {"label": "2 items in 2 rows"}, "option__6": {"label": "3 items in the row"}, "option__7": {"label": "3 items in 2 rows"}, "option__8": {"label": "4 items in the row"}, "option__9": {"label": "4 items in 2 rows"}, "option__10": {"label": "8/12 & 4/12 columns"}}, "column_right_size": {"label": "Column size", "info": "0 - auto"}, "header__4": {"content": "Vertical menu only (<PERSON><PERSON> #5)"}, "enable_width_limit": {"label": "Enable width limit", "info": "Automatically limit the edge of the page container"}, "width_limit": {"label": "Width limit"}}}, "megamenu_promobox": {"name": "Mega -> Promo box", "settings": {"title": {"label": "Title"}, "header__1": {"content": "General"}, "url": {"label": "URL for banner"}, "header__2": {"content": "Image"}, "image": {"label": "Image", "info": "Recommended size 1440x550 pixels"}, "image_size": {"label": "Image size", "info": "If images lazy loading is on, the option is ignored"}, "color_image_mask": {"label": "Image mask"}, "image_mask_opacity": {"label": "Image mask opacity"}, "header__3": {"content": "Text"}, "paragraph__1": {"content": "Delete the line value for the text or button if you want to hide the element"}, "paragraph__2": {"content": "Use '<br>' for the line break"}, "text_line_1": {"label": "Text line #1"}, "text_line_2": {"label": "Text line #2"}, "text_line_3": {"label": "Text line #3"}, "style": {"label": "Colorize style", "option__1": {"label": "Style #1"}, "option__2": {"label": "Style #2"}, "option__3": {"label": "Style #3"}, "option__4": {"label": "Style #4"}, "option__5": {"label": "Style #5"}, "option__6": {"label": "Style #6"}, "option__7": {"label": "Style #7"}, "option__8": {"label": "Style #8"}}, "header__4": {"content": "Button #1"}, "button_1": {"label": "Button text"}, "button_1_url": {"label": "URL for the button"}, "color_button_type_1": {"label": "Button style", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Secondary"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Clean"}, "option__5": {"label": "Transparent default"}, "option__6": {"label": "Transparent secondary"}, "option__7": {"label": "Transparent invert"}, "option__8": {"label": "Transparent clean"}}, "header__5": {"content": "Button #2"}, "button_2": {"label": "Button text"}, "button_2_url": {"label": "URL for the button"}, "color_button_type_2": {"label": "Button style", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Secondary"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Clean"}, "option__5": {"label": "Transparent default"}, "option__6": {"label": "Transparent secondary"}, "option__7": {"label": "Transparent invert"}, "option__8": {"label": "Transparent clean"}}, "header__6": {"content": "Other content"}, "list_1": {"label": "List", "info": "Select page with list"}, "custom_html": {"label": "Custom HTML", "info": "Replace the whole text content to the page content"}, "header__7": {"content": "Layout"}, "type": {"label": "Type", "option__1": {"label": "Clean (Image without text)"}, "option__2": {"label": "Clean with border"}, "option__3": {"label": "Clean with border on hover"}, "option__4": {"label": "Text v1"}, "option__5": {"label": "Text v2"}, "option__6": {"label": "Text v3"}, "option__7": {"label": "Text v4"}, "option__8": {"label": "Text v5"}, "option__9": {"label": "Text v6"}, "option__10": {"label": "Text v7"}, "option__11": {"label": "Text v8"}, "option__12": {"label": "Text v9"}, "option__13": {"label": "Text v10"}, "option__14": {"label": "Type #1 v1 (Text over the image)"}, "option__15": {"label": "Type #1 v2"}, "option__16": {"label": "Type #1 v3"}, "option__17": {"label": "Type #1 v4"}, "option__18": {"label": "Type #1 v5"}, "option__19": {"label": "Type #1 v6"}, "option__20": {"label": "Type #1 v7"}, "option__21": {"label": "Type #1 v8"}, "option__22": {"label": "Type #1 v9"}, "option__23": {"label": "Type #1 v10"}, "option__24": {"label": "Type #1 v11"}, "option__25": {"label": "Type #1 v12"}, "option__26": {"label": "Type #1 v13"}, "option__27": {"label": "Type #1 v14"}, "option__28": {"label": "Type #1 v15"}, "option__29": {"label": "Type #1 v16"}, "option__30": {"label": "Type #1 v17"}, "option__31": {"label": "Type #1 with Background v1"}, "option__32": {"label": "Type #1 with Background v2"}, "option__33": {"label": "Type #1 with Background v3"}, "option__34": {"label": "Type #1 with Background v4"}, "option__35": {"label": "Type #1 with <PERSON><PERSON>ain v1"}, "option__36": {"label": "Type #1 with <PERSON><PERSON><PERSON> v2"}, "option__37": {"label": "Type #1 with <PERSON><PERSON><PERSON> v3"}, "option__38": {"label": "Type #2 v1 (Text below the image)"}, "option__39": {"label": "Type #2 v2"}, "option__40": {"label": "Type #2 v3"}, "option__41": {"label": "Type #2 v4"}, "option__42": {"label": "Type #2 v5"}, "option__43": {"label": "Type #2 v6"}, "option__44": {"label": "Type #2 v7"}, "option__45": {"label": "Type #2 v8"}, "option__46": {"label": "Type #2 v9"}, "option__47": {"label": "Type #2 v10"}, "option__48": {"label": "Type #2 v11"}, "option__49": {"label": "Type #2 v12"}, "option__50": {"label": "Type #2 v13"}, "option__51": {"label": "Type #3 (Ribbon bottom the image)"}, "option__52": {"label": "Type #4 (Animation ribbon bottom the image)"}}, "content_position": {"label": "Content position (Only desktop)", "info": "Only for type #1 (Text over the image)", "option__1": {"label": "Center"}, "option__2": {"label": "Center & Left"}, "option__3": {"label": "Center & Right"}, "option__4": {"label": "Top & Center"}, "option__5": {"label": "Top & Left"}, "option__6": {"label": "Top & Right"}, "option__7": {"label": "Bottom & Center"}, "option__8": {"label": "Bottom & Left"}, "option__9": {"label": "Bottom & Right"}}, "content_align": {"label": "Content horizontal align", "option__1": {"label": "Left"}, "option__2": {"label": "Center"}, "option__3": {"label": "Right"}}, "header__8": {"content": "Animation"}, "animation_to": {"label": "Move image on hover to", "option__1": {"label": "None"}, "option__2": {"label": "Center"}, "option__3": {"label": "Top left"}, "option__4": {"label": "Top"}, "option__5": {"label": "Top right"}, "option__6": {"label": "Right"}, "option__7": {"label": "Bottom right"}, "option__8": {"label": "Bottom"}, "option__9": {"label": "Bottom left"}, "option__10": {"label": "Left"}}, "animation_from": {"label": "Static image position", "option__1": {"label": "None"}, "option__2": {"label": "Center"}, "option__3": {"label": "Top left"}, "option__4": {"label": "Top"}, "option__5": {"label": "Top right"}, "option__6": {"label": "Right"}, "option__7": {"label": "Bottom right"}, "option__8": {"label": "Bottom"}, "option__9": {"label": "Bottom left"}, "option__10": {"label": "Left"}}, "animation_opacity": {"label": "Image opacity", "option__1": {"label": "None"}, "option__2": {"label": "Static & hover"}, "option__3": {"label": "Static"}, "option__4": {"label": "Hover"}}, "header__9": {"content": "Customization"}, "customization_class": {"label": "Customization class", "info": "[Read user manual for adding extra classes](https://misell-manual.wraptas.site/)"}}}, "megamenu_brands": {"name": "Mega -> Brands", "settings": {"title": {"label": "Title"}, "url": {"label": "URL for title"}, "header__1": {"content": "General"}, "size_of_images": {"label": "Size of the images", "option__1": {"label": "1 image in the row"}, "option__2": {"label": "2 images in the row"}, "option__3": {"label": "3 images in the row"}, "option__4": {"label": "4 images in the row"}, "option__5": {"label": "5 images in the row"}, "option__6": {"label": "6 images in the row"}, "option__7": {"label": "12 images in the row"}}, "header__2": {"content": "Images"}, "image_1": {"label": "Image #1"}, "image_size_1": {"label": "Image size #1", "info": "If images lazy loading is on, the option is ignored"}, "url_1": {"label": "URL #1"}, "image_2": {"label": "Image #2"}, "image_size_2": {"label": "Image size #2"}, "url_2": {"label": "URL #2"}, "image_3": {"label": "Image #3"}, "image_size_3": {"label": "Image size #3"}, "url_3": {"label": "URL #3"}, "image_4": {"label": "Image #4"}, "image_size_4": {"label": "Image size #4"}, "url_4": {"label": "URL #4"}, "image_5": {"label": "Image #5"}, "image_size_5": {"label": "Image size #5"}, "url_5": {"label": "URL #5"}, "image_6": {"label": "Image #6"}, "image_size_6": {"label": "Image size #6"}, "url_6": {"label": "URL #6"}, "image_7": {"label": "Image #7"}, "image_size_7": {"label": "Image size #7"}, "url_7": {"label": "URL #7"}, "image_8": {"label": "Image #8"}, "image_size_8": {"label": "Image size #8"}, "url_8": {"label": "URL #8"}, "image_9": {"label": "Image #9"}, "image_size_9": {"label": "Image size #9"}, "url_9": {"label": "URL #9"}, "image_10": {"label": "Image #10"}, "image_size_10": {"label": "Image size #10"}, "url_10": {"label": "URL #10"}}}, "megamenu_products": {"name": "Mega -> Products", "settings": {"title": {"label": "Title"}, "header__1": {"content": "General"}, "collection": {"label": "Collection"}, "products_per_row": {"label": "Products per row", "option__1": {"label": "1"}, "option__2": {"label": "2"}, "option__3": {"label": "3"}, "option__4": {"label": "4"}, "option__5": {"label": "6"}, "option__6": {"label": "12"}}, "products_rows": {"label": "Rows"}}}, "megamenu_custom_html": {"name": "Mega -> Custom HTML", "settings": {"title": {"label": "Title"}, "url": {"label": "URL for title"}, "header__1": {"content": "General"}, "page_content": {"label": "Page content"}}}, "megamenu_subscription": {"name": "Mega -> Subscription", "settings": {"title": {"label": "Title"}, "url": {"label": "URL for title"}, "header__1": {"content": "General"}, "placeholder": {"label": "Placeholder"}, "button_text": {"label": "Button text"}}}, "menu": {"name": "Advanced menu (4 levels)", "settings": {"for_item": {"label": "For item", "info": "Example: Shop"}, "menu": {"label": "<PERSON><PERSON>"}}}, "colorize": {"name": "Colorize", "settings": {"header__1": {"content": "First line"}, "line_2_bg": {"label": "Background"}, "line_2_bd": {"label": "Border"}, "btns_line_2_d_c": {"label": "Buttons"}, "btns_line_2_d_h_c": {"label": "Hover buttons"}, "header__2": {"content": "Second line"}, "paragraph__1": {"content": "Header types #1, #2, #5, #6, #7"}, "line_1_bg": {"label": "Background"}, "line_1_bd": {"label": "Border"}, "btns_line_1_d_c": {"label": "Buttons"}, "btns_line_1_d_h_c": {"label": "Hover buttons"}, "search_btn_type": {"label": "Search button type", "option__1": {"label": "Auto"}, "option__2": {"label": "<PERSON><PERSON><PERSON>"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Secondary"}, "option__5": {"label": "Clean"}}, "header__3": {"content": "Mobile"}, "m_bg": {"label": "Background"}, "m_bd": {"label": "Border"}, "btns_m_c": {"label": "Buttons"}, "header__4": {"content": "Sticky header"}, "menu_sticky_c": {"label": "<PERSON><PERSON>"}, "menu_sticky_h_c": {"label": "Hover menu"}, "btns_sticky_c": {"label": "Buttons"}, "btns_sticky_h_c": {"label": "Hover buttons"}, "header__5": {"content": "Third line"}, "paragraph__2": {"content": "Header types #5, #6, #7"}, "line_additional_bg": {"label": "Background"}, "line_additional_bd": {"label": "Border"}, "line_additional_c": {"label": "Text"}, "line_additional_highlighted_buttons_c": {"label": "Highlighted text & buttons"}, "line_additional_h_c": {"label": "Hover text"}, "header__6": {"content": "Info line block"}, "info_line_bg": {"label": "Background"}, "info_line_bd": {"label": "Border"}, "info_line_c": {"label": "Text"}, "info_line_media_c": {"label": "Social icons"}, "info_line_media_h_c": {"label": "Hover social icons"}, "header__7": {"content": "Announcement bar block"}, "tape_bg": {"label": "Background"}, "tape_bd": {"label": "Border"}, "tape_c": {"label": "Text"}, "tape_btn_close_c": {"label": "Button close"}, "header__8": {"content": "<PERSON><PERSON>"}, "menu_c": {"label": "Text"}, "menu_h_c": {"label": "Hover text"}, "menu_h_bg": {"label": "Hover background"}, "header__9": {"content": "Menu dropdown"}, "menu_title_c": {"label": "Title text"}, "menu_list_c": {"label": "List text"}, "menu_list_h_c": {"label": "List hover text"}, "menu_list_h_bg": {"label": "List hover background"}, "header__10": {"content": "Vertical menu"}, "menu_vertical_button_bg": {"label": "Button background"}, "menu_vertical_button_c": {"label": "Button text"}, "menu_vertical_d_bg": {"label": "Menu background"}, "menu_vertical_d_bd": {"label": "Menu border"}, "menu_vertical_d_c": {"label": "Menu text"}, "menu_vertical_h_c": {"label": "Menu hover text"}}}}}, "builder": {"name": "Builder", "settings": {"paragraph__1": {"content": "By adding blocks below you can add the following content: Promo box (Banner), Slick Slider (fastest), Revolution Slider, Instagram, Product column and custom HTML"}, "title": {"label": "Title"}, "container": {"label": "Content width", "option__1": {"label": "Fullwidth"}, "option__2": {"label": "Boxed"}}, "header__1": {"content": "Column settings"}, "size_of_columns": {"label": "Size of the columns", "option__1": {"label": "1 item in the row"}, "option__2": {"label": "2 items in the row"}, "option__3": {"label": "3 items in the row"}, "option__4": {"label": "4 items in the row"}, "option__5": {"label": "5 items in the row"}, "option__6": {"label": "6 items in the row"}}, "size_of_columns_mobile": {"label": "Size of the columns on mobile", "option__1": {"label": "1 item in the row"}, "option__2": {"label": "2 items in the row"}}, "margins_for_columns": {"label": "<PERSON><PERSON> for the columns", "info": "Quotation marks indicate the value on the desktop and mobile", "option__1": {"label": "None"}, "option__2": {"label": "Preset #1 (15px, 10px)"}, "option__3": {"label": "Preset #2 (60px, 45px)"}, "option__4": {"label": "Preset #3 (30px, 30px)"}, "option__5": {"label": "Preset #4 (45px, 30px)"}, "option__6": {"label": "Preset #5 (40px, 35px)"}}, "align_of_columns": {"label": "Align of the columns", "option__1": {"label": "Top & left"}, "option__2": {"label": "Top & center"}, "option__3": {"label": "Top & right"}, "option__4": {"label": "Center & left"}, "option__5": {"label": "Center"}, "option__6": {"label": "Center & right"}, "option__7": {"label": "Bottom & left"}, "option__8": {"label": "Bottom & center"}, "option__9": {"label": "Bottom & right"}, "option__10": {"label": "Stretch in height & left"}, "option__11": {"label": "Stretch in height & center"}, "option__12": {"label": "Stretch in height & right"}}, "disable_column_paddings": {"label": "Disable column paddings"}, "precedence": {"label": "Mobile content precedence", "info": "Example: 1,3,2"}, "header__2": {"content": "Customization"}, "customization_class": {"label": "Customization class", "info": "[Read user manual](https://misell-manual.wraptas.site/)"}, "header__3": {"content": "Support"}, "paragraph__2": {"content": "[Watch video tutorials](https://www.youtube.com/@shopifymise1354)"}, "paragraph__3": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}, "blocks": {"promobox": {"name": "Promo box (Banner)", "settings": {"header__1": {"content": "General"}, "url": {"label": "URL for banner"}, "header__2": {"content": "Image"}, "image": {"label": "Image", "info": "Recommended size 1440x550 pixels"}, "image_size": {"label": "Image size", "info": "These are the fields for image quality. If images lazy loading is on, the option is ignored"}, "image_mobile": {"label": "Mobile image", "info": "Recommended size 540x550 pixels"}, "image_mobile_size": {"label": "Mobile image size", "info": "These are the fields for image quality. If images lazy loading is on, the option is ignored"}, "image_position_x": {"label": "Horizontal position for the mobile image", "info": "50% - center, 0% - move to the left, 100% - move to the right. Disabled with the auto image adaptation height"}, "color_image_mask": {"label": "Image mask"}, "image_mask_opacity": {"label": "Image mask opacity"}, "header__3": {"content": "Text"}, "paragraph__1": {"content": "Delete the line value for the text or button if you want to hide the element"}, "paragraph__2": {"content": "Use '<br>' for the line break"}, "text_line_1": {"label": "Text line #1"}, "text_line_2": {"label": "Text line #2"}, "text_line_3": {"label": "Text line #3"}, "style": {"label": "Colorize style", "option__1": {"label": "Style #1"}, "option__2": {"label": "Style #2"}, "option__3": {"label": "Style #3"}, "option__4": {"label": "Style #4"}, "option__5": {"label": "Style #5"}, "option__6": {"label": "Style #6"}, "option__7": {"label": "Style #7"}, "option__8": {"label": "Style #8"}}, "header__4": {"content": "Button #1"}, "button_1": {"label": "Button text"}, "button_1_url": {"label": "URL for the button"}, "color_button_type_1": {"label": "Button style", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Secondary"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Clean"}, "option__5": {"label": "Transparent default"}, "option__6": {"label": "Transparent secondary"}, "option__7": {"label": "Transparent invert"}, "option__8": {"label": "Transparent clean"}}, "header__5": {"content": "Button #2"}, "button_2": {"label": "Button text"}, "button_2_url": {"label": "URL for the button"}, "color_button_type_2": {"label": "Button style", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Secondary"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Clean"}, "option__5": {"label": "Transparent default"}, "option__6": {"label": "Transparent secondary"}, "option__7": {"label": "Transparent invert"}, "option__8": {"label": "Transparent clean"}}, "header__6": {"content": "Other content"}, "list_1": {"label": "List", "info": "Select page with list"}, "custom_html": {"label": "Custom HTML", "info": "Replace the whole text content to the page content"}, "header__7": {"content": "Video"}, "video_external_url": {"label": "YouTube or Vimeo video URL"}, "video_mp4_url": {"label": "Video URL", "info": "Upload your video in Admin-> Settings-> Files and add a link here"}, "video_autoplay": {"label": "Video autoplay"}, "video_controls": {"label": "Video controls"}, "header__8": {"content": "Layout"}, "type": {"label": "Type", "option__1": {"label": "Clean (Image without text)"}, "option__2": {"label": "Clean with border"}, "option__3": {"label": "Clean with border on hover"}, "option__4": {"label": "Text v1"}, "option__5": {"label": "Text v2"}, "option__6": {"label": "Text v3"}, "option__7": {"label": "Text v4"}, "option__8": {"label": "Text v5"}, "option__9": {"label": "Text v6"}, "option__10": {"label": "Text v7"}, "option__11": {"label": "Text v8"}, "option__12": {"label": "Text v9"}, "option__13": {"label": "Text v10"}, "option__14": {"label": "Type #1 v1 (Text over the image)"}, "option__15": {"label": "Type #1 v2"}, "option__16": {"label": "Type #1 v3"}, "option__17": {"label": "Type #1 v4"}, "option__18": {"label": "Type #1 v5"}, "option__19": {"label": "Type #1 v6"}, "option__20": {"label": "Type #1 v7"}, "option__21": {"label": "Type #1 v8"}, "option__22": {"label": "Type #1 v9"}, "option__23": {"label": "Type #1 v10"}, "option__24": {"label": "Type #1 v11"}, "option__25": {"label": "Type #1 v12"}, "option__26": {"label": "Type #1 v13"}, "option__27": {"label": "Type #1 v14"}, "option__28": {"label": "Type #1 v15"}, "option__29": {"label": "Type #1 v16"}, "option__30": {"label": "Type #1 v17"}, "option__31": {"label": "Type #1 with Background v1"}, "option__32": {"label": "Type #1 with Background v2"}, "option__33": {"label": "Type #1 with Background v3"}, "option__34": {"label": "Type #1 with Background v4"}, "option__35": {"label": "Type #1 with <PERSON><PERSON>ain v1"}, "option__36": {"label": "Type #1 with <PERSON><PERSON><PERSON> v2"}, "option__37": {"label": "Type #1 with <PERSON><PERSON><PERSON> v3"}, "option__38": {"label": "Type #2 v1 (Text below the image)"}, "option__39": {"label": "Type #2 v2"}, "option__40": {"label": "Type #2 v3"}, "option__41": {"label": "Type #2 v4"}, "option__42": {"label": "Type #2 v5"}, "option__43": {"label": "Type #2 v6"}, "option__44": {"label": "Type #2 v7"}, "option__45": {"label": "Type #2 v8"}, "option__46": {"label": "Type #2 v9"}, "option__47": {"label": "Type #2 v10"}, "option__48": {"label": "Type #2 v11"}, "option__49": {"label": "Type #2 v12"}, "option__50": {"label": "Type #2 v13"}, "option__51": {"label": "Type #3 (Ribbon bottom the image)"}, "option__52": {"label": "Type #4 (Animation ribbon bottom the image)"}}, "content_position": {"label": "Content position (Only desktop)", "info": "Only for type #1 (Text over the image)", "option__1": {"label": "Center"}, "option__2": {"label": "Center & Left"}, "option__3": {"label": "Center & Right"}, "option__4": {"label": "Top & Center"}, "option__5": {"label": "Top & Left"}, "option__6": {"label": "Top & Right"}, "option__7": {"label": "Bottom & Center"}, "option__8": {"label": "Bottom & Left"}, "option__9": {"label": "Bottom & Right"}}, "content_align": {"label": "Content horizontal align", "option__1": {"label": "Left"}, "option__2": {"label": "Center"}, "option__3": {"label": "Right"}}, "add_container": {"label": "Add container", "info": "Only for type #1 (Text over the image)"}, "content_width": {"label": "Content width", "info": "0 - auto"}, "text_width": {"label": "Text max width", "info": "0 - auto. 1000px = 100%"}, "height": {"label": "Height", "option__1": {"label": "Auto adaptation to the image"}, "option__2": {"label": "Fullscreen"}, "option__3": {"label": "Fullscreen excluding header"}, "option__4": {"label": "Preset #1 (height 550px)"}, "option__5": {"label": "Preset #2 (height 690px)"}, "option__6": {"label": "Preset #3 (height 730px)"}, "option__7": {"label": "30% of width"}, "option__8": {"label": "40% of width"}, "option__9": {"label": "50% of width"}, "option__10": {"label": "60% of width"}, "option__11": {"label": "70% of width"}, "option__12": {"label": "80% of width"}, "option__13": {"label": "90% of width"}, "option__14": {"label": "100% of width (Square)"}, "option__15": {"label": "110% of width"}, "option__16": {"label": "120% of width"}, "option__17": {"label": "130% of width"}, "option__18": {"label": "140% of width"}, "option__19": {"label": "150% of width"}}, "min_height": {"label": "Min height", "info": "0 - auto detect"}, "size_of_column": {"label": "Size of the column", "option__1": {"label": "Auto"}, "option__2": {"label": "1 item in the row"}, "option__3": {"label": "3/4 from the line"}, "option__4": {"label": "2/3 from the line"}, "option__5": {"label": "7/12 from the line"}, "option__6": {"label": "1/2 from the line"}, "option__7": {"label": "5/12 from the line"}, "option__8": {"label": "1/3 from the line"}, "option__9": {"label": "1/4 from the line"}, "option__10": {"label": "1/6 from the line"}, "option__11": {"label": "1/12 from the line"}}, "visible": {"label": "Visible", "option__1": {"label": "Desktop & mobile"}, "option__2": {"label": "Only desktop"}, "option__3": {"label": "Only mobile"}}, "header__9": {"content": "Animation"}, "parallax": {"label": "Image parallax", "info": "If the block height is not in the automatic adaptation mode, then the strength of the parallax effect will be adjusted to the selected height. If the height is too large for the image, the parallax effect may be minimal or not manifested at all. Also \"Fixed\" option is not supported by IOS mobile devices.", "option__1": {"label": "Disable"}, "option__2": {"label": "To top"}, "option__3": {"label": "To bottom"}, "option__4": {"label": "Fixed"}}, "animation_to": {"label": "Move image on hover to", "option__1": {"label": "None"}, "option__2": {"label": "Center"}, "option__3": {"label": "Top left"}, "option__4": {"label": "Top"}, "option__5": {"label": "Top right"}, "option__6": {"label": "Right"}, "option__7": {"label": "Bottom right"}, "option__8": {"label": "Bottom"}, "option__9": {"label": "Bottom left"}, "option__10": {"label": "Left"}}, "animation_from": {"label": "Static image position", "option__1": {"label": "None"}, "option__2": {"label": "Center"}, "option__3": {"label": "Top left"}, "option__4": {"label": "Top"}, "option__5": {"label": "Top right"}, "option__6": {"label": "Right"}, "option__7": {"label": "Bottom right"}, "option__8": {"label": "Bottom"}, "option__9": {"label": "Bottom left"}, "option__10": {"label": "Left"}}, "animation_opacity": {"label": "Image opacity", "option__1": {"label": "None"}, "option__2": {"label": "Static & hover"}, "option__3": {"label": "Static"}, "option__4": {"label": "Hover"}}, "animation_text": {"label": "Text animation", "info": "Animated on page load. Only for type #1 (Text over the image).", "option__1": {"label": "None"}, "option__2": {"label": "Scale In"}, "option__3": {"label": "Scale Out"}, "option__4": {"label": "Move Top"}, "option__5": {"label": "Move Bottom"}, "option__6": {"label": "Move Left"}, "option__7": {"label": "Move Right"}}, "header__10": {"content": "Colorize"}, "color_text_1": {"label": "Text #1"}, "color_text_2": {"label": "Text #2"}, "color_text_3": {"label": "Text #3"}, "color_text_bg": {"label": "Text background"}, "color_curtain_bg": {"label": "Curtain background"}, "header__11": {"content": "Customization"}, "customization_class": {"label": "Customization class", "info": "[Read user manual for adding extra classes](https://misell-manual.wraptas.site/)"}}}, "slick_slider": {"name": "<PERSON><PERSON>lider", "settings": {"header__1": {"content": "Layout"}, "height": {"label": "Height", "option__1": {"label": "Auto adaptation to the image"}, "option__2": {"label": "Fullscreen"}, "option__3": {"label": "Fullscreen excluding header"}, "option__4": {"label": "Preset #1 (height 550px)"}, "option__5": {"label": "Preset #2 (height 690px)"}, "option__6": {"label": "Preset #3 (height 730px)"}, "option__7": {"label": "30% of width"}, "option__8": {"label": "40% of width"}, "option__9": {"label": "50% of width"}, "option__10": {"label": "60% of width"}, "option__11": {"label": "70% of width"}, "option__12": {"label": "80% of width"}, "option__13": {"label": "90% of width"}, "option__14": {"label": "100% of width (Square)"}, "option__15": {"label": "110% of width"}, "option__16": {"label": "120% of width"}, "option__17": {"label": "130% of width"}, "option__18": {"label": "140% of width"}, "option__19": {"label": "150% of width"}}, "min_height": {"label": "Min height", "info": "0 - auto detect"}, "size_of_column": {"label": "Size of the column", "option__1": {"label": "Auto"}, "option__2": {"label": "1 item in the row"}, "option__3": {"label": "3/4 from the line"}, "option__4": {"label": "2/3 from the line"}, "option__5": {"label": "7/12 from the line"}, "option__6": {"label": "1/2 from the line"}, "option__7": {"label": "5/12 from the line"}, "option__8": {"label": "1/3 from the line"}, "option__9": {"label": "1/4 from the line"}, "option__10": {"label": "1/6 from the line"}, "option__11": {"label": "1/12 from the line"}}, "arrows": {"label": "Arrows"}, "bullets": {"label": "Bullets"}, "speed": {"label": "Speed (seconds)"}}}, "slick_slide": {"name": "Slide for Slick", "settings": {"header__1": {"content": "General"}, "url": {"label": "URL for slide"}, "header__2": {"content": "Image"}, "image": {"label": "Image", "info": "Recommended size 1440x550 pixels"}, "image_size": {"label": "Image size", "info": "These are the fields for image quality. If images lazy loading is on, the option is ignored"}, "image_mobile": {"label": "Mobile image", "info": "Recommended size 540x550 pixels"}, "image_mobile_size": {"label": "Mobile image size", "info": "These are the fields for image quality. If images lazy loading is on, the option is ignored"}, "image_position_x": {"label": "Horizontal position for the mobile image", "info": "50% - center, 0% - move to the left, 100% - move to the right. Disabled with the auto image adaptation height"}, "color_image_mask": {"label": "Image mask"}, "image_mask_opacity": {"label": "Image mask opacity"}, "header__3": {"content": "Text"}, "paragraph__1": {"content": "Delete the line value for the text or button if you want to hide the element"}, "paragraph__2": {"content": "Use '<br>' for the line break"}, "text_line_1": {"label": "Text line #1"}, "text_line_2": {"label": "Text line #2"}, "text_line_3": {"label": "Text line #3"}, "style": {"label": "Colorize style", "option__1": {"label": "Style #1"}, "option__2": {"label": "Style #2"}, "option__3": {"label": "Style #3"}, "option__4": {"label": "Style #4"}, "option__5": {"label": "Style #5"}, "option__6": {"label": "Style #6"}, "option__7": {"label": "Style #7"}, "option__8": {"label": "Style #8"}}, "header__4": {"content": "Button #1"}, "button_1": {"label": "Button text"}, "button_1_url": {"label": "URL for the button"}, "color_button_type_1": {"label": "Button style", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Secondary"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Clean"}, "option__5": {"label": "Transparent default"}, "option__6": {"label": "Transparent secondary"}, "option__7": {"label": "Transparent invert"}, "option__8": {"label": "Transparent clean"}}, "header__5": {"content": "Button #2"}, "button_2": {"label": "Button text"}, "button_2_url": {"label": "URL for the button"}, "color_button_type_2": {"label": "Button style", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Secondary"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Clean"}, "option__5": {"label": "Transparent default"}, "option__6": {"label": "Transparent secondary"}, "option__7": {"label": "Transparent invert"}, "option__8": {"label": "Transparent clean"}}, "header__6": {"content": "Other content"}, "custom_html": {"label": "Custom HTML", "info": "Replace the whole text content to the page content"}, "header__7": {"content": "Video"}, "video_external_url": {"label": "Youtube or Vimeo video URL"}, "video_mp4_url": {"label": "Video URL", "info": "Upload your video in Admin-> Settings-> Files and add a link here"}, "video_autoplay": {"label": "Video autoplay"}, "video_controls": {"label": "Video controls"}, "header__8": {"content": "Layout"}, "type": {"label": "Type", "option__1": {"label": "Clean (Image without text)"}, "option__2": {"label": "Type #1 v1 (Text over the image)"}, "option__3": {"label": "Type #1 v2"}, "option__4": {"label": "Type #1 v3"}, "option__5": {"label": "Type #1 v4"}, "option__6": {"label": "Type #1 v5"}, "option__7": {"label": "Type #1 v6"}, "option__8": {"label": "Type #1 v7"}, "option__9": {"label": "Type #1 v8"}, "option__10": {"label": "Type #1 v9"}, "option__11": {"label": "Type #1 v10"}, "option__12": {"label": "Type #1 v11"}, "option__13": {"label": "Type #1 v12"}, "option__14": {"label": "Type #1 v13"}, "option__15": {"label": "Type #1 v14"}, "option__16": {"label": "Type #1 v15"}, "option__17": {"label": "Type #1 v16"}, "option__18": {"label": "Type #1 v17"}, "option__19": {"label": "Type #1 with Background v1"}, "option__20": {"label": "Type #1 with Background v2"}, "option__21": {"label": "Type #1 with Background v3"}, "option__22": {"label": "Type #1 with Background v4"}, "option__23": {"label": "Type #1 with <PERSON><PERSON>ain v1"}, "option__24": {"label": "Type #1 with <PERSON><PERSON><PERSON> v2"}, "option__25": {"label": "Type #1 with <PERSON><PERSON><PERSON> v3"}}, "content_position": {"label": "Content position on the desktop", "info": "Only for type #1 (Text over the image)", "option__1": {"label": "Center"}, "option__2": {"label": "Center & Left"}, "option__3": {"label": "Center & Right"}, "option__4": {"label": "Top & Center"}, "option__5": {"label": "Top & Left"}, "option__6": {"label": "Top & Right"}, "option__7": {"label": "Bottom & Center"}, "option__8": {"label": "Bottom & Left"}, "option__9": {"label": "Bottom & Right"}}, "content_align": {"label": "Content horizontal align", "option__1": {"label": "Left"}, "option__2": {"label": "Center"}, "option__3": {"label": "Right"}}, "add_container": {"label": "Add container", "info": "Only for type #1 (Text over the image)"}, "content_width": {"label": "Content max width"}, "text_width": {"label": "Text max width", "info": "0 - auto. 1000px = 100%"}, "header__9": {"content": "Animation"}, "animation_text": {"label": "Text animation", "info": "Animated on page load. Only for type #1 (Text over the image).", "option__1": {"label": "None"}, "option__2": {"label": "Scale In"}, "option__3": {"label": "Scale Out"}, "option__4": {"label": "Move Top"}, "option__5": {"label": "Move Bottom"}, "option__6": {"label": "Move Left"}, "option__7": {"label": "Move Right"}}, "header__10": {"content": "Colorize"}, "color_text_1": {"label": "Text #1"}, "color_text_2": {"label": "Text #2"}, "color_text_3": {"label": "Text #3"}, "color_text_bg": {"label": "Text background"}, "color_curtain_bg": {"label": "Curtain background"}, "header__11": {"content": "Customization"}, "customization_class": {"label": "Customization class", "info": "[Read user manual for adding extra classes](https://misell-manual.wraptas.site/)"}}}, "revolution_slider": {"name": "Revolution Slider", "settings": {"header__1": {"content": "Layout"}, "height": {"label": "Height", "option__1": {"label": "Preset #1 (height 550px)"}, "option__2": {"label": "Fullscreen"}, "option__3": {"label": "Fullscreen excluding header"}, "option__4": {"label": "Preset #2 (height 690px)"}, "option__5": {"label": "Preset #3 (height 730px)"}}, "min_height": {"label": "Min height", "info": "0 - auto detect"}, "image_height": {"label": "Image height size"}, "size_of_column": {"label": "Size of the column", "option__1": {"label": "Auto"}, "option__2": {"label": "1 item in the row"}, "option__3": {"label": "3/4 from the line"}, "option__4": {"label": "2/3 from the line"}, "option__5": {"label": "7/12 from the line"}, "option__6": {"label": "1/2 from the line"}, "option__7": {"label": "5/12 from the line"}, "option__8": {"label": "1/3 from the line"}, "option__9": {"label": "1/4 from the line"}, "option__10": {"label": "1/6 from the line"}, "option__11": {"label": "1/12 from the line"}}, "arrows": {"label": "Arrows"}, "bullets": {"label": "Bullets"}, "delay": {"label": "Delay (seconds)"}, "image_parallax": {"label": "Parallax of the image"}, "content_parallax": {"label": "Parallax of the content"}, "preload_spacer": {"label": "Preload spacer", "info": "Remove slider jump on page load"}}}, "revolution_slide": {"name": "Slide for Revolution", "settings": {"header__1": {"content": "General"}, "url": {"label": "URL for slide"}, "header__2": {"content": "Image"}, "image": {"label": "Image", "info": "Recommended size 1440x550 pixels"}, "image_position_x": {"label": "Horizontal position for the mobile image", "info": "50% - center, 0% - move to the left, 100% - move to the right. Disabled with the auto image adaptation height"}, "header__3": {"content": "Text"}, "paragraph__1": {"content": "Delete the line value for the text or button if you want to hide the element"}, "paragraph__2": {"content": "Use '<br>' for the line break"}, "text_line_1": {"label": "Text line #1"}, "text_line_2": {"label": "Text line #2"}, "text_line_3": {"label": "Text line #3"}, "style": {"label": "Colorize style", "option__1": {"label": "Style #1"}, "option__2": {"label": "Style #2"}, "option__3": {"label": "Style #3"}, "option__4": {"label": "Style #4"}, "option__5": {"label": "Style #5"}, "option__6": {"label": "Style #6"}, "option__7": {"label": "Style #7"}, "option__8": {"label": "Style #8"}}, "header__4": {"content": "Button #1"}, "button_1": {"label": "Button text"}, "button_1_url": {"label": "URL for the button"}, "color_button_type_1": {"label": "Button style", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Secondary"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Clean"}, "option__5": {"label": "Transparent default"}, "option__6": {"label": "Transparent secondary"}, "option__7": {"label": "Transparent invert"}, "option__8": {"label": "Transparent clean"}}, "header__5": {"content": "Button #2"}, "button_2": {"label": "Button text"}, "button_2_url": {"label": "URL for the button"}, "color_button_type_2": {"label": "Button style", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Secondary"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Clean"}, "option__5": {"label": "Transparent default"}, "option__6": {"label": "Transparent secondary"}, "option__7": {"label": "Transparent invert"}, "option__8": {"label": "Transparent clean"}}, "header__6": {"content": "Other content"}, "custom_html": {"label": "Custom HTML", "info": "Replace the whole text content to the page content"}, "header__7": {"content": "Video"}, "video_external_url": {"label": "Youtube or Vimeo video URL"}, "video_mp4_url": {"label": "Video URL", "info": "Upload your video in Admin-> Settings-> Files and add a link here"}, "video_autoplay": {"label": "Video autoplay"}, "video_controls": {"label": "Video controls"}, "header__8": {"content": "Layout"}, "type": {"label": "Type", "option__1": {"label": "Clean (Image without text)"}, "option__2": {"label": "Type #1 v1 (Text over the image)"}, "option__3": {"label": "Type #1 v2"}, "option__4": {"label": "Type #1 v3"}, "option__5": {"label": "Type #1 v4"}, "option__6": {"label": "Type #1 v5"}, "option__7": {"label": "Type #1 v6"}, "option__8": {"label": "Type #1 v7"}, "option__9": {"label": "Type #1 v8"}, "option__10": {"label": "Type #1 v9"}, "option__11": {"label": "Type #1 v10"}, "option__12": {"label": "Type #1 v11"}, "option__13": {"label": "Type #1 v12"}, "option__14": {"label": "Type #1 v13"}, "option__15": {"label": "Type #1 v14"}, "option__16": {"label": "Type #1 v15"}, "option__17": {"label": "Type #1 v16"}, "option__18": {"label": "Type #1 v17"}, "option__19": {"label": "Type #1 with Background v1"}, "option__20": {"label": "Type #1 with Background v2"}, "option__21": {"label": "Type #1 with Background v3"}, "option__22": {"label": "Type #1 with Background v4"}, "option__23": {"label": "Type #1 with <PERSON><PERSON>ain v1"}, "option__24": {"label": "Type #1 with <PERSON><PERSON><PERSON> v2"}, "option__25": {"label": "Type #1 with <PERSON><PERSON><PERSON> v3"}}, "content_position": {"label": "Content position on the desktop", "info": "Only for type #1 (Text over the image)", "option__1": {"label": "Center"}, "option__2": {"label": "Center & Left"}, "option__3": {"label": "Center & Right"}, "option__4": {"label": "Top & Center"}, "option__5": {"label": "Top & Left"}, "option__6": {"label": "Top & Right"}, "option__7": {"label": "Bottom & Center"}, "option__8": {"label": "Bottom & Left"}, "option__9": {"label": "Bottom & Right"}}, "content_align": {"label": "Content horizontal align", "option__1": {"label": "Left"}, "option__2": {"label": "Center"}, "option__3": {"label": "Right"}}, "add_container": {"label": "Add container", "info": "Only for type #1 (Text over the image)"}, "content_width": {"label": "Content width", "info": "0 - auto"}, "text_width": {"label": "Text max width", "info": "0 - auto. 1000px = 100%"}, "header__9": {"content": "Effects"}, "delay": {"label": "Delay", "info": "0 is disabled personal slide delay"}, "slide_animation": {"label": "Slide animation", "info": "Take animation names in [Documentation](https://www.themepunch.com/revsliderjquery-doc/slide-transitions/)"}, "html_animation": {"label": "HTML layer animation", "info": "Take animation presets in [Documentation](https://www.themepunch.com/revsliderjquery-doc/layer-transitions/)"}, "header__10": {"content": "Customization"}, "customization_class": {"label": "Customization class", "info": "[Read user manual for adding extra classes](https://misell-manual.wraptas.site/)"}}}, "instagram": {"name": "Instagram", "settings": {"header__1": {"content": "General"}, "limit": {"label": "Images limit", "info": "User name limit - 12 images, tag limit - no limit"}, "size_of_images": {"label": "Amount of the images", "option__1": {"label": "1 image in the row"}, "option__2": {"label": "2 images in the row"}, "option__3": {"label": "3 images in the row"}, "option__4": {"label": "4 images in the row"}, "option__5": {"label": "5 images in the row"}, "option__6": {"label": "6 images in the row"}, "option__7": {"label": "6(4 on the mobile) images in the row"}, "option__8": {"label": "12 images in the row"}}, "inner_disable_paddings": {"label": "Disable paddings"}, "fixed_height": {"label": "Height", "info": "0 - auto. 100 - square image. Less then 100 - landscape. More then 100 - portrait."}, "disable_lazyload": {"label": "Disable lazy load"}, "header__2": {"content": "Layout"}, "size_of_column": {"label": "Size of the column", "option__1": {"label": "Auto"}, "option__2": {"label": "1 item in the row"}, "option__3": {"label": "3/4 from the line"}, "option__4": {"label": "2/3 from the line"}, "option__5": {"label": "7/12 from the line"}, "option__6": {"label": "1/2 from the line"}, "option__7": {"label": "5/12 from the line"}, "option__8": {"label": "1/3 from the line"}, "option__9": {"label": "1/4 from the line"}, "option__10": {"label": "1/6 from the line"}, "option__11": {"label": "1/12 from the line"}}}}, "instagram_block": {"name": "Instagram block", "settings": {"image": {"label": "Image"}, "image_size": {"label": "Image size", "info": "These are the fields for image quality. If images lazy loading is on, the option is ignored"}, "format_pjpg": {"label": "Enable format 'PJPG'", "info": "Don't work with transparent images"}, "disable_lazyload": {"label": "Disable lazy load"}, "url": {"label": "URL"}}}, "products": {"name": "Product column", "settings": {"header__1": {"content": "General"}, "title": {"label": "Title"}, "collection": {"label": "Collection"}, "max_products": {"label": "Maximum products in the column"}, "header__2": {"content": "Layout"}, "size_of_column": {"label": "Size of the column", "option__1": {"label": "Auto"}, "option__2": {"label": "1 item in the row"}, "option__3": {"label": "3/4 from the line"}, "option__4": {"label": "2/3 from the line"}, "option__5": {"label": "7/12 from the line"}, "option__6": {"label": "1/2 from the line"}, "option__7": {"label": "5/12 from the line"}, "option__8": {"label": "1/3 from the line"}, "option__9": {"label": "1/4 from the line"}, "option__10": {"label": "1/6 from the line"}, "option__11": {"label": "1/12 from the line"}}}}, "custom_html": {"name": "Custom HTML", "settings": {"header__1": {"content": "General"}, "page_content": {"label": "Page content"}, "header__2": {"content": "Layout"}, "size_of_column": {"label": "Size of the column", "option__1": {"label": "Auto"}, "option__2": {"label": "1 item in the row"}, "option__3": {"label": "3/4 from the line"}, "option__4": {"label": "2/3 from the line"}, "option__5": {"label": "7/12 from the line"}, "option__6": {"label": "1/2 from the line"}, "option__7": {"label": "5/12 from the line"}, "option__8": {"label": "1/3 from the line"}, "option__9": {"label": "1/4 from the line"}, "option__10": {"label": "1/6 from the line"}, "option__11": {"label": "1/12 from the line"}}}}, "vertical_menu_spacer": {"name": "Space for vertical menu", "settings": {"menu": {"label": "<PERSON><PERSON>"}}}}}, "information_line": {"name": "Information line", "settings": {"title": {"label": "Title"}, "type": {"label": "Type", "option__1": {"label": "Type #1"}, "option__2": {"label": "Type #2"}}, "style": {"label": "Colorize style", "option__1": {"label": "Style #1"}, "option__2": {"label": "Style #2"}, "option__3": {"label": "Style #3"}, "option__4": {"label": "Style #4"}, "option__5": {"label": "Style #5"}}, "header__1": {"content": "Support"}, "paragraph__1": {"content": "[Watch video tutorials](https://www.youtube.com/@shopifymise1354)"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}, "blocks": {"content": {"name": "Content", "settings": {"icon": {"label": "Icon snippet name"}, "title": {"label": "Title"}, "paragraph": {"label": "Paragraph"}, "url": {"label": "URL"}}}}}, "lookbook": {"name": "Lookbook", "settings": {"show_title": {"label": "Show title"}, "layout": {"label": "Layout", "option__1": {"label": "1"}, "option__2": {"label": "2"}}, "grid": {"label": "Products per row (Extra large)", "option__1": {"label": "2"}, "option__2": {"label": "3"}, "option__3": {"label": "4"}}, "grid_responsive": {"label": "Responsive size", "info": "Example is '12,12,12,12,12', 1 < value < 12"}, "image_size": {"label": "Image size"}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image"}}}, "product": {"name": "Product", "settings": {"product": {"label": "Product"}, "image": {"label": "Product image"}, "title": {"label": "Product title"}, "price": {"label": "Product price", "info": "Example: 100.00"}, "compare_at_price": {"label": "Product compare at price", "info": "Example: 120.00"}, "horizontal_position": {"label": "Horizontal position"}, "vertical_position": {"label": "Vertical position"}}}}}, "account": {"name": "Account"}, "activate_account": {"name": "Activate account"}, "addresses": {"name": "Addresses"}, "content_and_sidebar": {"name": "Content and Sidebar", "settings": {"header__1": {"content": "General"}, "show_information": {"label": "Show information"}, "show_image": {"label": "Show image"}, "show_content": {"label": "Show content"}, "show_tags": {"label": "Show tags"}, "show_social_share_buttons": {"label": "Show social share buttons"}, "social_share_buttons_type": {"label": "Social share buttons type", "option__1": {"label": "Small"}, "option__2": {"label": "Large"}}, "show_navigation": {"label": "Show navigation"}, "show_comments": {"label": "Show comments"}, "header__2": {"content": "Content"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}, "autoplay": {"label": "Autoplay"}, "speed": {"label": "Autoplay speed (sec)"}, "infinite": {"label": "Is infinite"}, "header__3": {"content": "Support"}, "paragraph__2": {"content": "Positioning and visibility settings are in the Theme settings -> Blog & Article Pages"}, "paragraph__3": {"content": "Click '+ Add block' to add content for SIDEBAR"}, "show_title": {"label": "Show page title"}, "type": {"label": "Type", "option__1": {"label": "Type #1, 1 item in the row"}, "option__2": {"label": "Type #2, 2 items in the row"}, "option__3": {"label": "Type #2, 3 items in the row"}, "option__4": {"label": "Type #2, 4 items in the row"}, "option__5": {"label": "Type #2, 6 items in the row"}}, "masonry_enable": {"label": "Enable masonry", "info": "Only with type #2"}, "max_posts": {"label": "Posts per page"}, "show_button": {"label": "Show button"}, "show_empty_comments": {"label": "Show empty comments"}, "max_post_content_length": {"label": "Max post content length (letters)"}}, "blocks": {"slide": {"name": "Content Slide", "settings": {"for_article": {"label": "For article"}, "image": {"label": "Image"}, "title": {"label": "Title"}}}, "categories": {"name": "Sidebar Categories", "settings": {"title": {"label": "Title"}, "menu": {"label": "<PERSON><PERSON>"}, "show_border": {"label": "Show border"}, "header__1": {"content": "Support"}, "paragraph__1": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}}, "tags": {"name": "Sidebar Tags", "settings": {"title": {"label": "Title"}, "show_border": {"label": "Show border"}}}, "recents": {"name": "Sidebar Recent posts", "settings": {"title": {"label": "Title"}, "menu": {"label": "<PERSON><PERSON>"}, "show_border": {"label": "Show border"}}}, "html": {"name": "Sidebar HTML", "settings": {"title": {"label": "Title"}, "page_content": {"label": "Page content", "info": "Default page: 'Include Blog Sidebar'"}, "show_border": {"label": "Show border"}}}, "subscription": {"name": "Sidebar Subscription", "settings": {"title": {"label": "Title"}, "show_border": {"label": "Show border"}, "paragraph": {"label": "Paragraph"}, "button_text": {"label": "Button text"}, "placeholder": {"label": "Placeholder"}}}}}, "cart": {"name": "<PERSON><PERSON>"}, "product_grid": {"name": "Product grid", "settings": {"paragraph__1": {"content": "You can add sections \"Heading\" and \"Banner Builder\" for the header of the collection pages and for the banners. To do this, select the \"Add section -> Collection page heading\" or \"Add section -> Collection banner builder\" section on this page."}, "paragraph__2": {"content": "Attention! After making changes in this section, press the \"Save\" button in the admin panel to see the \"Heading\" and \"Banner Builder\" sections if these sections have been added to the page."}, "container": {"label": "Content width", "option__1": {"label": "Boxed"}, "option__2": {"label": "Fullwidth"}}, "products_per_page": {"label": "Products per page"}, "header__1": {"content": "Filtering"}, "enable_default_filtering": {"label": "Enable default filtering", "info": "Or turn off filtering by default and turn on filtering by tags if this option is disabled."}, "paragraph__3": {"content": "[Customize filters](/admin/menus) in the admin panel and add a block to \"Add block -> Filters\" to show the filters. These settings apply only to the \"Filters\" block."}, "show_selected_filters_counter": {"label": "Show selected filters counter & button \"Reset\""}, "show_filter_product_count": {"label": "Show the count of products on the filter"}, "hide_disabled_filters": {"label": "Hide disabled filters", "info": "Or show and mark as unavailable if this option is disabled"}, "make_availability_as_rounded": {"label": "Make availability filter as rounded checkbox"}, "price_filter_type": {"label": "Price filter type", "info": "If the \"Theme settings -> Currency -> Enable legacy method of the currency conversion\" option is enabled then only the \"Slider\" option is available", "option__1": {"label": "Slider & inputs"}, "option__2": {"label": "Slide<PERSON>"}, "option__3": {"label": "Inputs"}}, "header__2": {"content": "Sort by"}, "sort_by_visibility": {"label": "Show 'Sort by' select", "option__1": {"label": "Only desktop"}, "option__2": {"label": "Desktop & mobile"}, "option__3": {"label": "<PERSON>de"}}, "paragraph__4": {"content": "Default value get from collection setting"}, "header__3": {"content": "Information"}, "info_visibility": {"label": "Show information", "option__1": {"label": "Only desktop"}, "option__2": {"label": "Desktop & mobile"}, "option__3": {"label": "<PERSON>de"}}, "header__4": {"content": "Grid"}, "view_grid_visibility": {"label": "Show grid buttons", "option__1": {"label": "Only desktop"}, "option__2": {"label": "Desktop & mobile"}, "option__3": {"label": "<PERSON>de"}}, "paragraph__5": {"content": "The buttons dynamically change their availability and remember the selected state at different screen widths. Resize your browser window from small to large to see all available buttons."}, "show_view_grid_1": {"label": "Show button '1 item per row'", "info": "Visible only on the mobile"}, "show_view_grid_2": {"label": "Show button '2 items per row'"}, "show_view_grid_3": {"label": "Show button '3 items per row'"}, "show_view_grid_4": {"label": "Show button '4 items per row'"}, "show_view_grid_6": {"label": "Show button '6 items per row'"}, "show_view_grid_list": {"label": "Show button 'List'", "info": "Visible only on the desktop"}, "view_grid_list_design": {"label": "Product 'List' design", "option__1": {"label": "Design #1"}, "option__2": {"label": "Design #2"}}, "enable_grid_tooltip": {"label": "Enable tooltips", "info": "Don't forget to enable tooltips for the whole theme for this option to work. Theme settings -> Animations -> Tooltips"}, "default_view_grid_xl": {"label": "Default products per row (Extra large)", "option__1": {"label": "2"}, "option__2": {"label": "3"}, "option__3": {"label": "4"}, "option__4": {"label": "6"}, "option__5": {"label": "List"}}, "default_view_grid_lg": {"label": "Default products per row (Large)", "option__1": {"label": "2"}, "option__2": {"label": "3"}, "option__3": {"label": "4"}, "option__4": {"label": "6"}, "option__5": {"label": "List"}}, "default_view_grid_md": {"label": "Default products per row (Medium)", "option__1": {"label": "2"}, "option__2": {"label": "3"}, "option__3": {"label": "4"}}, "default_view_grid_sm": {"label": "Default products per row (Small)", "option__1": {"label": "1 (centered)"}, "option__2": {"label": "2"}, "option__3": {"label": "3"}}, "default_view_grid_xs": {"label": "Default view grid (Extra small)", "option__1": {"label": "1 (centered)"}, "option__2": {"label": "2"}}, "header__5": {"content": "Current filters over the products grid"}, "current_filters_visibility": {"label": "Show current filters", "option__1": {"label": "Only mobile"}, "option__2": {"label": "Only desktop"}, "option__3": {"label": "Desktop & mobile"}, "option__4": {"label": "<PERSON>de"}}, "header__6": {"content": "Filters section"}, "uppercase_filter_title": {"label": "Uppercase filters section title"}, "show_filter_border": {"label": "Show filters section border"}, "header__7": {"content": "Support"}, "paragraph__6": {"content": "[Watch video tutorials](https://www.youtube.com/@shopifymise1354)"}, "paragraph__7": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}, "blocks": {"collections": {"name": "Collections", "settings": {"header__1": {"content": "Section settings"}, "title": {"label": "Title"}, "default_state": {"label": "Default state", "info": "If the \"Theme settings -> Sidebar position on desktop -> Top & vertical filters\" option is selected, only the \"Open\" and \"Close\" options are available", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "menu": {"label": "<PERSON><PERSON>"}, "show_collection_product_count": {"label": "Show the count of products on the collection"}}}, "current_filters": {"name": "Current filters", "settings": {"paragraph__1": {"content": "The block is always hidden if there are no active filters. Start filtering to see this block."}, "header__1": {"content": "Section settings"}, "title": {"label": "Title"}, "default_state": {"label": "Default state", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "show_group": {"label": "Show current filters group", "info": "Groups are shown if there is more than 1 group type in active filters. For example: Color and Price"}}}, "filters": {"name": "Filters", "settings": {"paragraph__1": {"content": "This block will be shown if the \"Product grid -> Filtering -> Enable default filtering\" option is enabled."}, "paragraph__2": {"content": "[Customize filters](/admin/menus)"}, "header__1": {"content": "Section settings"}, "default_state": {"label": "Default state", "info": "If the \"Theme settings -> Sidebar position on desktop -> Top & vertical filters\" option is selected, only the \"Open\" and \"Close\" options are available", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "layout": {"label": "Layout", "option__1": {"label": "1 column"}, "option__2": {"label": "2 columns"}, "option__3": {"label": "3 columns"}, "option__4": {"label": "Row"}}, "max_column_size": {"label": "Max column size", "info": "Only for setting \"Layout: 2 or 3 columns \". 0 is infinite"}}}, "filter_settings": {"name": "Filter settings", "settings": {"filter_name": {"label": "Filter name"}, "header__1": {"content": "Section settings"}, "title": {"label": "Title"}, "default_state": {"label": "Default state", "info": "If the \"Theme settings -> Sidebar position on desktop -> Top & vertical filters\" option is selected, only the \"Open\" and \"Close\" options are available", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "make_as_color": {"label": "Make as color circle", "info": "Make sure the values for this filter are colors."}, "layout": {"label": "Layout", "option__1": {"label": "1 column"}, "option__2": {"label": "2 columns"}, "option__3": {"label": "3 columns"}, "option__4": {"label": "Row"}}, "max_column_size": {"label": "Max column size", "info": "Only for setting \"Layout: 2 or 3 columns \". 0 is infinite"}}}, "tags": {"name": "Tags", "settings": {"paragraph__1": {"content": "This block will be shown if the \"Product grid -> Filtering -> Enable default filtering\" option is disabled."}, "paragraph__2": {"content": "Attention! The tag does not work in conjunction with filters! We recommend using the new version of the filter and the \"Filters\" block!"}, "header__1": {"content": "Section settings"}, "title": {"label": "Title"}, "default_state": {"label": "Default state", "info": "If the \"Theme settings -> Sidebar position on desktop -> Top & vertical filters\" option is selected, only the \"Open\" and \"Close\" options are available", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "show_checkbox": {"label": "Show checkbox area", "info": "If this option is disabled, then the \"Make tag as color circle\" option is not available for activation"}, "make_as_color": {"label": "Make tag as color circle", "info": "If this option is enabled, you must manually enter the names of the tags that you want to display in the \"Tags list\" field. For example: Red"}, "tags_list": {"label": "Tags list", "info": "Break the line to separate tags. Clear the field to automatically show all available tags on the collection."}, "layout": {"label": "Layout", "option__1": {"label": "1 column"}, "option__2": {"label": "2 columns"}, "option__3": {"label": "3 columns"}, "option__4": {"label": "Row"}}, "max_column_size": {"label": "Max column size", "info": "Only for setting \"Layout: 2 or 3 columns \". 0 is infinite"}, "for_collections": {"label": "Section visible on collections", "info": "Separator is ','. Example: mens,womens,accessories. Default - for all collections"}}}, "products": {"name": "Products", "settings": {"paragraph__1": {"content": "If the \"Theme settings -> Sidebar position on desktop -> Top & vertical filters\" option is selected, then the section will be displayed only on a mobile device."}, "header__1": {"content": "Section settings"}, "title": {"label": "Title"}, "default_state": {"label": "Default state", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "collection": {"label": "Collection"}, "max_count": {"label": "Max count", "info": "0 is infinite"}, "product_type": {"label": "Products type", "option__1": {"label": "Short"}, "option__2": {"label": "Full"}}}}, "custom_html": {"name": "Custom HTML", "settings": {"paragraph__1": {"content": "If the \"Theme settings -> Sidebar position on desktop -> Top & vertical filters\" option is selected, then the section will be displayed only on a mobile device."}, "header__1": {"content": "Section settings"}, "title": {"label": "Title"}, "default_state": {"label": "Default state", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "page_content": {"label": "Page content", "info": "Default page: 'Include Collections Sidebar'"}}}, "subscription": {"name": "Subscription", "settings": {"paragraph__1": {"content": "If the \"Theme settings -> Sidebar position on desktop -> Top & vertical filters\" option is selected, then the section will be displayed only on a mobile device."}, "header__1": {"content": "Section settings"}, "title": {"label": "Title"}, "default_state": {"label": "Default state", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "paragraph": {"label": "Paragraph"}, "placeholder": {"label": "Placeholder"}, "button_text": {"label": "Button text"}}}, "promobox": {"name": "Promo box (Banner)", "settings": {"header__1": {"content": "Section settings"}, "title": {"label": "Title"}, "default_state": {"label": "Default state", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "url": {"label": "URL for banner"}, "header__3": {"content": "Image"}, "image": {"label": "Image", "info": "Recommended size 1440x550 pixels"}, "image_size": {"label": "Image size", "info": "These are the fields for image quality. If images lazy loading is on, the option is ignored"}, "color_image_mask": {"label": "Image mask"}, "image_mask_opacity": {"label": "Image mask opacity"}, "header__4": {"content": "Text"}, "paragraph__1": {"content": "Delete the line value for the text or button if you want to hide the element"}, "paragraph__2": {"content": "Use '<br>' for the line break"}, "text_line_1": {"label": "Text line #1"}, "text_line_2": {"label": "Text line #2"}, "text_line_3": {"label": "Text line #3"}, "style": {"label": "Colorize style", "option__1": {"label": "Style #1"}, "option__2": {"label": "Style #2"}, "option__3": {"label": "Style #3"}, "option__4": {"label": "Style #4"}, "option__5": {"label": "Style #5"}, "option__6": {"label": "Style #6"}, "option__7": {"label": "Style #7"}, "option__8": {"label": "Style #8"}}, "header__5": {"content": "Button #1"}, "button_1": {"label": "Button text"}, "button_1_url": {"label": "URL for the button"}, "color_button_type_1": {"label": "Button style", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Secondary"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Clean"}, "option__5": {"label": "Transparent default"}, "option__6": {"label": "Transparent secondary"}, "option__7": {"label": "Transparent invert"}, "option__8": {"label": "Transparent clean"}}, "header__6": {"content": "Button #2"}, "button_2": {"label": "Button text"}, "button_2_url": {"label": "URL for the button"}, "color_button_type_2": {"label": "Button style", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Secondary"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Clean"}, "option__5": {"label": "Transparent default"}, "option__6": {"label": "Transparent secondary"}, "option__7": {"label": "Transparent invert"}, "option__8": {"label": "Transparent clean"}}, "header__7": {"content": "Other content"}, "list_1": {"label": "List", "info": "Select page with list"}, "custom_html": {"label": "Custom HTML", "info": "Replace the whole text content to the page content"}, "header__8": {"content": "Video"}, "video_external_url": {"label": "Youtube or Vimeo video URL"}, "video_mp4_url": {"label": "Video URL", "info": "Upload your video in Admin-> Settings-> Files and add a link here"}, "video_autoplay": {"label": "Video autoplay"}, "video_controls": {"label": "Video controls"}, "header__9": {"content": "Layout"}, "type": {"label": "Type", "option__1": {"label": "Clean (Image without text)"}, "option__2": {"label": "Clean with border"}, "option__3": {"label": "Clean with border on hover"}, "option__4": {"label": "Text v1"}, "option__5": {"label": "Text v2"}, "option__6": {"label": "Text v3"}, "option__7": {"label": "Text v4"}, "option__8": {"label": "Text v5"}, "option__9": {"label": "Text v6"}, "option__10": {"label": "Text v7"}, "option__11": {"label": "Text v8"}, "option__12": {"label": "Text v9"}, "option__13": {"label": "Text v10"}, "option__14": {"label": "Type #1 v1 (Text over the image)"}, "option__15": {"label": "Type #1 v2"}, "option__16": {"label": "Type #1 v3"}, "option__17": {"label": "Type #1 v4"}, "option__18": {"label": "Type #1 v5"}, "option__19": {"label": "Type #1 v6"}, "option__20": {"label": "Type #1 v7"}, "option__21": {"label": "Type #1 v8"}, "option__22": {"label": "Type #1 v9"}, "option__23": {"label": "Type #1 v10"}, "option__24": {"label": "Type #1 v11"}, "option__25": {"label": "Type #1 v12"}, "option__26": {"label": "Type #1 v13"}, "option__27": {"label": "Type #1 v14"}, "option__28": {"label": "Type #1 v15"}, "option__29": {"label": "Type #1 v16"}, "option__30": {"label": "Type #1 v17"}, "option__31": {"label": "Type #1 with Background v1"}, "option__32": {"label": "Type #1 with Background v2"}, "option__33": {"label": "Type #1 with Background v3"}, "option__34": {"label": "Type #1 with Background v4"}, "option__35": {"label": "Type #1 with <PERSON><PERSON>ain v1"}, "option__36": {"label": "Type #1 with <PERSON><PERSON><PERSON> v2"}, "option__37": {"label": "Type #1 with <PERSON><PERSON><PERSON> v3"}, "option__38": {"label": "Type #2 v1 (Text below the image)"}, "option__39": {"label": "Type #2 v2"}, "option__40": {"label": "Type #2 v3"}, "option__41": {"label": "Type #2 v4"}, "option__42": {"label": "Type #2 v5"}, "option__43": {"label": "Type #2 v6"}, "option__44": {"label": "Type #2 v7"}, "option__45": {"label": "Type #2 v8"}, "option__46": {"label": "Type #2 v9"}, "option__47": {"label": "Type #2 v10"}, "option__48": {"label": "Type #2 v11"}, "option__49": {"label": "Type #2 v12"}, "option__50": {"label": "Type #2 v13"}, "option__51": {"label": "Type #3 (Ribbon bottom the image)"}, "option__52": {"label": "Type #4 (Animation ribbon bottom the image)"}}, "header__10": {"content": "Animation"}, "animation_to": {"label": "Move image on hover to", "option__1": {"label": "None"}, "option__2": {"label": "Center"}, "option__3": {"label": "Top left"}, "option__4": {"label": "Top"}, "option__5": {"label": "Top right"}, "option__6": {"label": "Right"}, "option__7": {"label": "Bottom right"}, "option__8": {"label": "Bottom"}, "option__9": {"label": "Bottom left"}, "option__10": {"label": "Left"}}, "animation_from": {"label": "Static image position", "option__1": {"label": "None"}, "option__2": {"label": "Center"}, "option__3": {"label": "Top left"}, "option__4": {"label": "Top"}, "option__5": {"label": "Top right"}, "option__6": {"label": "Right"}, "option__7": {"label": "Bottom right"}, "option__8": {"label": "Bottom"}, "option__9": {"label": "Bottom left"}, "option__10": {"label": "Left"}}, "animation_opacity": {"label": "Image opacity", "option__1": {"label": "None"}, "option__2": {"label": "Static & hover"}, "option__3": {"label": "Static"}, "option__4": {"label": "Hover"}}}}}}, "list_collections": {"name": "List collections", "settings": {"display_type": {"label": "Select collections to show", "option__1": {"label": "All"}, "option__2": {"label": "Selected"}}, "sort": {"label": "Sort collections by:", "option__1": {"label": "Product count, high to low"}, "option__2": {"label": "Product count, low to high"}, "option__3": {"label": "Alphabetically, A-Z"}, "option__4": {"label": "Alphabetically, Z-A"}, "option__5": {"label": "Date, old to new"}, "option__6": {"label": "Date, new to old"}, "option__7": {"label": "Manually (By the order of the selected blocks)"}}, "show_products_count": {"label": "Show products count"}, "size_of_columns": {"label": "Size of the columns", "option__1": {"label": "1 item in the row"}, "option__2": {"label": "2 items in the row"}, "option__3": {"label": "3 items in the row"}, "option__4": {"label": "4 items in the row"}, "option__5": {"label": "5 items in the row"}, "option__6": {"label": "6 items in the row"}}, "size_of_columns_mobile": {"label": "Size of the columns on mobile", "option__1": {"label": "1 item in the row"}, "option__2": {"label": "2 items in the row"}}, "type": {"label": "Type", "option__1": {"label": "Type #1 (Below the image)"}, "option__2": {"label": "Type #2 (Ribbon bottom the image)"}, "option__3": {"label": "Clean (Image without text)"}, "option__4": {"label": "Clean with border"}, "option__5": {"label": "Clean with border on hover"}}, "style": {"label": "Colorize style", "option__1": {"label": "Auto"}, "option__2": {"label": "Style #1"}, "option__3": {"label": "Style #2"}, "option__4": {"label": "Style #3"}, "option__5": {"label": "Style #4"}, "option__6": {"label": "Style #5"}, "option__7": {"label": "Style #6"}, "option__8": {"label": "Style #7"}, "option__9": {"label": "Style #8"}}, "masonry_enable": {"label": "Enable masonry"}, "height": {"label": "Image height", "option__1": {"label": "Auto adaptation to the image"}, "option__2": {"label": "30% of width"}, "option__3": {"label": "40% of width"}, "option__4": {"label": "50% of width"}, "option__5": {"label": "60% of width"}, "option__6": {"label": "70% of width"}, "option__7": {"label": "80% of width"}, "option__8": {"label": "90% of width"}, "option__9": {"label": "100% of width (Square)"}, "option__10": {"label": "110% of width"}, "option__11": {"label": "120% of width"}, "option__12": {"label": "130% of width"}, "option__13": {"label": "140% of width"}, "option__14": {"label": "150% of width"}}, "image_size": {"label": "Image size", "info": "These are the fields for image quality. If images lazy loading is on, the option is ignored"}, "format_pjpg": {"label": "Enable format 'PJPG'", "info": "Don't work with transparent images"}, "header__1": {"content": "Animation"}, "animation_to": {"label": "Move image on hover to", "option__1": {"label": "None"}, "option__2": {"label": "Center"}, "option__3": {"label": "Top left"}, "option__4": {"label": "Top"}, "option__5": {"label": "Top right"}, "option__6": {"label": "Right"}, "option__7": {"label": "Bottom right"}, "option__8": {"label": "Bottom"}, "option__9": {"label": "Bottom left"}, "option__10": {"label": "Left"}}, "animation_from": {"label": "Static image position", "option__1": {"label": "None"}, "option__2": {"label": "Center"}, "option__3": {"label": "Top left"}, "option__4": {"label": "Top"}, "option__5": {"label": "Top right"}, "option__6": {"label": "Right"}, "option__7": {"label": "Bottom right"}, "option__8": {"label": "Bottom"}, "option__9": {"label": "Bottom left"}, "option__10": {"label": "Left"}}, "animation_opacity": {"label": "Image opacity", "option__1": {"label": "None"}, "option__2": {"label": "Static"}, "option__3": {"label": "Hover"}}}, "blocks": {"collection": {"name": "Collection", "settings": {"for_collection": {"label": "For collection"}, "image": {"label": "Image"}, "text_line_1": {"label": "Text line #1"}, "text_line_2": {"label": "Text line #2"}, "button_1": {"label": "Button text"}}}}}, "login": {"name": "<PERSON><PERSON>"}, "order": {"name": "Order"}, "policy_page": {"name": "Policy page", "settings": {"page_type": {"label": "Page type", "option__1": {"label": "Privacy policy"}, "option__2": {"label": "Refund policy"}, "option__3": {"label": "Shipping policy"}, "option__4": {"label": "Terms of service"}, "option__5": {"label": "Page content"}}}}, "page": {"name": "Page", "settings": {"show_title": {"label": "Show title"}, "show_content": {"label": "Show content"}}}, "content": {"name": "Content", "settings": {"header__1": {"content": "Subscription"}, "subscription_title": {"label": "Title"}, "subscription_paragraph": {"label": "Paragraph", "info": "Disabled if field 'Message for your visitors' in 'Password protection' is not empty"}, "subscription_placeholder": {"label": "Placeholder"}, "subscription_button_text": {"label": "Button text"}, "header__2": {"content": "Banner"}, "image": {"label": "Image"}}, "blocks": {"social_media": {"name": "Social media", "settings": {"show_facebook": {"label": "Show facebook"}, "show_twitter": {"label": "Show twitter"}, "show_instagram": {"label": "Show instagram"}, "show_pinterest": {"label": "Show pinterest"}, "show_youtube": {"label": "Show youtube"}, "show_behance": {"label": "Show behance"}, "show_skype": {"label": "Show skype"}, "show_tiktok": {"label": "Show tiktok"}, "show_line": {"label": "show line"}}}}}, "product_page": {"name": "Product page", "settings": {"header__1": {"content": "Sidebar"}, "gallery_size": {"label": "Gallery size", "option__1": {"label": "4/12"}, "option__2": {"label": "5/12"}, "option__3": {"label": "6/12"}, "option__4": {"label": "7/12"}, "option__5": {"label": "8/12"}}, "sidebar": {"label": "Sidebar position", "info": "Settings for the sidebar in the Product pages -> Sections -> 'Product page' section", "option__1": {"label": "Off"}, "option__2": {"label": "Left"}, "option__3": {"label": "Right"}}, "enable_sticky_sidebar": {"label": "Enable sticky sidebar"}, "header__2": {"content": "Tabs"}, "show_tabs": {"label": "Show tabs"}, "show_tab_description": {"label": "Show tab for Description"}, "scrolling_to_opened_tab": {"label": "Scrolling to opened tab", "info": "Only for vertical tabs on desktop and on mobile tabs"}, "tabs_type": {"label": "Tabs type", "info": "In this section you can select one of the options: \"Default tabs\" or \"Sheet of tabs\". Once you select the option \"Sheet of tabs\" the Reviews and Description blocks will be displayed without a tab, but all other tabs will be hidden using this combination.", "option__1": {"label": "Default tabs"}, "option__2": {"label": "Sheet of tabs"}}, "paragraph__1": {"content": "Click '+ Add block' to add content or tabs or content for sidebar"}, "header__3": {"content": "Support"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}, "blocks": {"collections": {"name": "Collections"}, "title": {"name": "Title"}, "details": {"name": "Details", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Product details\""}}}, "price": {"name": "Price", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Price\""}}}, "icon_with_text": {"name": "Icon with text", "settings": {"style": {"label": "Block style", "option__1": {"label": "Inline"}, "option__2": {"label": "Tile"}}, "header__1": {"content": "Section #1"}, "icon_svg_1": {"label": "Icon SVG"}, "icon_image_1": {"label": "Icon image"}, "text_1": {"label": "Text"}, "header__2": {"content": "Section #2"}, "icon_svg_2": {"label": "Icon SVG"}, "icon_image_2": {"label": "Icon image"}, "text_2": {"label": "Text"}, "header__3": {"content": "Section #3"}, "icon_svg_3": {"label": "Icon SVG"}, "icon_image_3": {"label": "Icon image"}, "text_3": {"label": "Text"}, "header__4": {"content": "Section #4"}, "icon_svg_4": {"label": "Icon SVG"}, "icon_image_4": {"label": "Icon image"}, "text_4": {"label": "Text"}, "header__5": {"content": "Section #5"}, "icon_svg_5": {"label": "Icon SVG"}, "icon_image_5": {"label": "Icon image"}, "text_5": {"label": "Text"}, "header__6": {"content": "Section #6"}, "icon_svg_6": {"label": "Icon SVG"}, "icon_image_6": {"label": "Icon image"}, "text_6": {"label": "Text"}}}, "description": {"name": "Description", "settings": {"title": {"label": "Title"}, "content": {"label": "Content", "info": "Field for custom description. Leave the field blank to show the product description"}}}, "text": {"name": "Text", "settings": {"content": {"label": "Content"}}}, "countdown": {"name": "Time countdown"}, "stock_countdown": {"name": "Stock countdown", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Stock countdown\""}}}, "delivery_countdown": {"name": "Delivery countdown", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Delivery countdown\""}}}, "border": {"name": "Border"}, "options": {"name": "Options", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product -> Variants\""}}}, "popups": {"name": "Popups", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Popups\""}}}, "notes": {"name": "Text for notes"}, "buttons_quantity": {"name": "Buttons and quantity", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Buttons and inputs\""}}}, "free_shipping": {"name": "Free shipping", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Cart -> Free shipping progress bar\""}}}, "pickup_availability": {"name": "Pickup availability", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Pickup availability\""}}}, "complementary_products": {"name": "Complementary products", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Complementary products\""}}}, "payments": {"name": "Payments"}, "social_share_buttons": {"name": "Social share buttons", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Social share buttons\""}}}, "tab_custom": {"name": "Tab for Custom content", "settings": {"title": {"label": "Title"}, "content": {"label": "Content"}, "paragraph__1": {"content": "OR SELECT"}, "page_content": {"label": "Page content", "info": "Default page: 'Include Product Tab Custom HTML'"}}}, "tab_custom_liquid": {"name": "Tab for Custom Liquid", "settings": {"title": {"label": "Title"}, "custom_liquid": {"label": "Custom Liquid"}}}, "sidebar_custom_html": {"name": "Sidebar custom HTML", "settings": {"page_content": {"label": "Page content", "info": "Default page: 'Include Product Sidebar Custom HTML'"}}}, "sidebar_products": {"name": "Sidebar products", "settings": {"title": {"label": "Title"}, "collection": {"label": "Collection"}, "max_count": {"label": "Max count", "info": "0 is infinite"}, "product_type": {"label": "Products type", "option__1": {"label": "Short"}, "option__2": {"label": "Full"}}}}, "custom_liquid": {"name": "Custom Liquid", "settings": {"custom_liquid": {"label": "Custom Liquid", "info": "Add app snippets or other Liquid code to create advanced customizations."}}}}}, "quick_view": {"name": "Quick view", "settings": {"gallery_size": {"label": "Gallery size", "option__1": {"label": "4/12"}, "option__2": {"label": "5/12"}, "option__3": {"label": "6/12"}, "option__4": {"label": "7/12"}, "option__5": {"label": "8/12"}}, "enable_sticky_sidebar": {"label": "Enable sticky sidebar"}, "paragraph__1": {"content": "Click '+ Add block' to add content"}, "header__1": {"content": "Support"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}, "blocks": {"collections": {"name": "Collections"}, "title": {"name": "Title"}, "details": {"name": "Details", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Product details\""}}}, "price": {"name": "Price", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Price\""}}}, "icon_with_text": {"name": "Icon with text", "settings": {"style": {"label": "Block style", "option__1": {"label": "Inline"}, "option__2": {"label": "Tile"}}, "header__1": {"content": "Section #1"}, "icon_svg_1": {"label": "Icon SVG"}, "icon_image_1": {"label": "Icon image"}, "text_1": {"label": "Text"}, "header__2": {"content": "Section #2"}, "icon_svg_2": {"label": "Icon SVG"}, "icon_image_2": {"label": "Icon image"}, "text_2": {"label": "Text"}, "header__3": {"content": "Section #3"}, "icon_svg_3": {"label": "Icon SVG"}, "icon_image_3": {"label": "Icon image"}, "text_3": {"label": "Text"}, "header__4": {"content": "Section #4"}, "icon_svg_4": {"label": "Icon SVG"}, "icon_image_4": {"label": "Icon image"}, "text_4": {"label": "Text"}, "header__5": {"content": "Section #5"}, "icon_svg_5": {"label": "Icon SVG"}, "icon_image_5": {"label": "Icon image"}, "text_5": {"label": "Text"}, "header__6": {"content": "Section #6"}, "icon_svg_6": {"label": "Icon SVG"}, "icon_image_6": {"label": "Icon image"}, "text_6": {"label": "Text"}}}, "description": {"name": "Description", "settings": {"title": {"label": "Title"}, "content": {"label": "Content", "info": "Field for custom description. Leave the field blank to show the product description"}}}, "text": {"name": "Text", "settings": {"content": {"label": "Content"}}}, "countdown": {"name": "Time countdown"}, "stock_countdown": {"name": "Stock countdown", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Stock countdown\""}}}, "delivery_countdown": {"name": "Delivery countdown", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Delivery countdown\""}}}, "border": {"name": "Border"}, "options": {"name": "Options", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product -> Variants\""}}}, "popups": {"name": "Popups", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Popups\""}}}, "notes": {"name": "Text for notes"}, "buttons_quantity": {"name": "Buttons and quantity", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Buttons and inputs\""}}}, "free_shipping": {"name": "Free shipping", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Cart -> Free shipping progress bar\""}}}, "pickup_availability": {"name": "Pickup availability", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Pickup availability\""}}}, "complementary_products": {"name": "Complementary products", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Complementary products\""}}}, "payments": {"name": "Payments"}, "social_share_buttons": {"name": "Social share buttons", "settings": {"paragraph__1": {"content": "You can configure this block in more detail in the section \"Theme settings -> Product page & Quick view -> Social share buttons\""}}}}}, "register": {"name": "Register"}, "reset_password": {"name": "Reset password"}, "search_page": {"name": "Search page", "settings": {"container": {"label": "Content width", "option__1": {"label": "Boxed"}, "option__2": {"label": "Fullwidth"}}, "products_per_page": {"label": "Products per page"}, "search_show_only_products": {"label": "Show only products", "info": "Remove pages and blog articles from search results."}, "header__1": {"content": "Filtering"}, "enable_default_filtering": {"label": "Enable default filtering"}, "paragraph__1": {"content": "[Customize filters](/admin/menus) in the admin panel and add a block to \"Add block -> Filters\" to show the filters. These settings apply only to the \"Filters\" block."}, "show_selected_filters_counter": {"label": "Show selected filters counter & button \"Reset\""}, "show_filter_product_count": {"label": "Show the count of products on the filter"}, "hide_disabled_filters": {"label": "Hide disabled filters", "info": "Or show and mark as unavailable if this option is disabled"}, "make_availability_as_rounded": {"label": "Make availability filter as rounded checkbox"}, "price_filter_type": {"label": "Price filter type", "info": "If the \"Theme settings -> Currency -> Enable legacy method of the currency conversion\" option is enabled then only the \"Slider\" option is available", "option__1": {"label": "Slider & inputs"}, "option__2": {"label": "Slide<PERSON>"}, "option__3": {"label": "Inputs"}}, "header__2": {"content": "Sort by"}, "sort_by_visibility": {"label": "Show 'Sort by' select", "option__1": {"label": "Only desktop"}, "option__2": {"label": "Desktop & mobile"}, "option__3": {"label": "<PERSON>de"}}, "paragraph__2": {"content": "Default value get from collection setting"}, "header__3": {"content": "Grid"}, "view_grid_visibility": {"label": "Show grid buttons", "option__1": {"label": "Only desktop"}, "option__2": {"label": "Desktop & mobile"}, "option__3": {"label": "<PERSON>de"}}, "paragraph__3": {"content": "The buttons dynamically change their availability and remember the selected state at different screen widths. Resize your browser window from small to large to see all available buttons."}, "show_view_grid_1": {"label": "Show button '1 item per row'", "info": "Visible only on the mobile"}, "show_view_grid_2": {"label": "Show button '2 items per row'"}, "show_view_grid_3": {"label": "Show button '3 items per row'"}, "show_view_grid_4": {"label": "Show button '4 items per row'"}, "show_view_grid_6": {"label": "Show button '6 items per row'"}, "show_view_grid_list": {"label": "Show button 'List'", "info": "Visible only on the desktop"}, "view_grid_list_design": {"label": "Product 'List' design", "option__1": {"label": "Design #1"}, "option__2": {"label": "Design #2"}}, "enable_grid_tooltip": {"label": "Enable tooltips", "info": "Don't forget to enable tooltips for the whole theme for this option to work. Theme settings -> Animations -> Tooltips"}, "default_view_grid_xl": {"label": "Default products per row (Extra large)", "option__1": {"label": "2"}, "option__2": {"label": "3"}, "option__3": {"label": "4"}, "option__4": {"label": "6"}, "option__5": {"label": "List"}}, "default_view_grid_lg": {"label": "Default products per row (Large)", "option__1": {"label": "2"}, "option__2": {"label": "3"}, "option__3": {"label": "4"}, "option__4": {"label": "6"}, "option__5": {"label": "List"}}, "default_view_grid_md": {"label": "Default products per row (Medium)", "option__1": {"label": "2"}, "option__2": {"label": "3"}, "option__3": {"label": "4"}}, "default_view_grid_sm": {"label": "Default products per row (Small)", "option__1": {"label": "1 (centered)"}, "option__2": {"label": "2"}, "option__3": {"label": "3"}}, "default_view_grid_xs": {"label": "Default view grid (Extra small)", "option__1": {"label": "1 (centered)"}, "option__2": {"label": "2"}}, "header__4": {"content": "Current filters over the products grid"}, "current_filters_visibility": {"label": "Show current filters", "option__1": {"label": "Only mobile"}, "option__2": {"label": "Only desktop"}, "option__3": {"label": "Desktop & mobile"}, "option__4": {"label": "<PERSON>de"}}, "header__5": {"content": "Filters section"}, "uppercase_filter_title": {"label": "Uppercase filters section title"}, "show_filter_border": {"label": "Show filters section border"}, "header__6": {"content": "Video tutorial"}, "paragraph__4": {"content": "[Watch video tutorials](https://www.youtube.com/@shopifymise1354)"}, "header__7": {"content": "Support"}, "paragraph__5": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}, "blocks": {"current_filters": {"name": "Current filters", "settings": {"paragraph__1": {"content": "The block is always hidden if there are no active filters. Start filtering to see this block."}, "header__1": {"content": "Section settings"}, "title": {"label": "Title"}, "default_state": {"label": "Default state", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "show_group": {"label": "Show current filters group", "info": "Groups are shown if there is more than 1 group type in active filters. For example: Color and Price"}}}, "filters": {"name": "Filters", "settings": {"paragraph__1": {"content": "This block will be shown if the \"Search page -> Filtering -> Enable default filtering\" option is enabled."}, "paragraph__2": {"content": "[Customize filters](/admin/menus)"}, "header__1": {"content": "Section settings"}, "default_state": {"label": "Default state", "info": "If the \"Theme settings -> Sidebar position on desktop -> Top & vertical filters\" option is selected, only the \"Open\" and \"Close\" options are available", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "layout": {"label": "Layout", "option__1": {"label": "1 column"}, "option__2": {"label": "2 columns"}, "option__3": {"label": "3 columns"}, "option__4": {"label": "Row"}}, "max_column_size": {"label": "Max column size", "info": "Only for setting \"Layout: 2 or 3 columns \". 0 is infinite"}}}, "filter_settings": {"name": "Filter settings", "settings": {"filter_name": {"label": "Filter name"}, "header__1": {"content": "Section settings"}, "title": {"label": "Title"}, "default_state": {"label": "Default state", "info": "If the \"Theme settings -> Sidebar position on desktop -> Top & vertical filters\" option is selected, only the \"Open\" and \"Close\" options are available", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "make_as_color": {"label": "Make as color circle", "info": "Make sure the values for this filter are colors."}, "layout": {"label": "Layout", "option__1": {"label": "1 column"}, "option__2": {"label": "2 columns"}, "option__3": {"label": "3 columns"}, "option__4": {"label": "Row"}}, "max_column_size": {"label": "Max column size", "info": "Only for setting \"Layout: 2 or 3 columns \". 0 is infinite"}}}, "products": {"name": "Products", "settings": {"paragraph__1": {"content": "If the \"Theme settings -> Sidebar position on desktop -> Top & vertical filters\" option is selected, then the section will be displayed only on a mobile device."}, "header__1": {"content": "Section settings"}, "title": {"label": "Title"}, "default_state": {"label": "Default state", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "collection": {"label": "Collection"}, "max_count": {"label": "Max count", "info": "0 is infinite"}, "product_type": {"label": "Products type", "option__1": {"label": "Short"}, "option__2": {"label": "Full"}}}}, "custom_html": {"name": "Custom HTML", "settings": {"paragraph__1": {"content": "If the \"Theme settings -> Sidebar position on desktop -> Top & vertical filters\" option is selected, then the section will be displayed only on a mobile device."}, "header__1": {"content": "Section settings"}, "title": {"label": "Title"}, "default_state": {"label": "Default state", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "page_content": {"label": "Page content", "info": "Default page: 'Include Collections Sidebar'"}}}, "subscription": {"name": "Subscription", "settings": {"paragraph__1": {"content": "If the \"Theme settings -> Sidebar position on desktop -> Top & vertical filters\" option is selected, then the section will be displayed only on a mobile device."}, "header__1": {"content": "Section settings"}, "title": {"label": "Title"}, "default_state": {"label": "Default state", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "paragraph": {"label": "Paragraph"}, "placeholder": {"label": "Placeholder"}, "button_text": {"label": "Button text"}}}, "promobox": {"name": "Promo box (Banner)", "settings": {"header__1": {"content": "Section settings"}, "title": {"label": "Title"}, "default_state": {"label": "Default state", "option__1": {"label": "Open"}, "option__2": {"label": "Close"}, "option__3": {"label": "Fixed"}, "option__4": {"label": "Without title"}}, "header__2": {"content": "Content"}, "url": {"label": "URL for banner"}, "header__3": {"content": "Image"}, "image": {"label": "Image", "info": "Recommended size 1440x550 pixels"}, "image_size": {"label": "Image size", "info": "These are the fields for image quality. If images lazy loading is on, the option is ignored"}, "color_image_mask": {"label": "Image mask"}, "image_mask_opacity": {"label": "Image mask opacity"}, "header__4": {"content": "Text"}, "paragraph__1": {"content": "Delete the line value for the text or button if you want to hide the element"}, "paragraph__2": {"content": "Use '<br>' for the line break"}, "text_line_1": {"label": "Text line #1"}, "text_line_2": {"label": "Text line #2"}, "text_line_3": {"label": "Text line #3"}, "style": {"label": "Colorize style", "option__1": {"label": "Style #1"}, "option__2": {"label": "Style #2"}, "option__3": {"label": "Style #3"}, "option__4": {"label": "Style #4"}, "option__5": {"label": "Style #5"}, "option__6": {"label": "Style #6"}, "option__7": {"label": "Style #7"}, "option__8": {"label": "Style #8"}}, "header__5": {"content": "Button #1"}, "button_1": {"label": "Button text"}, "button_1_url": {"label": "URL for the button"}, "color_button_type_1": {"label": "Button style", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Secondary"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Clean"}, "option__5": {"label": "Transparent default"}, "option__6": {"label": "Transparent secondary"}, "option__7": {"label": "Transparent invert"}, "option__8": {"label": "Transparent clean"}}, "header__6": {"content": "Button #2"}, "button_2": {"label": "Button text"}, "button_2_url": {"label": "URL for the button"}, "color_button_type_2": {"label": "Button style", "option__1": {"label": "<PERSON><PERSON><PERSON>"}, "option__2": {"label": "Secondary"}, "option__3": {"label": "Invert"}, "option__4": {"label": "Clean"}, "option__5": {"label": "Transparent default"}, "option__6": {"label": "Transparent secondary"}, "option__7": {"label": "Transparent invert"}, "option__8": {"label": "Transparent clean"}}, "header__7": {"content": "Other content"}, "list_1": {"label": "List", "info": "Select page with list"}, "custom_html": {"label": "Custom HTML", "info": "Replace the whole text content to the page content"}, "header__8": {"content": "Video"}, "video_external_url": {"label": "Youtube or Vimeo video URL"}, "video_mp4_url": {"label": "Video URL", "info": "Upload your video in Admin-> Settings-> Files and add a link here"}, "video_autoplay": {"label": "Video autoplay"}, "video_controls": {"label": "Video controls"}, "header__9": {"content": "Layout"}, "type": {"label": "Type", "option__1": {"label": "Clean (Image without text)"}, "option__2": {"label": "Clean with border"}, "option__3": {"label": "Clean with border on hover"}, "option__4": {"label": "Text v1"}, "option__5": {"label": "Text v2"}, "option__6": {"label": "Text v3"}, "option__7": {"label": "Text v4"}, "option__8": {"label": "Text v5"}, "option__9": {"label": "Text v6"}, "option__10": {"label": "Text v7"}, "option__11": {"label": "Text v8"}, "option__12": {"label": "Text v9"}, "option__13": {"label": "Text v10"}, "option__14": {"label": "Type #1 v1 (Text over the image)"}, "option__15": {"label": "Type #1 v2"}, "option__16": {"label": "Type #1 v3"}, "option__17": {"label": "Type #1 v4"}, "option__18": {"label": "Type #1 v5"}, "option__19": {"label": "Type #1 v6"}, "option__20": {"label": "Type #1 v7"}, "option__21": {"label": "Type #1 v8"}, "option__22": {"label": "Type #1 v9"}, "option__23": {"label": "Type #1 v10"}, "option__24": {"label": "Type #1 v11"}, "option__25": {"label": "Type #1 v12"}, "option__26": {"label": "Type #1 v13"}, "option__27": {"label": "Type #1 v14"}, "option__28": {"label": "Type #1 v15"}, "option__29": {"label": "Type #1 v16"}, "option__30": {"label": "Type #1 v17"}, "option__31": {"label": "Type #1 with Background v1"}, "option__32": {"label": "Type #1 with Background v2"}, "option__33": {"label": "Type #1 with Background v3"}, "option__34": {"label": "Type #1 with Background v4"}, "option__35": {"label": "Type #1 with <PERSON><PERSON>ain v1"}, "option__36": {"label": "Type #1 with <PERSON><PERSON><PERSON> v2"}, "option__37": {"label": "Type #1 with <PERSON><PERSON><PERSON> v3"}, "option__38": {"label": "Type #2 v1 (Text below the image)"}, "option__39": {"label": "Type #2 v2"}, "option__40": {"label": "Type #2 v3"}, "option__41": {"label": "Type #2 v4"}, "option__42": {"label": "Type #2 v5"}, "option__43": {"label": "Type #2 v6"}, "option__44": {"label": "Type #2 v7"}, "option__45": {"label": "Type #2 v8"}, "option__46": {"label": "Type #2 v9"}, "option__47": {"label": "Type #2 v10"}, "option__48": {"label": "Type #2 v11"}, "option__49": {"label": "Type #2 v12"}, "option__50": {"label": "Type #2 v13"}, "option__51": {"label": "Type #3 (Ribbon bottom the image)"}, "option__52": {"label": "Type #4 (Animation ribbon bottom the image)"}}, "header__10": {"content": "Animation"}, "animation_to": {"label": "Move image on hover to", "option__1": {"label": "None"}, "option__2": {"label": "Center"}, "option__3": {"label": "Top left"}, "option__4": {"label": "Top"}, "option__5": {"label": "Top right"}, "option__6": {"label": "Right"}, "option__7": {"label": "Bottom right"}, "option__8": {"label": "Bottom"}, "option__9": {"label": "Bottom left"}, "option__10": {"label": "Left"}}, "animation_from": {"label": "Static image position", "option__1": {"label": "None"}, "option__2": {"label": "Center"}, "option__3": {"label": "Top left"}, "option__4": {"label": "Top"}, "option__5": {"label": "Top right"}, "option__6": {"label": "Right"}, "option__7": {"label": "Bottom right"}, "option__8": {"label": "Bottom"}, "option__9": {"label": "Bottom left"}, "option__10": {"label": "Left"}}, "animation_opacity": {"label": "Image opacity", "option__1": {"label": "None"}, "option__2": {"label": "Static & hover"}, "option__3": {"label": "Static"}, "option__4": {"label": "Hover"}}}}}}, "product_variants": {"name": "Product variants", "settings": {"header__1": {"content": "Support"}, "paragraph__1": {"content": "[Watch video tutorials](https://www.youtube.com/@shopifymise1354)"}, "paragraph__2": {"content": "[Read user manual](https://misell-manual.wraptas.site/)"}}, "blocks": {"product_option_setting": {"name": "Option setting", "settings": {"product": {"label": "Product", "info": "Associate with the product. Is not a required field. If selected - you can assign it to only one product"}, "property": {"label": "Property"}, "value": {"label": "Value"}, "color": {"label": "Color"}, "image": {"label": "Image"}, "assign_to": {"label": "Assign to", "option__1": {"label": "All"}, "option__2": {"label": "Product"}, "option__3": {"label": "Filters"}}, "disable_default_color": {"label": "Disable default color"}, "disable_default_image": {"label": "Disable default image"}}}, "option_images": {"name": "Option images", "settings": {"product": {"label": "Product", "info": "Associate with the product. Is not required field. If selected - you can assign to only for product"}, "property_value_1": {"label": "Property & Value #1", "info": "Example: Property|Value"}, "image_1": {"label": "Image #1"}, "property_value_2": {"label": "Property & Value #2"}, "image_2": {"label": "Image #2"}, "property_value_3": {"label": "Property & Value #3"}, "image_3": {"label": "Image #3"}, "property_value_4": {"label": "Property & Value #4"}, "image_4": {"label": "Image #4"}, "property_value_5": {"label": "Property & Value #5"}, "image_5": {"label": "Image #5"}, "property_value_6": {"label": "Property & Value #6"}, "image_6": {"label": "Image #6"}, "property_value_7": {"label": "Property & Value #7"}, "image_7": {"label": "Image #7"}, "property_value_8": {"label": "Property & Value #8"}, "image_8": {"label": "Image #8"}, "property_value_9": {"label": "Property & Value #9"}, "image_9": {"label": "Image #9"}, "property_value_10": {"label": "Property & Value #10"}, "image_10": {"label": "Image #10"}}}, "option_colors": {"name": "Option colors", "settings": {"product": {"label": "Product", "info": "Associate with the product. Is not required field. If selected - you can assign to only for product"}, "property_value_1": {"label": "Property & Value #1", "info": "Example: Property|Value"}, "color_1": {"label": "Color #1"}, "property_value_2": {"label": "Property & Value #2"}, "color_2": {"label": "Color #2"}, "property_value_3": {"label": "Property & Value #3"}, "color_3": {"label": "Color #3"}, "property_value_4": {"label": "Property & Value #4"}, "color_4": {"label": "Color #4"}, "property_value_5": {"label": "Property & Value #5"}, "color_5": {"label": "Color #5"}, "property_value_6": {"label": "Property & Value #6"}, "color_6": {"label": "Color #6"}, "property_value_7": {"label": "Property & Value #7"}, "color_7": {"label": "Color #7"}, "property_value_8": {"label": "Property & Value #8"}, "color_8": {"label": "Color #8"}, "property_value_9": {"label": "Property & Value #9"}, "color_9": {"label": "Color #9"}, "property_value_10": {"label": "Property & Value #10"}, "color_10": {"label": "Color #10"}}}}}}}