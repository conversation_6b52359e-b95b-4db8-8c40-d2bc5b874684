/**
 * カレンダーコンポーネントテストページ
 *
 * このページはカレンダー表示と日付選択機能のテストを行います。
 * 予約済み日付、祝日、日曜日、メンテナンス期間の表示を確認できます。
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  BlockStack,
  Text,
  Select,
  Banner,
  InlineStack,
  Box,
  Divider,
  Frame,
  Tabs
} from "@shopify/polaris";
import { useState, useCallback, useEffect } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { UnifiedCalendarWithErrorBoundary } from "../components/Calendar/UnifiedCalendar";
import { addDays } from "date-fns";

/**
 * ローダー関数
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // 商品データを取得
    const products = await prisma.product.findMany({
      where: { shop },
      select: {
        id: true,
        title: true,
        sku: true,
        status: true,
        price: true,
        shopifyId: true
      },
      orderBy: { title: 'asc' },
    });

    // 予約データを取得
    const bookings = await prisma.booking.findMany({
      where: {
        shop,
        status: { in: ['PROVISIONAL', 'CONFIRMED'] }
      },
      select: {
        id: true,
        bookingId: true,
        productId: true,
        startDate: true,
        endDate: true,
        status: true,
        bookingType: true
      },
      orderBy: { startDate: 'asc' },
    });

    // Date型を文字列に変換
    const serializedBookings = bookings.map(b => ({
      ...b,
      startDate: b.startDate.toISOString(),
      endDate: b.endDate.toISOString()
    }));

    return json({
      products,
      bookings: serializedBookings,
      shop
    });
  } catch (error) {
    console.error("データ取得エラー:", error);
    return json({
      products: [],
      bookings: [],
      shop: session.shop,
      error: "データの取得中にエラーが発生しました"
    });
  }
}

export default function CalendarTest() {
  const { products, bookings, shop, error } = useLoaderData<typeof loader>();
  
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedProductId, setSelectedProductId] = useState("");
  const [errorMessage, setErrorMessage] = useState(error || "");
  const [selectedDates, setSelectedDates] = useState({
    start: new Date(),
    end: addDays(new Date(), 3)
  });

  // 選択された商品の予約を取得
  const productBookings = selectedProductId
    ? bookings.filter(b => b.productId === selectedProductId)
    : bookings;

  // タブの設定
  const tabs = [
    {
      id: "basic-calendar",
      content: "基本カレンダー",
      accessibilityLabel: "基本カレンダータブ",
      panelID: "basic-calendar-panel",
    },
    {
      id: "product-calendar",
      content: "商品別カレンダー",
      accessibilityLabel: "商品別カレンダータブ",
      panelID: "product-calendar-panel",
    },
    {
      id: "all-bookings",
      content: "全予約表示",
      accessibilityLabel: "全予約表示タブ",
      panelID: "all-bookings-panel",
    },
  ];

  // 日付選択処理
  const handleDateSelection = (start, end) => {
    if (start && end) {
      setSelectedDates({ start, end });
      console.log("選択された日付:", start, "〜", end);
    }
  };

  return (
    <Frame>
      <Page
        title="カレンダーテスト"
        backAction={{ content: "戻る", url: "/app" }}
      >
        {errorMessage && (
          <Banner status="critical" onDismiss={() => setErrorMessage("")}>
            <p>{errorMessage}</p>
          </Banner>
        )}
        
        <Layout>
          <Layout.Section>
            <Tabs
              tabs={tabs}
              selected={selectedTab}
              onSelect={(index) => setSelectedTab(index)}
            />
            
            <Card>
              {selectedTab === 0 && (
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">基本カレンダー</Text>
                  <Text>基本的なカレンダー表示と日付選択機能のテスト</Text>
                  
                  <Box paddingBlockStart="400">
                    <UnifiedCalendarWithErrorBoundary
                      bookings={[]}
                      initialStartDate={selectedDates.start}
                      initialEndDate={selectedDates.end}
                      minDate={new Date()}
                      maxDate={addDays(new Date(), 365)}
                      onRangeSelect={handleDateSelection}
                      showSelectedDates={true}
                      showLegend={true}
                      showValidationErrors={true}
                    />
                  </Box>
                  
                  <Divider />
                  
                  <InlineStack gap="400">
                    <Text>選択された日付:</Text>
                    <Text>{selectedDates.start.toLocaleDateString()} 〜 {selectedDates.end.toLocaleDateString()}</Text>
                  </InlineStack>
                </BlockStack>
              )}
              
              {selectedTab === 1 && (
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">商品別カレンダー</Text>
                  
                  <Select
                    label="商品を選択"
                    options={[
                      { label: "商品を選択してください", value: "" },
                      ...products.map(p => ({ label: p.title, value: p.id }))
                    ]}
                    value={selectedProductId}
                    onChange={setSelectedProductId}
                  />
                  
                  {selectedProductId ? (
                    <Box paddingBlockStart="400">
                      <UnifiedCalendarWithErrorBoundary
                        bookings={productBookings.map(booking => ({
                          startDate: new Date(booking.startDate),
                          endDate: new Date(booking.endDate),
                          bookingType: booking.bookingType
                        }))}
                        initialStartDate={selectedDates.start}
                        initialEndDate={selectedDates.end}
                        minDate={new Date()}
                        maxDate={addDays(new Date(), 365)}
                        onRangeSelect={handleDateSelection}
                        showSelectedDates={true}
                        showLegend={true}
                        showValidationErrors={true}
                      />
                    </Box>
                  ) : (
                    <Banner>商品を選択してください</Banner>
                  )}
                  
                  <Divider />
                  
                  <InlineStack gap="400">
                    <Text>選択された日付:</Text>
                    <Text>{selectedDates.start.toLocaleDateString()} 〜 {selectedDates.end.toLocaleDateString()}</Text>
                  </InlineStack>
                </BlockStack>
              )}
              
              {selectedTab === 2 && (
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">全予約表示</Text>
                  <Text>すべての予約を表示するカレンダー</Text>
                  
                  <Box paddingBlockStart="400">
                    <UnifiedCalendarWithErrorBoundary
                      bookings={bookings.map(booking => ({
                        startDate: new Date(booking.startDate),
                        endDate: new Date(booking.endDate),
                        bookingType: booking.bookingType
                      }))}
                      initialStartDate={selectedDates.start}
                      initialEndDate={selectedDates.end}
                      minDate={new Date()}
                      maxDate={addDays(new Date(), 365)}
                      onRangeSelect={handleDateSelection}
                      showSelectedDates={true}
                      showLegend={true}
                      showValidationErrors={true}
                    />
                  </Box>
                  
                  <Divider />
                  
                  <BlockStack gap="200">
                    <Text variant="headingMd">予約一覧</Text>
                    {bookings.length > 0 ? (
                      bookings.map(booking => (
                        <Card key={booking.id}>
                          <BlockStack gap="200">
                            <Text variant="headingSm">予約ID: {booking.bookingId}</Text>
                            <Text>期間: {new Date(booking.startDate).toLocaleDateString()} 〜 {new Date(booking.endDate).toLocaleDateString()}</Text>
                            <Text>ステータス: {booking.status}</Text>
                            <Text>タイプ: {booking.bookingType}</Text>
                          </BlockStack>
                        </Card>
                      ))
                    ) : (
                      <Text>予約データがありません</Text>
                    )}
                  </BlockStack>
                </BlockStack>
              )}
            </Card>
          </Layout.Section>
        </Layout>
      </Page>
    </Frame>
  );
}
