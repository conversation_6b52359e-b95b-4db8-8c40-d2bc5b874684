import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit, useNavigate } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  BlockStack,
  Button,
  InlineStack,
  Banner,
  ProgressBar,
  Spinner,
  List,
  Divider
} from "@shopify/polaris";
import { useState, useCallback, useEffect } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";

/**
 * 顧客同期ページのローダー
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // 最新の同期情報を取得
    const lastSync = await prisma.syncLog.findFirst({
      where: {
        shop,
        syncType: "CUSTOMER",
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // 顧客数を取得
    const customersCount = await prisma.customer.count({
      where: {
        shop,
      },
    });

    return json({
      lastSync,
      customersCount,
    });
  } catch (error) {
    console.error("データ取得エラー:", error);
    return json({
      error: "データの取得中にエラーが発生しました",
      lastSync: null,
      customersCount: 0,
    });
  }
}

/**
 * 顧客同期ページ
 */
export default function CustomersSyncPage() {
  const { lastSync, customersCount, error } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const navigate = useNavigate();

  // 同期状態
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncProgress, setSyncProgress] = useState(0);
  const [syncResult, setSyncResult] = useState<any>(null);
  const [syncError, setSyncError] = useState<string | null>(null);

  // 同期処理
  const handleSync = useCallback(async () => {
    setIsSyncing(true);
    setSyncProgress(10);
    setSyncResult(null);
    setSyncError(null);

    try {
      // 顧客同期APIを呼び出す
      const response = await fetch("/api/customers/sync", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      setSyncProgress(50);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "顧客同期に失敗しました");
      }

      const result = await response.json();
      setSyncProgress(100);
      setSyncResult(result);
    } catch (err) {
      setSyncError(err instanceof Error ? err.message : "顧客同期中にエラーが発生しました");
    } finally {
      setIsSyncing(false);
    }
  }, []);

  // 同期完了後に自動的にリロード
  useEffect(() => {
    if (syncProgress === 100 && syncResult) {
      const timer = setTimeout(() => {
        window.location.reload();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [syncProgress, syncResult]);

  // 最終同期日時をフォーマット
  const formatLastSyncDate = (dateString: string | null) => {
    if (!dateString) return "なし";
    const date = new Date(dateString);
    return date.toLocaleString("ja-JP", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <Page
      title="顧客データ同期"
      subtitle="Shopifyの顧客データをアプリと同期します"
      backAction={{ content: "顧客一覧に戻る", url: "/app/customers" }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                {error && (
                  <Box paddingBlockEnd="400">
                    <Banner tone="critical">
                      <p>{error}</p>
                    </Banner>
                  </Box>
                )}

                <Text variant="headingMd" as="h2">
                  同期ステータス
                </Text>

                <BlockStack gap="200">
                  <Text as="p">
                    <strong>登録顧客数:</strong> {customersCount}件
                  </Text>
                  <Text as="p">
                    <strong>最終同期日時:</strong> {formatLastSyncDate(lastSync?.createdAt)}
                  </Text>
                  {lastSync && (
                    <Text as="p">
                      <strong>前回の同期結果:</strong> 作成: {lastSync.created}件, 更新: {lastSync.updated}件, エラー: {lastSync.errors}件
                    </Text>
                  )}
                </BlockStack>

                <Divider />

                <Text variant="headingMd" as="h2">
                  顧客データを同期
                </Text>

                <Text as="p">
                  Shopifyの顧客データをアプリのデータベースと同期します。この処理には数分かかる場合があります。
                </Text>

                {isSyncing ? (
                  <BlockStack gap="400">
                    <ProgressBar progress={syncProgress} />
                    <Box padding="400" textAlign="center">
                      <Spinner accessibilityLabel="同期中" size="large" />
                      <Text as="p" fontWeight="bold">
                        顧客データを同期中...
                      </Text>
                    </Box>
                  </BlockStack>
                ) : (
                  <Box paddingBlockStart="400">
                    <Button primary onClick={handleSync} size="large">
                      今すぐ同期する
                    </Button>
                  </Box>
                )}

                {syncResult && (
                  <Banner tone="success">
                    <p>顧客データの同期が完了しました。</p>
                    <List>
                      <List.Item>作成: {syncResult.results.created}件</List.Item>
                      <List.Item>更新: {syncResult.results.updated}件</List.Item>
                      <List.Item>スキップ: {syncResult.results.skipped}件</List.Item>
                      <List.Item>エラー: {syncResult.results.errors}件</List.Item>
                    </List>
                  </Banner>
                )}

                {syncError && (
                  <Banner tone="critical">
                    <p>{syncError}</p>
                  </Banner>
                )}
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">
                  同期に関する注意事項
                </Text>

                <List>
                  <List.Item>
                    同期処理では、Shopifyの顧客データをアプリのデータベースにコピーします。
                  </List.Item>
                  <List.Item>
                    顧客のメタフィールド（rental名前空間）も同期されます。
                  </List.Item>
                  <List.Item>
                    メールアドレスが一致する顧客は更新されます。
                  </List.Item>
                  <List.Item>
                    同期中にエラーが発生した場合は、ログを確認してください。
                  </List.Item>
                </List>
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
