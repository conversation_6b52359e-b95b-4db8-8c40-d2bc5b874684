# 商品バリエーション方式実装手順

## 概要

この文書では、レンタル商品ECシステムにおける商品バリエーション方式の実装手順を説明します。この方式では、レンタル日数に応じた8種類のバリエーションを各商品に設定し、標準的なShopifyカートとチェックアウトフローを使用します。

## 実装内容

1. 日付選択機能の維持
2. 選択された日数に基づく適切なバリエーションの自動選択
3. カートに追加する際のレンタル情報の保存
4. カート画面でのレンタル情報の表示

## 実装手順

### 1. 商品バリエーションの設定

各商品に9種類のバリエーションを設定します：

```
オプション名: レンタル日数
オプション値: 1日, 2日, 3日, 4日, 5日, 6日, 7日, 8日以上, 仮予約
```

各バリエーションの価格は、基本料金に基づいて計算します：

1. **1日レンタル**: 基本料金の100%
2. **2日レンタル**: 基本料金の100% + 20% = 120%
3. **3日レンタル**: 基本料金の100% + 20% + 20% = 140%
4. **4日レンタル**: 基本料金の100% + 20% + 20% + 20% = 160%
5. **5日レンタル**: 基本料金の100% + 20% + 20% + 20% + 20% = 180%
6. **6日レンタル**: 基本料金の100% + 20% + 20% + 20% + 20% + 20% = 200%
7. **7日レンタル**: 基本料金の100% + 20% + 20% + 20% + 20% + 20% + 20% = 220%
8. **8日以上**: 基本料金の220%（8日目以降は別途計算）
9. **仮予約**: 基本料金の10%（仮予約金額）

### 2. 日付選択機能の修正

`date_picker.liquid`ファイルを修正して、以下の機能を追加しました：

1. 日付選択に基づいて適切なバリエーションを自動選択する機能
2. バリエーション選択時の価格再計算機能
3. カートに追加する際のレンタル情報の保存

主な変更点：

- `selectVariantBasedOnDays`関数：日数に基づいて適切なバリエーションを選択
- `updateShopifyVariantSelector`関数：Shopifyの標準バリアントセレクターを更新
- カートに追加する際のプロパティ設定の拡張：レンタル情報をより詳細に保存

### 3. カート表示のカスタマイズ

カートページでレンタル情報を表示するために、`rental-cart.js`ファイルを作成しました。このスクリプトは以下の機能を提供します：

1. レンタル期間の表示
2. レンタル日数の表示
3. 価格表示の調整

### 4. テーマへの統合

テーマのカートページ（`cart.liquid`または`main-cart.liquid`）に以下のコードを追加して、カスタムJavaScriptを読み込みます：

```liquid
<!-- レンタル商品カート表示カスタマイズ -->
{{ 'rental-cart.js' | asset_url | script_tag }}
```

## 8日以上のレンタルの処理

8日以上のレンタルについては、以下の方法で処理します：

1. 「8日以上」バリエーションを選択（価格は7日レンタルと同じ）
2. レンタル情報をLine Item Propertiesとして保存
3. カート画面で実際のレンタル日数と料金を表示

## 注意点

1. **商品バリエーションの管理**
   - 新しい商品を追加する際に、8種類のバリエーションを設定する必要があります
   - テンプレート商品を作成するか、一括編集ツールを使用することを推奨します

2. **在庫管理**
   - 同じ商品の異なるバリエーションでも、在庫は共有されるように設定する必要があります

3. **価格表示**
   - カート画面では、バリエーションの価格ではなく、計算されたレンタル料金を表示します
   - チェックアウト画面では、バリエーションの価格が表示されます

## テスト手順

1. 商品ページで日付を選択し、バリエーションが自動選択されることを確認
2. カートに追加し、レンタル情報が正しく表示されることを確認
3. チェックアウトに進み、注文が正常に処理されることを確認

## 今後の課題

1. **商品バリエーションの一括設定**
   - 多数の商品に対して効率的にバリエーションを設定する方法の検討
   - Shopify APIを使用した自動化スクリプトの開発

2. **予約カレンダーとの連携**
   - 予約状況をカレンダーに表示する機能の維持
   - 在庫管理との連携

3. **注文後の処理**
   - 注文情報からレンタル期間を抽出する機能
   - 予約管理システムとの連携
