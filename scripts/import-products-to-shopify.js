/**
 * Shopify商品登録スクリプト
 *
 * このスクリプトは、クレンジングされたCSVファイルから商品データを読み込み、
 * Shopify Admin APIを使用して商品を登録します。
 */

import { GraphQLClient, gql } from 'graphql-request';
import fs from 'fs';
import { parse } from 'csv-parse/sync';
import dotenv from 'dotenv';

dotenv.config();

// 設定
const INPUT_PATH = 'master-data-csv/cleaned_products.csv';
const BATCH_SIZE = 10; // 一度に処理する商品数

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品作成のGraphQLミューテーション
const CREATE_PRODUCT = gql`
  mutation productCreate($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// バリアント作成のGraphQLミューテーション
const CREATE_VARIANT = gql`
  mutation productVariantCreate($input: ProductVariantInput!) {
    productVariantCreate(input: $input) {
      productVariant {
        id
        title
        sku
        price
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールド設定のGraphQLミューテーション
const SET_METAFIELDS = gql`
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// CSVファイルを読み込む関数
function readCsvFile(filePath) {
  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const records = parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
      relax_quotes: true,
      relax_column_count: true
    });
    return records;
  } catch (error) {
    console.error(`CSVファイル ${filePath} の読み込み中にエラーが発生しました:`, error);
    return [];
  }
}

// 商品データをグループ化する関数
function groupProductData(records) {
  const productGroups = {};

  for (const record of records) {
    const handle = record.Handle;

    if (!productGroups[handle]) {
      productGroups[handle] = {
        product: record,
        variants: []
      };
    }

    if (record['Option1 Value'] !== '1日レンタル') {
      productGroups[handle].variants.push(record);
    }
  }

  return Object.values(productGroups);
}

// 商品を作成する関数
async function createProduct(productData) {
  try {
    const result = await client.request(CREATE_PRODUCT, {
      input: {
        title: productData.Title,
        descriptionHtml: productData['Body HTML'],
        vendor: productData.Vendor,
        productType: productData.Type,
        tags: productData.Tags.split(','),
        published: productData.Published === 'TRUE',
        options: ['レンタル日数'],
        variants: [
          {
            price: productData['Variant Price'],
            sku: productData['Variant SKU'],
            inventoryQuantities: {
              availableQuantity: parseInt(productData['Variant Inventory Qty']),
              locationId: "gid://shopify/Location/1"
            },
            inventoryManagement: 'SHOPIFY',
            inventoryPolicy: 'DENY',
            requiresShipping: productData['Variant Requires Shipping'] === 'TRUE',
            taxable: productData['Variant Taxable'] === 'TRUE',
            weight: 0,
            weightUnit: 'KILOGRAMS',
            option1: productData['Option1 Value']
          }
        ]
      }
    });

    if (result.productCreate.userErrors.length > 0) {
      console.error(`商品 ${productData.Title} の作成中にエラーが発生しました:`, result.productCreate.userErrors);
      return null;
    }

    console.log(`商品 ${productData.Title} を作成しました。ID: ${result.productCreate.product.id}`);
    return result.productCreate.product;
  } catch (error) {
    console.error(`商品 ${productData.Title} の作成中にエラーが発生しました:`, error);
    return null;
  }
}

// バリアントを作成する関数
async function createVariant(productId, variantData) {
  try {
    const result = await client.request(CREATE_VARIANT, {
      input: {
        productId,
        price: variantData['Variant Price'],
        sku: variantData['Variant SKU'],
        inventoryQuantities: {
          availableQuantity: parseInt(variantData['Variant Inventory Qty']),
          locationId: "gid://shopify/Location/1"
        },
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY',
        requiresShipping: variantData['Variant Requires Shipping'] === 'TRUE',
        taxable: variantData['Variant Taxable'] === 'TRUE',
        weight: 0,
        weightUnit: 'KILOGRAMS',
        options: [variantData['Option1 Value']]
      }
    });

    if (result.productVariantCreate.userErrors.length > 0) {
      console.error(`バリアント ${variantData['Option1 Value']} の作成中にエラーが発生しました:`, result.productVariantCreate.userErrors);
      return null;
    }

    console.log(`バリアント ${variantData['Option1 Value']} を作成しました。ID: ${result.productVariantCreate.productVariant.id}`);
    return result.productVariantCreate.productVariant;
  } catch (error) {
    console.error(`バリアント ${variantData['Option1 Value']} の作成中にエラーが発生しました:`, error);
    return null;
  }
}

// メタフィールドを設定する関数
async function setMetafields(ownerId, productData) {
  try {
    const metafields = [];

    // メタフィールドを抽出
    for (const key in productData) {
      if (key.startsWith('Metafield:')) {
        const [, metafieldInfo] = key.split('Metafield:');
        const [namespaceKey, type] = metafieldInfo.trim().split(' [');
        const [namespace, metafieldKey] = namespaceKey.split('.');
        const metafieldType = type.replace(']', '');

        if (productData[key]) {
          let value = productData[key];
          let metafieldInput = {
            ownerId,
            namespace,
            key: metafieldKey
          };

          if (metafieldType === 'json') {
            metafieldInput.value = value;
            metafieldInput.type = 'json';
          } else if (metafieldType === 'boolean') {
            metafieldInput.value = value.toLowerCase();
            metafieldInput.type = 'boolean';
          } else if (metafieldType === 'number_integer' || metafieldType === 'number_decimal') {
            metafieldInput.value = value;
            metafieldInput.type = metafieldType;
          } else if (metafieldType === 'date') {
            metafieldInput.value = value;
            metafieldInput.type = 'date';
          } else if (metafieldType === 'multi_line_text_field') {
            metafieldInput.value = value;
            metafieldInput.type = 'multi_line_text_field';
          } else {
            metafieldInput.value = value;
            metafieldInput.type = 'single_line_text_field';
          }

          metafields.push(metafieldInput);
        }
      }
    }

    if (metafields.length === 0) {
      console.log('設定するメタフィールドがありません。');
      return true;
    }

    const result = await client.request(SET_METAFIELDS, {
      metafields
    });

    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定中にエラーが発生しました:', result.metafieldsSet.userErrors);
      return false;
    }

    console.log(`${metafields.length}件のメタフィールドを設定しました。`);
    return true;
  } catch (error) {
    console.error('メタフィールド設定中にエラーが発生しました:', error);
    return false;
  }
}

// 商品を登録する関数
async function importProducts(productGroups, startIndex = 0, endIndex = BATCH_SIZE) {
  const batchGroups = productGroups.slice(startIndex, endIndex);

  console.log(`${startIndex + 1}〜${Math.min(endIndex, productGroups.length)}件目の商品を処理中...（全${productGroups.length}件）`);

  for (const group of batchGroups) {
    // 商品を作成
    const product = await createProduct(group.product);
    if (!product) continue;

    // バリアントを作成
    for (const variantData of group.variants) {
      await createVariant(product.id, variantData);
    }

    // メタフィールドを設定
    await setMetafields(product.id, group.product);

    // 処理間隔を空ける（APIレート制限対策）
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // 次のバッチがある場合は再帰的に処理
  if (endIndex < productGroups.length) {
    await importProducts(productGroups, endIndex, endIndex + BATCH_SIZE);
  }
}

// メイン処理
async function main() {
  console.log('Shopify商品登録を開始します...');

  // CSVファイルを読み込む
  console.log(`${INPUT_PATH} を読み込んでいます...`);
  const records = readCsvFile(INPUT_PATH);
  console.log(`${records.length}件の商品データを読み込みました。`);

  // 商品データをグループ化
  console.log('商品データをグループ化しています...');
  const productGroups = groupProductData(records);
  console.log(`${productGroups.length}件の商品グループを生成しました。`);

  // 商品を登録
  console.log('商品を登録しています...');
  await importProducts(productGroups);

  console.log('Shopify商品登録が完了しました。');
}

// スクリプト実行
main().catch(error => {
  console.error('スクリプト実行中にエラーが発生しました:', error);
  process.exit(1);
});
