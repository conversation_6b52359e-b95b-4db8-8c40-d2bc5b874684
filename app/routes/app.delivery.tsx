import { useState } from 'react';
import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate, useSearchParams, Outlet, useLocation } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import {
  Page,
  LegacyCard,
  ResourceList,
  ResourceItem,
  Filters,
  Badge,
  EmptyState,
  Text,
  Pagination
} from '@shopify/polaris';
import { prisma } from '../db.server';

// 配送ステータスに応じたバッジ設定
const deliveryStatusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  pending: { tone: 'info', label: '待機中' },
  picked: { tone: 'attention', label: 'ピッキング済み' },
  shipped: { tone: 'warning', label: '発送済み' },
  completed: { tone: 'success', label: '完了' },
  delayed: { tone: 'critical', label: '遅延' },
  cancelled: { tone: 'critical', label: 'キャンセル' }
};

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);

  // クエリパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  const limit = parseInt(url.searchParams.get('limit') || '50', 10);
  const status = url.searchParams.get('status') || undefined;
  const type = url.searchParams.get('type') || undefined;
  const query = url.searchParams.get('query') || undefined;
  const startDate = url.searchParams.get('startDate') || undefined;
  const endDate = url.searchParams.get('endDate') || undefined;

  // 検索条件を構築
  const where: any = {};

  if (status) {
    where.returnStatus = status;
  }

  if (type) {
    where.deliveryType = type;
  }

  if (query) {
    where.OR = [
      { destination: { contains: query, mode: 'insensitive' } },
      { address: { contains: query, mode: 'insensitive' } },
      { notes: { contains: query, mode: 'insensitive' } },
      {
        booking: {
          OR: [
            { bookingId: { contains: query } },
            { customerName: { contains: query, mode: 'insensitive' } },
            { customerEmail: { contains: query, mode: 'insensitive' } },
            {
              product: {
                title: { contains: query, mode: 'insensitive' }
              }
            },
            {
              product: {
                sku: { contains: query }
              }
            }
          ]
        }
      }
    ];
  }

  if (startDate) {
    where.deliveryDate = { gte: new Date(startDate) };
  }

  if (endDate) {
    where.deliveryDate = {
      ...(where.deliveryDate || {}),
      lte: new Date(endDate)
    };
  }

  // 配送データを取得
  const deliveries = await prisma.deliverySchedule.findMany({
    where,
    include: {
      booking: {
        include: {
          product: true
        }
      },
      staff: true,
      carrier: true,
      pickingStaff: true,
      shippingFee: true
    },
    orderBy: [
      { deliveryDate: 'asc' }
    ],
    skip: (page - 1) * limit,
    take: limit
  });

  // 総件数を取得
  const total = await prisma.deliverySchedule.count({ where });

  return json({
    deliveries: deliveries.map(delivery => ({
      id: delivery.id,
      bookingId: delivery.booking?.bookingId || '',
      productTitle: delivery.booking?.product?.title || '',
      productSku: delivery.booking?.product?.sku || '',
      status: delivery.returnStatus || delivery.pickingStatus || 'pending',
      type: delivery.deliveryType,
      customerName: delivery.booking?.customerName || '',
      customerEmail: delivery.booking?.customerEmail || '',
      address: delivery.address || delivery.destination || '',
      scheduledDate: delivery.deliveryDate.toISOString(),
      completedDate: delivery.actualReturnDate?.toISOString() || null,
      trackingNumber: delivery.orderId || '',
      carrier: delivery.carrier?.name || '',
      cost: delivery.shippingFeeAmount?.toString() || '',
      notes: delivery.notes || ''
    })),
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}

export default function DeliveryIndex() {
  const { deliveries, pagination } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();

  // URLパラメータから初期値を設定
  const [statusFilter, setStatusFilter] = useState(searchParams.get('status') || '');
  const [typeFilter, setTypeFilter] = useState(searchParams.get('type') || '');
  const [searchQuery, setSearchQuery] = useState(searchParams.get('query') || '');

  // 配送詳細ページへの遷移
  const handleDeliveryClick = (id: string) => {
    navigate(`/app/delivery/${id}`);
  };

  // ステータスフィルターを適用
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    const params = new URLSearchParams(window.location.search);

    if (value) {
      params.set('status', value);
    } else {
      params.delete('status');
    }

    navigate(`/app/delivery?${params.toString()}`);
  };

  // タイプフィルターを適用
  const handleTypeFilterChange = (value: string) => {
    setTypeFilter(value);
    const params = new URLSearchParams(window.location.search);

    if (value) {
      params.set('type', value);
    } else {
      params.delete('type');
    }

    navigate(`/app/delivery?${params.toString()}`);
  };

  // 検索クエリを適用
  const handleSearchSubmit = () => {
    const params = new URLSearchParams(window.location.search);

    if (searchQuery) {
      params.set('query', searchQuery);
    } else {
      params.delete('query');
    }

    navigate(`/app/delivery?${params.toString()}`);
  };

  // ページネーション処理
  const handlePaginationChange = (newPage: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set('page', newPage.toString());
    navigate(`/app/delivery?${params.toString()}`);
  };

  // 日付をフォーマット
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  // フィルターオプションを生成
  const filters = [
    {
      key: 'status',
      label: 'ステータス',
      filter: (
        <select
          value={statusFilter}
          onChange={(e) => handleStatusFilterChange(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="">すべて</option>
          <option value="pending">返品待ち</option>
          <option value="completed">返品完了</option>
          <option value="delayed">返品遅延</option>
        </select>
      ),
      shortcut: true,
    },
    {
      key: 'type',
      label: 'タイプ',
      filter: (
        <select
          value={typeFilter}
          onChange={(e) => handleTypeFilterChange(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="">すべて</option>
          <option value="delivery">配送</option>
          <option value="pickup">集荷</option>
        </select>
      ),
      shortcut: true,
    },
    {
      key: 'date',
      label: '予定日',
      filter: (
        <div>
          <input
            type="date"
            onChange={(e) => {
              const params = new URLSearchParams(window.location.search);
              if (e.target.value) {
                params.set('startDate', e.target.value);
              } else {
                params.delete('startDate');
              }
              navigate(`/app/delivery?${params.toString()}`);
            }}
            style={{ width: '100%', padding: '8px' }}
          />
        </div>
      ),
    }
  ];

  // 子ルートが表示される場合は、子ルートを表示
  if (location.pathname !== '/app/delivery') {
    return <Outlet />;
  }

  return (
    <Page
      title="【親】配送管理 - 概要"
      primaryAction={{
        content: "新規配送",
        url: "/app/delivery/new"
      }}
    >
      <LegacyCard>
        <ResourceList
          resourceName={{ singular: '配送', plural: '配送' }}
          items={deliveries}
          renderItem={(delivery) => {
            const {
              id,
              bookingId,
              productTitle,
              productSku,
              status,
              type,
              customerName,
              address,
              scheduledDate,
              completedDate,
              trackingNumber,
              carrier
            } = delivery;

            const badge = deliveryStatusBadgeMap[status] || { tone: 'new', label: status };
            const typeLabel = type === 'delivery' ? '配送' : '集荷';

            const shortcutActions = [
              {
                content: '詳細',
                url: `/app/delivery/${id}`,
              },
              {
                content: '編集',
                url: `/app/delivery/edit/${id}`,
              }
            ];

            if (status === 'pending' || status === 'picked') {
              shortcutActions.push({
                content: '発送',
                url: `/app/delivery/ship/${id}`,
              });
            }

            if (status === 'shipped') {
              shortcutActions.push({
                content: '配送完了',
                url: `/app/delivery/complete/${id}`,
              });
            }

            return (
              <ResourceItem
                id={id}
                onClick={() => handleDeliveryClick(id)}
                shortcutActions={shortcutActions}
                persistActions
              >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <Text as="h3" variant="bodyMd" fontWeight="bold">
                      {typeLabel}: {productTitle} {productSku ? `(${productSku})` : ''}
                    </Text>
                    {bookingId && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodyMd">
                          予約ID: {bookingId}
                        </Text>
                      </div>
                    )}
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodyMd">
                        {customerName} 様
                      </Text>
                    </div>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        住所: {address}
                      </Text>
                    </div>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        予定日: {formatDate(scheduledDate)}
                        {completedDate && ` / 完了日: ${formatDate(completedDate)}`}
                      </Text>
                    </div>
                    {trackingNumber && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          追跡番号: {trackingNumber} {carrier && `(${carrier})`}
                        </Text>
                      </div>
                    )}
                  </div>
                  <div>
                    <Badge tone={badge.tone}>{badge.label}</Badge>
                  </div>
                </div>
              </ResourceItem>
            );
          }}
          filterControl={
            <Filters
              queryValue={searchQuery}
              filters={filters}
              onQueryChange={setSearchQuery}
              onQueryClear={() => setSearchQuery('')}
              onClearAll={() => {
                setSearchQuery('');
                setStatusFilter('');
                setTypeFilter('');
                navigate('/app/delivery');
              }}
              onQueryBlur={handleSearchSubmit}
              hideQueryField={false}
            />
          }
          emptyState={
            <EmptyState
              heading="配送データがありません"
              image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
            >
              <p>配送データを作成して、商品の配送状況を管理しましょう。</p>
            </EmptyState>
          }
        />
        {pagination.totalPages > 1 && (
          <div style={{ padding: '16px', display: 'flex', justifyContent: 'center' }}>
            <Pagination
              hasPrevious={pagination.page > 1}
              onPrevious={() => handlePaginationChange(pagination.page - 1)}
              hasNext={pagination.page < pagination.totalPages}
              onNext={() => handlePaginationChange(pagination.page + 1)}
            />
          </div>
        )}
      </LegacyCard>
      <div style={{ marginTop: '16px', textAlign: 'right' }}>
        <Text as="p" variant="bodySm" tone="subdued">
          合計 {pagination.total} 件の配送データ ( {pagination.page} / {pagination.totalPages} ページ )
        </Text>
      </div>
    </Page>
  );
}
