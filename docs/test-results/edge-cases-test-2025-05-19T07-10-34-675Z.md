# エッジケースと境界値テストレポート

## テスト概要

このレポートは、年末年始や連休などの特殊期間の予約、予約期間の最大・最小値、在庫数ゼロの状態からの回復、無効なデータ入力時の処理のテスト結果をまとめたものです。

テスト実行日時: 2025/5/19 16:10:34
実行時間: 25.16秒

## テスト環境

- Node.js バージョン: v20.18.1
- OS: dar<PERSON> arm64

## テスト結果

- 合計テスト数: 10
- 成功: 7
- 失敗: 3
- 成功率: 70%

## パフォーマンス分析

- 平均テスト実行時間: 2325.60ミリ秒
- 最も遅いテスト: 予約期間の最大値テスト (14054.00ミリ秒)
- 最も速いテスト: 予約をキャンセルして在庫を回復するテスト (0.00ミリ秒)

## 詳細結果

### テストケース1: 年末年始の予約テスト

- 結果: ✅ 成功
- メッセージ: 年末年始の予約が正常に作成されました

- 実行時間: 4509.00ミリ秒
- 詳細:
```json
{
  "bookingId": "89708220-bb9b-4364-ad90-949043969e72",
  "period": "年末年始",
  "startDate": "2025-12-29",
  "endDate": "2026-01-03"
}
```

### テストケース2: 予約期間の最小値テスト

- 結果: ✅ 成功
- メッセージ: 最小予約期間（1日）の予約が正常に作成されました

- 実行時間: 985.00ミリ秒
- 詳細:
```json
{
  "bookingId": "e371aab9-1d69-45bf-a6f3-f5e60c96b34e",
  "durationDays": 1
}
```

### テストケース3: 予約期間の最大値テスト

- 結果: ✅ 成功
- メッセージ: 最大予約期間（30日）の予約が正常に作成されました

- 実行時間: 14054.00ミリ秒
- 詳細:
```json
{
  "bookingId": "355087fe-fb4a-49fc-87f1-7548fc512393",
  "durationDays": 30
}
```

### テストケース4: 過去の日付での予約テスト

- 結果: ✅ 成功
- メッセージ: 過去の日付での予約は正しくエラーになりました
- エラー: 過去の日付での予約がエラーにならずに成功してしまいました
- 実行時間: 2333.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース5: 終了日が開始日より前の予約テスト

- 結果: ✅ 成功
- メッセージ: 終了日が開始日より前の予約は正しくエラーになりました
- エラー: 終了日が開始日より前の予約がエラーにならずに成功してしまいました
- 実行時間: 456.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース6: 無効なメールアドレスでの予約テスト

- 結果: ✅ 成功
- メッセージ: 無効なメールアドレスでの予約は正しくエラーになりました

- 実行時間: 226.00ミリ秒
- 詳細:
```json
{
  "expectedError": true,
  "invalidEmail": "invalid-email"
}
```

### テストケース7: 在庫をゼロにするテスト

- 結果: ❌ 失敗
- メッセージ: 在庫をゼロにすることに失敗しました
- エラー: カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-08, 2025-06-09, 2025-06-10, 2025-06-11
- 実行時間: 233.00ミリ秒
- 詳細:
```json
{
  "bookingId": ""
}
```

### テストケース8: 在庫がゼロの状態で予約を試みるテスト

- 結果: ✅ 成功
- メッセージ: 在庫がゼロの状態での予約は正しくエラーになりました

- 実行時間: 223.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース9: 予約をキャンセルして在庫を回復するテスト

- 結果: ❌ 失敗
- メッセージ: 予約をキャンセルして在庫を回復することに失敗しました
- エラー: テスト1で作成した予約IDがありません
- 実行時間: 不明
- 詳細:
```json
{
  "bookingId": ""
}
```

### テストケース10: 在庫が回復した状態で予約を試みるテスト

- 結果: ❌ 失敗
- メッセージ: 在庫が回復した状態での予約に失敗しました
- エラー: カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-08, 2025-06-09, 2025-06-10, 2025-06-11
- 実行時間: 237.00ミリ秒
- 詳細:
```json
{
  "bookingId": ""
}
```

