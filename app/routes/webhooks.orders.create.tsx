import { ActionFunctionArgs, json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { logger } from "../utils/logger";
import { BookingService } from "../services/booking.service";

/**
 * 注文作成Webhook
 * POST: Shopifyからの注文作成通知を処理
 *
 * 機能:
 * 1. 既存の予約がある場合は予約ステータスを更新
 * 2. 新規注文の場合は予約を作成
 * 3. 注文と予約の関連付け
 * 4. 支払い状況に応じた予約ステータス設定
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  const webhookId = `webhook-orders-create-${Date.now()}`;

  try {
    logger.info("注文作成Webhook受信開始", { webhookId });

    // Webhookの検証
    const { session } = await authenticate.webhook(request);
    const shop = session.shop;

    // リクエストボディを取得
    const payload = await request.json();
    logger.info("注文作成Webhook受信", {
      webhookId,
      orderId: payload.id,
      orderName: payload.name,
      shop,
      financialStatus: payload.financial_status,
      fulfillmentStatus: payload.fulfillment_status,
      lineItemsCount: payload.line_items?.length || 0
    });

    // 注文IDを取得
    const orderId = payload.id.toString();
    if (!orderId) {
      logger.error("注文IDが見つかりません", { webhookId });
      return json({ error: "注文IDが見つかりません" }, { status: 400 });
    }

    // BookingServiceを初期化
    const bookingService = new BookingService();

    // 既存の予約を検索（予約IDまたはドラフト注文IDから）
    let existingBooking = null;

    // 1. 注文のプロパティから予約IDを検索
    const bookingIdFromProperties = extractBookingIdFromOrder(payload);
    if (bookingIdFromProperties) {
      existingBooking = await prisma.booking.findFirst({
        where: {
          bookingId: bookingIdFromProperties,
          shop
        }
      });
      logger.info("プロパティから予約を発見", {
        webhookId,
        bookingId: bookingIdFromProperties,
        found: !!existingBooking
      });
    }

    // 2. ドラフト注文IDから予約を検索
    if (!existingBooking && payload.draft_order_id) {
      existingBooking = await prisma.booking.findFirst({
        where: {
          shopifyDraftOrderId: payload.draft_order_id.toString(),
          shop
        }
      });
      logger.info("ドラフト注文IDから予約を発見", {
        webhookId,
        draftOrderId: payload.draft_order_id,
        found: !!existingBooking
      });
    }

    // 既存の予約がある場合は更新、ない場合は新規作成
    if (existingBooking) {
      // 既存の予約を更新
      await updateExistingBooking(existingBooking, payload, bookingService, webhookId);
    } else {
      // 新規予約を作成
      await createNewBookingsFromOrder(payload, shop, bookingService, webhookId);
    }

    // 注文データをOrderテーブルに保存
    await saveOrderToDatabase(payload, shop, webhookId);

    logger.info("注文作成Webhook処理完了", {
      webhookId,
      orderId: payload.id,
      orderName: payload.name,
      hasExistingBooking: !!existingBooking
    });

    return json({ success: true, message: "注文作成処理完了" });
  } catch (error) {
    logger.error("注文作成Webhook処理エラー", {
      webhookId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    return json({ error: "注文作成処理エラー" }, { status: 500 });
  }
};

/**
 * 注文から予約IDを抽出する
 */
function extractBookingIdFromOrder(payload: any): string | null {
  // 注文のラインアイテムのプロパティから予約IDを検索
  for (const lineItem of payload.line_items || []) {
    const properties = lineItem.properties || [];

    // プロパティから予約IDを検索
    for (const prop of properties) {
      if (prop.name === '_booking_id' || prop.name === 'booking_id' || prop.name === '予約ID') {
        return prop.value;
      }
    }
  }

  // 注文のnote_attributesから予約IDを検索
  for (const attr of payload.note_attributes || []) {
    if (attr.name === 'booking_id' || attr.name === '予約ID') {
      return attr.value;
    }
  }

  return null;
}

/**
 * 既存の予約を更新する
 */
async function updateExistingBooking(
  booking: any,
  payload: any,
  bookingService: BookingService,
  webhookId: string
) {
  try {
    // 支払い状況に応じて予約ステータスを決定
    let newStatus = booking.status;
    let paymentStatus = "PENDING";

    if (payload.financial_status === "paid") {
      newStatus = "CONFIRMED";
      paymentStatus = "COMPLETED";
    } else if (payload.financial_status === "partially_paid") {
      newStatus = "PROVISIONAL";
      paymentStatus = "PARTIALLY_PAID";
    } else if (payload.cancelled_at) {
      newStatus = "CANCELLED";
      paymentStatus = "REFUNDED";
    }

    // 予約ステータスを更新
    await bookingService.updateBookingStatus(booking.id, newStatus, {
      orderId: payload.id.toString(),
      orderName: payload.name,
      paymentStatus,
      notes: `注文作成による更新: ${payload.name}`
    });

    logger.info("既存予約を更新", {
      webhookId,
      bookingId: booking.bookingId,
      oldStatus: booking.status,
      newStatus,
      orderId: payload.id
    });
  } catch (error) {
    logger.error("既存予約更新エラー", {
      webhookId,
      bookingId: booking.bookingId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * 注文から新規予約を作成する
 */
async function createNewBookingsFromOrder(
  payload: any,
  shop: string,
  bookingService: BookingService,
  webhookId: string
) {
  try {
    // 注文の行アイテムから予約情報を抽出
    for (const lineItem of payload.line_items) {
      // カスタム属性から予約情報を取得
      const customAttributes = lineItem.properties || [];
      const startDateAttr = customAttributes.find((attr: any) =>
        attr.name === "レンタル開始日" || attr.name === "rental_start_date" || attr.name === "_rental_start_date"
      );
      const endDateAttr = customAttributes.find((attr: any) =>
        attr.name === "レンタル終了日" || attr.name === "rental_end_date" || attr.name === "_rental_end_date"
      );
      const rentalTypeAttr = customAttributes.find((attr: any) =>
        attr.name === "レンタルタイプ" || attr.name === "rental_type" || attr.name === "_rental_type"
      );

      if (startDateAttr && endDateAttr) {
        const startDate = new Date(startDateAttr.value);
        const endDate = new Date(endDateAttr.value);
        const rentalType = rentalTypeAttr?.value || "本予約";
        const isProvisional = rentalType === "仮予約" || rentalType === "PROVISIONAL";

        // 商品IDを取得
        const productId = lineItem.product_id?.toString();
        const variantId = lineItem.variant_id?.toString();

        if (productId) {
          // 支払い状況に応じて予約ステータスを決定
          let bookingStatus = "DRAFT";
          let paymentStatus = "PENDING";

          if (payload.financial_status === "paid") {
            bookingStatus = isProvisional ? "PROVISIONAL" : "CONFIRMED";
            paymentStatus = "COMPLETED";
          } else if (payload.financial_status === "partially_paid") {
            bookingStatus = "PROVISIONAL";
            paymentStatus = "PARTIALLY_PAID";
          }

          // 予約データを作成
          const booking = await prisma.booking.create({
            data: {
              productId,
              variantId,
              startDate,
              endDate,
              customerId: payload.customer?.id?.toString(),
              customerEmail: payload.email,
              customerName: `${payload.customer?.first_name || ''} ${payload.customer?.last_name || ''}`.trim(),
              customerPhone: payload.customer?.phone || '',
              totalAmount: parseFloat(lineItem.price),
              depositAmount: isProvisional ? (parseFloat(lineItem.price) * 0.1) : parseFloat(lineItem.price),
              depositPaid: paymentStatus === "COMPLETED",
              bookingId: `BK-${Date.now().toString().substring(6)}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
              shop,
              bookingType: isProvisional ? "PROVISIONAL" : "CONFIRMED",
              status: bookingStatus,
              paymentStatus,
              paymentMethod: paymentStatus === "COMPLETED" ? "CREDIT_CARD" : null,
              paymentDate: paymentStatus === "COMPLETED" ? new Date() : null,
              shopifyOrderId: payload.id.toString(),
              shopifyOrderName: payload.name,
              notes: `注文作成: ${payload.name} - ${isProvisional ? '仮予約' : '本予約'}`,
              metadata: {
                orderCreatedAt: payload.created_at,
                lineItemId: lineItem.id,
                variantTitle: lineItem.variant_title
              }
            }
          });

          logger.info("新規予約を作成", {
            webhookId,
            bookingId: booking.bookingId,
            orderId: payload.id,
            productId,
            status: bookingStatus
          });
        }
      }
    }
  } catch (error) {
    logger.error("新規予約作成エラー", {
      webhookId,
      orderId: payload.id,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * 注文データをOrderテーブルに保存する
 */
async function saveOrderToDatabase(payload: any, shop: string, webhookId: string) {
  try {
    await prisma.order.upsert({
      where: {
        shop_shopifyId: {
          shop,
          shopifyId: payload.id.toString()
        }
      },
      update: {
        orderNumber: payload.name,
        customerEmail: payload.email,
        customerName: `${payload.customer?.first_name || ''} ${payload.customer?.last_name || ''}`.trim(),
        totalAmount: parseFloat(payload.total_price),
        paymentStatus: payload.financial_status,
        syncStatus: "SYNCED",
        lastSyncedAt: new Date(),
        metadata: {
          fulfillmentStatus: payload.fulfillment_status,
          currency: payload.currency,
          subtotalPrice: payload.subtotal_price,
          totalTax: payload.total_tax,
          webhookId
        }
      },
      create: {
        shop,
        shopifyId: payload.id.toString(),
        orderNumber: payload.name,
        customerEmail: payload.email,
        customerName: `${payload.customer?.first_name || ''} ${payload.customer?.last_name || ''}`.trim(),
        totalAmount: parseFloat(payload.total_price),
        paymentStatus: payload.financial_status,
        syncStatus: "SYNCED",
        lastSyncedAt: new Date(),
        metadata: {
          fulfillmentStatus: payload.fulfillment_status,
          currency: payload.currency,
          subtotalPrice: payload.subtotal_price,
          totalTax: payload.total_tax,
          webhookId,
          createdAt: payload.created_at
        }
      }
    });

    logger.info("注文データを保存", {
      webhookId,
      orderId: payload.id,
      orderNumber: payload.name
    });
  } catch (error) {
    logger.error("注文データ保存エラー", {
      webhookId,
      orderId: payload.id,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}