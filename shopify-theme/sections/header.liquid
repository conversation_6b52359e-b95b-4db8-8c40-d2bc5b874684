{% include 'global-variables' %}
{%- if section.settings.menu != blank or section.settings.vertical_menu != blank -%}
    {%- if request.design_mode or canonical_url contains 'challenge' -%}
        {%- assign is_design_mode = true -%}
    {%- endif -%}
    
    {%- assign selected_blocks_tape = section.blocks | where: 'type', 'tape' -%}
    {%- assign selected_blocks_info_line = section.blocks | where: 'type', 'info_line' -%}
    {%- assign selected_blocks_colorize = section.blocks | where: 'type', 'colorize' -%}
    {%- assign selected_blocks_megamenu = section.blocks | where: 'type', 'megamenu' -%}
    {%- for block in selected_blocks_megamenu -%}
        {% capture items_with_megamenu %}{{ block.settings.for_item | handle }}||{{ items_with_megamenu }}{% endcapture %}
    {%- endfor -%}
    {%- assign items_with_megamenu = items_with_megamenu | split: '||' -%}
    {%- for block in section.blocks -%}
        {%- case block.type -%}
            {% when 'menu' %}
                {%- if block.settings.menu != blank -%}
                    {% capture menu_html %}{{ menu_html }}|||{{ block.settings.for_item | handleize }}|||{{ block.settings.menu }}{% endcapture %}
                {%- endif -%}
            {% when 'megamenu_label' %}
                {%- for i in (1..15) -%}
                    {% capture prop_for_item_name %}for_item{% if i > 1 %}_{{ i }}{% endif %}{% endcapture %}
                    {%- if block.settings[prop_for_item_name] == blank -%}
                        {%- break -%}
                    {%- endif -%}
                    {% capture label_html %}
                        {{ label_html }}|||{{ block.settings[prop_for_item_name] | handleize }}|||
                        {%- if block.settings.type == 'hot' -%}
                            {%- capture default_text -%}{{ 'layout.header.labels.hot' | t }}{%- endcapture -%}
                            <span class="menu__label menu__label--hot px-3 ml-5">{{ block.settings.text | default: default_text }}</span>
                        {%- elsif block.settings.type == 'sale' -%}
                            {%- capture default_text -%}{{ 'layout.header.labels.sale' | t }}{%- endcapture -%}
                            <span class="menu__label menu__label--sale px-3 ml-5">{{ block.settings.text | default: default_text }}</span>
                        {%- elsif block.settings.type == 'new' -%}
                            {%- capture default_text -%}{{ 'layout.header.labels.new' | t }}{%- endcapture -%}
                            <span class="menu__label menu__label--new px-3 ml-5">{{ block.settings.text | default: default_text }}</span>
                        {%- endif -%}
                    {% endcapture %}
                {%- endfor -%}
            {% when 'megamenu_title_image' %}
                {%- for i in (1..10) -%}
                    {% capture prop_for_item_name %}for_item{% if i > 1 %}_{{ i }}{% endif %}{% endcapture %}
                    {%- if block.settings[prop_for_item_name] == blank -%}
                        {%- break -%}
                    {%- endif -%}
                    {% capture prop_image_name %}image{% if i > 1 %}_{{ i }}{% endif %}{% endcapture %}
                    {% capture prop_image_size_name %}image_size{% if i > 1 %}_{{ i }}{% endif %}{% endcapture %}
                    {% capture prop_url_name %}url{% if i > 1 %}_{{ i }}{% endif %}{% endcapture %}
                    {% capture title_image_html %}
                        {{ title_image_html }}|||{{ block.settings[prop_for_item_name] | handleize }}|||
                        <a href="[link_url]" class="d-none d-lg-block mb-10">
                            {%- capture image_size -%}{{ block.settings[prop_image_size_name] }}x{%- endcapture -%}
                            {% render 'rimage' with image: block.settings[prop_image_name] size: image_size format: 'pjpg' %}
                        </a>
                    {% endcapture %}
                {%- endfor -%}
            {% when 'icon' %}
                {%- for i in (1..20) -%}
                    {% capture prop_for_item_name %}for_item_{{ i }}{% endcapture %}
                    {% capture prop_icon_name %}icon_{{ i }}{% endcapture %}
                    {%- if block.settings[prop_for_item_name] == blank or block.settings[prop_icon_name] == blank -%}
                        {%- break -%}
                    {%- endif -%}
                    {% capture icons_html %}
                        {{ icons_html }}|||{% if block.settings.for_vertical %}vertical-{% endif %}{{ block.settings[prop_for_item_name] | handleize }}|||
                        <i class="menu__item_icon mr-10">{% include block.settings[prop_icon_name] %}</i>
                    {% endcapture %}
                {%- endfor -%}
        {%- endcase -%}
    {%- endfor -%}
{%- endif -%}
{%- if section.settings.sticky == 'desktop_and_mobile' or section.settings.sticky == 'desktop' -%}
    {%- if section.settings.type != '3' and section.settings.type != '4' and section.settings.desktop_sticky_type == 'slim' -%}
        {%- assign need_sticky_sidebar = true -%}
        {%- if settings.logo_types == 'svg' and settings.logo_sticky_svg_file != blank -%}
            {%- assign need_sticky_logo = true -%}
        {%- elsif settings.logo_types == 'image' and settings.logo_sticky_image != blank -%}
            {%- assign need_sticky_logo = true -%}
        {%- endif -%}
    {%- elsif section.settings.type == '3' or section.settings.type == '4' -%}
        {%- if settings.logo_types == 'svg' and settings.logo_sticky_svg_file != blank -%}
            {%- assign need_sticky_logo_type_3_4 = true -%}
        {%- elsif settings.logo_types == 'image' and settings.logo_sticky_image != blank -%}
            {%- assign need_sticky_logo_type_3_4 = true -%}
        {%- endif -%}
    {%- endif -%}
{%- endif -%}
{%- if settings.logo_types == 'svg' and settings.logo_mobile_svg_file != blank -%}
    {%- assign need_mobile_logo = true -%}
{%- elsif settings.logo_types == 'image' and settings.logo_mobile_image != blank -%}
    {%- assign need_mobile_logo = true -%}
{%- endif -%}
{% capture style_base_html %}
    <style>
        :root {
            --header-button-cart-weight: var(--body-weight);

            --menu-family: {% if settings.font_base_apply_to_all %}var(--base-family){% else %}{{ settings.font_menu.family }}, {{ settings.font_menu.fallback_families }}{% endif %};
            --menu-style: {{ settings.font_menu.style }};
            --menu-size: {{ settings.font_size_menu }}px;
            --menu-line-height: {{ settings.font_size_menu | times: 1.076 | round }}px;
            --menu-letter-spacing: 0.05em;
            --menu-weight: {{ settings.font_menu.weight }};
            --menu-transform: uppercase;

            --menu-list-size: {{ settings.font_size_menu_list }}px;
            --menu-list-line-height: {{ settings.font_size_menu_list | times: 1.846 | round }}px;
            --menu-list-letter-spacing: 0em;
            --menu-list-weight: var(--menu-weight);

            --menu-title-size: {{ settings.font_size_menu_title }}px;
            --menu-title-line-height: {{ settings.font_size_menu_title | times: 1.4 | round }}px;
            --menu-title-letter-spacing: var(--menu-letter-spacing);
            --menu-title-weight: var(--h5-weight);
            --menu-title-transform: var(--menu-transform);

            --menu-main-mobile-size: {{ settings.font_size_menu_mobile }}px;
            --menu-main-mobile-line-height: {{ settings.font_size_menu_mobile | times: 2.4 | round }}px;
            --menu-main-mobile-letter-spacing: var(--menu-letter-spacing);
            --menu-main-mobile-weight: var(--menu-weight);
            --menu-main-mobile-transform: var(--menu-transform);

            --menu-secondary-mobile-size: var(--menu-main-mobile-size);
            --menu-secondary-mobile-line-height: var(--menu-main-mobile-line-height);
            --menu-secondary-mobile-letter-spacing: 0em;
            --menu-secondary-mobile-weight: var(--menu-weight);
            --menu-secondary-mobile-transform: none;

            --vertical-menu-size: {{ settings.font_size_menu }}px;
            --vertical-menu-line-height: {{ settings.font_size_menu | times: 3 | round }}px;
            --vertical-menu-letter-spacing: var(--menu-letter-spacing);
            --vertical-menu-weight: var(--menu-weight);
            --vertical-menu-transform: var(--menu-transform);
            --vertical-menu-icons-size: 24px;

            --vertical-menu-button-size: {{ settings.font_size_menu }}px;
            --vertical-menu-button-line-height: {{ settings.font_size_menu | times: 1.307 | round }}px;
            --vertical-menu-button-letter-spacing: 0.05em;
            --vertical-menu-button-weight: var(--button-weight);
            --vertical-menu-button-icons-size: 24px;

            --animation-menu-desktop-duration: 0.4s;
            --animation-menu-mobile-duration: 0.4s;
        
            {% if section.settings.type == '1' %}
                --header-top-line-min-height: {{ header_top_line_min_height }}px;
            {% elsif section.settings.type == '2' %}
                --header-top-line-min-height: {{ header_top_line_min_height }}px;
            {% elsif section.settings.type == '3' %}
                --header-top-line-min-height: {{ header_top_line_min_height }}px;
            {% elsif section.settings.type == '4' %}
                --header-top-line-min-height: {{ header_top_line_min_height }}px;
            {% elsif section.settings.type == '5' %}
                --header-top-line-min-height: {{ header_5_top_line_min_height }}px;
            {% elsif section.settings.type == '6' %}
                --header-top-line-min-height: {{ header_6_top_line_min_height }}px;
            {% elsif section.settings.type == '7' %}
                --header-top-line-min-height: {{ header_7_top_line_min_height }}px;
            {% endif %}
            
            {% if section.settings.style == '1' %}
                --header-offset-bottom-bd: 1px;
                --header-additional-line-bg: var(--theme2);
                --header-additional-line-bd: solid 1px var(--theme5);
                --header-additional-line-c: var(--theme-c);
                --header-additional-line-highlighted-c: var(--theme);
                --header-additional-line-account-c: var(--theme);
                --header-additional-line-account-h-c: var(--theme3);
                --header-additional-line-services-c: var(--theme);
                --header-additional-line-services-h-c: var(--theme3);
                --header-additional-line-currency-languages-c: var(--theme);
                --header-additional-line-currency-languages-h-c: var(--theme3);
                --header-line-1-bg: var(--theme2);
                --header-line-1-bd: solid 1px var(--theme5);
                --header-line-2-bg: var(--theme2);
                --header-line-2-bd: solid 1px var(--theme5);
                --header-m-bg: var(--theme2);
                --header-m-bd: solid 1px var(--theme5);

                --header-btns-d-c: var(--theme);
                --header-btns-d-h-c: var(--theme3);

                --header-btns-line-1-d-c: var(--header-btns-d-c);
                --header-btns-line-1-d-h-c: var(--header-btns-d-h-c);
                --header-btns-line-2-d-c: var(--header-btns-d-c);
                --header-btns-line-2-d-h-c: var(--header-btns-d-h-c);
                --header-menu-sticky-c: var(--header-menu-d-c);
                --header-menu-sticky-h-c: var(--header-menu-h-c);
                --header-btns-sticky-c: var(--header-btns-d-c);
                --header-btns-sticky-h-c: var(--header-btns-d-h-c);
                --header-btns-m-c: var(--theme);

                --header-counter-d-c: var(--header-btns-d-c);
                --header-counter-d-h-c: var(--header-btns-d-h-c);

                --header-counter-line-1-d-c: var(--header-counter-d-c);
                --header-counter-line-1-d-h-c: var(--header-counter-d-h-c);
                --header-counter-line-2-d-c: var(--header-counter-d-c);
                --header-counter-line-2-d-h-c: var(--header-counter-d-h-c);
                --header-counter-m-c: var(--header-btns-m-c);

                --header-btn-search-d-c: var(--header-btns-d-c);
                --header-btn-search-d-h-c: var(--header-btns-d-h-c);

                --header-btn-search-line-1-d-c: var(--header-btn-search-d-c);
                --header-btn-search-line-1-d-h-c: var(--header-btn-search-d-h-c);
                --header-btn-search-line-2-d-c: var(--header-btn-search-d-c);
                --header-btn-search-line-2-d-h-c: var(--header-btn-search-d-h-c);

                --header-btn-cart-d-c: var(--header-btns-d-c);
                --header-btn-cart-text-d-c: var(--header-btn-cart-d-c);
                --header-btn-cart-d-h-c: var(--header-btns-d-h-c);

                --header-btn-cart-line-1-d-c: var(--header-btn-cart-d-c);
                --header-btn-cart-line-1-text-d-c: var(--header-btn-cart-text-d-c);
                --header-btn-cart-line-1-d-h-c: var(--header-btn-cart-d-h-c);
                --header-btn-cart-line-2-d-c: var(--header-btn-cart-d-c);
                --header-btn-cart-line-2-text-d-c: var(--header-btn-cart-text-d-c);
                --header-btn-cart-line-2-d-h-c: var(--header-btn-cart-d-h-c);

                --header-search-input-border: solid 1px var(--theme5);
                --header-search-input-c: var(--input-c);
                --header-search-input-bg: var(--input-bg);
                --header-search-input-bd: var(--input-bd);
                --header-search-input-f-bg: var(--input-f-bg);
                --header-search-input-f-bd: var(--input-f-bd);
                {%- assign header_search_btn_type = 'default' -%}
                --header-menu-h-bg: transparent;
                --header-menu-d-c: var(--theme);
                --header-menu-h-c: var(--theme3);
                --header-menu-vertical-button-bg: var(--theme-primary);
                --header-menu-vertical-button-c: var(--theme2);
                --header-menu-vertical-d-bg: var(--theme2);
                --header-menu-vertical-d-bd: solid 1px var(--theme5);
                --header-menu-vertical-d-c: var(--header-menu-d-c);
                --header-menu-vertical-h-c: var(--header-menu-h-c);
                --header-menu-vertical-m-c: var(--theme);
                --header-menu-vertical-icon-c: var(--header-menu-vertical-d-c);
                --header-menu-vertical-icon-h-c: var(--header-menu-vertical-h-c);
            {% elsif section.settings.style == '2' %}
                --header-offset-bottom-bd: 0px;
                --header-additional-line-bg: var(--theme);
                --header-additional-line-bd: solid 1px #3e3e3e;
                --header-additional-line-c: var(--theme2);
                --header-additional-line-highlighted-c: var(--theme2);
                --header-additional-line-account-c: var(--theme2);
                --header-additional-line-account-h-c: var(--theme2);
                --header-additional-line-services-c: var(--theme2);
                --header-additional-line-services-h-c: var(--theme2);
                --header-additional-line-currency-languages-c: var(--theme2);
                --header-additional-line-currency-languages-h-c: var(--theme2);
                --header-line-1-bg: var(--theme);
                --header-line-1-bd: solid 1px #3e3e3e;
                --header-line-2-bg: var(--theme);
                --header-line-2-bd: none;
                --header-m-bg: var(--theme);
                --header-m-bd: none;

                --header-btns-d-c: var(--theme2);
                --header-btns-d-h-c: var(--theme3);

                --header-btns-line-1-d-c: var(--header-btns-d-c);
                --header-btns-line-1-d-h-c: var(--header-btns-d-h-c);
                --header-btns-line-2-d-c: var(--header-btns-d-c);
                --header-btns-line-2-d-h-c: var(--header-btns-d-h-c);
                --header-menu-sticky-c: var(--header-menu-d-c);
                --header-menu-sticky-h-c: var(--header-menu-h-c);
                --header-btns-sticky-c: var(--header-btns-d-c);
                --header-btns-sticky-h-c: var(--header-btns-d-h-c);
                --header-btns-m-c: var(--theme2);

                --header-counter-d-c: var(--header-btns-d-c);
                --header-counter-d-h-c: var(--header-btns-d-h-c);

                --header-counter-line-1-d-c: var(--header-counter-d-c);
                --header-counter-line-1-d-h-c: var(--header-counter-d-h-c);
                --header-counter-line-2-d-c: var(--header-counter-d-c);
                --header-counter-line-2-d-h-c: var(--header-counter-d-h-c);
                --header-counter-m-c: var(--header-btns-m-c);

                --header-btn-search-d-c: var(--header-btns-d-c);
                --header-btn-search-d-h-c: var(--header-btns-d-h-c);

                --header-btn-search-line-1-d-c: var(--header-btn-search-d-c);
                --header-btn-search-line-1-d-h-c: var(--header-btn-search-d-h-c);
                --header-btn-search-line-2-d-c: var(--header-btn-search-d-c);
                --header-btn-search-line-2-d-h-c: var(--header-btn-search-d-h-c);

                --header-btn-cart-d-c: var(--header-btns-d-c);
                --header-btn-cart-text-d-c: var(--header-btn-cart-d-c);
                --header-btn-cart-d-h-c: var(--header-btns-d-h-c);

                --header-btn-cart-line-1-d-c: var(--header-btn-cart-d-c);
                --header-btn-cart-line-1-text-d-c: var(--header-btn-cart-text-d-c);
                --header-btn-cart-line-1-d-h-c: var(--header-btn-cart-d-h-c);
                --header-btn-cart-line-2-d-c: var(--header-btn-cart-d-c);
                --header-btn-cart-line-2-text-d-c: var(--header-btn-cart-text-d-c);
                --header-btn-cart-line-2-d-h-c: var(--header-btn-cart-d-h-c);

                --header-search-input-border: solid 1px var(--theme5);
                --header-search-input-c: var(--input-c);
                --header-search-input-bg: var(--theme);
                --header-search-input-bd: solid 1px #3e3e3e;
                --header-search-input-f-bg: var(--theme);
                --header-search-input-f-bd: solid 1px var(--theme2);
                {%- assign header_search_btn_type = 'invert' -%}
                --header-menu-h-bg: transparent;
                --header-menu-d-c: var(--theme2);
                --header-menu-h-c: var(--theme3);
                --header-menu-vertical-button-bg: var(--theme-primary);
                --header-menu-vertical-button-c: var(--theme2);
                --header-menu-vertical-d-bg: var(--theme2);
                --header-menu-vertical-d-bd: solid 1px var(--theme5);
                --header-menu-vertical-d-c: var(--theme);
                --header-menu-vertical-h-c: var(--theme3);
                --header-menu-vertical-m-c: var(--theme);
                --header-menu-vertical-icon-c: var(--header-menu-vertical-d-c);
                --header-menu-vertical-icon-h-c: var(--header-menu-vertical-h-c);
            {% elsif section.settings.style == '3' %}
                --header-offset-bottom-bd: 0px;
                --header-additional-line-bg: var(--theme2);
                --header-additional-line-bd: solid 1px var(--theme5);
                --header-additional-line-c: var(--theme-c);
                --header-additional-line-highlighted-c: var(--theme);
                --header-additional-line-account-c: var(--theme);
                --header-additional-line-account-h-c: var(--theme3);
                --header-additional-line-services-c: var(--theme);
                --header-additional-line-services-h-c: var(--theme3);
                --header-additional-line-currency-languages-c: var(--theme);
                --header-additional-line-currency-languages-h-c: var(--theme3);
                --header-line-1-bg: var(--theme2);
                --header-line-1-bd: none;
                --header-line-2-bg: var(--theme);
                --header-line-2-bd: none;
                --header-m-bg: var(--theme2);
                --header-m-bd: solid 1px var(--theme5);

                --header-btns-d-c: var(--theme);
                --header-btns-d-h-c: var(--theme3);

                --header-btns-line-1-d-c: var(--header-btns-d-c);
                --header-btns-line-1-d-h-c: var(--header-btns-d-h-c);
                --header-btns-line-2-d-c: var(--theme2);
                --header-btns-line-2-d-h-c: var(--header-btns-d-h-c);
                --header-menu-sticky-c: var(--header-menu-d-c);
                --header-menu-sticky-h-c: var(--header-menu-h-c);
                --header-btns-sticky-c: var(--theme2);
                --header-btns-sticky-h-c: var(--header-btns-d-h-c);
                --header-btns-m-c: var(--theme);

                --header-counter-d-c: var(--header-btns-d-c);
                --header-counter-d-h-c: var(--header-btns-d-h-c);

                --header-counter-line-1-d-c: var(--header-counter-d-c);
                --header-counter-line-1-d-h-c: var(--header-counter-d-h-c);
                --header-counter-line-2-d-c: var(--header-btns-line-2-d-c);
                --header-counter-line-2-d-h-c: var(--header-btns-line-2-d-h-c);
                --header-counter-m-c: var(--header-btns-m-c);

                --header-btn-search-d-c: var(--header-btns-d-c);
                --header-btn-search-d-h-c: var(--header-btns-d-h-c);

                --header-btn-search-line-1-d-c: var(--header-btn-search-d-c);
                --header-btn-search-line-1-d-h-c: var(--header-btn-search-d-h-c);
                --header-btn-search-line-2-d-c: var(--theme2);
                --header-btn-search-line-2-d-h-c: var(--header-btn-search-line-1-d-h-c);

                --header-btn-cart-d-c: var(--header-btns-d-c);
                --header-btn-cart-text-d-c: var(--header-btn-cart-d-c);
                --header-btn-cart-d-h-c: var(--header-btns-d-h-c);

                --header-btn-cart-line-1-d-c: var(--header-btn-cart-d-c);
                --header-btn-cart-line-1-text-d-c: var(--header-btn-cart-line-1-d-c);
                --header-btn-cart-line-1-d-h-c: var(--header-btn-cart-d-h-c);
                --header-btn-cart-line-2-d-c: var(--theme2);
                --header-btn-cart-line-2-text-d-c: var(--theme2);
                --header-btn-cart-line-2-d-h-c: var(--header-btn-cart-line-1-d-h-c);

                --header-search-input-border: solid 1px var(--theme5);
                --header-search-input-c: var(--input-c);
                --header-search-input-bg: var(--input-bg);
                --header-search-input-bd: var(--input-bd);
                --header-search-input-f-bg: var(--input-f-bg);
                --header-search-input-f-bd: var(--input-f-bd);
                {%- assign header_search_btn_type = 'default' -%}
                --header-menu-h-bg: transparent;
                --header-menu-d-c: var(--theme2);
                --header-menu-h-c: var(--theme3);
                --header-menu-vertical-button-bg: var(--theme-primary);
                --header-menu-vertical-button-c: var(--theme2);
                --header-menu-vertical-d-bg: var(--theme2);
                --header-menu-vertical-d-bd: solid 1px var(--theme5);
                --header-menu-vertical-d-c: var(--theme);
                --header-menu-vertical-h-c: var(--theme3);
                --header-menu-vertical-m-c: var(--theme);
                --header-menu-vertical-icon-c: var(--header-menu-vertical-d-c);
                --header-menu-vertical-icon-h-c: var(--header-menu-vertical-h-c);
            {% elsif section.settings.style == '4' %}
                --header-offset-bottom-bd: 0px;
                --header-additional-line-bg: var(--theme2);
                --header-additional-line-bd: solid 1px var(--theme5);
                --header-additional-line-c: var(--theme-c);
                --header-additional-line-highlighted-c: var(--theme);
                --header-additional-line-account-c: var(--theme);
                --header-additional-line-account-h-c: var(--theme3);
                --header-additional-line-services-c: var(--theme);
                --header-additional-line-services-h-c: var(--theme3);
                --header-additional-line-currency-languages-c: var(--theme);
                --header-additional-line-currency-languages-h-c: var(--theme3);
                --header-line-1-bg: var(--theme2);
                --header-line-1-bd: none;
                --header-line-2-bg: var(--theme4);
                --header-line-2-bd: none;
                --header-m-bg: var(--theme2);
                --header-m-bd: solid 1px var(--theme5);

                --header-btns-d-c: var(--theme);
                --header-btns-d-h-c: var(--theme3);

                --header-btns-line-1-d-c: var(--header-btns-d-c);
                --header-btns-line-1-d-h-c: var(--header-btns-d-h-c);
                --header-btns-line-2-d-c: var(--header-btns-d-c);
                --header-btns-line-2-d-h-c: var(--header-btns-d-h-c);
                --header-menu-sticky-c: var(--header-menu-d-c);
                --header-menu-sticky-h-c: var(--header-menu-h-c);
                --header-btns-sticky-c: var(--header-btns-d-c);
                --header-btns-sticky-h-c: var(--header-btns-d-h-c);
                --header-btns-m-c: var(--theme);

                --header-counter-d-c: var(--header-btns-d-c);
                --header-counter-d-h-c: var(--header-btns-d-h-c);

                --header-counter-line-1-d-c: var(--header-counter-d-c);
                --header-counter-line-1-d-h-c: var(--header-counter-d-h-c);
                --header-counter-line-2-d-c: var(--header-counter-d-c);
                --header-counter-line-2-d-h-c: var(--header-counter-d-h-c);
                --header-counter-m-c: var(--header-btns-m-c);

                --header-btn-search-d-c: var(--header-btns-d-c);
                --header-btn-search-d-h-c: var(--header-btns-d-h-c);

                --header-btn-search-line-1-d-c: var(--header-btn-search-d-c);
                --header-btn-search-line-1-d-h-c: var(--header-btn-search-d-h-c);
                --header-btn-search-line-2-d-c: var(--header-btn-search-d-c);
                --header-btn-search-line-2-d-h-c: var(--header-btn-search-d-h-c);

                --header-btn-cart-d-c: var(--header-btns-d-c);
                --header-btn-cart-text-d-c: var(--header-btn-cart-d-c);
                --header-btn-cart-d-h-c: var(--header-btns-d-h-c);

                --header-btn-cart-line-1-d-c: var(--header-btn-cart-d-c);
                --header-btn-cart-line-1-text-d-c: var(--header-btn-cart-text-d-c);
                --header-btn-cart-line-1-d-h-c: var(--header-btn-cart-d-h-c);
                --header-btn-cart-line-2-d-c: var(--header-btn-cart-d-c);
                --header-btn-cart-line-2-text-d-c: var(--header-btn-cart-text-d-c);
                --header-btn-cart-line-2-d-h-c: var(--header-btn-cart-d-h-c);

                --header-search-input-border: solid 1px var(--theme5);
                --header-search-input-c: var(--input-c);
                --header-search-input-bg: var(--input-bg);
                --header-search-input-bd: var(--input-bd);
                --header-search-input-f-bg: var(--input-f-bg);
                --header-search-input-f-bd: var(--input-f-bd);
                {%- assign header_search_btn_type = 'default' -%}
                --header-menu-h-bg: transparent;
                --header-menu-d-c: var(--theme);
                --header-menu-h-c: var(--theme3);
                --header-menu-vertical-button-bg: var(--theme-primary);
                --header-menu-vertical-button-c: var(--theme2);
                --header-menu-vertical-d-bg: var(--theme2);
                --header-menu-vertical-d-bd: solid 1px var(--theme5);
                --header-menu-vertical-d-c: var(--header-menu-d-c);
                --header-menu-vertical-h-c: var(--header-menu-h-c);
                --header-menu-vertical-m-c: var(--theme);
                --header-menu-vertical-icon-c: var(--header-menu-vertical-d-c);
                --header-menu-vertical-icon-h-c: var(--header-menu-vertical-h-c);
            {% endif %}
            {% if selected_blocks_tape.size > 0 %}
                {% if selected_blocks_tape[0].settings.style == '1' %}
                    --header-tape-bg: var(--theme-primary);
                    --header-tape-bd: none;
                    --header-tape-c: var(--theme2);
                    --header-tape-btn-close-c: var(--theme3);
                {% elsif selected_blocks_tape[0].settings.style == '2' %}
                    --header-tape-bg: var(--theme2);
                    --header-tape-bd: solid 1px var(--theme5);
                    --header-tape-c: var(--theme);
                    --header-tape-btn-close-c: var(--theme3);
                {% elsif selected_blocks_tape[0].settings.style == '3' %}
                    --header-tape-bg: var(--theme4);
                    --header-tape-bd: none;
                    --header-tape-c: var(--theme-primary);
                    --header-tape-btn-close-c: var(--theme3);
                {% endif %}
            {% endif %}
            {% if selected_blocks_info_line.size > 0 %}
                {% if selected_blocks_info_line[0].settings.style == '1' %}
                    --header-info-line-bg: var(--theme2);
                    --header-info-line-bd: solid 1px var(--theme5);
                    --header-info-line-c: var(--theme-c);
                    --header-social-media-c: var(--theme6);
                    --header-social-media-h-c: var(--theme);
                {% elsif selected_blocks_info_line[0].settings.style == '2' %}
                    --header-info-line-bg: var(--theme);
                    --header-info-line-bd: solid 1px #3e3e3e;
                    --header-info-line-c: var(--theme2);
                    --header-social-media-c: var(--theme2);
                    --header-social-media-h-c: var(--theme2);
                {% endif %}
            {% endif %}

            --header-menu-title-d-c: var(--theme);
            --header-menu-title-h-c: var(--header-menu-title-d-c);
            --header-menu-list-h-bg: var(--theme4);
            --header-menu-list-c: var(--theme3);
            --header-menu-list-h-c: var(--theme);
            --header-menu-m-c: var(--theme3);
            --header-menu-m-bd: solid 1px var(--theme5);

            {%- if settings.layout_settings_file == 'skin-1' -%}
                --header-btn-cart-d-c: var(--theme-primary);
                --header-menu-h-c: var(--theme-primary);
                --header-menu-list-c: var(--theme-primary);
            {%- elsif settings.layout_settings_file == 'skin-2' -%}
                --header-counter-d-c: var(--theme-primary);
                --header-counter-d-h-c: var(--header-counter-d-c);
                --header-menu-h-c: var(--theme-primary);
                --header-social-media-c: #989C9F;
                --header-social-media-h-c: var(--theme6);
                --header-menu-list-h-bg: var(--theme-body);
                --header-menu-list-h-c: var(--theme-primary);
            {%- elsif settings.layout_settings_file == 'skin-3' -%}
                --header-offset-bottom-bd: 0px;
                --header-offset-bottom-bd: 0px;
                --header-additional-line-bd: none;
                --header-line-1-bd: none;
                --header-line-2-bd: none;
                --header-m-bd: none;
                --header-tape-btn-close-c: var(--theme2);
                --header-menu-list-h-bg: var(--theme-primary);
                --header-menu-list-h-c: var(--theme2);
            {%- elsif settings.layout_settings_file == 'skin-4' -%}
                --header-offset-bottom-bd: 0px;
                --header-additional-line-bd: none;
                --header-line-1-bd: none;
                --header-line-2-bd: none;
                --header-btn-cart-d-c: var(--theme-primary);
            {%- elsif settings.layout_settings_file == 'skin-5' -%}
                --menu-letter-spacing: 0em;
                --menu-weight: bold;
                --menu-transform: none;
                --vertical-menu-icons-size: 20px;
                --header-menu-h-bg: #1278A5;
                --header-menu-h-c: var(--theme2);
                --header-line-2-bg: var(--theme-primary);
                --header-btn-cart-line-1-d-c: #1278A5;
                --header-info-line-bg: var(--theme4);
                --header-info-line-c: #1278A5;
                --header-social-media-h-c: #1278A5;
                --header-menu-list-c: #1278A5;
                --header-menu-list-h-c: #1278A5;
            {%- elsif settings.layout_settings_file == 'skin-7' -%}
                --menu-transform: none;
                --header-menu-h-c: var(--header-menu-d-c);
            {%- elsif settings.layout_settings_file == 'skin-8' -%}
                --header-menu-list-h-bg: #F2F2F2;
            {%- elsif settings.layout_settings_file == 'skin-9' -%}
                --vertical-menu-weight: bold;
                --vertical-menu-icons-size: 20px;
                --vertical-menu-button-font-size: {{ settings.font_size_h4 }}px;
                --vertical-menu-button-line-height: {{ settings.font_size_h4 | times: 1.277 | round }}px;
                --header-button-cart-weight: bold;
                --header-additional-line-bg: var(--theme4);
                --header-additional-line-bd: none;
                --header-additional-line-highlighted-c: var(--theme-primary);
                --header-additional-line-account-c: var(--theme-primary);
                --header-search-input-bg: var(--theme4);
                --header-search-input-bd: none;
                --header-search-input-f-bg: var(--header-search-input-bd);
                --header-search-input-f-bd: var(--header-search-input-bd);
                --header-btn-cart-line-1-d-c: var(--theme-primary);
                --header-menu-h-c: var(--theme-primary);
                --header-menu-vertical-d-bg: var(--theme4);
                --header-menu-vertical-d-bd: none;
                --header-menu-vertical-h-c: var(--theme-primary);
                --header-menu-vertical-icon-c: var(--theme6);
                --header-menu-list-c: var(--theme-primary);
            {%- elsif settings.layout_settings_file == 'skin-10' -%}
                --menu-title-transform: none;
                --vertical-menu-size: {{ settings.font_size_h5 }}px;
                --vertical-menu-line-height: {{ settings.font_size_h5 | times: 2.6 | round }}px;
                --vertical-menu-letter-spacing: 0;
                --vertical-menu-weight: bold;
                --vertical-menu-transform: none;
                --vertical-menu-icons-size: 20px;
                --vertical-menu-button-font-size: {{ settings.font_size_h5 }}px;
                --vertical-menu-button-line-height: {{ settings.font_size_h5 | times: 1.066 | round }}px;
                --header-offset-bottom-bd: 0px;
                --header-additional-line-bg: var(--theme4);
                --header-additional-line-bd: none;
                --header-additional-line-highlighted-c: var(--theme-primary);
                --header-additional-line-account-c: var(--theme-primary);
                --header-line-1-bd: none;
                --header-line-2-bd: none;
                --header-input-bg: var(--theme4);
                --header-input-bd: none;
                --header-input-f-bg: var(--header-input-bd);
                --header-input-f-bd: var(--header-input-bd);
                {%- assign header_search_btn_type = 'invert' -%}
                --header-menu-h-c: var(--theme-primary);
                --header-menu-vertical-d-bg: var(--theme4);
                --header-menu-vertical-d-bd: none;
                --header-menu-vertical-h-c: var(--theme-primary);
                --header-menu-vertical-icon-c: var(--theme6);
                --header-menu-vertical-icon-h-c: var(--header-menu-vertical-h-c);
                --header-menu-list-h-bg: var(--theme-primary);
                --header-menu-list-h-c: var(--theme2);
            {%- elsif settings.layout_settings_file == 'skin-11' -%}
                --vertical-menu-icons-size: 4px;
                --header-offset-bottom-bd: 0px;
                --header-line-2-bd: none;
                --header-m-bd: none;
                --header-menu-h-c: var(--theme-primary);
                --header-menu-title-d-c: var(--theme-primary);
                --header-menu-list-h-bg: var(--theme-primary);
                --header-menu-list-c: var(--theme-primary);
                --header-menu-list-h-c: var(--theme2);
            {%- elsif settings.layout_settings_file == 'skin-12' -%}
                --menu-title-font-size: var(--h3-size);
                --menu-title-line-height: var(--h3-line-height);
                --menu-title-letter-spacing: var(--h3-letter-spacing);
                --menu-title-weight: var(--h3-weight);
                --menu-title-transform: none;
                --header-info-line-c: var(--theme);
                --header-social-media-c: var(--theme);
                --header-social-media-h-c: #365F3D;
                --header-menu-h-c: var(--theme-primary);
                --header-btns-d-h-c: var(--theme-primary);
                --header-menu-list-h-bg: var(--theme-primary);
                --header-menu-list-c: var(--theme-primary);
                --header-menu-list-h-c: var(--theme2);
            {%- elsif settings.layout_settings_file == 'skin-13' -%}
                --menu-line-height: {{ settings.font_size_menu | times: 1 | round }}px;
                --menu-letter-spacing: var(--h3-letter-spacing);
                --menu-transform: none;
                --menu-list-line-height: {{ settings.font_size_menu_list | times: 2 | round }}px;
                --menu-list-letter-spacing: var(--h4-letter-spacing);
                --header-line-2-bd: solid 1px var(--theme2);
                --header-menu-list-h-bg: var(--theme-primary);
                --header-menu-list-h-c: var(--theme2);
            {%- elsif settings.layout_settings_file == 'skin-14' -%}
                --header-line-2-bg: var(--theme-primary);
                --header-menu-h-c: #CDED74;
                --header-btn-search-line-1-d-c: #339860;
                --header-btns-sticky-h-c: #CDED74;
            {%- elsif settings.layout_settings_file == 'skin-15' -%}
                --header-line-2-bg: var(--theme-primary);
                --header-m-bg: var(--theme-primary);
                --header-menu-h-c: var(--theme2);
                --header-btns-d-h-c: var(--theme2);
                --header-menu-list-h-bg: #D9F8F4;
            {%- elsif settings.layout_settings_file == 'skin-16' -%}
                --header-line-1-bd: none;
                --header-menu-h-c: var(--theme-primary);
                --header-tape-bg: #E7FBFA;
                --header-tape-c: var(--theme);
                --header-menu-list-h-bg: var(--theme-primary);
                --header-menu-list-h-c: var(--theme2);
            {%- elsif settings.layout_settings_file == 'skin-17' -%}
                --header-line-2-bd: none;
                --header-offset-bottom-bd: 0px;
                --header-info-line-bg: var(--theme-primary);
                --header-menu-list-h-bg: #FFD2B1;
            {%- elsif settings.layout_settings_file == 'skin-18' -%}
                --header-line-2-bd: none;
            {%- endif -%}

            {%- for block in selected_blocks_colorize -%}
                {%- assign colorize = block.settings -%}
                {%- assign transparent_value = 'rgba(0,0,0,0)' -%}
                {%- if colorize.line_1_bg and colorize.line_1_bg != '' and colorize.line_1_bg != transparent_value -%}
                    --header-line-1-bg: {{ colorize.line_1_bg }};
                    --header-search-input-bg: {{ colorize.line_1_bg }};
                    --header-search-input-f-bg: {{ colorize.line_1_bg }};
                {%- endif -%}
                {%- if colorize.line_1_bd and colorize.line_1_bd != '' and colorize.line_1_bd != transparent_value -%}
                    --header-line-1-bd: solid 1px {{ colorize.line_1_bd }};
                    --header-search-input-bd: solid 1px {{ colorize.line_1_bd }};
                    --header-search-input-f-bd: solid 1px {{ colorize.line_1_bd }};
                {%- endif -%}
                {%- if colorize.line_2_bg and colorize.line_2_bg != '' and colorize.line_2_bg != transparent_value -%}
                    --header-line-2-bg: {{ colorize.line_2_bg }};
                {%- endif -%}
                {%- if colorize.line_2_bd and colorize.line_2_bd != '' and colorize.line_2_bd != transparent_value -%}
                    --header-line-2-bd: solid 1px {{ colorize.line_2_bd }};
                {%- endif -%}
                {%- if colorize.search_btn_type != 'auto' -%}
                    {%- assign header_search_btn_type = colorize.search_btn_type -%}
                {%- endif -%}
                {%- if colorize.m_bg and colorize.m_bg != '' and colorize.m_bg != transparent_value -%}
                    --header-m-bg: {{ colorize.m_bg }};
                {%- endif -%}
                {%- if colorize.m_bd and colorize.m_bd != '' and colorize.m_bd != transparent_value -%}
                    --header-m-bd: solid 1px {{ colorize.m_bd }};
                {%- endif -%}
                {%- if colorize.btns_line_2_d_c and colorize.btns_line_2_d_c != '' and colorize.btns_line_2_d_c != transparent_value -%}
                    --header-btns-line-2-d-c: {{ colorize.btns_line_2_d_c }};
                    --header-btn-cart-line-2-d-c: {{ colorize.btns_line_2_d_c }};
                    --header-btn-cart-line-2-text-d-c: {{ colorize.btns_line_2_d_c }};
                    --header-counter-line-2-d-c: {{ colorize.btns_line_2_d_c }};
                    --header-btn-search-line-2-d-c: {{ colorize.btns_line_2_d_c }};
                {%- endif -%}
                {%- if colorize.btns_line_2_d_h_c and colorize.btns_line_2_d_h_c != '' and colorize.btns_line_2_d_h_c != transparent_value -%}
                    --header-btns-line-2-d-h-c: {{ colorize.btns_line_1_d_h_c }};
                    --header-btn-cart-line-2-d-h-c: {{ colorize.btns_line_1_d_h_c }};
                    --header-counter-line-2-d-h-c: {{ colorize.btns_line_1_d_h_c }};
                    --header-btn-search-line-2-d-h-c: {{ colorize.btns_line_1_d_h_c }};
                {%- endif -%}
                {%- if colorize.btns_line_1_d_c and colorize.btns_line_1_d_c != '' and colorize.btns_line_1_d_c != transparent_value -%}
                    --header-btns-line-1-d-c: {{ colorize.btns_line_1_d_c }};
                    --header-btn-cart-line-1-d-c: {{ colorize.btns_line_1_d_c }};
                    --header-btn-cart-line-1-text-d-c: {{ colorize.btns_line_1_d_c }};
                    --header-counter-line-1-d-c: {{ colorize.btns_line_1_d_c }};
                    --header-btn-search-line-1-d-c: {{ colorize.btns_line_1_d_c }};
                {%- endif -%}
                {%- if colorize.btns_line_1_d_h_c and colorize.btns_line_1_d_h_c != '' and colorize.btns_line_1_d_h_c != transparent_value -%}
                    --header-btns-line-1-d-h-c: {{ colorize.btns_line_1_d_h_c }};
                    --header-btn-cart-line-1-d-h-c: {{ colorize.btns_line_1_d_h_c }};
                    --header-counter-line-1-d-h-c: {{ colorize.btns_line_1_d_h_c }};
                    --header-btn-search-line-1-d-h-c: {{ colorize.btns_line_1_d_h_c }};
                {%- endif -%}
                {%- if colorize.btns_line_1_d_h_c and colorize.btns_line_1_d_h_c != '' and colorize.btns_line_1_d_h_c != transparent_value -%}
                    --header-btns-line-1-d-h-c: {{ colorize.btns_line_1_d_h_c }};
                {%- endif -%}
                {%- if colorize.btns_line_1_d_h_c and colorize.btns_line_1_d_h_c != '' and colorize.btns_line_1_d_h_c != transparent_value -%}
                    --header-btns-line-1-d-h-c: {{ colorize.btns_line_1_d_h_c }};
                {%- endif -%}
                {%- if colorize.menu_sticky_c and colorize.menu_sticky_c != '' and colorize.menu_sticky_c != transparent_value -%}
                    --header-menu-sticky-c: {{ colorize.menu_sticky_c }};
                {%- endif -%}
                {%- if colorize.menu_sticky_h_c and colorize.menu_sticky_h_c != '' and colorize.menu_sticky_h_c != transparent_value -%}
                    --header-menu-sticky-h-c: {{ colorize.menu_sticky_h_c }};
                {%- endif -%}
                {%- if colorize.btns_sticky_c and colorize.btns_sticky_c != '' and colorize.btns_sticky_c != transparent_value -%}
                    --header-btns-sticky-c: {{ colorize.btns_sticky_c }};
                {%- endif -%}
                {%- if colorize.btns_sticky_h_c and colorize.btns_sticky_h_c != '' and colorize.btns_sticky_h_c != transparent_value -%}
                    --header-btns-sticky-h-c: {{ colorize.btns_sticky_h_c }};
                {%- endif -%}
                {%- if colorize.btns_m_c and colorize.btns_m_c != '' and colorize.btns_m_c != transparent_value -%}
                    --header-btns-m-c: {{ colorize.btns_m_c }};
                {%- endif -%}
                {%- if colorize.line_additional_bg and colorize.line_additional_bg != '' and colorize.line_additional_bg != transparent_value -%}
                    --header-additional-line-bg: {{ colorize.line_additional_bg }};
                {%- endif -%}
                {%- if colorize.line_additional_bd and colorize.line_additional_bd != '' and colorize.line_additional_bd != transparent_value -%}
                    --header-additional-line-bd: solid 1px {{ colorize.line_additional_bd }};
                {%- endif -%}
                {%- if colorize.line_additional_c and colorize.line_additional_c != '' and colorize.line_additional_c != transparent_value -%}
                    --header-additional-line-c: {{ colorize.line_additional_c }};
                {%- endif -%}
                {%- if colorize.line_additional_highlighted_buttons_c and colorize.line_additional_highlighted_buttons_c != '' and colorize.line_additional_highlighted_buttons_c != transparent_value -%}
                    --header-additional-line-highlighted-c: {{ colorize.line_additional_highlighted_buttons_c }};
                    --header-additional-line-account-c: {{ colorize.line_additional_highlighted_buttons_c }};
                    --header-additional-line-services-c: {{ colorize.line_additional_highlighted_buttons_c }};
                    --header-additional-line-currency-languages-c: {{ colorize.line_additional_highlighted_buttons_c }};
                {%- endif -%}
                {%- if colorize.line_additional_h_c and colorize.line_additional_h_c != '' and colorize.line_additional_h_c != transparent_value -%}
                    --header-additional-line-account-h-c: {{ colorize.line_additional_h_c }};
                    --header-additional-line-services-h-c: {{ colorize.line_additional_h_c }};
                    --header-additional-line-currency-languages-h-c: {{ colorize.line_additional_h_c }};
                {%- endif -%}
                {%- if colorize.info_line_bg and colorize.info_line_bg != '' and colorize.info_line_bg != transparent_value -%}
                    --header-info-line-bg: {{ colorize.info_line_bg }};
                {%- endif -%}
                {%- if colorize.info_line_bd and colorize.info_line_bd != '' and colorize.info_line_bd != transparent_value -%}
                    --header-info-line-bd: solid 1px {{ colorize.info_line_bd }};
                {%- endif -%}
                {%- if colorize.info_line_c and colorize.info_line_c != '' and colorize.info_line_c != transparent_value -%}
                    --header-info-line-c: {{ colorize.info_line_c }};
                {%- endif -%}
                {%- if colorize.info_line_media_c and colorize.info_line_media_c != '' and colorize.info_line_media_c != transparent_value -%}
                    --header-social-media-c: {{ colorize.info_line_media_c }};
                {%- endif -%}
                {%- if colorize.info_line_media_h_c and colorize.info_line_media_h_c != '' and colorize.info_line_media_h_c != transparent_value -%}
                    --header-social-media-h-c: {{ colorize.info_line_media_h_c }};
                {%- endif -%}
                {%- if colorize.tape_bg and colorize.tape_bg != '' and colorize.tape_bg != transparent_value -%}
                    --header-tape-bg: {{ colorize.tape_bg }};
                {%- endif -%}
                {%- if colorize.tape_bd and colorize.tape_bd != '' and colorize.tape_bd != transparent_value -%}
                    --header-tape-bd: solid 1px {{ colorize.tape_bd }};
                {%- endif -%}
                {%- if colorize.tape_c and colorize.tape_c != '' and colorize.tape_c != transparent_value -%}
                    --header-tape-c: {{ colorize.tape_c }};
                {%- endif -%}
                {%- if colorize.tape_btn_close_c and colorize.tape_btn_close_c != '' and colorize.tape_btn_close_c != transparent_value -%}
                    --header-tape-btn-close-c: {{ colorize.tape_btn_close_c }};
                {%- endif -%}
                {%- if colorize.menu_c and colorize.menu_c != '' and colorize.menu_c != transparent_value -%}
                    --header-menu-d-c: {{ colorize.menu_c }};
                {%- endif -%}
                {%- if colorize.menu_h_c and colorize.menu_h_c != '' and colorize.menu_h_c != transparent_value -%}
                    --header-menu-h-c: {{ colorize.menu_h_c }};
                {%- endif -%}
                {%- if colorize.menu_h_bg and colorize.menu_h_bg != '' and colorize.menu_h_bg != transparent_value -%}
                    --header-menu-h-bg: {{ colorize.menu_h_bg }};
                {%- endif -%}
                {%- if colorize.menu_title_c and colorize.menu_title_c != '' and colorize.menu_title_c != transparent_value -%}
                    --header-menu-title-d-c: {{ colorize.menu_title_c }};
                    --header-menu-title-h-c: {{ colorize.menu_title_c }};
                {%- endif -%}
                {%- if colorize.menu_list_c and colorize.menu_list_c != '' and colorize.menu_list_c != transparent_value -%}
                    --header-menu-list-c: {{ colorize.menu_list_c }};
                {%- endif -%}
                {%- if colorize.menu_list_h_c and colorize.menu_list_h_c != '' and colorize.menu_list_h_c != transparent_value -%}
                    --header-menu-list-h-c: {{ colorize.menu_list_h_c }};
                {%- endif -%}
                {%- if colorize.menu_list_h_bg and colorize.menu_list_h_bg != '' and colorize.menu_list_h_bg != transparent_value -%}
                    --header-menu-list-h-bg: {{ colorize.menu_list_h_bg }};
                {%- endif -%}
                {%- if colorize.menu_vertical_button_bg and colorize.menu_vertical_button_bg != '' and colorize.menu_vertical_button_bg != transparent_value -%}
                    --header-menu-vertical-button-bg: {{ colorize.menu_vertical_button_bg }};
                {%- endif -%}
                {%- if colorize.menu_vertical_button_c and colorize.menu_vertical_button_c != '' and colorize.menu_vertical_button_c != transparent_value -%}
                    --header-menu-vertical-button-c: {{ colorize.menu_vertical_button_c }};
                {%- endif -%}
                {%- if colorize.menu_vertical_d_bg and colorize.menu_vertical_d_bg != '' and colorize.menu_vertical_d_bg != transparent_value -%}
                    --header-menu-vertical-d-bg: {{ colorize.menu_vertical_d_bg }};
                {%- endif -%}
                {%- if colorize.menu_vertical_d_bd and colorize.menu_vertical_d_bd != '' and colorize.menu_vertical_d_bd != transparent_value -%}
                    --header-menu-vertical-d-bd: solid 1px {{ colorize.menu_vertical_d_bd }};
                {%- endif -%}
                {%- if colorize.menu_vertical_d_c and colorize.menu_vertical_d_c != '' and colorize.menu_vertical_d_c != transparent_value -%}
                    --header-menu-vertical-d-c: {{ colorize.menu_vertical_d_c }};
                {%- endif -%}
                {%- if colorize.menu_vertical_h_c and colorize.menu_vertical_h_c != '' and colorize.menu_vertical_h_c != transparent_value -%}
                    --header-menu-vertical-h-c: {{ colorize.menu_vertical_h_c }};
                {%- endif -%}
            {%- endfor -%}
        }

        {% render 'css-style-button' with class: '.header__search-form .btn' type: header_search_btn_type %}

        @media (min-width: 1025px) {
            .header__line-additional {
                min-height: {{ header_additional_line_min_height }}px;
            }
            .header__line-top {
                min-height: var(--header-top-line-min-height);
            }
            .header__line-bottom {
                min-height: {{ header_bottom_line_min_height }}px;
            }
            .menu:not(.menu--vertical) .menu__panel > .menu__item > a {
                padding-left: {{ header_menu_items_paddings_horizontal }}px;
                padding-right: {{ header_menu_items_paddings_horizontal }}px;
            }
            .menu:not(.menu--vertical) .menu__panel:not(.menu__panel--on-a-par) {
                margin-left: -{{ header_menu_items_paddings_horizontal }}px;
                margin-right: -{{ header_menu_items_paddings_horizontal }}px;
            }
            {%- if header_menu_list_border_radius > 0 -%}
                .menu__list--styled > .menu__item {
                    border-radius: {{ header_menu_list_border_radius }}px;
                }
            {%- endif -%}
            {%- if section.settings.height > 0 -%}
                .header__line-top {
                    min-height: {{ section.settings.height }}px !important;
                }
            {%- endif -%}
            {%- if section.settings.items_padding > 0 -%}
                .menu:not(.menu--vertical) .menu__panel > .menu__item > a {
                    padding-left: {{ section.settings.items_padding }}px !important;
                    padding-right: {{ section.settings.items_padding }}px !important;
                }
                .menu:not(.menu--vertical) .menu__panel:not(.menu__panel--on-a-par) {
                    margin-left: -{{ section.settings.items_padding }}px !important;
                    margin-right: -{{ section.settings.items_padding }}px !important;
                }
            {%- endif -%}
            {% if template contains 'index' %}
                {% if section.settings.transparent_bg contains 'transparent' or header_enable_transparent %}
                    {% capture transparent_selector %}.header:not(.header--sticky){% if section.settings.transparent_bg contains 'hover' %}:not(:hover){% endif %}{% endcapture %}
                    {{ transparent_selector }} .header__content,
                    {{ transparent_selector }} .header__line-additional,
                    {{ transparent_selector }} .header__line--colorize-1,
                    {{ transparent_selector }} .header__line--colorize-2,
                    {{ transparent_selector }} .header__nav,
                    {{ transparent_selector }} .header__search-form input,
                    {{ transparent_selector }} .header__search-form button,
                    {{ transparent_selector }} .header__select-currency select {
                        background-color: transparent !important;
                    }
                    {{ transparent_selector }} .header__content,
                    {{ transparent_selector }} .header__line-additional,
                    {{ transparent_selector }} .header__line--colorize-1,
                    {{ transparent_selector }} .header__line--colorize-2 {
                        border-color: transparent !important;
                    }
                    {% if selected_blocks_info_line.size > 0 and selected_blocks_info_line[0].settings.transparent_bg %}
                        {{ transparent_selector }} .header__line-info {
                            background-color: transparent !important;
                        }
                        {{ transparent_selector }} .header__line-info {
                            border-color: transparent !important;
                        }
                        .header__line-info {
                            transition-duration: 0.2s;
                            transition-property: background-color, border-color;
                        }
                    {% endif %}
                    {% if selected_blocks_tape.size > 0 and selected_blocks_tape[0].settings.transparent_bg %}
                        {{ transparent_selector }} .header__tape {
                            background-color: transparent !important;
                        }
                        .header__tape {
                            transition-duration: 0.2s;
                            transition-property: background-color, border-color;
                        }
                    {% endif %}
                    .header__content,
                    .header__line-additional,
                    .header__line--colorize-1,
                    .header__line--colorize-2,
                    .header__nav,
                    .header__search-form input,
                    .header__search-form button,
                    .header__select-currency select {
                        transition-duration: 0.2s;
                        transition-property: background-color, border-color;
                    }
                {% endif %}
            {% endif %}
        }
        {%- if header_logo_adaptive -%}
            {% if section.settings.type == '4' %}
                @media (min-width: 1025px) and (max-width: {{ header_logo_adaptive_max_width }}px) {
                    .header__logo--center {
                        position: relative !important;
                        margin-right: 25px;
                        order: -1;
                    }
                    [dir='rtl'] .header__logo--center {
                        margin-left: 25px;
                        margin-right: 0;
                    }
                }
            {%- endif -%}
        {%- endif -%}
        .header__sticky-logo svg {
            max-width: {{ settings.logo_sticky_width }}px;
        }
        .header__tape {
            min-height: {{ header_tape_min_height }}px;
        }
        .header__line-info {
            min-height: {{ header_info_line_min_height }}px;
        }
        {% if section.settings.country_selector_max_width > 0 %}
            .header__select-currency [name="currency"] {
                max-width: {{ section.settings.country_selector_max_width }}px;
            }
        {% endif %}
        {%- if need_mobile_logo -%}
            @media (max-width: 1024px) {
                html:not(.css-theme-loaded) .header__logo--mobile {
                    min-width: {{ settings.logo_mobile_width }}px;
                }
                {%- if section.settings.logo_mobile_centered -%}
                    .header__logo {
                        position: absolute;
                        left: 50%;
                        transform: translate3d(-50%, 0, 0);
                    }
                {%- endif -%}
            }
        {%- endif -%}
        {% unless is_design_mode %}
            .header:not(.header--visible) .header__logo,
            .header:not(.header--visible) .header__nav {
                visibility: hidden;
            }
        {% endunless %}
    </style>
{% endcapture %}
{%- if style_base_html -%}
    {%- if request.design_mode -%}
        {{ style_base_html }}
    {%- else -%}
        <template class="js-loader-inline-style" data-key="{{ style_base_html | hmac_sha1: 'secret_key' }}">{{ style_base_html }}</template>
    {%- endif -%}
{%- endif -%}
<script>
    theme.AssetsLoader.loadInlineStyles();
</script>
<header-section data-section-id="{{ section.id }}" data-section-type="header"{% render 'layout-get-container-class' with container: header_container custom_class: 'd-block' %}>
    <header id="header" class="header header--type-{{ section.settings.type }} header--style-{{ section.settings.style }}{% if template contains 'index' and section.settings.transparent_bg contains 'transparent' or header_enable_transparent %} header--transparent position-absolute top-0 w-100{% else %} position-lg-relative{% endif %}">
        {%- if section.settings.sticky != 'disable' -%}
            <sticky-header class="header--sticky-type-{{ section.settings.desktop_sticky_type }} d-block" data-sticky-type="{{ section.settings.sticky }}" data-desktop-sticky-sidebar="{% if need_sticky_sidebar %}true{% else %}false{% endif %}" data-hide-when-scroll-down="{{ section.settings.hide_sticky_when_scroll_down }}">
        {%- endif -%}
        {%- for block in selected_blocks_tape -%}
            <div class="d-none js-header-tape" data-js-show-once="{{ header_tape_show_once }}" data-js-delay="{{ block.settings.delay }}" data-js-cookies-life="{{ header_tape_cookies_life }}">
                <div class="header__tape header__tape--style-{{ block.settings.style }} d-flex py-5">
                    <div class="container d-flex flex-center">
                        <div class="position-relative d-flex align-items-center px-35">
                            <p class="m-0 px-30 px-lg-0 text-center">{{ block.settings.content }}</p>
                            <i class="header__tape-close position-absolute right-0 cursor-pointer" data-js-action="close">{% render 'icon-theme-190' %}</i>
                        </div>
                    </div>
                </div>
            </div>
            {%- if block.settings.delay == 0 -%}
                <script>
                    if(document.cookie.indexOf('header-tape=off') === -1) {
                        document.querySelector('.js-header-tape').classList.remove('d-none');
                    }
                </script>
            {%- endif -%}
        {%- endfor -%}
        {%- for block in selected_blocks_info_line -%}
            {%- capture info_line_html -%}
                <div class="header__line-info header__line-info--style-{{ block.settings.style }} {% if block.settings.show_on_mobile %}d-flex{% else %}d-none d-lg-flex{% endif %} align-items-lg-center px-10 px-lg-0 pt-15 pb-10 py-lg-2">
                    <div class="{% if section.settings.type == '4' %}w-100 px-10 px-lg-20{% else %}container{% endif %} d-flex flex-column flex-lg-row align-items-center">
                        <div class="text-center text-lg-left">
                            {% include 'parse-page-html-content' with default_page: 'include-header-info-line' page_content: block.settings.page_content %}
                        </div>
                        {%- if block.settings.show_social_media -%}
                            <div class="header__social-media d-flex align-items-center{% if block.settings.page_content != blank %} mt-10 mt-lg-0{% endif %} ml-lg-auto">
                                {% render 'social-media' with block: block type: 'row' social_enable_tooltip: header_enable_tooltips %}
                            </div>
                        {%- endif -%}
                    </div>
                </div>
            {%- endcapture -%}
        {%- endfor -%}
        {%- capture btn_menu_html -%}
            {%- if section.settings.menu != blank or section.settings.vertical_menu != blank -%}
                <span class="header__btn-menu d-flex align-items-center d-lg-none mr-20 cursor-pointer js-popup-button" data-js-popup-button="navigation">
                    {%- if settings.search_show -%}
                        <i class="mt-3">{% render 'icon-theme-321' %}</i>
                    {%- else -%}
                        <i>{% render 'icon-theme-191' %}</i>
                    {%- endif -%}
                </span>
            {%- endif -%}
        {%- endcapture -%}
        {%- capture logo_html -%}
            {%- if need_mobile_logo -%}
                <div class="header__logo header__logo--mobile d-flex d-lg-none align-items-center" style="width: {{ settings.logo_mobile_width }}px;">
                    {% include 'logo' svg: settings.logo_mobile_svg_file image: settings.logo_mobile_image width: settings.logo_mobile_width %}
                </div>
            {%- endif -%}
            <div class="header__logo align-items-center {% if need_mobile_logo %}d-none d-lg-flex{% else %}d-flex{% endif %}{% if section.settings.type == '3' %} mr-lg-25{% elsif section.settings.type == '2' %} header__logo--center mx-lg-auto{% elsif section.settings.type == '4' %} header__logo--center position-lg-absolute h-100 top-lg-0{% endif %}{% if need_sticky_logo or need_sticky_logo_type_3_4 %} header__logo--sticky-hidden{% else %}{% endif %}" style="width: {{ settings.logo_width }}px;"{% if section.settings.type == '4' %} data-js-header-width-limit-edge="nav"{% endif %}>
                {%- if template.name == 'index' -%}
                    {%- assign main_logo_tag = 'h1' -%}
                {%- endif -%}
                {%- if template contains 'index' and section.settings.transparent_bg contains 'transparent' or header_enable_transparent -%}
              		{%- if settings.logo_transparent_svg_file != blank or settings.logo_transparent_image != blank -%}
              			{% include 'logo' svg: settings.logo_transparent_svg_file image: settings.logo_transparent_image width: settings.logo_transparent_width tag: main_logo_tag %}
                        {%- assign added_transparent_header_logo = true -%}
                  	{%- endif -%}
                {%- endif -%}
              	{%- if added_transparent_header_logo == blank -%}
              		{% include 'logo' svg: settings.logo_svg_file image: settings.logo_image width: settings.logo_width tag: main_logo_tag %}
              	{%- endif -%}
            </div>
            {%- if need_sticky_logo_type_3_4 -%}
                <div class="header__sticky-logo-replace align-items-center d-none{% if section.settings.type == '3' %} mr-lg-25{% elsif section.settings.type == '4' %} header__logo--center position-lg-absolute h-100 top-lg-0{% endif %}" style="width: {{ settings.logo_sticky_width }}px;">
                    {% include 'logo' svg: settings.logo_sticky_svg_file image: settings.logo_sticky_image width: settings.logo_sticky_width %}
                </div>
            {%- endif -%}
        {%- endcapture -%}
        {%- if need_sticky_logo -%}
            {%- capture sticky_logo_html -%}
                <div class="header__sticky-logo d-none align-items-lg-center h-100{% if section.settings.type == '3' %} header__sticky-logo--containerized mr-25{% else %} header__sticky-logo--displaced position-absolute top-0 left-0 py-6 ml-15{% endif %}" style="width: {{ settings.logo_sticky_width }}px;">
                    {% include 'logo' svg: settings.logo_sticky_svg_file image: settings.logo_sticky_image width: settings.logo_sticky_width %}
                </div>
            {%- endcapture -%}
        {%- endif -%}
        {%- capture btn_account_html -%}
            {%- if settings.account_show_header_button and shop.customer_accounts_enabled -%}
                {%- if settings.app_growave_enable == true and settings.account_growave_social_login_enable == true -%}
                    <div id="ssw-topauth" class="ssw-topauth">
                        {%- if customer -%}
                            <div class="ssw-tprofile ssw-dropdown">
                                <a class="ssw-dropdown-toggle header__btn-account d-flex{% if section.settings.type == '5' or section.settings.type == '6' or section.settings.type == '7' %} d-lg-none{% endif %} align-items-center position-relative ml-25 ml-lg-15" data-toggle="ssw-dropdown" href="javascript:void(0);">
                                    <i>{% render 'icon-theme-201' %}</i>
                                </a>
                                <ul class="ssw-dropdown-menu" role="menu" aria-labelledby="dLabel">
                                    <li id="customer_myorders_li"><a tabindex="-1" href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}">{{ 'socialshopwave.my_orders' | t }}</a></li>
                                    {% render 'ssw-widget-dropdown' %}
                                    <li class="ssw-divider"></li>
                                    <li><a id="customer_logout_link" tabindex="-1" href="{{ routes.account_logout_url }}">{{ 'socialshopwave.logout' | t }}</a></li>
                                </ul>
                            </div>
                        {%- else -%}
                            <a id="customer_login_link" class="header__btn-account d-flex{% if section.settings.type == '5' or section.settings.type == '6' or section.settings.type == '7' %} d-lg-none{% endif %} align-items-center position-relative ml-25 ml-lg-15" href="javascript:void(0);" data-toggle="ssw-modal" data-target="#login_modal" onclick="trackShopStats('login_popup_view', 'all')"><i>{% render 'icon-theme-201' %}</i></a>
                        {%- endif -%}
                    </div>
                {%- else -%}
                    <a href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}" class="header__btn-account d-flex{% if section.settings.type == '5' or section.settings.type == '6' or section.settings.type == '7' %} d-lg-none{% endif %} align-items-center position-relative ml-25 ml-lg-15 js-popup-button"
                        data-js-popup-button="account"
                        {%- if settings.tooltips_enable and header_enable_tooltips %}
                            data-js-tooltip
                            data-tippy-content="{{ 'layout.header.tooltip.account' | t }}"
                            data-tippy-placement="bottom"
                            data-tippy-distance="6"
                        {%- endif -%}
                    >
                        <i>{% render 'icon-theme-201' %}</i>
                    </a>
                {%- endif -%}
            {%- endif -%}
        {%- endcapture -%}
        {%- capture btn_wishlist_html -%}
            {%- if settings.wishlist_type == 'default' -%}
                <div class="ml-25 ml-lg-15">
                    <a href="#" class="header__btn-wishlist d-flex align-items-center position-relative text-nowrap js-popup-button" data-js-popup-button="wishlist"{% if need_sticky_sidebar %} data-js-sticky-replace-element="wishlist"{% endif %}
                        {%- if settings.tooltips_enable and header_enable_tooltips %}
                            data-js-tooltip
                            data-tippy-content="{{ 'layout.header.tooltip.wishlist' | t }}"
                            data-tippy-placement="bottom"
                            data-tippy-distance="6"
                        {%- endif -%}
                    >
                        <i>{% render 'icon-theme-180' %}</i>
                        {% capture wishlist_count %}{% if customer %}{{ customer.metafields.wishlist | size }}{% else %}0{% endif %}{% endcapture %}
                        <span class="header__counter" data-js-wishlist-count="{{ wishlist_count }}">{{ wishlist_count }}</span>
                    </a>
                </div>
            {%- elsif settings.wishlist_type == 'growave' and settings.app_growave_enable == true -%}
                <div class="ml-25 ml-lg-15 mb-2">
                    {% capture the_snippet_fave_icon_menu %}{% render 'ssw-widget-faveicon-menu' with product.id %}{% endcapture %}
                    {%- unless the_snippet_fave_icon_menu contains 'Liquid error' -%}
                        {{ the_snippet_fave_icon_menu }}
                    {%- endunless -%}
                </div>
            {%- endif -%}
        {%- endcapture -%}
        {%- capture btn_compare_html -%}
            {%- if settings.compare_type == 'default' -%}
                <div class="ml-25 ml-lg-15">
                    <a href="#" class="header__btn-compare d-flex align-items-center position-relative text-nowrap js-popup-button" data-js-popup-button="compare-full"{% if need_sticky_sidebar %} data-js-sticky-replace-element="compare"{% endif %}
                        {%- if settings.tooltips_enable and header_enable_tooltips %}
                            data-js-tooltip
                            data-tippy-content="{{ 'layout.header.tooltip.compare' | t }}"
                            data-tippy-placement="bottom"
                            data-tippy-distance="6"
                        {%- endif -%}
                    >
                        <i>{% render 'icon-theme-039' %}</i>
                        {% capture compare_count %}{% if customer %}{{ customer.metafields.compare | size }}{% else %}0{% endif %}{% endcapture %}
                        <span class="header__counter" data-js-compare-count="{{ compare_count }}">{{ compare_count }}</span>
                    </a>
                </div>
            {%- endif -%}
        {%- endcapture -%}
        {%- capture btn_cart_html -%}
            {%- if settings.cart_show_header_button -%}
                <div class="ml-25 ml-lg-15">
                    <a href="/cart" class="header__btn-cart position-relative d-flex align-items-center text-nowrap js-popup-button" data-js-popup-button="cart"{% if need_sticky_sidebar %} data-js-sticky-replace-element="cart"{% endif %}>
                        {% capture cart_icon_name %}{{ settings.cart_icon | default: 'icon-theme-109' }}{% endcapture %}
                        <i class="mr-lg-7">{% include cart_icon_name %}</i>
                        <span class="d-none d-lg-inline mt-lg-4" data-js-cart-count-desktop="{{ cart.item_count }}">{{ 'layout.header.cart_count' | t: count: cart.item_count }}</span>
                        <span class="header__counter d-lg-none" data-js-cart-count-mobile="{{ cart.item_count }}">{{ cart.item_count }}</span>
                    </a>
                </div>
            {%- endif -%}
        {%- endcapture -%}
        {%- capture currencies_html -%}
            {%- if settings.show_multiple_currencies -%}
                {%- assign codes = 'USD,EUR,GBP,CAD,ARS,AUD,BBD,BDT,BSD,BHD,BRL,BOB,BND,BGN,ILS,MMK,KYD,CLP,CNY,COP,CRC,HRK,CZK,DKK,DOP,XCD,EGP,XPF,FJD,GHS,GTQ,GYD,GEL,HKD,HUF,ISK,INR,IDR,NIS,JMD,JPY,JOD,KZT,KES,KWD,LVL,LTL,MXN,MYR,MUR,MDL,MAD,MNT,MZN,ANG,NZD,NGN,NOK,OMR,PKR,PYG,PEN,PHP,PLN,QAR,RON,RUB,SAR,RSD,SCR,SGD,SYP,ZAR,KRW,LKR,SEK,CHF,TWD,THB,TZS,TTD,TRY,UAH,AED,UYU,VEB,VND,ZMK,IQD' | split: ',' -%}
                {%- assign supported_codes = settings.supported_currencies | split: ' ' -%}
                {%- assign supported_currencies_full_names = settings.supported_currencies_full_names | split: '|' -%}
                {%- assign supported_currencies_short_names = settings.supported_currencies_short_names | split: '|' -%}
                {%- capture list -%}
                    {%- if supported_codes.size > 0 -%}
                        <ul class="list-unstyled w-100 px-10 px-lg-15 py-30 py-lg-15">
                            {%- for code in supported_codes -%}
                                {%- if codes contains code -%}
                                    {%- assign index = forloop.index | minus: 1 -%}
                                    <li{% if code == shop.currency %} class="active"{% endif %}{% if supported_currencies_short_names[index] %} data-button-name="{{ supported_currencies_short_names[index] }}"{% endif %} data-currency-code="{{ code }}"><a href="#{{ code }}">{{ supported_currencies_full_names[index] | default: code }}</a></li>
                                    {%- if code == shop.currency -%}
                                        {%- assign shop_currency = supported_currencies_short_names[index] -%}
                                    {%- endif -%}
                                {%- endif -%}
                            {%- endfor -%}
                        </ul>
                    {%- endif -%}
                {%- endcapture -%}
                <div class="position-lg-relative d-none d-lg-block ml-lg-15" data-js-position-desktop="currencies">
                    <div class="js-position{% if supported_codes.size > 1 %} js-dropdown js-currencies-list{% endif %}" data-js-position-name="currencies">
                        <div class="header__btn-currency position-relative d-none d-lg-flex align-items-lg-center mt-4 cursor-pointer" data-js-dropdown-button>
                            <span data-current-currency>{{ shop_currency | default: shop.currency }}</span>
                            {%- if supported_codes.size > 1 -%}
                                <i>{% render 'icon-theme-229' %}</i>
                            {%- endif -%}
                        </div>
                        {%- if supported_codes.size > 0 -%}
                            <div class="header__dropdown dropdown d-lg-none position-lg-absolute top-lg-100 right-lg-0" data-js-dropdown>
                                {{ list }}
                            </div>
                        {%- endif -%}
                    </div>
                </div>
            {%- elsif section.settings.enable_country_selector and localization.available_countries.size > 1 -%}
                <div class="position-lg-relative d-none d-lg-block ml-lg-15" data-js-position-desktop="currencies">
                    <div class="js-position select position-relative js-dropdown js-select js-currencies-form" data-js-position-name="currencies">
                        <div class="header__select-currency d-flex align-items-center" data-js-dropdown-button>
                            {% form 'localization', class: 'mb-0' %}
                            <select name="currency" class="d-none d-lg-block position-lg-relative h-auto p-0 pr-10 mb-0 border-0 cursor-pointer">
                                    {%- assign dropdown_options = null -%}
                                    {%- for country in localization.available_countries -%}
                                        {% comment %}
                                        {%- unless shop.enabled_currencies contains country.currency -%}
                                            {%- continue -%}
                                        {%- endunless -%}
                                        {% endcomment %}
                                        {%- if country.iso_code == localization.country.iso_code -%}
                                            {%- assign is_selected = true -%}
                                        {%- else -%}
                                            {%- assign is_selected = false -%}
                                        {%- endif -%}
                                        <option value="{{country.iso_code}}"{% if is_selected %} selected{% endif %}>{% if section.settings.show_country_name %}{{ country.name }} {% endif %} {{ country.currency.iso_code }}({{ country.currency.symbol }})</option>
                                        {%- capture option -%}
                                            <span data-value="{{country.iso_code}}"{% if is_selected %} class="selected"{% endif %}>{% if section.settings.show_country_name %}{{ country.name }} {% endif %}{{ country.currency.iso_code }} ({{ country.currency.symbol }})</span>
                                        {%- endcapture -%}
                                        {%- assign dropdown_options = dropdown_options | append: option -%}
                                    {%- endfor -%}
                                </select>
                              <input type="hidden" name="country_code" value="{{ localization.country.iso_code }}">
                            {% endform %}
                            <i class="d-none d-lg-flex position-absolute right-0">{% render 'icon-theme-229' %}</i>
                        </div>
                        <div class="header__dropdown select__dropdown dropdown d-lg-none position-lg-absolute top-lg-100 right-lg-0" data-js-dropdown data-js-select-dropdown>
                            <div class="px-15 py-30 py-lg-15">
                                {{ dropdown_options }}
                            </div>
                        </div>
                    </div>
                </div>
            {%- endif -%}
        {%- endcapture -%}
        {%- capture languages_html -%}
            {%- if settings.app_language != 'none' -%}
                <div class="position-lg-relative d-none d-lg-block ml-lg-15" data-js-position-desktop="languages">
                    {%- case settings.app_language -%}
                        {% when 'store_languages' %}

                             <div class="js-position js-dropdown js-languages-list" data-js-position-name="languages">
                                <div class="header__btn-language position-relative d-none d-lg-flex align-items-lg-center mt-lg-4 cursor-pointer" data-js-dropdown-button>
                                    <span>{{ localization.language.endonym_name | capitalize }}</span>
                                    <i>{% render 'icon-theme-229' %}</i>
                                </div>
                                <div class="header__dropdown dropdown d-lg-none position-lg-absolute top-lg-100 right-lg-0" data-js-dropdown>
                                    <localization-form>
                                        {%- form 'localization', id: 'HeaderLanguageForm', class: 'mb-0' -%}
                                            <ul role="list" class="list-unstyled w-100 px-10 px-lg-15 py-30 py-lg-15">
                                                {%- for language in localization.available_languages -%}
                                                    <li>
                                                        <a
                                                            {% if language.iso_code == localization.language.iso_code %} class="active"{% endif %}
                                                            href="#"
                                                            hreflang="{{ language.iso_code }}"
                                                            lang="{{ language.iso_code }}"
                                                            {% if language.iso_code == localization.language.iso_code %}
                                                            aria-current="true"
                                                            {% endif %}
                                                            data-value="{{ language.iso_code }}"
                                                        >
                                                            {{ language.endonym_name | capitalize }}
                                                        </a>
                                                    </li>
                                                {%- endfor -%}
                                            </ul>
                                            <input type="hidden" name="locale_code" value="{{ localization.language.iso_code }}">
                                        {%- endform -%}
                                    </localization-form>
                                </div>
                            </div>
                            
                        {% when 'weglot' %}
                            <div class="js-position js-dropdown js-languages-list js-weglot d-none-important" data-js-position-name="languages">
                                <div class="header__btn-language position-relative d-none d-lg-flex align-items-lg-center mt-lg-4 cursor-pointer" data-js-dropdown-button>
                                    <span></span>
                                    <i>{% render 'icon-theme-229' %}</i>
                                </div>
                                <div class="header__dropdown dropdown d-lg-none position-lg-absolute top-lg-100 right-lg-0" data-js-dropdown>
                                    <ul class="list-unstyled w-100 px-10 px-lg-15 pb-30 py-lg-15"></ul>
                                </div>
                            </div>
                        {% when 'links_list' %}
                            <div class="js-position js-dropdown js-languages-list" data-js-position-name="languages">
                                <div class="header__btn-language position-relative d-none d-lg-flex align-items-lg-center mt-lg-4 cursor-pointer" data-js-dropdown-button>
                                    <span>{{ linklists[settings.app_language_link_list].links.first.title | default: 'Lang' }}</span>
                                    <i>{% render 'icon-theme-229' %}</i>
                                </div>
                                <div class="header__dropdown dropdown d-lg-none position-lg-absolute top-lg-100 right-lg-0" data-js-dropdown>
                                    <ul class="list-unstyled w-100 px-15 pb-30 py-lg-15">
                                       {%- for language in linklists[settings.app_language_link_list].links -%}
                                            <li {% if forloop.first %}class="active"{% endif %}>
                                                {%- if forloop.first -%}
                                                    <span>{{ language.title }}</span>
                                                {%- else -%}
                                                    <a href="{{ language.url }}">{{ language.title }}</a>
                                                {%- endif -%}
                                            </li>
                                       {%- endfor -%}
                                    </ul>
                                </div>
                            </div>
                    {%- endcase -%}
                </div>
            {%- endif -%}
        {%- endcapture -%}
        {%- capture btn_search_html -%}
            {%- if settings.search_show and section.settings.type != '5' and section.settings.type != '6' and section.settings.type != '7' -%}
                <a href="{{ routes.search_url }}" class="header__btn-search d-none d-lg-flex align-items-lg-center{% if section.settings.type == '1' %} ml-lg-auto{% else %} ml-25 ml-lg-15{% endif %} js-popup-button"
                   data-js-popup-button="navigation"
                    {%- if settings.tooltips_enable and header_enable_tooltips and section.settings.type != '1' %}
                       data-js-tooltip
                       data-tippy-content="{{ 'layout.header.tooltip.search' | t }}"
                       data-tippy-placement="bottom"
                       data-tippy-distance="6"
                    {%- endif -%}
                >
                {%- if section.settings.type == '1' -%}
                    <span class="mr-lg-7">{{ 'layout.header.search' | t }}</span>
                {%- endif -%}
                    <i>{% render 'icon-theme-168' %}</i>
                </a>
            {%- endif -%}
        {%- endcapture -%}
        {%- capture nav_html -%}
            <div class="header__nav d-none d-lg-flex{% if section.settings.type == '4' %} mr-lg-auto invisible-lg{% endif %}" data-js-position-desktop="menu"{% if section.settings.type == '4' %} data-js-header-width-limit="nav" data-js-header-width-limit-margin="15"{% endif %}>
                {%- if section.settings.type == '2' -%}
                    {%- assign menu_centered = true -%}
                {%- endif -%}
                {%- if section.settings.type != '5' and section.settings.type != '7' -%}
                    {%- assign is_main_menu = true -%}
                {%- endif -%}
                {% include 'header-get-menu' with is_main_menu: is_main_menu centered: menu_centered %}
            </div>
        {%- endcapture -%}
        {%- capture services_html -%}
            {%- if settings.services_show_header_button -%}
                <a href="{%- if settings.services_header_button_link != blank -%}{{ settings.services_header_button_link }}{% else %}/pages/customer-service {%- endif -%}" class="header__btn-services position-relative d-flex{% if section.settings.type == '5' or section.settings.type == '6' or section.settings.type == '7' %} d-lg-none{% endif %} align-items-center ml-25 ml-lg-15 js-popup-button"
                   data-js-popup-button="services"
                   {%- if settings.tooltips_enable and header_enable_tooltips %}
                       data-js-tooltip
                       data-tippy-content="{{ 'layout.header.tooltip.services' | t }}"
                       data-tippy-placement="bottom"
                       data-tippy-distance="6"
                   {%- endif -%}
                >
                    <i>{% render 'icon-theme-172' %}</i>
                </a>
            {%- endif -%}
        {%- endcapture -%}
        {%- if need_sticky_sidebar -%}
            {%- capture sticky_sidebar_html -%}
                <div class="header__sticky-sidebar position-absolute d-none align-items-lg-center top-0 right-0 h-100 mr-15">
                    {%- if settings.wishlist_type == 'default' -%}
                        <div class="ml-lg-15" data-js-sticky-replace-here="wishlist"></div>
                    {%- endif -%}
                    {%- if section.settings.show_button_compare -%}
                        <div class="ml-lg-15" data-js-sticky-replace-here="compare"></div>
                    {%- endif -%}
                    {%- if settings.cart_show_header_button -%}
                        <div class="ml-lg-15" data-js-sticky-replace-here="cart"></div>
                    {%- endif -%}
                </div>
            {%- endcapture -%}
        {%- endif -%}
        {%- case section.settings.type -%}
        {%- when '1' -%}
            <div class="header__content"{% if section.settings.desktop_sticky_type == 'wide' %} data-js-desktop-sticky{% endif %} data-js-mobile-sticky>
                {{ info_line_html }}
                <div class="header__line-top header__line--colorize-1 position-relative d-flex px-10 px-lg-0 py-lg-2">
                    <div class="container d-flex align-items-center">
                        {{ btn_menu_html }}
                        {{ logo_html }}
                        <div class="header__sidebar d-flex align-items-center ml-auto">
                            {{ services_html }}
                            {{ btn_account_html }}
                            {{ btn_wishlist_html }}
                            {{ btn_compare_html }}
                            {{ btn_cart_html }}
                            {{ currencies_html }}
                            {{ languages_html }}
                        </div>
                    </div>
                </div>
                <div{% if section.settings.desktop_sticky_type == 'slim' %} data-js-desktop-sticky{% endif %}>
                    <div class="header__line-bottom header__line--colorize-2 position-relative d-lg-flex py-lg-6">
                        {{ sticky_logo_html }}
                        <div class="container d-lg-flex">
                            {{ nav_html }}
                            {{ btn_search_html }}
                        </div>
                        {{ sticky_sidebar_html }}
                    </div>
                </div>
            </div>
        {%- when '2' -%}
            <div class="header__content"{% if section.settings.desktop_sticky_type == 'wide' %} data-js-desktop-sticky{% endif %} data-js-mobile-sticky>
                {{ info_line_html }}
                <div class="header__line-top header__line--colorize-1 position-relative d-flex px-10 px-lg-0 py-lg-6">
                    <div class="container position-lg-relative d-flex align-items-center">
                        {{ btn_menu_html }}
                        {{ logo_html }}
                        <div class="header__sidebar d-flex align-items-center ml-auto">
                            {{ btn_search_html }}
                            {{ services_html }}
                            {{ btn_account_html }}
                            {{ btn_wishlist_html }}
                            {{ btn_compare_html }}
                            {{ btn_cart_html }}
                            {{ currencies_html }}
                            {{ languages_html }}
                        </div>
                    </div>
                </div>
                <div{% if section.settings.desktop_sticky_type == 'slim' %} data-js-desktop-sticky{% endif %}>
                    <div class="header__line-bottom header__line--colorize-2 position-relative d-lg-flex py-lg-6">
                        {{ sticky_logo_html }}
                        <div class="container d-lg-flex justify-content-lg-center">
                            {{ nav_html }}
                        </div>
                        {{ sticky_sidebar_html }}
                    </div>
                </div>
            </div>
        {%- when '3' -%}
            <div class="header__content" data-js-desktop-sticky data-js-mobile-sticky>
                {{ info_line_html }}
                <div class="header__line-top header__line--colorize-2 position-relative d-flex px-10 px-lg-0 py-lg-6">
                    <div class="container d-flex align-items-center align-items-lg-stretch">
                        {{ btn_menu_html }}
                        {{ sticky_logo_html }}
                        {{ logo_html }}
                        {{ nav_html }}
                        <div class="header__sidebar d-flex align-items-center ml-auto">
                            {{ btn_search_html }}
                            {{ services_html }}
                            {{ btn_account_html }}
                            {{ btn_wishlist_html }}
                            {{ btn_compare_html }}
                            {{ btn_cart_html }}
                            {{ currencies_html }}
                            {{ languages_html }}
                        </div>
                    </div>
                </div>
            </div>
        {%- when '4' -%}
            <div class="header__content" data-js-mobile-sticky>
                {{ info_line_html }}
                <div class="header__line-top header__line--colorize-2 position-relative d-flex px-10 px-lg-0 py-lg-6" data-js-desktop-sticky>
                    <div class="container-fluid d-flex align-items-center align-items-lg-stretch justify-content-lg-center px-lg-{{ header_menu_container_paddings_horizontal }}">
                        {{ btn_menu_html }}
                        {{ sticky_logo_html }}
                        {{ nav_html }}
                        {{ logo_html }}
                        <div class="header__sidebar d-flex align-items-center pr-lg-{{ header_menu_container_paddings_horizontal }} ml-auto">
                            {{ btn_search_html }}
                            {{ services_html }}
                            {{ btn_account_html }}
                            {{ btn_wishlist_html }}
                            {{ btn_compare_html }}
                            {{ btn_cart_html }}
                            {{ currencies_html }}
                            {{ languages_html }}
                        </div>
                    </div>
                </div>
            </div>
        {%- when '5' -%}
            <div class="header__content"{% if section.settings.desktop_sticky_type == 'wide' %} data-js-desktop-sticky{% endif %}>
                {{ info_line_html }}
                <div class="header__line-additional position-relative {% if section.settings.additional_line_info_page_content != blank %}d-flex px-10{% else %}d-none d-lg-flex{% endif %} px-lg-0">
                    <div class="container d-lg-flex align-items-lg-center">
                        <div>
                            {% include 'parse-page-html-content' with default_page: 'include-header-info-line-4' page_content: section.settings.additional_line_info_page_content %}
                        </div>
                        <div class="header__line-additional_sidebar d-none d-lg-flex align-items-center ml-auto">
                            {%- if settings.services_show_header_button or settings.account_show_header_button and shop.customer_accounts_enabled -%}
                                <div class="d-lg-flex align-items-lg-center">
                                    {%- if settings.app_growave_enable == true and settings.account_growave_social_login_enable == true -%}
                                        {%- if shop.customer_accounts_enabled -%}
                                            <div id="ssw-topauth" class="ssw-topauth ml-25 ml-lg-0{% if settings.app_language != 'none' or settings.show_multiple_currencies or shop.enabled_currencies.size > 1 %} mr-lg-15{% endif %}">
                                                {%- if customer -%}
                                                    <div class="ssw-tprofile ssw-dropdown">
                                                        <a class="ssw-dropdown-toggle header__line-additional_button header__line-additional_button--login" data-toggle="ssw-dropdown" href="javascript:void(0);">
                                                            <i class="ssw-icon-user"></i>
                                                            {{ customer.first_name }}
                                                            {%- if customer.last_name != 'Unknown' -%}
                                                                {{ customer.last_name | slice: 0 | upcase }}.
                                                            {%- endif -%}
                                                            <i class="ssw-icon-down-open-big"></i>
                                                        </a>
                                                        <ul class="ssw-dropdown-menu" role="menu" aria-labelledby="dLabel">
                                                            <li id="customer_myorders_li"><a tabindex="-1" href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}">{{ 'socialshopwave.my_orders' | t }}</a></li>
                                                            {% render 'ssw-widget-dropdown' %}
                                                            <li class="ssw-divider"></li>
                                                            <li><a id="customer_logout_link" tabindex="-1" href="{{ routes.account_logout_url }}">{{ 'socialshopwave.logout' | t }}</a></li>
                                                        </ul>
                                                    </div>
                                                {%- else -%}
                                                    <a id="customer_login_link" class="header__line-additional_button header__line-additional_button--register" href="javascript:void(0);" data-toggle="ssw-modal" data-target="#login_modal" onclick="trackShopStats('login_popup_view', 'all')">{{ 'socialshopwave.log_in' | t }}</a>
                                                    <span class="ssw-login-divider">/</span>
                                                    <a id="customer_register_link" class="header__line-additional_button header__line-additional_button--register" data-toggle="ssw-modal" data-target="#signup_modal" href="javascript:void(0);" onclick="trackShopStats('login_popup_view', 'all')">{{ 'socialshopwave.sign_up' | t }}</a>
                                                {%- endif -%}
                                            </div>
                                        {%- endif -%}
                                    {%- else -%}
                                        {%- if settings.account_show_header_button and shop.customer_accounts_enabled -%}
                                            <span class="ml-25 ml-lg-0 mr-lg-15">
                                                {%- if customer -%}
                                                    <a href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}" class="header__line-additional_button header__line-additional_button--login js-popup-button" data-js-popup-button="account">{{ 'layout.header.account' | t: name: customer.first_name }}</a> {{ 'layout.header.register_and_account_separator' | t }} <a href="{{ routes.account_logout_url }}" class="header__line-additional_button header__line-additional_button--login js-popup-button" data-js-popup-button="account">{{ 'layout.header.sign_out' | t }}</a>
                                                {%- else -%}
                                                    <a href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}" class="header__line-additional_button header__line-additional_button--register js-popup-button" data-js-trigger="popup-button-sign-up" data-js-popup-button="account">{{ 'layout.header.register' | t }}</a> {{ 'layout.header.register_and_account_separator' | t }} <a href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}" class="header__line-additional_button header__line-additional_button--login js-popup-button" data-js-popup-button="account">{{ 'layout.header.login' | t }}</a>
                                                {%- endif -%}
                                            </span>
                                        {%- endif -%}
                                    {% endif %}
                                    {%- if settings.services_show_header_button -%}
                                        <a href="#" class="header__line-additional_button header__line-additional_button--services position-relative ml-25 ml-lg-0{% if settings.app_language != 'none' or settings.show_multiple_currencies or shop.enabled_currencies.size > 1 %} mr-lg-15{% endif %} js-popup-button" data-js-popup-button="services">{{ 'layout.header.services' | t }}</a>
                                    {%- endif -%}
                                </div>
                            {%- endif -%}
                            {%- if settings.app_language != 'none' or settings.show_multiple_currencies or shop.enabled_currencies.size > 1 -%}
                                {%- if settings.services_show_header_button or settings.account_show_header_button and shop.customer_accounts_enabled -%}
                                    <div class="header__line-additional_separator border-left"></div>
                                {%- endif -%}
                                <div class="d-lg-flex align-items-lg-center">
                                    {{ currencies_html }}
                                    {{ languages_html }}
                                </div>
                            {%- endif -%}
                        </div>
                    </div>
                </div>
                <div data-js-mobile-sticky>
                    <div class="header__line-top header__line--colorize-1 position-relative d-flex px-10 px-lg-0 py-lg-2">
                        <div class="container d-flex align-items-center position-lg-relative">
                            {{ btn_menu_html }}
                            {{ logo_html }}
                            <div class="header__search-form search-ajax d-none d-lg-block position-lg-absolute" data-js-max-products="5">
                                {%- if settings.search_predictive_enabled -%}
                                    <predictive-search class="search-modal__form" data-loading-text="{{ 'accessibility.loading' | t }}">
                                {%- else -%}
                                    <search-form class="search-modal__form">
                                {%- endif -%}
                                    <form action="{{ routes.search_url }}" method="get" role="search" class="mb-0">
                                        <label for="Search" class="label-hidden">{{ 'general.search.placeholder' | t }}</label>
                                        <div class="d-flex align-items-lg-center">
                                            <label class="d-flex align-items-center w-100 position-relative mr-15 mb-0">
                                                <input type="search" class="pl-35 pr-15 mb-0" name="q" value="{{ search.terms | escape }}" placeholder="{{ 'general.search.placeholder' | t }}">
                                                <input type="hidden" name="options[prefix]" value="last"/>
                                                <button type="reset" class="d-none" aria-label="{{ 'general.search.reset' | t }}">{{ 'general.search.reset' | t }}</button>
                                                <i class="position-absolute left-0 ml-10">{% render 'icon-theme-168' %}</i>
                                                {%- if settings.search_predictive_enabled -%}
                                                    <div class="search-ajax__content position-absolute flex-column w-100 left-0 right-0 top-100 px-20 overflow-hidden">
                                                        <div class="search-ajax__result py-20 d-none-important" data-predictive-search></div>
                                                        <div class="search-ajax__view-all mt-10 pb-15 d-none-important">
                                                            <a href="{{ routes.search_url }}" class="btn-link">{{ 'general.popups.search.view_all_products' | t }}</a>
                                                        </div>
                                                        <p class="search-ajax__empty py-20 mb-0 text-center d-none-important">{{ 'general.search.no_results_html' | t }}</p>
                                                    </div>
                                                    <span class="predictive-search-status d-none" role="status" aria-hidden="true"></span>
                                                {%- endif -%}
                                            </label>
                                            <button type="submit" class="btn px-20">{{ 'general.search.submit' | t }}</button>
                                        </div>
                                    </form>
                                {%- if settings.search_predictive_enabled -%}
                                    </predictive-search>
                                {%- else -%}
                                    </search-form>
                                {%- endif -%}
                            </div>
                            <div class="header__sidebar d-flex align-items-center ml-auto">
                                {{ services_html }}
                                {{ btn_account_html }}
                                {{ btn_wishlist_html }}
                                {{ btn_compare_html }}
                                {{ btn_cart_html }}
                            </div>
                        </div>
                    </div>
                </div>
                <div{% if section.settings.desktop_sticky_type == 'slim' %} data-js-desktop-sticky{% endif %}>
                    <div class="header__line-bottom header__line--colorize-2 position-relative d-lg-flex py-lg-6">
                        {{ sticky_logo_html }}
                        <div class="container d-none d-lg-block">
                            <div class="row h-100">
                                {%- if section.settings.vertical_menu != blank -%}
                                    <div class="col-lg-3">
                                        <div class="header__vertical-nav d-lg-flex">
                                            <div class="position-lg-relative d-lg-flex w-100" data-js-position-desktop="vertical-menu">
                                                {% include 'header-get-vertical-menu' %}
                                            </div>
                                        </div>
                                    </div>
                                {%- endif -%}
                                <div class="{% if section.settings.vertical_menu != blank %}col-lg-9{% else %}col-12{% endif %} position-lg-static" data-js-megamenu-width>
                                    {{ nav_html }}
                                </div>
                            </div>
                            {{ btn_search_html }}
                        </div>
                        {{ sticky_sidebar_html }}
                    </div>
                </div>
            </div>
        {%- when '6' -%}
            <div class="header__content"{% if section.settings.desktop_sticky_type == 'wide' %} data-js-desktop-sticky{% endif %}>
                {{ info_line_html }}
                <div class="header__line-additional position-relative {% if section.settings.additional_line_info_page_content != blank %}d-flex px-10{% else %}d-none d-lg-flex{% endif %} px-lg-0">
                    <div class="container d-lg-flex align-items-lg-center">
                        <div>
                            {% include 'parse-page-html-content' with default_page: 'include-header-info-line-4' page_content: section.settings.additional_line_info_page_content %}
                        </div>
                        <div class="header__line-additional_sidebar d-none d-lg-flex align-items-center ml-auto">
                            {%- if settings.services_show_header_button or settings.account_show_header_button and shop.customer_accounts_enabled -%}
                                <div class="d-lg-flex align-items-lg-center">
                                    {%- if settings.app_growave_enable == true and settings.account_growave_social_login_enable == true -%}
                                        {%- if shop.customer_accounts_enabled -%}
                                            <div id="ssw-topauth" class="ssw-topauth ml-25 ml-lg-0{% if settings.app_language != 'none' or settings.show_multiple_currencies or shop.enabled_currencies.size > 1 %} mr-lg-15{% endif %}">
                                                {%- if customer -%}
                                                    <div class="ssw-tprofile ssw-dropdown">
                                                        <a class="ssw-dropdown-toggle header__line-additional_button header__line-additional_button--login" data-toggle="ssw-dropdown" href="javascript:void(0);">
                                                            <i class="ssw-icon-user"></i>
                                                            {{ customer.first_name }}
                                                            {%- if customer.last_name != 'Unknown' -%}
                                                                {{ customer.last_name | slice: 0 | upcase }}.
                                                            {%- endif -%}
                                                            <i class="ssw-icon-down-open-big"></i>
                                                        </a>
                                                        <ul class="ssw-dropdown-menu" role="menu" aria-labelledby="dLabel">
                                                            <li id="customer_myorders_li"><a tabindex="-1" href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}">{{ 'socialshopwave.my_orders' | t }}</a></li>
                                                            {% render 'ssw-widget-dropdown' %}
                                                            <li class="ssw-divider"></li>
                                                            <li><a id="customer_logout_link" tabindex="-1" href="{{ routes.account_logout_url }}">{{ 'socialshopwave.logout' | t }}</a></li>
                                                        </ul>
                                                    </div>
                                                {%- else -%}
                                                    <a id="customer_login_link" class="header__line-additional_button header__line-additional_button--register" href="javascript:void(0);" data-toggle="ssw-modal" data-target="#login_modal" onclick="trackShopStats('login_popup_view', 'all')">{{ 'socialshopwave.log_in' | t }}</a>
                                                    <span class="ssw-login-divider">/</span>
                                                    <a id="customer_register_link" class="header__line-additional_button header__line-additional_button--register" data-toggle="ssw-modal" data-target="#signup_modal" href="javascript:void(0);" onclick="trackShopStats('login_popup_view', 'all')">{{ 'socialshopwave.sign_up' | t }}</a>
                                                {%- endif -%}
                                            </div>
                                        {%- endif -%}
                                    {%- else -%}
                                        {%- if settings.account_show_header_button and shop.customer_accounts_enabled -%}
                                            <span class="ml-25 ml-lg-0 mr-lg-15">
                                                    {%- if customer -%}
                                                        <a href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}" class="header__line-additional_button header__line-additional_button--login js-popup-button" data-js-popup-button="account">{{ 'layout.header.account' | t: name: customer.first_name }}</a> {{ 'layout.header.register_and_account_separator' | t }} <a href="{{ routes.account_logout_url }}" class="header__line-additional_button header__line-additional_button--login js-popup-button" data-js-popup-button="account">{{ 'layout.header.sign_out' | t }}</a>
                                                    {%- else -%}
                                                        <a href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}" class="header__line-additional_button header__line-additional_button--register js-popup-button" data-js-trigger="popup-button-sign-up" data-js-popup-button="account">{{ 'layout.header.register' | t }}</a> {{ 'layout.header.register_and_account_separator' | t }} <a href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}" class="header__line-additional_button header__line-additional_button--login js-popup-button" data-js-popup-button="account">{{ 'layout.header.login' | t }}</a>
                                                    {%- endif -%}
                                                </span>
                                        {%- endif -%}
                                    {% endif %}
                                    {%- if settings.services_show_header_button -%}
                                        <a href="#" class="header__line-additional_button header__line-additional_button--services position-relative ml-25 ml-lg-0{% if settings.app_language != 'none' or settings.show_multiple_currencies or shop.enabled_currencies.size > 1 %} mr-lg-15{% endif %} js-popup-button" data-js-popup-button="services">{{ 'layout.header.services' | t }}</a>
                                    {%- endif -%}
                                </div>
                            {%- endif -%}
                            {%- if settings.app_language != 'none' or settings.show_multiple_currencies or shop.enabled_currencies.size > 1 -%}
                                {%- if settings.services_show_header_button or settings.account_show_header_button and shop.customer_accounts_enabled -%}
                                    <div class="header__line-additional_separator border-left"></div>
                                {%- endif -%}
                                <div class="d-lg-flex align-items-lg-center">
                                    {{ currencies_html }}
                                    {{ languages_html }}
                                </div>
                            {%- endif -%}
                        </div>
                    </div>
                </div>
                <div data-js-mobile-sticky>
                    <div class="header__line-top header__line--colorize-1 position-relative d-flex px-10 px-lg-0 py-lg-2">
                        <div class="container d-flex align-items-center position-lg-relative">
                            {{ btn_menu_html }}
                            {{ logo_html }}
                            <div class="header__search-form search-ajax d-none d-lg-block position-lg-absolute" data-js-max-products="5">
                                {%- if settings.search_predictive_enabled -%}
                                    <predictive-search class="search-modal__form" data-loading-text="{{ 'accessibility.loading' | t }}">
                                {%- else -%}
                                    <search-form class="search-modal__form">
                                {%- endif -%}
                                    <form action="{{ routes.search_url }}" method="get" role="search" class="mb-0">
                                        <label for="Search" class="label-hidden">{{ 'general.search.placeholder' | t }}</label>
                                        <div class="d-flex align-items-lg-center">
                                            <label class="d-flex align-items-center w-100 position-relative mr-15 mb-0">
                                                <input type="search" class="pl-35 pr-15 mb-0" name="q" value="{{ search.terms | escape }}" placeholder="{{ 'general.search.placeholder' | t }}">
                                                <input type="hidden" name="options[prefix]" value="last"/>
                                                <button type="reset" class="d-none" aria-label="{{ 'general.search.reset' | t }}">{{ 'general.search.reset' | t }}</button>
                                                <i class="position-absolute left-0 ml-10">{% render 'icon-theme-168' %}</i>
                                                {%- if settings.search_predictive_enabled -%}
                                                    <div class="search-ajax__content position-absolute flex-column w-100 left-0 right-0 top-100 px-20 overflow-hidden">
                                                        <div class="search-ajax__result py-20 d-none-important" data-predictive-search></div>
                                                        <div class="search-ajax__view-all mt-10 pb-15 d-none-important">
                                                            <a href="{{ routes.search_url }}" class="btn-link">{{ 'general.popups.search.view_all_products' | t }}</a>
                                                        </div>
                                                        <p class="search-ajax__empty py-20 mb-0 text-center d-none-important">{{ 'general.search.no_results_html' | t }}</p>
                                                    </div>
                                                    <span class="predictive-search-status d-none" role="status" aria-hidden="true"></span>
                                                {%- endif -%}
                                            </label>
                                            <button type="submit" class="btn px-20">{{ 'general.search.submit' | t }}</button>
                                        </div>
                                    </form>
                                {%- if settings.search_predictive_enabled -%}
                                    </predictive-search>
                                {%- else -%}
                                    </search-form>
                                {%- endif -%}
                            </div>
                            <div class="header__sidebar d-flex align-items-center ml-auto">
                                {{ services_html }}
                                {{ btn_account_html }}
                                {{ btn_wishlist_html }}
                                {{ btn_compare_html }}
                                {{ btn_cart_html }}
                            </div>
                        </div>
                    </div>
                </div>
                <div{% if section.settings.desktop_sticky_type == 'slim' %} data-js-desktop-sticky{% endif %}>
                    <div class="header__line-bottom header__line--colorize-2 position-relative d-lg-flex py-lg-6">
                        {{ sticky_logo_html }}
                        <div class="container d-lg-flex justify-content-lg-center">
                            {{ nav_html }}
                        </div>
                        {{ sticky_sidebar_html }}
                    </div>
                </div>
            </div>
        {%- when '7' -%}
            <div class="header__content"{% if section.settings.desktop_sticky_type == 'wide' %} data-js-desktop-sticky{% endif %}>
                {{ info_line_html }}
                <div class="header__line-additional position-relative {% if section.settings.additional_line_info_page_content != blank %}d-flex px-10{% else %}d-none d-lg-flex{% endif %} px-lg-0">
                    <div class="container d-lg-flex align-items-lg-center">
                        <div>
                            {% include 'parse-page-html-content' with default_page: 'include-header-info-line-4' page_content: section.settings.additional_line_info_page_content %}
                        </div>
                        <div class="header__line-additional_sidebar d-none d-lg-flex align-items-center ml-auto">
                            {%- if settings.services_show_header_button or settings.account_show_header_button and shop.customer_accounts_enabled -%}
                                <div class="d-lg-flex align-items-lg-center">
                                    {%- if settings.app_growave_enable == true and settings.account_growave_social_login_enable == true -%}
                                        {%- if shop.customer_accounts_enabled -%}
                                            <div id="ssw-topauth" class="ssw-topauth ml-25 ml-lg-0{% if settings.app_language != 'none' or settings.show_multiple_currencies or shop.enabled_currencies.size > 1 %} mr-lg-15{% endif %}">
                                                {%- if customer -%}
                                                    <div class="ssw-tprofile ssw-dropdown">
                                                        <a class="ssw-dropdown-toggle header__line-additional_button header__line-additional_button--login" data-toggle="ssw-dropdown" href="javascript:void(0);">
                                                            <i class="ssw-icon-user"></i>
                                                            {{ customer.first_name }}
                                                            {%- if customer.last_name != 'Unknown' -%}
                                                                {{ customer.last_name | slice: 0 | upcase }}.
                                                            {%- endif -%}
                                                            <i class="ssw-icon-down-open-big"></i>
                                                        </a>
                                                        <ul class="ssw-dropdown-menu" role="menu" aria-labelledby="dLabel">
                                                            <li id="customer_myorders_li"><a tabindex="-1" href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}">{{ 'socialshopwave.my_orders' | t }}</a></li>
                                                            {% render 'ssw-widget-dropdown' %}
                                                            <li class="ssw-divider"></li>
                                                            <li><a id="customer_logout_link" tabindex="-1" href="{{ routes.account_logout_url }}">{{ 'socialshopwave.logout' | t }}</a></li>
                                                        </ul>
                                                    </div>
                                                {%- else -%}
                                                    <a id="customer_login_link" class="header__line-additional_button header__line-additional_button--register" href="javascript:void(0);" data-toggle="ssw-modal" data-target="#login_modal" onclick="trackShopStats('login_popup_view', 'all')">{{ 'socialshopwave.log_in' | t }}</a>
                                                    <span class="ssw-login-divider">/</span>
                                                    <a id="customer_register_link" class="header__line-additional_button header__line-additional_button--register" data-toggle="ssw-modal" data-target="#signup_modal" href="javascript:void(0);" onclick="trackShopStats('login_popup_view', 'all')">{{ 'socialshopwave.sign_up' | t }}</a>
                                                {%- endif -%}
                                            </div>
                                        {%- endif -%}
                                    {%- else -%}
                                        {%- if settings.account_show_header_button and shop.customer_accounts_enabled -%}
                                            <span class="ml-25 ml-lg-0 mr-lg-15">
                                                    {%- if customer -%}
                                                        <a href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}" class="header__line-additional_button header__line-additional_button--login js-popup-button" data-js-popup-button="account">{{ 'layout.header.account' | t: name: customer.first_name }}</a> {{ 'layout.header.register_and_account_separator' | t }} <a href="{{ routes.account_logout_url }}" class="header__line-additional_button header__line-additional_button--login js-popup-button" data-js-popup-button="account">{{ 'layout.header.sign_out' | t }}</a>
                                                    {%- else -%}
                                                        <a href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}" class="header__line-additional_button header__line-additional_button--register js-popup-button" data-js-trigger="popup-button-sign-up" data-js-popup-button="account">{{ 'layout.header.register' | t }}</a> {{ 'layout.header.register_and_account_separator' | t }} <a href="/{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}" class="header__line-additional_button header__line-additional_button--login js-popup-button" data-js-popup-button="account">{{ 'layout.header.login' | t }}</a>
                                                    {%- endif -%}
                                                </span>
                                        {%- endif -%}
                                    {% endif %}
                                    {%- if settings.services_show_header_button -%}
                                        <a href="#" class="header__line-additional_button header__line-additional_button--services position-relative ml-25 ml-lg-0{% if settings.app_language != 'none' or settings.show_multiple_currencies or shop.enabled_currencies.size > 1 %} mr-lg-15{% endif %} js-popup-button" data-js-popup-button="services">{{ 'layout.header.services' | t }}</a>
                                    {%- endif -%}
                                </div>
                            {%- endif -%}
                            {%- if settings.app_language != 'none' or settings.show_multiple_currencies or shop.enabled_currencies.size > 1 -%}
                                {%- if settings.services_show_header_button or settings.account_show_header_button and shop.customer_accounts_enabled -%}
                                    <div class="header__line-additional_separator border-left"></div>
                                {%- endif -%}
                                <div class="d-lg-flex align-items-lg-center">
                                    {{ currencies_html }}
                                    {{ languages_html }}
                                </div>
                            {%- endif -%}
                        </div>
                    </div>
                </div>
                <div data-js-mobile-sticky>
                    <div class="header__line-top header__line--colorize-1 position-relative d-flex px-10 px-lg-0 py-lg-2">
                        <div class="container d-flex align-items-center position-lg-relative">
                            {{ btn_menu_html }}
                            {{ logo_html }}
                            <div class="d-flex ml-auto">
                                <div class="header__search-form search-ajax d-none d-lg-block" data-js-max-products="5">
                                    {%- if settings.search_predictive_enabled -%}
                                        <predictive-search class="search-modal__form" data-loading-text="{{ 'accessibility.loading' | t }}">
                                    {%- else -%}
                                        <search-form class="search-modal__form">
                                    {%- endif -%}
                                        <form action="{{ routes.search_url }}" method="get" role="search" class="mb-0">
                                            <label for="Search" class="label-hidden">{{ 'general.search.placeholder' | t }}</label>
                                            <div class="d-flex align-items-lg-center">
                                                <label class="d-flex align-items-center w-100 position-relative mr-15 mb-0">
                                                    <input type="search" class="pl-35 pr-15 mb-0" name="q" value="{{ search.terms | escape }}" placeholder="{{ 'general.search.placeholder' | t }}">
                                                    <input type="hidden" name="options[prefix]" value="last"/>
                                                    <button type="reset" class="d-none" aria-label="{{ 'general.search.reset' | t }}">{{ 'general.search.reset' | t }}</button>
                                                    <i class="position-absolute left-0 ml-10">{% render 'icon-theme-168' %}</i>
                                                    {%- if settings.search_predictive_enabled -%}
                                                        <div class="search-ajax__content position-absolute flex-column w-100 left-0 right-0 top-100 px-20 overflow-hidden">
                                                            <div class="search-ajax__result py-20 d-none-important" data-predictive-search></div>
                                                            <div class="search-ajax__view-all mt-10 pb-15 d-none-important">
                                                                <a href="{{ routes.search_url }}" class="btn-link">{{ 'general.popups.search.view_all_products' | t }}</a>
                                                            </div>
                                                            <p class="search-ajax__empty py-20 mb-0 text-center d-none-important">{{ 'general.search.no_results_html' | t }}</p>
                                                        </div>
                                                        <span class="predictive-search-status d-none" role="status" aria-hidden="true"></span>
                                                    {%- endif -%}
                                                </label>
                                                <button type="submit" class="btn px-20">{{ 'general.search.submit' | t }}</button>
                                            </div>
                                        </form>
                                    {%- if settings.search_predictive_enabled -%}
                                        </predictive-search>
                                    {%- else -%}
                                        </search-form>
                                    {%- endif -%}
                                </div>
                                <div class="header__sidebar d-flex align-items-center ml-lg-20">
                                    {{ services_html }}
                                    {{ btn_account_html }}
                                    {{ btn_wishlist_html }}
                                    {{ btn_compare_html }}
                                    {{ btn_cart_html }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div{% if section.settings.desktop_sticky_type == 'slim' %} data-js-desktop-sticky{% endif %}>
                    <div class="header__line-bottom header__line--colorize-2 position-relative d-lg-flex py-lg-6">
                        {{ sticky_logo_html }}
                        <div class="container d-none d-lg-block">
                            <div class="row h-100">
                                {%- if section.settings.vertical_menu != blank -%}
                                    <div class="col-lg-3">
                                        <div class="header__vertical-nav d-lg-flex">
                                            <div class="position-lg-relative d-lg-flex w-100" data-js-position-desktop="vertical-menu">
                                                {% include 'header-get-vertical-menu' %}
                                            </div>
                                        </div>
                                    </div>
                                {%- endif -%}
                                <div class="{% if section.settings.vertical_menu != blank %}col-lg-9{% else %}col-12{% endif %} position-lg-static" data-js-megamenu-width>
                                    {{ nav_html }}
                                </div>
                            </div>
                            {{ btn_search_html }}
                        </div>
                        {{ sticky_sidebar_html }}
                    </div>
                </div>
            </div>
        {%- endcase -%}
        {%- if section.settings.sticky != 'disable' -%}
            </sticky-header>
        {%- endif -%}
    </header>
</header-section>
<script>
    document.getElementsByTagName('header')[0].classList.add('header--visible');
    theme.AssetsLoader.{% if section.settings.type == '4' or section.settings.vertical_menu != blank and section.settings.fixed_vertical_menu %}onPageLoaded{% else %}onUserAction{% endif %}(function() {
        theme.AssetsLoader.require('scripts', 'header');
    });
    {%- if section.settings.sticky != 'disable' -%}
        theme.AssetsLoader.onUserAction(function() {
            theme.AssetsLoader.require('scripts', 'sticky_header');
        });
    {%- endif -%}
</script>

<script type="application/ld+json">
{
  "@context": "http://schema.org",
  "@type": "Organization",
  "name": {{ shop.name | json }},
  {% if section.settings.logo %}
    {% assign image_size = section.settings.logo.width | append:'x' %}
    "logo": {{ section.settings.logo | img_url: image_size | prepend: "https:" | json }},
  {% endif %}
  "sameAs": [
    {{ settings.social_twitter | json }},
    {{ settings.social_facebook | json }},
    {{ settings.social_pinterest | json }},
    {{ settings.social_instagram | json }},
    {{ settings.social_youtube | json }}
  ],
  "url": {{ shop.url | append: page.url | json }}
}
</script>

{% if request.page_type == 'index' %}
    {% assign potential_action_target = shop.url | append: routes.search_url | append: "?q={search_term_string}" %}
    <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": {{ shop.name | json }},
      "potentialAction": {
        "@type": "SearchAction",
        "target": {{ potential_action_target | json }},
        "query-input": "required name=search_term_string"
      },
      "url": {{ shop.url | append: page.url | json }}
    }
  </script>
{% endif %}


{% schema %}
{
    "name": "t:sections.header.name",
    "settings": [
        {
            "type": "select",
            "id": "type",
            "label": "t:sections.header.settings.type.label",
            "default": "1",
            "options": [
                {
                    "value": "1",
                    "label": "t:sections.header.settings.type.option__1.label"
                },
                {
                    "value": "2",
                    "label": "t:sections.header.settings.type.option__2.label"
                },
                {
                    "value": "3",
                    "label": "t:sections.header.settings.type.option__3.label"
                },
                {
                    "value": "4",
                    "label": "t:sections.header.settings.type.option__4.label"
                },
                {
                    "value": "5",
                    "label": "t:sections.header.settings.type.option__5.label"
                },
                {
                    "value": "6",
                    "label": "t:sections.header.settings.type.option__6.label"
                },
                {
                    "value": "7",
                    "label": "t:sections.header.settings.type.option__7.label"
                }
            ]
        },
        {
            "type": "select",
            "id": "style",
            "label": "t:sections.header.settings.style.label",
            "default": "1",
            "info": "t:sections.header.settings.style.info",
            "options": [
                {
                    "value": "1",
                    "label": "t:sections.header.settings.style.option__1.label"
                },
                {
                    "value": "2",
                    "label": "t:sections.header.settings.style.option__2.label"
                },
                {
                    "value": "3",
                    "label": "t:sections.header.settings.style.option__3.label"
                },
                {
                    "value": "4",
                    "label": "t:sections.header.settings.style.option__4.label"
                }
            ]
        },
        {
            "type": "select",
            "id": "transparent_bg",
            "label": "t:sections.header.settings.transparent_bg.label",
            "default": "disable",
            "options": [
                {
                    "value": "disable",
                    "label": "t:sections.header.settings.transparent_bg.option__1.label"
                },
                {
                    "value": "transparent",
                    "label": "t:sections.header.settings.transparent_bg.option__2.label"
                },
                {
                    "value": "transparent-hover",
                    "label": "t:sections.header.settings.transparent_bg.option__3.label"
                }
            ]
        },
        {
            "type": "checkbox",
            "id": "logo_mobile_centered",
            "label": "t:sections.header.settings.logo_mobile_centered.label",
            "info": "t:sections.header.settings.logo_mobile_centered.info",
            "default": false
        },
        {
            "type": "range",
            "id": "height",
            "min": 0,
            "max": 200,
            "step": 5,
            "unit": "px",
            "label": "t:sections.header.settings.height.label",
            "info": "t:sections.header.settings.height.info",
            "default": 0
        },
        {
            "type": "range",
            "id": "items_padding",
            "min": 0,
            "max": 30,
            "step": 1,
            "unit": "px",
            "label": "t:sections.header.settings.items_padding.label",
            "info": "t:sections.header.settings.items_padding.info",
            "default": 0
        },
        {
            "type": "header",
            "content": "t:sections.header.settings.header__1.content"
        },
        {
            "type": "link_list",
            "id": "menu",
            "label": "t:sections.header.settings.menu.label",
            "default": "main-menu"
        },
        {
            "type": "checkbox",
            "id": "show_menu_arrows",
            "label": "t:sections.header.settings.show_menu_arrows.label",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_menu_hover_underline",
            "label": "t:sections.header.settings.show_menu_hover_underline.label",
            "default": false
        },
        {
            "type": "header",
            "content": "t:sections.header.settings.header__2.content"
        },
        {
            "type": "paragraph",
            "content": "t:sections.header.settings.paragraph__1.content"
        },
        {
            "type": "link_list",
            "id": "vertical_menu",
            "label": "t:sections.header.settings.vertical_menu.label"
        },
        {
            "type": "checkbox",
            "id": "fixed_vertical_menu",
            "label": "t:sections.header.settings.fixed_vertical_menu.label",
            "info": "t:sections.header.settings.fixed_vertical_menu.info",
            "default": false
        },
        {
            "type": "header",
            "content": "t:sections.header.settings.header__3.content"
        },
        {
            "type": "paragraph",
            "content": "t:sections.header.settings.paragraph__2.content"
        },
        {
            "type": "page",
            "id": "additional_line_info_page_content",
            "label": "t:sections.header.settings.additional_line_info_page_content.label",
            "info": "t:sections.header.settings.additional_line_info_page_content.info"
        },
        {
            "type": "header",
            "content": "t:sections.header.settings.header__4.content",
            "info": "t:sections.header.settings.undefined.info"
        },
        {
            "type": "checkbox",
            "id": "enable_country_selector",
            "default": false,
            "label": "t:sections.header.settings.enable_country_selector.label"
        },
        {
            "type": "checkbox",
            "id": "show_country_name",
            "default": false,
            "label": "t:sections.header.settings.show_country_name.label"
        },
        {
            "type": "range",
            "id": "country_selector_max_width",
            "min": 0,
            "max": 300,
            "step": 10,
            "unit": "px",
            "label": "t:sections.header.settings.country_selector_max_width.label",
            "info": "t:sections.header.settings.country_selector_max_width.info",
            "default": 140
        },
        {
            "type": "header",
            "content": "t:sections.header.settings.header__5.content"
        },
        {
            "type": "select",
            "id": "sticky",
            "label": "t:sections.header.settings.sticky.label",
            "default": "desktop_and_mobile",
            "options": [
                {
                    "value": "disable",
                    "label": "t:sections.header.settings.sticky.option__1.label"
                },
                {
                    "value": "desktop_and_mobile",
                    "label": "t:sections.header.settings.sticky.option__2.label"
                },
                {
                    "value": "desktop",
                    "label": "t:sections.header.settings.sticky.option__3.label"
                },
                {
                    "value": "mobile",
                    "label": "t:sections.header.settings.sticky.option__4.label"
                }
            ]
        },
        {
            "type": "select",
            "id": "desktop_sticky_type",
            "label": "t:sections.header.settings.desktop_sticky_type.label",
            "default": "slim",
            "options": [
                {
                    "value": "wide",
                    "label": "t:sections.header.settings.desktop_sticky_type.option__1.label"
                },
                {
                    "value": "slim",
                    "label": "t:sections.header.settings.desktop_sticky_type.option__2.label"
                }
            ]
        },
        {
            "type": "checkbox",
            "id": "hide_sticky_when_scroll_down",
            "label": "t:sections.header.settings.hide_sticky_when_scroll_down.label",
            "default": true
        },
        {
            "type": "header",
            "content": "t:sections.header.settings.header__6.content"
        },
        {
            "type": "paragraph",
            "content": "t:sections.header.settings.paragraph__3.content"
        },
        {
            "type": "paragraph",
            "content": "t:sections.header.settings.paragraph__4.content"
        }
    ],
    "blocks": [
        {
            "type": "megamenu_title_image",
            "name": "t:sections.header.blocks.megamenu_title_image.name",
            "settings": [
                {
                    "type": "paragraph",
                    "content": "t:sections.header.blocks.megamenu_title_image.settings.paragraph__1.content"
                },
                {
                    "type": "text",
                    "id": "for_item",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.for_item.label",
                    "info": "t:sections.header.blocks.megamenu_title_image.settings.for_item.info"
                },
                {
                    "type": "image_picker",
                    "id": "image",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.image.label"
                },
                {
                    "type": "range",
                    "id": "image_size",
                    "min": 100,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.image_size.label",
                    "info": "t:sections.header.blocks.megamenu_title_image.settings.image_size.info",
                    "default": 200
                },
                {
                    "type": "text",
                    "id": "for_item_2",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.for_item_2.label"
                },
                {
                    "type": "image_picker",
                    "id": "image_2",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.image_2.label"
                },
                {
                    "type": "range",
                    "id": "image_size_2",
                    "min": 100,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.image_size_2.label",
                    "default": 200
                },
                {
                    "type": "text",
                    "id": "for_item_3",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.for_item_3.label"
                },
                {
                    "type": "image_picker",
                    "id": "image_3",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.image_3.label"
                },
                {
                    "type": "range",
                    "id": "image_size_3",
                    "min": 100,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.image_size_3.label",
                    "default": 200
                },
                {
                    "type": "text",
                    "id": "for_item_4",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.for_item_4.label"
                },
                {
                    "type": "image_picker",
                    "id": "image_4",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.image_4.label"
                },
                {
                    "type": "range",
                    "id": "image_size_4",
                    "min": 100,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.image_size_4.label",
                    "default": 200
                },
                {
                    "type": "text",
                    "id": "for_item_5",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.for_item_5.label"
                },
                {
                    "type": "image_picker",
                    "id": "image_5",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.image_5.label"
                },
                {
                    "type": "range",
                    "id": "image_size_5",
                    "min": 100,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_title_image.settings.image_size_5.label",
                    "default": 200
                }
            ]
        },
        {
            "type": "megamenu_label",
            "name": "t:sections.header.blocks.megamenu_label.name",
            "settings": [
                {
                    "type": "select",
                    "id": "type",
                    "label": "t:sections.header.blocks.megamenu_label.settings.type.label",
                    "default": "hot",
                    "options": [
                        {
                            "value": "hot",
                            "label": "t:sections.header.blocks.megamenu_label.settings.type.option__1.label"
                        },
                        {
                            "value": "sale",
                            "label": "t:sections.header.blocks.megamenu_label.settings.type.option__2.label"
                        },
                        {
                            "value": "new",
                            "label": "t:sections.header.blocks.megamenu_label.settings.type.option__3.label"
                        }
                    ]
                },
                {
                    "type": "text",
                    "id": "text",
                    "label": "t:sections.header.blocks.megamenu_label.settings.text.label"
                },
                {
                    "type": "text",
                    "id": "for_item",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item.label",
                    "info": "t:sections.header.blocks.megamenu_label.settings.for_item.info"
                },
                {
                    "type": "text",
                    "id": "for_item_2",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item_2.label"
                },
                {
                    "type": "text",
                    "id": "for_item_3",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item_3.label"
                },
                {
                    "type": "text",
                    "id": "for_item_4",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item_4.label"
                },
                {
                    "type": "text",
                    "id": "for_item_5",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item_5.label"
                },
                {
                    "type": "text",
                    "id": "for_item_6",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item_6.label"
                },
                {
                    "type": "text",
                    "id": "for_item_7",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item_7.label"
                },
                {
                    "type": "text",
                    "id": "for_item_8",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item_8.label"
                },
                {
                    "type": "text",
                    "id": "for_item_9",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item_9.label"
                },
                {
                    "type": "text",
                    "id": "for_item_10",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item_10.label"
                },
                {
                    "type": "text",
                    "id": "for_item_11",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item_11.label"
                },
                {
                    "type": "text",
                    "id": "for_item_12",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item_12.label"
                },
                {
                    "type": "text",
                    "id": "for_item_13",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item_13.label"
                },
                {
                    "type": "text",
                    "id": "for_item_14",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item_14.label"
                },
                {
                    "type": "text",
                    "id": "for_item_15",
                    "label": "t:sections.header.blocks.megamenu_label.settings.for_item_15.label"
                }
            ]
        },
        {
            "type": "icon",
            "name": "t:sections.header.blocks.icon.name",
            "settings": [
                {
                    "type": "paragraph",
                    "content": "t:sections.header.blocks.icon.settings.paragraph__1.content"
                },
                {
                    "type": "checkbox",
                    "id": "for_vertical",
                    "label": "t:sections.header.blocks.icon.settings.for_vertical.label",
                    "default": true
                },
                {
                    "type": "text",
                    "id": "for_item_1",
                    "label": "t:sections.header.blocks.icon.settings.for_item_1.label",
                    "info": "t:sections.header.blocks.icon.settings.for_item_1.info"
                },
                {
                    "type": "text",
                    "id": "icon_1",
                    "label": "t:sections.header.blocks.icon.settings.icon_1.label",
                    "info": "t:sections.header.blocks.icon.settings.icon_1.info"
                },
                {
                    "type": "text",
                    "id": "for_item_2",
                    "label": "t:sections.header.blocks.icon.settings.for_item_2.label"
                },
                {
                    "type": "text",
                    "id": "icon_2",
                    "label": "t:sections.header.blocks.icon.settings.icon_2.label"
                },
                {
                    "type": "text",
                    "id": "for_item_3",
                    "label": "t:sections.header.blocks.icon.settings.for_item_3.label"
                },
                {
                    "type": "text",
                    "id": "icon_3",
                    "label": "t:sections.header.blocks.icon.settings.icon_3.label"
                },
                {
                    "type": "text",
                    "id": "for_item_4",
                    "label": "t:sections.header.blocks.icon.settings.for_item_4.label"
                },
                {
                    "type": "text",
                    "id": "icon_4",
                    "label": "t:sections.header.blocks.icon.settings.icon_4.label"
                },
                {
                    "type": "text",
                    "id": "for_item_5",
                    "label": "t:sections.header.blocks.icon.settings.for_item_5.label"
                },
                {
                    "type": "text",
                    "id": "icon_5",
                    "label": "t:sections.header.blocks.icon.settings.icon_5.label"
                },
                {
                    "type": "text",
                    "id": "for_item_6",
                    "label": "t:sections.header.blocks.icon.settings.for_item_6.label"
                },
                {
                    "type": "text",
                    "id": "icon_6",
                    "label": "t:sections.header.blocks.icon.settings.icon_6.label"
                },
                {
                    "type": "text",
                    "id": "for_item_7",
                    "label": "t:sections.header.blocks.icon.settings.for_item_7.label"
                },
                {
                    "type": "text",
                    "id": "icon_7",
                    "label": "t:sections.header.blocks.icon.settings.icon_7.label"
                },
                {
                    "type": "text",
                    "id": "for_item_8",
                    "label": "t:sections.header.blocks.icon.settings.for_item_8.label"
                },
                {
                    "type": "text",
                    "id": "icon_8",
                    "label": "t:sections.header.blocks.icon.settings.icon_8.label"
                },
                {
                    "type": "text",
                    "id": "for_item_9",
                    "label": "t:sections.header.blocks.icon.settings.for_item_9.label"
                },
                {
                    "type": "text",
                    "id": "icon_9",
                    "label": "t:sections.header.blocks.icon.settings.icon_9.label"
                },
                {
                    "type": "text",
                    "id": "for_item_10",
                    "label": "t:sections.header.blocks.icon.settings.for_item_10.label"
                },
                {
                    "type": "text",
                    "id": "icon_10",
                    "label": "t:sections.header.blocks.icon.settings.icon_10.label"
                },
                {
                    "type": "text",
                    "id": "for_item_11",
                    "label": "t:sections.header.blocks.icon.settings.for_item_11.label"
                },
                {
                    "type": "text",
                    "id": "icon_11",
                    "label": "t:sections.header.blocks.icon.settings.icon_11.label"
                },
                {
                    "type": "text",
                    "id": "for_item_12",
                    "label": "t:sections.header.blocks.icon.settings.for_item_12.label"
                },
                {
                    "type": "text",
                    "id": "icon_12",
                    "label": "t:sections.header.blocks.icon.settings.icon_12.label"
                },
                {
                    "type": "text",
                    "id": "for_item_13",
                    "label": "t:sections.header.blocks.icon.settings.for_item_13.label"
                },
                {
                    "type": "text",
                    "id": "icon_13",
                    "label": "t:sections.header.blocks.icon.settings.icon_13.label"
                },
                {
                    "type": "text",
                    "id": "for_item_14",
                    "label": "t:sections.header.blocks.icon.settings.for_item_14.label"
                },
                {
                    "type": "text",
                    "id": "icon_14",
                    "label": "t:sections.header.blocks.icon.settings.icon_14.label"
                },
                {
                    "type": "text",
                    "id": "for_item_15",
                    "label": "t:sections.header.blocks.icon.settings.for_item_15.label"
                },
                {
                    "type": "text",
                    "id": "icon_15",
                    "label": "t:sections.header.blocks.icon.settings.icon_15.label"
                },
                {
                    "type": "text",
                    "id": "for_item_16",
                    "label": "t:sections.header.blocks.icon.settings.for_item_16.label"
                },
                {
                    "type": "text",
                    "id": "icon_16",
                    "label": "t:sections.header.blocks.icon.settings.icon_16.label"
                },
                {
                    "type": "text",
                    "id": "for_item_17",
                    "label": "t:sections.header.blocks.icon.settings.for_item_17.label"
                },
                {
                    "type": "text",
                    "id": "icon_17",
                    "label": "t:sections.header.blocks.icon.settings.icon_17.label"
                },
                {
                    "type": "text",
                    "id": "for_item_18",
                    "label": "t:sections.header.blocks.icon.settings.for_item_18.label"
                },
                {
                    "type": "text",
                    "id": "icon_18",
                    "label": "t:sections.header.blocks.icon.settings.icon_18.label"
                },
                {
                    "type": "text",
                    "id": "for_item_19",
                    "label": "t:sections.header.blocks.icon.settings.for_item_19.label"
                },
                {
                    "type": "text",
                    "id": "icon_19",
                    "label": "t:sections.header.blocks.icon.settings.icon_19.label"
                },
                {
                    "type": "text",
                    "id": "for_item_20",
                    "label": "t:sections.header.blocks.icon.settings.for_item_20.label"
                },
                {
                    "type": "text",
                    "id": "icon_20",
                    "label": "t:sections.header.blocks.icon.settings.icon_20.label"
                }
            ]
        },
        {
            "type": "info_line",
            "name": "t:sections.header.blocks.info_line.name",
            "limit": 1,
            "settings": [
                {
                    "type": "checkbox",
                    "id": "show_on_mobile",
                    "label": "t:sections.header.blocks.info_line.settings.show_on_mobile.label",
                    "default": false
                },
                {
                    "type": "select",
                    "id": "style",
                    "label": "t:sections.header.blocks.info_line.settings.style.label",
                    "default": "1",
                    "options": [
                        {
                            "value": "1",
                            "label": "t:sections.header.blocks.info_line.settings.style.option__1.label"
                        },
                        {
                            "value": "2",
                            "label": "t:sections.header.blocks.info_line.settings.style.option__2.label"
                        }
                    ]
                },
                {
                    "type": "checkbox",
                    "id": "transparent_bg",
                    "label": "t:sections.header.blocks.info_line.settings.transparent_bg.label",
                    "default": false
                },
                {
                    "type": "page",
                    "id": "page_content",
                    "label": "t:sections.header.blocks.info_line.settings.page_content.label",
                    "info": "t:sections.header.blocks.info_line.settings.page_content.info"
                },
                {
                    "type": "checkbox",
                    "id": "show_social_media",
                    "label": "t:sections.header.blocks.info_line.settings.show_social_media.label",
                    "default": true
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.info_line.settings.header__1.content"
                },
                {
                    "type": "checkbox",
                    "id": "show_facebook",
                    "label": "t:sections.header.blocks.info_line.settings.show_facebook.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_twitter",
                    "label": "t:sections.header.blocks.info_line.settings.show_twitter.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_instagram",
                    "label": "t:sections.header.blocks.info_line.settings.show_instagram.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_pinterest",
                    "label": "t:sections.header.blocks.info_line.settings.show_pinterest.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_youtube",
                    "label": "t:sections.header.blocks.info_line.settings.show_youtube.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_behance",
                    "label": "t:sections.header.blocks.info_line.settings.show_behance.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_skype",
                    "label": "t:sections.header.blocks.info_line.settings.show_skype.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_tiktok",
                    "label": "t:sections.header.blocks.info_line.settings.show_tiktok.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_line",
                    "label": "t:sections.header.blocks.info_line.settings.show_line.label"
                }
            ]
        },
        {
            "type": "tape",
            "name": "t:sections.header.blocks.tape.name",
            "limit": 1,
            "settings": [
                {
                    "type": "range",
                    "id": "delay",
                    "min": 0,
                    "max": 10,
                    "step": 1,
                    "unit": "sec",
                    "label": "t:sections.header.blocks.tape.settings.delay.label",
                    "default": 0
                },
                {
                    "type": "select",
                    "id": "style",
                    "label": "t:sections.header.blocks.tape.settings.style.label",
                    "default": "1",
                    "options": [
                        {
                            "value": "1",
                            "label": "t:sections.header.blocks.tape.settings.style.option__1.label"
                        },
                        {
                            "value": "2",
                            "label": "t:sections.header.blocks.tape.settings.style.option__2.label"
                        },
                        {
                            "value": "3",
                            "label": "t:sections.header.blocks.tape.settings.style.option__3.label"
                        }
                    ]
                },
                {
                    "type": "checkbox",
                    "id": "transparent_bg",
                    "label": "t:sections.header.blocks.tape.settings.transparent_bg.label",
                    "default": false
                },
                {
                    "type": "html",
                    "id": "content",
                    "label": "t:sections.header.blocks.tape.settings.content.label",
                    "default": "FINAL CLEARANCE: Take 20% Off ‘Sale Must-Haves'"
                }
            ]
        },
        {
            "type": "megamenu",
            "name": "t:sections.header.blocks.megamenu.name",
            "settings": [
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu.settings.header__1.content"
                },
                {
                    "type": "text",
                    "id": "for_item",
                    "label": "t:sections.header.blocks.megamenu.settings.for_item.label"
                },
                {
                    "type": "range",
                    "id": "grid",
                    "min": 1,
                    "max": 12,
                    "step": 1,
                    "unit": "/12",
                    "label": "t:sections.header.blocks.megamenu.settings.grid.label",
                    "default": 2
                },
                {
                    "type": "checkbox",
                    "id": "second_level_column",
                    "label": "t:sections.header.blocks.megamenu.settings.second_level_column.label",
                    "default": false
                },
                {
                    "type": "range",
                    "id": "second_level_column_size",
                    "min": 1,
                    "max": 50,
                    "step": 1,
                    "label": "t:sections.header.blocks.megamenu.settings.second_level_column_size.label",
                    "default": 20
                },
                {
                    "type": "checkbox",
                    "id": "wrap",
                    "label": "t:sections.header.blocks.megamenu.settings.wrap.label",
                    "default": false
                },
                {
                    "type": "range",
                    "id": "wrapper_grid",
                    "min": 1,
                    "max": 12,
                    "step": 1,
                    "unit": "/12",
                    "label": "t:sections.header.blocks.megamenu.settings.wrapper_grid.label",
                    "default": 12
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu.settings.header__2.content"
                },
                {
                    "type": "select",
                    "id": "column_left_type",
                    "label": "t:sections.header.blocks.megamenu.settings.column_left_type.label",
                    "default": "none",
                    "options": [
                        {
                            "value": "none",
                            "label": "t:sections.header.blocks.megamenu.settings.column_left_type.option__1.label"
                        },
                        {
                            "value": "1",
                            "label": "t:sections.header.blocks.megamenu.settings.column_left_type.option__2.label"
                        },
                        {
                            "value": "1*2",
                            "label": "t:sections.header.blocks.megamenu.settings.column_left_type.option__3.label"
                        },
                        {
                            "value": "2",
                            "label": "t:sections.header.blocks.megamenu.settings.column_left_type.option__4.label"
                        },
                        {
                            "value": "2*2",
                            "label": "t:sections.header.blocks.megamenu.settings.column_left_type.option__5.label"
                        },
                        {
                            "value": "3",
                            "label": "t:sections.header.blocks.megamenu.settings.column_left_type.option__6.label"
                        },
                        {
                            "value": "3*2",
                            "label": "t:sections.header.blocks.megamenu.settings.column_left_type.option__7.label"
                        },
                        {
                            "value": "4",
                            "label": "t:sections.header.blocks.megamenu.settings.column_left_type.option__8.label"
                        },
                        {
                            "value": "4*2",
                            "label": "t:sections.header.blocks.megamenu.settings.column_left_type.option__9.label"
                        },
                        {
                            "value": "8-4",
                            "label": "t:sections.header.blocks.megamenu.settings.column_left_type.option__10.label"
                        }
                    ]
                },
                {
                    "type": "range",
                    "id": "column_left_size",
                    "min": 0,
                    "max": 12,
                    "step": 1,
                    "unit": "/12",
                    "label": "t:sections.header.blocks.megamenu.settings.column_left_size.label",
                    "info": "t:sections.header.blocks.megamenu.settings.column_left_size.info",
                    "default": 0
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu.settings.header__3.content"
                },
                {
                    "type": "select",
                    "id": "column_right_type",
                    "label": "t:sections.header.blocks.megamenu.settings.column_right_type.label",
                    "default": "none",
                    "options": [
                        {
                            "value": "none",
                            "label": "t:sections.header.blocks.megamenu.settings.column_right_type.option__1.label"
                        },
                        {
                            "value": "1",
                            "label": "t:sections.header.blocks.megamenu.settings.column_right_type.option__2.label"
                        },
                        {
                            "value": "1*2",
                            "label": "t:sections.header.blocks.megamenu.settings.column_right_type.option__3.label"
                        },
                        {
                            "value": "2",
                            "label": "t:sections.header.blocks.megamenu.settings.column_right_type.option__4.label"
                        },
                        {
                            "value": "2*2",
                            "label": "t:sections.header.blocks.megamenu.settings.column_right_type.option__5.label"
                        },
                        {
                            "value": "3",
                            "label": "t:sections.header.blocks.megamenu.settings.column_right_type.option__6.label"
                        },
                        {
                            "value": "3*2",
                            "label": "t:sections.header.blocks.megamenu.settings.column_right_type.option__7.label"
                        },
                        {
                            "value": "4",
                            "label": "t:sections.header.blocks.megamenu.settings.column_right_type.option__8.label"
                        },
                        {
                            "value": "4*2",
                            "label": "t:sections.header.blocks.megamenu.settings.column_right_type.option__9.label"
                        },
                        {
                            "value": "8/12-4/12",
                            "label": "t:sections.header.blocks.megamenu.settings.column_right_type.option__10.label"
                        }
                    ]
                },
                {
                    "type": "range",
                    "id": "column_right_size",
                    "min": 0,
                    "max": 12,
                    "step": 1,
                    "unit": "/12",
                    "label": "t:sections.header.blocks.megamenu.settings.column_right_size.label",
                    "info": "t:sections.header.blocks.megamenu.settings.column_right_size.info",
                    "default": 0
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu.settings.header__4.content"
                },
                {
                    "type": "checkbox",
                    "id": "enable_width_limit",
                    "label": "t:sections.header.blocks.megamenu.settings.enable_width_limit.label",
                    "info": "t:sections.header.blocks.megamenu.settings.enable_width_limit.info",
                    "default": false
                },
                {
                    "type": "range",
                    "id": "width_limit",
                    "min": 300,
                    "max": 1000,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu.settings.width_limit.label",
                    "default": 630
                }
            ]
        },
        {
            "type": "megamenu_promobox",
            "name": "t:sections.header.blocks.megamenu_promobox.name",
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.title.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu_promobox.settings.header__1.content"
                },
                {
                    "type": "url",
                    "id": "url",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.url.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu_promobox.settings.header__2.content"
                },
                {
                    "type": "image_picker",
                    "id": "image",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.image.label",
                    "info": "t:sections.header.blocks.megamenu_promobox.settings.image.info"
                },
                {
                    "type": "range",
                    "id": "image_size",
                    "min": 200,
                    "max": 2000,
                    "step": 50,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.image_size.label",
                    "info": "t:sections.header.blocks.megamenu_promobox.settings.image_size.info",
                    "default": 1450
                },
                {
                    "type": "color",
                    "id": "color_image_mask",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.color_image_mask.label"
                },
                {
                    "type": "range",
                    "id": "image_mask_opacity",
                    "min": 0.1,
                    "max": 1,
                    "step": 0.1,
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.image_mask_opacity.label",
                    "default": 0.5
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu_promobox.settings.header__3.content"
                },
                {
                    "type": "paragraph",
                    "content": "t:sections.header.blocks.megamenu_promobox.settings.paragraph__1.content"
                },
                {
                    "type": "paragraph",
                    "content": "t:sections.header.blocks.megamenu_promobox.settings.paragraph__2.content"
                },
                {
                    "type": "text",
                    "id": "text_line_1",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.text_line_1.label",
                    "default": "テキストライン# 1" // "Text line #1"
                },
                {
                    "type": "text",
                    "id": "text_line_2",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.text_line_2.label",
                    "default": "テキストライン# 2" //"Text line #2"
                },
                {
                    "type": "text",
                    "id": "text_line_3",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.text_line_3.label",
                    "default": "テキストライン# 1" // "Text line #3"
                },
                {
                    "type": "select",
                    "id": "style",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.style.label",
                    "default": "1",
                    "options": [
                        {
                            "value": "1",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.style.option__1.label"
                        },
                        {
                            "value": "2",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.style.option__2.label"
                        },
                        {
                            "value": "3",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.style.option__3.label"
                        },
                        {
                            "value": "4",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.style.option__4.label"
                        },
                        {
                            "value": "5",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.style.option__5.label"
                        },
                        {
                            "value": "6",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.style.option__6.label"
                        },
                        {
                            "value": "7",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.style.option__7.label"
                        },
                        {
                            "value": "8",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.style.option__8.label"
                        }
                    ]
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu_promobox.settings.header__4.content"
                },
                {
                    "type": "text",
                    "id": "button_1",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.button_1.label",
                    "default": "ボタンテキスト #1" // "Button #1 text"
                },
                {
                    "type": "url",
                    "id": "button_1_url",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.button_1_url.label"
                },
                {
                    "type": "select",
                    "id": "color_button_type_1",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_1.label",
                    "default": "default",
                    "options": [
                        {
                            "value": "default",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_1.option__1.label"
                        },
                        {
                            "value": "secondary",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_1.option__2.label"
                        },
                        {
                            "value": "invert",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_1.option__3.label"
                        },
                        {
                            "value": "clean",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_1.option__4.label"
                        },
                        {
                            "value": "default-transparent",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_1.option__5.label"
                        },
                        {
                            "value": "secondary-transparent",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_1.option__6.label"
                        },
                        {
                            "value": "invert-transparent",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_1.option__7.label"
                        },
                        {
                            "value": "clean-transparent",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_1.option__8.label"
                        }
                    ]
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu_promobox.settings.header__5.content"
                },
                {
                    "type": "text",
                    "id": "button_2",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.button_2.label",
                    "default": "ボタンテキスト #2" // "Button #2 text"
                },
                {
                    "type": "url",
                    "id": "button_2_url",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.button_2_url.label"
                },
                {
                    "type": "select",
                    "id": "color_button_type_2",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_2.label",
                    "default": "default",
                    "options": [
                        {
                            "value": "default",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_2.option__1.label"
                        },
                        {
                            "value": "secondary",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_2.option__2.label"
                        },
                        {
                            "value": "invert",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_2.option__3.label"
                        },
                        {
                            "value": "clean",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_2.option__4.label"
                        },
                        {
                            "value": "default-transparent",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_2.option__5.label"
                        },
                        {
                            "value": "secondary-transparent",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_2.option__6.label"
                        },
                        {
                            "value": "invert-transparent",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_2.option__7.label"
                        },
                        {
                            "value": "clean-transparent",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.color_button_type_2.option__8.label"
                        }
                    ]
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu_promobox.settings.header__6.content"
                },
                {
                    "type": "page",
                    "id": "list_1",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.list_1.label",
                    "info": "t:sections.header.blocks.megamenu_promobox.settings.list_1.info"
                },
                {
                    "type": "page",
                    "id": "custom_html",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.custom_html.label",
                    "info": "t:sections.header.blocks.megamenu_promobox.settings.custom_html.info"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu_promobox.settings.header__7.content"
                },
                {
                    "type": "select",
                    "id": "type",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.type.label",
                    "default": "clean",
                    "options": [
                        {
                            "value": "clean",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__1.label"
                        },
                        {
                            "value": "clean-bordered",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__2.label"
                        },
                        {
                            "value": "clean-hover-bordered",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__3.label"
                        },
                        {
                            "value": "text",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__4.label"
                        },
                        {
                            "value": "text-2",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__5.label"
                        },
                        {
                            "value": "text-3",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__6.label"
                        },
                        {
                            "value": "text-4",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__7.label"
                        },
                        {
                            "value": "text-5",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__8.label"
                        },
                        {
                            "value": "text-6",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__9.label"
                        },
                        {
                            "value": "text-7",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__10.label"
                        },
                        {
                            "value": "text-8",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__11.label"
                        },
                        {
                            "value": "text-9",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__12.label"
                        },
                        {
                            "value": "text-10",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__13.label"
                        },
                        {
                            "value": "type-1",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__14.label"
                        },
                        {
                            "value": "type-1-2",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__15.label"
                        },
                        {
                            "value": "type-1-3",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__16.label"
                        },
                        {
                            "value": "type-1-4",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__17.label"
                        },
                        {
                            "value": "type-1-5",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__18.label"
                        },
                        {
                            "value": "type-1-6",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__19.label"
                        },
                        {
                            "value": "type-1-7",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__20.label"
                        },
                        {
                            "value": "type-1-8",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__21.label"
                        },
                        {
                            "value": "type-1-9",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__22.label"
                        },
                        {
                            "value": "type-1-10",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__23.label"
                        },
                        {
                            "value": "type-1-11",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__24.label"
                        },
                        {
                            "value": "type-1-12",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__25.label"
                        },
                        {
                            "value": "type-1-13",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__26.label"
                        },
                        {
                            "value": "type-1-14",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__27.label"
                        },
                        {
                            "value": "type-1-15",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__28.label"
                        },
                        {
                            "value": "type-1-16",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__29.label"
                        },
                        {
                            "value": "type-1-17",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__30.label"
                        },
                        {
                            "value": "type-1-background",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__31.label"
                        },
                        {
                            "value": "type-1-background-2",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__32.label"
                        },
                        {
                            "value": "type-1-background-3",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__33.label"
                        },
                        {
                            "value": "type-1-background-4",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__34.label"
                        },
                        {
                            "value": "type-1-curtain",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__35.label"
                        },
                        {
                            "value": "type-1-curtain-2",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__36.label"
                        },
                        {
                            "value": "type-1-curtain-3",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__37.label"
                        },
                        {
                            "value": "type-2",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__38.label"
                        },
                        {
                            "value": "type-2-2",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__39.label"
                        },
                        {
                            "value": "type-2-3",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__40.label"
                        },
                        {
                            "value": "type-2-4",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__41.label"
                        },
                        {
                            "value": "type-2-5",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__42.label"
                        },
                        {
                            "value": "type-2-6",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__43.label"
                        },
                        {
                            "value": "type-2-7",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__44.label"
                        },
                        {
                            "value": "type-2-8",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__45.label"
                        },
                        {
                            "value": "type-2-9",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__46.label"
                        },
                        {
                            "value": "type-2-10",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__47.label"
                        },
                        {
                            "value": "type-2-11",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__48.label"
                        },
                        {
                            "value": "type-2-12",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__49.label"
                        },
                        {
                            "value": "type-2-13",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__50.label"
                        },
                        {
                            "value": "type-3",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__51.label"
                        },
                        {
                            "value": "type-4",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.type.option__52.label"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "content_position",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.content_position.label",
                    "info": "t:sections.header.blocks.megamenu_promobox.settings.content_position.info",
                    "default": "center",
                    "options": [
                        {
                            "value": "center",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.content_position.option__1.label"
                        },
                        {
                            "value": "center_left",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.content_position.option__2.label"
                        },
                        {
                            "value": "center_right",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.content_position.option__3.label"
                        },
                        {
                            "value": "top_center",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.content_position.option__4.label"
                        },
                        {
                            "value": "top_left",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.content_position.option__5.label"
                        },
                        {
                            "value": "top_right",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.content_position.option__6.label"
                        },
                        {
                            "value": "bottom_center",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.content_position.option__7.label"
                        },
                        {
                            "value": "bottom_left",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.content_position.option__8.label"
                        },
                        {
                            "value": "bottom_right",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.content_position.option__9.label"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "content_align",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.content_align.label",
                    "default": "center",
                    "options": [
                        {
                            "value": "left",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.content_align.option__1.label"
                        },
                        {
                            "value": "center",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.content_align.option__2.label"
                        },
                        {
                            "value": "right",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.content_align.option__3.label"
                        }
                    ]
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu_promobox.settings.header__8.content"
                },
                {
                    "type": "select",
                    "id": "animation_to",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_to.label",
                    "default": "none",
                    "options": [
                        {
                            "value": "none",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_to.option__1.label"
                        },
                        {
                            "value": "center",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_to.option__2.label"
                        },
                        {
                            "value": "top-left",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_to.option__3.label"
                        },
                        {
                            "value": "top",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_to.option__4.label"
                        },
                        {
                            "value": "top-right",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_to.option__5.label"
                        },
                        {
                            "value": "right",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_to.option__6.label"
                        },
                        {
                            "value": "bottom-right",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_to.option__7.label"
                        },
                        {
                            "value": "bottom",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_to.option__8.label"
                        },
                        {
                            "value": "bottom-left",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_to.option__9.label"
                        },
                        {
                            "value": "left",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_to.option__10.label"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "animation_from",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_from.label",
                    "default": "none",
                    "options": [
                        {
                            "value": "none",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_from.option__1.label"
                        },
                        {
                            "value": "center",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_from.option__2.label"
                        },
                        {
                            "value": "top-left",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_from.option__3.label"
                        },
                        {
                            "value": "top",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_from.option__4.label"
                        },
                        {
                            "value": "top-right",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_from.option__5.label"
                        },
                        {
                            "value": "right",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_from.option__6.label"
                        },
                        {
                            "value": "bottom-right",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_from.option__7.label"
                        },
                        {
                            "value": "bottom",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_from.option__8.label"
                        },
                        {
                            "value": "bottom-left",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_from.option__9.label"
                        },
                        {
                            "value": "left",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_from.option__10.label"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "animation_opacity",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_opacity.label",
                    "default": "none",
                    "options": [
                        {
                            "value": "none",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_opacity.option__1.label"
                        },
                        {
                            "value": "static_n_hover",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_opacity.option__2.label"
                        },
                        {
                            "value": "static",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_opacity.option__3.label"
                        },
                        {
                            "value": "hover",
                            "label": "t:sections.header.blocks.megamenu_promobox.settings.animation_opacity.option__4.label"
                        }
                    ]
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu_promobox.settings.header__9.content"
                },
                {
                    "type": "text",
                    "id": "customization_class",
                    "label": "t:sections.header.blocks.megamenu_promobox.settings.customization_class.label",
                    "info": "t:sections.header.blocks.megamenu_promobox.settings.customization_class.info"
                }
            ]
        },
        {
            "type": "megamenu_brands",
            "name": "t:sections.header.blocks.megamenu_brands.name",
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.title.label"
                },
                {
                    "type": "url",
                    "id": "url",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.url.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu_brands.settings.header__1.content"
                },
                {
                    "type": "select",
                    "id": "size_of_images",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.size_of_images.label",
                    "default": "2",
                    "options": [
                        {
                            "value": "1",
                            "label": "t:sections.header.blocks.megamenu_brands.settings.size_of_images.option__1.label"
                        },
                        {
                            "value": "2",
                            "label": "t:sections.header.blocks.megamenu_brands.settings.size_of_images.option__2.label"
                        },
                        {
                            "value": "3",
                            "label": "t:sections.header.blocks.megamenu_brands.settings.size_of_images.option__3.label"
                        },
                        {
                            "value": "4",
                            "label": "t:sections.header.blocks.megamenu_brands.settings.size_of_images.option__4.label"
                        },
                        {
                            "value": "5",
                            "label": "t:sections.header.blocks.megamenu_brands.settings.size_of_images.option__5.label"
                        },
                        {
                            "value": "6",
                            "label": "t:sections.header.blocks.megamenu_brands.settings.size_of_images.option__6.label"
                        },
                        {
                            "value": "12",
                            "label": "t:sections.header.blocks.megamenu_brands.settings.size_of_images.option__7.label"
                        }
                    ]
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu_brands.settings.header__2.content"
                },
                {
                    "type": "image_picker",
                    "id": "image_1",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_1.label"
                },
                {
                    "type": "range",
                    "id": "image_size_1",
                    "min": 50,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_size_1.label",
                    "info": "t:sections.header.blocks.megamenu_brands.settings.image_size_1.info",
                    "default": 80
                },
                {
                    "type": "url",
                    "id": "url_1",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.url_1.label"
                },
                {
                    "type": "image_picker",
                    "id": "image_2",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_2.label"
                },
                {
                    "type": "range",
                    "id": "image_size_2",
                    "min": 50,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_size_2.label",
                    "default": 80
                },
                {
                    "type": "url",
                    "id": "url_2",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.url_2.label"
                },
                {
                    "type": "image_picker",
                    "id": "image_3",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_3.label"
                },
                {
                    "type": "range",
                    "id": "image_size_3",
                    "min": 50,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_size_3.label",
                    "default": 80
                },
                {
                    "type": "url",
                    "id": "url_3",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.url_3.label"
                },
                {
                    "type": "image_picker",
                    "id": "image_4",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_4.label"
                },
                {
                    "type": "range",
                    "id": "image_size_4",
                    "min": 50,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_size_4.label",
                    "default": 80
                },
                {
                    "type": "url",
                    "id": "url_4",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.url_4.label"
                },
                {
                    "type": "image_picker",
                    "id": "image_5",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_5.label"
                },
                {
                    "type": "range",
                    "id": "image_size_5",
                    "min": 50,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_size_5.label",
                    "default": 80
                },
                {
                    "type": "url",
                    "id": "url_5",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.url_5.label"
                },
                {
                    "type": "image_picker",
                    "id": "image_6",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_6.label"
                },
                {
                    "type": "range",
                    "id": "image_size_6",
                    "min": 50,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_size_6.label",
                    "default": 80
                },
                {
                    "type": "url",
                    "id": "url_6",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.url_6.label"
                },
                {
                    "type": "image_picker",
                    "id": "image_7",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_7.label"
                },
                {
                    "type": "range",
                    "id": "image_size_7",
                    "min": 50,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_size_7.label",
                    "default": 80
                },
                {
                    "type": "url",
                    "id": "url_7",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.url_7.label"
                },
                {
                    "type": "image_picker",
                    "id": "image_8",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_8.label"
                },
                {
                    "type": "range",
                    "id": "image_size_8",
                    "min": 50,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_size_8.label",
                    "default": 80
                },
                {
                    "type": "url",
                    "id": "url_8",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.url_8.label"
                },
                {
                    "type": "image_picker",
                    "id": "image_9",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_9.label"
                },
                {
                    "type": "range",
                    "id": "image_size_9",
                    "min": 50,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_size_9.label",
                    "default": 80
                },
                {
                    "type": "url",
                    "id": "url_9",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.url_9.label"
                },
                {
                    "type": "image_picker",
                    "id": "image_10",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_10.label"
                },
                {
                    "type": "range",
                    "id": "image_size_10",
                    "min": 50,
                    "max": 300,
                    "step": 10,
                    "unit": "px",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.image_size_10.label",
                    "default": 80
                },
                {
                    "type": "url",
                    "id": "url_10",
                    "label": "t:sections.header.blocks.megamenu_brands.settings.url_10.label"
                }
            ]
        },
        {
            "type": "megamenu_products",
            "name": "t:sections.header.blocks.megamenu_products.name",
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "t:sections.header.blocks.megamenu_products.settings.title.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu_products.settings.header__1.content"
                },
                {
                    "type": "collection",
                    "id": "collection",
                    "label": "t:sections.header.blocks.megamenu_products.settings.collection.label"
                },
                {
                    "type": "select",
                    "id": "products_per_row",
                    "label": "t:sections.header.blocks.megamenu_products.settings.products_per_row.label",
                    "default": "2",
                    "options": [
                        {
                            "value": "1",
                            "label": "t:sections.header.blocks.megamenu_products.settings.products_per_row.option__1.label"
                        },
                        {
                            "value": "2",
                            "label": "t:sections.header.blocks.megamenu_products.settings.products_per_row.option__2.label"
                        },
                        {
                            "value": "3",
                            "label": "t:sections.header.blocks.megamenu_products.settings.products_per_row.option__3.label"
                        },
                        {
                            "value": "4",
                            "label": "t:sections.header.blocks.megamenu_products.settings.products_per_row.option__4.label"
                        },
                        {
                            "value": "6",
                            "label": "t:sections.header.blocks.megamenu_products.settings.products_per_row.option__5.label"
                        },
                        {
                            "value": "12",
                            "label": "t:sections.header.blocks.megamenu_products.settings.products_per_row.option__6.label"
                        }
                    ]
                },
                {
                    "type": "range",
                    "id": "products_rows",
                    "min": 1,
                    "max": 6,
                    "step": 1,
                    "label": "t:sections.header.blocks.megamenu_products.settings.products_rows.label",
                    "default": 1
                }
            ]
        },
        {
            "type": "megamenu_custom_html",
            "name": "t:sections.header.blocks.megamenu_custom_html.name",
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "t:sections.header.blocks.megamenu_custom_html.settings.title.label"
                },
                {
                    "type": "url",
                    "id": "url",
                    "label": "t:sections.header.blocks.megamenu_custom_html.settings.url.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu_custom_html.settings.header__1.content"
                },
                {
                    "type": "page",
                    "id": "page_content",
                    "label": "t:sections.header.blocks.megamenu_custom_html.settings.page_content.label"
                }
            ]
        },
        {
            "type": "megamenu_subscription",
            "name": "t:sections.header.blocks.megamenu_subscription.name",
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "t:sections.header.blocks.megamenu_subscription.settings.title.label"
                },
                {
                    "type": "url",
                    "id": "url",
                    "label": "t:sections.header.blocks.megamenu_subscription.settings.url.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.megamenu_subscription.settings.header__1.content"
                },
                {
                    "type": "text",
                    "id": "placeholder",
                    "label": "t:sections.header.blocks.megamenu_subscription.settings.placeholder.label",
                    "default": "Eメールアドレスを入力してください" // "Enter please your email address"
                },
                {
                    "type": "text",
                    "id": "button_text",
                    "label": "t:sections.header.blocks.megamenu_subscription.settings.button_text.label",
                    "default": "購読" // "SUBSCRIBE!"
                }
            ]
        },
        {
            "type": "menu",
            "name": "t:sections.header.blocks.menu.name",
            "settings": [
                {
                    "type": "text",
                    "id": "for_item",
                    "label": "t:sections.header.blocks.menu.settings.for_item.label",
                    "info": "t:sections.header.blocks.menu.settings.for_item.info"
                },
                {
                    "type": "link_list",
                    "id": "menu",
                    "label": "t:sections.header.blocks.menu.settings.menu.label"
                }
            ]
        },
        {
            "type": "colorize",
            "name": "t:sections.header.blocks.colorize.name",
            "limit": 1,
            "settings": [
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.colorize.settings.header__1.content"
                },
                {
                    "type": "color",
                    "id": "line_2_bg",
                    "label": "t:sections.header.blocks.colorize.settings.line_2_bg.label"
                },
                {
                    "type": "color",
                    "id": "line_2_bd",
                    "label": "t:sections.header.blocks.colorize.settings.line_2_bd.label"
                },
                {
                    "type": "color",
                    "id": "btns_line_2_d_c",
                    "label": "t:sections.header.blocks.colorize.settings.btns_line_2_d_c.label"
                },
                {
                    "type": "color",
                    "id": "btns_line_2_d_h_c",
                    "label": "t:sections.header.blocks.colorize.settings.btns_line_2_d_h_c.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.colorize.settings.header__2.content"
                },
                {
                    "type": "paragraph",
                    "content": "t:sections.header.blocks.colorize.settings.paragraph__1.content"
                },
                {
                    "type": "color",
                    "id": "line_1_bg",
                    "label": "t:sections.header.blocks.colorize.settings.line_1_bg.label"
                },
                {
                    "type": "color",
                    "id": "line_1_bd",
                    "label": "t:sections.header.blocks.colorize.settings.line_1_bd.label"
                },
                {
                    "type": "color",
                    "id": "btns_line_1_d_c",
                    "label": "t:sections.header.blocks.colorize.settings.btns_line_1_d_c.label"
                },
                {
                    "type": "color",
                    "id": "btns_line_1_d_h_c",
                    "label": "t:sections.header.blocks.colorize.settings.btns_line_1_d_h_c.label"
                },
                {
                    "type": "select",
                    "id": "search_btn_type",
                    "label": "t:sections.header.blocks.colorize.settings.search_btn_type.label",
                    "default": "auto",
                    "options": [
                        {
                            "value": "auto",
                            "label": "t:sections.header.blocks.colorize.settings.search_btn_type.option__1.label"
                        },
                        {
                            "value": "default",
                            "label": "t:sections.header.blocks.colorize.settings.search_btn_type.option__2.label"
                        },
                        {
                            "value": "invert",
                            "label": "t:sections.header.blocks.colorize.settings.search_btn_type.option__3.label"
                        },
                        {
                            "value": "secondary",
                            "label": "t:sections.header.blocks.colorize.settings.search_btn_type.option__4.label"
                        },
                        {
                            "value": "clean",
                            "label": "t:sections.header.blocks.colorize.settings.search_btn_type.option__5.label"
                        }
                    ]
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.colorize.settings.header__3.content"
                },
                {
                    "type": "color",
                    "id": "m_bg",
                    "label": "t:sections.header.blocks.colorize.settings.m_bg.label"
                },
                {
                    "type": "color",
                    "id": "m_bd",
                    "label": "t:sections.header.blocks.colorize.settings.m_bd.label"
                },
                {
                    "type": "color",
                    "id": "btns_m_c",
                    "label": "t:sections.header.blocks.colorize.settings.btns_m_c.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.colorize.settings.header__4.content"
                },
                {
                    "type": "color",
                    "id": "menu_sticky_c",
                    "label": "t:sections.header.blocks.colorize.settings.menu_sticky_c.label"
                },
                {
                    "type": "color",
                    "id": "menu_sticky_h_c",
                    "label": "t:sections.header.blocks.colorize.settings.menu_sticky_h_c.label"
                },
                {
                    "type": "color",
                    "id": "btns_sticky_c",
                    "label": "t:sections.header.blocks.colorize.settings.btns_sticky_c.label"
                },
                {
                    "type": "color",
                    "id": "btns_sticky_h_c",
                    "label": "t:sections.header.blocks.colorize.settings.btns_sticky_h_c.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.colorize.settings.header__5.content"
                },
                {
                    "type": "paragraph",
                    "content": "t:sections.header.blocks.colorize.settings.paragraph__2.content"
                },
                {
                    "type": "color",
                    "id": "line_additional_bg",
                    "label": "t:sections.header.blocks.colorize.settings.line_additional_bg.label"
                },
                {
                    "type": "color",
                    "id": "line_additional_bd",
                    "label": "t:sections.header.blocks.colorize.settings.line_additional_bd.label"
                },
                {
                    "type": "color",
                    "id": "line_additional_c",
                    "label": "t:sections.header.blocks.colorize.settings.line_additional_c.label"
                },
                {
                    "type": "color",
                    "id": "line_additional_highlighted_buttons_c",
                    "label": "t:sections.header.blocks.colorize.settings.line_additional_highlighted_buttons_c.label"
                },
                {
                    "type": "color",
                    "id": "line_additional_h_c",
                    "label": "t:sections.header.blocks.colorize.settings.line_additional_h_c.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.colorize.settings.header__6.content"
                },
                {
                    "type": "color",
                    "id": "info_line_bg",
                    "label": "t:sections.header.blocks.colorize.settings.info_line_bg.label"
                },
                {
                    "type": "color",
                    "id": "info_line_bd",
                    "label": "t:sections.header.blocks.colorize.settings.info_line_bd.label"
                },
                {
                    "type": "color",
                    "id": "info_line_c",
                    "label": "t:sections.header.blocks.colorize.settings.info_line_c.label"
                },
                {
                    "type": "color",
                    "id": "info_line_media_c",
                    "label": "t:sections.header.blocks.colorize.settings.info_line_media_c.label"
                },
                {
                    "type": "color",
                    "id": "info_line_media_h_c",
                    "label": "t:sections.header.blocks.colorize.settings.info_line_media_h_c.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.colorize.settings.header__7.content"
                },
                {
                    "type": "color",
                    "id": "tape_bg",
                    "label": "t:sections.header.blocks.colorize.settings.tape_bg.label"
                },
                {
                    "type": "color",
                    "id": "tape_bd",
                    "label": "t:sections.header.blocks.colorize.settings.tape_bd.label"
                },
                {
                    "type": "color",
                    "id": "tape_c",
                    "label": "t:sections.header.blocks.colorize.settings.tape_c.label"
                },
                {
                    "type": "color",
                    "id": "tape_btn_close_c",
                    "label": "t:sections.header.blocks.colorize.settings.tape_btn_close_c.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.colorize.settings.header__8.content"
                },
                {
                    "type": "color",
                    "id": "menu_c",
                    "label": "t:sections.header.blocks.colorize.settings.menu_c.label"
                },
                {
                    "type": "color",
                    "id": "menu_h_c",
                    "label": "t:sections.header.blocks.colorize.settings.menu_h_c.label"
                },
                {
                    "type": "color",
                    "id": "menu_h_bg",
                    "label": "t:sections.header.blocks.colorize.settings.menu_h_bg.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.colorize.settings.header__9.content"
                },
                {
                    "type": "color",
                    "id": "menu_title_c",
                    "label": "t:sections.header.blocks.colorize.settings.menu_title_c.label"
                },
                {
                    "type": "color",
                    "id": "menu_list_c",
                    "label": "t:sections.header.blocks.colorize.settings.menu_list_c.label"
                },
                {
                    "type": "color",
                    "id": "menu_list_h_c",
                    "label": "t:sections.header.blocks.colorize.settings.menu_list_h_c.label"
                },
                {
                    "type": "color",
                    "id": "menu_list_h_bg",
                    "label": "t:sections.header.blocks.colorize.settings.menu_list_h_bg.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.header.blocks.colorize.settings.header__10.content"
                },
                {
                    "type": "color",
                    "id": "menu_vertical_button_bg",
                    "label": "t:sections.header.blocks.colorize.settings.menu_vertical_button_bg.label"
                },
                {
                    "type": "color",
                    "id": "menu_vertical_button_c",
                    "label": "t:sections.header.blocks.colorize.settings.menu_vertical_button_c.label"
                },
                {
                    "type": "color",
                    "id": "menu_vertical_d_bg",
                    "label": "t:sections.header.blocks.colorize.settings.menu_vertical_d_bg.label"
                },
                {
                    "type": "color",
                    "id": "menu_vertical_d_bd",
                    "label": "t:sections.header.blocks.colorize.settings.menu_vertical_d_bd.label"
                },
                {
                    "type": "color",
                    "id": "menu_vertical_d_c",
                    "label": "t:sections.header.blocks.colorize.settings.menu_vertical_d_c.label"
                },
                {
                    "type": "color",
                    "id": "menu_vertical_h_c",
                    "label": "t:sections.header.blocks.colorize.settings.menu_vertical_h_c.label"
                }
            ]
        }
    ]
}
{% endschema %}