/**
 * 日付バリデーションと料金計算のテスト
 * 
 * このスクリプトでは以下をテストします：
 * 1. 日曜日・祝日・年末年始の判定
 * 2. 開始日・終了日の選択可否
 * 3. 営業日数の計算
 * 4. 料金計算の正確性
 */

// 必要なモジュールをインポート
import { isJapaneseHoliday, isSunday, isNewYearHoliday, calculateBusinessDays, isClosedDay } from '../app/utils/calendar/unified-calendar.js';
import { calculateRentalPrice } from '../app/utils/pricing/unified-price-calculator.js';

// テスト結果を保存する配列
const testResults = [];

/**
 * テスト結果を記録する関数
 */
function logTestResult(testName, success, details = {}) {
  const result = {
    testName,
    success,
    timestamp: new Date().toISOString(),
    ...details
  };
  
  testResults.push(result);
  
  // コンソールにも出力
  console.log(`[${success ? 'SUCCESS' : 'FAILURE'}] ${testName}`);
  if (details.message) {
    console.log(`  ${details.message}`);
  }
  if (details.error) {
    console.error(`  Error: ${details.error}`);
  }
}

/**
 * 日曜日判定のテスト
 */
function testSundayDetection() {
  console.log("\nテストケース1: 日曜日判定のテスト");
  
  try {
    // 2023年の日曜日
    const sundays = [
      new Date(2023, 0, 1),  // 2023-01-01
      new Date(2023, 0, 8),  // 2023-01-08
      new Date(2023, 0, 15), // 2023-01-15
      new Date(2023, 0, 22), // 2023-01-22
      new Date(2023, 0, 29)  // 2023-01-29
    ];
    
    // 2023年の平日
    const weekdays = [
      new Date(2023, 0, 2),  // 2023-01-02 (月)
      new Date(2023, 0, 3),  // 2023-01-03 (火)
      new Date(2023, 0, 4),  // 2023-01-04 (水)
      new Date(2023, 0, 5),  // 2023-01-05 (木)
      new Date(2023, 0, 6)   // 2023-01-06 (金)
    ];
    
    // 日曜日のテスト
    const sundayResults = sundays.map(date => isSunday(date));
    const allSundaysDetected = sundayResults.every(result => result === true);
    
    // 平日のテスト
    const weekdayResults = weekdays.map(date => isSunday(date));
    const noWeekdaysDetected = weekdayResults.every(result => result === false);
    
    const success = allSundaysDetected && noWeekdaysDetected;
    
    logTestResult("日曜日判定", success, {
      message: `日曜日の判定: ${allSundaysDetected ? '正確' : '不正確'}, 平日の判定: ${noWeekdaysDetected ? '正確' : '不正確'}`
    });
    
    return success;
  } catch (error) {
    logTestResult("日曜日判定", false, {
      error: error.message
    });
    
    return false;
  }
}

/**
 * 祝日判定のテスト
 */
function testHolidayDetection() {
  console.log("\nテストケース2: 祝日判定のテスト");
  
  try {
    // 2023年の祝日
    const holidays = [
      new Date(2023, 0, 1),   // 元日
      new Date(2023, 0, 2),   // 元日（振替休日）
      new Date(2023, 0, 9),   // 成人の日
      new Date(2023, 1, 11),  // 建国記念の日
      new Date(2023, 1, 23),  // 天皇誕生日
      new Date(2023, 2, 21),  // 春分の日
      new Date(2023, 4, 3),   // 憲法記念日
      new Date(2023, 4, 4),   // みどりの日
      new Date(2023, 4, 5)    // こどもの日
    ];
    
    // 祝日ではない日
    const nonHolidays = [
      new Date(2023, 0, 3),   // 1月3日
      new Date(2023, 0, 4),   // 1月4日
      new Date(2023, 0, 5),   // 1月5日
      new Date(2023, 1, 1),   // 2月1日
      new Date(2023, 2, 1)    // 3月1日
    ];
    
    // 祝日のテスト
    const holidayResults = holidays.map(date => {
      const result = isJapaneseHoliday(date);
      console.log(`  ${date.toISOString().split('T')[0]} は祝日${result ? 'です' : 'ではありません'}`);
      return result;
    });
    
    // 祝日ではない日のテスト
    const nonHolidayResults = nonHolidays.map(date => {
      const result = isJapaneseHoliday(date);
      console.log(`  ${date.toISOString().split('T')[0]} は祝日${result ? 'です' : 'ではありません'}`);
      return result;
    });
    
    // 結果の集計
    const holidaysDetected = holidayResults.filter(result => result === true).length;
    const nonHolidaysCorrect = nonHolidayResults.filter(result => result === false).length;
    
    const success = holidaysDetected >= 7 && nonHolidaysCorrect === nonHolidays.length;
    
    logTestResult("祝日判定", success, {
      message: `祝日の判定: ${holidaysDetected}/${holidays.length} 正確, 非祝日の判定: ${nonHolidaysCorrect}/${nonHolidays.length} 正確`
    });
    
    return success;
  } catch (error) {
    logTestResult("祝日判定", false, {
      error: error.message
    });
    
    return false;
  }
}

/**
 * 年末年始判定のテスト
 */
function testNewYearHolidayDetection() {
  console.log("\nテストケース3: 年末年始判定のテスト");
  
  try {
    // 年末年始の日付
    const newYearDates = [
      new Date(2022, 11, 29), // 2022-12-29
      new Date(2022, 11, 30), // 2022-12-30
      new Date(2022, 11, 31), // 2022-12-31
      new Date(2023, 0, 1),   // 2023-01-01
      new Date(2023, 0, 2),   // 2023-01-02
      new Date(2023, 0, 3)    // 2023-01-03
    ];
    
    // 年末年始ではない日付
    const nonNewYearDates = [
      new Date(2022, 11, 28), // 2022-12-28
      new Date(2023, 0, 4),   // 2023-01-04
      new Date(2023, 0, 5),   // 2023-01-05
      new Date(2023, 1, 1),   // 2023-02-01
      new Date(2023, 5, 1)    // 2023-06-01
    ];
    
    // 年末年始のテスト
    const newYearResults = newYearDates.map(date => {
      const result = isNewYearHoliday(date);
      console.log(`  ${date.toISOString().split('T')[0]} は年末年始${result ? 'です' : 'ではありません'}`);
      return result;
    });
    
    // 年末年始ではない日のテスト
    const nonNewYearResults = nonNewYearDates.map(date => {
      const result = isNewYearHoliday(date);
      console.log(`  ${date.toISOString().split('T')[0]} は年末年始${result ? 'です' : 'ではありません'}`);
      return result;
    });
    
    // 結果の集計
    const newYearDetected = newYearResults.filter(result => result === true).length;
    const nonNewYearCorrect = nonNewYearResults.filter(result => result === false).length;
    
    const success = newYearDetected === newYearDates.length && nonNewYearCorrect === nonNewYearDates.length;
    
    logTestResult("年末年始判定", success, {
      message: `年末年始の判定: ${newYearDetected}/${newYearDates.length} 正確, 非年末年始の判定: ${nonNewYearCorrect}/${nonNewYearDates.length} 正確`
    });
    
    return success;
  } catch (error) {
    logTestResult("年末年始判定", false, {
      error: error.message
    });
    
    return false;
  }
}

/**
 * 休業日判定のテスト
 */
function testClosedDayDetection() {
  console.log("\nテストケース4: 休業日判定のテスト");
  
  try {
    // 休業日（日曜日、祝日、年末年始）
    const closedDays = [
      new Date(2023, 0, 1),   // 元日（日曜日かつ祝日）
      new Date(2023, 0, 2),   // 元日（振替休日）
      new Date(2023, 0, 8),   // 日曜日
      new Date(2023, 0, 9),   // 成人の日
      new Date(2022, 11, 29), // 年末年始
      new Date(2022, 11, 30), // 年末年始
      new Date(2022, 11, 31)  // 年末年始
    ];
    
    // 営業日
    const businessDays = [
      new Date(2023, 0, 4),   // 平日
      new Date(2023, 0, 5),   // 平日
      new Date(2023, 0, 6),   // 平日
      new Date(2023, 0, 7),   // 土曜日
      new Date(2023, 1, 1)    // 平日
    ];
    
    // 休業日のテスト
    const closedDayResults = closedDays.map(date => {
      const result = isClosedDay(date);
      console.log(`  ${date.toISOString().split('T')[0]} は休業日${result ? 'です' : 'ではありません'}`);
      return result;
    });
    
    // 営業日のテスト
    const businessDayResults = businessDays.map(date => {
      const result = isClosedDay(date);
      console.log(`  ${date.toISOString().split('T')[0]} は休業日${result ? 'です' : 'ではありません'}`);
      return result;
    });
    
    // 結果の集計
    const closedDaysDetected = closedDayResults.filter(result => result === true).length;
    const businessDaysCorrect = businessDayResults.filter(result => result === false).length;
    
    const success = closedDaysDetected >= 5 && businessDaysCorrect === businessDays.length;
    
    logTestResult("休業日判定", success, {
      message: `休業日の判定: ${closedDaysDetected}/${closedDays.length} 正確, 営業日の判定: ${businessDaysCorrect}/${businessDays.length} 正確`
    });
    
    return success;
  } catch (error) {
    logTestResult("休業日判定", false, {
      error: error.message
    });
    
    return false;
  }
}

/**
 * 営業日数計算のテスト
 */
function testBusinessDaysCalculation() {
  console.log("\nテストケース5: 営業日数計算のテスト");
  
  try {
    // テストケース
    const testCases = [
      {
        startDate: new Date(2023, 0, 4),   // 2023-01-04 (水)
        endDate: new Date(2023, 0, 6),     // 2023-01-06 (金)
        expectedDays: 3                     // 3営業日
      },
      {
        startDate: new Date(2023, 0, 6),   // 2023-01-06 (金)
        endDate: new Date(2023, 0, 9),     // 2023-01-09 (月・祝)
        expectedDays: 1                     // 1営業日（金曜日のみ、土日と祝日は除外）
      },
      {
        startDate: new Date(2023, 0, 4),   // 2023-01-04 (水)
        endDate: new Date(2023, 0, 10),    // 2023-01-10 (火)
        expectedDays: 4                     // 4営業日（水木金火、土日と祝日は除外）
      },
      {
        startDate: new Date(2022, 11, 28), // 2022-12-28 (水)
        endDate: new Date(2023, 0, 4),     // 2023-01-04 (水)
        expectedDays: 2                     // 2営業日（12/28と1/4のみ、年末年始は除外）
      }
    ];
    
    // 各テストケースを実行
    const results = testCases.map(testCase => {
      const { startDate, endDate, expectedDays } = testCase;
      
      const calculatedDays = calculateBusinessDays(startDate, endDate);
      
      console.log(`  ${startDate.toISOString().split('T')[0]} 〜 ${endDate.toISOString().split('T')[0]}: 計算結果=${calculatedDays}日, 期待値=${expectedDays}日`);
      
      return calculatedDays === expectedDays;
    });
    
    // 結果の集計
    const successCount = results.filter(result => result === true).length;
    
    const success = successCount === testCases.length;
    
    logTestResult("営業日数計算", success, {
      message: `${successCount}/${testCases.length} のテストケースが成功`
    });
    
    return success;
  } catch (error) {
    logTestResult("営業日数計算", false, {
      error: error.message
    });
    
    return false;
  }
}

/**
 * 料金計算のテスト
 */
function testPriceCalculation() {
  console.log("\nテストケース6: 料金計算のテスト");
  
  try {
    // 基本料金
    const basePrice = 5000;
    
    // テストケース
    const testCases = [
      {
        startDate: new Date(2023, 0, 4),   // 2023-01-04 (水)
        endDate: new Date(2023, 0, 4),     // 2023-01-04 (水)
        expectedPrice: 5000,                // 1日目: 5000円
        description: "1日間のレンタル"
      },
      {
        startDate: new Date(2023, 0, 4),   // 2023-01-04 (水)
        endDate: new Date(2023, 0, 5),     // 2023-01-05 (木)
        expectedPrice: 6000,                // 1日目: 5000円, 2日目: 1000円 (5000 * 0.2)
        description: "2日間のレンタル"
      },
      {
        startDate: new Date(2023, 0, 4),   // 2023-01-04 (水)
        endDate: new Date(2023, 0, 6),     // 2023-01-06 (金)
        expectedPrice: 7000,                // 1日目: 5000円, 2-3日目: 2000円 (5000 * 0.2 * 2)
        description: "3日間のレンタル"
      },
      {
        startDate: new Date(2023, 0, 4),   // 2023-01-04 (水)
        endDate: new Date(2023, 0, 10),    // 2023-01-10 (火)
        expectedPrice: 8500,                // 1日目: 5000円, 2-6日目: 3000円 (5000 * 0.2 * 3), 7日目: 500円 (5000 * 0.1)
        description: "7日間のレンタル（日曜と祝日を除く）"
      },
      {
        startDate: new Date(2023, 0, 4),   // 2023-01-04 (水)
        endDate: new Date(2023, 0, 13),    // 2023-01-13 (金)
        expectedPrice: 10000,               // 1日目: 5000円, 2-6日目: 4000円 (5000 * 0.2 * 4), 7-8日目: 1000円 (5000 * 0.1 * 2)
        description: "10日間のレンタル（日曜と祝日を除く）"
      }
    ];
    
    // 各テストケースを実行
    const results = testCases.map(testCase => {
      const { startDate, endDate, expectedPrice, description } = testCase;
      
      const calculationResult = calculateRentalPrice(startDate, endDate, basePrice);
      const calculatedPrice = calculationResult.totalPrice;
      
      console.log(`  ${description}: ${startDate.toISOString().split('T')[0]} 〜 ${endDate.toISOString().split('T')[0]}`);
      console.log(`    計算結果=${calculatedPrice}円, 期待値=${expectedPrice}円, 有効日数=${calculationResult.effectiveDays}日`);
      
      // 料金内訳を表示
      console.log(`    料金内訳:`);
      calculationResult.priceBreakdown.forEach(item => {
        console.log(`      ${item.day}日目: ${item.price}円`);
      });
      
      // 許容誤差を設定（四捨五入の違いなどを考慮）
      const tolerance = 100;
      return Math.abs(calculatedPrice - expectedPrice) <= tolerance;
    });
    
    // 結果の集計
    const successCount = results.filter(result => result === true).length;
    
    const success = successCount === testCases.length;
    
    logTestResult("料金計算", success, {
      message: `${successCount}/${testCases.length} のテストケースが成功`
    });
    
    return success;
  } catch (error) {
    logTestResult("料金計算", false, {
      error: error.message
    });
    
    return false;
  }
}

/**
 * メイン関数
 */
async function runTests() {
  console.log("=== 日付バリデーションと料金計算のテスト ===\n");
  
  try {
    // 各テストを実行
    testSundayDetection();
    testHolidayDetection();
    testNewYearHolidayDetection();
    testClosedDayDetection();
    testBusinessDaysCalculation();
    testPriceCalculation();
    
    // テスト結果のサマリーを表示
    console.log("\n=== テスト結果サマリー ===");
    const successCount = testResults.filter(r => r.success).length;
    const failureCount = testResults.length - successCount;
    
    console.log(`実行したテスト数: ${testResults.length}`);
    console.log(`成功: ${successCount}`);
    console.log(`失敗: ${failureCount}`);
    console.log("============================\n");
    
    // 詳細なテスト結果を表示
    console.log("詳細なテスト結果:");
    testResults.forEach((result, index) => {
      console.log(`${index + 1}. [${result.success ? 'SUCCESS' : 'FAILURE'}] ${result.testName}`);
      if (result.message) {
        console.log(`   ${result.message}`);
      }
    });
    
    // テスト結果を返す
    return {
      totalTests: testResults.length,
      successCount,
      failureCount,
      testResults
    };
  } catch (error) {
    console.error("テスト実行中にエラーが発生しました:", error);
  }
}

// テストを実行
runTests();
