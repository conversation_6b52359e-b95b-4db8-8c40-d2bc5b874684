/**
 * 料金計算整合性チェックサービス
 *
 * Shopifyバリエーション価格と統一計算ロジックの整合性をチェックし、
 * 計算間違いを防ぐためのサービス
 */

import { UnifiedPricingService, type PricingConfig, type PricingResult } from './unified-pricing.service';

export interface VariantPrice {
  days: number;
  price: number;
  variantId?: string;
  title?: string;
}

export interface ValidationResult {
  isValid: boolean;
  discrepancies: Array<{
    days: number;
    variantPrice: number;
    calculatedPrice: number;
    difference: number;
    percentageDiff: number;
  }>;
  missingVariants: number[];
  recommendations: string[];
}

export interface PricingSource {
  type: 'VARIANT' | 'CALCULATED' | 'HYBRID';
  confidence: number; // 0-1の信頼度
  reason: string;
}

export class PricingValidationService {
  private static instance: PricingValidationService;
  private pricingService: UnifiedPricingService;
  private readonly TOLERANCE_PERCENTAGE = 5; // 5%の許容誤差

  private constructor() {
    this.pricingService = UnifiedPricingService.getInstance();
  }

  public static getInstance(): PricingValidationService {
    if (!PricingValidationService.instance) {
      PricingValidationService.instance = new PricingValidationService();
    }
    return PricingValidationService.instance;
  }

  /**
   * バリエーション価格と計算価格の整合性をチェック
   */
  public validatePricing(
    basePrice: number,
    variantPrices: VariantPrice[],
    config?: Partial<PricingConfig>
  ): ValidationResult {
    console.log('=== 料金整合性チェック開始 ===');
    console.log('基本価格:', basePrice);
    console.log('バリエーション価格:', variantPrices);

    const discrepancies: ValidationResult['discrepancies'] = [];
    const missingVariants: number[] = [];
    const recommendations: string[] = [];

    // 1-7日のバリエーションをチェック
    for (let days = 1; days <= 7; days++) {
      const variant = variantPrices.find(v => v.days === days);

      if (!variant) {
        missingVariants.push(days);
        recommendations.push(`${days}日バリエーションが見つかりません`);
        continue;
      }

      // 統一計算ロジックで価格を計算
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(startDate.getDate() + days - 1);

      const calculatedResult = this.pricingService.calculatePrice(
        startDate,
        endDate,
        basePrice,
        config
      );

      const difference = Math.abs(variant.price - calculatedResult.totalPrice);
      const percentageDiff = (difference / calculatedResult.totalPrice) * 100;

      if (percentageDiff > this.TOLERANCE_PERCENTAGE) {
        discrepancies.push({
          days,
          variantPrice: variant.price,
          calculatedPrice: calculatedResult.totalPrice,
          difference,
          percentageDiff
        });
      }

      console.log(`${days}日: バリエーション=${variant.price}円, 計算=${calculatedResult.totalPrice}円, 差=${difference}円 (${percentageDiff.toFixed(1)}%)`);
    }

    // 推奨事項を生成
    if (discrepancies.length > 0) {
      recommendations.push(`${discrepancies.length}個の価格不整合が検出されました`);
      recommendations.push('バリエーション価格の更新を推奨します');
    }

    if (missingVariants.length > 0) {
      recommendations.push(`${missingVariants.join(', ')}日のバリエーションを追加してください`);
    }

    const isValid = discrepancies.length === 0 && missingVariants.length === 0;

    console.log('整合性チェック結果:', isValid ? '✅ 正常' : '❌ 不整合あり');
    console.log('=== 料金整合性チェック終了 ===');

    return {
      isValid,
      discrepancies,
      missingVariants,
      recommendations
    };
  }

  /**
   * 最適な価格ソースを決定
   */
  public determineBestPricingSource(
    days: number,
    basePrice: number,
    variantPrices: VariantPrice[],
    config?: Partial<PricingConfig>
  ): { price: number; source: PricingSource } {
    console.log(`${days}日間の最適価格ソースを決定中...`);

    // 8日以上の場合は常に計算ロジックを使用
    if (days >= 8) {
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(startDate.getDate() + days - 1);

      const result = this.pricingService.calculatePrice(startDate, endDate, basePrice, config);

      return {
        price: result.totalPrice,
        source: {
          type: 'CALCULATED',
          confidence: 1.0,
          reason: '8日以上はバリエーション価格が存在しないため計算ロジックを使用'
        }
      };
    }

    // 対応するバリエーションを検索
    const variant = variantPrices.find(v => v.days === days);

    if (!variant) {
      // バリエーションが見つからない場合は計算ロジックを使用
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(startDate.getDate() + days - 1);

      const result = this.pricingService.calculatePrice(startDate, endDate, basePrice, config);

      return {
        price: result.totalPrice,
        source: {
          type: 'CALCULATED',
          confidence: 0.8,
          reason: `${days}日バリエーションが見つからないため計算ロジックを使用`
        }
      };
    }

    // バリエーション価格と計算価格を比較
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(startDate.getDate() + days - 1);

    const calculatedResult = this.pricingService.calculatePrice(startDate, endDate, basePrice, config);
    const difference = Math.abs(variant.price - calculatedResult.totalPrice);
    const percentageDiff = (difference / calculatedResult.totalPrice) * 100;

    if (percentageDiff <= this.TOLERANCE_PERCENTAGE) {
      // 整合性が取れている場合はバリエーション価格を使用
      return {
        price: variant.price,
        source: {
          type: 'VARIANT',
          confidence: 1.0,
          reason: 'バリエーション価格と計算価格が整合している'
        }
      };
    } else {
      // 不整合がある場合は計算価格を使用し、警告を出す
      console.warn(`${days}日の価格不整合: バリエーション=${variant.price}円, 計算=${calculatedResult.totalPrice}円`);

      return {
        price: calculatedResult.totalPrice,
        source: {
          type: 'CALCULATED',
          confidence: 0.9,
          reason: `バリエーション価格に${percentageDiff.toFixed(1)}%の不整合があるため計算価格を使用`
        }
      };
    }
  }

  /**
   * 正しいバリエーション価格を生成
   */
  public generateCorrectVariantPrices(
    basePrice: number,
    config?: Partial<PricingConfig>
  ): VariantPrice[] {
    console.log('正しいバリエーション価格を生成中...');

    const variantPrices: VariantPrice[] = [];

    for (let days = 1; days <= 7; days++) {
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(startDate.getDate() + days - 1);

      const result = this.pricingService.calculatePrice(startDate, endDate, basePrice, config);

      variantPrices.push({
        days,
        price: result.totalPrice,
        title: `${days}日レンタル`
      });
    }

    // 8日以上用の1日あたり料金を計算（無限ループを避けるため直接計算）
    const additionalDayPrice = Math.round(basePrice * (config?.discountRules?.day7_plus_rate || 0.1));

    variantPrices.push({
      days: 8,
      price: additionalDayPrice,
      title: '8日以上レンタル（1日あたり）'
    });

    console.log('生成されたバリエーション価格:', variantPrices);
    return variantPrices;
  }

  /**
   * 仮予約料金の整合性をチェック
   */
  public validateProvisionalPricing(
    basePrice: number,
    provisionalRate: number = 0.1,
    config?: Partial<PricingConfig>
  ): {
    isValid: boolean;
    testResults: Array<{
      days: number;
      confirmedPrice: number;
      provisionalPrice: number;
      expectedProvisional: number;
      isCorrect: boolean;
      difference: number;
    }>;
    recommendations: string[];
  } {
    console.log('=== 仮予約料金整合性チェック開始 ===');

    const testResults: Array<{
      days: number;
      confirmedPrice: number;
      provisionalPrice: number;
      expectedProvisional: number;
      isCorrect: boolean;
      difference: number;
    }> = [];

    const recommendations: string[] = [];

    // 1, 3, 7, 10日でテスト
    const testDays = [1, 3, 7, 10];

    for (const days of testDays) {
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(startDate.getDate() + days - 1);

      // 正規予約計算
      const confirmed = this.pricingService.calculatePrice(startDate, endDate, basePrice, {
        ...config,
        isProvisional: false,
      });

      // 仮予約計算
      const provisional = this.pricingService.calculatePrice(startDate, endDate, basePrice, {
        ...config,
        isProvisional: true,
        provisionalRate,
      });

      const expectedProvisional = Math.round(confirmed.totalPrice * provisionalRate);
      const difference = Math.abs(provisional.totalPrice - expectedProvisional);
      const isCorrect = difference <= 1; // 1円の許容誤差

      testResults.push({
        days,
        confirmedPrice: confirmed.totalPrice,
        provisionalPrice: provisional.totalPrice,
        expectedProvisional,
        isCorrect,
        difference,
      });

      console.log(`${days}日: 正規=${confirmed.totalPrice}円, 仮予約=${provisional.totalPrice}円, 期待=${expectedProvisional}円, 差=${difference}円`);

      if (!isCorrect) {
        recommendations.push(`${days}日の仮予約料金に${difference}円の誤差があります`);
      }
    }

    const isValid = testResults.every(result => result.isCorrect);

    if (!isValid) {
      recommendations.push('仮予約料金計算ロジックの見直しを推奨します');
    }

    console.log('仮予約料金整合性チェック結果:', isValid ? '✅ 正常' : '❌ 問題あり');
    console.log('=== 仮予約料金整合性チェック終了 ===');

    return {
      isValid,
      testResults,
      recommendations,
    };
  }

  /**
   * 整合性レポートを生成
   */
  public generateValidationReport(
    productTitle: string,
    basePrice: number,
    variantPrices: VariantPrice[],
    config?: Partial<PricingConfig>
  ): string {
    const validation = this.validatePricing(basePrice, variantPrices, config);

    const lines = [
      `=== 料金整合性レポート ===`,
      `商品: ${productTitle}`,
      `基本価格: ${this.pricingService.formatPrice(basePrice)}`,
      `チェック日時: ${new Date().toLocaleString('ja-JP')}`,
      '',
      `整合性: ${validation.isValid ? '✅ 正常' : '❌ 問題あり'}`,
      ''
    ];

    if (validation.discrepancies.length > 0) {
      lines.push('価格不整合:');
      validation.discrepancies.forEach(disc => {
        lines.push(`  ${disc.days}日: バリエーション=${this.pricingService.formatPrice(disc.variantPrice)} vs 計算=${this.pricingService.formatPrice(disc.calculatedPrice)} (差額: ${this.pricingService.formatPrice(disc.difference)})`);
      });
      lines.push('');
    }

    if (validation.missingVariants.length > 0) {
      lines.push(`不足バリエーション: ${validation.missingVariants.join(', ')}日`);
      lines.push('');
    }

    if (validation.recommendations.length > 0) {
      lines.push('推奨事項:');
      validation.recommendations.forEach(rec => {
        lines.push(`  - ${rec}`);
      });
    }

    return lines.join('\n');
  }
}
