import { json, type LoaderFunctionArgs } from '@remix-run/node';
import { useLoaderData, useNavigate, useSearchParams } from '@remix-run/react';
import {
  Page,
  Card,
  Text,
  Button,
  TextField,
  Layout,
  Modal,
  FormLayout,
  DataTable,
  ButtonGroup,
  Box,
  InlineStack,
  BlockStack,
  Divider
} from '@shopify/polaris';
import { useState, useCallback } from 'react';
import { authenticate } from '../shopify.server';
import { prisma } from '../db.server';

// 返却ステータスに応じたバッジ設定
const returnStatusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  pending: { tone: 'info', label: '返却待ち' },
  completed: { tone: 'success', label: '返却完了' },
  delayed: { tone: 'critical', label: '返却遅延' },
  amount_pending: { tone: 'attention', label: '金額確定待ち' },
  amount_confirmed: { tone: 'success', label: '金額確定済み' }
};

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);

  // クエリパラメータを取得
  const url = new URL(request.url);
  const returnDate = url.searchParams.get('returnDate') || undefined;
  const query = url.searchParams.get('query') || undefined;

  // 基本検索条件
  const baseWhere: any = {
    deliveryType: 'pickup' // 集荷のみ（配送は返却処理不要）
  };

  if (returnDate) {
    const date = new Date(returnDate);
    const nextDay = new Date(date);
    nextDay.setDate(nextDay.getDate() + 1);

    baseWhere.deliveryDate = {
      gte: date,
      lt: nextDay
    };
  }

  if (query) {
    baseWhere.OR = [
      { orderId: { contains: query } },
      {
        booking: {
          OR: [
            { bookingId: { contains: query } },
            { customerName: { contains: query, mode: 'insensitive' } },
            { customerCompany: { contains: query, mode: 'insensitive' } },
            {
              product: {
                title: { contains: query, mode: 'insensitive' }
              }
            },
            {
              product: {
                sku: { contains: query }
              }
            }
          ]
        }
      }
    ];
  }

  // 3つのセクション用のデータを取得

  // 1. 返却予定一覧
  const scheduledReturns = await prisma.deliverySchedule.findMany({
    where: { ...baseWhere, returnStatus: 'pending' },
    include: {
      booking: {
        include: {
          product: true
        }
      },
      staff: true,
      carrier: true
    },
    orderBy: [
      { deliveryDate: 'asc' },
      { timeFrom: 'asc' }
    ],
    take: 50
  });

  // 2. 返却一覧
  const completedReturns = await prisma.deliverySchedule.findMany({
    where: { ...baseWhere, returnStatus: 'completed' },
    include: {
      booking: {
        include: {
          product: true
        }
      },
      staff: true,
      carrier: true
    },
    orderBy: [
      { actualReturnDate: 'desc' },
      { deliveryDate: 'asc' }
    ],
    take: 50
  });

  // 3. 金額確定待ち一覧
  const amountPendingReturns = await prisma.deliverySchedule.findMany({
    where: { ...baseWhere, returnStatus: 'amount_pending' },
    include: {
      booking: {
        include: {
          product: true
        }
      },
      staff: true,
      carrier: true
    },
    orderBy: [
      { actualReturnDate: 'desc' },
      { deliveryDate: 'asc' }
    ],
    take: 50
  });

  // データ変換用のヘルパー関数
  const mapScheduleData = (schedule: any) => ({
    id: schedule.id,
    bookingId: schedule.booking?.bookingId || '',
    customerNo: '0000050007', // 仮の顧客No
    customerName: schedule.booking?.customerName || '',
    customerCompany: schedule.booking?.customerCompany || '',
    department: schedule.booking?.customerName || '', // 部署名（仮）
    deliveryDate: schedule.deliveryDate.toLocaleDateString('ja-JP'),
    returnDate: schedule.actualReturnDate?.toLocaleDateString('ja-JP') || '',
    orderId: schedule.orderId || ''
  });

  return json({
    scheduledReturns: scheduledReturns.map(mapScheduleData),
    completedReturns: completedReturns.map(mapScheduleData),
    amountPendingReturns: amountPendingReturns.map(mapScheduleData)
  });
}

export default function DeliveryReturns() {
  const { scheduledReturns, completedReturns, amountPendingReturns } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // フィルター状態
  const [returnDate, setReturnDate] = useState(searchParams.get('returnDate') || '');
  const [searchQuery, setSearchQuery] = useState(searchParams.get('query') || '');

  // 操作状態
  const [selectedSchedules, setSelectedSchedules] = useState<string[]>([]);
  const [showReturnModal, setShowReturnModal] = useState(false);
  const [showAmountModal, setShowAmountModal] = useState(false);

  // 今日の日付をデフォルトに設定
  const today = new Date().toISOString().split('T')[0];

  // 検索実行
  const handleSearch = useCallback(() => {
    const params = new URLSearchParams();

    if (returnDate) params.set('returnDate', returnDate);
    if (searchQuery) params.set('query', searchQuery);

    navigate(`/app/delivery/returns?${params.toString()}`);
  }, [returnDate, searchQuery, navigate]);

  // フィルタークリア
  const handleClearFilters = useCallback(() => {
    setReturnDate('');
    setSearchQuery('');
    navigate('/app/delivery/returns');
  }, [navigate]);

  // CSV出力
  const handleCsvOutput = useCallback(() => {
    console.log('CSV出力');
  }, []);

  // CSV出力（調整）
  const handleCsvOutputAdjusted = useCallback(() => {
    console.log('CSV出力（調整）');
  }, []);

  // 選択処理
  const handleSelection = useCallback(() => {
    console.log('選択処理');
  }, []);

  // データテーブル用のデータ準備
  const scheduledRows = scheduledReturns.map(item => [
    item.bookingId,
    item.customerNo,
    item.customerName,
    item.customerCompany,
    item.department,
    item.deliveryDate,
    item.returnDate
  ]);

  const completedRows = completedReturns.map(item => [
    item.bookingId,
    item.customerNo,
    item.customerName,
    item.customerCompany,
    item.department,
    item.deliveryDate,
    item.returnDate
  ]);

  const amountPendingRows = amountPendingReturns.map(item => [
    item.bookingId,
    item.customerNo,
    item.customerName,
    item.customerCompany,
    item.department,
    item.deliveryDate,
    item.returnDate
  ]);

  return (
    <Page title="【新】返却・金額確定処理 - 更新済み">
      <Layout>
        {/* 返却伝票検索 */}
        <Layout.Section>
          <Card>
            <Box padding="400" background="bg-surface-info">
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd" tone="subdued">返却伝票検索</Text>
                <InlineStack gap="400" align="start">
                  <Box minWidth="200px">
                    <TextField
                      label="返却予定日"
                      type="date"
                      value={returnDate}
                      onChange={setReturnDate}
                      placeholder={today}
                    />
                  </Box>
                  <Box minWidth="200px">
                    <TextField
                      label="伝票No."
                      value={searchQuery}
                      onChange={setSearchQuery}
                      placeholder="完全一致で検索"
                    />
                  </Box>
                  <Box paddingBlockStart="600">
                    <ButtonGroup>
                      <Button onClick={handleSearch}>検索</Button>
                      <Button onClick={handleClearFilters}>クリア</Button>
                    </ButtonGroup>
                  </Box>
                </InlineStack>
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>

        {/* 返却予定一覧 */}
        <Layout.Section>
          <Card>
            <Box padding="400" background="bg-surface-info">
              <Text as="h2" variant="headingMd" tone="subdued">返却予定一覧</Text>
            </Box>
            <Box padding="400">
              <DataTable
                columnContentTypes={['text', 'text', 'text', 'text', 'text', 'text', 'text']}
                headings={['伝票No.', '顧客No.', '顧客名', '会社名', '部署名', '貸出開始日', '返却予定日']}
                rows={scheduledRows}
                truncate
              />
            </Box>
          </Card>
        </Layout.Section>

        {/* 返却一覧 */}
        <Layout.Section>
          <Card>
            <Box padding="400" background="bg-surface-info">
              <Text as="h2" variant="headingMd" tone="subdued">返却一覧</Text>
            </Box>
            <Box padding="400">
              <DataTable
                columnContentTypes={['text', 'text', 'text', 'text', 'text', 'text', 'text']}
                headings={['伝票No.', '顧客No.', '顧客名', '会社名', '部署名', '貸出開始日', '返却予定日']}
                rows={completedRows}
                truncate
              />
            </Box>
          </Card>
        </Layout.Section>

        {/* 金額確定待ち一覧 */}
        <Layout.Section>
          <Card>
            <Box padding="400" background="bg-surface-info">
              <Text as="h2" variant="headingMd" tone="subdued">金額確定待ち一覧</Text>
            </Box>
            <Box padding="400">
              <DataTable
                columnContentTypes={['text', 'text', 'text', 'text', 'text', 'text', 'text']}
                headings={['伝票No.', '顧客No.', '顧客名', '会社名', '部署名', '貸出開始日', '返却予定日']}
                rows={amountPendingRows}
                truncate
              />
            </Box>
            <Box padding="400" borderBlockStartWidth="025" borderColor="border">
              <InlineStack gap="400" align="end">
                <Button onClick={handleCsvOutput} variant="primary">CSV出力</Button>
                <Button onClick={handleCsvOutputAdjusted}>CSV出力(調整)</Button>
                <Button onClick={handleSelection}>選択</Button>
              </InlineStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>

    </Page>
  );
}
