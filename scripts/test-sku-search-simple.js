/**
 * SKU検索ロジックテスト（JavaScript版）
 */

// SKU検索条件を生成する関数
function buildSkuSearchConditions(searchQuery) {
  if (!searchQuery || searchQuery.trim() === '') {
    return [];
  }

  const trimmedQuery = searchQuery.trim();
  const conditions = [];

  // 1. 完全一致検索
  conditions.push({
    sku: { equals: trimmedQuery, mode: 'insensitive' }
  });

  // 2. 部分一致検索
  conditions.push({
    sku: { contains: trimmedQuery, mode: 'insensitive' }
  });

  // 3. 基本SKU形式の検索（201-07-107 形式）
  if (isBasicSkuFormat(trimmedQuery)) {
    conditions.push({
      sku: { startsWith: trimmedQuery + '-', mode: 'insensitive' }
    });
  }

  // 4. ハイフンなし形式の検索
  if (isNumericOnly(trimmedQuery) && trimmedQuery.length >= 8) {
    const formattedSku = formatNumericToSku(trimmedQuery);
    if (formattedSku) {
      conditions.push({
        sku: { equals: formattedSku, mode: 'insensitive' }
      });
      conditions.push({
        sku: { startsWith: formattedSku + '-', mode: 'insensitive' }
      });
    }
  }

  // 5. 前方10文字での検索
  if (trimmedQuery.length > 10) {
    const prefix = trimmedQuery.substring(0, 10);
    if (isBasicSkuFormat(prefix)) {
      conditions.push({
        sku: { startsWith: prefix, mode: 'insensitive' }
      });
    }
  }

  return conditions;
}

function isBasicSkuFormat(sku) {
  const basicSkuPattern = /^\d{3}-\d{2}-\d{3}$/;
  return basicSkuPattern.test(sku);
}

function isNumericOnly(str) {
  return /^\d+$/.test(str);
}

function formatNumericToSku(numericStr) {
  if (numericStr.length === 8) {
    return `${numericStr.substring(0, 3)}-${numericStr.substring(3, 5)}-${numericStr.substring(5, 8)}`;
  }
  
  if (numericStr.length >= 8) {
    const first8 = numericStr.substring(0, 8);
    return `${first8.substring(0, 3)}-${first8.substring(3, 5)}-${first8.substring(5, 8)}`;
  }
  
  return null;
}

// テスト実行
console.log('🧪 SKU検索ロジックテストを開始...\n');

const testCases = [
  {
    input: '201-07-107',
    description: '基本SKU形式',
    expectedMatches: ['201-07-107', '201-07-107-1D', '201-07-107-bk', '201-07-107-001']
  },
  {
    input: '20107107',
    description: 'ハイフンなし数字',
    expectedMatches: ['201-07-107', '201-07-107-1D', '201-07-107-bk']
  },
  {
    input: '201-07-107-1D',
    description: 'バリアント付きSKU',
    expectedMatches: ['201-07-107-1D']
  },
  {
    input: '201',
    description: '部分SKU',
    expectedMatches: ['201-07-107', '201-06-555', '201-08-999']
  }
];

testCases.forEach((testCase, index) => {
  console.log(`${index + 1}. ${testCase.description}: "${testCase.input}"`);
  
  const conditions = buildSkuSearchConditions(testCase.input);
  console.log(`   生成される検索条件: ${conditions.length}件`);
  
  conditions.forEach((condition, condIndex) => {
    const skuCondition = condition.sku;
    if (skuCondition.equals) {
      console.log(`     ${condIndex + 1}. 完全一致: "${skuCondition.equals}"`);
    } else if (skuCondition.contains) {
      console.log(`     ${condIndex + 1}. 部分一致: "*${skuCondition.contains}*"`);
    } else if (skuCondition.startsWith) {
      console.log(`     ${condIndex + 1}. 前方一致: "${skuCondition.startsWith}*"`);
    }
  });
  
  console.log(`   期待されるマッチ: ${testCase.expectedMatches.join(', ')}`);
  console.log('');
});

// 実際のデータベース商品でテスト
console.log('=== 実際の商品データでのテスト ===');

// 実際のSKU例
const actualSkus = [
  '201-07-107-1D',
  '201-07-107-2D', 
  '201-07-107-bk',
  '212-05-023-1D',
  '201-06-555-1D'
];

console.log('データベース内のSKU例:');
actualSkus.forEach(sku => {
  console.log(`  - ${sku}`);
});
console.log('');

const searchTests = ['201-07-107', '20107107', '201', '107'];

searchTests.forEach(searchQuery => {
  console.log(`検索: "${searchQuery}"`);
  
  const conditions = buildSkuSearchConditions(searchQuery);
  const matchedSkus = [];
  
  // 各条件で実際のSKUとマッチするかテスト
  actualSkus.forEach(sku => {
    conditions.forEach(condition => {
      const skuCondition = condition.sku;
      let matches = false;
      
      if (skuCondition.equals && sku.toLowerCase() === skuCondition.equals.toLowerCase()) {
        matches = true;
      } else if (skuCondition.contains && sku.toLowerCase().includes(skuCondition.contains.toLowerCase())) {
        matches = true;
      } else if (skuCondition.startsWith && sku.toLowerCase().startsWith(skuCondition.startsWith.toLowerCase())) {
        matches = true;
      }
      
      if (matches && !matchedSkus.includes(sku)) {
        matchedSkus.push(sku);
      }
    });
  });
  
  console.log(`  マッチするSKU: ${matchedSkus.join(', ') || 'なし'}`);
  console.log('');
});

console.log('✅ SKU検索ロジックテスト完了');
