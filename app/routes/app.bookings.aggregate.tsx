import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import {
  Page, Card, Tabs, TextField, Select, Checkbox, Button, DataTable,
  BlockStack, Collapsible, InlineStack, Text
} from '@shopify/polaris';
import { useState, useCallback } from 'react';
import { prisma } from '../db.server';
import { formatLocalDate } from '../utils/date/date-utils';

export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  // クエリパラメータを取得
  const url = new URL(request.url);
  const startDate = url.searchParams.get('startDate') || undefined;
  const endDate = url.searchParams.get('endDate') || undefined;
  const productCode = url.searchParams.get('productCode') || undefined;
  const category = url.searchParams.get('category') || undefined;
  const tag = url.searchParams.get('tag') || undefined;
  const note = url.searchParams.get('note') || undefined;
  const onlyAvailable = url.searchParams.get('onlyAvailable') === 'true';

  try {
    // 検索条件を構築
    const productWhere: any = { shop };

    if (productCode) {
      productWhere.sku = { contains: productCode, mode: 'insensitive' };
    }

    if (category) {
      // カテゴリはbasicInfoのJSONフィールドに格納されているため、JSONクエリを使用
      productWhere.basicInfo = {
        path: ['category'],
        equals: category
      };
    }

    if (tag || note) {
      const orConditions = [];

      if (tag) {
        // JSONフィールドのタグ検索
        orConditions.push({
          basicInfo: {
            path: ['tags'],
            array_contains: tag
          }
        });
      }

      if (note) {
        orConditions.push({
          description: {
            contains: note,
            mode: 'insensitive'
          }
        });
      }

      if (orConditions.length > 0) {
        productWhere.OR = orConditions;
      }
    }

    // 商品データを取得
    const products = await prisma.product.findMany({
      where: productWhere,
      include: {
        bookings: startDate && endDate ? {
          where: {
            OR: [
              {
                startDate: { lte: new Date(endDate) },
                endDate: { gte: new Date(startDate) }
              }
            ]
          }
        } : true
      }
    });

    // 商品からbasicInfoのカテゴリを抽出
    const products_with_info = await prisma.product.findMany({
      where: {
        shop
      },
      select: {
        basicInfo: true
      }
    });

    // basicInfoからカテゴリを抽出
    const categories = new Set<string>();
    products_with_info.forEach(product => {
      if (product.basicInfo && typeof product.basicInfo === 'object') {
        const basicInfo = product.basicInfo as any;
        if (basicInfo.category) {
          categories.add(basicInfo.category);
        }
      }
    });

    // 利用可能な商品のみをフィルタリング
    let filteredProducts = products;

    if (onlyAvailable && startDate && endDate) {
      filteredProducts = products.filter(product => {
        // 指定期間に予約がない商品のみを表示
        return !product.bookings.some(booking =>
          booking.status === 'CONFIRMED' || booking.status === 'COMPLETED'
        );
      });
    }

    // 商品データにカテゴリ情報を追加
    const productsWithCategory = filteredProducts.map(product => {
      let category = '';
      if (product.basicInfo && typeof product.basicInfo === 'object') {
        const basicInfo = product.basicInfo as any;
        if (basicInfo.category) {
          category = basicInfo.category;
        }
      }

      return {
        id: product.id,
        sku: product.sku,
        title: product.title,
        category: category,
        status: product.status,
        bookings: product.bookings.map(booking => ({
          id: booking.id,
          startDate: booking.startDate.toISOString(),
          endDate: booking.endDate.toISOString(),
          status: booking.status
        }))
      };
    });

    return json({
      products: productsWithCategory,
      categories: Array.from(categories),
      searchParams: {
        startDate,
        endDate,
        productCode,
        category,
        tag,
        note,
        onlyAvailable
      }
    });
  } catch (error) {
    console.error('検索エラー:', error);
    return json({
      products: [],
      categories: [],
      searchParams: {
        startDate,
        endDate,
        productCode,
        category,
        tag,
        note,
        onlyAvailable
      },
      error: '検索中にエラーが発生しました'
    } as const);
  }
}

export default function BookingAggregate() {
  const data = useLoaderData<typeof loader>();
  const products = data.products || [];
  const categories = data.categories || [];
  const searchParams = data.searchParams || {};
  const error = 'error' in data ? data.error : undefined;
  const navigate = useNavigate();

  // タブ状態
  const [selectedTab, setSelectedTab] = useState(0);
  const handleTabChange = useCallback(
    (selectedTabIndex: number) => setSelectedTab(selectedTabIndex),
    [],
  );

  // 検索条件パネルの開閉状態
  const [searchOpen, setSearchOpen] = useState(true);
  const toggleSearchOpen = useCallback(() => setSearchOpen((open) => !open), []);

  // 検索条件の状態管理
  const [startDate, setStartDate] = useState(
    searchParams.startDate ? searchParams.startDate.toString().split('T')[0] : new Date().toISOString().split('T')[0]
  );
  const [endDate, setEndDate] = useState(
    searchParams.endDate ? searchParams.endDate.toString().split('T')[0] : new Date().toISOString().split('T')[0]
  );

  // 日付範囲は文字列として管理
  const [productCode, setProductCode] = useState(searchParams.productCode || '');
  const [category, setCategory] = useState(searchParams.category || '');
  const [tag, setTag] = useState(searchParams.tag || '');
  const [note, setNote] = useState(searchParams.note || '');
  const [onlyAvailable, setOnlyAvailable] = useState(searchParams.onlyAvailable === true);

  // カテゴリ選択肢の生成
  const categoryOptions = [
    {label: '--', value: ''},
    ...categories.map(c => ({ label: c || '', value: c || '' }))
  ];

  // 検索結果の行データを生成
  const rows = products.map(product => {
    // nullチェックを追加
    if (!product) return ['', '', '', '', ''];

    return [
      product.sku || '',
      product.title || '',
      product.category || '',
      product.status === 'AVAILABLE' ? '利用可' :
      product.status === 'MAINTENANCE' ? 'メンテナンス中' :
      product.status === 'DAMAGED' ? '破損' :
      product.status === 'UNAVAILABLE' ? '利用不可' : '利用可',
      product.bookings && product.bookings.length > 0 ?
        product.bookings.map(b =>
          `${formatLocalDate(b.startDate)} 〜 ${formatLocalDate(b.endDate)} (${b.status === 'CONFIRMED' ? '確定' : b.status === 'PROVISIONAL' ? '仮予約' : b.status})`
        ).join(', ') : '予約なし'
    ];
  });

  const handleSearch = () => {
    // 検索条件をURLパラメータに変換
    const params = new URLSearchParams();

    params.set('startDate', startDate);
    params.set('endDate', endDate);

    if (productCode) {
      params.set('productCode', productCode);
    }

    if (category) {
      params.set('category', category);
    }

    if (tag) {
      params.set('tag', tag);
    }

    if (note) {
      params.set('note', note);
    }

    if (onlyAvailable) {
      params.set('onlyAvailable', 'true');
    }

    navigate(`/app/bookings/aggregate?${params.toString()}`);
  };

  const handleClear = () => {
    setStartDate(new Date().toISOString().split('T')[0]);
    setEndDate(new Date().toISOString().split('T')[0]);
    setProductCode('');
    setCategory('');
    setTag('');
    setNote('');
    setOnlyAvailable(false);
    navigate('/app/bookings/aggregate');
  };

  const tabs = [
    {id: 'category-search', content: 'カテゴリ検索', accessibilityLabel: 'カテゴリ検索', panelID: 'category-search-content'},
    {id: 'product-code-search', content: '商品コード検索', accessibilityLabel: '商品コード検索', panelID: 'product-code-search-content'},
  ];

  return (
    <Page
      title="予約状況一括照会"
      subtitle="レンタル商品の予約状況を一括で確認できます"
      backAction={{ content: 'ホーム', url: '/app' }}
      secondaryActions={[
        {
          content: searchOpen ? '検索条件を閉じる' : '検索条件を開く',
          onAction: toggleSearchOpen,
        },
      ]}
    >
      <BlockStack gap="400">
        {error && (
          <div style={{color: 'red', padding: '16px'}}>
            {error}
          </div>
        )}

        <Card>
          <Tabs tabs={tabs} selected={selectedTab} onSelect={handleTabChange} fitted>
            <BlockStack gap="400">
              <Collapsible
                open={searchOpen}
                id="search-collapsible"
                transition={{duration: '300ms', timingFunction: 'ease-in-out'}}
              >
                <div style={{marginTop: '16px', padding: '16px'}}>
                  <BlockStack gap="400">
                    <InlineStack gap="300" wrap={false}>
                      <div style={{ flex: 1 }}>
                        <TextField
                          label="開始日"
                          type="date"
                          value={startDate}
                          onChange={(value) => setStartDate(value)}
                          autoComplete="off"
                        />
                      </div>
                      <div style={{ marginTop: '24px', padding: '0 8px' }}>
                        <Text as="span">〜</Text>
                      </div>
                      <div style={{ flex: 1 }}>
                        <TextField
                          label="終了日"
                          type="date"
                          value={endDate}
                          onChange={(value) => setEndDate(value)}
                          autoComplete="off"
                        />
                      </div>
                    </InlineStack>
                    <TextField
                      label="商品コード"
                      value={productCode}
                      onChange={setProductCode}
                      placeholder="例: CAMERA-001"
                      disabled={selectedTab === 0}
                      autoComplete="off"
                    />
                    {selectedTab === 0 && (
                      <Select
                        label="カテゴリ"
                        options={categoryOptions}
                        onChange={setCategory}
                        value={category}
                      />
                    )}
                    <TextField
                      label="タグ"
                      value={tag}
                      onChange={setTag}
                      placeholder="例: 初心者向け"
                      autoComplete="off"
                    />
                    <TextField
                      label="備考"
                      value={note}
                      onChange={setNote}
                      placeholder="例: 特記事項あり"
                      multiline={3}
                      autoComplete="off"
                    />
                    <Checkbox
                      label="貸出可能なもののみ表示する"
                      checked={onlyAvailable}
                      onChange={(newChecked) => setOnlyAvailable(newChecked)}
                    />
                    <div style={{display: 'flex', justifyContent: 'flex-end', gap: '8px'}}>
                      <Button onClick={handleClear}>クリア</Button>
                      <Button onClick={handleSearch} variant="primary">検索</Button>
                    </div>
                  </BlockStack>
                </div>
              </Collapsible>
            </BlockStack>
          </Tabs>
          <DataTable
            columnContentTypes={[
              'text',
              'text',
              'text',
              'text',
              'text'
            ]}
            headings={[
              '商品コード',
              '商品名',
              'カテゴリ',
              '貸出可否',
              '予約期間'
            ]}
            rows={rows}
            footerContent={`全 ${rows.length} 件`}
          />
        </Card>
      </BlockStack>
    </Page>
  );
}
