/**
 * レンタル商品カレンダー用CSS
 */

.rental-calendar {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background-color: #fff;
}

/* ローディング表示 */
.rental-calendar__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.rental-calendar__loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* エラーメッセージ */
.rental-calendar__error {
  padding: 10px;
  margin-bottom: 15px;
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  color: #c62828;
}

/* 日付選択 */
.rental-calendar__date-selection {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.rental-calendar__date-field {
  flex: 1;
  min-width: 200px;
}

.rental-calendar__date-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.rental-calendar__date-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

/* カレンダー表示 */
.rental-calendar__calendar-container {
  margin-bottom: 20px;
}

/* 情報表示 */
.rental-calendar__info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.rental-calendar__rental-days,
.rental-calendar__price,
.rental-calendar__deposit {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.rental-calendar__rental-days:last-child,
.rental-calendar__price:last-child,
.rental-calendar__deposit:last-child {
  margin-bottom: 0;
}

/* 凡例 */
.rental-calendar__legend {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e8e8e8;
}

.rental-calendar__legend-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
}

.rental-calendar__legend-mark {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 5px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.rental-calendar__legend-mark--reserved {
  background-color: #ffebee;
  color: #d32f2f;
  border: 1px solid #ffcdd2;
}

.rental-calendar__legend-mark--provisional {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.rental-calendar__legend-mark--holiday {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
}

/* カレンダー日付セル */
.rental-calendar__date-cell {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.rental-calendar__date-cell:hover {
  background-color: #f5f5f5;
}

.rental-calendar__date-cell--selected {
  background-color: #e3f2fd;
  font-weight: bold;
}

.rental-calendar__date-cell--range {
  background-color: #e8f5e9;
}

.rental-calendar__date-cell--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.rental-calendar__date-cell--holiday {
  background-color: #f5f5f5;
}

.rental-calendar__date-cell--reserved::after,
.rental-calendar__date-cell--provisional::after {
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 10px;
  font-weight: bold;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.rental-calendar__date-cell--reserved::after {
  content: "予";
  background-color: #ffebee;
  color: #d32f2f;
}

.rental-calendar__date-cell--provisional::after {
  content: "仮";
  background-color: #e3f2fd;
  color: #1976d2;
}

/* レスポンシブ対応 */
@media (max-width: 768px) {
  .rental-calendar__date-selection {
    flex-direction: column;
  }
  
  .rental-calendar__legend {
    flex-direction: column;
    gap: 10px;
  }
}
