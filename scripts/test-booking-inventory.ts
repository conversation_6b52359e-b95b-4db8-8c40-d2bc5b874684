/**
 * 予約と在庫連携テストスクリプト
 *
 * このスクリプトは、予約作成・更新・キャンセル時の在庫カレンダー更新をテストします。
 * 実行方法: npx tsx scripts/test-booking-inventory.ts [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';
import { addDays, format } from 'date-fns';
import { ja } from 'date-fns/locale';
import { InventoryCalendarService } from '../app/services/inventory-calendar.service';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト設定
const config = {
  // テストする商品のShopify ID
  productId: process.argv[2] || '8597856903456', // コマンドライン引数から取得、または既定値を使用
  
  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
  
  // テスト予約の設定
  customerEmail: '<EMAIL>',
  customerName: 'テストユーザー',
  
  // 予約日数（今日から何日後に予約するか）
  startDaysFromNow: 7,
  durationDays: 3
};

/**
 * 商品情報を取得する関数
 */
async function getProductInfo(shopifyProductId: string) {
  try {
    // Prismaから商品情報を取得
    const product = await prisma.product.findFirst({
      where: { shopifyId: shopifyProductId }
    });
    
    if (!product) {
      console.error(`商品ID ${shopifyProductId} が見つかりません`);
      return null;
    }
    
    return product;
  } catch (error) {
    console.error('商品情報取得エラー:', error);
    return null;
  }
}

/**
 * 在庫カレンダー情報を表示する関数
 */
async function displayInventoryCalendar(productId: string, startDate: Date, endDate: Date) {
  try {
    // 在庫カレンダー情報を取得
    const inventoryCalendar = await prisma.inventoryCalendar.findMany({
      where: {
        productId,
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      orderBy: { date: 'asc' }
    });
    
    console.log('\n===== 在庫カレンダー情報 =====');
    if (inventoryCalendar.length === 0) {
      console.log('在庫カレンダー情報がありません');
    } else {
      for (const entry of inventoryCalendar) {
        const dateStr = format(new Date(entry.date), 'yyyy/MM/dd(E)', { locale: ja });
        const status = entry.isAvailable ? '○ 利用可能' : '× 利用不可';
        const reason = entry.unavailableReason ? `(${entry.unavailableReason})` : '';
        const bookingInfo = entry.bookingId ? `予約ID: ${entry.bookingId}` : '';
        console.log(`${dateStr}: ${status} ${reason} ${bookingInfo}`);
      }
    }
  } catch (error) {
    console.error('在庫カレンダー表示エラー:', error);
  }
}

/**
 * 予約を作成する関数
 */
async function createBooking(productId: string, startDate: Date, endDate: Date) {
  try {
    console.log(`\n予約を作成中... 商品ID: ${productId}`);
    console.log(`期間: ${format(startDate, 'yyyy/MM/dd')} 〜 ${format(endDate, 'yyyy/MM/dd')}`);
    
    // 予約データを作成
    const booking = await prisma.booking.create({
      data: {
        id: uuidv4(),
        bookingId: `TEST-${uuidv4().substring(0, 8).toUpperCase()}`,
        shop: config.shop,
        productId,
        variantId: '1', // テスト用の固定値
        startDate,
        endDate,
        customerName: config.customerName,
        customerEmail: config.customerEmail,
        bookingType: 'PROVISIONAL',
        status: 'PROVISIONAL',
        paymentStatus: 'PENDING',
        totalAmount: 10000,
        priority: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
    
    console.log('予約を作成しました:');
    console.log(`予約ID: ${booking.id}`);
    console.log(`予約番号: ${booking.bookingId}`);
    
    // 在庫カレンダーサービスを初期化
    const inventoryCalendarService = new InventoryCalendarService();
    
    // 予約に基づいて在庫カレンダーを更新
    await inventoryCalendarService.updateCalendarForBooking(booking);
    console.log('在庫カレンダーを更新しました');
    
    return booking;
  } catch (error) {
    console.error('予約作成エラー:', error);
    return null;
  }
}

/**
 * 予約をキャンセルする関数
 */
async function cancelBooking(bookingId: string) {
  try {
    console.log(`\n予約をキャンセル中... 予約ID: ${bookingId}`);
    
    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId }
    });
    
    if (!booking) {
      console.error(`予約ID ${bookingId} が見つかりません`);
      return false;
    }
    
    // 予約ステータスをキャンセルに更新
    const updatedBooking = await prisma.booking.update({
      where: { id: bookingId },
      data: {
        status: 'CANCELLED',
        updatedAt: new Date()
      }
    });
    
    console.log('予約をキャンセルしました');
    
    // 在庫カレンダーサービスを初期化
    const inventoryCalendarService = new InventoryCalendarService();
    
    // 予約に基づいて在庫カレンダーを更新
    await inventoryCalendarService.updateCalendarForBooking(updatedBooking);
    console.log('在庫カレンダーを更新しました');
    
    return true;
  } catch (error) {
    console.error('予約キャンセルエラー:', error);
    return false;
  }
}

/**
 * 予約日程を変更する関数
 */
async function updateBookingDates(bookingId: string, newStartDate: Date, newEndDate: Date) {
  try {
    console.log(`\n予約日程を変更中... 予約ID: ${bookingId}`);
    console.log(`新しい期間: ${format(newStartDate, 'yyyy/MM/dd')} 〜 ${format(newEndDate, 'yyyy/MM/dd')}`);
    
    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId }
    });
    
    if (!booking) {
      console.error(`予約ID ${bookingId} が見つかりません`);
      return false;
    }
    
    // 予約日程を更新
    const updatedBooking = await prisma.booking.update({
      where: { id: bookingId },
      data: {
        startDate: newStartDate,
        endDate: newEndDate,
        updatedAt: new Date()
      }
    });
    
    console.log('予約日程を変更しました');
    
    // 在庫カレンダーサービスを初期化
    const inventoryCalendarService = new InventoryCalendarService();
    
    // 予約に基づいて在庫カレンダーを更新
    await inventoryCalendarService.updateCalendarForBooking(updatedBooking);
    console.log('在庫カレンダーを更新しました');
    
    return true;
  } catch (error) {
    console.error('予約日程変更エラー:', error);
    return false;
  }
}

/**
 * 予約と在庫連携をテストする関数
 */
async function testBookingInventoryIntegration(shopifyProductId: string) {
  try {
    console.log(`商品ID ${shopifyProductId} の予約と在庫連携テストを実行します...`);
    
    // 商品情報を取得
    const product = await getProductInfo(shopifyProductId);
    
    if (!product) {
      return false;
    }
    
    // テスト用の日程を設定
    const today = new Date();
    const startDate = addDays(today, config.startDaysFromNow);
    const endDate = addDays(startDate, config.durationDays - 1);
    const displayEndDate = addDays(today, config.startDaysFromNow + config.durationDays + 3);
    
    // テスト前の在庫カレンダー情報を表示
    console.log('\n----- テスト前の在庫カレンダー情報 -----');
    await displayInventoryCalendar(product.id, today, displayEndDate);
    
    // テスト1: 予約作成
    console.log('\n----- テスト1: 予約作成 -----');
    const booking = await createBooking(product.id, startDate, endDate);
    
    if (!booking) {
      console.error('予約作成に失敗しました');
      return false;
    }
    
    // 予約作成後の在庫カレンダー情報を表示
    console.log('\n----- 予約作成後の在庫カレンダー情報 -----');
    await displayInventoryCalendar(product.id, today, displayEndDate);
    
    // テスト2: 予約日程変更
    console.log('\n----- テスト2: 予約日程変更 -----');
    const newStartDate = addDays(startDate, 5);
    const newEndDate = addDays(newStartDate, config.durationDays - 1);
    
    const updateResult = await updateBookingDates(booking.id, newStartDate, newEndDate);
    
    if (!updateResult) {
      console.error('予約日程変更に失敗しました');
      return false;
    }
    
    // 予約日程変更後の在庫カレンダー情報を表示
    console.log('\n----- 予約日程変更後の在庫カレンダー情報 -----');
    await displayInventoryCalendar(product.id, today, displayEndDate);
    
    // テスト3: 予約キャンセル
    console.log('\n----- テスト3: 予約キャンセル -----');
    const cancelResult = await cancelBooking(booking.id);
    
    if (!cancelResult) {
      console.error('予約キャンセルに失敗しました');
      return false;
    }
    
    // 予約キャンセル後の在庫カレンダー情報を表示
    console.log('\n----- 予約キャンセル後の在庫カレンダー情報 -----');
    await displayInventoryCalendar(product.id, today, displayEndDate);
    
    return true;
  } catch (error) {
    console.error('予約と在庫連携テストエラー:', error);
    return false;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('予約と在庫連携テストを開始します...');
    
    // コマンドライン引数から商品IDを取得
    const shopifyProductId = config.productId;
    
    // 予約と在庫連携をテスト
    const testResult = await testBookingInventoryIntegration(shopifyProductId);
    
    // テスト結果のサマリー
    console.log('\n===== テスト結果サマリー =====');
    console.log(`予約と在庫連携テスト: ${testResult ? '成功' : '失敗'}`);
    
    if (testResult) {
      console.log('\nテストが成功しました！');
    } else {
      console.error('\nテストが失敗しました。');
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
