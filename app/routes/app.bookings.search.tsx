import { LoaderFunction<PERSON>rgs, json, ActionFunctionArgs } from '@remix-run/node';
import { useLoaderData, useActionData, Form } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import {
  Page,
  Card,
  Layout,
  TextField,
  Button,
  Select,
  DatePicker,
  Checkbox,
  Banner,
  Text,
  BlockStack,
  Box,
  InlineStack
} from '@shopify/polaris';
import { useState, useCallback } from 'react';
import { prisma } from '../db.server';
import { formatLocalDate } from '../utils/date/date-utils';

export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  
  // カテゴリ一覧を取得
  const products = await prisma.product.findMany({
    where: { shop },
    select: { basicInfo: true }
  });
  
  // basicInfoからカテゴリを抽出
  const categories = new Set<string>();
  products.forEach(product => {
    if (product.basicInfo && typeof product.basicInfo === 'object') {
      const basicInfo = product.basicInfo as any;
      if (basicInfo.category) {
        categories.add(basicInfo.category);
      }
    }
  });
  
  return json({
    shop,
    categories: Array.from(categories),
    message: '予約状況検索'
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  
  // フォームデータを取得
  const formData = await request.formData();
  const productCode = formData.get('productCode')?.toString() || '';
  const category = formData.get('category')?.toString() || '';
  const startDate = formData.get('startDate')?.toString() || '';
  const endDate = formData.get('endDate')?.toString() || '';
  const onlyAvailable = formData.get('onlyAvailable') === 'true';
  
  try {
    // 検索条件を構築
    const productWhere: any = { shop };
    
    if (productCode) {
      productWhere.sku = { contains: productCode, mode: 'insensitive' };
    }
    
    if (category && category !== '') {
      // カテゴリはbasicInfoのJSONフィールドに格納されているため、JSONクエリを使用
      productWhere.basicInfo = {
        path: ['category'],
        equals: category
      };
    }
    
    // 商品データを取得
    const products = await prisma.product.findMany({
      where: productWhere,
      include: {
        bookings: startDate && endDate ? {
          where: {
            OR: [
              {
                startDate: { lte: new Date(endDate) },
                endDate: { gte: new Date(startDate) }
              }
            ]
          }
        } : true
      }
    });
    
    // 利用可能な商品のみをフィルタリング
    let filteredProducts = products;
    
    if (onlyAvailable && startDate && endDate) {
      filteredProducts = products.filter(product => {
        // 指定期間に予約がない商品のみを表示
        return !product.bookings.some(booking => 
          booking.status === 'CONFIRMED' || booking.status === 'COMPLETED'
        );
      });
    }
    
    // 商品データにカテゴリ情報を追加
    const productsWithCategory = filteredProducts.map(product => {
      let category = '';
      if (product.basicInfo && typeof product.basicInfo === 'object') {
        const basicInfo = product.basicInfo as any;
        if (basicInfo.category) {
          category = basicInfo.category;
        }
      }
      
      return {
        id: product.id,
        sku: product.sku,
        title: product.title,
        category: category,
        status: product.status,
        bookings: product.bookings.map(booking => ({
          id: booking.id,
          startDate: booking.startDate.toISOString(),
          endDate: booking.endDate.toISOString(),
          status: booking.status
        }))
      };
    });
    
    return json({
      success: true,
      products: productsWithCategory,
      searchParams: {
        productCode,
        category,
        startDate,
        endDate,
        onlyAvailable
      }
    });
  } catch (error) {
    console.error('検索エラー:', error);
    return json({
      success: false,
      error: '検索中にエラーが発生しました',
      searchParams: {
        productCode,
        category,
        startDate,
        endDate,
        onlyAvailable
      }
    });
  }
}

export default function BookingSearch() {
  const { categories, message } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  
  // 状態管理
  const [productCode, setProductCode] = useState('');
  const [category, setCategory] = useState('');
  const [startDate, setStartDate] = useState(new Date().toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0]);
  const [onlyAvailable, setOnlyAvailable] = useState(false);
  const [dateRange, setDateRange] = useState({
    start: new Date(),
    end: new Date()
  });
  
  // 日付選択の処理
  const handleDateChange = useCallback((range: {start: Date, end: Date}) => {
    setDateRange(range);
    setStartDate(range.start.toISOString().split('T')[0]);
    setEndDate(range.end.toISOString().split('T')[0]);
  }, []);
  
  // カテゴリ選択肢の生成
  const categoryOptions = [
    {label: '--', value: ''},
    ...categories.map(c => ({ label: c || '', value: c || '' }))
  ];
  
  return (
    <Page
      title="予約状況検索"
      subtitle="レンタル商品の予約状況を検索できます"
      backAction={{ content: 'ホーム', url: '/app' }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Card.Section>
              <BlockStack gap="400">
                {actionData?.error && (
                  <Banner title="エラー" tone="critical">
                    <p>{actionData.error}</p>
                  </Banner>
                )}
                
                {actionData?.success && (
                  <Banner title="検索結果" tone="success">
                    <p>{actionData.products.length}件の商品が見つかりました</p>
                  </Banner>
                )}
                
                <Form method="post">
                  <BlockStack gap="400">
                    <Box padding="400">
                      <BlockStack gap="400">
                        <Text as="h2" variant="headingMd">検索条件</Text>
                        
                        <InlineStack gap="400" align="start" blockAlign="center">
                          <div style={{ flex: 1 }}>
                            <DatePicker
                              month={dateRange.start.getMonth()}
                              year={dateRange.start.getFullYear()}
                              onChange={handleDateChange}
                              selected={dateRange}
                              allowRange
                              multiMonth
                            />
                            <input 
                              type="hidden" 
                              name="startDate" 
                              value={startDate} 
                            />
                            <input 
                              type="hidden" 
                              name="endDate" 
                              value={endDate} 
                            />
                          </div>
                        </InlineStack>
                        
                        <TextField
                          label="商品コード"
                          name="productCode"
                          value={productCode}
                          onChange={(value) => {
                            console.log('商品コード変更:', value);
                            setProductCode(value);
                          }}
                          placeholder="例: CAMERA-001"
                          autoComplete="off"
                        />
                        
                        <Select
                          label="カテゴリ"
                          name="category"
                          options={categoryOptions}
                          onChange={(value) => {
                            console.log('カテゴリ変更:', value);
                            setCategory(value);
                          }}
                          value={category}
                        />
                        
                        <Checkbox
                          label="貸出可能なもののみ表示する"
                          name="onlyAvailable"
                          checked={onlyAvailable}
                          onChange={(newChecked) => {
                            console.log('チェックボックス変更:', newChecked);
                            setOnlyAvailable(newChecked);
                          }}
                        />
                        
                        <div style={{display: 'flex', justifyContent: 'flex-end', gap: '8px'}}>
                          <Button onClick={() => {
                            setProductCode('');
                            setCategory('');
                            setOnlyAvailable(false);
                            setDateRange({
                              start: new Date(),
                              end: new Date()
                            });
                          }}>クリア</Button>
                          <Button submit primary>検索</Button>
                        </div>
                      </BlockStack>
                    </Box>
                  </BlockStack>
                </Form>
                
                {actionData?.success && (
                  <Box padding="400">
                    <BlockStack gap="400">
                      <Text as="h2" variant="headingMd">検索結果</Text>
                      
                      {actionData.products.length === 0 ? (
                        <Text>該当する商品はありません</Text>
                      ) : (
                        <div style={{overflowX: 'auto'}}>
                          <table style={{width: '100%', borderCollapse: 'collapse'}}>
                            <thead>
                              <tr>
                                <th style={{padding: '8px', textAlign: 'left', borderBottom: '1px solid #ddd'}}>商品コード</th>
                                <th style={{padding: '8px', textAlign: 'left', borderBottom: '1px solid #ddd'}}>商品名</th>
                                <th style={{padding: '8px', textAlign: 'left', borderBottom: '1px solid #ddd'}}>カテゴリ</th>
                                <th style={{padding: '8px', textAlign: 'left', borderBottom: '1px solid #ddd'}}>貸出可否</th>
                                <th style={{padding: '8px', textAlign: 'left', borderBottom: '1px solid #ddd'}}>予約期間</th>
                              </tr>
                            </thead>
                            <tbody>
                              {actionData.products.map(product => (
                                <tr key={product.id}>
                                  <td style={{padding: '8px', borderBottom: '1px solid #ddd'}}>{product.sku}</td>
                                  <td style={{padding: '8px', borderBottom: '1px solid #ddd'}}>{product.title}</td>
                                  <td style={{padding: '8px', borderBottom: '1px solid #ddd'}}>{product.category}</td>
                                  <td style={{padding: '8px', borderBottom: '1px solid #ddd'}}>
                                    {product.status === 'AVAILABLE' ? '利用可' : 
                                     product.status === 'MAINTENANCE' ? 'メンテナンス中' : 
                                     product.status === 'DAMAGED' ? '破損' : 
                                     product.status === 'UNAVAILABLE' ? '利用不可' : '利用可'}
                                  </td>
                                  <td style={{padding: '8px', borderBottom: '1px solid #ddd'}}>
                                    {product.bookings && product.bookings.length > 0 ? 
                                      product.bookings.map(b => 
                                        `${formatLocalDate(b.startDate)} 〜 ${formatLocalDate(b.endDate)} (${b.status === 'CONFIRMED' ? '確定' : b.status === 'PROVISIONAL' ? '仮予約' : b.status})`
                                      ).join(', ') : '予約なし'}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      )}
                    </BlockStack>
                  </Box>
                )}
              </BlockStack>
            </Card.Section>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
