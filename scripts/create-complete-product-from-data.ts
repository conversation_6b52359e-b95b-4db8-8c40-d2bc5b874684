import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();

// GraphQL client setup using fetch
async function createGraphQLClient() {
  const endpoint = `https://${process.env.SHOP}/admin/api/2024-01/graphql.json`;
  
  return {
    query: async ({ data }: { data: { query: string; variables?: any } }) => {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': process.env.SHOPIFY_ACCESS_TOKEN!
        },
        body: JSON.stringify(data)
      });
      
      return { body: await response.json() };
    }
  };
}

interface ProductData {
  productCode: string;
  detailCode: string;
  categoryId: number;
  categoryName: string;
  sku: string;
  itemNumber: number;
  title: string;
  kana: string;
  description: string;
  remarks: string;
  price: number;
  quantity: number;
  location: string;
  width: number;
  depth: number;
  height: number;
  weight: number;
  purchaseDate: string;
  purchasePrice: number;
  status: number;
  maintenanceNotes: string;
}

// 元データ（ユーザーから提供された情報）
const sourceData: ProductData[] = [
  {
    productCode: "10101007",
    detailCode: "1",
    categoryId: 3,
    categoryName: "ソファ",
    sku: "10101007",
    itemNumber: 1,
    title: "85_●ﾍﾞｰｼｯｸｿﾌｧ ｵﾌﾎﾜｲﾄ 1ｼｰﾀｰ",
    kana: "ﾍﾞｰｼｯｸｿﾌｧｵﾌﾎﾜｲﾄ1ｼｰﾀｰﾔﾏﾅﾘ",
    description: "１　背面向かって右側うっすら黒いしみ",
    remarks: "",
    price: 8000,
    quantity: 1,
    location: "NY",
    width: 87,
    depth: 74,
    height: 76,
    weight: 0,
    purchaseDate: "19700101",
    purchasePrice: 0,
    status: 1,
    maintenanceNotes: "●2台ともクリーニング済みですが全体的に黄ばみ発生（H30.12.15)／[002]向かって左アーム手前と正面左側に黄色い輪染み有／[001]背面向かって右側うっすら黒いしみあり(写真には写りません)　SW52/SD54/SH40"
  },
  {
    productCode: "10101007",
    detailCode: "2", 
    categoryId: 3,
    categoryName: "ソファ",
    sku: "10101007",
    itemNumber: 2,
    title: "85_●ﾍﾞｰｼｯｸｿﾌｧ ｵﾌﾎﾜｲﾄ 1ｼｰﾀｰ",
    kana: "ﾍﾞｰｼｯｸｿﾌｧｵﾌﾎﾜｲﾄ1ｼｰﾀｰﾔﾏﾅﾘ",
    description: "２　向かって左アーム手前と正面左側に黄色い輪染み有",
    remarks: "",
    price: 8000,
    quantity: 1,
    location: "NY",
    width: 87,
    depth: 74,
    height: 76,
    weight: 0,
    purchaseDate: "19700101",
    purchasePrice: 0,
    status: 1,
    maintenanceNotes: "●2台ともクリーニング済みですが全体的に黄ばみ発生（H30.12.15)／[002]向かって左アーム手前と正面左側に黄色い輪染み有／[001]背面向かって右側うっすら黒いしみあり(写真には写りません)　SW52/SD54/SH40"
  }
];

async function createCompleteProduct(data: ProductData) {
  const client = await createGraphQLClient();

  console.log(`🛍️ 商品作成開始: ${data.title} (${data.detailCode})`);

  // 1. Shopify商品を作成
  const productInput = {
    title: `${data.title} [${data.detailCode}]`,
    handle: `${data.productCode}-${data.detailCode}`,
    descriptionHtml: `
      <h3>${data.title}</h3>
      <p><strong>商品番号:</strong> ${data.productCode}-${data.detailCode}</p>
      <p><strong>状態:</strong> ${data.description}</p>
      <p><strong>サイズ:</strong> 幅${data.width}cm × 奥行${data.depth}cm × 高さ${data.height}cm</p>
      <p><strong>カテゴリ:</strong> ${data.categoryName}</p>
      <p><strong>保管場所:</strong> ${data.location}</p>
      ${data.maintenanceNotes ? `<p><strong>メンテナンス備考:</strong> ${data.maintenanceNotes}</p>` : ''}
    `,
    vendor: 'IZIZ RENTAL',
    productType: data.categoryName,
    tags: [`${data.categoryName}`, `商品番号:${data.productCode}`, `詳細:${data.detailCode}`, `場所:${data.location}`],
    status: 'ACTIVE',
    variants: [
      {
        title: '1日レンタル',
        sku: `${data.productCode}-${data.detailCode}-1D`,
        price: (data.price * 1).toString(),
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY',
        fulfillmentService: 'MANUAL',
        requiresShipping: true,
        taxable: true,
        weight: data.weight || 0,
        weightUnit: 'KILOGRAMS'
      },
      {
        title: '2日レンタル',
        sku: `${data.productCode}-${data.detailCode}-2D`,
        price: (data.price * 1.2).toString(),
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY',
        fulfillmentService: 'MANUAL',
        requiresShipping: true,
        taxable: true,
        weight: data.weight || 0,
        weightUnit: 'KILOGRAMS'
      },
      {
        title: '3日レンタル',
        sku: `${data.productCode}-${data.detailCode}-3D`,
        price: (data.price * 1.4).toString(),
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY',
        fulfillmentService: 'MANUAL',
        requiresShipping: true,
        taxable: true,
        weight: data.weight || 0,
        weightUnit: 'KILOGRAMS'
      },
      {
        title: '4日レンタル',
        sku: `${data.productCode}-${data.detailCode}-4D`,
        price: (data.price * 1.6).toString(),
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY',
        fulfillmentService: 'MANUAL',
        requiresShipping: true,
        taxable: true,
        weight: data.weight || 0,
        weightUnit: 'KILOGRAMS'
      },
      {
        title: '5日レンタル',
        sku: `${data.productCode}-${data.detailCode}-5D`,
        price: (data.price * 1.8).toString(),
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY',
        fulfillmentService: 'MANUAL',
        requiresShipping: true,
        taxable: true,
        weight: data.weight || 0,
        weightUnit: 'KILOGRAMS'
      },
      {
        title: '6日レンタル',
        sku: `${data.productCode}-${data.detailCode}-6D`,
        price: (data.price * 2.0).toString(),
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY',
        fulfillmentService: 'MANUAL',
        requiresShipping: true,
        taxable: true,
        weight: data.weight || 0,
        weightUnit: 'KILOGRAMS'
      },
      {
        title: '7日レンタル',
        sku: `${data.productCode}-${data.detailCode}-7D`,
        price: (data.price * 2.1).toString(),
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY',
        fulfillmentService: 'MANUAL',
        requiresShipping: true,
        taxable: true,
        weight: data.weight || 0,
        weightUnit: 'KILOGRAMS'
      },
      {
        title: '8日以上レンタル',
        sku: `${data.productCode}-${data.detailCode}-8D+`,
        price: (data.price * 0.1).toString(), // 追加日数料金
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY',
        fulfillmentService: 'MANUAL',
        requiresShipping: true,
        taxable: true,
        weight: data.weight || 0,
        weightUnit: 'KILOGRAMS'
      },
      {
        title: '仮予約',
        sku: `${data.productCode}-${data.detailCode}-PROV`,
        price: (data.price * 0.1).toString(), // 仮予約料金
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY',
        fulfillmentService: 'MANUAL',
        requiresShipping: true,
        taxable: true,
        weight: data.weight || 0,
        weightUnit: 'KILOGRAMS'
      }
    ]
  };

  const createProductMutation = `
    mutation CreateProduct($input: ProductInput!) {
      productCreate(input: $input) {
        product {
          id
          title
          handle
          variants(first: 20) {
            nodes {
              id
              title
              sku
            }
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const productResponse = await client.query({
    data: {
      query: createProductMutation,
      variables: { input: productInput }
    }
  });

  const productResult = productResponse.body as any;
  
  if (productResult.data.productCreate.userErrors.length > 0) {
    console.error('❌ 商品作成エラー:', productResult.data.productCreate.userErrors);
    return null;
  }

  const shopifyProduct = productResult.data.productCreate.product;
  console.log(`✅ Shopify商品作成完了: ${shopifyProduct.id}`);

  // 2. メタフィールドを設定
  const metafields = [
    {
      namespace: 'product',
      key: 'depth',
      value: data.depth.toString(),
      type: 'number_integer'
    },
    {
      namespace: 'product',
      key: 'height', 
      value: data.height.toString(),
      type: 'number_integer'
    },
    {
      namespace: 'product',
      key: 'width',
      value: data.width.toString(),
      type: 'number_integer'
    },
    {
      namespace: 'rental',
      key: 'basic_info',
      value: JSON.stringify({
        productCode: data.productCode,
        detailCode: data.detailCode,
        kana: data.kana,
        location: data.location,
        status: 'available'
      }),
      type: 'json'
    },
    {
      namespace: 'rental',
      key: 'pricing',
      value: JSON.stringify({
        basePrice: data.price,
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30,
        variantPrices: {
          "1day": data.price,
          "2day": Math.round(data.price * 1.2),
          "3day": Math.round(data.price * 1.4),
          "4day": Math.round(data.price * 1.6),
          "5day": Math.round(data.price * 1.8),
          "6day": Math.round(data.price * 2.0),
          "7day": Math.round(data.price * 2.1),
          "8plus": Math.round(data.price * 0.1)
        }
      }),
      type: 'json'
    },
    {
      namespace: 'rental',
      key: 'inventory_items',
      value: JSON.stringify([
        {
          itemId: `${data.productCode}-${data.detailCode}`,
          condition: data.description,
          lastInspectionDate: new Date().toISOString().split('T')[0],
          maintenanceNotes: data.maintenanceNotes,
          status: 'available'
        }
      ]),
      type: 'json'
    },
    {
      namespace: 'rental',
      key: 'reservation_info',
      value: JSON.stringify({
        reservations: [],
        maintenanceSchedule: [],
        availabilityRules: {
          minimumNotice: 1,
          maximumAdvance: 180,
          blackoutDates: []
        }
      }),
      type: 'json'
    },
    {
      namespace: 'rental',
      key: 'booking_notes',
      value: data.description ? `特記事項: ${data.description}` : '特別な指示はありません。通常通りの予約が可能です。',
      type: 'single_line_text_field'
    },
    {
      namespace: 'rental',
      key: 'booking_type',
      value: 'confirmed',
      type: 'single_line_text_field'
    },
    {
      namespace: 'rental',
      key: 'color',
      value: 'オフホワイト',
      type: 'single_line_text_field'
    },
    {
      namespace: 'rental',
      key: 'location',
      value: data.location,
      type: 'single_line_text_field'
    },
    {
      namespace: 'rental',
      key: 'maintenance_notes',
      value: data.maintenanceNotes,
      type: 'multi_line_text_field'
    },
    {
      namespace: 'rental',
      key: 'manufacturer',
      value: '',
      type: 'single_line_text_field'
    },
    {
      namespace: 'rental',
      key: 'material',
      value: '',
      type: 'single_line_text_field'
    },
    {
      namespace: 'rental',
      key: 'purchase_date',
      value: data.purchaseDate !== '19700101' ? data.purchaseDate : '',
      type: 'single_line_text_field'
    },
    {
      namespace: 'rental',
      key: 'purchase_place',
      value: '',
      type: 'single_line_text_field'
    },
    {
      namespace: 'rental',
      key: 'purchase_price',
      value: data.purchasePrice.toString(),
      type: 'number_decimal'
    },
    {
      namespace: 'rental',
      key: 'status',
      value: 'available',
      type: 'single_line_text_field'
    },
    {
      namespace: 'rental',
      key: 'variation_type',
      value: 'rental_period',
      type: 'single_line_text_field'
    }
  ];

  // メタフィールドを一括設定
  const setMetafieldsMutation = `
    mutation SetMetafields($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const metafieldInputs = metafields.map(mf => ({
    ownerId: shopifyProduct.id,
    namespace: mf.namespace,
    key: mf.key,
    value: mf.value,
    type: mf.type
  }));

  const metafieldResponse = await client.query({
    data: {
      query: setMetafieldsMutation,
      variables: { metafields: metafieldInputs }
    }
  });

  const metafieldResult = metafieldResponse.body as any;
  
  if (metafieldResult.data.metafieldsSet.userErrors.length > 0) {
    console.error('⚠️ メタフィールド設定エラー:', metafieldResult.data.metafieldsSet.userErrors);
  } else {
    console.log(`✅ メタフィールド設定完了: ${metafieldResult.data.metafieldsSet.metafields.length}件`);
  }

  // 3. Prismaに商品データを登録
  try {
    const productId = shopifyProduct.id.replace('gid://shopify/Product/', '');
    
    const prismaProduct = await prisma.product.create({
      data: {
        shopifyProductId: productId,
        title: shopifyProduct.title,
        handle: shopifyProduct.handle,
        sku: `${data.productCode}-${data.detailCode}`,
        shop: process.env.SHOP!,
        basicInfo: {
          productCode: data.productCode,
          detailCode: data.detailCode,
          kana: data.kana,
          location: data.location,
          status: 'available'
        },
        pricing: {
          basePrice: data.price,
          depositRate: 0.1,
          discountRules: {
            day2_6_rate: 0.2,
            day7_plus_rate: 0.1
          },
          minimumDays: 1,
          maximumDays: 30
        },
        dimensions: {
          width: data.width,
          depth: data.depth,  
          height: data.height,
          weight: data.weight || 0
        },
        metadata: {
          category: data.categoryName,
          description: data.description,
          maintenanceNotes: data.maintenanceNotes,
          purchaseDate: data.purchaseDate,
          purchasePrice: data.purchasePrice
        },
        syncStatus: 'synced',
        lastSyncedAt: new Date()
      }
    });

    console.log(`✅ Prisma商品登録完了: ${prismaProduct.id}`);

    // 4. バリアント情報をPrismaに登録
    for (const variant of shopifyProduct.variants.nodes) {
      await prisma.productVariant.create({
        data: {
          shopifyVariantId: variant.id.replace('gid://shopify/ProductVariant/', ''),
          productId: prismaProduct.id,
          title: variant.title,
          sku: variant.sku,
          price: parseFloat(productInput.variants.find(v => v.title === variant.title)?.price || '0'),
          inventoryManagement: 'shopify',
          inventoryPolicy: 'deny',
          position: shopifyProduct.variants.nodes.indexOf(variant) + 1
        }
      });
    }

    console.log(`✅ バリアント登録完了: ${shopifyProduct.variants.nodes.length}件`);

    return {
      shopifyProduct,
      prismaProduct,
      metafieldsCount: metafieldInputs.length
    };

  } catch (error) {
    console.error('❌ Prisma登録エラー:', error);
    return {
      shopifyProduct,
      prismaProduct: null,
      metafieldsCount: metafieldInputs.length
    };
  }
}

async function main() {
  try {
    console.log('🚀 元データから完全商品登録を開始...');
    console.log(`📦 登録対象: ${sourceData.length}件の商品`);

    for (const data of sourceData) {
      const result = await createCompleteProduct(data);
      
      if (result) {
        console.log(`✅ 商品登録完了: ${data.title} [${data.detailCode}]`);
        console.log(`   - Shopify ID: ${result.shopifyProduct.id}`);
        console.log(`   - メタフィールド: ${result.metafieldsCount}件`);
        console.log(`   - バリアント: ${result.shopifyProduct.variants.nodes.length}件`);
        if (result.prismaProduct) {
          console.log(`   - Prisma ID: ${result.prismaProduct.id}`);
        }
      } else {
        console.error(`❌ 商品登録失敗: ${data.title} [${data.detailCode}]`);
      }

      // API制限回避のため少し待機
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('🎉 全ての商品登録が完了しました！');

  } catch (error) {
    console.error('❌ エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// メイン実行
main().catch(console.error);

export { createCompleteProduct, sourceData };