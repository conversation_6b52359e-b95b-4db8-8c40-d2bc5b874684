import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function findProduct() {
  try {
    // 201-07-107を含む商品を検索
    const products = await prisma.product.findMany({
      where: { 
        sku: { contains: '201-07-107' }
      },
      select: { 
        id: true, 
        title: true, 
        sku: true, 
        price: true, 
        shopifyId: true 
      }
    });
    
    console.log('201-07-107を含む商品:', products);
    
    // 最近作成された商品も確認
    const recentProducts = await prisma.product.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5,
      select: { 
        id: true, 
        title: true, 
        sku: true, 
        price: true, 
        shopifyId: true 
      }
    });
    
    console.log('\n最近作成された商品（5件）:');
    recentProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.title} (${product.sku})`);
      console.log(`   価格: ¥${product.price ? (product.price / 100).toLocaleString() : '未設定'}`);
      console.log(`   Shopify ID: ${product.shopifyId}`);
    });
    
  } catch (error) {
    console.error('エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

findProduct();
