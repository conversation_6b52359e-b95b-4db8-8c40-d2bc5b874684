/**
 * 注文作成エラーバナーコンポーネント
 * 
 * 予約の注文作成に失敗した場合に、エラーメッセージとリカバリーオプションを表示するコンポーネント
 */

import { useState } from "react";
import { Banner, Button, Text, Modal, Spinner, InlineStack, BlockStack } from "@shopify/polaris";
import { createOrderFromBooking } from "~/utils/booking/order-creator";
import { retry } from "~/utils/booking/error-handler";
import { useSubmit } from "@remix-run/react";

interface OrderCreationErrorBannerProps {
  booking: {
    id: string;
    bookingId?: string;
    metadata?: any;
  };
  onSuccess?: () => void;
}

export function OrderCreationErrorBanner({ booking, onSuccess }: OrderCreationErrorBannerProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryResult, setRetryResult] = useState<{
    success: boolean;
    message: string;
    orderId?: string;
    orderName?: string;
  } | null>(null);
  const submit = useSubmit();

  // メタデータからエラー情報を取得
  const metadata = booking.metadata || {};
  const hasOrderCreationError = metadata.orderCreationError === true;
  const errorMessage = metadata.orderCreationErrorMessage || "注文作成に失敗しました";
  const errorDate = metadata.orderCreationErrorDate 
    ? new Date(metadata.orderCreationErrorDate).toLocaleString() 
    : "不明";

  if (!hasOrderCreationError) {
    return null;
  }

  // 注文作成を再試行する
  const handleRetry = async () => {
    setIsModalOpen(true);
    setIsRetrying(true);
    setRetryResult(null);

    try {
      // 注文作成APIを呼び出す
      const formData = new FormData();
      formData.append("action", "retry_order_creation");
      formData.append("bookingId", booking.id);
      
      submit(formData, { method: "post", action: `/api/bookings/${booking.id}/retry-order` });
      
      // モーダルは閉じずに、APIからのレスポンスを待つ
      // 実際のレスポンスはサーバーサイドから返される
    } catch (error) {
      console.error("注文作成再試行中にエラーが発生しました:", error);
      setIsRetrying(false);
      setRetryResult({
        success: false,
        message: error instanceof Error ? error.message : "予期しないエラーが発生しました"
      });
    }
  };

  // 成功時の処理
  const handleSuccess = () => {
    setIsModalOpen(false);
    if (onSuccess) {
      onSuccess();
    }
    // ページをリロードして最新の状態を表示
    window.location.reload();
  };

  return (
    <>
      <Banner tone="warning">
        <BlockStack gap="200">
          <Text as="h3" fontWeight="bold">
            注文作成エラー
          </Text>
          <Text as="p">
            予約 {booking.bookingId || booking.id.substring(0, 8)} の注文作成に失敗しました。
          </Text>
          <Text as="p">
            エラー: {errorMessage}
          </Text>
          <Text as="p" color="subdued">
            発生日時: {errorDate}
          </Text>
          <div style={{ marginTop: "8px" }}>
            <Button onClick={handleRetry}>注文作成を再試行</Button>
          </div>
        </BlockStack>
      </Banner>

      <Modal
        open={isModalOpen}
        onClose={() => !isRetrying && setIsModalOpen(false)}
        title="注文作成の再試行"
        primaryAction={
          retryResult
            ? {
                content: retryResult.success ? "完了" : "閉じる",
                onAction: retryResult.success ? handleSuccess : () => setIsModalOpen(false),
              }
            : undefined
        }
        secondaryActions={
          retryResult && !retryResult.success
            ? [
                {
                  content: "もう一度試す",
                  onAction: handleRetry,
                },
              ]
            : undefined
        }
      >
        <Modal.Section>
          {isRetrying && !retryResult ? (
            <div style={{ textAlign: "center", padding: "20px" }}>
              <Spinner size="large" />
              <div style={{ marginTop: "16px" }}>
                <Text as="p">注文を作成しています...</Text>
                <Text as="p" color="subdued">
                  このプロセスには数秒かかる場合があります。
                </Text>
              </div>
            </div>
          ) : retryResult ? (
            <BlockStack gap="400">
              <Banner tone={retryResult.success ? "success" : "critical"}>
                <Text as="p">{retryResult.message}</Text>
              </Banner>
              {retryResult.success && retryResult.orderId && (
                <Text as="p">
                  注文ID: {retryResult.orderName || retryResult.orderId}
                </Text>
              )}
            </BlockStack>
          ) : (
            <Text as="p">注文作成を再試行しますか？</Text>
          )}
        </Modal.Section>
      </Modal>
    </>
  );
}
