/**
 * 商品の予約状況を表示するカレンダーコンポーネント
 */

import React, { useState } from 'react';
import {
  Box,
  Text,
  Card,
  BlockStack,
  Badge,
  InlineStack,
  Banner,
  Button,
  Modal,
  TextField
} from '@shopify/polaris';
import { format, addMonths, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, isToday } from 'date-fns';
import { ja } from 'date-fns/locale';

// 予約データの型定義
interface Booking {
  id: string;
  startDate: string;
  endDate: string;
  status: string;
  bookingType: string;
  customerName: string | null;
  customerEmail: string | null;
  bookingId: string;
}

// 商品データの型定義
interface Product {
  id: string;
  title: string;
  sku: string;
  status: string;
}

// コンポーネントのProps
interface ProductBookingCalendarProps {
  product: Product;
  bookings: Booking[];
  onClose?: () => void;
}

export function ProductBookingCalendar({
  product,
  bookings,
  onClose
}: ProductBookingCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showNewBookingModal, setShowNewBookingModal] = useState(false);
  const [newBookingStartDate, setNewBookingStartDate] = useState('');
  const [newBookingEndDate, setNewBookingEndDate] = useState('');

  // 月の最初と最後の日を取得
  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // 日付が予約済みかチェック
  const getBookingForDate = (date: Date): Booking | null => {
    return bookings.find(booking => {
      const startDate = new Date(booking.startDate);
      const endDate = new Date(booking.endDate);
      return date >= startDate && date <= endDate;
    }) || null;
  };

  // 日付のスタイルを取得
  const getDateStyle = (date: Date) => {
    const booking = getBookingForDate(date);
    const baseStyle = {
      width: '40px',
      height: '40px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: '4px',
      cursor: 'pointer',
      border: '1px solid #e1e3e5',
      fontSize: '14px',
      position: 'relative' as const
    };

    if (!isSameMonth(date, currentDate)) {
      return {
        ...baseStyle,
        color: '#8c9196',
        backgroundColor: '#f6f6f7'
      };
    }

    if (isToday(date)) {
      return {
        ...baseStyle,
        border: '2px solid #008060',
        fontWeight: 'bold'
      };
    }

    if (booking) {
      const isProvisional = booking.status === 'PROVISIONAL' || booking.bookingType === 'PROVISIONAL';
      return {
        ...baseStyle,
        backgroundColor: isProvisional ? 'rgba(255, 184, 0, 0.2)' : 'rgba(0, 128, 96, 0.2)',
        color: isProvisional ? '#d82c0d' : '#00848e',
        fontWeight: 'bold'
      };
    }

    return {
      ...baseStyle,
      backgroundColor: '#ffffff',
      '&:hover': {
        backgroundColor: '#f9fafb'
      }
    };
  };

  // 日付クリックハンドラー
  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
    const booking = getBookingForDate(date);
    if (!booking) {
      // 予約がない日付の場合、新規予約モーダルを表示
      setNewBookingStartDate(format(date, 'yyyy-MM-dd'));
      setNewBookingEndDate(format(date, 'yyyy-MM-dd'));
      setShowNewBookingModal(true);
    }
  };

  // 選択された日付の予約情報
  const selectedBooking = selectedDate ? getBookingForDate(selectedDate) : null;

  // 曜日のヘッダー
  const weekDays = ['日', '月', '火', '水', '木', '金', '土'];

  return (
    <Card>
      <BlockStack gap="400">
        {/* ヘッダー */}
        <InlineStack align="space-between">
          <BlockStack gap="200">
            <Text variant="headingMd" as="h2">
              {product.title} の予約状況
            </Text>
            <Text as="p" tone="subdued">
              SKU: {product.sku} | ステータス: {product.status}
            </Text>
          </BlockStack>
          {onClose && (
            <Button onClick={onClose}>閉じる</Button>
          )}
        </InlineStack>

        {/* 月ナビゲーション */}
        <InlineStack align="space-between">
          <Button onClick={() => setCurrentDate(subMonths(currentDate, 1))}>
            ← 前月
          </Button>
          <Text variant="headingSm" as="h3">
            {format(currentDate, 'yyyy年MM月', { locale: ja })}
          </Text>
          <Button onClick={() => setCurrentDate(addMonths(currentDate, 1))}>
            次月 →
          </Button>
        </InlineStack>

        {/* カレンダー */}
        <Box>
          {/* 曜日ヘッダー */}
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: '4px', marginBottom: '8px' }}>
            {weekDays.map(day => (
              <div key={day} style={{ textAlign: 'center', fontWeight: 'bold', padding: '8px' }}>
                {day}
              </div>
            ))}
          </div>

          {/* 日付グリッド */}
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: '4px' }}>
            {monthDays.map(date => {
              const booking = getBookingForDate(date);
              return (
                <div
                  key={date.toISOString()}
                  style={getDateStyle(date)}
                  onClick={() => handleDateClick(date)}
                  title={booking ? `予約済み: ${booking.customerName || '顧客名なし'}` : '予約可能'}
                >
                  {format(date, 'd')}
                  {booking && (
                    <div style={{
                      position: 'absolute',
                      top: '2px',
                      right: '2px',
                      fontSize: '10px',
                      fontWeight: 'bold'
                    }}>
                      {booking.status === 'PROVISIONAL' || booking.bookingType === 'PROVISIONAL' ? '仮' : '予'}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </Box>

        {/* 凡例 */}
        <Box background="bg-surface-secondary" padding="300" borderRadius="100">
          <BlockStack gap="200">
            <Text variant="headingSm" as="h4">凡例</Text>
            <InlineStack gap="400">
              <InlineStack gap="100" align="center">
                <div style={{
                  width: '20px',
                  height: '20px',
                  backgroundColor: 'rgba(255, 184, 0, 0.2)',
                  borderRadius: '4px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '10px',
                  fontWeight: 'bold',
                  color: '#d82c0d'
                }}>仮</div>
                <Text as="span">仮予約</Text>
              </InlineStack>
              <InlineStack gap="100" align="center">
                <div style={{
                  width: '20px',
                  height: '20px',
                  backgroundColor: 'rgba(0, 128, 96, 0.2)',
                  borderRadius: '4px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '10px',
                  fontWeight: 'bold',
                  color: '#00848e'
                }}>予</div>
                <Text as="span">本予約</Text>
              </InlineStack>
              <InlineStack gap="100" align="center">
                <div style={{
                  width: '20px',
                  height: '20px',
                  border: '2px solid #008060',
                  borderRadius: '4px'
                }}></div>
                <Text as="span">今日</Text>
              </InlineStack>
            </InlineStack>
          </BlockStack>
        </Box>

        {/* 選択された日付の詳細 */}
        {selectedDate && selectedBooking && (
          <Banner tone="info">
            <BlockStack gap="200">
              <Text variant="headingSm" as="h4">
                {format(selectedDate, 'yyyy年MM月dd日', { locale: ja })} の予約詳細
              </Text>
              <Text as="p"><strong>予約ID:</strong> {selectedBooking.bookingId}</Text>
              <Text as="p"><strong>顧客名:</strong> {selectedBooking.customerName || '未設定'}</Text>
              <Text as="p"><strong>メール:</strong> {selectedBooking.customerEmail || '未設定'}</Text>
              <Text as="p"><strong>期間:</strong> {format(new Date(selectedBooking.startDate), 'MM/dd')} ～ {format(new Date(selectedBooking.endDate), 'MM/dd')}</Text>
              <InlineStack gap="200">
                <Badge tone={selectedBooking.status === 'CONFIRMED' ? 'success' : 'warning'}>
                  {selectedBooking.status === 'CONFIRMED' ? '確定' : '仮予約'}
                </Badge>
              </InlineStack>
            </BlockStack>
          </Banner>
        )}

        {/* 新規予約モーダル */}
        <Modal
          open={showNewBookingModal}
          onClose={() => setShowNewBookingModal(false)}
          title="新規予約作成"
          primaryAction={{
            content: '予約作成',
            onAction: () => {
              // TODO: 予約作成処理を実装
              console.log('新規予約:', { startDate: newBookingStartDate, endDate: newBookingEndDate });
              setShowNewBookingModal(false);
            }
          }}
          secondaryActions={[{
            content: 'キャンセル',
            onAction: () => setShowNewBookingModal(false)
          }]}
        >
          <Modal.Section>
            <BlockStack gap="400">
              <TextField
                label="開始日"
                type="date"
                value={newBookingStartDate}
                onChange={setNewBookingStartDate}
                autoComplete="off"
              />
              <TextField
                label="終了日"
                type="date"
                value={newBookingEndDate}
                onChange={setNewBookingEndDate}
                autoComplete="off"
              />
            </BlockStack>
          </Modal.Section>
        </Modal>
      </BlockStack>
    </Card>
  );
}
