import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';

const prisma = new PrismaClient();

interface CarrierData {
  name: string;
  code: string;
  contactInfo?: string;
  serviceAreas?: string;
  notes?: string;
}

async function importShippingCarriers() {
  try {
    console.log('配送業者マスタのインポートを開始します...');

    // CSVファイルのパス
    const csvFilePath = path.join(process.cwd(), 'master-data-csv/other-master-data/配送業者一覧_202505192115.csv');

    if (!fs.existsSync(csvFilePath)) {
      console.error(`CSVファイルが見つかりません: ${csvFilePath}`);
      return;
    }

    const carriers: CarrierData[] = [];

    // CSVファイルを読み込み
    await new Promise<void>((resolve, reject) => {
      fs.createReadStream(csvFilePath)
        .pipe(csv())
        .on('data', (row) => {
          // CSVの列名に基づいてデータを抽出
          const carrier: CarrierData = {
            name: row['業者名称'] || '',
            code: row['業者コード'] || '',
            contactInfo: row['電話番号'] || '',
            serviceAreas: `${row['都道府県コード'] || ''} ${row['市区町村'] || ''}`.trim(),
            notes: row['特記事項'] || ''
          };

          // 削除フラグが0（有効）で、名称とコードがある場合のみ追加
          if (carrier.name && carrier.code && row['削除フラグ'] === '0') {
            carriers.push(carrier);
          }
        })
        .on('end', resolve)
        .on('error', reject);
    });

    console.log(`${carriers.length}件の配送業者データを読み込みました`);

    // 既存データをクリア（開発環境のみ）
    if (process.env.NODE_ENV === 'development') {
      await prisma.shippingCarrier.deleteMany({});
      console.log('既存の配送業者データをクリアしました');
    }

    // データをインポート
    const shop = 'development.myshopify.com'; // 開発環境用

    for (const carrier of carriers) {
      await prisma.shippingCarrier.upsert({
        where: {
          shop_code: {
            shop: shop,
            code: carrier.code
          }
        },
        update: {
          name: carrier.name,
          phoneNumber: carrier.contactInfo,
          notes: carrier.notes,
        },
        create: {
          shop: shop,
          name: carrier.name,
          code: carrier.code,
          phoneNumber: carrier.contactInfo,
          notes: carrier.notes,
        },
      });
    }

    console.log(`${carriers.length}件の配送業者データをインポートしました`);

    // インポート結果を確認
    const importedCarriers = await prisma.shippingCarrier.findMany({
      orderBy: { name: 'asc' }
    });

    console.log('\n=== インポート結果 ===');
    importedCarriers.forEach((carrier, index) => {
      console.log(`${index + 1}. ${carrier.name} (${carrier.code})`);
      if (carrier.phoneNumber) {
        console.log(`   連絡先: ${carrier.phoneNumber}`);
      }
      if (carrier.notes) {
        console.log(`   備考: ${carrier.notes.substring(0, 50)}${carrier.notes.length > 50 ? '...' : ''}`);
      }
    });

  } catch (error) {
    console.error('配送業者マスタのインポートでエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  importShippingCarriers();
}

export { importShippingCarriers };
