name: Integration Tests

on:
  schedule:
    # 毎日午前3時に実行（UTC）
    - cron: '0 3 * * *'
  workflow_dispatch:
    inputs:
      use_real_api:
        description: 'Use real Shopify API'
        required: false
        default: false
        type: boolean

jobs:
  integration-test:
    runs-on: ubuntu-latest
    
    env:
      NODE_ENV: test
      USE_LOCAL_MODE: ${{ github.event.inputs.use_real_api != true }}
      SHOPIFY_API_KEY: ${{ secrets.SHOPIFY_API_KEY }}
      SHOPIFY_API_SECRET: ${{ secrets.SHOPIFY_API_SECRET }}
      SHOPIFY_SHOP: ${{ secrets.SHOPIFY_SHOP }}
      SHOPIFY_ADMIN_API_ACCESS_TOKEN: ${{ secrets.SHOPIFY_ADMIN_API_ACCESS_TOKEN }}
      DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
      TEST_PRODUCT_ID: ${{ secrets.TEST_PRODUCT_ID }}
    
    strategy:
      matrix:
        node-version: [20.x]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Setup database
      run: |
        npx prisma db push --accept-data-loss
        
        # テスト用のデータをシード
        if [ -f "prisma/seed.ts" ]; then
          npx prisma db seed
        fi
    
    - name: Run comprehensive integration tests
      run: |
        # API使用フラグに基づいてテストを実行
        if [ "${{ github.event.inputs.use_real_api }}" = "true" ]; then
          echo "Running tests with real Shopify API..."
          npm run test:shopify-prisma-integration
        else
          echo "Running tests with local mode..."
          npm run test:shopify-prisma-integration -- --local
        fi
        
        # エラーケーステスト
        npm run test:shopify-prisma-integration -- --error-cases --local
        
        # バリエーションテスト
        npm run test:shopify-prisma-integration -- --variants --local
    
    - name: Generate test summary
      run: |
        echo "# Integration Test Summary" > test-summary.md
        echo "Run date: $(date)" >> test-summary.md
        echo "" >> test-summary.md
        echo "## Test Results" >> test-summary.md
        
        if [ -f "docs/shopify-prisma-integration-test-report.md" ]; then
          # テストレポートから結果を抽出
          RESULTS=$(grep -A 5 "## テスト結果" docs/shopify-prisma-integration-test-report.md)
          echo "$RESULTS" >> test-summary.md
        else
          echo "No test report found" >> test-summary.md
        fi
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      with:
        name: integration-test-reports
        path: |
          docs/shopify-prisma-integration-test-report.md
          test-summary.md
    
    - name: Create GitHub Issue on failure
      if: failure()
      uses: actions/github-script@v6
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          const fs = require('fs');
          let summary = '';
          
          try {
            summary = fs.readFileSync('test-summary.md', 'utf8');
          } catch (error) {
            summary = 'Failed to read test summary.';
          }
          
          const issueTitle = `Integration Test Failure - ${new Date().toISOString().split('T')[0]}`;
          
          github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: issueTitle,
            body: `Integration tests failed. Please check the workflow run for details.\n\n${summary}\n\n[Workflow Run](${context.serverUrl}/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId})`,
            labels: ['bug', 'test-failure']
          });
    
    - name: Cleanup test data
      if: always()
      run: npm run test:shopify-prisma-integration -- --cleanup --local
    
    - name: Notify on Slack
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        fields: repo,message,commit,author,action,eventName,ref,workflow,job,took
        text: 'Integration Test ${{ job.status }}'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
