/**
 * テスト予約のクリーンアップスクリプト
 *
 * このスクリプトは、テスト用に作成された予約をすべて削除し、在庫カレンダーをリセットします。
 * 実行方法: npx tsx scripts/cleanup-test-bookings.ts [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { addDays, format } from 'date-fns';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * テスト予約をクリーンアップする関数
 */
async function cleanupTestBookings() {
  try {
    console.log('テスト予約のクリーンアップを開始します...');

    // 商品IDをコマンドライン引数から取得
    const shopifyProductId = process.argv[2] || '3333333333';

    // テスト用の予約を検索（customerEmailが'<EMAIL>'のもの）
    const testBookings = await prisma.booking.findMany({
      where: {
        customerEmail: '<EMAIL>'
      }
    });

    console.log(`${testBookings.length}件のテスト予約が見つかりました`);

    // 予約を削除
    if (testBookings.length > 0) {
      for (const booking of testBookings) {
        await prisma.booking.delete({
          where: { id: booking.id }
        });
        console.log(`予約 ${booking.id} (${booking.bookingId}) を削除しました`);
      }
      console.log(`${testBookings.length}件のテスト予約を削除しました`);
    }

    // 他のテスト予約も検索（notesに'エッジケーステスト'または'テスト'を含むもの、またはテスト用のメールアドレス）
    const otherTestBookings = await prisma.booking.findMany({
      where: {
        OR: [
          { notes: { contains: 'エッジケーステスト' } },
          { notes: { contains: 'テスト' } },
          { notes: { contains: '負荷テスト' } },
          { customerEmail: { contains: 'example.com' } },
          { customerEmail: { contains: 'test' } }
        ]
      }
    });

    console.log(`${otherTestBookings.length}件の追加テスト予約が見つかりました`);

    // 予約を削除
    if (otherTestBookings.length > 0) {
      for (const booking of otherTestBookings) {
        try {
          await prisma.booking.delete({
            where: { id: booking.id }
          });
          console.log(`予約 ${booking.id} (${booking.bookingId}) を削除しました`);
        } catch (error) {
          console.warn(`予約 ${booking.id} の削除に失敗しました:`, error);
        }
      }
      console.log(`${otherTestBookings.length}件の追加テスト予約を削除しました`);
    }

    // 在庫カレンダーをリセット
    console.log('在庫カレンダーをリセットします...');

    // テスト商品の在庫カレンダーを取得
    const testProducts = await prisma.product.findMany({
      where: {
        OR: [
          { title: { contains: 'テスト商品' } },
          { shopifyId: shopifyProductId }
        ]
      }
    });

    if (testProducts.length === 0) {
      console.log(`商品ID ${shopifyProductId} が見つかりません`);
      return;
    }

    for (const product of testProducts) {
      console.log(`商品 ${product.title} (${product.id}) の在庫カレンダーをリセットします...`);

      try {
        // 在庫カレンダーを削除
        const deleteResult = await prisma.inventoryCalendar.deleteMany({
          where: { productId: product.id }
        });

        console.log(`${deleteResult.count}件の在庫カレンダーを削除しました`);

        // 今日から1年分の在庫カレンダーを作成
        const today = new Date();
        const endDate = addDays(today, 365);

        console.log(`${format(today, 'yyyy-MM-dd')}から${format(endDate, 'yyyy-MM-dd')}までの在庫カレンダーを作成します`);

        // 日付ごとに在庫カレンダーを作成（バッチ処理）
        const calendarEntries = [];
        for (let date = new Date(today); date <= endDate; date = addDays(date, 1)) {
          calendarEntries.push({
            shop: product.shop,
            productId: product.id,
            shopifyProductId: product.shopifyId || '0',
            date: new Date(date),
            isAvailable: true,
            createdAt: new Date(),
            updatedAt: new Date()
          });

          // 100件ごとにバッチ処理
          if (calendarEntries.length >= 100) {
            await prisma.inventoryCalendar.createMany({
              data: calendarEntries,
              skipDuplicates: true
            });
            calendarEntries.length = 0;
          }
        }

        // 残りのエントリを処理
        if (calendarEntries.length > 0) {
          await prisma.inventoryCalendar.createMany({
            data: calendarEntries,
            skipDuplicates: true
          });
        }

        console.log(`在庫カレンダーの作成が完了しました`);
      } catch (error) {
        console.error(`商品 ${product.id} の在庫カレンダーリセット中にエラーが発生しました:`, error);
      }
    }

    console.log('テスト予約のクリーンアップが完了しました');
  } catch (error) {
    console.error('クリーンアップ中にエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
cleanupTestBookings();
