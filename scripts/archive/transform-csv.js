#!/usr/bin/env node

/**
 * 既存システムのCSVファイルをShopify形式に変換するスクリプト
 * 
 * 使用方法:
 * ```
 * node transform-csv.js --input=existing-data.csv --output=shopify-products.csv
 * ```
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');

// コマンドライン引数を解析
const argv = yargs(hideBin(process.argv))
  .option('input', {
    alias: 'i',
    description: '入力CSVファイルのパス',
    type: 'string',
    demandOption: true
  })
  .option('output', {
    alias: 'o',
    description: '出力CSVファイルのパス',
    type: 'string',
    default: 'shopify-products.csv'
  })
  .option('metafields', {
    alias: 'm',
    description: 'メタフィールドCSVファイルのパス',
    type: 'string',
    default: 'shopify-metafields.csv'
  })
  .help()
  .alias('help', 'h')
  .argv;

// 入出力ファイルパスの取得
const inputFile = argv.input;
const outputFile = argv.output;
const metafieldsFile = argv.metafields;

// 変換処理
async function transformCsv() {
  console.log(`CSVファイルの変換を開始します: ${inputFile} -> ${outputFile}`);
  
  const inputRows = [];
  
  // 入力CSVを読み込み
  fs.createReadStream(inputFile)
    .pipe(csv())
    .on('data', (row) => {
      inputRows.push(row);
    })
    .on('end', () => {
      console.log(`${inputRows.length}件のデータを読み込みました`);
      
      // Shopify商品CSV用のデータ構造に変換
      const shopifyProducts = transformToShopifyProducts(inputRows);
      
      // Shopifyメタフィールド用のデータ構造に変換
      const shopifyMetafields = transformToShopifyMetafields(inputRows);
      
      // 商品CSVを書き込み
      writeShopifyProductsCsv(shopifyProducts, outputFile);
      
      // メタフィールドCSVを書き込み
      writeShopifyMetafieldsCsv(shopifyMetafields, metafieldsFile);
    });
}

/**
 * 既存データをShopify商品形式に変換
 */
function transformToShopifyProducts(inputRows) {
  const shopifyProducts = [];
  
  inputRows.forEach((row) => {
    // 商品の基本情報を作成
    const title = (row['商品名'] || '').trim();
    const productCode = (row['商品コード'] || '').trim();
    const detailCode = (row['商品詳細コード'] || '').trim();
    const category = (row['商品カテゴリ'] || '').trim();
    const price = parseInt(row['レンタル単価'] || '0', 10);
    const inventoryQty = parseInt(row['在庫数'] || '0', 10);
    
    // サイズ情報がある場合は結合
    let dimensions = '';
    if (row['サイズW'] || row['サイズD'] || row['サイズH']) {
      dimensions = `W${row['サイズW'] || '0'} × D${row['サイズD'] || '0'} × H${row['サイズH'] || '0'}`;
    }
    
    // 状態の判定
    let status = 'active';
    if (row['廃棄区分'] === '1' || row['商品ステータス'] === '1') {
      status = 'archived'; // 廃棄済み商品は非表示
    }
    
    // 商品説明を構築
    let bodyHtml = `<p>商品コード: ${productCode}</p>`;
    if (dimensions) {
      bodyHtml += `<p>サイズ: ${dimensions}</p>`;
    }
    if (row['特記事項']) {
      bodyHtml += `<p>特記事項: ${row['特記事項']}</p>`;
    }
    if (row['備考']) {
      bodyHtml += `<p>備考: ${row['備考']}</p>`;
    }
    
    // タグ設定
    let tags = [];
    if (row['タグ']) {
      tags.push(row['タグ']);
    }
    if (row['在庫場所']) {
      tags.push(`在庫場所:${row['在庫場所']}`);
    }
    if (category) {
      tags.push(`カテゴリ:${category}`);
    }
    
    // 商品データをShopify形式に変換
    shopifyProducts.push({
      'Handle': createHandle(title, productCode),
      'Title': title,
      'Body (HTML)': bodyHtml,
      'Vendor': row['メーカー名'] || 'オリジナル',
      'Product Category': 'Home & Garden',
      'Type': category || 'レンタル商品',
      'Tags': tags.join(', '),
      'Published': 'TRUE',
      'Option1 Name': 'サイズ',
      'Option1 Value': detailCode || 'デフォルト',
      'Option2 Name': '',
      'Option2 Value': '',
      'Option3 Name': '',
      'Option3 Value': '',
      'Variant SKU': productCode,
      'Variant Grams': '0',
      'Variant Inventory Tracker': 'shopify',
      'Variant Inventory Qty': inventoryQty.toString(),
      'Variant Inventory Policy': 'deny',
      'Variant Fulfillment Service': 'manual',
      'Variant Price': price.toString(),
      'Variant Compare At Price': '',
      'Variant Requires Shipping': 'TRUE',
      'Variant Taxable': 'TRUE',
      'Variant Barcode': row['JANコード'] || '',
      'Image Src': '',
      'Image Position': '',
      'Image Alt Text': '',
      'Gift Card': 'FALSE',
      'SEO Title': title,
      'SEO Description': `${title} - ${category} レンタル商品`,
      'Google Shopping / Google Product Category': '',
      'Google Shopping / Gender': '',
      'Google Shopping / Age Group': '',
      'Google Shopping / MPN': '',
      'Google Shopping / AdWords Grouping': '',
      'Google Shopping / AdWords Labels': '',
      'Google Shopping / Condition': '',
      'Google Shopping / Custom Product': '',
      'Google Shopping / Custom Label 0': '',
      'Google Shopping / Custom Label 1': '',
      'Google Shopping / Custom Label 2': '',
      'Google Shopping / Custom Label 3': '',
      'Google Shopping / Custom Label 4': '',
      'Variant Image': '',
      'Variant Weight Unit': 'kg',
      'Variant Tax Code': '',
      'Cost per item': row['購入金額'] || '',
      'Price / International': '',
      'Compare At Price / International': '',
      'Status': status
    });
  });
  
  return shopifyProducts;
}

/**
 * 既存データをShopifyメタフィールド形式に変換
 */
function transformToShopifyMetafields(inputRows) {
  const shopifyMetafields = [];
  
  inputRows.forEach((row) => {
    const handle = createHandle(row['商品名'], row['商品コード']);
    const productCode = (row['商品コード'] || '').trim();
    
    // 基本情報をメタフィールドとして追加
    const basicInfo = {
      productCode: productCode,
      detailCode: row['商品詳細コード'] || '',
      kana: row['商品名フリガナ'] || '',
      location: row['在庫場所'] || '',
      status: row['商品ステータス'] === '1' ? 'unavailable' : 'available'
    };
    
    shopifyMetafields.push({
      'Handle': handle,
      'Metafield: rental.basic_info [json]': JSON.stringify(basicInfo)
    });
    
    // 購入情報をメタフィールドとして追加
    const purchaseInfo = {
      manufacturer: row['メーカー名'] || '',
      purchasePlace: row['購入場所'] || '',
      purchaseDate: row['購入日'] || '',
      purchasePrice: row['購入金額'] ? parseInt(row['購入金額'], 10) : null,
      purchaseNotes: row['備考'] || ''
    };
    
    shopifyMetafields.push({
      'Handle': handle,
      'Metafield: rental.purchase_info [json]': JSON.stringify(purchaseInfo)
    });
    
    // メンテナンス情報をメタフィールドとして追加
    const maintenanceInfo = {
      maintenanceStatus: row['特記事項'] || '良好',
      lastMaintenanceDate: '',
      nextMaintenanceDate: '',
      maintenanceNotes: ''
    };
    
    shopifyMetafields.push({
      'Handle': handle,
      'Metafield: rental.maintenance_info [json]': JSON.stringify(maintenanceInfo)
    });
    
    // レンタル期間情報をメタフィールドとして追加
    const rentalDates = {
      startDate: row['レンタル開始日'] || '',
      endDate: row['レンタル終了日'] || ''
    };
    
    shopifyMetafields.push({
      'Handle': handle,
      'Metafield: rental.rental_dates [json]': JSON.stringify(rentalDates)
    });
    
    // 廃棄情報をメタフィールドとして追加
    if (row['廃棄区分'] === '1') {
      const disposalInfo = {
        isDisposed: true,
        disposalDate: row['廃棄日'] || '',
        disposalReason: row['特記事項'] || '',
        disposalMethod: '廃棄',
        disposalNotes: row['備考'] || ''
      };
      
      shopifyMetafields.push({
        'Handle': handle,
        'Metafield: rental.disposal_info [json]': JSON.stringify(disposalInfo)
      });
    }
    
    // 料金設定をメタフィールドとして追加
    const pricing = {
      basePrice: parseInt(row['レンタル単価'] || '0', 10),
      depositRate: 0.1,
      discountRules: {
        day2_6_rate: 0.2,
        day7_plus_rate: 0.1
      },
      excludedDays: {
        weekends: true,
        holidays: true,
        newYear: "12-29,01-03"
      }
    };
    
    shopifyMetafields.push({
      'Handle': handle,
      'Metafield: rental.pricing [json]': JSON.stringify(pricing)
    });
    
    // 予約情報の初期化
    const bookingsData = {
      bookings: [],
      availability: {
        rentalStatus: row['商品ステータス'] === '1' ? 'unavailable' : 'available',
        maintenanceDates: [],
        blockedDates: []
      }
    };
    
    shopifyMetafields.push({
      'Handle': handle,
      'Metafield: rental.bookings [json]': JSON.stringify(bookingsData)
    });
  });
  
  return shopifyMetafields;
}

/**
 * 商品名とコードからShopifyハンドルを生成
 */
function createHandle(title, code) {
  if (!title && !code) return 'rental-product';
  
  // ハンドル作成（URL安全な文字列に変換）
  let handle = '';
  
  if (title) {
    // 日本語を含む可能性があるため、カスタム処理
    handle = title
      .toLowerCase()
      .replace(/[０-９ａ-ｚＡ-Ｚ]/g, (s) => { // 全角英数字を半角に変換
        return String.fromCharCode(s.charCodeAt(0) - 0xFEE0);
      })
      .replace(/[^a-z0-9]+/g, '-') // 英数字以外をハイフンに変換
      .replace(/^-+|-+$/g, ''); // 先頭と末尾のハイフンを削除
  }
  
  // ハンドルが空か短すぎる場合はコードを追加
  if (!handle || handle.length < 3) {
    handle = code ? `rental-${code}` : 'rental-product';
  } else if (code) {
    handle += `-${code}`;
  }
  
  return handle;
}

/**
 * Shopify商品CSVを書き込み
 */
function writeShopifyProductsCsv(shopifyProducts, outputFile) {
  if (shopifyProducts.length === 0) {
    console.log('変換する商品がありません');
    return;
  }
  
  // ヘッダーの作成
  const header = Object.keys(shopifyProducts[0]).map(key => ({
    id: key,
    title: key
  }));
  
  // CSVライターの作成
  const csvWriter = createObjectCsvWriter({
    path: outputFile,
    header: header
  });
  
  // CSVの書き込み
  csvWriter.writeRecords(shopifyProducts)
    .then(() => {
      console.log(`商品CSVを書き込みました: ${outputFile} (${shopifyProducts.length}件)`);
    });
}

/**
 * Shopifyメタフィールドを書き込み
 */
function writeShopifyMetafieldsCsv(shopifyMetafields, metafieldsFile) {
  if (shopifyMetafields.length === 0) {
    console.log('変換するメタフィールドがありません');
    return;
  }
  
  // ヘッダーの作成
  const header = Object.keys(shopifyMetafields[0]).map(key => ({
    id: key,
    title: key
  }));
  
  // CSVライターの作成
  const csvWriter = createObjectCsvWriter({
    path: metafieldsFile,
    header: header
  });
  
  // CSVの書き込み
  csvWriter.writeRecords(shopifyMetafields)
    .then(() => {
      console.log(`メタフィールドCSVを書き込みました: ${metafieldsFile} (${shopifyMetafields.length}件)`);
    });
}

// 変換実行
transformCsv().catch(err => {
  console.error('エラーが発生しました:', err);
  process.exit(1);
});