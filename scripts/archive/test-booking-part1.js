/**
 * 予約システムテスト - パート1: テスト準備
 * 
 * このスクリプトでは以下を行います：
 * 1. テスト用商品の作成
 * 2. 既存の予約データの確認
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// テスト結果を保存する配列
const testResults = [];

/**
 * テスト結果を記録する関数
 */
function logTestResult(testName, success, details = {}) {
  const result = {
    testName,
    success,
    timestamp: new Date().toISOString(),
    ...details
  };
  
  testResults.push(result);
  
  // コンソールにも出力
  console.log(`[${success ? 'SUCCESS' : 'FAILURE'}] ${testName}`);
  if (details.message) {
    console.log(`  ${details.message}`);
  }
  if (details.error) {
    console.error(`  Error: ${details.error}`);
  }
}

/**
 * テスト用商品を作成する関数
 */
async function createTestProducts() {
  console.log("テスト用商品を作成しています...");
  
  const shop = "peaces-test-block.myshopify.com";
  
  const products = [
    {
      title: "テスト商品A",
      description: "テスト商品A説明",
      price: 5000,
      sku: "TEST-A-001",
      shopifyId: "test-a-001",
      shop,
      status: "AVAILABLE",
      basicInfo: {
        productCode: "TEST-A-001",
        status: "good",
        location: "NY"
      }
    },
    {
      title: "テスト商品B",
      description: "テスト商品B説明",
      price: 10000,
      sku: "TEST-B-001",
      shopifyId: "test-b-001",
      shop,
      status: "AVAILABLE",
      basicInfo: {
        productCode: "TEST-B-001",
        status: "good",
        location: "PR"
      }
    },
    {
      title: "テスト商品C",
      description: "テスト商品C説明",
      price: 15000,
      sku: "TEST-C-001",
      shopifyId: "test-c-001",
      shop,
      status: "AVAILABLE",
      basicInfo: {
        productCode: "TEST-C-001",
        status: "good",
        location: "NY"
      }
    }
  ];
  
  const createdProducts = [];
  
  // 商品を作成または更新
  for (const productData of products) {
    try {
      const product = await prisma.product.upsert({
        where: {
          shop_sku: {
            shop: productData.shop,
            sku: productData.sku
          }
        },
        update: productData,
        create: productData
      });
      
      createdProducts.push(product);
      console.log(`商品「${product.title}」を作成/更新しました (ID: ${product.id})`);
    } catch (error) {
      console.error(`商品「${productData.title}」の作成/更新に失敗しました:`, error);
    }
  }
  
  logTestResult("テスト用商品の作成", createdProducts.length === products.length, {
    message: `${createdProducts.length}/${products.length}件の商品を作成しました`,
    productIds: createdProducts.map(p => p.id)
  });
  
  return createdProducts;
}

/**
 * 既存の予約データを確認する関数
 */
async function checkExistingBookings() {
  console.log("既存の予約データを確認しています...");
  
  try {
    // 既存の予約を取得
    const existingBookings = await prisma.booking.findMany({
      orderBy: {
        startDate: 'asc'
      },
      include: {
        product: true
      }
    });
    
    console.log(`データベース内の予約数: ${existingBookings.length}件\n`);
    
    if (existingBookings.length === 0) {
      console.log("予約データが見つかりませんでした。");
      return [];
    }
    
    console.log("予約データの詳細:\n");
    
    existingBookings.forEach((booking, index) => {
      console.log(`予約 ${index + 1}:`);
      console.log(`ID: ${booking.id}`);
      console.log(`予約ID: ${booking.bookingId}`);
      console.log(`商品ID: ${booking.productId}`);
      console.log(`商品名: ${booking.product?.title || '不明'}`);
      console.log(`開始日: ${booking.startDate}`);
      console.log(`終了日: ${booking.endDate}`);
      console.log(`予約タイプ: ${booking.bookingType}`);
      console.log(`ステータス: ${booking.status}`);
      console.log(`顧客メール: ${booking.customerEmail || '不明'}`);
      console.log(`ショップ: ${booking.shop}`);
      console.log("");
    });
    
    logTestResult("既存の予約データ確認", true, {
      message: `${existingBookings.length}件の予約データを確認しました`,
      bookingCount: existingBookings.length
    });
    
    return existingBookings;
  } catch (error) {
    console.error("予約データの確認中にエラーが発生しました:", error);
    
    logTestResult("既存の予約データ確認", false, {
      error: error.message
    });
    
    return [];
  }
}

/**
 * 在庫カレンダーを確認する関数
 */
async function checkInventoryCalendar() {
  console.log("在庫カレンダーを確認しています...");
  
  try {
    // 在庫カレンダーを取得
    const inventoryCalendars = await prisma.inventoryCalendar.findMany({
      where: {
        isAvailable: false
      },
      take: 10,
      orderBy: {
        date: 'asc'
      },
      include: {
        product: true
      }
    });
    
    console.log(`在庫カレンダーの予約済み日数: ${inventoryCalendars.length}件以上\n`);
    
    if (inventoryCalendars.length === 0) {
      console.log("予約済みの在庫カレンダーデータが見つかりませんでした。");
      return [];
    }
    
    console.log("在庫カレンダーのサンプル:\n");
    
    inventoryCalendars.forEach((calendar, index) => {
      console.log(`カレンダー ${index + 1}:`);
      console.log(`ID: ${calendar.id}`);
      console.log(`商品ID: ${calendar.productId}`);
      console.log(`商品名: ${calendar.product?.title || '不明'}`);
      console.log(`日付: ${calendar.date}`);
      console.log(`利用可能: ${calendar.isAvailable ? 'はい' : 'いいえ'}`);
      console.log(`利用不可理由: ${calendar.unavailableReason || '不明'}`);
      console.log("");
    });
    
    logTestResult("在庫カレンダー確認", true, {
      message: `${inventoryCalendars.length}件の在庫カレンダーデータを確認しました`,
      calendarCount: inventoryCalendars.length
    });
    
    return inventoryCalendars;
  } catch (error) {
    console.error("在庫カレンダーの確認中にエラーが発生しました:", error);
    
    logTestResult("在庫カレンダー確認", false, {
      error: error.message
    });
    
    return [];
  }
}

/**
 * メイン関数
 */
async function runTests() {
  console.log("=== 予約システムテスト - パート1: テスト準備 ===\n");
  
  try {
    // テスト用商品を作成
    const products = await createTestProducts();
    
    // 既存の予約データを確認
    const bookings = await checkExistingBookings();
    
    // 在庫カレンダーを確認
    const inventoryCalendars = await checkInventoryCalendar();
    
    // テスト結果のサマリーを表示
    console.log("\n=== テスト結果サマリー ===");
    const successCount = testResults.filter(r => r.success).length;
    const failureCount = testResults.length - successCount;
    
    console.log(`実行したテスト数: ${testResults.length}`);
    console.log(`成功: ${successCount}`);
    console.log(`失敗: ${failureCount}`);
    console.log("============================\n");
    
    // 詳細なテスト結果を表示
    console.log("詳細なテスト結果:");
    testResults.forEach((result, index) => {
      console.log(`${index + 1}. [${result.success ? 'SUCCESS' : 'FAILURE'}] ${result.testName}`);
      if (result.message) {
        console.log(`   ${result.message}`);
      }
    });
    
    // テスト結果を返す
    return {
      products,
      bookings,
      inventoryCalendars,
      testResults
    };
  } catch (error) {
    console.error("テスト実行中にエラーが発生しました:", error);
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// テストを実行
runTests();
