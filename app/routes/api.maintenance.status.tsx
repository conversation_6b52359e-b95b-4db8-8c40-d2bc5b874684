import { ActionFunctionArgs, json } from '@remix-run/node';
import { authenticate } from '../shopify.server';
import { prisma } from '../db.server';
import { MaintenanceStatus } from '@prisma/client';

export async function action({ request }: ActionFunctionArgs) {
  await authenticate.admin(request);

  if (request.method !== 'POST') {
    return json({ error: 'Method not allowed' }, { status: 405 });
  }

  try {
    const { id, status } = await request.json();

    if (!id || !status) {
      return json({ error: 'IDとステータスが必要です' }, { status: 400 });
    }

    // ステータスの妥当性をチェック
    const validStatuses: MaintenanceStatus[] = ['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'];
    if (!validStatuses.includes(status)) {
      return json({ error: '無効なステータスです' }, { status: 400 });
    }

    // メンテナンス記録を更新
    const updatedMaintenance = await prisma.maintenance.update({
      where: { id },
      data: {
        status,
        completionDate: status === 'COMPLETED' ? new Date() : null
      },
      include: {
        product: true
      }
    });

    return json({
      success: true,
      maintenance: {
        id: updatedMaintenance.id,
        status: updatedMaintenance.status,
        completionDate: updatedMaintenance.completionDate?.toISOString()
      }
    });

  } catch (error) {
    console.error('ステータス更新エラー:', error);
    return json({ error: 'ステータスの更新に失敗しました' }, { status: 500 });
  }
}
