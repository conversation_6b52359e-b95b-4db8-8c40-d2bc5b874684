#!/bin/bash

# 予約システムのテストを実行するスクリプト

# 色の定義
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}予約システムのテストを実行します...${NC}"

# 現在のディレクトリを保存
CURRENT_DIR=$(pwd)

# スクリプトのディレクトリに移動
cd "$(dirname "$0")"

# テストファイルのリスト
TEST_FILES=(
  "booking-availability.test.ts"
  "japan-time.test.ts"
  "prisma-schema.test.ts"
)

# 各テストファイルを実行
for test_file in "${TEST_FILES[@]}"; do
  echo -e "${YELLOW}テスト実行中: ${test_file}${NC}"
  npx vitest run "$test_file"

  # テスト結果を確認
  if [ $? -eq 0 ]; then
    echo -e "${GREEN}テスト成功: ${test_file}${NC}"
  else
    echo -e "${RED}テスト失敗: ${test_file}${NC}"
  fi

  echo ""
done

# 元のディレクトリに戻る
cd "$CURRENT_DIR"

echo -e "${YELLOW}すべてのテストが完了しました${NC}"