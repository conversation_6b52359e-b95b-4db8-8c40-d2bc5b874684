import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();

// Shopify API設定
const config = {
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block',
  apiSecretKey: process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
};

// 設定の検証
if (!config.apiSecretKey) {
  console.error('エラー: SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません。');
  process.exit(1);
}

// ショップ名を正規化
const shopName = config.shop.replace('.myshopify.com', '');

// Shopify GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${shopName}.myshopify.com/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': config.apiSecretKey,
      'Content-Type': 'application/json',
    },
  }
);

const GET_PRODUCTS_QUERY = gql`
  query GetProducts($first: Int!, $after: String) {
    products(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        id
        title
        handle
        status
        createdAt
        variants(first: 10) {
          nodes {
            id
            sku
          }
        }
      }
    }
  }
`;

const DELETE_PRODUCT_MUTATION = gql`
  mutation DeleteProduct($input: ProductDeleteInput!) {
    productDelete(input: $input) {
      deletedProductId
      userErrors {
        field
        message
      }
    }
  }
`;

interface Product {
  id: string;
  title: string;
  handle: string;
  status: string;
  createdAt: string;
  variants: {
    nodes: Array<{
      id: string;
      sku: string;
    }>;
  };
}

async function getAllProducts(): Promise<Product[]> {
  console.log('🔍 Shopifyから全商品を取得中...');
  
  const products: Product[] = [];
  let hasNextPage = true;
  let after: string | null = null;

  while (hasNextPage) {
    const response = await client.request(GET_PRODUCTS_QUERY, {
      first: 50,
      after
    });

    const data = response as { products: { pageInfo: { hasNextPage: boolean; endCursor: string }; nodes: Product[] } };
    
    products.push(...data.products.nodes);
    hasNextPage = data.products.pageInfo.hasNextPage;
    after = data.products.pageInfo.endCursor;
  }

  console.log(`📦 ${products.length}件の商品を取得しました`);
  return products;
}

async function identifyDuplicates(products: Product[]): Promise<{ keep: Product[]; delete: Product[] }> {
  console.log('🔍 重複商品を特定中...');
  
  // タイトルでグループ化
  const groups = new Map<string, Product[]>();
  
  for (const product of products) {
    const baseTitle = product.title.replace(/\s*\[\d+\]$/, ''); // [1], [2]などを除去
    
    if (!groups.has(baseTitle)) {
      groups.set(baseTitle, []);
    }
    groups.get(baseTitle)!.push(product);
  }

  const keep: Product[] = [];
  const toDelete: Product[] = [];

  for (const [title, groupProducts] of groups) {
    if (groupProducts.length === 1) {
      // 重複なしの商品はそのまま保持
      keep.push(groupProducts[0]);
    } else {
      // 重複商品がある場合
      console.log(`\n📋 重複商品グループ: "${title}" (${groupProducts.length}件)`);
      
      // 作成日時でソート（古い順）
      groupProducts.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
      
      // 最初の商品を保持、残りは削除対象
      const keepProduct = groupProducts[0];
      const deleteProducts = groupProducts.slice(1);
      
      console.log(`   ✅ 保持: ${keepProduct.id} (${keepProduct.createdAt})`);
      for (const product of deleteProducts) {
        console.log(`   ❌ 削除: ${product.id} (${product.createdAt})`);
      }
      
      keep.push(keepProduct);
      toDelete.push(...deleteProducts);
    }
  }

  console.log(`\n📊 重複商品特定結果:`);
  console.log(`   保持する商品: ${keep.length}件`);
  console.log(`   削除する商品: ${toDelete.length}件`);

  return { keep, delete: toDelete };
}

async function deleteProduct(product: Product): Promise<boolean> {
  try {
    console.log(`🗑️ 商品削除中: ${product.title} (${product.id})`);
    
    const response = await client.request(DELETE_PRODUCT_MUTATION, {
      input: { id: product.id }
    });

    const result = response as { productDelete: { deletedProductId: string | null; userErrors: Array<{ field: string; message: string }> } };
    
    if (result.productDelete.userErrors.length > 0) {
      console.error(`❌ 削除エラー: ${product.title}`, result.productDelete.userErrors);
      return false;
    }

    if (result.productDelete.deletedProductId) {
      console.log(`✅ 削除完了: ${product.title}`);
      
      // Prismaからも削除
      const productId = product.id.replace('gid://shopify/Product/', '');
      try {
        await prisma.product.deleteMany({
          where: {
            shopifyProductId: productId
          }
        });
        console.log(`   📄 Prismaからも削除完了`);
      } catch (error) {
        console.log(`   ⚠️ Prisma削除スキップ (商品が存在しない可能性)`);
      }
      
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 削除中にエラー: ${product.title}`, error);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 重複商品削除ツールを開始...');
    
    // 1. 全商品を取得
    const allProducts = await getAllProducts();
    
    if (allProducts.length === 0) {
      console.log('📝 削除対象の商品がありません。');
      return;
    }

    // 2. 重複商品を特定
    const { keep, delete: toDelete } = await identifyDuplicates(allProducts);
    
    if (toDelete.length === 0) {
      console.log('✅ 重複商品はありません。');
      return;
    }

    // 3. 確認プロンプト
    console.log(`\n⚠️ ${toDelete.length}件の重複商品を削除します。`);
    console.log('削除対象商品:');
    for (const product of toDelete) {
      console.log(`   - ${product.title} (${product.id})`);
    }
    
    // 実際の削除実行（本番環境では確認が必要）
    if (process.argv.includes('--force')) {
      console.log('\n🗑️ 削除を開始します...');
      
      let successCount = 0;
      let errorCount = 0;
      
      for (const product of toDelete) {
        const success = await deleteProduct(product);
        if (success) {
          successCount++;
        } else {
          errorCount++;
        }
        
        // API制限回避のため少し待機
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      console.log(`\n📊 削除結果:`);
      console.log(`   成功: ${successCount}件`);
      console.log(`   失敗: ${errorCount}件`);
      console.log(`   残り商品数: ${keep.length}件`);
      
    } else {
      console.log('\n💡 削除を実行するには --force フラグを付けてください:');
      console.log('   npx tsx scripts/delete-duplicate-products.ts --force');
    }

  } catch (error) {
    console.error('❌ エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// メイン実行
main().catch(console.error);