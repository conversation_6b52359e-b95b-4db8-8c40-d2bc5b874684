# 予約システム本番環境展開手順

## 目次

1. [概要](#概要)
2. [前提条件](#前提条件)
3. [デプロイ前の準備](#デプロイ前の準備)
4. [デプロイ手順](#デプロイ手順)
5. [デプロイ後の検証](#デプロイ後の検証)
6. [トラブルシューティング](#トラブルシューティング)
7. [ロールバック手順](#ロールバック手順)

## 概要

このドキュメントでは、レンタル予約システムを本番環境に展開するための詳細な手順を説明します。本番環境への展開は、以下の主要なステップで構成されています：

1. デプロイ前の準備（テスト、環境設定）
2. アプリのデプロイ（Render/Cloudflareへ）
3. データベースの設定（Neon PostgreSQL）
4. メタフィールドの設定
5. テーマアプリ拡張機能のデプロイ
6. デプロイ後の検証

## 前提条件

- 本番用のShopifyストアが作成済みであること
- Shopify Partner アカウントへのアクセス権があること
- Neonデータベースのアカウントがあること
- 以下の開発ツールがインストールされていること:
  - Node.js (v18以上)
  - npm (v9以上)
  - Shopify CLI (v3以上)
  - Git

## デプロイ前の準備

### 1. テストの実行

デプロイ前に、すべてのテストを実行して機能を検証します。

```bash
# 休日を挟んだレンタルテスト
node scripts/test-holiday-rental.js

# 長期・短期レンタルテスト
node scripts/test-rental-duration.js

# 包括的なレンタルテスト
node scripts/comprehensive-rental-test-all.js

# 予約の重複チェックテスト
node scripts/test-rental-booking.js
```

すべてのテストが成功することを確認してください。

### 2. 環境変数の準備

本番環境用の環境変数を準備します。以下の環境変数が必要です：

```
# Shopify App
SHOPIFY_API_KEY=本番用APIキー
SHOPIFY_API_SECRET=本番用APIシークレット
SCOPES=write_products,write_orders,read_customers,write_draft_orders
HOST=https://your-production-app-url.com
SHOPIFY_APP_URL=https://your-production-app-url.com

# Database
DATABASE_URL=本番用Neon PostgreSQL接続文字列

# App Settings
SESSION_SECRET=ランダムな文字列
NODE_ENV=production
```

### 3. 本番環境の準備

#### 3.1 本番用Shopifyストアの設定

1. Shopify管理画面にログインし、本番用ストアの基本設定を行います。
   - ストア名、通貨、言語などの基本設定
   - 支払い方法の設定
   - 配送設定

2. Shopify Partner ダッシュボードで新しいアプリを作成します。
   ```
   アプリ名: レンタル管理システム
   アプリURL: https://your-production-app-url.com
   リダイレクトURL: https://your-production-app-url.com/auth/callback
   ```

3. アプリのAPIキーとシークレットを取得し、安全に保管します。

#### 3.2 本番用Neonデータベースの作成

1. Neonダッシュボード（https://console.neon.tech）にログインします。

2. 新しいプロジェクトを作成します。
   ```
   プロジェクト名: rental-management-production
   リージョン: 最も近いリージョンを選択
   ```

3. データベース接続情報を取得し、安全に保管します。
   - 接続文字列
   - ホスト名
   - データベース名
   - ユーザー名
   - パスワード

## デプロイ手順

### 1. アプリのデプロイ

#### 1.1 アプリコードのクローンと依存関係のインストール

```bash
# リポジトリをクローン
git clone https://github.com/your-username/rental-management-app.git
cd rental-management-app

# 依存関係をインストール
npm install
```

#### 1.2 本番用設定ファイルの構成

1. `shopify.app.toml`ファイルを本番環境用に更新します。
```toml
name = "レンタル管理システム"
client_id = "your_shopify_api_key"
application_url = "https://your-production-app-url.com"
embedded = true

[build]
dev_command = "npm run dev"
build_command = "npm run build"

[auth]
redirect_urls = ["https://your-production-app-url.com/auth/callback"]

[webhooks]
api_version = "2023-10"

[pos]
embedded = false
```

2. 本番環境用の`.env`ファイルを作成します。
```
# 前述の環境変数を設定
```

#### 1.3 アプリのビルドとデプロイ

##### Renderへのデプロイ

1. Renderダッシュボード（https://dashboard.render.com）にログインします。

2. 「New +」ボタンをクリックし、「Web Service」を選択します。

3. GitリポジトリをRenderに接続します。

4. 以下の設定を行います：
   - Name: rental-management-production
   - Environment: Node
   - Build Command: `npm install && npx prisma generate && npm run build`
   - Start Command: `npm start`
   - 環境変数を設定

5. 「Create Web Service」ボタンをクリックしてデプロイを開始します。

##### Cloudflareへのデプロイ（代替手段）

1. Cloudflare Pagesダッシュボードにログインします。

2. 「Create a project」ボタンをクリックします。

3. GitリポジトリをCloudflareに接続します。

4. 以下の設定を行います：
   - Project name: rental-management-production
   - Build command: `npm install && npx prisma generate && npm run build`
   - Build output directory: `/build/client`
   - 環境変数を設定

5. 「Save and Deploy」ボタンをクリックしてデプロイを開始します。

### 2. データベースの設定

#### 2.1 データベーススキーマの適用

```bash
# Prismaスキーマをデータベースに適用
npx prisma db push

# または、マイグレーションを実行
npx prisma migrate deploy
```

#### 2.2 初期データの設定

```bash
# 必要な初期データをデータベースに設定
node scripts/setup/init-database.js
```

### 3. メタフィールドの設定

#### 3.1 メタフィールド定義の作成

```bash
# メタフィールド設定スクリプトを実行
node scripts/setup/metafields-setup.js
```

以下のメタフィールドが正しく作成されたことを確認します：
- `rental.basic_info` (JSON)
- `rental.pricing` (JSON)
- `rental.location` (single_line_text_field)

#### 3.2 メタフィールドの値の設定

```bash
# 既存の商品に対してメタフィールドの値を設定
node scripts/set-json-metafield.js
```

### 4. テーマアプリ拡張機能のデプロイ

#### 4.1 テーマアプリ拡張機能のデプロイ

```bash
# テーマアプリ拡張機能のディレクトリに移動
cd extensions/rental-calendar

# テーマアプリ拡張機能をデプロイ
shopify app deploy
```

#### 4.2 テーマへのブロック追加

1. Shopify管理画面でテーマをカスタマイズします。
   - テーマ > カスタマイズ

2. 商品ページテンプレートを編集し、「レンタル日程選択」ブロックを追加します。

3. ブロックの設定を行い、保存します。

## デプロイ後の検証

デプロイ後に以下の機能を検証します：

### 1. レンタル日程選択機能

- 商品ページで日程選択ブロックが表示されるか
- 日付を選択できるか
- 料金が正しく計算されるか
- 休日（日曜日、祝日、年末年始）が正しく除外されるか

### 2. ドラフトオーダー作成機能

- カートに追加ボタンをクリックしてドラフトオーダーが作成されるか
- ドラフトオーダーに正しい情報（日程、料金など）が含まれているか

### 3. 予約管理機能

- 管理画面で予約一覧が表示されるか
- 予約詳細を確認できるか
- 予約の作成、更新、削除ができるか

### 4. 在庫管理機能

- 商品の在庫状態が正しく表示されるか
- 予約済みの日程が正しく反映されるか

### 5. 休日を挟んだレンタルテスト

- 日曜日を含むレンタル期間で正しく料金計算されるか
- 祝日を含むレンタル期間で正しく料金計算されるか
- 年末年始を含むレンタル期間で正しく料金計算されるか

### 6. 長期・短期レンタルテスト

- 短期レンタル（1-2日）で正しく料金計算されるか
- 中期レンタル（3-6日）で正しく料金計算されるか
- 長期レンタル（7日以上）で正しく料金計算されるか

## トラブルシューティング

### アプリのインストールに失敗する場合

1. Shopify Partner ダッシュボードでアプリの設定を確認します。
   - APIキーとシークレットが正しいか
   - リダイレクトURLが正しいか

2. アプリのログを確認します。
   - デプロイ先のログダッシュボードを確認

### データベース接続エラーが発生する場合

1. 環境変数`DATABASE_URL`が正しく設定されているか確認します。

2. Neonダッシュボードでデータベースの状態を確認します。
   - データベースが起動しているか
   - IPアドレス制限がないか

### メタフィールドの設定に失敗する場合

1. Shopify APIのスコープが正しく設定されているか確認します。
   - `write_products`スコープが含まれているか

2. スクリプトのログを確認し、エラーメッセージを分析します。

### 料金計算が正しくない場合

1. 日付の処理が正しく行われているか確認します。
   - 日曜日、祝日、年末年始が正しく除外されているか

2. 料金計算ロジックが正しく実装されているか確認します。
   - 1日目、2-6日目、7日目以降の料率が正しいか

## ロールバック手順

問題が発生した場合のロールバック手順です。

### アプリのロールバック

1. 以前のバージョンのアプリにロールバックします。
   - デプロイ先のダッシュボードで以前のデプロイに戻す

### データベースのロールバック

1. Neonダッシュボードでデータベースのバックアップを復元します。
   - バックアップポイントを選択
   - 復元を実行

### メタフィールドのロールバック

1. メタフィールド復元スクリプトを実行します。
```bash
node scripts/restore-metafields.js
```

---

**注意事項:**
- 本番環境への移行は、営業時間外または影響の少ない時間帯に行うことをお勧めします。
- 移行前に必ずデータのバックアップを取得してください。
- 各ステップを実行する前に、前のステップが正常に完了していることを確認してください。
- 問題が発生した場合は、すぐにロールバック手順を実行してください。
