/**
 * 予約API
 * 
 * このAPIは、予約データを取得・操作するためのエンドポイントを提供します。
 * - GET /api/bookings - 予約一覧を取得
 * - GET /api/bookings?id=xxx - 特定の予約を取得
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { logger } from "../utils/logger";

/**
 * GET: 予約データを取得
 */
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // 認証
    const { session } = await authenticate.admin(request);
    const shop = session.shop;

    // URLパラメータの取得
    const url = new URL(request.url);
    const id = url.searchParams.get("id");
    const limit = parseInt(url.searchParams.get("limit") || "20", 10);
    const page = parseInt(url.searchParams.get("page") || "1", 10);
    const searchQuery = url.searchParams.get("q") || "";
    const statusFilter = url.searchParams.get("status") || "";
    const sortField = url.searchParams.get("sort") || "createdAt";
    const sortOrder = url.searchParams.get("order") || "desc";

    // 特定の予約IDが指定されている場合
    if (id) {
      const booking = await prisma.booking.findUnique({
        where: { id, shop },
        include: {
          product: {
            select: {
              id: true,
              title: true,
              sku: true,
              status: true,
              shopifyId: true,
              basicInfo: true,
            },
          },
        },
      });

      if (!booking) {
        return json({ error: "予約が見つかりません" }, { status: 404 });
      }

      // 商品情報のフォールバック処理
      const productInfo = booking.product || {
        id: booking.productId,
        title: "不明な商品",
        sku: "",
        status: "UNKNOWN",
        shopifyId: "",
        basicInfo: {},
      };

      // 商品情報をログに出力（デバッグ用）
      logger.debug(`商品情報: ${JSON.stringify(productInfo)}`);

      return json({
        booking: {
          ...booking,
          product: productInfo,
        },
      });
    }

    // 検索条件を構築
    let whereCondition: any = { shop };

    // 検索クエリがある場合
    if (searchQuery) {
      whereCondition.OR = [
        { customerName: { contains: searchQuery, mode: 'insensitive' } },
        { customerEmail: { contains: searchQuery, mode: 'insensitive' } },
        { bookingId: { contains: searchQuery, mode: 'insensitive' } },
        { customerId: { contains: searchQuery, mode: 'insensitive' } },
        {
          product: {
            OR: [
              { title: { contains: searchQuery, mode: 'insensitive' } },
              { sku: { contains: searchQuery, mode: 'insensitive' } },
            ],
          },
        },
      ];
    }

    // ステータスフィルターがある場合
    if (statusFilter) {
      whereCondition.status = statusFilter;
    }

    // 並び順を設定
    const orderBy: any = {};
    orderBy[sortField] = sortOrder;

    // 予約データを取得（商品情報も含める）
    const bookings = await prisma.booking.findMany({
      where: whereCondition,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      include: {
        product: {
          select: {
            id: true,
            title: true,
            sku: true,
            status: true,
            shopifyId: true,
            basicInfo: true,
          },
        },
      },
    });

    // 検索結果をログに出力（デバッグ用）
    logger.debug(`検索結果: ${bookings.length}件の予約が見つかりました`);

    // 総予約数を取得
    const totalBookings = await prisma.booking.count({
      where: whereCondition,
    });

    // 予約データをフォーマット
    const formattedBookings = bookings.map(booking => {
      // 商品情報を取得（商品が見つからない場合はフォールバック）
      const product = booking.product || {
        id: booking.productId,
        title: "不明な商品",
        sku: "",
        status: "UNKNOWN",
        shopifyId: "",
        basicInfo: {},
      };

      // 商品情報を取得
      const sku = product.sku || "";
      const basicInfo = product.basicInfo as any || {};
      const productCode = basicInfo.productCode || "";

      // 表示用のSKUを決定（元のSKUがある場合はそれを使用、なければproductCodeを使用）
      const displaySku = sku.includes("-") ? sku.split("-")[0] : sku || productCode || "";

      // 商品IDとShopify IDをログに出力（デバッグ用）
      if (process.env.NODE_ENV === "development") {
        logger.debug(`商品情報 - ID: ${booking.productId}, ShopifyID: ${product.shopifyId}, SKU: ${sku}, ProductCode: ${productCode}`);
      }

      return {
        id: booking.id,
        bookingId: booking.bookingId || "",
        productId: booking.productId,
        sku: displaySku,
        originalSku: sku,
        productCode,
        productTitle: product.title || "不明な商品",
        productStatus: product.status || "UNKNOWN",
        startDate: booking.startDate ? booking.startDate.toISOString() : null,
        endDate: booking.endDate ? booking.endDate.toISOString() : null,
        status: booking.status,
        customerId: booking.customerId || "",
        customerName: booking.customerName || "",
        customerEmail: booking.customerEmail || "",
        totalAmount: booking.totalAmount || 0,
        paymentMethod: booking.paymentMethod || "UNKNOWN",
        paymentStatus: booking.paymentStatus || "PENDING",
        orderId: booking.orderId || "",
        orderName: booking.orderName || "",
        createdAt: booking.createdAt ? booking.createdAt.toISOString() : null,
        updatedAt: booking.updatedAt ? booking.updatedAt.toISOString() : null,
        shopifyProductId: product.shopifyId || "",
      };
    });

    return json({
      bookings: formattedBookings,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalBookings / limit),
        totalItems: totalBookings,
        limit,
      },
      filters: {
        query: searchQuery,
        status: statusFilter,
        sort: sortField,
        order: sortOrder,
      },
    });
  } catch (error) {
    logger.error("予約データ取得エラー:", error);
    return json(
      { error: `予約データの取得中にエラーが発生しました: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}
