# freee連携機能の実装計画

**最終更新日**: 2024-04-30

## 概要

このドキュメントでは、レンタルシステムとfreeeを連携するための実装計画について説明します。freeeとの連携により、レンタル注文（事前）と配送料（返却時）の2つの注文を一括で請求管理できるようになります。

## freeeアプリ情報

以下のfreeeアプリが作成済みです：

- **アプリ名**: easeレンタル請求書
- **概要**: レンタル事業の請求書の管理をshopifyと連携
- **コールバックURL**: urn:ietf:wg:oauth:2.0:oob
- **Client ID**: ***************
- **Client Secret**: _HmbAJP_JDir2BfSZJQVbxsU0MVHj2ecg3Vhad51NSh1kw6OCMB7uZGWGs8Xr5YtjD8FwBb-a9DG6_Z9_6Zzfw
- **Webアプリ認証用URL**: https://accounts.secure.freee.co.jp/public_api/authorize?client_id=***************&redirect_uri=urn%3Aietf%3Awg%3Aoauth%3A2.0%3Aoob&response_type=code&prompt=select_company

## 目的

1. レンタル注文と配送料注文を一括で請求書発行できるようにする
2. 銀行振込や店頭支払いの場合のまとめ請求に対応する
3. 請求状況と入金状況を一元管理する
4. 手動での二重入力を避け、ミスを減らす

## 実装アプローチ

### フェーズ1: freee連携のPoC（概念実証）

**期間**: 2週間以内（フロントエンド基本フローの完成と並行して実施）

1. **freee APIの調査と認証方法の確認**
   - OAuth2.0認証フローの実装
   - アクセストークンとリフレッシュトークンの管理
   - 認証情報の安全な保存方法の検討

2. **簡易的な連携スクリプトの作成**
   - 請求書API（/invoices）の呼び出しテスト
   - 会計API（/companies, /deals）の呼び出しテスト
   - 基本的なデータ構造の確認

3. **請求書作成と取得のテスト**
   - 請求書テンプレートの取得と選択
   - 請求書データの構築と送信
   - 請求書ステータスの確認

4. **注文IDと請求IDの関連付けテスト**
   - 関連付けデータモデルの設計
   - 関連付け情報の保存と取得
   - 複数注文の一括処理の検証

### フェーズ2: テスト用APIエンドポイントの実装

**期間**: 2週間

1. **freee連携用のサービスクラスの作成**
   - FreeeService.tsの実装
   - 認証情報の安全な管理方法の実装
   - トークンの自動リフレッシュ機能

2. **APIエンドポイントの実装**
   - 請求書作成エンドポイント
   - 請求書取得エンドポイント
   - 請求書ステータス更新エンドポイント

3. **エラーハンドリングの実装**
   - API呼び出しエラーの処理
   - リトライ機能の実装
   - エラーログの記録

### フェーズ3: 注文・請求連携フローの実装

**期間**: 2週間

1. **レンタル注文と配送料注文の関連付け**
   - 同一顧客の複数注文の検出
   - 関連注文の一括処理機能
   - 注文ステータスの更新機能

2. **複数注文の一括請求機能**
   - 請求書データの構築ロジック
   - 請求書明細の自動生成
   - 請求書番号の自動採番

3. **請求ステータスの同期機能**
   - 定期的な請求ステータス確認
   - 入金状況の自動更新
   - 請求ステータス変更時の通知機能

### フェーズ4: 請求管理ダッシュボード

**期間**: 2週間

1. **請求状況の一覧表示**
   - フィルタリングと検索機能
   - ステータス別表示
   - 期間別集計機能

2. **入金状況の管理**
   - 入金確認機能
   - 未入金アラート機能
   - 入金予定日の管理

3. **請求書の生成と送信**
   - PDFダウンロード機能
   - メール送信機能
   - 一括処理機能

## データモデル

### OrderInvoice（注文と請求書の関連付け）

```typescript
interface OrderInvoice {
  id: string;
  orderId: string;
  invoiceId: string;
  invoiceNumber: string;
  amount: number;
  status: 'draft' | 'sent' | 'paid' | 'canceled';
  createdAt: Date;
  updatedAt: Date;
}
```

### FreeeToken（認証トークン）

```typescript
interface FreeeToken {
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
  scope: string;
  createdAt: Date;
  updatedAt: Date;
}
```

## API設計

### 請求書作成API

```
POST /api/freee/invoices
```

リクエスト:
```json
{
  "orderIds": ["ORDER-001", "SHIPPING-001"],
  "dueDate": "2024-05-31",
  "title": "レンタル料金と配送料",
  "description": "レンタル期間: 2024-05-01 〜 2024-05-05"
}
```

レスポンス:
```json
{
  "success": true,
  "invoiceId": "123456",
  "invoiceNumber": "INV-20240430-001",
  "url": "https://secure.freee.co.jp/invoices/123456"
}
```

### 請求書ステータス取得API

```
GET /api/freee/invoices/:invoiceId
```

レスポンス:
```json
{
  "invoiceId": "123456",
  "invoiceNumber": "INV-20240430-001",
  "status": "sent",
  "dueDate": "2024-05-31",
  "paidAt": null,
  "amount": 15000,
  "url": "https://secure.freee.co.jp/invoices/123456"
}
```

### 請求書一覧取得API

```
GET /api/freee/invoices
```

クエリパラメータ:
- `status`: 請求書のステータス（draft, sent, paid, canceled）
- `from`: 開始日
- `to`: 終了日
- `page`: ページ番号
- `limit`: 1ページあたりの件数

レスポンス:
```json
{
  "invoices": [
    {
      "invoiceId": "123456",
      "invoiceNumber": "INV-20240430-001",
      "status": "sent",
      "dueDate": "2024-05-31",
      "paidAt": null,
      "amount": 15000,
      "url": "https://secure.freee.co.jp/invoices/123456",
      "orders": [
        {
          "orderId": "ORDER-001",
          "orderNumber": "ORD-20240430-001",
          "amount": 10000
        },
        {
          "orderId": "SHIPPING-001",
          "orderNumber": "SHP-20240430-001",
          "amount": 5000
        }
      ]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1
  }
}
```

## セキュリティ考慮事項

1. **認証情報の保護**
   - アクセストークンとリフレッシュトークンは暗号化して保存
   - 定期的なキーローテーション
   - 環境変数を使用してシークレットを管理

2. **アクセス制御**
   - 請求管理機能へのアクセスは管理者権限を持つユーザーのみに制限
   - APIエンドポイントへのアクセスは認証済みユーザーのみに制限
   - 操作ログの記録と監査

3. **エラーハンドリング**
   - センシティブな情報をエラーメッセージに含めない
   - 適切なHTTPステータスコードの使用
   - 詳細なエラーログの記録（ただし機密情報は含めない）

## 今後の課題

1. **スケーラビリティ**
   - 大量の注文と請求書を効率的に処理する方法
   - バッチ処理の最適化

2. **障害対策**
   - freee APIが一時的に利用できない場合のリトライ戦略
   - データの整合性を確保するための方法

3. **ユーザビリティ**
   - 請求管理ダッシュボードのUI/UX改善
   - 一括操作機能の拡充

## テスト方法

freee連携のテストは、以下の手順で実施できます：

### 1. PoCスクリプトの実行

`scripts/freee-poc/freee-api-test.js`スクリプトを使用して、freee APIとの連携をテストできます。

```bash
# 必要なパッケージをインストール
cd scripts/freee-poc
npm install axios dotenv

# スクリプトを実行
node freee-api-test.js
```

スクリプトを実行すると、以下の処理が行われます：

1. freeeの認証URLが表示されます
2. 表示されたURLにアクセスして認証を行い、認証コードを取得します
3. 認証コードをコンソールに入力します
4. アクセストークンとリフレッシュトークンが取得され、`token.json`に保存されます
5. 事業所一覧、取引先一覧、請求書テンプレート一覧が取得されます
6. テスト用の請求書が作成されます
7. 注文IDと請求IDの関連付けが`order_invoice_relations.json`に保存されます

### 2. freee管理画面での確認

1. [freee管理画面](https://secure.freee.co.jp/)にログインします
2. 「請求書」メニューを選択します
3. PoCスクリプトで作成した請求書が表示されていることを確認します

### 3. 本番環境での実装

テストが成功したら、以下の手順で本番環境に実装します：

1. `app/services/FreeeService.ts`クラスを実装します
2. `app/routes/api.freee.*.tsx`エンドポイントを実装します
3. `app/routes/app.billing.*.tsx`請求管理ダッシュボードを実装します

## 参考リンク

- [freee Developers Community](https://developer.freee.co.jp/)
- [freee 請求書API概要](https://developer.freee.co.jp/reference/iv)
- [freee 会計API概要](https://developer.freee.co.jp/reference/accounting)
- [freee-app-template-firebase](https://github.com/freee/freee-app-template-firebase)
- [freee API スタートガイド](https://developer.freee.co.jp/startguide)
