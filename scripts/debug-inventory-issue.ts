/**
 * 在庫設定問題のデバッグスクリプト
 * 在庫設定エラーの原因を詳細に調査
 */

import { GraphQLClient } from 'graphql-request';
import * as dotenv from 'dotenv';

// .envファイルを読み込み
dotenv.config();

class InventoryDebugger {
  private admin: any;

  constructor() {
    const shopifyShop = process.env.SHOPIFY_SHOP;
    const shopifyAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

    if (!shopifyShop || !shopifyAccessToken) {
      throw new Error('環境変数が設定されていません');
    }

    const graphqlClient = new GraphQLClient(
      `https://${shopifyShop}/admin/api/2025-01/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': shopifyAccessToken,
          'Content-Type': 'application/json',
        },
      }
    );

    this.admin = {
      graphql: async (query: string, variables?: any) => {
        const result = await graphqlClient.request(query, variables);
        return {
          json: () => Promise.resolve({ data: result })
        };
      }
    };
  }

  /**
   * 在庫設定問題を詳細にデバッグ
   */
  async debugInventoryIssue(productId: string): Promise<void> {
    console.log('=== 在庫設定問題デバッグ開始 ===\n');
    console.log(`対象商品ID: ${productId}`);

    try {
      // 1. 商品とバリエーション情報を取得
      console.log('\n1. 商品とバリエーション情報を取得...');
      const productInfo = await this.getProductInfo(productId);

      // 2. ロケーション情報を取得
      console.log('\n2. ロケーション情報を取得...');
      const locations = await this.getLocations();

      // 3. 在庫アイテム情報を取得
      console.log('\n3. 在庫アイテム情報を取得...');
      await this.getInventoryItemInfo(productInfo.variants);

      // 4. 在庫レベル情報を取得
      console.log('\n4. 在庫レベル情報を取得...');
      await this.getInventoryLevels(productInfo.variants, locations);

      // 5. 在庫設定を試行
      console.log('\n5. 在庫設定を試行...');
      await this.testInventoryUpdate(productInfo.variants[0], locations[0]);

    } catch (error) {
      console.error('❌ デバッグエラー:', error);
    }

    console.log('\n=== 在庫設定問題デバッグ完了 ===');
  }

  /**
   * 商品とバリエーション情報を取得
   */
  private async getProductInfo(productId: string): Promise<any> {
    const response = await this.admin.graphql(`
      query getProductInfo($id: ID!) {
        product(id: $id) {
          id
          title
          variants(first: 10) {
            edges {
              node {
                id
                title
                sku
                inventoryItem {
                  id
                  tracked
                  requiresShipping
                }
                inventoryQuantity
              }
            }
          }
        }
      }
    `, {
      id: `gid://shopify/Product/${productId}`
    });

    const data = await response.json();

    if (data.errors) {
      throw new Error(`商品情報取得エラー: ${JSON.stringify(data.errors)}`);
    }

    const product = data.data.product;
    const variants = product.variants.edges.map((edge: any) => edge.node);

    console.log(`商品名: ${product.title}`);
    console.log(`バリエーション数: ${variants.length}`);

    variants.forEach((variant: any, index: number) => {
      console.log(`  ${index + 1}. ${variant.title} (SKU: ${variant.sku})`);
      console.log(`     ID: ${variant.id}`);
      console.log(`     在庫アイテムID: ${variant.inventoryItem.id}`);
      console.log(`     在庫追跡: ${variant.inventoryItem.tracked}`);
      console.log(`     現在の在庫数: ${variant.inventoryQuantity}`);
    });

    return { product, variants };
  }

  /**
   * ロケーション情報を取得
   */
  private async getLocations(): Promise<any[]> {
    const response = await this.admin.graphql(`
      query getLocations {
        locations(first: 10) {
          edges {
            node {
              id
              name
              isActive
            }
          }
        }
      }
    `);

    const data = await response.json();

    if (data.errors) {
      throw new Error(`ロケーション情報取得エラー: ${JSON.stringify(data.errors)}`);
    }

    const locations = data.data.locations.edges.map((edge: any) => edge.node);

    console.log(`ロケーション数: ${locations.length}`);
    locations.forEach((location: any, index: number) => {
      console.log(`  ${index + 1}. ${location.name} (${location.isActive ? 'アクティブ' : '非アクティブ'})`);
      console.log(`     ID: ${location.id}`);
    });

    return locations;
  }

  /**
   * 在庫アイテム情報を取得
   */
  private async getInventoryItemInfo(variants: any[]): Promise<void> {
    for (const variant of variants.slice(0, 3)) { // 最初の3つのみ
      try {
        const response = await this.admin.graphql(`
          query getInventoryItem($id: ID!) {
            inventoryItem(id: $id) {
              id
              tracked
              requiresShipping
              inventoryLevels(first: 10) {
                edges {
                  node {
                    id
                    available
                    location {
                      id
                      name
                    }
                  }
                }
              }
            }
          }
        `, {
          id: variant.inventoryItem.id
        });

        const data = await response.json();

        if (data.errors) {
          console.error(`在庫アイテム情報取得エラー (${variant.sku}): ${JSON.stringify(data.errors)}`);
          continue;
        }

        const inventoryItem = data.data.inventoryItem;
        console.log(`在庫アイテム (${variant.sku}):`);
        console.log(`  追跡: ${inventoryItem.tracked}`);
        console.log(`  配送必要: ${inventoryItem.requiresShipping}`);
        console.log(`  在庫レベル数: ${inventoryItem.inventoryLevels.edges.length}`);

        inventoryItem.inventoryLevels.edges.forEach((edge: any) => {
          const level = edge.node;
          console.log(`    ${level.location.name}: ${level.available}個`);
        });

      } catch (error) {
        console.error(`在庫アイテム情報取得エラー (${variant.sku}):`, error);
      }
    }
  }

  /**
   * 在庫レベル情報を取得
   */
  private async getInventoryLevels(variants: any[], locations: any[]): Promise<void> {
    const inventoryItemIds = variants.map(v => v.inventoryItem.id);
    const locationIds = locations.map(l => l.id);

    try {
      const response = await this.admin.graphql(`
        query getInventoryLevels($inventoryItemIds: [ID!]!, $locationIds: [ID!]!) {
          inventoryLevels(first: 50, query: "inventory_item_id:${inventoryItemIds.join(' OR inventory_item_id:')} AND location_id:${locationIds.join(' OR location_id:')}") {
            edges {
              node {
                id
                available
                inventoryItem {
                  id
                }
                location {
                  id
                  name
                }
              }
            }
          }
        }
      `, {
        inventoryItemIds,
        locationIds
      });

      const data = await response.json();

      if (data.errors) {
        console.error(`在庫レベル情報取得エラー: ${JSON.stringify(data.errors)}`);
        return;
      }

      const inventoryLevels = data.data.inventoryLevels.edges.map((edge: any) => edge.node);
      console.log(`在庫レベル数: ${inventoryLevels.length}`);

      inventoryLevels.forEach((level: any) => {
        console.log(`  ${level.location.name}: ${level.available}個 (在庫アイテム: ${level.inventoryItem.id})`);
      });

    } catch (error) {
      console.error('在庫レベル情報取得エラー:', error);
    }
  }

  /**
   * 在庫設定を試行
   */
  private async testInventoryUpdate(variant: any, location: any): Promise<void> {
    console.log(`在庫設定テスト: ${variant.sku} → ${location.name}`);
    console.log(`在庫アイテムID: ${variant.inventoryItem.id}`);
    console.log(`ロケーションID: ${location.id}`);

    try {
      // まず在庫レベルが存在するかチェック
      const checkResponse = await this.admin.graphql(`
        query checkInventoryLevel($inventoryItemId: ID!, $locationId: ID!) {
          inventoryLevel(inventoryItemId: $inventoryItemId, locationId: $locationId) {
            id
            available
          }
        }
      `, {
        inventoryItemId: variant.inventoryItem.id,
        locationId: location.id
      });

      const checkData = await checkResponse.json();

      if (checkData.errors) {
        console.log('在庫レベルが存在しません。作成が必要です。');

        // 在庫レベルを作成
        await this.createInventoryLevel(variant.inventoryItem.id, location.id);
      } else {
        console.log('在庫レベルが存在します:', checkData.data.inventoryLevel);
      }

      // 在庫設定を実行
      const updateResponse = await this.admin.graphql(`
        mutation inventorySetQuantities($input: InventorySetQuantitiesInput!) {
          inventorySetQuantities(input: $input) {
            inventoryAdjustmentGroup {
              id
              changes {
                name
                delta
                quantityAfterChange
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `, {
        input: {
          name: 'available',
          reason: 'correction',
          referenceDocumentUri: `https://example.com/inventory-test`,
          ignoreCompareQuantity: true,
          quantities: [{
            inventoryItemId: variant.inventoryItem.id,
            locationId: location.id,
            quantity: 1
          }]
        }
      });

      const updateData = await updateResponse.json();

      if (updateData.errors) {
        console.error('❌ 在庫設定GraphQLエラー:', updateData.errors);
      } else if (updateData.data.inventorySetQuantities.userErrors.length > 0) {
        console.error('❌ 在庫設定ユーザーエラー:', updateData.data.inventorySetQuantities.userErrors);
      } else {
        console.log('✅ 在庫設定成功:', updateData.data.inventorySetQuantities.inventoryAdjustmentGroup.changes);
      }

    } catch (error) {
      console.error('❌ 在庫設定テストエラー:', error);
    }
  }

  /**
   * 在庫レベルを作成
   */
  private async createInventoryLevel(inventoryItemId: string, locationId: string): Promise<void> {
    try {
      const response = await this.admin.graphql(`
        mutation inventoryActivate($inventoryItemId: ID!, $locationId: ID!) {
          inventoryActivate(inventoryItemId: $inventoryItemId, locationId: $locationId) {
            inventoryLevel {
              id
              available
            }
            userErrors {
              field
              message
            }
          }
        }
      `, {
        inventoryItemId,
        locationId
      });

      const data = await response.json();

      if (data.errors) {
        console.error('❌ 在庫レベル作成GraphQLエラー:', data.errors);
      } else if (data.data.inventoryActivate.userErrors.length > 0) {
        console.error('❌ 在庫レベル作成ユーザーエラー:', data.data.inventoryActivate.userErrors);
      } else {
        console.log('✅ 在庫レベル作成成功:', data.data.inventoryActivate.inventoryLevel);
      }

    } catch (error) {
      console.error('❌ 在庫レベル作成エラー:', error);
    }
  }
}

// メイン実行
async function main() {
  const productId = process.argv[2];

  if (!productId) {
    console.error('使用方法: npm run debug-inventory [商品ID]');
    console.error('例: npm run debug-inventory 8981433581736');
    process.exit(1);
  }

  const inventoryDebugger = new InventoryDebugger();
  await inventoryDebugger.debugInventoryIssue(productId);
}

if (import.meta.url === new URL(import.meta.url).href) {
  main().catch(console.error);
}

export { InventoryDebugger };
