# メタフィールド定義と用途明確化

**作成日**: 2025年1月17日  
**対象**: レンタル商品のメタフィールド管理  
**目的**: variation_typeの正しい使い方とメタフィールド整理

## 🏷️ 現在のメタフィールド定義

### **必要なメタフィールド（継続使用）**

#### 1. `rental.basic_info` (JSON)
**用途**: 商品の基本情報  
**データ構造**:
```json
{
  "status": "available|maintenance|damaged|unavailable",
  "location": "NY|PR",
  "description": "商品説明",
  "specifications": {
    "height": "高さ(cm)",
    "width": "幅(cm)", 
    "depth": "奥行き(cm)",
    "weight": "重量(kg)",
    "material": "素材"
  }
}
```

#### 2. `rental.pricing` (JSON)
**用途**: レンタル価格設定  
**データ構造**:
```json
{
  "basePrice": 9000,
  "discountRules": {
    "day2_6_rate": 0.2,
    "day7_plus_rate": 0.1
  },
  "provisionalRate": 0.1
}
```

#### 3. `rental.location` (text)
**用途**: 在庫保管場所  
**値**: "NY" | "PR" | "WAREHOUSE_A" など

#### 4. `rental.status` (text)
**用途**: 商品の現在ステータス  
**値**: "available" | "maintenance" | "damaged" | "unavailable"

#### 5. `rental.variant_mapping` (JSON)
**用途**: バリアントとレンタル期間のマッピング  
**データ構造**:
```json
{
  "variants": [
    {
      "variantId": "gid://shopify/ProductVariant/xxx",
      "rentalDays": 1,
      "title": "1日レンタル",
      "sku": "101-01-008-1D"
    },
    {
      "variantId": "gid://shopify/ProductVariant/yyy", 
      "rentalDays": 2,
      "title": "2日レンタル",
      "sku": "101-01-008-2D"
    }
  ]
}
```

### **問題のあるメタフィールド（要修正）**

#### 6. `rental.variation_type` (text) ⚠️
**現在の問題**: レンタル日数が入っている（"1D", "2D"など）  
**正しい用途**: 商品のバリエーションタイプ分類  
**正しい値**: "rental_period" | "color" | "size" | "material"

**修正が必要な理由**:
- レンタル日数は`rental.variant_mapping`で管理すべき
- `variation_type`は商品のバリエーション分類に使用すべき
- 現在の使い方では拡張性がない

## 🔧 修正計画

### **Phase 1: variation_typeの用途修正**

#### **現在の状況**
```json
// 間違った使い方
{
  "rental.variation_type": "1D"  // レンタル日数が入っている
}
```

#### **修正後の正しい使い方**
```json
// 正しい使い方
{
  "rental.variation_type": "rental_period"  // バリエーションの種類
}
```

#### **修正スクリプト**
```javascript
// variation_typeを正しい値に更新
const correctVariationType = "rental_period"; // レンタル期間バリエーション
// または
const correctVariationType = "color";         // 色バリエーション
const correctVariationType = "size";          // サイズバリエーション
const correctVariationType = "material";      // 素材バリエーション
```

### **Phase 2: 新しいメタフィールド構造**

#### **商品バリエーション管理の改善**
```json
{
  "rental.variation_type": "rental_period",
  "rental.variant_mapping": {
    "variationType": "rental_period",
    "variants": [
      {
        "variantId": "gid://shopify/ProductVariant/xxx",
        "value": "1",
        "unit": "day",
        "title": "1日レンタル",
        "sku": "101-01-008-1D",
        "price": 9000
      },
      {
        "variantId": "gid://shopify/ProductVariant/yyy",
        "value": "2", 
        "unit": "day",
        "title": "2日レンタル",
        "sku": "101-01-008-2D",
        "price": 10800
      }
    ]
  }
}
```

## 🛠️ 実装手順

### **Step 1: 現在のvariation_type値を調査**
```bash
node scripts/metafield/analyze-variation-type.js
```

### **Step 2: variation_typeを正しい値に更新**
```bash
node scripts/metafield/fix-variation-type.js
```

### **Step 3: variant_mappingの構造を改善**
```bash
node scripts/metafield/improve-variant-mapping.js
```

### **Step 4: 不要なメタフィールドを削除**
```bash
node scripts/metafield/cleanup-unused-metafields.js
```

## 📋 メタフィールド使用ガイドライン

### **新規商品登録時**
1. `rental.basic_info`: 商品の基本情報を設定
2. `rental.pricing`: 価格設定を定義
3. `rental.location`: 在庫場所を指定
4. `rental.status`: 初期ステータスを"available"に設定
5. `rental.variation_type`: バリエーションタイプを指定
6. `rental.variant_mapping`: バリアントマッピングを作成

### **バリアント作成時**
1. SKU形式: `{base-sku}-{variant-suffix}` (例: 101-01-008-1D)
2. タイトル形式: `{period}日レンタル` または `{color}` など
3. `variant_mapping`に追加
4. 価格を適切に設定

### **商品更新時**
1. `basic_info.status`でステータス管理
2. `pricing`で価格調整
3. `variant_mapping`でバリアント管理

## 🔍 データ整合性チェック

### **チェック項目**
- [ ] `variation_type`が正しい値になっている
- [ ] `variant_mapping`にすべてのバリアントが含まれている
- [ ] SKU形式が統一されている
- [ ] 価格設定が正しく反映されている
- [ ] ステータス管理が適切に機能している

### **定期メンテナンス**
- 月次: メタフィールドの整合性チェック
- 商品追加時: メタフィールド設定の確認
- システム更新時: メタフィールド定義の見直し

## 📚 関連ドキュメント

- [商品表示・価格計算修正仕様書](./商品表示・価格計算修正_20250117.md)
- [SKU番号体系とカテゴリ連携](../../../master-data-csv/other-master-data/商品カテゴリマスタ.csv)
- [Shopifyメタフィールド公式ドキュメント](https://shopify.dev/docs/apps/metafields)

## 🎯 期待される効果

### **修正前の問題**
- `variation_type`にレンタル日数が入っている
- バリエーション管理が不統一
- 拡張性がない

### **修正後の改善**
- ✅ `variation_type`が正しい分類に
- ✅ バリエーション管理が統一
- ✅ 新しいバリエーションタイプに対応可能
- ✅ データ構造が明確で保守しやすい

**この修正により、レンタル商品のメタフィールド管理が大幅に改善されます。**
