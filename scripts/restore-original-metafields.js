/**
 * 元のメタフィールド構造に戻すスクリプト
 * 
 * このスクリプトは、商品バリエーション方式で変更したメタフィールドを元の構造に戻します。
 * 具体的には、以下の処理を行います：
 * 1. rental.variant_mappingメタフィールドを削除
 * 2. rental.pricingメタフィールドからvariantPricesを削除
 * 3. rental.variation_typeメタフィールドを元の値に戻す
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数を読み込む
dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品を取得するGraphQLクエリ
const GET_PRODUCTS = gql`
  query getProducts($first: Int!, $after: String) {
    products(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          metafields(first: 20, namespace: "rental") {
            edges {
              node {
                id
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    }
  }
`;

// メタフィールドを更新するGraphQLクエリ
const UPDATE_METAFIELD = gql`
  mutation metafieldSet($metafield: MetafieldInput!) {
    metafieldSet(metafield: $metafield) {
      metafield {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールドを削除するGraphQLクエリ
const DELETE_METAFIELD = gql`
  mutation metafieldDelete($input: MetafieldDeleteInput!) {
    metafieldDelete(input: $input) {
      deletedId
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールド定義を削除するGraphQLクエリ
const DELETE_METAFIELD_DEFINITION = gql`
  mutation metafieldDefinitionDelete($id: ID!) {
    metafieldDefinitionDelete(id: $id) {
      deletedDefinitionId
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールド定義を取得するGraphQLクエリ
const GET_METAFIELD_DEFINITIONS = gql`
  query getMetafieldDefinitions($namespace: String!, $key: String) {
    metafieldDefinitions(namespace: $namespace, key: $key, ownerType: PRODUCT, first: 10) {
      edges {
        node {
          id
          namespace
          key
          name
        }
      }
    }
  }
`;

/**
 * 商品のメタフィールドを元に戻す
 * @param {string} productId 商品ID
 * @param {Object} metafields 商品のメタフィールド
 */
async function restoreProductMetafields(productId, metafields) {
  try {
    // rental.variant_mappingメタフィールドを削除
    const variantMappingMetafield = metafields.find(
      (m) => m.namespace === 'rental' && m.key === 'variant_mapping'
    );

    if (variantMappingMetafield) {
      await client.request(DELETE_METAFIELD, {
        input: {
          id: variantMappingMetafield.id
        }
      });

      console.log(`商品 ${productId} の rental.variant_mapping メタフィールドを削除しました`);
    }

    // rental.pricingメタフィールドからvariantPricesを削除
    const pricingMetafield = metafields.find(
      (m) => m.namespace === 'rental' && m.key === 'pricing'
    );

    if (pricingMetafield) {
      // 現在のpricing値を解析
      const pricingValue = JSON.parse(pricingMetafield.value);
      
      // variantPricesを削除
      if (pricingValue.variantPrices) {
        delete pricingValue.variantPrices;
        
        // rental.pricingメタフィールドを更新
        await client.request(UPDATE_METAFIELD, {
          metafield: {
            ownerId: productId,
            namespace: 'rental',
            key: 'pricing',
            value: JSON.stringify(pricingValue),
            type: 'json'
          }
        });

        console.log(`商品 ${productId} の rental.pricing メタフィールドからvariantPricesを削除しました`);
      }
    }

    // rental.variation_typeメタフィールドを元の値に戻す
    const variationTypeMetafield = metafields.find(
      (m) => m.namespace === 'rental' && m.key === 'variation_type'
    );

    if (variationTypeMetafield && variationTypeMetafield.value === 'レンタル日数') {
      // 商品タイトルから推測される適切な値を設定
      const productTitle = productId.split('/').pop();
      let variationType = '色';
      
      if (productTitle.toLowerCase().includes('size')) {
        variationType = 'サイズ';
      }
      
      // rental.variation_typeメタフィールドを更新
      await client.request(UPDATE_METAFIELD, {
        metafield: {
          ownerId: productId,
          namespace: 'rental',
          key: 'variation_type',
          value: variationType,
          type: 'single_line_text_field'
        }
      });

      console.log(`商品 ${productId} の rental.variation_type メタフィールドを「${variationType}」に更新しました`);
    }
  } catch (error) {
    console.error(`商品 ${productId} のメタフィールド復元中にエラーが発生しました: ${error.message}`);
  }
}

/**
 * variant_mappingメタフィールド定義を削除する
 */
async function deleteVariantMappingMetafieldDefinition() {
  try {
    console.log('variant_mappingメタフィールド定義を削除中...');

    // メタフィールド定義を取得
    const result = await client.request(GET_METAFIELD_DEFINITIONS, {
      namespace: 'rental',
      key: 'variant_mapping'
    });

    const definitions = result.metafieldDefinitions.edges;
    
    if (definitions.length === 0) {
      console.log('variant_mappingメタフィールド定義が見つかりませんでした');
      return;
    }

    // メタフィールド定義を削除
    const definitionId = definitions[0].node.id;
    
    await client.request(DELETE_METAFIELD_DEFINITION, {
      id: definitionId
    });

    console.log('variant_mappingメタフィールド定義を削除しました');
  } catch (error) {
    console.error(`メタフィールド定義の削除中にエラーが発生しました: ${error.message}`);
  }
}

/**
 * 全ての商品のメタフィールドを元に戻す
 */
async function restoreAllProductMetafields() {
  let hasNextPage = true;
  let cursor = null;
  let count = 0;

  console.log('全ての商品のメタフィールドを元の構造に戻します...');

  while (hasNextPage) {
    try {
      // 商品を取得
      const result = await client.request(GET_PRODUCTS, {
        first: 10,
        after: cursor
      });

      const products = result.products.edges;
      hasNextPage = result.products.pageInfo.hasNextPage;
      cursor = result.products.pageInfo.endCursor;

      // 各商品のメタフィールドを元に戻す
      for (const product of products) {
        const productId = product.node.id;
        const metafields = product.node.metafields.edges.map(edge => ({
          id: edge.node.id,
          namespace: edge.node.namespace,
          key: edge.node.key,
          value: edge.node.value,
          type: edge.node.type
        }));

        await restoreProductMetafields(productId, metafields);
        count++;

        // API制限を回避するための短い待機
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      console.log(`${count}件の商品を処理しました`);
    } catch (error) {
      console.error(`商品の取得中にエラーが発生しました: ${error.message}`);
      hasNextPage = false;
    }
  }

  console.log(`合計${count}件の商品のメタフィールドを元の構造に戻しました`);
}

// スクリプトの実行
async function main() {
  // 全ての商品のメタフィールドを元に戻す
  await restoreAllProductMetafields();
  
  // variant_mappingメタフィールド定義を削除
  await deleteVariantMappingMetafieldDefinition();
  
  console.log('メタフィールド構造の復元が完了しました');
}

main().catch(error => {
  console.error(`スクリプトの実行中にエラーが発生しました: ${error.message}`);
});
