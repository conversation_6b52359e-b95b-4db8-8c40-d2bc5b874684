{% include 'global-variables' %}
{% include 'product-res-variables' %}
{%- assign image_size = image_size | default: '200x' -%}
{%- assign show_label_in_stock = show_label_in_stock | default: settings.product_collection_show_label_in_stock -%}
{%- assign show_label_pre_order = show_label_pre_order | default: settings.product_collection_show_label_pre_order -%}
{%- assign show_label_out_stock = show_label_out_stock | default: settings.product_collection_show_label_out_stock -%}
{%- assign show_label_sale = show_label_sale | default: settings.product_collection_show_label_sale -%}
{%- assign show_label_new = show_label_new | default: settings.product_collection_show_label_new -%}
{%- assign show_label_hot = show_label_hot | default: settings.product_collection_show_label_hot -%}
{%- assign show_title = show_title | default: settings.product_collection_show_title -%}
{%- assign show_price = show_price | default: settings.product_collection_show_price -%}
{%- assign show_button_add_to_cart = show_button_add_to_cart | default: settings.product_collection_show_button_add_to_cart -%}
{%- assign show_button_quick_view = show_button_quick_view | default: settings.product_collection_show_button_quick_view -%}
{%- assign show_button_remove = show_button_remove | default: true -%}
{%- assign product_form_id = 'compare-quick-add-' | append: product.id | append: current_variant.id -%}
<product-item class="product-compare{% if settings.product_collection_centered_info %} product-compare--centered{% endif %} d-flex flex-column" data-js-product data-js-store-lists-product-compare data-product-handle="{{ product.handle }}" data-product-variant-id="{{ current_variant_id }}">
    <div class="d-flex flex-column">
        {%- if show_button_remove -%}
            <div class="product-compare__remove btn-link mb-15 js-store-lists-remove-compare">{{ 'products.product.remove' | t }}</div>
        {%- endif -%}
        <a href="#" class="product-compare__image product-image product-image--hover-{{ settings.product_hover_animation_type }} position-relative w-100 js-product-images-navigation js-product-images-hovered-end">
            <img>
            
        </a>
        <div class="product-compare__content d-flex flex-column align-items-start mt-15">
            {%- if show_title -%}
                <div class="product-compare__title mb-3">
                    <h3 class="m-0">
                        <a href="{{ url }}">{{ title }}</a>
                    </h3>
                </div>
            {%- endif -%}
            {%- if show_price -%}
                <div class="product-compare__price mb-10">
                    {% include 'product-get-price' %}
                </div>
            {%- endif -%}
            <div class="d-flex flex-column">
                {%- if show_button_add_to_cart -%}
                    <div class="product-compare__variants d-none">
                        {% render 'product-get-variants' with product: product current_variant: current_variant show_options: show_options show_custom_options: show_custom_options, product_form_id: product_form_id %}
                    </div>
                {%- endif -%}
                {%- if show_button_add_to_cart or show_button_quick_view -%}
                    <div class="product-compare__buttons d-flex flex-column flex-lg-row align-items-lg-center flex-wrap mt-4">
                        
                        {%- if show_button_quick_view -%}
                            <div class="product-collection__buttons-section d-flex d-lg-none px-lg-10">
                                <div class="product-compare__button-quick-view-mobile mb-10">
                                    {% render 'product-get-button-quick-view' with type: 'btn-text' product_enable_tooltips: product_enable_tooltips %}
                                </div>
                            </div>
                        {%- endif -%}
                    </div>
                {%- endif -%}
            </div>
        </div>
    </div>
</product-item>