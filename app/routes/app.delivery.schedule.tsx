import { json, type LoaderFunctionArgs } from '@remix-run/node';
import { useLoaderData, useNavigate, useSearchParams } from '@remix-run/react';
import {
  Page,
  Card,
  Text,
  Button,
  ButtonGroup,
  Select,
  TextField,
  Layout,
  DataTable,
  Box,
  InlineStack,
  BlockStack
} from '@shopify/polaris';
import { useState, useCallback } from 'react';
import { authenticate } from '../shopify.server';
import { prisma } from '../db.server';

// 配送ステータスに応じたバッジ設定
const deliveryStatusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  pending: { tone: 'info', label: '待機中' },
  picked: { tone: 'attention', label: 'ピッキング済み' },
  shipped: { tone: 'warning', label: '発送済み' },
  completed: { tone: 'success', label: '完了' },
  delayed: { tone: 'critical', label: '遅延' },
  cancelled: { tone: 'critical', label: 'キャンセル' }
};

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);

  // クエリパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  const limit = parseInt(url.searchParams.get('limit') || '50', 10);
  const deliveryDate = url.searchParams.get('deliveryDate') || undefined;
  const carrierId = url.searchParams.get('carrierId') || undefined;
  const returnStatus = url.searchParams.get('returnStatus') || undefined;
  const query = url.searchParams.get('query') || undefined;

  // 検索条件を構築
  const where: any = {};

  if (deliveryDate) {
    const date = new Date(deliveryDate);
    const nextDay = new Date(date);
    nextDay.setDate(nextDay.getDate() + 1);

    where.deliveryDate = {
      gte: date,
      lt: nextDay
    };
  }

  if (carrierId) {
    where.carrierId = carrierId;
  }

  if (returnStatus) {
    where.returnStatus = returnStatus;
  }

  if (query) {
    where.OR = [
      { destination: { contains: query, mode: 'insensitive' } },
      { address: { contains: query, mode: 'insensitive' } },
      { notes: { contains: query, mode: 'insensitive' } },
      { orderId: { contains: query } },
      {
        booking: {
          OR: [
            { bookingId: { contains: query } },
            { customerName: { contains: query, mode: 'insensitive' } },
            { customerEmail: { contains: query, mode: 'insensitive' } },
            {
              product: {
                title: { contains: query, mode: 'insensitive' }
              }
            },
            {
              product: {
                sku: { contains: query }
              }
            }
          ]
        }
      }
    ];
  }

  // 配送スケジュールを取得
  const schedules = await prisma.deliverySchedule.findMany({
    where,
    include: {
      booking: {
        include: {
          product: true
        }
      },
      staff: true,
      carrier: true,
      pickingStaff: true,
      shippingFee: true
    },
    orderBy: [
      { deliveryDate: 'asc' },
      { timeFrom: 'asc' }
    ],
    skip: (page - 1) * limit,
    take: limit
  });

  // 総件数を取得
  const total = await prisma.deliverySchedule.count({ where });

  // 配送業者一覧を取得
  const carriers = await prisma.shippingCarrier.findMany({
    orderBy: { name: 'asc' }
  });

  // 配送先一覧を取得
  const destinations = await prisma.deliveryDestination.findMany({
    where: { isActive: true },
    orderBy: { destinationCode: 'asc' },
    take: 100 // 上位100件
  });

  return json({
    schedules: schedules.map(schedule => ({
      id: schedule.id,
      deliveryType: schedule.deliveryType,
      status: schedule.returnStatus || 'pending',
      timeFrom: schedule.timeFrom || '',
      timeTo: schedule.timeTo || '',
      destination: schedule.destination || '',
      customerName: schedule.booking?.customerName || '',
      address: schedule.address || '',
      notes: schedule.notes || '',
      carrierId: schedule.carrierId || '',
      carrierName: schedule.carrier?.name || ''
    })),
    carriers: carriers.map(carrier => ({
      value: carrier.id,
      label: carrier.name
    }))
  });
}

export default function DeliverySchedule() {
  const { schedules, carriers } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // フィルター状態
  const [deliveryDate, setDeliveryDate] = useState(searchParams.get('deliveryDate') || '');
  const [carrierId, setCarrierId] = useState(searchParams.get('carrierId') || '');

  // 今日の日付をデフォルトに設定
  const today = new Date().toISOString().split('T')[0];

  // 検索実行
  const handleSearch = useCallback(() => {
    const params = new URLSearchParams();
    if (deliveryDate) params.set('deliveryDate', deliveryDate);
    if (carrierId) params.set('carrierId', carrierId);
    navigate(`/app/delivery/schedule?${params.toString()}`);
  }, [deliveryDate, carrierId, navigate]);

  // フィルタークリア
  const handleClearFilters = useCallback(() => {
    setDeliveryDate('');
    setCarrierId('');
    navigate('/app/delivery/schedule');
  }, [navigate]);

  // 配送一覧出力
  const handleDeliveryListOutput = useCallback(() => {
    console.log('配送一覧出力');
  }, []);

  // 保存
  const handleSave = useCallback(() => {
    console.log('保存');
  }, []);

  // データテーブル用のデータ準備
  const tableRows = schedules.map(schedule => [
    <Select
      options={[
        { label: '選択してください', value: '' },
        ...carriers
      ]}
      value={schedule.carrierId}
      onChange={(value) => {
        // TODO: 配送業者変更処理
        console.log('配送業者変更:', schedule.id, value);
      }}
    />,
    '10', // 配送順（仮）
    '202505161404404800014', // 伝票番号（仮）
    '0697460001', // 顧客出荷番号（仮）
    schedule.customerName,
    schedule.deliveryType === 'delivery' ? '搬入' : '搬出',
    schedule.status === 'pending' ? '指定' : '完了',
    schedule.timeFrom || '10:00',
    schedule.timeTo || '12:00',
    schedule.destination || '別保有'
  ]);

  return (
    <Page title="【新】配送一覧 - 更新済み">
      <Layout>
        {/* 対象データ検索 */}
        <Layout.Section>
          <Card>
            <Box padding="400" background="bg-surface-info">
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd" tone="subdued">対象データ検索</Text>
                <InlineStack gap="400" align="start">
                  <Box minWidth="200px">
                    <TextField
                      label="配送日付"
                      type="date"
                      value={deliveryDate}
                      onChange={setDeliveryDate}
                      placeholder={today}
                    />
                  </Box>
                  <Box minWidth="200px">
                    <Select
                      label="配送業者"
                      options={[
                        { label: '選択してください', value: '' },
                        ...carriers
                      ]}
                      value={carrierId}
                      onChange={setCarrierId}
                    />
                  </Box>
                  <Box paddingBlockStart="600">
                    <ButtonGroup>
                      <Button onClick={handleSearch}>検索</Button>
                      <Button onClick={handleClearFilters}>クリア</Button>
                    </ButtonGroup>
                  </Box>
                </InlineStack>
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>

        {/* 配送一覧テーブル */}
        <Layout.Section>
          <Card>
            <Box padding="400">
              <DataTable
                columnContentTypes={[
                  'text', // 配送業者
                  'text', // 配送順
                  'text', // 伝票番号
                  'text', // 顧客出荷番号
                  'text', // 顧客名
                  'text', // 区分
                  'text', // 指定
                  'text', // 時間From
                  'text', // 時間To
                  'text'  // 配送先
                ]}
                headings={[
                  '配送業者',
                  '配送順',
                  '伝票番号',
                  '顧客出荷番号',
                  '顧客名',
                  '区分',
                  '指定',
                  '時間From',
                  '時間To',
                  '配送先'
                ]}
                rows={tableRows}
                truncate
              />
            </Box>

            <Box padding="400" borderBlockStartWidth="025" borderColor="border">
              <Text as="p" variant="bodySm" tone="subdued">
                ※ 配送順およびメモを保存する場合は、「保存」ボタンを押してください。
              </Text>
              <Box paddingBlockStart="400">
                <InlineStack gap="400" align="end">
                  <Button onClick={handleDeliveryListOutput} variant="primary">
                    配送一覧出力
                  </Button>
                  <Button onClick={handleSave}>
                    保存
                  </Button>
                </InlineStack>
              </Box>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>

    </Page>
  );
}
