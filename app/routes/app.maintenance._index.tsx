import React, { useState, useEffect } from 'react';
import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import {
  Page,
  LegacyCard,
  ResourceList,
  ResourceItem,
  Filters,
  Badge,
  EmptyState,
  Text,
  Pagination,
  Button,
  ButtonGroup
} from '@shopify/polaris';
import { prisma } from '../db.server';
import { MaintenanceStatus } from '@prisma/client';

// ステータスに応じたバッジ設定
const statusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  SCHEDULED: { tone: 'attention', label: '予定' },
  IN_PROGRESS: { tone: 'warning', label: '進行中' },
  COMPLETED: { tone: 'success', label: '完了' },
  CANCELLED: { tone: 'critical', label: 'キャンセル' }
};

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);

  // クエリパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  const limit = parseInt(url.searchParams.get('limit') || '50', 10);
  const status = url.searchParams.get('status') || undefined;
  const query = url.searchParams.get('query') || undefined;
  const startDate = url.searchParams.get('startDate') || undefined;
  const endDate = url.searchParams.get('endDate') || undefined;

  // 検索条件を構築
  const where: any = {};

  if (status) {
    where.status = status as MaintenanceStatus;
  }

  if (query) {
    where.OR = [
      {
        product: {
          title: { contains: query, mode: 'insensitive' }
        }
      },
      {
        product: {
          sku: { contains: query }
        }
      },
      { notes: { contains: query, mode: 'insensitive' } }
    ];
  }

  if (startDate) {
    where.startDate = { gte: new Date(startDate) };
  }

  if (endDate) {
    where.endDate = { lte: new Date(endDate) };
  }

  // メンテナンスデータを取得
  const maintenanceRecords = await prisma.maintenance.findMany({
    where,
    include: {
      product: true
    },
    orderBy: [
      { status: 'asc' },
      { startDate: 'asc' }
    ],
    skip: (page - 1) * limit,
    take: limit
  });

  // 総件数を取得
  const total = await prisma.maintenance.count({ where });

  return json({
    maintenanceRecords: maintenanceRecords.map(record => ({
      id: record.id,
      productId: record.productId,
      productTitle: record.product.title,
      productSku: record.product.sku,
      status: record.status,
      type: record.type,
      startDate: record.startDate.toISOString(),
      endDate: record.endDate?.toISOString(),
      completionDate: record.completionDate?.toISOString(),
      notes: record.notes
    })),
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}

export default function MaintenanceIndex() {
  const { maintenanceRecords, pagination } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [isClient, setIsClient] = useState(false);

  // クライアントサイドでのみ実行
  useEffect(() => {
    setIsClient(true);
  }, []);

  // メンテナンス詳細ページへ移動
  const handleMaintenanceClick = (maintenanceId: string) => {
    console.log('handleMaintenanceClick 呼び出し:', maintenanceId);
    if (isClient) {
      window.location.href = `/app/maintenance/${maintenanceId}?shop=peaces-test-block.myshopify.com`;
    }
  };

  // ステータスフィルターを適用
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    const params = new URLSearchParams(window.location.search);

    if (value) {
      params.set('status', value);
    } else {
      params.delete('status');
    }

    navigate(`/app/maintenance?${params.toString()}`);
  };

  // 検索クエリを適用
  const handleSearchSubmit = () => {
    const params = new URLSearchParams(window.location.search);

    if (searchQuery) {
      params.set('query', searchQuery);
    } else {
      params.delete('query');
    }

    navigate(`/app/maintenance?${params.toString()}`);
  };

  // ページネーション処理
  const handlePaginationChange = (newPage: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set('page', newPage.toString());
    navigate(`/app/maintenance?${params.toString()}`);
  };

  // 日付をフォーマット
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  // フィルターオプションを生成
  const filters = [
    {
      key: 'status',
      label: 'ステータス',
      filter: (
        <select
          value={statusFilter}
          onChange={(e) => handleStatusFilterChange(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="">すべて</option>
          <option value="SCHEDULED">予定</option>
          <option value="IN_PROGRESS">進行中</option>
          <option value="COMPLETED">完了</option>
          <option value="CANCELLED">キャンセル</option>
        </select>
      ),
      shortcut: true,
    },
    {
      key: 'startDate',
      label: '開始日',
      filter: (
        <div>
          <input
            type="date"
            onChange={(e) => {
              const params = new URLSearchParams(window.location.search);
              if (e.target.value) {
                params.set('startDate', e.target.value);
              } else {
                params.delete('startDate');
              }
              navigate(`/app/maintenance?${params.toString()}`);
            }}
            style={{ width: '100%', padding: '8px' }}
          />
        </div>
      ),
    },
    {
      key: 'endDate',
      label: '終了日',
      filter: (
        <div>
          <input
            type="date"
            onChange={(e) => {
              const params = new URLSearchParams(window.location.search);
              if (e.target.value) {
                params.set('endDate', e.target.value);
              } else {
                params.delete('endDate');
              }
              navigate(`/app/maintenance?${params.toString()}`);
            }}
            style={{ width: '100%', padding: '8px' }}
          />
        </div>
      ),
    },
  ];

  return (
    <Page
      title="メンテナンス管理"
      primaryAction={{
        content: "新規メンテナンス",
        url: "/app/maintenance/new"
      }}
    >
      <LegacyCard>
        <ResourceList
          resourceName={{ singular: 'メンテナンス', plural: 'メンテナンス' }}
          items={maintenanceRecords}
          renderItem={(record) => {
            const { id, productTitle, productSku, status, type, notes, startDate, endDate } = record;
            const badge = statusBadgeMap[status] || { tone: 'new', label: status };

            const shortcutActions = [
              {
                content: '詳細',
                url: `/app/maintenance/${id}?shop=peaces-test-block.myshopify.com`,
              },
              {
                content: '編集',
                url: `/app/maintenance/${id}/edit?shop=peaces-test-block.myshopify.com`,
              }
            ];

            if (status === 'SCHEDULED') {
              shortcutActions.push({
                content: '開始',
                url: `/app/maintenance/start/${id}`,
              });
            }

            if (status === 'IN_PROGRESS') {
              shortcutActions.push({
                content: '完了',
                url: `/app/maintenance/complete/${id}`,
              });
            }

            return (
              <ResourceItem
                id={id}
                onClick={() => handleMaintenanceClick(id)}
                shortcutActions={shortcutActions}
                persistActions
              >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <Text as="h3" variant="bodyMd" fontWeight="bold">
                      {productTitle} (SKU: {productSku})
                    </Text>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodyMd">
                        {type}: {notes || '備考なし'}
                      </Text>
                    </div>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        期間: {formatDate(startDate)} 〜 {endDate ? formatDate(endDate) : '未定'}
                      </Text>
                    </div>
                  </div>
                  <div>
                    <Badge tone={badge.tone}>{badge.label}</Badge>
                  </div>
                </div>
              </ResourceItem>
            );
          }}
          filterControl={
            <Filters
              queryValue={searchQuery}
              filters={filters}
              onQueryChange={setSearchQuery}
              onQueryClear={() => setSearchQuery('')}
              onClearAll={() => {
                setSearchQuery('');
                setStatusFilter('');
                navigate('/app/maintenance');
              }}
              onQueryBlur={handleSearchSubmit}
              hideQueryField={false}
            />
          }
          emptyState={
            <EmptyState
              heading="メンテナンス記録がありません"
              image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
            >
              <p>メンテナンス記録を作成して、商品のメンテナンス状況を管理しましょう。</p>
            </EmptyState>
          }
        />
      </LegacyCard>
    </Page>
  );
}
