/**
 * 料金計算コンポーネント
 * 
 * 統一された料金計算UIを提供します
 */

import React, { useMemo } from 'react';
import { Card, Text, BlockStack, InlineStack, Badge, Divider } from '@shopify/polaris';
import { UnifiedPricingService, type PricingResult } from '~/services/pricing/unified-pricing.service';

interface PricingCalculatorProps {
  startDate: Date;
  endDate: Date;
  basePrice: number;
  config?: {
    day2_6_rate?: number;
    day7_plus_rate?: number;
    depositRate?: number;
    taxRate?: number;
  };
  showBreakdown?: boolean;
  showDeposit?: boolean;
  showTax?: boolean;
  compact?: boolean;
}

export function PricingCalculator({
  startDate,
  endDate,
  basePrice,
  config,
  showBreakdown = true,
  showDeposit = true,
  showTax = true,
  compact = false,
}: PricingCalculatorProps) {
  const pricingService = UnifiedPricingService.getInstance();

  // 料金計算結果をメモ化
  const pricingResult = useMemo(() => {
    try {
      return pricingService.calculatePrice(startDate, endDate, basePrice, {
        discountRules: {
          day2_6_rate: config?.day2_6_rate,
          day7_plus_rate: config?.day7_plus_rate,
        },
        depositRate: config?.depositRate,
        taxRate: config?.taxRate,
      });
    } catch (error) {
      console.error('料金計算エラー:', error);
      return null;
    }
  }, [startDate, endDate, basePrice, config, pricingService]);

  if (!pricingResult) {
    return (
      <Card>
        <Text tone="critical">料金計算に失敗しました</Text>
      </Card>
    );
  }

  if (compact) {
    return <CompactPricingDisplay result={pricingResult} />;
  }

  return (
    <Card>
      <BlockStack gap="400">
        {/* ヘッダー */}
        <BlockStack gap="200">
          <Text variant="headingMd" as="h3">
            料金計算
          </Text>
          <InlineStack gap="200" align="start">
            <Badge tone="info">
              {pricingResult.rentalDays}日間
            </Badge>
            <Text variant="bodyMd" tone="subdued">
              {pricingResult.metadata.startDate} 〜 {pricingResult.metadata.endDate}
            </Text>
          </InlineStack>
        </BlockStack>

        {/* 料金内訳 */}
        {showBreakdown && (
          <BlockStack gap="200">
            <Text variant="headingSm" as="h4">
              料金内訳
            </Text>
            <BlockStack gap="100">
              {pricingResult.priceBreakdown.map((item) => (
                <InlineStack key={item.day} align="space-between">
                  <Text variant="bodyMd">
                    {item.day}日目 ({item.description})
                  </Text>
                  <Text variant="bodyMd" fontWeight="medium">
                    {pricingService.formatPrice(item.price)}
                  </Text>
                </InlineStack>
              ))}
            </BlockStack>
          </BlockStack>
        )}

        <Divider />

        {/* 合計金額 */}
        <BlockStack gap="200">
          <InlineStack align="space-between">
            <Text variant="bodyLg" fontWeight="medium">
              小計
            </Text>
            <Text variant="bodyLg" fontWeight="medium">
              {pricingService.formatPrice(pricingResult.totalPrice)}
            </Text>
          </InlineStack>

          {showTax && (
            <InlineStack align="space-between">
              <Text variant="bodyMd" tone="subdued">
                税込価格
              </Text>
              <Text variant="bodyMd">
                {pricingService.formatPrice(pricingResult.taxInclusivePrice)}
              </Text>
            </InlineStack>
          )}

          {showDeposit && (
            <InlineStack align="space-between">
              <Text variant="bodyMd" tone="subdued">
                デポジット (10%)
              </Text>
              <Text variant="bodyMd">
                {pricingService.formatPrice(pricingResult.depositAmount)}
              </Text>
            </InlineStack>
          )}
        </BlockStack>
      </BlockStack>
    </Card>
  );
}

/**
 * コンパクト表示用コンポーネント
 */
function CompactPricingDisplay({ result }: { result: PricingResult }) {
  const pricingService = UnifiedPricingService.getInstance();

  return (
    <InlineStack gap="200" align="space-between">
      <BlockStack gap="050">
        <Text variant="bodyMd" fontWeight="medium">
          {result.rentalDays}日間
        </Text>
        <Text variant="bodySm" tone="subdued">
          {result.metadata.startDate} 〜 {result.metadata.endDate}
        </Text>
      </BlockStack>
      <Text variant="bodyLg" fontWeight="medium">
        {pricingService.formatPrice(result.totalPrice)}
      </Text>
    </InlineStack>
  );
}

/**
 * 料金計算フック
 */
export function usePricingCalculation(
  startDate: Date | null,
  endDate: Date | null,
  basePrice: number,
  config?: PricingCalculatorProps['config']
) {
  const pricingService = UnifiedPricingService.getInstance();

  return useMemo(() => {
    if (!startDate || !endDate || !basePrice) {
      return null;
    }

    try {
      return pricingService.calculatePrice(startDate, endDate, basePrice, {
        discountRules: {
          day2_6_rate: config?.day2_6_rate,
          day7_plus_rate: config?.day7_plus_rate,
        },
        depositRate: config?.depositRate,
        taxRate: config?.taxRate,
      });
    } catch (error) {
      console.error('料金計算エラー:', error);
      return null;
    }
  }, [startDate, endDate, basePrice, config, pricingService]);
}

export default PricingCalculator;
