#!/usr/bin/env node

/**
 * テスト用商品登録スクリプト
 *
 * 正しいデータの流れに従う: Shopify → Prisma
 * 1. Shopifyに商品・バリアント・メタフィールドを作成
 * 2. Prismaデータベースに同期
 * 3. カテゴリマスタと連動
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { PrismaClient } from '@prisma/client';
import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const prisma = new PrismaClient();

// Shopify API設定
const SHOPIFY_SHOP = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

if (!SHOPIFY_ACCESS_TOKEN) {
  console.error('❌ SHOPIFY_ADMIN_API_ACCESS_TOKEN が設定されていません');
  process.exit(1);
}

// 実際のSKUを使用したテスト用商品データ（CSVの全項目を含む）
const TEST_PRODUCTS = [
  {
    sku: '212-05-023',
    title: '花器　シルバー　穴開きボトル型',
    handle: '花器-シルバー-穴開きボトル型',
    basePrice: 1500,
    csvData: {
      // CSVの全項目をマッピング
      menu: 'PROP',
      majorCategory: 'ニュアンスオブジェ;花器',
      minorCategory: 'その他オブジェ;ゴールド・シルバー',
      name: '花器　シルバー　穴開きボトル型',
      modelNumber: '212-05-023',
      sizeW: 14,
      sizeD: 0,
      sizeH: 31,
      sizeSH: 0,
      sizeOther: '',
      color: 'シルバー',
      colorOther: '',
      material: '',
      other: '',
      stockQuantity: 1,
      rentalFee1Day: 1500,
      rentalFee1Night2Days: 1800,
      rentalFee2Nights3Days: 2100,
      rentalFee3Nights4Days: 2400,
      publicStatus: '公開',
      newStatus: 'あり',
      campaign: '2025 NEW ITEMS'
    },
    location: 'NY',
    status: 'available'
  },
  {
    sku: '201-07-107',
    title: 'シルバートレー　オーバル　フリルフレーム　持ち手付',
    handle: 'シルバートレー-オーバル-フリルフレーム-持ち手付',
    basePrice: 3000,
    csvData: {
      menu: 'PROP',
      majorCategory: 'キッチン;コスメ',
      minorCategory: 'コスメボックス・トレー;銀食器',
      name: 'シルバートレー　オーバル　フリルフレーム　持ち手付',
      modelNumber: '201-07-107',
      sizeW: 70,
      sizeD: 50,
      sizeH: 0,
      sizeSH: 0,
      sizeOther: '',
      color: 'シルバー',
      colorOther: '',
      material: '',
      other: '',
      stockQuantity: 1,
      rentalFee1Day: 3000,
      rentalFee1Night2Days: 3600,
      rentalFee2Nights3Days: 4200,
      rentalFee3Nights4Days: 4800,
      publicStatus: '公開',
      newStatus: 'あり',
      campaign: 'Silver ware;2025 NEW ITEMS'
    },
    location: 'NY',
    status: 'available'
  },
  {
    sku: '201-05-797',
    title: 'キャンドルスタンド　シャインシルバー　3灯',
    handle: 'キャンドルスタンド-シャインシルバー-3灯',
    basePrice: 2000,
    csvData: {
      menu: 'PROP',
      majorCategory: 'キャンドル',
      minorCategory: 'キャンドルスタンド',
      name: 'キャンドルスタンド　シャインシルバー　3灯',
      modelNumber: '201-05-797',
      sizeW: 31,
      sizeD: 0,
      sizeH: 35,
      sizeSH: 0,
      sizeOther: '',
      color: 'シルバー',
      colorOther: '',
      material: '',
      other: '',
      stockQuantity: 2,
      rentalFee1Day: 2000,
      rentalFee1Night2Days: 2400,
      rentalFee2Nights3Days: 2800,
      rentalFee3Nights4Days: 3200,
      publicStatus: '公開',
      newStatus: 'あり',
      campaign: '2025 NEW ITEMS'
    },
    location: 'NY',
    status: 'available'
  },
  {
    sku: '211-12-954',
    title: 'オブジェ　タイドロープ　ゴールド',
    handle: 'オブジェ-タイドロープ-ゴールド',
    basePrice: 1000,
    csvData: {
      menu: 'PROP',
      majorCategory: 'ニュアンスオブジェ',
      minorCategory: 'その他オブジェ',
      name: 'オブジェ　タイドロープ　ゴールド',
      modelNumber: '211-12-954',
      sizeW: 27,
      sizeD: 0,
      sizeH: 14,
      sizeSH: 0,
      sizeOther: '',
      color: 'ゴールド',
      colorOther: '',
      material: '',
      other: '',
      stockQuantity: 1,
      rentalFee1Day: 1000,
      rentalFee1Night2Days: 1200,
      rentalFee2Nights3Days: 1400,
      rentalFee3Nights4Days: 1600,
      publicStatus: '公開',
      newStatus: 'あり',
      campaign: '2025 NEW ITEMS'
    },
    location: 'NY',
    status: 'maintenance'
  },
  {
    sku: '206-02-320',
    title: 'ジュエリートレー　ホワイト×クリア　フリル型　持ち手付',
    handle: 'ジュエリートレー-ホワイト-クリア-フリル型-持ち手付',
    basePrice: 1200,
    csvData: {
      menu: 'PROP',
      majorCategory: 'コスメ',
      minorCategory: 'コスメボックス・トレー',
      name: 'ジュエリートレー　ホワイト×クリア　フリル型　持ち手付',
      modelNumber: '206-02-320',
      sizeW: 20,
      sizeD: 0,
      sizeH: 17,
      sizeSH: 0,
      sizeOther: '',
      color: 'ホワイト;クリア',
      colorOther: '',
      material: '',
      other: '',
      stockQuantity: 1,
      rentalFee1Day: 1200,
      rentalFee1Night2Days: 1440,
      rentalFee2Nights3Days: 1680,
      rentalFee3Nights4Days: 1920,
      publicStatus: '公開',
      newStatus: 'あり',
      campaign: '2025 NEW ITEMS'
    },
    location: 'PR',
    status: 'available'
  }
];

/**
 * Shopify GraphQL APIを呼び出し
 */
async function callShopifyGraphQL(query, variables = {}) {
  const response = await fetch(`https://${SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN
    },
    body: JSON.stringify({ query, variables })
  });

  const data = await response.json();

  if (data.errors) {
    console.error('GraphQLエラー:', JSON.stringify(data.errors, null, 2));
    throw new Error('Shopify API エラー');
  }

  return data.data;
}

/**
 * カテゴリマスタからカテゴリ情報を取得
 */
async function getCategoryInfo(majorCategory, minorCategory) {
  try {
    const shop = SHOPIFY_SHOP;

    // CSVのカテゴリ名からカテゴリマスタのコードにマッピング
    const categoryMapping = {
      'ニュアンスオブジェ': '201',
      '花器': '212',
      'キッチン': '201',
      'コスメ': '206',
      'キャンドル': '201',
      'オブジェ': '211',
      'ガーデン・アウトドア': '104'
    };

    const subCategoryMapping = {
      'その他オブジェ': '06',
      'ゴールド・シルバー': '01',
      'コスメボックス・トレー': '08',
      '銀食器': '07',
      'キャンドルスタンド': '05',
      'BBQグッズ': '10'
    };

    // メジャーカテゴリから最初のカテゴリを取得
    const firstMajorCategory = majorCategory.split(';')[0];
    const categoryCode = categoryMapping[firstMajorCategory] || '201';

    // マイナーカテゴリから最初のサブカテゴリを取得
    const firstMinorCategory = minorCategory.split(';')[0];
    const subCategoryCode = subCategoryMapping[firstMinorCategory] || '06';

    // カテゴリマスタから実際のデータを取得
    const category = await prisma.categoryMaster.findUnique({
      where: {
        shop_code: {
          shop,
          code: categoryCode
        }
      }
    });

    const subCategory = await prisma.subCategoryMaster.findUnique({
      where: {
        shop_code_parentCategoryCode: {
          shop,
          code: subCategoryCode,
          parentCategoryCode: categoryCode
        }
      }
    });

    return {
      categoryCode: category?.code || categoryCode,
      categoryName: category?.name || firstMajorCategory,
      subCategoryCode: subCategory?.code || subCategoryCode,
      subCategoryName: subCategory?.name || firstMinorCategory
    };

  } catch (error) {
    console.warn(`カテゴリ情報取得エラー: ${error.message}`);
    return {
      categoryCode: '201',
      categoryName: 'ニュアンスオブジェ',
      subCategoryCode: '06',
      subCategoryName: 'その他オブジェ'
    };
  }
}

/**
 * レンタル期間バリアントの価格計算
 */
function calculateVariantPrice(basePrice, days) {
  if (days === 1) return basePrice;
  if (days >= 2 && days <= 7) return Math.floor(basePrice * days * 0.2) + basePrice;
  if (days >= 8) return Math.floor(basePrice * 0.1); // 8日以上は1日あたりの価格
  return basePrice;
}

/**
 * 仮予約価格の計算
 */
function calculateProvisionalPrice(basePrice) {
  return Math.floor(basePrice * 0.1); // 10%
}

/**
 * 商品バリアントを作成（Shopify用データ構造）
 */
function createProductVariants(productData) {
  const { sku, title, basePrice } = productData;
  const variants = [];

  // 1-7日レンタルバリアント
  for (let days = 1; days <= 7; days++) {
    variants.push({
      sku: `${sku}-${days}D`,
      title: `${days}日レンタル`,
      price: calculateVariantPrice(basePrice, days),
      inventory: 1,
      option1: `${days}日レンタル`
    });
  }

  // 8日以上レンタルバリアント
  variants.push({
    sku: `${sku}-8D+`,
    title: '8日以上レンタル',
    price: calculateVariantPrice(basePrice, 8),
    inventory: 1,
    option1: '8日以上レンタル'
  });

  // 仮予約バリアント
  variants.push({
    sku: `${sku}-PROV`,
    title: '仮予約',
    price: calculateProvisionalPrice(basePrice),
    inventory: 1,
    option1: '仮予約'
  });

  return variants;
}

/**
 * Shopifyに商品を作成（正しいAPIスキーマ使用）
 */
async function createShopifyProduct(productData) {
  const { sku, title, handle, basePrice, csvData, location, status } = productData;

  // カテゴリ情報を取得
  const categoryInfo = await getCategoryInfo(csvData.majorCategory, csvData.minorCategory);

  // メタフィールド作成（productCodeを追加）
  const metafields = [
    {
      namespace: 'rental',
      key: 'basic_info',
      value: JSON.stringify({
        productCode: sku, // 必須フィールドを追加
        ...csvData,
        categoryMasterCode: categoryInfo.categoryCode,
        categoryMasterName: categoryInfo.categoryName,
        subCategoryMasterCode: categoryInfo.subCategoryCode,
        subCategoryMasterName: categoryInfo.subCategoryName,
        location: location,
        status: status
      }),
      type: 'json'
    },
    {
      namespace: 'rental',
      key: 'pricing',
      value: JSON.stringify({
        productCode: sku, // 必須フィールドを追加
        basePrice: basePrice,
        depositRate: 0.1,
        discountRules: {
          day2_7_rate: 0.2,
          day8_plus_rate: 0.1
        },
        provisionalRate: 0.1,
        minimumDays: 1,
        maximumDays: 30
      }),
      type: 'json'
    }
  ];

  // 1. 基本商品を作成
  const productMutation = `
    mutation productCreate($product: ProductCreateInput!) {
      productCreate(product: $product) {
        product {
          id
          title
          handle
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const productVariables = {
    product: {
      title,
      handle,
      tags: ['テスト', 'レンタル商品', csvData.menu],
      productType: 'レンタル商品',
      vendor: 'PEACES',
      metafields,
      productOptions: [
        {
          name: 'レンタル期間',
          position: 1,
          values: [
            { name: '1日レンタル' },
            { name: '2日レンタル' },
            { name: '3日レンタル' },
            { name: '4日レンタル' },
            { name: '5日レンタル' },
            { name: '6日レンタル' },
            { name: '7日レンタル' },
            { name: '8日以上レンタル' },
            { name: '仮予約' }
          ]
        }
      ]
    }
  };

  const productResult = await callShopifyGraphQL(productMutation, productVariables);

  if (productResult.productCreate.userErrors.length > 0) {
    throw new Error(`Shopify商品作成エラー: ${JSON.stringify(productResult.productCreate.userErrors)}`);
  }

  const product = productResult.productCreate.product;
  console.log(`✅ Shopify商品作成: ${sku} - ${title}`);

  // 2. バリアントを一括作成
  const variants = [];

  // 1-7日レンタルバリアント
  for (let days = 1; days <= 7; days++) {
    variants.push({
      price: calculateVariantPrice(basePrice, days).toString(),
      inventoryItem: {
        sku: `${sku}-${days}D`,
        tracked: true
      },
      optionValues: [
        {
          optionName: 'レンタル期間',
          name: `${days}日レンタル`
        }
      ],
      inventoryQuantities: [
        {
          availableQuantity: status === 'available' ? 1 : 0,
          locationId: 'gid://shopify/Location/95618515240' // デフォルトロケーション
        }
      ]
    });
  }

  // 8日以上レンタルバリアント
  variants.push({
    price: calculateVariantPrice(basePrice, 8).toString(),
    inventoryItem: {
      sku: `${sku}-8D+`,
      tracked: true
    },
    optionValues: [
      {
        optionName: 'レンタル期間',
        name: '8日以上レンタル'
      }
    ],
    inventoryQuantities: [
      {
        availableQuantity: status === 'available' ? 1 : 0,
        locationId: 'gid://shopify/Location/95618515240'
      }
    ]
  });

  // 仮予約バリアント
  variants.push({
    price: Math.floor(basePrice * 0.1).toString(),
    inventoryItem: {
      sku: `${sku}-PROV`,
      tracked: true
    },
    optionValues: [
      {
        optionName: 'レンタル期間',
        name: '仮予約'
      }
    ],
    inventoryQuantities: [
      {
        availableQuantity: status === 'available' ? 1 : 0,
        locationId: 'gid://shopify/Location/95618515240'
      }
    ]
  });

  // バリアント作成
  const variantMutation = `
    mutation productVariantsBulkCreate($productId: ID!, $variants: [ProductVariantsBulkInput!]!) {
      productVariantsBulkCreate(productId: $productId, variants: $variants) {
        product {
          id
          variants(first: 20) {
            edges {
              node {
                id
                sku
                price
                displayName
              }
            }
          }
        }
        productVariants {
          id
          sku
          price
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variantVariables = {
    productId: product.id,
    variants
  };

  const variantResult = await callShopifyGraphQL(variantMutation, variantVariables);

  if (variantResult.productVariantsBulkCreate.userErrors.length > 0) {
    throw new Error(`Shopifyバリアント作成エラー: ${JSON.stringify(variantResult.productVariantsBulkCreate.userErrors)}`);
  }

  console.log(`✅ Shopifyバリアント作成: ${variants.length}個`);

  return {
    ...product,
    variants: variantResult.productVariantsBulkCreate.productVariants
  };
}

/**
 * Prismaに商品を同期
 */
async function syncProductToPrisma(shopifyProduct, productData) {
  const { sku, title, basePrice, csvData, location, status } = productData;
  const categoryInfo = await getCategoryInfo(csvData.majorCategory, csvData.minorCategory);

  // basicInfo構築
  const basicInfo = {
    ...csvData,
    categoryMasterCode: categoryInfo.categoryCode,
    categoryMasterName: categoryInfo.categoryName,
    subCategoryMasterCode: categoryInfo.subCategoryCode,
    subCategoryMasterName: categoryInfo.subCategoryName,
    location: location,
    status: status
  };

  const product = await prisma.product.create({
    data: {
      shop: SHOPIFY_SHOP,
      shopifyId: shopifyProduct.id,
      sku,
      title,
      handle: shopifyProduct.handle,
      price: basePrice,
      status: status === 'maintenance' ? 'MAINTENANCE' : 'AVAILABLE',
      inventory: status === 'available' ? 1 : 0,
      basicInfo: JSON.stringify(basicInfo),
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });

  console.log(`✅ Prisma同期完了: ${sku}`);
  return product;
}

/**
 * テスト用商品を一括登録（正しいデータフロー: Shopify → Prisma）
 */
async function registerTestProducts() {
  console.log('=== 正しいデータフローでのテスト商品登録 ===');
  console.log('データの流れ: Shopify → Prisma');
  console.log(`対象ショップ: ${SHOPIFY_SHOP}`);
  console.log(`登録予定商品数: ${TEST_PRODUCTS.length}件`);

  try {
    await prisma.$connect();
    console.log('✅ データベース接続成功');

    // 既存のテスト商品を削除
    console.log('\n既存のテスト商品を削除中...');
    const testSKUs = TEST_PRODUCTS.map(p => p.sku);

    // 関連する予約データを削除
    try {
      await prisma.booking.deleteMany({
        where: {
          shop: SHOPIFY_SHOP,
          product: {
            sku: { in: testSKUs }
          }
        }
      });
    } catch (error) {
      console.log('予約データ削除スキップ（テーブルが存在しない可能性）');
    }

    // 商品を削除
    const deletedCount = await prisma.product.deleteMany({
      where: {
        shop: SHOPIFY_SHOP,
        sku: { in: testSKUs }
      }
    });

    console.log(`🗑️ 既存商品削除完了: ${deletedCount.count}件`);

    // 新しい商品を登録（Shopify → Prisma）
    console.log('\n新しいテスト商品を登録中...');
    const registeredProducts = [];

    for (const productData of TEST_PRODUCTS) {
      console.log(`\n📦 商品登録中: ${productData.sku} - ${productData.title}`);

      try {
        // 1. Shopifyに商品作成
        const shopifyProduct = await createShopifyProduct(productData);

        // 2. Prismaに同期
        const prismaProduct = await syncProductToPrisma(shopifyProduct, productData);

        registeredProducts.push(prismaProduct);
        console.log(`✅ 商品登録成功: ${productData.sku}`);

      } catch (error) {
        console.error(`❌ 商品登録エラー (${productData.sku}):`, error.message);
        continue; // 次の商品に進む
      }
    }

    console.log(`\n✅ 商品登録完了: ${registeredProducts.length}件`);
    return registeredProducts;

  } catch (error) {
    console.error('❌ エラー:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * メイン処理
 */
async function main() {
  try {
    const products = await registerTestProducts();

    console.log('\n=== 登録完了 ===');
    console.log('次のステップ:');
    console.log('1. Shopifyに商品とバリアントを同期');
    console.log('2. テスト用予約データの作成');
    console.log('3. 価格計算テストの実行');
    console.log('4. 予約システムの動作確認');

  } catch (error) {
    console.error('テスト商品登録に失敗しました:', error);
    process.exit(1);
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
