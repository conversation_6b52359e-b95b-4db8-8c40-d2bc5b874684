/**
 * 仮予約期限切れチェックスクリプト
 *
 * 期限切れの仮予約を自動的にキャンセルするスクリプト
 * 実行方法: npx tsx scripts/check-provisional-expiration.ts
 */

import dotenv from 'dotenv';
import { ProvisionalExpirationService } from '../app/services/scheduled/check-provisional-expiration.service';

// 環境変数の読み込み
dotenv.config();

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('仮予約期限切れチェックを開始します...');
    
    // 仮予約期限切れチェックサービスを初期化
    const expirationService = new ProvisionalExpirationService();
    
    // 期限切れの仮予約をチェック
    const result = await expirationService.checkExpiredProvisionalBookings();
    
    // 結果を表示
    console.log('\n===== 処理結果 =====');
    console.log(`処理ステータス: ${result.success ? '成功' : '失敗'}`);
    console.log(`処理件数: ${result.processedCount}件`);
    console.log(`エラー件数: ${result.errors.length}件`);
    
    if (result.errors.length > 0) {
      console.log('\n===== エラー詳細 =====');
      result.errors.forEach((error, index) => {
        console.log(`[${index + 1}] ${error}`);
      });
    }
    
    console.log('\n処理が完了しました。');
    
    // 成功した場合は0、失敗した場合は1を返す
    process.exit(result.success ? 0 : 1);
  } catch (error) {
    console.error('処理中にエラーが発生しました:', error);
    process.exit(1);
  }
}

// スクリプトを実行
main();
