{"timestamp": "2025-05-19T07:10:24.777Z", "duration": 2.215, "totalTests": 3, "successCount": 3, "failureCount": 0, "tests": [{"testName": "API障害からの回復テスト", "success": true, "timestamp": "2025-05-19T07:10:24.842Z", "message": "API障害から正常に回復しました", "error": "", "duration": 64}, {"testName": "ネットワーク切断からの回復テスト", "success": true, "timestamp": "2025-05-19T07:10:24.856Z", "message": "ネットワーク切断から正常に回復しました", "error": "", "duration": 14}, {"testName": "データ不整合の自動修復テスト", "success": true, "timestamp": "2025-05-19T07:10:26.991Z", "message": "データ不整合を正常に修復しました", "error": "", "duration": 2135}], "environment": {"nodeVersion": "v20.18.1", "os": "darwin arm64"}, "performance": {"averageTestDuration": 737.6666666666666, "slowestTest": {"name": "データ不整合の自動修復テスト", "duration": 2135}, "fastestTest": {"name": "ネットワーク切断からの回復テスト", "duration": 14}}}