/**
 * 商品情報取得テストスクリプト
 * 
 * このスクリプトは、Shopify Admin APIを使用して商品情報を取得します。
 * 
 * 実行方法: npx tsx scripts/test-get-products.ts
 */

import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Shopify GraphQL APIクライアントの初期化
function createShopifyClient() {
  const shop = process.env.SHOPIFY_SHOP;
  const accessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
  
  if (!shop || !accessToken) {
    throw new Error('SHOPIFY_SHOP または SHOPIFY_ADMIN_API_ACCESS_TOKEN が設定されていません');
  }
  
  return new GraphQLClient(
    `https://${shop}/admin/api/2025-01/graphql.json`,
    {
      headers: {
        'X-Shopify-Access-Token': accessToken,
        'Content-Type': 'application/json',
      },
    }
  );
}

// 商品取得クエリ
const GET_PRODUCTS = gql`
  query getProducts($first: Int!) {
    products(first: $first) {
      edges {
        node {
          id
          title
          handle
          status
          variants(first: 10) {
            edges {
              node {
                id
                title
                price
                inventoryQuantity
                sku
              }
            }
          }
        }
      }
    }
  }
`;

// 商品情報取得テスト
async function testGetProducts() {
  try {
    console.log('商品情報取得テストを開始します...');
    
    // Shopify APIクライアントを作成
    const client = createShopifyClient();
    
    // 商品情報を取得
    console.log('商品情報を取得中...');
    const response = await client.request(GET_PRODUCTS, {
      first: 10
    });
    
    // 商品情報を表示
    console.log('商品情報:');
    const products = response.products.edges.map((edge: any) => edge.node);
    
    products.forEach((product: any, index: number) => {
      console.log(`\n商品 ${index + 1}:`);
      console.log(`  ID: ${product.id}`);
      console.log(`  タイトル: ${product.title}`);
      console.log(`  ハンドル: ${product.handle}`);
      console.log(`  ステータス: ${product.status}`);
      
      console.log('  バリアント:');
      const variants = product.variants.edges.map((edge: any) => edge.node);
      
      variants.forEach((variant: any, vIndex: number) => {
        console.log(`    バリアント ${vIndex + 1}:`);
        console.log(`      ID: ${variant.id}`);
        console.log(`      タイトル: ${variant.title}`);
        console.log(`      価格: ${variant.price}`);
        console.log(`      在庫数: ${variant.inventoryQuantity}`);
        console.log(`      SKU: ${variant.sku}`);
      });
    });
    
    // テスト用の商品IDとバリアントIDを表示
    if (products.length > 0) {
      const firstProduct = products[0];
      const firstVariant = firstProduct.variants.edges[0]?.node;
      
      if (firstVariant) {
        console.log('\nテスト用の環境変数設定:');
        console.log(`TEST_PRODUCT_ID=${firstProduct.id}`);
        console.log(`TEST_VARIANT_ID=${firstVariant.id}`);
      }
    }
    
    return {
      success: true,
      products
    };
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    throw error;
  }
}

// スクリプトを実行
testGetProducts()
  .then(() => {
    console.log('\nテストが完了しました');
    process.exit(0);
  })
  .catch((error) => {
    console.error('テストが失敗しました:', error);
    process.exit(1);
  });
