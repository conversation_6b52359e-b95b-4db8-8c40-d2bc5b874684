/**
 * 予約を削除するスクリプト
 *
 * 使用方法:
 * node scripts/delete-booking.js <商品ID> <開始日> <終了日>
 *
 * 例:
 * node scripts/delete-booking.js cmaw8bj8s0005h66jop4huobo 2025-05-30 2025-05-30
 */

import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function deleteBooking() {
  try {
    // コマンドライン引数を取得
    const args = process.argv.slice(2);

    if (args.length < 1) {
      console.error('使用方法: node scripts/delete-booking.js <商品ID> [<開始日> <終了日>]');
      process.exit(1);
    }

    const productId = args[0];
    const startDate = args[1] ? new Date(args[1]) : undefined;
    const endDate = args[2] ? new Date(args[2]) : undefined;

    console.log('=== 予約削除処理開始 ===');
    console.log(`商品ID: ${productId}`);

    if (startDate && endDate) {
      console.log(`期間: ${startDate.toISOString().split('T')[0]} 〜 ${endDate.toISOString().split('T')[0]}`);
    }

    // 予約を検索
    const whereClause = {
      productId,
      ...(startDate && endDate ? {
        AND: [
          { startDate: { lte: endDate } },
          { endDate: { gte: startDate } }
        ]
      } : {})
    };

    const bookings = await prisma.booking.findMany({
      where: whereClause,
      select: {
        id: true,
        bookingId: true,
        productId: true,
        startDate: true,
        endDate: true,
        status: true,
        customerName: true,
      }
    });

    console.log(`${bookings.length}件の予約が見つかりました`);

    // 予約情報を表示
    bookings.forEach((booking, index) => {
      console.log(`\n予約 ${index + 1}:`);
      console.log(`  ID: ${booking.id}`);
      console.log(`  予約ID: ${booking.bookingId}`);
      console.log(`  商品ID: ${booking.productId}`);
      console.log(`  期間: ${booking.startDate.toISOString().split('T')[0]} 〜 ${booking.endDate.toISOString().split('T')[0]}`);
      console.log(`  状態: ${booking.status}`);
      console.log(`  顧客: ${booking.customerName}`);
    });

    // 削除確認
    console.log('\n上記の予約を削除します。');

    // 予約を削除
    const deleteResult = await prisma.booking.deleteMany({
      where: whereClause
    });

    console.log(`${deleteResult.count}件の予約を削除しました`);
    console.log('=== 予約削除処理完了 ===');

  } catch (error) {
    console.error('予約削除エラー:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

deleteBooking();