# Shopify同期問題の解決

**作成日**: 2025-05-28
**作成者**: システム開発チーム
**最終更新日**: 2025-05-28

## 概要

本ドキュメントでは、Shopifyとの同期処理に関する問題と解決策について説明します。特に、予約データがPrismaデータベースに保存されるものの、Shopifyに同期されない問題に焦点を当てています。

## 現状の問題

現在、以下の問題が発生しています：

1. **Prismaには保存されるがShopifyには同期されない**：
   - 予約データはPrismaデータベースに正常に保存される
   - しかし、Shopifyのメタフィールドには反映されない
   - 結果として、Shopify上で予約状況が確認できない

2. **同期処理のエラー**：
   - Shopify GraphQL APIの呼び出しに問題がある可能性
   - 認証情報やアクセス権限の問題
   - エラーハンドリングの不足

3. **環境変数とAPI認証の問題**：
   - 環境変数からShopify APIアクセストークンを取得する方法に問題
   - Shopify Admin APIの使用方法が最適でない

## 原因分析

詳細な調査の結果、以下の原因が特定されました：

1. **API認証方法の問題**：
   - 環境変数からトークンを取得する代わりに、Shopify Remixアプリの`admin`オブジェクトを使用すべき
   - `createShopifyClient`関数が適切に機能していない

2. **GraphQL呼び出しの問題**：
   - GraphQLクエリの実行方法に問題がある
   - レスポンス処理が適切でない

3. **エラーハンドリングの不足**：
   - エラーが発生しても詳細なログが出力されない
   - リトライ機能が適切に機能していない

## 解決策

以下の修正を実施しました：

### 1. Shopify APIアクセスの統一

```typescript
// 修正前
function createShopifyClient(): GraphQLClient {
  const shopifyShop = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';
  const shopifyAdminApiAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

  if (!shopifyAdminApiAccessToken) {
    throw new Error('SHOPIFY_ADMIN_API_ACCESS_TOKEN環境変数が設定されていません');
  }

  return new GraphQLClient(
    `https://${shopifyShop}/admin/api/2024-01/graphql.json`,
    {
      headers: {
        'X-Shopify-Access-Token': shopifyAdminApiAccessToken,
        'Content-Type': 'application/json',
      },
    }
  );
}

// 修正後
// GraphQLクライアント関数は削除し、代わりにShopify adminオブジェクトを使用
```

### 2. メタフィールド更新関数の修正

```typescript
// 修正前
export async function updateBookingMetafield(
  prisma: PrismaClient,
  productId: string,
  forceUpdate: boolean = false,
  retryCount: number = 3
): Promise<any> {
  // ...
  const shopifyClient = createShopifyClient();
  // ...
}

// 修正後
export async function updateBookingMetafield(
  prisma: PrismaClient,
  admin: AdminApiContext,
  productId: string,
  forceUpdate: boolean = false,
  retryCount: number = 3
): Promise<any> {
  // ...
  // admin オブジェクトを使用
  // ...
}
```

### 3. GraphQL呼び出しの修正

```typescript
// 修正前
const result = await shopifyClient.request(UPDATE_METAFIELD, {
  metafields: [
    {
      ownerId: shopifyId,
      namespace: 'rental',
      key: 'bookings',
      value: JSON.stringify(metafieldData),
      type: 'json'
    }
  ]
});

// 修正後
const response = await admin.graphql(
  UPDATE_METAFIELD,
  {
    variables: {
      metafields: [
        {
          ownerId: shopifyId,
          namespace: 'rental',
          key: 'bookings',
          value: JSON.stringify(metafieldData),
          type: 'json'
        }
      ]
    }
  }
);

const updateResult = await response.json();
```

### 4. エラーハンドリングの強化

```typescript
try {
  console.log('GraphQL呼び出しを実行します...');
  try {
    const response = await admin.graphql(
      UPDATE_METAFIELD,
      {
        variables: {
          metafields: [
            {
              ownerId: shopifyId,
              namespace: 'rental',
              key: 'bookings',
              value: JSON.stringify(metafieldData),
              type: 'json'
            }
          ]
        }
      }
    );
    
    console.log('GraphQL応答を受信しました');
    const updateResult = await response.json();
    console.log('GraphQL応答内容:', JSON.stringify(updateResult, null, 2));

    if (updateResult.data?.metafieldsSet?.userErrors?.length > 0) {
      console.error('メタフィールド更新エラー:', updateResult.data.metafieldsSet.userErrors);
      throw new Error(`メタフィールドの設定中にエラーが発生しました: ${JSON.stringify(updateResult.data.metafieldsSet.userErrors)}`);
    }

    return updateResult.data;
  } catch (graphqlError) {
    console.error('GraphQL呼び出しエラー:', graphqlError);
    throw graphqlError;
  }
} catch (error) {
  // 詳細なエラー情報を記録
  handleError(error, {
    operation: 'updateBookingMetafield',
    productId,
    forceUpdate,
    retryCount
  }, true); // エラー通知を送信

  throw error;
}
```

### 5. 予約作成アクション関数の修正

```typescript
// Shopifyメタフィールドを更新
try {
  console.log('===== Shopifyメタフィールド更新処理開始 =====');
  console.log(`作成された予約数: ${createdBookings.length}`);
  
  for (const booking of createdBookings) {
    console.log(`予約ID: ${booking.id}, 商品ID: ${booking.productId} の処理を開始`);
    
    // 商品情報を取得
    const product = await prisma.product.findUnique({
      where: { id: booking.productId },
      select: { shopifyId: true, title: true, sku: true }
    });

    console.log('商品情報:', product);

    if (product && product.shopifyId) {
      console.log(`Shopify商品ID: ${product.shopifyId} のメタフィールドを更新します`);
      
      try {
        // Shopifyメタフィールドを更新
        const result = await updateBookingMetafield(prisma, admin, booking.productId, true);
        console.log('メタフィールド更新結果:', result);
        console.log(`商品ID ${booking.productId} (Shopify ID: ${product.shopifyId}) のメタフィールドを更新しました`);
      } catch (innerError) {
        console.error(`商品ID ${booking.productId} のメタフィールド更新中にエラーが発生:`, innerError);
      }
    } else {
      console.error(`商品ID ${booking.productId} のShopify IDが見つかりません`);
    }
  }
  
  console.log('===== Shopifyメタフィールド更新処理完了 =====');
} catch (metafieldError) {
  console.error('Shopifyメタフィールドの更新中にエラーが発生しました:', metafieldError);
  // メタフィールドの更新に失敗しても、予約の作成自体は成功とする
}
```

## 実装の詳細

1. **`metafield-updater.ts`の修正**：
   - `createShopifyClient`関数を削除
   - `updateBookingMetafield`関数を修正して`admin`オブジェクトを受け取るように変更
   - GraphQL呼び出しを`admin.graphql`を使用するように修正
   - エラーハンドリングを強化

2. **`app.bookings.new.tsx`の修正**：
   - `updateBookingMetafield`関数の呼び出しを修正して`admin`オブジェクトを渡すように変更
   - デバッグログを追加

## テスト結果

修正後、以下のテストを実施しました：

1. **予約作成テスト**：
   - 新規予約を作成
   - Prismaデータベースに正常に保存されることを確認
   - Shopifyメタフィールドに正常に同期されることを確認

2. **エラーハンドリングテスト**：
   - 意図的にエラーを発生させて、エラーハンドリングが機能することを確認
   - リトライ機能が正常に動作することを確認

## 今後の課題

1. **パフォーマンス最適化**：
   - 同期処理の最適化
   - バッチ処理の検討

2. **エラー通知の強化**：
   - エラー発生時の通知システムの構築
   - エラーログの集中管理

3. **同期状態の可視化**：
   - 同期状態を確認するためのダッシュボードの実装
   - 同期エラーの自動修復機能

## 参考資料

- [Shopify Admin API Documentation](https://shopify.dev/api/admin)
- [Shopify App Remix Documentation](https://shopify.dev/apps/tools/app-bridge/getting-started/remix)
- [GraphQL API Best Practices](https://shopify.dev/api/usage/best-practices)
