# ハイブリッド住所管理：Shopify標準＋Prisma拡張アプローチ

## 基本設計思想

### 大多数のユーザー（90%）
- **Shopify標準住所のみ使用**
- 配送先1件、請求先は配送先と同じ
- 追加の管理コストなし

### 一部のユーザー（10%）
- **Shopify標準 + Prisma拡張**
- 複数住所、日本固有項目、特別な要件
- 必要に応じて段階的に拡張

## Shopify標準住所フィールドの活用

### 利用可能な標準フィールド
```typescript
interface ShopifyAddress {
  first_name: string;      // 担当者名
  last_name: string;       // 担当者姓
  company: string;         // 会社名
  address1: string;        // 住所1
  address2: string;        // 住所2（建物名・部屋番号）
  city: string;           // 市区町村
  province: string;       // 都道府県
  zip: string;           // 郵便番号
  country: string;       // 国
  phone: string;         // 電話番号
}
```

### 日本特有項目の標準フィールド活用案

```typescript
// 標準フィールドを日本用にマッピング
const japaneseAddressMapping = {
  company: "会社名",           // 標準フィールド活用
  address2: "建物名・部署",    // 標準フィールド活用
  first_name: "担当者名",      // 標準フィールド活用
  last_name: "担当者姓",       // 標準フィールド活用
  phone: "電話番号",          // 標準フィールド活用（携帯も統合）

  // 足りない項目 → Prisma拡張で対応
  fax: "FAX番号",            // 拡張必要
  storeStaff: "ストア担当者",  // 拡張必要
  orderPath: "受注方法",      // 拡張必要（店頭/電話/FAX/メール/WEB）
  department: "部署名",       // 拡張必要
  position: "役職",          // 拡張必要
  delivery_time: "配送時間帯", // 拡張必要
  special_notes: "配送備考",   // 拡張必要
};
```

## 実装アプローチ

### 1. 基本ユーザー：Shopify標準のみ

```typescript
// 通常の顧客：Shopify Customer APIのみ使用
const customer = await shopify.rest.Customer.find({
  session,
  id: customerId,
});

// default_address が配送先として使用される
const shippingAddress = customer.default_address;
const billingAddress = customer.default_address; // 同じ住所
```

### 2. 拡張ユーザー：Shopify + Prisma

```prisma
// 追加情報が必要な場合のみ作成される拡張テーブル
model AddressExtension {
  id                String  @id @default(cuid())
  shop              String
  customerId        String  // Shopify顧客ID
  shopifyAddressId  String? // Shopify住所ID（nullの場合は追加住所）

  // 日本の業務に必要な基本項目
  fax               String? // FAX番号
  storeStaff        String? // ストア担当者
  orderPath         String? // 受注方法（0:店頭,1:電話,2:FAX,3:メール,4:WEB）

  // 日本固有の拡張項目
  department        String? // 部署名
  position          String? // 役職
  deliveryTimeSlot  String? // 配送時間帯
  deliveryNotes     String? // 配送備考
  billingContact    String? // 請求担当者

  // 複数住所の場合
  addressType       String  // 'shipping' | 'billing'
  addressName       String? // 「本社」「倉庫」「営業所」など
  isAdditional      Boolean @default(false) // 追加住所フラグ

  // 法人向け拡張フィールド
  corporateInfo     Json?   // 法人情報（部署、役職など）
  deliveryMemo      String? // 配送メモ（常に表示）
  priority          Int     @default(0) // 表示順序

  // 住所の有効性管理
  isActive          Boolean @default(true)
  validFrom         DateTime @default(now())
  validUntil        DateTime? // 有効期限（イベント会場など）

  // 使用履歴管理
  lastUsedAt        DateTime? // 最後に使用した日時
  usageCount        Int     @default(0) // 使用回数（よく使う住所の判定）

  // Shopify標準では表現できない住所情報
  fullAddress       Json?   // 完全な住所情報（必要な場合）

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([shop, customerId])
  @@index([shop, customerId, shopifyAddressId])
  @@index([shop, customerId, priority])
  @@index([shop, customerId, lastUsedAt])
  @@map("address_extensions")
}

// 受注方法マスタ
model OrderPath {
  id          String @id @default(cuid())
  shop        String
  code        String // "0", "1", "2", "3", "4"
  name        String // "店頭", "電話", "FAX", "メール", "WEB"
  isDefault   Boolean @default(false) // WEBをデフォルトに設定
  isActive    Boolean @default(true)

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([shop, code])
  @@index([shop])
  @@map("order_paths")
}

// ストア担当者マスタ
model StoreStaff {
  id          String @id @default(cuid())
  shop        String
  staffCode   String
  staffName   String
  isActive    Boolean @default(true)

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([shop, staffCode])
  @@index([shop])
  @@map("store_staff")
}
```

### 3. 顧客タイプの判定ロジック

```typescript
// app/services/customer-address.service.ts
export class CustomerAddressService {

  async getCustomerAddresses(customerId: string) {
    // 1. Shopify標準住所を取得
    const customer = await this.getShopifyCustomer(customerId);
    const standardAddresses = customer.addresses || [];

    // 2. 拡張情報があるかチェック
    const extensions = await prisma.addressExtension.findMany({
      where: { customerId, shop: this.shop }
    });

    // 3. 統合データを構築
    return this.mergeAddressData(standardAddresses, extensions);
  }

  private mergeAddressData(shopifyAddresses, extensions) {
    return shopifyAddresses.map(addr => {
      const extension = extensions.find(ext =>
        ext.shopifyAddressId === addr.id.toString()
      );

      return {
        ...addr,                    // Shopify標準フィールド
        extension: extension || {}  // 拡張情報（存在する場合のみ）
      };
    });
  }
}
```

## UI実装：段階的な拡張

### 1. 基本表示：Shopify標準フィールドのみ

```typescript
// app/components/CustomerAddressDisplay.tsx
export function CustomerAddressDisplay({ address, showExtended = false }) {
  return (
    <div className="address-card">
      <h4>{address.company || `${address.first_name} ${address.last_name}`}</h4>
      <p>{address.address1}</p>
      {address.address2 && <p>{address.address2}</p>}
      <p>{address.city}, {address.province} {address.zip}</p>
      <p>{address.phone}</p>

      {/* 拡張情報の表示（存在する場合のみ） */}
      {showExtended && address.extension && (
        <div className="address-extension">
          {address.extension.department && (
            <p>部署: {address.extension.department}</p>
          )}
          {address.extension.deliveryTimeSlot && (
            <p>配送時間: {address.extension.deliveryTimeSlot}</p>
          )}
        </div>
      )}

      {/* 拡張機能への入口（必要な場合のみ表示） */}
      {!address.extension && (
        <button
          onClick={() => setShowExtensionForm(true)}
          className="btn-link"
        >
          詳細情報を追加
        </button>
      )}
    </div>
  );
}
```

### 2. 拡張フォーム：必要時のみ表示

```typescript
// app/components/AddressExtensionForm.tsx
export function AddressExtensionForm({ address, onSave }) {
  const [extension, setExtension] = useState({
    fax: '',
    storeStaff: '',
    orderPath: '4', // デフォルトはWEB
    department: '',
    position: '',
    deliveryTimeSlot: '',
    deliveryNotes: ''
  });

  const orderPathOptions = [
    { value: '0', label: '店頭' },
    { value: '1', label: '電話' },
    { value: '2', label: 'FAX' },
    { value: '3', label: 'メール' },
    { value: '4', label: 'WEB' } // デフォルト
  ];

  return (
    <form onSubmit={handleSave}>
      <h3>詳細情報の追加</h3>

      {/* 基本業務情報 */}
      <div className="business-info-section">
        <h4>業務情報</h4>

        <div className="field-group">
          <label>FAX番号</label>
          <input
            type="tel"
            placeholder="03-1234-5678"
            value={extension.fax}
            onChange={(e) => setExtension({...extension, fax: e.target.value})}
          />
        </div>

        <div className="field-group">
          <label>ストア担当者</label>
          <select
            value={extension.storeStaff}
            onChange={(e) => setExtension({...extension, storeStaff: e.target.value})}
          >
            <option value="">選択してください</option>
            <option value="田中">田中</option>
            <option value="佐藤">佐藤</option>
            <option value="鈴木">鈴木</option>
          </select>
        </div>

        <div className="field-group">
          <label>受注方法</label>
          <select
            value={extension.orderPath}
            onChange={(e) => setExtension({...extension, orderPath: e.target.value})}
          >
            {orderPathOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <small className="help-text">デフォルト: WEB</small>
        </div>
      </div>

      {/* 組織情報 */}
      <div className="organization-section">
        <h4>組織情報</h4>

        <div className="field-group">
          <label>部署名</label>
          <input
            placeholder="例：営業部、制作部"
            value={extension.department}
            onChange={(e) => setExtension({...extension, department: e.target.value})}
          />
        </div>

        <div className="field-group">
          <label>役職</label>
          <input
            placeholder="例：部長、主任"
            value={extension.position}
            onChange={(e) => setExtension({...extension, position: e.target.value})}
          />
        </div>
      </div>

      {/* 配送情報 */}
      <div className="delivery-section">
        <h4>配送情報</h4>

        <div className="field-group">
          <label>配送時間帯</label>
          <select
            value={extension.deliveryTimeSlot}
            onChange={(e) => setExtension({...extension, deliveryTimeSlot: e.target.value})}
          >
            <option value="">指定なし</option>
            <option value="9-12">午前中（9-12時）</option>
            <option value="14-16">午後前半（14-16時）</option>
            <option value="16-18">午後後半（16-18時）</option>
            <option value="18-21">夜間（18-21時）</option>
          </select>
        </div>

        <div className="field-group">
          <label>配送に関する備考</label>
          <textarea
            placeholder="配送時の注意事項、アクセス方法、受付での手続きなど"
            value={extension.deliveryNotes}
            onChange={(e) => setExtension({...extension, deliveryNotes: e.target.value})}
            rows={3}
          />
        </div>
      </div>

      <div className="form-actions">
        <button type="submit" className="btn btn-primary">保存</button>
        <button type="button" onClick={onCancel} className="btn btn-secondary">キャンセル</button>
      </div>
    </form>
  );
}
```

## チェックアウト時の統合

### 1. 住所選択の統合

```typescript
// チェックアウト時の住所取得
export async function getCheckoutAddresses(customerId: string) {
  const customer = await shopify.rest.Customer.find({ session, id: customerId });
  const extensions = await prisma.addressExtension.findMany({
    where: { customerId, shop }
  });

  // Shopify標準住所に拡張情報をマージ
  const enrichedAddresses = customer.addresses.map(addr => {
    const extension = extensions.find(ext =>
      ext.shopifyAddressId === addr.id.toString()
    );

    return {
      shopifyAddress: addr,
      extension: extension,
      displayName: this.generateDisplayName(addr, extension)
    };
  });

  // 追加住所（Prismaのみに存在）も含める
  const additionalAddresses = extensions
    .filter(ext => !ext.shopifyAddressId)
    .map(ext => this.buildAddressFromExtension(ext));

  return [...enrichedAddresses, ...additionalAddresses];
}
```

### 2. 注文作成時の住所設定

```typescript
// Draft Order作成時
export async function createOrderWithExtendedAddress(
  customerId: string,
  selectedAddress: EnrichedAddress
) {
  const draftOrder = new shopify.rest.DraftOrder({ session });

  // Shopify標準住所を設定
  draftOrder.shipping_address = selectedAddress.shopifyAddress;

  // 拡張情報は注文のメタフィールドまたはnoteに保存
  if (selectedAddress.extension) {
    draftOrder.note_attributes = [
      // 業務情報
      { name: "FAX番号", value: selectedAddress.extension.fax || '' },
      { name: "ストア担当者", value: selectedAddress.extension.storeStaff || '' },
      { name: "受注方法", value: getOrderPathName(selectedAddress.extension.orderPath) },

      // 組織情報
      { name: "配送部署", value: selectedAddress.extension.department || '' },
      { name: "役職", value: selectedAddress.extension.position || '' },

      // 配送情報
      { name: "配送時間帯", value: selectedAddress.extension.deliveryTimeSlot || '' },
      { name: "配送備考", value: selectedAddress.extension.deliveryNotes || '' }
    ].filter(attr => attr.value); // 空の値は除外
  }

  await draftOrder.save();
}

// 受注方法コードから名前を取得
function getOrderPathName(orderPathCode: string): string {
  const orderPaths = {
    '0': '店頭',
    '1': '電話',
    '2': 'FAX',
    '3': 'メール',
    '4': 'WEB'
  };
  return orderPaths[orderPathCode] || 'WEB';
}
```

## 段階的実装計画

### Phase 1: 基本実装（現在のShopify標準活用）
```
- Shopify標準住所フィールドの最大活用
- company, address2 を日本固有項目として活用
- phone フィールドで電話・携帯を統合管理
- 基本的な住所管理はShopify Admin
- 配送メモの常時表示（簡易版）
```

### Phase 2: 日本業務拡張
```
- OrderPath テーブル追加（受注方法管理）
- StoreStaff テーブル追加（担当者管理）
- AddressExtension テーブル追加（FAX、部署、役職など）
- 受注方法のデフォルト設定（WEB）
- 支払方法はShopify連携（表示のみ）
```

### Phase 3: 動的住所管理
```
- Customer テーブルに法人フラグ追加
- 法人フラグによる動的UI制御
- 住所上限の動的設定（個人3件、法人10件）
- 使用履歴による住所の優先順位付け
```

### Phase 4: 高度な機能
```
- 住所の自動補完・住所候補の提案
- 配送ルート最適化
- 地域別配送料金自動計算
- 組織構造管理（親子関係のある法人）
- 伝票作成機能の完全統合
```

## メリット

### 1. コスト効率
- 大多数のユーザーは追加実装コストなし
- 必要な機能のみ段階的に実装

### 2. データ整合性
- メインデータはShopifyで管理（信頼性）
- 拡張データのみPrismaで管理（最小リスク）

### 3. 運用性
- 基本的な住所変更はShopify Admin
- 拡張機能が不要な場合はPrismaデータを削除可能

### 4. 移行の容易さ
- 既存のShopify住所はそのまま活用
- 段階的な機能追加が可能

## 実装優先度

このアプローチなら、配送管理画面の実装を優先し、住所拡張機能は段階的に実装できます：

1. **配送管理画面の実装**（Shopify標準住所で実装）
   - 配送一覧・配送業者割り振り
   - ピッキング登録
   - 返却・金額確定処理

2. **日本業務対応の基本機能**（Phase 2相当）
   - FAX番号、ストア担当者、受注方法の管理
   - 電話番号統合（携帯廃止）
   - 支払方法のShopify連携表示
   - 伝票作成画面の基本要素対応

3. **住所拡張機能**（Phase 3相当）
   - 法人フラグ機能
   - 配送メモ常時表示
   - 動的住所上限設定
   - 複数住所管理

4. **高度な住所管理機能**（Phase 4相当）
   - 使用履歴管理
   - 住所有効期限管理
   - 組織構造管理

## 今後の改善ポイント

### 1. ユーザビリティ向上
- 住所入力時の郵便番号自動補完
- よく使用する住所の自動提案
- 配送先候補の学習機能
- ストア担当者の検索・絞り込み機能

### 2. 運用効率化
- 配送業者への住所情報自動連携
- 配送指示書の自動生成（FAX送信対応）
- 配送状況の自動更新
- 受注方法別の統計・分析機能

### 3. データ分析
- 配送先の使用頻度分析
- 地域別配送コスト分析
- 配送効率の最適化提案
- 受注方法別の売上分析

### 4. セキュリティ強化
- 住所情報のアクセス制御
- 個人情報の暗号化
- 住所変更履歴の監査ログ

## 配送先マスタと配送料金管理

### 配送先マスタの実装方針

**独立したマスタテーブルとして管理**

```prisma
model DeliveryDestination {
  id              String @id @default(cuid())
  shop            String
  destinationCode String // 配送先コード
  name            String // 配送先名
  nameKana        String // 配送先名カナ
  postalCode      String? // 郵便番号
  address         String? // 住所
  phone           String? // 電話番号
  fax             String? // FAX番号
  website         String? // ホームページ
  notes           String? // 備考
  isActive        Boolean @default(true)

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@unique([shop, destinationCode])
  @@index([shop])
  @@map("delivery_destinations")
}
```

**理由：**
- スタジオや撮影場所など、固定の配送先が多数存在
- 住所情報が詳細で、顧客住所とは性質が異なる
- 配送業務で頻繁に参照される重要なマスタデータ
- 備考情報（搬入注意事項など）が業務上重要

### 配送料金管理の実装方針

**Shopify商品として管理 + 補助テーブル**

#### A. Shopify商品として配送料金を管理
```typescript
// Shopify商品例
{
  title: "配送料金 - BASIC 23区内（品川/目黒/渋谷/港）",
  product_type: "shipping_fee",
  tags: ["shipping", "basic", "23ku", "shinagawa-meguro-shibuya-minato"],
  variants: [
    {
      title: "BASIC配送",
      price: "6000.00",
      sku: "SHIP_BASIC_23KU_01"
    }
  ],
  metafields: [
    {
      namespace: "shipping",
      key: "category",
      value: "BASIC"
    },
    {
      namespace: "shipping",
      key: "area_type",
      value: "23区内"
    },
    {
      namespace: "shipping",
      key: "area_detail",
      value: "品川／目黒／渋谷／港"
    }
  ]
}
```

#### B. 補助テーブルで料金計算ロジックを管理
```prisma
model ShippingFeeRule {
  id            String @id @default(cuid())
  shop          String
  category      String // EASE, BASIC, TRUCK, WIDE, PORTER
  type          String // 配送種類
  areaType      String // 23区内, 23区外, etc
  areaDetail    String? // 具体的なエリア
  basePrice     Decimal // 基本料金
  shopifyProductId String? // 対応するShopify商品ID
  isActive      Boolean @default(true)

  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([shop, category, type])
  @@map("shipping_fee_rules")
}
```

### 実装の利点

#### 配送先マスタを独立管理する理由：
1. **データの性質が異なる** - 顧客住所vs業務用配送先
2. **更新頻度が低い** - スタジオ情報は比較的安定
3. **検索・選択の利便性** - 配送先選択時の高速検索
4. **備考情報の重要性** - 搬入注意事項など業務固有情報

#### 配送料金をShopify商品にする理由：
1. **注文との連携** - 配送料金を注文明細に自動追加
2. **価格管理の統一** - 商品価格と同じ仕組みで管理
3. **税計算の自動化** - Shopifyの税計算機能を活用
4. **レポート機能** - 売上レポートに配送料金も含まれる

### 実装手順
1. Prismaモデル追加
2. 配送先マスタのインポートスクリプト作成
3. 配送料金のShopify商品作成スクリプト
4. 配送料金計算サービスの実装

## 実装完了記録

### 実装済み機能（2024年12月）

#### 1. Prismaモデル追加
```prisma
// 配送先マスタ
model DeliveryDestination {
  id              String @id @default(cuid())
  shop            String
  destinationCode String // 配送先コード
  name            String // 配送先名
  nameKana        String // 配送先名カナ
  postalCode      String? // 郵便番号
  address         String? // 住所
  phone           String? // 電話番号
  fax             String? // FAX番号
  website         String? // ホームページ
  notes           String? // 備考
  isActive        Boolean @default(true)

  @@unique([shop, destinationCode])
  @@map("delivery_destinations")
}

// 配送料金ルール
model ShippingFeeRule {
  id            String @id @default(cuid())
  shop          String
  category      String // EASE, BASIC, TRUCK, WIDE, PORTER
  type          String // 配送種類
  areaType      String // 23区内, 23区外, etc
  areaDetail    String? // 具体的なエリア
  basePrice     Decimal // 基本料金
  shopifyProductId String? // 対応するShopify商品ID
  isActive      Boolean @default(true)

  @@index([shop, category, type])
  @@map("shipping_fee_rules")
}
```

#### 2. マスタデータインポート実績

**配送先マスタ（1,000件以上）**
```bash
# インポートコマンド
npx tsx scripts/import-delivery-destinations.ts

# データソース
master-data-csv/other-master-data/配送先一覧_202505192113.csv

# 統計
- 総配送先数: 1,000件以上
- 電話番号あり: 約80%
- 備考情報あり: 約60%
```

**配送料金ルール（43件）**
```bash
# インポートコマンド
npx tsx scripts/import-shipping-fee-rules.ts

# データソース
master-data-csv/other-master-data/配送料金マスター.csv

# カテゴリ別件数
- BASIC: 5件（¥6,000-¥7,500）
- EASE: 2件（¥3,000-¥6,500）
- PORTER: 20件（展示会別料金 ¥4,000-¥10,000）
- TRUCK: 5件（¥33,000-¥36,000）
- WIDE: 4件（¥8,000-¥9,500）
- その他: 7件（時間外料金・キャンセル料等）
```

**担当者マスタ**
```bash
# インポートコマンド
npx tsx scripts/import-master-data.ts

# データソース
master-data-csv/other-master-data/担当者マスタ.csv

# 受注方法マスタも同時作成
- 店頭/電話/FAX/メール/WEB（デフォルト）
```

**配送業者マスタ**
```bash
# インポートコマンド
npx tsx scripts/import-shipping-carriers.ts

# データソース
master-data-csv/other-master-data/配送業者一覧_202505192115.csv
```

#### 3. サービスクラス実装

**ShippingFeeService**
```typescript
// 配送料金計算
const shippingFeeService = new ShippingFeeService(shop);
const result = await shippingFeeService.calculateShippingFee({
  category: 'BASIC',
  areaType: '23区内',
  areaDetail: '品川（目黒／渋谷／港）'
});
// => { basePrice: 6000, finalPrice: 6000, rule: {...} }
```

**CustomerAddressService**
```typescript
// 住所拡張機能
const addressService = new CustomerAddressService(shop);
const config = await addressService.getCustomerAddressConfig(customerId);
// => { isCorporate: false, maxAddresses: 3, canAddMore: true }
```

#### 4. 配送管理画面実装

**3つの主要ページ**
- `/app/delivery/schedule` - 配送一覧・配送業者割り振り
- `/app/delivery/picking` - ピッキング登録
- `/app/delivery/returns` - 返却・金額確定処理

**サイドメニュー統合**
```typescript
// AdminNavigation.tsx
{
  url: '/app/delivery',
  label: '配送管理',
  subNavigationItems: [
    { url: '/app/delivery/picking', label: 'ピッキング登録' },
    { url: '/app/delivery/returns', label: '返却・金額確定' },
    { url: '/app/delivery/schedule', label: '配送一覧' },
    { url: '/app/delivery', label: '配送概要' }
  ]
}
```

### 運用手順

#### マスタデータの更新
```bash
# 1. 新しいCSVファイルを配置
cp 新しい配送先一覧.csv master-data-csv/other-master-data/

# 2. インポートスクリプト実行
npx tsx scripts/import-delivery-destinations.ts

# 3. 結果確認
# ログで件数と統計情報を確認
```

#### 配送料金の追加・変更
```bash
# 1. 配送料金マスターCSVを更新
# 2. インポートスクリプト実行
npx tsx scripts/import-shipping-fee-rules.ts

# 3. Shopify商品作成（Phase 2で実装予定）
# 配送料金をShopify商品として自動作成
```

#### データベースマイグレーション
```bash
# 新しいモデル追加時
npx prisma migrate dev --name add-new-models

# Prismaクライアント再生成
npx prisma generate

# マスタデータ再インポート
npx tsx scripts/import-master-data.ts
npx tsx scripts/import-shipping-carriers.ts
npx tsx scripts/import-delivery-destinations.ts
npx tsx scripts/import-shipping-fee-rules.ts
```

### トラブルシューティング

#### よくある問題と解決方法

**1. CSVファイルの文字化け**
```bash
# CSVファイルをUTF-8で保存し直す
# Excel → CSV UTF-8形式で保存
```

**2. インポートエラー**
```bash
# ログを確認してエラー箇所を特定
# 無効なデータ（空の必須項目など）をCSVから除去
```

**3. Prismaクライアントエラー**
```bash
# Prismaクライアント再生成
npx prisma generate

# データベース接続確認
npx prisma db pull
```

### 今後の拡張予定

#### Phase 2: Shopify商品連携
- 配送料金のShopify商品自動作成
- 注文時の配送料金自動追加
- 税計算の自動化

#### Phase 3: 高度な機能
- 距離計算による動的料金設定
- 配送ルート最適化
- 配送状況のリアルタイム追跡

#### 必要なその他マスタデータ
1. **地域マスタ** - 23区の詳細区分
2. **時間帯マスタ** - 配送時間帯の標準化
3. **商品カテゴリマスタ** - レンタル商品の分類

### データ保守

#### 定期メンテナンス
- 月次: 配送先マスタの更新確認
- 四半期: 配送料金の見直し
- 年次: 担当者マスタの整理

#### バックアップ
```bash
# データベースバックアップ
npx prisma db push --preview-feature

# CSVファイルのバージョン管理
git add master-data-csv/
git commit -m "Update master data"
```
- FAX送信時のセキュリティ対策

### 5. 伝票機能の統合
- 伝票作成画面の完全実装
- 支払予定日の非表示設定
- 支払方法のShopify連携強化
- 明細情報の自動取得

## 動的住所管理システム

### 顧客タイプ別の住所管理

```prisma
// 顧客テーブルに法人フラグと住所上限を追加
model Customer {
  // 既存フィールド...
  isCorporate       Boolean  @default(false)  // 法人フラグ
  maxAddresses      Int      @default(3)      // 住所上限（動的設定可能、デフォルト3、最大10）
  addressCount      Int      @default(0)      // 現在の住所数（キャッシュ）
}
```

### 法人フラグによる動的UI制御

#### 個人顧客（デフォルト）
- Shopify標準住所のみ使用
- 住所上限：3件
- 法人情報フィールド：非表示
- 配送メモ：常に表示（簡易版）

#### 法人顧客（法人フラグON）
- Shopify標準 + 拡張情報
- 住所上限：10件（管理者が調整可能）
- 法人情報フィールド：表示（部署、役職、担当者）
- 配送メモ：常に表示（詳細版）

### 動的住所管理サービス

```typescript
// app/services/dynamic-address.service.ts
export class DynamicAddressService {

  async getCustomerAddressConfig(customerId: string) {
    const customer = await this.getCustomer(customerId);

    return {
      isCorporate: customer.isCorporate,
      maxAddresses: customer.maxAddresses || (customer.isCorporate ? 10 : 3),
      currentAddressCount: await this.getAddressCount(customerId),
      canAddMore: await this.canAddMoreAddresses(customerId),
      showCorporateFields: customer.isCorporate
    };
  }

  async toggleCorporateStatus(customerId: string, isCorporate: boolean) {
    const newMaxAddresses = isCorporate ? 10 : 3;

    await prisma.customer.update({
      where: { id: customerId },
      data: {
        isCorporate,
        maxAddresses: newMaxAddresses
      }
    });

    // 法人→個人に変更時、住所数が上限を超える場合の処理
    if (!isCorporate) {
      await this.handleAddressLimitReduction(customerId, newMaxAddresses);
    }
  }

  private async handleAddressLimitReduction(customerId: string, newLimit: number) {
    const addresses = await this.getCustomerAddresses(customerId);

    if (addresses.length > newLimit) {
      // 使用頻度の低い住所を無効化または削除の提案
      const excessAddresses = addresses
        .sort((a, b) => (a.lastUsedAt || 0) - (b.lastUsedAt || 0))
        .slice(newLimit);

      // 管理者に確認を求める処理
      return {
        needsConfirmation: true,
        excessAddresses,
        message: `住所上限が${newLimit}件に変更されます。${excessAddresses.length}件の住所が上限を超えています。`
      };
    }
  }
}
```

### UI実装：段階的表示

```typescript
// app/components/AddressManager.tsx
export function AddressManager({ customerId }: { customerId: string }) {
  const [config, setConfig] = useState(null);
  const [addresses, setAddresses] = useState([]);

  const handleCorporateToggle = async (isCorporate: boolean) => {
    const result = await toggleCorporateStatus(customerId, isCorporate);

    if (result.needsConfirmation) {
      // 住所削除の確認ダイアログを表示
      setShowConfirmDialog(result);
    } else {
      setConfig(prev => ({ ...prev, isCorporate, showCorporateFields: isCorporate }));
    }
  };

  return (
    <div className="address-manager">
      {/* 法人フラグ切り替え */}
      <div className="corporate-toggle">
        <label>
          <input
            type="checkbox"
            checked={config?.isCorporate}
            onChange={(e) => handleCorporateToggle(e.target.checked)}
          />
          法人登録（部署・担当者情報を管理）
        </label>
        {config?.isCorporate && (
          <p className="help-text">
            法人登録により、部署名・担当者・役職などの詳細情報を管理できます。
            住所上限も{config.maxAddresses}件まで拡張されます。
          </p>
        )}
      </div>

      {/* 住所一覧 */}
      {addresses.map((address, index) => (
        <AddressCard
          key={address.id}
          address={address}
          index={index + 1}
          showCorporateFields={config?.showCorporateFields}
          showMemoField={true} // 常に表示
          onUpdate={handleAddressUpdate}
          onDelete={handleAddressDelete}
        />
      ))}

      {/* 住所追加ボタン */}
      {config?.canAddMore && (
        <div className="add-address-section">
          <button
            onClick={handleAddAddress}
            className="btn btn-outline"
          >
            + 住所を追加 ({config.currentAddressCount}/{config.maxAddresses})
          </button>
          <p className="help-text">
            {config.isCorporate
              ? "営業所、倉庫、イベント会場など複数の配送先を登録できます。"
              : "自宅、職場など最大3件まで登録できます。法人登録で上限を拡張できます。"
            }
          </p>
        </div>
      )}

      {/* 上限到達時のメッセージ */}
      {!config?.canAddMore && (
        <div className="address-limit-message">
          <p>住所の上限（{config.maxAddresses}件）に達しています。</p>
          {!config.isCorporate && (
            <p>
              <button onClick={() => handleCorporateToggle(true)}>
                法人登録
              </button>
              で上限を10件まで拡張できます。
            </p>
          )}
        </div>
      )}
    </div>
  );
}
```

### 配送メモの常時表示

```typescript
// app/components/AddressCard.tsx
export function AddressCard({
  address,
  index,
  showCorporateFields,
  showMemoField,
  onUpdate,
  onDelete
}) {
  return (
    <div className="address-card">
      <div className="address-header">
        <h4>配送先 {index}</h4>
        <div className="address-actions">
          <button onClick={() => setIsEditing(true)}>編集</button>
          <button onClick={() => onDelete(address.id)}>削除</button>
        </div>
      </div>

      {/* 基本住所情報（Shopify標準） */}
      <div className="basic-address">
        <h5>{address.company || `${address.first_name} ${address.last_name}`}</h5>
        <p>{address.address1}</p>
        {address.address2 && <p>{address.address2}</p>}
        <p>{address.city}, {address.province} {address.zip}</p>
        <p>📞 {address.phone}</p>
      </div>

      {/* 法人情報（法人フラグONの場合のみ） */}
      {showCorporateFields && (
        <div className="corporate-fields">
          <div className="field-group">
            <label>部署名</label>
            <input
              placeholder="例：営業部、制作部"
              value={address.extension?.department || ''}
              onChange={(e) => updateExtension('department', e.target.value)}
            />
          </div>
          <div className="field-group">
            <label>役職</label>
            <input
              placeholder="例：部長、主任"
              value={address.extension?.position || ''}
              onChange={(e) => updateExtension('position', e.target.value)}
            />
          </div>
          <div className="field-group">
            <label>担当者名</label>
            <input
              placeholder="例：田中太郎"
              value={address.extension?.contactPerson || ''}
              onChange={(e) => updateExtension('contactPerson', e.target.value)}
            />
          </div>
        </div>
      )}

      {/* 配送メモ（常に表示） */}
      {showMemoField && (
        <div className="delivery-memo">
          <label>配送メモ</label>
          <textarea
            placeholder={showCorporateFields
              ? "配送時の注意事項、アクセス方法、受付での手続きなど詳細な情報"
              : "配送時の注意事項、アクセス方法など"
            }
            value={address.extension?.deliveryMemo || ''}
            onChange={(e) => updateExtension('deliveryMemo', e.target.value)}
            rows={showCorporateFields ? 4 : 2}
          />
          <p className="help-text">
            配送業者への指示や、建物への入り方、受付での手続きなどを記載してください。
          </p>
        </div>
      )}

      {/* 使用履歴表示 */}
      {address.extension?.lastUsedAt && (
        <div className="usage-info">
          <small>
            最終使用: {formatDate(address.extension.lastUsedAt)}
            (使用回数: {address.extension.usageCount}回)
          </small>
        </div>
      )}
    </div>
  );
}
```