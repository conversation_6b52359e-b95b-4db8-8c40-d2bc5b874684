{"timestamp": "2025-05-19T08:15:51.095Z", "duration": 35.061, "totalTests": 10, "successCount": 10, "failureCount": 0, "tests": [{"testName": "年末年始の予約テスト", "success": true, "timestamp": "2025-05-19T08:15:56.819Z", "message": "年末年始の予約が正常に作成されました", "error": "", "duration": 4590, "details": {"bookingId": "418724de-f114-422a-a7a3-0ae6eddc3500", "period": "年末年始", "startDate": "2025-12-29", "endDate": "2026-01-03"}}, {"testName": "予約期間の最小値テスト", "success": true, "timestamp": "2025-05-19T08:15:57.878Z", "message": "最小予約期間（1日）の予約が正常に作成されました", "error": "", "duration": 1059, "details": {"bookingId": "20478229-4204-4402-a00e-dfc595166056", "durationDays": 1}}, {"testName": "予約期間の最大値テスト", "success": true, "timestamp": "2025-05-19T08:16:12.220Z", "message": "最大予約期間（30日）の予約が正常に作成されました", "error": "", "duration": 14342, "details": {"bookingId": "99ddc6b6-8fb0-445e-941c-d213e81dc1bc", "durationDays": 30}}, {"testName": "過去の日付での予約テスト", "success": true, "timestamp": "2025-05-19T08:16:15.660Z", "message": "過去の日付での予約は正しくエラーになりました", "error": "過去の日付での予約がエラーにならずに成功してしまいました", "duration": 2369, "details": {"expectedError": true}}, {"testName": "終了日が開始日より前の予約テスト", "success": true, "timestamp": "2025-05-19T08:16:16.180Z", "message": "終了日が開始日より前の予約は正しくエラーになりました", "error": "終了日が開始日より前の予約がエラーにならずに成功してしまいました", "duration": 520, "details": {"expectedError": true}}, {"testName": "無効なメールアドレスでの予約テスト", "success": true, "timestamp": "2025-05-19T08:16:16.403Z", "message": "無効なメールアドレスでの予約は正しくエラーになりました", "error": "", "duration": 223, "details": {"expectedError": true, "invalidEmail": "invalid-email"}}, {"testName": "在庫をゼロにするテスト", "success": true, "timestamp": "2025-05-19T08:16:19.468Z", "message": "在庫をゼロにすることに成功しました", "error": "", "duration": 3065, "details": {"bookingId": "2c8c8265-bdb1-4c67-8588-8e9df1d88e41"}}, {"testName": "在庫がゼロの状態で予約を試みるテスト", "success": true, "timestamp": "2025-05-19T08:16:19.690Z", "message": "在庫がゼロの状態での予約は正しくエラーになりました", "error": "", "duration": 222, "details": {"expectedError": true}}, {"testName": "予約をキャンセルして在庫を回復するテスト", "success": true, "timestamp": "2025-05-19T08:16:23.396Z", "message": "予約をキャンセルして在庫を回復することに成功しました", "error": "", "duration": 3706, "details": {"bookingId": "2c8c8265-bdb1-4c67-8588-8e9df1d88e41"}}, {"testName": "在庫が回復した状態で予約を試みるテスト", "success": true, "timestamp": "2025-05-19T08:16:25.845Z", "message": "在庫が回復した状態での予約が正常に作成されました", "error": "", "duration": 2449, "details": {"bookingId": "3f4815bd-810f-4417-a983-dcb5b945c8ad"}}], "environment": {"nodeVersion": "v20.18.1", "os": "darwin arm64"}, "performance": {"averageTestDuration": 3254.5, "slowestTest": {"name": "予約期間の最大値テスト", "duration": 14342}, "fastestTest": {"name": "在庫がゼロの状態で予約を試みるテスト", "duration": 222}}}