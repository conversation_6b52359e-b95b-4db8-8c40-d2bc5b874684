import { ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { PrismaClient } from "@prisma/client";
import { logger } from "../services/logger.service";

const prisma = new PrismaClient();

export const action = async ({ request }: ActionFunctionArgs) => {
  const { topic, shop, webhookId } = await authenticate.webhook(request);

  if (topic !== "ORDERS_UPDATED") {
    return new Response(`Webhook topic ${topic} not supported`, { status: 400 });
  }

  try {
    const payload = await request.json();
    logger.info(`Received order update webhook for shop ${shop}`, { 
      webhookId, 
      orderId: payload.id,
      orderName: payload.name
    });

    // 注文データをPrismaに更新
    await prisma.order.upsert({
      where: { 
        id: payload.id.toString() 
      },
      update: {
        name: payload.name,
        email: payload.email,
        customerId: payload.customer?.id?.toString(),
        totalPrice: payload.total_price,
        subtotalPrice: payload.subtotal_price,
        totalTax: payload.total_tax,
        currency: payload.currency,
        financialStatus: payload.financial_status,
        fulfillmentStatus: payload.fulfillment_status,
        shop,
        shopifyData: JSON.stringify(payload),
        updatedAt: new Date()
      },
      create: {
        id: payload.id.toString(),
        name: payload.name,
        email: payload.email,
        customerId: payload.customer?.id?.toString(),
        totalPrice: payload.total_price,
        subtotalPrice: payload.subtotal_price,
        totalTax: payload.total_tax,
        currency: payload.currency,
        financialStatus: payload.financial_status,
        fulfillmentStatus: payload.fulfillment_status,
        shop,
        shopifyData: JSON.stringify(payload),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    // 関連する予約データを更新
    const bookings = await prisma.booking.findMany({
      where: {
        orderId: payload.id.toString()
      }
    });

    if (bookings.length > 0) {
      // 注文のステータスに基づいて予約ステータスを更新
      let bookingStatus;
      let paymentStatus;

      if (payload.cancelled_at) {
        bookingStatus = "CANCELLED";
        paymentStatus = "REFUNDED";
      } else if (payload.financial_status === "paid") {
        bookingStatus = "CONFIRMED";
        paymentStatus = "COMPLETED";
      } else if (payload.financial_status === "partially_paid") {
        bookingStatus = "PROVISIONAL";
        paymentStatus = "PARTIALLY_PAID";
      }

      if (bookingStatus) {
        for (const booking of bookings) {
          await prisma.booking.update({
            where: {
              id: booking.id
            },
            data: {
              status: bookingStatus,
              paymentStatus,
              updatedAt: new Date()
            }
          });

          logger.info(`Booking ${booking.id} status updated to ${bookingStatus}`, { 
            webhookId, 
            orderId: payload.id,
            bookingId: booking.id
          });
        }
      }
    }

    logger.info(`Order ${payload.id} updated in database`, { 
      webhookId, 
      orderId: payload.id 
    });

    return new Response("Order updated in database", { status: 200 });
  } catch (error) {
    logger.error(`Error processing order update webhook: ${error}`, { 
      webhookId, 
      error: error instanceof Error ? error.message : String(error) 
    });
    return new Response("Error processing webhook", { status: 500 });
  }
};
