import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  BlockStack,
  IndexTable,
  Link,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { prisma } from "../db.server";
import { authenticate } from "../shopify.server";
import { format } from "date-fns";
import { ja } from "date-fns/locale";

interface BookingWithProduct {
  id: string;
  bookingId: string;
  status: string;
  startDate: Date;
  endDate: Date;
  product: {
    id: string;
    title: string;
    sku?: string;
  };
}

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);

  const bookings = await prisma.booking.findMany({
    include: {
      product: {
        select: {
          id: true,
          title: true,
          sku: true,
        },
      },
    },
    orderBy: {
      startDate: "asc",
    },
  });

  return json(bookings);
}

export default function BookingList() {
  const bookings = useLoaderData<typeof loader>();

  return (
    <Page title="予約一覧" backAction={{ content: "戻る", url: "/app" }}>
      <TitleBar title="予約一覧" />

      <Layout>
        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">
                  登録済み予約: {bookings.length}件
                </Text>

                <IndexTable
                  resourceName={{ singular: "予約", plural: "予約" }}
                  itemCount={bookings.length}
                  headings={[
                    { title: "予約ID" },
                    { title: "商品名" },
                    { title: "SKU" },
                    { title: "開始日" },
                    { title: "終了日" },
                    { title: "ステータス" },
                  ]}
                >
                  {bookings.map((booking) => (
                    <IndexTable.Row
                      id={booking.id}
                      key={booking.id}
                      position={bookings.indexOf(booking)}
                    >
                      <IndexTable.Cell>
                        <Link
                          url={`/app/bookings/${booking.id}`}
                          removeUnderline
                        >
                          {booking.bookingId}
                        </Link>
                      </IndexTable.Cell>
                      <IndexTable.Cell>{booking.product.title}</IndexTable.Cell>
                      <IndexTable.Cell>
                        {booking.product.sku || "未設定"}
                      </IndexTable.Cell>
                      <IndexTable.Cell>
                        {format(new Date(booking.startDate), "yyyy年MM月dd日", {
                          locale: ja,
                        })}
                      </IndexTable.Cell>
                      <IndexTable.Cell>
                        {format(new Date(booking.endDate), "yyyy年MM月dd日", {
                          locale: ja,
                        })}
                      </IndexTable.Cell>
                      <IndexTable.Cell>
                        <Text
                          variant="bodyMd"
                          as="span"
                          tone={
                            booking.status === "confirmed"
                              ? "success"
                              : booking.status === "cancelled"
                                ? "critical"
                                : "subdued"
                          }
                        >
                          {booking.status === "confirmed"
                            ? "確定"
                            : booking.status === "cancelled"
                              ? "キャンセル"
                              : "仮予約"}
                        </Text>
                      </IndexTable.Cell>
                    </IndexTable.Row>
                  ))}
                </IndexTable>
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
