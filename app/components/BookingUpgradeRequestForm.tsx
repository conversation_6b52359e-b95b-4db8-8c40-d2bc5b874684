/**
 * 仮予約から本予約への変更リクエストフォームコンポーネント
 */

import { useState } from 'react';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';

interface BookingUpgradeRequestFormProps {
  booking: {
    id: string;
    bookingId: string;
    productId: string;
    startDate: string;
    endDate: string;
    customerName?: string;
    customerEmail?: string;
    bookingType: string;
    status: string;
  };
  product: {
    id: string;
    title: string;
    price: number;
  };
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
}

export default function BookingUpgradeRequestForm({
  booking,
  product,
  onSuccess,
  onError
}: BookingUpgradeRequestFormProps) {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // 仮予約から本予約への変更リクエストを送信
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name || !email) {
      setError('お名前とメールアドレスは必須です');
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      const response = await fetch('/api/booking/provisional-upgrade-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookingId: booking.id,
          requesterName: name,
          requesterEmail: email,
          requesterPhone: phone,
          requesterNotes: notes
        }),
      });
      
      const result = await response.json();
      
      if (result.success) {
        setSuccess('仮予約から本予約への変更リクエストを送信しました。スタッフが確認後、ご連絡いたします。');
        setName('');
        setEmail('');
        setPhone('');
        setNotes('');
        
        if (onSuccess) {
          onSuccess(result);
        }
      } else {
        setError(`エラー: ${result.error}`);
        
        if (onError) {
          onError(result);
        }
      }
    } catch (err) {
      console.error('リクエスト送信エラー:', err);
      setError('リクエスト送信中にエラーが発生しました。後でもう一度お試しください。');
      
      if (onError) {
        onError(err);
      }
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // 仮予約でない場合は表示しない
  if (booking.bookingType !== 'PROVISIONAL') {
    return null;
  }
  
  return (
    <div className="booking-upgrade-request-form">
      <h3 className="form-title">この商品を本予約したい</h3>
      
      <div className="booking-info">
        <p>
          <strong>商品名:</strong> {product.title}
        </p>
        <p>
          <strong>予約期間:</strong> {format(new Date(booking.startDate), 'yyyy年MM月dd日', { locale: ja })} 〜 {format(new Date(booking.endDate), 'yyyy年MM月dd日', { locale: ja })}
        </p>
        <p>
          <strong>予約状態:</strong> 仮予約中
        </p>
        <p className="note">
          この商品は現在仮予約中です。本予約をご希望の場合は、以下のフォームからリクエストを送信してください。
          スタッフが確認後、ご連絡いたします。
        </p>
      </div>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      {success && (
        <div className="success-message">
          {success}
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="name">お名前 <span className="required">*</span></label>
          <input
            type="text"
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            disabled={isSubmitting}
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="email">メールアドレス <span className="required">*</span></label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            disabled={isSubmitting}
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="phone">電話番号</label>
          <input
            type="tel"
            id="phone"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            disabled={isSubmitting}
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="notes">備考</label>
          <textarea
            id="notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            rows={3}
            disabled={isSubmitting}
          />
        </div>
        
        <div className="form-actions">
          <button
            type="submit"
            className="submit-button"
            disabled={isSubmitting}
          >
            {isSubmitting ? '送信中...' : '本予約リクエストを送信'}
          </button>
        </div>
      </form>
      
      <style jsx>{`
        .booking-upgrade-request-form {
          margin: 2rem 0;
          padding: 1.5rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          background-color: #f9f9f9;
        }
        
        .form-title {
          margin-top: 0;
          margin-bottom: 1rem;
          font-size: 1.25rem;
          color: #333;
        }
        
        .booking-info {
          margin-bottom: 1.5rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid #eee;
        }
        
        .booking-info p {
          margin: 0.5rem 0;
        }
        
        .note {
          font-size: 0.9rem;
          color: #666;
          margin-top: 1rem;
        }
        
        .error-message {
          padding: 0.75rem;
          margin-bottom: 1rem;
          background-color: #fff0f0;
          border: 1px solid #ffcccc;
          border-radius: 4px;
          color: #cc0000;
        }
        
        .success-message {
          padding: 0.75rem;
          margin-bottom: 1rem;
          background-color: #f0fff0;
          border: 1px solid #ccffcc;
          border-radius: 4px;
          color: #006600;
        }
        
        .form-group {
          margin-bottom: 1rem;
        }
        
        label {
          display: block;
          margin-bottom: 0.5rem;
          font-weight: 500;
        }
        
        .required {
          color: #cc0000;
        }
        
        input, textarea {
          width: 100%;
          padding: 0.75rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 1rem;
        }
        
        .form-actions {
          margin-top: 1.5rem;
        }
        
        .submit-button {
          padding: 0.75rem 1.5rem;
          background-color: #008060;
          color: white;
          border: none;
          border-radius: 4px;
          font-size: 1rem;
          cursor: pointer;
          transition: background-color 0.2s;
        }
        
        .submit-button:hover {
          background-color: #006e52;
        }
        
        .submit-button:disabled {
          background-color: #cccccc;
          cursor: not-allowed;
        }
      `}</style>
    </div>
  );
}
