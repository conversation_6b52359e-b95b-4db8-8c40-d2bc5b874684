import { LoaderFunctionArgs, json, ActionFunctionArgs } from '@remix-run/node';
import { useLoaderData, useActionData, Form } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import { Page, Card, Text, Layout, TextField, Button, Banner } from '@shopify/polaris';
import { useState } from 'react';

export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  
  return json({
    shop,
    message: '予約状況一括照会フォームテスト'
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const productCode = formData.get('productCode');
  const category = formData.get('category');
  
  return json({
    success: true,
    formData: {
      productCode,
      category
    }
  });
}

export default function BookingAggregateForm() {
  const { shop, message } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const [productCode, setProductCode] = useState('');
  const [category, setCategory] = useState('');
  
  return (
    <Page
      title="予約状況一括照会フォームテスト"
      subtitle="テスト用の簡易フォーム"
      backAction={{ content: 'ホーム', url: '/app' }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Card.Section>
              <Text as="h2" variant="headingMd">ショップ: {shop}</Text>
              <Text as="p">{message}</Text>
              
              {actionData?.success && (
                <Banner title="送信成功" tone="success">
                  <p>フォームが正常に送信されました。</p>
                  <p>商品コード: {actionData.formData.productCode}</p>
                  <p>カテゴリ: {actionData.formData.category}</p>
                </Banner>
              )}
              
              <Form method="post">
                <div style={{ marginTop: '20px' }}>
                  <TextField
                    label="商品コード"
                    name="productCode"
                    value={productCode}
                    onChange={(value) => {
                      console.log('商品コード変更:', value);
                      setProductCode(value);
                    }}
                    autoComplete="off"
                  />
                </div>
                
                <div style={{ marginTop: '20px' }}>
                  <TextField
                    label="カテゴリ"
                    name="category"
                    value={category}
                    onChange={(value) => {
                      console.log('カテゴリ変更:', value);
                      setCategory(value);
                    }}
                    autoComplete="off"
                  />
                </div>
                
                <div style={{ marginTop: '20px' }}>
                  <Button submit primary>送信</Button>
                </div>
              </Form>
            </Card.Section>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
