/**
 * Shopifyセッション情報のテストスクリプト
 *
 * このスクリプトは、Shopify認証からセッション情報を取得し、
 * 正しく設定されているかどうかをテストします。
 *
 * 実行方法: npx tsx scripts/test-session-info.ts
 */

import dotenv from 'dotenv';
import { prisma } from '../app/db.server';

// 環境変数の読み込み
dotenv.config();

/**
 * セッション情報をテストする関数
 */
async function testSessionInfo() {
  console.log('Shopifyセッション情報のテストを開始します...');

  try {
    // オフラインセッションを取得
    const shopName = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';
    const offlineSessionId = `offline_${shopName}`;
    console.log(`オフラインセッションID: ${offlineSessionId}`);

    // データベースからセッションを取得
    const session = await prisma.session.findUnique({
      where: { id: offlineSessionId }
    });

    if (session) {
      console.log('セッションが見つかりました:');
      console.log(`\nセッション情報:`);
      console.log(`- ID: ${session.id}`);
      console.log(`- ショップ: ${session.shop}`);
      console.log(`- アクセストークン: ${session.accessToken ? '設定されています' : '設定されていません'}`);
      console.log(`- オンライン: ${session.is_online ? 'はい' : 'いいえ'}`);
      console.log(`- 有効期限: ${session.expires ? new Date(session.expires).toLocaleString() : 'なし'}`);

      // ENV変数に設定する形式でセッション情報を表示
      const envSessionInfo = {
        shop: session.shop,
        accessToken: session.accessToken,
      };
      console.log('\nENV変数に設定する形式:');
      console.log(JSON.stringify(envSessionInfo, null, 2));

      // window.ENV.shopifySessionに設定するコードの例を表示
      console.log('\nクライアントサイドでセッション情報を設定するコード例:');
      console.log(`if (typeof window !== 'undefined') {
  window.ENV = window.ENV || {};
  window.ENV.shopifySession = ${JSON.stringify({
    shop: session.shop,
    accessToken: session.accessToken,
  }, null, 2)};
}`);

      return true;
    } else {
      console.log('セッションが見つかりませんでした。');
      console.log('アプリがShopifyストアにインストールされていることを確認してください。');
      return false;
    }
  } catch (error) {
    console.error('セッション情報のテスト中にエラーが発生しました:', error);
    if (error instanceof Error) {
      console.error('エラーメッセージ:', error.message);
      console.error('スタックトレース:', error.stack);
    }
    return false;
  }
}

/**
 * ShopifyCustomerDisplayコンポーネントのテスト関数
 */
function testShopifyCustomerDisplay() {
  console.log('\n----- ShopifyCustomerDisplayコンポーネントのテスト -----');
  console.log('ShopifyCustomerDisplayコンポーネントは以下の条件でセッション情報を取得します:');
  console.log('1. セッションが直接propsとして渡された場合はそれを使用');
  console.log('2. グローバルなセッション情報（window.ENV.shopifySession）が存在する場合はそれを使用');
  console.log('3. どちらも存在しない場合はフォールバック表示を使用');

  console.log('\nコンポーネントの動作確認方法:');
  console.log('1. アプリケーションにアクセスして予約一覧画面を開く');
  console.log('2. ブラウザのコンソールで以下を実行して、セッション情報が設定されているか確認:');
  console.log('   console.log(window.ENV.shopifySession)');
  console.log('3. 顧客情報が正しく表示されていることを確認');
  console.log('4. ブラウザのコンソールでエラーが表示されていないことを確認');
}

// メイン処理
async function main() {
  const sessionTestResult = await testSessionInfo();

  if (sessionTestResult) {
    console.log('\nセッション情報のテストが成功しました！');
    testShopifyCustomerDisplay();
  } else {
    console.log('\nセッション情報のテストが失敗しました。');
  }
}

// スクリプトを実行
main();
