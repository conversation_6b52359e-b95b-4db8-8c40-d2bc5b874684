# テストデータ再構築計画

## 概要

バックエンド管理画面の開発において、テストデータの問題（デモ用データの表示や不足しているShopifyデータなど）に対処するため、一度すべてのデータをクリーンアップし、テストに必要なデータを再構築する計画です。

## 目次

1. [データのクリーンアップ](#1-データのクリーンアップ)
2. [新しいテストデータの作成](#2-新しいテストデータの作成)
3. [テストの実行と検証](#3-テストの実行と検証)
4. [実行手順](#4-実行手順)
5. [注意点と補足](#5-注意点と補足)

## 1. データのクリーンアップ

### 1.1 Prismaデータベースのクリーンアップ

Prismaデータベースの全テーブルをクリーンアップします。

```bash
node scripts/reset-database.js
```

このスクリプトは以下のテーブルを順番にクリアします：
- DeliverySchedule
- Booking
- InventoryCalendar
- RentalAvailability
- BlackoutDate
- AvailableDates
- Maintenance
- Product
- Order
- Invoice
- ShippingCarrier
- ShippingFee
- ShippingDestination
- Staff
- SyncLog
- AppSettings
- test_invoices
- booking_status_changes

### 1.2 Shopifyのテストデータの削除

#### 手動削除方法
1. Shopify管理画面にログイン
2. 商品 > すべての商品から、テスト用商品を選択して削除
3. 顧客 > すべての顧客から、テスト用顧客を選択して削除
4. 注文 > すべての注文から、テスト用注文を選択して削除（必要に応じて）

#### APIを使用した削除方法
```bash
node scripts/test-products-setup.js
```
このスクリプトは、タイトルに「テスト」を含む商品を検索して削除します。

## 2. 新しいテストデータの作成

### 2.1 Shopifyでの商品登録

#### 商品の種類と構成
- 基本的な商品（バリエーションなし）
- バリエーションを持つ商品（サイズ、色など）
- 複数の在庫場所（NY/PR）を持つ商品
- 異なるステータス（available/maintenance/damaged/unavailable）の商品

#### メタフィールド設定
- 基本情報（basic_info）: 商品コード、詳細コード、ステータス、在庫場所など
- 料金設定（pricing）: 基本料金、割引ルールなど
- 在庫情報（inventory_items）: 在庫アイテムの状態、場所など

#### 商品作成スクリプト
```bash
# 基本的なテスト商品の作成
node scripts/test-products-setup.js

# 追加のサンプル商品作成（iziz.co.jpを参考にした商品）
node scripts/create-iziz-sample-products.js
```

### 2.2 顧客データの作成

#### 顧客情報の種類
- 日本語名（姓名の間にスペースあり/なし）
- メールアドレス、電話番号、住所など
- 過去の注文履歴を持つ顧客

#### 顧客データ作成スクリプト
```bash
node scripts/create-test-data.ts
```

### 2.3 予約データの作成

#### 予約の種類
- 確定予約（CONFIRMED）
- 仮予約（PROVISIONAL）
- キャンセル済み予約（CANCELLED）
- 完了済み予約（COMPLETED）
- 過去の予約と将来の予約

#### 予約データ作成スクリプト
```bash
# テスト予約データの作成
node scripts/create-test-data.ts

# 特定の商品に対する予約データの作成
node scripts/initialize-booking-data.js [商品ID]
```

### 2.4 Prismaデータベースへの同期

Shopifyの商品データをPrismaデータベースに同期します。

```bash
npx tsx scripts/test-shopify-prisma-integration.ts
```

## 3. テストの実行と検証

### 3.1 商品同期のテスト

商品データがShopifyとPrisma間で正しく同期されることを確認します。

```bash
npx tsx scripts/test-shopify-prisma-integration.ts
```

### 3.2 予約管理のテスト

予約の作成、編集、キャンセルなどの機能をテストします。

```bash
npx tsx scripts/test-booking-system.js
```

### 3.3 在庫管理のテスト

予約に応じて在庫状態が正しく更新されることを確認します。

```bash
node scripts/test-inventory-management.js
```

### 3.4 管理画面の表示確認

1. アプリの管理画面にアクセス
2. 商品管理、予約管理、顧客管理の各画面でデータが正しく表示されることを確認
3. 検索、フィルタリング機能をテスト

## 4. 実行手順

### 4.1 データクリーンアップ

```bash
# 1. Shopifyデータの削除（手動または以下のスクリプトを使用）
node scripts/test-products-setup.js

# 2. Prismaデータベースのクリーンアップ
node scripts/reset-database.js
```

### 4.2 テストデータ作成

```bash
# 1. 基本的なテスト商品の作成
node scripts/test-products-setup.js

# 2. 追加のサンプル商品作成
node scripts/create-iziz-sample-products.js

# 3. テスト予約・顧客データの作成
node scripts/create-test-data.ts
```

### 4.3 テストと検証

```bash
# 1. 商品同期テスト
npx tsx scripts/test-shopify-prisma-integration.ts

# 2. 予約管理テスト
npx tsx scripts/test-booking-system.js

# 3. 管理画面での確認
# ブラウザでアプリにアクセスし、各画面を確認
```

## 5. 注意点と補足

- テストデータ作成時は、実際の運用環境に影響を与えないよう注意する
- テスト完了後は、必要に応じてテストデータをクリーンアップする
- 各テストスクリプトのオプションや引数を確認し、適切に使用する
- エラーが発生した場合は、ログを確認して原因を特定する
- 商品バリエーション、メタフィールド、予約状態など、様々なケースをカバーするテストデータを作成する
- 日本語の顧客名（スペースあり/なしの両方）でのテストを行う
- 在庫場所（NY/PR）の違いによる動作確認を行う
- 予約の日付範囲、重複チェックなどの機能を検証する

## 6. テスト項目チェックリスト

### 商品関連
- [ ] 商品一覧表示
- [ ] 商品詳細表示
- [ ] 商品検索・フィルタリング
- [ ] 商品ステータス変更
- [ ] 商品メタフィールド表示
- [ ] 商品バリエーション表示
- [ ] 在庫場所による表示切替

### 予約関連
- [ ] 予約一覧表示
- [ ] 予約詳細表示
- [ ] 予約検索・フィルタリング
- [ ] 予約ステータス変更
- [ ] 予約カレンダー表示
- [ ] 予約の日付範囲検索
- [ ] 予約の顧客情報表示

### 顧客関連
- [ ] 顧客一覧表示
- [ ] 顧客詳細表示
- [ ] 顧客検索（日本語名対応）
- [ ] 顧客の予約履歴表示
- [ ] Shopify顧客情報との連携確認
