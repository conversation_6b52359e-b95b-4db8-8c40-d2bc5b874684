import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2025-01';

// GraphQLクライアントの設定
const client = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品情報
const PRODUCT_ID = "gid://shopify/Product/8982388146344";
const BASE_SKU = "10301009-001";
const PRODUCT_DATA = {
  productCode: "10301009",
  detailCode: "001",
  categoryName: "椅子",
  kana: "ハーヴェイチェア",
  basePrice: 700,
  extraDayRate: 350,
  quantity: 1,
  width: 450,
  depth: 580,
  height: 940,
  purchasePrice: 30000
};

// レンタル期間のバリアント定義（最初の1日レンタルは既存のデフォルトバリアントを使用）
const ADDITIONAL_VARIANTS = [
  { title: "2日レンタル", suffix: "2D", days: 2, price: 1050 },
  { title: "3日レンタル", suffix: "3D", days: 3, price: 1400 },
  { title: "4日レンタル", suffix: "4D", days: 4, price: 1750 },
  { title: "5日レンタル", suffix: "5D", days: 5, price: 2100 },
  { title: "6日レンタル", suffix: "6D", days: 6, price: 2450 },
  { title: "7日レンタル", suffix: "7D", days: 7, price: 2800 },
  { title: "8日以上レンタル", suffix: "8D+", days: 8, price: 2800 },
  { title: "仮予約", suffix: "PROV", days: 0, price: 70 }
];

// ロケーションを取得
async function getLocations() {
  const query = gql`
    query GetLocations {
      locations(first: 10) {
        edges {
          node {
            id
            name
            isActive
          }
        }
      }
    }
  `;

  const data = await client.request(query);
  return data.locations.edges.map(edge => edge.node).filter(loc => loc.isActive);
}

async function addVariantsToProduct() {
  try {
    console.log('=== 商品バリアント追加開始 ===');
    console.log(`商品ID: ${PRODUCT_ID}\n`);

    // 1. ロケーションを取得
    console.log('1. ロケーション情報を取得中...');
    const locations = await getLocations();
    const nyLocation = locations.find(loc => loc.name === 'NY');
    
    if (!nyLocation) {
      throw new Error('NYロケーションが見つかりません');
    }
    console.log(`✓ NYロケーション: ${nyLocation.id}`);

    // 2. 既存のバリアントを更新（デフォルトバリアントを1日レンタルとして使用）
    console.log('\n2. 既存のバリアントを更新中...');
    const getVariantQuery = gql`
      query getVariant($id: ID!) {
        product(id: $id) {
          variants(first: 1) {
            edges {
              node {
                id
                title
                inventoryItem {
                  id
                }
              }
            }
          }
        }
      }
    `;

    const variantResult = await client.request(getVariantQuery, { id: PRODUCT_ID });
    const defaultVariant = variantResult.product.variants.edges[0]?.node;

    if (!defaultVariant) {
      throw new Error('デフォルトバリアントが見つかりません');
    }

    // バリアントを個別に作成（一括作成ではなく）
    console.log('\n3. 追加バリアントを作成中...');
    const variantIds = { "1D": defaultVariant.id };

    for (const variant of ADDITIONAL_VARIANTS) {
      const createVariantMutation = gql`
        mutation createVariant($productId: ID!, $input: ProductVariantInput!) {
          productVariantCreate(productId: $productId, input: $input) {
            productVariant {
              id
              title
              price
              inventoryItem {
                id
                sku
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variantInput = {
        price: variant.price.toString(),
        inventoryPolicy: "DENY"
      };

      try {
        const result = await client.request(createVariantMutation, {
          productId: PRODUCT_ID,
          input: variantInput
        });

        if (result.productVariantCreate.userErrors.length > 0) {
          console.error(`エラー (${variant.title}):`, result.productVariantCreate.userErrors);
        } else {
          const createdVariant = result.productVariantCreate.productVariant;
          variantIds[variant.suffix] = createdVariant.id;
          console.log(`✓ ${variant.title}: ¥${variant.price} 作成完了`);

          // SKUを更新
          const updateSkuMutation = gql`
            mutation updateInventoryItem($id: ID!, $input: InventoryItemInput!) {
              inventoryItemUpdate(id: $id, input: $input) {
                inventoryItem {
                  id
                  sku
                  tracked
                }
                userErrors {
                  field
                  message
                }
              }
            }
          `;

          await client.request(updateSkuMutation, {
            id: createdVariant.inventoryItem.id,
            input: {
              sku: `${BASE_SKU}-${variant.suffix}`,
              tracked: true
            }
          });

          // 在庫を設定
          const setQuantityMutation = gql`
            mutation setQuantity($input: InventorySetQuantitiesInput!) {
              inventorySetQuantities(input: $input) {
                userErrors {
                  field
                  message
                }
              }
            }
          `;

          await client.request(setQuantityMutation, {
            input: {
              reason: "correction",
              name: "available",
              quantities: [{
                inventoryItemId: createdVariant.inventoryItem.id,
                locationId: nyLocation.id,
                quantity: PRODUCT_DATA.quantity
              }]
            }
          });
        }
      } catch (error) {
        console.error(`バリアント作成エラー (${variant.title}):`, error.message);
      }
    }

    // 4. デフォルトバリアントも更新
    console.log('\n4. デフォルトバリアントを更新中...');
    const updateDefaultSkuMutation = gql`
      mutation updateInventoryItem($id: ID!, $input: InventoryItemInput!) {
        inventoryItemUpdate(id: $id, input: $input) {
          inventoryItem {
            id
            sku
            tracked
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    await client.request(updateDefaultSkuMutation, {
      id: defaultVariant.inventoryItem.id,
      input: {
        sku: `${BASE_SKU}-1D`,
        tracked: true
      }
    });

    // 在庫を設定
    const setDefaultQuantityMutation = gql`
      mutation setQuantity($input: InventorySetQuantitiesInput!) {
        inventorySetQuantities(input: $input) {
          userErrors {
            field
            message
          }
        }
      }
    `;

    await client.request(setDefaultQuantityMutation, {
      input: {
        reason: "correction",
        name: "available",
        quantities: [{
          inventoryItemId: defaultVariant.inventoryItem.id,
          locationId: nyLocation.id,
          quantity: PRODUCT_DATA.quantity
        }]
      }
    });

    console.log('✓ デフォルトバリアント更新完了');

    // 5. メタフィールドを設定
    console.log('\n5. メタフィールドを設定中...');
    const metafieldsSetMutation = gql`
      mutation setMetafields($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          metafields {
            id
            namespace
            key
            value
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const allVariants = [
      { title: "1日レンタル", suffix: "1D", days: 1, price: 700 },
      ...ADDITIONAL_VARIANTS
    ];

    const metafields = [
      {
        ownerId: PRODUCT_ID,
        namespace: "rental",
        key: "basic_info",
        value: JSON.stringify({
          product_code: PRODUCT_DATA.productCode,
          detail_code: PRODUCT_DATA.detailCode,
          category_name: PRODUCT_DATA.categoryName,
          kana: PRODUCT_DATA.kana
        }),
        type: "json"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "rental",
        key: "pricing",
        value: JSON.stringify({
          base_price: PRODUCT_DATA.basePrice,
          extra_day_rate: PRODUCT_DATA.extraDayRate
        }),
        type: "json"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "rental",
        key: "variant_mapping",
        value: JSON.stringify(Object.entries(variantIds).map(([suffix, id]) => ({
          variant_id: id,
          rental_period: suffix,
          days: allVariants.find(v => v.suffix === suffix)?.days || 0
        }))),
        type: "json"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "rental",
        key: "status",
        value: JSON.stringify({
          enabled: true,
          maintenance_notes: ""
        }),
        type: "json"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "width",
        value: PRODUCT_DATA.width.toString(),
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "depth",
        value: PRODUCT_DATA.depth.toString(),
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "height",
        value: PRODUCT_DATA.height.toString(),
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "purchase_price",
        value: PRODUCT_DATA.purchasePrice.toString(),
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "variation_type",
        value: "rental_period",
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "booking_type",
        value: "日単位",
        type: "single_line_text_field"
      },
      {
        ownerId: PRODUCT_ID,
        namespace: "custom",
        key: "booking_enabled",
        value: "true",
        type: "single_line_text_field"
      }
    ];

    const metafieldsResult = await client.request(metafieldsSetMutation, { metafields });
    
    if (metafieldsResult.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', metafieldsResult.metafieldsSet.userErrors);
    } else {
      console.log('✓ メタフィールド設定完了');
      console.log(`  設定されたメタフィールド数: ${metafieldsResult.metafieldsSet.metafields.length}`);
    }

    console.log('\n=== 商品バリアント追加完了 ===');
    console.log(`商品ID: ${PRODUCT_ID}`);
    console.log(`商品URL: https://admin.shopify.com/store/peaces-test-block/products/${PRODUCT_ID.split('/').pop()}`);
    
  } catch (error) {
    console.error('エラーが発生しました:', error);
    if (error.response && error.response.errors) {
      console.error('GraphQLエラー:', JSON.stringify(error.response.errors, null, 2));
    }
  }
}

addVariantsToProduct();