/**
 * Shopify商品をデータベースに同期するスクリプト
 */

import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import { v4 as uuidv4 } from 'uuid';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品一覧を取得するクエリ
const GET_PRODUCTS = gql`
  query getProducts($first: Int!, $after: String) {
    products(first: $first, after: $after) {
      edges {
        node {
          id
          title
          status
          handle
          variants(first: 10) {
            edges {
              node {
                id
                title
                sku
                price
                inventoryQuantity
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
              }
            }
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`;

async function syncShopifyProducts() {
  console.log('🔄 Shopify商品をデータベースに同期開始...\n');

  try {
    let hasNextPage = true;
    let cursor = null;
    let totalProducts = 0;
    let syncedProducts = 0;
    let skippedProducts = 0;

    while (hasNextPage) {
      console.log(`📦 商品を取得中... ${cursor ? `(cursor: ${cursor.slice(-8)})` : '(初回)'}`);

      const result = await shopifyClient.request(GET_PRODUCTS, {
        first: 20,
        after: cursor
      });

      const products = result.products.edges.map(edge => edge.node);
      totalProducts += products.length;

      console.log(`   取得した商品: ${products.length}件`);

      for (const shopifyProduct of products) {
        const shopifyId = shopifyProduct.id.replace('gid://shopify/Product/', '');
        
        console.log(`\n🔍 処理中: ${shopifyProduct.title} (ID: ${shopifyId})`);

        // データベースに既存の商品があるかチェック
        const existingProduct = await prisma.product.findFirst({
          where: { shopifyId: shopifyId }
        });

        if (existingProduct) {
          console.log(`   ⏭️ スキップ: 既に存在します (DB ID: ${existingProduct.id})`);
          skippedProducts++;
          continue;
        }

        // バリアントから基本SKUを抽出（最初のバリアントのSKUから-1D等を除去）
        const firstVariant = shopifyProduct.variants.edges[0]?.node;
        let baseSku = firstVariant?.sku || `SHOPIFY-${shopifyId}`;
        
        // SKUから末尾の-1D, -2D等を除去して基本SKUを取得
        baseSku = baseSku.replace(/-\d+D$/, '');

        // 価格を取得（最初のバリアントの価格）
        const price = firstVariant?.price ? parseFloat(firstVariant.price) * 100 : 10000; // セント単位に変換

        // メタフィールドを処理
        const metafields = {};
        shopifyProduct.metafields.edges.forEach(edge => {
          const meta = edge.node;
          metafields[`${meta.namespace}.${meta.key}`] = meta.value;
        });

        // 基本情報を構築
        const basicInfo = {
          category: metafields['rental.category'] || 'その他',
          material: metafields['rental.material'] || '未設定',
          color: metafields['rental.color'] || '未設定',
          size: metafields['rental.size'] || 'M',
          weight: metafields['rental.weight'] || '未設定',
          manufacturer: metafields['rental.manufacturer'] || '未設定'
        };

        // データベースに商品を作成
        const newProduct = await prisma.product.create({
          data: {
            id: uuidv4(),
            shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
            shopifyId: shopifyId,
            title: shopifyProduct.title,
            sku: baseSku,
            price: price,
            status: shopifyProduct.status === 'ACTIVE' ? 'AVAILABLE' : 'INACTIVE',
            basicInfo: basicInfo,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });

        console.log(`   ✅ 作成完了: ${newProduct.id}`);
        console.log(`      SKU: ${baseSku}`);
        console.log(`      価格: ¥${(price / 100).toLocaleString()}`);
        console.log(`      ステータス: ${newProduct.status}`);
        
        syncedProducts++;
      }

      // 次のページがあるかチェック
      hasNextPage = result.products.pageInfo.hasNextPage;
      cursor = result.products.pageInfo.endCursor;

      if (hasNextPage) {
        console.log(`\n⏭️ 次のページに進みます...`);
      }
    }

    console.log('\n=== 同期完了 ===');
    console.log(`📊 処理結果:`);
    console.log(`   総商品数: ${totalProducts}件`);
    console.log(`   新規作成: ${syncedProducts}件`);
    console.log(`   スキップ: ${skippedProducts}件`);

    // 同期後の確認
    console.log('\n=== 同期後の確認 ===');
    const dbProducts = await prisma.product.findMany({
      select: {
        id: true,
        title: true,
        sku: true,
        shopifyId: true,
        status: true
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    console.log(`📦 データベース内の商品（最新10件）:`);
    dbProducts.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.title} (${product.sku})`);
      console.log(`      DB ID: ${product.id}`);
      console.log(`      Shopify ID: ${product.shopifyId}`);
      console.log(`      ステータス: ${product.status}`);
    });

    // 特定のSKUをチェック
    console.log('\n=== 特定商品の確認 ===');
    const targetSkus = ['201-07-107', '212-05-023', '201-06-555'];
    
    for (const sku of targetSkus) {
      const product = await prisma.product.findFirst({
        where: { sku: sku }
      });
      
      if (product) {
        console.log(`✅ ${sku}: 見つかりました (${product.title})`);
      } else {
        console.log(`❌ ${sku}: 見つかりません`);
      }
    }

  } catch (error) {
    console.error('❌ 同期中にエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
syncShopifyProducts();
