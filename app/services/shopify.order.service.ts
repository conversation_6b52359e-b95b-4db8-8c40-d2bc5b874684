import { Session } from "@shopify/shopify-api";
import { shopifyApi } from "@shopify/shopify-api";
import { prisma } from "~/db.server";

/**
 * Shopify注文データ取得サービス
 */
export class ShopifyOrderService {
  private session: Session;
  private client: any;

  constructor(session: Session) {
    this.session = session;
    // 新しいGraphQLクライアントの初期化方法
    this.client = {
      query: async ({ data }: { data: any }) => {
        const client = new shopifyApi.clients.Graphql({ session });
        return await client.query({ data });
      }
    };
  }

  /**
   * Shopifyから注文データを取得
   * @param limit 取得する注文数
   * @returns 注文データ
   */
  async fetchOrders(limit = 50) {
    try {
      const query = `
        {
          orders(first: ${limit}, sortKey: CREATED_AT, reverse: true) {
            edges {
              node {
                id
                name
                email
                phone
                createdAt
                displayFinancialStatus
                displayFulfillmentStatus
                subtotalPriceSet {
                  shopMoney {
                    amount
                    currencyCode
                  }
                }
                totalPriceSet {
                  shopMoney {
                    amount
                    currencyCode
                  }
                }
                customer {
                  id
                  firstName
                  lastName
                  email
                  phone
                }
                shippingAddress {
                  address1
                  address2
                  city
                  province
                  zip
                  country
                  phone
                }
                lineItems(first: 10) {
                  edges {
                    node {
                      id
                      title
                      quantity
                      variant {
                        id
                        title
                        sku
                        product {
                          id
                          title
                        }
                      }
                      customAttributes {
                        key
                        value
                      }
                    }
                  }
                }
                metafields(first: 10, namespace: "rental") {
                  edges {
                    node {
                      id
                      namespace
                      key
                      value
                    }
                  }
                }
              }
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      `;

      const response = await this.client.query({
        data: query,
      });

      if (response.body.data) {
        return response.body.data.orders.edges.map((edge: any) => edge.node);
      }

      return [];
    } catch (error) {
      console.error("Shopify注文データ取得エラー:", error);
      throw error;
    }
  }

  /**
   * 注文メタフィールドを更新
   * @param orderId 注文ID
   * @param metafields メタフィールド
   */
  async updateOrderMetafields(orderId: string, metafields: any[]) {
    try {
      const metafieldInputs = metafields.map((metafield) => {
        return {
          namespace: metafield.namespace || "rental",
          key: metafield.key,
          value: typeof metafield.value === "string"
            ? metafield.value
            : JSON.stringify(metafield.value),
          type: metafield.type || "string",
        };
      });

      const mutation = `
        mutation orderUpdate($input: OrderInput!) {
          orderUpdate(input: $input) {
            order {
              id
              metafields(first: 10, namespace: "rental") {
                edges {
                  node {
                    id
                    namespace
                    key
                    value
                  }
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const response = await this.client.query({
        data: {
          query: mutation,
          variables: {
            input: {
              id: orderId,
              metafields: metafieldInputs,
            },
          },
        },
      });

      if (response.body.data.orderUpdate.userErrors.length > 0) {
        throw new Error(response.body.data.orderUpdate.userErrors[0].message);
      }

      return response.body.data.orderUpdate.order;
    } catch (error) {
      console.error("注文メタフィールド更新エラー:", error);
      throw error;
    }
  }

  /**
   * Shopify注文データをPrismaデータベースと同期
   * @param shop ショップドメイン
   */
  async syncOrdersWithDatabase(shop: string) {
    try {
      // Shopifyから注文データを取得
      const shopifyOrders = await this.fetchOrders();

      // 同期結果を記録
      const syncResults = {
        created: 0,
        updated: 0,
        skipped: 0,
        errors: 0,
      };

      // 各注文を処理
      for (const order of shopifyOrders) {
        try {
          // Shopify IDからGID部分を抽出
          const shopifyId = order.id.replace("gid://shopify/Order/", "");
          const orderNumber = order.name;

          // データベースに既存の注文があるか確認
          const existingOrder = await prisma.order.findFirst({
            where: {
              shop,
              shopifyId,
            },
          });

          // メタフィールドを解析
          const metafields = order.metafields?.edges.map((edge: any) => ({
            key: edge.node.key,
            value: edge.node.value,
            namespace: edge.node.namespace,
          })) || [];

          // 予約IDを取得
          const reservationMetafield = metafields.find((m: any) => m.key === "reservation_id");
          const reservationId = reservationMetafield ? reservationMetafield.value : null;

          // 予約タイプを取得
          const reservationTypeMetafield = metafields.find((m: any) => m.key === "reservation_type");
          const reservationType = reservationTypeMetafield ? reservationTypeMetafield.value : null;

          // 予約日程を取得
          const startDateMetafield = metafields.find((m: any) => m.key === "start_date");
          const endDateMetafield = metafields.find((m: any) => m.key === "end_date");

          const startDate = startDateMetafield ? new Date(startDateMetafield.value) : null;
          const endDate = endDateMetafield ? new Date(endDateMetafield.value) : null;

          // 顧客情報を取得
          const customerName = order.customer
            ? `${order.customer.firstName || ''} ${order.customer.lastName || ''}`.trim()
            : '';
          const customerEmail = order.customer?.email || order.email || '';

          // 金額情報を取得
          const totalAmount = order.totalPriceSet?.shopMoney?.amount
            ? parseFloat(order.totalPriceSet.shopMoney.amount)
            : 0;

          // 支払い状況を取得
          const paymentStatus = order.displayFinancialStatus || '';

          if (existingOrder) {
            // 既存の注文を更新
            await prisma.order.update({
              where: {
                id: existingOrder.id,
              },
              data: {
                orderNumber,
                customerEmail,
                customerName,
                startDate,
                endDate,
                reservationType,
                reservationId,
                totalAmount,
                paymentStatus,
                metadata: {
                  order: order,
                  metafields: metafields,
                },
                syncStatus: "SYNCED",
                lastSyncedAt: new Date(),
                updatedAt: new Date(),
              },
            });
            syncResults.updated++;
          } else {
            // 新規注文を作成
            await prisma.order.create({
              data: {
                shop,
                shopifyId,
                orderNumber,
                customerEmail,
                customerName,
                startDate,
                endDate,
                reservationType,
                reservationId,
                totalAmount,
                paymentStatus,
                metadata: {
                  order: order,
                  metafields: metafields,
                },
                syncStatus: "SYNCED",
                lastSyncedAt: new Date(),
              },
            });
            syncResults.created++;
          }

          // 関連する予約があれば更新
          if (reservationId) {
            const booking = await prisma.booking.findUnique({
              where: {
                bookingId: reservationId,
              },
            });

            if (booking) {
              await prisma.booking.update({
                where: {
                  id: booking.id,
                },
                data: {
                  orderId: shopifyId,
                  orderName: orderNumber,
                  paymentStatus: paymentStatus === "PAID" ? "COMPLETED" : "PENDING",
                  updatedAt: new Date(),
                },
              });
            }
          }
        } catch (error) {
          console.error(`注文同期エラー (${order.name}):`, error);
          syncResults.errors++;
        }
      }

      return syncResults;
    } catch (error) {
      console.error("注文同期エラー:", error);
      throw error;
    }
  }
}
