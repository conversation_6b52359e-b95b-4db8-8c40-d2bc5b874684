import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品検索のクエリ
const GET_PRODUCT_BY_TITLE = gql`
  query getProductByTitle($query: String!) {
    products(first: 1, query: $query) {
      edges {
        node {
          id
          title
          metafields(first: 20) {
            edges {
              node {
                namespace
                key
                value
                type
              }
            }
          }
          variants(first: 50) {
            edges {
              node {
                id
                sku
                title
                price
              }
            }
          }
        }
      }
    }
  }
`;

// タイトルで商品を検索（完全一致）
async function getProductByTitle(title) {
  try {
    const result = await graphQLClient.request(GET_PRODUCT_BY_TITLE, {
      query: `title:"${title}"`,
    });

    if (result.products.edges.length === 0) {
      console.error(`タイトル "${title}" の商品が見つかりませんでした`);
      return null;
    }

    // 完全一致を確認
    const matchedProduct = result.products.edges.find(edge => edge.node.title === title);
    if (!matchedProduct) {
      console.error(`タイトル "${title}" の商品が完全一致しませんでした`);
      return null;
    }

    return matchedProduct.node;
  } catch (error) {
    console.error('商品検索エラー:', error);
    return null;
  }
}

// 日付をフォーマット
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
}

// 予約状況を確認
async function checkReservationStatus(productTitle) {
  console.log(`\n=== ${productTitle} の予約状況を確認 ===`);
  
  // 商品を検索
  const product = await getProductByTitle(productTitle);
  if (!product) {
    console.error(`${productTitle} の商品が見つかりませんでした。`);
    return;
  }
  
  console.log(`商品を見つけました: ${product.title} (${product.id})`);
  
  // メタフィールドから情報を取得
  const metafields = product.metafields.edges.map(edge => edge.node);
  
  // 基本情報を取得
  const basicInfoMetafield = metafields.find(
    meta => meta.namespace === 'rental' && meta.key === 'basic_info'
  );
  
  if (basicInfoMetafield) {
    const basicInfo = JSON.parse(basicInfoMetafield.value);
    console.log('\n基本情報:');
    console.log(`商品コード: ${basicInfo.productCode}-${basicInfo.detailCode}`);
    console.log(`商品名カナ: ${basicInfo.kana}`);
    console.log(`サイズ: W${basicInfo.dimensions.width} × D${basicInfo.dimensions.depth} × H${basicInfo.dimensions.height} cm`);
    console.log(`座面サイズ: W${basicInfo.seatDimensions.width} × D${basicInfo.seatDimensions.depth} × H${basicInfo.seatDimensions.height} cm`);
    console.log(`素材: ${basicInfo.material}`);
    console.log(`色: ${basicInfo.color}`);
    console.log(`メーカー: ${basicInfo.maker}`);
    console.log(`キャンペーン: ${basicInfo.campaign}`);
    console.log(`備考: ${basicInfo.notes}`);
  } else {
    console.log('基本情報が見つかりませんでした。');
  }
  
  // 料金設定を取得
  const pricingMetafield = metafields.find(
    meta => meta.namespace === 'rental' && meta.key === 'pricing'
  );
  
  if (pricingMetafield) {
    const pricing = JSON.parse(pricingMetafield.value);
    console.log('\n料金設定:');
    console.log(`基本料金: ${pricing.basePrice}円`);
    console.log(`デポジット率: ${pricing.depositRate * 100}%`);
    console.log(`割引ルール: 2-6日 ${pricing.discountRules.day2_6_rate * 100}%, 7日以上 ${pricing.discountRules.day7_plus_rate * 100}%`);
    console.log(`最小レンタル日数: ${pricing.minimumDays}日`);
    console.log(`最大レンタル日数: ${pricing.maximumDays}日`);
  } else {
    console.log('料金設定が見つかりませんでした。');
  }
  
  // 予約情報を取得
  const reservationMetafield = metafields.find(
    meta => meta.namespace === 'rental' && meta.key === 'reservation_info'
  );
  
  if (!reservationMetafield) {
    console.log('予約情報が見つかりませんでした。');
    return;
  }
  
  // 在庫情報を取得
  const inventoryMetafield = metafields.find(
    meta => meta.namespace === 'rental' && meta.key === 'inventory_info'
  );
  
  if (!inventoryMetafield) {
    console.log('在庫情報が見つかりませんでした。');
    return;
  }
  
  // 予約情報をパース
  const reservations = JSON.parse(reservationMetafield.value);
  
  // 在庫情報をパース
  const inventoryInfo = JSON.parse(inventoryMetafield.value);
  
  // 各SKUごとに予約状況を表示
  for (const item of reservations) {
    const sku = item.sku;
    const inventory = inventoryInfo[sku] || { available: 0, reserved: 0, status: '不明' };
    
    console.log(`\n商品番号: ${sku}`);
    console.log(`在庫状況: 利用可能=${inventory.available}, 予約済み=${inventory.reserved}, 総数=${inventory.onHand}`);
    console.log(`ステータス: ${inventory.status}`);
    
    if (item.reservations.length === 0) {
      console.log('予約はありません。');
      continue;
    }
    
    console.log('\n予約一覧:');
    for (const reservation of item.reservations) {
      console.log(`- 予約ID: ${reservation.id}`);
      console.log(`  期間: ${formatDate(reservation.startDate)} 〜 ${formatDate(reservation.endDate)}`);
      console.log(`  状態: ${reservation.status === 'confirmed' ? '確定予約' : '仮予約'}`);
      console.log(`  顧客: ${reservation.customerName} (${reservation.customerEmail})`);
      console.log(`  注文ID: ${reservation.orderId}`);
      console.log(`  備考: ${reservation.notes || 'なし'}`);
    }
  }
  
  // バリアント情報を表示
  console.log('\nバリアント一覧:');
  const variants = product.variants.edges.map(edge => edge.node);
  for (const variant of variants) {
    console.log(`- ${variant.title} (${variant.sku}): ${variant.price}円`);
  }
}

// 特定の期間の予約状況を確認
async function checkPeriodAvailability(productTitle, startDate, endDate) {
  console.log(`\n=== ${productTitle} の ${startDate} 〜 ${endDate} の予約状況を確認 ===`);
  
  // 商品を検索
  const product = await getProductByTitle(productTitle);
  if (!product) {
    console.error(`${productTitle} の商品が見つかりませんでした。`);
    return;
  }
  
  // メタフィールドから予約情報を取得
  const metafields = product.metafields.edges.map(edge => edge.node);
  
  // 予約情報を取得
  const reservationMetafield = metafields.find(
    meta => meta.namespace === 'rental' && meta.key === 'reservation_info'
  );
  
  if (!reservationMetafield) {
    console.log('予約情報が見つかりませんでした。');
    return;
  }
  
  // 在庫情報を取得
  const inventoryMetafield = metafields.find(
    meta => meta.namespace === 'rental' && meta.key === 'inventory_info'
  );
  
  if (!inventoryMetafield) {
    console.log('在庫情報が見つかりませんでした。');
    return;
  }
  
  // 予約情報をパース
  const reservations = JSON.parse(reservationMetafield.value);
  
  // 在庫情報をパース
  const inventoryInfo = JSON.parse(inventoryMetafield.value);
  
  // 期間内の各日付をチェック
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  // 各SKUごとに予約状況を確認
  for (const item of reservations) {
    const sku = item.sku;
    const inventory = inventoryInfo[sku] || { status: '不明' };
    
    console.log(`\n商品番号: ${sku}`);
    console.log(`ステータス: ${inventory.status}`);
    
    // ステータスが利用可能でない場合はスキップ
    if (inventory.status !== 'available') {
      console.log(`この商品は現在 ${inventory.status} 状態のため予約できません。`);
      continue;
    }
    
    let isAvailable = true;
    const conflictingReservations = [];
    
    // 期間内の各日付をチェック
    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      const dateStr = date.toISOString().split('T')[0];
      
      // 予約済みかチェック
      for (const reservation of item.reservations) {
        const reservationStart = new Date(reservation.startDate);
        const reservationEnd = new Date(reservation.endDate);
        
        if (date >= reservationStart && date <= reservationEnd) {
          isAvailable = false;
          if (!conflictingReservations.includes(reservation)) {
            conflictingReservations.push(reservation);
          }
        }
      }
    }
    
    if (isAvailable) {
      console.log(`${startDate} 〜 ${endDate} の期間は予約可能です。`);
    } else {
      console.log(`${startDate} 〜 ${endDate} の期間は予約できません。`);
      console.log('競合する予約:');
      
      for (const reservation of conflictingReservations) {
        console.log(`- 予約ID: ${reservation.id}`);
        console.log(`  期間: ${formatDate(reservation.startDate)} 〜 ${formatDate(reservation.endDate)}`);
        console.log(`  状態: ${reservation.status === 'confirmed' ? '確定予約' : '仮予約'}`);
        console.log(`  顧客: ${reservation.customerName}`);
      }
    }
  }
}

// メイン処理
async function main() {
  const testProducts = [
    "【テスト】ベーシックソファ オフホワイト 1シーター",
    "【テスト】ウイングソファ アンティークグリーン 1シーター",
    "【テスト】カリモクソファ モケットグリーン 1シーター",
    "【テスト】プレーンソファ 1シーター"
  ];
  
  // 各商品の予約状況を確認
  for (const productTitle of testProducts) {
    await checkReservationStatus(productTitle);
  }
  
  // 特定の期間の予約状況を確認
  const startDate = new Date();
  startDate.setDate(startDate.getDate() + 5);
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + 10);
  
  for (const productTitle of testProducts) {
    await checkPeriodAvailability(
      productTitle,
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0]
    );
  }
}

// 実行
main();
