/**
 * 仮予約から本予約への変更リクエスト一覧ページ
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { 
  Page, 
  Layout, 
  Card, 
  ResourceList, 
  ResourceItem, 
  Text, 
  Badge, 
  ButtonGroup, 
  Button,
  Modal,
  TextField,
  Banner,
  Toast,
  Frame
} from "@shopify/polaris";
import { useState, useCallback } from "react";
import { format } from "date-fns";
import { ja } from "date-fns/locale";
import { prisma } from "../db.server";
import { authenticate } from "../shopify.server";

/**
 * ローダー関数
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin } = await authenticate.admin(request);

  // 仮予約から本予約への変更リクエスト一覧を取得
  const upgradeRequests = await prisma.bookingUpgradeRequest.findMany({
    where: {
      status: { in: ['PENDING', 'APPROVED', 'REJECTED'] }
    },
    orderBy: [
      { status: 'asc' },
      { createdAt: 'desc' }
    ],
    include: {
      booking: {
        include: {
          product: true
        }
      }
    }
  });

  return json({
    shop: admin.shop,
    upgradeRequests
  });
}

/**
 * 仮予約から本予約への変更リクエスト一覧ページコンポーネント
 */
export default function UpgradeRequestsPage() {
  const { upgradeRequests } = useLoaderData<typeof loader>();
  const navigate = useNavigate();

  // モーダル状態
  const [activeRequest, setActiveRequest] = useState<any | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [adminNotes, setAdminNotes] = useState('');
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  
  // トースト状態
  const [toastActive, setToastActive] = useState(false);
  const [toastContent, setToastContent] = useState('');
  const [toastError, setToastError] = useState(false);

  // モーダルを開く
  const handleOpenApproveModal = useCallback((request: any) => {
    setActiveRequest(request);
    setAdminNotes('');
    setShowApproveModal(true);
  }, []);

  const handleOpenRejectModal = useCallback((request: any) => {
    setActiveRequest(request);
    setAdminNotes('');
    setShowRejectModal(true);
  }, []);

  // モーダルを閉じる
  const handleCloseModal = useCallback(() => {
    setShowApproveModal(false);
    setShowRejectModal(false);
    setActiveRequest(null);
    setAdminNotes('');
  }, []);

  // リクエストを処理する
  const handleProcessRequest = useCallback(async (action: 'approve' | 'reject' | 'cancel') => {
    if (!activeRequest) return;

    setIsProcessing(true);

    try {
      const response = await fetch('/api/booking/process-upgrade-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          upgradeRequestId: activeRequest.id,
          action,
          adminNotes,
          adminId: 'admin' // TODO: 実際の管理者IDを設定
        }),
      });

      const result = await response.json();

      if (result.success) {
        setToastContent(result.message);
        setToastError(false);
        setToastActive(true);
        handleCloseModal();
        
        // ページをリロード
        navigate('.', { replace: true });
      } else {
        setToastContent(`エラー: ${result.error}`);
        setToastError(true);
        setToastActive(true);
      }
    } catch (error) {
      console.error('リクエスト処理エラー:', error);
      setToastContent('リクエスト処理中にエラーが発生しました');
      setToastError(true);
      setToastActive(true);
    } finally {
      setIsProcessing(false);
    }
  }, [activeRequest, adminNotes, navigate, handleCloseModal]);

  // ステータスに応じたバッジを取得
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge status="attention">処理待ち</Badge>;
      case 'APPROVED':
        return <Badge status="success">承認済み</Badge>;
      case 'REJECTED':
        return <Badge status="warning">拒否</Badge>;
      case 'CANCELLED':
        return <Badge status="critical">キャンセル</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  return (
    <Frame>
      <Page
        title="仮予約から本予約への変更リクエスト"
        backAction={{ content: '予約管理へ戻る', url: '/admin/bookings' }}
      >
        <Layout>
          <Layout.Section>
            <Card>
              {upgradeRequests.length === 0 ? (
                <Card.Section>
                  <Text variant="bodyMd" as="p">
                    変更リクエストはありません
                  </Text>
                </Card.Section>
              ) : (
                <ResourceList
                  resourceName={{ singular: '変更リクエスト', plural: '変更リクエスト' }}
                  items={upgradeRequests}
                  renderItem={(request) => {
                    const { id, status, requesterName, requesterEmail, requesterPhone, createdAt, booking } = request;
                    const { bookingId, productId, startDate, endDate, customerName, customerEmail } = booking;
                    const productTitle = booking.product?.title || '不明な商品';
                    
                    return (
                      <ResourceItem id={id}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <div>
                            <Text variant="headingMd" as="h3">
                              予約番号: {bookingId} {getStatusBadge(status)}
                            </Text>
                            <Text variant="bodyMd" as="p">
                              商品: {productTitle}
                            </Text>
                            <Text variant="bodyMd" as="p">
                              予約期間: {format(new Date(startDate), 'yyyy/MM/dd', { locale: ja })} 〜 {format(new Date(endDate), 'yyyy/MM/dd', { locale: ja })}
                            </Text>
                            <Text variant="bodyMd" as="p">
                              仮予約顧客: {customerName || '不明'} ({customerEmail || '不明'})
                            </Text>
                            <Text variant="bodyMd" as="p">
                              リクエスト者: {requesterName || '不明'} ({requesterEmail || '不明'}) {requesterPhone ? `電話: ${requesterPhone}` : ''}
                            </Text>
                            <Text variant="bodyMd" as="p">
                              リクエスト日時: {format(new Date(createdAt), 'yyyy/MM/dd HH:mm', { locale: ja })}
                            </Text>
                          </div>
                          
                          {status === 'PENDING' && (
                            <ButtonGroup>
                              <Button primary onClick={() => handleOpenApproveModal(request)}>
                                承認
                              </Button>
                              <Button onClick={() => handleOpenRejectModal(request)}>
                                拒否
                              </Button>
                            </ButtonGroup>
                          )}
                        </div>
                      </ResourceItem>
                    );
                  }}
                />
              )}
            </Card>
          </Layout.Section>
        </Layout>

        {/* 承認モーダル */}
        <Modal
          open={showApproveModal}
          onClose={handleCloseModal}
          title="変更リクエストを承認"
          primaryAction={{
            content: '承認',
            onAction: () => handleProcessRequest('approve'),
            loading: isProcessing
          }}
          secondaryActions={[
            {
              content: 'キャンセル',
              onAction: handleCloseModal
            }
          ]}
        >
          <Modal.Section>
            {activeRequest && (
              <>
                <Banner status="warning">
                  <p>
                    この操作により、仮予約が本予約に変更されます。
                    リクエスト者と仮予約顧客の両方にメール通知が送信されます。
                  </p>
                </Banner>
                <div style={{ marginTop: '1rem' }}>
                  <TextField
                    label="管理者メモ（オプション）"
                    value={adminNotes}
                    onChange={setAdminNotes}
                    multiline={3}
                    placeholder="承認に関する備考があれば入力してください"
                  />
                </div>
              </>
            )}
          </Modal.Section>
        </Modal>

        {/* 拒否モーダル */}
        <Modal
          open={showRejectModal}
          onClose={handleCloseModal}
          title="変更リクエストを拒否"
          primaryAction={{
            content: '拒否',
            onAction: () => handleProcessRequest('reject'),
            loading: isProcessing,
            destructive: true
          }}
          secondaryActions={[
            {
              content: 'キャンセル',
              onAction: handleCloseModal
            }
          ]}
        >
          <Modal.Section>
            {activeRequest && (
              <>
                <Banner status="critical">
                  <p>
                    この操作により、変更リクエストが拒否されます。
                    仮予約は引き続き有効です。
                    リクエスト者と仮予約顧客の両方にメール通知が送信されます。
                  </p>
                </Banner>
                <div style={{ marginTop: '1rem' }}>
                  <TextField
                    label="拒否理由（オプション）"
                    value={adminNotes}
                    onChange={setAdminNotes}
                    multiline={3}
                    placeholder="拒否理由があれば入力してください"
                  />
                </div>
              </>
            )}
          </Modal.Section>
        </Modal>

        {/* トースト通知 */}
        {toastActive && (
          <Toast
            content={toastContent}
            error={toastError}
            onDismiss={() => setToastActive(false)}
          />
        )}
      </Page>
    </Frame>
  );
}
