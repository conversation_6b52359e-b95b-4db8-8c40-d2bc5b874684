/**
 * 予約フローテストページ
 *
 * 標準化された予約サービスとカレンダーコンポーネントを使用した予約フローのテストページです。
 */

import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, useSubmit, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  BlockStack,
  Text,
  Select,
  TextField,
  Button,
  Banner,
  InlineStack,
  Box,
  Divider,
  Frame,
  Modal,
  ButtonGroup,
  Tabs,
  Link
} from "@shopify/polaris";
import { useState, useCallback, useEffect } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { UnifiedCalendarWithErrorBoundary } from "../components/Calendar/UnifiedCalendar";
import { createBookingWithDraftOrder } from "../services/booking.service";
import { addDays, format } from "date-fns";

/**
 * ローダー関数
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // 商品データを取得
    const products = await prisma.product.findMany({
      where: { shop },
      select: {
        id: true,
        title: true,
        sku: true,
        status: true,
        price: true,
        shopifyId: true
      },
      orderBy: { title: 'asc' },
    });

    // 予約データを取得
    const bookings = await prisma.booking.findMany({
      where: {
        shop,
        status: { in: ['PROVISIONAL', 'CONFIRMED'] }
      },
      select: {
        id: true,
        bookingId: true,
        productId: true,
        startDate: true,
        endDate: true,
        status: true,
        bookingType: true
      },
      orderBy: { startDate: 'asc' },
    });

    // Date型を文字列に変換
    const serializedBookings = bookings.map(b => ({
      ...b,
      startDate: b.startDate.toISOString(),
      endDate: b.endDate.toISOString()
    }));

    // Shopifyから商品情報を取得
    const shopifyProductsResponse = await admin.graphql(`
      query getProducts {
        products(first: 10) {
          edges {
            node {
              id
              title
              handle
              status
              variants(first: 1) {
                edges {
                  node {
                    id
                    title
                    sku
                    price
                  }
                }
              }
            }
          }
        }
      }
    `);

    const shopifyProductsData = await shopifyProductsResponse.json();
    const shopifyProducts = shopifyProductsData.data?.products?.edges?.map(edge => ({
      id: edge.node.id,
      title: edge.node.title,
      handle: edge.node.handle,
      status: edge.node.status,
      variant: edge.node.variants.edges[0]?.node || null
    })) || [];

    return json({
      products,
      bookings: serializedBookings,
      shopifyProducts,
      shop
    });
  } catch (error) {
    console.error("データ取得エラー:", error);
    return json({
      products: [],
      bookings: [],
      shopifyProducts: [],
      shop: session.shop,
      error: "データの取得中にエラーが発生しました"
    });
  }
}

/**
 * アクション関数
 */
export async function action({ request }: ActionFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    const formData = await request.formData();
    const intent = formData.get("intent") as string;

    if (intent === "create-booking") {
      const productId = formData.get("productId") as string;
      const variantId = formData.get("variantId") as string;
      const startDate = formData.get("startDate") as string;
      const endDate = formData.get("endDate") as string;
      const customerEmail = formData.get("customerEmail") as string;
      const customerName = formData.get("customerName") as string;
      const notes = formData.get("notes") as string;

      if (!productId || !variantId || !startDate || !endDate) {
        return json({
          success: false,
          error: "必須項目が入力されていません"
        });
      }

      // 予約サービスを使用して予約を作成
      const result = await createBookingWithDraftOrder({
        shop,
        productId,
        variantId,
        startDate,
        endDate,
        customerEmail,
        customerName,
        notes,
        bookingType: "PROVISIONAL",
        admin
      });

      if (!result.success) {
        return json({
          success: false,
          error: result.error
        });
      }

      return json({
        success: true,
        booking: result.booking,
        draftOrder: result.draftOrder,
        totalPrice: result.totalPrice
      });
    }

    return json({
      success: false,
      error: "不明な操作が指定されました"
    });
  } catch (error) {
    console.error("アクションエラー:", error);
    return json({
      success: false,
      error: "処理中にエラーが発生しました: " + (error instanceof Error ? error.message : String(error))
    });
  }
}

export default function BookingFlowTest() {
  const { products, bookings, shopifyProducts, shop, error } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();

  const [selectedTab, setSelectedTab] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(error || "");
  const [successMessage, setSuccessMessage] = useState("");
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  // 予約フォーム
  const [bookingForm, setBookingForm] = useState({
    productId: "",
    variantId: "",
    startDate: new Date(),
    endDate: addDays(new Date(), 3),
    customerEmail: "<EMAIL>",
    customerName: "テストユーザー",
    notes: ""
  });

  // 選択された商品の予約を取得
  const productBookings = bookingForm.productId
    ? bookings.filter(b => b.productId === bookingForm.productId)
    : [];

  // タブの設定
  const tabs = [
    {
      id: "booking-flow",
      content: "予約フロー",
      accessibilityLabel: "予約フロータブ",
      panelID: "booking-flow-panel",
    },
    {
      id: "booking-list",
      content: "予約一覧",
      accessibilityLabel: "予約一覧タブ",
      panelID: "booking-list-panel",
    }
  ];

  // アクションデータの処理
  useEffect(() => {
    if (actionData) {
      setIsLoading(false);

      if (actionData.success) {
        setSuccessMessage("予約が正常に作成されました");
        setErrorMessage("");
        setShowSuccessModal(true);
      } else if (actionData.error) {
        setErrorMessage(actionData.error);
        setSuccessMessage("");
      }
    }
  }, [actionData]);

  // 商品選択時の処理
  const handleProductChange = useCallback((value: string) => {
    // Shopify商品IDから対応するデータベース商品IDを取得
    const selectedShopifyProduct = shopifyProducts.find(p => p.id === value);
    const selectedProduct = products.find(p => p.shopifyId === selectedShopifyProduct?.id.replace("gid://shopify/Product/", ""));

    if (selectedProduct) {
      setBookingForm(prev => ({
        ...prev,
        productId: selectedProduct.id,
        variantId: ""
      }));

      // 選択された商品のバリアントを取得
      if (selectedShopifyProduct && selectedShopifyProduct.variant) {
        setBookingForm(prev => ({
          ...prev,
          productId: selectedProduct.id,
          variantId: selectedShopifyProduct.variant.id.replace("gid://shopify/ProductVariant/", "")
        }));
      }
    } else {
      // データベースに対応する商品が見つからない場合
      setErrorMessage(`選択された商品 (${value}) に対応するデータベースの商品が見つかりません。先にテストデータセットアップページで商品を登録してください。`);
      setBookingForm(prev => ({
        ...prev,
        productId: "",
        variantId: ""
      }));
    }
  }, [shopifyProducts, products]);

  // 日付選択時の処理
  const handleDateSelection = useCallback((start: Date, end: Date) => {
    setBookingForm(prev => ({
      ...prev,
      startDate: start,
      endDate: end
    }));
  }, []);

  // 予約作成処理
  const handleCreateBooking = useCallback(() => {
    setIsLoading(true);
    setErrorMessage("");
    setSuccessMessage("");

    const formData = new FormData();
    formData.append("intent", "create-booking");
    formData.append("productId", bookingForm.productId);
    formData.append("variantId", bookingForm.variantId);
    formData.append("startDate", bookingForm.startDate.toISOString().split('T')[0]);
    formData.append("endDate", bookingForm.endDate.toISOString().split('T')[0]);
    formData.append("customerEmail", bookingForm.customerEmail);
    formData.append("customerName", bookingForm.customerName);
    formData.append("notes", bookingForm.notes);

    submit(formData, { method: "post" });
  }, [bookingForm, submit]);

  return (
    <Frame>
      <Page
        title="予約フローテスト"
        backAction={{ content: "戻る", url: "/app" }}
      >
        {errorMessage && (
          <Banner status="critical" onDismiss={() => setErrorMessage("")}>
            <p>{errorMessage}</p>
          </Banner>
        )}

        {successMessage && (
          <Banner status="success" onDismiss={() => setSuccessMessage("")}>
            <p>{successMessage}</p>
          </Banner>
        )}

        <Layout>
          <Layout.Section>
            <Tabs
              tabs={tabs}
              selected={selectedTab}
              onSelect={(index) => setSelectedTab(index)}
            />

            <Card>
              {selectedTab === 0 && (
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">予約フロー</Text>
                  <Text>標準化された予約サービスとカレンダーコンポーネントを使用した予約フローのテストです。</Text>

                  <Select
                    label="商品を選択"
                    options={[
                      { label: "商品を選択してください", value: "" },
                      ...shopifyProducts.map(p => ({ label: p.title, value: p.id }))
                    ]}
                    value={bookingForm.productId}
                    onChange={handleProductChange}
                  />

                  {bookingForm.productId && (
                    <BlockStack gap="400">
                      <Text as="h3" variant="headingMd">日程を選択</Text>

                      <Box paddingBlockStart="400">
                        <UnifiedCalendarWithErrorBoundary
                          bookings={productBookings.map(booking => ({
                            startDate: new Date(booking.startDate),
                            endDate: new Date(booking.endDate),
                            bookingType: booking.bookingType
                          }))}
                          initialStartDate={bookingForm.startDate}
                          initialEndDate={bookingForm.endDate}
                          minDate={new Date()}
                          maxDate={addDays(new Date(), 365)}
                          onRangeSelect={handleDateSelection}
                          showSelectedDates={true}
                          showLegend={true}
                          showValidationErrors={true}
                        />
                      </Box>

                      <Divider />

                      <BlockStack gap="400">
                        <Text as="h3" variant="headingMd">顧客情報</Text>

                        <TextField
                          label="メールアドレス"
                          type="email"
                          value={bookingForm.customerEmail}
                          onChange={(value) => setBookingForm({...bookingForm, customerEmail: value})}
                          autoComplete="email"
                        />

                        <TextField
                          label="氏名"
                          value={bookingForm.customerName}
                          onChange={(value) => setBookingForm({...bookingForm, customerName: value})}
                          autoComplete="name"
                        />

                        <TextField
                          label="備考"
                          multiline={3}
                          value={bookingForm.notes}
                          onChange={(value) => setBookingForm({...bookingForm, notes: value})}
                        />
                      </BlockStack>

                      <Divider />

                      <InlineStack gap="400" align="end">
                        <Button
                          primary
                          loading={isLoading}
                          onClick={handleCreateBooking}
                        >
                          予約を作成
                        </Button>
                      </InlineStack>
                    </BlockStack>
                  )}
                </BlockStack>
              )}

              {selectedTab === 1 && (
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">予約一覧</Text>

                  {bookings.length > 0 ? (
                    bookings.map(booking => (
                      <Card key={booking.id}>
                        <BlockStack gap="200">
                          <Text variant="headingSm">予約ID: {booking.bookingId}</Text>
                          <Text>期間: {new Date(booking.startDate).toLocaleDateString()} 〜 {new Date(booking.endDate).toLocaleDateString()}</Text>
                          <Text>ステータス: {booking.status}</Text>
                          <Text>タイプ: {booking.bookingType}</Text>
                        </BlockStack>
                      </Card>
                    ))
                  ) : (
                    <Banner>予約データがありません</Banner>
                  )}
                </BlockStack>
              )}
            </Card>
          </Layout.Section>
        </Layout>

        {/* 予約成功モーダル */}
        <Modal
          open={showSuccessModal}
          onClose={() => setShowSuccessModal(false)}
          title="予約が作成されました"
          primaryAction={{
            content: "閉じる",
            onAction: () => setShowSuccessModal(false)
          }}
        >
          <Modal.Section>
            <BlockStack gap="400">
              {actionData?.success && (
                <>
                  <Banner status="success">
                    <Text>予約が正常に作成されました。</Text>
                  </Banner>

                  <BlockStack gap="200">
                    <Text variant="headingSm">予約情報</Text>
                    <Text>予約ID: {actionData.booking.bookingId}</Text>
                    <Text>期間: {new Date(actionData.booking.startDate).toLocaleDateString()} 〜 {new Date(actionData.booking.endDate).toLocaleDateString()}</Text>
                    <Text>合計金額: {actionData.totalPrice}円</Text>
                  </BlockStack>

                  <BlockStack gap="200">
                    <Text variant="headingSm">ドラフトオーダー情報</Text>
                    <Text>注文番号: {actionData.draftOrder.name}</Text>
                    <Text>合計金額: {actionData.draftOrder.totalPrice}円</Text>
                    <Link url={actionData.draftOrder.invoiceUrl} external>
                      請求書を表示
                    </Link>
                  </BlockStack>
                </>
              )}
            </BlockStack>
          </Modal.Section>
        </Modal>
      </Page>
    </Frame>
  );
}
