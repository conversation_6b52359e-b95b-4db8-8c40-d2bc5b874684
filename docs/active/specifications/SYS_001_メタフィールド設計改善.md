# メタフィールド設計の改善

**作成日**: 2025-05-28
**作成者**: システム開発チーム
**最終更新日**: 2025-05-30

## 概要

本ドキュメントでは、Shopifyメタフィールドの設計改善について説明します。特に、複数のバリアントを持つ商品の予約状態管理に関する課題と解決策を提案します。

## 現状の課題

現在のメタフィールド設計では、以下の課題があります：

1. **単一のステータスフィールド**：
   - 現在は`rental.status`という単一のメタフィールドで商品全体の状態を管理
   - これでは複数の予約や異なる状態（仮予約・本予約など）を表現しきれない

2. **バリアント対応の不足**：
   - 複数のバリアントがある場合、それぞれに異なる予約状態があり得る
   - 現在のメタフィールド設計ではこれを適切に表現できていない

3. **予約情報の構造化不足**：
   - 予約情報が単純なリスト形式で保存されており、バリアントごとの状態管理が困難
   - 複数の予約タイプ（仮予約・確定予約）や異なるレンタル期間の管理が難しい

## 改善案

より適切なメタフィールド設計として、以下を提案します：

### 1. 予約タイプメタフィールドの追加

「予約タイプ実装方式検討.md」で推奨されている方式に基づき、予約タイプを独立したメタフィールドとして管理します：

- 商品用: `rental.booking_type` (provisional/confirmed)
- 注文用: `rental.reservation_type` (provisional/confirmed)

これにより、予約タイプ（仮予約・本予約）を明示的に管理できるようになります。

### 2. JSON構造化メタフィールド

```json
{
  "status": "available",  // 商品全体の状態
  "lastUpdated": "2025-05-28 15:30:00",
  "variants": {
    "gid://shopify/ProductVariant/123456": {
      "status": "available",
      "reservations": [
        {
          "id": "BOOK-123456",
          "type": "PROVISIONAL",
          "startDate": "2025-05-22",
          "endDate": "2025-05-25"
        }
      ]
    },
    "gid://shopify/ProductVariant/789012": {
      "status": "unavailable",
      "reservations": [
        {
          "id": "BOOK-789012",
          "type": "CONFIRMED",
          "startDate": "2025-05-20",
          "endDate": "2025-05-30"
        }
      ]
    }
  },
  "availability": {
    "rentalStatus": "available",
    "startDate": "2025-05-28",
    "maintenanceDates": [],
    "blockedDates": []
  }
}
```

この構造により：
- 商品全体の状態と各バリアントの状態を個別に管理できる
- 各バリアントに対する予約情報を明確に関連付けられる
- 予約タイプ（仮予約・確定予約）を区別できる
- 従来の形式との互換性も維持できる

### 2. バリアント別メタフィールド（代替案）

各バリアントに対して個別のメタフィールドを作成する方法も考えられます：
- 例：`rental.variant_123456_status`, `rental.variant_789012_status`

ただし、この方法はメタフィールドの数が増えるため、管理が複雑になる可能性があります。

## 実装方法

### 1. `formatBookingsForMetafield`関数の修正

```typescript
function formatBookingsForMetafield(bookings: Booking[]): any {
  // バリアント別に予約をグループ化
  const bookingsByVariant: Record<string, any[]> = {};

  bookings.forEach(booking => {
    const variantId = booking.variantId || 'default';
    if (!bookingsByVariant[variantId]) {
      bookingsByVariant[variantId] = [];
    }

    bookingsByVariant[variantId].push({
      id: booking.bookingId,
      startDate: format(booking.startDate, 'yyyy-MM-dd'),
      endDate: format(booking.endDate, 'yyyy-MM-dd'),
      status: booking.status,
      type: booking.bookingType,
      customerName: booking.customerName,
      customerEmail: booking.customerEmail
    });
  });

  // バリアント情報を構築
  const variants: Record<string, any> = {};

  Object.entries(bookingsByVariant).forEach(([variantId, variantBookings]) => {
    variants[variantId] = {
      status: variantBookings.some(b => b.status === 'CONFIRMED') ? 'unavailable' : 'available',
      reservations: variantBookings
    };
  });

  // 従来の形式との互換性のために、すべての予約も含める
  const allBookings = bookings.map(booking => ({
    id: booking.bookingId,
    startDate: format(booking.startDate, 'yyyy-MM-dd'),
    endDate: format(booking.endDate, 'yyyy-MM-dd'),
    status: booking.status,
    customerName: booking.customerName,
    customerEmail: booking.customerEmail,
    variantId: booking.variantId
  }));

  // メタフィールド用のデータ構造
  const metafieldData = {
    // 従来の形式との互換性のために残す
    bookings: allBookings,
    // 新しい構造化データ
    status: Object.values(variants).some((v: any) => v.status === 'unavailable') ? 'unavailable' : 'available',
    lastUpdated: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
    variants,
    availability: {
      rentalStatus: 'available',
      startDate: format(new Date(), 'yyyy-MM-dd'),
      maintenanceDates: [],
      blockedDates: []
    }
  };

  return metafieldData;
}
```

### 2. メタフィールド更新処理の修正

`updateBookingMetafield`関数を修正して、Shopify Admin APIを使用するように変更します：

```typescript
export async function updateBookingMetafield(
  prisma: PrismaClient,
  admin: AdminApiContext,
  productId: string,
  forceUpdate: boolean = false,
  retryCount: number = 3
): Promise<any> {
  // 実装詳細は省略
}
```

## 移行計画

1. **予約タイプメタフィールドの追加**：
   - `scripts/add-booking-type-metafield.js`スクリプトの実行
   - 商品用と注文用の予約タイプメタフィールドを作成

2. **コード修正**：
   - `metafield-updater.ts`の修正
   - `app.bookings.new.tsx`の修正
   - 予約タイプメタフィールドを使用するように関連コードを更新

3. **テスト**：
   - 新しいメタフィールド構造でのテスト予約作成
   - 予約タイプメタフィールドを使用した予約フローのテスト
   - バリアント対応のテスト
   - 既存データとの互換性テスト

4. **本番展開**：
   - コード変更のデプロイ
   - 既存データの移行（必要に応じて）

## 今後の課題

1. **パフォーマンス最適化**：
   - メタフィールドのサイズが大きくなる可能性があるため、必要に応じて最適化が必要

2. **UI対応**：
   - 新しいメタフィールド構造に対応したUI実装

3. **バックアップと復元**：
   - メタフィールドデータのバックアップと復元機能の実装

## 参考資料

- [Shopify Metafield API Documentation](https://shopify.dev/api/admin-rest/2023-01/resources/metafield)
- [JSON Metafield Best Practices](https://shopify.dev/apps/metafields/best-practices)
