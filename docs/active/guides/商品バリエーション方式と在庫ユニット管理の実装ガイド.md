# 商品バリエーション方式と在庫ユニット管理の実装ガイド

## 概要

このドキュメントでは、Shopifyを使用したレンタル商品ECサイトにおける「商品バリエーション方式」と「在庫ユニット管理」の詳細な実装方法について説明します。従来の商品管理から新しい方式への移行手順、コード例、APIエンドポイントの設計、フロントエンド実装などを含む包括的な実装ガイドです。

## システムアーキテクチャ

### 基本構造

```
shopify-app/
├── app/
│   ├── routes/
│   │   ├── products.$handle.tsx       # 商品詳細ページ
│   │   ├── collections.$handle.tsx    # コレクションページ
│   │   ├── api.inventory-units.tsx    # 在庫ユニット管理API
│   │   ├── admin.inventory.tsx        # 管理画面：在庫管理
│   │   └── admin.maintenance.tsx      # 管理画面：メンテナンス管理
│   ├── components/
│   │   ├── ProductVariantSelector.tsx # バリエーション選択コンポーネント
│   │   ├── DateRangePicker.tsx        # 日付選択コンポーネント
│   │   ├── InventoryUnitList.tsx      # 在庫ユニット一覧コンポーネント
│   │   └── QRScanner.tsx              # QRコードスキャナーコンポーネント
│   ├── models/
│   │   ├── product.server.ts          # 商品データモデル
│   │   ├── inventory.server.ts        # 在庫データモデル
│   │   └── maintenance.server.ts      # メンテナンスデータモデル
│   └── services/
│       ├── shopify.server.ts          # Shopify API連携
│       ├── inventory.server.ts        # 在庫管理サービス
│       └── sync.server.ts             # データ同期サービス
└── prisma/
    └── schema.prisma                  # データベーススキーマ
```

### データモデル

#### 1. メタフィールド構造

**商品グループ（マスター商品）のメタフィールド**:
```javascript
{
  "rental.is_master": true,
  "rental.series_code": "TABLE-001",
  "rental.series_name": "デザイナーズテーブル シリーズ"
}
```

**実物バリエーション商品のメタフィールド**:
```javascript
{
  "rental.series_code": "TABLE-001",
  "rental.variation_type": "color",
  "rental.variation_value": "black",
  "rental.inventory_units": [
    {
      "unit_id": "TABLE-001-BLK-01",
      "status": "available",
      "location": "Tokyo",
      "last_maintenance_date": "2024-05-01",
      "notes": "新品同様の状態"
    },
    {
      "unit_id": "TABLE-001-BLK-02",
      "status": "maintenance",
      "location": "Tokyo",
      "last_maintenance_date": "2024-04-15",
      "notes": "脚部の修理中"
    }
  ]
}
```

#### 2. データベーススキーマ

**Prismaスキーマ**:

```prisma
// prisma/schema.prisma

model InventoryUnit {
  id                String            @id @default(cuid())
  shop              String
  shopifyProductId  String            // Shopify商品ID
  unitId            String            // 在庫ユニットID（例: TABLE-001-BLK-01）
  status            String            @default("available") // available, maintenance, damaged, unavailable
  location          String?
  lastMaintenanceDate DateTime?
  notes             String?
  
  // リレーション
  maintenanceLogs   MaintenanceLog[]
  inventoryCalendar InventoryCalendar[]
  
  // タイムスタンプ
  createdAt         DateTime          @default(now()) @map("created_at")
  updatedAt         DateTime          @updatedAt @map("updated_at")

  @@unique([shop, unitId])
  @@index([shopifyProductId])
  @@map("inventory_units")
}

model InventoryCalendar {
  id                String          @id @default(cuid())
  shop              String
  shopifyProductId  String          // Shopify商品ID
  unitId            String          // 在庫ユニットID
  date              DateTime        // 対象日
  isAvailable       Boolean         @default(true) // 利用可能フラグ
  unavailableReason String?         // 利用不可の理由（予約中、メンテナンス中など）
  
  // リレーション
  inventoryUnit     InventoryUnit   @relation(fields: [shop, unitId], references: [shop, unitId])

  // タイムスタンプ
  createdAt         DateTime        @default(now()) @map("created_at")
  updatedAt         DateTime        @updatedAt @map("updated_at")

  @@unique([shop, shopifyProductId, unitId, date])
  @@index([shop, date])
  @@map("inventory_calendar")
}

model MaintenanceLog {
  id                String          @id @default(cuid())
  shop              String
  unitId            String
  type              String          // regular, repair, cleaning など
  startDate         DateTime
  endDate           DateTime?
  status            String          @default("scheduled") // scheduled, in_progress, completed, cancelled
  notes             String?
  performedBy       String?
  
  // リレーション
  inventoryUnit     InventoryUnit   @relation(fields: [shop, unitId], references: [shop, unitId])
  
  // タイムスタンプ
  createdAt         DateTime        @default(now()) @map("created_at")
  updatedAt         DateTime        @updatedAt @map("updated_at")

  @@index([shop, unitId])
  @@index([startDate, endDate])
  @@map("maintenance_logs")
}
```

## 実装手順

### 1. メタフィールド定義の更新

**スクリプト実装**:

```javascript
// scripts/update-metafield-definitions.js

const { Shopify } = require('@shopify/shopify-api');

async function updateMetafieldDefinitions() {
  const client = new Shopify.Clients.Rest(shop, accessToken);
  
  // 在庫ユニット用のメタフィールド定義
  await client.post({
    path: 'metafield_definitions',
    data: {
      metafield_definition: {
        name: "在庫ユニット",
        namespace: "rental",
        key: "inventory_units",
        type: "json",
        description: "商品の物理的な在庫ユニットとその状態",
        ownerType: "PRODUCT"
      },
    },
  });
  
  // シリーズコード用のメタフィールド定義
  await client.post({
    path: 'metafield_definitions',
    data: {
      metafield_definition: {
        name: "シリーズコード",
        namespace: "rental",
        key: "series_code",
        type: "single_line_text_field",
        description: "商品シリーズを識別するコード",
        ownerType: "PRODUCT"
      },
    },
  });
  
  // バリエーションタイプ用のメタフィールド定義
  await client.post({
    path: 'metafield_definitions',
    data: {
      metafield_definition: {
        name: "バリエーションタイプ",
        namespace: "rental",
        key: "variation_type",
        type: "single_line_text_field",
        description: "バリエーションの種類（色、サイズなど）",
        ownerType: "PRODUCT"
      },
    },
  });
  
  // バリエーション値用のメタフィールド定義
  await client.post({
    path: 'metafield_definitions',
    data: {
      metafield_definition: {
        name: "バリエーション値",
        namespace: "rental",
        key: "variation_value",
        type: "single_line_text_field",
        description: "バリエーションの値（ブラック、Lサイズなど）",
        ownerType: "PRODUCT"
      },
    },
  });
  
  // マスター商品フラグ用のメタフィールド定義
  await client.post({
    path: 'metafield_definitions',
    data: {
      metafield_definition: {
        name: "マスター商品",
        namespace: "rental",
        key: "is_master",
        type: "boolean",
        description: "商品グループのマスター商品かどうか",
        ownerType: "PRODUCT"
      },
    },
  });
  
  console.log('メタフィールド定義を更新しました');
}

updateMetafieldDefinitions().catch(console.error);
```

### 2. 在庫ユニット管理APIの実装

**在庫ユニット更新API**:

```typescript
// app/routes/api.inventory-units.update.ts

import { ActionArgs, json } from "@remix-run/node";
import { requireAuthenticatedUser } from "~/services/auth.server";
import { updateInventoryUnitStatus } from "~/services/inventory.server";
import { prisma } from "~/services/db.server";

export async function action({ request }: ActionArgs) {
  const { user, shop } = await requireAuthenticatedUser(request);
  
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }
  
  try {
    const { unitId, status, notes } = await request.json();
    
    if (!unitId || !status) {
      return json({ error: "unitId and status are required" }, { status: 400 });
    }
    
    // 有効なステータス値かチェック
    const validStatuses = ["available", "maintenance", "damaged", "unavailable"];
    if (!validStatuses.includes(status)) {
      return json({ error: "Invalid status value" }, { status: 400 });
    }
    
    // 在庫ユニットの存在確認
    const unit = await prisma.inventoryUnit.findUnique({
      where: { 
        shop_unitId: {
          shop,
          unitId
        }
      }
    });
    
    if (!unit) {
      return json({ error: "Inventory unit not found" }, { status: 404 });
    }
    
    // 在庫ユニットのステータスを更新
    const updatedUnit = await updateInventoryUnitStatus(shop, unitId, status, notes);
    
    return json({ success: true, unit: updatedUnit });
  } catch (error) {
    console.error("Failed to update inventory unit:", error);
    return json({ error: "Failed to update inventory unit" }, { status: 500 });
  }
}
```

**在庫ユニット管理サービス**:

```typescript
// app/services/inventory.server.ts

import { prisma } from "~/services/db.server";
import { updateProductMetafield } from "~/services/shopify.server";

/**
 * 在庫ユニットのステータスを更新する
 */
export async function updateInventoryUnitStatus(
  shop: string,
  unitId: string,
  status: string,
  notes?: string
) {
  // トランザクション内で処理
  return await prisma.$transaction(async (tx) => {
    // 1. データベース上の在庫ユニットを更新
    const updatedUnit = await tx.inventoryUnit.update({
      where: {
        shop_unitId: {
          shop,
          unitId
        }
      },
      data: {
        status,
        notes: notes || undefined,
        lastMaintenanceDate: status === "available" ? new Date() : undefined,
        updatedAt: new Date()
      }
    });
    
    // 2. Shopifyメタフィールドの在庫ユニット情報も更新
    await updateInventoryUnitInShopify(
      shop,
      updatedUnit.shopifyProductId,
      unitId,
      status,
      notes
    );
    
    // 3. 在庫カレンダーも更新
    if (status !== "available") {
      // 利用不可の場合は、今後30日間の予約を不可に
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + 30);
      
      await updateInventoryCalendar(
        shop,
        updatedUnit.shopifyProductId,
        unitId,
        status,
        startDate,
        endDate
      );
    }
    
    // 4. メンテナンスログを作成（メンテナンス開始または完了の場合）
    if (status === "maintenance" || 
        (updatedUnit.status === "maintenance" && status === "available")) {
      await createMaintenanceLogEntry(
        shop,
        unitId,
        status === "maintenance" ? "start" : "complete",
        notes
      );
    }
    
    return updatedUnit;
  });
}

/**
 * Shopifyメタフィールドの在庫ユニット情報を更新
 */
async function updateInventoryUnitInShopify(
  shop: string,
  productId: string,
  unitId: string,
  status: string,
  notes?: string
) {
  // 1. 現在のメタフィールド値を取得
  const metafields = await getProductMetafields(shop, productId);
  const inventoryUnitsField = metafields.find(
    m => m.namespace === "rental" && m.key === "inventory_units"
  );
  
  let inventoryUnits = [];
  if (inventoryUnitsField && inventoryUnitsField.value) {
    try {
      inventoryUnits = JSON.parse(inventoryUnitsField.value);
    } catch (error) {
      console.error("Failed to parse inventory_units metafield:", error);
      inventoryUnits = [];
    }
  }
  
  // 2. 在庫ユニット情報を更新
  const unitIndex = inventoryUnits.findIndex(unit => unit.unit_id === unitId);
  
  if (unitIndex >= 0) {
    // 既存のユニットを更新
    inventoryUnits[unitIndex] = {
      ...inventoryUnits[unitIndex],
      status,
      notes: notes || inventoryUnits[unitIndex].notes,
      last_update: new Date().toISOString().split("T")[0],
      ...(status === "available" && inventoryUnits[unitIndex].status === "maintenance"
        ? { last_maintenance_date: new Date().toISOString().split("T")[0] }
        : {})
    };
  } else {
    // 新しいユニットを追加
    inventoryUnits.push({
      unit_id: unitId,
      status,
      notes: notes || "",
      last_update: new Date().toISOString().split("T")[0],
      ...(status === "available"
        ? { last_maintenance_date: new Date().toISOString().split("T")[0] }
        : {})
    });
  }
  
  // 3. メタフィールドを更新
  await updateProductMetafield(
    shop,
    productId,
    "rental",
    "inventory_units",
    JSON.stringify(inventoryUnits),
    "json"
  );
}

/**
 * 在庫カレンダーを更新
 */
async function updateInventoryCalendar(
  shop: string,
  productId: string,
  unitId: string,
  status: string,
  startDate: Date,
  endDate: Date
) {
  // 日付範囲を生成
  const dates = [];
  const currentDate = new Date(startDate);
  while (currentDate <= endDate) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  // 各日付について在庫カレンダーを更新
  const operations = dates.map(date => ({
    where: {
      shop_shopifyProductId_unitId_date: {
        shop,
        shopifyProductId: productId,
        unitId,
        date: new Date(date.setHours(0, 0, 0, 0))
      }
    },
    create: {
      shop,
      shopifyProductId: productId,
      unitId,
      date: new Date(date.setHours(0, 0, 0, 0)),
      isAvailable: status === "available",
      unavailableReason: status === "available" ? null : status
    },
    update: {
      isAvailable: status === "available",
      unavailableReason: status === "available" ? null : status
    }
  }));
  
  // バルクアップサート実行
  for (const operation of operations) {
    await prisma.inventoryCalendar.upsert(operation);
  }
}

/**
 * メンテナンスログを作成
 */
async function createMaintenanceLogEntry(
  shop: string,
  unitId: string,
  action: "start" | "complete",
  notes?: string
) {
  if (action === "start") {
    // メンテナンス開始ログを作成
    await prisma.maintenanceLog.create({
      data: {
        shop,
        unitId,
        type: "general",
        startDate: new Date(),
        status: "in_progress",
        notes: notes || "メンテナンス開始"
      }
    });
  } else {
    // 進行中のメンテナンスログを更新
    const activeMaintenance = await prisma.maintenanceLog.findFirst({
      where: {
        shop,
        unitId,
        status: "in_progress",
        endDate: null
      },
      orderBy: {
        startDate: "desc"
      }
    });
    
    if (activeMaintenance) {
      await prisma.maintenanceLog.update({
        where: { id: activeMaintenance.id },
        data: {
          endDate: new Date(),
          status: "completed",
          notes: notes ? `${activeMaintenance.notes}\n\n${notes}` : activeMaintenance.notes
        }
      });
    }
  }
}
```

### 3. データベースマイグレーション

**マイグレーションスクリプト**:

```bash
# マイグレーションの作成
npx prisma migrate dev --name add_inventory_unit_management

# データベースの更新
npx prisma migrate deploy
```

### 4. フロントエンド実装

**商品詳細ページ実装**:

```tsx
// app/routes/products.$handle.tsx

import { json, LoaderArgs } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { useEffect, useState } from "react";
import { ProductVariantSelector } from "~/components/ProductVariantSelector";
import { DateRangePicker } from "~/components/DateRangePicker";
import { getProductByHandle, getRelatedVariants } from "~/services/shopify.server";
import { checkAvailabilityForDateRange } from "~/services/inventory.server";

export async function loader({ params, request }: LoaderArgs) {
  const { handle } = params;
  if (!handle) {
    throw new Response("Not Found", { status: 404 });
  }
  
  // 商品情報を取得
  const product = await getProductByHandle(handle);
  if (!product) {
    throw new Response("Not Found", { status: 404 });
  }
  
  // シリーズコードを取得
  const seriesCode = product.metafields?.find(
    m => m.namespace === "rental" && m.key === "series_code"
  )?.value;
  
  // 関連バリエーションを取得
  const relatedVariants = seriesCode 
    ? await getRelatedVariants(seriesCode, product.id)
    : [];
  
  // 在庫ユニット情報を取得
  const inventoryUnitsField = product.metafields?.find(
    m => m.namespace === "rental" && m.key === "inventory_units"
  );
  
  let inventoryUnits = [];
  if (inventoryUnitsField?.value) {
    try {
      inventoryUnits = JSON.parse(inventoryUnitsField.value);
    } catch (error) {
      console.error("Failed to parse inventory_units:", error);
    }
  }
  
  return json({
    product,
    relatedVariants,
    inventoryUnits,
    seriesCode
  });
}

export default function ProductDetails() {
  const { product, relatedVariants, inventoryUnits } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  
  // 状態管理
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [selectedDates, setSelectedDates] = useState({ start: null, end: null });
  const [rentalDays, setRentalDays] = useState(0);
  const [isAvailable, setIsAvailable] = useState(false);
  const [selectedRentalVariant, setSelectedRentalVariant] = useState(null);
  const [price, setPrice] = useState(0);
  
  // 商品バリエーションの初期選択
  useEffect(() => {
    if (relatedVariants.length > 0) {
      setSelectedVariant(relatedVariants[0]);
    } else {
      setSelectedVariant(product);
    }
  }, [product, relatedVariants]);
  
  // 日付選択の処理
  const handleDateSelection = async (start, end) => {
    setSelectedDates({ start, end });
    
    if (!start || !end || !selectedVariant) return;
    
    // レンタル日数を計算
    const days = calculateRentalDays(start, end);
    setRentalDays(days);
    
    // 在庫の利用可能状況をチェック
    const availableUnits = await checkAvailability(
      selectedVariant.id,
      start,
      end
    );
    
    setIsAvailable(availableUnits.length > 0);
    
    // レンタル日数に応じたバリエーションを選択
    const variantKey = days <= 7 ? `${days}day` : "8plus";
    const rentalVariant = selectedVariant.variants.find(
      v => v.title.includes(variantKey)
    );
    
    setSelectedRentalVariant(rentalVariant);
    
    // 価格を計算
    let totalPrice = parseFloat(rentalVariant?.price || "0");
    
    // 8日以上の場合は追加料金を計算
    if (days > 7) {
      const basePrice = parseFloat(product.metafields?.find(
        m => m.namespace === "rental" && m.key === "pricing"
      )?.value?.basePrice || "0");
      
      const additionalDays = days - 7;
      const additionalFee = additionalDays * (basePrice * 0.1);
      totalPrice += additionalFee;
    }
    
    setPrice(totalPrice);
  };
  
  // バリエーション選択の処理
  const handleVariantSelection = (variant) => {
    setSelectedVariant(variant);
    
    // 日付が選択されている場合は在庫チェックを再実行
    if (selectedDates.start && selectedDates.end) {
      handleDateSelection(selectedDates.start, selectedDates.end);
    }
  };
  
  // カートに追加
  const addToCart = async () => {
    if (!selectedRentalVariant || !selectedDates.start || !selectedDates.end) {
      return;
    }
    
    try {
      const response = await fetch("/cart/add.js", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          items: [{
            id: selectedRentalVariant.id,
            quantity: 1,
            properties: {
              "開始日": selectedDates.start.toISOString().split("T")[0],
              "終了日": selectedDates.end.toISOString().split("T")[0],
              "レンタル日数": rentalDays,
              "追加日数料金": rentalDays > 7 ? `${(rentalDays - 7) * (product.basePrice * 0.1)}` : "0"
            }
          }]
        })
      });
      
      if (response.ok) {
        navigate("/cart");
      } else {
        console.error("Failed to add to cart:", await response.json());
        alert("カートに追加できませんでした。");
      }
    } catch (error) {
      console.error("Failed to add to cart:", error);
      alert("カートに追加できませんでした。");
    }
  };
  
  // レンタル日数計算関数（営業日計算ロジックを含む）
  const calculateRentalDays = (start, end) => {
    // 営業日計算ロジックの実装（仮の実装）
    const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
    return days;
  };
  
  // 在庫チェック関数
  const checkAvailability = async (productId, start, end) => {
    try {
      const response = await fetch(`/api/check-availability?productId=${productId}&start=${start.toISOString()}&end=${end.toISOString()}`);
      const data = await response.json();
      return data.availableUnits || [];
    } catch (error) {
      console.error("Failed to check availability:", error);
      return [];
    }
  };
  
  // UI実装
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* 商品画像 */}
        <div>
          <img 
            src={selectedVariant?.featuredImage?.url || product.featuredImage?.url} 
            alt={selectedVariant?.title || product.title}
            className="w-full h-auto rounded-lg shadow"
          />
          
          {/* バリエーション画像サムネイル */}
          {relatedVariants.length > 0 && (
            <div className="mt-4 flex space-x-2 overflow-x-auto">
              {relatedVariants.map(variant => (
                <div 
                  key={variant.id}
                  className={`cursor-pointer border-2 rounded ${
                    selectedVariant?.id === variant.id ? 'border-blue-500' : 'border-gray-200'
                  }`}
                  onClick={() => handleVariantSelection(variant)}
                >
                  <img 
                    src={variant.featuredImage?.url} 
                    alt={variant.title}
                    className="w-16 h-16 object-cover"
                  />
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* 商品情報 */}
        <div>
          <h1 className="text-3xl font-bold">{product.title}</h1>
          <div className="mt-2 text-gray-700" dangerouslySetInnerHTML={{ __html: product.descriptionHtml }} />
          
          {/* バリエーション選択 */}
          {relatedVariants.length > 0 && (
            <div className="mt-6">
              <h2 className="text-lg font-semibold">バリエーション</h2>
              <ProductVariantSelector 
                variants={relatedVariants}
                selectedVariant={selectedVariant}
                onSelect={handleVariantSelection}
              />
            </div>
          )}
          
          {/* 日付選択 */}
          <div className="mt-6">
            <h2 className="text-lg font-semibold">レンタル期間</h2>
            <DateRangePicker
              onSelect={handleDateSelection}
              productId={selectedVariant?.id || product.id}
            />
            
            {selectedDates.start && selectedDates.end && (
              <div className="mt-2">
                <p>
                  選択期間: {selectedDates.start.toLocaleDateString()} 〜 {selectedDates.end.toLocaleDateString()}
                </p>
                <p>レンタル日数: {rentalDays}日</p>
              </div>
            )}
          </div>
          
          {/* 価格表示 */}
          {selectedRentalVariant && (
            <div className="mt-6">
              <h2 className="text-lg font-semibold">レンタル料金</h2>
              <div className="mt-2 p-4 border rounded bg-gray-50">
                <p className="text-2xl font-bold">¥{price.toLocaleString()}</p>
                <p className="text-sm text-gray-600">{rentalDays}日間レンタル</p>
                
                {rentalDays > 7 && (
                  <div className="mt-2 pt-2 border-t">
                    <p className="text-sm">
                      内訳: 7日間 ¥{parseFloat(selectedRentalVariant.price).toLocaleString()} + 
                      追加 {rentalDays - 7}日 ¥{(price - parseFloat(selectedRentalVariant.price)).toLocaleString()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
          
          {/* 在庫状況 */}
          <div className="mt-4">
            <p className={`${isAvailable ? 'text-green-600' : 'text-red-500'}`}>
              在庫状況: {isAvailable ? '利用可能' : '在庫なし'}
            </p>
          </div>
          
          {/* カートに追加ボタン */}
          <div className="mt-6">
            <button 
              className="w-full bg-blue-600 text-white py-3 rounded-lg font-bold hover:bg-blue-700 transition disabled:bg-gray-400"
              disabled={!isAvailable || !selectedRentalVariant}
              onClick={addToCart}
            >
              カートに追加
            </button>
          </div>
        </div>
      </div>
      
      {/* 商品詳細情報 */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-4">商品詳細</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold">仕様</h3>
            <div className="mt-2">
              {/* 仕様情報を表示 */}
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold">レンタル条件</h3>
            <div className="mt-2">
              {/* レンタル条件を表示 */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
```

### 5. 管理画面実装

**在庫ユニット管理画面**:

```tsx
// app/routes/admin.inventory.tsx

import { json, LoaderArgs } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { useState } from "react";
import { InventoryUnitList } from "~/components/InventoryUnitList";
import { QRScanner } from "~/components/QRScanner";
import { requireAuthenticatedUser } from "~/services/auth.server";
import { getAllInventoryUnits, searchInventoryUnits } from "~/services/inventory.server";

export async function loader({ request }: LoaderArgs) {
  const { user, shop } = await requireAuthenticatedUser(request);
  
  const url = new URL(request.url);
  const search = url.searchParams.get("search") || "";
  const status = url.searchParams.get("status") || "";
  const page = parseInt(url.searchParams.get("page") || "1", 10);
  const limit = 50;
  
  let units;
  if (search) {
    units = await searchInventoryUnits(shop, search, status, page, limit);
  } else {
    units = await getAllInventoryUnits(shop, status, page, limit);
  }
  
  return json({
    units,
    search,
    status,
    page
  });
}

export default function InventoryManagement() {
  const { units, search, status, page } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  
  const [searchTerm, setSearchTerm] = useState(search);
  const [selectedStatus, setSelectedStatus] = useState(status);
  const [showScanner, setShowScanner] = useState(false);
  const [scanResult, setScanResult] = useState("");
  
  // 検索実行
  const handleSearch = () => {
    const params = new URLSearchParams();
    if (searchTerm) params.set("search", searchTerm);
    if (selectedStatus) params.set("status", selectedStatus);
    navigate(`/admin/inventory?${params.toString()}`);
  };
  
  // QRコードスキャン結果の処理
  const handleScan = (result) => {
    if (result) {
      setScanResult(result);
      setSearchTerm(result);
      setShowScanner(false);
      // 自動的に検索を実行
      const params = new URLSearchParams();
      params.set("search", result);
      if (selectedStatus) params.set("status", selectedStatus);
      navigate(`/admin/inventory?${params.toString()}`);
    }
  };
  
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">在庫ユニット管理</h1>
      
      {/* 検索フォーム */}
      <div className="mb-6 flex flex-wrap items-center gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="ユニットID、商品名で検索..."
            className="w-full p-2 border border-gray-300 rounded"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={(e) => e.key === "Enter" && handleSearch()}
          />
        </div>
        
        <div>
          <select
            className="p-2 border border-gray-300 rounded"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="">すべてのステータス</option>
            <option value="available">利用可能</option>
            <option value="maintenance">メンテナンス中</option>
            <option value="damaged">破損</option>
            <option value="unavailable">利用不可</option>
          </select>
        </div>
        
        <button
          className="bg-blue-500 text-white px-4 py-2 rounded"
          onClick={handleSearch}
        >
          検索
        </button>
        
        <button
          className="bg-green-500 text-white px-4 py-2 rounded flex items-center"
          onClick={() => setShowScanner(true)}
        >
          <span className="mr-2">QRスキャン</span>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 2V5h1v1H5zm-2 7a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1v-3zm2 2v-1h1v1H5zm7-12a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1V4a1 1 0 00-1-1h-3zm0 2h1v1h-1V5zm0 10a1 1 0 001 1h3a1 1 0 001-1v-3a1 1 0 00-1-1h-3a1 1 0 00-1 1v3zm2-2h1v1h-1v-1z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
      
      {/* QRコードスキャナー */}
      {showScanner && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg w-96">
            <h2 className="text-xl font-bold mb-4">QRコードをスキャン</h2>
            <QRScanner onScan={handleScan} />
            <button 
              className="mt-4 bg-gray-500 text-white px-4 py-2 rounded"
              onClick={() => setShowScanner(false)}
            >
              キャンセル
            </button>
          </div>
        </div>
      )}
      
      {/* 在庫ユニット一覧 */}
      <InventoryUnitList
        units={units}
        onStatusChange={(unitId, newStatus, notes) => {
          // 状態変更API呼び出し
          fetch("/api/inventory-units/update", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ unitId, status: newStatus, notes })
          }).then(response => {
            if (response.ok) {
              // 成功時は一覧を再読み込み
              navigate(`/admin/inventory?search=${searchTerm}&status=${selectedStatus}`);
            } else {
              alert("更新に失敗しました");
            }
          }).catch(error => {
            console.error("Failed to update status:", error);
            alert("更新に失敗しました");
          });
        }}
      />
      
      {/* ページネーション */}
      <div className="mt-6 flex justify-center">
        <div className="flex space-x-2">
          <button
            className="px-4 py-2 border rounded disabled:opacity-50"
            disabled={page <= 1}
            onClick={() => {
              const params = new URLSearchParams();
              if (searchTerm) params.set("search", searchTerm);
              if (selectedStatus) params.set("status", selectedStatus);
              params.set("page", String(page - 1));
              navigate(`/admin/inventory?${params.toString()}`);
            }}
          >
            前へ
          </button>
          <button
            className="px-4 py-2 border rounded disabled:opacity-50"
            disabled={units.length < 50}
            onClick={() => {
              const params = new URLSearchParams();
              if (searchTerm) params.set("search", searchTerm);
              if (selectedStatus) params.set("status", selectedStatus);
              params.set("page", String(page + 1));
              navigate(`/admin/inventory?${params.toString()}`);
            }}
          >
            次へ
          </button>
        </div>
      </div>
    </div>
  );
}
```

## データ移行戦略

### 1. バックアップの作成

```javascript
// scripts/backup-product-data.js

const fs = require('fs');
const { Shopify } = require('@shopify/shopify-api');

async function backupProductData() {
  const client = new Shopify.Clients.Rest(shop, accessToken);
  
  // すべての商品を取得
  let allProducts = [];
  let params = { limit: 250 };
  
  do {
    const response = await client.get({
      path: 'products',
      query: params
    });
    
    allProducts = [...allProducts, ...response.body.products];
    
    // リンクヘッダーからnext情報を取得
    if (response.headers.get('Link')) {
      const match = response.headers.get('Link').match(/<([^>]+)>; rel="next"/);
      if (match) {
        const url = new URL(match[1]);
        params = { page_info: url.searchParams.get('page_info') };
      } else {
        params = null;
      }
    } else {
      params = null;
    }
  } while (params);
  
  // メタフィールドを取得して商品データに追加
  for (const product of allProducts) {
    const metafieldsResponse = await client.get({
      path: `products/${product.id}/metafields`
    });
    
    product.metafields = metafieldsResponse.body.metafields;
  }
  
  // バックアップファイルに保存
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  fs.writeFileSync(
    `./backups/products-${timestamp}.json`,
    JSON.stringify(allProducts, null, 2)
  );
  
  console.log(`${allProducts.length}件の商品データをバックアップしました`);
}

backupProductData().catch(console.error);
```

### 2. 在庫ユニットデータの作成

```javascript
// scripts/create-inventory-units.js

const { Shopify } = require('@shopify/shopify-api');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createInventoryUnits() {
  const client = new Shopify.Clients.Rest(shop, accessToken);
  
  // すべての商品を取得
  let allProducts = [];
  let params = { limit: 250 };
  
  do {
    const response = await client.get({
      path: 'products',
      query: params
    });
    
    allProducts = [...allProducts, ...response.body.products];
    
    // リンクヘッダーからnext情報を取得
    if (response.headers.get('Link')) {
      const match = response.headers.get('Link').match(/<([^>]+)>; rel="next"/);
      if (match) {
        const url = new URL(match[1]);
        params = { page_info: url.searchParams.get('page_info') };
      } else {
        params = null;
      }
    } else {
      params = null;
    }
  } while (params);
  
  // 各商品のメタフィールドを取得し、在庫ユニットを作成
  for (const product of allProducts) {
    // メタフィールドを取得
    const metafieldsResponse = await client.get({
      path: `products/${product.id}/metafields`
    });
    
    const metafields = metafieldsResponse.body.metafields;
    
    // レンタル状態メタフィールドを探す
    const statusField = metafields.find(
      m => m.key === 'レンタル状態' || 
           (m.namespace === 'rental' && m.key === 'status')
    );
    
    // 在庫ユニットIDを生成
    const productCode = metafields.find(
      m => m.namespace === 'rental' && m.key === 'basic_info'
    )?.value?.productCode || product.handle;
    
    const unitId = `${productCode}-01`;
    
    // 在庫ユニットをShopifyメタフィールドに追加
    const inventoryUnits = [{
      unit_id: unitId,
      status: statusField?.value || 'available',
      location: metafields.find(
        m => m.key === '保管場所' ||
             (m.namespace === 'rental' && m.key === 'location')
      )?.value || 'default',
      last_maintenance_date: new Date().toISOString().split('T')[0],
      notes: ""
    }];
    
    // inventory_unitsメタフィールドを更新または作成
    await client.post({
      path: `products/${product.id}/metafields`,
      data: {
        metafield: {
          namespace: 'rental',
          key: 'inventory_units',
          value: JSON.stringify(inventoryUnits),
          type: 'json'
        }
      }
    });
    
    // データベースにも在庫ユニットを作成
    await prisma.inventoryUnit.create({
      data: {
        shop,
        shopifyProductId: product.id,
        unitId,
        status: statusField?.value || 'available',
        location: metafields.find(
          m => m.key === '保管場所' ||
               (m.namespace === 'rental' && m.key === 'location')
        )?.value || 'default',
        lastMaintenanceDate: new Date(),
        notes: ""
      }
    });
    
    console.log(`商品 ${product.title} (${product.id}) の在庫ユニットを作成しました`);
  }
  
  console.log(`${allProducts.length}件の商品の在庫ユニットを作成しました`);
}

createInventoryUnits().catch(console.error);
```

## テスト戦略

### 1. 単体テスト

```javascript
// tests/inventory-service.test.js

const { updateInventoryUnitStatus } = require('~/services/inventory.server');
const { prisma } = require('~/services/db.server');
const { updateProductMetafield } = require('~/services/shopify.server');

// モック
jest.mock('~/services/db.server', () => ({
  prisma: {
    $transaction: jest.fn(callback => callback({
      inventoryUnit: {
        update: jest.fn(),
        findUnique: jest.fn()
      },
      inventoryCalendar: {
        upsert: jest.fn()
      },
      maintenanceLog: {
        create: jest.fn(),
        findFirst: jest.fn(),
        update: jest.fn()
      }
    }))
  }
}));

jest.mock('~/services/shopify.server', () => ({
  updateProductMetafield: jest.fn(),
  getProductMetafields: jest.fn()
}));

describe('Inventory Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('updateInventoryUnitStatus should update unit status and Shopify metafield', async () => {
    // モックの準備
    const mockUnit = {
      id: 'unit1',
      shopifyProductId: 'product1',
      status: 'available',
      unitId: 'TABLE-001-01'
    };
    
    prisma.$transaction.mockImplementation(callback => callback({
      inventoryUnit: {
        update: jest.fn().mockResolvedValue(mockUnit),
        findUnique: jest.fn().mockResolvedValue(mockUnit)
      },
      inventoryCalendar: {
        upsert: jest.fn()
      },
      maintenanceLog: {
        create: jest.fn(),
        findFirst: jest.fn(),
        update: jest.fn()
      }
    }));
    
    // 関数を実行
    const result = await updateInventoryUnitStatus(
      'test-shop',
      'TABLE-001-01',
      'maintenance',
      'メンテナンス開始'
    );
    
    // 期待する結果
    expect(result).toEqual(mockUnit);
    expect(prisma.$transaction).toHaveBeenCalled();
    expect(updateProductMetafield).toHaveBeenCalled();
  });
});
```

### 2. 統合テスト

```javascript
// tests/integration/inventory-management.test.js

const request = require('supertest');
const app = require('~/app.server');
const { prisma } = require('~/services/db.server');

describe('Inventory Management API', () => {
  let testUnit;
  
  beforeAll(async () => {
    // テスト用の在庫ユニットを作成
    testUnit = await prisma.inventoryUnit.create({
      data: {
        shop: 'test-shop',
        shopifyProductId: 'test-product',
        unitId: 'TEST-UNIT-001',
        status: 'available',
        location: 'test-location',
        lastMaintenanceDate: new Date(),
        notes: 'Test unit'
      }
    });
  });
  
  afterAll(async () => {
    // テストデータをクリーンアップ
    await prisma.inventoryUnit.delete({
      where: { id: testUnit.id }
    });
  });
  
  test('GET /api/inventory-units should return inventory units', async () => {
    const response = await request(app)
      .get('/api/inventory-units')
      .set('Authorization', `Bearer ${testToken}`);
    
    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('units');
    expect(Array.isArray(response.body.units)).toBe(true);
  });
  
  test('POST /api/inventory-units/update should update unit status', async () => {
    const response = await request(app)
      .post('/api/inventory-units/update')
      .set('Authorization', `Bearer ${testToken}`)
      .send({
        unitId: testUnit.unitId,
        status: 'maintenance',
        notes: 'Testing maintenance'
      });
    
    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('success', true);
    expect(response.body).toHaveProperty('unit');
    expect(response.body.unit.status).toBe('maintenance');
    
    // データベースの状態も確認
    const updatedUnit = await prisma.inventoryUnit.findUnique({
      where: { id: testUnit.id }
    });
    
    expect(updatedUnit.status).toBe('maintenance');
    expect(updatedUnit.notes).toBe('Testing maintenance');
  });
});
```

## デプロイメント戦略

### 1. ステージングデプロイ

```bash
# ステージング環境へのデプロイ
npm run deploy:staging

# データベースマイグレーション
npx prisma migrate deploy --preview-feature
```

### 2. 本番デプロイ前チェックリスト

1. バックアップを作成済みか
2. 単体テストがパスしているか
3. 統合テストがパスしているか
4. データ移行スクリプトが正常に動作するか
5. パフォーマンステストを実施済みか
6. ロールバック手順を準備済みか

### 3. 本番デプロイ

```bash
# データベースのバックアップ
npm run backup:database

# 商品データのバックアップ
npm run backup:products

# デプロイ
npm run deploy:production

# データベースマイグレーション
npx prisma migrate deploy

# データ移行スクリプト実行
npm run migrate:inventory-units
```

## トラブルシューティングガイド

### 1. データ同期エラーの対応

データベースとShopifyメタフィールドの間で同期エラーが発生した場合：

1. 同期ログを確認
2. 不整合があるデータを特定
3. 以下のスクリプトを実行して修復

```javascript
// scripts/fix-data-inconsistency.js

const { Shopify } = require('@shopify/shopify-api');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixDataInconsistency(shop, unitId) {
  try {
    // 1. データベースから情報を取得
    const unit = await prisma.inventoryUnit.findUnique({
      where: {
        shop_unitId: {
          shop,
          unitId
        }
      }
    });
    
    if (!unit) {
      console.error(`Unit ${unitId} not found in database`);
      return;
    }
    
    // 2. Shopifyから情報を取得
    const client = new Shopify.Clients.Rest(shop, accessToken);
    
    const metafieldsResponse = await client.get({
      path: `products/${unit.shopifyProductId}/metafields`
    });
    
    const inventoryUnitsField = metafieldsResponse.body.metafields.find(
      m => m.namespace === 'rental' && m.key === 'inventory_units'
    );
    
    let inventoryUnits = [];
    if (inventoryUnitsField?.value) {
      try {
        inventoryUnits = JSON.parse(inventoryUnitsField.value);
      } catch (error) {
        console.error('Failed to parse inventory_units metafield:', error);
      }
    }
    
    // 3. 不整合を修正
    const unitIndex = inventoryUnits.findIndex(u => u.unit_id === unitId);
    
    if (unitIndex >= 0) {
      // Shopifyメタフィールドの情報をデータベースに合わせて更新
      inventoryUnits[unitIndex] = {
        unit_id: unit.unitId,
        status: unit.status,
        location: unit.location,
        last_maintenance_date: unit.lastMaintenanceDate?.toISOString().split('T')[0],
        notes: unit.notes || '',
        last_update: new Date().toISOString().split('T')[0]
      };
      
      await client.post({
        path: `products/${unit.shopifyProductId}/metafields`,
        data: {
          metafield: {
            namespace: 'rental',
            key: 'inventory_units',
            value: JSON.stringify(inventoryUnits),
            type: 'json'
          }
        }
      });
      
      console.log(`Unit ${unitId} synced to Shopify`);
    } else {
      // ユニットがShopifyにない場合は追加
      inventoryUnits.push({
        unit_id: unit.unitId,
        status: unit.status,
        location: unit.location,
        last_maintenance_date: unit.lastMaintenanceDate?.toISOString().split('T')[0],
        notes: unit.notes || '',
        last_update: new Date().toISOString().split('T')[0]
      });
      
      await client.post({
        path: `products/${unit.shopifyProductId}/metafields`,
        data: {
          metafield: {
            namespace: 'rental',
            key: 'inventory_units',
            value: JSON.stringify(inventoryUnits),
            type: 'json'
          }
        }
      });
      
      console.log(`Unit ${unitId} added to Shopify`);
    }
    
    console.log(`Fixed data inconsistency for unit ${unitId}`);
  } catch (error) {
    console.error(`Failed to fix data inconsistency for unit ${unitId}:`, error);
  }
}

// 使用例: node fix-data-inconsistency.js your-shop-domain TABLE-001-01
const [,, shop, unitId] = process.argv;
if (shop && unitId) {
  fixDataInconsistency(shop, unitId).finally(() => prisma.$disconnect());
} else {
  console.error('Usage: node fix-data-inconsistency.js SHOP_DOMAIN UNIT_ID');
}
```

### 2. API制限対応

APIレート制限に達した場合の対応：

```javascript
// services/shopify.server.ts
// レート制限に対応するための再試行ロジック

export async function retryableRequest(requestFn, maxRetries = 3) {
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      return await requestFn();
    } catch (error) {
      if (error.statusCode === 429) { // レート制限
        retries++;
        
        // Retry-Afterヘッダーを確認
        const retryAfter = parseInt(error.headers['retry-after'] || '1', 10);
        console.log(`Rate limit reached. Waiting ${retryAfter} seconds before retry (${retries}/${maxRetries})`);
        
        // 待機
        await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
      } else {
        // その他のエラーは再スロー
        throw error;
      }
    }
  }
  
  throw new Error(`Max retries (${maxRetries}) exceeded`);
}
```

## 運用手順

### 1. 在庫ユニットの追加

新しい在庫ユニットを追加する際の手順：

1. 管理画面で商品を選択
2. 「在庫ユニット管理」タブを開く
3. 「新規ユニット追加」ボタンをクリック
4. ユニットID、場所、ステータスなどを入力
5. 保存ボタンをクリック

### 2. QRコード生成

在庫ユニット用のQRコードを生成する手順：

1. 管理画面で在庫ユニット一覧を表示
2. QRコードを生成したいユニットを選択
3. 「QRコード生成」ボタンをクリック
4. QRコードをPDF形式でダウンロード
5. QRコードを印刷して商品に貼付

### 3. 商品返却時のプロセス

商品が返却された際の処理手順：

1. QRコードをスキャン
2. 商品の状態を確認
3. 問題なければ「available」に設定
4. メンテナンスが必要であれば「maintenance」に設定
5. 備考欄にメンテナンス内容などを記録
6. 保存ボタンをクリック

## まとめ

この実装ガイドでは、Shopifyレンタル商品ECサイトにおける「商品バリエーション方式」と「在庫ユニット管理」の詳細な実装方法を説明しました。主な改善点は以下の通りです：

1. 物理的な在庫ユニットの個別管理によるより細かな状態管理
2. 階層的な商品構造による実バリエーションとレンタル日数バリエーションの両立
3. 統合ユーザーインターフェースによる直感的な操作性
4. データ同期メカニズムによる一貫性の確保
5. 管理画面の導入によるオペレーション効率の向上

この実装方式は、シンプルな商品管理から高度な在庫管理まで対応可能な柔軟な設計となっています。実装にあたっては、段階的なアプローチを取り、各フェーズでの検証を念入りに行うことが推奨されます。