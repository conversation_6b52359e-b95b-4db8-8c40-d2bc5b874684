{% include 'global-variables' %}
{%- if settings.product_format_pjpg -%}
    {%- assign image_format = 'pjpg' -%}
{%- else -%}
    {%- assign image_format = null -%}
{%- endif -%}
{%- assign product_margin_bottom = product_margin_bottom | default: 30 -%}
{% include 'product-res-variables' with url_without_collection: true %}
{%- assign image_size = image_size | default: '600x' -%}
{%- assign show_label_in_stock = show_label_in_stock | default: settings.product_collection_show_label_in_stock -%}
{%- assign show_label_pre_order = show_label_pre_order | default: settings.product_collection_show_label_pre_order -%}
{%- assign show_label_out_stock = show_label_out_stock | default: settings.product_collection_show_label_out_stock -%}
{%- assign show_label_sale = show_label_sale | default: settings.product_collection_show_label_sale -%}
{%- assign show_label_new = show_label_new | default: settings.product_collection_show_label_new -%}
{%- assign show_label_hot = show_label_hot | default: settings.product_collection_show_label_hot -%}
{%- assign show_countdown = show_countdown | default: settings.product_collection_show_countdown -%}
{%- assign show_images_navigation = show_images_navigation | default: settings.product_collection_show_images_navigation -%}
{%- assign show_title = show_title | default: settings.product_collection_show_title -%}
{%- assign show_sku = show_sku | default: settings.product_collection_show_sku -%}
{%- assign show_barcode = show_barcode | default: settings.product_collection_show_barcode -%}
{%- assign show_availability = show_availability | default: settings.product_collection_show_availability -%}
{%- assign show_type = show_type | default: settings.product_collection_show_type -%}
{%- assign show_vendor = show_vendor | default: settings.product_collection_show_vendor -%}
{%- assign show_description = show_description | default: settings.product_collection_show_description -%}
{%- assign show_price = show_price | default: settings.product_collection_show_price -%}
{%- assign show_options = show_options | default: settings.product_collection_show_options -%}
{%- assign show_custom_options = show_custom_options | default: settings.product_show_custom_options -%}
{%- assign show_quantity = show_quantity | default: settings.product_collection_show_quantity -%}
{%- assign show_button_add_to_cart = show_button_add_to_cart | default: settings.product_collection_show_button_add_to_cart -%}
{%- assign show_button_add_to_wishlist = show_button_add_to_wishlist | default: settings.product_collection_show_button_add_to_wishlist -%}
{%- assign show_button_add_to_compare = show_button_add_to_compare | default: settings.product_collection_show_button_add_to_compare -%}
{%- assign show_button_quick_view = show_button_quick_view | default: settings.product_collection_show_button_quick_view -%}
{%- assign show_reviews = show_reviews | default: settings.product_collection_show_reviews -%}
{%- assign hide_reviews_counter = hide_reviews_counter | default: settings.product_collection_hide_reviews_counter -%}
{%- assign hide_reviews_mobile_counter = hide_reviews_mobile_counter | default: settings.product_collection_hide_reviews_mobile_counter -%}
{%- assign show_list_quantity = show_list_quantity | default: settings.product_collection_list_show_quantity -%}
{%- if settings.compare_type == 'disable' -%}
    {%- assign show_button_add_to_compare = false -%}
{%- endif -%}
{%- if settings.wishlist_type == 'disable' -%}
    {%- assign show_button_add_to_wishlist = false -%}
{%- endif -%}
{%- if settings.reviews_type == 'disable' -%}
    {%- assign show_reviews = false -%}
{%- endif -%}
{%- if settings.product_collection_set_first_image_by_variant and current_variant.featured_image -%}
    {%- assign current_image = current_variant.featured_image -%}
    {%- for img in product.images -%}
        {%- if current_variant.featured_image == img -%}
            {%- assign next_img_index = forloop.index0 | plus: 1 -%}
            {%- if product.images[next_img_index] != blank -%}
                {%- assign hover_image = product.images[next_img_index] -%}
            {%- endif -%}
            {%- break -%}
        {%- endif -%}
    {%- endfor -%}
{%- else -%}
    {%- assign current_image = image -%}
{%- endif -%}
{%- assign img_hover_url = hover_image | img_url: '1x1', format: image_format | replace: '_1x1.', '_{width}x.' -%}
{%- assign product_form_id = 'quick-add-' | append: section.id | append: product.id -%}
<product-item class="product-collection{% if settings.product_collection_centered_info %} product-collection--centered{% endif %} d-block"{% include 'product-get-attributes' %} data-qv-check-change>
    <div class="product-collection__wrapper mb-30 mb-lg-{{ product_margin_bottom }}">
        <div class="d-flex flex-column">
            <div class="product-collection__image product-image product-image--hover-{{ settings.product_hover_animation_type }} position-relative w-100 js-product-images-navigation js-product-images-hovered-end{% if settings.product_replace_images_hover %} js-product-images-hover{% endif %}"{% if settings.product_replace_images_hover and hover_image != blank %} data-js-product-image-hover="{{ img_hover_url }}" data-js-product-image-hover-id="{{ hover_image.id }}"{% endif %}>
                {% render 'product-get-images' with url: url image: current_image image_size: image_size title: title %}
                {%- if show_label_in_stock or show_label_pre_order or show_label_out_stock or show_label_sale or show_label_new or show_label_hot -%}
                    {%- assign label_present = true -%}
                {%- else -%}
                    {%- assign label_present = false -%}
                {%- endif -%}
                {%- if label_present or show_button_quick_view -%}
                    <div class="product-image__overlay-top position-absolute d-flex flex-wrap top-0 left-0 w-100 px-10 pt-10">
                        <a href="{{ url }}" class="absolute-stretch cursor-default"></a>
                        {%- if label_present -%}
                            <div class="product-image__overlay-top-left product-collection__labels position-relative d-flex flex-column align-items-start mb-10">
                                {%- if show_label_hot -%}
                                    {% include 'product-get-label-hot' %}
                                {%- endif -%}
                                {%- if show_label_new -%}
                                    {% include 'product-get-label-new' %}
                                {%- endif -%}
                                {%- if show_label_sale -%}
                                    {% include 'product-get-label-sale' %}
                                {%- endif -%}
                                {%- if show_label_in_stock -%}
                                    {% include 'product-get-label-in-stock' %}
                                {%- endif -%}
                                {%- if show_label_pre_order -%}
                                    {% include 'product-get-label-pre-order' %}
                                {%- endif -%}
                                {%- if show_label_out_stock -%}
                                    {% include 'product-get-label-out-stock' %}
                                {%- endif -%}
                            </div>
                        {%- endif -%}
                        {%- if show_button_quick_view -%}
                            <div class="product-image__overlay-top-right product-collection__button-quick-view position-lg-relative d-none d-lg-flex mb-lg-10 ml-lg-auto">
                                {% render 'product-get-button-quick-view' with product_enable_tooltips: product_enable_tooltips %}
                            </div>
                        {%- endif -%}
                    </div>
                {%- endif -%}
                {%- if show_countdown or show_images_navigation -%}
                    <div class="product-image__overlay-bottom position-absolute d-flex justify-content-center justify-content-lg-start align-items-end bottom-0 left-0 w-100 px-10 pb-10">
                        <a href="{{ url }}" class="absolute-stretch cursor-default"></a>
                        {%- if show_countdown -%}
                            <div class="product-image__overlay-bottom-left product-collection__countdown position-relative mt-10">
                                {% render 'product-get-countdown' with countdown_date: countdown_date centered: centered %}
                            </div>
                        {%- endif -%}
                        {%- if show_images_navigation -%}
                            <div class="product-image__overlay-bottom-right product-collection__images-navigation position-relative d-none d-lg-block mt-10 ml-auto">
                                {% render 'product-get-images-navigation' with product: product %}
                            </div>
                        {%- endif -%}
                    </div>
                {%- endif -%}
            </div>
            <div class="product-collection__content d-flex flex-column align-items-start mt-15">
                {%- case settings.product_collection_more_info_type -%}
                    {% when 'type' %}
                        {%- if product.type != blank -%}
                            <div class="product-collection__more-info mb-{% if product_collection_content_offsets == 1 %}3{% else %}6{% endif %}">
                                <a href="{{ product.type | link_to_type | split: 'href="' | last | split: '"' | first }}">{{ product.type }}</a>
                            </div>
                        {%- endif -%}
                    {% when 'collections' %}
                        {%- if product.collections.size > 0 -%}
                            <div class="product-collection__more-info mb-{% if product_collection_content_offsets == 1 %}3{% else %}6{% endif %}">
                                {% render 'product-get-collections' with product: product %}
                            </div>
                        {%- endif -%}
                    {% when 'vendor' %}
                        {%- if product.vendor.size > 0 -%}
                            <div class="product-collection__more-info mb-{% if product_collection_content_offsets == 1 %}3{% else %}6{% endif %}">
                                {%- for vendor in product.vendor -%}
                                    <a href="{{ vendor | link_to_vendor | split: 'href="' | last | split: '"' | first }}">{{ vendor }}{% if forloop.last != true %},{% endif %}</a>{% if forloop.last != true %} {% endif %}
                                {%- endfor -%}
                            </div>
                        {%- endif -%}
                {%- endcase -%}
                {%- if show_title -%}
                    <div class="product-collection__title mb-{% if product_collection_content_offsets == 1 %}3{% else %}9{% endif %}">
                        <h4 class="m-0">
                            <a href="{{ url }}">{{ title }}</a>
                        </h4>
                    </div>
                {%- endif -%}

                <!-- Start of Judge.me code -->
                <div style='{{ jm_style }}' class='jdgm-widget jdgm-preview-badge' data-id='{{ product.id }}' data-auto-install='false'>
                {{ product.metafields.judgeme.badge }}
                </div>
                <!-- End of Judge.me code -->

                {% if settings.product_collection_show_metafield_sub_description %}
                    <p class="product-collection__sub-description my-5 ">
                    {{ product.metafields.custom.sub_description }}
                    </p>
                {% endif %}

                {%- if show_sku or show_barcode or show_availability or show_type or show_vendor -%}
                    <div class="product-collection__details d-none mb-8">
                        {%- if show_sku -%}
                            <div class="product-collection__sku{% if sku == blank %} d-none-important{% endif %} mb-5">
                                <p class="m-0" data-js-product-sku>{{ 'products.product.sku' | t }}: <span>{{ sku }}</span></p>
                            </div>
                        {%- endif -%}
                        {%- if show_barcode -%}
                            <div class="product-collection__barcode{% if barcode == blank %} d-none-important{% endif %} mb-5">
                                <p class="m-0" data-js-product-barcode>{{ 'products.product.barcode' | t }}: <span>{{ barcode }}</span></p>
                            </div>
                        {%- endif -%}
                        {%- if show_availability -%}
                            <div class="product-collection__availability mb-5">
                                {%- if quantity == 1 -%}
                                    {%- capture item -%}{{ 'layout.cart.items_count.one' | t }}{%- endcapture -%}
                                {%- else -%}
                                    {%- capture item -%}{{ 'layout.cart.items_count.other' | t }}{%- endcapture -%}
                                {%- endif -%}
                                <p class="m-0" data-js-product-availability data-availability="{% if available %}true{% else %}false{% endif %}">{{ 'products.product.availability' | t }}: <span>{% if available %}{{ 'products.product.availability_value_in_stock' | t: count: quantity, item: item }}{% else %}{{ 'products.product.availability_value_out_stock' | t }}{% endif %}</span></p>
                            </div>
                        {%- endif -%}
                        {%- if show_type -%}
                            <div class="product-collection__type{% if type == blank %} d-none-important{% endif %} mb-5">
                                <p class="m-0" data-js-product-type>{{ 'products.product.type' | t }}: <span>{{ type }}</span></p>
                            </div>
                        {%- endif -%}
                        {%- if show_vendor -%}
                            <div class="product-collection__vendor{% if vendor == blank %} d-none-important{% endif %} mb-5">
                                <p class="m-0" data-js-product-vendor>{{ 'products.product.vendor' | t }}: <span>{{ vendor }}</span></p>
                            </div>
                        {%- endif -%}
                    </div>
                {%- endif -%}
                {%- if show_description and description != blank -%}
                    <div class="product-collection__description d-none mb-15">
                        <p class="m-0">{{ description | strip_html | truncatewords: product_collection_description_truncatewords }}</p>
                    </div>
                {%- endif -%}
                {%- if show_price -%}
                    <div class="product-collection__price mb-{% if product_collection_content_offsets == 1 %}10{% else %}20{% endif %}">
                        {% include 'product-get-price' %}
                    </div>
                {%- endif -%}
                <div class="product-collection__control d-flex flex-column w-100">
                    {%- if show_options and show_custom_options -%}
                        <div class="product-collection__options">
                            {% include 'product-get-options' with select_dropdown_position: 'top' %}
                        </div>
                    {%- else -%}
                        {%- assign variants_size = product.variants | size -%}
                        {%- if variants_size > 1 -%}
                            {%- assign enable_select_options_button = true -%}
                        {%- endif -%}
                    {%- endif -%}
                    {%- liquid
                        assign current_selling_plan_allocation = current_variant.selected_selling_plan_allocation
                        if current_selling_plan_allocation == nil and current_variant.requires_selling_plan
                        assign current_selling_plan_allocation = current_variant.selling_plan_allocations | first
                        endif
                    -%}
                    <input type="hidden" name="selling_plan" value="{{ current_selling_plan_allocation.selling_plan.id | default: '' }}">
                    {%- if show_button_add_to_cart or show_options -%}
                        <div class="product-collection__variants mb-10{% if show_options == false or show_custom_options %} d-none{% elsif product.variants.size == 1 and product.variants.first.title contains 'Default' %} d-none{% endif %}">
                            {% render 'product-get-variants' with product: product current_variant: current_variant show_options: show_options show_custom_options: show_custom_options, product_form_id: product_form_id %}
                        </div>
                    {%- endif -%}
                    {%- if show_quantity -%}
                        <div class="product-collection__quantity d-flex justify-content-center mb-10">
                            {% render 'product-get-quantity' with quantity_type: 5, product_form_id: product_form_id %}
                        </div>
                    {%- endif -%}
                    {%- if show_button_add_to_cart or show_button_add_to_wishlist or show_button_add_to_compare or show_button_quick_view -%}
                        <div class="product-collection__buttons d-flex flex-column flex-lg-row align-items-lg-center flex-wrap{% if show_button_add_to_cart %} mt-5 mt-lg-10{% endif %}">
                            {%- if show_button_add_to_cart -%}
                                <div class="product-collection__button-add-to-cart mb-10">
                                    {% include 'product-get-button-cart' %}
                                </div>
                            {%- endif -%}
                            {%- if show_button_add_to_wishlist or show_button_add_to_compare or show_button_quick_view -%}
                                <div class="product-collection__buttons-section d-flex{% if show_button_add_to_wishlist != true and show_button_add_to_compare != true %} d-lg-none{% endif %}{% if show_button_add_to_cart %} px-lg-10{% endif %}">
                                    {%- if show_button_add_to_wishlist -%}
                                        <div class="product-collection__button-add-to-wishlist mb-10">
                                            {%- if settings.wishlist_type == 'default' -%}
                                                {% include 'product-get-button-wishlist' %}
                                            {%- elsif settings.wishlist_type == 'growave' and settings.app_growave_enable == true -%}
                                                {% capture the_snippet_fave_icon %}{% render 'ssw-widget-faveicon' with product.id %}{% endcapture %}
                                                {%- unless the_snippet_fave_icon contains 'Liquid error' -%}
                                                    {{ the_snippet_fave_icon }}
                                                {%- endunless -%}
                                            {%- endif -%}
                                        </div>
                                    {%- endif -%}
                                    {%- if show_button_add_to_compare -%}
                                        <div class="product-collection__button-add-to-compare mb-10">
                                            {% include 'product-get-button-compare' %}
                                        </div>
                                    {%- endif -%}
                                    {%- if show_button_quick_view -%}
                                        <div class="product-collection__button-quick-view-mobile d-lg-none mb-10">
                                            {% render 'product-get-button-quick-view' with type: 'btn-text' product_enable_tooltips: product_enable_tooltips %}
                                        </div>
                                    {%- endif -%}
                                </div>
                            {%- endif -%}
                        </div>
                    {%- endif -%}
                </div>
                {%- if show_reviews -%}
                    <div class="product-collection__reviews">
                        {%- if settings.reviews_type == 'default' -%}
                            {% render 'product-get-review' with product: product hide_counter: hide_reviews_counter hide_mobile_counter: hide_reviews_mobile_counter %}
                        {%- elsif settings.reviews_type == 'growave' and settings.app_growave_enable == true -%}
                            {% capture the_snippet_review_avg %}{% render 'ssw-widget-avg-rate-listing', product: product %}{% endcapture %}
                            {%- unless the_snippet_review_avg contains 'Liquid error' or product.metafields.ssw['review'].avg == 0 -%}
                                {{ the_snippet_review_avg }}
                            {%- endunless -%}
                        
                        {%- endif -%}
                    </div>
                {%- endif -%}
            </div>
            {%- if section.settings.view_grid_list_design == '2' -%}
                <div class="product-collection__list-v2-content d-none flex-column align-items-end pl-10 ml-auto border-left">
                    {%- if show_price -%}
                        <div class="product-collection__list-v2-price">
                            {% include 'product-get-price' %}
                        </div>
                    {%- endif -%}
                    {%- if show_reviews -%}
                        <div class="product-collection__list-v2-reviews">
                            {%- if settings.reviews_type == 'default' -%}
                                {% render 'product-get-review' with product: product hide_counter: false hide_mobile_counter: hide_reviews_mobile_counter %}
                            {%- elsif settings.reviews_type == 'growave' and settings.app_growave_enable == true -%}
                                {% capture the_snippet_review_avg %}{% render 'ssw-widget-avg-rate-listing', product: product %}{% endcapture %}
                                {%- unless the_snippet_review_avg contains 'Liquid error' or product.metafields.ssw['review'].avg == 0 -%}
                                    {{ the_snippet_review_avg }}
                                {%- endunless -%}
                            
                            {%- endif -%}
                        </div>
                    {%- endif -%}
                    {%- if show_list_quantity -%}
                        <div class="product-collection__list-v2-quantity mt-20">
                            {% render 'product-get-quantity' with quantity_type: 2, product_form_id: product_form_id %}
                        </div>
                    {%- endif -%}
                    {%- if show_button_add_to_cart -%}
                        <div class="product-collection__list-v2-button-add-to-cart mt-25">
                            {% include 'product-get-button-cart' with button_colorize: 2 %}
                        </div>
                    {%- endif -%}
                    {%- if show_button_add_to_wishlist or show_button_add_to_compare -%}
                        <div class="product-collection__list-v2-buttons d-flex flex-column align-items-end pt-3 mt-15">
                            {%- if show_button_add_to_wishlist -%}
                                <div class="product-collection__list-v2-button-add-to-wishlist">
                                    {%- if settings.wishlist_type == 'default' -%}
                                        {% include 'product-get-button-wishlist-text' %}
                                    {%- elsif settings.wishlist_type == 'growave' and settings.app_growave_enable == true -%}
                                        {% capture the_snippet_fave_icon %}{% render 'ssw-widget-faveicon' with product.id %}{% endcapture %}
                                        {%- unless the_snippet_fave_icon contains 'Liquid error' -%}
                                            {{ the_snippet_fave_icon }}
                                        {%- endunless -%}
                                    {%- endif -%}
                                </div>
                            {%- endif -%}
                            {%- if show_button_add_to_compare -%}
                                <div class="product-collection__list-v2-button-add-to-compare">
                                    {% include 'product-get-button-compare-text' %}
                                </div>
                            {%- endif -%}
                        </div>
                    {%- endif -%}
                </div>
            {%- endif -%}
        </div>
    </div>
    {%- if section.settings.view_grid_list_design == '2' -%}
        <div class="product-collection__list-v2-border d-none mb-30 border-bottom"></div>
    {%- endif -%}
    {%- if template.name == 'collection' -%}
        <script type="application/ld+json">
        {
            "@context": "http://schema.org/",
            "@type": "Product",
            "name": {{ product.title | json }},
            "url": {{ shop.url | append: product.url | json }},
            {%- if product.featured_media -%}
                {%- assign media_size = product.featured_media.preview_image.width | append: 'x' -%}
                "image": [
                    {{ product.featured_media | img_url: media_size | prepend: "https:" | json }}
                ],
            {%- endif -%}
            "description": {{ product.description | strip_html | json }},
            {%- if current_variant.sku != blank -%}
                "sku": {{ current_variant.sku | json }},
            {%- endif -%}
            "brand": {
                "@type": "Thing",
                "name": {{ product.vendor | json }}
            },
            "offers": [
                {
                    "@type" : "Offer",
                    {%- if current_variant.sku != blank -%}
                        "sku": {{ current_variant.sku | json }},
                    {%- endif -%}
                    "availability" : "http://schema.org/{% if current_variant.available %}InStock{% else %}OutOfStock{% endif %}",
                    "price" : {{ current_variant.price | divided_by: 100.00 | json }},
                    "priceCurrency" : {{ cart.currency.iso_code | json }},
                    "url" : {{ shop.url | append: current_variant.url | json }}
                }
            ]
        }
        </script>
    {%- endif -%}
</product-item>
{% include 'product-res-variables-clear' %}