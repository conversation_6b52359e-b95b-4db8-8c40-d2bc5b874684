import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品作成のミューテーション
const CREATE_PRODUCT_MUTATION = gql`
  mutation productCreate($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// バリアント作成のミューテーション
const CREATE_VARIANT_MUTATION = gql`
  mutation productVariantCreate($input: ProductVariantInput!) {
    productVariantCreate(input: $input) {
      productVariant {
        id
        title
        price
        sku
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールドの設定
async function setJsonMetafield(productId, namespace, key, jsonValue) {
  const mutation = gql`
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    metafields: [
      {
        ownerId: productId,
        namespace,
        key,
        value: JSON.stringify(jsonValue),
        type: 'json',
      }
    ]
  };

  try {
    const result = await graphQLClient.request(mutation, variables);
    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', result.metafieldsSet.userErrors);
      return false;
    }
    return true;
  } catch (error) {
    console.error(`メタフィールド ${namespace}.${key} 設定エラー:`, error);
    return false;
  }
}

// 商品を作成する関数
async function createProduct(productData) {
  try {
    const variables = {
      input: productData
    };
    
    const result = await graphQLClient.request(CREATE_PRODUCT_MUTATION, variables);
    
    if (result.productCreate.userErrors.length > 0) {
      console.error('商品作成エラー:', result.productCreate.userErrors);
      return null;
    }
    
    return result.productCreate.product;
  } catch (error) {
    console.error('商品作成中にエラーが発生しました:', error);
    return null;
  }
}

// バリアントを作成する関数
async function createVariant(productId, variantData) {
  try {
    const variables = {
      input: {
        productId,
        ...variantData
      }
    };
    
    const result = await graphQLClient.request(CREATE_VARIANT_MUTATION, variables);
    
    if (result.productVariantCreate.userErrors.length > 0) {
      console.error('バリアント作成エラー:', result.productVariantCreate.userErrors);
      return null;
    }
    
    return result.productVariantCreate.productVariant;
  } catch (error) {
    console.error('バリアント作成中にエラーが発生しました:', error);
    return null;
  }
}

// 現在の日付から指定した日数後の日付を取得
function getDateAfterDays(days) {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return date.toISOString().split('T')[0];
}

// テスト用商品データ
const testProducts = [
  {
    title: "【テスト】ベーシックソファ オフホワイト 1シーター",
    descriptionHtml: "ベーシックソファ オフホワイト 1シーター - 予約テスト用商品",
    vendor: "ヤマナリ",
    productType: "ソファ",
    tags: ["テスト", "ソファ", "オフホワイト"],
    status: "ACTIVE",
    options: ["レンタル日数", "商品番号"],
    variants: [
      {
        price: "8000",
        sku: "10101007-001",
        options: ["1日レンタル", "1台目"],
        inventoryManagement: "SHOPIFY"
      },
      {
        price: "8000",
        sku: "10101007-002",
        options: ["1日レンタル", "2台目"],
        inventoryManagement: "SHOPIFY"
      }
    ],
    metafields: {
      basic_info: {
        productCode: "10101007",
        detailCode: "001",
        kana: "ベーシックソファオフホワイト1シーターヤマナリ",
        dimensions: {
          width: 87,
          depth: 74,
          height: 76
        },
        seatDimensions: {
          width: 52,
          depth: 54,
          height: 40
        },
        material: "ファブリック",
        color: "オフホワイト",
        maker: "ヤマナリ",
        campaign: "通常商品",
        notes: "2台ともクリーニング済みですが全体的に黄ばみ発生（H30.12.15)"
      },
      pricing: {
        basePrice: 8000,
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30
      },
      reservation_info: [
        {
          sku: "10101007-001",
          reservations: [
            {
              id: "reservation-001",
              startDate: getDateAfterDays(5),
              endDate: getDateAfterDays(8),
              status: "confirmed",
              customerName: "山田太郎",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1001",
              orderLineItemId: "gid://shopify/LineItem/1001",
              notes: "特になし"
            },
            {
              id: "reservation-002",
              startDate: getDateAfterDays(15),
              endDate: getDateAfterDays(20),
              status: "pending",
              customerName: "佐藤花子",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1002",
              orderLineItemId: "gid://shopify/LineItem/1002",
              notes: "配送希望"
            }
          ]
        },
        {
          sku: "10101007-002",
          reservations: []
        }
      ],
      inventory_info: {
        "10101007-001": {
          available: 0,
          onHand: 1,
          reserved: 2,
          physicalCount: 1,
          lastUpdated: new Date().toISOString(),
          location: "NY",
          status: "available"
        },
        "10101007-002": {
          available: 0,
          onHand: 1,
          reserved: 0,
          physicalCount: 1,
          lastUpdated: new Date().toISOString(),
          location: "NY",
          status: "maintenance"
        }
      }
    }
  },
  {
    title: "【テスト】ウイングソファ アンティークグリーン 1シーター",
    descriptionHtml: "ウイングソファ アンティークグリーン 1シーター - 予約テスト用商品",
    vendor: "不明",
    productType: "ソファ",
    tags: ["テスト", "ソファ", "アンティークグリーン"],
    status: "ACTIVE",
    options: ["レンタル日数"],
    variants: [
      {
        price: "10000",
        sku: "10101009-001",
        options: ["1日レンタル"],
        inventoryManagement: "SHOPIFY"
      }
    ],
    metafields: {
      basic_info: {
        productCode: "10101009",
        detailCode: "001",
        kana: "ウイングソファアンティークグリーン1シーター",
        dimensions: {
          width: 71,
          depth: 80,
          height: 106
        },
        seatDimensions: {
          width: 48,
          depth: 52,
          height: 35
        },
        material: "ファブリック",
        color: "アンティークグリーン",
        maker: "不明",
        campaign: "LA_200803",
        notes: "[WEB詳細画像アリ] ①正面向って右側/座面に５センチ幅のアイロン跡あり ②右側上部ウィング全体/布地に擦れ跡あり ③背もたれ腰辺りｎテープ跡あり"
      },
      pricing: {
        basePrice: 10000,
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30
      },
      reservation_info: [
        {
          sku: "10101009-001",
          reservations: [
            {
              id: "reservation-003",
              startDate: getDateAfterDays(10),
              endDate: getDateAfterDays(25),
              status: "confirmed",
              customerName: "鈴木一郎",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1003",
              orderLineItemId: "gid://shopify/LineItem/1003",
              notes: "長期レンタル"
            }
          ]
        }
      ],
      inventory_info: {
        "10101009-001": {
          available: 0,
          onHand: 1,
          reserved: 1,
          physicalCount: 1,
          lastUpdated: new Date().toISOString(),
          location: "NY",
          status: "available"
        }
      }
    }
  },
  {
    title: "【テスト】カリモクソファ モケットグリーン 1シーター",
    descriptionHtml: "カリモクソファ モケットグリーン 1シーター - 予約テスト用商品",
    vendor: "カリモク",
    productType: "ソファ",
    tags: ["テスト", "ソファ", "モケットグリーン"],
    status: "ACTIVE",
    options: ["レンタル日数", "商品番号"],
    variants: [
      {
        price: "7000",
        sku: "10101031-001",
        options: ["1日レンタル", "1台目"],
        inventoryManagement: "SHOPIFY"
      },
      {
        price: "7000",
        sku: "10101031-002",
        options: ["1日レンタル", "2台目"],
        inventoryManagement: "SHOPIFY"
      }
    ],
    metafields: {
      basic_info: {
        productCode: "10101031",
        detailCode: "001",
        kana: "カリモクソファモケットグリーン1シーター",
        dimensions: {
          width: 61,
          depth: 66,
          height: 70
        },
        seatDimensions: {
          width: 52,
          depth: 46,
          height: 34
        },
        material: "モケット",
        color: "グリーン",
        maker: "カリモク",
        campaign: "Japan_+α_200904",
        notes: "2シーター1台あり"
      },
      pricing: {
        basePrice: 7000,
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30
      },
      reservation_info: [
        {
          sku: "10101031-001",
          reservations: [
            {
              id: "reservation-004",
              startDate: getDateAfterDays(3),
              endDate: getDateAfterDays(4),
              status: "confirmed",
              customerName: "田中次郎",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1004",
              orderLineItemId: "gid://shopify/LineItem/1004",
              notes: "短期レンタル"
            },
            {
              id: "reservation-005",
              startDate: getDateAfterDays(7),
              endDate: getDateAfterDays(9),
              status: "confirmed",
              customerName: "高橋三郎",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1005",
              orderLineItemId: "gid://shopify/LineItem/1005",
              notes: "休日を挟んだレンタル"
            }
          ]
        },
        {
          sku: "10101031-002",
          reservations: [
            {
              id: "reservation-006",
              startDate: getDateAfterDays(20),
              endDate: getDateAfterDays(30),
              status: "pending",
              customerName: "伊藤四郎",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1006",
              orderLineItemId: "gid://shopify/LineItem/1006",
              notes: "仮予約"
            }
          ]
        }
      ],
      inventory_info: {
        "10101031-001": {
          available: 0,
          onHand: 1,
          reserved: 2,
          physicalCount: 1,
          lastUpdated: new Date().toISOString(),
          location: "NY",
          status: "available"
        },
        "10101031-002": {
          available: 0,
          onHand: 1,
          reserved: 1,
          physicalCount: 1,
          lastUpdated: new Date().toISOString(),
          location: "NY",
          status: "damaged"
        }
      }
    }
  },
  {
    title: "【テスト】プレーンソファ 1シーター",
    descriptionHtml: "プレーンソファ 1シーター - 予約テスト用商品",
    vendor: "不明",
    productType: "ソファ",
    tags: ["テスト", "ソファ", "ベージュ"],
    status: "ACTIVE",
    options: ["レンタル日数", "商品番号"],
    variants: [
      {
        price: "8000",
        sku: "10101064-001",
        options: ["1日レンタル", "1台目"],
        inventoryManagement: "SHOPIFY"
      },
      {
        price: "8000",
        sku: "10101064-002",
        options: ["1日レンタル", "2台目"],
        inventoryManagement: "SHOPIFY"
      },
      {
        price: "8000",
        sku: "10101064-003",
        options: ["1日レンタル", "3台目"],
        inventoryManagement: "SHOPIFY"
      }
    ],
    metafields: {
      basic_info: {
        productCode: "10101064",
        detailCode: "001",
        kana: "プレーンソファヌード1シーターホンタイノミ",
        dimensions: {
          width: 83,
          depth: 83,
          height: 74
        },
        seatDimensions: {
          width: 53,
          depth: 55,
          height: 40
        },
        material: "ファブリック",
        color: "ベージュ",
        maker: "不明",
        campaign: "LONDON_201210",
        notes: "2シーター2台あり 他カバーあり"
      },
      pricing: {
        basePrice: 8000,
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30
      },
      reservation_info: [
        {
          sku: "10101064-001",
          reservations: [
            {
              id: "reservation-007",
              startDate: getDateAfterDays(1),
              endDate: getDateAfterDays(3),
              status: "confirmed",
              customerName: "中村五郎",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1007",
              orderLineItemId: "gid://shopify/LineItem/1007",
              notes: "直近の予約"
            },
            {
              id: "reservation-008",
              startDate: getDateAfterDays(5),
              endDate: getDateAfterDays(10),
              status: "confirmed",
              customerName: "小林六郎",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1008",
              orderLineItemId: "gid://shopify/LineItem/1008",
              notes: "連続した予約"
            }
          ]
        },
        {
          sku: "10101064-002",
          reservations: []
        },
        {
          sku: "10101064-003",
          reservations: [
            {
              id: "reservation-009",
              startDate: getDateAfterDays(15),
              endDate: getDateAfterDays(20),
              status: "pending",
              customerName: "加藤七郎",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1009",
              orderLineItemId: "gid://shopify/LineItem/1009",
              notes: "仮予約"
            },
            {
              id: "reservation-010",
              startDate: getDateAfterDays(25),
              endDate: getDateAfterDays(35),
              status: "confirmed",
              customerName: "松本八郎",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1010",
              orderLineItemId: "gid://shopify/LineItem/1010",
              notes: "長期レンタル"
            }
          ]
        }
      ],
      inventory_info: {
        "10101064-001": {
          available: 0,
          onHand: 1,
          reserved: 2,
          physicalCount: 1,
          lastUpdated: new Date().toISOString(),
          location: "NY",
          status: "available"
        },
        "10101064-002": {
          available: 1,
          onHand: 1,
          reserved: 0,
          physicalCount: 1,
          lastUpdated: new Date().toISOString(),
          location: "NY",
          status: "available"
        },
        "10101064-003": {
          available: 0,
          onHand: 1,
          reserved: 2,
          physicalCount: 1,
          lastUpdated: new Date().toISOString(),
          location: "NY",
          status: "unavailable"
        }
      }
    }
  }
];

// 追加バリアントデータ
const additionalVariants = [
  {
    productIndex: 0,
    baseVariants: [
      { title: "1台目", sku: "10101007-001" },
      { title: "2台目", sku: "10101007-002" }
    ],
    rentalDays: [
      { title: "2日レンタル", priceMultiplier: 1.2 },
      { title: "3日レンタル", priceMultiplier: 1.4 },
      { title: "4日レンタル", priceMultiplier: 1.6 },
      { title: "5日レンタル", priceMultiplier: 1.8 },
      { title: "6日レンタル", priceMultiplier: 2.0 },
      { title: "7日レンタル", priceMultiplier: 2.2 },
      { title: "8日以上レンタル", priceMultiplier: 2.3 }
    ]
  },
  {
    productIndex: 1,
    baseVariants: [
      { title: "1台目", sku: "10101009-001" }
    ],
    rentalDays: [
      { title: "2日レンタル", priceMultiplier: 1.2 },
      { title: "3日レンタル", priceMultiplier: 1.4 },
      { title: "4日レンタル", priceMultiplier: 1.6 },
      { title: "5日レンタル", priceMultiplier: 1.8 },
      { title: "6日レンタル", priceMultiplier: 2.0 },
      { title: "7日レンタル", priceMultiplier: 2.2 },
      { title: "8日以上レンタル", priceMultiplier: 2.3 }
    ]
  },
  {
    productIndex: 2,
    baseVariants: [
      { title: "1台目", sku: "10101031-001" },
      { title: "2台目", sku: "10101031-002" }
    ],
    rentalDays: [
      { title: "2日レンタル", priceMultiplier: 1.2 },
      { title: "3日レンタル", priceMultiplier: 1.4 },
      { title: "4日レンタル", priceMultiplier: 1.6 },
      { title: "5日レンタル", priceMultiplier: 1.8 },
      { title: "6日レンタル", priceMultiplier: 2.0 },
      { title: "7日レンタル", priceMultiplier: 2.2 },
      { title: "8日以上レンタル", priceMultiplier: 2.3 }
    ]
  },
  {
    productIndex: 3,
    baseVariants: [
      { title: "1台目", sku: "10101064-001" },
      { title: "2台目", sku: "10101064-002" },
      { title: "3台目", sku: "10101064-003" }
    ],
    rentalDays: [
      { title: "2日レンタル", priceMultiplier: 1.2 },
      { title: "3日レンタル", priceMultiplier: 1.4 },
      { title: "4日レンタル", priceMultiplier: 1.6 },
      { title: "5日レンタル", priceMultiplier: 1.8 },
      { title: "6日レンタル", priceMultiplier: 2.0 },
      { title: "7日レンタル", priceMultiplier: 2.2 },
      { title: "8日以上レンタル", priceMultiplier: 2.3 }
    ]
  }
];

// メイン処理
async function createComprehensiveTestProducts() {
  console.log('総合テスト用商品を作成しています...');
  
  const createdProducts = [];
  
  for (const productData of testProducts) {
    console.log(`\n商品を作成中: ${productData.title}`);
    
    // メタフィールドを一時的に削除
    const metafields = productData.metafields;
    delete productData.metafields;
    
    // 商品を作成
    const product = await createProduct(productData);
    
    if (!product) {
      console.error(`商品の作成に失敗しました: ${productData.title}`);
      continue;
    }
    
    console.log(`商品を作成しました: ${product.title} (ID: ${product.id})`);
    createdProducts.push(product);
    
    // 追加バリアントの作成
    const productIndex = createdProducts.length - 1;
    const additionalVariantData = additionalVariants.find(v => v.productIndex === productIndex);
    
    if (additionalVariantData) {
      console.log(`${product.title} の追加バリアントを作成中...`);
      
      for (const baseVariant of additionalVariantData.baseVariants) {
        for (const rentalDay of additionalVariantData.rentalDays) {
          const basePrice = parseInt(testProducts[productIndex].variants.find(v => v.sku === baseVariant.sku).price);
          const price = Math.round(basePrice * rentalDay.priceMultiplier);
          const skuSuffix = rentalDay.title.includes('8日以上') ? '008' : `00${rentalDay.title.charAt(0)}`;
          const sku = `${baseVariant.sku.slice(0, -3)}${skuSuffix}`;
          
          console.log(`バリアントを作成中: ${rentalDay.title} - ${baseVariant.title} (${sku})`);
          
          const variantData = {
            options: [rentalDay.title, baseVariant.title],
            price: price.toString(),
            sku: sku,
            inventoryManagement: "SHOPIFY"
          };
          
          // 単一オプションの場合は特別処理
          if (additionalVariantData.baseVariants.length === 1) {
            variantData.options = [rentalDay.title];
          }
          
          const variant = await createVariant(product.id, variantData);
          
          if (!variant) {
            console.error(`バリアントの作成に失敗しました: ${rentalDay.title} - ${baseVariant.title}`);
          }
        }
      }
    }
    
    // メタフィールドの設定
    if (metafields) {
      console.log(`${product.title} のメタフィールドを設定中...`);
      
      // 基本情報
      if (metafields.basic_info) {
        console.log('基本情報を設定中...');
        await setJsonMetafield(product.id, 'rental', 'basic_info', metafields.basic_info);
      }
      
      // 料金設定
      if (metafields.pricing) {
        console.log('料金設定を設定中...');
        await setJsonMetafield(product.id, 'rental', 'pricing', metafields.pricing);
      }
      
      // 予約情報
      if (metafields.reservation_info) {
        console.log('予約情報を設定中...');
        await setJsonMetafield(product.id, 'rental', 'reservation_info', metafields.reservation_info);
      }
      
      // 在庫情報
      if (metafields.inventory_info) {
        console.log('在庫情報を設定中...');
        await setJsonMetafield(product.id, 'rental', 'inventory_info', metafields.inventory_info);
      }
    }
  }
  
  console.log('\n=== 作成完了 ===');
  console.log(`${createdProducts.length}件の商品を作成しました`);
  
  if (createdProducts.length > 0) {
    console.log('\n作成された商品:');
    createdProducts.forEach(product => {
      console.log(`- ${product.title} (ID: ${product.id})`);
    });
    
    console.log('\nShopify管理画面で商品を確認: https://admin.shopify.com/store/peaces-test-block/products');
  }
}

// 実行
createComprehensiveTestProducts();
