/**
 * SKU検索ユーティリティ
 * 
 * 基本SKU（201-07-107）からバリアント形式（201-07-107-1D、201-07-107-bk等）を含む
 * 柔軟な検索条件を生成します。
 */

/**
 * SKU検索条件を生成する
 * @param searchQuery 検索クエリ
 * @returns Prisma検索条件
 */
export function buildSkuSearchConditions(searchQuery: string) {
  if (!searchQuery || searchQuery.trim() === '') {
    return [];
  }

  const trimmedQuery = searchQuery.trim();
  const conditions = [];

  // 1. 完全一致検索
  conditions.push({
    sku: { equals: trimmedQuery, mode: 'insensitive' }
  });

  // 2. 部分一致検索（既存の動作を維持）
  conditions.push({
    sku: { contains: trimmedQuery, mode: 'insensitive' }
  });

  // 3. 基本SKU形式の検索（201-07-107 形式）
  if (isBasicSkuFormat(trimmedQuery)) {
    // 基本SKUから始まるバリアントを検索
    conditions.push({
      sku: { startsWith: trimmedQuery + '-', mode: 'insensitive' }
    });
  }

  // 4. ハイフンなし形式の検索（20107107 → 201-07-107）
  if (isNumericOnly(trimmedQuery) && trimmedQuery.length >= 8) {
    const formattedSku = formatNumericToSku(trimmedQuery);
    if (formattedSku) {
      // フォーマットされたSKUでの検索
      conditions.push({
        sku: { equals: formattedSku, mode: 'insensitive' }
      });
      conditions.push({
        sku: { startsWith: formattedSku + '-', mode: 'insensitive' }
      });
    }
  }

  // 5. 前方10文字での検索（201-07-107-1D → 201-07-107）
  if (trimmedQuery.length > 10) {
    const prefix = trimmedQuery.substring(0, 10);
    if (isBasicSkuFormat(prefix)) {
      conditions.push({
        sku: { startsWith: prefix, mode: 'insensitive' }
      });
    }
  }

  return conditions;
}

/**
 * 基本SKU形式かどうかを判定（XXX-XX-XXX形式）
 * @param sku SKU文字列
 * @returns 基本SKU形式かどうか
 */
function isBasicSkuFormat(sku: string): boolean {
  // 201-07-107 のような形式をチェック
  const basicSkuPattern = /^\d{3}-\d{2}-\d{3}$/;
  return basicSkuPattern.test(sku);
}

/**
 * 数字のみの文字列かどうかを判定
 * @param str 文字列
 * @returns 数字のみかどうか
 */
function isNumericOnly(str: string): boolean {
  return /^\d+$/.test(str);
}

/**
 * 数字のみの文字列を基本SKU形式にフォーマット
 * @param numericStr 数字文字列（例: "20107107"）
 * @returns フォーマットされたSKU（例: "201-07-107"）または null
 */
function formatNumericToSku(numericStr: string): string | null {
  // 8桁の数字を XXX-XX-XXX 形式に変換
  if (numericStr.length === 8) {
    return `${numericStr.substring(0, 3)}-${numericStr.substring(3, 5)}-${numericStr.substring(5, 8)}`;
  }
  
  // 9桁以上の場合は最初の8桁を使用
  if (numericStr.length >= 8) {
    const first8 = numericStr.substring(0, 8);
    return `${first8.substring(0, 3)}-${first8.substring(3, 5)}-${first8.substring(5, 8)}`;
  }
  
  return null;
}

/**
 * 商品検索用のSKU条件を含む検索条件を構築
 * @param searchQuery 検索クエリ
 * @param titleField タイトルフィールド名（デフォルト: 'title'）
 * @param skuField SKUフィールド名（デフォルト: 'sku'）
 * @returns Prisma OR条件の配列
 */
export function buildProductSearchConditions(
  searchQuery: string, 
  titleField: string = 'title', 
  skuField: string = 'sku'
) {
  if (!searchQuery || searchQuery.trim() === '') {
    return [];
  }

  const trimmedQuery = searchQuery.trim();
  const conditions = [];

  // タイトル検索
  conditions.push({
    [titleField]: { contains: trimmedQuery, mode: 'insensitive' }
  });

  // SKU検索条件を追加
  const skuConditions = buildSkuSearchConditions(trimmedQuery);
  skuConditions.forEach(condition => {
    // フィールド名を適切に設定
    const skuCondition = {};
    Object.keys(condition).forEach(key => {
      if (key === 'sku') {
        skuCondition[skuField] = condition[key];
      } else {
        skuCondition[key] = condition[key];
      }
    });
    conditions.push(skuCondition);
  });

  return conditions;
}

/**
 * SKU検索のテスト用関数
 * @param searchQuery 検索クエリ
 * @returns 生成される検索条件の説明
 */
export function explainSkuSearch(searchQuery: string): string[] {
  const conditions = buildSkuSearchConditions(searchQuery);
  const explanations = [];

  explanations.push(`検索クエリ: "${searchQuery}"`);
  explanations.push(`生成される検索条件: ${conditions.length}件`);

  conditions.forEach((condition, index) => {
    const skuCondition = condition.sku;
    if (skuCondition.equals) {
      explanations.push(`  ${index + 1}. 完全一致: "${skuCondition.equals}"`);
    } else if (skuCondition.contains) {
      explanations.push(`  ${index + 1}. 部分一致: "*${skuCondition.contains}*"`);
    } else if (skuCondition.startsWith) {
      explanations.push(`  ${index + 1}. 前方一致: "${skuCondition.startsWith}*"`);
    }
  });

  return explanations;
}

/**
 * SKU検索のテストケース
 */
export const SKU_SEARCH_TEST_CASES = [
  {
    input: '201-07-107',
    description: '基本SKU形式',
    expectedMatches: ['201-07-107', '201-07-107-1D', '201-07-107-bk', '201-07-107-001']
  },
  {
    input: '20107107',
    description: 'ハイフンなし数字',
    expectedMatches: ['201-07-107', '201-07-107-1D', '201-07-107-bk']
  },
  {
    input: '201-07-107-1D',
    description: 'バリアント付きSKU',
    expectedMatches: ['201-07-107-1D', '201-07-107']
  },
  {
    input: '201',
    description: '部分SKU',
    expectedMatches: ['201-07-107', '201-06-555', '201-08-999']
  }
];
