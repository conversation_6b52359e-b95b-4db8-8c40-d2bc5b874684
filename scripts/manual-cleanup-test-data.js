/**
 * テストデータを手動でクリーンアップするスクリプト
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * テストデータをクリーンアップする関数
 */
async function cleanupTestData() {
  try {
    console.log('テストデータのクリーンアップを開始します...');

    // テスト中に作成された予約を削除
    const bookings = await prisma.booking.findMany({
      where: {
        OR: [
          {
            metadata: {
              path: ['testCreated'],
              equals: true
            }
          },
          {
            bookingId: {
              startsWith: 'TEST-'
            }
          }
        ]
      }
    });

    if (bookings.length > 0) {
      console.log(`${bookings.length}件のテスト予約を削除します...`);
      
      for (const booking of bookings) {
        console.log(`予約ID: ${booking.id}, 予約番号: ${booking.bookingId}, 期間: ${booking.startDate} 〜 ${booking.endDate}`);
      }

      await prisma.booking.deleteMany({
        where: {
          id: {
            in: bookings.map(b => b.id)
          }
        }
      });

      console.log('テスト予約が削除されました');
    } else {
      console.log('削除対象のテスト予約はありません');
    }

    // テスト中に作成された在庫カレンダーを削除
    const inventoryCalendars = await prisma.inventoryCalendar.findMany({
      where: {
        productId: {
          in: bookings.map(b => b.productId)
        }
      }
    });

    if (inventoryCalendars.length > 0) {
      console.log(`${inventoryCalendars.length}件の在庫カレンダーを削除します...`);

      await prisma.inventoryCalendar.deleteMany({
        where: {
          id: {
            in: inventoryCalendars.map(ic => ic.id)
          }
        }
      });

      console.log('在庫カレンダーが削除されました');
    } else {
      console.log('削除対象の在庫カレンダーはありません');
    }

    // テスト中に作成された商品を削除（オプション）
    const deleteProducts = process.argv.includes('--delete-products');
    
    if (deleteProducts) {
      const products = await prisma.product.findMany({
        where: {
          OR: [
            {
              metadata: {
                path: ['testCreated'],
                equals: true
              }
            },
            {
              sku: {
                startsWith: 'TEST-'
              }
            }
          ]
        }
      });

      if (products.length > 0) {
        console.log(`${products.length}件のテスト商品を削除します...`);
        
        for (const product of products) {
          console.log(`商品ID: ${product.id}, 商品名: ${product.title}, SKU: ${product.sku}`);
        }

        await prisma.product.deleteMany({
          where: {
            id: {
              in: products.map(p => p.id)
            }
          }
        });

        console.log('テスト商品が削除されました');
      } else {
        console.log('削除対象のテスト商品はありません');
      }
    }

    console.log('テストデータのクリーンアップが完了しました');
    return true;
  } catch (error) {
    console.error('テストデータクリーンアップエラー:', error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
cleanupTestData().catch(e => {
  console.error('スクリプト実行中に予期せぬエラーが発生しました:', e);
  process.exit(1);
});
