/**
 * 商品データ表示問題修正スクリプト
 *
 * このスクリプトは、予約一覧画面での商品データ表示の問題を修正します。
 * 主に以下の問題を修正します：
 * 1. 商品データが正しく表示されない問題
 * 2. 商品情報の取得エラー
 * 3. 商品情報の表示形式の問題
 *
 * 実行方法: npx tsx scripts/fix-product-display-issues.ts
 */

import { PrismaClient } from '@prisma/client';
import fetch from 'node-fetch';
import * as fs from 'fs';
import * as path from 'path';

// 色付きログ出力のための定数
const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
};

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// Shopify APIの設定
const SHOPIFY_SHOP = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';
const SHOPIFY_API_KEY = process.env.SHOPIFY_API_KEY;
const SHOPIFY_API_SECRET = process.env.SHOPIFY_API_SECRET;
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ACCESS_TOKEN;

/**
 * 問題のある予約を特定する関数
 */
async function identifyProblematicBookings() {
  console.log(`${COLORS.CYAN}=== 問題のある予約を特定しています... ===${COLORS.RESET}`);
  
  try {
    // 商品情報が欠落している予約を検索
    const bookingsWithoutProduct = await prisma.booking.findMany({
      where: {
        productId: { not: null },
        product: null,
      },
      select: {
        id: true,
        productId: true,
        customerName: true,
        startDate: true,
        endDate: true,
      },
    });
    
    console.log(`商品情報が欠落している予約: ${bookingsWithoutProduct.length}件`);
    
    // 商品情報が不完全な予約を検索
    const bookingsWithIncompleteProduct = await prisma.booking.findMany({
      where: {
        productId: { not: null },
        product: {
          OR: [
            { title: null },
            { title: '' },
            { sku: null },
            { sku: '' },
            { shopifyId: null },
            { shopifyId: '' },
          ],
        },
      },
      select: {
        id: true,
        productId: true,
        customerName: true,
        startDate: true,
        endDate: true,
        product: {
          select: {
            id: true,
            title: true,
            sku: true,
            shopifyId: true,
          },
        },
      },
    });
    
    console.log(`商品情報が不完全な予約: ${bookingsWithIncompleteProduct.length}件`);
    
    return {
      bookingsWithoutProduct,
      bookingsWithIncompleteProduct,
    };
  } catch (error) {
    console.error(`${COLORS.RED}エラー:${COLORS.RESET}`, error);
    return {
      bookingsWithoutProduct: [],
      bookingsWithIncompleteProduct: [],
    };
  }
}

/**
 * Shopify APIから商品情報を取得する関数
 */
async function fetchProductFromShopify(shopifyId: string) {
  if (!SHOPIFY_ACCESS_TOKEN) {
    console.error(`${COLORS.RED}エラー: SHOPIFY_ACCESS_TOKEN が設定されていません${COLORS.RESET}`);
    return null;
  }
  
  try {
    // GraphQL APIを使用して商品情報を取得
    const query = `
      query GetProduct($id: ID!) {
        product(id: $id) {
          id
          title
          handle
          description
          productType
          vendor
          variants(first: 10) {
            edges {
              node {
                id
                title
                sku
                price
                inventoryQuantity
              }
            }
          }
          metafields(first: 10) {
            edges {
              node {
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    `;
    
    const variables = {
      id: shopifyId,
    };
    
    const response = await fetch(`https://${SHOPIFY_SHOP}/admin/api/2023-10/graphql.json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
      },
      body: JSON.stringify({
        query,
        variables,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`API呼び出しエラー: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.data.product;
  } catch (error) {
    console.error(`${COLORS.RED}Shopify APIエラー:${COLORS.RESET}`, error);
    return null;
  }
}

/**
 * 商品情報を修復する関数
 */
async function repairProductData(problematicBookings: any) {
  console.log(`${COLORS.CYAN}=== 商品情報を修復しています... ===${COLORS.RESET}`);
  
  const { bookingsWithoutProduct, bookingsWithIncompleteProduct } = problematicBookings;
  let repairedCount = 0;
  let failedCount = 0;
  
  // 商品情報が欠落している予約を修復
  for (const booking of bookingsWithoutProduct) {
    console.log(`\n予約ID: ${booking.id} の商品情報を修復しています...`);
    
    try {
      // 商品IDから商品情報を取得
      const product = await prisma.product.findUnique({
        where: { id: booking.productId },
      });
      
      if (!product) {
        console.log(`${COLORS.YELLOW}⚠️ 商品ID ${booking.productId} の商品が見つかりません${COLORS.RESET}`);
        failedCount++;
        continue;
      }
      
      // 予約と商品の関連付けを更新
      await prisma.booking.update({
        where: { id: booking.id },
        data: {
          product: {
            connect: { id: product.id },
          },
        },
      });
      
      console.log(`${COLORS.GREEN}✅ 予約ID ${booking.id} の商品情報を修復しました${COLORS.RESET}`);
      repairedCount++;
    } catch (error) {
      console.error(`${COLORS.RED}エラー:${COLORS.RESET}`, error);
      failedCount++;
    }
  }
  
  // 商品情報が不完全な予約を修復
  for (const booking of bookingsWithIncompleteProduct) {
    console.log(`\n予約ID: ${booking.id} の不完全な商品情報を修復しています...`);
    
    try {
      const product = booking.product;
      
      // Shopify IDがある場合はShopify APIから情報を取得
      if (product.shopifyId) {
        const shopifyProduct = await fetchProductFromShopify(product.shopifyId);
        
        if (shopifyProduct) {
          // 商品情報を更新
          await prisma.product.update({
            where: { id: product.id },
            data: {
              title: shopifyProduct.title || product.title,
              sku: shopifyProduct.variants.edges[0]?.node.sku || product.sku,
              // その他の必要なフィールドを更新
            },
          });
          
          console.log(`${COLORS.GREEN}✅ 商品ID ${product.id} の情報をShopify APIから更新しました${COLORS.RESET}`);
          repairedCount++;
          continue;
        }
      }
      
      // Shopify APIから取得できない場合は、他の方法で修復を試みる
      // 例: SKUから商品を検索
      if (!product.title && product.sku) {
        const similarProduct = await prisma.product.findFirst({
          where: {
            sku: product.sku,
            title: { not: null },
          },
        });
        
        if (similarProduct) {
          await prisma.product.update({
            where: { id: product.id },
            data: {
              title: similarProduct.title,
              // その他の必要なフィールドを更新
            },
          });
          
          console.log(`${COLORS.GREEN}✅ 商品ID ${product.id} の情報を類似商品から更新しました${COLORS.RESET}`);
          repairedCount++;
          continue;
        }
      }
      
      console.log(`${COLORS.YELLOW}⚠️ 商品ID ${product.id} の情報を修復できませんでした${COLORS.RESET}`);
      failedCount++;
    } catch (error) {
      console.error(`${COLORS.RED}エラー:${COLORS.RESET}`, error);
      failedCount++;
    }
  }
  
  return {
    repairedCount,
    failedCount,
  };
}

/**
 * APIレスポンスの商品データ表示を改善する関数
 */
async function improveProductDataDisplay() {
  console.log(`${COLORS.CYAN}=== APIレスポンスの商品データ表示を改善しています... ===${COLORS.RESET}`);
  
  try {
    // 予約APIのレスポンスを改善するためのコード
    // 実際のアプリケーションコードの修正が必要なため、ここでは説明のみ
    console.log(`
${COLORS.YELLOW}注意: APIレスポンスの改善には、以下のファイルの修正が必要です:${COLORS.RESET}

1. app/routes/api.bookings.tsx
   - 予約データを取得する際に、商品情報も確実に取得するように修正
   - 商品情報が見つからない場合のフォールバック処理を追加

2. app/routes/app.bookings._index.tsx
   - 商品データの表示ロジックを改善
   - 商品情報が欠落している場合のフォールバック表示を追加

3. app/components/BookingList.tsx (存在する場合)
   - 商品情報の表示方法を改善
   - エラーハンドリングを強化
    `);
    
    return true;
  } catch (error) {
    console.error(`${COLORS.RED}エラー:${COLORS.RESET}`, error);
    return false;
  }
}

/**
 * キャッシュ機構を最適化する関数
 */
async function optimizeCacheSystem() {
  console.log(`${COLORS.CYAN}=== キャッシュ機構を最適化しています... ===${COLORS.RESET}`);
  
  try {
    // キャッシュ機構の最適化に関するコード
    // 実際のアプリケーションコードの修正が必要なため、ここでは説明のみ
    console.log(`
${COLORS.YELLOW}注意: キャッシュ機構の最適化には、以下の修正が必要です:${COLORS.RESET}

1. app/services/product.service.ts
   - 商品データのキャッシュ機構を実装または改善
   - キャッシュの有効期限を適切に設定
   - キャッシュの更新トリガーを追加

2. app/services/booking.service.ts
   - 予約データ取得時に商品データもキャッシュするロジックを追加
   - キャッシュの整合性を確保するメカニズムを追加
    `);
    
    return true;
  } catch (error) {
    console.error(`${COLORS.RED}エラー:${COLORS.RESET}`, error);
    return false;
  }
}

/**
 * メイン実行関数
 */
async function main() {
  console.log(`${COLORS.MAGENTA}=== 商品データ表示問題修正スクリプト ===${COLORS.RESET}`);
  console.log(`開始時刻: ${new Date().toLocaleString()}`);
  
  try {
    // ステップ1: 問題のある予約を特定
    const problematicBookings = await identifyProblematicBookings();
    
    // ステップ2: 商品情報を修復
    const repairResults = await repairProductData(problematicBookings);
    
    // ステップ3: APIレスポンスの商品データ表示を改善
    await improveProductDataDisplay();
    
    // ステップ4: キャッシュ機構を最適化
    await optimizeCacheSystem();
    
    // 結果サマリー
    console.log(`\n${COLORS.CYAN}=== 結果サマリー ===${COLORS.RESET}`);
    console.log(`問題のある予約数: ${problematicBookings.bookingsWithoutProduct.length + problematicBookings.bookingsWithIncompleteProduct.length}件`);
    console.log(`修復成功: ${repairResults.repairedCount}件`);
    console.log(`修復失敗: ${repairResults.failedCount}件`);
    
    console.log(`\n${COLORS.MAGENTA}=== 修正完了 ===${COLORS.RESET}`);
    console.log(`終了時刻: ${new Date().toLocaleString()}`);
    
    // Prisma接続を閉じる
    await prisma.$disconnect();
  } catch (error) {
    console.error(`${COLORS.RED}スクリプト実行中にエラーが発生しました:${COLORS.RESET}`, error);
    
    // Prisma接続を閉じる
    await prisma.$disconnect();
    process.exit(1);
  }
}

// スクリプトを実行
main();
