/**
 * Webhookの登録状況を確認するスクリプト
 * 
 * このスクリプトは、Shopify Admin APIを使用してWebhookの登録状況を確認します。
 * 
 * 使用方法:
 * npx tsx scripts/check-webhooks.ts
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// Webhookサブスクリプションを取得するクエリ
const GET_WEBHOOK_SUBSCRIPTIONS = gql`
  query getWebhookSubscriptions {
    webhookSubscriptions(first: 100) {
      edges {
        node {
          id
          topic
          endpoint {
            __typename
            ... on WebhookHttpEndpoint {
              callbackUrl
            }
            ... on WebhookEventBridgeEndpoint {
              arn
            }
            ... on WebhookPubSubEndpoint {
              pubSubProject
              pubSubTopic
            }
          }
          format
          createdAt
          updatedAt
          metafieldNamespaces
        }
      }
    }
  }
`;

/**
 * Webhookの登録状況を確認する
 */
async function checkWebhooks() {
  try {
    console.log('Webhookの登録状況を確認中...');
    
    // Webhookサブスクリプションを取得
    const response = await client.request(GET_WEBHOOK_SUBSCRIPTIONS);
    
    const webhooks = response.webhookSubscriptions.edges.map((edge: any) => edge.node);
    
    console.log(`\n=== Webhook登録状況 (${webhooks.length}件) ===`);
    
    if (webhooks.length === 0) {
      console.log('登録されているWebhookはありません。');
      return;
    }
    
    // Webhookをトピックごとに分類
    const webhooksByTopic: { [key: string]: any[] } = {};
    
    webhooks.forEach((webhook: any) => {
      const topic = webhook.topic;
      
      if (!webhooksByTopic[topic]) {
        webhooksByTopic[topic] = [];
      }
      
      webhooksByTopic[topic].push(webhook);
    });
    
    // トピックごとにWebhookを表示
    Object.keys(webhooksByTopic).sort().forEach((topic) => {
      console.log(`\n## ${topic} (${webhooksByTopic[topic].length}件)`);
      
      webhooksByTopic[topic].forEach((webhook: any, index: number) => {
        console.log(`${index + 1}. ID: ${webhook.id}`);
        
        // エンドポイントの種類に応じて表示
        if (webhook.endpoint.__typename === 'WebhookHttpEndpoint') {
          console.log(`   URL: ${webhook.endpoint.callbackUrl}`);
        } else if (webhook.endpoint.__typename === 'WebhookEventBridgeEndpoint') {
          console.log(`   ARN: ${webhook.endpoint.arn}`);
        } else if (webhook.endpoint.__typename === 'WebhookPubSubEndpoint') {
          console.log(`   PubSub: ${webhook.endpoint.pubSubProject}/${webhook.endpoint.pubSubTopic}`);
        }
        
        console.log(`   フォーマット: ${webhook.format}`);
        console.log(`   作成日時: ${webhook.createdAt}`);
        console.log(`   更新日時: ${webhook.updatedAt}`);
        
        if (webhook.metafieldNamespaces && webhook.metafieldNamespaces.length > 0) {
          console.log(`   メタフィールド名前空間: ${webhook.metafieldNamespaces.join(', ')}`);
        }
      });
    });
    
    // 重要なWebhookの確認
    console.log('\n=== 重要なWebhookの確認 ===');
    
    const importantTopics = [
      'PRODUCTS_CREATE',
      'PRODUCTS_UPDATE',
      'PRODUCTS_DELETE',
      'ORDERS_CREATE',
      'ORDERS_UPDATED',
      'CUSTOMERS_CREATE',
      'CUSTOMERS_UPDATE',
      'DRAFT_ORDERS_CREATE',
      'DRAFT_ORDERS_UPDATE',
      'DRAFT_ORDERS_COMPLETE'
    ];
    
    importantTopics.forEach((topic) => {
      const hasWebhook = webhooksByTopic[topic] && webhooksByTopic[topic].length > 0;
      console.log(`${topic}: ${hasWebhook ? '✅ 登録済み' : '❌ 未登録'}`);
    });
    
  } catch (error) {
    console.error('Webhook確認中にエラーが発生しました:', error);
  }
}

// スクリプトを実行
checkWebhooks()
  .then(() => console.log('\nWebhook確認が完了しました'))
  .catch((error) => console.error('予期しないエラーが発生しました:', error));
