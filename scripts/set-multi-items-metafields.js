import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品検索のクエリ
const GET_PRODUCT_BY_SKU = gql`
  query getProductBySku($query: String!) {
    products(first: 1, query: $query) {
      edges {
        node {
          id
          title
          variants(first: 50) {
            edges {
              node {
                id
                sku
                title
              }
            }
          }
        }
      }
    }
  }
`;

// メタフィールドの設定
async function setJsonMetafield(productId, namespace, key, jsonValue) {
  const mutation = gql`
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    metafields: [
      {
        ownerId: productId,
        namespace,
        key,
        value: JSON.stringify(jsonValue),
        type: 'json',
      }
    ]
  };

  try {
    const result = await graphQLClient.request(mutation, variables);
    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', result.metafieldsSet.userErrors);
      return false;
    }
    return true;
  } catch (error) {
    console.error(`メタフィールド ${namespace}.${key} 設定エラー:`, error);
    return false;
  }
}

// SKUで商品を検索
async function getProductBySku(sku) {
  try {
    const result = await graphQLClient.request(GET_PRODUCT_BY_SKU, {
      query: `sku:${sku}`,
    });

    if (result.products.edges.length === 0) {
      console.error(`SKU ${sku} の商品が見つかりませんでした`);
      return null;
    }

    return result.products.edges[0].node;
  } catch (error) {
    console.error('商品検索エラー:', error);
    return null;
  }
}

// タイトルで商品を検索（完全一致）
async function getProductByTitle(title) {
  try {
    const result = await graphQLClient.request(GET_PRODUCT_BY_SKU, {
      query: `title:"${title}"`,
    });

    if (result.products.edges.length === 0) {
      console.error(`タイトル "${title}" の商品が見つかりませんでした`);
      return null;
    }

    // 完全一致を確認
    const matchedProduct = result.products.edges.find(edge => edge.node.title === title);
    if (!matchedProduct) {
      console.error(`タイトル "${title}" の商品が完全一致しませんでした`);
      return null;
    }

    return matchedProduct.node;
  } catch (error) {
    console.error('商品検索エラー:', error);
    return null;
  }
}

// バリアントマッピングを作成
function createVariantMapping(variants, baseSkuPrefix) {
  const mapping = {};

  // 1台目のバリアントマッピング
  const firstItemVariants = variants.filter(v => v.title.includes('1台目'));
  const firstItemMapping = {};

  firstItemVariants.forEach(variant => {
    const rentalDay = variant.title.split(' / ')[0];
    let key = '';

    if (rentalDay === '1日レンタル') key = '1day';
    else if (rentalDay === '2日レンタル') key = '2day';
    else if (rentalDay === '3日レンタル') key = '3day';
    else if (rentalDay === '4日レンタル') key = '4day';
    else if (rentalDay === '5日レンタル') key = '5day';
    else if (rentalDay === '6日レンタル') key = '6day';
    else if (rentalDay === '7日レンタル') key = '7day';
    else if (rentalDay === '8日以上レンタル') key = '8plus';

    if (key) {
      firstItemMapping[key] = variant.id;
    }
  });

  // 2台目のバリアントマッピング
  const secondItemVariants = variants.filter(v => v.title.includes('2台目'));
  const secondItemMapping = {};

  secondItemVariants.forEach(variant => {
    const rentalDay = variant.title.split(' / ')[0];
    let key = '';

    if (rentalDay === '1日レンタル') key = '1day';
    else if (rentalDay === '2日レンタル') key = '2day';
    else if (rentalDay === '3日レンタル') key = '3day';
    else if (rentalDay === '4日レンタル') key = '4day';
    else if (rentalDay === '5日レンタル') key = '5day';
    else if (rentalDay === '6日レンタル') key = '6day';
    else if (rentalDay === '7日レンタル') key = '7day';
    else if (rentalDay === '8日以上レンタル') key = '8plus';

    if (key) {
      secondItemMapping[key] = variant.id;
    }
  });

  // 3台目のバリアントマッピング（存在する場合）
  const thirdItemVariants = variants.filter(v => v.title.includes('3台目'));
  const thirdItemMapping = {};

  if (thirdItemVariants.length > 0) {
    thirdItemVariants.forEach(variant => {
      const rentalDay = variant.title.split(' / ')[0];
      let key = '';

      if (rentalDay === '1日レンタル') key = '1day';
      else if (rentalDay === '2日レンタル') key = '2day';
      else if (rentalDay === '3日レンタル') key = '3day';
      else if (rentalDay === '4日レンタル') key = '4day';
      else if (rentalDay === '5日レンタル') key = '5day';
      else if (rentalDay === '6日レンタル') key = '6day';
      else if (rentalDay === '7日レンタル') key = '7day';
      else if (rentalDay === '8日以上レンタル') key = '8plus';

      if (key) {
        thirdItemMapping[key] = variant.id;
      }
    });
  }

  // マッピングをまとめる
  mapping[`${baseSkuPrefix}-001`] = firstItemMapping;
  mapping[`${baseSkuPrefix}-002`] = secondItemMapping;

  if (Object.keys(thirdItemMapping).length > 0) {
    mapping[`${baseSkuPrefix}-003`] = thirdItemMapping;
  }

  return mapping;
}

// テスト用データ
const testData = [
  {
    sku: '10101031-001',
    title: 'カリモクソファ モケットグリーン 1シーター',
    baseSkuPrefix: '10101031',
    rental_basic_info: {
      productCode: '10101031',
      detailCode: '001',
      kana: 'カリモクソファモケットグリーン1シーター',
      location: 'NY',
      status: 'available'
    },
    rental_pricing: {
      basePrice: 7000,
      depositRate: 0.1,
      discountRules: {
        day2_6_rate: 0.2,
        day7_plus_rate: 0.1
      },
      minimumDays: 1,
      maximumDays: 30
    }
  },
  {
    sku: '10101064-001',
    title: 'プレーンソファ 1シーター',
    baseSkuPrefix: '10101064',
    rental_basic_info: {
      productCode: '10101064',
      detailCode: '001',
      kana: 'プレーンソファヌード1シーターホンタイノミ',
      location: 'NY',
      status: 'available'
    },
    rental_pricing: {
      basePrice: 8000,
      depositRate: 0.1,
      discountRules: {
        day2_6_rate: 0.2,
        day7_plus_rate: 0.1
      },
      minimumDays: 1,
      maximumDays: 30
    }
  }
];

// 新しく作成した複数台ある商品のデータ
const newMultiItemsData = [
  {
    exactTitle: 'カリモクソファ モケットグリーン 1シーター',
    baseSkuPrefix: '10101031',
    rental_basic_info: {
      productCode: '10101031',
      detailCode: '001',
      kana: 'カリモクソファモケットグリーン1シーター',
      location: 'NY',
      status: 'available'
    },
    rental_pricing: {
      basePrice: 7000,
      depositRate: 0.1,
      discountRules: {
        day2_6_rate: 0.2,
        day7_plus_rate: 0.1
      },
      minimumDays: 1,
      maximumDays: 30
    }
  },
  {
    exactTitle: 'プレーンソファ 1シーター',
    baseSkuPrefix: '10101064',
    rental_basic_info: {
      productCode: '10101064',
      detailCode: '001',
      kana: 'プレーンソファヌード1シーターホンタイノミ',
      location: 'NY',
      status: 'available'
    },
    rental_pricing: {
      basePrice: 8000,
      depositRate: 0.1,
      discountRules: {
        day2_6_rate: 0.2,
        day7_plus_rate: 0.1
      },
      minimumDays: 1,
      maximumDays: 30
    }
  }
];

// メイン処理
async function setMultiItemsMetafields() {
  console.log('複数台ある商品のJSONメタフィールドを設定しています...');

  // 既存の商品にメタフィールドを設定
  console.log('\n=== 既存の商品にメタフィールドを設定 ===');
  for (const data of testData) {
    console.log(`\n処理中: ${data.title} (${data.sku})`);

    // 商品を検索
    const product = await getProductBySku(data.sku);
    if (!product) {
      console.error(`${data.sku} の商品が見つかりませんでした。スキップします。`);
      continue;
    }

    console.log(`商品を見つけました: ${product.title} (${product.id})`);

    // バリアントの情報を取得
    const variants = product.variants.edges.map(edge => ({
      id: edge.node.id,
      sku: edge.node.sku,
      title: edge.node.title
    }));

    console.log(`バリアント数: ${variants.length}`);

    // バリアントマッピングを作成
    const variantMapping = createVariantMapping(variants, data.baseSkuPrefix);

    // レンタル商品基本情報の設定
    console.log('レンタル商品基本情報を設定中...');
    const basicInfoResult = await setJsonMetafield(
      product.id,
      'rental',
      'basic_info',
      data.rental_basic_info
    );

    if (basicInfoResult) {
      console.log('レンタル商品基本情報を設定しました');
    } else {
      console.error('レンタル商品基本情報の設定に失敗しました');
    }

    // レンタル料金設定の設定
    console.log('レンタル料金設定を設定中...');
    const pricingResult = await setJsonMetafield(
      product.id,
      'rental',
      'pricing',
      data.rental_pricing
    );

    if (pricingResult) {
      console.log('レンタル料金設定を設定しました');
    } else {
      console.error('レンタル料金設定の設定に失敗しました');
    }

    // バリエーションマッピングの設定
    console.log('バリエーションマッピングを設定中...');
    const mappingResult = await setJsonMetafield(
      product.id,
      'rental',
      'variant_mapping',
      variantMapping
    );

    if (mappingResult) {
      console.log('バリエーションマッピングを設定しました');
    } else {
      console.error('バリエーションマッピングの設定に失敗しました');
    }
  }

  // 新しく作成した複数台ある商品にメタフィールドを設定
  console.log('\n=== 新しく作成した複数台ある商品にメタフィールドを設定 ===');
  for (const data of newMultiItemsData) {
    console.log(`\n処理中: ${data.exactTitle}`);

    // 商品を検索
    const product = await getProductByTitle(data.exactTitle);
    if (!product) {
      console.error(`${data.exactTitle} の商品が見つかりませんでした。スキップします。`);
      continue;
    }

    console.log(`商品を見つけました: ${product.title} (${product.id})`);

    // バリアントの情報を取得
    const variants = product.variants.edges.map(edge => ({
      id: edge.node.id,
      sku: edge.node.sku,
      title: edge.node.title
    }));

    console.log(`バリアント数: ${variants.length}`);

    // バリアントマッピングを作成
    const variantMapping = createVariantMapping(variants, data.baseSkuPrefix);

    // レンタル商品基本情報の設定
    console.log('レンタル商品基本情報を設定中...');
    const basicInfoResult = await setJsonMetafield(
      product.id,
      'rental',
      'basic_info',
      data.rental_basic_info
    );

    if (basicInfoResult) {
      console.log('レンタル商品基本情報を設定しました');
    } else {
      console.error('レンタル商品基本情報の設定に失敗しました');
    }

    // レンタル料金設定の設定
    console.log('レンタル料金設定を設定中...');
    const pricingResult = await setJsonMetafield(
      product.id,
      'rental',
      'pricing',
      data.rental_pricing
    );

    if (pricingResult) {
      console.log('レンタル料金設定を設定しました');
    } else {
      console.error('レンタル料金設定の設定に失敗しました');
    }

    // バリエーションマッピングの設定
    console.log('バリエーションマッピングを設定中...');
    const mappingResult = await setJsonMetafield(
      product.id,
      'rental',
      'variant_mapping',
      variantMapping
    );

    if (mappingResult) {
      console.log('バリエーションマッピングを設定しました');
    } else {
      console.error('バリエーションマッピングの設定に失敗しました');
    }
  }

  console.log('\n=== 処理完了 ===');
}

// 実行
setMultiItemsMetafields();
