import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品検索のクエリ
const GET_PRODUCT_BY_SKU = gql`
  query getProductBySku($query: String!) {
    products(first: 1, query: $query) {
      edges {
        node {
          id
          title
          variants(first: 10) {
            edges {
              node {
                id
                sku
                title
              }
            }
          }
        }
      }
    }
  }
`;

// メタフィールドの設定
async function setJsonMetafield(productId, namespace, key, jsonValue) {
  const mutation = gql`
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    metafields: [
      {
        ownerId: productId,
        namespace,
        key,
        value: JSON.stringify(jsonValue),
        type: 'json',
      }
    ]
  };

  try {
    const result = await graphQLClient.request(mutation, variables);
    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', result.metafieldsSet.userErrors);
      return false;
    }
    return true;
  } catch (error) {
    console.error(`メタフィールド ${namespace}.${key} 設定エラー:`, error);
    return false;
  }
}

// SKUで商品を検索
async function getProductBySku(sku) {
  try {
    const result = await graphQLClient.request(GET_PRODUCT_BY_SKU, {
      query: `sku:${sku}`,
    });

    if (result.products.edges.length === 0) {
      console.error(`SKU ${sku} の商品が見つかりませんでした`);
      return null;
    }

    return result.products.edges[0].node;
  } catch (error) {
    console.error('商品検索エラー:', error);
    return null;
  }
}

// テスト用データ
const testData = [
  {
    sku: '10101007-001',
    title: 'ベーシックソファ オフホワイト 1シーター',
    rental_basic_info: {
      productCode: '10101007',
      detailCode: '001',
      kana: 'ベーシックソファオフホワイト1シーターヤマナリ',
      location: 'NY',
      status: 'available'
    },
    rental_pricing: {
      basePrice: 8000,
      depositRate: 0.1,
      discountRules: {
        day2_6_rate: 0.2,
        day7_plus_rate: 0.1
      },
      minimumDays: 1,
      maximumDays: 30
    },
    variant_mapping: {
      '1day': 'gid://shopify/ProductVariant/52583890092200',
      '2day': 'gid://shopify/ProductVariant/52583890124968',
      '3day': 'gid://shopify/ProductVariant/52583890157736',
      '4day': 'gid://shopify/ProductVariant/52583890190504',
      '5day': 'gid://shopify/ProductVariant/52583890747560',
      '6day': 'gid://shopify/ProductVariant/52583890780328',
      '7day': 'gid://shopify/ProductVariant/52583890813096',
      '8plus': 'gid://shopify/ProductVariant/52583890845864'
    }
  },
  {
    sku: '10101008-001',
    title: 'クレーリーソファ ベージュ 1シーター',
    rental_basic_info: {
      productCode: '10101008',
      detailCode: '001',
      kana: 'クレーリーソファベージュ1シーター',
      location: 'PR',
      status: 'available'
    },
    rental_pricing: {
      basePrice: 9000,
      depositRate: 0.1,
      discountRules: {
        day2_6_rate: 0.2,
        day7_plus_rate: 0.1
      },
      minimumDays: 1,
      maximumDays: 30
    },
    variant_mapping: {
      '1day': 'gid://shopify/ProductVariant/52583890845864',
      '2day': 'gid://shopify/ProductVariant/52583890878632',
      '3day': 'gid://shopify/ProductVariant/52583890911400',
      '4day': 'gid://shopify/ProductVariant/52583890944168',
      '5day': 'gid://shopify/ProductVariant/52583890976936',
      '6day': 'gid://shopify/ProductVariant/52583891009704',
      '7day': 'gid://shopify/ProductVariant/52583891042472',
      '8plus': 'gid://shopify/ProductVariant/52583891566760'
    }
  },
  {
    sku: '10101009-001',
    title: 'ウイングソファ アンティークグリーン 1シーター',
    rental_basic_info: {
      productCode: '10101009',
      detailCode: '001',
      kana: 'ウイングソファアンティークグリーン1シーター',
      location: 'NY',
      status: 'available'
    },
    rental_pricing: {
      basePrice: 10000,
      depositRate: 0.1,
      discountRules: {
        day2_6_rate: 0.2,
        day7_plus_rate: 0.1
      },
      minimumDays: 1,
      maximumDays: 30
    },
    variant_mapping: {
      '1day': 'gid://shopify/ProductVariant/52583891599528',
      '2day': 'gid://shopify/ProductVariant/52583891632296',
      '3day': 'gid://shopify/ProductVariant/52583891665064',
      '4day': 'gid://shopify/ProductVariant/52583891697832',
      '5day': 'gid://shopify/ProductVariant/52583891730600',
      '6day': 'gid://shopify/ProductVariant/52583891763368',
      '7day': 'gid://shopify/ProductVariant/52583891796136',
      '8plus': 'gid://shopify/ProductVariant/52583891828904'
    }
  }
];

// メイン処理
async function setJsonMetafields() {
  console.log('JSONメタフィールドを設定しています...');
  
  for (const data of testData) {
    console.log(`\n処理中: ${data.title} (${data.sku})`);
    
    // 商品を検索
    const product = await getProductBySku(data.sku);
    if (!product) {
      console.error(`${data.sku} の商品が見つかりませんでした。スキップします。`);
      continue;
    }
    
    console.log(`商品を見つけました: ${product.title} (${product.id})`);
    
    // レンタル商品基本情報の設定
    console.log('レンタル商品基本情報を設定中...');
    const basicInfoResult = await setJsonMetafield(
      product.id,
      'rental',
      'basic_info',
      data.rental_basic_info
    );
    
    if (basicInfoResult) {
      console.log('レンタル商品基本情報を設定しました');
    } else {
      console.error('レンタル商品基本情報の設定に失敗しました');
    }
    
    // レンタル料金設定の設定
    console.log('レンタル料金設定を設定中...');
    const pricingResult = await setJsonMetafield(
      product.id,
      'rental',
      'pricing',
      data.rental_pricing
    );
    
    if (pricingResult) {
      console.log('レンタル料金設定を設定しました');
    } else {
      console.error('レンタル料金設定の設定に失敗しました');
    }
    
    // バリエーションマッピングの設定
    console.log('バリエーションマッピングを設定中...');
    const mappingResult = await setJsonMetafield(
      product.id,
      'rental',
      'variant_mapping',
      data.variant_mapping
    );
    
    if (mappingResult) {
      console.log('バリエーションマッピングを設定しました');
    } else {
      console.error('バリエーションマッピングの設定に失敗しました');
    }
  }
  
  console.log('\n=== 処理完了 ===');
}

// 実行
setJsonMetafields();
