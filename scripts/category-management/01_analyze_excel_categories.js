#!/usr/bin/env node

/**
 * Excelファイルから商品カテゴリ階層構造を分析するスクリプト
 * 
 * 使用方法:
 * node scripts/category-management/01_analyze_excel_categories.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// CSVファイルのパス
const RENTAL_ITEMS_CSV = path.join(__dirname, '../../master-data-csv/rental_items_20250509_153839.csv');
const CATEGORY_MASTER_CSV = path.join(__dirname, '../../master-data-csv/other-master-data/商品カテゴリマスタ.csv');
const OUTPUT_DIR = path.join(__dirname, '../../master-data-csv/generated');

/**
 * CSVファイルを読み込んでパースする
 */
function parseCSV(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8');
  const lines = content.split('\n').filter(line => line.trim());
  const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
  
  return lines.slice(1).map(line => {
    const values = line.split(',').map(v => v.replace(/"/g, '').trim());
    const obj = {};
    headers.forEach((header, index) => {
      obj[header] = values[index] || '';
    });
    return obj;
  });
}

/**
 * SKUからカテゴリ情報を抽出
 */
function extractCategoryFromSKU(sku) {
  const match = sku.match(/^(\d{3})-(\d{2})-(\d{3})$/);
  if (!match) return null;
  
  return {
    categoryCode: match[1],
    subCategoryCode: match[2],
    serialNumber: match[3]
  };
}

/**
 * 商品データからカテゴリ構造を分析
 */
function analyzeCategoryStructure() {
  console.log('=== 商品カテゴリ構造分析開始 ===');
  
  // 商品データを読み込み
  const rentalItems = parseCSV(RENTAL_ITEMS_CSV);
  console.log(`商品データ読み込み完了: ${rentalItems.length}件`);
  
  // 既存カテゴリマスタを読み込み
  const categoryMaster = parseCSV(CATEGORY_MASTER_CSV);
  console.log(`カテゴリマスタ読み込み完了: ${categoryMaster.length}件`);
  
  // カテゴリ分析結果
  const categoryAnalysis = new Map();
  const subCategoryAnalysis = new Map();
  const skuPatterns = new Map();
  
  // 商品データを分析
  rentalItems.forEach((item, index) => {
    const sku = item['型番'];
    const menu = item['家具小道具メニュー'];
    const majorCategory = item['家具小道具大カテゴリー'];
    const minorCategory = item['家具小道具小カテゴリー'];
    
    if (!sku) return;
    
    // SKU構造を分析
    const skuInfo = extractCategoryFromSKU(sku);
    if (skuInfo) {
      const { categoryCode, subCategoryCode } = skuInfo;
      
      // カテゴリコード分析
      if (!categoryAnalysis.has(categoryCode)) {
        categoryAnalysis.set(categoryCode, {
          code: categoryCode,
          menu: new Set(),
          majorCategory: new Set(),
          minorCategory: new Set(),
          count: 0,
          skus: []
        });
      }
      
      const catData = categoryAnalysis.get(categoryCode);
      catData.count++;
      catData.skus.push(sku);
      if (menu) catData.menu.add(menu);
      if (majorCategory) catData.majorCategory.add(majorCategory);
      if (minorCategory) catData.minorCategory.add(minorCategory);
      
      // サブカテゴリ分析
      const subKey = `${categoryCode}-${subCategoryCode}`;
      if (!subCategoryAnalysis.has(subKey)) {
        subCategoryAnalysis.set(subKey, {
          categoryCode,
          subCategoryCode,
          count: 0,
          skus: [],
          majorCategory: new Set(),
          minorCategory: new Set()
        });
      }
      
      const subData = subCategoryAnalysis.get(subKey);
      subData.count++;
      subData.skus.push(sku);
      if (majorCategory) subData.majorCategory.add(majorCategory);
      if (minorCategory) subData.minorCategory.add(minorCategory);
    }
    
    // 進捗表示
    if ((index + 1) % 1000 === 0) {
      console.log(`処理中: ${index + 1}/${rentalItems.length}`);
    }
  });
  
  console.log('\n=== 分析結果 ===');
  console.log(`発見されたカテゴリコード: ${categoryAnalysis.size}個`);
  console.log(`発見されたサブカテゴリ: ${subCategoryAnalysis.size}個`);
  
  // 結果をソート
  const sortedCategories = Array.from(categoryAnalysis.entries())
    .sort(([a], [b]) => a.localeCompare(b));
  
  const sortedSubCategories = Array.from(subCategoryAnalysis.entries())
    .sort(([a], [b]) => a.localeCompare(b));
  
  return {
    categories: sortedCategories,
    subCategories: sortedSubCategories,
    categoryMaster
  };
}

/**
 * 分析結果をCSVファイルに出力
 */
function outputAnalysisResults(analysis) {
  // 出力ディレクトリを作成
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }
  
  // カテゴリ分析結果を出力
  const categoryCSV = [
    'カテゴリコード,商品数,メニュー,大カテゴリ,小カテゴリ,サンプルSKU'
  ];
  
  analysis.categories.forEach(([code, data]) => {
    const menus = Array.from(data.menu).join(';');
    const majors = Array.from(data.majorCategory).join(';');
    const minors = Array.from(data.minorCategory).join(';');
    const sampleSKU = data.skus[0] || '';
    
    categoryCSV.push(`${code},${data.count},"${menus}","${majors}","${minors}",${sampleSKU}`);
  });
  
  const categoryOutputPath = path.join(OUTPUT_DIR, 'category_analysis.csv');
  fs.writeFileSync(categoryOutputPath, categoryCSV.join('\n'), 'utf-8');
  console.log(`カテゴリ分析結果を出力: ${categoryOutputPath}`);
  
  // サブカテゴリ分析結果を出力
  const subCategoryCSV = [
    'カテゴリコード,サブカテゴリコード,商品数,大カテゴリ,小カテゴリ,サンプルSKU'
  ];
  
  analysis.subCategories.forEach(([key, data]) => {
    const majors = Array.from(data.majorCategory).join(';');
    const minors = Array.from(data.minorCategory).join(';');
    const sampleSKU = data.skus[0] || '';
    
    subCategoryCSV.push(
      `${data.categoryCode},${data.subCategoryCode},${data.count},"${majors}","${minors}",${sampleSKU}`
    );
  });
  
  const subCategoryOutputPath = path.join(OUTPUT_DIR, 'subcategory_analysis.csv');
  fs.writeFileSync(subCategoryOutputPath, subCategoryCSV.join('\n'), 'utf-8');
  console.log(`サブカテゴリ分析結果を出力: ${subCategoryOutputPath}`);
  
  // 詳細レポートを出力
  const reportLines = [
    '# 商品カテゴリ分析レポート',
    '',
    `分析日時: ${new Date().toLocaleString('ja-JP')}`,
    `対象商品数: ${analysis.categories.reduce((sum, [, data]) => sum + data.count, 0)}件`,
    `カテゴリコード数: ${analysis.categories.length}個`,
    `サブカテゴリ数: ${analysis.subCategories.length}個`,
    '',
    '## カテゴリ別商品数',
    ''
  ];
  
  analysis.categories.forEach(([code, data]) => {
    const categoryName = analysis.categoryMaster.find(c => c['商品カテゴリコード'] === code)?.['商品カテゴリ名称'] || '不明';
    reportLines.push(`- ${code} (${categoryName}): ${data.count}件`);
  });
  
  const reportPath = path.join(OUTPUT_DIR, 'category_analysis_report.md');
  fs.writeFileSync(reportPath, reportLines.join('\n'), 'utf-8');
  console.log(`分析レポートを出力: ${reportPath}`);
}

/**
 * メイン処理
 */
function main() {
  try {
    const analysis = analyzeCategoryStructure();
    outputAnalysisResults(analysis);
    
    console.log('\n=== 分析完了 ===');
    console.log('生成されたファイル:');
    console.log('- master-data-csv/generated/category_analysis.csv');
    console.log('- master-data-csv/generated/subcategory_analysis.csv');
    console.log('- master-data-csv/generated/category_analysis_report.md');
    
  } catch (error) {
    console.error('エラーが発生しました:', error);
    process.exit(1);
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
