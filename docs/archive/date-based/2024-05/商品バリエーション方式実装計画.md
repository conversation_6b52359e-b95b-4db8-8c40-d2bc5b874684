# 商品バリエーション方式実装計画

## 概要

レンタル商品の料金計算をドラフトオーダー方式から商品バリエーション方式に変更する計画です。この方式では、レンタル日数に応じた8種類のバリエーションを各商品に設定し、標準的なShopifyカートとチェックアウトフローを使用します。

## 背景

現在のドラフトオーダー方式では、以下の問題が発生しています：

1. APIエンドポイントへのリクエストでCORSエラーが発生
2. 認証の問題によりドラフトオーダーの作成に失敗
3. チェックアウトページへのリダイレクトが正常に機能しない

これらの問題を解決するため、より標準的なShopifyの機能を活用した商品バリエーション方式に切り替えます。

## 料金計算ロジック

レンタル料金は以下のルールに基づいて計算されます：

1. **1日目**: 基本料金の100%
2. **2〜7日目**: 各日基本料金の20%
3. **8日目以降**: 各日基本料金の10%
4. **仮予約**: 基本料金の10%

これに基づき、以下の8種類のバリエーションを設定します：

1. **1日レンタル**: 基本料金の100%
2. **2日レンタル**: 基本料金の100% + 20% = 120%
3. **3日レンタル**: 基本料金の100% + 20% + 20% = 140%
4. **4日レンタル**: 基本料金の100% + 20% + 20% + 20% = 160%
5. **5日レンタル**: 基本料金の100% + 20% + 20% + 20% + 20% = 180%
6. **6日レンタル**: 基本料金の100% + 20% + 20% + 20% + 20% + 20% = 200%
7. **7日レンタル**: 基本料金の100% + 20% + 20% + 20% + 20% + 20% + 20% = 220%

8日以上のレンタルについては、7日レンタルを基本として、8日目以降は1日あたり基本料金の10%を追加料金として自動計算します。

## 日数計算ロジック

レンタル日数の計算では、以下の日を除外します：

1. **日曜日**: 営業日数に含めない
2. **指定休業日**: 営業日数に含めない
3. **年末年始(12/29〜1/4)**: 営業日数に含めない

これにより、実際のレンタル期間（開始日から終了日まで）と営業日数（料金計算の基準となる日数）が異なる場合があります。

## 実装計画

### 1. 商品バリエーションの設定

各商品に8種類のバリエーションを設定します：

```
オプション名: レンタル日数
オプション値: 1日, 2日, 3日, 4日, 5日, 6日, 7日, 8日以上
```

各バリエーションの価格は、基本料金に基づいて計算します。

### 2. 商品ページの実装

商品ページでは、以下の機能を実装します：

1. **日付選択機能**
   - 開始日と終了日を選択するカレンダー
   - 選択された期間から営業日数を計算

2. **バリエーション自動選択**
   - 計算された営業日数に基づいて適切なバリエーションを自動選択
   - 8日以上の場合は「8日以上」バリエーションを選択

3. **レンタル情報の保存**
   - 選択された開始日、終了日、営業日数をLine Item Propertiesとして保存

### 3. カート画面の実装

カート画面では、以下の機能を実装します：

1. **レンタル情報の表示**
   - 各商品のレンタル期間（開始日と終了日）を表示
   - 営業日数を表示

2. **通常のチェックアウトフロー**
   - 標準的なShopifyのチェックアウトボタンを使用
   - ドラフトオーダー関連のカスタムコードを削除または無効化

## 実装手順

1. **新しいブランチの作成**
   - `feature/standard-cart-implementation`ブランチを作成

2. **商品バリエーションの設定**
   - テスト商品に8種類のバリエーションを設定
   - 価格計算ロジックを確認

3. **商品ページの修正**
   - 日付選択機能を維持
   - バリエーション自動選択機能を追加
   - カートに追加する際のプロパティ設定を修正

4. **カート画面の修正**
   - レンタル情報の表示を追加
   - ドラフトオーダー関連のコードを削除または無効化

5. **テストと検証**
   - 日付選択からチェックアウトまでの一連のフローをテスト
   - 料金計算が正しく行われることを確認

## メリット

1. **シンプルな実装**
   - 標準的なShopifyの機能を使用
   - 複雑なカスタムコードが少ない

2. **安定性**
   - Shopifyの標準機能に依存するため、安定している
   - APIエラーやCORS問題のリスクが低い

3. **ユーザー体験**
   - 標準的なShopifyのチェックアウトフローを使用
   - 待ち時間なくスムーズにチェックアウトできる

## 8日以上のレンタルの自動計算

8日以上のレンタルについては、以下の方法で自動計算します：

1. **基本アプローチ**
   - 7日レンタルのバリエーションを基本として使用
   - 8日目以降は、1日あたり基本料金の10%を追加料金として計算

2. **実装方法**
   - 実際のレンタル日数をLine Item Propertiesとして保存
   - 追加料金を別商品としてカートに自動追加
   - カート画面で追加料金を明示的に表示

3. **料金計算例**
   - 9日間レンタル：7日レンタル料金（基本料金の220%）+ 基本料金の10%×2日
   - 10日間レンタル：7日レンタル料金（基本料金の220%）+ 基本料金の10%×3日

### 追加料金商品の設定

追加料金を別の商品として扱うために、以下の手順で設定します：

1. **追加料金用の商品を作成**
   - 「レンタル追加料金」などの名前で商品を作成
   - 価格は1円などの最小単位に設定
   - この商品はカタログには表示しない設定にする

2. **カートでの表示調整**
   - カスタムスクリプトで、追加料金商品の表示を調整
   - 「〇〇の追加料金（8日目以降×△日）」などと表示

3. **価格の調整**
   - カート追加時に、計算した追加料金に基づいて数量を調整
   - または、Line Item Propertiesに金額を保存し、後で注文処理時に参照

## 注意点

2. **商品バリエーションの管理**
   - 新しい商品を追加する際に、8種類のバリエーションを設定する必要がある
   - テンプレート商品を作成するか、一括編集ツールを使用することを推奨

3. **在庫管理**
   - 同じ商品の異なるバリエーションでも、在庫は共有されるように設定する必要がある

## 今後の課題

1. **商品バリエーションの一括設定**
   - 多数の商品に対して効率的にバリエーションを設定する方法の検討
   - Shopify APIを使用した自動化スクリプトの開発

2. **予約カレンダーとの連携**
   - 予約状況をカレンダーに表示する機能の維持
   - 在庫管理との連携

3. **注文後の処理**
   - 注文情報からレンタル期間を抽出する機能
   - 予約管理システムとの連携
