# Shopify Dev MCP Server セットアップガイド

**作成日**: 2025年5月25日  
**目的**: Shopify Dev MCP ServerをClaude DesktopやCursorで使用するための設定

## 概要

Shopify Dev MCP Serverは、Model Context Protocol (MCP)を使用してShopify開発を支援するツールです。
以下の機能を提供します：

- **search_dev_docs**: shopify.devドキュメントの検索
- **introspect_admin_schema**: Shopify Admin GraphQLスキーマへのアクセスと検索
- **shopify_admin_graphql**: Admin API用のGraphQLクエリ作成支援

## セットアップ手順

### 1. プロジェクトのビルド

プロジェクトは既にビルド済みです。再ビルドが必要な場合：

```bash
cd /Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp/dev-mcp
npm install
npm run build
```

### 2. Claude Desktop設定

Claude Desktopの設定ファイルに以下を追加します：

#### macOS/Linux
`~/Library/Application Support/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "shopify-dev-mcp": {
      "command": "node",
      "args": ["/Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp/dev-mcp/dist/index.js"]
    }
  }
}
```

#### または、npxを使用する場合（推奨）

```json
{
  "mcpServers": {
    "shopify-dev-mcp": {
      "command": "npx",
      "args": ["-y", "@shopify/dev-mcp@latest"]
    }
  }
}
```

### 3. Cursor設定

Cursorを使用する場合は、同様の設定を追加します。
詳細は[Cursor MCP documentation](https://docs.cursor.com/context/model-context-protocol)を参照してください。

## 使用方法

### 1. Shopifyドキュメント検索

```
search_dev_docs("GraphQL mutations")
```

### 2. Admin GraphQLスキーマの調査

```
introspect_admin_schema("Product")
```

### 3. GraphQLクエリ作成支援

プロンプト `shopify_admin_graphql` を使用してGraphQLクエリの作成を支援します。

## トラブルシューティング

### エラー: "command not found"

- Node.jsがインストールされていることを確認
- パスが正しいことを確認

### エラー: "permission denied"

```bash
chmod +x /Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp/dev-mcp/dist/index.js
```

### Claude Desktopの再起動

設定変更後は、Claude Desktopを再起動する必要があります。

## 関連リンク

- [Shopify Dev MCP GitHub](https://github.com/Shopify/dev-mcp)
- [Model Context Protocol](https://modelcontextprotocol.io/)
- [Shopify GraphQL Admin API](https://shopify.dev/docs/api/admin-graphql)

---

本ドキュメントは設定の参考として作成されました。
最終更新: 2025年5月25日