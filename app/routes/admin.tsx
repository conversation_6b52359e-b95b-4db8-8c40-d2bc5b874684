/**
 * 管理画面のルートレイアウト
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, Outlet, useLoaderData } from "@remix-run/react";
import {
  Frame,
  Navigation,
  TopBar,
  AppProvider,
  ContextualSaveBar
} from "@shopify/polaris";
import {
  HomeIcon,
  OrderIcon,
  ProductIcon,
  PersonIcon,
  SettingsIcon
} from "@shopify/polaris-icons";
import { useState, useCallback } from "react";
import { authenticate } from "../shopify.server";

/**
 * ローダー関数
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin } = await authenticate.admin(request);
  return json({ shop: admin.shop });
}

/**
 * 管理画面のルートレイアウトコンポーネント
 */
export default function AdminLayout() {
  const { shop } = useLoaderData<typeof loader>();
  const [mobileNavigationActive, setMobileNavigationActive] = useState(false);

  const toggleMobileNavigationActive = useCallback(
    () => setMobileNavigationActive((active) => !active),
    [],
  );

  const navigationMarkup = (
    <Navigation location="/">
      <Navigation.Section
        items={[
          {
            url: '/admin',
            label: 'ダッシュボード',
            icon: HomeIcon,
          },
          {
            url: '/admin/bookings',
            label: '予約管理',
            icon: OrderIcon,
          },
          {
            url: '/admin/products',
            label: '商品管理',
            icon: ProductIcon,
          },
          {
            url: '/admin/customers',
            label: '顧客管理',
            icon: PersonIcon,
          },
          {
            url: '/admin/settings',
            label: '設定',
            icon: SettingsIcon,
          },
        ]}
      />
    </Navigation>
  );

  const topBarMarkup = (
    <TopBar
      showNavigationToggle
      onNavigationToggle={toggleMobileNavigationActive}
      userMenu={<TopBar.UserMenu name="管理者" initials="管" />}
    />
  );

  return (
    <AppProvider i18n={{}}>
      <Frame
        topBar={topBarMarkup}
        navigation={navigationMarkup}
        showMobileNavigation={mobileNavigationActive}
        onNavigationDismiss={toggleMobileNavigationActive}
      >
        <Outlet />
      </Frame>
    </AppProvider>
  );
}
