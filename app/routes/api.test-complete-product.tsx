/**
 * 完全商品登録テスト用APIエンドポイント
 */

import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "~/shopify.server";
import { CompleteProductCreatorService, CSVProductData } from "~/services/shopify/complete-product-creator.service";

export async function action({ request }: ActionFunctionArgs) {
  try {
    const { admin } = await authenticate.admin(request);
    
    if (request.method !== "POST") {
      return json({ error: "Method not allowed" }, { status: 405 });
    }

    const body = await request.json();
    
    // テスト用のCSVデータ
    const testProduct: CSVProductData = {
      menu: 'PROP',
      majorCategory: 'ニュアンスオブジェ;花器',
      minorCategory: 'その他オブジェ;ゴールド・シルバー',
      name: '花器　シルバー　穴開きボトル型',
      modelNumber: '212-05-023',
      sizeW: '14',
      sizeD: '0',
      sizeH: '31',
      sizeSH: '0',
      sizeOther: '',
      color: 'シルバー',
      colorOther: '',
      material: '',
      other: '',
      stock: '1',
      rentalPrice1Day: '1500',
      rentalPrice2Days: '1800',
      rentalPrice3Days: '2100',
      rentalPrice4Days: '2400',
      publicStatus: '公開',
      newStatus: 'あり',
      campaign: '2025 NEW ITEMS'
    };

    const productCreator = new CompleteProductCreatorService();
    
    console.log('=== 完全商品登録テスト開始 ===');
    
    const result = await productCreator.createCompleteProduct(admin, testProduct, {
      location: 'NY',
      createProvisionalVariants: false
    });

    console.log('=== 完全商品登録テスト完了 ===');

    return json({
      success: result.success,
      shopifyProductId: result.shopifyProductId,
      createdVariants: result.createdVariants,
      inventoryUpdated: result.inventoryUpdated,
      metafieldsSet: result.metafieldsSet,
      errors: result.errors,
      productUrl: result.shopifyProductId 
        ? `https://peaces-test-block.myshopify.com/admin/products/${result.shopifyProductId}`
        : null,
      expectedSKUs: [
        '212-05-023-1D',
        '212-05-023-2D', 
        '212-05-023-3D',
        '212-05-023-4D',
        '212-05-023-5D',
        '212-05-023-6D',
        '212-05-023-7D',
        '212-05-023-8D+'
      ]
    });

  } catch (error) {
    console.error('完全商品登録テストエラー:', error);
    return json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}

export async function loader() {
  return json({ message: "POST method required" }, { status: 405 });
}
