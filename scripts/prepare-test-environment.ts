/**
 * テスト環境準備スクリプト
 *
 * このスクリプトは、テスト実行前に環境をクリーンアップし、必要なデータを準備します。
 * 実行方法: npx tsx scripts/prepare-test-environment.ts [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { addDays, format } from 'date-fns';
import { execSync } from 'child_process';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * テスト環境を準備する関数
 */
async function prepareTestEnvironment() {
  try {
    console.log('テスト環境の準備を開始します...');

    // 商品IDをコマンドライン引数から取得
    const shopifyProductId = process.argv[2] || '3333333333';

    // 1. テスト予約のクリーンアップ
    console.log('1. テスト予約のクリーンアップを実行します...');
    execSync(`npx tsx scripts/cleanup-test-bookings.ts ${shopifyProductId}`, { stdio: 'inherit' });

    // 2. テスト商品の確認
    console.log('\n2. テスト商品の確認を行います...');
    const testProducts = await prisma.product.findMany({
      where: {
        OR: [
          { title: { contains: 'テスト商品' } },
          { shopifyId: shopifyProductId }
        ]
      }
    });

    if (testProducts.length === 0) {
      console.log(`商品ID ${shopifyProductId} が見つかりません。テスト商品を作成します...`);
      try {
        execSync('npx tsx scripts/create-test-products.ts', { stdio: 'inherit' });
      } catch (error) {
        console.error('テスト商品の作成に失敗しました:', error);
      }
    } else {
      console.log(`${testProducts.length}件のテスト商品が見つかりました:`);
      testProducts.forEach(product => {
        console.log(`- ${product.title} (ID: ${product.id}, ShopifyID: ${product.shopifyId})`);
      });
    }

    // 3. 在庫カレンダーの確認
    console.log('\n3. 在庫カレンダーの確認を行います...');
    for (const product of testProducts) {
      const inventoryCount = await prisma.inventoryCalendar.count({
        where: { productId: product.id }
      });

      console.log(`商品 ${product.title} (${product.id}) の在庫カレンダー: ${inventoryCount}件`);

      // 在庫カレンダーが不足している場合は作成
      if (inventoryCount < 365) {
        console.log(`在庫カレンダーが不足しています。在庫カレンダーをリセットします...`);
        try {
          // 在庫カレンダーを削除
          await prisma.inventoryCalendar.deleteMany({
            where: { productId: product.id }
          });

          // 今日から1年分の在庫カレンダーを作成
          const today = new Date();
          const endDate = addDays(today, 365);

          console.log(`${format(today, 'yyyy-MM-dd')}から${format(endDate, 'yyyy-MM-dd')}までの在庫カレンダーを作成します`);

          // 日付ごとに在庫カレンダーを作成（バッチ処理）
          const calendarEntries = [];
          for (let date = new Date(today); date <= endDate; date = addDays(date, 1)) {
            calendarEntries.push({
              shop: product.shop,
              productId: product.id,
              shopifyProductId: product.shopifyId || '0',
              date: new Date(date),
              isAvailable: true,
              createdAt: new Date(),
              updatedAt: new Date()
            });

            // 100件ごとにバッチ処理
            if (calendarEntries.length >= 100) {
              await prisma.inventoryCalendar.createMany({
                data: calendarEntries,
                skipDuplicates: true
              });
              calendarEntries.length = 0;
            }
          }

          // 残りのエントリを処理
          if (calendarEntries.length > 0) {
            await prisma.inventoryCalendar.createMany({
              data: calendarEntries,
              skipDuplicates: true
            });
          }

          console.log(`在庫カレンダーの作成が完了しました`);
        } catch (error) {
          console.error(`商品 ${product.id} の在庫カレンダー作成中にエラーが発生しました:`, error);
        }
      }
    }

    console.log('\nテスト環境の準備が完了しました');
  } catch (error) {
    console.error('テスト環境の準備中にエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
prepareTestEnvironment();
