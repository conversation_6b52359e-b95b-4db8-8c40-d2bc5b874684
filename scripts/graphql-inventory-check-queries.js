/**
 * GraphQL App用 在庫確認クエリ集
 *
 * Shopify GraphQL App (https://shopify.dev/apps/tools/graphql-admin-api) で実行してください
 */

// 1. 最近作成された商品の詳細在庫情報を取得
const GET_RECENT_PRODUCTS_WITH_INVENTORY = `
query getRecentProductsWithInventory {
  products(first: 3, reverse: true) {
    edges {
      node {
        id
        title
        handle
        createdAt
        variants(first: 20) {
          edges {
            node {
              id
              title
              sku
              price
              inventoryItem {
                id
                tracked
                requiresShipping
                inventoryLevels(first: 10) {
                  edges {
                    node {
                      id
                      quantities(names: ["available", "committed", "incoming", "on_hand"]) {
                        edges {
                          node {
                            name
                            quantity
                          }
                        }
                      }
                      location {
                        id
                        name
                        address {
                          formatted
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
`;

// 2. 特定商品の在庫詳細（商品IDを指定）
const GET_SPECIFIC_PRODUCT_INVENTORY = `
query getSpecificProductInventory($id: ID!) {
  product(id: $id) {
    id
    title
    variants(first: 20) {
      edges {
        node {
          id
          title
          sku
          price
          inventoryItem {
            id
            tracked
            inventoryLevels(first: 10) {
              edges {
                node {
                  id
                  quantities(names: ["available", "committed", "incoming", "on_hand"]) {
                    edges {
                      node {
                        name
                        quantity
                      }
                    }
                  }
                  location {
                    id
                    name
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
`;

// 変数例（実際の商品IDに変更してください）
const SPECIFIC_PRODUCT_VARIABLES = {
  "id": "gid://shopify/Product/8981377941672"  // ワイングラス マスカット柄のID
};

// 3. ロケーション一覧取得
const GET_LOCATIONS = `
query getLocations {
  locations(first: 10) {
    edges {
      node {
        id
        name
        address {
          formatted
        }
        fulfillmentService {
          id
          serviceName
        }
      }
    }
  }
}
`;

// 4. 在庫設定テスト用mutation（実際のIDに変更して使用）
const SET_INVENTORY_TEST = `
mutation inventorySetQuantities($input: InventorySetQuantitiesInput!) {
  inventorySetQuantities(input: $input) {
    inventoryAdjustmentGroup {
      id
      changes {
        name
        delta
        quantityAfterChange
      }
    }
    userErrors {
      field
      message
    }
  }
}
`;

// 在庫設定テスト用変数（実際のIDに変更してください）
const SET_INVENTORY_TEST_VARIABLES = {
  "input": {
    "name": "available",
    "reason": "correction",
    "referenceDocumentUri": "manual-test",
    "ignoreCompareQuantity": true,
    "quantities": [
      {
        "inventoryItemId": "gid://shopify/InventoryItem/YOUR_INVENTORY_ITEM_ID",
        "locationId": "gid://shopify/Location/69852102824",
        "quantity": 1
      }
    ]
  }
};

// 5. 在庫履歴確認
const GET_INVENTORY_HISTORY = `
query getInventoryHistory($inventoryItemId: ID!) {
  inventoryItem(id: $inventoryItemId) {
    id
    sku
    tracked
    inventoryHistoryUrl
    inventoryLevels(first: 10) {
      edges {
        node {
          id
          quantities(names: ["available", "committed", "incoming", "on_hand"]) {
            edges {
              node {
                name
                quantity
              }
            }
          }
          location {
            id
            name
          }
        }
      }
    }
  }
}
`;

/**
 * 使用手順:
 *
 * ステップ1: GET_RECENT_PRODUCTS_WITH_INVENTORY を実行
 * - 最近作成された商品の在庫状況を確認
 * - 在庫が0になっているバリエーションを特定
 *
 * ステップ2: GET_LOCATIONS を実行
 * - 利用可能なロケーションを確認
 * - メインロケーションのIDを取得
 *
 * ステップ3: GET_SPECIFIC_PRODUCT_INVENTORY を実行
 * - 特定商品の詳細在庫情報を確認
 * - SPECIFIC_PRODUCT_VARIABLES の id を実際の商品IDに変更
 *
 * ステップ4: SET_INVENTORY_TEST を実行（必要に応じて）
 * - 手動で在庫を設定
 * - SET_INVENTORY_TEST_VARIABLES の inventoryItemId を実際のIDに変更
 *
 * ステップ5: GET_INVENTORY_HISTORY を実行（トラブルシューティング用）
 * - 在庫変更履歴を確認
 *
 * 注意事項:
 * - GraphQL App (https://shopify.dev/apps/tools/graphql-admin-api) で実行
 * - YOUR_INVENTORY_ITEM_ID を実際のInventoryItemIDに変更
 * - 在庫設定には write_inventory 権限が必要
 */

console.log('GraphQL App用在庫確認クエリが準備されました');
console.log('上記のクエリをShopify GraphQL Appで順番に実行してください');

// 実行例の出力
console.log('\n=== 実行例 ===');
console.log('1. GET_RECENT_PRODUCTS_WITH_INVENTORY を実行');
console.log('2. 結果から商品IDとInventoryItemIDを取得');
console.log('3. 在庫が0の場合、SET_INVENTORY_TEST で手動設定');
console.log('4. 再度 GET_RECENT_PRODUCTS_WITH_INVENTORY で確認');
