import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const endpoint = `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`;
const graphQLClient = new GraphQLClient(endpoint, {
  headers: {
    'X-Shopify-Access-Token': ACCESS_TOKEN,
    'Content-Type': 'application/json',
  },
});

// メタフィールド定義の取得
async function getMetafieldDefinitions() {
  const query = gql`
    query getMetafieldDefinitions {
      metafieldDefinitions(first: 100, ownerType: PRODUCT) {
        edges {
          node {
            id
            name
            namespace
            key
            description
            type {
              name
            }
            validationStatus
          }
        }
      }
    }
  `;

  try {
    const data = await graphQLClient.request(query);
    return data.metafieldDefinitions.edges.map(edge => edge.node);
  } catch (error) {
    console.error('メタフィールド定義の取得エラー:', error);
    return [];
  }
}

// メイン処理
async function listMetafieldDefinitions() {
  try {
    console.log('メタフィールド定義を取得中...');
    const definitions = await getMetafieldDefinitions();
    
    if (definitions.length === 0) {
      console.log('メタフィールド定義が見つかりませんでした');
      return;
    }
    
    console.log(`\n=== 商品メタフィールド定義一覧 (${definitions.length}件) ===\n`);
    
    // 名前空間ごとにグループ化
    const groupedByNamespace = {};
    definitions.forEach(def => {
      if (!groupedByNamespace[def.namespace]) {
        groupedByNamespace[def.namespace] = [];
      }
      groupedByNamespace[def.namespace].push(def);
    });
    
    // 名前空間ごとに表示
    for (const [namespace, defs] of Object.entries(groupedByNamespace)) {
      console.log(`\n## 名前空間: ${namespace}`);
      
      defs.forEach(def => {
        console.log(`\n  - 名前: ${def.name || def.key}`);
        console.log(`    キー: ${def.key}`);
        console.log(`    タイプ: ${def.type.name}`);
        if (def.description) console.log(`    説明: ${def.description}`);
        console.log(`    ID: ${def.id}`);
        console.log(`    ステータス: ${def.validationStatus}`);
      });
    }
    
    // JavaScriptでの使用例を表示
    console.log('\n\n=== JavaScriptでの使用例 ===\n');
    console.log('const metafieldDefinitions = {');
    
    for (const def of definitions) {
      console.log(`  ${def.key}: {`);
      console.log(`    namespace: '${def.namespace}',`);
      console.log(`    key: '${def.key}',`);
      console.log(`    type: '${def.type.name}',`);
      if (def.name) console.log(`    name: '${def.name}',`);
      if (def.description) console.log(`    description: '${def.description}',`);
      console.log('  },');
    }
    
    console.log('};');
    
  } catch (error) {
    console.error('処理中にエラーが発生しました:', error);
  }
}

// 実行
listMetafieldDefinitions();
