/**
 * 複数商品の同時予約テスト（拡張版）
 *
 * このスクリプトは、複数商品の同時予約処理をテストし、詳細な分析結果を提供します。
 * 実行方法: npx tsx scripts/test-multi-product-booking.ts [商品ID1] [商品ID2]
 *
 * 機能:
 * - 複数商品の同時予約テスト
 * - 詳細なテスト結果の分析
 * - 標準化されたテスト結果の出力
 * - JSONとMarkdownレポートの生成
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';
import { addDays, format, differenceInMilliseconds } from 'date-fns';
import { ja } from 'date-fns/locale';
import { CartService } from '../app/services/cart.service';
import { BookingService } from '../app/services/booking.service';
import * as fs from 'fs';
import * as path from 'path';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト結果の型定義
interface TestResult {
  testName: string;
  success: boolean;
  timestamp: string;
  message?: string;
  error?: string;
  details?: Record<string, any>;
  duration?: number; // ミリ秒単位
}

// テスト結果セットの型定義
interface TestResultSet {
  timestamp: string;
  duration: number;
  totalTests: number;
  successCount: number;
  failureCount: number;
  tests: TestResult[];
  environment: {
    productId1: string;
    productId2: string;
    shop: string;
    customer: {
      email: string;
      name: string;
      phone: string;
      address: string;
    };
    startDaysFromNow: number;
    durationDays: number;
    nodeVersion: string;
    os: string;
  };
  performance: {
    averageTestDuration: number;
    slowestTest: {
      name: string;
      duration: number;
    };
    fastestTest: {
      name: string;
      duration: number;
    };
  };
}

// テスト設定
const config = {
  // テストする商品のShopify ID
  productId1: process.argv[2] || '123456789', // コマンドライン引数から取得、または既定値を使用
  productId2: process.argv[3] || '987654321', // コマンドライン引数から取得、または既定値を使用

  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',

  // テスト用の顧客情報
  customer: {
    email: '<EMAIL>',
    name: 'テストユーザー',
    phone: '090-1234-5678',
    address: '東京都渋谷区'
  },

  // 予約日数（今日から何日後に予約するか）
  startDaysFromNow: 7,
  durationDays: 3,

  // レポート出力設定
  outputDir: path.join(process.cwd(), 'docs/test-results')
};

/**
 * 商品情報を取得する関数
 */
async function getProductInfo(shopifyProductId: string) {
  try {
    // Prismaから商品情報を取得
    const product = await prisma.product.findFirst({
      where: { shopifyId: shopifyProductId }
    });

    if (!product) {
      console.error(`商品ID ${shopifyProductId} が見つかりません`);
      return null;
    }

    return product;
  } catch (error) {
    console.error('商品情報取得エラー:', error);
    return null;
  }
}

/**
 * 予約情報を表示する関数
 */
async function displayBookingInfo(bookingId: string) {
  try {
    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId }
    });

    if (!booking) {
      console.error(`予約ID ${bookingId} が見つかりません`);
      return;
    }

    // 商品情報を取得
    const product = await prisma.product.findUnique({
      where: { id: booking.productId }
    });

    console.log('\n===== 予約情報 =====');
    console.log(`予約ID: ${booking.id}`);
    console.log(`予約番号: ${booking.bookingId}`);
    console.log(`商品名: ${product ? product.title : '不明'}`);
    console.log(`SKU: ${product ? product.sku : '不明'}`);
    console.log(`期間: ${format(new Date(booking.startDate), 'yyyy/MM/dd')} 〜 ${format(new Date(booking.endDate), 'yyyy/MM/dd')}`);
    console.log(`ステータス: ${booking.status}`);
    console.log(`予約タイプ: ${booking.bookingType}`);
    console.log(`支払い状態: ${booking.paymentStatus}`);
    console.log(`顧客名: ${booking.customerName || '未設定'}`);
    console.log(`顧客メール: ${booking.customerEmail || '未設定'}`);
    console.log(`金額: ${booking.totalAmount}円`);
    console.log(`注文ID: ${booking.orderId || '未設定'}`);
    console.log(`注文名: ${booking.orderName || '未設定'}`);
    console.log(`作成日時: ${format(new Date(booking.createdAt), 'yyyy/MM/dd HH:mm:ss', { locale: ja })}`);
    console.log(`更新日時: ${format(new Date(booking.updatedAt), 'yyyy/MM/dd HH:mm:ss', { locale: ja })}`);
  } catch (error) {
    console.error('予約情報表示エラー:', error);
  }
}

/**
 * 複数商品の同時予約をテストする関数
 */
async function testMultiProductBooking(shopifyProductId1: string, shopifyProductId2: string) {
  try {
    console.log(`商品ID ${shopifyProductId1} と ${shopifyProductId2} の同時予約テストを実行します...`);

    // 商品情報を取得
    const product1 = await getProductInfo(shopifyProductId1);
    const product2 = await getProductInfo(shopifyProductId2);

    if (!product1 || !product2) {
      return false;
    }

    // テスト用の日程を設定
    const today = new Date();
    const startDate = addDays(today, config.startDaysFromNow);
    const endDate = addDays(startDate, config.durationDays - 1);

    // CartServiceを初期化（モックリクエストを作成）
    const mockRequest = new Request(`https://${config.shop}/admin/api/2024-01/graphql.json`);
    const cartService = new CartService(mockRequest);

    // テスト1: 最初の商品をカートに追加
    console.log('\n----- テスト1: 最初の商品をカートに追加 -----');

    const cartResult1 = await cartService.addToCart({
      productId: product1.id,
      variantId: '1', // テスト用の固定値
      shopifyProductId: product1.shopifyId,
      startDate: format(startDate, 'yyyy-MM-dd'),
      endDate: format(endDate, 'yyyy-MM-dd'),
      customerEmail: config.customer.email,
      customerName: config.customer.name,
      customerPhone: config.customer.phone,
      customerAddress: config.customer.address,
      bookingType: 'provisional',
      notes: 'テスト予約（複数商品）'
    });

    if (!cartResult1.success) {
      console.error('最初の商品のカート追加に失敗しました:', cartResult1.error);
      return false;
    }

    console.log('最初の商品をカートに追加しました:');
    console.log(`予約ID: ${cartResult1.bookingId}`);
    console.log(`カートURL: ${cartResult1.cartUrl}`);

    // 予約情報を表示
    await displayBookingInfo(cartResult1.bookingId);

    // テスト2: 2つ目の商品をカートに追加
    console.log('\n----- テスト2: 2つ目の商品をカートに追加 -----');

    const cartResult2 = await cartService.addToCart({
      productId: product2.id,
      variantId: '1', // テスト用の固定値
      shopifyProductId: product2.shopifyId,
      startDate: format(startDate, 'yyyy-MM-dd'),
      endDate: format(endDate, 'yyyy-MM-dd'),
      customerEmail: config.customer.email,
      customerName: config.customer.name,
      customerPhone: config.customer.phone,
      customerAddress: config.customer.address,
      bookingType: 'provisional',
      notes: 'テスト予約（複数商品）'
    });

    if (!cartResult2.success) {
      console.error('2つ目の商品のカート追加に失敗しました:', cartResult2.error);
      return false;
    }

    console.log('2つ目の商品をカートに追加しました:');
    console.log(`予約ID: ${cartResult2.bookingId}`);
    console.log(`カートURL: ${cartResult2.cartUrl}`);

    // 予約情報を表示
    await displayBookingInfo(cartResult2.bookingId);

    // テスト3: 注文作成をシミュレート
    console.log('\n----- テスト3: 注文作成をシミュレート -----');

    // BookingServiceを初期化
    const bookingService = new BookingService();

    // 注文IDを生成
    const orderId = Math.floor(Math.random() * 10000000000).toString();
    const orderName = `#${Math.floor(Math.random() * 10000)}`;

    // 最初の予約の注文を更新
    const updateResult1 = await bookingService.updateBookingStatus(cartResult1.bookingId, 'CONFIRMED', {
      orderId,
      orderName,
      notes: 'テスト注文作成'
    });

    if (!updateResult1.success) {
      console.error('最初の予約の注文更新に失敗しました:', updateResult1.error);
      return false;
    }

    // 2つ目の予約の注文を更新
    const updateResult2 = await bookingService.updateBookingStatus(cartResult2.bookingId, 'CONFIRMED', {
      orderId,
      orderName,
      notes: 'テスト注文作成'
    });

    if (!updateResult2.success) {
      console.error('2つ目の予約の注文更新に失敗しました:', updateResult2.error);
      return false;
    }

    console.log('注文を作成しました:');
    console.log(`注文ID: ${orderId}`);
    console.log(`注文名: ${orderName}`);

    // 更新された予約情報を表示
    console.log('\n最初の予約の更新後情報:');
    await displayBookingInfo(cartResult1.bookingId);

    console.log('\n2つ目の予約の更新後情報:');
    await displayBookingInfo(cartResult2.bookingId);

    // テスト4: 支払い完了をシミュレート
    console.log('\n----- テスト4: 支払い完了をシミュレート -----');

    // 最初の予約の支払い状態を更新
    const paymentResult1 = await bookingService.updatePaymentStatus(cartResult1.bookingId, 'COMPLETED', {
      paymentMethod: 'CREDIT_CARD',
      notes: 'テスト支払い完了'
    });

    if (!paymentResult1.success) {
      console.error('最初の予約の支払い状態更新に失敗しました:', paymentResult1.error);
      return false;
    }

    // 2つ目の予約の支払い状態を更新
    const paymentResult2 = await bookingService.updatePaymentStatus(cartResult2.bookingId, 'COMPLETED', {
      paymentMethod: 'CREDIT_CARD',
      notes: 'テスト支払い完了'
    });

    if (!paymentResult2.success) {
      console.error('2つ目の予約の支払い状態更新に失敗しました:', paymentResult2.error);
      return false;
    }

    console.log('支払いが完了しました');

    // 更新された予約情報を表示
    console.log('\n最初の予約の支払い完了後情報:');
    await displayBookingInfo(cartResult1.bookingId);

    console.log('\n2つ目の予約の支払い完了後情報:');
    await displayBookingInfo(cartResult2.bookingId);

    // テスト後のクリーンアップ
    console.log('\n----- テスト後のクリーンアップ -----');

    // 作成した予約を削除
    await prisma.booking.delete({ where: { id: cartResult1.bookingId } });
    console.log(`予約 ${cartResult1.bookingId} を削除しました`);

    await prisma.booking.delete({ where: { id: cartResult2.bookingId } });
    console.log(`予約 ${cartResult2.bookingId} を削除しました`);

    // 在庫カレンダーをクリーンアップ
    await prisma.inventoryCalendar.deleteMany({
      where: {
        productId: {
          in: [product1.id, product2.id]
        },
        date: {
          gte: startDate,
          lte: endDate
        }
      }
    });
    console.log('在庫カレンダーをクリーンアップしました');

    return true;
  } catch (error) {
    console.error('複数商品の同時予約テストエラー:', error);
    return false;
  }
}

/**
 * テスト結果をJSONファイルに保存する関数
 */
function saveTestResults(results: TestResultSet, filePath: string): void {
  try {
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(filePath, JSON.stringify(results, null, 2));
    console.log(`テスト結果JSONファイルを生成しました: ${filePath}`);
  } catch (error) {
    console.error(`テスト結果の保存に失敗しました: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * テスト結果をMarkdownレポートとして生成する関数
 */
function generateMarkdownReport(results: TestResultSet, outputPath: string): string {
  try {
    let markdown = `# 複数商品の同時予約テストレポート

## テスト概要

このレポートは、複数商品の同時予約テストの結果をまとめたものです。

テスト実行日時: ${new Date(results.timestamp).toLocaleString('ja-JP')}
実行時間: ${results.duration.toFixed(2)}秒

## テスト環境

- 商品ID1: ${results.environment.productId1}
- 商品ID2: ${results.environment.productId2}
- ショップ: ${results.environment.shop}
- 予約開始日: 今日から${results.environment.startDaysFromNow}日後
- 予約期間: ${results.environment.durationDays}日間
- Node.js バージョン: ${results.environment.nodeVersion}
- OS: ${results.environment.os}

## テスト結果

- 合計テスト数: ${results.totalTests}
- 成功: ${results.successCount}
- 失敗: ${results.failureCount}
- 成功率: ${Math.round((results.successCount / results.totalTests) * 100)}%

## パフォーマンス分析

- 平均テスト実行時間: ${results.performance.averageTestDuration.toFixed(2)}ミリ秒
- 最も遅いテスト: ${results.performance.slowestTest.name} (${results.performance.slowestTest.duration.toFixed(2)}ミリ秒)
- 最も速いテスト: ${results.performance.fastestTest.name} (${results.performance.fastestTest.duration.toFixed(2)}ミリ秒)

## 詳細結果

`;

    // 各テストケースの詳細結果を追加
    results.tests.forEach((result, index) => {
      markdown += `### テストケース${index + 1}: ${result.testName}

- 結果: ${result.success ? '✅ 成功' : '❌ 失敗'}
${result.message ? `- メッセージ: ${result.message}` : ''}
${result.error ? `- エラー: ${result.error}` : ''}
- 実行時間: ${result.duration ? `${result.duration.toFixed(2)}ミリ秒` : '不明'}
`;

      if (result.details) {
        markdown += `- 詳細:\n\`\`\`json\n${JSON.stringify(result.details, null, 2)}\n\`\`\`\n\n`;
      }
    });

    // レポートファイルに書き込み
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(outputPath, markdown);
    console.log(`テスト結果レポートを生成しました: ${outputPath}`);

    return outputPath;
  } catch (error) {
    console.error(`Markdownレポートの生成に失敗しました: ${error instanceof Error ? error.message : String(error)}`);
    return '';
  }
}

/**
 * テスト結果を分析する関数
 */
function analyzeTestResults(results: TestResult[]): {
  averageTestDuration: number;
  slowestTest: { name: string; duration: number };
  fastestTest: { name: string; duration: number };
} {
  // 実行時間が記録されているテストのみを対象にする
  const testsWithDuration = results.filter(test => test.duration !== undefined) as (TestResult & { duration: number })[];

  if (testsWithDuration.length === 0) {
    return {
      averageTestDuration: 0,
      slowestTest: { name: '不明', duration: 0 },
      fastestTest: { name: '不明', duration: 0 }
    };
  }

  // 平均実行時間を計算
  const totalDuration = testsWithDuration.reduce((sum, test) => sum + test.duration, 0);
  const averageTestDuration = totalDuration / testsWithDuration.length;

  // 最も遅いテストを特定
  const slowestTest = testsWithDuration.reduce((slowest, test) =>
    test.duration > slowest.duration ? { name: test.testName, duration: test.duration } : slowest,
    { name: '', duration: -1 }
  );

  // 最も速いテストを特定
  const fastestTest = testsWithDuration.reduce((fastest, test) =>
    test.duration < fastest.duration || fastest.duration === -1 ? { name: test.testName, duration: test.duration } : fastest,
    { name: '', duration: -1 }
  );

  return {
    averageTestDuration,
    slowestTest,
    fastestTest
  };
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('複数商品の同時予約テストを開始します...');

    // 開始時刻を記録
    const startTime = new Date();

    // テスト結果を格納する配列
    const testResults: TestResult[] = [];

    // コマンドライン引数から商品IDを取得
    const shopifyProductId1 = config.productId1;
    const shopifyProductId2 = config.productId2;

    // 複数商品の同時予約をテスト
    const testStartTime = new Date();
    const testResult = await testMultiProductBooking(shopifyProductId1, shopifyProductId2);
    const testEndTime = new Date();
    const testDuration = differenceInMilliseconds(testEndTime, testStartTime);

    // テスト結果を記録
    testResults.push({
      testName: '複数商品の同時予約テスト',
      success: testResult,
      timestamp: new Date().toISOString(),
      message: testResult ? '複数商品の同時予約が正常に行われました' : '複数商品の同時予約に失敗しました',
      duration: testDuration
    });

    // テスト結果のサマリー
    console.log('\n===== テスト結果サマリー =====');
    console.log(`複数商品の同時予約テスト: ${testResult ? '成功' : '失敗'}`);
    console.log(`実行時間: ${testDuration}ミリ秒`);

    // 終了時刻を記録
    const endTime = new Date();
    const totalDuration = differenceInMilliseconds(endTime, startTime) / 1000; // 秒単位

    // テスト結果を分析
    const performanceAnalysis = analyzeTestResults(testResults);

    // テスト結果セットを作成
    const resultSet: TestResultSet = {
      timestamp: startTime.toISOString(),
      duration: totalDuration,
      totalTests: testResults.length,
      successCount: testResults.filter(r => r.success).length,
      failureCount: testResults.filter(r => !r.success).length,
      tests: testResults,
      environment: {
        productId1: shopifyProductId1,
        productId2: shopifyProductId2,
        shop: config.shop,
        customer: config.customer,
        startDaysFromNow: config.startDaysFromNow,
        durationDays: config.durationDays,
        nodeVersion: process.version,
        os: `${process.platform} ${process.arch}`
      },
      performance: performanceAnalysis
    };

    // 結果ディレクトリが存在しない場合は作成
    if (!fs.existsSync(config.outputDir)) {
      fs.mkdirSync(config.outputDir, { recursive: true });
    }

    // タイムスタンプを生成
    const timestamp = startTime.toISOString().replace(/[:.]/g, '-');

    // JSONファイルに保存
    const jsonFilePath = path.join(config.outputDir, `multi-product-booking-${timestamp}.json`);
    saveTestResults(resultSet, jsonFilePath);

    // Markdownレポートを生成
    const markdownFilePath = path.join(config.outputDir, `multi-product-booking-${timestamp}.md`);
    generateMarkdownReport(resultSet, markdownFilePath);

    if (testResult) {
      console.log('\nテストが成功しました！');
    } else {
      console.error('\nテストが失敗しました。');
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
