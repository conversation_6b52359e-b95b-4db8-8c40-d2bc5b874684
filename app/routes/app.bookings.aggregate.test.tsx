import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import { Page, Card, Text, Layout } from '@shopify/polaris';

export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  
  return json({
    shop,
    message: '予約状況一括照会テスト画面'
  });
}

export default function BookingAggregateTest() {
  const { shop, message } = useLoaderData<typeof loader>();
  
  return (
    <Page
      title="予約状況一括照会テスト"
      subtitle="テスト用の簡易画面"
      backAction={{ content: 'ホーム', url: '/app' }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Card.Section>
              <Text as="h2" variant="headingMd">ショップ: {shop}</Text>
              <Text as="p">{message}</Text>
            </Card.Section>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
