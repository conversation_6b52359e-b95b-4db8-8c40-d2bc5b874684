/**
 * 商品バリエーション方式のためのメタフィールド更新スクリプト
 * 
 * このスクリプトは、商品バリエーション方式の実装に必要なメタフィールドの更新を行います。
 * 具体的には、以下の処理を行います：
 * 1. rental.variation_groupとrental.variation_typeメタフィールドの使用方法を変更
 * 2. rental.pricingメタフィールドの構造を更新
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数を読み込む
dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品を取得するGraphQLクエリ
const GET_PRODUCTS = gql`
  query getProducts($first: Int!, $after: String) {
    products(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          metafields(first: 10, namespace: "rental") {
            edges {
              node {
                id
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    }
  }
`;

// メタフィールドを更新するGraphQLクエリ
const UPDATE_METAFIELD = gql`
  mutation metafieldSet($metafield: MetafieldInput!) {
    metafieldSet(metafield: $metafield) {
      metafield {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールドを削除するGraphQLクエリ
const DELETE_METAFIELD = gql`
  mutation metafieldDelete($input: MetafieldDeleteInput!) {
    metafieldDelete(input: $input) {
      deletedId
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * 商品のメタフィールドを更新する
 * @param {string} productId 商品ID
 * @param {Object} metafields 商品のメタフィールド
 */
async function updateProductMetafields(productId, metafields) {
  try {
    // rental.pricingメタフィールドを取得
    const pricingMetafield = metafields.find(
      (m) => m.namespace === 'rental' && m.key === 'pricing'
    );

    if (pricingMetafield) {
      // 現在のpricing値を解析
      const pricingValue = JSON.parse(pricingMetafield.value);
      const basePrice = pricingValue.basePrice || 10000;
      const day2_6_rate = pricingValue.discountRules?.day2_6_rate || 0.2;
      const day7_plus_rate = pricingValue.discountRules?.day7_plus_rate || 0.1;

      // バリエーション価格を計算
      const variantPrices = {
        "1day": basePrice,
        "2day": Math.round(basePrice + basePrice * day2_6_rate),
        "3day": Math.round(basePrice + basePrice * day2_6_rate * 2),
        "4day": Math.round(basePrice + basePrice * day2_6_rate * 3),
        "5day": Math.round(basePrice + basePrice * day2_6_rate * 4),
        "6day": Math.round(basePrice + basePrice * day2_6_rate * 5),
        "7day": Math.round(basePrice + basePrice * day2_6_rate * 6),
        "8plus": Math.round(basePrice + basePrice * day2_6_rate * 6)
      };

      // 新しいpricing値を作成
      const newPricingValue = {
        ...pricingValue,
        variantPrices
      };

      // rental.pricingメタフィールドを更新
      await client.request(UPDATE_METAFIELD, {
        metafield: {
          ownerId: productId,
          namespace: 'rental',
          key: 'pricing',
          value: JSON.stringify(newPricingValue),
          type: 'json'
        }
      });

      console.log(`商品 ${productId} の rental.pricing メタフィールドを更新しました`);
    } else {
      console.log(`商品 ${productId} には rental.pricing メタフィールドがありません`);
    }

    // rental.variation_typeメタフィールドを更新
    const variationTypeMetafield = metafields.find(
      (m) => m.namespace === 'rental' && m.key === 'variation_type'
    );

    if (variationTypeMetafield) {
      // rental.variation_typeメタフィールドを「レンタル日数」に更新
      await client.request(UPDATE_METAFIELD, {
        metafield: {
          ownerId: productId,
          namespace: 'rental',
          key: 'variation_type',
          value: 'レンタル日数',
          type: 'single_line_text_field'
        }
      });

      console.log(`商品 ${productId} の rental.variation_type メタフィールドを更新しました`);
    } else {
      // rental.variation_typeメタフィールドがない場合は作成
      await client.request(UPDATE_METAFIELD, {
        metafield: {
          ownerId: productId,
          namespace: 'rental',
          key: 'variation_type',
          value: 'レンタル日数',
          type: 'single_line_text_field'
        }
      });

      console.log(`商品 ${productId} に rental.variation_type メタフィールドを作成しました`);
    }
  } catch (error) {
    console.error(`商品 ${productId} のメタフィールド更新中にエラーが発生しました: ${error.message}`);
  }
}

/**
 * 全ての商品のメタフィールドを更新する
 */
async function updateAllProductMetafields() {
  let hasNextPage = true;
  let cursor = null;
  let count = 0;

  console.log('商品バリエーション方式のためのメタフィールド更新を開始します...');

  while (hasNextPage) {
    try {
      // 商品を取得
      const result = await client.request(GET_PRODUCTS, {
        first: 10,
        after: cursor
      });

      const products = result.products.edges;
      hasNextPage = result.products.pageInfo.hasNextPage;
      cursor = result.products.pageInfo.endCursor;

      // 各商品のメタフィールドを更新
      for (const product of products) {
        const productId = product.node.id;
        const metafields = product.node.metafields.edges.map(edge => ({
          id: edge.node.id,
          namespace: edge.node.namespace,
          key: edge.node.key,
          value: edge.node.value,
          type: edge.node.type
        }));

        await updateProductMetafields(productId, metafields);
        count++;

        // API制限を回避するための短い待機
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      console.log(`${count}件の商品を処理しました`);
    } catch (error) {
      console.error(`商品の取得中にエラーが発生しました: ${error.message}`);
      hasNextPage = false;
    }
  }

  console.log(`合計${count}件の商品のメタフィールドを更新しました`);
}

// スクリプトの実行
updateAllProductMetafields().catch(error => {
  console.error(`スクリプトの実行中にエラーが発生しました: ${error.message}`);
});
