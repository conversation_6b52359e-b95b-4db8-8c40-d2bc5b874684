/**
 * 商品バリエーション設定スクリプト
 *
 * このスクリプトでは、Shopify APIを使用して商品のバリエーションを設定します。
 * 各商品に8種類のバリエーション（1日、2日、3日、4日、5日、6日、7日、8日以上）を設定します。
 */

import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数を読み込む
dotenv.config();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// GraphQLクエリとミューテーション
const GET_PRODUCTS = gql`
  query getProducts($query: String) {
    products(first: 50, query: $query) {
      edges {
        node {
          id
          title
          handle
          variants(first: 10) {
            edges {
              node {
                id
                title
                price
              }
            }
          }
        }
      }
    }
  }
`;

const UPDATE_PRODUCT = gql`
  mutation productUpdate($input: ProductInput!) {
    productUpdate(input: $input) {
      product {
        id
        title
        handle
        variants(first: 10) {
          edges {
            node {
              id
              title
              price
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * 商品のバリエーションを設定
 */
async function setupProductVariants() {
  console.log("=== 商品バリエーションの設定 ===\n");

  try {
    // テスト商品を検索
    const result = await shopifyClient.request(GET_PRODUCTS, {
      query: "title:*テスト商品*"
    });

    const products = result.products.edges;
    console.log(`${products.length}件のテスト商品が見つかりました`);

    // 各商品のバリエーションを設定
    for (const product of products) {
      const productId = product.node.id;
      const title = product.node.title;
      const basePrice = parseFloat(product.node.variants.edges[0].node.price);

      console.log(`商品のバリエーションを設定: ${title} (${productId})`);
      console.log(`基本価格: ${basePrice}`);

      // バリエーションの価格を計算
      const prices = calculateVariantPrices(basePrice);

      // 商品を更新
      const updateResult = await shopifyClient.request(UPDATE_PRODUCT, {
        input: {
          id: productId,
          options: ["レンタル日数"],
          variants: [
            { price: prices[0].toString(), options: ["1日"] },
            { price: prices[1].toString(), options: ["2日"] },
            { price: prices[2].toString(), options: ["3日"] },
            { price: prices[3].toString(), options: ["4日"] },
            { price: prices[4].toString(), options: ["5日"] },
            { price: prices[5].toString(), options: ["6日"] },
            { price: prices[6].toString(), options: ["7日"] },
            { price: prices[6].toString(), options: ["8日以上"] }
          ]
        }
      });

      if (updateResult.productUpdate.userErrors.length > 0) {
        console.error("商品更新エラー:", updateResult.productUpdate.userErrors);
        continue;
      }

      console.log(`商品のバリエーションが設定されました: ${title}`);
      console.log("バリエーション価格:");
      prices.forEach((price, index) => {
        const days = index + 1;
        const label = days <= 7 ? `${days}日` : "8日以上";
        console.log(`- ${label}: ${price}`);
      });
    }

    console.log("\n商品バリエーションの設定が完了しました");
  } catch (error) {
    console.error("商品バリエーションの設定中にエラーが発生しました:", error);
  }
}

/**
 * バリエーションの価格を計算
 * @param {number} basePrice 基本料金
 * @returns {number[]} 各バリエーションの価格
 */
function calculateVariantPrices(basePrice) {
  return [
    basePrice,                    // 1日: 100%
    basePrice * 1.2,              // 2日: 120%
    basePrice * 1.4,              // 3日: 140%
    basePrice * 1.6,              // 4日: 160%
    basePrice * 1.8,              // 5日: 180%
    basePrice * 2.0,              // 6日: 200%
    basePrice * 2.2,              // 7日: 220%
    // 8日以上も7日と同じ価格（追加料金は別途計算）
  ];
}

// メイン処理
async function main() {
  try {
    await setupProductVariants();
  } catch (error) {
    console.error("エラーが発生しました:", error);
  }
}

main();
