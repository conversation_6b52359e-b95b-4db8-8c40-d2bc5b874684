# 負荷テストと同時実行テストレポート

## テスト概要

このレポートは、多数の同時予約リクエストの処理、同一商品への複数同時アクセス時の競合解決、高負荷時のパフォーマンスのテスト結果をまとめたものです。

テスト実行日時: 2025/5/19 16:23:59
実行時間: 2.68秒

## テスト環境

- 同時リクエスト数: 3
- Node.js バージョン: v20.18.1
- OS: darwin arm64
- CPUコア数: 10

## テスト結果

- 合計テスト数: 3
- 成功: 0
- 失敗: 3
- 成功率: 0%

## パフォーマンス分析

- スループット: 1.12 リクエスト/秒
- 平均テスト実行時間: 1367.00ミリ秒
- 最も遅いテスト: リクエスト #2 (1547.00ミリ秒)
- 最も速いテスト: リクエスト #0 (1021.00ミリ秒)

## 詳細結果

### リクエスト1

- 結果: ❌ 失敗
- メッセージ: 予約リクエストが失敗しました
- エラー: カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-05-26
- 実行時間: 1021.00ミリ秒
- 詳細:
```json
{
  "requestId": 0,
  "bookingId": ""
}
```

### リクエスト1

- 結果: ❌ 失敗
- メッセージ: 予約リクエストが失敗しました
- エラー: カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-05, 2025-06-06, 2025-06-07
- 実行時間: 1533.00ミリ秒
- 詳細:
```json
{
  "requestId": 1,
  "bookingId": ""
}
```

### リクエスト2

- 結果: ❌ 失敗
- メッセージ: 予約リクエストが失敗しました
- エラー: カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-15, 2025-06-16, 2025-06-17
- 実行時間: 1547.00ミリ秒
- 詳細:
```json
{
  "requestId": 2,
  "bookingId": ""
}
```

