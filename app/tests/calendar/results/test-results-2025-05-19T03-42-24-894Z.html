<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>テスト結果レポート</title>
  <style>
    body {
      font-family: 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #2c3e50;
    }
    .summary {
      background-color: #f8f9fa;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .summary-item {
      margin: 5px 15px;
    }
    .test-list {
      margin-top: 20px;
    }
    .test-item {
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 15px;
      position: relative;
    }
    .test-name {
      font-weight: bold;
      margin-bottom: 10px;
      padding-right: 80px;
    }
    .test-status {
      position: absolute;
      top: 15px;
      right: 15px;
      padding: 3px 10px;
      border-radius: 3px;
      font-size: 14px;
      font-weight: bold;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    .failure {
      background-color: #f8d7da;
      color: #721c24;
    }
    .test-details {
      background-color: #f8f9fa;
      border-radius: 3px;
      padding: 10px;
      margin-top: 10px;
      font-family: monospace;
      white-space: pre-wrap;
      overflow-x: auto;
    }
    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 14px;
      color: #6c757d;
    }
    .validation-section {
      margin-top: 30px;
      border-top: 1px solid #ddd;
      padding-top: 20px;
    }
    .validation-item {
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 15px;
    }
    .validation-message {
      font-weight: bold;
      margin-bottom: 10px;
    }
    .validation-details {
      background-color: #f8f9fa;
      border-radius: 3px;
      padding: 10px;
      margin-top: 10px;
      font-family: monospace;
      white-space: pre-wrap;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>テスト結果レポート</h1>
  
  <div class="summary">
    <div class="summary-item">
      <strong>実行日時:</strong> 2025/5/19 12:42:24
    </div>
    <div class="summary-item">
      <strong>実行時間:</strong> 5.47秒
    </div>
    <div class="summary-item">
      <strong>合計テスト数:</strong> 4
    </div>
    <div class="summary-item">
      <strong>成功:</strong> 3
    </div>
    <div class="summary-item">
      <strong>失敗:</strong> 1
    </div>
  </div>
  
  <h2>テスト結果詳細</h2>
  <div class="test-list">
    <div class="test-item failure">
  <div class="test-name">
    商品情報取得エラーのユニットテスト
    <span class="test-status failure">失敗</span>
  </div>
  <div class="test-details">エラー: テスト実行中にエラーが発生しました</div><div class="test-details">詳細: <pre>{
  "duration": 3.131,
  "output": "\n RUN  v1.6.1 /Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp\n\n ❯ app/tests/calendar/product-fetch-error.test.ts  (0 test)\n\n Test Files  1 failed (1)\n      Tests  no tests\n   Start at  12:42:27\n   Duration  329ms (transform 81ms, setup 0ms, collect 0ms, tests 0ms, environment 0ms, prepare 190ms)\n\n"
}</pre></div>
</div><div class="test-item success">
  <div class="test-name">
    商品情報取得エラーのE2Eテスト
    <span class="test-status success">成功</span>
  </div>
  <div class="test-details">メッセージ: テストが正常に完了しました (0.56秒)</div><div class="test-details">詳細: <pre>{
  "duration": 0.556
}</pre></div>
</div><div class="test-item success">
  <div class="test-name">
    統一カレンダーのテスト
    <span class="test-status success">成功</span>
  </div>
  <div class="test-details">メッセージ: テストが正常に完了しました (0.75秒)</div><div class="test-details">詳細: <pre>{
  "duration": 0.751
}</pre></div>
</div><div class="test-item success">
  <div class="test-name">
    チェックアウト統合テスト
    <span class="test-status success">成功</span>
  </div>
  <div class="test-details">メッセージ: テストが正常に完了しました (1.03秒)</div><div class="test-details">詳細: <pre>{
  "duration": 1.026
}</pre></div>
</div>
  </div>
  
  
  
  <div class="footer">
    <p>レポート生成日時: 2025/5/19 12:42:30</p>
  </div>
</body>
</html>