{% comment %}
  Rental Calendar Block - 改良された予約カレンダー
  Polarisに依存しない、完全カスタマイズ可能なカレンダー実装
{% endcomment %}

{% assign product = block.settings.product %}

<div id="rental-calendar-root-{{ block.id }}" class="rental-calendar-container" data-product-id="{{ product.id }}">
  <div class="rental-calendar-loading">
    <p>カレンダーを読み込んでいます...</p>
  </div>
</div>

<!-- カスタムカレンダースタイル -->
<style>
  .rental-calendar-container {
    margin: 20px 0;
  }
  
  .rental-calendar-loading {
    text-align: center;
    padding: 40px;
    color: #637381;
  }
  
  /* カスタムテーマスタイル（テーマに合わせて調整可能） */
  .rental-calendar-wrapper {
    --rc-primary-color: {{ settings.colors_solid_button_labels | default: '#008060' }};
    --rc-primary-hover: {{ settings.colors_solid_button_labels | color_darken: 10 | default: '#006e52' }};
    --rc-border-color: #e1e3e5;
    --rc-text-color: {{ settings.colors_text | default: '#202223' }};
    --rc-disabled-color: #c4cdd5;
    --rc-holiday-color: #bf0711;
    --rc-confirmed-bg: rgba(0, 128, 96, 0.2);
    --rc-provisional-bg: rgba(255, 184, 0, 0.2);
    --rc-maintenance-bg: rgba(171, 171, 171, 0.3);
  }
</style>

<!-- メインスクリプト -->
<script type="module">
  // 日付ライブラリのCDNから読み込み
  import { format, startOfMonth, endOfMonth, eachDayOfInterval, isWithinInterval, isSameDay, addMonths, subMonths } from 'https://cdn.skypack.dev/date-fns@2.30.0';
  import { ja } from 'https://cdn.skypack.dev/date-fns@2.30.0/locale';

  class RentalCalendar {
    constructor(containerId, productId) {
      this.container = document.getElementById(containerId);
      this.productId = productId;
      this.currentMonth = new Date();
      this.numberOfMonths = {{ block.settings.number_of_months | default: 2 }};
      this.bookings = [];
      this.maintenancePeriods = [];
      this.selectedRange = { start: null, end: null };
      this.selecting = false;
      
      this.init();
    }

    async init() {
      try {
        // 予約データを取得
        await this.fetchBookingData();
        
        // カレンダーをレンダリング
        this.render();
        
        // イベントリスナーを設定
        this.attachEventListeners();
      } catch (error) {
        console.error('カレンダーの初期化エラー:', error);
        this.showError();
      }
    }

    async fetchBookingData() {
      try {
        const response = await fetch(`/api/product-availability?productId=${this.productId}`);
        const data = await response.json();
        
        if (data.success) {
          this.bookings = data.bookings || [];
          this.maintenancePeriods = data.maintenance || [];
        }
      } catch (error) {
        console.error('予約データの取得エラー:', error);
      }
    }

    getJapaneseHolidays(year) {
      return [
        new Date(year, 0, 1),    // 元日
        new Date(year, 1, 11),   // 建国記念日
        new Date(year, 2, 21),   // 春分の日
        new Date(year, 4, 3),    // 憲法記念日
        new Date(year, 4, 4),    // みどりの日
        new Date(year, 4, 5),    // こどもの日
        new Date(year, 7, 11),   // 山の日
        new Date(year, 8, 23),   // 秋分の日
        new Date(year, 10, 3),   // 文化の日
        new Date(year, 10, 23),  // 勤労感謝の日
        new Date(year, 11, 23),  // 天皇誕生日
      ];
    }

    getDateStatus(date) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      date.setHours(0, 0, 0, 0);

      const status = {
        isToday: date.getTime() === today.getTime(),
        isSunday: date.getDay() === 0,
        isHoliday: false,
        isBooked: false,
        bookingType: null,
        isInMaintenance: false,
        isSelected: false,
        isInRange: false,
        isDisabled: false,
        isPast: date < today,
      };

      // 祝日判定
      const holidays = this.getJapaneseHolidays(date.getFullYear());
      status.isHoliday = holidays.some(holiday => {
        holiday.setHours(0, 0, 0, 0);
        return holiday.getTime() === date.getTime();
      });

      // 予約状態判定
      this.bookings.forEach(booking => {
        const start = new Date(booking.startDate);
        const end = new Date(booking.endDate);
        start.setHours(0, 0, 0, 0);
        end.setHours(0, 0, 0, 0);
        
        if (date >= start && date <= end) {
          status.isBooked = true;
          status.bookingType = booking.status || booking.bookingType;
        }
      });

      // メンテナンス期間判定
      this.maintenancePeriods.forEach(period => {
        const start = new Date(period.startDate);
        const end = new Date(period.endDate);
        start.setHours(0, 0, 0, 0);
        end.setHours(0, 0, 0, 0);
        
        if (date >= start && date <= end) {
          status.isInMaintenance = true;
        }
      });

      // 選択範囲判定
      if (this.selectedRange.start && this.selectedRange.end) {
        const start = new Date(this.selectedRange.start);
        const end = new Date(this.selectedRange.end);
        start.setHours(0, 0, 0, 0);
        end.setHours(0, 0, 0, 0);
        
        status.isSelected = date.getTime() === start.getTime() || date.getTime() === end.getTime();
        status.isInRange = date >= start && date <= end;
      }

      // 選択可能判定
      if (status.isPast || status.isBooked || status.isInMaintenance) {
        status.isDisabled = true;
      }

      return status;
    }

    createDayElement(date, isCurrentMonth) {
      const status = this.getDateStatus(date);
      const dayEl = document.createElement('button');
      dayEl.className = 'rc-day';
      
      if (!isCurrentMonth) dayEl.classList.add('rc-day--other-month');
      if (status.isToday) dayEl.classList.add('rc-day--today');
      if (status.isSunday || status.isHoliday) dayEl.classList.add('rc-day--holiday');
      if (status.isBooked) {
        dayEl.classList.add('rc-day--booked');
        if (status.bookingType === 'CONFIRMED') {
          dayEl.classList.add('rc-day--confirmed');
        } else {
          dayEl.classList.add('rc-day--provisional');
        }
      }
      if (status.isInMaintenance) dayEl.classList.add('rc-day--maintenance');
      if (status.isSelected) dayEl.classList.add('rc-day--selected');
      if (status.isInRange) dayEl.classList.add('rc-day--in-range');
      if (status.isDisabled) dayEl.classList.add('rc-day--disabled');

      dayEl.disabled = status.isDisabled;
      dayEl.dataset.date = date.toISOString();
      
      dayEl.innerHTML = `
        <span class="rc-day-number">${date.getDate()}</span>
        ${status.isBooked ? `<span class="rc-marker">${status.bookingType === 'CONFIRMED' ? '予' : '仮'}</span>` : ''}
        ${status.isInMaintenance && !status.isBooked ? '<span class="rc-marker">メ</span>' : ''}
        ${(status.isSunday || status.isHoliday) && !status.isBooked && !status.isInMaintenance ? '<span class="rc-marker">休</span>' : ''}
      `;

      return dayEl;
    }

    renderMonth(monthDate) {
      const monthEl = document.createElement('div');
      monthEl.className = 'rc-month';

      // 月のタイトル
      const titleEl = document.createElement('h3');
      titleEl.className = 'rc-month-title';
      titleEl.textContent = format(monthDate, 'yyyy年M月', { locale: ja });
      monthEl.appendChild(titleEl);

      // カレンダーテーブル
      const tableEl = document.createElement('table');
      tableEl.className = 'rc-table';

      // 曜日ヘッダー
      const theadEl = document.createElement('thead');
      const headerRow = document.createElement('tr');
      ['日', '月', '火', '水', '木', '金', '土'].forEach((day, index) => {
        const th = document.createElement('th');
        th.textContent = day;
        if (index === 0) th.className = 'rc-weekday--sunday';
        headerRow.appendChild(th);
      });
      theadEl.appendChild(headerRow);
      tableEl.appendChild(theadEl);

      // カレンダー本体
      const tbodyEl = document.createElement('tbody');
      const start = startOfMonth(monthDate);
      const end = endOfMonth(monthDate);
      const startDayOfWeek = start.getDay();

      // 前月の日付
      const prevMonthDays = [];
      for (let i = startDayOfWeek - 1; i >= 0; i--) {
        const date = new Date(start);
        date.setDate(date.getDate() - i - 1);
        prevMonthDays.push(date);
      }

      // 当月の日付
      const currentMonthDays = eachDayOfInterval({ start, end });

      // 次月の日付
      const totalCells = 42;
      const nextMonthDays = [];
      const filledCells = prevMonthDays.length + currentMonthDays.length;
      for (let i = 1; i <= totalCells - filledCells; i++) {
        const date = new Date(end);
        date.setDate(date.getDate() + i);
        nextMonthDays.push(date);
      }

      const allDays = [...prevMonthDays, ...currentMonthDays, ...nextMonthDays];

      // 週ごとに行を作成
      for (let week = 0; week < 6; week++) {
        const row = document.createElement('tr');
        for (let day = 0; day < 7; day++) {
          const dayIndex = week * 7 + day;
          if (dayIndex < allDays.length) {
            const date = allDays[dayIndex];
            const cell = document.createElement('td');
            const isCurrentMonth = date.getMonth() === monthDate.getMonth();
            cell.appendChild(this.createDayElement(date, isCurrentMonth));
            row.appendChild(cell);
          }
        }
        tbodyEl.appendChild(row);
      }

      tableEl.appendChild(tbodyEl);
      monthEl.appendChild(tableEl);

      return monthEl;
    }

    render() {
      this.container.innerHTML = `
        <div class="rental-calendar-wrapper">
          <div class="rc-header">
            <button class="rc-nav-button rc-nav-prev" aria-label="前月">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M12.5 5L7.5 10L12.5 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <button class="rc-nav-button rc-nav-next" aria-label="次月">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M7.5 5L12.5 10L7.5 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
          <div class="rc-months"></div>
          <div class="rc-legend">
            <div class="rc-legend-item">
              <span class="rc-legend-marker rc-legend-marker--confirmed">予</span>
              <span>確定予約</span>
            </div>
            <div class="rc-legend-item">
              <span class="rc-legend-marker rc-legend-marker--provisional">仮</span>
              <span>仮予約</span>
            </div>
            <div class="rc-legend-item">
              <span class="rc-legend-marker rc-legend-marker--maintenance">メ</span>
              <span>メンテナンス</span>
            </div>
            <div class="rc-legend-item">
              <span class="rc-legend-marker rc-legend-marker--holiday">休</span>
              <span>休業日</span>
            </div>
          </div>
          <div class="rc-selected-info" style="display: none;">
            <p>選択された期間: <span class="rc-selected-dates"></span></p>
            <p>レンタル日数: <span class="rc-rental-days"></span>日</p>
            <button class="rc-add-to-cart">カートに追加</button>
          </div>
        </div>
      `;

      this.renderMonths();
    }

    renderMonths() {
      const monthsContainer = this.container.querySelector('.rc-months');
      monthsContainer.innerHTML = '';

      for (let i = 0; i < this.numberOfMonths; i++) {
        const monthDate = addMonths(this.currentMonth, i);
        monthsContainer.appendChild(this.renderMonth(monthDate));
      }
    }

    attachEventListeners() {
      // ナビゲーションボタン
      this.container.querySelector('.rc-nav-prev').addEventListener('click', () => {
        this.currentMonth = subMonths(this.currentMonth, 1);
        this.renderMonths();
      });

      this.container.querySelector('.rc-nav-next').addEventListener('click', () => {
        this.currentMonth = addMonths(this.currentMonth, 1);
        this.renderMonths();
      });

      // 日付クリック
      this.container.addEventListener('click', (e) => {
        const dayButton = e.target.closest('.rc-day:not(.rc-day--disabled)');
        if (dayButton) {
          this.handleDateClick(new Date(dayButton.dataset.date));
        }
      });

      // カートに追加
      const addToCartButton = this.container.querySelector('.rc-add-to-cart');
      if (addToCartButton) {
        addToCartButton.addEventListener('click', () => {
          this.addToCart();
        });
      }
    }

    handleDateClick(date) {
      if (!this.selecting || !this.selectedRange.start) {
        this.selectedRange = { start: date, end: null };
        this.selecting = true;
      } else {
        if (date < this.selectedRange.start) {
          this.selectedRange = { start: date, end: this.selectedRange.start };
        } else {
          this.selectedRange.end = date;
        }
        this.selecting = false;
        this.updateSelectedInfo();
      }
      this.renderMonths();
    }

    updateSelectedInfo() {
      const infoEl = this.container.querySelector('.rc-selected-info');
      const datesEl = this.container.querySelector('.rc-selected-dates');
      const daysEl = this.container.querySelector('.rc-rental-days');

      if (this.selectedRange.start && this.selectedRange.end) {
        const days = this.calculateBusinessDays(this.selectedRange.start, this.selectedRange.end);
        datesEl.textContent = `${format(this.selectedRange.start, 'yyyy/MM/dd', { locale: ja })} 〜 ${format(this.selectedRange.end, 'yyyy/MM/dd', { locale: ja })}`;
        daysEl.textContent = days;
        infoEl.style.display = 'block';
      } else {
        infoEl.style.display = 'none';
      }
    }

    calculateBusinessDays(start, end) {
      let days = 0;
      const current = new Date(start);
      
      while (current <= end) {
        const status = this.getDateStatus(current);
        if (!status.isSunday && !status.isHoliday) {
          days++;
        }
        current.setDate(current.getDate() + 1);
      }
      
      return days;
    }

    async addToCart() {
      if (!this.selectedRange.start || !this.selectedRange.end) return;

      const formData = {
        items: [{
          id: {{ product.selected_or_first_available_variant.id | json }},
          quantity: 1,
          properties: {
            'レンタル開始日': format(this.selectedRange.start, 'yyyy-MM-dd'),
            'レンタル終了日': format(this.selectedRange.end, 'yyyy-MM-dd'),
            'レンタル日数': this.calculateBusinessDays(this.selectedRange.start, this.selectedRange.end),
            '_rental_start_date': format(this.selectedRange.start, 'yyyy-MM-dd'),
            '_rental_end_date': format(this.selectedRange.end, 'yyyy-MM-dd'),
          }
        }]
      };

      try {
        const response = await fetch('/cart/add.js', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData)
        });

        if (response.ok) {
          window.location.href = '/cart';
        } else {
          throw new Error('カートへの追加に失敗しました');
        }
      } catch (error) {
        console.error('Error:', error);
        alert('カートへの追加に失敗しました。もう一度お試しください。');
      }
    }

    showError() {
      this.container.innerHTML = `
        <div class="rc-error">
          <p>カレンダーの読み込みに失敗しました。</p>
          <button onclick="location.reload()">再読み込み</button>
        </div>
      `;
    }
  }

  // DOMContentLoadedイベントで初期化
  document.addEventListener('DOMContentLoaded', () => {
    new RentalCalendar('rental-calendar-root-{{ block.id }}', '{{ product.id }}');
  });
</script>

<!-- カスタムスタイル -->
<style>
  {% comment %} 基本的なカレンダースタイルをインライン化 {% endcomment %}
  .rc-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
  }

  .rc-nav-button {
    background: white;
    border: 1px solid #e1e3e5;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .rc-nav-button:hover {
    background: #f6f6f7;
    border-color: #c4cdd5;
  }

  .rc-months {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
  }

  .rc-month {
    flex: 1;
    min-width: 280px;
  }

  .rc-month-title {
    text-align: center;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
  }

  .rc-table {
    width: 100%;
    border-collapse: collapse;
  }

  .rc-table th {
    text-align: center;
    padding: 0.5rem 0;
    font-size: 0.875rem;
    font-weight: 500;
    border-bottom: 1px solid #e1e3e5;
  }

  .rc-weekday--sunday {
    color: #bf0711;
  }

  .rc-table td {
    text-align: center;
    padding: 2px;
  }

  .rc-day {
    position: relative;
    width: 36px;
    height: 36px;
    border: 1px solid transparent;
    border-radius: 4px;
    background: none;
    cursor: pointer;
    font-size: 0.875rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
  }

  .rc-day:hover:not(:disabled) {
    background: #f1f2f4;
    border-color: #c4cdd5;
  }

  .rc-day:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .rc-day--other-month {
    color: #c4cdd5;
  }

  .rc-day--today {
    font-weight: bold;
    border-color: #202223;
  }

  .rc-day--holiday {
    color: #bf0711;
    background: rgba(253, 201, 201, 0.2);
  }

  .rc-day--confirmed {
    background: rgba(0, 128, 96, 0.2);
  }

  .rc-day--provisional {
    background: rgba(255, 184, 0, 0.2);
  }

  .rc-day--maintenance {
    background: rgba(171, 171, 171, 0.3);
  }

  .rc-day--selected {
    background: var(--rc-primary-color);
    color: white;
    border-color: var(--rc-primary-color);
  }

  .rc-day--in-range {
    background: rgba(0, 128, 96, 0.1);
    border-color: rgba(0, 128, 96, 0.3);
  }

  .rc-marker {
    position: absolute;
    bottom: 2px;
    font-size: 10px;
    font-weight: 600;
  }

  .rc-legend {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 4px;
    font-size: 0.875rem;
  }

  .rc-legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .rc-legend-marker {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
  }

  .rc-legend-marker--confirmed {
    background: rgba(0, 128, 96, 0.2);
    color: #004c3f;
  }

  .rc-legend-marker--provisional {
    background: rgba(255, 184, 0, 0.2);
    color: #8a6116;
  }

  .rc-legend-marker--maintenance {
    background: rgba(171, 171, 171, 0.3);
    color: #637381;
  }

  .rc-legend-marker--holiday {
    background: rgba(253, 201, 201, 0.2);
    color: #bf0711;
  }

  .rc-selected-info {
    margin-top: 1rem;
    padding: 1rem;
    background: #f6f6f7;
    border-radius: 4px;
  }

  .rc-add-to-cart {
    background: var(--rc-primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    margin-top: 0.5rem;
  }

  .rc-add-to-cart:hover {
    background: var(--rc-primary-hover);
  }

  @media (max-width: 768px) {
    .rc-months {
      flex-direction: column;
    }
  }
</style>

{% schema %}
{
  "name": "レンタルカレンダー（改良版）",
  "target": "section",
  "templates": ["product"],
  "settings": [
    { 
      "type": "product", 
      "id": "product", 
      "label": "商品", 
      "autofill": true 
    },
    {
      "type": "range",
      "id": "number_of_months",
      "label": "表示する月数",
      "min": 1,
      "max": 3,
      "step": 1,
      "default": 2
    },
    {
      "type": "checkbox",
      "id": "show_legend",
      "label": "凡例を表示",
      "default": true
    }
  ]
}
{% endschema %}
