/**
 * コレクションページでのレンタル機能を管理するJavaScript
 *
 * このスクリプトは、コレクションページでの日付選択、商品フィルタリング、
 * 複数商品のカートへの追加機能を提供します。
 */

class CollectionRental {
  constructor(options = {}) {
    // 設定を読み込んでから初期化
    this.loadConfig().then(() => {
      // オプションの設定
      this.options = {
        apiEndpoint: `${this.config.appUrl}/api/check-collection-availability`,
        ...options
      };

      // 状態の初期化
      this.state = {
        startDate: null,
        endDate: null,
        rentalDays: 0,
        selectedProducts: [],
        isLoading: false,
        errorMessage: ''
      };

      // DOM要素の参照
      this.elements = {
        startDateInput: document.getElementById('collection-start-date'),
        endDateInput: document.getElementById('collection-end-date'),
        rentalDaysElement: document.getElementById('collection-rental-days'),
        productContainer: document.getElementById('collection-products'),
        selectedProductsContainer: document.getElementById('selected-products'),
        addToCartButton: document.getElementById('add-selected-to-cart'),
        errorMessageElement: document.getElementById('collection-error-message'),
        loadingIndicator: document.getElementById('collection-loading-indicator')
      };

      // イベントリスナーの設定
      this.setupEventListeners();

      // 日付選択フィールドの初期化を遅延させて強制的に行う
      setTimeout(() => {
        this.initDateFields();
      }, 500);

      console.log("CollectionRental initialized with config:", this.config);
    }).catch(error => {
      console.error("Failed to load config:", error);
      // フォールバック設定を使用して初期化
      this.initWithFallbackConfig(options);
    });
  }

  /**
   * フォールバック設定で初期化
   */
  initWithFallbackConfig(options) {
    // フォールバック設定
    this.config = {
      appUrl: 'https://app.shopify-app-test.xyz',
      environment: 'development'
    };

    // オプションの設定
    this.options = {
      apiEndpoint: `${this.config.appUrl}/api/check-collection-availability`,
      ...options
    };

    // 状態の初期化
    this.state = {
      startDate: null,
      endDate: null,
      rentalDays: 0,
      selectedProducts: [],
      isLoading: false,
      errorMessage: ''
    };

    // DOM要素の参照
    this.elements = {
      startDateInput: document.getElementById('collection-start-date'),
      endDateInput: document.getElementById('collection-end-date'),
      rentalDaysElement: document.getElementById('collection-rental-days'),
      productContainer: document.getElementById('collection-products'),
      selectedProductsContainer: document.getElementById('selected-products'),
      addToCartButton: document.getElementById('add-selected-to-cart'),
      errorMessageElement: document.getElementById('collection-error-message'),
      loadingIndicator: document.getElementById('collection-loading-indicator')
    };

    // イベントリスナーの設定
    this.setupEventListeners();

    // 日付選択フィールドの初期化を遅延させて強制的に行う
    setTimeout(() => {
      this.initDateFields();
    }, 500);

    console.log("CollectionRental initialized with fallback config:", this.config);
  }

  /**
   * 設定を読み込む
   */
  async loadConfig() {
    try {
      // テーマから環境設定を取得
      if (window.shopConfig) {
        this.config = window.shopConfig;
        console.log("Config loaded from theme:", this.config);
        return;
      }

      // テーマから設定が取得できない場合はフォールバック設定を使用
      this.config = {
        appUrl: 'https://app.shopify-app-test.xyz',
        environment: 'development'
      };
      console.log("Using fallback config:", this.config);
    } catch (error) {
      console.error("Error loading config:", error);
      // フォールバック設定
      this.config = {
        appUrl: 'https://app.shopify-app-test.xyz',
        environment: 'development'
      };
      console.log("Using fallback config:", this.config);
    }
  }

  /**
   * 日付選択フィールドの初期化
   */
  initDateFields() {
    if (this.elements.startDateInput && this.elements.endDateInput) {
      // 現在の日付を取得して設定
      const today = new Date();
      const yyyy = today.getFullYear();
      const mm = String(today.getMonth() + 1).padStart(2, '0');
      const dd = String(today.getDate()).padStart(2, '0');
      const formattedToday = `${yyyy}-${mm}-${dd}`;

      // 元のmin属性を確認
      const originalStartDateMin = this.elements.startDateInput.getAttribute('min');
      const originalEndDateMin = this.elements.endDateInput.getAttribute('min');

      console.log('元のmin属性:', {
        startDateInputMin: originalStartDateMin,
        endDateInputMin: originalEndDateMin
      });

      // min属性を削除
      this.elements.startDateInput.removeAttribute('min');
      this.elements.endDateInput.removeAttribute('min');

      // min属性を現在の日付に設定（複数の方法で設定）
      // 方法1: 直接プロパティとして設定
      this.elements.startDateInput.min = formattedToday;
      this.elements.endDateInput.min = formattedToday;

      // 方法2: setAttribute メソッドを使用
      this.elements.startDateInput.setAttribute('min', formattedToday);
      this.elements.endDateInput.setAttribute('min', formattedToday);

      // 方法3: defaultValue を設定
      this.elements.startDateInput.defaultValue = formattedToday;

      // 日付選択フィールドの設定をログに出力
      console.log('日付選択フィールドの初期化:', {
        today: formattedToday,
        startDateMin: this.elements.startDateInput.getAttribute('min'),
        endDateInputMin: this.elements.endDateInput.getAttribute('min')
      });

      // MutationObserverを使用して属性の変更を監視
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'min') {
            const newValue = mutation.target.getAttribute('min');
            if (newValue !== formattedToday) {
              console.log('min属性が変更されました:', {
                element: mutation.target.id,
                oldValue: formattedToday,
                newValue: newValue
              });

              // 再度設定
              mutation.target.setAttribute('min', formattedToday);
            }
          }
        });
      });

      // 監視を開始
      observer.observe(this.elements.startDateInput, { attributes: true, attributeFilter: ['min'] });
      observer.observe(this.elements.endDateInput, { attributes: true, attributeFilter: ['min'] });
    }
  }

  /**
   * イベントリスナーの設定
   */
  setupEventListeners() {
    // 日付入力のイベントリスナー
    if (this.elements.startDateInput && this.elements.endDateInput) {
      // 現在の日付を取得して設定
      const today = new Date();
      const yyyy = today.getFullYear();
      const mm = String(today.getMonth() + 1).padStart(2, '0');
      const dd = String(today.getDate()).padStart(2, '0');
      const formattedToday = `${yyyy}-${mm}-${dd}`;

      // min属性を現在の日付に設定（複数の方法で設定）
      // 方法1: 直接プロパティとして設定
      this.elements.startDateInput.min = formattedToday;
      this.elements.endDateInput.min = formattedToday;

      // 方法2: setAttribute メソッドを使用
      this.elements.startDateInput.setAttribute('min', formattedToday);
      this.elements.endDateInput.setAttribute('min', formattedToday);

      // 方法3: defaultValue を設定
      this.elements.startDateInput.defaultValue = formattedToday;

      // 日付選択フィールドの設定をログに出力
      console.log('日付選択フィールドの設定:', {
        today: formattedToday,
        startDateMin: this.elements.startDateInput.getAttribute('min'),
        endDateMin: this.elements.endDateInput.getAttribute('min')
      });

      this.elements.startDateInput.addEventListener('change', () => this.handleDateChange());
      this.elements.endDateInput.addEventListener('change', () => this.handleDateChange());
    }

    // 商品選択のイベントリスナー
    if (this.elements.productContainer) {
      this.elements.productContainer.addEventListener('click', (event) => {
        const addButton = event.target.closest('.add-product-button');
        if (addButton) {
          const productId = addButton.getAttribute('data-product-id');
          const variantId = addButton.getAttribute('data-variant-id');
          if (productId && variantId) {
            this.addSelectedProduct(productId, variantId);
          }
        }
      });
    }

    // 選択商品の削除イベントリスナー
    if (this.elements.selectedProductsContainer) {
      this.elements.selectedProductsContainer.addEventListener('click', (event) => {
        const removeButton = event.target.closest('.remove-product-button');
        if (removeButton) {
          const productId = removeButton.getAttribute('data-product-id');
          if (productId) {
            this.removeSelectedProduct(productId);
          }
        }
      });
    }

    // カートに追加ボタンのイベントリスナー
    if (this.elements.addToCartButton) {
      this.elements.addToCartButton.addEventListener('click', () => this.handleAddToCart());
    }
  }

  /**
   * 日付変更時の処理
   */
  handleDateChange() {
    // 入力値の取得
    const startDateStr = this.elements.startDateInput.value;
    const endDateStr = this.elements.endDateInput.value;

    // 日付のバリデーション
    if (!startDateStr || !endDateStr) {
      this.updateState({
        startDate: null,
        endDate: null,
        rentalDays: 0,
        errorMessage: '開始日と終了日を選択してください'
      });
      this.updateUI();
      return;
    }

    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);

    // 終了日が開始日より前の場合
    if (endDate < startDate) {
      this.updateState({
        startDate: startDate,
        endDate: null,
        rentalDays: 0,
        errorMessage: '終了日は開始日より後の日付を選択してください'
      });
      this.updateUI();
      return;
    }

    // レンタル日数の計算
    const rentalDays = this.calculateRentalDays(startDate, endDate);

    // 状態の更新
    this.updateState({
      startDate: startDate,
      endDate: endDate,
      rentalDays: rentalDays,
      errorMessage: ''
    });

    // 在庫状況の確認
    this.checkCollectionAvailability();
  }

  /**
   * レンタル日数の計算
   */
  calculateRentalDays(startDate, endDate) {
    // 日曜日と祝日を除外した営業日数を計算
    // 簡易版: 日数の差を計算
    const diffTime = Math.abs(endDate - startDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // 初日も含める
    return diffDays;
  }

  /**
   * コレクション全体の在庫状況の確認
   */
  async checkCollectionAvailability() {
    if (!this.state.startDate || !this.state.endDate) {
      return;
    }

    this.updateState({ isLoading: true, errorMessage: '' });
    this.updateUI();

    try {
      const response = await fetch(this.options.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          startDate: this.formatDate(this.state.startDate),
          endDate: this.formatDate(this.state.endDate)
        })
      });

      const data = await response.json();

      if (!data.success) {
        this.updateState({
          errorMessage: data.error || '在庫状況の確認中にエラーが発生しました'
        });
      } else {
        // 商品の在庫状況を更新
        this.updateProductAvailability(data.products || []);
      }
    } catch (error) {
      console.error('在庫確認エラー:', error);
      this.updateState({
        errorMessage: '在庫状況の確認中にエラーが発生しました'
      });
    } finally {
      this.updateState({ isLoading: false });
      this.updateUI();
    }
  }

  /**
   * 商品の在庫状況を更新
   */
  updateProductAvailability(availableProducts) {
    // 商品カードの在庫状況を更新
    const productCards = document.querySelectorAll('.product-card');

    productCards.forEach(card => {
      const productId = card.getAttribute('data-product-id');
      const addButton = card.querySelector('.add-product-button');
      const availabilityBadge = card.querySelector('.availability-badge');

      // 在庫状況の確認
      const isAvailable = availableProducts.some(p => p.productId === productId && p.available);

      // ボタンの有効/無効
      if (addButton) {
        addButton.disabled = !isAvailable;
      }

      // 在庫バッジの表示
      if (availabilityBadge) {
        availabilityBadge.textContent = isAvailable ? '予約可能' : '予約不可';
        availabilityBadge.className = `availability-badge ${isAvailable ? 'available' : 'unavailable'}`;
      }
    });
  }

  /**
   * 選択商品の追加
   */
  addSelectedProduct(productId, variantId) {
    // 既に選択されている商品かチェック
    const existingProduct = this.state.selectedProducts.find(p => p.productId === productId);

    if (existingProduct) {
      // 既に選択されている場合は数量を増やす
      existingProduct.quantity += 1;
    } else {
      // 商品情報の取得
      const productCard = document.querySelector(`.product-card[data-product-id="${productId}"]`);

      if (!productCard) {
        console.error('商品情報が見つかりません:', productId);
        return;
      }

      const productTitle = productCard.getAttribute('data-product-title') || '';
      const variantTitle = productCard.getAttribute('data-variant-title') || '';
      const basePrice = parseFloat(productCard.getAttribute('data-price') || 0);
      const productImage = productCard.getAttribute('data-image') || '';

      // 料金計算
      const { totalPrice } = this.calculatePrice(basePrice, this.state.rentalDays);

      // 選択商品に追加
      this.state.selectedProducts.push({
        productId,
        variantId,
        title: productTitle,
        variantTitle,
        basePrice,
        price: totalPrice,
        image: productImage,
        quantity: 1
      });
    }

    // UI更新
    this.updateUI();
  }

  /**
   * 選択商品の削除
   */
  removeSelectedProduct(productId) {
    this.state.selectedProducts = this.state.selectedProducts.filter(p => p.productId !== productId);
    this.updateUI();
  }

  /**
   * 料金の計算
   */
  calculatePrice(basePrice, days) {
    if (days <= 0 || !basePrice) {
      return { totalPrice: 0, depositAmount: 0 };
    }

    let totalPrice = 0;

    // 1日目: 100%
    totalPrice += basePrice;

    // 2-6日目: 20%
    const days2to6 = Math.min(days - 1, 5);
    if (days2to6 > 0) {
      totalPrice += basePrice * 0.2 * days2to6;
    }

    // 7日目以降: 10%
    const days7plus = Math.max(days - 6, 0);
    if (days7plus > 0) {
      totalPrice += basePrice * 0.1 * days7plus;
    }

    // デポジット: 基本料金の10%
    const depositAmount = Math.round(basePrice * 0.1);

    return {
      totalPrice: Math.round(totalPrice),
      depositAmount: depositAmount
    };
  }

  /**
   * カートに追加
   */
  async handleAddToCart() {
    // 日付と選択商品のチェック
    if (!this.state.startDate || !this.state.endDate) {
      this.updateState({ errorMessage: 'レンタル期間を選択してください' });
      this.updateUI();
      return;
    }

    if (this.state.selectedProducts.length === 0) {
      this.updateState({ errorMessage: '商品を選択してください' });
      this.updateUI();
      return;
    }

    // ローディング表示
    this.updateState({ isLoading: true, errorMessage: '' });
    this.updateUI();

    try {
      // 選択商品をカートに追加
      for (const product of this.state.selectedProducts) {
        const cartItem = {
          variantId: product.variantId,
          productId: product.productId,
          title: product.title,
          variantTitle: product.variantTitle,
          startDate: this.formatDate(this.state.startDate),
          endDate: this.formatDate(this.state.endDate),
          bookingType: 'CONFIRMED',
          rentalDays: this.state.rentalDays,
          price: product.price,
          image: product.image,
          quantity: product.quantity
        };

        window.rentalCart.addItem(cartItem);
      }

      // 成功メッセージを表示
      alert('選択した商品がカートに追加されました');

      // 選択商品をクリア
      this.updateState({ selectedProducts: [] });

      // カートページに遷移するかどうかを確認
      if (confirm('カートページに移動しますか？')) {
        window.location.href = '/pages/rental-cart';
      }
    } catch (error) {
      console.error('カート追加エラー:', error);
      this.updateState({ errorMessage: 'カートへの追加中にエラーが発生しました' });
    } finally {
      this.updateState({ isLoading: false });
      this.updateUI();
    }
  }

  /**
   * 状態の更新
   */
  updateState(newState) {
    this.state = { ...this.state, ...newState };
  }

  /**
   * UIの更新
   */
  updateUI() {
    // レンタル日数の表示
    if (this.elements.rentalDaysElement) {
      this.elements.rentalDaysElement.textContent = this.state.rentalDays;
    }

    // 選択商品の表示
    if (this.elements.selectedProductsContainer) {
      this.elements.selectedProductsContainer.innerHTML = '';

      if (this.state.selectedProducts.length === 0) {
        this.elements.selectedProductsContainer.innerHTML = '<p>選択された商品はありません</p>';
      } else {
        this.state.selectedProducts.forEach(product => {
          const productElement = document.createElement('div');
          productElement.className = 'selected-product';
          productElement.innerHTML = `
            <div class="selected-product-info">
              <img src="${product.image || '/assets/no-image.jpg'}" alt="${product.title}" class="selected-product-image">
              <div class="selected-product-details">
                <h4>${product.title}</h4>
                <p>${product.variantTitle || ''}</p>
                <p>数量: ${product.quantity}</p>
                <p>価格: ${this.formatPrice(product.price)}</p>
              </div>
            </div>
            <button class="remove-product-button" data-product-id="${product.productId}">削除</button>
          `;
          this.elements.selectedProductsContainer.appendChild(productElement);
        });
      }
    }

    // エラーメッセージの表示
    if (this.elements.errorMessageElement) {
      this.elements.errorMessageElement.textContent = this.state.errorMessage;
      this.elements.errorMessageElement.style.display = this.state.errorMessage ? 'block' : 'none';
    }

    // ローディングインジケーターの表示
    if (this.elements.loadingIndicator) {
      this.elements.loadingIndicator.style.display = this.state.isLoading ? 'block' : 'none';
    }

    // カートに追加ボタンの有効/無効
    if (this.elements.addToCartButton) {
      this.elements.addToCartButton.disabled = this.state.selectedProducts.length === 0 || this.state.isLoading;
    }
  }

  /**
   * 日付のフォーマット (YYYY-MM-DD)
   */
  formatDate(date) {
    if (!date) return '';
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * 金額のフォーマット
   */
  formatPrice(price) {
    return '¥' + parseInt(price).toLocaleString();
  }
}

// グローバルオブジェクトとして公開
window.CollectionRental = CollectionRental;
