# Shopify-Prisma同期設計

このドキュメントでは、ShopifyとPrismaデータベース間のデータ同期の設計と実装について説明します。

## 目次

1. [同期データ項目](#同期データ項目)
2. [同期タイミング](#同期タイミング)
3. [同期プロセス](#同期プロセス)
4. [データ照合ロジック](#データ照合ロジック)
5. [セキュリティとプライバシー](#セキュリティとプライバシー)
6. [エラー処理と再試行](#エラー処理と再試行)
7. [同期ステータス管理](#同期ステータス管理)
8. [実装コンポーネント](#実装コンポーネント)
9. [今後の改善点](#今後の改善点)

## 同期データ項目

ShopifyとPrisma間で同期が必要なデータ項目と、それぞれの同期方針は以下の通りです。レンタルサービスのスケジュール管理という主目的に基づき、必要最小限のデータのみを同期します：

| データ項目 | 同期方向 | 同期内容 | 優先度 | セキュリティ考慮事項 |
|----------|---------|---------|-------|-----------------|
| **商品基本情報** | Shopify → Prisma | ID、タイトル、SKU、ステータス | 高 | 低感度データ |
| **顧客** | Shopify → Prisma | ID参照のみ | 中 | 高感度データ |
| **注文** | Shopify → Prisma | 注文ID、ステータス | 最高 | 中感度データ |
| **予約** | Prisma → Shopify | メタデータとして | 高 | 中感度データ |
| **在庫** | 双方向 | 在庫レベル | 最高 | 低感度データ |
| **支払い情報** | Shopify → Prisma | 支払いステータスのみ | 高 | 高感度データ |
| **ロケーション** | Shopify → Prisma | 在庫場所ID | 中 | 低感度データ |

### 同期しないデータ項目

以下のデータ項目は、レンタルサービスのスケジュール管理という主目的に直接関係ないため、同期しません：

| データ項目 | 理由 | 代替アプローチ |
|----------|------|--------------|
| **商品詳細説明** | 予約管理に不要 | 必要時にShopify APIで参照 |
| **商品画像** | 予約管理に不要 | Shopifyで表示、URLのみ必要時に参照 |
| **コレクション** | 予約管理に直接関係なし | 必要時にShopify APIで参照 |
| **割引/プロモーション** | バリエーションで価格対応済み | Shopifyの機能を使用 |
| **配送詳細情報** | 予約管理に詳細不要 | ステータスのみ必要時に参照 |
| **返品/交換詳細** | 予約管理に詳細不要 | ステータスのみ必要時に参照 |
| **メディア/画像** | 予約管理に不要 | Shopifyで表示、URLのみ必要時に参照 |
| **顧客詳細情報** | プライバシー保護のため | 必要時にShopify APIで参照 |

## 同期タイミング

### 1. 商品登録のタイミング

| 同期トリガー | 説明 | 実装状況 |
|------------|------|---------|
| **Shopifyウェブフック (PRODUCTS_CREATE)** | 商品がShopifyで作成された時に自動的に同期 | 実装済み (`app/routes/webhooks.products.jsx`) |
| **手動同期** | 管理画面から手動で同期を実行 | 実装済み (`app/services/sync/db-sync.service.ts`) |
| **初回アプリインストール時** | アプリがインストールされた時に全商品を同期 | 実装済み (`app/routes/app.jsx`) |

### 2. 商品削除のタイミング

| 同期トリガー | 説明 | 実装状況 |
|------------|------|---------|
| **Shopifyウェブフック (PRODUCTS_DELETE)** | 商品がShopifyで削除された時に自動的に同期 | 未実装 |
| **手動同期** | 管理画面から手動で同期を実行（削除フラグの更新） | 実装済み (`app/services/sync/db-sync.service.ts`) |
| **定期バッチ処理** | 定期的にShopifyと照合し、削除された商品を検出 | 部分実装 (`app/jobs/syncProducts.server.ts`) |

### 3. 定期的な更新

| 同期トリガー | 説明 | 実装状況 |
|------------|------|---------|
| **スケジュールされたジョブ** | 毎日または設定された間隔で全商品を同期 | 実装済み (`app/jobs/syncProducts.server.ts`) |
| **未同期データの更新** | 同期ステータスが「pending」または「error」の商品を優先的に同期 | 実装済み (`app/services/sync/db-sync.service.ts`) |
| **古いデータの更新** | 最終同期日時から一定期間（例：24時間）経過した商品を同期 | 実装済み (`app/services/sync/db-sync.service.ts`) |

### 4. 登録データ更新のタイミング

| 同期トリガー | 説明 | 実装状況 |
|------------|------|---------|
| **Shopifyウェブフック (PRODUCTS_UPDATE)** | 商品がShopifyで更新された時に自動的に同期 | 実装済み (`app/routes/webhooks.products.update.tsx`) |
| **在庫変更時** | 在庫レベルが変更された時に同期 | 部分実装 (`app/services/sync/inventory-sync.service.ts`) |
| **予約作成/更新/キャンセル時** | 予約状態が変更された時に在庫カレンダーを更新 | 実装済み (`app/services/inventory-calendar.service.ts`) |
| **メタフィールド更新時** | メタフィールドが更新された時に同期 | 実装済み (`app/services/sync/metafield-sync.service.ts`) |

### 5. 顧客データの同期タイミング

| 同期トリガー | 説明 | 実装状況 |
|------------|------|---------|
| **注文作成時** | 注文が作成された時に顧客IDのみを参照 | 部分実装 |
| **予約作成時** | 予約が作成された時に顧客IDのみを参照 | 部分実装 |
| **オンデマンド参照** | 必要な時にShopify APIで顧客情報を参照 | 未実装 |

### 6. 注文データの同期タイミング

| 同期トリガー | 説明 | 実装状況 |
|------------|------|---------|
| **Shopifyウェブフック (ORDERS_CREATE)** | 注文がShopifyで作成された時に同期 | 実装済み (`app/routes/webhooks.orders.jsx`) |
| **Shopifyウェブフック (ORDERS_UPDATED)** | 注文がShopifyで更新された時に同期 | 実装済み |
| **Shopifyウェブフック (ORDERS_CANCELLED)** | 注文がキャンセルされた時に同期 | 実装済み |
| **定期バッチ処理** | 定期的に注文データを同期 | 部分実装 |

### 7. 在庫データの同期タイミング

| 同期トリガー | 説明 | 実装状況 |
|------------|------|---------|
| **Shopifyウェブフック (INVENTORY_LEVELS_UPDATE)** | 在庫レベルが更新された時に同期 | 部分実装 |
| **予約状態変更時** | 予約の作成/確定/キャンセル時に在庫を更新 | 実装済み |
| **定期バッチ処理** | 定期的に在庫データを同期 | 部分実装 |

## 同期プロセス

### Shopify → Prisma の同期フロー

1. **同期トリガーの発生**
   - ウェブフック受信
   - 手動同期実行
   - スケジュールされたジョブの実行

2. **データの取得**
   - Shopify Admin API（GraphQL/REST）を使用してデータを取得
   - 必要なメタフィールドも取得

3. **データ変換**
   - Shopifyデータ形式からPrismaモデル形式に変換
   - 必要な計算や整形を実行

4. **データベース操作**
   - 既存データの検索（ID、SKU等で照合）
   - 新規作成または更新
   - 関連データの更新

5. **同期ステータスの更新**
   - 同期完了時刻の記録
   - 同期ステータスの更新

6. **同期ログの記録**
   - 同期結果の記録
   - エラー情報の記録（発生した場合）

### Prisma → Shopify の同期フロー

1. **同期トリガーの発生**
   - 管理画面からの操作
   - 予約状態の変更

2. **データ変換**
   - Prismaモデル形式からShopify API形式に変換

3. **Shopify API呼び出し**
   - Admin APIを使用してデータを更新
   - メタフィールドの更新

4. **結果の確認と記録**
   - API応答の確認
   - 同期ログの記録

## データ照合ロジック

商品データの照合は以下の優先順位で行われます：

1. **ShopifyID**による照合
   - 最も信頼性の高い照合方法
   - `gid://shopify/Product/123456789` 形式のIDを使用

2. **SKU**による照合（バックアップ）
   - ShopifyIDが見つからない場合に使用
   - SKUの一意性を確保する必要あり

3. **タイトルと属性**による照合（最終手段）
   - 他の方法で照合できない場合の最終手段
   - 完全一致ではなく、類似度による照合も考慮

## セキュリティとプライバシー

### 顧客データの最小化原則

顧客情報などの機密データについては、以下の原則に従って同期を行います：

1. **ID参照のみ**: 顧客の個人情報はShopifyに保存し、Prismaには顧客IDのみを保存
2. **必要時のみ参照**: 顧客情報が必要な場合のみ、Shopify APIを通じて参照
3. **表示時のマスキング**: 管理画面での表示時に個人情報を部分的にマスキング

### 顧客参照サービスの実装

顧客IDのみを保存し、必要時にShopify APIから顧客情報を取得する方式のテストを実施し、実行可能性を確認しました。詳細は「Shopify_Prisma顧客参照テスト結果.md」を参照してください。

実装のポイント：

1. **キャッシュ機構**: 同じ顧客情報の複数回取得を最適化するためのキャッシュ実装
2. **エラーハンドリング**: API接続エラーや権限エラーの適切な処理
3. **表示コンポーネント**: 顧客情報を効率的に表示するUIコンポーネント
4. **検索機能**: 顧客名やメールアドレスによる検索機能

### 支払い情報の保護

支払い情報は高感度データであるため、以下の方針で取り扱います：

1. **ステータスのみ同期**: 支払い状態（完了、保留、失敗等）のみを同期
2. **決済詳細は非同期**: カード情報等の決済詳細はPrismaに保存しない
3. **トークン参照**: 必要な場合はShopify/決済プロバイダーのトークンのみを参照

### データアクセス制御

同期データへのアクセスを制御するため、以下の対策を実施します：

1. **ロールベースアクセス**: 管理者、スタッフ等の役割に応じたアクセス制限
2. **監査ログ**: データアクセスの記録と監視
3. **暗号化**: 保存データの暗号化（特に機密情報）

## エラー処理と再試行

### エラー種別と対応

| エラー種別 | 対応 |
|----------|------|
| **API接続エラー** | 指数バックオフによる再試行 |
| **レート制限エラー** | 待機後に再試行 |
| **データ不整合エラー** | ログ記録と手動確認フラグ |
| **権限エラー** | 管理者通知 |

### 再試行メカニズム

- **即時再試行**: 一時的なエラーの場合、同じリクエスト内で再試行
- **遅延再試行**: 同期キューに再度追加し、後で再試行
- **手動再試行**: 管理画面から手動で再同期を実行

## 同期ステータス管理

データ項目には以下の同期ステータスが設定されます：

| ステータス | 説明 |
|----------|------|
| **pending** | 初期状態、同期待ち |
| **processing** | 同期処理中 |
| **synced** | 同期完了 |
| **error** | 同期エラー発生 |
| **deleted** | Shopifyで削除済み |

## 実装コンポーネント

### 主要コンポーネント

1. **DbSyncService** (`app/services/sync/db-sync.service.ts`)
   - 商品・注文の同期を担当する中心的なサービス

2. **ウェブフックハンドラー**
   - `app/routes/webhooks.products.jsx` - 商品作成・更新
   - `app/routes/webhooks.products.update.tsx` - 商品更新
   - `app/routes/webhooks.products.create.tsx` - 商品作成
   - `app/routes/webhooks.orders.jsx` - 注文同期

3. **同期ジョブ**
   - `app/jobs/syncProducts.server.ts` - 定期的な商品同期
   - `app/jobs/syncOrders.server.ts` - 定期的な注文同期
   - `app/jobs/syncInventory.server.ts` - 定期的な在庫同期

4. **在庫同期**
   - `app/services/sync/inventory-sync.service.ts` - 在庫データの同期
   - `app/services/inventory-calendar.service.ts` - 在庫カレンダーの管理

5. **メタフィールド同期**
   - `app/services/sync/metafield-sync.service.ts` - メタフィールドの同期

6. **顧客参照サービス**
   - `app/services/customer-reference.service.ts` - 顧客IDによる参照

### テストツール

- `scripts/test-shopify-prisma-integration.ts` - 同期機能の総合テスト
- `scripts/sync-products.js` - 商品同期のテスト
- `scripts/sync-product-to-db.ts` - 個別商品の同期テスト

## 今後の改善点

1. **商品削除ウェブフックの実装**
   - 商品削除時の適切な処理を実装

2. **同期キューの最適化**
   - 優先度ベースの同期キューの実装
   - 同期処理のバッチ化

3. **競合解決メカニズムの強化**
   - 同時更新時の競合解決ロジックの改善
   - 楽観的ロック機構の導入

4. **同期モニタリングの強化**
   - 同期状態のダッシュボード
   - 同期エラーの通知システム

5. **差分同期の実装**
   - 変更されたフィールドのみを更新する最適化

6. **顧客データ参照の最適化**
   - 顧客IDのみを保存し、必要時にShopify APIで参照する仕組みの強化
   - キャッシュ戦略の実装

7. **不要データの同期削減**
   - 商品詳細、画像URL、コレクション情報など不要なデータの同期を停止
   - 必要最小限のデータのみを同期する仕組みへの移行

8. **ロケーション管理の最適化**
   - 在庫場所IDのみの同期に簡略化

9. **オンデマンド参照の強化**
   - 必要時のみShopify APIを呼び出す効率的な仕組みの実装
   - API呼び出し回数の最適化
