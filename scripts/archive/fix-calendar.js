// scripts/fix-calendar.js
// カレンダーの表示問題を修正するスクリプト

// 予約データを削除した後、カレンダーのスタイルをリセットする
document.addEventListener('DOMContentLoaded', () => {
  console.log('カレンダーの修正スクリプトを実行します');
  
  // すべての予約マークを削除
  const resetCalendarStyles = () => {
    console.log('カレンダーのスタイルをリセットします');

    // すべての日付要素を取得
    const dateElements = document.querySelectorAll('.Polaris-DatePicker__Day span');
    console.log(`日付要素を ${dateElements.length} 個見つけました`);

    // 各要素のスタイルをリセット
    dateElements.forEach((spanElement) => {
      // 予約マークを削除
      const bookingMark = spanElement.querySelector('.booking-mark');
      if (bookingMark) {
        bookingMark.remove();
        console.log('予約マークを削除しました');
      }

      // スタイルをリセット
      if (spanElement.style) {
        spanElement.style.color = '';
        spanElement.style.position = '';
        spanElement.style.textDecoration = '';
        console.log('スタイルをリセットしました');
      }
    });

    console.log('カレンダーのスタイルをリセットしました');
  };

  // 5秒後にリセットを実行
  setTimeout(resetCalendarStyles, 5000);
});
