import React, { useState, useEffect } from 'react';
import {
  Modal,
  FormLayout,
  TextField,
  Select,
  TextArea,
  Button,
  ButtonGroup,
  Card,
  Text,
  Divider,
  Checkbox
} from '@shopify/polaris';
import type { EnrichedAddress } from '../services/customer-address.service';

interface AddressExtensionFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (extensionData: any) => Promise<void>;
  address?: EnrichedAddress;
  orderPaths?: Array<{ code: string; name: string; isDefault: boolean }>;
  storeStaff?: Array<{ staffCode: string; staffName: string }>;
  showCorporateFields?: boolean;
}

export function AddressExtensionForm({
  isOpen,
  onClose,
  onSave,
  address,
  orderPaths = [],
  storeStaff = [],
  showCorporateFields = false
}: AddressExtensionFormProps) {
  const [extension, setExtension] = useState({
    fax: '',
    storeStaff: '',
    orderPath: '4', // デフォルトはWEB
    department: '',
    position: '',
    deliveryTimeSlot: '',
    deliveryNotes: '',
    deliveryMemo: '',
    addressName: '',
    addressType: 'shipping'
  });

  const [isLoading, setIsLoading] = useState(false);

  // フォームの初期化
  useEffect(() => {
    if (address?.extension) {
      setExtension({
        fax: address.extension.fax || '',
        storeStaff: address.extension.storeStaff || '',
        orderPath: address.extension.orderPath || '4',
        department: address.extension.department || '',
        position: address.extension.position || '',
        deliveryTimeSlot: address.extension.deliveryTimeSlot || '',
        deliveryNotes: address.extension.deliveryNotes || '',
        deliveryMemo: address.extension.deliveryMemo || '',
        addressName: address.extension.addressName || '',
        addressType: address.extension.addressType || 'shipping'
      });
    } else {
      // 新規作成時はデフォルト値を設定
      const defaultOrderPath = orderPaths.find(op => op.isDefault)?.code || '4';
      setExtension(prev => ({
        ...prev,
        orderPath: defaultOrderPath
      }));
    }
  }, [address, orderPaths]);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await onSave(extension);
      onClose();
    } catch (error) {
      console.error('住所拡張情報の保存エラー:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  // 受注方法のオプション
  const orderPathOptions = [
    { label: '受注方法を選択', value: '' },
    ...orderPaths.map(op => ({
      label: op.name + (op.isDefault ? ' (デフォルト)' : ''),
      value: op.code
    }))
  ];

  // ストア担当者のオプション
  const storeStaffOptions = [
    { label: 'ストア担当者を選択', value: '' },
    ...storeStaff.map(staff => ({
      label: `${staff.staffCode}: ${staff.staffName}`,
      value: staff.staffCode
    }))
  ];

  // 配送時間帯のオプション
  const deliveryTimeSlotOptions = [
    { label: '指定なし', value: '' },
    { label: '午前中（9-12時）', value: '9-12' },
    { label: '午後前半（14-16時）', value: '14-16' },
    { label: '午後後半（16-18時）', value: '16-18' },
    { label: '夜間（18-21時）', value: '18-21' }
  ];

  return (
    <Modal
      open={isOpen}
      onClose={onClose}
      title="住所詳細情報"
      primaryAction={{
        content: '保存',
        onAction: handleSave,
        loading: isLoading
      }}
      secondaryActions={[
        {
          content: 'キャンセル',
          onAction: handleCancel
        }
      ]}
      large
    >
      <Modal.Section>
        <FormLayout>
          {/* 基本住所情報の表示 */}
          {address?.isShopifyAddress && (
            <Card>
              <div style={{ padding: '16px' }}>
                <Text as="h3" variant="headingMd">基本住所情報</Text>
                <div style={{ marginTop: '8px' }}>
                  <Text as="p" variant="bodyMd">
                    {address.company && `${address.company}`}
                    {address.first_name && address.last_name && ` / ${address.last_name} ${address.first_name}`}
                  </Text>
                  <Text as="p" variant="bodyMd">
                    〒{address.zip} {address.province} {address.city} {address.address1}
                  </Text>
                  {address.address2 && (
                    <Text as="p" variant="bodyMd">{address.address2}</Text>
                  )}
                  <Text as="p" variant="bodyMd">TEL: {address.phone}</Text>
                </div>
              </div>
            </Card>
          )}

          <Divider />

          {/* 業務情報セクション */}
          <Card>
            <div style={{ padding: '16px' }}>
              <Text as="h3" variant="headingMd">業務情報</Text>
              <div style={{ marginTop: '16px' }}>
                <FormLayout>
                  <TextField
                    label="FAX番号"
                    value={extension.fax}
                    onChange={(value) => setExtension({ ...extension, fax: value })}
                    placeholder="03-1234-5678"
                    helpText="FAXでの連絡が必要な場合に入力してください"
                  />

                  <Select
                    label="ストア担当者"
                    options={storeStaffOptions}
                    value={extension.storeStaff}
                    onChange={(value) => setExtension({ ...extension, storeStaff: value })}
                    helpText="この顧客の担当者を選択してください"
                  />

                  <Select
                    label="受注方法"
                    options={orderPathOptions}
                    value={extension.orderPath}
                    onChange={(value) => setExtension({ ...extension, orderPath: value })}
                    helpText="主な受注方法を選択してください（デフォルト: WEB）"
                  />
                </FormLayout>
              </div>
            </div>
          </Card>

          {/* 組織情報セクション（法人フラグONの場合のみ） */}
          {showCorporateFields && (
            <Card>
              <div style={{ padding: '16px' }}>
                <Text as="h3" variant="headingMd">組織情報</Text>
                <div style={{ marginTop: '16px' }}>
                  <FormLayout>
                    <TextField
                      label="部署名"
                      value={extension.department}
                      onChange={(value) => setExtension({ ...extension, department: value })}
                      placeholder="例：営業部、制作部"
                    />

                    <TextField
                      label="役職"
                      value={extension.position}
                      onChange={(value) => setExtension({ ...extension, position: value })}
                      placeholder="例：部長、主任"
                    />

                    <TextField
                      label="住所名称"
                      value={extension.addressName}
                      onChange={(value) => setExtension({ ...extension, addressName: value })}
                      placeholder="例：本社、営業所、倉庫"
                      helpText="複数住所を管理する際の識別名"
                    />
                  </FormLayout>
                </div>
              </div>
            </Card>
          )}

          {/* 配送情報セクション */}
          <Card>
            <div style={{ padding: '16px' }}>
              <Text as="h3" variant="headingMd">配送情報</Text>
              <div style={{ marginTop: '16px' }}>
                <FormLayout>
                  <Select
                    label="配送時間帯"
                    options={deliveryTimeSlotOptions}
                    value={extension.deliveryTimeSlot}
                    onChange={(value) => setExtension({ ...extension, deliveryTimeSlot: value })}
                    helpText="希望する配送時間帯を選択してください"
                  />

                  <TextArea
                    label="配送に関する備考"
                    value={extension.deliveryNotes}
                    onChange={(value) => setExtension({ ...extension, deliveryNotes: value })}
                    placeholder="配送時の注意事項、アクセス方法、受付での手続きなど"
                    rows={3}
                    helpText="配送業者への指示や注意事項を記載してください"
                  />

                  <TextArea
                    label="配送メモ（常時表示）"
                    value={extension.deliveryMemo}
                    onChange={(value) => setExtension({ ...extension, deliveryMemo: value })}
                    placeholder={showCorporateFields 
                      ? "配送時の注意事項、アクセス方法、受付での手続きなど詳細な情報"
                      : "配送時の注意事項、アクセス方法など"
                    }
                    rows={showCorporateFields ? 4 : 2}
                    helpText="配送一覧画面で常に表示されるメモです"
                  />
                </FormLayout>
              </div>
            </div>
          </Card>
        </FormLayout>
      </Modal.Section>
    </Modal>
  );
}
