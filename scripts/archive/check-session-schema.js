const { PrismaClient } = require('@prisma/client');

async function checkSessionSchema() {
  const prisma = new PrismaClient();
  
  try {
    // データベースに接続
    await prisma.$connect();
    
    // テーブル情報を取得
    console.log('Prismaクライアントのモデル一覧:');
    console.log(Object.keys(prisma).filter(key => !key.startsWith('_') && !key.startsWith('$')));
    
    // sessionsテーブルの構造を確認
    try {
      const result = await prisma.$queryRaw`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'sessions'
      `;
      
      console.log('\nsessionsテーブルのカラム構造:');
      console.table(result);
    } catch (err) {
      console.error('sessionsテーブルの構造確認中にエラーが発生しました:', err);
    }
    
    // PrismaSessionStorageが期待するフィールド名を表示
    console.log('\nPrismaSessionStorageが期待するフィールド名:');
    console.log([
      'id', 'shop', 'state', 'isOnline', 'scope', 'expires', 
      'accessToken', 'userId', 'firstName', 'lastName', 'email',
      'accountOwner', 'locale', 'collaborator', 'emailVerified'
    ]);
    
  } catch (error) {
    console.error('エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSessionSchema();
