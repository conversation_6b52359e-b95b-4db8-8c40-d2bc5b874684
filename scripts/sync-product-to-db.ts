/**
 * 商品同期スクリプト
 *
 * このスクリプトは、Shopifyの商品データをPrismaデータベースに同期します。
 * 特定の商品名を検索して、データベースに同期します。
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';
import { formatSyncError } from '../app/utils/sync/error-handler';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// 商品検索のGraphQLクエリ
const SEARCH_PRODUCTS = gql`
  query searchProducts($query: String!, $first: Int!) {
    products(first: $first, query: $query) {
      edges {
        node {
          id
          title
          handle
          status
          description
          variants(first: 5) {
            edges {
              node {
                id
                title
                sku
                price
                inventoryQuantity
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    }
  }
`;

/**
 * 商品をデータベースに同期する関数
 * @param searchTerm 検索キーワード
 */
async function syncProductsToDatabase(searchTerm: string) {
  try {
    console.log(`Shopifyで「${searchTerm}」を検索中...`);

    // Shopify GraphQL APIを使用して商品を検索
    const response = await shopifyClient.request(SEARCH_PRODUCTS, {
      query: searchTerm,
      first: 10
    });

    // 検索結果を表示
    const products = response.products.edges;
    console.log(`Shopifyで ${products.length} 件の商品が見つかりました`);

    if (products.length === 0) {
      console.log('同期する商品がありません。');
      return;
    }

    console.log('\n=== 同期を開始します ===');

    // 同期結果を記録
    const syncResults = {
      created: 0,
      updated: 0,
      skipped: 0,
      errors: 0,
    };

    // 各商品を処理
    for (const edge of products) {
      const shopifyProduct = edge.node;
      const shopifyId = shopifyProduct.id.replace('gid://shopify/Product/', '');

      try {
        console.log(`商品「${shopifyProduct.title}」(ID: ${shopifyId})を処理中...`);

        // データベースに既存の商品があるか確認
        const existingProduct = await prisma.product.findFirst({
          where: {
            shopifyId,
          },
        });

        // バリアント情報を取得
        const variant = shopifyProduct.variants.edges[0]?.node;
        // SKUに商品IDを追加して一意性を確保（全桁を使用）
        const sku = variant?.sku ? `${variant.sku}-${shopifyId}` : `SKU-${shopifyId}`;
        const price = parseFloat(variant?.price || '0');

        // メタフィールドを解析
        const metafieldsData = shopifyProduct.metafields.edges.map((edge: any) => ({
          key: edge.node.key,
          value: edge.node.value,
          namespace: edge.node.namespace,
        }));

        // メタデータとして保存
        const metadata = {
          metafields: metafieldsData
        };

        // 基本情報を設定
        const basicInfo = {
          productCode: sku,
          status: shopifyProduct.status === 'ACTIVE' ? 'available' : 'unavailable',
          location: 'NY', // デフォルト値
        };

        if (existingProduct) {
          // 既存の商品を更新
          await prisma.product.update({
            where: {
              id: existingProduct.id,
            },
            data: {
              title: shopifyProduct.title,
              description: shopifyProduct.description,
              status: shopifyProduct.status === 'ACTIVE' ? 'AVAILABLE' : 'UNAVAILABLE',
              sku,
              price,
              metadata,
              basicInfo,
              updatedAt: new Date(),
            },
          });
          console.log(`商品「${shopifyProduct.title}」を更新しました`);
          syncResults.updated++;
        } else {
          // 新規商品を作成
          await prisma.product.create({
            data: {
              shop: process.env.SHOPIFY_SHOP || '',
              shopifyId,
              title: shopifyProduct.title,
              description: shopifyProduct.description,
              status: shopifyProduct.status === 'ACTIVE' ? 'AVAILABLE' : 'UNAVAILABLE',
              sku,
              price,
              metadata,
              basicInfo,
            },
          });
          console.log(`商品「${shopifyProduct.title}」を作成しました`);
          syncResults.created++;
        }
      } catch (error) {
        // エラーの種類を判断
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          // Prismaの既知のエラー
          if (error.code === 'P2002') {
            // 一意性制約違反
            console.warn(`商品「${shopifyProduct.title}」の同期中に一意性制約違反が発生しました。SKU: ${sku}`);
            console.warn(`詳細: ${error.message}`);
            syncResults.skipped++;
          } else {
            // その他のPrismaエラー
            console.error(`商品「${shopifyProduct.title}」の同期中にPrismaエラーが発生しました:`, error);
            syncResults.errors++;
          }
        } else {
          // その他のエラー
          console.error(`商品「${shopifyProduct.title}」の同期中にエラーが発生しました:`, formatSyncError(error));
          syncResults.errors++;
        }
      }
    }

    console.log('\n=== 同期結果 ===');
    console.log(`作成: ${syncResults.created} 件`);
    console.log(`更新: ${syncResults.updated} 件`);
    console.log(`スキップ: ${syncResults.skipped} 件`);
    console.log(`エラー: ${syncResults.errors} 件`);
    console.log('同期が完了しました。');

  } catch (error) {
    console.error('エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// コマンドライン引数から検索キーワードを取得
const searchTerm = process.argv[2] || 'Webhookテスト用レンタル商品';

// 商品同期を実行
syncProductsToDatabase(searchTerm)
  .then(() => console.log('処理完了'))
  .catch(error => console.error('処理エラー:', error));
