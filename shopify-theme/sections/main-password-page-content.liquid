<div data-section-id="{{ section.id }}" data-section-type="main-password-page-content">
    <div class="password-page-content py-100">
        <div class="row align-items-md-center">
            <div class="col-12{% if section.settings.image != blank %} col-md-6 col-lg-5 col-xl-4{% else %} d-flex justify-content-center{% endif %} align-items-center">
                <div>
                    <div class="password-page-content__subscription{% if section.settings.image != blank %} password-page-content__subscription--width-limit{% endif %}">
                        {% form 'customer', class: 'w-100' %}
                            {%- unless form.posted_successfully? -%}
                                {%- if section.settings.subscription_title -%}
                                    <h4 class="h3 mb-15 text-center{% if section.settings.image != blank %} text-lg-left{% endif %}">{{ section.settings.subscription_title }}</h4>
                                {%- endif -%}
                                {% if shop.password_message != blank %}
                                    <p class="text-center{% if section.settings.image != blank %} text-lg-left{% endif %}">{{ shop.password_message }}</p>
                                {%- elsif section.settings.subscription_paragraph -%}
                                    <p class="text-center{% if section.settings.image != blank %} text-lg-left{% endif %}">{{ section.settings.subscription_paragraph }}</p>
                                {%- endif -%}
                                <div class="d-flex flex-column mt-35 mb-15">
                                    <input type="hidden" name="contact[tags]" value="prospect, password page" required="required">
                                    <input type="email"
                                           name="contact[email]"
                                           class="mb-0"
                                           id="Email"
                                           spellcheck="false"
                                           autocomplete="off"
                                           autocapitalize="off"
                                           placeholder="{{ section.settings.subscription_placeholder }}">
                                    <button type="submit" name="commit" class="btn btn--secondary mt-10">{{ section.settings.subscription_button_text }}</button>
                                </div>
                            {%- endunless -%}
                            {% include 'form-get-message' %}
                        {% endform %}
                    </div>
                    {%- assign selected_blocks = section.blocks | where: 'type', 'social_media' -%}
                    {%- for block in selected_blocks -%}
                        <div class="password-page-content__social-media{% if section.settings.image == blank %} d-md-flex justify-content-md-center ml-md-10{% endif %} mt-35">
                            {% render 'social-media' with block: block type: 'row' %}
                        </div>
                    {%- endfor -%}
                </div>
            </div>
            {%- if section.settings.image != blank -%}
                <div class="d-none d-md-flex col-12 col-md-6 offset-lg-1 offset-xl-2 justify-content-md-end">
                    <div class="password-page-content__image w-100">
                        {% render 'rimage' with image: section.settings.image size: '700x' format: 'pjpg' image_class: 'w-100' disable_lazyload: true %}
                    </div>
                </div>
            {%- endif -%}
        </div>
    </div>
</div>

{% schema %}
{
    "name": "t:sections.content.name",
    "settings": [
        {
            "type": "header",
            "content": "t:sections.content.settings.header__1.content"
        },
        {
            "type": "text",
            "id": "subscription_title",
            "label": "t:sections.content.settings.subscription_title.label",
            "default": "当ストアは準備中です。楽しみにお待ち下さい。" // "Our new website will be available soon"
        },
        {
            "type": "text",
            "id": "subscription_paragraph",
            "label": "t:sections.content.settings.subscription_paragraph.label",
            "default": "『MISEル』のメルマガを購読してお得な情報・最新の情報を受け取ろう!", //"Be the first to know when website is ready",
            "info": "t:sections.content.settings.subscription_paragraph.info"
        },
        {
            "type": "text",
            "id": "subscription_placeholder",
            "label": "t:sections.content.settings.subscription_placeholder.label",
            "default": "Eメールアドレスを入力してください"// "Enter please your email address"
        },
        {
            "type": "text",
            "id": "subscription_button_text",
            "label": "t:sections.content.settings.subscription_button_text.label",
            "default": "購読" // "SUBSCRIBE!"
        },
        {
            "type": "header",
            "content": "t:sections.content.settings.header__2.content"
        },
        {
            "type": "image_picker",
            "id": "image",
            "label": "t:sections.content.settings.image.label"
        }
    ],
    "blocks": [
        {
            "type": "social_media",
            "name": "t:sections.content.blocks.social_media.name",
            "limit": 1,
            "settings": [
                {
                    "type": "checkbox",
                    "id": "show_facebook",
                    "label": "t:sections.content.blocks.social_media.settings.show_facebook.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_twitter",
                    "label": "t:sections.content.blocks.social_media.settings.show_twitter.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_instagram",
                    "label": "t:sections.content.blocks.social_media.settings.show_instagram.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_pinterest",
                    "label": "t:sections.content.blocks.social_media.settings.show_pinterest.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_youtube",
                    "label": "t:sections.content.blocks.social_media.settings.show_youtube.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_behance",
                    "label": "t:sections.content.blocks.social_media.settings.show_behance.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_skype",
                    "label": "t:sections.content.blocks.social_media.settings.show_skype.label",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_line",
                    "label": {
                        "ja": "LINE",
                        "en": "LINE"
                    },
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "show_tiktok",
                    "label": "t:sections.content.blocks.social_media.settings.show_tiktok.label",
                    "default": true
                }
            ]
        }
    ]
}
{% endschema %}