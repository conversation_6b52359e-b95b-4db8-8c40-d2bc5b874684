/**
 * メタフィールドとPrisma連携の包括的テスト
 * 仕様書準拠のメタフィールド設定とPrismaデータベースとの同期をテスト
 */

import { CompleteProductCreatorService, CSVProductData } from '../app/services/shopify/complete-product-creator.service';
import { GraphQLClient } from 'graphql-request';
import { PrismaClient } from '@prisma/client';
import * as dotenv from 'dotenv';

// .envファイルを読み込み
dotenv.config();

const prisma = new PrismaClient();

interface MetafieldValidationResult {
  success: boolean;
  totalMetafields: number;
  specCompliantMetafields: number;
  missingMetafields: string[];
  extraMetafields: string[];
  prismaSync: {
    success: boolean;
    syncedFields: string[];
    errors: string[];
  };
}

class MetafieldPrismaIntegrationTester {
  private productCreator: CompleteProductCreatorService;

  constructor() {
    this.productCreator = new CompleteProductCreatorService();
  }

  /**
   * 仕様書準拠のメタフィールドとPrisma連携をテスト
   */
  async testMetafieldPrismaIntegration(): Promise<void> {
    console.log('=== メタフィールド・Prisma連携包括テスト開始 ===\n');

    try {
      // 環境変数から設定を取得
      const shopifyShop = process.env.SHOPIFY_SHOP;
      const shopifyAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

      if (!shopifyShop || !shopifyAccessToken) {
        console.error('❌ 環境変数が設定されていません');
        return;
      }

      // GraphQLClientを直接作成
      const graphqlClient = new GraphQLClient(
        `https://${shopifyShop}/admin/api/2025-01/graphql.json`,
        {
          headers: {
            'X-Shopify-Access-Token': shopifyAccessToken,
            'Content-Type': 'application/json',
          },
        }
      );

      // adminオブジェクトを作成
      const admin = {
        graphql: async (query: string, variables?: any) => {
          const result = await graphqlClient.request(query, variables);
          return {
            json: () => Promise.resolve({ data: result })
          };
        }
      };

      // テスト商品データ（仕様書準拠テスト用）
      const testProduct: CSVProductData = {
        menu: 'FURNITURE',
        majorCategory: 'チェア',
        minorCategory: 'ダイニングチェア',
        name: 'デザイナーズチェア　ブラック　レザー',
        modelNumber: '101-01-001',
        sizeW: '450',
        sizeD: '500',
        sizeH: '800',
        sizeSH: '450',
        sizeOther: '肘掛け高さ: 650mm',
        color: 'ブラック',
        colorOther: 'マットブラック',
        material: 'レザー、スチール',
        other: 'イタリア製、デザイナー: ジョン・スミス',
        stock: '1',
        rentalPrice1Day: '2500',
        rentalPrice2Days: '3000',
        rentalPrice3Days: '3500',
        rentalPrice4Days: '4000',
        publicStatus: '公開',
        newStatus: 'あり',
        campaign: 'NEW ARRIVAL 2025'
      };

      console.log('1. 完全商品作成（仕様書準拠メタフィールド）...');
      
      const result = await this.productCreator.createCompleteProduct(admin, testProduct, {
        location: 'NY',
        createProvisionalVariants: false
      });

      if (!result.success || !result.shopifyProductId) {
        console.error('❌ 商品作成に失敗しました');
        console.error('エラー:', result.errors);
        return;
      }

      console.log(`✅ 商品作成成功: ${result.shopifyProductId}`);

      // 2. メタフィールド検証
      console.log('\n2. メタフィールド仕様書準拠性検証...');
      const validationResult = await this.validateMetafields(admin, result.shopifyProductId);
      
      this.displayMetafieldValidation(validationResult);

      // 3. Prisma同期テスト
      console.log('\n3. Prismaデータベース同期テスト...');
      const syncResult = await this.testPrismaSync(result.shopifyProductId, testProduct);
      
      this.displayPrismaSyncResult(syncResult);

      // 4. 総合評価
      console.log('\n4. 総合評価...');
      this.displayOverallAssessment(validationResult, syncResult);

      // 5. クリーンアップ
      console.log('\n5. テスト商品削除...');
      await this.cleanupTestProduct(admin, result.shopifyProductId);

    } catch (error) {
      console.error('❌ テスト実行エラー:', error);
    } finally {
      await prisma.$disconnect();
    }

    console.log('\n=== メタフィールド・Prisma連携包括テスト完了 ===');
  }

  /**
   * メタフィールドの仕様書準拠性を検証
   */
  private async validateMetafields(admin: any, shopifyProductId: string): Promise<MetafieldValidationResult> {
    try {
      // Shopifyからメタフィールドを取得
      const response = await admin.graphql(`
        query getProductMetafields($id: ID!) {
          product(id: $id) {
            metafields(first: 50) {
              edges {
                node {
                  namespace
                  key
                  value
                  type
                }
              }
            }
          }
        }
      `, {
        id: `gid://shopify/Product/${shopifyProductId}`
      });

      const data = await response.json();
      const metafields = data.data.product.metafields.edges.map((edge: any) => edge.node);

      // 仕様書で定義されている必須メタフィールド
      const requiredMetafields = [
        // 人が直接管理する項目
        'rental.status',
        'rental.location',
        'rental.maintenance_notes',
        'rental.booking_notes',
        'rental.booking_type',
        'rental.last_maintenance_date',
        
        // システムで管理する項目
        'rental.basic_info',
        'rental.pricing',
        
        // 寸法情報
        'product.width',
        'product.depth',
        'product.height',
        
        // 商品属性情報
        'rental.material',
        'rental.color',
        'rental.designer',
        
        // 購入・廃棄情報
        'rental.purchase_price',
        'rental.purchase_date',
        'rental.purchase_place',
        'rental.manufacturer',
        'rental.is_disposed',
        
        // バリエーション管理
        'rental.variation_group',
        'rental.variation_type'
      ];

      const foundMetafields = metafields.map((mf: any) => `${mf.namespace}.${mf.key}`);
      const missingMetafields = requiredMetafields.filter(req => !foundMetafields.includes(req));
      const extraMetafields = foundMetafields.filter((found: string) => !requiredMetafields.includes(found));

      return {
        success: missingMetafields.length === 0,
        totalMetafields: metafields.length,
        specCompliantMetafields: requiredMetafields.length - missingMetafields.length,
        missingMetafields,
        extraMetafields,
        prismaSync: {
          success: false,
          syncedFields: [],
          errors: []
        }
      };

    } catch (error) {
      console.error('メタフィールド検証エラー:', error);
      return {
        success: false,
        totalMetafields: 0,
        specCompliantMetafields: 0,
        missingMetafields: [],
        extraMetafields: [],
        prismaSync: {
          success: false,
          syncedFields: [],
          errors: [error.message]
        }
      };
    }
  }

  /**
   * Prismaデータベースとの同期をテスト
   */
  private async testPrismaSync(shopifyProductId: string, testProduct: CSVProductData): Promise<any> {
    try {
      // Prismaに商品データを作成/更新
      const productData = {
        shopifyId: shopifyProductId,
        shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
        title: testProduct.name,
        sku: testProduct.modelNumber,
        price: parseFloat(testProduct.rentalPrice1Day),
        status: testProduct.publicStatus === '公開' ? 'AVAILABLE' : 'UNAVAILABLE',
        width: parseInt(testProduct.sizeW) || null,
        height: parseInt(testProduct.sizeH) || null,
        depth: parseInt(testProduct.sizeD) || null,
        category: testProduct.majorCategory,
        basicInfo: {
          productCode: testProduct.modelNumber.split('-')[0],
          detailCode: testProduct.modelNumber.split('-')[1] || '001',
          modelNumber: testProduct.modelNumber,
          name: testProduct.name,
          location: 'NY',
          status: testProduct.publicStatus === '公開' ? 'available' : 'unavailable'
        },
        pricing: {
          basePrice: parseInt(testProduct.rentalPrice1Day),
          price2Days: parseInt(testProduct.rentalPrice2Days),
          price3Days: parseInt(testProduct.rentalPrice3Days),
          price4Days: parseInt(testProduct.rentalPrice4Days),
          depositRate: 0.1,
          discountRules: {
            day2_6_rate: 0.2,
            day7_plus_rate: 0.1
          }
        },
        metadata: {
          menu: testProduct.menu,
          majorCategory: testProduct.majorCategory,
          minorCategory: testProduct.minorCategory,
          color: testProduct.color,
          material: testProduct.material,
          campaign: testProduct.campaign,
          testCreated: true
        }
      };

      const product = await prisma.product.upsert({
        where: {
          shop_shopifyId: {
            shop: productData.shop,
            shopifyId: shopifyProductId
          }
        },
        update: productData,
        create: productData
      });

      return {
        success: true,
        productId: product.id,
        syncedFields: Object.keys(productData),
        errors: []
      };

    } catch (error) {
      console.error('Prisma同期エラー:', error);
      return {
        success: false,
        productId: null,
        syncedFields: [],
        errors: [error.message]
      };
    }
  }

  /**
   * メタフィールド検証結果を表示
   */
  private displayMetafieldValidation(result: MetafieldValidationResult): void {
    console.log('📋 メタフィールド仕様書準拠性検証結果:');
    console.log(`総メタフィールド数: ${result.totalMetafields}`);
    console.log(`仕様書準拠メタフィールド数: ${result.specCompliantMetafields}`);
    console.log(`準拠率: ${((result.specCompliantMetafields / 23) * 100).toFixed(1)}%`); // 23は仕様書の必須メタフィールド数

    if (result.missingMetafields.length > 0) {
      console.log('\n❌ 不足しているメタフィールド:');
      result.missingMetafields.forEach(field => console.log(`  - ${field}`));
    }

    if (result.extraMetafields.length > 0) {
      console.log('\n📝 追加のメタフィールド（仕様書外）:');
      result.extraMetafields.forEach(field => console.log(`  - ${field}`));
    }

    console.log(`\n総合評価: ${result.success ? '✅ 仕様書完全準拠' : '⚠️ 一部不足あり'}`);
  }

  /**
   * Prisma同期結果を表示
   */
  private displayPrismaSyncResult(result: any): void {
    console.log('🗄️ Prismaデータベース同期結果:');
    console.log(`同期状況: ${result.success ? '✅ 成功' : '❌ 失敗'}`);
    
    if (result.success) {
      console.log(`商品ID: ${result.productId}`);
      console.log(`同期フィールド数: ${result.syncedFields.length}`);
    }

    if (result.errors.length > 0) {
      console.log('\nエラー:');
      result.errors.forEach((error: string) => console.log(`  - ${error}`));
    }
  }

  /**
   * 総合評価を表示
   */
  private displayOverallAssessment(metafieldResult: MetafieldValidationResult, syncResult: any): void {
    const metafieldScore = (metafieldResult.specCompliantMetafields / 23) * 100;
    const syncScore = syncResult.success ? 100 : 0;
    const overallScore = (metafieldScore + syncScore) / 2;

    console.log('🎯 総合評価:');
    console.log(`メタフィールド準拠率: ${metafieldScore.toFixed(1)}%`);
    console.log(`Prisma同期成功率: ${syncScore}%`);
    console.log(`総合スコア: ${overallScore.toFixed(1)}%`);

    if (overallScore >= 90) {
      console.log('🎉 優秀！仕様書準拠とPrisma連携が完璧です');
    } else if (overallScore >= 70) {
      console.log('✅ 良好！一部改善の余地があります');
    } else {
      console.log('⚠️ 改善が必要です');
    }
  }

  /**
   * テスト商品を削除
   */
  private async cleanupTestProduct(admin: any, shopifyProductId: string): Promise<void> {
    try {
      // Shopify商品削除
      await admin.graphql(`
        mutation productDelete($input: ProductDeleteInput!) {
          productDelete(input: $input) {
            deletedProductId
            userErrors {
              field
              message
            }
          }
        }
      `, {
        input: {
          id: `gid://shopify/Product/${shopifyProductId}`
        }
      });

      // Prisma商品削除
      await prisma.product.deleteMany({
        where: {
          shopifyId: shopifyProductId
        }
      });

      console.log('✅ テスト商品削除完了');

    } catch (error) {
      console.error('⚠️ クリーンアップエラー:', error);
    }
  }
}

// メイン実行
async function main() {
  const tester = new MetafieldPrismaIntegrationTester();
  await tester.testMetafieldPrismaIntegration();
}

if (import.meta.url === new URL(import.meta.url).href) {
  main().catch(console.error);
}

export { MetafieldPrismaIntegrationTester };
