name: Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of test to run'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - basic
          - error-cases
          - variants
          - local

jobs:
  test:
    runs-on: ubuntu-latest
    
    env:
      NODE_ENV: test
      USE_LOCAL_MODE: ${{ github.event.inputs.test_type == 'local' || '' }}
      SHOPIFY_API_KEY: ${{ secrets.SHOPIFY_API_KEY }}
      SHOPIFY_API_SECRET: ${{ secrets.SHOPIFY_API_SECRET }}
      SHOPIFY_SHOP: ${{ secrets.SHOPIFY_SHOP }}
      SHOPIFY_ADMIN_API_ACCESS_TOKEN: ${{ secrets.SHOPIFY_ADMIN_API_ACCESS_TOKEN }}
      DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
    
    strategy:
      matrix:
        node-version: [20.x]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Setup database
      run: npx prisma db push --accept-data-loss
    
    - name: Run tests based on input
      run: |
        if [ "${{ github.event.inputs.test_type }}" = "basic" ] || [ "${{ github.event.inputs.test_type }}" = "all" ] || [ "${{ github.event.inputs.test_type }}" = "" ]; then
          echo "Running basic tests..."
          npm run test:shopify-prisma-integration -- --local
        fi
        
        if [ "${{ github.event.inputs.test_type }}" = "error-cases" ] || [ "${{ github.event.inputs.test_type }}" = "all" ]; then
          echo "Running error case tests..."
          npm run test:shopify-prisma-integration -- --error-cases --local
        fi
        
        if [ "${{ github.event.inputs.test_type }}" = "variants" ] || [ "${{ github.event.inputs.test_type }}" = "all" ]; then
          echo "Running variant tests..."
          npm run test:shopify-prisma-integration -- --variants --local
        fi
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      with:
        name: test-reports
        path: docs/shopify-prisma-integration-test-report.md
    
    - name: Cleanup test data
      if: always()
      run: npm run test:shopify-prisma-integration -- --cleanup --local
