# 在庫管理方法の検討

## 現状の実装
現在は、商品のメタフィールド（`rental.location`）を使用して在庫の場所（PR/NY）を管理しています。

## 管理方法の比較

### 1. メタフィールドでの管理（現状）

#### メリット
- **シンプルな実装**: メタフィールドは既に実装済みで、追加の開発が不要
- **Shopify管理画面での視認性**: 商品詳細画面で直接場所を確認できる
- **検索のしやすさ**: メタフィールドで検索・フィルタリングが可能
- **データ構造の一貫性**: 他のレンタル関連情報も全てメタフィールドで管理

#### デメリット
- **Shopifyの在庫管理機能との統合性が低い**: Shopifyの標準在庫管理機能と連携しにくい
- **複数ロケーションの拡張性**: 将来的にロケーションが増えた場合の拡張性に制限がある可能性

### 2. Shopifyロケーションでの管理

#### メリット
- **Shopifyの標準機能を活用**: Shopifyの在庫管理機能をそのまま利用できる
- **拡張性**: 将来的にロケーションが増えても対応しやすい
- **在庫移動の管理**: ロケーション間の在庫移動を記録できる
- **レポート機能**: Shopifyの標準レポート機能で在庫状況を確認できる

#### デメリット
- **実装の変更が必要**: 現状のメタフィールドベースの実装から変更が必要
- **追加開発の工数**: ロケーションベースの在庫管理に対応するための開発が必要
- **複雑性の増加**: システム全体の複雑性が増す可能性がある

## 推奨アプローチ

現状のビジネス要件と開発状況を考慮すると、**メタフィールドでの管理を継続する**ことを推奨します。

### 理由
1. **現在の規模に適している**: 現在はPRとNYの2ロケーションのみであり、メタフィールドでも十分管理可能
2. **開発効率**: 既に実装済みの方法を継続することで、開発リソースを他の優先度の高いタスクに集中できる
3. **シンプルさ**: メタフィールドでの管理はシンプルで理解しやすく、運用も容易
4. **将来の拡張性**: 将来的にロケーション数が大幅に増加する場合は、その時点でShopifyロケーションへの移行を検討できる

### 将来的な検討事項
- ロケーション数が5以上に増加する場合
- 在庫移動の頻度が高くなる場合
- より詳細な在庫レポートが必要になる場合

これらの状況になった場合は、Shopifyロケーションへの移行を再検討することをお勧めします。
