# テスト結果検証レポート

## 検証概要

このレポートは、テスト結果の自動検証結果をまとめたものです。

検証実行日時: 2025/5/19 12:42:30

## 検証結果

- 合計検証数: 5
- 成功: 3
- 失敗: 2
- 成功率: 60%

## 詳細結果

### 検証1: テスト結果の成功/失敗数が期待と異なります。

- 結果: ❌ 失敗
- 詳細:
```json
{
  "expectedSuccess": 4,
  "actualSuccess": 3,
  "expectedFailure": 0,
  "actualFailure": 1
}
```

### 検証2: テスト "商品情報取得エラーのユニットテスト" の結果が期待と異なります。期待: 成功, 実際: 失敗

- 結果: ❌ 失敗
- 詳細:
```json
{
  "expected": true,
  "actual": false
}
```

### 検証3: テスト "商品情報取得エラーのE2Eテスト" は期待通りの結果です。

- 結果: ✅ 成功
### 検証4: テスト "統一カレンダーのテスト" は期待通りの結果です。

- 結果: ✅ 成功
### 検証5: テスト "チェックアウト統合テスト" は期待通りの結果です。

- 結果: ✅ 成功
