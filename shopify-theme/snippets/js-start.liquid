<script>
    theme.AssetsLoader.require('scripts', 'theme');
    {%- if template.name == 'product'-%}
        {%- if product.media.size > 1 -%}
            theme.AssetsLoader.require('styles', 'plugin_slick');
            theme.AssetsLoader.require('scripts', 'plugin_slick');
        {%- endif -%}
        theme.AssetsLoader.require('scripts', 'product_page');
    {%- endif -%}
    theme.AssetsLoader.onUserAction(function() {
        theme.AssetsLoader.require('scripts', 'theme_pt2');
    });
    {%- if settings.show_multiple_currencies -%}
        theme.AssetsLoader.require('scripts', 'currency_rates');
        theme.AssetsLoader.require('scripts', 'currency');
    {%- endif -%}
    {%- if template.name == 'collection' or template.name == 'search'-%}
        theme.AssetsLoader.onUserAction(function() {
            theme.AssetsLoader.require('scripts', 'collections');
        });
    {%- endif -%}
    {%- if settings.tooltips_enable -%}
        theme.AssetsLoader.onUserAction(function() {
            theme.AssetsLoader.require('scripts', 'tooltip');
        });
    {%- endif -%}
    {%- if template == 'cart' -%}
        theme.AssetsLoader.require('scripts', 'handlebars');
        theme.AssetsLoader.require('scripts', 'countries');
    {%- endif -%}
    {%- if template.directory == 'customers' or template == 'addresses' -%}
        {%- assign need_shopify_common_js = true -%}
    
    {%- endif -%}
    {%- if need_shopify_common_js -%}
        theme.AssetsLoader.require('scripts', 'shopify_common');
    {%- endif -%}
    {%- if settings.dev_enable_custom_js == true -%}
        theme.AssetsLoader.require('scripts', 'custom');
    {%- endif -%}
</script>