{"settings_schema": {"quick_start": {"name": "クイックスタート", "settings": {"header__1": {"content": "はじめに"}, "paragraph__1": {"content": "この度は『MISEル』テーマをお買い上げいただきありがとうございます。このテーマ『MISEル』には多くの機能やオプションがあり、お好みの設定が可能となっています。皆様のストア制作にお役立ちできるよう、ご案内させていただきます。"}, "header__2": {"content": "オプションの設定について"}, "paragraph__2": {"content": "この｢テーマ設定｣で様々な設定が行えます。"}, "paragraph__3": {"content": "セクションには3つのタイプがあります。"}, "paragraph__4": {"content": "a) 動的セクション - 各ページに自由に付け加えることができるタイプのセクションです。｢セクションを追加｣より好きなセクションを選び追加してください。"}, "paragraph__5": {"content": "b) 静的セクション - ストア全体に使われているセクションです。ヘッダーやフッターなど。"}, "paragraph__6": {"content": "c) ページごとの静的セクション - 特定のページごとにあらかじめ設定されているセクションです。例えば｢商品ページ｣や｢関連製品カルーセル｣などのセクションは｢商品｣のテンプレートページにあらかじめ設置されている固有のものです。"}, "paragraph__7": {"content": "テンプレートによって表示されるセクションの種類は異なってきます。編集したいテンプレートを見るにはカスタマイズ画面の上部にあるページ選択タブから編集したいテンプレートを選んでください。例:ホームページ・コレクション・商品など"}, "header__3": {"content": "テキストの変更"}, "paragraph__8": {"content": "言語を変更したい場合｢テーマ → ･･･（カスタマイズ左） → デフォルトテーマのコンテンツを編集する｣から行ってください。"}, "header__4": {"content": "リソース"}, "paragraph__9": {"content": "[Shopifyのヘルプページ](https://help.shopify.com/ja/manual/intro-to-shopify/initial-setup/setup-getting-started)"}, "paragraph__10": {"content": "[MISEル ユーザーマニュアル](https://misell-manual.wraptas.site/)"}, "paragraph__11": {"content": "[ MISEル ビデオ](https://www.youtube.com/channel/UChCFvSvr3YgbYDcumJ_OqzQ)"}, "paragraph__12": {"content": "[ MISEル 販売ページ](https://shop.misell-theme.com/)"}, "header__5": {"content": "サポート"}, "paragraph__13": {"content": "[ MISEル - サポート](https://shop.misell-theme.com/pages/theme-support)"}, "paragraph__14": {"content": ""}, "header__6": {"content": ""}, "paragraph__15": {"content": ""}}}, "license": {"name": "ライセンスコード", "settings": {"purchase_code": {"label": "ライセンスコードの入力", "info": "『MISEル』のライセンスコードは『MISEル』のテーマショップからご購入いただけます。[『MISEル』販売サイト](https://shop.misell-theme.com/)"}, "presentation_enable": {"label": "プレゼンテーションモードを有効化", "info": "このオプションはデモサイト用のものです。本番環境ではチェックを外してください。"}, "header__1": {"content": "お知らせ"}, "paragraph__1": {"content": "テーマを有効化するにはライセンスコードを入力してください。ライセンスコード 1つにつき、1ストア（Shopify）となります。同じストア内であれば、複数のテーマに使用できます。詳しい方法はユーザーマニュアルでご覧いただけます。 [テーマの有効化](https://misell-manual.wraptas.site/start-guide/theme-install)"}}}, "logo_and_favicon": {"name": "ロゴとファビコン", "settings": {"paragraph__1": {"content": "ロゴに使用したい画像を選択してください。または｢SVG｣タイプの画像を使用する場合は｢SVG｣を選択してください。"}, "logo_types": {"label": "ロゴのタイプ", "option__1": {"label": "SVG"}, "option__2": {"label": "画像"}}, "header__1": {"content": "メインのロゴ"}, "logo_svg_file": {"label": "ロゴのファイル名", "info": "コードを編集 > スニペット [svg-file-name].liquid"}, "logo_image": {"label": "ロゴ画像", "info": "推奨サイズ 64x27 px"}, "logo_width": {"label": "ロゴの幅"}, "header__2": {"content": "モバイルでのヘッダーロゴ"}, "logo_mobile_svg_file": {"label": "モバイルでのロゴのファイル名", "info": "コードを編集 > スニペット [svg-file-name].liquid"}, "logo_mobile_image": {"label": "モバイルでのロゴの画像", "info": "推奨サイズ 34x27 px"}, "logo_mobile_width": {"label": "モバイルのロゴの幅"}, "header__3": {"content": "スティッキーヘッダーのロゴ"}, "logo_sticky_svg_file": {"label": "スティッキーロゴのファイル名", "info": "コードを編集 > スニペット [svg-file-name].liquid"}, "logo_sticky_image": {"label": "スティッキーロゴ画像", "info": "推奨サイズ 34x27 px"}, "logo_sticky_width": {"label": "スティッキーロゴの幅"}, "header__4": {"content": "透過ヘッダーのロゴ"}, "logo_transparent_svg_file": {"label": "透過ヘッダーのロゴのファイル名", "info": "コードを編集 > スニペット [svg-file-name].liquid"}, "logo_transparent_image": {"label": "透過ヘッダーのロゴの画像", "info": "推奨サイズ 34x27 px"}, "logo_transparent_width": {"label": "透過ヘッダーロゴの幅"}, "header__5": {"content": "フッターロゴ"}, "logo_footer_svg_file": {"label": "フッターロゴのファイル名", "info": "コードを編集 > スニペット [svg-file-name].liquid"}, "logo_footer_image": {"label": "フッターロゴの画像", "info": "推奨サイズ 64x27 px"}, "logo_footer_width": {"label": "フッターロゴの幅"}, "header__6": {"content": "モバイルでのフッターロゴ"}, "logo_footer_mobile_svg_file": {"label": "モバイルでのフッターロゴのファイル名", "info": "コードを編集 > スニペット [svg-file-name].liquid"}, "logo_footer_mobile_image": {"label": "モバイルでのフッターロゴの画像", "info": "推奨サイズ 64x27 px"}, "logo_footer_mobile_width": {"label": "モバイルでのフッターロゴの幅"}, "header__7": {"content": "ファビコン"}, "favicon": {"label": "ファビコン画像", "info": "推奨サイズ 32x32 px"}, "header__8": {"content": "ユーザーマニュアル"}, "paragraph__2": {"content": "[ロゴとファビコン](https://misell-manual.wraptas.site/theme-settings/logo-favicon)"}}}, "layout": {"name": "レイアウト", "settings": {"layout_container": {"label": "デフォルトでのコンテンツ幅", "info": "ヘッダー・フッター・ホームページのセクションに反映されます。ただし各セクションのオプション項目により上書きすることができます。", "option__1": {"label": "フル幅"}, "option__2": {"label": "ボックス幅"}}, "layout_show_breadcrumbs": {"label": "パンくずリストを表示"}, "layout_pagination_type": {"label": "ページネーションのタイプ", "info": "この設定はコレクションのページに適用されます。｢無限スクロール｣ではスクロールするごとに商品を読み込みます。｢さらに読み込む｣では読み込みのボタンがページ下部に表示され、ボタンを押すと商品をよみこむようになります。｢クラシック｣ではページの番号のリストが表示されます。", "option__1": {"label": "クラシック"}, "option__2": {"label": "クラシック(中央)"}, "option__3": {"label": "さらに読み込むボタン"}, "option__4": {"label": "無限スクロール"}}, "layout_enable_rtl": {"label": "RTL (右から左書き言語) を有効化", "info": "RTL言語モードで表示します。この設定を有効にするには、Shopify管理画面 > 設定 > 言語 などの設定が必要になります。"}, "layout_images_lazyload": {"label": "レイジーロードを有効化", "info": "ページの読み込み速度を最適化するために、このオプションの有効化を推奨します。"}, "layout_settings_file": {"label": "スキン", "option__1": {"label": "デフォルト"}, "option__2": {"label": "ホーム v16"}, "option__3": {"label": "ホーム v18"}, "option__4": {"label": "スキン #1"}, "option__5": {"label": "スキン #2"}, "option__6": {"label": "スキン #3"}, "option__7": {"label": "スキン #4"}, "option__8": {"label": "スキン #5"}, "option__9": {"label": "スキン #6"}, "option__10": {"label": "スキン #7"}, "option__11": {"label": "スキン #8"}, "option__12": {"label": "スキン #9"}, "option__13": {"label": "スキン #10"}, "option__14": {"label": "スキン #11"}, "option__15": {"label": "スキン #12"}, "option__16": {"label": "スキン #13"}, "option__17": {"label": "スキン #14"}, "option__18": {"label": "スキン #15"}, "option__19": {"label": "スキン #16"}, "option__20": {"label": "スキン #17"}, "option__21": {"label": "スキン #18"}, "option__22": {"label": "カスタム"}}, "header__1": {"content": "年齢確認"}, "layout_age_confirmation": {"label": "年齢確認のポップアップを有効化"}, "layout_age_confirmation_image": {"label": "背景画像"}, "layout_age_confirmation_image_width": {"label": "背景画像の幅"}, "layout_age_confirmation_checkbox": {"label": "年齢確認チェックを有効化"}, "header__2": {"content": "ユーザーマニュアル"}, "paragraph__1": {"content": "[レイアウト](https://misell-manual.wraptas.site/theme-settings/layout)"}}}, "colors": {"name": "カラー", "settings": {"header__1": {"content": "グローバル"}, "color_theme_primary": {"label": "プライマリー"}, "color_theme_body": {"label": "ボディ背景"}, "color_theme_c": {"label": "テキスト"}, "color_theme_head_c": {"label": "見出し"}, "color_theme_link_c": {"label": "リンク"}, "color_theme_popups_bg": {"label": "ポップアップ背景"}, "header__2": {"content": "ボタン"}, "color_button_default_bg": {"label": "デフォルト背景"}, "color_button_default_bd": {"label": "デフォルトボーダー(枠)"}, "color_button_default_c": {"label": "デフォルトテキスト"}, "color_button_default_act_bg": {"label": "デフォルト アクティブ背景"}, "color_button_default_act_bd": {"label": "デフォルト アクティブボーダー"}, "color_button_default_act_c": {"label": "デフォルト アクティブテキスト"}, "color_button_invert_bg": {"label": "反転 背景"}, "color_button_invert_bd": {"label": "反転 ボーダー"}, "color_button_invert_c": {"label": "反転 テキスト"}, "color_button_invert_act_bg": {"label": "反転 アクティブ背景"}, "color_button_invert_act_bd": {"label": "反転 アクティブボーダー"}, "color_button_invert_act_c": {"label": "反転 アクティブテキスト"}, "color_button_secondary_bg": {"label": "セカンダリ背景"}, "color_button_secondary_bd": {"label": "セカンダリボーダー"}, "color_button_secondary_c": {"label": "セカンダリテキスト"}, "color_button_secondary_act_bg": {"label": "セカンダリアクティブ背景"}, "color_button_secondary_act_bd": {"label": "セカンダリアクティブボーダー"}, "color_button_secondary_act_c": {"label": "セカンダリアクティブテキスト"}, "color_button_clean_bg": {"label": "クリーン 背景"}, "color_button_clean_bd": {"label": "クリーン ボーダー"}, "color_button_clean_c": {"label": "クリーン テキスト"}, "color_button_clean_act_bg": {"label": "クリーン アクティブ背景"}, "color_button_clean_act_bd": {"label": "クリーン アクティブボーダー"}, "color_button_clean_act_c": {"label": "クリーン アクティブテキスト"}, "color_collection_item_addtocart_btn_type": {"label": "コレクションアイテムのカートボタンのタイプ", "option__1": {"label": "オート"}, "option__2": {"label": "デフォルト"}, "option__3": {"label": "反転"}, "option__4": {"label": "セカンダリ"}, "option__5": {"label": "クリーン"}}, "color_product_page_addtocart_btn_type": {"label": "商品ページのカートボタンのタイプ", "option__1": {"label": "オート"}, "option__2": {"label": "デフォルト"}, "option__3": {"label": "反転"}, "option__4": {"label": "セカンダリ"}, "option__5": {"label": "クリーン"}}, "color_product_page_wishlist_btn_type": {"label": "商品ページのお気に入りリストボタンのタイプ", "option__1": {"label": "オート"}, "option__2": {"label": "デフォルト"}, "option__3": {"label": "反転"}, "option__4": {"label": "セカンダリ"}, "option__5": {"label": "クリーン"}}, "color_product_page_compare_btn_type": {"label": "商品ページの比較リストボタンのタイプ", "option__1": {"label": "オート"}, "option__2": {"label": "デフォルト"}, "option__3": {"label": "反転"}, "option__4": {"label": "セカンダリ"}, "option__5": {"label": "クリーン"}}, "header__3": {"content": "カラーパレット"}, "paragraph__1": {"content": "すべてのカラーはこのパレットの色を継承します。"}, "color_theme": {"label": "1) ベース"}, "color_theme2": {"label": "2) ベース背景"}, "color_theme3": {"label": "3) テキスト"}, "color_theme4": {"label": "4) 追加背景"}, "color_theme5": {"label": "5) ボーダー"}, "color_theme6": {"label": "6) アイコン"}, "color_theme7": {"label": "7) サクセス, 在庫あり"}, "color_theme8": {"label": "8) エラー, セール"}, "color_theme9": {"label": "9) ラベル '新着'"}, "color_theme10": {"label": "10) ラベル 'おすすめ'"}, "color_theme11": {"label": "11) 動的チェックアウトボタン"}, "color_theme12": {"label": "12) ラベル 'プリオーダー(予約)'"}, "color_theme13": {"label": "13) インプット背景"}, "header__4": {"content": "ユーザーマニュアル"}, "paragraph__2": {"content": "[カラー](https://misell-manual.wraptas.site/theme-settings/colors)"}}}, "typography": {"name": "フォントスタイル", "settings": {"header__1": {"content": "ボディ(本文)"}, "font_base": {"label": "Shopify管理フォント(本文)"}, "font_base_custom": {"label": "カスタムフォントを使用", "info": "Unclick if you want to choose a different font for headings, buttons, inputs or menu"}, "font_base_type": {"label": "スタイル(本文)", "info": "Shopify管理フォント(本文)", "option__1": {"label": "なし"}, "option__2": {"label": "セミボールド"}, "option__3": {"label": "ボールド"}, "option__4": {"label": "ミディアム"}, "option__5": {"label": "イタリック体"}, "option__6": {"label": "セミボールド・イタリック体"}, "option__7": {"label": "ボールド・イタリック体"}, "option__8": {"label": "ボールドイタリック体"}, "option__9": {"label": "すべて"}}, "font_size_body": {"label": "ベースフォントサイズ(本文)"}, "header__2": {"content": "見出し"}, "font_heading": {"label": "Shopify管理フォント(見出し)"}, "font_heading_type": {"label": "フォントスタイル(見出し)", "info": "見出しのフォントファミリーに適用", "option__1": {"label": "なし"}, "option__2": {"label": "セミボールド"}, "option__3": {"label": "ボールド"}, "option__4": {"label": "イタリック体"}, "option__5": {"label": "セミボールド・イタリック体"}, "option__6": {"label": "ボールド・イタリック体"}, "option__7": {"label": "ボールドイタリック体"}, "option__8": {"label": "すべて"}}, "font_size_h1": {"label": "プロモボックスの大きいテキストサイズ・h1(見出し)"}, "font_size_h2": {"label": "プロモボックスのテキストサイズ・h2(見出し)"}, "font_size_h3": {"label": "ページタイトルサイズ・h3(見出し)"}, "font_size_h4": {"label": "セクションタイトルサイズ・h4(見出し)"}, "font_size_h5": {"label": "メニュータイトルサイズ・h5(見出し)"}, "font_size_h6": {"label": "ハイライトテキストサイズ・h6(見出し)"}, "header__3": {"content": "ボタン・入力フォーム（インプット）"}, "font_button": {"label": "ボタンフォントファミリー"}, "font_size_button": {"label": "ボタンフォントサイズ"}, "font_size_input": {"label": "入力フォームフォントサイズ"}, "header__4": {"content": "メニュー"}, "font_menu": {"label": "フォントファミリー"}, "font_size_menu": {"label": "1階層目のフォントサイズ"}, "font_size_menu_list": {"label": "リストフォントサイズ", "info": "ドロップダウン・メガメニュー"}, "font_size_menu_title": {"label": "タイトルフォントサイズ", "info": "メガメニュー"}, "font_size_menu_mobile": {"label": "モバイルメニューのフォントサイズ"}, "header__5": {"content": "サブフォントファミリー"}, "paragraph__1": {"content": "'ff-alternative'のクラスを追加で、サブフォントファミリーを適用(高度な設定)"}, "font_load_alternative": {"label": "サブフォントファミリーを有効化"}, "font_alternative": {"label": "サブフォントファミリー"}, "font_alternative_type": {"label": "追加スタイルの適用", "info": "サブフォントファミリーに対して適用されます。", "option__1": {"label": "なし"}, "option__2": {"label": "ボールド"}, "option__3": {"label": "イタリック体"}, "option__4": {"label": "ボールド・イタリック体"}, "option__5": {"label": "ボールドイタリック体"}, "option__6": {"label": "すべて"}}, "header__6": {"content": "ユーザーマニュアル"}, "paragraph__2": {"content": "[フォントスタイル](https://misell-manual.wraptas.site/theme-settings/font-styles)"}}}, "collection_and_search_page": {"name": "コレクションと検索ページ", "settings": {"header__1": {"content": "サイドバー"}, "collection_show_sidebar": {"label": "サイドバーの表示", "option__1": {"label": "デスクトップとモバイル"}, "option__2": {"label": "デスクトップのみ"}, "option__3": {"label": "非表示"}}, "collection_sidebar_position": {"label": "デスクトップでのサイドバーの位置", "info": "サイドバーのポジションの設定・フィルター表示ボタンの設定", "option__1": {"label": "左"}, "option__2": {"label": "右"}, "option__3": {"label": "トップとプルダウンフィルター"}, "option__4": {"label": "フィルターボタン、ポップアップ表示"}}, "collection_enable_sticky": {"label": "スティッキーサイドバーを有効化", "info": "スクロールでサイドバーを常に表示"}, "header__2": {"content": "その他"}, "collection_enable_ajax": {"label": "ページの読み込みを待つことなくフィルター結果を表示(AJAX)"}, "header__3": {"content": "ユーザーマニュアル"}, "paragraph__1": {"content": "[コレクションページ](https://misell-manual.wraptas.site/theme-settings/collection-page)"}}}, "product_general": {"name": "商品 -> 一般設定", "settings": {"paragraph__1": {"content": "ストア全体の商品の設定"}, "product_format_pjpg": {"label": "PJPG形式の画像フォーマットを有効化", "info": "有効にするとページの読み込みパフォーマンスが上がります。ただし、透過画像には適用されません。"}, "product_cursor": {"label": "カーソル画像", "info": "商品をホバーしたときのマウスカーソルの画像を変更できます。"}, "header__1": {"content": "ユーザーマニュアル"}, "paragraph__2": {"content": "[商品 -> 一般設定](https://misell-manual.wraptas.site/theme-settings/product-general)"}}}, "product_variants": {"name": "商品 -> バリエーション", "settings": {"paragraph__1": {"content": "商品バリエーション選択のデザインの設定。バリエーションの選択アイコンを｢テキスト｣・｢大きいテキスト｣・｢サークルカラー｣・｢サークル画像｣などに置き換えることができます。"}, "header__1": {"content": "一般設定"}, "product_show_custom_options": {"label": "バリエーション表示のアドバンスデザインを有効化"}, "product_enable_variants_section": {"label": "商品のバリエーションを有効化※セクション選択時に表示。詳細なオプション設定が可能"}, "product_hide_options_without_availability_variants": {"label": "購入不可の商品バリエーションを非表示", "info": "在庫がないものは非表示、または購入不可オプションとして表示します。"}, "product_variant_auto_select": {"label": "バリエーションの自動選択", "option__1": {"label": "有効化"}, "option__2": {"label": "1つ目のバリエーションのみ選択"}, "option__3": {"label": "1つ目と2つ目のバリエーションの選択"}, "option__4": {"label": "無効化"}}, "product_auto_selected_options": {"label": "オプションの自動選択", "info": "バリエーションの自動選択を【無効化】にした時に、有効な設定です。オプションの区切りには'|'(パイプライン)を使用してください。例:カラー|サイズ"}, "product_collection_set_first_image_by_variant": {"label": "1つ目のバリエーション画像をリストに表示"}, "product_info_set_first_image_by_variant": {"label": "1つ目のバリエーション画像を商品ページに表示"}, "product_options_listing_visibility": {"label": "バリエーション表示数", "info": "オプション数が、オプション表示数を超える場合、｢カートにいれる｣ボタンが「オプションを選択する」ボタンに変わります。", "option__1": {"label": "すべてのオプションを表示"}, "option__2": {"label": "1つ目のバリエーションを表示"}, "option__3": {"label": "1つ目と2つ目のバリエーションを表示"}}, "product_variant_change_on_hover": {"label": "ホバーで画像切替を有効化", "info": "コレクションページで商品をマウスホバーした時、他のバリエーション画像を表示します。"}, "header__2": {"content": "オプションタイプ"}, "paragraph__2": {"content": "デフォルトでのバリエーションのタイプは｢テキスト｣です。"}, "product_options_type_circle_color": {"label": "「サークルカラー」のオプションタイプ", "info": "商品のバリエーションを色付きのサークルで表示。ただし、商品オプション登録でそれぞれのバリエーション(オプションの値)の色名はCSSの標準カラー名(英語)にする必要があります。区切りには'|'(パイプライン)を使用します。[CSSカラー名](https://onyxtan-nl.myshopify.com/pages/css-color-name)"}, "product_options_type_circle_image": {"label": "「サークル画像」のオプションタイプ", "info": "商品のオプションを小さなサークル画像で表示。商品バリエーション登録でそれぞれのバリエーションに個別の画像登録が必要です。区切りには'|'(パイプライン)を使用します。"}, "product_options_type_square_color": {"label": "「スクエアカラー」のオプションタイプ", "info": "商品のオプションをスクエアカラー(色付きの四角いアイコン)で表示。区切りには'|'(パイプライン)を使用します。"}, "product_options_type_square_image": {"label": "｢スクエア画像｣のオプションタイプ", "info": "商品のオプションを小さな四角い画像で表示。区切りには'|'(パイプライン)を使用します。"}, "product_options_type_select": {"label": "選択オプションで表示", "info": "オプションをドロップダウンメニューで表示。区切りには'|'(パイプライン)を使用します。"}, "product_options_type_large_text": {"label": "｢大きいテキスト｣のオプションタイプ", "info": "オプションを大きめのテキストで表示。区切りには'|'(パイプライン)を使用します。"}, "header__3": {"content": "ビデオチュートリアル・ユーザーマニュアル"}, "paragraph__3": {"content": "[商品オプションの追加方法（動画）](https://youtu.be/4-So_i2XwZA)"}, "paragraph__4": {"content": "[商品 -> バリエーション](https://misell-manual.wraptas.site/theme-settings/product-variations)"}}}, "product_collection_page": {"name": "商品 -> コレクションページ", "settings": {"header__1": {"content": "画像"}, "product_collection_image_height_percent": {"label": "画像の比率", "info": "100の場合スクエア(正方形)。100より少ない場合横長。100より大きい場合ポートレートサイズ(縦長)。画像のリサイズをオートにした場合は反映されません。"}, "product_collection_image_size": {"label": "画像のリサイズ", "option__1": {"label": "オート"}, "option__2": {"label": "コンテイン"}, "option__3": {"label": "カバー"}, "option__4": {"label": "幅に合わせる"}, "option__5": {"label": "高さに合わせる"}}, "paragraph__1": {"content": "オート ー ｢オート｣を選んだ場合、上記の｢画像の比率｣での設定は反映されません。もとのアップロードした画像のサイズに合わせた比率で表示されます。"}, "paragraph__2": {"content": "コンテイン ー ｢コンテイン｣を選んだ場合、CSSの｢object-fit = contain｣と同じになります。画像が自動的に切り取られ指定の比率に収まるようになります。[object-fit css のデモ](https://developer.mozilla.org/ja/docs/Web/CSS/object-fit)."}, "paragraph__3": {"content": "カバー ー ｢カバー｣を選んだ場合、CSSの｢object-fit = cover｣と同じになります。画像全体が指定の比率に収まるようになります。[object-fit cssのデモ](https://developer.mozilla.org/ja/docs/Web/CSS/object-fit)."}, "paragraph__4": {"content": "幅に合わせる - 「幅に合わせる」を選んだ場合、画像の幅が画像ボックスに合わされます。もし、画像の高さが画像ボックスより大きい場合自動的に上下が切り取られます。画像の高さが画像ボックスより小さい場合、空白が上下に自動的に挿入されます。"}, "paragraph__5": {"content": "高さに合わせる - ｢高さに合わせる｣を選んだ場合、画像の高さが画像ボックス合わされます。画像の中心を基準として配置されます。"}, "product_collection_image_border_radius": {"label": "画像枠の丸み調整", "info": "画像の角に丸みをつけます。"}, "product_replace_images_hover": {"label": "ホバー時に2つ目の画像を表示", "info": "マウスホバー時に二番目にセットした商品画像を表示します。"}, "header__2": {"content": "ラベル"}, "product_collection_show_label_in_stock": {"label": "在庫ありのラベル表示"}, "product_collection_show_label_pre_order": {"label": "プリオーダー(予約)のラベル表示"}, "product_collection_show_label_out_stock": {"label": "在庫切れのラベル表示"}, "product_collection_show_label_sale": {"label": "｢セール｣のラベル表示"}, "product_collection_show_label_new": {"label": "｢新商品｣のラベル表示"}, "product_collection_show_label_hot": {"label": "｢おすすめ｣のラベル表示"}, "header__3": {"content": "要素"}, "product_collection_centered_info": {"label": "商品説明を中心に", "info": "画像の下の商品説明の位置を中心にします。"}, "product_collection_more_info_type": {"label": "タイトル前の商品タイプ・販売元・コレクションの表示", "option__1": {"label": "商品タイプ"}, "option__2": {"label": "販売元"}, "option__3": {"label": "コレクション"}, "option__4": {"label": "非表示"}}, "product_collection_show_title": {"label": "商品タイトルを表示"}, "product_collection_title_trim": {"label": "タイトルを一行に収める"}, "product_collection_show_price": {"label": "商品価格の表示"}, "product_collection_show_button_quick_view": {"label": "クイックビューボタン表示"}, "product_collection_show_countdown": {"label": "カウントダウンの表示"}, "product_collection_show_images_navigation": {"label": "画像ナビゲーションの表示"}, "product_collection_show_options": {"label": "バリエーションオプションの表示"}, "product_collection_show_quantity": {"label": "数量の表示"}, "product_collection_show_button_add_to_cart": {"label": "カート追加ボタンの表示"}, "product_collection_show_button_add_to_wishlist": {"label": "お気に入り追加ボタンの表示"}, "product_collection_show_button_add_to_compare": {"label": "比較リスト追加ボタンの表示"}, "header__4": {"content": "リストモードでの要素"}, "product_collection_show_sku": {"label": "SKU表示"}, "product_collection_show_barcode": {"label": "バーコード表示"}, "product_collection_show_availability": {"label": "在庫状況表示"}, "product_collection_show_type": {"label": "商品タイプ表示"}, "product_collection_show_vendor": {"label": "販売元表示"}, "product_collection_show_description": {"label": "商品説明表示"}, "product_collection_list_show_quantity": {"label": "数量の表示", "info": "リストデザイン#2でのみ有効"}, "product_collection_show_metafield_sub_description": {"title": "商品メタフィールドの利用項目", "label": "メタフィールドサブ説明を表示", "info": "こちらは商品メタフィールドをつかった高度なストア設定です。詳しくはマニュアルをご覧ください。"}, "header__5": {"content": "ユーザーマニュアル"}, "paragraph__6": {"content": "[商品 -> コレクションページ](https://misell-manual.wraptas.site/theme-settings/product-collection-page)"}}}, "product_page_and_quick_view": {"name": "商品ページとクイックビュー", "settings": {"product_info_layout": {"label": "ギャラリーレイアウト", "info": "｢シートギャラリー｣と｢グリッドギャラリー｣のレイアウトではスティッキーブロック(固定ブロック)となります。また、各商品に 'gallery-layout-2' のように上記５種類のギャラリーレイアウトの番号のタグを加えることにより、個別にレイアウトを選ぶこともできます。", "option__1": {"label": "#1 ギャラリーとサイドサムネイル"}, "option__2": {"label": "#2 ギャラリーと下にサムネイル"}, "option__3": {"label": "#3 シートギャラリー"}, "option__4": {"label": "#4 グリッドギャラリー"}, "option__5": {"label": "#5 中央ギャラリーと情報表示"}}, "header__1": {"content": "ギャラリー"}, "product_info_show_mobile_thumbnails": {"label": "モバイルでサムネイル表示"}, "product_info_enable_fullscreen_popup": {"label": "フルスクリーンギャラリーポップアップの有効化", "info": "ギャラリーの上でクリックすると開きます。"}, "product_info_show_btn_fullscreen": {"label": "フルスクリーンギャラリーボタンの表示"}, "product_info_show_btn_video": {"label": "ビデオボタンの表示"}, "product_info_video_autoplay": {"label": "ビデオの自動再生を有効化 *自動再生はブラウザのセキュリティにより制限される場合があります。"}, "product_info_enable_zoom": {"label": "マウスでのズームを有効化"}, "product_info_zoom_scale_coef": {"label": "ズームのスケール調整"}, "product_info_gallery_height_and_size_path": {"label": "画像の高さとサイズの調整", "info": "このオプションはコレクションページ・商品ページでのカスタマイズを簡単により柔軟なものにします。デフォルトでは「テーマ設定：商品 -> コレクションページ：画像」の設定値を引き継ぎます。", "option__1": {"label": "コレクションページ設定を商品設定に引き継ぐ"}, "option__2": {"label": "このセクション独自の設定にする"}}, "product_info_gallery_height_percent": {"label": "ギャラリー画像の高さ設定", "info": "100の場合スクエア(正方形)。100より少ない場合ランドスケープ(横長)。100より大きい場合ポートレートサイズ(縦長)。画像のリサイズをオートにした場合は反映されません。"}, "product_info_gallery_image_size": {"label": "画像のリサイズ", "option__1": {"label": "オート"}, "option__2": {"label": "コンテイン"}, "option__3": {"label": "カバー"}, "option__4": {"label": "幅に合わせる"}, "option__5": {"label": "高さに合わせる"}}, "paragraph__1": {"content": "この設定の詳細は「テーマ設定：商品 -> コレクションページ：画像」を参照ください。"}, "product_info_gallery_image_size_mobile": {"label": "モバイルでの画像リサイズ", "option__1": {"label": "オート"}, "option__2": {"label": "デスクトップでの設定を使う"}}, "product_info_gallery_mobile_zoom_type": {"label": "モバイルでのズーム方式", "option__1": {"label": "MISEルテーマ方式"}, "option__2": {"label": "ブラウザに依存"}}, "product_info_gallery_grouped": {"label": "オプションごとのグループギャラリー", "option__1": {"label": "無効化"}, "option__2": {"label": "全商品を有効化"}, "option__3": {"label": "group-galleryタグ付商品のみ有効化"}}, "product_info_enable_sticky_gallery": {"label": "スティッキーギャラリーを有効化"}, "header__2": {"content": "ラベル"}, "product_info_show_label_in_stock": {"label": "在庫ありのラベル表示"}, "product_info_show_label_pre_order": {"label": "予約プリオーダーのラベル表示"}, "product_info_show_label_out_stock": {"label": "在庫なしのラベル表示"}, "product_info_show_label_sale": {"label": "｢セール｣のラベル表示"}, "product_info_show_label_new": {"label": "｢新商品｣のラベル表示"}, "product_info_show_label_hot": {"label": "｢おすすめ｣のラベル表示"}, "header__3": {"content": "商品の詳細"}, "product_info_show_sku": {"label": "SKUの表示"}, "product_info_show_barcode": {"label": "バーコードの表示"}, "product_info_show_availability": {"label": "在庫状況の表示"}, "product_info_show_type": {"label": "商品タイプの表示"}, "product_info_show_vendor": {"label": "販売元の表示"}, "header__4": {"content": "価格"}, "product_info_show_sale_details": {"label": "セール価格の詳細表示"}, "product_info_show_tax_included": {"label": "価格の横に(税込)のテキストを表示", "info": "価格の横に(税込)のテキストを表示します。あくまでテーマ側で税込のテキストを表示させる機能になります。実際の税込金額の反映にはShopifyストア側の設定を行ってください。"}, "product_info_show_taxes": {"label": "送料・税に関するテキストの表示"}, "product_info_payment_terms": {"label": "支払いについての表示"}, "header__5": {"content": "セールスプラン"}, "paragraph__2": {"content": "ボタンと数量のブロックに配置されます。"}, "product_info_show_selling_plan": {"label": "セールプランの選択表示"}, "header__6": {"content": "在庫カウントダウン"}, "product_info_show_stock_countdown_range": {"label": "在庫カウントダウン範囲の表示"}, "product_stock_countdown_min": {"label": "在庫カウントダウンの最小値設定", "info": "｢在庫残りxx個!｣表示のしきい値設定"}, "header__7": {"content": "配送カウントダウンの表示"}, "paragraph__3": {"content": "｢XX時間内の注文でYY日までにお届け｣ブロックの設定"}, "product_delivery_countdown_reset_time": {"label": "配送カウントダウンをリセットする時間の設定"}, "product_delivery_countdown_delivery_time": {"label": "平均配送時間のカウントダウン(日)"}, "product_delivery_countdown_delivery_format": {"label": "配送日時のフォーマット", "info": "例:YYYY/MM/DD Day:2024/11/31 月曜"}, "product_delivery_countdown_delivery_excludes": {"label": "カウントしない曜日", "info": "週末を除く場合の例:Saturday, Sunday"}, "header__8": {"content": "ポップアップ"}, "paragraph__4": {"content": "詳細ボタンブロックに配置"}, "product_info_show_size_guide": {"label": "サイズガイドの表示"}, "product_info_size_guide_page_content": {"label": "サイズガイドのコンテンツ", "info": "デフォルトではページの'Include Popup Size Guide'が適用されます。(ExImアプリでMisell-theme-page.zipがインストールされている場合)"}, "product_info_show_delivery_return": {"label": "｢配送と返品｣の表示"}, "product_info_delivery_return_page_content": {"label": "｢配送と返品｣のコンテンツ", "info": "デフォルトではページの'Include Popup Delivery Return'が適用されます。(ExImアプリでMisell-theme-page.zipがインストールされている場合)"}, "product_info_show_message": {"label": "｢メッセージ｣ボタンの表示"}, "header__9": {"content": "ボタンとインプット"}, "product_info_show_quantity": {"label": "数量の表示"}, "product_info_button_layout": {"label": "ボタンのレイアウト", "option__1": {"label": "1"}, "option__2": {"label": "2"}, "option__3": {"label": "3"}, "option__4": {"label": "4"}}, "product_info_show_button_add_to_cart": {"label": "カートに入れるボタンの表示"}, "product_info_button_add_to_cart_size": {"label": "カートに入れるボタンのサイズ", "option__1": {"label": "デフォルト"}, "option__2": {"label": "ラージ"}}, "product_info_show_button_dynamic_checkout": {"label": "動的チェックアウトボタンの表示"}, "product_info_show_dynamic_checkout_confirmation": {"label": "利用規約に同意等のチェックボックス(動的チェックアウトボタン)を表示"}, "product_info_styled_dynamic_checkout": {"label": "動的チェックアウトボタンのカスタムスタイル"}, "product_info_show_gift_card_recipient": {"label": "ギフトカード商品の受取人情報フォームを表示", "info": "ギフトカード商品は、メッセージとともに受取人に直接送ることができます。 [詳しくはこちら](https://help.shopify.com/ja/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}, "product_info_show_button_add_to_wishlist": {"label": "お気に入りボタンの表示"}, "product_info_show_button_add_to_compare": {"label": "比較リストボタンの表示"}, "header__10": {"content": "店舗受取サービス"}, "product_info_show_unavailable_pickup_available": {"label": "店舗受取可の表示"}, "header__11": {"content": "付属商品"}, "product_info_complementary_products_limit": {"label": "最大表示数"}, "product_info_complementary_products_add_to_cart": {"label": "クイックビューの代わりにカートに追加ボタンを表示"}, "header__12": {"content": "SNSシェアボタン"}, "product_info_social_share_buttons_type": {"label": "SNSシェアボタンのタイプ", "option__1": {"label": "スモール"}, "option__2": {"label": "ラージ"}}, "header__13": {"content": "フットバー"}, "product_footbar_enable": {"label": "フットバーを有効化"}, "product_footbar_show_title": {"label": "商品タイトルの表示"}, "product_footbar_show_price": {"label": "商品価格の表示"}, "product_footbar_show_options": {"label": "オプションの表示(バリエーション)"}, "product_footbar_options_type": {"label": "オプションのタイプ(デスクトップのみ)", "info": "モバイルでは｢セレクト(プルダウン)｣表示のみになります。", "option__1": {"label": "デフォルト"}, "option__2": {"label": "セレクト(プルダウン)"}}, "product_footbar_show_quantity": {"label": "数量の表示"}, "product_footbar_show_button_add_to_cart": {"label": "カートに入れるボタンを表示"}, "header__14": {"content": "クイックビューのみ"}, "product_quick_view_layout": {"label": "レイアウト", "info": "この設定はデフォルトでは｢プロダクトページとクイックビュー｣の最初の設定を引き継ぎます。またクイックビューのポップアップとは違うものを選択することも可能です。｢シートギャラリー｣と｢グリッドギャラリー｣のタイプでは自動的にスティッキーブロック(固定表示)が有効になります。", "option__1": {"label": "レイアウトのグローバル設定を継承"}, "option__2": {"label": "#1 ギャラリーとサイドサムネイル"}, "option__3": {"label": "#2 ギャラリーと下のサムネイル"}, "option__4": {"label": "#3 シートギャラリー"}, "option__5": {"label": "#4 グリッドギャラリー"}, "option__6": {"label": "#5 中央ギャラリーと情報"}}, "product_quick_view_show_full_details": {"label": "'詳細を見る'リンクを表示"}, "header__15": {"content": "ユーザーマニュアル"}, "paragraph__5": {"content": "[商品ページとクイックビュー](https://misell-manual.wraptas.site/theme-settings/product-quick-view)"}}}, "search": {"name": "検索(サーチ)", "settings": {"search_show": {"label": "検索をヘッダーとナビゲーションのポップアップに表示"}, "search_predictive_enabled": {"label": "商品サジェスト(予想検索)を有効化", "info": "Shopifyは日本語の予想検索には対応していません。予想検索ではなく、シンプルな検索機能にするためにチェックボックスを外してください。[予想検索がサポートされている言語](https://shopify.dev/docs/themes/ajax-api/reference/predictive-search)"}, "header__1": {"content": "ユーザーマニュアル"}, "paragraph__1": {"content": "[検索(サーチ)](https://misell-manual.wraptas.site/theme-settings/search)"}}}, "currency": {"name": "通貨", "settings": {"header__1": {"content": "一般設定"}, "paragraph__1": {"content": "デフォルトではテーマの表示通貨はShopifyペイメントで設定したものになっています。 [Shopify ペイメント](https://help.shopify.com/ja/manual/payments/shopify-payments/multi-currency)。Shopifyでサポートされていない通貨形式を表示したい場合は、下記の通貨表示変換オプションを有効にしてください。"}, "price_show_sale_separator": {"label": "セールの価格を表示した場合、もとの価格から割り引いたことを表示"}, "header__2": {"content": "通貨情報"}, "paragraph__2": {"content": "複数の通貨表示を行うには、予めストアでの設定を行う必要があります。"}, "show_multiple_currencies": {"label": "従来の通貨変換方式を有効化", "info": "価格が異なる通貨で表示されている場合でも、注文の際はストアの通貨設定が適用されます。"}, "currency_format": {"label": "フォーマット", "option__1": {"label": "通貨名表示なし (例:$10.00)"}, "option__2": {"label": "通貨名表示 (例:$10.00 CAD)"}}, "supported_currencies": {"label": "サポート通貨", "info": "[ISO通貨コード](http://www.xe.com/iso4217.php)を使用してください。。区切りにはスペースを使用してください。 例:USD ERU"}, "supported_currencies_short_names": {"label": "通貨の省略ネーム", "info": "区切りには'|'(パイプライン)を使用します。"}, "supported_currencies_full_names": {"label": "通貨のフルネーム", "info": "区切りには'|'(パイプライン)を使用します。"}, "default_currency": {"label": "デフォルト"}, "header__3": {"content": "ユーザーマニュアル"}, "paragraph__3": {"content": "[通貨](https://misell-manual.wraptas.site/theme-settings/currency)"}}}, "cart": {"name": "カート", "settings": {"header__1": {"content": "一般設定"}, "cart_show_header_button": {"label": "カートのヘッダーボタンを表示"}, "cart_ajax": {"label": "カートに追加してもそのままページに残る(AJAX)", "info": "チェックを外した場合、カートページに移動します。"}, "cart_icon": {"label": "カートボタンのアイコン", "info": "デフォルト:icon-theme-109"}, "cart_popup_show_checkout_confirmation_checkbox": {"label": "利用規約の同意確認を表示"}, "header__2": {"content": "ポップアップ"}, "cart_popup_enable": {"label": "ポップアップを有効化"}, "cart_popup_type": {"label": "ポップアップの位置", "option__1": {"label": "画面の端"}, "option__2": {"label": "画面中央"}}, "cart_popup_show_quantity_input": {"label": "数量インプットを有効化"}, "cart_popup_show_taxes_and_shipping_information": {"label": "配送と税金の情報を表示"}, "cart_popup_show_link_to_cart_page": {"label": "カートを見るリンクを表示"}, "header__3": {"content": "カートページ"}, "cart_show_vendor": {"label": "商品の販売元を表示"}, "cart_show_notes": {"label": "サイドバーに備考欄を表示"}, "header__4": {"content": "送料無料のプログレスバー"}, "cart_show_free_shipping": {"label": "プログレスバーを有効化"}, "cart_free_shipping_value": {"label": "送料無料の値", "info": "USD アメリカドルを使用している場合セント単位での数字を入力します。日本円入力の場合も円に 100 をかけた数字を入力してください。例えば 3000 円以上で送料無料であれば'300000'となります。複数の通貨を表示している場合、'|'(パイプライン)で区切って表示してください。ご注意:｢この送料無料のプログレスバー｣はあくまでテーマ上の表示で、実際にカートで決済される送料の設定はShopifyの管理画面の[｢配送と配達｣](/admin/settings/shipping)にて行う必要があります。"}, "header__5": {"content": "ユーザーマニュアル"}, "paragraph__1": {"content": "[カート](https://misell-manual.wraptas.site/theme-settings/cart)"}}}, "wishlist_and_compare": {"name": "お気に入りと比較リスト", "settings": {"header__1": {"content": "お気に入りリスト"}, "wishlist_type": {"label": "お気に入りリストのタイプ", "option__1": {"label": "無効化"}, "option__2": {"label": "デフォルト (Basic Wishlist)"}}, "header__2": {"content": "比較リスト"}, "compare_type": {"label": "比較リストのタイプ", "option__1": {"label": "無効化"}, "option__2": {"label": "デフォルト (Basic Wishlist)"}}, "header__3": {"content": "リストの価格表示の設定"}, "header__4": {"content": "お気に入りと比較リストの表示項目設定"}, "storelists_price_multiplier": {"label": "ストア価格形式", "option__1": {"label": "小数点あり (99.99)"}, "option__2": {"label": "小数点なし"}, "info": "ストアのデフォルト通貨が日本円の場合、小数点なしを選択してください。"}, "paragraph__1": {"content": "比較リストのポップアップ画面で表示する項目を選んでください。"}, "compare_show_description": {"label": "商品説明を表示"}, "compare_show_collection": {"label": "コレクションの表示"}, "compare_show_availability": {"label": "在庫状況の表示"}, "compare_show_product_type": {"label": "商品タイプの表示"}, "compare_show_vendor": {"label": "販売元の表示"}, "compare_show_sku": {"label": "SKUの表示"}, "compare_show_barcode": {"label": "バーコードの表示"}, "compare_show_options": {"label": "オプションの表示"}, "compare_description_max_size": {"label": "商品説明の表示文字数の制限"}, "header__5": {"content": "ユーザーマニュアル"}, "paragraph__2": {"content": "[お気に入りリストと比較リスト](https://misell-manual.wraptas.site/theme-settings/wishlist-compare)"}}}, "payments": {"name": "お支払い(決済方法)", "settings": {"payment_design": {"label": "アイコンデザイン", "option__1": {"label": "MISEル アイコン"}, "option__2": {"label": "Shopify アイコン"}, "content": "Shopifyにより用意されていないアイコンは表示することができません。MISEルアイコンを選択してください。"}, "header__1": {"content": "決済アイコンの設定"}, "paragraph__1": {"content": "ペイメントアイコンを独自に設定できます。デフォルトを表示する場合は空欄にしてください。"}, "paragraph__2": {"content": "区切りには下記のように','(コンマ)を使用してください。"}, "paragraph__3": {"content": "afterpay,american_express,apple_pay,amazon_pay,bitcoin,dankort,diners_club,discover,dogecoin,dwolla,forbrugsforeningen,google_pay,ideal,jcb,litecoin,maestro,master,paypal,rakuten_pay,shopify_pay,sofort,visa,alipay,au_pay,au,d_barai,docomo,family_mart,lawson,line_pay,merupay,mini_stop,paypay,rakuten_pay,seven_eleven,softbank,union_pay,custom_1,custom_2,custom_3,custom_4,custom_5"}, "payment_sequence_product": {"label": "商品ページとクイックビュー"}, "payment_sequence_footer": {"label": "フッター"}, "header__2": {"content": "アイコン画像"}, "payment_images": {"label": "画像コード", "info": "アイコンを使う代わりに好きな画像を設定することもできます。フォーマット形式は、visa[image_url]のように行ってください。"}, "header__3": {"content": "ユーザーマニュアル"}, "paragraph__4": {"content": "[お支払い(決済方法)](https://misell-manual.wraptas.site/theme-settings/payments)"}}}, "social_media": {"name": "SNS(ソーシャルメディア)", "settings": {"header__1": {"content": "SNSシェア"}, "social_share_image": {"label": "画像", "info": "SNSでシェアされたときのサムネイル画像を設定します。詳しくは[こちら](https://help.shopify.com/ja/manual/using-themes/troubleshooting/showing-social-media-thumbnail-images/)"}, "header__2": {"content": "SNS"}, "social_facebook": {"label": "Facebook"}, "social_twitter": {"label": "X(Twitter)"}, "social_instagram": {"label": "Instagram"}, "social_pinterest": {"label": "Pinterest"}, "social_youtube": {"label": "YouTube"}, "social_behance": {"label": "<PERSON><PERSON><PERSON>"}, "social_skype": {"label": "Skype"}, "social_tiktok": {"label": "TikTok link"}, "header__3": {"content": "SNSシェアボタン"}, "paragraph__1": {"content": "SNSシェアボタンを有効にするには｢テーマ設定 > 商品ページとクイックビュー｣の項目でシェアボタンを有効にしてください。"}, "social_share_enable_facebook": {"label": "Facebook シェアボタンを表示"}, "social_share_enable_twitter": {"label": "X(Twitter) シェアボタンを表示"}, "social_share_enable_pinterest": {"label": "Pinterest シェアボタンを表示"}, "social_share_enable_linkedin": {"label": "Linkedin シェアボタンを表示"}, "social_share_enable_buffer": {"label": "Buffer シェアボタンを表示"}, "social_share_enable_reddit": {"label": "Reddit シェアボタンを表示"}, "social_share_enable_line": {"label": "LINE シェアボタンを表示"}, "social_share_enable_hatena": {"label": "はてな シェアボタンを表示"}, "social_share_enable_pocket": {"label": "Pocket シェアボタンを表示"}, "header__4": {"content": "ユーザーマニュアル"}, "paragraph__2": {"content": "[SNS(ソーシャルメディア)](https://misell-manual.wraptas.site/theme-settings/sns)"}}}, "news_subscription": {"name": "メルマガ購読・ポップアップ", "settings": {"subscription_method": {"label": "メルマガ購読方法", "info": "Shopify (デフォルト)の購読者は｢顧客管理｣のリストに加えられます。[顧客管理](/admin/customers?query=&accepts_marketing=1)", "option__1": {"label": "Shopify (デフォルト)"}, "option__2": {"label": "MailChimp"}}, "subscription_mailchimp_form_action": {"label": "MailChimp URL", "info": "[MailChimp form のフォームアクションのURL取得方法](https://mailchimp.com/)"}, "subscription_show_confirmation_checkbox": {"label": "確認チェックボックスを表示"}, "header__1": {"content": "メルマガ購読のポップアップ"}, "subscription_popup_enable": {"label": "メルマガ購読のポップアップを有効化"}, "subscription_popup_layout": {"label": "レイアウト", "option__1": {"label": "1"}, "option__2": {"label": "2"}, "option__3": {"label": "3"}, "option__4": {"label": "4"}, "option__5": {"label": "5"}}, "subscription_popup_show_icon": {"label": "アイコンを表示 (レイアウト1のとき)"}, "paragraph__1": {"content": "注意:このフォームのテキストを削除するとブロックが隠れます。"}, "subscription_popup_text_line_01": {"label": "テキストライン #1"}, "subscription_popup_text_line_02": {"label": "テキストライン #2"}, "subscription_popup_text_line_03": {"label": "テキストライン #3"}, "subscription_popup_text_line_04": {"label": "テキストライン #4"}, "subscription_popup_show_form": {"label": "フォームを表示"}, "subscription_popup_text_input_placeholder": {"label": "インプットのプレースホルダー"}, "subscription_popup_text_submit_button_text": {"label": "送信(購読)ボタンのテキスト"}, "subscription_popup_text_button": {"label": "リンクボタンのテキスト"}, "subscription_popup_text_dont_show_again": {"label": "｢今後表示しない｣のテキスト"}, "subscription_popup_text_close": {"label": "｢閉じる｣のテキスト"}, "subscription_popup_image": {"label": "背景画像"}, "subscription_popup_image_width": {"label": "背景画像の幅"}, "subscription_popup_image_format_pjpg": {"label": "PJPG形式の画像フォーマットを有効化", "info": "透過画像には適用できません。"}, "subscription_popup_image_mobile": {"label": "モバイルの背景画像", "info": "｢レイアウト #4｣のみ有効です。"}, "subscription_popup_insert_image": {"label": "挿入画像"}, "subscription_popup_insert_image_width": {"label": "挿入画像の幅"}, "subscription_popup_insert_image_format_pjpg": {"label": "PJPG形式の画像フォーマットを有効化", "info": "透過画像には適用できません。"}, "subscription_popup_link": {"label": "リンク"}, "subscription_popup_show_once": {"label": "表示回数", "option__1": {"label": "｢今後表示しない｣を選択するまで"}, "option__2": {"label": "一度きり"}}, "subscription_popup_delay": {"label": "遅れて表示(秒)"}, "subscription_popup_cookies_life": {"label": "クッキーの継続期間", "option__1": {"label": "1日"}, "option__2": {"label": "3日"}, "option__3": {"label": "1週間"}, "option__4": {"label": "1ヶ月"}, "option__5": {"label": "1年"}}, "header__2": {"content": "メルマガ購読時のポップアップ"}, "subscription_confirmation_popup_enable": {"label": "メルマガ購読確認のポップアップを有効化"}, "subscription_confirmation_popup_success_message": {"label": "メルマガ購読時のメッセージ"}, "subscription_confirmation_popup_success_button": {"label": "メルマガ購読時のボタンのテキスト"}, "header__3": {"content": "ユーザーマニュアル"}, "paragraph__2": {"content": "[メルマガ購読・ポップアップ](https://misell-manual.wraptas.site/theme-settings/subscription)"}}}, "blog_and_article_pages": {"name": "ブログとブログ記事ページ", "settings": {"header__1": {"content": "ブログサイドバー"}, "blog_show_sidebar": {"label": "サイドバー表示", "option__1": {"label": "デスクトップとモバイル"}, "option__2": {"label": "デスクトップのみ"}, "option__3": {"label": "非表示"}}, "blog_sidebar_position": {"label": "サイドバーの位置", "option__1": {"label": "左"}, "option__2": {"label": "右"}, "option__3": {"label": "ドロップダウン"}}, "blog_enable_sticky": {"label": "スティッキーサイドバーを有効化", "info": "常時固定となりスクロールしてもサイドバーを常に表示します。"}, "header__2": {"content": "ブログ記事ページサイドバー"}, "article_show_sidebar": {"label": "サイドバーを表示", "option__1": {"label": "デスクトップとモバイル"}, "option__2": {"label": "デスクトップのみ"}, "option__3": {"label": "非表示"}}, "article_sidebar_position": {"label": "サイドバーの位置", "option__1": {"label": "左"}, "option__2": {"label": "右"}, "option__3": {"label": "ドロップダウン"}}, "article_enable_sticky": {"label": "スティッキーサイドバーを有効化", "info": "常時固定となりスクロールしてもサイドバーを常に表示します。"}, "header__3": {"content": "ユーザーマニュアル"}, "paragraph__1": {"content": "[ブログとブログ記事ページ](https://misell-manual.wraptas.site/theme-settings/blog-article)"}}}, "account": {"name": "アカウント", "settings": {"header__1": {"content": "一般設定"}, "account_show_header_button": {"label": "アカウントのヘッダーボタンを表示"}, "header__2": {"content": "ポップアップ"}, "account_popup_enable": {"label": "ポップアップを有効化"}, "account_popup_sign_up_info_page_content": {"label": "ポップアップのコンテンツ", "info": "デフォルトではページの'Include Popup Account'が適用されます。(ExImアプリでMisell-theme-page.zipがインストールされている場合)"}, "account_popup_show_subscription": {"label": "メルマガ購読", "info": "ユーザー認証後にのみ表示されます。"}, "header__3": {"content": "ログインページ"}, "account_page_sing_up_page_content": {"label": "新規登録ページのコンテンツ"}, "header__4": {"content": "ユーザーマニュアル"}, "paragraph__1": {"content": "[アカウント](https://misell-manual.wraptas.site/theme-settings/account)"}}}, "services": {"name": "ストア案内(サービス)", "settings": {"header__1": {"content": "一般設定"}, "services_show_header_button": {"label": "ストア案内(サービス)のヘッダーボタンを表示"}, "header__2": {"content": "ポップアップ"}, "services_popup_enable": {"label": "ポップアップを有効化"}, "services_header_button_link": {"label": "ストア案内ヘッダーボタンのリンク", "info": "ポップアップを利用しない場合のリンクを設定してください。設定しない場合はpages/customer-servicesへリンクします。"}, "services_popup_page_content": {"label": "ポップアップのコンテンツ", "info": "デフォルトではページの'Include Popup Services'が適用されます。(ExImアプリでMisell-theme-page.zipがインストールされている場合)くわしくはマニュアルをご覧ください。[サイト案内(サービス)](https://misell-manual.wraptas.site/theme-settings/service)"}}}, "apps_and_language": {"name": "アプリと言語", "settings": {"header__1": {"content": "言語"}, "app_language": {"label": "言語アプリの選択", "option__1": {"label": "ストア設定の言語(デフォルト)"}, "option__2": {"label": "Weglot"}, "option__3": {"label": "リンクリスト (メニューから選択)"}, "option__4": {"label": "なし"}}, "app_language_link_list": {"label": "言語のドロップダウンメニュー"}, "header__2": {"content": "ユーザーマニュアル"}, "paragraph__1": {"content": "[アプリと言語](https://misell-manual.wraptas.site/theme-settings/language)"}}}, "button_input_and_promo_box": {"name": "ボタン・インプット・プロモボックス", "settings": {"header__1": {"content": "ボタンとインプット"}, "button_border_radius": {"label": "ボタン枠の丸み"}, "input_border_radius": {"label": "インプット枠の丸み"}, "header__2": {"content": "プロモボックス"}, "promobox_image_border_radius": {"label": "画像の枠の丸み"}, "promobox_format_pjpg": {"label": "PJPGフォーマットを有効化", "info": "透過画像には適用できません。"}, "header__3": {"content": "ユーザーマニュアル"}, "paragraph__1": {"content": "[ボタン・インプット・プロモボックス](https://misell-manual.wraptas.site/theme-settings/button-input-promobox)"}}}, "notifications": {"name": "お知らせ(クッキー通知)", "settings": {"header__1": {"content": "クッキー通知"}, "notification_cookies_enable": {"label": "有効化"}, "notification_cookies_button_information_link": {"label": "インフォーメーションボタンリンク"}, "notification_cookies_delay": {"label": "遅らせる(秒)"}, "notification_cookies_show_once": {"label": "表示回数制限", "info": "ユーザーが通知を閉じた場合に適用されます。"}, "notification_cookies_cookies_life": {"label": "クッキーの継続期間", "option__1": {"label": "1日"}, "option__2": {"label": "3日"}, "option__3": {"label": "1週間"}, "option__4": {"label": "1ヶ月"}, "option__5": {"label": "1年"}}, "header__2": {"content": "ユーザーマニュアル"}, "paragraph__1": {"content": "[お知らせ(クッキー通知)](https://misell-manual.wraptas.site/theme-settings/notification)"}}}, "pre-loader": {"name": "プリロード", "settings": {"layout_preloader_image": {"label": "プリロード画像のカスタム"}, "layout_preloader_image_size": {"label": "プリロード画像のサイズ"}, "layout_preloader_opacity": {"label": "背景画像の透明度"}, "layout_enable_page_preloader": {"label": "ページのプリロードを有効化"}, "header__1": {"content": "ユーザーマニュアル"}, "paragraph__1": {"content": "[プリロード](https://misell-manual.wraptas.site/theme-settings/pre-loader)"}}}, "animations": {"name": "アニメーション", "settings": {"header__1": {"content": "ポップアップ"}, "popup_enable_bg_blur": {"label": "背景画像へのブラー効果を有効化"}, "header__2": {"content": "プロモボックス"}, "promobox_animation_duration": {"label": "プロモボックスのエフェクト(秒)"}, "header__3": {"content": "ツールチップ"}, "tooltips_enable": {"label": "ツールチップ(サイト全体)を有効化"}, "tooltips_animation_type": {"label": "アニメーションスタイル", "option__1": {"label": "フェード"}, "option__2": {"label": "スケール"}, "option__3": {"label": "シフト1"}, "option__4": {"label": "シフト2"}, "option__5": {"label": "パースペクティブ"}}, "header__4": {"content": "ヘッダー"}, "header_animation_sticky_opacity": {"label": "スティッキーヘッダーの透明度"}, "header_tape_animation_duration": {"label": "ヘッダーテープスライドの長さ(秒)"}, "header__5": {"content": "商品"}, "product_hover_animation_type": {"label": "商品画像ホーバー時のアニメーションスタイル(コレクションページ)", "info": "矢印・クイックビュー・ラベル・カウントダウン", "option__1": {"label": "デフォルト"}, "option__2": {"label": "フェード"}, "option__3": {"label": "水平方向"}, "option__4": {"label": "縦方向"}, "option__5": {"label": "全方向"}, "option__6": {"label": "スケール"}}, "buttons_animation_icon_enable": {"label": "｢｢カートに入れる｣・｢お気に入りリスト｣ボタンのアイコンのアニメーション(ホバー時)を有効化"}, "button_add_to_cart_animation_enable": {"label": "｢カートに入れる｣ボタンのシェイクアニメーションを有効化"}, "header__6": {"content": "ユーザーマニュアル"}, "paragraph__1": {"content": "[アニメーション](https://misell-manual.wraptas.site/theme-settings/animation)"}}}, "developer": {"name": "開発者向け(ディベロッパー)", "settings": {"paragraph__1": {"content": "Shopifyパートナー・開発者の方へ:1)将来のアップデートに対応するために、テーマカスタマイズに行った変更を把握し記録を残しておくこと・またgitなどのバージョンコントロールシステムの使用を推奨します。 2) 変更は別のファイルにしておくこと。下記のCSSとJavaScriptコードを別の独立したファイルとしての使用方法を用意しています。"}, "dev_enable_custom_css": {"label": "CSSでのカスタマイズを有効化", "info": "コードを編集する > アセット(Assets)フォルダ内にあるcustom.cssファイルにCSSコードを記述します。"}, "dev_enable_custom_js": {"label": "JavaScriptでのカスタマイズを有効化", "info": "コードを編集する > アセット(Assets)フォルダ内にあるcustom.jsファイルにJavaScriptコードを記述します。"}}}}, "sections": {"article_carousel": {"name": "記事カルーセル", "settings": {"title": {"label": "タイトル"}, "header__1": {"content": "一般設定"}, "blog": {"label": "ブログ"}, "layout": {"label": "レイアウト", "option__1": {"label": "行"}, "option__2": {"label": "コラム"}, "option__3": {"label": "コラムセンター"}}, "max_symbols_count": {"label": "コンテンツの最大表示文字数"}, "show_details": {"label": "記事の詳細(日時・投稿者)を表示"}, "button_text": {"label": "ボタンテキスト", "info": "コラムのレイアウト"}, "size_of_columns": {"label": "コラムのサイズ", "option__1": {"label": "1行に2アイテム"}, "option__2": {"label": "1行に3アイテム"}, "option__3": {"label": "1行に4アイテム"}}, "max_items_count": {"label": "アイテムの最大数"}, "header__2": {"content": "スライダーの設定"}, "autoplay": {"label": "自動再生"}, "speed": {"label": "自動再生のスピード(秒)"}, "infinite": {"label": "無限ループ"}, "arrows": {"label": "矢印表示"}, "bullets": {"label": "ブレット表示"}, "header__3": {"content": "ビデオチュートリアル・ユーザーマニュアル"}, "paragraph__1": {"content": "[記事カルーセル（動画）](https://www.youtube.com/channel/UChCFvSvr3YgbYDcumJ_OqzQ)"}, "paragraph__2": {"content": "[記事カルーセル](https://misell-manual.wraptas.site/sections/article-carousel)"}}}, "brand_carousel": {"name": "ブランドカルーセル", "settings": {"title": {"label": "タイトル"}, "header__1": {"content": "一般設定"}, "layout": {"label": "レイアウト", "option__1": {"label": "スライダー"}, "option__2": {"label": "グリッド"}}, "size_of_columns": {"label": "コラムのサイズ", "info": "スライダーのみに適用", "option__1": {"label": "1行に4アイテム"}, "option__2": {"label": "1行に5アイテム"}, "option__3": {"label": "1行に6アイテム"}, "option__4": {"label": "1行に7アイテム"}, "option__5": {"label": "1行に8アイテム"}}, "image_max_width": {"label": "画像幅の最大値", "info": "0 - オ<PERSON><PERSON>"}, "bordered_links": {"label": "枠付きリンク"}, "border_radius": {"label": "枠の丸み設定 (px, %)", "info": "例:5px or 50%"}, "header__2": {"content": "スライダーの設定"}, "autoplay": {"label": "自動再生"}, "speed": {"label": "自動再生のスピード (秒)"}, "infinite": {"label": "無限ループ"}, "arrows": {"label": "矢印表示"}, "bullets": {"label": "ブレット表示"}, "header__3": {"content": "ビデオチュートリアル・ユーザーマニュアル"}, "paragraph__1": {"content": "[ブランドカルーセル（動画）](https://www.youtube.com/channel/UChCFvSvr3YgbYDcumJ_OqzQ)"}, "paragraph__2": {"content": "[ブランドカルーセル](https://misell-manual.wraptas.site/sections/brand-carousel)"}}, "blocks": {"brand": {"name": "ブランド", "settings": {"image": {"label": "画像"}, "link": {"label": "リンク", "info": "ブランドによってコレクションフィルターへのリンクを加えることができます。その場合、｢ナビゲーション > フィルター｣で設定する必要があります。 [フィルター](/admin/menus) リンクフォーマット:{collection link}?filter.p.vendor={Brand})"}}}}}, "product_carousel": {"name": "商品カルーセル", "settings": {"show_title": {"label": "商品タイトルを表示"}, "header__1": {"content": "一般設定"}, "container": {"label": "コンテンツ幅", "option__1": {"label": "フル幅"}, "option__2": {"label": "ボックス幅"}}, "size_of_columns": {"label": "コラムのサイズ", "option__1": {"label": "1行に2アイテム"}, "option__2": {"label": "1行に3アイテム"}, "option__3": {"label": "1行に4アイテム"}}, "max_count": {"label": "最大数"}, "header__2": {"content": "スライダーの設定"}, "autoplay": {"label": "自動再生"}, "speed": {"label": "自動再生のスピード(秒)"}, "infinite": {"label": "無限ループ"}, "arrows": {"label": "矢印表示"}, "bullets": {"label": "ブレット表示"}, "header__3": {"content": "ビデオチュートリアル・ユーザーマニュアル"}, "paragraph__1": {"content": "[商品カルーセル（動画）](https://www.youtube.com/channel/UChCFvSvr3YgbYDcumJ_OqzQ)"}, "paragraph__2": {"content": "[商品カルーセル](https://misell-manual.wraptas.site/sections/product-carousel)"}}, "blocks": {"collection": {"name": "コレクション", "settings": {"title": {"label": "タイトル"}, "collection": {"label": "コレクション"}}}}}, "related_product_carousel": {"name": "関連商品カルーセル", "settings": {"show_section": {"label": "関連商品を表示"}, "type": {"label": "タイプ", "option__1": {"label": "関連商品"}, "option__2": {"label": "おすすめ商品"}}, "size_of_columns": {"label": "コラムのサイズ", "option__1": {"label": "1行に2アイテム"}, "option__2": {"label": "1行に3アイテム"}, "option__3": {"label": "1行に4アイテム"}}, "max_count": {"label": "最大表示数"}, "autoplay": {"label": "自動再生"}, "speed": {"label": "自動再生のスピード(秒)"}, "infinite": {"label": "無限ループ"}, "arrows": {"label": "矢印表示"}, "bullets": {"label": "ブレット表示"}}}, "review_carousel": {"name": "レビューカルーセル", "settings": {"title": {"label": "タイトル"}, "header__1": {"content": "一般設定"}, "type": {"label": "タイプ", "option__1": {"label": "1"}, "option__2": {"label": "2"}}, "style": {"label": "カラースタイル", "option__1": {"label": "スタイル #1"}, "option__2": {"label": "スタイル #2"}, "option__3": {"label": "スタイル #3"}}, "size_of_columns": {"label": "コラムのサイズ", "option__1": {"label": "1行に1アイテム"}, "option__2": {"label": "1行に2アイテム"}, "option__3": {"label": "1行に3アイテム"}, "option__4": {"label": "1行に4アイテム"}}, "autoplay": {"label": "自動再生"}, "speed": {"label": "自動再生のスピード(秒)"}, "arrows": {"label": "矢印表示"}}, "blocks": {"slide": {"name": "スライド", "settings": {"title": {"label": "タイトル"}, "stars": {"label": "評価(星)"}, "paragraph": {"label": "パラグラフ文章"}, "user_image": {"label": "ユーザー画像"}, "user_text_line_1": {"label": "ユーザーテキストライン #1"}, "user_text_line_2": {"label": "ユーザーテキストライン #2"}, "header__1": {"content": "ビデオチュートリアル・ユーザーマニュアル"}, "paragraph__1": {"content": "[レビューカルーセル（動画）](https://www.youtube.com/channel/UChCFvSvr3YgbYDcumJ_OqzQ)"}, "paragraph__2": {"content": "[レビューカルーセル](https://misell-manual.wraptas.site/sections/review-carousel)"}}}}}, "custom_liquid": {"name": "カスタム Liquid", "settings": {"use_container": {"label": "コンテナを使う"}, "custom_liquid": {"label": "カスタム Liquid", "info": "アプリ提供によるコードスニペットやその他Liquidコードの挿入が必要な場合こちらにコードを加えてください。*こちらは高度なカスタマイズとなります。詳細はアプリのサポート等にお問い合わせください。"}}}, "footer": {"name": "フッター", "settings": {"type": {"label": "タイプ", "option__1": {"label": "タイプ #1"}, "option__2": {"label": "タイプ #2"}, "option__3": {"label": "タイプ #3"}, "option__4": {"label": "タイプ #4"}, "option__5": {"label": "タイプ #5"}, "option__6": {"label": "タイプ #6"}, "option__7": {"label": "タイプ #7"}}, "style": {"label": "カラースタイル", "info": "より詳細な設定は各セクションで行えます。｢ブロックを追加 > カラー｣で行って下さい。", "option__1": {"label": "スタイル #1"}, "option__2": {"label": "スタイル #2"}}, "show_logo": {"label": "ロゴを表示"}, "show_button_back_to_top": {"label": "トップへ戻るボタンを表示"}, "enable_fixed": {"label": "固定フッターを有効化"}, "header__1": {"content": "Shopでフォロー"}, "undefined": {"info": "｢Shopでフォロー｣を追加するとお客様はShopアプリでストアをフォローできるようになります。｢Shopでフォロー｣を使用するにはShop Payが有効になっている必要があります。 [詳しくはこちら](https://help.shopify.com/ja/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "enable_follow_on_shop": {"label": "Shopでフォローを有効化"}, "header__2": {"content": "ユーザーマニュアル"}, "paragraph__1": {"content": "[フッター](https://misell-manual.wraptas.site/footer/about)"}}, "blocks": {"menu": {"name": "メニュー", "settings": {"title": {"label": "タイトル"}, "show_title_on_desktop": {"label": "デスクトップでタイトルを表示"}, "menu": {"label": "メニュー"}, "size": {"label": "コラムのサイズ", "info": "0 ではオート"}, "rows_limit": {"label": "行の制限", "info": "0 では制限なし"}}}, "custom_html": {"name": "Custom html", "settings": {"title": {"label": "タイトル"}, "show_title_on_desktop": {"label": "デスクトップでタイトルを表示"}, "page_content": {"label": "ページコンテンツ", "info": "デフォルトでは'Include Footer Custom HTML'のページが挿入されます。"}}}, "social_media": {"name": "SNS(ソーシャルメディア)", "settings": {"title": {"label": "タイトル"}, "show_facebook": {"label": "Facebook"}, "show_twitter": {"label": "X(Twitter)"}, "show_instagram": {"label": "Instagram"}, "show_pinterest": {"label": "Pinterest"}, "show_youtube": {"label": "YouTube"}, "show_behance": {"label": "<PERSON><PERSON><PERSON>"}, "show_skype": {"label": "Skype"}, "show_tiktok": {"label": "tiktok"}}}, "subscription": {"name": "メルマガ購読", "settings": {"title": {"label": "タイトル"}, "paragraph": {"label": "パラグラフ文章"}, "placeholder": {"label": "プレースホルダー"}, "button_text": {"label": "ボタンテキスト"}}}, "copyright": {"name": "コピーライト", "settings": {"page_content": {"label": "ページコンテンツ", "info": "デフォルトでは'Include Footer Copyright'のページが挿入されます。"}}}, "payments": {"name": "決済方法(ペイメント)"}, "colorize": {"name": "カラースタイル", "settings": {"bg": {"label": "背景"}, "bd": {"label": "ボーダー"}, "c": {"label": "テキスト"}, "subscription_btn_type": {"label": "検索ボタンのタイプ", "option__1": {"label": "オート"}, "option__2": {"label": "デフォルト"}, "option__3": {"label": "反転"}, "option__4": {"label": "セカンダリ"}, "option__5": {"label": "クリーン"}}, "links_c": {"label": "リンク"}, "links_h_c": {"label": "ホバーリンク"}, "title_c": {"label": "タイトル"}, "icons_c": {"label": "アイコン"}, "icons_h_c": {"label": "ホバーアイコン"}}}}}, "header": {"name": "ヘッダー", "settings": {"type": {"label": "ヘッダータイプ", "option__1": {"label": "タイプ #1"}, "option__2": {"label": "タイプ #2"}, "option__3": {"label": "タイプ #3"}, "option__4": {"label": "タイプ #4"}, "option__5": {"label": "タイプ #5 (縦メニュー)"}, "option__6": {"label": "タイプ #6"}, "option__7": {"label": "タイプ #7 (縦メニュー)"}}, "style": {"label": "カラースタイル", "info": "詳細なカラー設定は｢ブロックを追加 > カラースタイル｣から行えます。", "option__1": {"label": "スタイル #1"}, "option__2": {"label": "スタイル #2"}, "option__3": {"label": "スタイル #3"}, "option__4": {"label": "スタイル #4"}}, "transparent_bg": {"label": "透明背景", "option__1": {"label": "無効化"}, "option__2": {"label": "透明背景を有効化"}, "option__3": {"label": "透明背景 - ホバー時にカラー"}}, "logo_mobile_centered": {"label": "モバイルでのセンターロゴ(中央)", "info": "モバイル画面のプレビューでロゴが重なっていないか確認してください。"}, "height": {"label": "トップラインの高さ(デスクトップ)", "info": "0 - オ<PERSON><PERSON>"}, "items_padding": {"label": "水平軸の余白", "info": "0 - オ<PERSON><PERSON>"}, "header__1": {"content": "メインメニュー"}, "menu": {"label": "メニューの選択"}, "show_menu_arrows": {"label": "ドロップダウンの最初のレベルに矢印表示"}, "show_menu_hover_underline": {"label": "ホバー時に下線を表示"}, "header__2": {"content": "縦メニュー"}, "paragraph__1": {"content": "ヘッダータイプ #5, #7"}, "vertical_menu": {"label": "縦メニューの選択"}, "fixed_vertical_menu": {"label": "ホームページの固定縦メニュー", "info": "このオプションを有効にすると、縦メニューのスペースブロックがビルダーセクションに追加できます。"}, "header__3": {"content": "コンテンツ"}, "paragraph__2": {"content": "ヘッダータイプ #5, #7"}, "additional_line_info_page_content": {"label": "トップラインのコンテンツ", "info": "デフォルトでは'Include Header Info Line #4'のページが挿入されます。"}, "header__4": {"content": "国/地域セレクター"}, "undefined": {"info": "国/地域を加えるにはShopifyストア側での設定が必要となります。詳しくはShopifyのヘルプセンターを参照ください。[Shopify Markets](https://help.shopify.com/ja/manual/markets)"}, "enable_country_selector": {"label": "国/地域のセレクターを有効化"}, "show_country_name": {"label": "国名を表示"}, "country_selector_max_width": {"label": "最大幅", "info": "0 - で制限なし"}, "header__5": {"content": "スティッキーヘッダー(常時固定表示のヘッダー)"}, "sticky": {"label": "スティッキーヘッダー", "option__1": {"label": "無効化"}, "option__2": {"label": "デスクトップとモバイル"}, "option__3": {"label": "デスクトップのみ"}, "option__4": {"label": "モバイルのみ"}}, "desktop_sticky_type": {"label": "タイプ(デスクトップ)", "option__1": {"label": "ワイドタイプ(フルサイズ)"}, "option__2": {"label": "スリムタイプ"}}, "hide_sticky_when_scroll_down": {"label": "スクロールした時に非表示"}, "header__6": {"content": "ビデオチュートリアル・ユーザーマニュアル"}, "paragraph__3": {"content": "[ヘッダーオプション（動画）](https://youtu.be/bZBgcUNZ2AM?si=ZNs1FfS2B_8U6_Qj)"}, "paragraph__4": {"content": "[ヘッダー](https://misell-manual.wraptas.site/header/about)"}}, "blocks": {"megamenu_title_image": {"name": "タイトル画像", "settings": {"paragraph__1": {"content": "2階層目のみ"}, "for_item": {"label": "アイテム #1", "info": "例:メンズ+トップス"}, "image": {"label": "画像 #1"}, "image_size": {"label": "画像サイス #1", "info": "画像の画質を調整します。ただしレイジーロードの設定が有効の場合このオプションは適用されません。"}, "for_item_2": {"label": "アイテム #2"}, "image_2": {"label": "画像 #2"}, "image_size_2": {"label": "画像サイズ #2"}, "for_item_3": {"label": "アイテム #3"}, "image_3": {"label": "画像 #3"}, "image_size_3": {"label": "画像サイズ #3"}, "for_item_4": {"label": "アイテム #4"}, "image_4": {"label": "画像 #4"}, "image_size_4": {"label": "画像 #4"}, "for_item_5": {"label": "アイテム #5"}, "image_5": {"label": "画像 #5"}, "image_size_5": {"label": "画像サイズ #5"}}}, "megamenu_label": {"name": "ラベル", "settings": {"type": {"label": "タイプ", "option__1": {"label": "おすすめ"}, "option__2": {"label": "セール"}, "option__3": {"label": "新商品"}}, "text": {"label": "テキスト"}, "for_item": {"label": "アイテム #1", "info": "例:メンズ+シューズ+フラット"}, "for_item_2": {"label": "アイテム #2"}, "for_item_3": {"label": "アイテム #3"}, "for_item_4": {"label": "アイテム #4"}, "for_item_5": {"label": "アイテム #5"}, "for_item_6": {"label": "アイテム #6"}, "for_item_7": {"label": "アイテム #7"}, "for_item_8": {"label": "アイテム #8"}, "for_item_9": {"label": "アイテム #9"}, "for_item_10": {"label": "アイテム #10"}, "for_item_11": {"label": "アイテム #11"}, "for_item_12": {"label": "アイテム #12"}, "for_item_13": {"label": "アイテム #13"}, "for_item_14": {"label": "アイテム #14"}, "for_item_15": {"label": "アイテム #15"}}}, "icon": {"name": "アイコン (1階層目)", "settings": {"paragraph__1": {"content": "アイコンは'icon-theme-50'のように指定します。アイコンのコードリストは[こちら](https://onyxtan-nl.myshopify.com/pages/icons)"}, "for_vertical": {"label": "縦メニュー"}, "for_item_1": {"label": "アイテム #1", "info": "例:'メンズ'"}, "icon_1": {"label": "アイコン #1", "info": "例:'icon-theme-050'"}, "for_item_2": {"label": "アイテム #2"}, "icon_2": {"label": "アイコン #2"}, "for_item_3": {"label": "アイテム #3"}, "icon_3": {"label": "アイコン #3"}, "for_item_4": {"label": "アイテム #4"}, "icon_4": {"label": "アイコン #4"}, "for_item_5": {"label": "アイテム #5"}, "icon_5": {"label": "アイコン #5"}, "for_item_6": {"label": "アイテム #6"}, "icon_6": {"label": "アイコン #6"}, "for_item_7": {"label": "アイテム #7"}, "icon_7": {"label": "アイコン #7"}, "for_item_8": {"label": "アイテム #8"}, "icon_8": {"label": "アイコン #8"}, "for_item_9": {"label": "アイテム #9"}, "icon_9": {"label": "アイコン #9"}, "for_item_10": {"label": "アイテム #10"}, "icon_10": {"label": "アイコン #10"}, "for_item_11": {"label": "アイテム #11"}, "icon_11": {"label": "アイコン #11"}, "for_item_12": {"label": "アイテム #12"}, "icon_12": {"label": "アイコン #12"}, "for_item_13": {"label": "アイテム #13"}, "icon_13": {"label": "アイコン #13"}, "for_item_14": {"label": "アイテム #14"}, "icon_14": {"label": "アイコン #14"}, "for_item_15": {"label": "アイテム #15"}, "icon_15": {"label": "アイコン #15"}, "for_item_16": {"label": "アイテム #16"}, "icon_16": {"label": "アイコン #16"}, "for_item_17": {"label": "アイテム #17"}, "icon_17": {"label": "アイコン #17"}, "for_item_18": {"label": "アイテム #18"}, "icon_18": {"label": "アイコン #18"}, "for_item_19": {"label": "アイテム #19"}, "icon_19": {"label": "アイコン #19"}, "for_item_20": {"label": "アイテム #20"}, "icon_20": {"label": "アイコン #20"}}}, "info_line": {"name": "インフォーメーションライン", "settings": {"show_on_mobile": {"label": "モバイルで表示"}, "style": {"label": "カラースタイル", "option__1": {"label": "スタイル #1"}, "option__2": {"label": "スタイル #2"}}, "transparent_bg": {"label": "透明背景を有効化"}, "page_content": {"label": "ページコンテンツ", "info": "デフォルトでは'Include Header Info Line'のページが挿入されます。(ExImアプリでMisell-theme-page.zipがインストールされている場合)"}, "show_social_media": {"label": "SNS(ソーシャルメディア)を表示"}, "header__1": {"content": "メディアの設定"}, "show_facebook": {"label": "Facebook"}, "show_twitter": {"label": "X(Twitter)"}, "show_instagram": {"label": "Instagram"}, "show_pinterest": {"label": "Pinterest"}, "show_youtube": {"label": "YouTube"}, "show_behance": {"label": "<PERSON><PERSON><PERSON>"}, "show_skype": {"label": "Skype"}, "show_line": {"label": "LINE"}, "show_tiktok": {"label": "Show tiktok"}}}, "tape": {"name": "アナウンスメントバー", "settings": {"delay": {"label": "遅らせて表示"}, "style": {"label": "カラースタイル", "option__1": {"label": "スタイル #1"}, "option__2": {"label": "スタイル #2"}, "option__3": {"label": "スタイル #3"}}, "transparent_bg": {"label": "透明背景を有効化"}, "content": {"label": "テキストまたはHTML"}}}, "megamenu": {"name": "メガメニュー", "settings": {"header__1": {"content": "一般設定"}, "for_item": {"label": "アイテム"}, "grid": {"label": "リンクのコラムサイズ"}, "second_level_column": {"label": "2階層目のコラム"}, "second_level_column_size": {"label": "コラムラインの制限"}, "wrap": {"label": "メニューコラムをグリッドで囲む"}, "wrapper_grid": {"label": "囲むコラムのサイズ"}, "header__2": {"content": "左の追加コンテンツ"}, "column_left_type": {"label": "コンテンツのタイプ", "option__1": {"label": "なし"}, "option__2": {"label": "1行に1アイテム"}, "option__3": {"label": "2行に1アイテム"}, "option__4": {"label": "1行に2アイテム"}, "option__5": {"label": "2行に2アイテム"}, "option__6": {"label": "1行に3アイテム"}, "option__7": {"label": "2行に3アイテム"}, "option__8": {"label": "1行に4アイテム"}, "option__9": {"label": "2行に4アイテム"}, "option__10": {"label": "2/3 と 1/3 コラム"}}, "column_left_size": {"label": "コラムのサイズ", "info": "0 - オ<PERSON><PERSON>"}, "header__3": {"content": "右の追加コンテンツ"}, "column_right_type": {"label": "コンテンツのタイプ", "option__1": {"label": "なし"}, "option__2": {"label": "1行に1アイテム"}, "option__3": {"label": "2行に1アイテム"}, "option__4": {"label": "1行に2アイテム"}, "option__5": {"label": "2行に2アイテム"}, "option__6": {"label": "1行に3アイテム"}, "option__7": {"label": "2行に3アイテム"}, "option__8": {"label": "1行に4アイテム"}, "option__9": {"label": "2行に4アイテム"}, "option__10": {"label": "8/12 と 4/12 コラム"}}, "column_right_size": {"label": "コラムのサイズ", "info": "0 - オ<PERSON><PERSON>"}, "header__4": {"content": "縦メニューのみ(ヘッダータイプ #5)"}, "enable_width_limit": {"label": "幅の制限を有効化", "info": "ページコンテナ幅を自動的に制限します。"}, "width_limit": {"label": "制限幅"}}}, "megamenu_promobox": {"name": "メガメニュー -> プロモボックス", "settings": {"title": {"label": "タイトル"}, "header__1": {"content": "一般設定"}, "url": {"label": "バナーのリンク先URL"}, "header__2": {"content": "画像"}, "image": {"label": "画像", "info": "推奨サイズ 1440x550 px"}, "image_size": {"label": "画像サイズ", "info": "レイジーロードが有効の場合、このオプションは適用されません。"}, "color_image_mask": {"label": "画像マスク"}, "image_mask_opacity": {"label": "画像マスクの透明度"}, "header__3": {"content": "テキスト"}, "paragraph__1": {"content": "表示したくない場合はテキストラインまたはボタンテキストの値を空欄にしてください。"}, "paragraph__2": {"content": "'<br>'のHTMLタグで改行できます。"}, "text_line_1": {"label": "テキストライン #1"}, "text_line_2": {"label": "テキストライン #2"}, "text_line_3": {"label": "テキストライン #3"}, "style": {"label": "カラースタイル", "option__1": {"label": "スタイル #1"}, "option__2": {"label": "スタイル #2"}, "option__3": {"label": "スタイル #3"}, "option__4": {"label": "スタイル #4"}, "option__5": {"label": "スタイル #5"}, "option__6": {"label": "スタイル #6"}, "option__7": {"label": "スタイル #7"}, "option__8": {"label": "スタイル #8"}}, "header__4": {"content": "ボタン #1"}, "button_1": {"label": "ボタンテキスト"}, "button_1_url": {"label": "ボタンのリンク先URL"}, "color_button_type_1": {"label": "ボタンスタイル", "option__1": {"label": "デフォルト"}, "option__2": {"label": "セカンダリ"}, "option__3": {"label": "反転"}, "option__4": {"label": "クリーン"}, "option__5": {"label": "透明デフォルト"}, "option__6": {"label": "透明セカンダリ"}, "option__7": {"label": "透明反転"}, "option__8": {"label": "透明クリーン"}}, "header__5": {"content": "ボタン #2"}, "button_2": {"label": "ボタンテキスト"}, "button_2_url": {"label": "ボタンのURL"}, "color_button_type_2": {"label": "ボタンスタイル", "option__1": {"label": "デフォルト"}, "option__2": {"label": "セカンダリ"}, "option__3": {"label": "反転"}, "option__4": {"label": "クリーン"}, "option__5": {"label": "透明デフォルト"}, "option__6": {"label": "透明セカンダリ"}, "option__7": {"label": "透明反転"}, "option__8": {"label": "透明クリーン"}}, "header__6": {"content": "その他のコンテンツ"}, "list_1": {"label": "リスト", "info": "リストのあるページを選択"}, "custom_html": {"label": "カスタムHTML", "info": "テキストをページコンテンツに置き換えます。"}, "header__7": {"content": "レイアウト"}, "type": {"label": "タイプ", "option__1": {"label": "クリーン - テキストなし画像"}, "option__2": {"label": "クリーン - ボーダー枠付き"}, "option__3": {"label": "クリーン - ホバー時にボーダー"}, "option__4": {"label": "テキスト v1"}, "option__5": {"label": "テキスト v2"}, "option__6": {"label": "テキスト v3"}, "option__7": {"label": "テキスト v4"}, "option__8": {"label": "テキスト v5"}, "option__9": {"label": "テキスト v6"}, "option__10": {"label": "テキスト v7"}, "option__11": {"label": "テキスト v8"}, "option__12": {"label": "テキスト v9"}, "option__13": {"label": "テキスト v10"}, "option__14": {"label": "タイプ #1 v1 (画像の上にテキスト)"}, "option__15": {"label": "タイプ #1 v2"}, "option__16": {"label": "タイプ #1 v3"}, "option__17": {"label": "タイプ #1 v4"}, "option__18": {"label": "タイプ #1 v5"}, "option__19": {"label": "タイプ #1 v6"}, "option__20": {"label": "タイプ #1 v7"}, "option__21": {"label": "タイプ #1 v8"}, "option__22": {"label": "タイプ #1 v9"}, "option__23": {"label": "タイプ #1 v10"}, "option__24": {"label": "タイプ #1 v11"}, "option__25": {"label": "タイプ #1 v12"}, "option__26": {"label": "タイプ #1 v13"}, "option__27": {"label": "タイプ #1 v14"}, "option__28": {"label": "タイプ #1 v15"}, "option__29": {"label": "タイプ #1 v16"}, "option__30": {"label": "タイプ #1 v17"}, "option__31": {"label": "タイプ #1 背景付き v1"}, "option__32": {"label": "タイプ #1 背景付き v2"}, "option__33": {"label": "タイプ #1 背景付き v3"}, "option__34": {"label": "タイプ #1 背景付き v4"}, "option__35": {"label": "タイプ #1 カーテン付き v1"}, "option__36": {"label": "タイプ #1 カーテン付き v2"}, "option__37": {"label": "タイプ #1 カーテン付き v3"}, "option__38": {"label": "タイプ #2 v1 (画像下にテキスト)"}, "option__39": {"label": "タイプ #2 v2"}, "option__40": {"label": "タイプ #2 v3"}, "option__41": {"label": "タイプ #2 v4"}, "option__42": {"label": "タイプ #2 v5"}, "option__43": {"label": "タイプ #2 v6"}, "option__44": {"label": "タイプ #2 v7"}, "option__45": {"label": "タイプ #2 v8"}, "option__46": {"label": "タイプ #2 v9"}, "option__47": {"label": "タイプ #2 v10"}, "option__48": {"label": "タイプ #2 v11"}, "option__49": {"label": "タイプ #2 v12"}, "option__50": {"label": "タイプ #2 v13"}, "option__51": {"label": "タイプ #3 (リボンボタン)"}, "option__52": {"label": "タイプ #4 (アニメーション付きリボンボタン)"}}, "content_position": {"label": "コンテンツの位置 (デスクトップのみ)", "info": "タイプ #1 (画像上にテキスト)に適用", "option__1": {"label": "中央"}, "option__2": {"label": "中央左"}, "option__3": {"label": "中央右"}, "option__4": {"label": "中央上"}, "option__5": {"label": "左上"}, "option__6": {"label": "右上"}, "option__7": {"label": "中央下"}, "option__8": {"label": "左下"}, "option__9": {"label": "右下"}}, "content_align": {"label": "コンテンツの水平調整", "option__1": {"label": "左"}, "option__2": {"label": "中央"}, "option__3": {"label": "右"}}, "header__8": {"content": "アニメーション"}, "animation_to": {"label": "ホバー時の画像移動方向設定", "option__1": {"label": "なし"}, "option__2": {"label": "中央"}, "option__3": {"label": "左上"}, "option__4": {"label": "上"}, "option__5": {"label": "右上"}, "option__6": {"label": "右"}, "option__7": {"label": "右下"}, "option__8": {"label": "下"}, "option__9": {"label": "左下"}, "option__10": {"label": "左"}}, "animation_from": {"label": "画像の初期位置設定", "option__1": {"label": "なし"}, "option__2": {"label": "中央"}, "option__3": {"label": "左上"}, "option__4": {"label": "上"}, "option__5": {"label": "右上"}, "option__6": {"label": "右"}, "option__7": {"label": "右下"}, "option__8": {"label": "下"}, "option__9": {"label": "左下"}, "option__10": {"label": "左"}}, "animation_opacity": {"label": "画像の透明化", "option__1": {"label": "なし"}, "option__2": {"label": "初期&ホバー"}, "option__3": {"label": "初期"}, "option__4": {"label": "ホバー"}}, "header__9": {"content": "カスタム"}, "customization_class": {"label": "追加CSSクラス", "info": "CSSクラスを加えるにはユーザーマニュアルを参照してください。こちらは高度な設定です。[追加CSSクラス](https://misell-manual.wraptas.site/theme-settings/developer)"}}}, "megamenu_brands": {"name": "メガメニュー -> ブランド", "settings": {"title": {"label": "タイトル"}, "url": {"label": "タイトルのURL"}, "header__1": {"content": "一般設定"}, "size_of_images": {"label": "画像のサイズ", "option__1": {"label": "1行に1画像"}, "option__2": {"label": "1行に2画像"}, "option__3": {"label": "1行に3画像"}, "option__4": {"label": "1行に4画像"}, "option__5": {"label": "1行に5画像"}, "option__6": {"label": "1行に6画像"}, "option__7": {"label": "1行に12画像"}}, "header__2": {"content": "画像"}, "image_1": {"label": "画像 #1"}, "image_size_1": {"label": "画像サイズ #1", "info": "レイジーロードが有効の場合、このオプションは適用されません。"}, "url_1": {"label": "URL #1"}, "image_2": {"label": "画像 #2"}, "image_size_2": {"label": "画像サイズ #2"}, "url_2": {"label": "URL #2"}, "image_3": {"label": "画像 #3"}, "image_size_3": {"label": "画像サイズ #3"}, "url_3": {"label": "URL #3"}, "image_4": {"label": "画像 #4"}, "image_size_4": {"label": "画像サイズ #4"}, "url_4": {"label": "URL #4"}, "image_5": {"label": "画像 #5"}, "image_size_5": {"label": "画像サイズ #5"}, "url_5": {"label": "URL #5"}, "image_6": {"label": "画像 #6"}, "image_size_6": {"label": "画像サイズ #6"}, "url_6": {"label": "URL #6"}, "image_7": {"label": "画像 #7"}, "image_size_7": {"label": "画像サイズ #7"}, "url_7": {"label": "URL #7"}, "image_8": {"label": "画像 #8"}, "image_size_8": {"label": "画像サイズ #8"}, "url_8": {"label": "URL #8"}, "image_9": {"label": "画像 #9"}, "image_size_9": {"label": "画像サイズ #9"}, "url_9": {"label": "URL #9"}, "image_10": {"label": "画像 #10"}, "image_size_10": {"label": "画像サイズ #10"}, "url_10": {"label": "URL #10"}}}, "megamenu_products": {"name": "メガメニュー -> 商品", "settings": {"title": {"label": "タイトル"}, "header__1": {"content": "一般設定"}, "collection": {"label": "コレクション"}, "products_per_row": {"label": "1行に表示する商品数", "option__1": {"label": "1"}, "option__2": {"label": "2"}, "option__3": {"label": "3"}, "option__4": {"label": "4"}, "option__5": {"label": "6"}, "option__6": {"label": "12"}}, "products_rows": {"label": "行数"}}}, "megamenu_custom_html": {"name": "メガメニュー -> カスタムHTML", "settings": {"title": {"label": "タイトル"}, "url": {"label": "タイトルのURL"}, "header__1": {"content": "一般設定"}, "page_content": {"label": "ページコンテンツ"}}}, "megamenu_subscription": {"name": "メガメニュー -> メルマガ購読", "settings": {"title": {"label": "タイトル"}, "url": {"label": "タイトルのURL"}, "header__1": {"content": "一般設定"}, "placeholder": {"label": "プレースホルダー"}, "button_text": {"label": "ボタンテキスト"}}}, "menu": {"name": "アドバンスメニュー(4階層目)", "settings": {"for_item": {"label": "アイテム", "info": "例:ショップ"}, "menu": {"label": "メニュー"}}}, "colorize": {"name": "カラー設定", "settings": {"header__1": {"content": "ファーストライン"}, "line_2_bg": {"label": "背景"}, "line_2_bd": {"label": "ボーダー"}, "btns_line_2_d_c": {"label": "ボタン"}, "btns_line_2_d_h_c": {"label": "ボタン - ホバー時"}, "header__2": {"content": "セカンドライン"}, "paragraph__1": {"content": "ヘッダータイプ #1, #2, #5, #6, #7"}, "line_1_bg": {"label": "背景"}, "line_1_bd": {"label": "ボーダー"}, "btns_line_1_d_c": {"label": "ボタン"}, "btns_line_1_d_h_c": {"label": "ボタン - ホバー時"}, "search_btn_type": {"label": "検索ボタンのタイプ", "option__1": {"label": "オート"}, "option__2": {"label": "デフォルト"}, "option__3": {"label": "反転"}, "option__4": {"label": "セカンダリ"}, "option__5": {"label": "クリーン"}}, "header__3": {"content": "モバイル"}, "m_bg": {"label": "背景"}, "m_bd": {"label": "ボーダー"}, "btns_m_c": {"label": "ボタン"}, "header__4": {"content": "スティッキーヘッダー(常時固定表示ヘッダー)"}, "menu_sticky_c": {"label": "メニュー"}, "menu_sticky_h_c": {"label": "ホバーメニュー"}, "btns_sticky_c": {"label": "ボタン"}, "btns_sticky_h_c": {"label": "ボタン - ホバー時"}, "header__5": {"content": "サードライン"}, "paragraph__2": {"content": "ヘッダータイプ #5, #6, #7"}, "line_additional_bg": {"label": "背景"}, "line_additional_bd": {"label": "ボーダー"}, "line_additional_c": {"label": "テキスト"}, "line_additional_highlighted_buttons_c": {"label": "ハイライト テキストとボタン"}, "line_additional_h_c": {"label": "テキスト - ホバー時"}, "header__6": {"content": "インフォーメーションラインブロック"}, "info_line_bg": {"label": "背景"}, "info_line_bd": {"label": "ボーダー"}, "info_line_c": {"label": "テキスト"}, "info_line_media_c": {"label": "SNSアイコン"}, "info_line_media_h_c": {"label": "SNSアイコン - ホバー時"}, "header__7": {"content": "アナウンスメントバーのブロック"}, "tape_bg": {"label": "背景"}, "tape_bd": {"label": "ボーダー"}, "tape_c": {"label": "テキスト"}, "tape_btn_close_c": {"label": "閉じるボタン"}, "header__8": {"content": "メニュー"}, "menu_c": {"label": "テキスト"}, "menu_h_c": {"label": "テキスト - ホバー時"}, "menu_h_bg": {"label": "背景 - ホバー時"}, "header__9": {"content": "ドロップダウンメニュー"}, "menu_title_c": {"label": "タイトルテキスト"}, "menu_list_c": {"label": "リストテキスト"}, "menu_list_h_c": {"label": "リストテキスト - ホバー時"}, "menu_list_h_bg": {"label": "リスト背景 - ホバー時"}, "header__10": {"content": "縦メニュー"}, "menu_vertical_button_bg": {"label": "ボタン背景"}, "menu_vertical_button_c": {"label": "ボタンテキスト"}, "menu_vertical_d_bg": {"label": "メニュー背景"}, "menu_vertical_d_bd": {"label": "メニューボーダー"}, "menu_vertical_d_c": {"label": "メニューテキスト"}, "menu_vertical_h_c": {"label": "メニューテキスト - ホバー時"}}}}}, "builder": {"name": "ビルダー", "settings": {"paragraph__1": {"content": "ビルダーセクションでは次のブロックを加えることができます。プロモボックス(バナー)・スリックスライダー・レボリューションスライダー・SNS風画像・商品コラム・カスタムHTML。"}, "title": {"label": "タイトル"}, "container": {"label": "コンテンツ幅", "option__1": {"label": "フル幅"}, "option__2": {"label": "ボックス幅"}}, "header__1": {"content": "コラム設定"}, "size_of_columns": {"label": "コラムのサイズ", "option__1": {"label": "1行に1アイテム"}, "option__2": {"label": "1行に2アイテム"}, "option__3": {"label": "1行に3アイテム"}, "option__4": {"label": "1行に4アイテム"}, "option__5": {"label": "1行に5アイテム"}, "option__6": {"label": "1行に6アイテム"}}, "size_of_columns_mobile": {"label": "モバイルでのコラムのサイズ", "option__1": {"label": "1行に1アイテム"}, "option__2": {"label": "1行に2アイテム"}}, "margins_for_columns": {"label": "コラムの余白", "info": "括弧()内は、デスクトップとモバイルの幅を表しています。", "option__1": {"label": "なし"}, "option__2": {"label": "プリセット #1 (15px, 10px)"}, "option__3": {"label": "プリセット #2 (60px, 45px)"}, "option__4": {"label": "プリセット #3 (30px, 30px)"}, "option__5": {"label": "プリセット #4 (45px, 30px)"}, "option__6": {"label": "プリセット #5 (40px, 35px)"}}, "align_of_columns": {"label": "コラムの位置", "option__1": {"label": "左上"}, "option__2": {"label": "中央上"}, "option__3": {"label": "右上"}, "option__4": {"label": "中央左"}, "option__5": {"label": "中央"}, "option__6": {"label": "中央右"}, "option__7": {"label": "左下"}, "option__8": {"label": "中央下"}, "option__9": {"label": "右下"}, "option__10": {"label": "左 - 高さを揃える"}, "option__11": {"label": "中央 - 高さを揃える"}, "option__12": {"label": "右 - 高さを揃える"}}, "disable_column_paddings": {"label": "コラムの余白を無効化"}, "precedence": {"label": "モバイルでのコンテンツの順序調整", "info": "例:1,3,2"}, "header__2": {"content": "カスタム"}, "customization_class": {"label": "追加CSSクラス", "info": "CSSクラスを加えるにはユーザーマニュアルを参照してください。こちらは高度な設定です。[追加CSSクラス](https://misell-manual.wraptas.site/theme-settings/developer)"}, "header__3": {"content": "ビデオチュートリアル・ユーザーマニュアル"}, "paragraph__2": {"content": "[ビルダー（動画）](https://youtu.be/m9XV85Rkxhc)"}, "paragraph__3": {"content": "[ビルダー](https://misell-manual.wraptas.site/sections/builder)"}}, "blocks": {"promobox": {"name": "プロモボックス(バナー)", "settings": {"header__1": {"content": "一般設定"}, "url": {"label": "バナーのリンク先URL"}, "header__2": {"content": "画像"}, "image": {"label": "画像", "info": "推奨サイズ 1440x550 px"}, "image_size": {"label": "画像サイズ", "info": "画像の画質を調整します。ただしレイジーロードの設定が有効の場合、このオプションは適用されません。"}, "image_mobile": {"label": "モバイルの画像", "info": "推奨サイズ 540x550 px"}, "image_mobile_size": {"label": "モバイルの画像サイズ", "info": "画像の画質を調整します。ただしレイジーロードの設定が有効の場合、このオプションは適用されません。"}, "image_position_x": {"label": "モバイルでの画像の位置調整 (水平軸)", "info": "50%で中央・0%方向で左に・100%方向で右に移動します。｢画像の高さに応じる｣のオプション時には適用されません。"}, "color_image_mask": {"label": "画像マスク"}, "image_mask_opacity": {"label": "画像マスクの透明度"}, "header__3": {"content": "テキスト"}, "paragraph__1": {"content": "表示したくない場合はテキストラインまたはボタンテキストの値を空欄にしてください。"}, "paragraph__2": {"content": "'<br>'のHTMLタグで改行できます。"}, "text_line_1": {"label": "テキストライン #1"}, "text_line_2": {"label": "テキストライン #2"}, "text_line_3": {"label": "テキストライン #3"}, "style": {"label": "カラースタイル", "option__1": {"label": "スタイル #1"}, "option__2": {"label": "スタイル #2"}, "option__3": {"label": "スタイル #3"}, "option__4": {"label": "スタイル #4"}, "option__5": {"label": "スタイル #5"}, "option__6": {"label": "スタイル #6"}, "option__7": {"label": "スタイル #7"}, "option__8": {"label": "スタイル #8"}}, "header__4": {"content": "ボタン #1"}, "button_1": {"label": "ボタンテキスト"}, "button_1_url": {"label": "ボタンのリンク先URL"}, "color_button_type_1": {"label": "ボタンスタイル", "option__1": {"label": "デフォルト"}, "option__2": {"label": "セカンダリ"}, "option__3": {"label": "反転"}, "option__4": {"label": "クリーン"}, "option__5": {"label": "透明デフォルト"}, "option__6": {"label": "透明セカンダリ"}, "option__7": {"label": "透明反転"}, "option__8": {"label": "透明クリーン"}}, "header__5": {"content": "ボタン #2"}, "button_2": {"label": "ボタンテキスト"}, "button_2_url": {"label": "ボタンのリンク先URL"}, "color_button_type_2": {"label": "ボタンスタイル", "option__1": {"label": "デフォルト"}, "option__2": {"label": "セカンダリ"}, "option__3": {"label": "反転"}, "option__4": {"label": "クリーン"}, "option__5": {"label": "透明デフォルト"}, "option__6": {"label": "透明セカンダリ"}, "option__7": {"label": "透明反転"}, "option__8": {"label": "透明クリーン"}}, "header__6": {"content": "他のコンテンツ"}, "list_1": {"label": "リスト", "info": "リストのあるページを選択してください。"}, "custom_html": {"label": "カスタムHTML", "info": "テキストをページコンテンツに置き換えます。"}, "header__7": {"content": "ビデオ"}, "video_external_url": {"label": "YouTube または Vimeo ビデオのURL"}, "video_mp4_url": {"label": "ビデオのURL", "info": "ビデオのアップロードは「Shopify管理画面 > コンテンツ > ファイル」から行ってください。"}, "video_autoplay": {"label": "ビデオの自動再生を有効化 *自動再生はブラウザのセキュリティにより制限される場合があります。"}, "video_controls": {"label": "ビデオコントロール"}, "header__8": {"content": "レイアウト"}, "type": {"label": "タイプ", "option__1": {"label": "クリーン - テキストなし画像"}, "option__2": {"label": "クリーン - ボーダー枠付き"}, "option__3": {"label": "クリーン - ホバー時にボーダー"}, "option__4": {"label": "テキスト v1"}, "option__5": {"label": "テキスト v2"}, "option__6": {"label": "テキスト v3"}, "option__7": {"label": "テキスト v4"}, "option__8": {"label": "テキスト v5"}, "option__9": {"label": "テキスト v6"}, "option__10": {"label": "テキスト v7"}, "option__11": {"label": "テキスト v8"}, "option__12": {"label": "テキスト v9"}, "option__13": {"label": "テキスト v10"}, "option__14": {"label": "タイプ #1 v1 (画像上にテキスト)"}, "option__15": {"label": "タイプ #1 v2"}, "option__16": {"label": "タイプ #1 v3"}, "option__17": {"label": "タイプ #1 v4"}, "option__18": {"label": "タイプ #1 v5"}, "option__19": {"label": "タイプ #1 v6"}, "option__20": {"label": "タイプ #1 v7"}, "option__21": {"label": "タイプ #1 v8"}, "option__22": {"label": "タイプ #1 v9"}, "option__23": {"label": "タイプ #1 v10"}, "option__24": {"label": "タイプ #1 v11"}, "option__25": {"label": "タイプ #1 v12"}, "option__26": {"label": "タイプ #1 v13"}, "option__27": {"label": "タイプ #1 v14"}, "option__28": {"label": "タイプ #1 v15"}, "option__29": {"label": "タイプ #1 v16"}, "option__30": {"label": "タイプ #1 v17"}, "option__31": {"label": "タイプ #1 背景付き v1"}, "option__32": {"label": "タイプ #1 背景付き v2"}, "option__33": {"label": "タイプ #1 背景付き v3"}, "option__34": {"label": "タイプ #1 背景付き v4"}, "option__35": {"label": "タイプ #1 カーテン付き v1"}, "option__36": {"label": "タイプ #1 カーテン付き v2"}, "option__37": {"label": "タイプ #1 カーテン付き v3"}, "option__38": {"label": "タイプ #2 v1 (画像下にテキスト)"}, "option__39": {"label": "タイプ #2 v2"}, "option__40": {"label": "タイプ #2 v3"}, "option__41": {"label": "タイプ #2 v4"}, "option__42": {"label": "タイプ #2 v5"}, "option__43": {"label": "タイプ #2 v6"}, "option__44": {"label": "タイプ #2 v7"}, "option__45": {"label": "タイプ #2 v8"}, "option__46": {"label": "タイプ #2 v9"}, "option__47": {"label": "タイプ #2 v10"}, "option__48": {"label": "タイプ #2 v11"}, "option__49": {"label": "タイプ #2 v12"}, "option__50": {"label": "タイプ #2 v13"}, "option__51": {"label": "タイプ #3 - リボンボタン"}, "option__52": {"label": "タイプ #4 - アニメーション付きリボンボタン"}}, "content_position": {"label": "コンテンツの位置 - デスクトップ", "info": "タイプ #1 (画像上にテキスト)に適用されます。", "option__1": {"label": "中央"}, "option__2": {"label": "中央左"}, "option__3": {"label": "中央右"}, "option__4": {"label": "中央上"}, "option__5": {"label": "左上"}, "option__6": {"label": "右上"}, "option__7": {"label": "中央下"}, "option__8": {"label": "左下"}, "option__9": {"label": "右下"}}, "content_align": {"label": "コンテンツの水平調整", "option__1": {"label": "左"}, "option__2": {"label": "中央"}, "option__3": {"label": "右"}}, "add_container": {"label": "コンテナを加える", "info": "タイプ #1 (画像上にテキスト)に適用されます。"}, "content_width": {"label": "コンテンツ幅", "info": "0 - オ<PERSON><PERSON>"}, "text_width": {"label": "テキストの最大幅", "info": "0 - オート。 1000px = 100%"}, "height": {"label": "高さ", "option__1": {"label": "画像に応じる"}, "option__2": {"label": "フルスクリーン"}, "option__3": {"label": "フルスクリーン - ヘッダーを除く"}, "option__4": {"label": "プリセット #1 (高さ 550px)"}, "option__5": {"label": "プリセット #2 (高さ 690px)"}, "option__6": {"label": "プリセット #3 (高さ 730px)"}, "option__7": {"label": "30% 幅"}, "option__8": {"label": "40% 幅"}, "option__9": {"label": "50% 幅"}, "option__10": {"label": "60% 幅"}, "option__11": {"label": "70% 幅"}, "option__12": {"label": "80% 幅"}, "option__13": {"label": "90% 幅"}, "option__14": {"label": "100% 幅 (正方形)"}, "option__15": {"label": "110% 幅"}, "option__16": {"label": "120% 幅"}, "option__17": {"label": "130% 幅"}, "option__18": {"label": "140% 幅"}, "option__19": {"label": "150% 幅"}}, "min_height": {"label": "高さの最小値", "info": "0 - オ<PERSON><PERSON>"}, "size_of_column": {"label": "コラムのサイズ", "option__1": {"label": "オート"}, "option__2": {"label": "1行に1アイテム"}, "option__3": {"label": "ラインから 3/4"}, "option__4": {"label": "ラインから 2/3"}, "option__5": {"label": "ライン<PERSON><PERSON> 7/12"}, "option__6": {"label": "ラインから 1/2"}, "option__7": {"label": "ライン<PERSON>ら 5/12"}, "option__8": {"label": "ラインから 1/3"}, "option__9": {"label": "ラインから 1/4"}, "option__10": {"label": "ラインから 1/6"}, "option__11": {"label": "ラインから 1/12"}}, "visible": {"label": "表示", "option__1": {"label": "デスクトップとモバイル"}, "option__2": {"label": "デスクトップのみ"}, "option__3": {"label": "モバイルのみ"}}, "header__9": {"content": "アニメーション"}, "parallax": {"label": "パララックス画像", "info": "ブロックの高さが｢画像に応じる｣でない場合、パララックスは選択された高さに応じて強さが決まります。高さが画像に対し大きすぎる場合はパララックスの効果が得られない場合があります。｢固定｣のオプションは iOS ではサポートされていません。", "option__1": {"label": "無効化"}, "option__2": {"label": "上へ"}, "option__3": {"label": "下へ"}, "option__4": {"label": "固定"}}, "animation_to": {"label": "ホバー時の画像移動方向設定", "option__1": {"label": "なし"}, "option__2": {"label": "中央"}, "option__3": {"label": "左上"}, "option__4": {"label": "上"}, "option__5": {"label": "右上"}, "option__6": {"label": "右"}, "option__7": {"label": "右下"}, "option__8": {"label": "下"}, "option__9": {"label": "左下"}, "option__10": {"label": "左"}}, "animation_from": {"label": "画像の初期位置設定", "option__1": {"label": "なし"}, "option__2": {"label": "中央"}, "option__3": {"label": "左上"}, "option__4": {"label": "上"}, "option__5": {"label": "右上"}, "option__6": {"label": "右"}, "option__7": {"label": "右下"}, "option__8": {"label": "下"}, "option__9": {"label": "左下"}, "option__10": {"label": "左"}}, "animation_opacity": {"label": "画像の透明化", "option__1": {"label": "なし"}, "option__2": {"label": "初期&ホバー"}, "option__3": {"label": "初期"}, "option__4": {"label": "ホバー"}}, "animation_text": {"label": "テキストのアニメーション", "info": "ページ読み込み時のアニメーション。 ｢タイプ #1(画像上にテキスト)｣に適用されます。", "option__1": {"label": "なし"}, "option__2": {"label": "縮小"}, "option__3": {"label": "拡大"}, "option__4": {"label": "上へ移動"}, "option__5": {"label": "下へ移動"}, "option__6": {"label": "左へ移動"}, "option__7": {"label": "右へ移動"}}, "header__10": {"content": "カラー"}, "color_text_1": {"label": "テキスト #1"}, "color_text_2": {"label": "テキスト #2"}, "color_text_3": {"label": "テキスト #3"}, "color_text_bg": {"label": "テキスト背景"}, "color_curtain_bg": {"label": "カーテン背景"}, "header__11": {"content": "カスタム"}, "customization_class": {"label": "追加CSSクラス", "info": "CSSクラスを加えるにはユーザーマニュアルを参照してください。こちらは高度な設定です。[追加CSSクラス](https://misell-manual.wraptas.site/theme-settings/developer)"}}}, "slick_slider": {"name": "スリックスライダー", "settings": {"header__1": {"content": "レイアウト"}, "height": {"label": "高さ", "option__1": {"label": "画像に応じる"}, "option__2": {"label": "フル幅"}, "option__3": {"label": "フルスクリーン - ヘッダーを除く"}, "option__4": {"label": "プリセット #1 (高さ 550px)"}, "option__5": {"label": "プリセット #2 (高さ 690px)"}, "option__6": {"label": "プリセット #3 (高さ 730px)"}, "option__7": {"label": "30% 幅"}, "option__8": {"label": "40% 幅"}, "option__9": {"label": "50% 幅"}, "option__10": {"label": "60% 幅"}, "option__11": {"label": "70% 幅"}, "option__12": {"label": "80% 幅"}, "option__13": {"label": "90% 幅"}, "option__14": {"label": "100% 幅 (正方形)"}, "option__15": {"label": "110% 幅"}, "option__16": {"label": "120% 幅"}, "option__17": {"label": "130% 幅"}, "option__18": {"label": "140% 幅"}, "option__19": {"label": "150% 幅"}}, "min_height": {"label": "Min height", "info": "0 - auto detect"}, "size_of_column": {"label": "コラムのサイズ", "option__1": {"label": "オート"}, "option__2": {"label": "1行に1アイテム"}, "option__3": {"label": "ラインから 3/4"}, "option__4": {"label": "ラインから 2/3"}, "option__5": {"label": "ライン<PERSON><PERSON> 7/12"}, "option__6": {"label": "ラインから 1/2"}, "option__7": {"label": "ライン<PERSON>ら 5/12"}, "option__8": {"label": "ラインから 1/3"}, "option__9": {"label": "ラインから 1/4"}, "option__10": {"label": "ラインから 1/6"}, "option__11": {"label": "ラインから 1/12"}}, "arrows": {"label": "矢印表示"}, "bullets": {"label": "ブレット表示"}, "speed": {"label": "スピード (秒)"}}}, "slick_slide": {"name": "スライド - スリックスライダー", "settings": {"header__1": {"content": "一般設定"}, "url": {"label": "スライドのURL"}, "header__2": {"content": "画像"}, "image": {"label": "画像", "info": "推奨サイズ 1440x550 px"}, "image_size": {"label": "画像サイズ", "info": "画像の画質を調整します。ただしレイジーロードの設定が有効の場合、このオプションは適用されません。"}, "image_mobile": {"label": "モバイルの画像", "info": "推奨サイズ 540x550 px"}, "image_mobile_size": {"label": "モバイルの画像サイズ", "info": "画像の画質を調整します。ただしレイジーロードの設定が有効の場合、このオプションは適用されません。"}, "image_position_x": {"label": "モバイルでの画像位置調整 - 水平軸", "info": "50%で中央・0%方向で左に・100%方向で右に移動します。｢画像の高さに応じる｣のオプション時には適用されません。"}, "color_image_mask": {"label": "画像マスク"}, "image_mask_opacity": {"label": "画像マスクの透明度"}, "header__3": {"content": "テキスト"}, "paragraph__1": {"content": "表示したくない場合はテキストラインまたはボタンテキストの値を空欄にしてください。"}, "paragraph__2": {"content": "'<br>'のHTMLタグで改行できます。"}, "text_line_1": {"label": "テキストライン #1"}, "text_line_2": {"label": "テキストライン #2"}, "text_line_3": {"label": "テキストライン #3"}, "style": {"label": "カラースタイル", "option__1": {"label": "スタイル #1"}, "option__2": {"label": "スタイル #2"}, "option__3": {"label": "スタイル #3"}, "option__4": {"label": "スタイル #4"}, "option__5": {"label": "スタイル #5"}, "option__6": {"label": "スタイル #6"}, "option__7": {"label": "スタイル #7"}, "option__8": {"label": "スタイル #8"}}, "header__4": {"content": "ボタン #1"}, "button_1": {"label": "ボタンテキスト"}, "button_1_url": {"label": "ボタンのリンク先URL"}, "color_button_type_1": {"label": "ボタンスタイル", "option__1": {"label": "デフォルト"}, "option__2": {"label": "セカンダリ"}, "option__3": {"label": "反転"}, "option__4": {"label": "クリーン"}, "option__5": {"label": "透明デフォルト"}, "option__6": {"label": "透明セカンダリ"}, "option__7": {"label": "透明反転"}, "option__8": {"label": "透明クリーン"}}, "header__5": {"content": "ボタン #2"}, "button_2": {"label": "ボタンテキスト"}, "button_2_url": {"label": "ボタンのリンク先URL"}, "color_button_type_2": {"label": "ボタンスタイル", "option__1": {"label": "デフォルト"}, "option__2": {"label": "セカンダリ"}, "option__3": {"label": "反転"}, "option__4": {"label": "クリーン"}, "option__5": {"label": "透明デフォルト"}, "option__6": {"label": "透明セカンダリ"}, "option__7": {"label": "透明反転"}, "option__8": {"label": "透明クリーン"}}, "header__6": {"content": "その他のコンテンツ"}, "custom_html": {"label": "カスタムHTML", "info": "テキストをページコンテンツに置き換えます。"}, "header__7": {"content": "ビデオ"}, "video_external_url": {"label": "Youtube または Vimeo のURL"}, "video_mp4_url": {"label": "ビデオのURL", "info": "ビデオのアップロードは「Shopify管理画面 > コンテンツ > ファイル」から行って下さい。"}, "video_autoplay": {"label": "ビデオの自動再生を有効化 *自動再生はブラウザのセキュリティにより制限される場合があります。"}, "video_controls": {"label": "ビデオのコントロール"}, "header__8": {"content": "レイアウト"}, "type": {"label": "タイプ", "option__1": {"label": "クリーン - テキストなし画像"}, "option__2": {"label": "タイプ #1 v1 (画像上にテキスト)"}, "option__3": {"label": "タイプ #1 v2"}, "option__4": {"label": "タイプ #1 v3"}, "option__5": {"label": "タイプ #1 v4"}, "option__6": {"label": "タイプ #1 v5"}, "option__7": {"label": "タイプ #1 v6"}, "option__8": {"label": "タイプ #1 v7"}, "option__9": {"label": "タイプ #1 v8"}, "option__10": {"label": "タイプ #1 v9"}, "option__11": {"label": "タイプ #1 v10"}, "option__12": {"label": "タイプ #1 v11"}, "option__13": {"label": "タイプ #1 v12"}, "option__14": {"label": "タイプ #1 v13"}, "option__15": {"label": "タイプ #1 v14"}, "option__16": {"label": "タイプ #1 v15"}, "option__17": {"label": "タイプ #1 v16"}, "option__18": {"label": "タイプ #1 v17"}, "option__19": {"label": "タイプ #1 背景付き v1"}, "option__20": {"label": "タイプ #1 背景付き v2"}, "option__21": {"label": "タイプ #1 背景付き v3"}, "option__22": {"label": "タイプ #1 背景付き v4"}, "option__23": {"label": "タイプ #1 カーテン付き v1"}, "option__24": {"label": "タイプ #1 カーテン付き v2"}, "option__25": {"label": "タイプ #1 カーテン付き v3"}}, "content_position": {"label": "コンテンツの位置 - デスクトップ", "info": "タイプ #1 (画像上にテキスト)にのみ適用されます。", "option__1": {"label": "中央"}, "option__2": {"label": "中央左"}, "option__3": {"label": "中央右"}, "option__4": {"label": "中央上"}, "option__5": {"label": "左上"}, "option__6": {"label": "右上"}, "option__7": {"label": "中央下"}, "option__8": {"label": "左下"}, "option__9": {"label": "右下"}}, "content_align": {"label": "コンテンツの水平調整", "option__1": {"label": "左"}, "option__2": {"label": "中央"}, "option__3": {"label": "右"}}, "add_container": {"label": "コンテナを加える", "info": "タイプ #1 (画像上にテキスト)にのみ適用されます。"}, "content_width": {"label": "コンテンツの最大幅"}, "text_width": {"label": "テキストの最大幅", "info": "0 - オート. 1000px = 100%"}, "header__9": {"content": "アニメーション"}, "animation_text": {"label": "テキストのアニメーション", "info": "ページ読み込み時のアニメーション。 ｢タイプ #1(画像上にテキスト)｣にのみ適用されます。", "option__1": {"label": "なし"}, "option__2": {"label": "縮小"}, "option__3": {"label": "拡大"}, "option__4": {"label": "上へ移動"}, "option__5": {"label": "下へ移動"}, "option__6": {"label": "左へ移動"}, "option__7": {"label": "右へ移動"}}, "header__10": {"content": "カラー"}, "color_text_1": {"label": "テキスト #1"}, "color_text_2": {"label": "テキスト #2"}, "color_text_3": {"label": "テキスト #3"}, "color_text_bg": {"label": "テキスト背景"}, "color_curtain_bg": {"label": "カーテン背景"}, "header__11": {"content": "カスタム"}, "customization_class": {"label": "追加CSSクラス", "info": "CSSクラスを加えるにはユーザーマニュアルを参照してください。こちらは高度な設定です。[追加CSSクラス](https://misell-manual.wraptas.site/theme-settings/developer)"}}}, "revolution_slider": {"name": "レボリューションスライダー", "settings": {"header__1": {"content": "レイアウト"}, "height": {"label": "高さ", "option__1": {"label": "プリセット #1 (高さ 550px)"}, "option__2": {"label": "フルスクリーン"}, "option__3": {"label": "フルスクリーン - ヘッダーを除く"}, "option__4": {"label": "プリセット #2 (高さ 690px)"}, "option__5": {"label": "プリセット #3 (高さ 730px)"}}, "min_height": {"label": "高さ最小値", "info": "0 - オ<PERSON><PERSON>"}, "image_height": {"label": "画像の高さ"}, "size_of_column": {"label": "コラムのサイズ", "option__1": {"label": "オート"}, "option__2": {"label": "1行に1アイテム"}, "option__3": {"label": "ラインから 3/4"}, "option__4": {"label": "ラインから 2/3"}, "option__5": {"label": "ライン<PERSON><PERSON> 7/12"}, "option__6": {"label": "ラインから 1/2"}, "option__7": {"label": "ライン<PERSON>ら 5/12"}, "option__8": {"label": "ラインから 1/3"}, "option__9": {"label": "ラインから 1/4"}, "option__10": {"label": "ラインから 1/6"}, "option__11": {"label": "ラインから 1/12"}}, "arrows": {"label": "矢印表示"}, "bullets": {"label": "ブレット表示"}, "delay": {"label": "遅らせる (秒)"}, "image_parallax": {"label": "パララックスの画像"}, "content_parallax": {"label": "パララックスのコンテンツ"}, "preload_spacer": {"label": "プリロードのスペース(余白)", "info": "ロード中ページのスライダー移動を除きます。"}}}, "revolution_slide": {"name": "スライド - レボリューションスライダー", "settings": {"header__1": {"content": "一般設定"}, "url": {"label": "スライドのURL"}, "header__2": {"content": "画像"}, "image": {"label": "画像", "info": "推奨サイズ 1440x550 px"}, "image_position_x": {"label": "モバイルでの水平位置調整", "info": "50%で中央・0%方向で左に・100%方向で右に移動します。｢画像の高さに応じる｣のオプション時には適用されません。"}, "header__3": {"content": "テキスト"}, "paragraph__1": {"content": "表示したくない場合はテキストラインまたはボタンテキストの値を空欄にしてください。"}, "paragraph__2": {"content": "'<br>'のHTMLタグで改行できます。"}, "text_line_1": {"label": "テキストライン #1"}, "text_line_2": {"label": "テキストライン #2"}, "text_line_3": {"label": "テキストライン #3"}, "style": {"label": "カラースタイル", "option__1": {"label": "スタイル #1"}, "option__2": {"label": "スタイル #2"}, "option__3": {"label": "スタイル #3"}, "option__4": {"label": "スタイル #4"}, "option__5": {"label": "スタイル #5"}, "option__6": {"label": "スタイル #6"}, "option__7": {"label": "スタイル #7"}, "option__8": {"label": "スタイル #8"}}, "header__4": {"content": "ボタン #1"}, "button_1": {"label": "ボタンテキスト"}, "button_1_url": {"label": "ボタンのリンク先URL"}, "color_button_type_1": {"label": "ボタンスタイル", "option__1": {"label": "デフォルト"}, "option__2": {"label": "セカンダリ"}, "option__3": {"label": "反転"}, "option__4": {"label": "クリーン"}, "option__5": {"label": "透明デフォルト"}, "option__6": {"label": "透明セカンダリ"}, "option__7": {"label": "透明反転"}, "option__8": {"label": "透明クリーン"}}, "header__5": {"content": "ボタン #2"}, "button_2": {"label": "ボタンテキスト"}, "button_2_url": {"label": "ボタンのリンク先URL"}, "color_button_type_2": {"label": "ボタンスタイル", "option__1": {"label": "デフォルト"}, "option__2": {"label": "セカンダリ"}, "option__3": {"label": "反転"}, "option__4": {"label": "クリーン"}, "option__5": {"label": "透明デフォルト"}, "option__6": {"label": "透明セカンダリ"}, "option__7": {"label": "透明反転"}, "option__8": {"label": "透明クリーン"}}, "header__6": {"content": "その他のコンテンツ"}, "custom_html": {"label": "カスタムHTML", "info": "テキストをページコンテンツに置き換えます。"}, "header__7": {"content": "ビデオ"}, "video_external_url": {"label": "Youtube または Vimeo のURL"}, "video_mp4_url": {"label": "ビデオのURL", "info": "ビデオのアップロードは「Shopify管理画面 > コンテンツ > ファイル」から行ってください。"}, "video_autoplay": {"label": "ビデオの自動再生を有効化 *自動再生はブラウザのセキュリティにより制限される場合があります。"}, "video_controls": {"label": "ビデオのコントロール"}, "header__8": {"content": "レイアウト"}, "type": {"label": "タイプ", "option__1": {"label": "クリーン - テキストなし画像"}, "option__2": {"label": "タイプ #1 v1 (画像上にテキスト)"}, "option__3": {"label": "タイプ #1 v2"}, "option__4": {"label": "タイプ #1 v3"}, "option__5": {"label": "タイプ #1 v4"}, "option__6": {"label": "タイプ #1 v5"}, "option__7": {"label": "タイプ #1 v6"}, "option__8": {"label": "タイプ #1 v7"}, "option__9": {"label": "タイプ #1 v8"}, "option__10": {"label": "タイプ #1 v9"}, "option__11": {"label": "タイプ #1 v10"}, "option__12": {"label": "タイプ #1 v11"}, "option__13": {"label": "タイプ #1 v12"}, "option__14": {"label": "タイプ #1 v13"}, "option__15": {"label": "タイプ #1 v14"}, "option__16": {"label": "タイプ #1 v15"}, "option__17": {"label": "タイプ #1 v16"}, "option__18": {"label": "タイプ #1 v17"}, "option__19": {"label": "タイプ #1 背景付き v1"}, "option__20": {"label": "タイプ #1 背景付き v2"}, "option__21": {"label": "タイプ #1 背景付き v3"}, "option__22": {"label": "タイプ #1 背景付き v4"}, "option__23": {"label": "タイプ #1 カーテン付き v1"}, "option__24": {"label": "タイプ #1 カーテン付き v2"}, "option__25": {"label": "タイプ #1 カーテン付き v3"}}, "content_position": {"label": "コンテンツの位置 - デスクトップ", "info": "タイプ #1 (画像上にテキスト)にのみ適用されます。", "option__1": {"label": "中央"}, "option__2": {"label": "中央左"}, "option__3": {"label": "中央右"}, "option__4": {"label": "中央上"}, "option__5": {"label": "左上"}, "option__6": {"label": "右上"}, "option__7": {"label": "中央下"}, "option__8": {"label": "左下"}, "option__9": {"label": "右下"}}, "content_align": {"label": "コンテンツの水平調整", "option__1": {"label": "左"}, "option__2": {"label": "中央"}, "option__3": {"label": "右"}}, "add_container": {"label": "コンテナを加える", "info": "タイプ #1 (画像上にテキスト)にのみ適用されます。"}, "content_width": {"label": "コンテンツ幅", "info": "0 - オ<PERSON><PERSON>"}, "text_width": {"label": "テキストの最大幅", "info": "0 - オート. 1000px = 100%"}, "header__9": {"content": "エフェクト"}, "delay": {"label": "ディレイ(遅らせる)", "info": "0にするとスライド個別のディレイが無効になります。"}, "slide_animation": {"label": "スライドのアニメーションの種類", "info": "アニメーションの名前は[こちら](https://www.themepunch.com/revsliderjquery-doc/slide-transitions/)コピーして貼り付けしてください。"}, "html_animation": {"label": "HTML レイヤーアニメーション", "info": "アニメーションのプリセットは[こちら](https://www.themepunch.com/revsliderjquery-doc/layer-transitions/)"}, "header__10": {"content": "カスタム"}, "customization_class": {"label": "追加CSSクラス", "info": "CSSクラスを加えるにはユーザーマニュアルを参照してください。こちらは高度な設定です。[追加CSSクラス](https://misell-manual.wraptas.site/theme-settings/developer)"}}}, "instagram": {"name": "SNS風画像", "settings": {"header__1": {"content": "一般設定"}, "limit": {"label": "画像数の制限", "info": "最大画像12 タグ無制限"}, "size_of_images": {"label": "画像の数", "option__1": {"label": "1行に1画像"}, "option__2": {"label": "1行に2画像"}, "option__3": {"label": "1行に3画像"}, "option__4": {"label": "1行に4画像"}, "option__5": {"label": "1行に5画像"}, "option__6": {"label": "1行に6画像"}, "option__7": {"label": "1行に6画像(モバイルでは4画像)"}, "option__8": {"label": "1行に12画像"}}, "inner_disable_paddings": {"label": "余白をなしにする"}, "fixed_height": {"label": "高さ", "info": "100では正方形画像(スクエア)・100より小さい場合は横長画像(ランドスケープ)・100より大きい場合は縦長画像(ポートレート)"}, "disable_lazyload": {"label": "レイジーロードを無効化"}, "header__2": {"content": "レイアウト"}, "size_of_column": {"label": "コラムのサイズ", "option__1": {"label": "オート"}, "option__2": {"label": "1行に1アイテム"}, "option__3": {"label": "ラインから 3/4"}, "option__4": {"label": "ラインから 2/3"}, "option__5": {"label": "ライン<PERSON><PERSON> 7/12"}, "option__6": {"label": "ラインから 1/2"}, "option__7": {"label": "ライン<PERSON>ら 5/12"}, "option__8": {"label": "ラインから 1/3"}, "option__9": {"label": "ラインから 1/4"}, "option__10": {"label": "ラインから 1/6"}, "option__11": {"label": "ラインから 1/12"}}}}, "instagram_block": {"name": "SNS風画像ブロック", "settings": {"image": {"label": "画像"}, "image_size": {"label": "画像サイズ", "info": "画像の画質を調整します。ただしレイジーロードの設定が有効の場合、このオプションは適用されません。"}, "format_pjpg": {"label": "PJPGフォーマットを有効化", "info": "透過画像には適用できません。"}, "disable_lazyload": {"label": "レイジーロードを無効化"}, "url": {"label": "URL"}}}, "products": {"name": "商品コラム", "settings": {"header__1": {"content": "一般設定"}, "title": {"label": "タイトル"}, "collection": {"label": "コレクション"}, "max_products": {"label": "コラムでの最大商品表示数"}, "header__2": {"content": "レイアウト"}, "size_of_column": {"label": "コラムのサイズ", "option__1": {"label": "オート"}, "option__2": {"label": "1行に1アイテム"}, "option__3": {"label": "ラインから 3/4"}, "option__4": {"label": "ラインから 2/3"}, "option__5": {"label": "ライン<PERSON><PERSON> 7/12"}, "option__6": {"label": "ラインから 1/2"}, "option__7": {"label": "ライン<PERSON>ら 5/12"}, "option__8": {"label": "ラインから 1/3"}, "option__9": {"label": "ラインから 1/4"}, "option__10": {"label": "ラインから 1/6"}, "option__11": {"label": "ラインから 1/12"}}}}, "custom_html": {"name": "カスタムHTML", "settings": {"header__1": {"content": "一般設定"}, "page_content": {"label": "ページコンテンツの選択"}, "header__2": {"content": "レイアウト"}, "size_of_column": {"label": "コラムのサイズ", "option__1": {"label": "オート"}, "option__2": {"label": "1行に1アイテム"}, "option__3": {"label": "ラインから 3/4"}, "option__4": {"label": "ラインから 2/3"}, "option__5": {"label": "ライン<PERSON><PERSON> 7/12"}, "option__6": {"label": "ラインから 1/2"}, "option__7": {"label": "ライン<PERSON>ら 5/12"}, "option__8": {"label": "ラインから 1/3"}, "option__9": {"label": "ラインから 1/4"}, "option__10": {"label": "ラインから 1/6"}, "option__11": {"label": "ラインから 1/12"}}}}, "vertical_menu_spacer": {"name": "縦メニューのスペース", "settings": {"menu": {"label": "メニュー"}}}}}, "information_line": {"name": "インフォーメーションライン", "settings": {"title": {"label": "タイトル"}, "type": {"label": "タイプ", "option__1": {"label": "タイプ #1"}, "option__2": {"label": "タイプ #2"}}, "style": {"label": "カラースタイル", "option__1": {"label": "スタイル #1"}, "option__2": {"label": "スタイル #2"}, "option__3": {"label": "スタイル #3"}, "option__4": {"label": "スタイル #4"}, "option__5": {"label": "スタイル #5"}}, "header__1": {"content": "ビデオチュートリアル・ユーザーマニュアル"}, "paragraph__1": {"content": "[MISEル（動画）](https://www.youtube.com/channel/UChCFvSvr3YgbYDcumJ_OqzQ)"}, "paragraph__2": {"content": "[インフォーメーションライン](https://misell-manual.wraptas.site/sections/information-line)"}}, "blocks": {"content": {"name": "コンテンツ", "settings": {"icon": {"label": "アイコンのスニペット名"}, "title": {"label": "タイトル"}, "paragraph": {"label": "パラグラフ文章"}, "url": {"label": "URL"}}}}}, "lookbook": {"name": "ルックブック", "settings": {"show_title": {"label": "タイトルを表示"}, "layout": {"label": "レイアウト", "option__1": {"label": "1"}, "option__2": {"label": "2"}}, "grid": {"label": "1行に表示する商品数 - エクストララージ画面", "option__1": {"label": "2"}, "option__2": {"label": "3"}, "option__3": {"label": "4"}}, "grid_responsive": {"label": "レスポンシブサイズ", "info": "例:'12,12,12,12,12', 1〜12の間の数字"}, "image_size": {"label": "画像サイズ"}}, "blocks": {"image": {"name": "画像", "settings": {"image": {"label": "画像"}}}, "product": {"name": "商品", "settings": {"product": {"label": "商品"}, "image": {"label": "商品画像"}, "title": {"label": "商品タイトル"}, "price": {"label": "商品価格", "info": "例:100.00"}, "compare_at_price": {"label": "商品の比較価格", "info": "例:120.00"}, "horizontal_position": {"label": "水平軸のポジション"}, "vertical_position": {"label": "垂直軸のポジション"}}}}}, "account": {"name": "Account"}, "activate_account": {"name": "Activate account"}, "addresses": {"name": "Addresses"}, "content_and_sidebar": {"name": "コンテンツとサイドバー", "settings": {"header__1": {"content": "一般設定"}, "show_information": {"label": "インフォーメーション表示"}, "show_image": {"label": "画像を表示"}, "show_content": {"label": "コンテンツを表示"}, "show_tags": {"label": "タグを表示"}, "show_social_share_buttons": {"label": "SNSシェアボタンを表示"}, "social_share_buttons_type": {"label": "SNSシェアボタンのタイプ", "option__1": {"label": "スモール"}, "option__2": {"label": "ラージ"}}, "show_navigation": {"label": "ナビゲーションの表示"}, "show_comments": {"label": "コメントを表示"}, "header__2": {"content": "コンテンツ"}, "paragraph__1": {"content": "[ブログ記事](https://misell-manual.wraptas.site/template/article)"}, "autoplay": {"label": "自動再生"}, "speed": {"label": "自動再生のスピード(秒)"}, "infinite": {"label": "無限ループ"}, "header__3": {"content": "ユーザーマニュアル"}, "paragraph__2": {"content": "｢テーマ設定 > ブログと記事｣の項目でポジションや表示の設定を行います。"}, "paragraph__3": {"content": "[コンテンツとサイドバー](https://misell-manual.wraptas.site/template/article-blocks)"}, "show_title": {"label": "ページタイトルを表示"}, "type": {"label": "タイプ", "option__1": {"label": "タイプ #1, 1行に1アイテム"}, "option__2": {"label": "タイプ #2, 1行に2アイテム"}, "option__3": {"label": "タイプ #2, 1行に3アイテム"}, "option__4": {"label": "タイプ #2, 1行に4アイテム"}, "option__5": {"label": "タイプ #2, 1行に6アイテム"}}, "masonry_enable": {"label": "自動整列配置を有効化 - masonary", "info": "タイプ #2のみ有効"}, "max_posts": {"label": "1ページに表示する記事の数"}, "show_button": {"label": "ボタンを表示"}, "show_empty_comments": {"label": "空のコメントを表示"}, "max_post_content_length": {"label": "記事コンテンツの最大表示文字数"}}, "blocks": {"slide": {"name": "スライドのコンテンツ", "settings": {"for_article": {"label": "記事"}, "image": {"label": "画像"}, "title": {"label": "タイトル"}}}, "categories": {"name": "サイドバーカテゴリー", "settings": {"title": {"label": "タイトル"}, "menu": {"label": "メニュー"}, "show_border": {"label": "ボーダーを表示"}, "header__1": {"content": "ユーザーマニュアル"}, "paragraph__1": {"content": "[サイドバー](https://misell-manual.wraptas.site/template/article-blocks)"}}}, "tags": {"name": "サイドバー - タグ", "settings": {"title": {"label": "タイトル"}, "show_border": {"label": "ボーダーを表示"}}}, "recents": {"name": "サイドバー - 最近の記事", "settings": {"title": {"label": "タイトル"}, "menu": {"label": "メニュー"}, "show_border": {"label": "ボーダーを表示"}}}, "html": {"name": "サイドバー - カスタムHTML", "settings": {"title": {"label": "タイトル"}, "page_content": {"label": "ページコンテンツの選択", "info": "デフォルトでは'Include Blog Sidebar'のページが挿入されます。"}, "show_border": {"label": "ボーダーを表示"}}}, "subscription": {"name": "サイドバー - メルマガ購読", "settings": {"title": {"label": "タイトル"}, "show_border": {"label": "ボーダーを表示"}, "paragraph": {"label": "パラグラフ文章"}, "button_text": {"label": "ボタンテキスト"}, "placeholder": {"label": "プレースホルダー"}}}}}, "cart": {"name": "カート"}, "product_grid": {"name": "商品グリッド", "settings": {"paragraph__1": {"content": "｢ヘッディング｣と｢バナービルダー｣のセクションをコレクションのページ加えることができます。｢セクションをを追加｣をクリックして｢コレクションページヘディング｣や｢コレクションバナービルダー｣のセクションを追加してください。"}, "paragraph__2": {"content": "注意:セクションの変更を加えた後、画面右上の｢保存する｣をクリックして変更を適用させてください。"}, "container": {"label": "コンテンツ幅", "option__1": {"label": "ボックス幅"}, "option__2": {"label": "フル幅"}}, "products_per_page": {"label": "1ページに表示する商品数"}, "header__1": {"content": "フィルター"}, "enable_default_filtering": {"label": "デフォルトフィルターを有効化", "info": "タグによるフィルターを有効にしたい場合、このオプションを無効にしてください。"}, "paragraph__3": {"content": "｢ブロックを追加｣によりフィルターのブロックを追加してフィルターを表示させてください。この設定は｢フィルター｣のブロックにのみ適用されます。[フィルターのカスタマイズ](/admin/menus)"}, "show_selected_filters_counter": {"label": "フィルターのカウントとリセットボタンを表示"}, "show_filter_product_count": {"label": "商品数のカウンターを表示"}, "hide_disabled_filters": {"label": "無効になっているフィルターを非表示", "info": "このオプションが無効の場合、選択できないフィルターは｢無効状態｣で表示されます。"}, "make_availability_as_rounded": {"label": "利用可能なフィルターは丸いチェックボックスで表示"}, "price_filter_type": {"label": "価格フィルターのタイプ", "info": "｢テーマ設定 > 通貨｣で｢従来の通貨変換方式を有効｣の場合、｢スライダー｣のオプションのみが利用できます。", "option__1": {"label": "スライダーとインプット"}, "option__2": {"label": "スライダー"}, "option__3": {"label": "インプット"}}, "header__2": {"content": "並べ替え方法"}, "sort_by_visibility": {"label": "並べ替えメニューの表示", "option__1": {"label": "デスクトップのみ"}, "option__2": {"label": "デスクトップとモバイル"}, "option__3": {"label": "非表示"}}, "paragraph__4": {"content": "コレクション設定からデフォルトの値が決まります。"}, "header__3": {"content": "インフォーメーション"}, "info_visibility": {"label": "インフォーメーションの表示", "option__1": {"label": "デスクトップのみ"}, "option__2": {"label": "デスクトップとモバイル"}, "option__3": {"label": "非表示"}}, "header__4": {"content": "グリッド"}, "view_grid_visibility": {"label": "グリッドボタンを表示", "option__1": {"label": "デスクトップのみ"}, "option__2": {"label": "デスクトップとモバイル"}, "option__3": {"label": "非表示"}}, "paragraph__5": {"content": "このボタンを押すことによりグリッドのレイアウトが変わります。また選択した後もブラウザの画面幅に応じて適切な大きさになるように自動的にレイアウトが変更されます。"}, "show_view_grid_1": {"label": "｢1行に1アイテム｣ボタンを表示", "info": "モバイルでのみ表示されます。"}, "show_view_grid_2": {"label": "｢1行に2アイテム｣ボタンを表示"}, "show_view_grid_3": {"label": "｢1行に3アイテム｣ボタンを表示"}, "show_view_grid_4": {"label": "｢1行に4アイテム｣ボタンを表示"}, "show_view_grid_6": {"label": "｢1行に6アイテム｣ボタンを表示"}, "show_view_grid_list": {"label": "｢リスト｣ボタンを表示", "info": "デスクトップのみで有効です。"}, "view_grid_list_design": {"label": "商品の｢リスト｣のデザイン", "option__1": {"label": "デザイン #1"}, "option__2": {"label": "デザイン #2"}}, "enable_grid_tooltip": {"label": "ツールチップを有効化", "info": "このオプションを有効にするには、｢テーマ設定 > アニメーション>ツールチップ｣の項目でもツールチップを有効にしてください。"}, "default_view_grid_xl": {"label": "デフォルトでの1行の表示商品数 - エクストララージ画面", "option__1": {"label": "2"}, "option__2": {"label": "3"}, "option__3": {"label": "4"}, "option__4": {"label": "6"}, "option__5": {"label": "リスト"}}, "default_view_grid_lg": {"label": "デフォルトでの1行の表示商品数 - ラージ画面", "option__1": {"label": "2"}, "option__2": {"label": "3"}, "option__3": {"label": "4"}, "option__4": {"label": "6"}, "option__5": {"label": "リスト"}}, "default_view_grid_md": {"label": "デフォルトでの1行の表示商品数 - ミディアム画面", "option__1": {"label": "2"}, "option__2": {"label": "3"}, "option__3": {"label": "4"}}, "default_view_grid_sm": {"label": "デフォルトでの1行の表示商品数 - スモール画面", "option__1": {"label": "1 - 中央"}, "option__2": {"label": "2"}, "option__3": {"label": "3"}}, "default_view_grid_xs": {"label": "デフォルトでの1行の表示商品数 - エクストラスモール画面", "option__1": {"label": "1 - 中央"}, "option__2": {"label": "2"}}, "header__5": {"content": "現在使用中のフィルター"}, "current_filters_visibility": {"label": "現在使用中のフィルターを表示する", "option__1": {"label": "モバイルのみ"}, "option__2": {"label": "デスクトップのみ"}, "option__3": {"label": "デスクトップとモバイル"}, "option__4": {"label": "非表示"}}, "header__6": {"content": "フィルターセクション"}, "uppercase_filter_title": {"label": "フィルターセクションのタイトルを大文字にする"}, "show_filter_border": {"label": "フィルターセクションのボーダーを表示"}, "header__7": {"content": "ビデオチュートリアル・ユーザーマニュアル"}, "paragraph__6": {"content": "[フィルターの追加（動画）](https://www.youtube.com/watch?v=SC-zA52wJ0g&t=57s)"}, "paragraph__7": {"content": "[コレクション](https://misell-manual.wraptas.site/template/product-grid)"}}, "blocks": {"collections": {"name": "コレクション", "settings": {"header__1": {"content": "セクション設定(サイドバー)"}, "title": {"label": "タイトル"}, "default_state": {"label": "デフォルトでの状態", "info": "｢テーマ設定 > コレクションページ｣の｢デスクトップのサイドバーの位置｣を｢トップと縦フィルター｣にしている場合、｢オープン｣または｢クローズ｣のオプションのみが設定できます。", "option__1": {"label": "オープン"}, "option__2": {"label": "閉じる"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "menu": {"label": "メニュー"}, "show_collection_product_count": {"label": "コレクションの商品数を表示"}}}, "current_filters": {"name": "現在使用中のフィルター", "settings": {"paragraph__1": {"content": "現在使用しているフィルターがない場合、このブロックは常に非表示となります。フィルター機能を使用している場合にのみ表示されます。"}, "header__1": {"content": "セクション設定"}, "title": {"label": "タイトル"}, "default_state": {"label": "デフォルトでの状態", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "show_group": {"label": "現在使用中のフィルターのグループ表示", "info": "複数のフィルターが機能している場合、グループが表示されます。例:色・価格"}}}, "filters": {"name": "フィルター", "settings": {"paragraph__1": {"content": "このブロックは｢商品グリッド>フィルター｣で｢デフォルトフィルター｣のオプションが有効の場合のみ表示されます。\n ｢Search & Discover｣アプリで設定したフィルターも、こちらのブロックを追加して表示させてください。"}, "paragraph__2": {"content": "[フィルターのカスタマイズ](/admin/menus)"}, "header__1": {"content": "セクション設定"}, "default_state": {"label": "デフォルトでの状態", "info": "｢テーマ設定 > コレクションページ｣の｢デスクトップのサイドバーの位置｣を｢トップと縦フィルター｣にしている場合、｢オープン｣または｢クローズ｣のオプションのみが設定できます。", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "layout": {"label": "レイアウト", "option__1": {"label": "1 コラム"}, "option__2": {"label": "2 コラム"}, "option__3": {"label": "3 コラム"}, "option__4": {"label": "行"}}, "max_column_size": {"label": "コラムの最大サイズ", "info": "この設定はレイアウト｢2コラム｣または｢3コラム｣の場合にのみ有効です。\n｢0｣では無制限となります。"}}}, "filter_settings": {"name": "フィルター設定", "settings": {"filter_name": {"label": "フィルターの名前"}, "header__1": {"content": "セクション設定"}, "title": {"label": "タイトル"}, "default_state": {"label": "デフォルトの状態", "info": "｢テーマ設定 > コレクションページ｣の｢デスクトップのサイドバーの位置｣を｢トップと縦フィルター｣にしている場合、｢オープン｣または｢クローズ｣のオプションのみが設定できます。", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "make_as_color": {"label": "カラーサークルで表示", "info": "オプションのバリエーション名がCSSの標準カラー名になっていることを確認してください。"}, "layout": {"label": "レイアウト", "option__1": {"label": "1 コラム"}, "option__2": {"label": "2 コラム"}, "option__3": {"label": "3 コム"}, "option__4": {"label": "行"}}, "max_column_size": {"label": "コラムの最大サイズ", "info": "この設定はレイアウトが｢2コラム｣または｢3コラム｣のときに有効です。｢0｣では無制限となります。"}}}, "tags": {"name": "タグ フィルター", "settings": {"paragraph__1": {"content": "このブロックを表示するには｢商品グリッド>デフォルトのフィルター｣を無効にしてください。"}, "paragraph__2": {"content": "注意:タグの連結機能はつかえません。複雑な絞り込みをしたい場合は通常のフィルターを使うことを推奨します。\nまた｢Search & Discovery｣アプリにてタグのフィルターを追加されている場合は、｢デフォルトのフィルター｣をご使用ください。"}, "header__1": {"content": "セクション設定"}, "title": {"label": "タイトル"}, "default_state": {"label": "デフォルトの状態", "info": "｢テーマ設定 > コレクションページ｣の｢デスクトップのサイドバーの位置｣を｢トップと縦フィルター｣にしている場合、｢オープン｣または｢クローズ｣のオプションのみが設定できます。", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "show_checkbox": {"label": "チェックボックスのエリア表示", "info": "このオプションが有効でない場合｢タグをカラーサークルにする｣のオプションは利用できません。"}, "make_as_color": {"label": "タグをカラーサークルに", "info": "このオプションを有効にしたい場合、｢タグリスト｣の中の名前を手動で入力する必要があります。例:Red"}, "tags_list": {"label": "タグリスト", "info": "改行を使ってタグを分けてください。空欄の場合は、有効なタグすべてが自動的に表示されます。"}, "layout": {"label": "レイアウト", "option__1": {"label": "1 コラム"}, "option__2": {"label": "2 コラム"}, "option__3": {"label": "3 コラム"}, "option__4": {"label": "行"}}, "max_column_size": {"label": "コラムの最大サイズ", "info": "この設定はレイアウトが｢2コラム｣または｢3コラム｣のときに有効です。｢0｣では無制限となります。"}, "for_collections": {"label": "適用するコレクション名", "info": "(,) コンマを使って分けてください。例:mens,womens,accessories デフォルトではすべてのコレクションが対象となります。"}}}, "products": {"name": "商品", "settings": {"paragraph__1": {"content": "｢テーマ設定 > コレクションページ｣の｢デスクトップのサイドバーの位置｣を｢トップと縦フィルター｣にしている場合、モバイルのみでの表示となります。"}, "header__1": {"content": "セクション設定"}, "title": {"label": "タイトル"}, "default_state": {"label": "デフォルトの状態", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "collection": {"label": "コレクション"}, "max_count": {"label": "最大表示数", "info": "0 で無制限"}, "product_type": {"label": "商品表示のタイプ", "option__1": {"label": "ショート"}, "option__2": {"label": "フル"}}}}, "custom_html": {"name": "カスタムHTML", "settings": {"paragraph__1": {"content": "｢テーマ設定 > コレクションページ｣の｢デスクトップのサイドバーの位置｣を｢トップと縦フィルター｣にしている場合、モバイルのみでの表示となります。"}, "header__1": {"content": "セクション設定"}, "title": {"label": "タイトル"}, "default_state": {"label": "デフォルトの状態", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "page_content": {"label": "ページコンテンツ", "info": "デフォルトでは'Include Collections Sidebar'が挿入されます。(ExImアプリでMisell-theme-page.zipがインストールされている場合)"}}}, "subscription": {"name": "メルマガ購読", "settings": {"paragraph__1": {"content": "｢テーマ設定 > コレクションページ｣の｢デスクトップのサイドバーの位置｣を｢トップと縦フィルター｣にしている場合、モバイルデバイスのみでの表示となります。"}, "header__1": {"content": "セクション設定"}, "title": {"label": "タイトル"}, "default_state": {"label": "デフォルトの状態", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "paragraph": {"label": "パラグラフ文章"}, "placeholder": {"label": "プレースホルダー"}, "button_text": {"label": "ボタンテキスト"}}}, "promobox": {"name": "プロモボックス(バナー)", "settings": {"header__1": {"content": "セクション設定"}, "title": {"label": "タイトル"}, "default_state": {"label": "デフォルトの状態", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "url": {"label": "バナーのリンク先URL"}, "header__3": {"content": "画像"}, "image": {"label": "画像", "info": "推奨サイズ 1440x550 px"}, "image_size": {"label": "画像サイズ", "info": "画像の画質を調整します。ただしレイジーロードの設定が有効の場合、このオプションは適用されません。"}, "color_image_mask": {"label": "画像マスク"}, "image_mask_opacity": {"label": "画像マスクの透明度"}, "header__4": {"content": "テキスト"}, "paragraph__1": {"content": "表示したくない場合は、テキストラインまたはボタンテキストの値を空欄にしてください。"}, "paragraph__2": {"content": "'<br>'のHTMLタグで改行できます。"}, "text_line_1": {"label": "テキストライン #1"}, "text_line_2": {"label": "テキストライン #2"}, "text_line_3": {"label": "テキストライン #3"}, "style": {"label": "カラースタイル", "option__1": {"label": "スタイル #1"}, "option__2": {"label": "スタイル #2"}, "option__3": {"label": "スタイル #3"}, "option__4": {"label": "スタイル #4"}, "option__5": {"label": "スタイル #5"}, "option__6": {"label": "スタイル #6"}, "option__7": {"label": "スタイル #7"}, "option__8": {"label": "スタイル #8"}}, "header__5": {"content": "ボタン #1"}, "button_1": {"label": "ボタンテキスト"}, "button_1_url": {"label": "ボタンのリンク先URL"}, "color_button_type_1": {"label": "ボタンスタイル", "option__1": {"label": "デフォルト"}, "option__2": {"label": "セカンダリ"}, "option__3": {"label": "反転"}, "option__4": {"label": "クリーン"}, "option__5": {"label": "透明デフォルト"}, "option__6": {"label": "透明セカンダリ"}, "option__7": {"label": "透明反転"}, "option__8": {"label": "透明クリーン"}}, "header__6": {"content": "ボタン #2"}, "button_2": {"label": "ボタンテキスト"}, "button_2_url": {"label": "ボタンのリンク先URL"}, "color_button_type_2": {"label": "ボタンスタイル", "option__1": {"label": "デフォルト"}, "option__2": {"label": "セカンダリ"}, "option__3": {"label": "反転"}, "option__4": {"label": "クリーン"}, "option__5": {"label": "透明デフォルト"}, "option__6": {"label": "透明セカンダリ"}, "option__7": {"label": "透明反転"}, "option__8": {"label": "透明クリーン"}}, "header__7": {"content": "その他のコンテンツ"}, "list_1": {"label": "リスト", "info": "リストのあるページを選択してください。"}, "custom_html": {"label": "カスタムHTML", "info": "テキストをページコンテンツに置き換えます。"}, "header__8": {"content": "ビデオ"}, "video_external_url": {"label": "YouTube または Vimeo のURL"}, "video_mp4_url": {"label": "ビデオURL", "info": "ビデオのアップロードは｢Shopify管理画面 > コンテンツ > ファイル｣から行ってください。"}, "video_autoplay": {"label": "ビデオの自動再生を有効化 *自動再生はブラウザのセキュリティにより制限される場合があります。"}, "video_controls": {"label": "ビデオコントロール"}, "header__9": {"content": "レイアウト"}, "type": {"label": "タイプ", "option__1": {"label": "クリーン - テキストなし画像"}, "option__2": {"label": "クリーン - ボーダー枠付き"}, "option__3": {"label": "クリーン - ホバー時にボーダー"}, "option__4": {"label": "テキスト v1"}, "option__5": {"label": "テキスト v2"}, "option__6": {"label": "テキスト v3"}, "option__7": {"label": "テキスト v4"}, "option__8": {"label": "テキスト v5"}, "option__9": {"label": "テキスト v6"}, "option__10": {"label": "テキスト v7"}, "option__11": {"label": "テキスト v8"}, "option__12": {"label": "テキスト v9"}, "option__13": {"label": "テキスト v10"}, "option__14": {"label": "タイプ #1 v1 (画像上にテキスト)"}, "option__15": {"label": "タイプ #1 v2"}, "option__16": {"label": "タイプ #1 v3"}, "option__17": {"label": "タイプ #1 v4"}, "option__18": {"label": "タイプ #1 v5"}, "option__19": {"label": "タイプ #1 v6"}, "option__20": {"label": "タイプ #1 v7"}, "option__21": {"label": "タイプ #1 v8"}, "option__22": {"label": "タイプ #1 v9"}, "option__23": {"label": "タイプ #1 v10"}, "option__24": {"label": "タイプ #1 v11"}, "option__25": {"label": "タイプ #1 v12"}, "option__26": {"label": "タイプ #1 v13"}, "option__27": {"label": "タイプ #1 v14"}, "option__28": {"label": "タイプ #1 v15"}, "option__29": {"label": "タイプ #1 v16"}, "option__30": {"label": "タイプ #1 v17"}, "option__31": {"label": "タイプ #1 背景付き v1"}, "option__32": {"label": "タイプ #1 背景付き v2"}, "option__33": {"label": "タイプ #1 背景付き v3"}, "option__34": {"label": "タイプ #1 背景付き v4"}, "option__35": {"label": "タイプ #1 カーテン付き v1"}, "option__36": {"label": "タイプ #1 カーテン付き v2"}, "option__37": {"label": "タイプ #1 カーテン付き v3"}, "option__38": {"label": "タイプ #2 v1 (画像下にテキスト)"}, "option__39": {"label": "タイプ #2 v2"}, "option__40": {"label": "タイプ #2 v3"}, "option__41": {"label": "タイプ #2 v4"}, "option__42": {"label": "タイプ #2 v5"}, "option__43": {"label": "タイプ #2 v6"}, "option__44": {"label": "タイプ #2 v7"}, "option__45": {"label": "タイプ #2 v8"}, "option__46": {"label": "タイプ #2 v9"}, "option__47": {"label": "タイプ #2 v10"}, "option__48": {"label": "タイプ #2 v11"}, "option__49": {"label": "タイプ #2 v12"}, "option__50": {"label": "タイプ #2 v13"}, "option__51": {"label": "タイプ #3 - リボンボタン"}, "option__52": {"label": "タイプ #4 - アニメーション付きリボンボタン"}}, "header__10": {"content": "アニメーション"}, "animation_to": {"label": "ホバー時の画像移動方向設定", "option__1": {"label": "なし"}, "option__2": {"label": "中央"}, "option__3": {"label": "左上"}, "option__4": {"label": "上"}, "option__5": {"label": "右上"}, "option__6": {"label": "右"}, "option__7": {"label": "右下"}, "option__8": {"label": "下"}, "option__9": {"label": "左下"}, "option__10": {"label": "左"}}, "animation_from": {"label": "画像の初期位置設定", "option__1": {"label": "なし"}, "option__2": {"label": "中央"}, "option__3": {"label": "左上"}, "option__4": {"label": "上"}, "option__5": {"label": "右上"}, "option__6": {"label": "右"}, "option__7": {"label": "右下"}, "option__8": {"label": "下"}, "option__9": {"label": "左下"}, "option__10": {"label": "左"}}, "animation_opacity": {"label": "画像の透明化", "option__1": {"label": "なし"}, "option__2": {"label": "初期 & ホバー"}, "option__3": {"label": "初期"}, "option__4": {"label": "ホバー"}}}}}}, "list_collections": {"name": "コレクションリスト", "settings": {"display_type": {"label": "表示するコレクションの選択", "option__1": {"label": "すべて"}, "option__2": {"label": "選択したもの"}}, "sort": {"label": "コレクションの整理", "option__1": {"label": "商品数 - 多い順"}, "option__2": {"label": "商品数 - 少ない順"}, "option__3": {"label": "アルファベット順 - A-Z"}, "option__4": {"label": "アルファベット順 - Z-A"}, "option__5": {"label": "日付順 - 古いものから"}, "option__6": {"label": "日付順 - 新しいものから"}, "option__7": {"label": "手動 - コレクションブロック順"}}, "show_products_count": {"label": "商品数の表示"}, "size_of_columns": {"label": "コラムのサイズ", "option__1": {"label": "1行に1アイテム"}, "option__2": {"label": "1行に2アイテム"}, "option__3": {"label": "1行に3アイテム"}, "option__4": {"label": "1行に4アイテム"}, "option__5": {"label": "1行に5アイテム"}, "option__6": {"label": "1行に6アイテム"}}, "size_of_columns_mobile": {"label": "モバイルでのコラムのサイズ", "option__1": {"label": "1行に1アイテム"}, "option__2": {"label": "1行に2アイテム"}}, "type": {"label": "タイプ", "option__1": {"label": "タイプ #1 - 画像の下にテキスト"}, "option__2": {"label": "タイプ #2 - リボンボタン"}, "option__3": {"label": "クリーン - テキストなし画像"}, "option__4": {"label": "クリーン - ボーダー枠付き"}, "option__5": {"label": "クリーン - ホバー時にボーダー"}}, "style": {"label": "カラースタイル", "option__1": {"label": "オート"}, "option__2": {"label": "スタイル #1"}, "option__3": {"label": "スタイル #2"}, "option__4": {"label": "スタイル #3"}, "option__5": {"label": "スタイル #4"}, "option__6": {"label": "スタイル #5"}, "option__7": {"label": "スタイル #6"}, "option__8": {"label": "スタイル #7"}, "option__9": {"label": "スタイル #8"}}, "masonry_enable": {"label": "自動整列配置を有効化(masonary)"}, "height": {"label": "画像の高さ", "option__1": {"label": "画像に応じる - オート"}, "option__2": {"label": "30% 幅"}, "option__3": {"label": "40% 幅"}, "option__4": {"label": "50% 幅"}, "option__5": {"label": "60% 幅"}, "option__6": {"label": "70% 幅"}, "option__7": {"label": "80% 幅"}, "option__8": {"label": "90% 幅"}, "option__9": {"label": "100% 幅 (正方形)"}, "option__10": {"label": "110% 幅"}, "option__11": {"label": "120% 幅"}, "option__12": {"label": "130% 幅"}, "option__13": {"label": "140% 幅"}, "option__14": {"label": "150% 幅"}}, "image_size": {"label": "画像サイズ", "info": "画像の画質の設定を行います。レイジーロードを有効にしている場合、この設定は適用されません。"}, "format_pjpg": {"label": "PJPGフォーマットを有効化", "info": "透過画像には適用されません。"}, "header__1": {"content": "アニメーション"}, "animation_to": {"label": "ホバー時の画像移動方向設定", "option__1": {"label": "なし"}, "option__2": {"label": "中央"}, "option__3": {"label": "左上"}, "option__4": {"label": "上"}, "option__5": {"label": "右上"}, "option__6": {"label": "右"}, "option__7": {"label": "右下"}, "option__8": {"label": "下"}, "option__9": {"label": "左下"}, "option__10": {"label": "左"}}, "animation_from": {"label": "画像の初期位置設定", "option__1": {"label": "なし"}, "option__2": {"label": "中央"}, "option__3": {"label": "左上"}, "option__4": {"label": "上"}, "option__5": {"label": "右上"}, "option__6": {"label": "右"}, "option__7": {"label": "右下"}, "option__8": {"label": "下"}, "option__9": {"label": "左下"}, "option__10": {"label": "左"}}, "animation_opacity": {"label": "画像の透明化", "option__1": {"label": "なし"}, "option__2": {"label": "初期"}, "option__3": {"label": "ホバー"}}}, "blocks": {"collection": {"name": "コレクション", "settings": {"for_collection": {"label": "コレクション"}, "image": {"label": "画像"}, "text_line_1": {"label": "テキストライン #1"}, "text_line_2": {"label": "テキストライン #2"}, "button_1": {"label": "ボタンテキスト"}}}}}, "login": {"name": "ログイン"}, "order": {"name": "オーダー"}, "policy_page": {"name": "ポリシーページ", "settings": {"page_type": {"label": "ページのタイプ", "option__1": {"label": "プライバシーポリシー"}, "option__2": {"label": "返金ポリシー"}, "option__3": {"label": "配送ポリシー"}, "option__4": {"label": "利用規約"}, "option__5": {"label": "ページコンテンツ"}}}}, "page": {"name": "ページ", "settings": {"show_title": {"label": "タイトルを表示"}, "show_content": {"label": "コンテンツを表示"}}}, "content": {"name": "コンテンツ", "settings": {"header__1": {"content": "メルマガ購読"}, "subscription_title": {"label": "タイトル"}, "subscription_paragraph": {"label": "パラグラフ文章", "info": "空欄の場合はメッセージは表示されません。"}, "subscription_placeholder": {"label": "プレースホルダー"}, "subscription_button_text": {"label": "ボタンテキスト"}, "header__2": {"content": "バナー"}, "image": {"label": "画像"}}, "blocks": {"social_media": {"name": "SNS(ソーシャルメディア)", "settings": {"show_facebook": {"label": "Facebook"}, "show_twitter": {"label": "X(Twitter)"}, "show_instagram": {"label": "Instagram"}, "show_pinterest": {"label": "Pinterest"}, "show_youtube": {"label": "YouTube"}, "show_behance": {"label": "<PERSON><PERSON><PERSON>"}, "show_skype": {"label": "Skype"}, "show_tiktok": {"label": "tiktok"}, "show_line": {"label": "LINE"}}}}}, "product_page": {"name": "商品ページ", "settings": {"header__1": {"content": "サイドバー"}, "gallery_size": {"label": "ギャラリーサイズ", "option__1": {"label": "4/12"}, "option__2": {"label": "5/12"}, "option__3": {"label": "6/12"}, "option__4": {"label": "7/12"}, "option__5": {"label": "8/12"}}, "sidebar": {"label": "サイドバーの位置", "info": "商品ページのサイドバーの設定は｢セクション > 商品ページ｣で行います。", "option__1": {"label": "オフ"}, "option__2": {"label": "左"}, "option__3": {"label": "右"}}, "enable_sticky_sidebar": {"label": "スティッキーサイドバーを有効化 - 常時固定サイドバー"}, "header__2": {"content": "タブ"}, "show_tabs": {"label": "タブを表示"}, "show_tab_description": {"label": "商品説明のタブを表示"}, "show_tab_reviews": {"label": "レビューのタブを表示", "info": "｢テーマ設定 > レビュー｣でレビューアプリの設定の確認を行ってください。"}, "scrolling_to_opened_tab": {"label": "スクロールでタブを開く", "info": "縦のタブ表示の場合のみ適用されます。"}, "tabs_type": {"label": "タブのタイプ", "info": "このセクションでは｢デフォルトタブ｣もしくは｢シートタブ｣を選択できます。 ｢シートタブ｣を選択した場合、レビューと商品説明のブロックはタブなしで表示されます。ただし、他のタブは表示されなくなります。", "option__1": {"label": "デフォルトのタブ"}, "option__2": {"label": "シートタブ"}}, "paragraph__1": {"content": "｢ブロックを追加｣によってタブやコンテンツをサイドバーに追加します。"}, "header__3": {"content": "ユーザーマニュアル"}, "paragraph__2": {"content": "[商品ページ](https://misell-manual.wraptas.site/template/product-template)"}}, "blocks": {"collections": {"name": "コレクション"}, "title": {"name": "タイトル"}, "reviews": {"name": "レビュー", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > レビュー｣の項目から詳細を設定できます。"}}}, "details": {"name": "詳細", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > 商品詳細｣の項目から詳細を設定できます。"}}}, "price": {"name": "価格", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > 価格｣の項目から詳細を設定できます。"}}}, "icon_with_text": {"name": "テキスト付きアイコン", "settings": {"style": {"label": "ブロックのスタイル", "option__1": {"label": "インライン"}, "option__2": {"label": "タイル"}}, "header__1": {"content": "セクション #1"}, "icon_svg_1": {"label": "SVGアイコン"}, "icon_image_1": {"label": "アイコン画像"}, "text_1": {"label": "テキスト"}, "header__2": {"content": "セクション #2"}, "icon_svg_2": {"label": "SVGアイコン"}, "icon_image_2": {"label": "アイコン画像"}, "text_2": {"label": "テキスト"}, "header__3": {"content": "セクション #3"}, "icon_svg_3": {"label": "SVGアイコン"}, "icon_image_3": {"label": "アイコン画像"}, "text_3": {"label": "テキスト"}, "header__4": {"content": "セクション #4"}, "icon_svg_4": {"label": "SVGアイコン"}, "icon_image_4": {"label": "アイコン画像"}, "text_4": {"label": "テキスト"}, "header__5": {"content": "セクション #5"}, "icon_svg_5": {"label": "SVGアイコン"}, "icon_image_5": {"label": "アイコン画像"}, "text_5": {"label": "テキスト"}, "header__6": {"content": "セクション #6"}, "icon_svg_6": {"label": "SVGアイコン"}, "icon_image_6": {"label": "アイコン画像"}, "text_6": {"label": "テキスト"}}}, "description": {"name": "商品説明", "settings": {"title": {"label": "タイトル"}, "content": {"label": "コンテンツ", "info": "カスタムの商品説明のためのフィールド。商品説明を表示させたい場合は空欄にしてください。"}}}, "text": {"name": "テキスト", "settings": {"content": {"label": "コンテンツ"}}}, "countdown": {"name": "タイムカウントダウン"}, "stock_countdown": {"name": "在庫カウントダウン", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > 在庫カウントダウン｣の項目から詳細を設定できます。"}}}, "delivery_countdown": {"name": "配送カウントダウン", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > 配送カウントダウン｣の項目から詳細を設定できます。"}}}, "border": {"name": "ボーダー"}, "options": {"name": "オプション", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品 -> バリエーション｣の項目から詳細を設定できます。"}}}, "popups": {"name": "ポップアップ", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > ポップアップ｣の項目から詳細を設定できます。"}}}, "notes": {"name": "備考ノート"}, "buttons_quantity": {"name": "数量とカートに追加ボタン", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > ボタンとインプット｣の項目から詳細を設定できます。"}}}, "free_shipping": {"name": "送料無料のプログレスバー", "settings": {"paragraph__1": {"content": "｢テーマ設定 > カート > 送料無料のプログレスバー｣の項目から詳細を設定できます。"}}}, "pickup_availability": {"name": "店舗受取サービス", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > 店舗受取サービス｣の項目から詳細を設定できます。"}}}, "complementary_products": {"name": "付属商品", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > 付属商品｣の項目から詳細を設定できます。"}}}, "payments": {"name": "支払い情報"}, "social_share_buttons": {"name": "SNSシェアボタン", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > SNSシェアボタン｣の項目から詳細を設定できます。"}}}, "tab_custom": {"name": "カスタムコンテンツタブ", "settings": {"title": {"label": "タイトル"}, "content": {"label": "テキスト"}, "paragraph__1": {"content": "または挿入する｢ページ｣を選択"}, "page_content": {"label": "ページコンテンツの選択", "info": "デフォルトでは'Include Product Tab Custom HTML'のページが挿入されます。(ExImアプリでMisell-theme-page.zipがインストールされている場合)"}}}, "tab_custom_liquid": {"name": "カスタムLiquidタブ", "settings": {"title": {"label": "タイトル"}, "custom_liquid": {"label": "カスタム Liquid"}}}, "sidebar_custom_html": {"name": "サイドバーのカスタムHTML", "settings": {"page_content": {"label": "ページコンテンツを選択", "info": "デフォルトでは'Include Product Sidebar Custom HTML'のページが挿入されます。(ExImアプリでMisell-theme-page.zipがインストールされている場合)"}}}, "sidebar_products": {"name": "サイドバーの商品", "settings": {"title": {"label": "タイトル"}, "collection": {"label": "コレクション"}, "max_count": {"label": "最大表示数", "info": "0で無制限"}, "product_type": {"label": "商品表示のタイプ", "option__1": {"label": "ショート"}, "option__2": {"label": "フル"}}}}, "custom_liquid": {"name": "カスタム Liquid", "settings": {"custom_liquid": {"label": "カスタム Liquid", "info": "アプリ提供によるコードスニペットやその他Liquidコードの挿入が必要な場合こちらにコードを加えてください。*こちらは高度なカスタマイズとなります。詳細はアプリのサポート等にお問い合わせください。"}}}}}, "quick_view": {"name": "クイックビュー", "settings": {"gallery_size": {"label": "ギャラリーのサイズ", "option__1": {"label": "4/12"}, "option__2": {"label": "5/12"}, "option__3": {"label": "6/12"}, "option__4": {"label": "7/12"}, "option__5": {"label": "8/12"}}, "enable_sticky_sidebar": {"label": "スティッキーサイドバーを有効化 - 常時固定表示のサイドバー"}, "paragraph__1": {"content": "｢ブロックを追加｣でコンテンツを追加します。"}, "header__1": {"content": "ユーザーマニュアル"}, "paragraph__2": {"content": "[商品ページ](https://misell-manual.wraptas.site/template/quick-view)"}}, "blocks": {"collections": {"name": "コレクション"}, "title": {"name": "タイトル"}, "details": {"name": "商品詳細", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > 商品詳細｣の項目から詳細を設定できます。"}}}, "price": {"name": "価格", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > 価格｣の項目から詳細を設定できます。"}}}, "icon_with_text": {"name": "テキスト付きアイコン", "settings": {"style": {"label": "ブロックのスタイル", "option__1": {"label": "インライン"}, "option__2": {"label": "タイル"}}, "header__1": {"content": "セクション #1"}, "icon_svg_1": {"label": "SVGアイコン"}, "icon_image_1": {"label": "アイコン画像"}, "text_1": {"label": "テキスト"}, "header__2": {"content": "セクション #2"}, "icon_svg_2": {"label": "SVGアイコン"}, "icon_image_2": {"label": "アイコン画像"}, "text_2": {"label": "テキスト"}, "header__3": {"content": "セクション #3"}, "icon_svg_3": {"label": "SVGアイコン"}, "icon_image_3": {"label": "アイコン画像"}, "text_3": {"label": "テキスト"}, "header__4": {"content": "セクション #4"}, "icon_svg_4": {"label": "SVGアイコン"}, "icon_image_4": {"label": "アイコン画像"}, "text_4": {"label": "テキスト"}, "header__5": {"content": "セクション #5"}, "icon_svg_5": {"label": "SVGアイコン"}, "icon_image_5": {"label": "アイコン画像"}, "text_5": {"label": "テキスト"}, "header__6": {"content": "セクション #6"}, "icon_svg_6": {"label": "SVGアイコン"}, "icon_image_6": {"label": "アイコン画像"}, "text_6": {"label": "テキスト"}}}, "description": {"name": "Description", "settings": {"title": {"label": "タイトル"}, "content": {"label": "コンテンツ", "info": "カスタムの商品説明のためのフィールド。商品説明を表示させたい場合は空欄にしてください。"}}}, "text": {"name": "テキスト", "settings": {"content": {"label": "コンテンツ"}}}, "countdown": {"name": "タイムカウントダウン"}, "stock_countdown": {"name": "在庫カウントダウン", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > 在庫カウントダウン｣の項目から詳細を設定できます。"}}}, "delivery_countdown": {"name": "配送カウントダウン", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > 配送カウントダウン｣の項目から詳細を設定できます。"}}}, "border": {"name": "ボーダー"}, "options": {"name": "オプション", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページ > バリエーション｣の項目から詳細を設定できます。"}}}, "popups": {"name": "ポップアップ", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > ポップアップ｣の項目から詳細を設定できます。"}}}, "notes": {"name": "備考ノート"}, "buttons_quantity": {"name": "数量とカートに追加ボタン", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > ボタンとインプット｣の項目から詳細を設定できます。"}}}, "free_shipping": {"name": "送料無料のプログレスバー", "settings": {"paragraph__1": {"content": "｢テーマ設定 > カート > 送料無料のプログレスバー｣の項目から詳細を設定できます。"}}}, "pickup_availability": {"name": "店舗受取サービス", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > 店舗受取サービス｣の項目から詳細を設定できます。"}}}, "complementary_products": {"name": "付属商品", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > 付属商品｣の項目から詳細を設定できます。"}}}, "payments": {"name": "支払い情報"}, "social_share_buttons": {"name": "SNSシェアボタン", "settings": {"paragraph__1": {"content": "｢テーマ設定 > 商品ページとクイックビュー > SNSシェアボタン｣の項目から詳細を設定できます。"}}}}}, "register": {"name": "Register"}, "reset_password": {"name": "Reset password"}, "search_page": {"name": "検索ページ", "settings": {"container": {"label": "コンテンツ幅", "option__1": {"label": "ボックス幅"}, "option__2": {"label": "フル幅"}}, "products_per_page": {"label": "ページごとの商品の最大表示数"}, "search_show_only_products": {"label": "商品のみを表示", "info": "検索結果からページやブログ記事を除きます。"}, "header__1": {"content": "フィルター"}, "enable_default_filtering": {"label": "デフォルトフィルターを有効化"}, "paragraph__1": {"content": "管理画面から絞り込み要素を追加して、ブロックを追加してフィルターを表示します。[フィルターのカスタマイズ](/admin/menus)"}, "show_selected_filters_counter": {"label": "フィルターのカウントとリセットボタンを表示"}, "show_filter_product_count": {"label": "商品数のカウンターを表示"}, "hide_disabled_filters": {"label": "無効になっているフィルターを非表示", "info": "このオプションが無効の場合、選択できないフィルターは｢無効状態｣で表示されます。"}, "make_availability_as_rounded": {"label": "利用可能なフィルターは丸いチェックボックスで表示"}, "price_filter_type": {"label": "価格フィルターのタイプ", "info": "｢テーマ設定 > 通貨｣で｢従来の通貨変換方式を有効｣の場合、｢スライダー｣のオプションのみが利用できます。", "option__1": {"label": "スライダーとインプット"}, "option__2": {"label": "スライダー"}, "option__3": {"label": "インプット"}}, "header__2": {"content": "並べ替え方法"}, "sort_by_visibility": {"label": "並べ替えメニューの表示", "option__1": {"label": "デスクトップのみ"}, "option__2": {"label": "デスクトップとモバイル"}, "option__3": {"label": "非表示"}}, "paragraph__2": {"content": "コレクション設定からデフォルトの値が決まります。"}, "header__3": {"content": "グリッド"}, "view_grid_visibility": {"label": "グリッドボタンを表示", "option__1": {"label": "デスクトップのみ"}, "option__2": {"label": "デスクトップとモバイル"}, "option__3": {"label": "非表示"}}, "paragraph__3": {"content": "このボタンを押すことによりグリッドのレイアウトが変わります。また選択した後もブラウザの画面幅に応じて適切な大きさになるように自動的にレイアウトが変更されます。"}, "show_view_grid_1": {"label": "｢1行に1アイテム｣ボタンを表示", "info": "モバイルでのみ表示されます。"}, "show_view_grid_2": {"label": "｢1行に2アイテム｣ボタンを表示"}, "show_view_grid_3": {"label": "｢1行に3アイテム｣ボタンを表示"}, "show_view_grid_4": {"label": "｢1行に4アイテム｣ボタンを表示"}, "show_view_grid_6": {"label": "｢1行に6アイテム｣ボタンを表示"}, "show_view_grid_list": {"label": "｢リスト｣ボタンを表示", "info": "デスクトップのみで有効です。"}, "view_grid_list_design": {"label": "商品の｢リスト｣のデザイン", "option__1": {"label": "デザイン #1"}, "option__2": {"label": "デザイン #2"}}, "enable_grid_tooltip": {"label": "ツールチップを有効化", "info": "このオプションを有効にするには、｢テーマ設定 > アニメーション > ツールチップ｣の項目でもツールチップを有効にしてください。"}, "default_view_grid_xl": {"label": "デフォルトでの1行の表示商品数 - エクストララージ画面", "option__1": {"label": "2"}, "option__2": {"label": "3"}, "option__3": {"label": "4"}, "option__4": {"label": "6"}, "option__5": {"label": "リスト"}}, "default_view_grid_lg": {"label": "デフォルトでの1行の表示商品数 - ラージ画面", "option__1": {"label": "2"}, "option__2": {"label": "3"}, "option__3": {"label": "4"}, "option__4": {"label": "6"}, "option__5": {"label": "リスト"}}, "default_view_grid_md": {"label": "デフォルトでの1行の表示商品数 - ミディアム画面", "option__1": {"label": "2"}, "option__2": {"label": "3"}, "option__3": {"label": "4"}}, "default_view_grid_sm": {"label": "デフォルトでの1行の表示商品数 - スモール画面", "option__1": {"label": "1 - 中央"}, "option__2": {"label": "2"}, "option__3": {"label": "3"}}, "default_view_grid_xs": {"label": "デフォルトでの1行の表示商品数 - エクストラスモール画面", "option__1": {"label": "1 - 中央"}, "option__2": {"label": "2"}}, "header__4": {"content": "現在使用中のフィルター"}, "current_filters_visibility": {"label": "現在使用中のフィルターを表示", "option__1": {"label": "モバイルのみ"}, "option__2": {"label": "デスクトップのみ"}, "option__3": {"label": "デスクトップとモバイル"}, "option__4": {"label": "非表示"}}, "header__5": {"content": "フィルターセクション"}, "uppercase_filter_title": {"label": "フィルターセクションのタイトルを大文字に変更"}, "show_filter_border": {"label": "フィルターセクションのボーダーを表示"}, "header__6": {"content": "ビデオガイド"}, "paragraph__4": {"content": "[フィルターの追加 YouTube](https://www.youtube.com/watch?v=SC-zA52wJ0g&t=57s)"}, "header__7": {"content": "ユーザーマニュアル"}, "paragraph__5": {"content": "[コレクション](https://misell-manual.wraptas.site/template/filter)"}}, "blocks": {"current_filters": {"name": "現在使用中のフィルター", "settings": {"paragraph__1": {"content": "現在使用しているフィルターがない場合、このブロックは常に非表示となります。フィルター機能を使用している場合にのみ表示されます。"}, "header__1": {"content": "セクション設定"}, "title": {"label": "タイトル"}, "default_state": {"label": "デフォルトでの状態", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "show_group": {"label": "現在使用中のフィルターのグループ表示", "info": "複数のフィルターが機能している場合、グループが表示されます。例:色・価格"}}}, "filters": {"name": "フィルター", "settings": {"paragraph__1": {"content": "このブロックは｢商品グリッド>フィルター｣で｢デフォルトフィルター｣のオプションが有効の場合のみ表示されます。\n ｢Search & Discover｣アプリで設定したフィルターも、こちらのブロックを追加して表示させてください。"}, "paragraph__2": {"content": "[フィルターのカスタマイズ](/admin/menus)"}, "header__1": {"content": "セクション設定"}, "default_state": {"label": "デフォルトでの状態", "info": "｢テーマ設定 > コレクションページ｣の｢デスクトップのサイドバーの位置｣を｢トップと縦フィルター｣にしている場合、｢オープン｣または｢クローズ｣のオプションのみが設定できます。", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "layout": {"label": "レイアウト", "option__1": {"label": "1 コラム"}, "option__2": {"label": "2 コラム"}, "option__3": {"label": "3 コラム"}, "option__4": {"label": "行"}}, "max_column_size": {"label": "コラムの最大サイズ", "info": "この設定はレイアウト｢2コラム｣または｢3コラム｣の場合にのみ有効です。\n｢0｣では無制限となります。"}}}, "filter_settings": {"name": "フィルター設定", "settings": {"filter_name": {"label": "フィルターの名前"}, "header__1": {"content": "セクション設定"}, "title": {"label": "タイトル"}, "default_state": {"label": "デフォルトでの状態", "info": "｢テーマ設定 > コレクションページ｣の｢デスクトップのサイドバーの位置｣を｢トップと縦フィルター｣にしている場合、｢オープン｣または｢クローズ｣のオプションのみが設定できます。", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "make_as_color": {"label": "カラーサークルで表示", "info": "オプションのバリエーション名がCSSの標準カラー名になっていることを確認してください。"}, "layout": {"label": "レイアウト", "option__1": {"label": "1 コラム"}, "option__2": {"label": "2 コラム"}, "option__3": {"label": "3 コラム"}, "option__4": {"label": "行"}}, "max_column_size": {"label": "コラムの最大サイズ", "info": "この設定はレイアウトが｢2コラム｣または｢3コラム｣のときに有効です。｢0｣では無制限となります。"}}}, "products": {"name": "商品", "settings": {"paragraph__1": {"content": "｢テーマ設定 > コレクションページ｣の｢デスクトップのサイドバーの位置｣を｢トップと縦フィルター｣にしている場合、モバイルのみでの表示となります。"}, "header__1": {"content": "セクション設定"}, "title": {"label": "タイトル"}, "default_state": {"label": "デフォルトでの状態", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "collection": {"label": "コレクション"}, "max_count": {"label": "最大表示数", "info": "0 で無制限"}, "product_type": {"label": "商品表示のタイプ", "option__1": {"label": "ショート"}, "option__2": {"label": "フル"}}}}, "custom_html": {"name": "カスタムHTML", "settings": {"paragraph__1": {"content": "｢テーマ設定 > コレクションページ｣の｢デスクトップのサイドバーの位置｣を｢トップと縦フィルター｣にしている場合、モバイルのみでの表示となります。"}, "header__1": {"content": "セクション設定"}, "title": {"label": "タイトル"}, "default_state": {"label": "デフォルトでの状態", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "page_content": {"label": "ページコンテンツ", "info": "デフォルトでは'Include Collections Sidebar'が挿入されます。"}}}, "subscription": {"name": "メルマガ購読", "settings": {"paragraph__1": {"content": "｢テーマ設定 > コレクションページ｣の｢デスクトップのサイドバーの位置｣を｢トップと縦フィルター｣にしている場合、モバイルデバイスのみでの表示となります。"}, "header__1": {"content": "セクション設定"}, "title": {"label": "タイトル"}, "default_state": {"label": "デフォルトでの状態", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "paragraph": {"label": "パラグラフ文章"}, "placeholder": {"label": "プレースホルダー"}, "button_text": {"label": "ボタンテキスト"}}}, "promobox": {"name": "プロモボックス (バナー)", "settings": {"header__1": {"content": "セクション設定"}, "title": {"label": "タイトル"}, "default_state": {"label": "デフォルトでの状態", "option__1": {"label": "オープン"}, "option__2": {"label": "クローズ"}, "option__3": {"label": "固定"}, "option__4": {"label": "タイトルなし"}}, "header__2": {"content": "コンテンツ"}, "url": {"label": "バナーのリンク先URL"}, "header__3": {"content": "画像"}, "image": {"label": "画像", "info": "推奨サイズ 1440x550 px"}, "image_size": {"label": "画像サイズ", "info": "画像の画質を調整します。ただしレイジーロードの設定が有効の場合、このオプションは適用されません。"}, "color_image_mask": {"label": "画像マスク"}, "image_mask_opacity": {"label": "画像マスクの透明度"}, "header__4": {"content": "テキスト"}, "paragraph__1": {"content": "非表示にしたい場合は、テキストラインまたはボタンテキストの値を空欄にしてください。"}, "paragraph__2": {"content": "'<br>'のHTMLタグで改行できます。"}, "text_line_1": {"label": "テキストライン #1"}, "text_line_2": {"label": "テキストライン #2"}, "text_line_3": {"label": "テキストライン #3"}, "style": {"label": "カラースタイル", "option__1": {"label": "スタイル #1"}, "option__2": {"label": "スタイル #2"}, "option__3": {"label": "スタイル #3"}, "option__4": {"label": "スタイル #4"}, "option__5": {"label": "スタイル #5"}, "option__6": {"label": "スタイル #6"}, "option__7": {"label": "スタイル #7"}, "option__8": {"label": "スタイル #8"}}, "header__5": {"content": "ボタン #1"}, "button_1": {"label": "ボタンテキスト"}, "button_1_url": {"label": "ボタンのリンク先URL"}, "color_button_type_1": {"label": "ボタンスタイル", "option__1": {"label": "デフォルト"}, "option__2": {"label": "セカンダリ"}, "option__3": {"label": "反転"}, "option__4": {"label": "クリーン"}, "option__5": {"label": "透明デフォルト"}, "option__6": {"label": "透明セカンダリ"}, "option__7": {"label": "透明反転"}, "option__8": {"label": "透明クリーン"}}, "header__6": {"content": "ボタン #2"}, "button_2": {"label": "ボタンテキスト"}, "button_2_url": {"label": "ボタンのリンク先URL"}, "color_button_type_2": {"label": "ボタンスタイル", "option__1": {"label": "デフォルト"}, "option__2": {"label": "セカンダリ"}, "option__3": {"label": "反転"}, "option__4": {"label": "クリーン"}, "option__5": {"label": "透明デフォルト"}, "option__6": {"label": "透明セカンダリ"}, "option__7": {"label": "透明反転"}, "option__8": {"label": "透明クリーン"}}, "header__7": {"content": "その他のコンテンツ"}, "list_1": {"label": "リスト", "info": "リストのあるページを選択してください。"}, "custom_html": {"label": "カスタムHTML", "info": "テキストページコンテンツに置き換えます。"}, "header__8": {"content": "ビデオ"}, "video_external_url": {"label": "YouTube また Vimeo のURL"}, "video_mp4_url": {"label": "ビデオURL", "info": "ビデオのアップロードは｢Shopify管理画面 > コンテンツ > ファイル｣から行ってください。"}, "video_autoplay": {"label": "ビデオの自動再生を有効化 *自動再生はブラウザのセキュリティにより制限される場合があります。"}, "video_controls": {"label": "ビデオコントロール"}, "header__9": {"content": "レイアウト"}, "type": {"label": "タイプ", "option__1": {"label": "クリーン - テキストなし画像"}, "option__2": {"label": "クリーン - ボーダー枠付き"}, "option__3": {"label": "クリーン - ホバー時にボーダー"}, "option__4": {"label": "テキスト v1"}, "option__5": {"label": "テキスト v2"}, "option__6": {"label": "テキスト v3"}, "option__7": {"label": "テキスト v4"}, "option__8": {"label": "テキスト v5"}, "option__9": {"label": "テキスト v6"}, "option__10": {"label": "テキスト v7"}, "option__11": {"label": "テキスト v8"}, "option__12": {"label": "テキスト v9"}, "option__13": {"label": "テキスト v10"}, "option__14": {"label": "タイプ #1 v1 (画像上にテキスト)"}, "option__15": {"label": "タイプ #1 v2"}, "option__16": {"label": "タイプ #1 v3"}, "option__17": {"label": "タイプ #1 v4"}, "option__18": {"label": "タイプ #1 v5"}, "option__19": {"label": "タイプ #1 v6"}, "option__20": {"label": "タイプ #1 v7"}, "option__21": {"label": "タイプ #1 v8"}, "option__22": {"label": "タイプ #1 v9"}, "option__23": {"label": "タイプ #1 v10"}, "option__24": {"label": "タイプ #1 v11"}, "option__25": {"label": "タイプ #1 v12"}, "option__26": {"label": "タイプ #1 v13"}, "option__27": {"label": "タイプ #1 v14"}, "option__28": {"label": "タイプ #1 v15"}, "option__29": {"label": "タイプ #1 v16"}, "option__30": {"label": "タイプ #1 v17"}, "option__31": {"label": "タイプ #1 背景付き v1"}, "option__32": {"label": "タイプ #1 背景付き v2"}, "option__33": {"label": "タイプ #1 背景付き v3"}, "option__34": {"label": "タイプ #1 背景付き v4"}, "option__35": {"label": "タイプ #1 カーテン付き v1"}, "option__36": {"label": "タイプ #1 カーテン付き v2"}, "option__37": {"label": "タイプ #1 カーテン付き v3"}, "option__38": {"label": "タイプ #2 v1 (画像下にテキスト)"}, "option__39": {"label": "タイプ #2 v2"}, "option__40": {"label": "タイプ #2 v3"}, "option__41": {"label": "タイプ #2 v4"}, "option__42": {"label": "タイプ #2 v5"}, "option__43": {"label": "タイプ #2 v6"}, "option__44": {"label": "タイプ #2 v7"}, "option__45": {"label": "タイプ #2 v8"}, "option__46": {"label": "タイプ #2 v9"}, "option__47": {"label": "タイプ #2 v10"}, "option__48": {"label": "タイプ #2 v11"}, "option__49": {"label": "タイプ #2 v12"}, "option__50": {"label": "タイプ #2 v13"}, "option__51": {"label": "タイプ #3 - リボンボタン"}, "option__52": {"label": "タイプ #4 - アニメーション付きリボンボタン"}}, "header__10": {"content": "アニメーション"}, "animation_to": {"label": "ホバー時の画像移動方向設定", "option__1": {"label": "なし"}, "option__2": {"label": "中央"}, "option__3": {"label": "左上"}, "option__4": {"label": "上"}, "option__5": {"label": "右上"}, "option__6": {"label": "右"}, "option__7": {"label": "右下"}, "option__8": {"label": "下"}, "option__9": {"label": "左下"}, "option__10": {"label": "左"}}, "animation_from": {"label": "画像の初期位置設定", "option__1": {"label": "なし"}, "option__2": {"label": "中央"}, "option__3": {"label": "左上"}, "option__4": {"label": "上"}, "option__5": {"label": "右上"}, "option__6": {"label": "右"}, "option__7": {"label": "右下"}, "option__8": {"label": "下"}, "option__9": {"label": "左下"}, "option__10": {"label": "左"}}, "animation_opacity": {"label": "画像の透明化", "option__1": {"label": "なし"}, "option__2": {"label": "初期&ホバー"}, "option__3": {"label": "初期"}, "option__4": {"label": "ホバー"}}}}}}, "product_variants": {"name": "商品のバリエーション", "settings": {"header__1": {"content": "ビデオチュートリアル・ユーザーマニュアル"}, "paragraph__1": {"content": "[商品バリエーション（動画）](https://youtu.be/4-So_i2XwZA)"}, "paragraph__2": {"content": "[商品バリエーション](https://misell-manual.wraptas.site/theme-settings/product-variations)"}}, "blocks": {"product_option_setting": {"name": "オプション設定", "settings": {"product": {"label": "商品", "info": "選択は必須ではありません。選択した場合、単一の商品のみ適用されます。"}, "property": {"label": "プロパティ オプション名 例:色"}, "value": {"label": "値"}, "color": {"label": "色"}, "image": {"label": "画像"}, "assign_to": {"label": "適用先", "option__1": {"label": "すべて"}, "option__2": {"label": "商品"}, "option__3": {"label": "フィルター"}}, "disable_default_color": {"label": "デフォルトカラーを無効化"}, "disable_default_image": {"label": "デフォルト画像を無効化"}}}, "option_images": {"name": "オプション画像", "settings": {"product": {"label": "商品", "info": "選択は必須ではありません。選択した場合、単一の商品のみに適用されます。"}, "property_value_1": {"label": "プロパティと値 #1", "info": "例:プロパティ|値"}, "image_1": {"label": "画像 #1"}, "property_value_2": {"label": "プロパティと値 #2"}, "image_2": {"label": "画像 #2"}, "property_value_3": {"label": "プロパティと値 #3"}, "image_3": {"label": "画像 #3"}, "property_value_4": {"label": "プロパティと値 #4"}, "image_4": {"label": "画像 #4"}, "property_value_5": {"label": "プロパティと値 #5"}, "image_5": {"label": "画像 #5"}, "property_value_6": {"label": "プロパティと値 #6"}, "image_6": {"label": "画像 #6"}, "property_value_7": {"label": "プロパティと値 #7"}, "image_7": {"label": "画像 #7"}, "property_value_8": {"label": "プロパティと値 #8"}, "image_8": {"label": "画像 #8"}, "property_value_9": {"label": "プロパティと値 #9"}, "image_9": {"label": "画像 #9"}, "property_value_10": {"label": "プロパティと値 #10"}, "image_10": {"label": "画像 #10"}}}, "option_colors": {"name": "オプションカラー", "settings": {"product": {"label": "商品", "info": "選択は必須ではありません。選択した場合、単一の商品のみに適用されます。"}, "property_value_1": {"label": "プロパティと値 #1", "info": "例:プロパティ|値"}, "color_1": {"label": "カラー #1"}, "property_value_2": {"label": "プロパティと値 #2"}, "color_2": {"label": "カラー #2"}, "property_value_3": {"label": "プロパティと値 #3"}, "color_3": {"label": "カラー #3"}, "property_value_4": {"label": "プロパティと値 #4"}, "color_4": {"label": "カラー #4"}, "property_value_5": {"label": "プロパティと値 #5"}, "color_5": {"label": "カラー #5"}, "property_value_6": {"label": "プロパティと値 #6"}, "color_6": {"label": "カラー #6"}, "property_value_7": {"label": "プロパティと値 #7"}, "color_7": {"label": "カラー #7"}, "property_value_8": {"label": "プロパティと値 #8"}, "color_8": {"label": "カラー #8"}, "property_value_9": {"label": "プロパティと値 #9"}, "color_9": {"label": "カラー #9"}, "property_value_10": {"label": "プロパティと値 #10"}, "color_10": {"label": "カラー #10"}}}}}}}