import { json, type LoaderFunctionArgs, type ActionFunctionArgs, redirect } from "@remix-run/node";
import { useLoaderData, useSubmit, useNavigate, useNavigation, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  BlockStack,
  Button,
  InlineStack,
  TextField,
  Select,
  Checkbox,
  Banner,
  Divider,
  EmptyState,
  ResourceList,
  ResourceItem,
  Thumbnail,
  Badge,
  Modal,
  Spinner,
  Tabs,
  RadioButton,
  DatePicker
} from "@shopify/polaris";
import { useState, useCallback, useEffect } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { isClosedDay, getClosedDayType, getHolidayName } from "../utils/booking/holidays";
import "~/styles/custom-datepicker.css";
import { addDays, format, isAfter, isBefore, parseISO, differenceInDays } from "date-fns";
import { ja } from "date-fns/locale";
import {
  JAPAN_TIMEZONE,
  JAPAN_TIMEZONE_OPTIONS,
  formatJapanDate,
  getNowInJapan,
  startOfDayJapan,
  endOfDayJapan,
  addDaysJapan
} from "../utils/date/japan-time";
import type { BookingStatus, BookingType } from "@prisma/client";
import { ShopifyCustomerSearch } from "../components/ShopifyCustomerSearch";
import { updateBookingMetafield } from "../utils/booking/metafield-updater";
import { createOrderFromBooking } from "../utils/booking/order-creator";
import { GraphQLClient } from "graphql-request";
import { retry, handleError } from "../utils/booking/error-handler";
import { UnifiedPricingService } from "../services/pricing/unified-pricing.service";

/**
 * 商品が指定された期間に予約可能かどうかを判定する
 * @param productId 商品ID
 * @param startDate 開始日
 * @param endDate 終了日
 * @param bookings 予約データ
 * @returns 予約可能な場合はtrue、そうでない場合はfalse
 */
export function checkProductAvailability(
  productId: string,
  startDate: Date,
  endDate: Date,
  bookings: any[]
): boolean {
  // 予約データから該当商品の予約を抽出
  const productBookings = bookings.filter(
    (booking) =>
      booking.productId === productId &&
      (booking.status === 'CONFIRMED' || booking.status === 'PROVISIONAL')
  );

  // 予約期間が重複するかチェック
  for (const booking of productBookings) {
    const bookingStart = new Date(booking.startDate);
    const bookingEnd = new Date(booking.endDate);

    // 期間が重複する場合
    if (
      (startDate <= bookingEnd && endDate >= bookingStart)
    ) {
      return false; // 利用不可
    }
  }

  return true; // 利用可能
}

/**
 * 予約情報をメタフィールド用のフォーマットに変換する関数
 * @param bookings 予約情報
 * @returns メタフィールド用の予約情報
 */
function formatBookingsForMetafield(bookings: any[]): any {
  // バリアント別に予約をグループ化
  const bookingsByVariant: Record<string, any[]> = {};

  bookings.forEach(booking => {
    const variantId = booking.variantId || 'default';
    if (!bookingsByVariant[variantId]) {
      bookingsByVariant[variantId] = [];
    }

    bookingsByVariant[variantId].push({
      id: booking.bookingId,
      startDate: formatJapanDate(booking.startDate, 'yyyy-MM-dd'),
      endDate: formatJapanDate(booking.endDate, 'yyyy-MM-dd'),
      status: booking.status,
      type: booking.bookingType,
      customerName: booking.customerName,
      customerEmail: booking.customerEmail
    });
  });

  // バリアント情報を構築
  const variants: Record<string, any> = {};

  Object.entries(bookingsByVariant).forEach(([variantId, variantBookings]) => {
    variants[variantId] = {
      status: variantBookings.some(b => b.status === 'CONFIRMED') ? 'unavailable' : 'available',
      reservations: variantBookings
    };
  });

  // 従来の形式との互換性のために、すべての予約も含める
  const allBookings = bookings.map(booking => ({
    id: booking.bookingId,
    startDate: formatJapanDate(booking.startDate, 'yyyy-MM-dd'),
    endDate: formatJapanDate(booking.endDate, 'yyyy-MM-dd'),
    status: booking.status,
    customerName: booking.customerName,
    customerEmail: booking.customerEmail,
    variantId: booking.variantId
  }));

  // メタフィールド用のデータ構造
  const metafieldData = {
    // 従来の形式との互換性のために残す
    bookings: allBookings,
    // 新しい構造化データ
    status: Object.values(variants).some((v: any) => v.status === 'unavailable') ? 'unavailable' : 'available',
    lastUpdated: formatJapanDate(getNowInJapan(), 'yyyy-MM-dd HH:mm:ss'),
    variants,
    availability: {
      rentalStatus: 'available',
      startDate: formatJapanDate(getNowInJapan(), 'yyyy-MM-dd'),
      maintenanceDates: [],
      blockedDates: []
    }
  };

  return metafieldData;
}

/**
 * 新規予約作成ページのローダー
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { session, admin } = await authenticate.admin(request);
  const shop = session.shop;

  // Shopify店舗情報を取得
  const shopResponse = await admin.graphql(
    `#graphql
      query getShopInfo {
        shop {
          id
          name
          url
        }
      }
    `
  );
  const shopData = await shopResponse.json();
  const shopifyShop = shopData.data.shop;

  // URLからパラメータを取得
  const url = new URL(request.url);
  const searchQuery = url.searchParams.get("q") || "";
  const startDateParam = url.searchParams.get("startDate");
  const endDateParam = url.searchParams.get("endDate");

  try {
    // 商品データを取得
    const products = await prisma.product.findMany({
      where: {
        shop,
        ...(searchQuery ? {
          OR: [
            { title: { contains: searchQuery, mode: 'insensitive' } },
            { sku: { contains: searchQuery, mode: 'insensitive' } },
          ],
        } : {}),
      },
      select: {
        id: true,
        title: true,
        sku: true,
        status: true,
        price: true,
        shopifyId: true,
      },
      orderBy: {
        title: "asc",
      },
      take: 50, // 最大50件まで取得
    });

    // 予約データを取得（現在の予約状況を確認するため）
    const bookings = await prisma.booking.findMany({
      where: {
        shop,
        status: {
          in: ["DRAFT", "PROVISIONAL", "CONFIRMED"],
        },
      },
      select: {
        id: true,
        bookingId: true,
        productId: true,
        startDate: true,
        endDate: true,
        status: true,
        bookingType: true,
        priority: true,
      },
    });

    // Date型を文字列に変換してLoaderDataの型に合わせる
    const serializedBookings = bookings.map((b) => ({
      id: b.id,
      bookingId: b.bookingId || "",
      productId: b.productId,
      startDate: b.startDate.toISOString(),
      endDate: b.endDate.toISOString(),
      status: b.status,
      bookingType: b.bookingType,
      priority: b.priority,
    }));

    // 初期日付の設定
    const today = new Date();
    const defaultStartDate = startDateParam ? new Date(startDateParam) : today;
    const defaultEndDate = endDateParam ? new Date(endDateParam) : addDays(today, 3);

    return json({
      products,
      bookings: serializedBookings,
      defaultDates: {
        startDate: defaultStartDate.toISOString(),
        endDate: defaultEndDate.toISOString(),
      },
      shopifyShop,
    });
  } catch (error) {
    console.error("データ取得エラー:", error);
    return json({
      products: [],
      bookings: [],
      defaultDates: {
        startDate: new Date().toISOString(),
        endDate: addDays(new Date(), 3).toISOString(),
      },
      shopifyShop,
      error: "データの取得中にエラーが発生しました",
    });
  }
}

/**
 * 新規予約作成ページのアクション
 */
export async function action({ request }: ActionFunctionArgs) {
  try {
    // 認証情報を取得
    const { session, admin } = await authenticate.admin(request);
    const shop = session.shop;

    console.log('===== アクション関数開始 =====');
    console.log('ショップ:', shop);

    // フォームデータを取得
    const formData = await request.formData();
    const action = formData.get("action") as string;

    console.log('アクション:', action);

    if (action === "create_booking") {
      console.log('予約作成アクションを実行します');
      // 予約作成処理
      const productIds = formData.getAll("productId") as string[];
      const startDate = formData.get("startDate") as string;
      const endDate = formData.get("endDate") as string;
      const customerName = formData.get("customerName") as string;
      const customerEmail = formData.get("customerEmail") as string;
      const customerId = formData.get("customerId") as string; // Shopify顧客ID
      const bookingType = formData.get("bookingType") as "PROVISIONAL" | "CONFIRMED";
      const paymentMethod = formData.get("paymentMethod") as "CREDIT_CARD" | "BANK_TRANSFER";
      const isPaid = formData.get("isPaid") === "true";
      const notes = formData.get("notes") as string;

      if (!productIds.length || !startDate || !endDate || !customerName || !customerId) {
        return json({
          success: false,
          error: "必須項目が入力されていません",
        });
      }

      // 予約の重複チェック
      const existingBookings = await prisma.booking.findMany({
        where: {
          productId: { in: productIds },
          status: { in: ["CONFIRMED", "PROVISIONAL"] },
          OR: [
            {
              AND: [
                { startDate: { lte: new Date(endDate) } },
                { endDate: { gte: new Date(startDate) } },
              ],
            },
          ],
        },
      });

      if (existingBookings.length > 0) {
        // 重複する商品のIDを取得
        const conflictProductIds = existingBookings.map(b => b.productId);

        // 重複する商品の情報を取得
        const conflictProducts = await prisma.product.findMany({
          where: {
            id: { in: conflictProductIds },
          },
          select: {
            title: true,
            sku: true,
          },
        });

        return json({
          success: false,
          error: "選択された期間に既に予約が存在する商品があります",
          conflictProducts,
        });
      }

      // 予約を作成
      const createdBookings = [];
      for (const productId of productIds) {
        // 商品情報を取得
        const product = await prisma.product.findUnique({
          where: { id: productId },
        });

        if (!product) continue;

        // 予約IDを生成
        const bookingId = `BOOK-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

        // 統一料金計算サービスを使用
        const pricingService = UnifiedPricingService.getInstance();
        const pricingResult = pricingService.calculatePrice(
          new Date(startDate),
          new Date(endDate),
          product.price
        );
        const calculatedPrice = pricingResult.totalPrice;

        // 予約を作成
        const booking = await prisma.booking.create({
          data: {
            bookingId,
            shop,
            productId,
            startDate: new Date(startDate),
            endDate: new Date(endDate),
            customerName,
            customerEmail,
            customerId, // Shopify顧客IDを保存
            status: bookingType,
            bookingType,
            paymentMethod, // 支払い方法を保存
            notes,
            priority: 1,
            totalAmount: calculatedPrice,
            depositAmount: calculatedPrice * 0.1, // デポジットは10%
            paymentStatus: isPaid ? 'COMPLETED' : 'PENDING',
            metadata: {
              isPaid,
              paymentCompletedAt: isPaid ? new Date().toISOString() : null,
              orderType: isPaid ? 'DIRECT_ORDER' : 'DRAFT_ORDER'
            }
          },
        });

        createdBookings.push(booking);
      }

      // Shopifyメタフィールドを更新
      try {
        console.log('===== Shopifyメタフィールド更新処理開始 =====');
        console.log(`作成された予約数: ${createdBookings.length}`);

        // Shopify APIアクセストークンを取得
        const shopifyAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
        const shopifyShop = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';

        console.log('環境変数:');
        console.log('- SHOPIFY_ADMIN_API_ACCESS_TOKEN:', shopifyAccessToken ? '設定されています' : '設定されていません');
        console.log('- SHOPIFY_SHOP:', shopifyShop);

        if (!shopifyAccessToken) {
          throw new Error('SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません');
        }

        // GraphQLクライアントを初期化
        // 2025-01 バージョンを使用（メモリーに記載されている推奨バージョン）
        const apiVersion = '2025-01';
        console.log(`Shopify API バージョン: ${apiVersion}`);

        const graphQLClient = new GraphQLClient(
          `https://${shopifyShop}/admin/api/${apiVersion}/graphql.json`,
          {
            headers: {
              'X-Shopify-Access-Token': shopifyAccessToken,
              'Content-Type': 'application/json',
            },
          }
        );

        for (const booking of createdBookings) {
          console.log(`予約ID: ${booking.id}, 商品ID: ${booking.productId} の処理を開始`);

          // 商品情報を取得
          const product = await prisma.product.findUnique({
            where: { id: booking.productId },
            select: { shopifyId: true, title: true, sku: true }
          });

          console.log('商品情報:', product);

          if (product && product.shopifyId) {
            console.log(`Shopify商品ID: ${product.shopifyId} のメタフィールドを更新します`);

            try {
              // 商品の全予約情報を取得（有効な予約のみ）
              const bookings = await prisma.booking.findMany({
                where: {
                  productId: booking.productId,
                  status: {
                    in: ['PROVISIONAL', 'CONFIRMED']
                  }
                },
                orderBy: {
                  startDate: 'asc'
                }
              });

              console.log(`予約情報を取得しました: ${bookings.length}件`);

              // 予約情報をメタフィールド用のフォーマットに変換
              const metafieldData = formatBookingsForMetafield(bookings);

              // Shopify IDを正規化
              const shopifyId = product.shopifyId.startsWith('gid://shopify/Product/')
                ? product.shopifyId
                : `gid://shopify/Product/${product.shopifyId}`;

              console.log('メタフィールド更新を実行します:');
              console.log('- shopifyId:', shopifyId);
              console.log('- namespace:', 'rental');
              console.log('- key:', 'bookings');
              console.log('- metafieldData:', JSON.stringify(metafieldData, null, 2));

              // メタフィールドの設定（リトライ機能付き）
              const result = await retry(
                async () => {
                  console.log('GraphQL呼び出しを実行します...');

                  const mutation = `
                    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
                      metafieldsSet(metafields: $metafields) {
                        metafields {
                          id
                          namespace
                          key
                          value
                        }
                        userErrors {
                          field
                          message
                        }
                      }
                    }
                  `;

                  const variables = {
                    metafields: [
                      {
                        ownerId: shopifyId,
                        namespace: 'rental',
                        key: 'bookings',
                        value: JSON.stringify(metafieldData),
                        type: 'json'
                      }
                    ]
                  };

                  console.log('GraphQLリクエストを送信します:');
                  console.log('- mutation:', mutation);
                  console.log('- variables:', JSON.stringify(variables, null, 2));

                  try {
                    const response = await graphQLClient.request(mutation, variables);
                    console.log('GraphQL応答を受信しました');
                    console.log('GraphQL応答内容:', JSON.stringify(response, null, 2));

                    if (response.metafieldsSet.userErrors.length > 0) {
                      console.error('メタフィールド更新エラー:', response.metafieldsSet.userErrors);
                      throw new Error(`メタフィールドの設定中にエラーが発生しました: ${JSON.stringify(response.metafieldsSet.userErrors)}`);
                    }

                    return response;
                  } catch (requestError) {
                    console.error('GraphQLリクエストエラー:', requestError);
                    if (requestError instanceof Error) {
                      console.error('エラーメッセージ:', requestError.message);
                      console.error('スタックトレース:', requestError.stack);
                    }
                    throw requestError;
                  }
                },
                3, // リトライ回数
                1000, // 1秒後にリトライ
                2 // 指数バックオフ（1秒、2秒、4秒...）
              );

              console.log('予約情報メタフィールドを更新しました');
              console.log('メタフィールド更新結果:', result);
              console.log(`商品ID ${booking.productId} (Shopify ID: ${product.shopifyId}) のメタフィールドを更新しました`);

              // Shopify注文を作成
              console.log('===== Shopify注文作成処理開始 =====');
              try {
                // 注文作成処理を3回までリトライ
                const orderResult = await retry(
                  async () => {
                    console.log(`予約ID ${booking.id} の注文を作成します...`);
                    return await createOrderFromBooking(prisma, admin, booking.id, { isPaid, paymentMethod });
                  },
                  3, // リトライ回数
                  1000, // 1秒後にリトライ
                  2 // 指数バックオフ（1秒、2秒、4秒...）
                );

                if (orderResult && orderResult.success) {
                  console.log('注文作成結果:', orderResult);
                  console.log(`予約ID ${booking.id} の注文を作成しました: 注文ID ${orderResult.orderId}, 注文番号 ${orderResult.orderName}`);

                  // 予約データを更新して注文情報を関連付け
                  await prisma.booking.update({
                    where: { id: booking.id },
                    data: {
                      orderId: orderResult.orderId,
                      orderName: orderResult.orderName,
                      paymentStatus: isPaid ? 'COMPLETED' : (bookingType === 'PROVISIONAL' ? 'PENDING' : 'COMPLETED'),
                      // 注文作成成功フラグを追加
                      metadata: {
                        ...(booking.metadata || {}),
                        orderCreationSuccess: true,
                        orderCreationDate: new Date().toISOString(),
                        shopifyOrderId: orderResult.orderId, // shopifyOrderIdをメタデータに保存
                        shopifyOrderName: orderResult.orderName, // shopifyOrderNameをメタデータに保存
                        orderType: isPaid ? 'DIRECT_ORDER' : 'DRAFT_ORDER'
                      }
                    }
                  });

                  console.log(`予約ID ${booking.id} に注文情報を関連付けました`);
                } else {
                  // 注文作成失敗の場合
                  console.error(`予約ID ${booking.id} の注文作成に失敗しました:`, orderResult);

                  // エラー情報を予約データに保存
                  await prisma.booking.update({
                    where: { id: booking.id },
                    data: {
                      metadata: {
                        ...(booking.metadata || {}),
                        orderCreationError: true,
                        orderCreationErrorMessage: orderResult.errorMessage || '注文作成に失敗しました',
                        orderCreationErrorDate: new Date().toISOString(),
                        orderCreationErrorDetails: JSON.stringify(orderResult.error || {}),
                        // shopifyOrderIdとshopifyOrderNameはメタデータに保存
                        shopifyOrderId: null,
                        shopifyOrderName: null
                      }
                    }
                  });

                  // 注文作成失敗を記録するが、予約自体は成功とする
                  console.log(`予約ID ${booking.id} に注文作成失敗情報を記録しました。後で再試行できます。`);
                }
              } catch (orderError) {
                console.error(`予約ID ${booking.id} の注文作成中にエラーが発生:`, orderError);
                if (orderError instanceof Error) {
                  console.error('エラーメッセージ:', orderError.message);
                  console.error('スタックトレース:', orderError.stack);
                }

                // 例外発生時もエラー情報を予約データに保存
                try {
                  await prisma.booking.update({
                    where: { id: booking.id },
                    data: {
                      metadata: {
                        ...(booking.metadata || {}),
                        orderCreationError: true,
                        orderCreationErrorMessage: orderError instanceof Error ? orderError.message : '注文作成中に例外が発生しました',
                        orderCreationErrorDate: new Date().toISOString(),
                        // shopifyOrderIdとshopifyOrderNameはメタデータに保存
                        shopifyOrderId: null,
                        shopifyOrderName: null
                      }
                    }
                  });
                  console.log(`予約ID ${booking.id} に注文作成エラー情報を記録しました。後で再試行できます。`);
                } catch (updateError) {
                  console.error(`予約ID ${booking.id} のエラー情報更新に失敗:`, updateError);
                }
              }
              console.log('===== Shopify注文作成処理完了 =====');
            } catch (innerError) {
              console.error(`商品ID ${booking.productId} のメタフィールド更新中にエラーが発生:`, innerError);
            }
          } else {
            console.error(`商品ID ${booking.productId} のShopify IDが見つかりません`);
          }
        }

        console.log('===== Shopifyメタフィールド更新処理完了 =====');
      } catch (metafieldError) {
        console.error('Shopifyメタフィールドの更新中にエラーが発生しました:', metafieldError);
        // メタフィールドの更新に失敗しても、予約の作成自体は成功とする
      }

      // 予約が作成されたら予約一覧ページにリダイレクト
      if (createdBookings.length > 0) {
        console.log(`予約が正常に作成されました: ${createdBookings.length}件`);
        console.log('予約一覧ページにリダイレクトします');
        return redirect(`/app/bookings?success=true&count=${createdBookings.length}`);
      }

      return json({
        success: false,
        error: "予約の作成に失敗しました",
      });
    }

    return json({
      success: false,
      error: "無効なアクションです",
    });
  } catch (error) {
    console.error("予約作成エラー:", error);
    if (error instanceof Error) {
      console.error('エラーメッセージ:', error.message);
      console.error('スタックトレース:', error.stack);
    }

    // エラーの詳細情報を含めて返す
    const errorMessage = error instanceof Error
      ? `予約の作成中にエラーが発生しました: ${error.message}`
      : "予約の作成中にエラーが発生しました";

    console.error('エラーメッセージ:', errorMessage);

    return json({
      success: false,
      error: errorMessage,
    });
  }
}

/**
 * 新規予約作成ページ
 */
export default function BookingsNewPage() {
  const { products, bookings, defaultDates, shopifyShop, error } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const navigate = useNavigate();
  const navigation = useNavigation();

  // タブの状態
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);

  // 日付選択の状態
  const [{ month, year }, setDate] = useState(() => {
    // defaultDatesが存在しない場合は現在の日付を使用
    const today = new Date();
    const defaultDate = defaultDates?.startDate ? new Date(defaultDates.startDate) : today;

    return {
      month: defaultDate.getMonth(),
      year: defaultDate.getFullYear(),
    };
  });

  // 選択された日付範囲
  const [selectedDateRange, setSelectedDateRange] = useState(() => {
    // defaultDatesが存在しない場合は現在の日付と3日後を使用
    const today = new Date();
    const threeDaysLater = addDays(today, 3);

    return {
      startDate: defaultDates?.startDate ? new Date(defaultDates.startDate) : today,
      endDate: defaultDates?.endDate ? new Date(defaultDates.endDate) : threeDaysLater,
    };
  });

  // 商品検索の状態
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<typeof products>([]);
  const [selectedProducts, setSelectedProducts] = useState<typeof products>([]);
  const [showUnavailableProducts, setShowUnavailableProducts] = useState(false);

  // 顧客情報の状態
  const [customerName, setCustomerName] = useState("");
  const [customerEmail, setCustomerEmail] = useState("");
  const [customerId, setCustomerId] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [bookingType, setBookingType] = useState<"PROVISIONAL" | "CONFIRMED">("PROVISIONAL");
  const [paymentMethod, setPaymentMethod] = useState<"CREDIT_CARD" | "BANK_TRANSFER">("CREDIT_CARD");
  const [isPaid, setIsPaid] = useState(false);
  const [notes, setNotes] = useState("");

  // モーダルの状態
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState("");



  // レンタル日数を計算
  const rentalDays = selectedDateRange.startDate && selectedDateRange.endDate
    ? differenceInDays(selectedDateRange.endDate, selectedDateRange.startDate) + 1
    : 1;

  // 総額を計算（統一料金計算サービスを使用）
  const totalAmount = selectedProducts.reduce((sum, product) => {
    if (!selectedDateRange.startDate || !selectedDateRange.endDate) {
      return sum + (product.price || 0);
    }

    try {
      // 統一料金計算サービスを使用
      const pricingService = UnifiedPricingService.getInstance();
      const pricingResult = pricingService.calculatePrice(
        selectedDateRange.startDate,
        selectedDateRange.endDate,
        product.price
      );

      console.log(`商品 ${product.title} の計算結果:`, pricingResult.totalPrice);
      return sum + pricingResult.totalPrice;
    } catch (error) {
      console.error(`商品 ${product.title} の料金計算エラー:`, error);
      // エラーの場合は基本価格を使用
      const basePrice = typeof product.price === 'object' ? Number(product.price) : (product.price || 0);
      return sum + basePrice;
    }
  }, 0);

  // 通貨フォーマット関数
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ja-JP', {
      style: 'currency',
      currency: 'JPY'
    }).format(amount);
  };

  // 支払い方法の説明を取得する関数
  const getPaymentDescription = () => {
    if (paymentMethod === "BANK_TRANSFER") {
      if (isPaid) {
        return "銀行振込が完了済みです。直接注文が作成されます。";
      } else {
        return "銀行振込の場合、振込確認後に注文が作成されます。";
      }
    } else {
      if (isPaid) {
        return "店頭でクレジットカード決済が完了済みです。直接注文が作成されます。";
      } else {
        return "オンライン決済用のドラフトオーダーが作成され、顧客にメールが送信されます。";
      }
    }
  };

  // 支払い方法のバナートーンを取得する関数
  const getPaymentBannerTone = () => {
    if (isPaid) {
      return "success";
    } else if (paymentMethod === "BANK_TRANSFER") {
      return "info";
    } else {
      return "neutral";
    }
  };

  // 月変更ハンドラー
  const handleMonthChange = useCallback((month: number, year: number) => {
    setDate({ month, year });
  }, []);

  // 日付のカスタムレンダリング
  const renderDayLabel = useCallback((date: Date) => {
    // 日付が無効な場合は早期リターン
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
      return <span>-</span>;
    }

    try {
      const dayNum = date.getDate();
      let tooltip = "";

      // 今日の日付かどうかを判定
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const isToday = date.getTime() === today.getTime();

      // 休業日情報を取得（当日は許可）
      const closed = isClosedDay(date, true);
      const closedType = getClosedDayType(date);
      const holidayName = getHolidayName(date);

      // 休業日の場合はツールチップを設定
      if (closed) {
        if (closedType === 'sunday') {
          tooltip = "日曜日は休業日";
        } else if (closedType === 'holiday') {
          tooltip = `${holidayName || '祝日'}は休業日`;
        } else if (closedType === 'newyear') {
          tooltip = "年末年始は休業日";
        }
      }

      // カスタム属性を追加
      const customProps = {
        'data-date': date.toISOString().split('T')[0],
      } as any;

      if (closed) {
        customProps['data-closed'] = 'true';
        customProps['data-closed-type'] = closedType;

        if (holidayName) {
          customProps['data-holiday-name'] = holidayName;
        }
      }

      if (isToday) {
        customProps['data-today'] = 'true';
      }

      // Polarisスタイルに合わせたレンダリング
      return (
        <span
          className={`Polaris-Text--root Polaris-Text--bodySm Polaris-Text--regular Polaris-Text--block Polaris-Text--center ${isToday ? 'today-text' : ''}`}
          title={tooltip}
          {...customProps}
        >
          {dayNum}
        </span>
      );
    } catch (error) {
      console.error('日付のレンダリングエラー:', error);
      return <span>-</span>;
    }
  }, []);

  // 日付が選択可能かどうかを判定する関数
  const isDateSelectable = useCallback((date: Date): boolean => {
    // 日付が無効な場合はfalseを返す
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
      return false;
    }

    try {
      // 今日の日付
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const isToday = date.getTime() === today.getTime();

      // 今日の日付は常に選択可能
      if (isToday) {
        return true;
      }

      // 休業日は選択不可（当日は許可）
      return !isClosedDay(date, true);
    } catch (error) {
      console.error('日付選択可否判定エラー:', error);
      return false;
    }
  }, []);

  // 日付範囲選択ハンドラー
  const handleDateRangeChange = useCallback(({ start, end }: { start?: Date; end?: Date }) => {
    try {
      // 開始日と終了日が両方選択されている場合のみ処理
      if (!start || !end) return;

      // 今日の日付（日本時間）
      const today = startOfDayJapan(getNowInJapan());
      console.log('今日の日付（日本時間）:', formatJapanDate(today));

      // 選択された日付（デバッグ用）
      console.log('選択された開始日:', formatJapanDate(start));
      console.log('選択された終了日:', formatJapanDate(end));

      // 開始日が休業日の場合は選択不可（当日は許可）
      if (isClosedDay(start, true)) {
        alert("開始日に休業日（日曜・祝日・年末年始）は選択できません");
        return;
      }

      // 終了日が休業日の場合は選択不可（当日は許可）
      if (isClosedDay(end, true)) {
        alert("終了日に休業日（日曜・祝日・年末年始）は選択できません");
        return;
      }

      // 日付範囲を更新
      setSelectedDateRange({
        startDate: start,
        endDate: end,
      });

      // 日付が変更されたら商品検索結果を更新
      if (searchResults.length > 0 || selectedProducts.length > 0) {
        // 次のレンダリングサイクルで検索を実行
        setTimeout(() => handleSearch(), 0);
      }
    } catch (error) {
      console.error('日付選択エラー:', error);
    }
  }, []);

  // 商品が指定された期間に利用可能かどうかをチェックする関数
  const isProductAvailable = useCallback((productId: string, startDate: Date, endDate: Date) => {
    // グローバル関数を使用
    return checkProductAvailability(productId, startDate, endDate, bookings);
  }, [bookings]);

  // 商品検索ハンドラー
  const handleSearch = useCallback(() => {
    // 検索条件に合致する商品をフィルタリング
    let filtered = products.filter(
      (product) =>
        product.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.sku.toLowerCase().includes(searchQuery.toLowerCase())
    );

    // 選択された日付範囲で利用可能な商品のみを表示（チェックボックスがオフの場合）
    if (!showUnavailableProducts && selectedDateRange.startDate && selectedDateRange.endDate) {
      filtered = filtered.filter(product =>
        isProductAvailable(product.id, selectedDateRange.startDate, selectedDateRange.endDate)
      );
    }

    setSearchResults(filtered);
  }, [products, searchQuery, showUnavailableProducts, selectedDateRange, isProductAvailable]);

  // 商品選択ハンドラー
  const handleProductSelect = useCallback((product: (typeof products)[0]) => {
    // 日付が選択されている場合は利用可能性をチェック
    if (selectedDateRange.startDate && selectedDateRange.endDate && !showUnavailableProducts) {
      const isAvailable = isProductAvailable(product.id, selectedDateRange.startDate, selectedDateRange.endDate);
      if (!isAvailable) {
        alert(`選択された期間（${formatJapanDate(selectedDateRange.startDate)} 〜 ${formatJapanDate(selectedDateRange.endDate)}）には、この商品は既に予約されています。`);
        return;
      }
    }

    setSelectedProducts((prev) => {
      // 既に選択されている場合は何もしない
      if (prev.some((p) => p.id === product.id)) {
        return prev;
      }
      return [...prev, product];
    });
  }, [selectedDateRange, showUnavailableProducts, isProductAvailable]);

  // 商品選択解除ハンドラー
  const handleProductDeselect = useCallback((productId: string) => {
    setSelectedProducts((prev) => prev.filter((p) => p.id !== productId));
  }, []);

  // 顧客選択ハンドラー
  const handleCustomerSelect = useCallback((customer: any) => {
    console.log('選択された顧客情報:', customer);
    setSelectedCustomer(customer);

    // Shopify顧客IDを保存（完全なgid形式で保存）
    // gid://shopify/Customer/123456789 形式を維持
    if (customer.id.startsWith('gid://shopify/Customer/')) {
      setCustomerId(customer.id);
    } else {
      // 数値のみの場合はgid形式に変換
      setCustomerId(`gid://shopify/Customer/${customer.id}`);
    }

    // 表示用の情報を設定
    // 日本語名の場合は「姓 名」の形式で表示
    if (customer.firstName && customer.lastName) {
      setCustomerName(`${customer.lastName} ${customer.firstName}`);
    } else {
      setCustomerName(customer.displayName);
    }

    setCustomerEmail(customer.email || "");
  }, []);

  // 予約作成ハンドラー
  const handleCreateBooking = useCallback(() => {
    if (selectedProducts.length === 0) {
      setSubmitError("商品を選択してください");
      return;
    }

    if (!selectedCustomer) {
      setSubmitError("Shopifyから顧客を選択してください");
      return;
    }

    // 選択された商品が予約可能かどうかを最終確認
    if (selectedDateRange.startDate && selectedDateRange.endDate) {
      const unavailableProducts = selectedProducts.filter(
        product => !isProductAvailable(product.id, selectedDateRange.startDate, selectedDateRange.endDate)
      );

      if (unavailableProducts.length > 0) {
        const productNames = unavailableProducts.map(p => p.title).join(', ');
        setSubmitError(`選択された期間には、以下の商品は既に予約されています: ${productNames}`);
        return;
      }
    }

    setIsSubmitting(true);
    setSubmitError("");

    try {
      const formData = new FormData();
      formData.append("action", "create_booking");

      // 複数の商品IDを追加
      selectedProducts.forEach(product => {
        formData.append("productId", product.id);
      });

      formData.append("startDate", selectedDateRange.startDate.toISOString());
      formData.append("endDate", selectedDateRange.endDate.toISOString());
      formData.append("customerName", customerName);
      formData.append("customerEmail", customerEmail);

      // Shopify顧客IDを設定（完全なgid形式で保存）
      console.log('送信する顧客ID:', customerId);
      formData.append("customerId", customerId);
      formData.append("bookingType", bookingType);
      formData.append("paymentMethod", paymentMethod);
      formData.append("isPaid", isPaid.toString());
      formData.append("notes", notes);

      // 予約タイプに応じた追加情報
      if (bookingType === "PROVISIONAL") {
        // 仮予約の場合はデポジット金額（10%）を設定
        const depositAmount = Math.round(totalAmount * 0.1);
        formData.append("depositAmount", depositAmount.toString());
      }

      // フォームを送信
      console.log('フォームを送信します...');
      submit(formData, {
        method: "post",
        replace: true, // 現在のページを置き換え
        preventScrollReset: false, // スクロール位置をリセット
      });

      // モーダルを閉じる
      setConfirmModalOpen(false);
    } catch (error) {
      console.error('予約作成エラー:', error);
      setSubmitError("予約の作成中にエラーが発生しました");
      setIsSubmitting(false);
      setConfirmModalOpen(false);
    }
  }, [selectedProducts, selectedDateRange, selectedCustomer, customerName, customerEmail, customerId, bookingType, paymentMethod, isPaid, notes, totalAmount, submit]);

  // 検索結果が変わったときに検索結果を更新
  useEffect(() => {
    handleSearch();
  }, [searchQuery, handleSearch]);

  // フォーム送信状態の監視
  useEffect(() => {
    // ナビゲーション状態が変わったときに実行
    console.log('ナビゲーション状態:', navigation.state);

    if (navigation.state === 'submitting') {
      // フォーム送信中
      console.log('フォーム送信中...');
      setIsSubmitting(true);
    } else if (navigation.state === 'loading') {
      // データ読み込み中（リダイレクト中など）
      console.log('データ読み込み中...');
      setIsSubmitting(true);
    } else {
      // 送信完了または送信失敗
      console.log('送信完了または送信失敗');
      setIsSubmitting(false);

      // モーダルを閉じる
      setConfirmModalOpen(false);
    }
  }, [navigation.state]);

  // タブの設定
  const tabs = [
    {
      id: "date-selection",
      content: "日程選択",
      accessibilityLabel: "日程選択タブ",
      panelID: "date-selection-panel",
    },
    {
      id: "product-selection",
      content: "商品選択",
      accessibilityLabel: "商品選択タブ",
      panelID: "product-selection-panel",
    },
    {
      id: "customer-info",
      content: "顧客情報",
      accessibilityLabel: "顧客情報タブ",
      panelID: "customer-info-panel",
    },
    {
      id: "confirmation",
      content: "予約確認",
      accessibilityLabel: "予約確認タブ",
      panelID: "confirmation-panel",
    },
  ];

  // 予約タイプのオプション
  const bookingTypeOptions = [
    { label: "仮予約", value: "PROVISIONAL" },
    { label: "本予約", value: "CONFIRMED" },
  ];

  return (
    <Page
      title="新規予約作成"
      subtitle="レンタル商品の新規予約を作成します"
      backAction={{ content: "予約一覧に戻る", url: "/app/bookings" }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Tabs
              tabs={tabs}
              selected={selectedTabIndex}
              onSelect={(index) => setSelectedTabIndex(index)}
            />
            <Box padding="400">
              {error && (
                <Box paddingBlockEnd="400">
                  <Banner tone="critical">
                    <p>{error}</p>
                  </Banner>
                </Box>
              )}

              {submitError && (
                <Box paddingBlockEnd="400">
                  <Banner tone="critical">
                    <p>{submitError}</p>
                  </Banner>
                </Box>
              )}

              {/* 日程選択タブ */}
              {selectedTabIndex === 0 && (
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2">
                    レンタル期間を選択
                  </Text>

                  <Box paddingBlockStart="200">
                    <DatePicker
                      month={month}
                      year={year}
                      onChange={handleDateRangeChange}
                      onMonthChange={handleMonthChange}
                      selected={{
                        start: selectedDateRange.startDate || getNowInJapan(),
                        end: selectedDateRange.endDate || addDays(getNowInJapan(), 3)
                      }}
                      allowRange
                      disableDatesBefore={startOfDayJapan(addDays(getNowInJapan(), -1))} // 前日以前を無効化（当日を選択可能に）
                      locale={ja}
                      renderDayLabel={renderDayLabel}
                      multiMonth
                    />
                    <Box paddingBlockStart="200">
                      <div className="calendar-legend">
                        <div className="legend-item">
                          <span className="legend-color sunday-color"></span>
                          <span className="legend-text">日曜休業</span>
                        </div>
                        <div className="legend-item">
                          <span className="legend-color holiday-color"></span>
                          <span className="legend-text">祝日休業</span>
                        </div>
                        <div className="legend-item">
                          <span className="legend-color newyear-color"></span>
                          <span className="legend-text">年末年始</span>
                        </div>
                      </div>
                    </Box>
                  </Box>

                  <Box paddingBlockStart="400">
                    <InlineStack gap="400" align="space-between">
                      <Text as="p">
                        選択期間: {selectedDateRange.startDate && formatJapanDate(selectedDateRange.startDate)} 〜{" "}
                        {selectedDateRange.endDate && formatJapanDate(selectedDateRange.endDate)}
                      </Text>
                      <Button onClick={() => setSelectedTabIndex(1)}>次へ: 商品選択</Button>
                    </InlineStack>
                  </Box>
                </BlockStack>
              )}

              {/* 商品選択タブ */}
              {selectedTabIndex === 1 && (
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2">
                    レンタル商品を選択
                  </Text>

                  <Box paddingBlockStart="200">
                    <BlockStack gap="300">
                      <form
                        onSubmit={(e) => {
                          e.preventDefault();
                          handleSearch();
                        }}
                        style={{ display: "flex", gap: "8px", alignItems: "end" }}
                      >
                        <div style={{ flex: 1 }}>
                          <TextField
                            label="商品検索"
                            value={searchQuery}
                            onChange={setSearchQuery}
                            placeholder="商品名またはSKUで検索"
                            autoComplete="off"
                          />
                        </div>
                        <Button onClick={handleSearch} type="submit">検索</Button>
                      </form>
                      <Checkbox
                        label="貸出不可能な商品も表示する"
                        checked={showUnavailableProducts}
                        onChange={() => {
                          setShowUnavailableProducts(!showUnavailableProducts);
                          // チェックボックスの状態が変わったら検索結果を更新
                          setTimeout(() => handleSearch(), 0);
                        }}
                        helpText="選択された期間に既に予約されている商品も表示します"
                      />
                    </BlockStack>
                  </Box>

                  <Box paddingBlockStart="400">
                    <Text variant="headingMd" as="h3">
                      選択された商品
                    </Text>
                    {selectedProducts.length === 0 ? (
                      <Box padding="400" style={{ textAlign: "center" }}>
                        <Text as="p" color="subdued">
                          商品が選択されていません
                        </Text>
                      </Box>
                    ) : (
                      <ResourceList
                        resourceName={{ singular: "商品", plural: "商品" }}
                        items={selectedProducts}
                        renderItem={(item) => {
                          return (
                            <ResourceItem
                              id={item.id}
                              accessibilityLabel={`${item.title} の詳細を表示`}
                              name={item.title}
                              onClick={() => {}}
                            >
                              <InlineStack gap="400" align="space-between" blockAlign="center">
                                <Text variant="bodyMd" fontWeight="bold" as="h3">
                                  {item.title} ({item.sku})
                                </Text>
                                <Button
                                  tone="critical"
                                  onClick={() => handleProductDeselect(item.id)}
                                >
                                  削除
                                </Button>
                              </InlineStack>
                            </ResourceItem>
                          );
                        }}
                      />
                    )}
                  </Box>

                  <Divider />

                  <Box paddingBlockStart="400">
                    <Text variant="headingMd" as="h3">
                      検索結果
                    </Text>
                    {searchResults.length === 0 ? (
                      <Box padding="400" style={{ textAlign: "center" }}>
                        <Text as="p" color="subdued">
                          検索結果がありません
                        </Text>
                      </Box>
                    ) : (
                      <ResourceList
                        resourceName={{ singular: "商品", plural: "商品" }}
                        items={searchResults}
                        renderItem={(item) => {
                          const isSelected = selectedProducts.some((p) => p.id === item.id);
                          return (
                            <ResourceItem
                              id={item.id}
                              accessibilityLabel={`${item.title} の詳細を表示`}
                              name={item.title}
                              onClick={() => {
                                if (!isSelected) {
                                  handleProductSelect(item);
                                }
                              }}
                            >
                              <InlineStack gap="400" align="space-between" blockAlign="center">
                                <BlockStack gap="100">
                                  <Text variant="bodyMd" fontWeight="bold" as="h3">
                                    {item.title} ({item.sku})
                                  </Text>
                                  {selectedDateRange.startDate && selectedDateRange.endDate && (
                                    <InlineStack gap="200">
                                      {isProductAvailable(item.id, selectedDateRange.startDate, selectedDateRange.endDate) ? (
                                        <Badge tone="success">利用可能</Badge>
                                      ) : (
                                        <Badge tone="critical">予約済み</Badge>
                                      )}
                                    </InlineStack>
                                  )}
                                </BlockStack>
                                {isSelected ? (
                                  <Badge tone="success">選択済み</Badge>
                                ) : (
                                  <Button
                                    onClick={() => handleProductSelect(item)}
                                    disabled={selectedDateRange.startDate &&
                                             selectedDateRange.endDate &&
                                             !isProductAvailable(item.id, selectedDateRange.startDate, selectedDateRange.endDate) &&
                                             !showUnavailableProducts}
                                  >
                                    選択
                                  </Button>
                                )}
                              </InlineStack>
                            </ResourceItem>
                          );
                        }}
                      />
                    )}
                  </Box>

                  <Box paddingBlockStart="400">
                    <InlineStack gap="400" align="space-between">
                      <Button onClick={() => setSelectedTabIndex(0)}>戻る: 日程選択</Button>
                      <Button
                        primary
                        disabled={selectedProducts.length === 0}
                        onClick={() => setSelectedTabIndex(2)}
                      >
                        次へ: 顧客情報
                      </Button>
                    </InlineStack>
                  </Box>
                </BlockStack>
              )}

              {/* 顧客情報タブ */}
              {selectedTabIndex === 2 && (
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2">
                    顧客情報を入力
                  </Text>

                  <Box paddingBlockStart="200">
                    <ShopifyCustomerSearch
                      onSelect={handleCustomerSelect}
                      shopifyShop={shopifyShop?.url}
                    />
                  </Box>

                  {selectedCustomer && (
                    <Box paddingBlockStart="200">
                      <Banner tone="success">
                        <BlockStack gap="200">
                          <Text variant="bodyMd" fontWeight="bold">選択された顧客:</Text>
                          <Text variant="bodyMd">{selectedCustomer.displayName}</Text>
                          {selectedCustomer.email && <Text variant="bodyMd">メール: {selectedCustomer.email}</Text>}
                          {selectedCustomer.phone && <Text variant="bodyMd">電話: {selectedCustomer.phone}</Text>}
                        </BlockStack>
                      </Banner>
                    </Box>
                  )}

                  <Box paddingBlockStart="200">
                    <Text variant="headingMd" as="h3">
                      予約タイプ
                    </Text>
                    <BlockStack gap="200">
                      <BlockStack gap="200">
                        <RadioButton
                          label={
                            <InlineStack gap="200" blockAlign="center">
                              <Text variant="bodyMd">仮予約</Text>
                              <Text variant="bodyMd" color="subdued">
                                (デポジット10%のみ支払い、本予約に変更可能)
                              </Text>
                            </InlineStack>
                          }
                          checked={bookingType === "PROVISIONAL"}
                          id="booking-type-provisional"
                          name="booking-type"
                          onChange={() => setBookingType("PROVISIONAL")}
                        />
                        <RadioButton
                          label={
                            <InlineStack gap="200" blockAlign="center">
                              <Text variant="bodyMd">本予約</Text>
                              <Text variant="bodyMd" color="subdued">
                                (全額支払い、即時確定)
                              </Text>
                            </InlineStack>
                          }
                          checked={bookingType === "CONFIRMED"}
                          id="booking-type-confirmed"
                          name="booking-type"
                          onChange={() => setBookingType("CONFIRMED")}
                        />
                      </BlockStack>
                      <Box paddingBlockStart="200">
                        <Banner tone="info">
                          <Text variant="bodyMd">
                            {bookingType === "PROVISIONAL"
                              ? "仮予約では、デポジット（料金の10%）のみを支払い、予約を確保できます。後日、本予約に変更することで確定します。"
                              : "本予約では、全額を支払い、予約を即時確定します。"}
                          </Text>
                        </Banner>
                      </Box>
                    </BlockStack>
                  </Box>

                  <Box paddingBlockStart="200">
                    <Text variant="headingMd" as="h3">
                      支払い方法
                    </Text>
                    <BlockStack gap="200">
                      <RadioButton
                        label="クレジットカード決済"
                        checked={paymentMethod === "CREDIT_CARD"}
                        id="payment-method-credit"
                        name="payment-method"
                        onChange={() => setPaymentMethod("CREDIT_CARD")}
                      />
                      <RadioButton
                        label="銀行振込"
                        checked={paymentMethod === "BANK_TRANSFER"}
                        id="payment-method-bank"
                        name="payment-method"
                        onChange={() => setPaymentMethod("BANK_TRANSFER")}
                      />

                      <Box paddingBlockStart="200">
                        <Checkbox
                          label="支払い済み"
                          checked={isPaid}
                          onChange={setIsPaid}
                          helpText={isPaid
                            ? "店頭で決済が完了している場合にチェックしてください"
                            : "チェックすると直接注文が作成されます。未チェックの場合はドラフトオーダーが作成されます"}
                        />
                      </Box>

                      <Box paddingBlockStart="100">
                        <Banner tone={getPaymentBannerTone()}>
                          <Text variant="bodyMd">
                            {getPaymentDescription()}
                          </Text>
                        </Banner>
                      </Box>
                    </BlockStack>
                  </Box>

                  <Box paddingBlockStart="200">
                    <TextField
                      label="備考"
                      value={notes}
                      onChange={setNotes}
                      placeholder="備考があれば入力してください"
                      autoComplete="off"
                      multiline={3}
                    />
                  </Box>

                  <Box paddingBlockStart="400">
                    <InlineStack gap="400" align="space-between">
                      <Button onClick={() => setSelectedTabIndex(1)}>戻る: 商品選択</Button>
                      <Button
                        primary
                        disabled={!selectedCustomer}
                        onClick={() => setSelectedTabIndex(3)}
                      >
                        次へ: 予約確認
                      </Button>
                    </InlineStack>
                  </Box>
                </BlockStack>
              )}

              {/* 予約確認タブ */}
              {selectedTabIndex === 3 && (
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2">
                    予約内容の確認
                  </Text>

                  <Box paddingBlockStart="200">
                    <Card>
                      <Box padding="400">
                        <BlockStack gap="400">
                          <Text variant="headingMd" as="h3">
                            レンタル期間
                          </Text>
                          <Text as="p">
                            {formatJapanDate(selectedDateRange.startDate)} 〜{" "}
                            {formatJapanDate(selectedDateRange.endDate)}
                          </Text>

                          <Divider />

                          <Text variant="headingMd" as="h3">
                            レンタル商品
                          </Text>
                          <ResourceList
                            resourceName={{ singular: "商品", plural: "商品" }}
                            items={selectedProducts}
                            renderItem={(item) => (
                              <ResourceItem
                                id={item.id}
                                accessibilityLabel={`${item.title} の詳細を表示`}
                                name={item.title}
                                onClick={() => {}}
                              >
                                <Text variant="bodyMd" fontWeight="bold" as="h3">
                                  {item.title} ({item.sku})
                                </Text>
                              </ResourceItem>
                            )}
                          />

                          <Divider />

                          <Text variant="headingMd" as="h3">
                            料金情報
                          </Text>
                          {bookingType === "PROVISIONAL" ? (
                            <BlockStack gap="100">
                              <Text as="p" tone="subdued" textDecorationLine="line-through">
                                通常料金: {formatCurrency(totalAmount)} ({rentalDays}日間)
                              </Text>
                              <Text as="p" fontWeight="bold" tone="success">
                                デポジット料金（10%）: {formatCurrency(Math.round(totalAmount * 0.1))}
                              </Text>
                            </BlockStack>
                          ) : (
                            <Text as="p" fontWeight="bold">
                              料金: {formatCurrency(totalAmount)} ({rentalDays}日間)
                            </Text>
                          )}

                          <Divider />

                          <Text variant="headingMd" as="h3">
                            顧客情報
                          </Text>
                          <Text as="p">
                            <strong>顧客名:</strong> {customerName}
                          </Text>
                          {customerEmail && (
                            <Text as="p">
                              <strong>メールアドレス:</strong> {customerEmail}
                            </Text>
                          )}
                          <Text as="p">
                            <strong>予約タイプ:</strong>{" "}
                            {bookingType === "PROVISIONAL" ? (
                              <Badge tone="attention">仮予約</Badge>
                            ) : (
                              <Badge tone="success">本予約</Badge>
                            )}
                          </Text>
                          <Text as="p">
                            <strong>支払い方法:</strong>{" "}
                            {paymentMethod === "BANK_TRANSFER" ? (
                              <Badge tone="info">銀行振込</Badge>
                            ) : (
                              <Badge tone="success">クレジットカード決済</Badge>
                            )}
                          </Text>
                          <Text as="p">
                            <strong>支払い状況:</strong>{" "}
                            {isPaid ? (
                              <Badge tone="success">支払い済み</Badge>
                            ) : (
                              <Badge tone="warning">未払い</Badge>
                            )}
                          </Text>

                          {bookingType === "PROVISIONAL" && (
                            <Box paddingBlockStart="200">
                              <Banner tone="info">
                                <BlockStack gap="200">
                                  <Text variant="bodyMd" fontWeight="bold">
                                    仮予約情報:
                                  </Text>
                                  <Text variant="bodyMd">
                                    • デポジット（料金の10%）のみを支払い、予約を確保します。
                                  </Text>
                                  <Text variant="bodyMd">
                                    • 仮予約は開始日の7日前までに本予約に変更する必要があります。
                                  </Text>
                                  <Text variant="bodyMd">
                                    • 本予約への変更は「予約一覧」から行えます。
                                  </Text>
                                </BlockStack>
                              </Banner>
                            </Box>
                          )}

                          {notes && (
                            <Text as="p">
                              <strong>備考:</strong> {notes}
                            </Text>
                          )}
                        </BlockStack>
                      </Box>
                    </Card>
                  </Box>

                  <Box paddingBlockStart="400">
                    <InlineStack gap="400" align="space-between">
                      <Button onClick={() => setSelectedTabIndex(2)}>戻る: 顧客情報</Button>
                      <Button
                        primary
                        onClick={() => setConfirmModalOpen(true)}
                        loading={isSubmitting}
                      >
                        予約を作成
                      </Button>
                    </InlineStack>
                  </Box>
                </BlockStack>
              )}
            </Box>
          </Card>
        </Layout.Section>
      </Layout>

      {/* 予約確認モーダル */}
      <Modal
        open={confirmModalOpen}
        onClose={() => setConfirmModalOpen(false)}
        title="予約を作成しますか？"
        primaryAction={{
          content: "予約を作成",
          onAction: handleCreateBooking,
          loading: isSubmitting,
        }}
        secondaryActions={[
          {
            content: "キャンセル",
            onAction: () => setConfirmModalOpen(false),
          },
        ]}
      >
        <Modal.Section>
          <BlockStack gap="400">
            <Text as="p">
              以下の内容で予約を作成します。よろしいですか？
            </Text>
            <Text as="p">
              <strong>レンタル期間:</strong>{" "}
              {formatJapanDate(selectedDateRange.startDate)} 〜{" "}
              {formatJapanDate(selectedDateRange.endDate)}
            </Text>
            <Text as="p">
              <strong>商品数:</strong> {selectedProducts.length}点
            </Text>
            <Text as="p">
              <strong>顧客名:</strong> {customerName}
            </Text>
            <Text as="p">
              <strong>予約タイプ:</strong>{" "}
              {bookingType === "PROVISIONAL" ? (
                <Badge tone="attention">仮予約（デポジット10%のみ）</Badge>
              ) : (
                <Badge tone="success">本予約（全額支払い）</Badge>
              )}
            </Text>
            <Text as="p">
              <strong>支払い方法:</strong>{" "}
              {paymentMethod === "BANK_TRANSFER" ? (
                <Badge tone="info">銀行振込</Badge>
              ) : (
                <Badge tone="success">クレジットカード決済</Badge>
              )}
            </Text>
            <Text as="p">
              <strong>支払い状況:</strong>{" "}
              {isPaid ? (
                <Badge tone="success">支払い済み</Badge>
              ) : (
                <Badge tone="warning">未払い</Badge>
              )}
            </Text>
            <Text as="p">
              <strong>料金:</strong>{" "}
              {bookingType === "PROVISIONAL" ? (
                <>
                  <Text as="span" tone="subdued" textDecorationLine="line-through">
                    {formatCurrency(totalAmount)}
                  </Text>{" "}
                  → <Text as="span" fontWeight="bold" tone="success">{formatCurrency(Math.round(totalAmount * 0.1))}</Text>
                  <Text as="span" fontWeight="regular"> (デポジット10%)</Text>
                </>
              ) : (
                <Text as="span" fontWeight="bold">{formatCurrency(totalAmount)}</Text>
              )}
            </Text>
            {bookingType === "PROVISIONAL" && (
              <Box paddingBlockStart="200">
                <Text as="p" fontWeight="medium">
                  仮予約では、デポジット（料金の10%）のみを支払い、予約を確保します。
                  後日、本予約に変更する必要があります。
                </Text>
              </Box>
            )}
          </BlockStack>
        </Modal.Section>
      </Modal>
    </Page>
  );
}