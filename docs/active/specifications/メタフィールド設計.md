# メタフィールド設計

## 概要

商品バリエーション方式の実装において、以下のメタフィールドを使用します。この文書では、各メタフィールドの目的、構造、および使用方法について説明します。

## メタフィールド一覧

### 1. rental.basic_info

**タイプ**: JSON
**目的**: 商品の基本情報を保存

**構造**:
```json
{
  "productCode": "212-05-023",
  "detailCode": "BK001",
  "kana": "デザイナーズチェア",
  "location": "NY",
  "status": "available"
}
```

**フィールド説明**:
- `productCode`: 商品コード（例: "212-05-023"）
- `detailCode`: 詳細コード（例: "BK001"）
- `kana`: 商品名のカナ表記
- `location`: 在庫場所（"NY"または"PR"）
- `status`: 商品の状態（"available"/"maintenance"/"damaged"/"unavailable"）

### 2. rental.pricing

**タイプ**: JSON
**目的**: 料金設定を保存

**構造**:
```json
{
  "basePrice": 5000,
  "depositRate": 0.1,
  "discountRules": {
    "day2_6_rate": 0.2,
    "day7_plus_rate": 0.1
  },
  "minimumDays": 1,
  "maximumDays": 30,
  "variantPrices": {
    "1day": 5000,
    "2day": 6000,
    "3day": 7000,
    "4day": 8000,
    "5day": 9000,
    "6day": 10000,
    "7day": 11000,
    "8plus": 11000
  }
}
```

**フィールド説明**:
- `basePrice`: 基本価格（1日レンタルの価格）
- `depositRate`: デポジット率（例: 0.1 = 10%）
- `discountRules`: 割引ルール
  - `day2_6_rate`: 2〜6日目の割引率（例: 0.2 = 20%）
  - `day7_plus_rate`: 7日目以降の割引率（例: 0.1 = 10%）
- `minimumDays`: 最小レンタル日数
- `maximumDays`: 最大レンタル日数
- `variantPrices`: 各バリエーションの価格

### 3. rental.variant_mapping

**タイプ**: JSON
**目的**: 日数とバリエーションIDのマッピングを保存

**構造**:
```json
{
  "1day": "gid://shopify/ProductVariant/12345678901234",
  "2day": "gid://shopify/ProductVariant/12345678901235",
  "3day": "gid://shopify/ProductVariant/12345678901236",
  "4day": "gid://shopify/ProductVariant/12345678901237",
  "5day": "gid://shopify/ProductVariant/12345678901238",
  "6day": "gid://shopify/ProductVariant/12345678901239",
  "7day": "gid://shopify/ProductVariant/12345678901240",
  "8plus": "gid://shopify/ProductVariant/12345678901241"
}
```

**フィールド説明**:
- 各キーは日数を表す（"1day"〜"8plus"）
- 各値はShopifyのバリエーションIDを表す

### 4. rental.variation_type

**タイプ**: 単一行テキスト
**目的**: バリエーションの種類を表す
**値**: "レンタル日数"（固定）

### 5. rental.variation_group

**タイプ**: 単一行テキスト
**目的**: 関連商品をグループ化する
**値**: 例: "CHAIR-A"（同じデザインの椅子のグループ）

## メタフィールドの必要性分析

### 1. physicalItemId

**結論**: 必須ではない

**理由**:
- 既存のフィールド（`productCode`と`detailCode`）で物理商品を特定可能
- `variantId`と`productId`の組み合わせからも物理商品を特定可能

**代替案**:
- `rental.basic_info`の`productCode`と`detailCode`から物理商品を特定

### 2. レンタル料金設定（rental.pricing）

**結論**: 必要

**理由**:
- 日数に応じた料金計算のルールを保存
- 商品バリエーション方式でも料金計算の基準として使用
- `variantPrices`フィールドを追加して各バリエーションの価格を保存

### 3. レンタル状態（rental.basic_info.status）

**結論**: 必要

**理由**:
- 商品の利用可能状態を管理するために必要
- 物理バリエーションごとの状態管理に使用可能

### 4. バリエーショングループ（rental.variation_group）

**結論**: オプション

**理由**:
- 物理バリエーションを標準バリエーションとして管理する場合、役割は減少
- 検索やフィルタリングで同じグループの商品を見つけるのに役立つ

### 5. バリエーションタイプ（rental.variation_type）

**結論**: オプション

**理由**:
- 物理バリエーションを標準バリエーションのオプション1として管理する場合、「レンタル日数」固定になる
- フロントエンドでのバリエーション表示に使用可能

### 6. 色（color）

**結論**: タグに移行可能

**理由**:
- コレクションでのフィルタリングが主な目的であればタグが適切
- APIからのアクセスや構造化データとしての利用が必要な場合はメタフィールドを維持

## 推奨メタフィールド構成

商品バリエーション方式での推奨メタフィールド構成は以下の通りです：

### 必須メタフィールド

1. **rental.basic_info** (JSON)
2. **rental.pricing** (JSON)
3. **rental.variant_mapping** (JSON)

### オプションメタフィールド

1. **rental.variation_group** (単一行テキスト)
2. **rental.variation_type** (単一行テキスト)
3. **色、素材、サイズなど** (タグに移行可能)

## メタフィールドの自動設定

メタフィールドの自動設定は、以下の方法で実装します：

1. **Webhookを使用した自動設定**:
   - 商品作成・更新時にWebhookを発火
   - Webhookハンドラーでメタフィールドを設定

2. **バリエーション命名規則の統一**:
   - 1日レンタル: "商品名 - 1日" または "商品名 - 1day"
   - 2日レンタル: "商品名 - 2日" または "商品名 - 2day"
   - ...
   - 8日以上レンタル: "商品名 - 8日以上" または "商品名 - 8plus"

3. **SKU命名規則の統一**:
   - 1日レンタル: "基本SKU-1D"
   - 2日レンタル: "基本SKU-2D"
   - ...
   - 8日以上レンタル: "基本SKU-8P"

## まとめ

商品バリエーション方式の実装において、適切なメタフィールド設計と自動設定の仕組みを構築することで、効率的な商品管理が可能になります。Webhookを活用した自動設定により、手動でのメタフィールド設定が不要になり、運用の負担が軽減されます。
