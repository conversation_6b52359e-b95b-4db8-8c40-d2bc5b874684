import { LoaderFunctionArgs, json } from "@remix-run/node";
import { authenticate } from "../shopify.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const { admin } = await authenticate.admin(request);
  
  const url = new URL(request.url);
  const query = url.searchParams.get("query") || "";
  
  if (!query) {
    return json({ customers: [] });
  }
  
  try {
    // Shopify GraphQL APIを使用して顧客を検索
    const response = await admin.graphql(
      `#graphql
        query searchCustomers($query: String!) {
          customers(first: 10, query: $query) {
            edges {
              node {
                id
                displayName
                firstName
                lastName
                email
                phone
              }
            }
          }
        }`,
      {
        variables: {
          query
        }
      }
    );
    
    const responseJson = await response.json();
    
    // レスポンスを整形
    const customers = responseJson.data.customers.edges.map(
      ({ node }: any) => ({
        id: node.id,
        displayName: node.displayName || `${node.firstName || ''} ${node.lastName || ''}`.trim(),
        firstName: node.firstName,
        lastName: node.lastName,
        email: node.email,
        phone: node.phone
      })
    );
    
    return json({ customers });
  } catch (error) {
    console.error("顧客検索エラー:", error);
    return json({ error: "顧客の検索中にエラーが発生しました", customers: [] });
  }
}
