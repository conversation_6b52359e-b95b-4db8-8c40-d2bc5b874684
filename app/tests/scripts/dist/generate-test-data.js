"use strict";
/**
 * テスト用データ生成スクリプト
 *
 * このスクリプトは、テスト用の商品データと予約データを生成します。
 * 実行方法: npx ts-node app/tests/scripts/generate-test-data.ts
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var client_1 = require("@prisma/client");
var uuid_1 = require("uuid");
var date_fns_1 = require("date-fns");
// Prismaクライアントの初期化
var prisma = new client_1.PrismaClient();
// テスト用ショップ名
var TEST_SHOP = 'test-shop.myshopify.com';
// テスト用顧客データ
var TEST_CUSTOMERS = [
    { email: '<EMAIL>', name: 'テスト顧客1' },
    { email: '<EMAIL>', name: 'テスト顧客2' },
    { email: '<EMAIL>', name: 'テスト顧客3' },
];
// テスト用商品データ
var TEST_PRODUCTS = [
    {
        title: 'テスト【85_ドレスソファ オフホワイト 1シーター】',
        sku: 'TEST-001',
        price: 23000,
        status: 'ACTIVE',
        shopifyId: '8942696661160'
    },
    {
        title: 'テスト【砧】アンティークストライプ ソファ 花柄イエロー 1シーター',
        sku: 'TEST-002',
        price: 25000,
        status: 'ACTIVE',
        shopifyId: '8942696693928'
    },
    {
        title: 'テスト【85_●ベーシックソファ オフホワイト 1シーター】',
        sku: 'TEST-003',
        price: 20000,
        status: 'ACTIVE',
        shopifyId: '8942696726696'
    },
];
// メイン関数
function main() {
    return __awaiter(this, void 0, void 0, function () {
        var products, error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    console.log('テスト用データの生成を開始します...');
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 5, 6, 8]);
                    // 既存のテストデータをクリーンアップ
                    return [4 /*yield*/, cleanupTestData()];
                case 2:
                    // 既存のテストデータをクリーンアップ
                    _a.sent();
                    return [4 /*yield*/, createTestProducts()];
                case 3:
                    products = _a.sent();
                    // テスト用予約データの作成
                    return [4 /*yield*/, createTestBookings(products)];
                case 4:
                    // テスト用予約データの作成
                    _a.sent();
                    console.log('テスト用データの生成が完了しました！');
                    return [3 /*break*/, 8];
                case 5:
                    error_1 = _a.sent();
                    console.error('テスト用データの生成中にエラーが発生しました:', error_1);
                    return [3 /*break*/, 8];
                case 6: return [4 /*yield*/, prisma.$disconnect()];
                case 7:
                    _a.sent();
                    return [7 /*endfinally*/];
                case 8: return [2 /*return*/];
            }
        });
    });
}
// 既存のテストデータをクリーンアップ
function cleanupTestData() {
    return __awaiter(this, void 0, void 0, function () {
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    console.log('既存のテストデータをクリーンアップしています...');
                    // テスト用予約データの削除
                    return [4 /*yield*/, prisma.booking.deleteMany({
                            where: {
                                shop: TEST_SHOP,
                            },
                        })];
                case 1:
                    // テスト用予約データの削除
                    _a.sent();
                    // テスト用商品データの削除
                    return [4 /*yield*/, prisma.product.deleteMany({
                            where: {
                                shop: TEST_SHOP,
                                title: {
                                    startsWith: 'テスト',
                                },
                            },
                        })];
                case 2:
                    // テスト用商品データの削除
                    _a.sent();
                    console.log('既存のテストデータをクリーンアップしました');
                    return [2 /*return*/];
            }
        });
    });
}
// テスト用商品データの作成
function createTestProducts() {
    return __awaiter(this, void 0, void 0, function () {
        var products, _i, TEST_PRODUCTS_1, productData, product;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    console.log('テスト用商品データを作成しています...');
                    products = [];
                    _i = 0, TEST_PRODUCTS_1 = TEST_PRODUCTS;
                    _a.label = 1;
                case 1:
                    if (!(_i < TEST_PRODUCTS_1.length)) return [3 /*break*/, 4];
                    productData = TEST_PRODUCTS_1[_i];
                    return [4 /*yield*/, prisma.product.create({
                            data: {
                                id: (0, uuid_1.v4)(),
                                shopifyId: productData.shopifyId,
                                title: productData.title,
                                sku: productData.sku,
                                price: productData.price,
                                status: productData.status,
                                shop: TEST_SHOP,
                                createdAt: new Date(),
                                updatedAt: new Date(),
                            },
                        })];
                case 2:
                    product = _a.sent();
                    products.push(product);
                    console.log("\u5546\u54C1\u3092\u4F5C\u6210\u3057\u307E\u3057\u305F: ".concat(product.title));
                    _a.label = 3;
                case 3:
                    _i++;
                    return [3 /*break*/, 1];
                case 4: return [2 /*return*/, products];
            }
        });
    });
}
// テスト用予約データの作成
function createTestBookings(products) {
    return __awaiter(this, void 0, void 0, function () {
        var today, i, product, customer;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    console.log('テスト用予約データを作成しています...');
                    today = new Date();
                    today.setHours(0, 0, 0, 0);
                    i = 0;
                    _a.label = 1;
                case 1:
                    if (!(i < products.length)) return [3 /*break*/, 8];
                    product = products[i];
                    customer = TEST_CUSTOMERS[i % TEST_CUSTOMERS.length];
                    // 仮予約（過去）
                    return [4 /*yield*/, prisma.booking.create({
                            data: {
                                id: (0, uuid_1.v4)(),
                                bookingId: "TEST-PROV-PAST-".concat(i + 1),
                                productId: product.id,
                                variantId: "gid://shopify/ProductVariant/".concat(8000000000 + i),
                                shop: TEST_SHOP,
                                status: 'PROVISIONAL',
                                bookingType: 'PROVISIONAL',
                                startDate: (0, date_fns_1.subDays)(today, 14 + i),
                                endDate: (0, date_fns_1.subDays)(today, 10 + i),
                                customerEmail: customer.email,
                                customerName: customer.name,
                                transactionId: (0, uuid_1.v4)(),
                                createdAt: (0, date_fns_1.subDays)(today, 20 + i),
                                updatedAt: (0, date_fns_1.subDays)(today, 20 + i),
                            },
                        })];
                case 2:
                    // 仮予約（過去）
                    _a.sent();
                    // 確定予約（過去）
                    return [4 /*yield*/, prisma.booking.create({
                            data: {
                                id: (0, uuid_1.v4)(),
                                bookingId: "TEST-CONF-PAST-".concat(i + 1),
                                productId: product.id,
                                variantId: "gid://shopify/ProductVariant/".concat(8000000000 + i),
                                shop: TEST_SHOP,
                                status: 'COMPLETED',
                                bookingType: 'CONFIRMED',
                                startDate: (0, date_fns_1.subDays)(today, 7 + i),
                                endDate: (0, date_fns_1.subDays)(today, 5 + i),
                                customerEmail: customer.email,
                                customerName: customer.name,
                                transactionId: (0, uuid_1.v4)(),
                                createdAt: (0, date_fns_1.subDays)(today, 10 + i),
                                updatedAt: (0, date_fns_1.subDays)(today, 10 + i),
                            },
                        })];
                case 3:
                    // 確定予約（過去）
                    _a.sent();
                    // 仮予約（現在）
                    return [4 /*yield*/, prisma.booking.create({
                            data: {
                                id: (0, uuid_1.v4)(),
                                bookingId: "TEST-PROV-CURRENT-".concat(i + 1),
                                productId: product.id,
                                variantId: "gid://shopify/ProductVariant/".concat(8000000000 + i),
                                shop: TEST_SHOP,
                                status: 'PROVISIONAL',
                                bookingType: 'PROVISIONAL',
                                startDate: (0, date_fns_1.addDays)(today, 1 + i),
                                endDate: (0, date_fns_1.addDays)(today, 3 + i),
                                customerEmail: customer.email,
                                customerName: customer.name,
                                transactionId: (0, uuid_1.v4)(),
                                createdAt: (0, date_fns_1.subDays)(today, 2),
                                updatedAt: (0, date_fns_1.subDays)(today, 2),
                            },
                        })];
                case 4:
                    // 仮予約（現在）
                    _a.sent();
                    // 確定予約（現在）
                    return [4 /*yield*/, prisma.booking.create({
                            data: {
                                id: (0, uuid_1.v4)(),
                                bookingId: "TEST-CONF-CURRENT-".concat(i + 1),
                                productId: product.id,
                                variantId: "gid://shopify/ProductVariant/".concat(8000000000 + i),
                                shop: TEST_SHOP,
                                status: 'CONFIRMED',
                                bookingType: 'CONFIRMED',
                                startDate: (0, date_fns_1.addDays)(today, 5 + i),
                                endDate: (0, date_fns_1.addDays)(today, 7 + i),
                                customerEmail: customer.email,
                                customerName: customer.name,
                                transactionId: (0, uuid_1.v4)(),
                                createdAt: (0, date_fns_1.subDays)(today, 1),
                                updatedAt: (0, date_fns_1.subDays)(today, 1),
                            },
                        })];
                case 5:
                    // 確定予約（現在）
                    _a.sent();
                    // 仮予約（未来）
                    return [4 /*yield*/, prisma.booking.create({
                            data: {
                                id: (0, uuid_1.v4)(),
                                bookingId: "TEST-PROV-FUTURE-".concat(i + 1),
                                productId: product.id,
                                variantId: "gid://shopify/ProductVariant/".concat(8000000000 + i),
                                shop: TEST_SHOP,
                                status: 'PROVISIONAL',
                                bookingType: 'PROVISIONAL',
                                startDate: (0, date_fns_1.addDays)(today, 14 + i),
                                endDate: (0, date_fns_1.addDays)(today, 16 + i),
                                customerEmail: customer.email,
                                customerName: customer.name,
                                transactionId: (0, uuid_1.v4)(),
                                createdAt: today,
                                updatedAt: today,
                            },
                        })];
                case 6:
                    // 仮予約（未来）
                    _a.sent();
                    console.log("\u5546\u54C1 \"".concat(product.title, "\" \u306E\u4E88\u7D04\u30C7\u30FC\u30BF\u3092\u4F5C\u6210\u3057\u307E\u3057\u305F"));
                    _a.label = 7;
                case 7:
                    i++;
                    return [3 /*break*/, 1];
                case 8: return [2 /*return*/];
            }
        });
    });
}
// スクリプトの実行
main()
    .catch(function (e) {
    console.error(e);
    process.exit(1);
})
    .finally(function () { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0: return [4 /*yield*/, prisma.$disconnect()];
            case 1:
                _a.sent();
                return [2 /*return*/];
        }
    });
}); });
