import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品検索のクエリ
const GET_PRODUCTS_QUERY = gql`
  query getProducts($query: String!, $first: Int!, $after: String) {
    products(first: $first, after: $after, query: $query) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          handle
          vendor
          productType
          tags
          variants(first: 50) {
            edges {
              node {
                id
                title
                sku
                price
                inventoryQuantity
              }
            }
          }
        }
      }
    }
  }
`;

// バリアントのSKUを更新するミューテーション
const UPDATE_VARIANT_SKU_MUTATION = gql`
  mutation productVariantUpdate($input: ProductVariantInput!) {
    productVariantUpdate(input: $input) {
      productVariant {
        id
        sku
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 商品を検索する関数
async function getProducts(query = '', limit = 50) {
  const products = [];
  let hasNextPage = true;
  let cursor = null;

  while (hasNextPage) {
    try {
      const variables = {
        query,
        first: limit,
        after: cursor
      };

      const result = await graphQLClient.request(GET_PRODUCTS_QUERY, variables);
      
      const edges = result.products.edges;
      products.push(...edges.map(edge => edge.node));
      
      hasNextPage = result.products.pageInfo.hasNextPage;
      cursor = result.products.pageInfo.endCursor;
    } catch (error) {
      console.error('商品検索エラー:', error);
      hasNextPage = false;
    }
  }

  return products;
}

// バリアントのSKUを更新する関数
async function updateVariantSKU(variantId, newSKU) {
  try {
    const variables = {
      input: {
        id: variantId,
        sku: newSKU
      }
    };
    
    const result = await graphQLClient.request(UPDATE_VARIANT_SKU_MUTATION, variables);
    
    if (result.productVariantUpdate.userErrors.length > 0) {
      console.error('バリアントSKU更新エラー:', result.productVariantUpdate.userErrors);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('バリアントSKU更新中にエラーが発生しました:', error);
    return false;
  }
}

// SKUを正規化する関数
function normalizeVariantSKU(sku, variantTitle, index) {
  // SKUが空の場合は処理しない
  if (!sku) return sku;
  
  // 既に正規化されているSKUは処理しない
  if (sku.match(/^[0-9]+-[0-9]+-[0-9]+$/)) return sku;
  
  // 基本的なSKUパターン: 商品コード-詳細コード
  const match = sku.match(/^(\d+)-(\d+)/);
  if (!match) return sku;
  
  const productCode = match[1];
  const detailCode = match[2];
  
  // レンタル日数に基づいてSKUを生成
  if (variantTitle.includes('日レンタル')) {
    const dayMatch = variantTitle.match(/(\d+)日/);
    if (dayMatch) {
      const days = dayMatch[1];
      // 1日の場合は001、2日の場合は002、...
      const daySuffix = days.padStart(3, '0');
      return `${productCode}-${detailCode}-${daySuffix}`;
    } else if (variantTitle.includes('8日以上')) {
      // 8日以上の場合は008
      return `${productCode}-${detailCode}-008`;
    }
  }
  
  // 商品番号に基づいてSKUを生成
  if (variantTitle.includes('台目')) {
    const itemMatch = variantTitle.match(/(\d+)台目/);
    if (itemMatch) {
      const itemNumber = itemMatch[1];
      return `${productCode}-${detailCode}-${itemNumber}`;
    }
  }
  
  // Default Titleの場合は001
  if (variantTitle === 'Default Title') {
    return `${productCode}-${detailCode}-001`;
  }
  
  // その他の場合はインデックスを使用
  return `${productCode}-${detailCode}-${(index + 1).toString().padStart(3, '0')}`;
}

// 商品のバリアントSKUを正規化する関数
async function normalizeProductSKUs(product) {
  const variants = product.variants.edges.map(edge => edge.node);
  let successCount = 0;
  
  console.log(`商品 ${product.title} のバリアントSKUを正規化中...`);
  
  for (let i = 0; i < variants.length; i++) {
    const variant = variants[i];
    const oldSKU = variant.sku;
    const newSKU = normalizeVariantSKU(oldSKU, variant.title, i);
    
    // SKUが変更されない場合はスキップ
    if (oldSKU === newSKU) {
      console.log(`バリアント ${variant.title} のSKU ${oldSKU} は既に正規化されています。スキップします。`);
      continue;
    }
    
    console.log(`バリアント ${variant.title} のSKUを ${oldSKU} から ${newSKU} に更新中...`);
    
    // SKUを更新
    const success = await updateVariantSKU(variant.id, newSKU);
    
    if (success) {
      console.log(`バリアント ${variant.title} のSKUを ${oldSKU} から ${newSKU} に更新しました。`);
      successCount++;
    } else {
      console.error(`バリアント ${variant.title} のSKU更新に失敗しました。`);
    }
  }
  
  return successCount;
}

// メイン処理
async function main() {
  try {
    // 商品を検索
    console.log('商品を検索中...');
    const products = await getProducts('tag:レンタル商品', 10);
    console.log(`${products.length}件の商品が見つかりました`);
    
    // 各商品のバリアントSKUを正規化
    let totalSuccessCount = 0;
    for (const product of products) {
      console.log(`\n商品 ${product.title} の処理を開始...`);
      
      // バリアントSKUを正規化
      const successCount = await normalizeProductSKUs(product);
      
      console.log(`商品 ${product.title} の処理が完了しました。${successCount}件のバリアントSKUを更新しました。`);
      totalSuccessCount += successCount;
    }
    
    console.log(`\n処理が完了しました。合計${totalSuccessCount}件のバリアントSKUを更新しました。`);
  } catch (error) {
    console.error('エラーが発生しました:', error);
  }
}

// 実行
main();
