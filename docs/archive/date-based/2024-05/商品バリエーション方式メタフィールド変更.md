# 商品バリエーション方式メタフィールド変更

## 概要

商品バリエーション方式の実装に伴い、メタフィールド構造の変更が必要です。このドキュメントでは、現在のメタフィールド構造と新しい商品バリエーション方式に必要な変更内容を説明します。

## 現在のメタフィールド構造

現在のシステムでは、以下のメタフィールドが使用されています：

### 1. バリエーション管理関連メタフィールド

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| - | バリエーショングループ | 単一行のテキスト | バリエーショングループID | 関連商品のグループ化に使用 |
| - | バリエーションタイプ | 単一行のテキスト | バリエーションタイプ | バリエーションの種類（色、サイズなど）を管理 |
| - | レンタル状態 | 単一行のテキスト | 商品のレンタル状態 | 管理者が商品の状態を管理するために使用 |

### 2. 料金計算関連メタフィールド

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| rental | pricing | JSON | レンタル料金設定 | 基本料金、割引率などの料金計算用データを管理 |
| rental | basic_info | JSON | レンタル商品基本情報 | 商品コード、詳細コード、在庫場所などを管理 |

`rental.pricing`の現在の構造：
```json
{
  "basePrice": 5000,
  "depositRate": 0.1,
  "discountRules": {
    "day2_6_rate": 0.2,
    "day7_plus_rate": 0.1
  },
  "minimumDays": 1,
  "maximumDays": 30
}
```

`rental.basic_info`の現在の構造：
```json
{
  "productCode": "TEST-VAR",
  "detailCode": "001",
  "kana": "テスト商品",
  "location": "NY",
  "status": "available"
}
```

## 商品バリエーション方式での変更点

商品バリエーション方式では、以下の変更が必要です：

### 1. バリエーション管理の変更

現在のシステムでは、バリエーションは独立した商品として管理され、「バリエーショングループ」と「バリエーションタイプ」メタフィールドで関連付けられています。新しい方式では、Shopifyの標準バリエーション機能を使用するため、これらのメタフィールドの役割が変わります。

#### 変更内容：

1. **「バリエーショングループ」メタフィールド**
   - **現在**: 関連商品をグループ化するためのID（名前空間なし）
   - **新方式**: 名前空間付きの`rental.variation_group`に移行または削除
   - **変更**: 既存のメタフィールドは削除し、必要に応じて`rental.variation_group`を新設

2. **「バリエーションタイプ」メタフィールド**
   - **現在**: バリエーションの種類（色、サイズなど）（名前空間なし）
   - **新方式**: 名前空間付きの`rental.variation_type`に移行し、値を「レンタル日数」に固定
   - **変更**: 既存のメタフィールドは削除し、`rental.variation_type`を新設

3. **「レンタル状態」メタフィールド**
   - **現在**: 商品のレンタル状態を管理（名前空間なし）
   - **新方式**: そのまま維持（管理者が頻繁に使用するため）
   - **変更**: 変更なし

### 2. 料金計算の変更

現在のシステムでは、`rental.pricing`メタフィールドのJSONデータに基づいて動的に料金を計算しています。新しい方式では、各バリエーションに事前計算された固定価格を設定するため、料金計算ロジックが変わります。

#### 変更内容：

1. **`rental.pricing`メタフィールド**
   - **現在**: 動的な料金計算のためのルールを保存
   - **新方式**: バリエーション価格の計算に使用するが、実際の価格はバリエーションに設定
   - **変更**: メタフィールドは維持するが、新しい構造に更新

2. **新しい`rental.pricing`構造**:
```json
{
  "basePrice": 5000,
  "depositRate": 0.1,
  "discountRules": {
    "day2_6_rate": 0.2,
    "day7_plus_rate": 0.1
  },
  "minimumDays": 1,
  "maximumDays": 30,
  "variantPrices": {
    "1day": 5000,
    "2day": 6000,
    "3day": 7000,
    "4day": 8000,
    "5day": 9000,
    "6day": 10000,
    "7day": 11000,
    "8plus": 11000
  }
}
```

### 3. 新しいメタフィールド

商品バリエーション方式では、以下の新しいメタフィールドが必要です：

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| rental | variant_mapping | JSON | バリエーションマッピング | 日数とバリエーションIDのマッピングを管理 |
| rental | variation_type | 単一行のテキスト | バリエーションタイプ | 「レンタル日数」固定値 |

`rental.variant_mapping`の構造：
```json
{
  "1day": "gid://shopify/ProductVariant/12345678901234",
  "2day": "gid://shopify/ProductVariant/12345678901235",
  "3day": "gid://shopify/ProductVariant/12345678901236",
  "4day": "gid://shopify/ProductVariant/12345678901237",
  "5day": "gid://shopify/ProductVariant/12345678901238",
  "6day": "gid://shopify/ProductVariant/12345678901239",
  "7day": "gid://shopify/ProductVariant/12345678901240",
  "8plus": "gid://shopify/ProductVariant/12345678901241"
}
```

## 変更スクリプト

以下のスクリプトを使用して、メタフィールド構造を更新します：

1. `scripts/update-variant-metafields.js`: バリエーション関連メタフィールドを更新
2. `scripts/create-variant-mapping-metafield.js`: 新しい`variant_mapping`メタフィールドを作成

## 実行手順

### 1. メタフィールド構造の更新

```bash
# バリエーション関連メタフィールドを更新
npm run update:variant-metafields

# 新しいvariant_mappingメタフィールドを作成
npm run create:variant-mapping
```

### 2. 変更の確認

1. Shopify管理画面で商品のメタフィールドを確認
2. `rental.pricing`メタフィールドに`variantPrices`が追加されていることを確認
3. `rental.variant_mapping`メタフィールドが作成されていることを確認
4. `rental.variation_type`メタフィールドが「レンタル日数」に更新されていることを確認

## 元に戻す方法

商品バリエーション方式からドラフトオーダー方式に戻す場合は、以下の手順を実行します：

```bash
# 元のメタフィールド構造に戻す
npm run restore:original-metafields
```

その後、必要に応じて以下の作業を行います：

1. Shopifyの商品バリエーションを削除し、元の独立した商品構造に戻す
2. Shopify管理画面で商品のメタフィールドを確認し、正しく復元されていることを確認

## 推奨メタフィールド設定

商品バリエーション方式の実装に必要なメタフィールドの推奨設定は以下の通りです：

### 必須メタフィールド

| 名前空間 | キー | 日本語表示名 | タイプ | 説明 | 状態 |
|---------|-----|------------|-------|------|------|
| rental | pricing | レンタル料金設定 | JSON | 料金計算用データ | 既存・維持 |
| rental | basic_info | レンタル商品基本情報 | JSON | 商品の基本情報 | 既存・維持 |
| rental | variant_mapping | バリエーションマッピング | JSON | 日数とバリエーションIDのマッピング | 新規追加 |
| rental | variation_type | バリエーションタイプ | 単一行のテキスト | バリエーションの種類 | 新規追加 |
| - | レンタル状態 | レンタル状態 | 単一行のテキスト | 商品のレンタル状態 | 既存・維持 |

### 削除検討可能なメタフィールド

| 名前空間 | キー | 日本語表示名 | タイプ | 説明 | 対応 |
|---------|-----|------------|-------|------|------|
| - | バリエーショングループ | バリエーショングループ | 単一行のテキスト | バリエーショングループID | 削除または`rental.variation_group`に移行 |
| - | バリエーションタイプ | バリエーションタイプ | 単一行のテキスト | バリエーションタイプ | 削除して`rental.variation_type`に移行 |

### 設定手順

1. Shopify管理画面の「設定」→「メタフィールド」から、必要なメタフィールドを追加します
2. 既存のメタフィールドは、Webhookハンドラーが正常に動作することを確認してから削除を検討します
3. 特に「レンタル状態」メタフィールドは、管理者が頻繁に使用するため維持することをお勧めします

## 注意点

1. メタフィールド構造の変更は、既存のデータに影響を与える可能性があります
2. 変更前に必ずバックアップを取得してください
3. テスト環境で十分にテストしてから本番環境に適用してください
4. メタフィールドの名前空間（`rental.`）は重要です。Webhookハンドラーはこの名前空間を使用してメタフィールドを識別します
5. JSONメタフィールドの内容は自動的に設定されるため、手動で編集する必要はありません
