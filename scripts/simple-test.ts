/**
 * シンプルなテストスクリプト
 * 
 * このスクリプトは、order-creator.tsの動作を単純にテストします。
 * 
 * 実行方法: npx tsx scripts/simple-test.ts
 */

import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';
import { createOrderFromBooking } from '../app/utils/booking/order-creator';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// シンプルなモックAdminApiContext
const mockAdminApi = {
  graphql: async (query: string, options?: any) => {
    console.log('GraphQLリクエスト:', { query: query.substring(0, 100) + '...', variables: options?.variables });
    
    // 成功レスポンスをモック
    return {
      json: async () => ({
        data: {
          orderCreate: {
            order: {
              id: 'gid://shopify/Order/mock-order-id',
              name: '#1234',
              totalPrice: '1500.00'
            },
            userErrors: []
          }
        }
      })
    };
  }
};

// メイン実行関数
async function runSimpleTest() {
  try {
    console.log('シンプルなテストを開始します...');
    
    // 環境変数を確認
    const bookingId = process.env.TEST_BOOKING_ID;
    if (!bookingId) {
      throw new Error('TEST_BOOKING_ID環境変数が設定されていません');
    }
    
    console.log(`テスト対象の予約ID: ${bookingId}`);
    
    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: { product: true }
    });
    
    if (!booking) {
      throw new Error(`予約ID ${bookingId} が見つかりません`);
    }
    
    console.log('予約情報:', {
      id: booking.id,
      bookingId: booking.bookingId,
      productTitle: booking.product.title,
      startDate: booking.startDate,
      endDate: booking.endDate,
      customerId: booking.customerId
    });
    
    // 注文作成を実行
    console.log('注文作成を実行します...');
    const result = await createOrderFromBooking(
      prisma,
      mockAdminApi as any,
      bookingId,
      3 // リトライ回数
    );
    
    console.log('注文作成結果:', result);
    
    // 更新された予約情報を取得
    const updatedBooking = await prisma.booking.findUnique({
      where: { id: bookingId },
      select: {
        id,
        bookingId: true,
        shopifyOrderId: true,
        shopifyOrderName: true
      }
    });
    
    console.log('更新後の予約情報:', updatedBooking);
    
    return {
      result,
      updatedBooking
    };
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    throw error;
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// スクリプトを実行
console.log('シンプルなテストスクリプトを開始します...');
runSimpleTest()
  .then((results) => {
    console.log('テストが完了しました');
    console.log('結果:', JSON.stringify(results, null, 2));
    process.exit(0);
  })
  .catch((error) => {
    console.error('テストが失敗しました:', error);
    console.error('エラーの詳細:', error.stack);
    process.exit(1);
  });
