/**
 * テストデータセットアップページ
 *
 * このページは基本的なデータモデルのテストと準備を行います。
 * 商品、顧客、予約データの作成と取得を確認できます。
 */

import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, useSubmit, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  BlockStack,
  Text,
  Select,
  TextField,
  Button,
  Banner,
  InlineStack,
  Box,
  Divider,
  DataTable,
  Link,
  Frame,
  Modal,
  ButtonGroup,
  Tabs
} from "@shopify/polaris";
import { useState, useCallback, useEffect } from "react";
import { v4 as uuidv4 } from 'uuid';
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";

/**
 * ローダー関数
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // 商品データを取得
    const products = await prisma.product.findMany({
      where: { shop },
      select: {
        id: true,
        title: true,
        sku: true,
        status: true,
        price: true,
        shopifyId: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    // 予約データを取得
    const bookings = await prisma.booking.findMany({
      where: { shop },
      select: {
        id: true,
        bookingId: true,
        productId: true,
        startDate: true,
        endDate: true,
        status: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    // Date型を文字列に変換
    const serializedProducts = products.map(p => ({
      ...p,
      createdAt: p.createdAt.toISOString(),
      price: p.price.toString()
    }));

    const serializedBookings = bookings.map(b => ({
      ...b,
      startDate: b.startDate.toISOString(),
      endDate: b.endDate.toISOString(),
      createdAt: b.createdAt.toISOString()
    }));

    // Shopifyから商品情報を取得
    const shopifyProductsResponse = await admin.graphql(`
      query getProducts {
        products(first: 10) {
          edges {
            node {
              id
              title
              handle
              status
              variants(first: 1) {
                edges {
                  node {
                    id
                    title
                    sku
                    price
                  }
                }
              }
            }
          }
        }
      }
    `);

    const shopifyProductsData = await shopifyProductsResponse.json();
    const shopifyProducts = shopifyProductsData.data?.products?.edges?.map(edge => ({
      id: edge.node.id,
      title: edge.node.title,
      handle: edge.node.handle,
      status: edge.node.status,
      variant: edge.node.variants.edges[0]?.node || null
    })) || [];

    return json({
      products: serializedProducts,
      bookings: serializedBookings,
      shopifyProducts,
      shop
    });
  } catch (error) {
    console.error("データ取得エラー:", error);
    return json({
      products: [],
      bookings: [],
      shopifyProducts: [],
      shop: session.shop,
      error: "データの取得中にエラーが発生しました"
    });
  }
}

/**
 * アクション関数
 */
export async function action({ request }: ActionFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    const formData = await request.formData();
    const intent = formData.get("intent") as string;

    if (intent === "create-product") {
      const shopifyId = formData.get("shopifyId") as string;
      const title = formData.get("title") as string;
      const sku = formData.get("sku") as string;
      const price = formData.get("price") as string;

      if (!shopifyId || !title || !sku || !price) {
        return json({
          success: false,
          error: "必須項目が入力されていません"
        });
      }

      // 商品をデータベースに登録
      const product = await prisma.product.create({
        data: {
          shopifyId,
          title,
          sku,
          price: parseFloat(price),
          shop,
          status: "AVAILABLE",
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      return json({
        success: true,
        product
      });
    }

    if (intent === "create-booking") {
      const productId = formData.get("productId") as string;
      const startDate = formData.get("startDate") as string;
      const endDate = formData.get("endDate") as string;
      const customerEmail = formData.get("customerEmail") as string;

      if (!productId || !startDate || !endDate) {
        return json({
          success: false,
          error: "必須項目が入力されていません"
        });
      }

      // 予約をデータベースに登録
      const booking = await prisma.booking.create({
        data: {
          id: uuidv4(),
          bookingId: `BOOK-${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
          shop,
          productId,
          variantId: "test-variant",
          status: 'PROVISIONAL',
          bookingType: 'PROVISIONAL',
          startDate: new Date(startDate),
          endDate: new Date(endDate),
          customerEmail: customerEmail || "<EMAIL>",
          customerName: "テストユーザー",
          totalAmount: 10000,
          createdAt: new Date(),
          updatedAt: new Date(),
          priority: 1
        }
      });

      return json({
        success: true,
        booking
      });
    }

    if (intent === "import-shopify-product") {
      const shopifyProductId = formData.get("shopifyProductId") as string;

      if (!shopifyProductId) {
        return json({
          success: false,
          error: "商品IDが指定されていません"
        });
      }

      // Shopify商品IDからgid部分を抽出
      const idMatch = shopifyProductId.match(/gid:\/\/shopify\/Product\/(\d+)/);
      const numericId = idMatch ? idMatch[1] : shopifyProductId;

      // Shopifyから商品情報を取得
      const productResponse = await admin.graphql(`
        query getProduct($id: ID!) {
          product(id: $id) {
            id
            title
            handle
            status
            variants(first: 1) {
              edges {
                node {
                  id
                  title
                  sku
                  price
                }
              }
            }
          }
        }
      `, {
        variables: {
          id: shopifyProductId
        }
      });

      const productData = await productResponse.json();

      if (!productData.data?.product) {
        return json({
          success: false,
          error: "Shopifyから商品情報を取得できませんでした"
        });
      }

      const shopifyProduct = productData.data.product;
      const variant = shopifyProduct.variants.edges[0]?.node;

      if (!variant) {
        return json({
          success: false,
          error: "商品のバリアント情報が見つかりません"
        });
      }

      // データベースに商品を登録
      const product = await prisma.product.create({
        data: {
          shopifyId: numericId,
          title: shopifyProduct.title,
          handle: shopifyProduct.handle,
          status: shopifyProduct.status,
          shop,
          price: parseFloat(variant.price),
          sku: variant.sku || `SKU-${numericId}`,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      return json({
        success: true,
        product
      });
    }

    return json({
      success: false,
      error: "不明な操作が指定されました"
    });
  } catch (error) {
    console.error("アクションエラー:", error);
    return json({
      success: false,
      error: "処理中にエラーが発生しました: " + (error instanceof Error ? error.message : String(error))
    });
  }
}

export default function TestDataSetup() {
  const { products, bookings, shopifyProducts, shop, error } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();

  const [selectedTab, setSelectedTab] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(error || "");
  const [successMessage, setSuccessMessage] = useState("");

  // 商品作成フォーム
  const [productForm, setProductForm] = useState({
    shopifyId: "8925304946856",
    title: "デザイナーズチェア A型（レッド）",
    sku: "CHAIR-A-RED-001",
    price: "10000"
  });

  // 予約作成フォーム
  const [bookingForm, setBookingForm] = useState({
    productId: "",
    startDate: "2024-05-01", // 水曜日
    endDate: "2024-05-06", // 月曜日
    customerEmail: "<EMAIL>"
  });

  // Shopify商品インポートフォーム
  const [importForm, setImportForm] = useState({
    shopifyProductId: ""
  });

  // タブの設定
  const tabs = [
    {
      id: "products",
      content: "商品データ",
      accessibilityLabel: "商品データタブ",
      panelID: "products-panel",
    },
    {
      id: "bookings",
      content: "予約データ",
      accessibilityLabel: "予約データタブ",
      panelID: "bookings-panel",
    },
    {
      id: "shopify-products",
      content: "Shopify商品",
      accessibilityLabel: "Shopify商品タブ",
      panelID: "shopify-products-panel",
    },
  ];

  // アクションデータの処理
  useEffect(() => {
    if (actionData) {
      setIsLoading(false);

      if (actionData.success) {
        setSuccessMessage("操作が成功しました");
        setErrorMessage("");

        // フォームをリセット
        if (actionData.product) {
          setProductForm({
            shopifyId: "",
            title: "",
            sku: "",
            price: "10000"
          });

          setImportForm({
            shopifyProductId: ""
          });
        }

        if (actionData.booking) {
          setBookingForm({
            productId: "",
            startDate: "2024-05-01", // 水曜日
            endDate: "2024-05-06", // 月曜日
            customerEmail: "<EMAIL>"
          });
        }
      } else if (actionData.error) {
        setErrorMessage(actionData.error);
        setSuccessMessage("");
      }
    }
  }, [actionData]);

  // 商品作成処理
  const handleCreateProduct = useCallback(() => {
    setIsLoading(true);
    setErrorMessage("");
    setSuccessMessage("");

    const formData = new FormData();
    formData.append("intent", "create-product");
    formData.append("shopifyId", productForm.shopifyId);
    formData.append("title", productForm.title);
    formData.append("sku", productForm.sku);
    formData.append("price", productForm.price);

    submit(formData, { method: "post" });
  }, [productForm, submit]);

  // 予約作成処理
  const handleCreateBooking = useCallback(() => {
    setIsLoading(true);
    setErrorMessage("");
    setSuccessMessage("");

    const formData = new FormData();
    formData.append("intent", "create-booking");
    formData.append("productId", bookingForm.productId);
    formData.append("startDate", bookingForm.startDate);
    formData.append("endDate", bookingForm.endDate);
    formData.append("customerEmail", bookingForm.customerEmail);

    submit(formData, { method: "post" });
  }, [bookingForm, submit]);

  // Shopify商品インポート処理
  const handleImportShopifyProduct = useCallback(() => {
    setIsLoading(true);
    setErrorMessage("");
    setSuccessMessage("");

    const formData = new FormData();
    formData.append("intent", "import-shopify-product");
    formData.append("shopifyProductId", importForm.shopifyProductId);

    submit(formData, { method: "post" });
  }, [importForm, submit]);

  // 商品テーブルデータ
  const productTableRows = products.map(product => [
    product.id,
    product.title,
    product.sku,
    product.price,
    product.status,
    new Date(product.createdAt).toLocaleString()
  ]);

  // 予約テーブルデータ
  const bookingTableRows = bookings.map(booking => [
    booking.bookingId,
    booking.productId,
    new Date(booking.startDate).toLocaleDateString(),
    new Date(booking.endDate).toLocaleDateString(),
    booking.status,
    new Date(booking.createdAt).toLocaleString()
  ]);

  // Shopify商品テーブルデータ
  const shopifyProductTableRows = shopifyProducts.map(product => [
    product.id,
    product.title,
    product.status,
    product.variant ? product.variant.sku : "なし",
    product.variant ? product.variant.price : "なし"
  ]);

  return (
    <Frame>
      <Page
        title="テストデータセットアップ"
        backAction={{ content: "戻る", url: "/app" }}
      >
        {errorMessage && (
          <Banner status="critical" onDismiss={() => setErrorMessage("")}>
            <p>{errorMessage}</p>
          </Banner>
        )}

        {successMessage && (
          <Banner status="success" onDismiss={() => setSuccessMessage("")}>
            <p>{successMessage}</p>
          </Banner>
        )}

        <Layout>
          <Layout.Section>
            <Tabs
              tabs={tabs}
              selected={selectedTab}
              onSelect={(index) => setSelectedTab(index)}
            />

            <Card>
              {selectedTab === 0 && (
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">商品データ</Text>

                  <Card>
                    <BlockStack gap="400">
                      <Text as="h3" variant="headingMd">新規商品作成</Text>

                      <TextField
                        label="Shopify商品ID"
                        value={productForm.shopifyId}
                        onChange={(value) => setProductForm({...productForm, shopifyId: value})}
                        autoComplete="off"
                      />

                      <TextField
                        label="商品名"
                        value={productForm.title}
                        onChange={(value) => setProductForm({...productForm, title: value})}
                        autoComplete="off"
                      />

                      <TextField
                        label="SKU"
                        value={productForm.sku}
                        onChange={(value) => setProductForm({...productForm, sku: value})}
                        autoComplete="off"
                      />

                      <TextField
                        label="価格"
                        type="number"
                        value={productForm.price}
                        onChange={(value) => setProductForm({...productForm, price: value})}
                        autoComplete="off"
                      />

                      <Button
                        primary
                        loading={isLoading}
                        onClick={handleCreateProduct}
                      >
                        商品を作成
                      </Button>
                    </BlockStack>
                  </Card>

                  <DataTable
                    columnContentTypes={[
                      "text",
                      "text",
                      "text",
                      "numeric",
                      "text",
                      "text"
                    ]}
                    headings={[
                      "ID",
                      "商品名",
                      "SKU",
                      "価格",
                      "ステータス",
                      "作成日時"
                    ]}
                    rows={productTableRows}
                    emptyState={<Text>商品データがありません</Text>}
                  />
                </BlockStack>
              )}

              {selectedTab === 1 && (
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">予約データ</Text>

                  <Card>
                    <BlockStack gap="400">
                      <Text as="h3" variant="headingMd">新規予約作成</Text>

                      <Select
                        label="商品"
                        options={products.map(p => ({ label: p.title, value: p.id }))}
                        value={bookingForm.productId}
                        onChange={(value) => setBookingForm({...bookingForm, productId: value})}
                      />

                      <TextField
                        label="開始日"
                        type="date"
                        value={bookingForm.startDate}
                        onChange={(value) => setBookingForm({...bookingForm, startDate: value})}
                      />

                      <TextField
                        label="終了日"
                        type="date"
                        value={bookingForm.endDate}
                        onChange={(value) => setBookingForm({...bookingForm, endDate: value})}
                      />

                      <TextField
                        label="顧客メールアドレス"
                        type="email"
                        value={bookingForm.customerEmail}
                        onChange={(value) => setBookingForm({...bookingForm, customerEmail: value})}
                        autoComplete="off"
                      />

                      <Button
                        primary
                        loading={isLoading}
                        onClick={handleCreateBooking}
                      >
                        予約を作成
                      </Button>
                    </BlockStack>
                  </Card>

                  <DataTable
                    columnContentTypes={[
                      "text",
                      "text",
                      "text",
                      "text",
                      "text",
                      "text"
                    ]}
                    headings={[
                      "予約ID",
                      "商品ID",
                      "開始日",
                      "終了日",
                      "ステータス",
                      "作成日時"
                    ]}
                    rows={bookingTableRows}
                    emptyState={<Text>予約データがありません</Text>}
                  />
                </BlockStack>
              )}

              {selectedTab === 2 && (
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">Shopify商品</Text>

                  <Card>
                    <BlockStack gap="400">
                      <Text as="h3" variant="headingMd">Shopify商品をインポート</Text>

                      <Select
                        label="Shopify商品"
                        options={shopifyProducts.map(p => ({ label: p.title, value: p.id }))}
                        value={importForm.shopifyProductId}
                        onChange={(value) => setImportForm({...importForm, shopifyProductId: value})}
                      />

                      <Button
                        primary
                        loading={isLoading}
                        onClick={handleImportShopifyProduct}
                      >
                        商品をインポート
                      </Button>
                    </BlockStack>
                  </Card>

                  <DataTable
                    columnContentTypes={[
                      "text",
                      "text",
                      "text",
                      "text",
                      "numeric"
                    ]}
                    headings={[
                      "Shopify ID",
                      "商品名",
                      "ステータス",
                      "SKU",
                      "価格"
                    ]}
                    rows={shopifyProductTableRows}
                    emptyState={<Text>Shopify商品データがありません</Text>}
                  />
                </BlockStack>
              )}
            </Card>
          </Layout.Section>
        </Layout>
      </Page>
    </Frame>
  );
}
