import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import { 
  Page, 
  LegacyCard, 
  ResourceList, 
  ResourceItem, 
  Filters, 
  Badge, 
  EmptyState, 
  Text,
  Pagination,
  Thumbnail
} from '@shopify/polaris';
import { useState } from 'react';
import { prisma } from '../db.server';
import { ProductStatus } from '@prisma/client';

// ステータスに応じたバッジ設定
const statusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  AVAILABLE: { tone: 'success', label: '利用可能' },
  MAINTENANCE: { tone: 'attention', label: 'メンテナンス中' },
  DAMAGED: { tone: 'critical', label: '破損' },
  UNAVAILABLE: { tone: 'warning', label: '利用不可' }
};

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  
  // クエリパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  const limit = parseInt(url.searchParams.get('limit') || '50', 10);
  const status = url.searchParams.get('status') || undefined;
  const query = url.searchParams.get('query') || undefined;
  
  // 検索条件を構築
  const where: any = {};
  
  if (status) {
    where.status = status as ProductStatus;
  }
  
  if (query) {
    where.OR = [
      { title: { contains: query, mode: 'insensitive' } },
      { sku: { contains: query } },
      { description: { contains: query, mode: 'insensitive' } }
    ];
  }
  
  // 商品データを取得
  const products = await prisma.product.findMany({
    where,
    orderBy: [
      { status: 'asc' },
      { title: 'asc' }
    ],
    skip: (page - 1) * limit,
    take: limit
  });
  
  // 総件数を取得
  const total = await prisma.product.count({ where });
  
  return json({
    products: products.map(product => {
      // basicInfoからカテゴリと画像URLを抽出
      let category = '';
      let imageUrl = '';
      
      if (product.basicInfo && typeof product.basicInfo === 'object') {
        const basicInfo = product.basicInfo as any;
        category = basicInfo.category || '';
        imageUrl = basicInfo.imageUrl || '';
      }
      
      return {
        id: product.id,
        title: product.title,
        sku: product.sku,
        status: product.status,
        price: product.price.toString(),
        category,
        imageUrl,
        shopifyId: product.shopifyId,
        createdAt: product.createdAt.toISOString(),
        updatedAt: product.updatedAt.toISOString()
      };
    }),
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}

export default function ProductsIndex() {
  const { products, pagination } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  
  // 商品詳細ページへ移動
  const handleProductClick = (productId: string) => {
    navigate(`/products/${productId}`);
  };
  
  // ステータスフィルターを適用
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    const params = new URLSearchParams(window.location.search);
    
    if (value) {
      params.set('status', value);
    } else {
      params.delete('status');
    }
    
    navigate(`/products?${params.toString()}`);
  };
  
  // 検索クエリを適用
  const handleSearchSubmit = () => {
    const params = new URLSearchParams(window.location.search);
    
    if (searchQuery) {
      params.set('query', searchQuery);
    } else {
      params.delete('query');
    }
    
    navigate(`/products?${params.toString()}`);
  };
  
  // ページネーション処理
  const handlePaginationChange = (newPage: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set('page', newPage.toString());
    navigate(`/products?${params.toString()}`);
  };
  
  // フィルターオプションを生成
  const filters = [
    {
      key: 'status',
      label: 'ステータス',
      filter: (
        <select
          value={statusFilter}
          onChange={(e) => handleStatusFilterChange(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="">すべて</option>
          <option value="AVAILABLE">利用可能</option>
          <option value="MAINTENANCE">メンテナンス中</option>
          <option value="DAMAGED">破損</option>
          <option value="UNAVAILABLE">利用不可</option>
        </select>
      ),
      shortcut: true,
    }
  ];
  
  return (
    <Page
      title="商品管理"
      primaryAction={{
        content: "新規商品",
        url: "/products/new"
      }}
    >
      <LegacyCard>
        <ResourceList
          resourceName={{ singular: '商品', plural: '商品' }}
          items={products}
          renderItem={(product) => {
            const { id, title, sku, status, price, category, imageUrl } = product;
            const badge = statusBadgeMap[status] || { tone: 'new', label: status };
            
            const shortcutActions = [
              {
                content: '詳細',
                url: `/products/${id}`,
              },
              {
                content: '編集',
                url: `/products/edit/${id}`,
              }
            ];
            
            return (
              <ResourceItem
                id={id}
                onClick={() => handleProductClick(id)}
                shortcutActions={shortcutActions}
                persistActions
                media={
                  <Thumbnail
                    source={imageUrl || 'https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png'}
                    alt={title}
                  />
                }
              >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <Text as="h3" variant="bodyMd" fontWeight="bold">
                      {title}
                    </Text>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        商品コード: {sku}
                      </Text>
                    </div>
                    {category && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          カテゴリ: {category}
                        </Text>
                      </div>
                    )}
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        料金: ¥{parseInt(price).toLocaleString()}
                      </Text>
                    </div>
                  </div>
                  <div>
                    <Badge tone={badge.tone}>{badge.label}</Badge>
                  </div>
                </div>
              </ResourceItem>
            );
          }}
          filterControl={
            <Filters
              queryValue={searchQuery}
              filters={filters}
              onQueryChange={setSearchQuery}
              onQueryClear={() => setSearchQuery('')}
              onClearAll={() => {
                setSearchQuery('');
                setStatusFilter('');
                navigate('/products');
              }}
              onQueryBlur={handleSearchSubmit}
              hideQueryField={false}
            />
          }
          emptyState={
            <EmptyState
              heading="商品がありません"
              image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
            >
              <p>商品を登録して、レンタル商品を管理しましょう。</p>
            </EmptyState>
          }
        />
        {pagination.totalPages > 1 && (
          <div style={{ padding: '16px', display: 'flex', justifyContent: 'center' }}>
            <Pagination
              hasPrevious={pagination.page > 1}
              onPrevious={() => handlePaginationChange(pagination.page - 1)}
              hasNext={pagination.page < pagination.totalPages}
              onNext={() => handlePaginationChange(pagination.page + 1)}
            />
          </div>
        )}
      </LegacyCard>
      <div style={{ marginTop: '16px', textAlign: 'right' }}>
        <Text as="p" variant="bodySm" tone="subdued">
          合計 {pagination.total} 件の商品 ( {pagination.page} / {pagination.totalPages} ページ )
        </Text>
      </div>
    </Page>
  );
}
