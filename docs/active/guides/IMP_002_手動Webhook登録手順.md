# 手動Webhook登録手順

## 概要

Shopify商品のバリエーション方式実装のためのWebhookを手動で登録する方法について説明します。

## 手順

### 1. Shopify管理画面にログイン

1. Shopify管理画面にログインします。
2. ストア: `peaces-test-block.myshopify.com`

### 2. Webhookの設定

1. 「設定」をクリックします。
2. 「通知」をクリックします。
3. 「Webhook」セクションまでスクロールします。

### 3. products/create Webhookの追加

1. 「Webhookを作成」ボタンをクリックします。
2. 以下の情報を入力します：
   - イベント: `products/create`
   - 形式: `JSON`
   - URL: `https://app.shopify-app-test.xyz/webhooks/products`
3. 「保存」ボタンをクリックします。

### 4. products/update Webhookの追加

1. 「Webhookを作成」ボタンをクリックします。
2. 以下の情報を入力します：
   - イベント: `products/update`
   - 形式: `JSON`
   - URL: `https://app.shopify-app-test.xyz/webhooks/products`
3. 「保存」ボタンをクリックします。

## テスト方法

### 1. テスト用商品の作成

1. Shopify管理画面で「商品」→「商品を追加」を選択します。
2. 以下の情報を入力します：
   - 商品名: `テスト商品 - バリエーションテスト`
   - SKU: `TEST-VAR-001`
3. 以下のバリエーションを追加します：
   - `テスト商品 - バリエーションテスト - 1日` (SKU: `TEST-VAR-001-1D`)
   - `テスト商品 - バリエーションテスト - 2日` (SKU: `TEST-VAR-001-2D`)
   - `テスト商品 - バリエーションテスト - 3日` (SKU: `TEST-VAR-001-3D`)
   - `テスト商品 - バリエーションテスト - 4日` (SKU: `TEST-VAR-001-4D`)
   - `テスト商品 - バリエーションテスト - 5日` (SKU: `TEST-VAR-001-5D`)
   - `テスト商品 - バリエーションテスト - 6日` (SKU: `TEST-VAR-001-6D`)
   - `テスト商品 - バリエーションテスト - 7日` (SKU: `TEST-VAR-001-7D`)
   - `テスト商品 - バリエーションテスト - 8日以上` (SKU: `TEST-VAR-001-8P`)
4. 商品を保存します。

### 2. メタフィールドの確認

1. 商品詳細画面に移動します。
2. 「メタフィールド」タブを選択します。
3. 以下のメタフィールドが自動的に設定されていることを確認します：
   - `rental.variant_mapping`
   - `rental.pricing`
   - `rental.basic_info`
   - `rental.variation_type`

### 3. メタフィールドの内容確認

1. `rental.variant_mapping`メタフィールドの内容を確認します。
   - 各日数とバリエーションIDのマッピングが正しく設定されているか確認します。

   ```json
   {
     "1day": "gid://shopify/ProductVariant/[ID]",
     "2day": "gid://shopify/ProductVariant/[ID]",
     ...
     "8plus": "gid://shopify/ProductVariant/[ID]"
   }
   ```

2. `rental.pricing`メタフィールドの内容を確認します。
   - 料金設定が正しく設定されているか確認します。
   - `variantPrices`フィールドが追加されているか確認します。

   ```json
   {
     "basePrice": 5000,
     "depositRate": 0.1,
     "discountRules": {
       "day2_6_rate": 0.2,
       "day7_plus_rate": 0.1
     },
     "minimumDays": 1,
     "maximumDays": 30,
     "variantPrices": {
       "1day": 5000,
       "2day": 6000,
       ...
     }
   }
   ```

3. `rental.basic_info`メタフィールドの内容を確認します。
   - 商品の基本情報が正しく設定されているか確認します。

   ```json
   {
     "productCode": "TEST-VAR",
     "detailCode": "001",
     "kana": "テスト商品 - バリエーションテスト",
     "location": "NY",
     "status": "available"
   }
   ```

4. `rental.variation_type`メタフィールドの内容を確認します。
   - 値が「レンタル日数」に設定されているか確認します。

## トラブルシューティング

### メタフィールドが設定されない場合

1. アプリのログを確認します。
   ```bash
   npm run dev:fixed-tunnel
   ```

2. Webhookが正しく登録されているか確認します。
   - Shopify管理画面で「アプリ」→「アプリと販売チャネル」→「ease-next-temp」→「アプリの設定」→「Webhook」タブで確認します。

3. 環境変数が正しく設定されているか確認します。
   - `.env`ファイルで以下の環境変数が設定されているか確認します。
     - `SHOPIFY_API_KEY`
     - `SHOPIFY_API_SECRET`
     - `SHOPIFY_SHOP`
     - `SHOPIFY_ADMIN_API_ACCESS_TOKEN`

4. バリエーション名が命名規則に従っているか確認します。
   - バリエーション名に「1日」「2日」などの文字列が含まれているか確認します。

### Webhookが発火しない場合

1. Shopify管理画面でWebhookの登録状況を確認します。
   - 「設定」→「通知」→「Webhook」セクションで確認します。
2. Webhookの登録URLが正しいか確認します。
   - URL: `https://app.shopify-app-test.xyz/webhooks/products`
3. アプリが正常に起動しているか確認します。
   ```bash
   npm run dev:fixed-tunnel
   ```

## メタフィールドの推奨設定

Webhookが正常に動作するためには、以下のメタフィールドが必要です。Shopify管理画面の「設定」→「メタフィールド」から設定してください。

### 必須メタフィールド

1. **レンタル料金設定** (`rental.pricing`)
   - タイプ: JSON
   - 説明: レンタル商品の料金設定情報を保存します
   - 既存のメタフィールドを維持

2. **レンタル商品基本情報** (`rental.basic_info`)
   - タイプ: JSON
   - 説明: 商品コード、詳細コード、在庫場所などの基本情報を保存します
   - 既存のメタフィールドを維持

3. **バリエーションマッピング** (`rental.variant_mapping`)
   - タイプ: JSON
   - 説明: 商品バリエーション間の関連付けを保存します
   - **新規追加が必要**

4. **レンタル状態** (`rental.status` または既存の「レンタル状態」)
   - タイプ: 1行テキスト
   - 説明: 商品のレンタル状態（利用可能、メンテナンス中など）を管理者が簡単に変更できるようにします
   - 既存のメタフィールドを維持

5. **バリエーションタイプ** (`rental.variation_type`)
   - タイプ: 1行テキスト
   - 説明: バリエーションの種類（レンタル日数など）を指定します
   - 既存の「バリエーションタイプ」を名前空間付きに変更するか、新規追加

### 削除検討可能なメタフィールド

以下のメタフィールドは、`rental.`名前空間付きのメタフィールドに置き換えることで削除を検討できます：

- 「バリエーショングループ」- `rental.variation_group`として再作成するか削除

### 注意事項

- メタフィールドの名前空間（`rental.`）は重要です。Webhookハンドラーはこの名前空間を使用してメタフィールドを識別します。
- JSONメタフィールドの内容は自動的に設定されるため、手動で編集する必要はありません。
- 「レンタル状態」メタフィールドは管理者が手動で変更することが多いため、単独のフィールドとして維持することをお勧めします。

## まとめ

手動でWebhookを登録することで、Shopify商品のバリエーション方式実装のためのメタフィールド自動設定が可能になります。この方法は、アプリの自動Webhook登録機能に問題がある場合の代替手段として有効です。また、適切なメタフィールドを設定することで、レンタル商品管理システムが正常に機能します。
