#!/usr/bin/env node

/**
 * データベースクリーンアップスクリプト
 * 
 * 外部キー制約を考慮して、関連データを正しい順序で削除
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * データベースの現在の状況を確認
 */
async function checkDatabaseStatus(shop) {
  console.log('=== データベース状況確認 ===');
  
  try {
    // 各テーブルのレコード数を確認
    const counts = {
      products: await prisma.product.count({ where: { shop } }),
      bookings: await prisma.booking.count({ where: { shop } }),
      customers: await prisma.customer.count({ where: { shop } }),
      deliverySchedules: await prisma.deliverySchedule.count({ where: { shop } }),
      pickingRegistrations: await prisma.pickingRegistration.count({ where: { shop } }),
      returnProcessings: await prisma.returnProcessing.count({ where: { shop } }),
      categoryMasters: await prisma.categoryMaster.count({ where: { shop } }),
      subCategoryMasters: await prisma.subCategoryMaster.count({ where: { shop } })
    };
    
    console.log('📊 現在のレコード数:');
    Object.entries(counts).forEach(([table, count]) => {
      console.log(`  ${table}: ${count}件`);
    });
    
    return counts;
    
  } catch (error) {
    console.error('❌ データベース状況確認エラー:', error.message);
    return null;
  }
}

/**
 * 関連データを正しい順序で削除
 */
async function cleanupRelatedData(shop) {
  console.log('\n=== 関連データクリーンアップ開始 ===');
  
  try {
    // 1. 配送関連データを削除（最も依存関係が深い）
    console.log('1. 配送関連データを削除中...');
    
    // ピッキング登録を削除
    const deletedPicking = await prisma.pickingRegistration.deleteMany({
      where: { shop }
    });
    console.log(`  ピッキング登録削除: ${deletedPicking.count}件`);
    
    // 返却処理を削除
    const deletedReturn = await prisma.returnProcessing.deleteMany({
      where: { shop }
    });
    console.log(`  返却処理削除: ${deletedReturn.count}件`);
    
    // 配送スケジュールを削除
    const deletedDelivery = await prisma.deliverySchedule.deleteMany({
      where: { shop }
    });
    console.log(`  配送スケジュール削除: ${deletedDelivery.count}件`);
    
    // 2. 予約データを削除
    console.log('2. 予約データを削除中...');
    const deletedBookings = await prisma.booking.deleteMany({
      where: { shop }
    });
    console.log(`  予約削除: ${deletedBookings.count}件`);
    
    // 3. 商品データを削除
    console.log('3. 商品データを削除中...');
    const deletedProducts = await prisma.product.deleteMany({
      where: { shop }
    });
    console.log(`  商品削除: ${deletedProducts.count}件`);
    
    // 4. 顧客データを削除（必要に応じて）
    console.log('4. 顧客データを削除中...');
    const deletedCustomers = await prisma.customer.deleteMany({
      where: { shop }
    });
    console.log(`  顧客削除: ${deletedCustomers.count}件`);
    
    console.log('✅ 関連データクリーンアップ完了');
    
    return {
      picking: deletedPicking.count,
      return: deletedReturn.count,
      delivery: deletedDelivery.count,
      bookings: deletedBookings.count,
      products: deletedProducts.count,
      customers: deletedCustomers.count
    };
    
  } catch (error) {
    console.error('❌ クリーンアップエラー:', error.message);
    throw error;
  }
}

/**
 * カテゴリマスタは保持（テスト用商品で使用するため）
 */
async function preserveCategoryMasters(shop) {
  console.log('\n=== カテゴリマスタ保持確認 ===');
  
  try {
    const categoryCount = await prisma.categoryMaster.count({ where: { shop } });
    const subCategoryCount = await prisma.subCategoryMaster.count({ where: { shop } });
    
    console.log(`✅ カテゴリマスタ保持: ${categoryCount}件`);
    console.log(`✅ サブカテゴリマスタ保持: ${subCategoryCount}件`);
    
    if (categoryCount === 0) {
      console.log('⚠️ カテゴリマスタが存在しません。先にカテゴリマスタをインポートしてください。');
      console.log('実行コマンド: node scripts/category-management/03_import_master_data.js');
    }
    
    return { categories: categoryCount, subCategories: subCategoryCount };
    
  } catch (error) {
    console.error('❌ カテゴリマスタ確認エラー:', error.message);
    return { categories: 0, subCategories: 0 };
  }
}

/**
 * テスト用の基本データを作成
 */
async function createBasicTestData(shop) {
  console.log('\n=== 基本テストデータ作成 ===');
  
  try {
    // テスト用顧客を作成
    const testCustomers = [
      {
        shop,
        shopifyId: 'test-customer-1',
        email: '<EMAIL>',
        firstName: '太郎',
        lastName: '田中',
        phone: '090-1234-5678'
      },
      {
        shop,
        shopifyId: 'test-customer-2',
        email: '<EMAIL>',
        firstName: '花子',
        lastName: '佐藤',
        phone: '090-2345-6789'
      }
    ];
    
    console.log('テスト用顧客を作成中...');
    for (const customerData of testCustomers) {
      await prisma.customer.create({ data: customerData });
    }
    console.log(`✅ テスト用顧客作成完了: ${testCustomers.length}件`);
    
    // テスト用商品を作成
    const testProducts = [
      {
        shop,
        shopifyId: 'test-product-1',
        sku: '212-05-023',
        title: '花器　シルバー　穴開きボトル型',
        handle: 'test-product-1',
        status: 'AVAILABLE',
        inventory: 1,
        price: 1500,
        basicInfo: {
          category: '花器',
          basePrice: 1500,
          description: 'テスト用商品'
        }
      },
      {
        shop,
        shopifyId: 'test-product-2',
        sku: '201-07-107',
        title: 'シルバートレー　オーバル',
        handle: 'test-product-2',
        status: 'AVAILABLE',
        inventory: 1,
        price: 3000,
        basicInfo: {
          category: 'キッチン',
          basePrice: 3000,
          description: 'テスト用商品'
        }
      },
      {
        shop,
        shopifyId: 'test-product-3',
        sku: '211-12-954',
        title: 'オブジェ　タイドロープ',
        handle: 'test-product-3',
        status: 'MAINTENANCE',
        inventory: 0,
        price: 1000,
        basicInfo: {
          category: 'オブジェ',
          basePrice: 1000,
          description: 'メンテナンス中のテスト用商品'
        }
      }
    ];
    
    console.log('テスト用商品を作成中...');
    for (const productData of testProducts) {
      await prisma.product.create({ data: productData });
    }
    console.log(`✅ テスト用商品作成完了: ${testProducts.length}件`);
    
    return {
      customers: testCustomers.length,
      products: testProducts.length
    };
    
  } catch (error) {
    console.error('❌ 基本テストデータ作成エラー:', error.message);
    throw error;
  }
}

/**
 * メイン処理
 */
async function main() {
  const shop = process.argv[2] || 'ease-next-temp.myshopify.com';
  
  console.log('🧹 === データベースクリーンアップ開始 ===');
  console.log(`対象ショップ: ${shop}`);
  console.log(`実行日時: ${new Date().toLocaleString('ja-JP')}`);
  
  try {
    await prisma.$connect();
    console.log('✅ データベース接続成功');
    
    // 1. 現在の状況確認
    const initialCounts = await checkDatabaseStatus(shop);
    if (!initialCounts) {
      throw new Error('データベース状況確認に失敗しました');
    }
    
    // 2. 関連データクリーンアップ
    const deletedCounts = await cleanupRelatedData(shop);
    
    // 3. カテゴリマスタ保持確認
    const categoryStatus = await preserveCategoryMasters(shop);
    
    // 4. 基本テストデータ作成
    const createdCounts = await createBasicTestData(shop);
    
    // 5. 最終状況確認
    console.log('\n=== 最終状況確認 ===');
    const finalCounts = await checkDatabaseStatus(shop);
    
    console.log('\n🎉 === クリーンアップ完了 ===');
    console.log('削除されたデータ:');
    Object.entries(deletedCounts).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}件`);
    });
    
    console.log('\n作成されたテストデータ:');
    Object.entries(createdCounts).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}件`);
    });
    
    console.log('\n次のステップ:');
    console.log('1. テスト用予約データの作成');
    console.log('2. 包括的テストの実行');
    console.log('3. アプリでの動作確認');
    
  } catch (error) {
    console.error('❌ クリーンアップ処理エラー:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
