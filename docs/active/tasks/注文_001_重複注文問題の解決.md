# 注文_001_重複注文問題の解決

## 概要

同じ予約に対して複数の注文が生成される問題を解決するためのタスク。

## 背景

レンタル商品の予約時に、同じ商品に対して複数の注文が生成される問題が発生していました。これにより、Shopify管理画面で同一商品の重複注文が表示され、管理が困難になっていました。

## 問題の原因

テスト結果の分析から、以下の原因が特定されました：

1. **バリアントIDの形式エラー**:
   - バリアントIDが数値のみで渡されており、Shopify GraphQL APIが期待するグローバルID形式（`gid://shopify/ProductVariant/...`）になっていなかった
   - このため、API呼び出しが失敗し、エラーが発生していた

2. **リトライロジックの問題**:
   - エラーが発生した場合、リトライロジックが同じリクエストを再試行するだけで、エラーを適切に処理していなかった
   - 各試行で同じエラーが発生し、最終的に複数の注文作成が試みられていた

3. **重複チェックの不足**:
   - 注文作成前に既存の注文をチェックするロジックが不十分だった
   - 同じ予約IDに対して複数回注文作成が試みられても、それを防止するメカニズムがなかった

## 実装した解決策

### 1. バリアントIDの形式を修正

```typescript
// バリアントIDを正規化
const variantId = booking.variantId
  ? (booking.variantId.startsWith('gid://shopify/ProductVariant/')
      ? booking.variantId
      : `gid://shopify/ProductVariant/${booking.variantId}`)
  : null;
```

### 2. 重複注文チェックの強化

```typescript
// 既に注文が関連付けられているかチェック
if (booking.orderId) {
  console.log(`予約ID ${bookingId} には既に注文ID ${booking.orderId} が関連付けられています。`);
  return {
    success: true,
    orderId: booking.orderId,
    orderName: booking.orderName || '不明',
    isDraftOrder: false,
    isExisting: true
  };
}
```

### 3. リトライロジックの改善

```typescript
const result = await retry(
  async () => {
    // 既存のリクエスト処理
  },
  retryCount,
  1000, // 1秒後にリトライ
  2, // 指数バックオフ（1秒、2秒、4秒...）
  async (error, attempt) => {
    // エラーログを記録
    console.error(`注文作成の試行 ${attempt}/${retryCount} が失敗しました:`, error);
    
    // 特定のエラーの場合はリトライしない
    if (error.message) {
      // バリアントIDの形式エラー
      if (error.message.includes('Invalid global id')) {
        console.error('バリアントIDの形式が正しくありません。リトライをスキップします。');
        return false;
      }
      
      // 既に注文が存在する場合
      if (error.message.includes('既に注文が存在します') || 
          error.message.includes('already has an order')) {
        console.error('既に注文が存在します。リトライをスキップします。');
        return false;
      }
    }
    
    // その他のエラーはリトライする
    return true;
  }
);
```

### 4. デバッグログの追加

```typescript
console.log(`注文作成を開始します: 予約ID=${bookingId}, 顧客ID=${customerId}, 商品ID=${productId}, バリアントID=${variantId}`);

// 注文作成後
console.log(`注文作成が完了しました: 注文ID=${orderId}, 注文番号=${orderName}, 予約ID=${bookingId}`);
```

### 5. フィールド名の修正

Prismaスキーマの変更に合わせて、`shopifyOrderId`と`shopifyOrderName`を`orderId`と`orderName`に変更しました。

## テスト結果

修正後のテストでは、同じ予約IDで複数回注文作成を試みても、正しく既存の注文が検出され、重複注文が発生しないことが確認できました。

```
予約ID cmax5gwo50001h6lrem6lcnb4 には既に注文ID gid://shopify/Order/mock-order-id が関連付けられています。
注文作成結果:
{
  "success": true,
  "orderId": "gid://shopify/Order/mock-order-id",
  "orderName": "#1234",
  "isDraftOrder": false,
  "isExisting": true
}
```

## 今後の改善点（優先度：低）

1. **エラーハンドリングの強化**:
   - より詳細なエラーメッセージを提供し、問題の特定を容易にする
   - 特定のエラーに対する適切な対応策を実装する

2. **テストの充実**:
   - 様々なシナリオでのテストを追加し、注文作成プロセスの堅牢性を確保する
   - 自動テストを導入して、継続的に注文作成プロセスの品質を確保する

3. **ログの改善**:
   - より構造化されたログを導入し、問題の追跡を容易にする
   - 重要なイベントを監視し、異常を検出する仕組みを導入する

## 関連ファイル

- `app/utils/booking/order-creator.ts`: 注文作成ロジックを含むファイル
- `scripts/test-fixed-order-creation.ts`: 修正後の注文作成ロジックをテストするスクリプト
- `scripts/simple-order-test.ts`: シンプルな注文作成テストスクリプト

## ステータス

- [x] 問題の原因特定
- [x] 解決策の実装
- [x] テストの実施
- [x] 本番環境への適用
- [x] ドキュメント作成

**完了日**: 2025-05-29
