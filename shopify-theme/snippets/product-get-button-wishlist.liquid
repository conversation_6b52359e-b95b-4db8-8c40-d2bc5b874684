{%- if customer and customer.metafields.wishlist -%}
    {%- for item in customer.metafields.wishlist -%}
        {%- assign wishlist_id = item[0] | abs -%}
        {%- assign wishlist_handle = item[1] -%}
        {%- if current_variant_id == wishlist_id and product.handle == wishlist_handle -%}
            {%- assign present_in_wishlist = true -%}
        {%- endif -%}
    {%- endfor -%}
{%- endif -%}
<a href="/account" class="btn btn--text btn--status px-lg-6 js-store-lists-add-wishlist"
    {% if present_in_wishlist %} data-button-status="added"{% endif %}
    {%- if settings.tooltips_enable and product_enable_tooltips %}
        data-js-tooltip
        data-tippy-content="{{ 'products.product.tooltip.wishlist' | t }}"
        data-tippy-placement="top"
        data-tippy-distance="-3"
    {%- endif -%}
>
    <i class="mb-1 ml-1">{% render 'icon-theme-180' %}</i>
    <i class="mb-1 ml-1" data-button-content="added">{% render 'icon-theme-181' %}</i>
</a>
{%- assign present_in_wishlist = false -%}
