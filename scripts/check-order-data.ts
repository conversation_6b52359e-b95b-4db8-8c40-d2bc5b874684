import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function checkOrderData() {
  try {
    console.log("=== 予約データの注文情報チェック ===\n");

    // 全予約データを取得
    const bookings = await prisma.booking.findMany({
      include: {
        product: {
          select: {
            title: true,
            sku: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20 // 最新20件
    });

    console.log(`データベース内の予約数: ${bookings.length}件\n`);

    if (bookings.length === 0) {
      console.log("予約データが見つかりませんでした。");
      return;
    }

    // 注文情報の統計
    const withShopifyOrderId = bookings.filter(b => b.shopifyOrderId);
    const withShopifyOrderName = bookings.filter(b => b.shopifyOrderName);
    const withOrderId = bookings.filter(b => b.orderId);
    const withOrderName = bookings.filter(b => b.orderName);

    console.log("=== 注文情報の統計 ===");
    console.log(`shopifyOrderId あり: ${withShopifyOrderId.length}件`);
    console.log(`shopifyOrderName あり: ${withShopifyOrderName.length}件`);
    console.log(`orderId あり: ${withOrderId.length}件`);
    console.log(`orderName あり: ${withOrderName.length}件\n`);

    // 詳細表示
    console.log("=== 予約データの詳細 (最新10件) ===");
    for (const booking of bookings.slice(0, 10)) {
      console.log(`予約ID: ${booking.bookingId}`);
      console.log(`  商品: ${booking.product?.title || '不明'} (${booking.product?.sku || 'SKU不明'})`);
      console.log(`  顧客: ${booking.customerName || '不明'} (${booking.customerEmail || 'メール不明'})`);
      console.log(`  ステータス: ${booking.status}`);
      console.log(`  shopifyOrderId: ${booking.shopifyOrderId || 'なし'}`);
      console.log(`  shopifyOrderName: ${booking.shopifyOrderName || 'なし'}`);
      console.log(`  orderId: ${booking.orderId || 'なし'}`);
      console.log(`  orderName: ${booking.orderName || 'なし'}`);
      console.log(`  作成日: ${booking.createdAt.toLocaleString()}`);
      console.log('---');
    }

    // 注文情報がある予約の例
    const bookingsWithOrders = bookings.filter(b => 
      b.shopifyOrderId || b.shopifyOrderName || b.orderId || b.orderName
    );

    if (bookingsWithOrders.length > 0) {
      console.log("\n=== 注文情報がある予約の例 ===");
      for (const booking of bookingsWithOrders.slice(0, 5)) {
        console.log(`予約ID: ${booking.bookingId}`);
        console.log(`  shopifyOrderId: ${booking.shopifyOrderId || 'なし'}`);
        console.log(`  shopifyOrderName: ${booking.shopifyOrderName || 'なし'}`);
        console.log(`  orderId: ${booking.orderId || 'なし'}`);
        console.log(`  orderName: ${booking.orderName || 'なし'}`);
        console.log('---');
      }
    } else {
      console.log("\n注文情報がある予約が見つかりませんでした。");
    }

  } catch (error) {
    console.error("エラーが発生しました:", error);
  } finally {
    await prisma.$disconnect();
  }
}

checkOrderData();
