{"timestamp": "2025-05-19T07:11:06.420Z", "duration": 4.246, "totalTests": 10, "successCount": 4, "failureCount": 6, "tests": [{"testName": "年末年始の予約テスト", "success": false, "timestamp": "2025-05-19T07:11:08.215Z", "message": "年末年始の予約に失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-12-29, 2025-12-30, 2025-12-31, 2026-01-01, 2026-01-02, 2026-01-03", "duration": 1053, "details": {"bookingId": "", "period": "年末年始", "startDate": "2025-12-29", "endDate": "2026-01-03"}}, {"testName": "予約期間の最小値テスト", "success": false, "timestamp": "2025-05-19T07:11:08.452Z", "message": "最小予約期間の予約に失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-05-26", "duration": 236, "details": {"bookingId": "", "durationDays": 1}}, {"testName": "予約期間の最大値テスト", "success": false, "timestamp": "2025-05-19T07:11:08.770Z", "message": "最大予約期間の予約に失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-02, 2025-06-03, 2025-06-04, 2025-06-05, 2025-06-06, 2025-06-07, 2025-06-08, 2025-06-09, 2025-06-10, 2025-06-11, 2025-06-12, 2025-06-13, 2025-06-14, 2025-06-15, 2025-06-16, 2025-06-17, 2025-06-18, 2025-06-19, 2025-06-20, 2025-06-21, 2025-06-22, 2025-06-23, 2025-06-24, 2025-06-25, 2025-06-26, 2025-06-27, 2025-06-28, 2025-06-29, 2025-06-30, 2025-07-01", "duration": 318, "details": {"bookingId": "", "durationDays": 30}}, {"testName": "過去の日付での予約テスト", "success": true, "timestamp": "2025-05-19T07:11:09.005Z", "message": "過去の日付での予約は正しくエラーになりました", "error": "", "duration": 234, "details": {"expectedError": true}}, {"testName": "終了日が開始日より前の予約テスト", "success": true, "timestamp": "2025-05-19T07:11:09.656Z", "message": "終了日が開始日より前の予約は正しくエラーになりました", "error": "終了日が開始日より前の予約がエラーにならずに成功してしまいました", "duration": 651, "details": {"expectedError": true}}, {"testName": "無効なメールアドレスでの予約テスト", "success": true, "timestamp": "2025-05-19T07:11:09.889Z", "message": "無効なメールアドレスでの予約は正しくエラーになりました", "error": "", "duration": 233, "details": {"expectedError": true, "invalidEmail": "invalid-email"}}, {"testName": "在庫をゼロにするテスト", "success": false, "timestamp": "2025-05-19T07:11:10.171Z", "message": "在庫をゼロにすることに失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-08, 2025-06-09, 2025-06-10, 2025-06-11", "duration": 281, "details": {"bookingId": ""}}, {"testName": "在庫がゼロの状態で予約を試みるテスト", "success": true, "timestamp": "2025-05-19T07:11:10.418Z", "message": "在庫がゼロの状態での予約は正しくエラーになりました", "error": "", "duration": 247, "details": {"expectedError": true}}, {"testName": "予約をキャンセルして在庫を回復するテスト", "success": false, "timestamp": "2025-05-19T07:11:10.418Z", "message": "予約をキャンセルして在庫を回復することに失敗しました", "error": "テスト1で作成した予約IDがありません", "duration": 0, "details": {"bookingId": ""}}, {"testName": "在庫が回復した状態で予約を試みるテスト", "success": false, "timestamp": "2025-05-19T07:11:10.666Z", "message": "在庫が回復した状態での予約に失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-08, 2025-06-09, 2025-06-10, 2025-06-11", "duration": 248, "details": {"bookingId": ""}}], "environment": {"nodeVersion": "v20.18.1", "os": "darwin arm64"}, "performance": {"averageTestDuration": 350.1, "slowestTest": {"name": "年末年始の予約テスト", "duration": 1053}, "fastestTest": {"name": "予約をキャンセルして在庫を回復するテスト", "duration": 0}}}