/**
 * 予約データからShopify注文を作成するテストスクリプト
 *
 * このスクリプトは、既存の予約データからShopify注文を作成し、
 * 予約データとShopify注文が正しく関連付けられているかをテストします。
 *
 * 実行方法: npx tsx scripts/test-booking-order-creation.ts [予約ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';
import { GraphQLClient } from 'graphql-request';
import { retry, handleError } from '../app/utils/booking/error-handler';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト設定
const config = {
  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',

  // Shopify APIアクセストークン
  shopifyAccessToken: process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,

  // Shopify API バージョン
  apiVersion: '2025-01'
};

/**
 * 予約情報を表示する関数
 */
async function displayBookingInfo(bookingId: string) {
  try {
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        product: true
      }
    });

    if (!booking) {
      console.error(`予約ID ${bookingId} が見つかりません`);
      return;
    }

    console.log('----- 予約情報 -----');
    console.log(`ID: ${booking.id}`);
    console.log(`予約ID: ${booking.bookingId}`);
    console.log(`開始日: ${format(booking.startDate, 'yyyy/MM/dd', { locale: ja })}`);
    console.log(`終了日: ${format(booking.endDate, 'yyyy/MM/dd', { locale: ja })}`);
    console.log(`ステータス: ${booking.status}`);
    console.log(`予約タイプ: ${booking.bookingType}`);
    console.log(`顧客名: ${booking.customerName}`);
    console.log(`顧客メール: ${booking.customerEmail}`);
    console.log(`顧客ID: ${booking.customerId || 'なし'}`);
    console.log(`商品: ${booking.product?.title || 'なし'} (ID: ${booking.productId})`);
    console.log(`Shopify注文ID: ${booking.shopifyOrderId || 'なし'}`);
    console.log(`Shopify注文番号: ${booking.shopifyOrderName || 'なし'}`);
    console.log('-------------------');
  } catch (error) {
    console.error('予約情報表示エラー:', error);
  }
}

/**
 * Shopify注文を取得する関数
 */
async function getShopifyOrder(orderId: string) {
  try {
    if (!config.shopifyAccessToken) {
      throw new Error('SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません');
    }

    // GraphQLクライアントを初期化
    const graphQLClient = new GraphQLClient(
      `https://${config.shop}/admin/api/${config.apiVersion}/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': config.shopifyAccessToken,
          'Content-Type': 'application/json',
        },
      }
    );

    // Shopify IDを正規化
    const shopifyId = orderId.startsWith('gid://shopify/Order/')
      ? orderId
      : `gid://shopify/Order/${orderId}`;

    // GraphQLクエリを実行
    const query = `
      query getOrder($id: ID!) {
        order(id: $id) {
          id
          name
          createdAt
          displayFinancialStatus
          totalPriceSet {
            shopMoney {
              amount
              currencyCode
            }
          }
          customer {
            id
            displayName
            email
          }
          lineItems(first: 10) {
            edges {
              node {
                title
                quantity
                originalUnitPrice
                variant {
                  id
                  title
                }
              }
            }
          }
        }
      }
    `;

    console.log(`Shopify注文を取得中... (注文ID: ${shopifyId})`);
    const result = await graphQLClient.request(query, { id: shopifyId });

    if (!result.order) {
      console.error('注文が見つかりません');
      return null;
    }

    return result.order;
  } catch (error) {
    console.error('Shopify注文取得エラー:', error);
    return null;
  }
}

/**
 * 予約からShopify注文を作成する関数
 */
async function createOrderFromBookingTest(bookingId: string) {
  try {
    console.log(`予約ID ${bookingId} からShopify注文を作成します...`);

    // 予約情報を表示
    console.log('\n----- 注文作成前の予約情報 -----');
    await displayBookingInfo(bookingId);

    // GraphQLクライアントを直接使用
    if (!config.shopifyAccessToken) {
      throw new Error('SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません');
    }

    console.log('GraphQLクライアントを初期化中...');
    const graphQLClient = new GraphQLClient(
      `https://${config.shop}/admin/api/${config.apiVersion}/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': config.shopifyAccessToken,
          'Content-Type': 'application/json',
        },
      }
    );

    // 注文を作成
    console.log('\n----- 注文作成処理を実行中... -----');

    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        product: true
      }
    });

    if (!booking) {
      throw new Error(`予約ID ${bookingId} が見つかりません`);
    }

    console.log(`予約情報を取得しました: ${booking.bookingId}`);

    // 顧客IDを正規化
    const customerId = booking.customerId?.startsWith('gid://shopify/Customer/')
      ? booking.customerId
      : `gid://shopify/Customer/${booking.customerId}`;

    // 商品IDを正規化
    const productId = booking.product?.shopifyId.startsWith('gid://shopify/Product/')
      ? booking.product.shopifyId
      : `gid://shopify/Product/${booking.product?.shopifyId}`;

    // 予約期間を計算
    const startDate = format(booking.startDate, 'yyyy-MM-dd');
    const endDate = format(booking.endDate, 'yyyy-MM-dd');
    const rentalPeriod = `${startDate} 〜 ${endDate}`;

    // 注文作成用の入力データを作成
    // Shopify APIの仕様に合わせて、DraftOrderLineItemInputの正しいフィールドを使用
    const orderInput = {
      customerId: customerId,
      lineItems: [
        {
          // variantIdまたはtitle+priceのいずれかが必要
          // productIdは使用できない（APIエラーになる）
          title: `${booking.product?.title} (レンタル: ${rentalPeriod})`,
          originalUnitPrice: booking.bookingType === 'PROVISIONAL'
            ? booking.depositAmount?.toString() // 仮予約の場合はデポジット金額
            : booking.totalAmount?.toString(), // 確定予約の場合は全額
          quantity: 1,
          taxable: true,
          requiresShipping: false
        }
      ],
      tags: ['rental', booking.bookingType.toLowerCase(), `booking-${booking.bookingId}`],
      note: `予約ID: ${booking.bookingId}\n予約期間: ${rentalPeriod}\n備考: ${booking.notes || 'なし'}`
    };

    console.log('注文作成データ:', JSON.stringify(orderInput, null, 2));

    // 注文を作成（リトライ機能付き）
    const result = await retry(
      async () => {
        console.log('GraphQL呼び出しを実行します (注文作成)...');

        try {
          // ドラフト注文を作成
          const createDraftOrderMutation = `
            mutation draftOrderCreate($input: DraftOrderInput!) {
              draftOrderCreate(input: $input) {
                draftOrder {
                  id
                  name
                  totalPrice
                  customer {
                    id
                    email
                  }
                  lineItems(first: 10) {
                    edges {
                      node {
                        title
                        quantity
                        originalUnitPrice
                        variant {
                          id
                          title
                        }
                      }
                    }
                  }
                }
                userErrors {
                  field
                  message
                }
              }
            }
          `;

          const createResponse = await graphQLClient.request(createDraftOrderMutation, {
            input: orderInput
          });

          console.log('GraphQL応答を受信しました (ドラフト注文作成)');
          console.log('GraphQL応答内容:', JSON.stringify(createResponse, null, 2));

          if (createResponse.draftOrderCreate?.userErrors?.length > 0) {
            console.error('注文作成エラー:', createResponse.draftOrderCreate.userErrors);
            throw new Error(`注文の作成中にエラーが発生しました: ${JSON.stringify(createResponse.draftOrderCreate.userErrors)}`);
          }

          // 作成されたドラフト注文を完了状態にする
          const draftOrderId = createResponse.draftOrderCreate.draftOrder.id;
          console.log(`ドラフト注文ID: ${draftOrderId} を完了状態にします`);

          const completeDraftOrderMutation = `
            mutation draftOrderComplete($id: ID!, $paymentPending: Boolean) {
              draftOrderComplete(id: $id, paymentPending: $paymentPending) {
                draftOrder {
                  id
                  order {
                    id
                    name
                    totalPrice
                  }
                }
                userErrors {
                  field
                  message
                }
              }
            }
          `;

          const completeResponse = await graphQLClient.request(completeDraftOrderMutation, {
            id: draftOrderId,
            paymentPending: booking.bookingType === 'PROVISIONAL' // 仮予約の場合は支払い保留
          });

          console.log('GraphQL応答を受信しました (ドラフト注文完了)');
          console.log('GraphQL応答内容:', JSON.stringify(completeResponse, null, 2));

          if (completeResponse.draftOrderComplete?.userErrors?.length > 0) {
            console.error('注文完了エラー:', completeResponse.draftOrderComplete.userErrors);
            throw new Error(`注文の完了中にエラーが発生しました: ${JSON.stringify(completeResponse.draftOrderComplete.userErrors)}`);
          }

          // 予約情報を更新（注文IDを保存）
          const orderId = completeResponse.draftOrderComplete.draftOrder.order.id;
          const orderName = completeResponse.draftOrderComplete.draftOrder.order.name;

          await prisma.booking.update({
            where: { id: booking.id },
            data: {
              shopifyOrderId: orderId,
              shopifyOrderName: orderName
            }
          });

          console.log(`予約情報を更新しました: 注文ID ${orderId}, 注文番号 ${orderName}`);

          return {
            success: true,
            orderId,
            orderName,
            draftOrderId
          };
        } catch (graphqlError) {
          console.error('GraphQL呼び出しエラー:', graphqlError);
          throw graphqlError;
        }
      },
      3, // リトライ回数
      1000, // 1秒後にリトライ
      2 // 指数バックオフ（1秒、2秒、4秒...）
    );

    if (result.success) {
      console.log('注文作成が成功しました');
      console.log(`注文ID: ${result.orderId}`);
      console.log(`注文番号: ${result.orderName}`);

      // 更新された予約情報を表示
      console.log('\n----- 注文作成後の予約情報 -----');
      await displayBookingInfo(bookingId);

      // Shopify注文情報を取得
      console.log('\n----- Shopify注文情報 -----');
      const order = await getShopifyOrder(result.orderId);

      if (order) {
        console.log(`注文ID: ${order.id}`);
        console.log(`注文番号: ${order.name}`);
        console.log(`作成日時: ${order.createdAt}`);
        console.log(`支払い状態: ${order.displayFinancialStatus}`);
        console.log(`合計金額: ${order.totalPriceSet.shopMoney.amount} ${order.totalPriceSet.shopMoney.currencyCode}`);

        if (order.customer) {
          console.log(`顧客: ${order.customer.displayName} (${order.customer.email})`);
        }

        console.log('商品:');
        order.lineItems.edges.forEach((edge: any, index: number) => {
          const item = edge.node;
          console.log(`  ${index + 1}. ${item.title} x ${item.quantity} (${item.originalUnitPrice})`);
        });
      } else {
        console.error('Shopify注文情報の取得に失敗しました');
      }

      return true;
    } else {
      console.error('注文作成に失敗しました');
      console.error(`エラー: ${result.error || 'エラー詳細なし'}`);
      return false;
    }
  } catch (error) {
    console.error('注文作成テストエラー:', error);
    if (error instanceof Error) {
      console.error('エラーメッセージ:', error.message);
      console.error('スタックトレース:', error.stack);
    }
    return false;
  }
}

/**
 * 最新の予約を取得する関数
 */
async function getLatestBooking() {
  try {
    const booking = await prisma.booking.findFirst({
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!booking) {
      console.error('予約が見つかりません');
      return null;
    }

    return booking;
  } catch (error) {
    console.error('最新予約取得エラー:', error);
    return null;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('予約からShopify注文を作成するテストを開始します...');

    // コマンドライン引数から予約IDを取得
    const bookingId = process.argv[2];

    // 予約IDが指定されていない場合は最新の予約を使用
    let targetBookingId = bookingId;
    if (!targetBookingId) {
      console.log('予約IDが指定されていないため、最新の予約を使用します');
      const latestBooking = await getLatestBooking();
      if (!latestBooking) {
        console.error('予約が見つかりません。予約を作成してから再試行してください。');
        process.exit(1);
      }
      targetBookingId = latestBooking.id;
      console.log(`最新の予約ID: ${targetBookingId}`);
    }

    // 予約からShopify注文を作成
    const result = await createOrderFromBookingTest(targetBookingId);

    if (result) {
      console.log('\nテストが成功しました！');
    } else {
      console.error('\nテストが失敗しました。');
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    if (error instanceof Error) {
      console.error('エラーメッセージ:', error.message);
      console.error('スタックトレース:', error.stack);
    }
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
