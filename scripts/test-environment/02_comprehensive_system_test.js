/**
 * 包括的システムテスト
 *
 * 全ての予約（重複・本予約・仮予約（ステータス変更・キャンセル・自動キャンセル））
 * 商品登録（メタフィールド・在庫）
 * 価格計算（日数・本予約・仮予約）
 * などの機能をテストします
 */

import { PrismaClient } from '@prisma/client';
import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();
const SHOPIFY_SHOP = process.env.SHOPIFY_SHOP;
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

/**
 * Shopify GraphQL APIを呼び出し
 */
async function callShopifyGraphQL(query, variables = {}) {
  const response = await fetch(`https://${SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN
    },
    body: JSON.stringify({ query, variables })
  });

  const data = await response.json();

  if (data.errors) {
    console.error('GraphQLエラー:', JSON.stringify(data.errors, null, 2));
    throw new Error('Shopify API エラー');
  }

  return data.data;
}

/**
 * 価格計算テスト
 */
function testPriceCalculation() {
  console.log('\n=== 📊 価格計算テスト ===');

  // 正しい価格計算ロジック
  function calculateVariantPrice(basePrice, days) {
    if (days === 1) return basePrice;
    if (days >= 2 && days <= 7) return Math.floor(basePrice + (basePrice * 0.2 * (days - 1)));
    if (days >= 8) return Math.floor(basePrice + (basePrice * 0.2 * 6)); // 8日以上は7日分の料金
    return basePrice;
  }

  // 仮予約価格計算
  function calculateProvisionalPrice(basePrice) {
    return Math.floor(basePrice * 0.1); // 10%
  }

  const testCases = [
    { basePrice: 8000, days: 1, expected: 8000 },
    { basePrice: 8000, days: 2, expected: 9600 },
    { basePrice: 8000, days: 3, expected: 11200 },
    { basePrice: 8000, days: 7, expected: 17600 },
    { basePrice: 8000, days: 8, expected: 17600 }, // 8日以上は7日分
    { basePrice: 9000, days: 1, expected: 9000 },
    { basePrice: 9000, days: 7, expected: 19800 },
    { basePrice: 9000, days: 8, expected: 19800 }
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  testCases.forEach((testCase, index) => {
    const { basePrice, days, expected } = testCase;
    const calculated = calculateVariantPrice(basePrice, days);
    const isCorrect = calculated === expected;

    console.log(`${index + 1}. ${days}日間レンタル (基本価格: ¥${basePrice.toLocaleString()}):`);
    console.log(`   計算結果: ¥${calculated.toLocaleString()}`);
    console.log(`   期待値: ¥${expected.toLocaleString()}`);
    console.log(`   判定: ${isCorrect ? '✅ 正しい' : '❌ 間違い'}`);

    if (isCorrect) passedTests++;
    console.log('');
  });

  // 仮予約価格テスト
  console.log('仮予約価格テスト:');
  const provisionalTests = [
    { basePrice: 8000, expected: 800 },
    { basePrice: 9000, expected: 900 }
  ];

  provisionalTests.forEach((test, index) => {
    const calculated = calculateProvisionalPrice(test.basePrice);
    const isCorrect = calculated === test.expected;

    console.log(`${index + 1}. 仮予約 (基本価格: ¥${test.basePrice.toLocaleString()}):`);
    console.log(`   計算結果: ¥${calculated.toLocaleString()}`);
    console.log(`   期待値: ¥${test.expected.toLocaleString()}`);
    console.log(`   判定: ${isCorrect ? '✅ 正しい' : '❌ 間違い'}`);

    if (isCorrect) passedTests++;
    totalTests++;
    console.log('');
  });

  console.log(`📊 価格計算テスト結果: ${passedTests}/${totalTests} 成功`);
  return passedTests === totalTests;
}

/**
 * 商品登録テスト
 */
async function testProductRegistration() {
  console.log('\n=== 📦 商品登録テスト ===');

  try {
    // 最新の商品を取得
    const products = await prisma.product.findMany({
      where: { shop: SHOPIFY_SHOP },
      orderBy: { createdAt: 'desc' },
      take: 2
    });

    if (products.length === 0) {
      console.log('❌ テスト商品が見つかりません');
      return false;
    }

    console.log(`✅ 商品数: ${products.length}件`);

    // 各商品の詳細をチェック
    for (const product of products) {
      console.log(`\n📦 商品: ${product.sku} - ${product.title}`);

      // Shopify商品の詳細を取得
      const shopifyQuery = `
        query getProduct($id: ID!) {
          product(id: $id) {
            id
            title
            variants(first: 10) {
              edges {
                node {
                  id
                  sku
                  price
                  inventoryQuantity
                }
              }
            }
            metafields(first: 10, namespace: "rental") {
              edges {
                node {
                  namespace
                  key
                  value
                }
              }
            }
          }
        }
      `;

      try {
        const shopifyData = await callShopifyGraphQL(shopifyQuery, { id: product.shopifyId });
        const shopifyProduct = shopifyData.product;

        if (shopifyProduct) {
          console.log(`   ✅ Shopify同期: OK`);
          console.log(`   📊 バリアント数: ${shopifyProduct.variants.edges.length}個`);
          console.log(`   🏷️ メタフィールド数: ${shopifyProduct.metafields.edges.length}個`);

          // バリアント価格チェック
          const variants = shopifyProduct.variants.edges;
          if (variants.length === 8) {
            console.log(`   ✅ バリアント数: 正しい (8個)`);
          } else {
            console.log(`   ❌ バリアント数: 間違い (${variants.length}個)`);
          }

          // メタフィールドチェック
          const metafields = shopifyProduct.metafields.edges;
          const expectedMetafields = ['basic_info', 'pricing', 'location', 'status', 'variation_type', 'booking_type', 'variant_mapping'];
          const foundMetafields = metafields.map(m => m.node.key);

          const missingMetafields = expectedMetafields.filter(key => !foundMetafields.includes(key));
          if (missingMetafields.length === 0) {
            console.log(`   ✅ メタフィールド: 完全`);
          } else {
            console.log(`   ❌ 不足メタフィールド: ${missingMetafields.join(', ')}`);
          }

        } else {
          console.log(`   ❌ Shopify商品が見つかりません`);
        }
      } catch (error) {
        console.log(`   ❌ Shopify API エラー: ${error.message}`);
      }
    }

    return true;
  } catch (error) {
    console.error('商品登録テストエラー:', error);
    return false;
  }
}

/**
 * 予約システムテスト
 */
async function testBookingSystem() {
  console.log('\n=== 📅 予約システムテスト ===');

  try {
    // テスト用商品を取得
    const product = await prisma.product.findFirst({
      where: { shop: SHOPIFY_SHOP },
      orderBy: { createdAt: 'desc' }
    });

    if (!product) {
      console.log('❌ テスト商品が見つかりません');
      return false;
    }

    console.log(`📦 テスト商品: ${product.sku} - ${product.title}`);

    // テスト日付を設定
    const today = new Date();
    const startDate = new Date(today);
    startDate.setDate(today.getDate() + 7); // 1週間後
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 2); // 3日間レンタル

    console.log(`📅 テスト期間: ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`);

    // 1. 仮予約テスト
    console.log('\n1. 仮予約テスト');
    const provisionalBooking = await prisma.booking.create({
      data: {
        bookingId: `TEST-PROV-${Date.now()}`,
        shop: SHOPIFY_SHOP,
        productId: product.id,
        startDate,
        endDate,
        customerName: '仮予約テストユーザー',
        customerEmail: '<EMAIL>',
        bookingType: 'PROVISIONAL',
        status: 'PROVISIONAL',
        paymentStatus: 'PENDING',
        totalAmount: 10000,
        depositAmount: 1000,
        priority: 1
      }
    });
    console.log(`   ✅ 仮予約作成: ${provisionalBooking.bookingId}`);

    // 2. 本予約テスト（重複チェック）
    console.log('\n2. 本予約テスト（重複期間）');
    try {
      const duplicateBooking = await prisma.booking.create({
        data: {
          bookingId: `TEST-CONF-${Date.now()}`,
          shop: SHOPIFY_SHOP,
          productId: product.id,
          startDate,
          endDate,
          customerName: '本予約テストユーザー',
          customerEmail: '<EMAIL>',
          bookingType: 'CONFIRMED',
          status: 'CONFIRMED',
          paymentStatus: 'COMPLETED',
          totalAmount: 15000,
          depositAmount: 1500,
          priority: 2
        }
      });
      console.log(`   ✅ 本予約作成: ${duplicateBooking.bookingId}`);
      console.log(`   ⚠️ 重複期間でも予約可能（要確認）`);
    } catch (error) {
      console.log(`   ✅ 重複期間の予約が正しく拒否されました`);
    }

    // 3. ステータス変更テスト
    console.log('\n3. ステータス変更テスト');
    const updatedBooking = await prisma.booking.update({
      where: { id: provisionalBooking.id },
      data: {
        status: 'CONFIRMED',
        bookingType: 'CONFIRMED',
        paymentStatus: 'COMPLETED'
      }
    });
    console.log(`   ✅ 仮予約→本予約変更: ${updatedBooking.bookingId}`);

    // 4. キャンセルテスト
    console.log('\n4. キャンセルテスト');
    const cancelledBooking = await prisma.booking.update({
      where: { id: provisionalBooking.id },
      data: {
        status: 'CANCELLED',
        paymentStatus: 'REFUNDED'
      }
    });
    console.log(`   ✅ 予約キャンセル: ${cancelledBooking.bookingId}`);

    // 5. 自動キャンセルテスト（期限切れ仮予約）
    console.log('\n5. 自動キャンセルテスト');
    const expiredDate = new Date();
    expiredDate.setDate(expiredDate.getDate() - 8); // 8日前（期限切れ）

    const expiredBooking = await prisma.booking.create({
      data: {
        bookingId: `TEST-EXP-${Date.now()}`,
        shop: SHOPIFY_SHOP,
        productId: product.id,
        startDate: new Date(expiredDate.getTime() + 10 * 24 * 60 * 60 * 1000), // 10日後
        endDate: new Date(expiredDate.getTime() + 12 * 24 * 60 * 60 * 1000), // 12日後
        customerName: '期限切れテストユーザー',
        customerEmail: '<EMAIL>',
        bookingType: 'PROVISIONAL',
        status: 'PROVISIONAL',
        paymentStatus: 'PENDING',
        totalAmount: 8000,
        depositAmount: 800,
        priority: 1,
        createdAt: expiredDate // 8日前に作成
      }
    });

    // 期限切れ仮予約を自動キャンセル（EXPIREDステータスを使用）
    const autoCancel = await prisma.booking.update({
      where: { id: expiredBooking.id },
      data: {
        status: 'EXPIRED',
        paymentStatus: 'FAILED'
      }
    });
    console.log(`   ✅ 自動キャンセル（期限切れ）: ${autoCancel.bookingId}`);

    // テストデータのクリーンアップ（関連データを考慮した順序で削除）
    console.log('\n6. テストデータクリーンアップ');

    // まず、TEST-で始まる予約に関連する配送スケジュールを削除
    const testBookings = await prisma.booking.findMany({
      where: {
        bookingId: {
          startsWith: 'TEST-'
        }
      },
      select: { id: true, bookingId: true }
    });

    console.log(`   📦 対象予約数: ${testBookings.length}件`);

    // 関連する配送スケジュールを削除
    for (const booking of testBookings) {
      const deletedSchedules = await prisma.deliverySchedule.deleteMany({
        where: {
          bookingId: booking.id
        }
      });
      if (deletedSchedules.count > 0) {
        console.log(`   🚚 配送スケジュール削除: ${booking.bookingId} (${deletedSchedules.count}件)`);
      }
    }

    // 次に、予約データを削除
    const deletedBookings = await prisma.booking.deleteMany({
      where: {
        bookingId: {
          startsWith: 'TEST-'
        }
      }
    });
    console.log(`   ✅ テスト予約削除完了: ${deletedBookings.count}件`);

    return true;
  } catch (error) {
    console.error('予約システムテストエラー:', error);
    return false;
  }
}

/**
 * メイン関数
 */
async function main() {
  console.log('🧪 === 包括的システムテスト開始 ===');
  console.log(`対象ショップ: ${SHOPIFY_SHOP}`);
  console.log(`実行時刻: ${new Date().toLocaleString('ja-JP')}`);

  const startTime = Date.now();
  const results = {
    priceCalculation: false,
    productRegistration: false,
    bookingSystem: false
  };

  try {
    await prisma.$connect();
    console.log('✅ データベース接続成功');

    // 1. 価格計算テスト
    results.priceCalculation = testPriceCalculation();

    // 2. 商品登録テスト
    results.productRegistration = await testProductRegistration();

    // 3. 予約システムテスト
    results.bookingSystem = await testBookingSystem();

    // 結果サマリー
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);

    console.log('\n🎯 === テスト結果サマリー ===');
    console.log(`📊 価格計算: ${results.priceCalculation ? '✅ 成功' : '❌ 失敗'}`);
    console.log(`📦 商品登録: ${results.productRegistration ? '✅ 成功' : '❌ 失敗'}`);
    console.log(`📅 予約システム: ${results.bookingSystem ? '✅ 成功' : '❌ 失敗'}`);
    console.log(`⏱️ 実行時間: ${duration}秒`);

    const successCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    console.log(`📈 成功率: ${Math.round((successCount / totalCount) * 100)}% (${successCount}/${totalCount})`);

    if (successCount === totalCount) {
      console.log('\n🎉 全てのテストが成功しました！');
    } else {
      console.log('\n⚠️ 一部のテストが失敗しました。');
    }

  } catch (error) {
    console.error('❌ テスト実行エラー:', error);
  } finally {
    await prisma.$disconnect();
    console.log('📋 データベース接続終了');
  }
}

// スクリプト実行
main().catch(console.error);
