/**
 * テスト用の予約を作成するスクリプト
 * 
 * このスクリプトは、重複注文テスト用の新しい予約データを作成します。
 * 
 * 実行方法: npx tsx scripts/create-test-booking.ts
 */

import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';
import { addDays, format } from 'date-fns';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// ランダムな予約IDを生成する関数
function generateBookingId() {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);
  return `BOOK-${timestamp}-${random}`;
}

// テスト用の予約を作成する関数
async function createTestBooking() {
  try {
    console.log('テスト用の予約を作成します...');
    
    // 利用可能な商品を取得
    const product = await prisma.product.findFirst({
      where: {
        shop: process.env.SHOPIFY_SHOP,
        status: 'AVAILABLE'
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    if (!product) {
      throw new Error('利用可能な商品が見つかりません。商品を登録してください。');
    }
    
    console.log(`商品情報: ID=${product.id}, タイトル=${product.title}, SKU=${product.sku}`);
    
    // 予約期間を設定（明日から3日間）
    const now = new Date();
    const startDate = addDays(now, 1);
    const endDate = addDays(startDate, 3);
    
    // 予約データを作成
    const bookingData = {
      bookingId: generateBookingId(),
      productId: product.id,
      startDate,
      endDate,
      customerName: 'テスト顧客',
      customerEmail: '<EMAIL>',
      customerId: process.env.TEST_CUSTOMER_ID || '8420600185000', // 環境変数から、または既定値
      totalAmount: 1500,
      depositAmount: 150,
      bookingType: 'CONFIRMED',
      status: 'CONFIRMED',
      shop: process.env.SHOPIFY_SHOP,
      variantId: product.shopifyId, // 商品IDをバリアントIDとして使用（テスト用）
      notes: 'テスト用の予約データ - 重複注文テスト用'
    };
    
    console.log('作成する予約データ:', bookingData);
    
    // 予約を作成
    const booking = await prisma.booking.create({
      data: bookingData
    });
    
    console.log('予約が作成されました:');
    console.log(`- 予約ID: ${booking.id}`);
    console.log(`- 予約番号: ${booking.bookingId}`);
    console.log(`- 商品ID: ${booking.productId}`);
    console.log(`- 期間: ${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')}`);
    console.log(`- 顧客ID: ${booking.customerId}`);
    
    // 環境変数に設定するための指示を表示
    console.log('\n重複注文テストを実行するには、以下の環境変数を設定してください:');
    console.log(`TEST_BOOKING_ID=${booking.id}`);
    
    return booking;
  } catch (error) {
    console.error('予約の作成に失敗しました:', error);
    throw error;
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// スクリプトを実行
createTestBooking()
  .then((booking) => {
    console.log('テスト用の予約が正常に作成されました');
    process.exit(0);
  })
  .catch((error) => {
    console.error('テスト用の予約の作成に失敗しました:', error);
    process.exit(1);
  });
