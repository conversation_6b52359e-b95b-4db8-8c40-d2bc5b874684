/**
 * 予約の日付重複チェックテスト
 *
 * このスクリプトは、同じ商品に対して日付が重複する予約ができないことをテストします。
 * 実行方法: npx tsx scripts/test-booking-date-conflict.ts [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';
import { addDays, format } from 'date-fns';
import { ja } from 'date-fns/locale';
import { InventoryCalendarService } from '../app/services/inventory-calendar.service';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト設定
const config = {
  // テストする商品のShopify ID
  productId: process.argv[2] || '123456789', // コマンドライン引数から取得、または既定値を使用

  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',

  // テスト用の顧客情報
  customer: {
    email: '<EMAIL>',
    name: 'テストユーザー'
  },

  // 予約日数（今日から何日後に予約するか）
  startDaysFromNow: 7,
  durationDays: 3
};

/**
 * 商品情報を取得する関数
 */
async function getProductInfo(shopifyProductId: string) {
  try {
    // Prismaから商品情報を取得
    const product = await prisma.product.findFirst({
      where: { shopifyId: shopifyProductId }
    });

    if (!product) {
      console.error(`商品ID ${shopifyProductId} が見つかりません`);
      return null;
    }

    return product;
  } catch (error) {
    console.error('商品情報取得エラー:', error);
    return null;
  }
}

/**
 * 在庫カレンダー情報を表示する関数
 */
async function displayInventoryCalendar(productId: string, startDate: Date, endDate: Date) {
  try {
    // 在庫カレンダー情報を取得
    const inventoryCalendar = await prisma.inventoryCalendar.findMany({
      where: {
        productId,
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      orderBy: { date: 'asc' }
    });

    console.log('\n===== 在庫カレンダー情報 =====');
    if (inventoryCalendar.length === 0) {
      console.log('在庫カレンダー情報がありません');
    } else {
      for (const entry of inventoryCalendar) {
        const dateStr = format(new Date(entry.date), 'yyyy/MM/dd(E)', { locale: ja });
        const status = entry.isAvailable ? '○ 利用可能' : '× 利用不可';
        const reason = entry.unavailableReason ? `(${entry.unavailableReason})` : '';
        const bookingInfo = entry.bookingId ? `予約ID: ${entry.bookingId}` : '';
        console.log(`${dateStr}: ${status} ${reason} ${bookingInfo}`);
      }
    }
  } catch (error) {
    console.error('在庫カレンダー表示エラー:', error);
  }
}

/**
 * 予約を作成する関数
 */
async function createBooking(productId: string, startDate: Date, endDate: Date, bookingId?: string) {
  try {
    console.log(`\n予約を作成中... 商品ID: ${productId}`);
    console.log(`期間: ${format(startDate, 'yyyy/MM/dd')} 〜 ${format(endDate, 'yyyy/MM/dd')}`);

    // 在庫カレンダーサービスを初期化
    const inventoryCalendarService = new InventoryCalendarService();

    // 事前に在庫状態をチェック
    const availabilityResult = await inventoryCalendarService.checkAvailability(productId, startDate, endDate);

    // 在庫が利用不可の場合はエラーを返す
    if (!availabilityResult.available) {
      console.error('選択された日程では予約できません。以下の日付が既に予約されています:');
      for (const date of availabilityResult.unavailableDates) {
        console.error(`${date}: ${availabilityResult.reasons[date]}`);
      }
      return null;
    }

    // 予約データを作成
    const booking = await prisma.booking.create({
      data: {
        id: uuidv4(),
        bookingId: bookingId || `TEST-${Date.now()}-${uuidv4().substring(0, 8).toUpperCase()}`,
        shop: config.shop,
        productId,
        variantId: '1', // テスト用の固定値
        startDate,
        endDate,
        customerName: config.customer.name,
        customerEmail: config.customer.email,
        bookingType: 'PROVISIONAL',
        status: 'PROVISIONAL',
        paymentStatus: 'PENDING',
        totalAmount: 10000,
        priority: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('予約を作成しました:');
    console.log(`予約ID: ${booking.id}`);
    console.log(`予約番号: ${booking.bookingId}`);

    // 予約に基づいて在庫カレンダーを更新
    const calendarResult = await inventoryCalendarService.updateCalendarForBooking(booking);

    // 在庫カレンダーの更新に失敗した場合は予約を削除
    if (!calendarResult.success) {
      console.error('在庫カレンダー更新エラー:', calendarResult.error);

      // 作成した予約を削除
      await prisma.booking.delete({
        where: { id: booking.id }
      });

      return null;
    }

    console.log('在庫カレンダーを更新しました');

    return booking;
  } catch (error) {
    console.error('予約作成エラー:', error);
    return null;
  }
}

/**
 * 在庫状態をチェックする関数
 */
async function checkAvailability(productId: string, startDate: Date, endDate: Date) {
  try {
    console.log(`\n在庫状態をチェック中... 商品ID: ${productId}`);
    console.log(`期間: ${format(startDate, 'yyyy/MM/dd')} 〜 ${format(endDate, 'yyyy/MM/dd')}`);

    // 在庫カレンダーサービスを初期化
    const inventoryCalendarService = new InventoryCalendarService();

    // 在庫状態をチェック
    const result = await inventoryCalendarService.checkAvailability(productId, startDate, endDate);

    console.log(`在庫状態: ${result.available ? '利用可能' : '利用不可'}`);

    if (!result.available) {
      console.log('利用不可の日付:');
      for (const date of result.unavailableDates) {
        console.log(`${date}: ${result.reasons[date]}`);
      }
    }

    return result;
  } catch (error) {
    console.error('在庫状態チェックエラー:', error);
    return { available: false, unavailableDates: [], reasons: {} };
  }
}

/**
 * 予約の日付重複チェックをテストする関数
 */
async function testBookingDateConflict(shopifyProductId: string) {
  try {
    console.log(`商品ID ${shopifyProductId} の予約の日付重複チェックテストを実行します...`);

    // 商品情報を取得
    const product = await getProductInfo(shopifyProductId);

    if (!product) {
      return false;
    }

    // テスト用の日程を設定
    const today = new Date();
    const startDate1 = addDays(today, config.startDaysFromNow);
    const endDate1 = addDays(startDate1, config.durationDays - 1);

    // 重複する日程
    const startDate2 = addDays(startDate1, 1); // 1日後から開始
    const endDate2 = addDays(endDate1, 1); // 1日後まで

    // 重複しない日程
    const startDate3 = addDays(endDate1, 1); // 最初の予約の終了日の翌日から
    const endDate3 = addDays(startDate3, config.durationDays - 1);

    // テスト前の在庫カレンダー情報を表示
    console.log('\n----- テスト前の在庫カレンダー情報 -----');
    await displayInventoryCalendar(product.id, today, addDays(endDate3, 1));

    // テスト1: 最初の予約を作成
    console.log('\n----- テスト1: 最初の予約を作成 -----');
    const booking1 = await createBooking(product.id, startDate1, endDate1);

    if (!booking1) {
      console.error('最初の予約作成に失敗しました');
      return false;
    }

    // 予約作成後の在庫カレンダー情報を表示
    console.log('\n----- 最初の予約作成後の在庫カレンダー情報 -----');
    await displayInventoryCalendar(product.id, today, addDays(endDate3, 1));

    // テスト2: 重複する日程で予約を作成（失敗するはず）
    console.log('\n----- テスト2: 重複する日程で予約を作成 -----');

    // 事前に在庫状態をチェック
    const availabilityCheck = await checkAvailability(product.id, startDate2, endDate2);

    if (availabilityCheck.available) {
      console.error('重複する日程なのに在庫が利用可能と判定されました');
    } else {
      console.log('在庫チェックが正しく機能しています（利用不可と判定）');
    }

    // 重複する予約を作成（エラーになるはず）
    const booking2 = await createBooking(product.id, startDate2, endDate2);

    if (booking2) {
      console.error('重複する予約が作成できてしまいました');
      return false;
    } else {
      console.log('重複する予約は正しく拒否されました');
    }

    // テスト3: 重複しない日程で予約を作成（成功するはず）
    console.log('\n----- テスト3: 重複しない日程で予約を作成 -----');

    // 事前に在庫状態をチェック
    const availabilityCheck2 = await checkAvailability(product.id, startDate3, endDate3);

    if (availabilityCheck2.available) {
      console.log('在庫チェックが正しく機能しています（利用可能と判定）');
    } else {
      console.error('重複しない日程なのに在庫が利用不可と判定されました');
    }

    // 重複しない予約を作成
    const booking3 = await createBooking(product.id, startDate3, endDate3);

    if (!booking3) {
      console.error('重複しない予約の作成に失敗しました');
      return false;
    } else {
      console.log('重複しない予約は正しく作成されました');
    }

    // 最終的な在庫カレンダー情報を表示
    console.log('\n----- 最終的な在庫カレンダー情報 -----');
    await displayInventoryCalendar(product.id, today, addDays(endDate3, 1));

    // テスト後のクリーンアップ
    console.log('\n----- テスト後のクリーンアップ -----');

    // 作成した予約を削除
    if (booking1) {
      await prisma.booking.delete({ where: { id: booking1.id } });
      console.log(`予約 ${booking1.bookingId} を削除しました`);
    }

    if (booking3) {
      await prisma.booking.delete({ where: { id: booking3.id } });
      console.log(`予約 ${booking3.bookingId} を削除しました`);
    }

    // 在庫カレンダーをクリーンアップ
    await prisma.inventoryCalendar.deleteMany({
      where: {
        productId: product.id,
        date: {
          gte: startDate1,
          lte: endDate3
        }
      }
    });
    console.log('在庫カレンダーをクリーンアップしました');

    return true;
  } catch (error) {
    console.error('予約の日付重複チェックテストエラー:', error);
    return false;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('予約の日付重複チェックテストを開始します...');

    // コマンドライン引数から商品IDを取得
    const shopifyProductId = config.productId;

    // 予約の日付重複チェックをテスト
    const testResult = await testBookingDateConflict(shopifyProductId);

    // テスト結果のサマリー
    console.log('\n===== テスト結果サマリー =====');
    console.log(`予約の日付重複チェックテスト: ${testResult ? '成功' : '失敗'}`);

    if (testResult) {
      console.log('\nテストが成功しました！');
    } else {
      console.error('\nテストが失敗しました。');
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
