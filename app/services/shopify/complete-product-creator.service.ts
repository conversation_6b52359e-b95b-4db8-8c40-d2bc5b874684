/**
 * 完全なメタフィールド対応商品登録サービス
 * CSVデータから全情報を取得してShopifyに登録
 */

import { VariantAutoCreatorService } from './variant-auto-creator.service';

export interface CSVProductData {
  // 基本情報
  menu: string; // 家具小道具メニュー
  majorCategory: string; // 家具小道具大カテゴリー
  minorCategory: string; // 家具小道具小カテゴリー
  name: string; // 名前
  modelNumber: string; // 型番

  // サイズ情報
  sizeW: string; // サイズW
  sizeD: string; // サイズD
  sizeH: string; // サイズH
  sizeSH: string; // サイズSH
  sizeOther: string; // サイズ(その他)

  // 外観情報
  color: string; // 色
  colorOther: string; // 色(その他)
  material: string; // 素材
  other: string; // その他

  // 在庫・料金情報
  stock: string; // 在庫数
  rentalPrice1Day: string; // レンタル料金1日
  rentalPrice2Days: string; // レンタル料金(1泊2日)
  rentalPrice3Days: string; // レンタル料金(2泊3日)
  rentalPrice4Days: string; // レンタル料金(3泊4日)

  // ステータス情報
  publicStatus: string; // 公開ステータス(公開/非公開)
  newStatus: string; // 新着ステータス(なし/あり)
  campaign: string; // キャンペーン
}

export interface CompleteProductCreationConfig {
  location: 'NY' | 'PR';
  createProvisionalVariants?: boolean;
}

export interface CompleteProductCreationResult {
  success: boolean;
  shopifyProductId?: string;
  createdVariants: number;
  inventoryUpdated: boolean;
  metafieldsSet: boolean;
  errors: string[];
}

export class CompleteProductCreatorService {
  private variantCreator: VariantAutoCreatorService;

  constructor() {
    this.variantCreator = new VariantAutoCreatorService();
  }

  /**
   * CSVデータから完全な商品を作成
   */
  async createCompleteProduct(
    admin: any,
    csvData: CSVProductData,
    config: CompleteProductCreationConfig
  ): Promise<CompleteProductCreationResult> {
    const result: CompleteProductCreationResult = {
      success: false,
      createdVariants: 0,
      inventoryUpdated: false,
      metafieldsSet: false,
      errors: []
    };

    try {
      console.log(`=== 完全商品作成開始: ${csvData.name} ===`);

      // 1. 基本商品情報を準備
      const productInput = this.prepareProductInput(csvData);

      // 2. Shopifyに商品を作成
      console.log('1. Shopify商品作成中...');
      const createResponse = await admin.graphql(`
        mutation productCreate($input: ProductInput!) {
          productCreate(input: $input) {
            product {
              id
              title
              handle
            }
            userErrors {
              field
              message
            }
          }
        }
      `, {
        input: productInput
      });

      const createData = await createResponse.json();

      if (createData.errors || createData.data.productCreate.userErrors.length > 0) {
        throw new Error(`商品作成エラー: ${JSON.stringify(createData.errors || createData.data.productCreate.userErrors)}`);
      }

      const productId = createData.data.productCreate.product.id;
      const shopifyProductId = productId.split('/').pop();
      result.shopifyProductId = shopifyProductId;

      console.log(`✅ 商品作成成功: ${shopifyProductId}`);

      // 2.5. デフォルトバリエーションを更新（スキップ - バリエーション自動作成で対応）
      console.log('2.5. デフォルトバリエーション更新をスキップ（バリエーション自動作成で対応）');

      // 3. 完全なメタフィールドを設定
      console.log('2. 完全メタフィールド設定中...');
      await this.setCompleteMetafields(admin, shopifyProductId, csvData, config);
      result.metafieldsSet = true;
      console.log('✅ 完全メタフィールド設定完了');

      // 4. バリエーション自動作成
      console.log('3. バリエーション自動作成中...');
      const basePrice = parseInt(csvData.rentalPrice1Day) || 1000;

      const variantResult = await this.variantCreator.createMissingVariants(admin, shopifyProductId, {
        basePrice: basePrice,
        productStatus: csvData.publicStatus === '公開' ? 'available' : 'unavailable',
        location: config.location,
        sku: csvData.modelNumber,
        createProvisionalVariants: config.createProvisionalVariants || false,
        createDays: [1, 2, 3, 4, 5, 6, 7] // 1-7日のバリエーションを作成
      });

      result.createdVariants = variantResult.createdVariants.length;
      result.inventoryUpdated = variantResult.inventoryUpdated;

      if (!variantResult.success) {
        result.errors.push(...variantResult.errors);
      }

      console.log(`✅ バリエーション作成完了: ${result.createdVariants}個`);

      // 在庫設定エラーは無視して成功とする（メタフィールドとバリエーション作成が成功していれば）
      const hasInventoryError = result.errors.some(error => error.includes('在庫設定エラー'));
      result.success = result.createdVariants > 0 && result.metafieldsSet;
      console.log(`=== 完全商品作成完了: ${result.success ? '成功' : '失敗'} ===`);

      return result;

    } catch (error) {
      result.errors.push(`完全商品作成エラー: ${error.message}`);
      console.error('❌ 完全商品作成エラー:', error);
      return result;
    }
  }

  /**
   * デフォルトバリエーションを更新
   */
  private async updateDefaultVariant(admin: any, productId: string, csvData: CSVProductData): Promise<void> {
    try {
      // 商品のバリエーションを取得
      const variantResponse = await admin.graphql(`
        query getProductVariants($id: ID!) {
          product(id: $id) {
            variants(first: 1) {
              edges {
                node {
                  id
                }
              }
            }
          }
        }
      `, {
        id: productId
      });

      const variantData = await variantResponse.json();

      if (variantData.errors) {
        throw new Error(`バリエーション取得エラー: ${JSON.stringify(variantData.errors)}`);
      }

      const variants = variantData.data.product.variants.edges;
      if (variants.length === 0) {
        throw new Error('デフォルトバリエーションが見つかりません');
      }

      const defaultVariantId = variants[0].node.id;

      // デフォルトバリエーションを更新
      await admin.graphql(`
        mutation productVariantUpdate($input: ProductVariantInput!) {
          productVariantUpdate(input: $input) {
            productVariant {
              id
              sku
              price
            }
            userErrors {
              field
              message
            }
          }
        }
      `, {
        input: {
          id: defaultVariantId,
          sku: csvData.modelNumber,
          price: csvData.rentalPrice1Day,
          inventoryManagement: 'SHOPIFY',
          inventoryPolicy: 'DENY'
        }
      });

      console.log(`デフォルトバリエーション更新: SKU=${csvData.modelNumber}, 価格=¥${csvData.rentalPrice1Day}`);

    } catch (error) {
      console.error('デフォルトバリエーション更新エラー:', error);
      throw error;
    }
  }

  /**
   * 商品入力データを準備
   */
  private prepareProductInput(csvData: CSVProductData): any {
    // タグを準備
    const tags = [
      csvData.menu,
      csvData.majorCategory,
      csvData.minorCategory,
      csvData.color,
      csvData.material
    ].filter(tag => tag && tag.trim() !== '').map(tag => tag.trim());

    // キャンペーンタグを追加
    if (csvData.campaign && csvData.campaign.trim() !== '') {
      tags.push(...csvData.campaign.split(';').map(tag => tag.trim()));
    }

    // 商品説明を生成
    const description = this.generateProductDescription(csvData);

    return {
      title: csvData.name,
      descriptionHtml: description,
      vendor: 'IZIZ RENTAL',
      productType: csvData.majorCategory,
      tags: [...new Set(tags)], // 重複を除去
      status: csvData.publicStatus === '公開' ? 'ACTIVE' : 'DRAFT'
    };
  }

  /**
   * 商品説明を生成
   */
  private generateProductDescription(csvData: CSVProductData): string {
    let description = `<h3>${csvData.name}</h3>`;

    // サイズ情報
    const sizes = [];
    if (csvData.sizeW && csvData.sizeW !== '0') sizes.push(`幅${csvData.sizeW}cm`);
    if (csvData.sizeD && csvData.sizeD !== '0') sizes.push(`奥行${csvData.sizeD}cm`);
    if (csvData.sizeH && csvData.sizeH !== '0') sizes.push(`高さ${csvData.sizeH}cm`);
    if (csvData.sizeSH && csvData.sizeSH !== '0') sizes.push(`座面高${csvData.sizeSH}cm`);

    if (sizes.length > 0) {
      description += `<p><strong>サイズ:</strong> ${sizes.join(' × ')}</p>`;
    }

    if (csvData.sizeOther && csvData.sizeOther.trim() !== '') {
      description += `<p><strong>サイズ詳細:</strong> ${csvData.sizeOther}</p>`;
    }

    // 色・素材情報
    if (csvData.color && csvData.color.trim() !== '') {
      description += `<p><strong>色:</strong> ${csvData.color}</p>`;
    }

    if (csvData.material && csvData.material.trim() !== '') {
      description += `<p><strong>素材:</strong> ${csvData.material}</p>`;
    }

    // その他の情報
    if (csvData.other && csvData.other.trim() !== '') {
      description += `<p><strong>備考:</strong> ${csvData.other}</p>`;
    }

    // 型番
    description += `<p><strong>型番:</strong> ${csvData.modelNumber}</p>`;

    // カテゴリ情報
    description += `<p><strong>カテゴリ:</strong> ${csvData.menu} > ${csvData.majorCategory} > ${csvData.minorCategory}</p>`;

    return description;
  }

  /**
   * 完全なメタフィールドを設定
   */
  private async setCompleteMetafields(
    admin: any,
    shopifyProductId: string,
    csvData: CSVProductData,
    config: CompleteProductCreationConfig
  ): Promise<void> {
    // 基本情報メタフィールド
    const basicInfo = {
      productCode: csvData.modelNumber.split('-')[0] || csvData.modelNumber,
      detailCode: csvData.modelNumber.split('-')[1] || '001',
      modelNumber: csvData.modelNumber,
      name: csvData.name,
      location: config.location,
      status: csvData.publicStatus === '公開' ? 'available' : 'unavailable'
    };

    // サイズ情報メタフィールド
    const sizeInfo = {
      width: csvData.sizeW || '0',
      depth: csvData.sizeD || '0',
      height: csvData.sizeH || '0',
      seatHeight: csvData.sizeSH || '0',
      otherSize: csvData.sizeOther || ''
    };

    // 外観情報メタフィールド
    const appearanceInfo = {
      color: csvData.color || '',
      colorOther: csvData.colorOther || '',
      material: csvData.material || '',
      other: csvData.other || ''
    };

    // カテゴリ情報メタフィールド
    const categoryInfo = {
      menu: csvData.menu || '',
      majorCategory: csvData.majorCategory || '',
      minorCategory: csvData.minorCategory || ''
    };

    // 料金情報メタフィールド
    const pricingInfo = {
      basePrice: parseInt(csvData.rentalPrice1Day) || 0,
      price2Days: parseInt(csvData.rentalPrice2Days) || 0,
      price3Days: parseInt(csvData.rentalPrice3Days) || 0,
      price4Days: parseInt(csvData.rentalPrice4Days) || 0,
      depositRate: 0.1,
      discountRules: {
        day2_6_rate: 0.2,
        day7_plus_rate: 0.1
      }
    };

    // ステータス情報メタフィールド
    const statusInfo = {
      publicStatus: csvData.publicStatus || '非公開',
      newStatus: csvData.newStatus || 'なし',
      campaign: csvData.campaign || '',
      stock: parseInt(csvData.stock) || 1
    };

    try {
      await admin.graphql(`
        mutation productUpdate($input: ProductInput!) {
          productUpdate(input: $input) {
            product {
              id
            }
            userErrors {
              field
              message
            }
          }
        }
      `, {
        input: {
          id: `gid://shopify/Product/${shopifyProductId}`,
          metafields: [
            {
              namespace: 'rental',
              key: 'basic_info',
              value: JSON.stringify(basicInfo),
              type: 'json'
            },
            {
              namespace: 'rental',
              key: 'size_info',
              value: JSON.stringify(sizeInfo),
              type: 'json'
            },
            {
              namespace: 'rental',
              key: 'appearance_info',
              value: JSON.stringify(appearanceInfo),
              type: 'json'
            },
            {
              namespace: 'rental',
              key: 'category_info',
              value: JSON.stringify(categoryInfo),
              type: 'json'
            },
            {
              namespace: 'rental',
              key: 'pricing_info',
              value: JSON.stringify(pricingInfo),
              type: 'json'
            },
            {
              namespace: 'rental',
              key: 'status_info',
              value: JSON.stringify(statusInfo),
              type: 'json'
            },
            // 仕様書準拠の個別フィールド
            {
              namespace: 'rental',
              key: 'status',
              value: csvData.publicStatus === '公開' ? 'available' : 'unavailable',
              type: 'single_line_text_field'
            },
            {
              namespace: 'rental',
              key: 'location',
              value: config.location,
              type: 'single_line_text_field'
            },
            {
              namespace: 'rental',
              key: 'maintenance_notes',
              value: '',
              type: 'multi_line_text_field'
            },
            {
              namespace: 'rental',
              key: 'booking_notes',
              value: '',
              type: 'multi_line_text_field'
            },
            {
              namespace: 'rental',
              key: 'booking_type',
              value: 'confirmed',
              type: 'single_line_text_field'
            },
            {
              namespace: 'rental',
              key: 'last_maintenance_date',
              value: new Date().toISOString().split('T')[0],
              type: 'date'
            },
            // 寸法情報（仕様書準拠）
            {
              namespace: 'product',
              key: 'width',
              value: csvData.sizeW || '0',
              type: 'number_integer'
            },
            {
              namespace: 'product',
              key: 'depth',
              value: csvData.sizeD || '0',
              type: 'number_integer'
            },
            {
              namespace: 'product',
              key: 'height',
              value: csvData.sizeH || '0',
              type: 'number_integer'
            },
            // 商品属性情報（仕様書準拠）
            {
              namespace: 'rental',
              key: 'material',
              value: csvData.material || '',
              type: 'single_line_text_field'
            },
            {
              namespace: 'rental',
              key: 'color',
              value: csvData.color || '',
              type: 'single_line_text_field'
            },
            {
              namespace: 'rental',
              key: 'designer',
              value: '',
              type: 'single_line_text_field'
            },
            // 購入・廃棄情報（仕様書準拠）
            {
              namespace: 'rental',
              key: 'purchase_price',
              value: '0',
              type: 'number_decimal'
            },
            {
              namespace: 'rental',
              key: 'purchase_date',
              value: new Date().toISOString().split('T')[0],
              type: 'date'
            },
            {
              namespace: 'rental',
              key: 'purchase_place',
              value: '',
              type: 'single_line_text_field'
            },
            {
              namespace: 'rental',
              key: 'manufacturer',
              value: '',
              type: 'single_line_text_field'
            },
            {
              namespace: 'rental',
              key: 'is_disposed',
              value: 'false',
              type: 'boolean'
            },
            // バリエーション管理（仕様書準拠）
            {
              namespace: 'rental',
              key: 'variation_group',
              value: csvData.modelNumber.split('-')[0] || csvData.modelNumber,
              type: 'single_line_text_field'
            },
            {
              namespace: 'rental',
              key: 'variation_type',
              value: 'レンタル期間',
              type: 'single_line_text_field'
            },
            // 検索・フィルタリング用（後方互換性）
            {
              namespace: 'rental',
              key: 'model_number',
              value: csvData.modelNumber,
              type: 'single_line_text_field'
            },
            {
              namespace: 'rental',
              key: 'menu',
              value: csvData.menu,
              type: 'single_line_text_field'
            },
            {
              namespace: 'rental',
              key: 'major_category',
              value: csvData.majorCategory,
              type: 'single_line_text_field'
            },
            {
              namespace: 'rental',
              key: 'minor_category',
              value: csvData.minorCategory,
              type: 'single_line_text_field'
            }
          ]
        }
      });

      console.log(`完全メタフィールド設定完了: ${csvData.modelNumber}`);

    } catch (error) {
      console.error('完全メタフィールド設定エラー:', error);
      throw error;
    }
  }

  /**
   * CSVデータをパース
   */
  static parseCSVRow(csvRow: string[]): CSVProductData {
    return {
      menu: csvRow[0] || '',
      majorCategory: csvRow[1] || '',
      minorCategory: csvRow[2] || '',
      name: csvRow[3] || '',
      modelNumber: csvRow[4] || '',
      sizeW: csvRow[5] || '',
      sizeD: csvRow[6] || '',
      sizeH: csvRow[7] || '',
      sizeSH: csvRow[8] || '',
      sizeOther: csvRow[9] || '',
      color: csvRow[10] || '',
      colorOther: csvRow[11] || '',
      material: csvRow[12] || '',
      other: csvRow[13] || '',
      stock: csvRow[14] || '',
      rentalPrice1Day: csvRow[15] || '',
      rentalPrice2Days: csvRow[16] || '',
      rentalPrice3Days: csvRow[17] || '',
      rentalPrice4Days: csvRow[18] || '',
      publicStatus: csvRow[19] || '',
      newStatus: csvRow[20] || '',
      campaign: csvRow[21] || ''
    };
  }
}
