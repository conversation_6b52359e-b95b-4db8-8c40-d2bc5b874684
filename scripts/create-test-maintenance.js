import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestMaintenance() {
  try {
    // 既存の商品を取得
    const products = await prisma.product.findMany({
      take: 3
    });

    if (products.length === 0) {
      console.log('商品が見つかりません。先に商品を作成してください。');
      return;
    }

    // テスト用メンテナンスデータを作成
    const maintenanceData = [
      {
        productId: products[0].id,
        type: 'REGULAR_INSPECTION',
        status: 'SCHEDULED',
        startDate: new Date('2025-01-25'),
        endDate: new Date('2025-01-26'),
        notes: '定期点検のテストデータ'
      },
      {
        productId: products[1]?.id || products[0].id,
        type: 'REPAIR',
        status: 'IN_PROGRESS',
        startDate: new Date('2025-01-20'),
        endDate: new Date('2025-01-22'),
        notes: '修理作業のテストデータ'
      },
      {
        productId: products[2]?.id || products[0].id,
        type: 'CLEANING',
        status: 'COMPLETED',
        startDate: new Date('2025-01-15'),
        endDate: new Date('2025-01-16'),
        notes: 'クリーニング完了のテストデータ'
      }
    ];

    for (const data of maintenanceData) {
      const maintenance = await prisma.maintenance.create({
        data,
        include: {
          product: true
        }
      });
      
      console.log(`メンテナンス作成: ${maintenance.id} - ${maintenance.product.title} (${maintenance.type})`);
    }

    console.log('テスト用メンテナンスデータの作成が完了しました。');
  } catch (error) {
    console.error('エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestMaintenance();
