/**
 * 日本の祝日と休業日を管理するユーティリティ
 */

import { RENTAL_SETTINGS } from './rental-settings';
import { isWithinInterval, isSunday, getMonth, getDate } from 'date-fns';

/**
 * 日本の祝日データ（2024年〜2025年）
 * 毎年変わる可能性があるため、定期的に更新が必要
 */
export const JAPANESE_HOLIDAYS = [
  // 2024年の祝日
  { name: '元日', date: new Date(2024, 0, 1) },
  { name: '成人の日', date: new Date(2024, 0, 8) },
  { name: '建国記念の日', date: new Date(2024, 1, 11) },
  { name: '天皇誕生日', date: new Date(2024, 1, 23) },
  { name: '春分の日', date: new Date(2024, 2, 20) },
  { name: '昭和の日', date: new Date(2024, 3, 29) },
  { name: '憲法記念日', date: new Date(2024, 4, 3) },
  { name: 'みどりの日', date: new Date(2024, 4, 4) },
  { name: 'こどもの日', date: new Date(2024, 4, 5) },
  { name: '海の日', date: new Date(2024, 6, 15) },
  { name: '山の日', date: new Date(2024, 7, 11) },
  { name: '敬老の日', date: new Date(2024, 8, 16) },
  { name: '秋分の日', date: new Date(2024, 8, 22) },
  { name: 'スポーツの日', date: new Date(2024, 9, 14) },
  { name: '文化の日', date: new Date(2024, 10, 3) },
  { name: '勤労感謝の日', date: new Date(2024, 10, 23) },

  // 2025年の祝日
  { name: '元日', date: new Date(2025, 0, 1) },
  { name: '成人の日', date: new Date(2025, 0, 13) },
  { name: '建国記念の日', date: new Date(2025, 1, 11) },
  { name: '天皇誕生日', date: new Date(2025, 1, 23) },
  { name: '春分の日', date: new Date(2025, 2, 20) },
  { name: '昭和の日', date: new Date(2025, 3, 29) },
  { name: '憲法記念日', date: new Date(2025, 4, 3) },
  { name: 'みどりの日', date: new Date(2025, 4, 4) },
  { name: 'こどもの日', date: new Date(2025, 4, 5) },
  { name: '海の日', date: new Date(2025, 6, 21) },
  { name: '山の日', date: new Date(2025, 7, 11) },
  { name: '敬老の日', date: new Date(2025, 8, 15) },
  { name: '秋分の日', date: new Date(2025, 8, 23) },
  { name: 'スポーツの日', date: new Date(2025, 9, 13) },
  { name: '文化の日', date: new Date(2025, 10, 3) },
  { name: '勤労感謝の日', date: new Date(2025, 10, 23) },
];

/**
 * 指定された日付が日本の祝日かどうかを判定する
 * @param date 判定する日付
 * @returns 祝日の場合はtrue、そうでない場合はfalse
 */
export function isJapaneseHoliday(date: Date): boolean {
  return JAPANESE_HOLIDAYS.some(holiday =>
    holiday.date.getFullYear() === date.getFullYear() &&
    holiday.date.getMonth() === date.getMonth() &&
    holiday.date.getDate() === date.getDate()
  );
}

/**
 * 指定された日付が年末年始の休業期間かどうかを判定する
 * @param date 判定する日付
 * @returns 年末年始の休業期間の場合はtrue、そうでない場合はfalse
 */
export function isNewYearHoliday(date: Date): boolean {
  const month = date.getMonth();
  const day = date.getDate();

  // 12月の年末休業期間（12/29〜12/31）
  if (month === 11 && day >= RENTAL_SETTINGS.NEW_YEAR_START_DAY) {
    return true;
  }

  // 1月の年始休業期間（1/1〜1/3）
  if (month === 0 && day <= RENTAL_SETTINGS.NEW_YEAR_END_DAY) {
    return true;
  }

  return false;
}

/**
 * 指定された日付が休業日（日曜、祝日、年末年始）かどうかを判定する
 * @param date 判定する日付
 * @param allowToday 当日を許可するかどうか（デフォルトはfalse）
 * @returns 休業日の場合はtrue、そうでない場合はfalse
 */
export function isClosedDay(date: Date, allowToday: boolean = false): boolean {
  // 日付が無効な場合はfalseを返す
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return false;
  }

  // 当日を許可する場合は、当日かどうかをチェック
  if (allowToday) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const isToday = date.getTime() === today.getTime();

    // 当日の場合は休業日ではない
    if (isToday) {
      return false;
    }
  }

  return isSunday(date) || isJapaneseHoliday(date) || isNewYearHoliday(date);
}

/**
 * 指定された日付の休業日タイプを取得する
 * @param date 判定する日付
 * @returns 休業日タイプ（'sunday', 'holiday', 'newyear', null）
 */
export function getClosedDayType(date: Date): 'sunday' | 'holiday' | 'newyear' | null {
  // 日付が無効な場合はnullを返す
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return null;
  }

  if (isSunday(date)) {
    return 'sunday';
  }

  if (isJapaneseHoliday(date)) {
    return 'holiday';
  }

  if (isNewYearHoliday(date)) {
    return 'newyear';
  }

  return null;
}

/**
 * 指定された日付の祝日名を取得する
 * @param date 判定する日付
 * @returns 祝日名（祝日でない場合はnull）
 */
export function getHolidayName(date: Date): string | null {
  // 日付が無効な場合はnullを返す
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return null;
  }

  const holiday = JAPANESE_HOLIDAYS.find(holiday =>
    holiday.date.getFullYear() === date.getFullYear() &&
    holiday.date.getMonth() === date.getMonth() &&
    holiday.date.getDate() === date.getDate()
  );

  return holiday ? holiday.name : null;
}

/**
 * 日付に関する追加情報を取得する
 * @param date 日付
 * @returns 日付の追加情報
 */
export function getDateInfo(date: Date): {
  isClosed: boolean;
  closedType: 'sunday' | 'holiday' | 'newyear' | null;
  holidayName: string | null;
} {
  const closedType = getClosedDayType(date);

  return {
    isClosed: closedType !== null,
    closedType,
    holidayName: getHolidayName(date)
  };
}
