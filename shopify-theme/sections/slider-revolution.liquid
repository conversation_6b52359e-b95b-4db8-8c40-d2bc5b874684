{% comment %} <slider-revolution data-section-id="{{ section.id }}" data-section-type="slider-revolution" class="d-block">
    <div id="rev_slider_1061_1_wrapper" class="rev_slider_wrapper fullscreen-container" data-alias="creative-freedom" data-source="gallery" style="background-color:#1f1d24;padding:0px;">
        <div id="rev_slider_1061_1" class="rev_slider fullscreenbanner" style="display:none;" data-version="5.4.1">
            <ul>
                <li data-index="rs-2978" data-transition="fadethroughdark" data-slotamount="default" data-hideafterloop="0" data-hideslideonmobile="off"  data-easein="default" data-easeout="default" data-masterspeed="2000"  data-thumb="Sail-Away-100x50.jpg"  data-rotate="0"  data-saveperformance="off"  data-title="Creative" data-param1="01" data-param2="" data-param3="" data-param4="" data-param5="" data-param6="" data-param7="" data-param8="" data-param9="" data-param10="" data-description="">
                    <img src="https://cdn.shopify.com/s/files/1/0026/2956/6516/files/Sail-Away.jpg?74674989410185825"  alt=""  data-bgposition="center center" data-bgfit="cover" data-bgparallax="3" class="rev-slidebg" data-no-retina>
                    <div class="rs-background-video-layer"
                         data-forcerewind="on"
                         data-volume="mute"
                         data-videowidth="100%"
                         data-videoheight="100%"
                         data-videomp4="https://cdn.shopify.com/s/files/1/0026/2956/6516/files/Sail-Away.mp4?1509864870388084753"
                         data-videopreload="auto"
                         data-videoloop="loop"
                         data-aspectratio="16:9"
                         data-autoplay="true"
                         data-autoplayonlyfirsttime="false"
                    ></div>
                    <div class="tp-caption tp-shape tp-shapewrapper  rs-parallaxlevel-tobggroup"
                         id="slide-2978-layer-1"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['0','0','0','0']"
                         data-fontweight="['100','100','400','400']"
                         data-width="full"
                         data-height="full"
                         data-whitespace="nowrap"
                         data-type="shape"
                         data-basealign="slide"
                         data-responsive_offset="off"
                         data-responsive="off"
                         data-frames='[{"from":"opacity:0;","speed":1500,"to":"o:1;","delay":150,"ease":"Power2.easeInOut"},{"delay":"wait","speed":1500,"to":"opacity:0;","ease":"Power2.easeInOut"}]'
                         data-textAlign="['left','left','left','left']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 5;text-transform:left;background-color:rgba(18, 12, 20, 0.75);border-color:rgba(0, 0, 0, 0);border-width:0px;"></div>
                    <div class="tp-caption tp-shape tp-shapewrapper  rs-parallaxlevel-3"
                         id="slide-2978-layer-4"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['-178','-178','-168','-141']"
                         data-width="1"
                         data-height="100"
                         data-whitespace="nowrap"
                         data-type="shape"
                         data-responsive_offset="on"
                         data-responsive="off"
                         data-frames='[{"from":"y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;","mask":"x:0px;y:0px;s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1500,"ease":"Power3.easeInOut"},{"delay":"wait","speed":500,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power1.easeIn"}]'
                         data-textAlign="['left','left','left','left']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 6;text-transform:left;background-color:rgba(205, 176, 131, 1.00);border-color:rgba(0, 0, 0, 0);border-width:0px;"></div>
                    <div class="tp-caption Creative-SubTitle   tp-resizeme rs-parallaxlevel-2"
                         id="slide-2978-layer-3"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['-91','-91','-81','-64']"
                         data-fontsize="['14','14','14','12']"
                         data-lineheight="['14','14','14','12']"
                         data-width="none"
                         data-height="none"
                         data-whitespace="nowrap"
                         data-type="text"
                         data-responsive_offset="on"
                         data-frames='[{"from":"y:50px;opacity:0;","speed":1500,"to":"o:1;","delay":2350,"ease":"Power3.easeOut"},{"delay":"wait","speed":1000,"to":"x:0;y:0;z:0;rX:0;rY:0;rZ:0;sX:0.9;sY:0.9;skX:0;skY:0;opacity:0;","ease":"Power3.easeInOut"}]'
                         data-textAlign="['center','center','center','center']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 7; white-space: nowrap;text-transform:left;">WITH SLIDER REVOLUTION YOU WILL </div>
                    <div class="tp-caption Creative-Title   tp-resizeme rs-parallaxlevel-1"
                         id="slide-2978-layer-2"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['-10','-10','-10','-10']"
                         data-fontsize="['70','70','50','40']"
                         data-lineheight="['70','70','55','45']"
                         data-width="['none','none','none','320']"
                         data-height="none"
                         data-whitespace="nowrap"
                         data-type="text"
                         data-responsive_offset="on"
                         data-frames='[{"from":"y:50px;opacity:0;","speed":1500,"to":"o:1;","delay":2550,"ease":"Power3.easeOut"},{"delay":"wait","speed":1000,"to":"x:0;y:0;z:0;rX:0;rY:0;rZ:0;sX:0.9;sY:0.9;skX:0;skY:0;opacity:0;","ease":"Power3.easeInOut"}]'
                         data-textAlign="['center','center','center','center']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 8; white-space: nowrap;text-transform:left;">Experience<br/>
                        Creative Freedom</div>
                    <div class="tp-caption tp-shape tp-shapewrapper  rs-parallaxlevel-3"
                         id="slide-2978-layer-5"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['137','137','119','100']"
                         data-width="1"
                         data-height="100"
                         data-whitespace="nowrap"
                         data-type="shape"
                         data-responsive_offset="on"
                         data-responsive="off"
                         data-frames='[{"from":"y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;","mask":"x:0px;y:0px;s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":2900,"ease":"Power3.easeInOut"},{"delay":"wait","speed":500,"to":"y:[-100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power1.easeIn"}]'
                         data-textAlign="['left','left','left','left']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 9;text-transform:left;background-color:rgba(205, 176, 131, 1.00);border-color:rgba(0, 0, 0, 0);border-width:0px;"> </div>
                    <div class="tp-caption Creative-Button rev-btn  rs-parallaxlevel-15"
                         id="slide-2978-layer-6"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['top','top','top','top']" data-voffset="['694','611','689','540']"
                         data-fontweight="['400','500','500','500']"
                         data-width="none"
                         data-height="none"
                         data-whitespace="nowrap"
                         data-type="button"
                         data-actions='[{"event":"click","action":"jumptoslide","slide":"next","delay":""}]'
                         data-responsive_offset="on"
                         data-responsive="off"
                         data-frames='[{"from":"z:0;rX:0;rY:0;rZ:0;sX:0.9;sY:0.9;skX:0;skY:0;opacity:0;","speed":1500,"to":"o:1;","delay":3850,"ease":"Power2.easeOut"},{"delay":"wait","speed":500,"to":"x:0;y:0;z:0;rX:0;rY:0;rZ:0;sX:0.75;sY:0.75;skX:0;skY:0;opacity:0;","ease":"Power1.easeIn"},{"frame":"hover","speed":"300","ease":"Power1.easeInOut","to":"o:1;rX:0;rY:0;rZ:0;z:0;","style":"c:rgba(205, 176, 131, 1.00);bc:rgba(205, 176, 131, 1.00);bw:1px 1px 1px 1px;"}]'
                         data-textAlign="['left','left','left','left']"
                         data-paddingtop="[15,15,15,15]"
                         data-paddingright="[50,50,50,50]"
                         data-paddingbottom="[15,15,15,15]"
                         data-paddingleft="[50,50,50,50]"
                         style="z-index: 10; white-space: nowrap;text-transform:left;outline:none;box-shadow:none;box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;cursor:pointer;">CONTINUE THE JOURNEY </div>
                </li>
                <li data-index="rs-2979" data-transition="fadethroughdark" data-slotamount="default" data-hideafterloop="0" data-hideslideonmobile="off"  data-easein="default" data-easeout="default" data-masterspeed="2000"  data-thumb="creative_bg1-100x50.jpg"  data-rotate="0"  data-saveperformance="off"  data-title="Quality" data-param1="02" data-param2="" data-param3="" data-param4="" data-param5="" data-param6="" data-param7="" data-param8="" data-param9="" data-param10="" data-description="">
                    <img src="https://cdn.shopify.com/s/files/1/0026/2956/6516/files/creative_bg1.jpg?9158175175038397839"  alt=""  data-bgposition="center center" data-bgfit="cover" data-bgrepeat="no-repeat" data-bgparallax="3" class="rev-slidebg" data-no-retina>
                    <div class="tp-caption tp-shape tp-shapewrapper  rs-parallaxlevel-tobggroup"
                         id="slide-2979-layer-1"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['0','0','0','0']"
                         data-width="full"
                         data-height="full"
                         data-whitespace="nowrap"
                         data-type="shape"
                         data-basealign="slide"
                         data-responsive_offset="off"
                         data-responsive="off"
                         data-frames='[{"from":"opacity:0;","speed":1500,"to":"o:1;","delay":150,"ease":"Power2.easeInOut"},{"delay":"wait","speed":1500,"to":"opacity:0;","ease":"Power2.easeInOut"}]'
                         data-textAlign="['left','left','left','left']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 11;text-transform:left;background-color:rgba(18, 12, 20, 0.75);border-color:rgba(0, 0, 0, 0);border-width:0px;"> </div>
                    <div class="tp-caption tp-shape tp-shapewrapper  rs-parallaxlevel-3"
                         id="slide-2979-layer-4"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['-178','-178','-168','-141']"
                         data-width="1"
                         data-height="100"
                         data-whitespace="nowrap"
                         data-type="shape"
                         data-responsive_offset="on"
                         data-responsive="off"
                         data-frames='[{"from":"y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;","mask":"x:0px;y:0px;s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1500,"ease":"Power3.easeInOut"},{"delay":"wait","speed":500,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power1.easeIn"}]'
                         data-textAlign="['left','left','left','left']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 12;text-transform:left;background-color:rgba(205, 176, 131, 1.00);border-color:rgba(0, 0, 0, 0);border-width:0px;"> </div>
                    <div class="tp-caption Creative-SubTitle   tp-resizeme rs-parallaxlevel-2"
                         id="slide-2979-layer-3"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['-91','-91','-81','-64']"
                         data-fontsize="['14','14','14','12']"
                         data-lineheight="['14','14','14','12']"
                         data-width="none"
                         data-height="none"
                         data-whitespace="nowrap"
                         data-type="text"
                         data-responsive_offset="on"
                         data-frames='[{"from":"y:50px;opacity:0;","speed":1500,"to":"o:1;","delay":2350,"ease":"Power3.easeOut"},{"delay":"wait","speed":1000,"to":"x:0;y:0;z:0;rX:0;rY:0;rZ:0;sX:0.9;sY:0.9;skX:0;skY:0;opacity:0;","ease":"Power3.easeInOut"}]'
                         data-textAlign="['center','center','center','center']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 13; white-space: nowrap;text-transform:left;">OPTIMIZED FOR MOBILE DEVICES </div>
                    <div class="tp-caption Creative-Title   tp-resizeme rs-parallaxlevel-1"
                         id="slide-2979-layer-2"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['-10','-10','-10','-10']"
                         data-fontsize="['70','70','50','40']"
                         data-lineheight="['70','70','55','45']"
                         data-width="['none','none','none','320']"
                         data-height="none"
                         data-whitespace="nowrap"
                         data-type="text"
                         data-responsive_offset="on"
                         data-frames='[{"from":"y:50px;opacity:0;","speed":1500,"to":"o:1;","delay":2550,"ease":"Power3.easeOut"},{"delay":"wait","speed":1000,"to":"x:0;y:0;z:0;rX:0;rY:0;rZ:0;sX:0.9;sY:0.9;skX:0;skY:0;opacity:0;","ease":"Power3.easeInOut"}]'
                         data-textAlign="['center','center','center','center']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 14; white-space: nowrap;text-transform:left;">Quality Web<br/>
                        Presentations </div>
                    <div class="tp-caption tp-shape tp-shapewrapper  rs-parallaxlevel-3"
                         id="slide-2979-layer-5"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['137','137','119','100']"
                         data-width="1"
                         data-height="100"
                         data-whitespace="nowrap"
                         data-type="shape"
                         data-responsive_offset="on"
                         data-responsive="off"
                         data-frames='[{"from":"y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;","mask":"x:0px;y:0px;s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":2900,"ease":"Power3.easeInOut"},{"delay":"wait","speed":500,"to":"y:[-100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power1.easeIn"}]'
                         data-textAlign="['left','left','left','left']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 15;text-transform:left;background-color:rgba(205, 176, 131, 1.00);border-color:rgba(0, 0, 0, 0);border-width:0px;"> </div>
                    <div class="tp-caption Creative-Button rev-btn  rs-parallaxlevel-15"
                         id="slide-2979-layer-6"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['top','top','top','top']" data-voffset="['694','611','689','540']"
                         data-fontweight="['400','500','500','500']"
                         data-width="none"
                         data-height="none"
                         data-whitespace="nowrap"
                         data-type="button"
                         data-actions='[{"event":"click","action":"jumptoslide","slide":"next","delay":""}]'
                         data-responsive_offset="on"
                         data-responsive="off"
                         data-frames='[{"from":"z:0;rX:0;rY:0;rZ:0;sX:0.9;sY:0.9;skX:0;skY:0;opacity:0;","speed":1500,"to":"o:1;","delay":3850,"ease":"Power2.easeOut"},{"delay":"wait","speed":500,"to":"x:0;y:0;z:0;rX:0;rY:0;rZ:0;sX:0.75;sY:0.75;skX:0;skY:0;opacity:0;","ease":"Power1.easeIn"},{"frame":"hover","speed":"300","ease":"Power1.easeInOut","to":"o:1;rX:0;rY:0;rZ:0;z:0;","style":"c:rgba(205, 176, 131, 1.00);bc:rgba(205, 176, 131, 1.00);bw:1px 1px 1px 1px;"}]'
                         data-textAlign="['left','left','left','left']"
                         data-paddingtop="[15,15,15,15]"
                         data-paddingright="[50,50,50,50]"
                         data-paddingbottom="[15,15,15,15]"
                         data-paddingleft="[50,50,50,50]"
                         style="z-index: 16; white-space: nowrap;text-transform:left;outline:none;box-shadow:none;box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;cursor:pointer;">CONTINUE THE JOURNEY </div>
                </li>
                <li data-index="rs-2980" data-transition="fadethroughdark" data-slotamount="default" data-hideafterloop="0" data-hideslideonmobile="off"  data-easein="default" data-easeout="default" data-masterspeed="2000"  data-thumb="icebg-100x50.jpg"  data-rotate="0"  data-saveperformance="off"  data-title="Concept" data-param1="03" data-param2="" data-param3="" data-param4="" data-param5="" data-param6="" data-param7="" data-param8="" data-param9="" data-param10="" data-description="">
                    <img src="https://cdn.shopify.com/s/files/1/0026/2956/6516/files/icebg.jpg?9574852630185236297"  alt=""  data-bgposition="center center" data-bgfit="cover" data-bgrepeat="no-repeat" data-bgparallax="3" class="rev-slidebg" data-no-retina>
                    <div class="tp-caption tp-shape tp-shapewrapper  rs-parallaxlevel-tobggroup"
                         id="slide-2980-layer-1"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['0','0','0','0']"
                         data-width="full"
                         data-height="full"
                         data-whitespace="nowrap"
                         data-type="shape"
                         data-basealign="slide"
                         data-responsive_offset="off"
                         data-responsive="off"
                         data-frames='[{"from":"opacity:0;","speed":1500,"to":"o:1;","delay":150,"ease":"Power2.easeInOut"},{"delay":"wait","speed":1500,"to":"opacity:0;","ease":"Power2.easeInOut"}]'
                         data-textAlign="['left','left','left','left']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 17;text-transform:left;background-color:rgba(18, 12, 20, 0.75);border-color:rgba(0, 0, 0, 0);border-width:0px;"> </div>
                    <div class="tp-caption tp-shape tp-shapewrapper  rs-parallaxlevel-3"
                         id="slide-2980-layer-4"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['-178','-178','-168','-141']"
                         data-width="1"
                         data-height="100"
                         data-whitespace="nowrap"
                         data-type="shape"
                         data-responsive_offset="on"
                         data-responsive="off"
                         data-frames='[{"from":"y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;","mask":"x:0px;y:0px;s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1500,"ease":"Power3.easeInOut"},{"delay":"wait","speed":500,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power1.easeIn"}]'
                         data-textAlign="['left','left','left','left']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 18;text-transform:left;background-color:rgba(205, 176, 131, 1.00);border-color:rgba(0, 0, 0, 0);border-width:0px;"> </div>
                    <div class="tp-caption Creative-SubTitle   tp-resizeme rs-parallaxlevel-2"
                         id="slide-2980-layer-3"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['-91','-91','-81','-64']"
                         data-fontsize="['14','14','14','12']"
                         data-lineheight="['14','14','14','12']"
                         data-width="none"
                         data-height="none"
                         data-whitespace="nowrap"
                         data-type="text"
                         data-responsive_offset="on"
                         data-frames='[{"from":"y:50px;opacity:0;","speed":1500,"to":"o:1;","delay":2350,"ease":"Power3.easeOut"},{"delay":"wait","speed":1000,"to":"x:0;y:0;z:0;rX:0;rY:0;rZ:0;sX:0.9;sY:0.9;skX:0;skY:0;opacity:0;","ease":"Power3.easeInOut"}]'
                         data-textAlign="['center','center','center','center']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 19; white-space: nowrap;text-transform:left;">EXPLORE NEW CONCEPT WORLDS </div>
                    <div class="tp-caption Creative-Title   tp-resizeme rs-parallaxlevel-1"
                         id="slide-2980-layer-2"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['-10','-10','-10','-10']"
                         data-fontsize="['70','70','50','40']"
                         data-lineheight="['70','70','55','45']"
                         data-width="['none','none','none','320']"
                         data-height="none"
                         data-whitespace="nowrap"
                         data-type="text"
                         data-responsive_offset="on"
                         data-frames='[{"from":"y:50px;opacity:0;","speed":1500,"to":"o:1;","delay":2550,"ease":"Power3.easeOut"},{"delay":"wait","speed":1000,"to":"x:0;y:0;z:0;rX:0;rY:0;rZ:0;sX:0.9;sY:0.9;skX:0;skY:0;opacity:0;","ease":"Power3.easeInOut"}]'
                         data-textAlign="['center','center','center','center']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 20; white-space: nowrap;text-transform:left;">Groundbreaking<br/>
                        Technology </div>
                    <div class="tp-caption tp-shape tp-shapewrapper  rs-parallaxlevel-3"
                         id="slide-2980-layer-5"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['middle','middle','middle','middle']" data-voffset="['137','137','119','100']"
                         data-width="1"
                         data-height="100"
                         data-whitespace="nowrap"
                         data-type="shape"
                         data-responsive_offset="on"
                         data-responsive="off"
                         data-frames='[{"from":"y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;","mask":"x:0px;y:0px;s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":2900,"ease":"Power3.easeInOut"},{"delay":"wait","speed":500,"to":"y:[-100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power1.easeIn"}]'
                         data-textAlign="['left','left','left','left']"
                         data-paddingtop="[0,0,0,0]"
                         data-paddingright="[0,0,0,0]"
                         data-paddingbottom="[0,0,0,0]"
                         data-paddingleft="[0,0,0,0]"
                         style="z-index: 21;text-transform:left;background-color:rgba(205, 176, 131, 1.00);border-color:rgba(0, 0, 0, 0);border-width:0px;"> </div>
                    <div class="tp-caption Creative-Button rev-btn  rs-parallaxlevel-15"
                         id="slide-2980-layer-6"
                         data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
                         data-y="['top','top','top','top']" data-voffset="['694','611','689','540']"
                         data-fontweight="['400','500','500','500']"
                         data-width="none"
                         data-height="none"
                         data-whitespace="nowrap"
                         data-type="button"
                         data-actions='[{"event":"click","action":"jumptoslide","slide":"next","delay":""}]'
                         data-responsive_offset="on"
                         data-responsive="off"
                         data-frames='[{"from":"z:0;rX:0;rY:0;rZ:0;sX:0.9;sY:0.9;skX:0;skY:0;opacity:0;","speed":1500,"to":"o:1;","delay":3850,"ease":"Power2.easeOut"},{"delay":"wait","speed":500,"to":"x:0;y:0;z:0;rX:0;rY:0;rZ:0;sX:0.75;sY:0.75;skX:0;skY:0;opacity:0;","ease":"Power1.easeIn"},{"frame":"hover","speed":"300","ease":"Power1.easeInOut","to":"o:1;rX:0;rY:0;rZ:0;z:0;","style":"c:rgba(205, 176, 131, 1.00);bc:rgba(205, 176, 131, 1.00);bw:1px 1px 1px 1px;"}]'
                         data-textAlign="['left','left','left','left']"
                         data-paddingtop="[15,15,15,15]"
                         data-paddingright="[50,50,50,50]"
                         data-paddingbottom="[15,15,15,15]"
                         data-paddingleft="[50,50,50,50]"
                         style="z-index: 22; white-space: nowrap;text-transform:left;outline:none;box-shadow:none;box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;cursor:pointer;">BACK TO THE FIRST SLIDE </div>
                </li>
            </ul>
            <div class="tp-bannertimer tp-bottom" style="visibility: hidden !important;"></div>
        </div>
    </div>
    <script>
        theme.AssetsLoader.onPageLoaded(function () {
            (function($){
                page.RevolutionInit = function () {
                    return $("#rev_slider_1061_1").show().revolution({
                        sliderType:"standard",
                        jsFileLocation:"revolution/js/",
                        sliderLayout:"fullscreen",
                        dottedOverlay:"none",
                        delay:9000,
                        navigation: {
                            keyboardNavigation:"off",
                            keyboard_direction: "horizontal",
                            mouseScrollNavigation:"off",
                            mouseScrollReverse:"default",
                            onHoverStop:"off",
                            touch:{
                                touchenabled:"on",
                                swipe_threshold: 75,
                                swipe_min_touches: 50,
                                swipe_direction: "horizontal",
                                drag_block_vertical: false
                            }
                            ,
                            tabs: {
                                style:"metis",
                                enable:true,
                                width:250,
                                height:40,
                                min_width:249,
                                wrapper_padding:0,
                                wrapper_color:"",
                                wrapper_opacity:"0",
                                tmp:'<div class="tp-tab-wrapper"><div class="tp-tab-number">\{\{param1\}\}</div><div class="tp-tab-divider"></div><div class="tp-tab-title-mask"><div class="tp-tab-title">\{\{title\}\}</div></div></div>',
                                visibleAmount: 5,
                                hide_onmobile: true,
                                hide_under:800,
                                hide_onleave:false,
                                hide_delay:200,
                                direction:"vertical",
                                span:true,
                                position:"inner",
                                space:0,
                                h_align:"left",
                                v_align:"center",
                                h_offset:0,
                                v_offset:0
                            }
                        },
                        responsiveLevels:[1240,1024,768,480],
                        visibilityLevels:[1240,1024,768,480],
                        gridwidth:[1240,1024,768,480],
                        gridheight:[868,768,960,720],
                        lazyType:"none",
                        parallax: {
                            type:"3D",
                            origo:"slidercenter",
                            speed:1000,
                            levels:[2,4,6,8,10,12,14,16,45,50,47,48,49,50,0,50],
                            ddd_shadow:"off",
                            ddd_bgfreeze:"on",
                            ddd_overflow:"hidden",
                            ddd_layer_overflow:"visible",
                            ddd_z_correction:100
                        },
                        spinner:"off",
                        stopLoop:"on",
                        stopAfterLoops:0,
                        stopAtSlide:1,
                        shuffle:"off",
                        autoHeight:"off",
                        fullScreenAutoWidth:"off",
                        fullScreenAlignForce:"off",
                        fullScreenOffsetContainer: "",
                        fullScreenOffset: "60px",
                        disableProgressBar:"on",
                        hideThumbsOnMobile:"off",
                        hideSliderAtLimit:0,
                        hideCaptionAtLimit:0,
                        hideAllCaptionAtLilmit:0,
                        debugMode:false,
                        fallbacks: {
                            simplifyAll:"off",
                            nextSlideOnWindowFocus:"off",
                            disableFocusListener:false
                        }
                    });
                };
            })(jQueryTheme);
        }, ['theme']);
        
    </script>
</slider-revolution>

<script>
    theme.AssetsLoader.require('styles', 'plugin_revolution');
    theme.AssetsLoader.require('scripts', 'plugin_revolution_slider');
    theme.AssetsLoader.require('scripts', 'slider_revolution');
</script> {% endcomment %}
{% comment %} 
{% # schema %}
{
    "name": "Slider Revolution",
    "settings": [
        {
            "type": "header",
            "content": "Documentation",
            "info": "[Online Revolution Slider Documentation](https://www.themepunch.com/revsliderjquery-doc/slider-revolution-jquery-5-x-documentation/)"
        },
        {
            "type": "header",
            "content": "Settings",
            "info": "Change slider code in sections/revolution-slider"
        }
    ],
    "presets": [
        {
            "name": "Slider Revolution",
            "category": "5) Custom Slider Revolution"
        }
    ]
}
{% #  endschema %} {% endcomment %}