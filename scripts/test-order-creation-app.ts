/**
 * 予約データからShopify注文を作成するテストスクリプト（アプリケーションコード使用版）
 *
 * このスクリプトは、既存の予約データからShopify注文を作成し、
 * 予約データとShopify注文が正しく関連付けられているかをテストします。
 * 実際のアプリケーションコードを使用して認証を行います。
 *
 * 実行方法: npx tsx scripts/test-order-creation-app.ts [予約ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { format, addDays } from 'date-fns';
import { ja } from 'date-fns/locale';
import { authenticate } from '../app/shopify.server';
import { createOrderFromBooking } from '../app/utils/booking/order-creator';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * 予約情報を表示する関数
 */
async function displayBookingInfo(bookingId: string) {
  try {
    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        product: true
      }
    });

    if (!booking) {
      console.error(`予約ID ${bookingId} が見つかりません`);
      return;
    }

    // 予約情報を表示
    console.log('予約ID:', booking.id);
    console.log('予約番号:', booking.bookingId);
    console.log('予約タイプ:', booking.bookingType);
    console.log('開始日:', format(booking.startDate, 'yyyy/MM/dd', { locale: ja }));
    console.log('終了日:', format(booking.endDate, 'yyyy/MM/dd', { locale: ja }));
    console.log('顧客名:', booking.customerName);
    console.log('顧客メール:', booking.customerEmail);
    console.log('顧客ID:', booking.customerId);
    console.log('商品名:', booking.product.title);
    console.log('商品ID:', booking.product.id);
    console.log('Shopify商品ID:', booking.product.shopifyId);
    console.log('合計金額:', booking.totalAmount);
    console.log('注文ID:', booking.orderId || 'なし');
    console.log('注文番号:', booking.orderName || 'なし');
    console.log('作成日時:', format(booking.createdAt, 'yyyy/MM/dd HH:mm:ss', { locale: ja }));
    console.log('更新日時:', format(booking.updatedAt, 'yyyy/MM/dd HH:mm:ss', { locale: ja }));

    return booking;
  } catch (error) {
    console.error('予約情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 注文作成をテストする関数
 */
async function testOrderCreation(bookingId: string) {
  try {
    console.log(`予約ID ${bookingId} の注文作成テストを実行します...`);

    // 予約情報を表示
    console.log('\n----- 予約情報 -----');
    await displayBookingInfo(bookingId);

    // 注文を作成
    console.log('\n----- 注文作成実行 -----');
    try {
      console.log('注文作成処理を開始します...');
      console.log('- prisma:', !!prisma);
      console.log('- bookingId:', bookingId);
      
      // アプリケーションの実装を使用して注文を作成
      console.log('アプリケーションの実装を使用して注文を作成します...');
      console.log('app/utils/booking/order-creator.ts の createOrderFromBooking 関数を呼び出します');
      
      // リクエストオブジェクトを作成（ダミー）
      const headers = new Headers();
      headers.append('Content-Type', 'application/json');
      
      const request = new Request('https://example.com', {
        method: 'GET',
        headers: headers
      });
      
      // authenticate.adminを呼び出す
      const { admin } = await authenticate.admin(request);
      
      console.log('Shopify Admin APIの初期化が完了しました');
      console.log('createOrderFromBooking関数を呼び出します...');
      
      const result = await createOrderFromBooking(
        prisma,
        admin,
        bookingId,
        3 // リトライ回数
      );

      console.log('注文作成結果:', result);
      
      return result;
    } catch (orderError) {
      console.error('注文作成中にエラーが発生しました:', orderError);
      if (orderError instanceof Error) {
        console.error('エラーメッセージ:', orderError.message);
        console.error('スタックトレース:', orderError.stack);
      }
      throw orderError;
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    throw error;
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// メイン処理
async function main() {
  try {
    // コマンドライン引数から予約IDを取得
    const bookingId = process.argv[2];
    
    if (!bookingId) {
      console.error('予約IDを指定してください');
      console.error('使用方法: npx tsx scripts/test-order-creation-app.ts [予約ID]');
      process.exit(1);
    }
    
    // 注文作成テストを実行
    await testOrderCreation(bookingId);
    
    console.log('\nテストが完了しました');
    process.exit(0);
  } catch (error) {
    console.error('エラーが発生しました:', error);
    process.exit(1);
  }
}

// スクリプトを実行
main();
