/**
 * 在庫同期テストスクリプト
 *
 * このスクリプトは、ShopifyとPrismaデータベース間の在庫同期をテストします。
 * 実行方法: npx tsx scripts/test-sync-inventory.ts [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { InventorySyncService } from '../app/services/sync/inventory-sync.service';
import { InventoryCalendarService } from '../app/services/inventory-calendar.service';
import { addDays, format } from 'date-fns';
import { ja } from 'date-fns/locale';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト設定
const config = {
  // テストする商品のShopify ID
  productId: process.argv[2] || '8597856903456', // コマンドライン引数から取得、または既定値を使用

  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',

  // テスト用のロケーション
  locations: ['NY', 'PR']
};

/**
 * 商品情報を取得する関数
 */
async function getProductInfo(shopifyProductId: string) {
  try {
    // Prismaから商品情報を取得
    const product = await prisma.product.findFirst({
      where: { shopifyId: shopifyProductId }
    });

    if (!product) {
      console.error(`商品ID ${shopifyProductId} が見つかりません`);
      return null;
    }

    return product;
  } catch (error) {
    console.error('商品情報取得エラー:', error);
    return null;
  }
}

/**
 * 在庫情報を表示する関数
 */
async function displayInventoryInfo(productId: string) {
  try {
    // 商品情報を取得
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!product) {
      console.error(`商品ID ${productId} が見つかりません`);
      return;
    }

    console.log('\n===== 商品情報 =====');
    console.log(`商品ID: ${product.id}`);
    console.log(`ShopifyID: ${product.shopifyId}`);
    console.log(`商品名: ${product.title}`);
    console.log(`SKU: ${product.sku}`);
    console.log(`ステータス: ${product.status}`);
    console.log(`ロケーション: ${product.locationId || '未設定'}`);

    // 在庫情報を表示
    const metadata = product.metadata || {};
    const inventory = metadata.inventory || {};

    console.log('\n===== 在庫情報 =====');
    if (Object.keys(inventory).length === 0) {
      console.log('在庫情報がありません');
    } else {
      for (const [locationId, info] of Object.entries(inventory)) {
        console.log(`ロケーション ${locationId}:`);
        console.log(`  在庫数: ${(info as any).available}`);
        console.log(`  更新日時: ${new Date((info as any).updatedAt).toLocaleString()}`);
      }
    }

    // 在庫カレンダー情報を取得
    const today = new Date();
    const nextWeek = addDays(today, 7);

    const inventoryCalendar = await prisma.inventoryCalendar.findMany({
      where: {
        productId: product.id,
        date: {
          gte: today,
          lte: nextWeek
        }
      },
      orderBy: { date: 'asc' }
    });

    console.log('\n===== 在庫カレンダー（7日間） =====');
    if (inventoryCalendar.length === 0) {
      console.log('在庫カレンダー情報がありません');
    } else {
      for (const entry of inventoryCalendar) {
        const dateStr = format(new Date(entry.date), 'yyyy/MM/dd(E)', { locale: ja });
        const status = entry.isAvailable ? '○ 利用可能' : '× 利用不可';
        const reason = entry.unavailableReason ? `(${entry.unavailableReason})` : '';
        console.log(`${dateStr}: ${status} ${reason}`);
      }
    }
  } catch (error) {
    console.error('在庫情報表示エラー:', error);
  }
}

/**
 * 在庫同期をテストする関数
 */
async function testInventorySync(shopifyProductId: string) {
  try {
    console.log(`商品ID ${shopifyProductId} の在庫同期テストを実行します...`);

    // 商品情報を取得
    const product = await getProductInfo(shopifyProductId);

    if (!product) {
      return false;
    }

    // 同期前の在庫情報を表示
    console.log('\n----- 同期前の在庫情報 -----');
    await displayInventoryInfo(product.id);

    // 在庫カレンダーサービスを初期化
    const inventoryCalendarService = new InventoryCalendarService();

    // 在庫カレンダーを更新
    console.log('\n----- 在庫カレンダー更新を実行中... -----');
    await inventoryCalendarService.updateInventoryCalendar(product.id);

    // 更新結果を表示
    const syncResult = {
      success: true,
      message: '在庫カレンダーの更新が完了しました'
    };

    if (syncResult.success) {
      console.log('在庫同期が成功しました');
      console.log(`メッセージ: ${syncResult.message}`);
    } else {
      console.error('在庫同期が失敗しました');
      console.error(`エラー: ${syncResult.message}`);
      return false;
    }

    // 同期後の在庫情報を表示
    console.log('\n----- 同期後の在庫情報 -----');
    await displayInventoryInfo(product.id);

    return true;
  } catch (error) {
    console.error('在庫同期テストエラー:', error);
    return false;
  }
}

/**
 * 在庫カレンダー更新をテストする関数
 */
async function testInventoryCalendarUpdate(shopifyProductId: string) {
  try {
    console.log(`\n商品ID ${shopifyProductId} の在庫カレンダー更新テストを実行します...`);

    // 商品情報を取得
    const product = await getProductInfo(shopifyProductId);

    if (!product) {
      return false;
    }

    // 在庫カレンダーサービスを初期化
    const inventoryCalendarService = new InventoryCalendarService();

    // 在庫カレンダーを更新
    console.log('\n----- 在庫カレンダー更新を実行中... -----');
    await inventoryCalendarService.updateInventoryCalendar(product.id);
    console.log('在庫カレンダー更新が完了しました');

    // 更新後の在庫カレンダー情報を表示
    console.log('\n----- 更新後の在庫カレンダー情報 -----');
    await displayInventoryInfo(product.id);

    return true;
  } catch (error) {
    console.error('在庫カレンダー更新テストエラー:', error);
    return false;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('在庫同期テストを開始します...');

    // コマンドライン引数から商品IDを取得
    const shopifyProductId = config.productId;

    // 在庫同期をテスト
    const syncResult = await testInventorySync(shopifyProductId);

    // 在庫カレンダー更新をテスト
    const calendarResult = await testInventoryCalendarUpdate(shopifyProductId);

    // テスト結果のサマリー
    console.log('\n===== テスト結果サマリー =====');
    console.log(`在庫同期テスト: ${syncResult ? '成功' : '失敗'}`);
    console.log(`在庫カレンダー更新テスト: ${calendarResult ? '成功' : '失敗'}`);

    if (syncResult && calendarResult) {
      console.log('\n全てのテストが成功しました！');
    } else {
      console.error('\n一部のテストが失敗しました。');
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
