/**
 * 仮予約期限切れ通知スクリプト
 *
 * 期限切れ前の仮予約に通知を送信するスクリプト
 * 実行方法: npx tsx scripts/notify-provisional-expiration.ts [日数]
 */

import { ProvisionalExpirationNotificationService } from '../app/services/scheduled/provisional-expiration-notification.service';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

/**
 * メイン関数
 */
async function main() {
  try {
    // コマンドライン引数から日数を取得
    const daysBeforeExpiration = parseInt(process.argv[2] || '3', 10);
    
    console.log(`期限切れ${daysBeforeExpiration}日前の仮予約に通知を送信します...`);
    
    // 通知サービスを初期化
    const notificationService = new ProvisionalExpirationNotificationService();
    
    // 通知を送信
    const result = await notificationService.sendExpirationNotifications(daysBeforeExpiration);
    
    if (result.success) {
      console.log(`通知処理が完了しました: ${result.processedCount}件処理, ${result.errors.length}件エラー`);
      
      if (result.errors.length > 0) {
        console.log('\n===== エラー一覧 =====');
        result.errors.forEach((error, index) => {
          console.log(`${index + 1}. ${error}`);
        });
      }
    } else {
      console.error('通知処理中にエラーが発生しました');
      
      if (result.errors.length > 0) {
        console.log('\n===== エラー一覧 =====');
        result.errors.forEach((error, index) => {
          console.log(`${index + 1}. ${error}`);
        });
      }
      
      process.exit(1);
    }
    
    console.log('\n===== 処理結果 =====');
    console.log(`処理ステータス: ${result.success ? '成功' : '失敗'}`);
    console.log(`処理件数: ${result.processedCount}件`);
    console.log(`エラー件数: ${result.errors.length}件`);
    
    console.log('\n処理が完了しました。');
  } catch (error) {
    console.error('通知処理中にエラーが発生しました:', error);
    process.exit(1);
  }
}

// スクリプトを実行
main();
