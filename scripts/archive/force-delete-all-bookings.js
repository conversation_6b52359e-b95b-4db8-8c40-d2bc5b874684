// scripts/force-delete-all-bookings.js
// 全ての予約データを強制的に削除するスクリプト

import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function forceDeleteAllBookings() {
  try {
    console.log('データベース接続を確認中...');
    
    // データベース接続テスト
    await prisma.$connect();
    console.log('データベースに接続しました');
    
    // 予約データを取得して確認
    const bookingsBefore = await prisma.booking.findMany();
    console.log(`削除前の予約数: ${bookingsBefore.length}件`);
    
    if (bookingsBefore.length > 0) {
      console.log('予約IDリスト:');
      bookingsBefore.forEach(b => console.log(`- ${b.id} (${b.bookingId})`));
      
      // すべての予約を強制的に削除
      console.log('\n予約データを削除中...');
      
      // 各予約を個別に削除
      for (const booking of bookingsBefore) {
        try {
          await prisma.booking.delete({
            where: { id: booking.id }
          });
          console.log(`予約 ${booking.id} を削除しました`);
        } catch (err) {
          console.error(`予約 ${booking.id} の削除に失敗しました:`, err);
        }
      }
      
      // 削除後の確認
      const bookingsAfter = await prisma.booking.findMany();
      console.log(`\n削除後の予約数: ${bookingsAfter.length}件`);
      
      if (bookingsAfter.length > 0) {
        console.log('残っている予約:');
        bookingsAfter.forEach(b => console.log(`- ${b.id} (${b.bookingId})`));
        
        // 最後の手段として deleteMany を再度実行
        console.log('\n残りの予約を一括削除します...');
        const { count } = await prisma.booking.deleteMany({});
        console.log(`${count}件の予約を一括削除しました`);
      } else {
        console.log('すべての予約が正常に削除されました');
      }
    } else {
      console.log('削除対象の予約データがありません');
    }
  } catch (error) {
    console.error('予約削除エラー:', error);
  } finally {
    await prisma.$disconnect();
    console.log('データベース接続を切断しました');
  }
}

// スクリプトを実行
forceDeleteAllBookings();
