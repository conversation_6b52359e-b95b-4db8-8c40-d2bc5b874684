import { useState } from "react";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  BlockStack,
} from "@shopify/polaris";
import InvoiceSearchForm from "../components/InvoiceSearch/InvoiceSearchForm";
import InvoiceSearchResults from "../components/InvoiceSearch/InvoiceSearchResults";

// ダミーデータ
const MOCK_INVOICES = [
  {
    id: "1",
    invoiceNo: "202504171405670120002",
    status: "processing",
    requestNo: "0123670000",
    staffNo: "0123670000",
    assigneeName: "田中山雄",
    companyName: "EASE",
    department: "",
    requestName: "本当日商事",
    rentalDate: "2025-04-17",
    returnDate: "2025-04-20",
    paymentMethod: "cash",
    amount: 50000,
    balance: 0,
  },
  {
    id: "2",
    invoiceNo: "202504171245013275",
    status: "completed",
    requestNo: "0000050003",
    staffNo: "0000050003",
    assigneeName: "本当日商事",
    companyName: "EASE",
    department: "",
    requestName: "本当日商事",
    rentalDate: "2025-04-17",
    returnDate: "2025-04-20",
    paymentMethod: "credit",
    amount: 35000,
    balance: 0,
  },
  {
    id: "3",
    invoiceNo: "202504161830671930002",
    status: "processing",
    requestNo: "**********",
    staffNo: "**********",
    assigneeName: "川村知子",
    companyName: "",
    department: "",
    requestName: "",
    rentalDate: "2025-04-16",
    returnDate: "2025-04-19",
    paymentMethod: "bankTransfer",
    amount: 42000,
    balance: 42000,
  },
  {
    id: "4",
    invoiceNo: "202504161750372210009",
    status: "pending",
    requestNo: "**********",
    staffNo: "**********",
    assigneeName: "島津里子",
    companyName: "㈱バロックジャパンリミテッド",
    department: "プレス",
    requestName: "㈱バロックジャパンリミテッド",
    rentalDate: "2025-04-16",
    returnDate: "2025-04-19",
    paymentMethod: "bankTransfer",
    amount: 78000,
    balance: 78000,
  },
  {
    id: "5",
    invoiceNo: "202504161715052860001",
    status: "completed",
    requestNo: "**********",
    staffNo: "**********",
    assigneeName: "関川直子",
    companyName: "",
    department: "",
    requestName: "",
    rentalDate: "2025-04-16",
    returnDate: "2025-04-19",
    paymentMethod: "cash",
    amount: 25000,
    balance: 0,
  },
];

export const loader = async () => {
  // 実際のアプリケーションではデータベースからデータを取得する
  return json({
    invoices: MOCK_INVOICES,
  });
};

export default function InvoiceSearchPage() {
  const { invoices } = useLoaderData<typeof loader>();
  const [searchResults, setSearchResults] = useState(invoices);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  // 検索処理
  const handleSearch = (formData: any) => {
    setIsLoading(true);
    
    // 実際のアプリケーションではAPIリクエストを行う
    setTimeout(() => {
      // ダミーの検索処理（実際はサーバーサイドで行う）
      const filteredResults = invoices.filter(invoice => {
        // ステータスでフィルタリング
        if (formData.status && invoice.status !== formData.status) {
          return false;
        }
        
        // 伝票番号でフィルタリング
        if (formData.invoiceNo && !invoice.invoiceNo.includes(formData.invoiceNo)) {
          return false;
        }
        
        // 支払い方法でフィルタリング
        if (
          (!formData.paymentMethods.cash && invoice.paymentMethod === 'cash') ||
          (!formData.paymentMethods.credit && invoice.paymentMethod === 'credit') ||
          (!formData.paymentMethods.bankTransfer && invoice.paymentMethod === 'bankTransfer')
        ) {
          return false;
        }
        
        // 残高でフィルタリング
        if (
          (!formData.balanceFilter.hasBalance && invoice.balance > 0) ||
          (!formData.balanceFilter.noBalance && invoice.balance === 0)
        ) {
          return false;
        }
        
        // 貸出日でフィルタリング
        if (formData.rentalStartDate) {
          const startDate = new Date(formData.rentalStartDate);
          const invoiceDate = new Date(invoice.rentalDate);
          if (invoiceDate < startDate) {
            return false;
          }
        }
        
        if (formData.rentalEndDate) {
          const endDate = new Date(formData.rentalEndDate);
          const invoiceDate = new Date(invoice.rentalDate);
          if (invoiceDate > endDate) {
            return false;
          }
        }
        
        // 会社名でフィルタリング
        if (formData.companyName && !invoice.companyName.includes(formData.companyName)) {
          return false;
        }
        
        // 担当者名でフィルタリング
        if (formData.assignedName && !invoice.assigneeName.includes(formData.assignedName)) {
          return false;
        }
        
        return true;
      });
      
      setSearchResults(filteredResults);
      setIsLoading(false);
      setCurrentPage(1);
    }, 500);
  };

  // フォームクリア
  const handleClear = () => {
    setSearchResults(invoices);
  };

  // 詳細表示
  const handleViewDetails = (invoiceId: string) => {
    console.log(`View details for invoice ${invoiceId}`);
    // 実際のアプリケーションでは詳細ページに遷移する
  };

  // CSV出力
  const handleExportCSV = () => {
    console.log("Export to CSV");
    // 実際のアプリケーションではCSVダウンロード処理を行う
  };

  // コピーして新規作成
  const handleCopyNew = () => {
    console.log("Copy and create new");
    // 実際のアプリケーションでは新規作成ページに遷移する
  };

  // ページ変更
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // 実際のアプリケーションではページに応じたデータを取得する
  };

  return (
    <Page title="伝票検索">
      <Layout>
        <Layout.Section>
          <BlockStack gap="500">
            <InvoiceSearchForm 
              onSearch={handleSearch} 
              onClear={handleClear} 
            />
            
            <InvoiceSearchResults 
              results={searchResults}
              isLoading={isLoading}
              totalCount={searchResults.length}
              currentPage={currentPage}
              onPageChange={handlePageChange}
              onViewDetails={handleViewDetails}
              onExportCSV={handleExportCSV}
              onCopyNew={handleCopyNew}
            />
          </BlockStack>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
