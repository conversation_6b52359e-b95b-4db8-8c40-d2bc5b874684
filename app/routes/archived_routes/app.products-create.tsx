import { json } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import { authenticate } from "../shopify.server";
import {
  Page,
  Layout,
  Card,
  Button,
  Text,
  BlockStack,
} from "@shopify/polaris";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";

// loaderを追加
export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  return json({
    status: "idle"
  });
};

const PRODUCT_VARIANT_CREATE = `#graphql
  mutation productVariantsBulkCreate($productId: ID!, $variants: [ProductVariantsBulkInput!]!) {
    productVariantsBulkCreate(productId: $productId, variants: $variants) {
      productVariants {
        id
        title
        price
        inventoryItem {
          id
          tracked
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

const CREATE_METAFIELD_DEFINITIONS = `#graphql
  mutation CreateMetafieldDefinition($definition: MetafieldDefinitionInput!) {
    metafieldDefinitionCreate(definition: $definition) {
      metafieldDefinition {
        id
        name
        key
        namespace
        type {
          name
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

const PRODUCT_METAFIELD_CREATE = `#graphql
  mutation productMetafieldsSet($metafields: [MetafieldsSetInput!]!, $productId: ID!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        key
        namespace
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin } = await authenticate.admin(request);

  // メタフィールド定義を作成
  const metafieldDefinitions = [
    {
      name: "状態備考",
      namespace: "custom",
      key: "condition_note",
      type: "multi_line_text_field",
      description: "商品の状態に関する備考"
    },
    {
      name: "サイズ情報",
      namespace: "custom",
      key: "size_info",
      type: "multi_line_text_field",
      description: "商品のサイズ情報"
    },
    {
      name: "次回メンテナンス予定日",
      namespace: "custom",
      key: "next_maintenance_date",
      type: "date",
      description: "次回のメンテナンス予定日"
    },
    {
      name: "最終メンテナンス日",
      namespace: "custom",
      key: "last_maintenance_date",
      type: "date",
      description: "最後にメンテナンスを実施した日"
    },
    {
      name: "メンテナンス状態",
      namespace: "custom",
      key: "maintenance_status",
      type: "single_line_text_field",
      description: "現在のメンテナンス状態"
    },
    {
      name: "デポジット金額",
      namespace: "custom",
      key: "deposit_amount",
      type: "number_integer",
      description: "必要なデポジット金額"
    },
    {
      name: "レンタル期間",
      namespace: "custom",
      key: "rental_period",
      type: "single_line_text_field",
      description: "レンタル可能期間"
    },
    {
      name: "レンタル種別",
      namespace: "custom",
      key: "rental_type",
      type: "single_line_text_field",
      description: "レンタルの種類"
    }
  ];

  // メタフィールド定義を作成
  for (const definition of metafieldDefinitions) {
    try {
      const response = await admin.graphql(
        CREATE_METAFIELD_DEFINITIONS,
        {
          variables: {
            definition: {
              name: definition.name,
              namespace: definition.namespace,
              key: definition.key,
              type: definition.type,
              description: definition.description,
              ownerType: "PRODUCT"
            }
          }
        }
      );
      console.log(`Created metafield definition: ${definition.name}`, response);
    } catch (error) {
      console.log(`Error creating metafield definition: ${definition.name}`, error);
      // 既に存在する場合はスキップ
    }
  }

  // テスト商品データ
  const testProducts = [
    {
      title: "テストソファ グレー 2シーター",
      description: "商品コード: TEST001\n在庫数: 1\nサイズ: W150 × D80 × H85",
      vendor: "TEST_VENDOR",
      price: "15000",
      tags: ["ソファ", "グレー", "TEST_VENDOR"],
      metafields: [
        {
          namespace: "custom",
          key: "condition_note",
          value: "全体的に使用感あり。右肘掛けに小さな傷あり。",
          type: "multi_line_text_field"
        },
        {
          namespace: "custom",
          key: "size_info",
          value: "W150cm × D80cm × H85cm\n座面高: 42cm\n座面幅: 130cm",
          type: "multi_line_text_field"
        },
        {
          namespace: "custom",
          key: "next_maintenance_date",
          value: "2024-06-30",
          type: "date"
        },
        {
          namespace: "custom",
          key: "last_maintenance_date",
          value: "2024-03-15",
          type: "date"
        },
        {
          namespace: "custom",
          key: "maintenance_status",
          value: "メンテナンス済み",
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "deposit_amount",
          value: "30000",
          type: "number_integer"
        },
        {
          namespace: "custom",
          key: "rental_period",
          value: "1週間～3ヶ月",
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "rental_type",
          value: "短期レンタル",
          type: "single_line_text_field"
        }
      ]
    }
  ];

  for (const product of testProducts) {
    // 商品を作成
    const response = await admin.graphql(
      `#graphql
        mutation productCreate($input: ProductInput!) {
          productCreate(input: $input) {
            product {
              id
              title
            }
            userErrors {
              field
              message
            }
          }
        }`,
      {
        variables: {
          input: {
            title: product.title,
            descriptionHtml: product.description,
            vendor: product.vendor,
            tags: product.tags,
            status: "ACTIVE"
          },
        },
      }
    );

    const responseJson = await response.json();
    const productId = responseJson.data?.productCreate?.product?.id;

    if (productId) {
      // メタフィールドを設定
      const metafieldResponse = await admin.graphql(
        PRODUCT_METAFIELD_CREATE,
        {
          variables: {
            productId: productId,
            metafields: product.metafields.map(metafield => ({
              ...metafield,
              ownerId: productId
            }))
          }
        }
      );
      console.log(`Added metafields for product: ${product.title}`, await metafieldResponse.json());

      // バリアントを作成
      const variantResponse = await admin.graphql(
        PRODUCT_VARIANT_CREATE,
        {
          variables: {
            productId: productId,
            variants: [
              {
                price: product.price,
                options: [product.title],
                inventoryQuantities: [
                  {
                    availableQuantity: 1,
                    locationId: "gid://shopify/Location/76366348584"
                  }
                ]
              }
            ]
          },
        }
      );

      const variantJson = await variantResponse.json();
      console.log(`Created variant for product: ${product.title}`, variantJson);
    }

    console.log(`Created product: ${product.title}`, responseJson);
  }

  return json({ status: "success", message: "テスト商品を作成しました" });
};

export default function ProductsCreate() {
  const { status } = useLoaderData<typeof loader>();
  const submit = useSubmit();

  const handleCreate = () => {
    submit({}, { method: "post" });
  };

  return (
    <Page title="テスト商品作成">
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="p">
                CSVデータを元にテスト用の商品を作成します。
              </Text>
              <Button onClick={handleCreate} variant="primary">
                商品を作成
              </Button>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}