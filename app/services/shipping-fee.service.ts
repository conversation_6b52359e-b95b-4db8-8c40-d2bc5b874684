import { prisma } from '../db.server';

export interface ShippingFeeCalculationRequest {
  category: string; // EASE, BASIC, TRUCK, WIDE, PORTER
  type?: string; // 配送種類
  areaType: string; // 23区内, 23区外, etc
  areaDetail?: string; // 具体的なエリア
  destinationCode?: string; // 配送先コード
}

export interface ShippingFeeResult {
  basePrice: number;
  finalPrice: number;
  rule?: {
    id: string;
    category: string;
    type: string;
    areaType: string;
    areaDetail?: string;
  };
  destination?: {
    code: string;
    name: string;
    address?: string;
  };
  shopifyProductId?: string;
}

export class ShippingFeeService {
  private shop: string;

  constructor(shop: string) {
    this.shop = shop;
  }

  /**
   * 配送料金を計算
   */
  async calculateShippingFee(request: ShippingFeeCalculationRequest): Promise<ShippingFeeResult | null> {
    try {
      // 1. 配送先情報を取得（指定されている場合）
      let destination = null;
      if (request.destinationCode) {
        destination = await this.getDeliveryDestination(request.destinationCode);
      }

      // 2. 配送料金ルールを検索
      const rule = await this.findShippingFeeRule(request);
      
      if (!rule) {
        return null;
      }

      // 3. 料金計算
      const basePrice = Number(rule.basePrice);
      let finalPrice = basePrice;

      // 特別料金計算ロジック（必要に応じて拡張）
      finalPrice = await this.applySpecialPricing(finalPrice, request, destination);

      return {
        basePrice,
        finalPrice,
        rule: {
          id: rule.id,
          category: rule.category,
          type: rule.type,
          areaType: rule.areaType,
          areaDetail: rule.areaDetail || undefined
        },
        destination: destination ? {
          code: destination.destinationCode,
          name: destination.name,
          address: destination.address || undefined
        } : undefined,
        shopifyProductId: rule.shopifyProductId || undefined
      };

    } catch (error) {
      console.error('配送料金計算エラー:', error);
      return null;
    }
  }

  /**
   * 配送料金ルールを検索
   */
  private async findShippingFeeRule(request: ShippingFeeCalculationRequest) {
    // 完全一致で検索
    let rule = await prisma.shippingFeeRule.findFirst({
      where: {
        shop: this.shop,
        category: request.category,
        type: request.type || '',
        areaType: request.areaType,
        areaDetail: request.areaDetail || null,
        isActive: true
      }
    });

    // 完全一致しない場合、areaDetailなしで検索
    if (!rule && request.areaDetail) {
      rule = await prisma.shippingFeeRule.findFirst({
        where: {
          shop: this.shop,
          category: request.category,
          type: request.type || '',
          areaType: request.areaType,
          areaDetail: null,
          isActive: true
        }
      });
    }

    // それでも見つからない場合、typeなしで検索
    if (!rule) {
      rule = await prisma.shippingFeeRule.findFirst({
        where: {
          shop: this.shop,
          category: request.category,
          areaType: request.areaType,
          isActive: true
        }
      });
    }

    return rule;
  }

  /**
   * 配送先情報を取得
   */
  private async getDeliveryDestination(destinationCode: string) {
    return await prisma.deliveryDestination.findFirst({
      where: {
        shop: this.shop,
        destinationCode,
        isActive: true
      }
    });
  }

  /**
   * 特別料金計算ロジック
   */
  private async applySpecialPricing(
    basePrice: number, 
    request: ShippingFeeCalculationRequest, 
    destination: any
  ): Promise<number> {
    let finalPrice = basePrice;

    // 距離計算が必要な場合（23区外など）
    if (request.areaType === '23区外' && basePrice === 0) {
      // TODO: 距離計算ロジックを実装
      finalPrice = await this.calculateDistanceBasedPrice(request, destination);
    }

    // 時間外料金の適用
    if (request.type === '時間外料金') {
      // TODO: 時間外料金計算ロジックを実装
      finalPrice = this.calculateOvertimeFee(basePrice);
    }

    return finalPrice;
  }

  /**
   * 距離ベースの料金計算
   */
  private async calculateDistanceBasedPrice(
    request: ShippingFeeCalculationRequest, 
    destination: any
  ): Promise<number> {
    // TODO: 実際の距離計算APIを使用
    // 仮の実装
    return 10000; // 基本料金
  }

  /**
   * 時間外料金計算
   */
  private calculateOvertimeFee(basePrice: number): number {
    // 時間外料金は7000円固定
    return 7000;
  }

  /**
   * 配送先一覧を取得
   */
  async getDeliveryDestinations(searchQuery?: string, limit: number = 50) {
    const where: any = {
      shop: this.shop,
      isActive: true
    };

    if (searchQuery) {
      where.OR = [
        { name: { contains: searchQuery, mode: 'insensitive' } },
        { nameKana: { contains: searchQuery, mode: 'insensitive' } },
        { destinationCode: { contains: searchQuery } },
        { address: { contains: searchQuery, mode: 'insensitive' } }
      ];
    }

    return await prisma.deliveryDestination.findMany({
      where,
      orderBy: [
        { destinationCode: 'asc' }
      ],
      take: limit
    });
  }

  /**
   * 配送料金ルール一覧を取得
   */
  async getShippingFeeRules(category?: string) {
    const where: any = {
      shop: this.shop,
      isActive: true
    };

    if (category) {
      where.category = category;
    }

    return await prisma.shippingFeeRule.findMany({
      where,
      orderBy: [
        { category: 'asc' },
        { type: 'asc' },
        { areaType: 'asc' }
      ]
    });
  }

  /**
   * 配送カテゴリ一覧を取得
   */
  async getShippingCategories() {
    const rules = await prisma.shippingFeeRule.findMany({
      where: {
        shop: this.shop,
        isActive: true
      },
      select: {
        category: true
      },
      distinct: ['category']
    });

    return rules.map(rule => rule.category);
  }

  /**
   * エリアタイプ一覧を取得
   */
  async getAreaTypes(category?: string) {
    const where: any = {
      shop: this.shop,
      isActive: true
    };

    if (category) {
      where.category = category;
    }

    const rules = await prisma.shippingFeeRule.findMany({
      where,
      select: {
        areaType: true
      },
      distinct: ['areaType']
    });

    return rules.map(rule => rule.areaType);
  }
}
