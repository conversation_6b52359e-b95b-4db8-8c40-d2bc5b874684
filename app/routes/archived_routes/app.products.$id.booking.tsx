import React, { useState, useEffect } from 'react';
import type { ActionFunctionArgs, LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { useLoaderData, useSubmit } from '@remix-run/react';
import {
  Page,
  Layout,
  Card,
  Text,
  BlockStack,
  Banner,
  Box,
  Grid,
  Button,
  InlineStack,
} from '@shopify/polaris';
import { TitleBar } from '@shopify/app-bridge-react';
import { SimpleCalendar } from '../components/SimpleCalendar';
import { authenticate } from '../shopify.server';
import { prisma } from '../db.server';

// 商品データの型
interface ProductData {
  id: string;
  title: string;
  shopifyId: string;
  status: string;
  bookingsData?: {
    bookings?: {
      id: string;
      startDate: string;
      endDate: string;
      status: string;
    }[];
  };
  bookings?: {
    id: string;
    startDate: Date;
    endDate: Date;
    status: string;
  }[];
}

// 既存の予約データの型
interface BookedDate {
  startDate: string;
  endDate: string;
}

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { admin } = await authenticate.admin(request);
  
  // URLパラメータから商品IDを取得
  const productId = params.id;
  
  if (!productId) {
    return json({ 
      product: null,
      errorMessage: '商品IDが指定されていません'
    });
  }
  
  try {
    // Prismaから商品と予約データを取得
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: {
        id: true,
        title: true,
        shopifyId: true,
        status: true,
        bookingsData: true,
        bookings: {
          where: {
            status: { in: ['reserved', 'completed'] }
          },
          select: {
            id: true,
            startDate: true,
            endDate: true,
            status: true
          }
        }
      }
    });
    
    if (!product) {
      return json({ 
        product: null,
        errorMessage: '商品が見つかりませんでした'
      });
    }
    
    // 商品のShopifyデータを取得（オプション）
    const response = await admin.graphql(
      `#graphql
        query getProduct($id: ID!) {
          product(id: $id) {
            id
            title
            handle
            images(first: 1) {
              edges {
                node {
                  url
                }
              }
            }
          }
        }`,
      {
        variables: {
          id: `gid://shopify/Product/${product.shopifyId}`,
        },
      }
    );
    
    const responseJson = await response.json();
    const shopifyProduct = responseJson.data.product;
    
    return json({ 
      product,
      shopifyProduct,
      errorMessage: null
    });
  } catch (error) {
    console.error('商品データの取得エラー:', error);
    return json({ 
      product: null,
      errorMessage: 'データの取得中にエラーが発生しました'
    });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin } = await authenticate.admin(request);
  const formData = await request.formData();
  
  const productId = formData.get('productId') as string;
  const startDate = formData.get('startDate') as string;
  const endDate = formData.get('endDate') as string;
  
  if (!productId || !startDate || !endDate) {
    return json({
      success: false,
      errorMessage: '必要なデータが不足しています'
    });
  }
  
  try {
    // 予約データを仮作成
    const newBooking = await prisma.booking.create({
      data: {
        bookingId: `BOOK-${Date.now()}`,
        status: 'draft',
        productId,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        priority: 1
      }
    });
    
    return json({
      success: true,
      booking: newBooking,
      errorMessage: null
    });
  } catch (error) {
    console.error('予約作成エラー:', error);
    return json({
      success: false,
      errorMessage: '予約の作成中にエラーが発生しました'
    });
  }
};

export default function ProductBookingPage() {
  const { product, shopifyProduct, errorMessage } = useLoaderData<typeof loader>();
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [bookedDates, setBookedDates] = useState<BookedDate[]>([]);
  const submit = useSubmit();
  
  useEffect(() => {
    if (product?.bookings && product.bookings.length > 0) {
      // 既存の予約から日付の配列を作成
      const bookedRanges = product.bookings.map(booking => ({
        startDate: booking.startDate.toString(),
        endDate: booking.endDate.toString()
      }));
      setBookedDates(bookedRanges);
    }
  }, [product]);
  
  const handleRangeSelect = (start: Date | null, end: Date | null) => {
    setStartDate(start);
    setEndDate(end);
  };
  
  const handleSubmitBooking = () => {
    if (!product || !startDate || !endDate) return;
    
    const formData = new FormData();
    formData.append('productId', product.id);
    formData.append('startDate', startDate.toISOString());
    formData.append('endDate', endDate.toISOString());
    
    submit(formData, { method: 'post' });
  };
  
  if (errorMessage) {
    return (
      <Page>
        <TitleBar title="商品予約" />
        <Banner status="critical">
          <p>{errorMessage}</p>
        </Banner>
      </Page>
    );
  }
  
  if (!product) {
    return (
      <Page>
        <TitleBar title="商品予約" />
        <Banner status="warning">
          <p>商品データを読み込んでいます...</p>
        </Banner>
      </Page>
    );
  }
  
  return (
    <Page
      title={product.title}
      backAction={{ content: '商品一覧に戻る', url: '/app/products' }}
    >
      <TitleBar title={`商品予約: ${product.title}`} />
      
      <Layout>
        <Layout.Section>
          <BlockStack gap="400">
            <Card>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">商品情報</Text>
                <Grid>
                  <Grid.Cell columnSpan={{ xs: 6, md: 4 }}>
                    {shopifyProduct?.images?.edges[0]?.node?.url && (
                      <Box paddingBlock="400">
                        <img 
                          src={shopifyProduct.images.edges[0].node.url} 
                          alt={product.title}
                          style={{ maxWidth: '100%', maxHeight: '300px', objectFit: 'contain' }}
                        />
                      </Box>
                    )}
                  </Grid.Cell>
                  <Grid.Cell columnSpan={{ xs: 6, md: 8 }}>
                    <BlockStack gap="200">
                      <Text variant="headingLg" as="h1">{product.title}</Text>
                      <Text variant="bodyMd" as="p">
                        ステータス: {product.status === 'available' ? '予約可能' : '予約不可'}
                      </Text>
                      {product.status !== 'available' && (
                        <Banner status="warning">
                          <p>この商品は現在予約できません</p>
                        </Banner>
                      )}
                    </BlockStack>
                  </Grid.Cell>
                </Grid>
              </BlockStack>
            </Card>
            
            {product.status === 'available' && (
              <SimpleCalendar
                bookedDates={bookedDates}
                minDays={1}
                maxDays={30}
                onRangeSelect={handleRangeSelect}
              />
            )}
            
            {product.status === 'available' && startDate && endDate && (
              <Card>
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2">予約内容の確認</Text>
                  <BlockStack gap="200">
                    <Text variant="bodyMd" as="p">
                      商品: {product.title}
                    </Text>
                    <Text variant="bodyMd" as="p">
                      レンタル期間: {startDate.toLocaleDateString()} 〜 {endDate.toLocaleDateString()}
                    </Text>
                    <Text variant="bodyMd" as="p">
                      レンタル日数: {Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1}日間
                    </Text>
                  </BlockStack>
                  <InlineStack gap="200">
                    <Button onClick={() => {
                      setStartDate(null);
                      setEndDate(null);
                    }}>
                      キャンセル
                    </Button>
                    <Button primary onClick={handleSubmitBooking}>
                      予約を確定する
                    </Button>
                  </InlineStack>
                </BlockStack>
              </Card>
            )}
          </BlockStack>
        </Layout.Section>
        
        <Layout.Section variant="oneThird">
          <Card>
            <BlockStack gap="400">
              <Text variant="headingMd" as="h2">予約状況</Text>
              {bookedDates.length === 0 ? (
                <Text variant="bodyMd" as="p">現在予約はありません</Text>
              ) : (
                <BlockStack gap="200">
                  <Text variant="bodyMd" as="p">予約済み期間:</Text>
                  {bookedDates.map((booking, index) => (
                    <Box
                      key={index}
                      paddingBlock="300"
                      paddingInline="300"
                      borderWidth="025"
                      borderRadius="200"
                      borderColor="border"
                    >
                      <Text variant="bodyMd" as="p">
                        {new Date(booking.startDate).toLocaleDateString()} 〜 {new Date(booking.endDate).toLocaleDateString()}
                      </Text>
                    </Box>
                  ))}
                </BlockStack>
              )}
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}