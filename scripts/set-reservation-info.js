import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品検索のクエリ
const GET_PRODUCT_BY_TITLE = gql`
  query getProductByTitle($query: String!) {
    products(first: 1, query: $query) {
      edges {
        node {
          id
          title
          variants(first: 50) {
            edges {
              node {
                id
                sku
                title
              }
            }
          }
        }
      }
    }
  }
`;

// メタフィールドの設定
async function setJsonMetafield(productId, namespace, key, jsonValue) {
  const mutation = gql`
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    metafields: [
      {
        ownerId: productId,
        namespace,
        key,
        value: JSON.stringify(jsonValue),
        type: 'json',
      }
    ]
  };

  try {
    const result = await graphQLClient.request(mutation, variables);
    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', result.metafieldsSet.userErrors);
      return false;
    }
    return true;
  } catch (error) {
    console.error(`メタフィールド ${namespace}.${key} 設定エラー:`, error);
    return false;
  }
}

// タイトルで商品を検索（完全一致）
async function getProductByTitle(title) {
  try {
    const result = await graphQLClient.request(GET_PRODUCT_BY_TITLE, {
      query: `title:"${title}"`,
    });

    if (result.products.edges.length === 0) {
      console.error(`タイトル "${title}" の商品が見つかりませんでした`);
      return null;
    }

    // 完全一致を確認
    const matchedProduct = result.products.edges.find(edge => edge.node.title === title);
    if (!matchedProduct) {
      console.error(`タイトル "${title}" の商品が完全一致しませんでした`);
      return null;
    }

    return matchedProduct.node;
  } catch (error) {
    console.error('商品検索エラー:', error);
    return null;
  }
}

// 現在の日付から指定した日数後の日付を取得
function getDateAfterDays(days) {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return date.toISOString().split('T')[0];
}

// テスト用の予約情報データ
const reservationTestData = [
  {
    title: "カリモクソファ モケットグリーン 1シーター",
    reservations: [
      {
        // 1台目の予約情報
        sku: "10101031-001",
        reservations: [
          {
            id: "reservation-001",
            startDate: getDateAfterDays(5),
            endDate: getDateAfterDays(8),
            status: "confirmed",
            customerName: "山田太郎",
            customerEmail: "<EMAIL>",
            orderId: "gid://shopify/Order/1000",
            orderLineItemId: "gid://shopify/LineItem/1001",
            notes: "特になし"
          },
          {
            id: "reservation-002",
            startDate: getDateAfterDays(15),
            endDate: getDateAfterDays(20),
            status: "pending",
            customerName: "佐藤花子",
            customerEmail: "<EMAIL>",
            orderId: "gid://shopify/Order/1002",
            orderLineItemId: "gid://shopify/LineItem/1003",
            notes: "配送希望"
          }
        ]
      },
      {
        // 2台目の予約情報
        sku: "10101031-002",
        reservations: [
          {
            id: "reservation-003",
            startDate: getDateAfterDays(10),
            endDate: getDateAfterDays(12),
            status: "confirmed",
            customerName: "鈴木一郎",
            customerEmail: "<EMAIL>",
            orderId: "gid://shopify/Order/1004",
            orderLineItemId: "gid://shopify/LineItem/1005",
            notes: "特になし"
          }
        ]
      }
    ]
  },
  {
    title: "プレーンソファ 1シーター",
    reservations: [
      {
        // 1台目の予約情報
        sku: "10101064-001",
        reservations: [
          {
            id: "reservation-004",
            startDate: getDateAfterDays(3),
            endDate: getDateAfterDays(7),
            status: "confirmed",
            customerName: "田中次郎",
            customerEmail: "<EMAIL>",
            orderId: "gid://shopify/Order/1006",
            orderLineItemId: "gid://shopify/LineItem/1007",
            notes: "特になし"
          }
        ]
      },
      {
        // 2台目の予約情報
        sku: "10101064-002",
        reservations: []
      },
      {
        // 3台目の予約情報
        sku: "10101064-003",
        reservations: [
          {
            id: "reservation-005",
            startDate: getDateAfterDays(20),
            endDate: getDateAfterDays(25),
            status: "confirmed",
            customerName: "高橋三郎",
            customerEmail: "<EMAIL>",
            orderId: "gid://shopify/Order/1008",
            orderLineItemId: "gid://shopify/LineItem/1009",
            notes: "特になし"
          },
          {
            id: "reservation-006",
            startDate: getDateAfterDays(30),
            endDate: getDateAfterDays(35),
            status: "pending",
            customerName: "伊藤四郎",
            customerEmail: "<EMAIL>",
            orderId: "gid://shopify/Order/1010",
            orderLineItemId: "gid://shopify/LineItem/1011",
            notes: "配送希望"
          }
        ]
      }
    ]
  }
];

// 在庫情報の更新
async function updateInventoryInfo(product, reservations) {
  // 在庫情報を作成
  const inventoryInfo = {};
  
  // 各SKUごとに在庫情報を設定
  for (const item of reservations) {
    const sku = item.sku;
    const reserved = item.reservations.length;
    
    inventoryInfo[sku] = {
      available: reserved > 0 ? 0 : 1,
      onHand: 1,
      reserved: reserved,
      physicalCount: 1,
      lastUpdated: new Date().toISOString()
    };
  }
  
  // 在庫情報をメタフィールドに設定
  console.log('在庫情報を設定中...');
  const inventoryResult = await setJsonMetafield(
    product.id,
    'rental',
    'inventory_info',
    inventoryInfo
  );
  
  if (inventoryResult) {
    console.log('在庫情報を設定しました');
  } else {
    console.error('在庫情報の設定に失敗しました');
  }
}

// メイン処理
async function setReservationInfo() {
  console.log('予約情報を設定しています...');
  
  for (const data of reservationTestData) {
    console.log(`\n処理中: ${data.title}`);
    
    // 商品を検索
    const product = await getProductByTitle(data.title);
    if (!product) {
      console.error(`${data.title} の商品が見つかりませんでした。スキップします。`);
      continue;
    }
    
    console.log(`商品を見つけました: ${product.title} (${product.id})`);
    
    // 予約情報をメタフィールドに設定
    console.log('予約情報を設定中...');
    const reservationResult = await setJsonMetafield(
      product.id,
      'rental',
      'reservation_info',
      data.reservations
    );
    
    if (reservationResult) {
      console.log('予約情報を設定しました');
    } else {
      console.error('予約情報の設定に失敗しました');
    }
    
    // 在庫情報を更新
    await updateInventoryInfo(product, data.reservations);
  }
  
  console.log('\n=== 処理完了 ===');
}

// 実行
setReservationInfo();
