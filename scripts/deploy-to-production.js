/**
 * 本番環境デプロイスクリプト
 * 
 * このスクリプトは以下の機能を実行します：
 * 1. 本番環境用の設定ファイルを準備
 * 2. アプリをビルド
 * 3. Renderへのデプロイを実行
 * 4. テーマアプリ拡張機能をデプロイ
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 設定
const config = {
  // 本番環境のURL
  productionUrl: process.env.PRODUCTION_URL || 'https://your-production-app-url.com',
  // Renderのサービス名
  renderServiceName: process.env.RENDER_SERVICE_NAME || 'rental-management-production',
  // デプロイ前に実行するテスト
  testsToRun: [
    'scripts/test-holiday-rental.js',
    'scripts/test-rental-duration.js'
  ]
};

// ユーティリティ関数
function executeCommand(command, options = {}) {
  console.log(`実行: ${command}`);
  try {
    const output = execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options
    });
    return output ? output.toString() : '';
  } catch (error) {
    if (options.ignoreError) {
      console.warn(`警告: コマンド実行中にエラーが発生しましたが無視します: ${error.message}`);
      return '';
    }
    console.error(`エラー: コマンド実行中にエラーが発生しました: ${error.message}`);
    process.exit(1);
  }
}

// 1. テストの実行
function runTests() {
  console.log('\n===== 1. テストの実行 =====');
  
  for (const testScript of config.testsToRun) {
    console.log(`\n----- ${testScript} の実行 -----`);
    executeCommand(`node ${testScript}`);
  }
}

// 2. 本番環境用の設定ファイルを準備
function prepareProductionConfig() {
  console.log('\n===== 2. 本番環境用の設定ファイルを準備 =====');
  
  // .env.productionファイルが存在するか確認
  const envProductionPath = path.join(process.cwd(), '.env.production');
  if (!fs.existsSync(envProductionPath)) {
    console.error('エラー: .env.productionファイルが見つかりません。');
    console.error('本番環境用の環境変数を設定した.env.productionファイルを作成してください。');
    process.exit(1);
  }
  
  // .env.productionの内容を確認
  const envContent = fs.readFileSync(envProductionPath, 'utf8');
  const requiredVars = [
    'SHOPIFY_API_KEY',
    'SHOPIFY_API_SECRET',
    'DATABASE_URL',
    'SESSION_SECRET'
  ];
  
  const missingVars = [];
  for (const varName of requiredVars) {
    if (!envContent.includes(`${varName}=`) || envContent.includes(`${varName}=\n`)) {
      missingVars.push(varName);
    }
  }
  
  if (missingVars.length > 0) {
    console.error(`エラー: .env.productionファイルに以下の必須環境変数が設定されていません: ${missingVars.join(', ')}`);
    process.exit(1);
  }
  
  // .env.productionを.envにコピー
  fs.copyFileSync(envProductionPath, path.join(process.cwd(), '.env'));
  console.log('✅ .env.productionを.envにコピーしました。');
  
  // shopify.app.tomlの更新
  const shopifyAppTomlPath = path.join(process.cwd(), 'shopify.app.toml');
  if (fs.existsSync(shopifyAppTomlPath)) {
    let tomlContent = fs.readFileSync(shopifyAppTomlPath, 'utf8');
    
    // application_urlを更新
    tomlContent = tomlContent.replace(
      /application_url\s*=\s*".*?"/,
      `application_url = "${config.productionUrl}"`
    );
    
    // redirect_urlsを更新
    tomlContent = tomlContent.replace(
      /redirect_urls\s*=\s*\[".*?"\]/,
      `redirect_urls = ["${config.productionUrl}/auth/callback"]`
    );
    
    fs.writeFileSync(shopifyAppTomlPath, tomlContent);
    console.log('✅ shopify.app.tomlを更新しました。');
  }
}

// 3. アプリをビルド
function buildApp() {
  console.log('\n===== 3. アプリをビルド =====');
  
  // 依存関係のインストール
  console.log('\n----- 依存関係のインストール -----');
  executeCommand('npm install');
  
  // Prismaクライアントの生成
  console.log('\n----- Prismaクライアントの生成 -----');
  executeCommand('npx prisma generate');
  
  // アプリのビルド
  console.log('\n----- アプリのビルド -----');
  executeCommand('npm run build');
}

// 4. Renderへのデプロイを実行
function deployToRender() {
  console.log('\n===== 4. Renderへのデプロイを実行 =====');
  
  // Renderへのデプロイ（実際のデプロイコマンドはRenderの設定によって異なります）
  console.log('\n----- Renderへのデプロイ -----');
  console.log('Renderへのデプロイは、Renderダッシュボードから手動で行うか、');
  console.log('GitHubリポジトリと連携して自動デプロイを設定することをお勧めします。');
  
  // デプロイ手順の表示
  console.log('\nRenderへのデプロイ手順:');
  console.log('1. Renderダッシュボード（https://dashboard.render.com）にログイン');
  console.log('2. 「New +」ボタンをクリックし、「Web Service」を選択');
  console.log('3. GitリポジトリをRenderに接続');
  console.log('4. 以下の設定を行う:');
  console.log(`   - Name: ${config.renderServiceName}`);
  console.log('   - Environment: Node');
  console.log('   - Build Command: npm install && npx prisma generate && npm run build');
  console.log('   - Start Command: npm start');
  console.log('   - 環境変数を設定（.env.productionの内容）');
  console.log('5. 「Create Web Service」ボタンをクリックしてデプロイを開始');
}

// 5. テーマアプリ拡張機能をデプロイ
function deployThemeExtensions() {
  console.log('\n===== 5. テーマアプリ拡張機能をデプロイ =====');
  
  // テーマアプリ拡張機能のディレクトリが存在するか確認
  const extensionsDir = path.join(process.cwd(), 'extensions');
  if (!fs.existsSync(extensionsDir)) {
    console.log('テーマアプリ拡張機能のディレクトリが見つかりません。スキップします。');
    return;
  }
  
  // テーマアプリ拡張機能をデプロイ
  console.log('\n----- テーマアプリ拡張機能のデプロイ -----');
  console.log('テーマアプリ拡張機能のデプロイは、以下のコマンドを実行してください:');
  console.log('cd extensions/rental-calendar');
  console.log('shopify app deploy');
}

// メイン実行関数
function main() {
  console.log('===== 本番環境デプロイスクリプト開始 =====');
  
  // 確認
  console.log(`本番環境URL: ${config.productionUrl}`);
  console.log(`Renderサービス名: ${config.renderServiceName}`);
  console.log('\n本番環境へのデプロイを開始します。よろしいですか？ (y/n)');
  
  // 実際のデプロイ処理はここに実装
  // 注意: このスクリプトは実際のデプロイを行わず、手順を表示するだけです
  
  // 1. テストの実行
  runTests();
  
  // 2. 本番環境用の設定ファイルを準備
  prepareProductionConfig();
  
  // 3. アプリをビルド
  buildApp();
  
  // 4. Renderへのデプロイを実行
  deployToRender();
  
  // 5. テーマアプリ拡張機能をデプロイ
  deployThemeExtensions();
  
  console.log('\n===== 本番環境デプロイスクリプト完了 =====');
  console.log('デプロイ手順が完了しました。');
  console.log('Renderダッシュボードでデプロイの進行状況を確認してください。');
}

// スクリプト実行
main();
