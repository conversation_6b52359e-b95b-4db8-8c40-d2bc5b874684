{"timestamp": "2025-05-19T07:01:31.607Z", "duration": 24.534, "totalTests": 10, "successCount": 7, "failureCount": 3, "tests": [{"testName": "年末年始の予約テスト", "success": true, "timestamp": "2025-05-19T07:01:36.714Z", "message": "年末年始の予約が正常に作成されました", "error": "", "duration": 4391, "details": {"bookingId": "0d042451-d113-43a6-8184-e6095d5d9485", "period": "年末年始", "startDate": "2025-12-29", "endDate": "2026-01-03"}}, {"testName": "予約期間の最小値テスト", "success": true, "timestamp": "2025-05-19T07:01:37.672Z", "message": "最小予約期間（1日）の予約が正常に作成されました", "error": "", "duration": 958, "details": {"bookingId": "5f6d4a49-f86d-4d6b-b9af-8c05e3e2d79b", "durationDays": 1}}, {"testName": "予約期間の最大値テスト", "success": true, "timestamp": "2025-05-19T07:01:51.453Z", "message": "最大予約期間（30日）の予約が正常に作成されました", "error": "", "duration": 13781, "details": {"bookingId": "0354cb65-7eb3-4d37-b146-c2941a469312", "durationDays": 30}}, {"testName": "過去の日付での予約テスト", "success": true, "timestamp": "2025-05-19T07:01:54.801Z", "message": "過去の日付での予約は正しくエラーになりました", "error": "過去の日付での予約がエラーにならずに成功してしまいました", "duration": 2272, "details": {"expectedError": true}}, {"testName": "終了日が開始日より前の予約テスト", "success": true, "timestamp": "2025-05-19T07:01:55.248Z", "message": "終了日が開始日より前の予約は正しくエラーになりました", "error": "終了日が開始日より前の予約がエラーにならずに成功してしまいました", "duration": 447, "details": {"expectedError": true}}, {"testName": "無効なメールアドレスでの予約テスト", "success": true, "timestamp": "2025-05-19T07:01:55.470Z", "message": "無効なメールアドレスでの予約は正しくエラーになりました", "error": "", "duration": 221, "details": {"expectedError": true, "invalidEmail": "invalid-email"}}, {"testName": "在庫をゼロにするテスト", "success": false, "timestamp": "2025-05-19T07:01:55.697Z", "message": "在庫をゼロにすることに失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-05-26", "duration": 226, "details": {"bookingId": ""}}, {"testName": "在庫がゼロの状態で予約を試みるテスト", "success": true, "timestamp": "2025-05-19T07:01:55.920Z", "message": "在庫がゼロの状態での予約は正しくエラーになりました", "error": "", "duration": 223, "details": {"expectedError": true}}, {"testName": "予約をキャンセルして在庫を回復するテスト", "success": false, "timestamp": "2025-05-19T07:01:55.920Z", "message": "予約をキャンセルして在庫を回復することに失敗しました", "error": "テスト1で作成した予約IDがありません", "duration": 0, "details": {"bookingId": ""}}, {"testName": "在庫が回復した状態で予約を試みるテスト", "success": false, "timestamp": "2025-05-19T07:01:56.138Z", "message": "在庫が回復した状態での予約に失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-05-26", "duration": 218, "details": {"bookingId": ""}}], "environment": {"nodeVersion": "v20.18.1", "os": "darwin arm64"}, "performance": {"averageTestDuration": 2273.7, "slowestTest": {"name": "予約期間の最大値テスト", "duration": 13781}, "fastestTest": {"name": "予約をキャンセルして在庫を回復するテスト", "duration": 0}}}