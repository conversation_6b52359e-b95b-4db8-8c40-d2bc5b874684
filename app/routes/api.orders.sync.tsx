import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { ShopifyOrderService } from "../services/shopify.order.service";

/**
 * 注文同期API
 * POST /api/orders/sync - 注文データを同期
 */
export async function action({ request }: ActionFunctionArgs) {
  // 認証チェック
  const { admin, session } = await authenticate.admin(request);
  
  try {
    // リクエストメソッドをチェック
    if (request.method !== "POST") {
      return json({ success: false, message: "Method not allowed" }, { status: 405 });
    }

    // Shopify注文サービスを初期化
    const orderService = new ShopifyOrderService(session);
    
    // 注文データを同期
    const syncResults = await orderService.syncOrdersWithDatabase(session.shop);
    
    return json({
      success: true,
      message: "注文データを同期しました",
      results: syncResults
    });
  } catch (error) {
    console.error("注文同期エラー:", error);
    return json({
      success: false,
      message: "注文データの同期中にエラーが発生しました",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
