import React, { useState } from 'react';
import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import { 
  Page, 
  LegacyCard, 
  ResourceList, 
  ResourceItem, 
  Filters, 
  Badge, 
  EmptyState, 
  Text,
  Pagination
} from '@shopify/polaris';
import { prisma } from '../db.server';
import { PaymentMethod, PaymentStatus } from '@prisma/client';

// 支払いステータスに応じたバッジ設定
const paymentStatusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  PENDING: { tone: 'attention', label: '未払い' },
  COMPLETED: { tone: 'success', label: '支払済' },
  REFUNDED: { tone: 'info', label: '返金済' },
  FAILED: { tone: 'critical', label: '失敗' }
};

// 支払い方法に応じたラベル
const paymentMethodLabels: Record<string, string> = {
  CREDIT_CARD: 'クレジットカード',
  BANK_TRANSFER: '銀行振込',
  CASH_IN_STORE: '店頭現金'
};

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  
  // クエリパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  const limit = parseInt(url.searchParams.get('limit') || '50', 10);
  const status = url.searchParams.get('status') || undefined;
  const method = url.searchParams.get('method') || undefined;
  const query = url.searchParams.get('query') || undefined;
  const dateFrom = url.searchParams.get('dateFrom') || undefined;
  const dateTo = url.searchParams.get('dateTo') || undefined;
  
  // 検索条件を構築
  const where: any = {};
  
  if (status) {
    where.paymentStatus = status as PaymentStatus;
  }
  
  if (method) {
    where.paymentMethod = method as PaymentMethod;
  }
  
  if (dateFrom) {
    where.createdAt = { gte: new Date(dateFrom) };
  }
  
  if (dateTo) {
    if (where.createdAt) {
      where.createdAt.lte = new Date(dateTo);
    } else {
      where.createdAt = { lte: new Date(dateTo) };
    }
  }
  
  if (query) {
    where.OR = [
      { invoiceNumber: { contains: query } },
      { notes: { contains: query, mode: 'insensitive' } },
      { 
        customerInfo: { 
          path: ['name'],
          string_contains: query 
        } 
      },
      { 
        customerInfo: { 
          path: ['email'],
          string_contains: query 
        } 
      },
      {
        staff: {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { code: { contains: query } }
          ]
        }
      }
    ];
  }
  
  // 請求書データを取得
  const invoices = await prisma.invoice.findMany({
    where,
    include: {
      staff: true,
      bookings: {
        include: {
          product: true
        }
      }
    },
    orderBy: [
      { createdAt: 'desc' }
    ],
    skip: (page - 1) * limit,
    take: limit
  });
  
  // 総件数を取得
  const total = await prisma.invoice.count({ where });
  
  return json({
    invoices: invoices.map(invoice => {
      // 顧客情報を取得
      let customerName = '';
      let customerEmail = '';
      let customerPhone = '';
      
      if (invoice.customerInfo && typeof invoice.customerInfo === 'object') {
        const customerInfo = invoice.customerInfo as any;
        customerName = customerInfo.name || '';
        customerEmail = customerInfo.email || '';
        customerPhone = customerInfo.phone || '';
      }
      
      // 請求情報を取得
      let totalAmount = 0;
      let taxAmount = 0;
      
      if (invoice.billingInfo && typeof invoice.billingInfo === 'object') {
        const billingInfo = invoice.billingInfo as any;
        totalAmount = billingInfo.totalAmount || 0;
        taxAmount = billingInfo.taxAmount || 0;
      }
      
      return {
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        customerName,
        customerEmail,
        customerPhone,
        paymentMethod: invoice.paymentMethod,
        paymentStatus: invoice.paymentStatus,
        paymentDate: invoice.paymentDate?.toISOString() || null,
        totalAmount,
        taxAmount,
        staffName: invoice.staff?.name || '',
        notes: invoice.notes || '',
        createdAt: invoice.createdAt.toISOString(),
        updatedAt: invoice.updatedAt.toISOString(),
        bookings: invoice.bookings.map(booking => ({
          id: booking.id,
          productTitle: booking.product.title,
          productSku: booking.product.sku,
          startDate: booking.startDate.toISOString(),
          endDate: booking.endDate.toISOString(),
          totalAmount: booking.totalAmount?.toNumber() || 0
        }))
      };
    }),
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}

export default function InvoicesIndex() {
  const { invoices, pagination } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [methodFilter, setMethodFilter] = useState('');
  
  // 請求書詳細ページへ移動
  const handleInvoiceClick = (invoiceId: string) => {
    navigate(`/invoices/${invoiceId}`);
  };
  
  // ステータスフィルターを適用
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    const params = new URLSearchParams(window.location.search);
    
    if (value) {
      params.set('status', value);
    } else {
      params.delete('status');
    }
    
    navigate(`/invoices?${params.toString()}`);
  };
  
  // 支払い方法フィルターを適用
  const handleMethodFilterChange = (value: string) => {
    setMethodFilter(value);
    const params = new URLSearchParams(window.location.search);
    
    if (value) {
      params.set('method', value);
    } else {
      params.delete('method');
    }
    
    navigate(`/invoices?${params.toString()}`);
  };
  
  // 検索クエリを適用
  const handleSearchSubmit = () => {
    const params = new URLSearchParams(window.location.search);
    
    if (searchQuery) {
      params.set('query', searchQuery);
    } else {
      params.delete('query');
    }
    
    navigate(`/invoices?${params.toString()}`);
  };
  
  // ページネーション処理
  const handlePaginationChange = (newPage: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set('page', newPage.toString());
    navigate(`/invoices?${params.toString()}`);
  };
  
  // 日付をフォーマット
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };
  
  // 金額をフォーマット
  const formatAmount = (amount: number) => {
    return `¥${amount.toLocaleString()}`;
  };
  
  // フィルターオプションを生成
  const filters = [
    {
      key: 'status',
      label: '支払いステータス',
      filter: (
        <select
          value={statusFilter}
          onChange={(e) => handleStatusFilterChange(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="">すべて</option>
          <option value="PENDING">未払い</option>
          <option value="COMPLETED">支払済</option>
          <option value="REFUNDED">返金済</option>
          <option value="FAILED">失敗</option>
        </select>
      ),
      shortcut: true,
    },
    {
      key: 'method',
      label: '支払い方法',
      filter: (
        <select
          value={methodFilter}
          onChange={(e) => handleMethodFilterChange(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="">すべて</option>
          <option value="CREDIT_CARD">クレジットカード</option>
          <option value="BANK_TRANSFER">銀行振込</option>
          <option value="CASH_IN_STORE">店頭現金</option>
        </select>
      ),
      shortcut: true,
    }
  ];
  
  return (
    <Page
      title="請求書管理"
      primaryAction={{
        content: "新規請求書",
        url: "/invoices/new"
      }}
    >
      <LegacyCard>
        <ResourceList
          resourceName={{ singular: '請求書', plural: '請求書' }}
          items={invoices}
          renderItem={(invoice) => {
            const { 
              id, 
              invoiceNumber, 
              customerName, 
              paymentMethod, 
              paymentStatus, 
              paymentDate, 
              totalAmount, 
              staffName, 
              bookings 
            } = invoice;
            
            const badge = paymentStatusBadgeMap[paymentStatus] || { tone: 'new', label: paymentStatus };
            const methodLabel = paymentMethodLabels[paymentMethod || ''] || paymentMethod;
            
            const shortcutActions = [
              {
                content: '詳細',
                url: `/invoices/${id}`,
              },
              {
                content: '編集',
                url: `/invoices/edit/${id}`,
              }
            ];
            
            if (paymentStatus === 'PENDING') {
              shortcutActions.push({
                content: '支払い完了',
                url: `/invoices/complete-payment/${id}`,
              });
            }
            
            return (
              <ResourceItem
                id={id}
                onClick={() => handleInvoiceClick(id)}
                shortcutActions={shortcutActions}
                persistActions
              >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <Text as="h3" variant="bodyMd" fontWeight="bold">
                      請求書番号: {invoiceNumber}
                    </Text>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodyMd">
                        {customerName || '顧客名なし'}
                      </Text>
                    </div>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        合計金額: {formatAmount(totalAmount)}
                      </Text>
                    </div>
                    {methodLabel && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          支払い方法: {methodLabel}
                        </Text>
                      </div>
                    )}
                    {paymentDate && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          支払日: {formatDate(paymentDate)}
                        </Text>
                      </div>
                    )}
                    {staffName && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          担当者: {staffName}
                        </Text>
                      </div>
                    )}
                    {bookings.length > 0 && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          商品: {bookings.map(b => b.productTitle).join(', ')}
                        </Text>
                      </div>
                    )}
                  </div>
                  <div>
                    <Badge tone={badge.tone}>{badge.label}</Badge>
                  </div>
                </div>
              </ResourceItem>
            );
          }}
          filterControl={
            <Filters
              queryValue={searchQuery}
              filters={filters}
              onQueryChange={setSearchQuery}
              onQueryClear={() => setSearchQuery('')}
              onClearAll={() => {
                setSearchQuery('');
                setStatusFilter('');
                setMethodFilter('');
                navigate('/invoices');
              }}
              onQueryBlur={handleSearchSubmit}
              hideQueryField={false}
            />
          }
          emptyState={
            <EmptyState
              heading="請求書がありません"
              image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
            >
              <p>請求書を登録して、レンタル商品の請求を管理しましょう。</p>
            </EmptyState>
          }
        />
        {pagination.totalPages > 1 && (
          <div style={{ padding: '16px', display: 'flex', justifyContent: 'center' }}>
            <Pagination
              hasPrevious={pagination.page > 1}
              onPrevious={() => handlePaginationChange(pagination.page - 1)}
              hasNext={pagination.page < pagination.totalPages}
              onNext={() => handlePaginationChange(pagination.page + 1)}
            />
          </div>
        )}
      </LegacyCard>
      <div style={{ marginTop: '16px', textAlign: 'right' }}>
        <Text as="p" variant="bodySm" tone="subdued">
          合計 {pagination.total} 件の請求書 ( {pagination.page} / {pagination.totalPages} ページ )
        </Text>
      </div>
    </Page>
  );
}
