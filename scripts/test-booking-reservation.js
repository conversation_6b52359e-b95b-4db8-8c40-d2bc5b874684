/**
 * 予約テストスクリプト
 *
 * このスクリプトは、商品の予約機能をテストします。
 * 実行方法: node scripts/test-booking-reservation.js [商品ID] [テストタイプ]
 * テストタイプ: basic, provisional, confirmed, holiday, longterm
 */

import { PrismaClient } from '@prisma/client';
import { addDays, format } from 'date-fns';
import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';
import { v4 as uuidv4 } from 'uuid';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品情報を取得するクエリ
const GET_PRODUCT = gql`
  query getProduct($id: ID!) {
    product(id: $id) {
      id
      title
      status
      variants(first: 5) {
        edges {
          node {
            id
            title
            sku
            inventoryQuantity
            inventoryPolicy
          }
        }
      }
      metafields(first: 10) {
        edges {
          node {
            namespace
            key
            value
          }
        }
      }
    }
  }
`;

// メタフィールドを更新するミューテーション
const UPDATE_METAFIELD = gql`
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * 商品情報を取得する関数
 * @param {string} productId 商品ID
 * @returns {Promise<object>} 商品情報
 */
async function getProductInfo(productId) {
  try {
    // Shopify IDを正規化
    const shopifyId = productId.startsWith('gid://shopify/Product/')
      ? productId
      : `gid://shopify/Product/${productId}`;

    console.log(`商品情報を取得中... (Shopify ID: ${shopifyId})`);

    // Shopifyから商品情報を取得
    const result = await shopifyClient.request(GET_PRODUCT, {
      id: shopifyId
    });

    if (!result.product) {
      throw new Error(`商品ID ${productId} が見つかりません`);
    }

    const product = result.product;
    console.log(`商品情報を取得しました: ${product.title}`);

    // データベースから商品情報を取得
    const dbProduct = await prisma.product.findFirst({
      where: {
        shopifyId: productId.replace('gid://shopify/Product/', '')
      }
    });

    if (!dbProduct) {
      throw new Error(`商品ID ${productId} がデータベースに存在しません`);
    }

    return { shopifyProduct: product, dbProduct };
  } catch (error) {
    console.error('商品情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 予約情報を取得する関数
 * @param {string} productId 商品ID
 * @returns {Promise<object>} 予約情報
 */
async function getBookingInfo(productId) {
  try {
    // データベースから予約情報を取得
    const bookings = await prisma.booking.findMany({
      where: {
        productId,
        endDate: {
          gte: new Date()
        }
      },
      orderBy: {
        startDate: 'asc'
      }
    });

    console.log(`予約情報を取得しました: ${bookings.length}件`);
    return bookings;
  } catch (error) {
    console.error('予約情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 基本的な予約テストを実行する関数
 * @param {object} product 商品情報
 * @returns {Promise<object>} テスト結果
 */
async function runBasicTest(product) {
  try {
    console.log('基本的な予約テストを実行中...');

    // 現在の日付
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // テスト用の予約期間（3日後から5日後）
    const startDate = addDays(today, 3);
    const endDate = addDays(today, 5);

    // 予約データを作成
    const booking = await prisma.booking.create({
      data: {
        id: uuidv4(),
        bookingId: `TEST-${uuidv4().substring(0, 8).toUpperCase()}`,
        shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
        productId: product.dbProduct.id,
        variantId: product.shopifyProduct.variants.edges[0]?.node.id || null,
        startDate,
        endDate,
        customerName: 'テストユーザー',
        customerEmail: '<EMAIL>',
        bookingType: 'PROVISIONAL',
        status: 'PROVISIONAL',
        paymentStatus: 'PENDING',
        totalAmount: 10000,
        depositAmount: 1000,
        // notes: 'テスト予約', // notesフィールドは存在しない
        priority: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('テスト予約を作成しました:');
    console.log(`予約ID: ${booking.id}`);
    console.log(`開始日: ${format(startDate, 'yyyy-MM-dd')}`);
    console.log(`終了日: ${format(endDate, 'yyyy-MM-dd')}`);

    return { success: true, booking };
  } catch (error) {
    console.error('基本的な予約テスト中にエラーが発生しました:', error);
    return { success: false, error };
  }
}

/**
 * 仮予約テストを実行する関数
 * @param {object} product 商品情報
 * @returns {Promise<object>} テスト結果
 */
async function runProvisionalTest(product) {
  try {
    console.log('仮予約テストを実行中...');

    // 現在の日付
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // テスト用の予約期間（10日後から12日後）
    const startDate = addDays(today, 10);
    const endDate = addDays(today, 12);

    // 予約データを作成
    const booking = await prisma.booking.create({
      data: {
        id: uuidv4(),
        bookingId: `TEST-PROV-${uuidv4().substring(0, 8).toUpperCase()}`,
        shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
        productId: product.dbProduct.id,
        variantId: product.shopifyProduct.variants.edges[0]?.node.id || null,
        startDate,
        endDate,
        customerName: '仮予約テストユーザー',
        customerEmail: '<EMAIL>',
        bookingType: 'PROVISIONAL',
        status: 'PROVISIONAL',
        paymentStatus: 'PENDING',
        totalAmount: 10000,
        depositAmount: 1000,
        // notes: '仮予約テスト', // notesフィールドは存在しない
        priority: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('仮予約を作成しました:');
    console.log(`予約ID: ${booking.id}`);
    console.log(`開始日: ${format(startDate, 'yyyy-MM-dd')}`);
    console.log(`終了日: ${format(endDate, 'yyyy-MM-dd')}`);

    return { success: true, booking };
  } catch (error) {
    console.error('仮予約テスト中にエラーが発生しました:', error);
    return { success: false, error };
  }
}

/**
 * 確定予約テストを実行する関数
 * @param {object} product 商品情報
 * @returns {Promise<object>} テスト結果
 */
async function runConfirmedTest(product) {
  try {
    console.log('確定予約テストを実行中...');

    // 現在の日付
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // テスト用の予約期間（20日後から22日後）
    const startDate = addDays(today, 20);
    const endDate = addDays(today, 22);

    // 予約データを作成
    const booking = await prisma.booking.create({
      data: {
        id: uuidv4(),
        bookingId: `TEST-CONF-${uuidv4().substring(0, 8).toUpperCase()}`,
        shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
        productId: product.dbProduct.id,
        variantId: product.shopifyProduct.variants.edges[0]?.node.id || null,
        startDate,
        endDate,
        customerName: '確定予約テストユーザー',
        customerEmail: '<EMAIL>',
        bookingType: 'CONFIRMED',
        status: 'CONFIRMED',
        paymentStatus: 'COMPLETED',
        totalAmount: 10000,
        depositAmount: 1000,
        // notes: '確定予約テスト', // notesフィールドは存在しない
        priority: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('確定予約を作成しました:');
    console.log(`予約ID: ${booking.id}`);
    console.log(`開始日: ${format(startDate, 'yyyy-MM-dd')}`);
    console.log(`終了日: ${format(endDate, 'yyyy-MM-dd')}`);

    return { success: true, booking };
  } catch (error) {
    console.error('確定予約テスト中にエラーが発生しました:', error);
    return { success: false, error };
  }
}

/**
 * 重複予約テストを実行する関数
 * @param {object} product 商品情報
 * @param {Array} existingBookings 既存の予約情報
 * @returns {Promise<object>} テスト結果
 */
async function runDuplicateTest(product, existingBookings) {
  try {
    console.log('重複予約テストを実行中...');

    if (existingBookings.length === 0) {
      console.log('既存の予約がないため、重複予約テストをスキップします');
      return { success: true, message: '既存の予約がないため、テストをスキップしました' };
    }

    // 既存の予約から最初の予約を取得
    const existingBooking = existingBookings[0];
    console.log(`既存の予約: ${existingBooking.bookingId}, 期間: ${format(existingBooking.startDate, 'yyyy-MM-dd')} 〜 ${format(existingBooking.endDate, 'yyyy-MM-dd')}`);

    // 同じ期間で予約を作成しようとする
    console.log('同じ期間で予約を作成しようとしています...');

    try {
      const duplicateBooking = await prisma.booking.create({
        data: {
          id: uuidv4(),
          bookingId: `TEST-DUP-${uuidv4().substring(0, 8).toUpperCase()}`,
          shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
          productId: product.dbProduct.id,
          variantId: product.shopifyProduct.variants.edges[0]?.node.id || null,
          startDate: existingBooking.startDate,
          endDate: existingBooking.endDate,
          customerName: '重複予約テストユーザー',
          customerEmail: '<EMAIL>',
          bookingType: 'PROVISIONAL',
          status: 'PROVISIONAL',
          paymentStatus: 'PENDING',
          totalAmount: 10000,
          depositAmount: 1000,
          priority: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      console.log('重複予約が作成されました（エラーが発生するべきでした）:');
      console.log(`予約ID: ${duplicateBooking.id}`);
      return { success: false, message: '重複予約が作成されました（エラーが発生するべきでした）' };
    } catch (error) {
      console.log('予想通り、重複予約の作成に失敗しました:');
      console.log(`エラーメッセージ: ${error.message}`);
      return { success: true, message: '重複予約の作成に失敗しました（期待通りの動作）' };
    }
  } catch (error) {
    console.error('重複予約テスト中にエラーが発生しました:', error);
    return { success: false, error };
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    // コマンドライン引数から商品IDとテストタイプを取得
    const productId = process.argv[2];
    const testType = process.argv[3] || 'basic';

    if (!productId) {
      console.error('商品IDが指定されていません。使用方法: node scripts/test-booking-reservation.js [商品ID] [テストタイプ]');
      process.exit(1);
    }

    console.log(`商品ID ${productId} の予約テストを実行します...`);
    console.log(`テストタイプ: ${testType}`);

    // 商品情報を取得
    const product = await getProductInfo(productId);

    // 既存の予約情報を取得
    const existingBookings = await getBookingInfo(product.dbProduct.id);
    console.log(`既存の予約数: ${existingBookings.length}`);

    // テストタイプに応じたテストを実行
    let testResult;
    switch (testType) {
      case 'basic':
        testResult = await runBasicTest(product);
        break;
      case 'provisional':
        testResult = await runProvisionalTest(product);
        break;
      case 'confirmed':
        testResult = await runConfirmedTest(product);
        break;
      case 'duplicate':
        testResult = await runDuplicateTest(product, existingBookings);
        break;
      default:
        console.error(`未知のテストタイプです: ${testType}`);
        process.exit(1);
    }

    if (testResult.success) {
      console.log('予約テストが成功しました！');
    } else {
      console.error('予約テストが失敗しました:', testResult.error);
      process.exit(1);
    }
  } catch (error) {
    console.error('予約テスト中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
