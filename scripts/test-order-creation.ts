/**
 * 予約データからShopify注文を作成するテストスクリプト
 *
 * このスクリプトは、既存の予約データからShopify注文を作成し、
 * 予約データとShopify注文が正しく関連付けられているかをテストします。
 *
 * 実行方法: npx tsx scripts/test-order-creation.ts [予約ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { format, addDays } from 'date-fns';
import { ja } from 'date-fns/locale';
import { authenticate } from '../app/shopify.server';
import { createOrderFromBooking } from '../app/utils/booking/order-creator';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * Shopify Admin APIを初期化する関数
 */
async function initializeShopifyAdmin() {
  try {
    // 環境変数からアクセストークンを取得
    const shopifyAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
    const shop = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';

    if (!shopifyAccessToken) {
      throw new Error('SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません');
    }

    console.log(`ショップ: ${shop}`);
    console.log('アクセストークン: 設定済み');

    // 直接AdminAPIクライアントを作成
    const admin = {
      graphql: async (query, options) => {
        const url = `https://${shop}/admin/api/2025-01/graphql.json`;
        console.log(`GraphQL URL: ${url}`);

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': shopifyAccessToken
          },
          body: JSON.stringify({
            query,
            variables: options?.variables
          })
        });

        return response;
      },
      rest: {
        resources: {
          // 必要に応じてRESTリソースを追加
        }
      }
    };

    console.log('Shopify Admin APIの初期化が完了しました');
    return admin;
  } catch (error) {
    console.error('Shopify Admin API初期化エラー:', error);
    throw error;
  }
}

// テスト設定
const config = {
  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',

  // テスト用の顧客情報
  customerName: 'テスト顧客',
  customerEmail: '<EMAIL>',
  customerId: '8420600119464', // 実際のShopify顧客ID

  // テスト用の予約期間（日数）
  bookingDays: 3
};

/**
 * 予約情報を表示する関数
 */
async function displayBookingInfo(bookingId: string) {
  try {
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        product: true
      }
    });

    if (!booking) {
      console.error(`予約ID ${bookingId} が見つかりません`);
      return;
    }

    console.log('----- 予約情報 -----');
    console.log(`ID: ${booking.id}`);
    console.log(`予約ID: ${booking.bookingId}`);
    console.log(`開始日: ${format(booking.startDate, 'yyyy/MM/dd', { locale: ja })}`);
    console.log(`終了日: ${format(booking.endDate, 'yyyy/MM/dd', { locale: ja })}`);
    console.log(`ステータス: ${booking.status}`);
    console.log(`予約タイプ: ${booking.bookingType}`);
    console.log(`顧客名: ${booking.customerName}`);
    console.log(`顧客メール: ${booking.customerEmail}`);
    console.log(`顧客ID: ${booking.customerId || 'なし'}`);
    console.log(`商品: ${booking.product?.title || 'なし'} (ID: ${booking.productId})`);
    console.log(`Shopify注文ID: ${booking.shopifyOrderId || 'なし'}`);
    console.log(`Shopify注文番号: ${booking.shopifyOrderName || 'なし'}`);
    console.log('-------------------');
  } catch (error) {
    console.error('予約情報表示エラー:', error);
  }
}

/**
 * 最新の予約を取得する関数
 */
async function getLatestBooking() {
  try {
    const booking = await prisma.booking.findFirst({
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!booking) {
      console.error('予約が見つかりません');
      return null;
    }

    return booking;
  } catch (error) {
    console.error('最新予約取得エラー:', error);
    return null;
  }
}

/**
 * 商品情報を取得する関数
 */
async function getProductInfo(productId: string) {
  try {
    // Prismaから商品情報を取得
    const product = await prisma.product.findFirst({
      where: {
        OR: [
          { id: productId },
          { shopifyId: productId }
        ]
      }
    });

    if (!product) {
      console.error(`商品ID ${productId} が見つかりません`);
      return null;
    }

    return product;
  } catch (error) {
    console.error('商品情報取得エラー:', error);
    return null;
  }
}

/**
 * テスト用の予約を作成する関数
 */
async function createTestBooking(productId: string) {
  try {
    console.log(`商品ID ${productId} のテスト予約を作成します...`);

    // 商品情報を取得
    const product = await getProductInfo(productId);

    if (!product) {
      return null;
    }

    // 予約IDを生成
    const bookingId = `TEST-ORDER-${Date.now().toString().substring(7)}`;

    // 予約期間を設定
    const startDate = new Date();
    const endDate = addDays(startDate, config.bookingDays);

    // 予約を作成
    const booking = await prisma.booking.create({
      data: {
        bookingId,
        shop: config.shop,
        productId: product.id,
        startDate,
        endDate,
        customerName: config.customerName,
        customerEmail: config.customerEmail,
        customerId: config.customerId,
        status: 'CONFIRMED',
        bookingType: 'CONFIRMED',
        notes: 'テスト予約（注文作成テスト）',
        priority: 1,
        totalAmount: product.price,
        depositAmount: product.price * 0.1, // デポジットは10%
      },
    });

    console.log('テスト予約が作成されました');
    console.log(`予約ID: ${booking.id}`);
    console.log(`予約番号: ${booking.bookingId}`);
    console.log(`開始日: ${format(booking.startDate, 'yyyy/MM/dd', { locale: ja })}`);
    console.log(`終了日: ${format(booking.endDate, 'yyyy/MM/dd', { locale: ja })}`);

    return booking;
  } catch (error) {
    console.error('テスト予約作成エラー:', error);
    return null;
  }
}

/**
 * 注文作成をテストする関数
 */
async function testOrderCreation(bookingId: string) {
  try {
    console.log(`予約ID ${bookingId} の注文作成テストを実行します...`);

    // 予約情報を表示
    console.log('\n----- 予約情報 -----');
    await displayBookingInfo(bookingId);

    // Shopify Admin APIを初期化
    console.log('\n----- Shopify Admin API初期化 -----');
    const admin = await initializeShopifyAdmin();

    // 注文を作成
    console.log('\n----- 注文作成実行 -----');
    try {
      console.log('注文作成処理を開始します...');
      console.log('- prisma:', !!prisma);
      console.log('- bookingId:', bookingId);

      // アプリケーションの実装を使用して注文を作成
      console.log('アプリケーションの実装を使用して注文を作成します...');
      console.log('app/utils/booking/order-creator.ts の createOrderFromBooking 関数を呼び出します');

      // 注: 実際のアプリケーションコードを使用するため、authenticate.adminを使用
      const { authenticate } = await import('../app/shopify.server');
      const { createOrderFromBooking } = await import('../app/utils/booking/order-creator');

      // リクエストオブジェクトを作成（ダミー）
      const headers = new Headers();
      headers.append('Content-Type', 'application/json');

      const request = new Request('https://example.com', {
        method: 'GET',
        headers: headers
      });

      // authenticate.adminを呼び出す
      const { admin } = await authenticate.admin(request);

      console.log('Shopify Admin APIの初期化が完了しました');
      console.log('createOrderFromBooking関数を呼び出します...');

      const result = await createOrderFromBooking(
        prisma,
        admin,
        bookingId,
        3 // リトライ回数
      );

      console.log('注文作成結果:', result);

      return result;
    } catch (orderError) {
      console.error('注文作成中にエラーが発生しました:', orderError);
      if (orderError instanceof Error) {
        console.error('エラーメッセージ:', orderError.message);
        console.error('スタックトレース:', orderError.stack);
      }
      throw orderError;
    }

    // 更新された予約情報を表示
    console.log('\n----- 更新後の予約情報 -----');
    await displayBookingInfo(bookingId);

    return result;
  } catch (error) {
    console.error('注文作成テストエラー:', error);
    return null;
  }
}

/**
 * テスト予約を削除する関数
 */
async function cleanupTestBooking(bookingId: string) {
  try {
    console.log(`予約ID ${bookingId} を削除します...`);

    await prisma.booking.delete({
      where: { id: bookingId }
    });

    console.log('テスト予約が削除されました');
    return true;
  } catch (error) {
    console.error('テスト予約削除エラー:', error);
    return false;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('注文作成テストを開始します...');

    // コマンドライン引数から商品IDまたは予約IDを取得
    const arg = process.argv[2];

    if (!arg) {
      console.error('引数が指定されていません。商品IDまたは予約IDを指定してください。');
      console.error('例: npx tsx scripts/test-order-creation.ts 123456789');
      process.exit(1);
    }

    // 引数が予約IDかどうかを判定
    const isBookingId = arg.length > 20; // 予約IDは通常長い

    let booking;
    let bookingId;

    if (isBookingId) {
      // 予約IDが指定された場合
      bookingId = arg;
      console.log(`指定された予約ID: ${bookingId}`);
    } else {
      // 商品IDが指定された場合、テスト予約を作成
      console.log(`指定された商品ID: ${arg}`);
      booking = await createTestBooking(arg);

      if (!booking) {
        console.error('テスト予約の作成に失敗しました');
        process.exit(1);
      }

      bookingId = booking.id;
    }

    // 注文作成をテスト
    const orderResult = await testOrderCreation(bookingId);

    // テスト予約を作成した場合は削除
    let cleanupResult = true;
    if (booking) {
      cleanupResult = await cleanupTestBooking(booking.id);
    }

    // テスト結果のサマリー
    console.log('\n===== テスト結果サマリー =====');
    if (booking) {
      console.log(`テスト予約作成: 成功`);
    }
    console.log(`注文作成テスト: ${orderResult?.success ? '成功' : '失敗'}`);
    if (booking) {
      console.log(`テスト予約削除: ${cleanupResult ? '成功' : '失敗'}`);
    }

    if (orderResult?.success) {
      console.log('\n注文作成テストが成功しました！');
      console.log(`注文ID: ${orderResult.orderId}`);
      console.log(`注文番号: ${orderResult.orderName}`);
      console.log(`ドラフト注文: ${orderResult.isDraftOrder ? 'はい' : 'いいえ'}`);
    } else {
      console.error('\n注文作成テストが失敗しました。');
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:');
    console.error(error);
    if (error instanceof Error) {
      console.error('エラーメッセージ:', error.message);
      console.error('スタックトレース:', error.stack);
    }
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
