/**
 * メタフィールド定義作成スクリプト
 *
 * このスクリプトは、レンタル商品ECシステムに必要なメタフィールド定義を作成します。
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// メタフィールド定義作成のGraphQLミューテーション
const CREATE_METAFIELD_DEFINITION = gql`
  mutation metafieldDefinitionCreate($definition: MetafieldDefinitionInput!) {
    metafieldDefinitionCreate(definition: $definition) {
      createdDefinition {
        id
        name
        namespace
        key
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールド定義取得のGraphQLクエリ
const GET_METAFIELD_DEFINITIONS = gql`
  query getMetafieldDefinitions($ownerType: MetafieldOwnerType!, $first: Int!) {
    metafieldDefinitions(ownerType: $ownerType, first: $first) {
      edges {
        node {
          id
          name
          namespace
          key
          description
          type {
            name
          }
          validations {
            name
            value
          }
        }
      }
    }
  }
`;

// メタフィールド定義を作成する関数
async function createMetafieldDefinition(definition) {
  try {
    const result = await client.request(CREATE_METAFIELD_DEFINITION, {
      definition
    });

    if (result.metafieldDefinitionCreate.userErrors.length > 0) {
      console.error(`メタフィールド定義 ${definition.namespace}.${definition.key} の作成中にエラーが発生しました:`, result.metafieldDefinitionCreate.userErrors);
      return false;
    }

    console.log(`メタフィールド定義 ${definition.namespace}.${definition.key} を作成しました。`);
    return true;
  } catch (error) {
    console.error(`メタフィールド定義 ${definition.namespace}.${definition.key} の作成中にエラーが発生しました:`, error);
    return false;
  }
}

// メタフィールド定義を取得する関数
async function getMetafieldDefinitions(ownerType = 'PRODUCT') {
  try {
    const result = await client.request(GET_METAFIELD_DEFINITIONS, {
      ownerType,
      first: 100 // 最大100件取得
    });

    return result.metafieldDefinitions.edges.map(edge => edge.node);
  } catch (error) {
    console.error('メタフィールド定義の取得中にエラーが発生しました:', error);
    return [];
  }
}

// メインの実行関数
async function createMetafieldDefinitions() {
  console.log('メタフィールド定義の作成を開始します...');

  // 既存のメタフィールド定義を取得
  const existingDefinitions = await getMetafieldDefinitions();
  console.log(`${existingDefinitions.length}件のメタフィールド定義が見つかりました。`);

  // 作成するメタフィールド定義
  const definitionsToCreate = [
    {
      name: 'レンタル在庫アイテム',
      namespace: 'rental',
      key: 'inventory_items',
      description: '在庫アイテムのID、SKU、状態、場所、備考などの情報',
      ownerType: 'PRODUCT',
      type: 'json'
    },
    {
      name: 'レンタル予約情報',
      namespace: 'rental',
      key: 'reservation_info',
      description: '在庫アイテムごとの予約情報',
      ownerType: 'PRODUCT',
      type: 'json'
    }
  ];

  // 各メタフィールド定義を作成
  for (const definition of definitionsToCreate) {
    // 既に存在するか確認
    const exists = existingDefinitions.some(
      def => def.namespace === definition.namespace && def.key === definition.key
    );

    if (exists) {
      console.log(`メタフィールド定義 ${definition.namespace}.${definition.key} は既に存在します。スキップします。`);
      continue;
    }

    // メタフィールド定義を作成
    await createMetafieldDefinition(definition);
  }

  console.log('メタフィールド定義の作成が完了しました。');
}

// スクリプト実行
createMetafieldDefinitions().catch(error => {
  console.error('スクリプト実行中にエラーが発生しました:', error);
  process.exit(1);
});
