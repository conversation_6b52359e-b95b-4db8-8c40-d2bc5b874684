import { prisma } from '../db.server';

export interface EnrichedAddress {
  // Shopify標準フィールド
  id?: string;
  first_name?: string;
  last_name?: string;
  company?: string;
  address1?: string;
  address2?: string;
  city?: string;
  province?: string;
  zip?: string;
  country?: string;
  phone?: string;
  
  // 拡張情報
  extension?: {
    id?: string;
    fax?: string;
    storeStaff?: string;
    orderPath?: string;
    department?: string;
    position?: string;
    deliveryTimeSlot?: string;
    deliveryNotes?: string;
    billingContact?: string;
    addressType?: string;
    addressName?: string;
    deliveryMemo?: string;
    priority?: number;
    lastUsedAt?: Date;
    usageCount?: number;
  };
  
  // 表示用
  displayName?: string;
  isShopifyAddress?: boolean;
}

export interface CustomerAddressConfig {
  isCorporate: boolean;
  maxAddresses: number;
  currentAddressCount: number;
  canAddMore: boolean;
  showCorporateFields: boolean;
}

export class CustomerAddressService {
  private shop: string;

  constructor(shop: string) {
    this.shop = shop;
  }

  /**
   * 顧客の住所設定を取得
   */
  async getCustomerAddressConfig(customerId: string): Promise<CustomerAddressConfig> {
    const customer = await this.getCustomer(customerId);
    const currentAddressCount = await this.getAddressCount(customerId);
    
    return {
      isCorporate: customer?.isCorporate || false,
      maxAddresses: customer?.maxAddresses || (customer?.isCorporate ? 10 : 3),
      currentAddressCount,
      canAddMore: currentAddressCount < (customer?.maxAddresses || 3),
      showCorporateFields: customer?.isCorporate || false
    };
  }

  /**
   * 顧客の全住所を取得（Shopify + 拡張情報）
   */
  async getCustomerAddresses(customerId: string): Promise<EnrichedAddress[]> {
    // 1. Shopify標準住所を取得（実際の実装では Shopify API を使用）
    const shopifyAddresses = await this.getShopifyCustomerAddresses(customerId);
    
    // 2. 拡張情報を取得
    const extensions = await prisma.addressExtension.findMany({
      where: { 
        customerId, 
        shop: this.shop,
        isActive: true
      },
      orderBy: [
        { priority: 'desc' },
        { lastUsedAt: 'desc' }
      ]
    });

    // 3. 統合データを構築
    return this.mergeAddressData(shopifyAddresses, extensions);
  }

  /**
   * 住所拡張情報を保存
   */
  async saveAddressExtension(
    customerId: string,
    shopifyAddressId: string | null,
    extensionData: Partial<EnrichedAddress['extension']>
  ) {
    const data = {
      shop: this.shop,
      customerId,
      shopifyAddressId,
      ...extensionData
    };

    if (extensionData?.id) {
      // 更新
      return await prisma.addressExtension.update({
        where: { id: extensionData.id },
        data
      });
    } else {
      // 新規作成
      return await prisma.addressExtension.create({
        data
      });
    }
  }

  /**
   * 法人フラグの切り替え
   */
  async toggleCorporateStatus(customerId: string, isCorporate: boolean) {
    const newMaxAddresses = isCorporate ? 10 : 3;
    
    // 顧客情報を更新
    await prisma.customer.upsert({
      where: { 
        shopifyId: customerId // Shopify IDで検索
      },
      update: { 
        isCorporate,
        maxAddresses: newMaxAddresses
      },
      create: {
        shop: this.shop,
        shopifyId: customerId,
        name: '', // 実際の実装では Shopify から取得
        isCorporate,
        maxAddresses: newMaxAddresses
      }
    });

    // 法人→個人に変更時、住所数が上限を超える場合の処理
    if (!isCorporate) {
      return await this.handleAddressLimitReduction(customerId, newMaxAddresses);
    }

    return { success: true };
  }

  /**
   * 住所使用履歴を更新
   */
  async updateAddressUsage(customerId: string, shopifyAddressId: string | null) {
    const extension = await prisma.addressExtension.findFirst({
      where: {
        customerId,
        shopifyAddressId,
        shop: this.shop
      }
    });

    if (extension) {
      await prisma.addressExtension.update({
        where: { id: extension.id },
        data: {
          lastUsedAt: new Date(),
          usageCount: { increment: 1 }
        }
      });
    }
  }

  /**
   * 受注方法マスタを取得
   */
  async getOrderPaths() {
    return await prisma.orderPath.findMany({
      where: { 
        shop: this.shop,
        isActive: true
      },
      orderBy: { code: 'asc' }
    });
  }

  /**
   * ストア担当者マスタを取得
   */
  async getStoreStaff() {
    return await prisma.storeStaff.findMany({
      where: { 
        shop: this.shop,
        isActive: true
      },
      orderBy: { staffCode: 'asc' }
    });
  }

  // プライベートメソッド

  private async getCustomer(customerId: string) {
    return await prisma.customer.findFirst({
      where: { 
        shopifyId: customerId,
        shop: this.shop
      }
    });
  }

  private async getAddressCount(customerId: string): Promise<number> {
    // 実際の実装では Shopify API で住所数を取得
    // ここでは仮の実装
    const extensions = await prisma.addressExtension.count({
      where: { 
        customerId, 
        shop: this.shop,
        isActive: true
      }
    });
    
    return extensions + 1; // Shopify標準住所 + 拡張住所
  }

  private async getShopifyCustomerAddresses(customerId: string): Promise<EnrichedAddress[]> {
    // 実際の実装では Shopify Customer API を使用
    // ここでは仮のデータを返す
    return [
      {
        id: '1',
        first_name: '太郎',
        last_name: '田中',
        company: 'サンプル株式会社',
        address1: '東京都渋谷区1-1-1',
        address2: 'サンプルビル3F',
        city: '渋谷区',
        province: '東京都',
        zip: '150-0001',
        country: 'Japan',
        phone: '03-1234-5678',
        isShopifyAddress: true
      }
    ];
  }

  private mergeAddressData(
    shopifyAddresses: EnrichedAddress[], 
    extensions: any[]
  ): EnrichedAddress[] {
    const enrichedAddresses = shopifyAddresses.map(addr => {
      const extension = extensions.find(ext =>
        ext.shopifyAddressId === addr.id?.toString()
      );

      return {
        ...addr,
        extension: extension || {},
        displayName: this.generateDisplayName(addr, extension),
        isShopifyAddress: true
      };
    });

    // 追加住所（Prismaのみに存在）も含める
    const additionalAddresses = extensions
      .filter(ext => !ext.shopifyAddressId)
      .map(ext => this.buildAddressFromExtension(ext));

    return [...enrichedAddresses, ...additionalAddresses];
  }

  private generateDisplayName(address: EnrichedAddress, extension?: any): string {
    if (extension?.addressName) {
      return extension.addressName;
    }
    
    if (address.company) {
      return address.company;
    }
    
    return `${address.last_name || ''} ${address.first_name || ''}`.trim() || '住所';
  }

  private buildAddressFromExtension(extension: any): EnrichedAddress {
    return {
      id: extension.id,
      extension,
      displayName: extension.addressName || '追加住所',
      isShopifyAddress: false
    };
  }

  private async handleAddressLimitReduction(customerId: string, newLimit: number) {
    const addresses = await this.getCustomerAddresses(customerId);
    
    if (addresses.length > newLimit) {
      // 使用頻度の低い住所を特定
      const excessAddresses = addresses
        .sort((a, b) => (a.extension?.lastUsedAt?.getTime() || 0) - (b.extension?.lastUsedAt?.getTime() || 0))
        .slice(newLimit);
        
      return {
        needsConfirmation: true,
        excessAddresses,
        message: `住所上限が${newLimit}件に変更されます。${excessAddresses.length}件の住所が上限を超えています。`
      };
    }

    return { success: true };
  }
}
