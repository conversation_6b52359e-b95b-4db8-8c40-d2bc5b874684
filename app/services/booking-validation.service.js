/**
 * 予約バリデーション機能
 * 
 * 本格運用レベルの厳密なバリデーション機能
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 予約データの包括的バリデーション
 */
export async function validateBooking(bookingData) {
  const errors = [];

  try {
    // 1. 基本データバリデーション
    const basicErrors = validateBasicData(bookingData);
    errors.push(...basicErrors);

    // 2. 商品存在チェック
    const productErrors = await validateProductExists(bookingData.productId, bookingData.shop);
    errors.push(...productErrors);

    // 3. 日付バリデーション
    const dateErrors = validateDates(bookingData.startDate, bookingData.endDate);
    errors.push(...dateErrors);

    // 4. 重複予約チェック
    const conflictErrors = await validateBookingConflicts(bookingData);
    errors.push(...conflictErrors);

    return {
      isValid: errors.length === 0,
      errors
    };

  } catch (error) {
    console.error('予約バリデーションエラー:', error);
    return {
      isValid: false,
      errors: [{
        field: 'system',
        message: 'バリデーション処理中にエラーが発生しました',
        code: 'VALIDATION_ERROR'
      }]
    };
  }
}

/**
 * 基本データのバリデーション
 */
function validateBasicData(bookingData) {
  const errors = [];

  // 必須フィールドチェック
  if (!bookingData.productId) {
    errors.push({
      field: 'productId',
      message: '商品IDは必須です',
      code: 'REQUIRED_FIELD'
    });
  }

  if (!bookingData.startDate) {
    errors.push({
      field: 'startDate',
      message: '開始日は必須です',
      code: 'REQUIRED_FIELD'
    });
  }

  if (!bookingData.endDate) {
    errors.push({
      field: 'endDate',
      message: '終了日は必須です',
      code: 'REQUIRED_FIELD'
    });
  }

  if (!bookingData.shop) {
    errors.push({
      field: 'shop',
      message: 'ショップは必須です',
      code: 'REQUIRED_FIELD'
    });
  }

  // メールアドレス形式チェック
  if (bookingData.customerEmail && !isValidEmail(bookingData.customerEmail)) {
    errors.push({
      field: 'customerEmail',
      message: '有効なメールアドレスを入力してください',
      code: 'INVALID_EMAIL'
    });
  }

  return errors;
}

/**
 * 商品存在チェック
 */
async function validateProductExists(productId, shop) {
  const errors = [];

  try {
    const product = await prisma.product.findFirst({
      where: {
        id: productId,
        shop: shop
      }
    });

    if (!product) {
      errors.push({
        field: 'productId',
        message: '指定された商品が見つかりません',
        code: 'PRODUCT_NOT_FOUND'
      });
    } else if (product.status !== 'AVAILABLE') {
      errors.push({
        field: 'productId',
        message: `商品は現在利用できません (ステータス: ${product.status})`,
        code: 'PRODUCT_UNAVAILABLE'
      });
    }
  } catch (error) {
    errors.push({
      field: 'productId',
      message: '商品の確認中にエラーが発生しました',
      code: 'PRODUCT_CHECK_ERROR'
    });
  }

  return errors;
}

/**
 * 日付バリデーション
 */
function validateDates(startDate, endDate) {
  const errors = [];
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  // 日付の有効性チェック
  if (isNaN(startDate.getTime())) {
    errors.push({
      field: 'startDate',
      message: '有効な開始日を入力してください',
      code: 'INVALID_DATE'
    });
  }

  if (isNaN(endDate.getTime())) {
    errors.push({
      field: 'endDate',
      message: '有効な終了日を入力してください',
      code: 'INVALID_DATE'
    });
  }

  // 過去日付チェック
  if (startDate < today) {
    errors.push({
      field: 'startDate',
      message: '開始日は今日以降の日付を指定してください',
      code: 'PAST_DATE'
    });
  }

  // 開始日 > 終了日チェック
  if (startDate >= endDate) {
    errors.push({
      field: 'endDate',
      message: '終了日は開始日より後の日付を指定してください',
      code: 'INVALID_DATE_RANGE'
    });
  }

  // 最大レンタル期間チェック（30日）
  const maxDays = 30;
  const diffTime = endDate.getTime() - startDate.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays > maxDays) {
    errors.push({
      field: 'endDate',
      message: `レンタル期間は最大${maxDays}日までです`,
      code: 'EXCEEDS_MAX_DURATION'
    });
  }

  return errors;
}

/**
 * 重複予約チェック
 */
async function validateBookingConflicts(bookingData) {
  const errors = [];

  try {
    // 同じ商品で期間が重複する確定予約をチェック
    const conflictingBookings = await prisma.booking.findMany({
      where: {
        productId: bookingData.productId,
        shop: bookingData.shop,
        status: {
          in: ['CONFIRMED', 'IN_PROGRESS']
        },
        OR: [
          // 新しい予約の開始日が既存予約の期間内
          {
            AND: [
              { startDate: { lte: bookingData.startDate } },
              { endDate: { gt: bookingData.startDate } }
            ]
          },
          // 新しい予約の終了日が既存予約の期間内
          {
            AND: [
              { startDate: { lt: bookingData.endDate } },
              { endDate: { gte: bookingData.endDate } }
            ]
          },
          // 新しい予約が既存予約を完全に包含
          {
            AND: [
              { startDate: { gte: bookingData.startDate } },
              { endDate: { lte: bookingData.endDate } }
            ]
          }
        ]
      }
    });

    if (conflictingBookings.length > 0) {
      const conflictDates = conflictingBookings.map(booking => 
        `${booking.startDate.toLocaleDateString()} - ${booking.endDate.toLocaleDateString()}`
      ).join(', ');

      errors.push({
        field: 'dateRange',
        message: `指定期間は既に予約されています: ${conflictDates}`,
        code: 'BOOKING_CONFLICT'
      });
    }

  } catch (error) {
    errors.push({
      field: 'dateRange',
      message: '予約競合チェック中にエラーが発生しました',
      code: 'CONFLICT_CHECK_ERROR'
    });
  }

  return errors;
}

/**
 * メールアドレス形式チェック
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 予約作成前の最終チェック
 */
export async function validateBeforeCreate(bookingData) {
  const validation = await validateBooking(bookingData);
  
  if (!validation.isValid) {
    console.warn('予約バリデーション失敗:', validation.errors);
  }
  
  return validation;
}
