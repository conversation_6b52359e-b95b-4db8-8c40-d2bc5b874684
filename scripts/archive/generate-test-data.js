const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

async function generateTestData() {
  const testProducts = [
    {
      shop: "peaces-test-block.myshopify.com",
      shopifyId: "test_10101001",
      title: "【82_ﾄﾞﾚｽｿﾌｧ ｵﾌﾎﾜｲﾄ 1ｼｰﾀｰ】",
      sku: "10101001",
      price: 7000.0,
      status: "active",
      basicInfo: {
        productCode: "10101001",
        location: "NY",
        status: "available",
        detailCode: "1",
        kana: "ﾄﾞﾚｽｿﾌｧｵﾌﾎﾜｲﾄ1ｼｰﾀｰ",
      },
      maintenanceInfo: {
        lastMaintenanceDate: "2024-03-15",
        nextMaintenanceDate: "2024-09-15",
        maintenanceStatus: "良好",
        maintenanceNotes: "クリーニング済み",
      },
    },
    {
      shop: "peaces-test-block.myshopify.com",
      shopifyId: "test_10101002",
      title: "【83_ﾄﾞﾚｽｿﾌｧ ｸﾞﾚｰ 1ｼｰﾀｰ】",
      sku: "10101002",
      price: 7000.0,
      status: "active",
      basicInfo: {
        productCode: "10101002",
        location: "PR",
        status: "available",
        detailCode: "1",
        kana: "ﾄﾞﾚｽｿﾌｧｸﾞﾚｰ1ｼｰﾀｰ",
      },
    },
    {
      shop: "peaces-test-block.myshopify.com",
      shopifyId: "test_10102001",
      title: "【84_ﾄﾞﾚｽｿﾌｧ ﾌﾞﾗｳﾝ 2ｼｰﾀｰ】",
      sku: "10102001",
      price: 10000.0,
      status: "active",
      basicInfo: {
        productCode: "10102001",
        location: "NY",
        status: "available",
        detailCode: "1",
        kana: "ﾄﾞﾚｽｿﾌｧﾌﾞﾗｳﾝ2ｼｰﾀｰ",
      },
    },
    {
      shop: "peaces-test-block.myshopify.com",
      shopifyId: "test_10103001",
      title: "【85_ﾄﾞﾚｽｿﾌｧ ﾌﾞﾗｯｸ 3ｼｰﾀｰ】",
      sku: "10103001",
      price: 13000.0,
      status: "active",
      basicInfo: {
        productCode: "10103001",
        location: "PR",
        status: "maintenance",
        detailCode: "1",
        kana: "ﾄﾞﾚｽｿﾌｧﾌﾞﾗｯｸ3ｼｰﾀｰ",
      },
      maintenanceInfo: {
        lastMaintenanceDate: "2024-03-20",
        nextMaintenanceDate: "2024-04-05",
        maintenanceStatus: "修理中",
        maintenanceNotes: "張り替え作業中",
      },
    },
    {
      shop: "peaces-test-block.myshopify.com",
      shopifyId: "test_20101001",
      title: "【91_ｳｯﾄﾞﾁｪｱ ﾅﾁｭﾗﾙ】",
      sku: "20101001",
      price: 5000.0,
      status: "active",
      basicInfo: {
        productCode: "20101001",
        location: "NY",
        status: "available",
        detailCode: "1",
        kana: "ｳｯﾄﾞﾁｪｱﾅﾁｭﾗﾙ",
      },
    },
    {
      shop: "peaces-test-block.myshopify.com",
      shopifyId: "test_20101002",
      title: "【92_ｳｯﾄﾞﾁｪｱ ﾌﾞﾗｯｸ】",
      sku: "20101002",
      price: 5000.0,
      status: "active",
      basicInfo: {
        productCode: "20101002",
        location: "PR",
        status: "available",
        detailCode: "1",
        kana: "ｳｯﾄﾞﾁｪｱﾌﾞﾗｯｸ",
      },
    },
    {
      shop: "peaces-test-block.myshopify.com",
      shopifyId: "test_30101001",
      title: "【101_ｶﾞﾗｽﾃｰﾌﾞﾙ ｸﾘｱ】",
      sku: "30101001",
      price: 8000.0,
      status: "active",
      basicInfo: {
        productCode: "30101001",
        location: "NY",
        status: "available",
        detailCode: "1",
        kana: "ｶﾞﾗｽﾃｰﾌﾞﾙｸﾘｱ",
      },
    },
    {
      shop: "peaces-test-block.myshopify.com",
      shopifyId: "test_30102001",
      title: "【102_ｶﾞﾗｽﾃｰﾌﾞﾙ ｽﾓｰｸ】",
      sku: "30102001",
      price: 8000.0,
      status: "active",
      basicInfo: {
        productCode: "30102001",
        location: "PR",
        status: "available",
        detailCode: "1",
        kana: "ｶﾞﾗｽﾃｰﾌﾞﾙｽﾓｰｸ",
      },
    },
    {
      shop: "peaces-test-block.myshopify.com",
      shopifyId: "test_40101001",
      title: "【121_ﾗｸﾞﾏｯﾄ ｱｲﾎﾞﾘｰ】",
      sku: "40101001",
      price: 3000.0,
      status: "active",
      basicInfo: {
        productCode: "40101001",
        location: "NY",
        status: "available",
        detailCode: "1",
        kana: "ﾗｸﾞﾏｯﾄｱｲﾎﾞﾘｰ",
      },
    },
    {
      shop: "peaces-test-block.myshopify.com",
      shopifyId: "test_40101002",
      title: "【122_ﾗｸﾞﾏｯﾄ ｸﾞﾚｰ】",
      sku: "40101002",
      price: 3000.0,
      status: "active",
      basicInfo: {
        productCode: "40101002",
        location: "PR",
        status: "available",
        detailCode: "1",
        kana: "ﾗｸﾞﾏｯﾄｸﾞﾚｰ",
      },
    },
  ];

  console.log("テストデータの生成を開始します...");

  try {
    // 一括でデータを挿入
    const createdProducts = await prisma.product.createMany({
      data: testProducts,
      skipDuplicates: true, // SKUの重複を無視
    });

    console.log(`${createdProducts.count}件の商品データを作成しました`);
  } catch (error) {
    console.error("データ生成中にエラーが発生しました:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトの実行
generateTestData().catch((error) => {
  console.error("スクリプトの実行中にエラーが発生しました:", error);
  process.exit(1);
});
