<div data-section-id="{{ section.id }}" data-section-type="search-form"{% render 'layout-get-container-class' custom_class: 'd-block' %}>
    <div class="search-form py-55 text-center">
        {%- if section.settings.title != blank -%}
            <h3 class="search-form__title mb-10">{{ section.settings.title }}</h3>
        {%- endif -%}
        {%- if section.settings.paragraph != blank -%}
            <p class="search-form__paragraph mb-15">{{ section.settings.paragraph }}</p>
        {%- endif -%}
        <div class="search-form__form px-10 mx-auto">
            {% case section.settings.type %}
            {% when '1' %}
                <form action="{{ routes.search_url }}" method="get" role="search" class="mb-0">
                    <label class="position-relative d-flex align-items-center mr-0">
                        <input type="search"
                               class="pl-20 pr-40 mb-0"
                               name="q"
                               value="{{ search.terms | escape }}"
                               placeholder="{{ section.settings.placeholder }}">
                        <input type="hidden" name="options[prefix]" value="last" />
                        <i class="position-absolute right-0 mr-15">
                            {% render 'icon-theme-168' %}
                        </i>
                    </label>
                </form>
            {% when '2' %}
                <form action="{{ routes.search_url }}" method="get" role="search" class="mb-0">
                    <div class="d-flex">
                        <input type="search"
                               class="mb-0 mr-15"
                               name="q"
                               value="{{ search.terms | escape }}"
                               placeholder="{{ section.settings.placeholder }}">
                        <input type="hidden" name="options[prefix]" value="last" />
                        <button type="submit" class="btn btn-invert text-nowrap">{{ section.settings.button_text }}</button>
                    </div>
                </form>
            {% endcase %}
        </div>
    </div>
</div>


{% schema %}
{
    "name": "Search form",
    "settings": [
        {
            "type": "select",
            "id": "container",
            "label": { "en": "Content width", "ja": "コンテンツ幅"},
            "default": "fullwidth",
            "options": [
                {
                    "value": "fullwidth",
                    "label": { "en": "Fullwidth", "ja": "フル幅"}
                },
                {
                    "value": "boxed",
                    "label": { "en": "Boxed", "ja": "ボックス幅"}
                }
            ]
        },
        {
            "type": "header",
            "content": { "en": "Content", "ja": "コンテンツ"}
        },
        {
            "type": "text",
            "id": "title",
            "label": { "en": "Title", "ja": "タイトル"},
            "default": "検索する" //"SEARCH OUR SHOP"
        },
        {
            "type": "text",
            "id": "paragraph",
            "label": { "en": "Paragraph", "ja": "パラグラフ文章"},
            "default": "ご希望の商品のキーワードを入力してください" //"We've got tons of great products."
        },
        {
            "type": "select",
            "id": "type",
            "label": { "en": "Search design type", "ja": "デザインのタイプ"},
            "default": "1",
            "options": [
                {
                    "value": "1",
                    "label": { "en": "Type #1", "ja": "タイプ #1"}
                },
                {
                    "value": "2",
                    "label": { "en": "Type #2", "ja": "タイプ #2"}
                }
            ]
        },
        {
            "type": "text",
            "id": "placeholder",
            "label": { "en": "Placeholder", "ja": "プレースホルダー"},
            "default": "商品を検索" // "Search Products..."
        },
        {
            "type": "text",
            "id": "button_text",
            "label": { "en": "Button text", "ja": "ボタンテキスト"},
            "default": "検索" //"Search"
        },
        {
            "type": "header",
            "content": { "en": "Support", "ja": "サポート"}
        },
        {
            "type": "paragraph",
            "content": { "en": "[Watch video tutorials](https://www.youtube.com/watch?v=zSN75qF0olA&list=PLAy2KDG_No-AxqsLMLW4apX0Sn8MuvXrs&index=1)", "ja": "[MISEル マニュアル（動画）](https://www.youtube.com/channel/UChCFvSvr3YgbYDcumJ_OqzQ)"}
        },
        {
            "type": "paragraph",
            "content": { "en": "[Read user manual](https://misell-manual.wraptas.site/)", "ja": "[検索フォーム](https://misell-manual.wraptas.site/sections/search-form)"}
        }
    ],
    "presets": [
        {
            "name": { "en": "Search form", "ja": "検索フォーム"},
            "category": "3) Static content"
        }
    ]
}
{% endschema %}