# CSVからメタフィールドを一括インポートする方法

このスクリプトを使用すると、CSVファイルから商品のメタフィールド情報を一括でインポートできます。
有料アプリを使わずに、GraphQL APIを使用して効率的にメタフィールドを設定します。

## 準備

1. 必要なパッケージをインストールします：

```bash
npm install csv-parse graphql-request dotenv
```

2. `.env` ファイルに以下の環境変数を設定します：

```
SHOPIFY_SHOP=あなたのショップ名.myshopify.com
SHOPIFY_ADMIN_API_ACCESS_TOKEN=あなたのアクセストークン
```

アクセストークンは、Shopify管理画面の「アプリと販売チャネル」→「アプリの開発」→「個人用アクセストークン」から取得できます。
必要な権限スコープ: `write_products`

## CSVファイルの準備

CSVファイルは以下の形式で準備します：

```csv
sku,height,width,depth,weight,material,color
TEST-WH-001,120,60,45,5.5,木製,ブラウン
TEST-WH-002,90,45,30,3.2,プラスチック,ホワイト
```

- 1行目はヘッダー行で、各列の名前を指定します
- `sku` 列は必須です（商品を特定するために使用）
- その他の列は、設定したいメタフィールドに対応します

## メタフィールド定義のカスタマイズ

`import-metafields-from-csv.js` ファイル内の `metafieldDefinitions` オブジェクトを編集して、
インポートしたいメタフィールドの定義を追加・変更できます：

```javascript
const metafieldDefinitions = {
  height: {
    namespace: 'custom',
    key: 'height',
    type: 'number_decimal',
    name: '高さ',
    description: '商品の高さ（cm）',
  },
  // 他のメタフィールド定義...
};
```

各メタフィールド定義には以下の情報を含めます：
- `namespace`: メタフィールドの名前空間
- `key`: メタフィールドのキー
- `type`: メタフィールドのデータ型（例: `number_decimal`, `single_line_text_field`, `json` など）
- `name`: 管理画面に表示される名前（オプション）
- `description`: 説明（オプション）

## 使用方法

以下のコマンドでスクリプトを実行します：

```bash
node import-metafields-from-csv.js path/to/your/file.csv
```

## 注意事項

- 大量の商品を処理する場合は、Shopify API のレート制限に注意してください
- エラーが発生した場合は、ログを確認して問題を特定してください
- 既存のメタフィールドは上書きされます
- SKUが一致する商品が見つからない場合は、その商品はスキップされます

## トラブルシューティング

1. **API制限エラー**: 一度に多くの商品を処理しようとすると、API制限に達する可能性があります。その場合は、処理を分割するか、遅延を追加してください。

2. **商品が見つからない**: SKUが正確に一致する商品が見つからない場合は、エラーログを確認してください。SKUの形式が正しいことを確認してください。

3. **メタフィールド型エラー**: メタフィールドの型が正しくない場合、エラーが発生します。例えば、数値フィールドに文字列を設定しようとした場合などです。CSVデータとメタフィールド定義の型が一致していることを確認してください。
