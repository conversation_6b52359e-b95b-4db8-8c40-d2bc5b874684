import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import prisma from "../db.server";
interface TestResult {
  result: number;
}

export const loader = async () => {
  const testData = await prisma.$queryRaw<TestResult[]>`SELECT 1+1 as result`;
  return json({ testData });
  return json({ testData });
};

export default function PrismaTest() {
  const { testData } = useLoaderData<typeof loader>();

  return (
    <div style={{ fontFamily: "system-ui, sans-serif", lineHeight: "1.8" }}>
      <h1>Prisma Connection Test</h1>
      <p>Database connection result: {testData[0].result}</p>
    </div>
  );
}
