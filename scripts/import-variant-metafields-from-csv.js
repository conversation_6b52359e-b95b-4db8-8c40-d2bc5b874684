import dotenv from 'dotenv';
import fs from 'fs';
import { GraphQLClient, gql } from 'graphql-request';

// csv-parse/syncがない場合の代替処理
function parseCsv(content) {
  const lines = content.split('\n');
  const headers = lines[0].split(',').map(h => h.trim());

  const records = [];
  for (let i = 1; i < lines.length; i++) {
    if (!lines[i].trim()) continue;

    const values = lines[i].split(',').map(v => v.trim());
    const record = {};

    headers.forEach((header, index) => {
      record[header] = values[index] || '';
    });

    records.push(record);
  }

  return records;
}

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const endpoint = `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`;
const graphQLClient = new GraphQLClient(endpoint, {
  headers: {
    'X-Shopify-Access-Token': ACCESS_TOKEN,
    'Content-Type': 'application/json',
  },
});

// メタフィールド定義のマッピング
const metafieldDefinitions = {
  stock: {
    namespace: 'custom',
    key: 'stock',
    type: 'number_integer',
    name: '在庫数',
    description: 'バリアントの在庫数',
  },
  location: {
    namespace: 'custom',
    key: 'location',
    type: 'single_line_text_field',
    name: '在庫場所',
    description: '在庫の保管場所（NY/PR）',
  },
  rental_days: {
    namespace: 'custom',
    key: 'rental_days',
    type: 'number_integer',
    name: 'レンタル日数',
    description: 'このバリアントのレンタル日数',
  },
  price_per_day: {
    namespace: 'custom',
    key: 'price_per_day',
    type: 'number_integer',
    name: '1日あたりの価格',
    description: '1日あたりのレンタル価格',
  }
};

// CSVファイルの読み込み
async function readCSV(filePath) {
  try {
    const content = fs.readFileSync(filePath, { encoding: 'utf-8' });
    // 自作のCSVパーサーを使用
    const records = parseCsv(content);
    return records;
  } catch (error) {
    console.error('CSVファイルの読み込みエラー:', error);
    throw error;
  }
}

// バリアントIDの取得（SKUから）
async function getVariantIdFromSku(sku) {
  const query = gql`
    query getVariantBySku($query: String!) {
      productVariants(first: 1, query: $query) {
        edges {
          node {
            id
            displayName
            product {
              title
            }
          }
        }
      }
    }
  `;

  try {
    const data = await graphQLClient.request(query, { query: `sku:${sku}` });
    if (data.productVariants.edges.length > 0) {
      return {
        id: data.productVariants.edges[0].node.id,
        title: data.productVariants.edges[0].node.displayName,
        productTitle: data.productVariants.edges[0].node.product.title,
      };
    }
    return null;
  } catch (error) {
    console.error(`SKU ${sku} のバリアントID取得エラー:`, error);
    return null;
  }
}

// メタフィールドの設定
async function setMetafield(variantId, namespace, key, value, type) {
  const mutation = gql`
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    metafields: [
      {
        ownerId: variantId,
        namespace,
        key,
        value: String(value),
        type,
      }
    ]
  };

  try {
    const result = await graphQLClient.request(mutation, variables);
    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', result.metafieldsSet.userErrors);
      return false;
    }
    return true;
  } catch (error) {
    console.error(`メタフィールド ${namespace}.${key} 設定エラー:`, error);
    return false;
  }
}

// メイン処理
async function importVariantMetafieldsFromCSV(csvFilePath) {
  try {
    // CSVファイルの読み込み
    const variants = await readCSV(csvFilePath);
    console.log(`${variants.length}件のバリアントデータを読み込みました`);

    let successCount = 0;
    let errorCount = 0;

    // 各バリアントのメタフィールドを設定
    for (const variant of variants) {
      const sku = variant.sku || variant.SKU;
      if (!sku) {
        console.warn('SKUが見つかりません:', variant);
        errorCount++;
        continue;
      }

      // SKUからバリアントIDを取得
      const variantInfo = await getVariantIdFromSku(sku);
      if (!variantInfo) {
        console.warn(`SKU ${sku} のバリアントが見つかりません`);
        errorCount++;
        continue;
      }

      console.log(`処理中: ${variantInfo.productTitle} - ${variantInfo.title} (${sku})`);

      // 各メタフィールドを設定
      let variantSuccess = true;
      for (const [field, definition] of Object.entries(metafieldDefinitions)) {
        if (variant[field] && variant[field].trim() !== '') {
          const success = await setMetafield(
            variantInfo.id,
            definition.namespace,
            definition.key,
            variant[field],
            definition.type
          );

          if (!success) {
            variantSuccess = false;
            console.error(`${variantInfo.title} の ${field} 設定に失敗しました`);
          }
        }
      }

      if (variantSuccess) {
        successCount++;
        console.log(`${variantInfo.title} のメタフィールドを設定しました`);
      } else {
        errorCount++;
      }
    }

    console.log('=== 処理完了 ===');
    console.log(`成功: ${successCount}件`);
    console.log(`失敗: ${errorCount}件`);
  } catch (error) {
    console.error('処理中にエラーが発生しました:', error);
  }
}

// コマンドライン引数からCSVファイルパスを取得
const csvFilePath = process.argv[2];
if (!csvFilePath) {
  console.error('使用方法: node import-variant-metafields-from-csv.js <CSVファイルパス>');
  process.exit(1);
}

// 実行
importVariantMetafieldsFromCSV(csvFilePath);
