{%- if product.metafields.reviews.rating.value != blank -%}
    {% liquid
    assign type = type | default: 1
    assign rating_abs = product.metafields.reviews.rating.value.rating | abs
    assign rating_increment = rating_abs | plus: 1
    assign rating_decimal = 0 
    assign decimal = product.metafields.reviews.rating.value.rating | modulo: 1 
    if decimal >= 0.3 and decimal <= 0.7
        assign rating_decimal = 0.5
    elsif decimal > 0.7
        assign rating_decimal = 1
    endif 
    %}
    <div class="shopify-review shopify-review--type-{{ type }} d-flex flex-column" data-aria-label="{{ 'general.accessibility.star_reviews_info' | t: rating_value: product.metafields.reviews.rating.value, rating_max: product.metafields.reviews.rating.value.scale_max }}">
        <span class="shopify-review-badge d-flex flex-wrap align-items-center justify-content-center justify-content-md-{% if centered %}center{% else %}start{% endif %} {% if type == 1 %}mt-lg-7{% else %}mb-20{% endif %}" data-rating="{{ product.metafields.reviews.rating.value.rating }}">
            <span class="shopify-review-starrating shopify-review-badge-starrating{% unless hide_counter %} {% if hide_mobile_counter %}mr-lg-10{% else %}mr-10{% endif %}{% endunless %}">
                {%- for star in (1..product.metafields.reviews.rating.value.scale_max) -%}
                    <i class="shopify-review-icon">
                        {%- if star <= rating_abs -%}
                            {% render 'icon-theme-221' %}
                        {%- else -%}
                            {%- if rating_increment > star and rating_decimal != 0 -%}
                                {%- if rating_decimal == 0.5 -%}
                                    {% render 'icon-theme-222' %}
                                {%- elsif rating_decimal == 1 -%}
                                    {% render 'icon-theme-221' %}
                                {%- endif -%}
                            {%- else -%}
                                {% render 'icon-theme-223' %}
                            {%- endif -%}
                        {%- endif -%}
                    </i>
                {%- endfor -%}
            </span>
            <span class="shopify-review-badge-caption text-underline cursor-pointer{% if hide_counter %} d-none{% elsif hide_mobile_counter %} d-none d-lg-inline{% endif %}{% if type == 2 %} js-to-tab-shopify-review{% endif %}">{{ "general.accessibility.total_reviews" | t: count: product.metafields.reviews.rating_count }}</span>
        </span>
    </div>
{%- endif -%}