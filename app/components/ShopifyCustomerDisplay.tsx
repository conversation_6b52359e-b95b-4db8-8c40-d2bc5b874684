import React, { useEffect, useState } from 'react';
import { Text, Spinner, Link, Tooltip, Icon, InlineStack } from '@shopify/polaris';
import { InfoIcon, ExternalIcon } from '@shopify/polaris-icons';
import { CustomerReferenceService, CustomerInfo } from '~/services/customer-reference.service';
import { ClientOnly } from './ClientOnly';

/**
 * Shopify顧客表示コンポーネントのプロパティ
 *
 * @param customerId Shopify顧客ID（必須）
 * @param fallbackName 顧客情報が取得できない場合に表示する名前
 * @param fallbackEmail 顧客情報が取得できない場合に表示するメールアドレス
 * @param fallbackPhone 顧客情報が取得できない場合に表示する電話番号
 * @param session Shopifyセッション（省略可、省略時はグローバルセッションを使用）
 * @param shopifyShop Shopifyショップドメイン（Shopify管理画面へのリンクに使用）
 * @param showEmail メールアドレスを表示するかどうか（デフォルト: true）
 * @param showPhone 電話番号を表示するかどうか（デフォルト: true）
 * @param showDetails 詳細情報を表示するかどうか（デフォルト: false）
 * @param linkToShopify Shopify管理画面へのリンクを表示するかどうか（デフォルト: false）
 */
interface ShopifyCustomerDisplayProps {
  customerId?: string;
  fallbackName?: string;
  fallbackEmail?: string;
  fallbackPhone?: string;
  session?: any;
  shopifyShop?: string;
  showEmail?: boolean;
  showPhone?: boolean;
  showDetails?: boolean;
  linkToShopify?: boolean;
}

/**
 * Shopify顧客表示コンポーネント
 * 顧客IDから顧客情報を取得して表示する
 *
 * このコンポーネントは、Shopify顧客IDから顧客情報を取得して表示します。
 * 顧客情報の取得には、CustomerReferenceServiceを使用します。
 * 顧客情報が取得できない場合は、フォールバック値を表示します。
 *
 * 使用例:
 * ```tsx
 * <ShopifyCustomerDisplay
 *   customerId="1234567890"
 *   fallbackName="ゲスト"
 *   shopifyShop="myshop.myshopify.com"
 *   showDetails={true}
 *   linkToShopify={true}
 * />
 * ```
 */
export function ShopifyCustomerDisplay({
  customerId,
  fallbackName,
  fallbackEmail,
  fallbackPhone,
  session,
  shopifyShop,
  showEmail = true,
  showPhone = true,
  showDetails = false,
  linkToShopify = false
}: ShopifyCustomerDisplayProps) {
  return (
    <ClientOnly fallback={<Spinner size="small" />}>
      {() => <ShopifyCustomerDisplayInner
        customerId={customerId}
        fallbackName={fallbackName}
        fallbackEmail={fallbackEmail}
        fallbackPhone={fallbackPhone}
        session={session}
        shopifyShop={shopifyShop}
        showEmail={showEmail}
        showPhone={showPhone}
        showDetails={showDetails}
        linkToShopify={linkToShopify}
      />}
    </ClientOnly>
  );
}

// 内部コンポーネント - クライアントサイドでのみレンダリングされる
function ShopifyCustomerDisplayInner({
  customerId,
  fallbackName,
  fallbackEmail,
  fallbackPhone,
  session,
  shopifyShop,
  showEmail = true,
  showPhone = true,
  showDetails = false,
  linkToShopify = false
}: ShopifyCustomerDisplayProps) {
  // サーバーサイドレンダリング時は初期値をnullに設定
  const [customer, setCustomer] = useState<CustomerInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // 顧客情報を取得
  useEffect(() => {
    // 顧客IDがない場合は何もしない
    if (!customerId) {
      return;
    }

    // 顧客情報を取得
    const fetchCustomer = async () => {
      setLoading(true);
      setError(null);

      try {
        // 顧客IDが前回と同じで、顧客情報が既に取得済みの場合はスキップ
        if (customer && customer.id === customerId) {
          setLoading(false);
          return;
        }

        // グローバルなセッション情報を取得
        const globalSession = window.ENV?.shopifySession;

        // セッション情報がない場合はフォールバック処理
        if (!globalSession && !session) {
          console.warn('ShopifyCustomerDisplay: セッション情報がありません - フォールバック表示を使用します');
          // エラーをスローせず、nullを返してフォールバック表示を使用
          setCustomer(null);
          setLoading(false);
          return;
        }

        // 顧客参照サービスを初期化
        const customerService = new CustomerReferenceService(session || globalSession);

        // 顧客情報を取得
        const customerInfo = await customerService.getCustomerById(customerId);

        if (customerInfo) {
          // 顧客情報を設定
          setCustomer(customerInfo);
        } else {
          setError('顧客情報が見つかりませんでした');
        }
      } catch (err) {
        console.error('ShopifyCustomerDisplay: 顧客情報取得エラー:', err);
        setError('顧客情報の取得に失敗しました');
      } finally {
        setLoading(false);
      }
    };

    // 非同期処理を実行
    fetchCustomer();
  }, [customerId, session]);

  // ローディング中
  if (loading) {
    return <Spinner size="small" />;
  }

  // エラーまたは顧客情報がない場合はフォールバック値を表示
  if (error || !customer) {
    return (
      <div>
        <InlineStack gap="200" blockAlign="center">
          <Text>{fallbackName || '名前なし'}</Text>
          {customerId && (
            <Tooltip content={error || '顧客情報が取得できませんでした'}>
              <Icon source={InfoIcon} />
            </Tooltip>
          )}
        </InlineStack>
        {showEmail && fallbackEmail && (
          <div>
            <Text tone="subdued" as="span">{fallbackEmail}</Text>
          </div>
        )}
        {showPhone && fallbackPhone && (
          <div>
            <Text tone="subdued" as="span">{fallbackPhone}</Text>
          </div>
        )}
        {customerId && linkToShopify && shopifyShop && (
          <div>
            <a
              href={`https://${shopifyShop}/admin/customers/${customerId}`}
              target="_blank"
              rel="noopener noreferrer"
              style={{ display: 'inline-flex', alignItems: 'center', gap: '4px', color: 'var(--p-interactive)', textDecoration: 'none' }}
            >
              Shopifyで表示 <Icon source={ExternalIcon} />
            </a>
          </div>
        )}
      </div>
    );
  }

  // 顧客情報を表示
  const customerName = customer.name || `${customer.firstName} ${customer.lastName}`.trim() || '名前なし';
  const customerEmail = customer.email || fallbackEmail || '';
  const customerPhone = customer.phone || fallbackPhone || '';

  return (
    <div>
      {linkToShopify && shopifyShop ? (
        <a
          href={`https://${shopifyShop}/admin/customers/${customer.shopifyId}`}
          target="_blank"
          rel="noopener noreferrer"
          style={{ display: 'inline-flex', alignItems: 'center', gap: '4px', color: 'var(--p-interactive)', textDecoration: 'none', fontWeight: 'bold' }}
        >
          {customerName} <Icon source={ExternalIcon} />
        </a>
      ) : (
        <Text fontWeight="bold">{customerName}</Text>
      )}

      {showEmail && customerEmail && (
        <div>
          <Text tone="subdued" as="span">{customerEmail}</Text>
        </div>
      )}

      {showPhone && customerPhone && (
        <div>
          <Text tone="subdued" as="span">{customerPhone}</Text>
        </div>
      )}

      {showDetails && customer.address && (
        <div>
          <Text tone="subdued" as="span">{customer.address}</Text>
        </div>
      )}
    </div>
  );
}
