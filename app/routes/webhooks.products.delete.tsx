import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";

/**
 * 商品削除Webhookハンドラー
 * 
 * Shopifyで商品が削除された際に呼び出され、
 * Prismaデータベースからも対応する商品を削除または無効化します。
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  console.log("=== 商品削除Webhook受信 ===");
  
  try {
    // Shopify Webhookの認証
    const { payload, shop } = await authenticate.webhook(request);
    
    console.log("Shop:", shop);
    console.log("Webhook payload:", JSON.stringify(payload, null, 2));

    // 削除された商品のShopify ID
    const shopifyProductId = payload.id?.toString();
    
    if (!shopifyProductId) {
      console.error("商品IDが見つかりません");
      return json({ error: "商品IDが見つかりません" }, { status: 400 });
    }

    console.log(`削除された商品のShopify ID: ${shopifyProductId}`);

    // Prismaデータベースで該当商品を検索
    const existingProduct = await prisma.product.findFirst({
      where: {
        shop: shop,
        shopifyId: shopifyProductId
      },
      include: {
        bookings: {
          where: {
            status: {
              in: ['PROVISIONAL', 'CONFIRMED']
            }
          }
        }
      }
    });

    if (!existingProduct) {
      console.log(`商品ID ${shopifyProductId} はデータベースに存在しません`);
      return json({ message: "商品が見つかりません" }, { status: 200 });
    }

    console.log(`データベースで商品を発見: ${existingProduct.title}`);
    console.log(`関連する有効な予約数: ${existingProduct.bookings.length}`);

    // 有効な予約がある場合は削除せずに無効化
    if (existingProduct.bookings.length > 0) {
      console.log("有効な予約が存在するため、商品を無効化します");
      
      const updatedProduct = await prisma.product.update({
        where: { id: existingProduct.id },
        data: {
          status: 'UNAVAILABLE',
          syncStatus: 'DELETED_FROM_SHOPIFY',
          lastSyncedAt: new Date(),
          metadata: {
            ...(existingProduct.metadata || {}),
            deletedFromShopify: true,
            deletedAt: new Date().toISOString(),
            hasActiveBookings: true,
            activeBookingsCount: existingProduct.bookings.length
          }
        }
      });

      console.log(`商品を無効化しました: ${updatedProduct.title}`);
      
      return json({ 
        message: "商品を無効化しました（有効な予約が存在するため）",
        productId: shopifyProductId,
        productTitle: updatedProduct.title,
        activeBookings: existingProduct.bookings.length
      }, { status: 200 });
    } else {
      console.log("有効な予約がないため、商品を完全削除します");
      
      // 関連データも含めて削除
      await prisma.$transaction(async (tx) => {
        // 関連する予約を削除（キャンセル済みなど）
        await tx.booking.deleteMany({
          where: { productId: existingProduct.id }
        });

        // 関連するメンテナンス記録を削除
        await tx.maintenance.deleteMany({
          where: { productId: existingProduct.id }
        });

        // 関連するレンタル可能性記録を削除
        await tx.rentalAvailability.deleteMany({
          where: { productId: existingProduct.id }
        });

        // 関連するインベントリカレンダーを削除
        await tx.inventoryCalendar.deleteMany({
          where: { productId: existingProduct.id }
        });

        // 関連するブラックアウト日を削除
        await tx.blackoutDate.deleteMany({
          where: { productId: existingProduct.id }
        });

        // 商品本体を削除
        await tx.product.delete({
          where: { id: existingProduct.id }
        });
      });

      console.log(`商品を完全削除しました: ${existingProduct.title}`);
      
      return json({ 
        message: "商品を完全削除しました",
        productId: shopifyProductId,
        productTitle: existingProduct.title
      }, { status: 200 });
    }

  } catch (error) {
    console.error("商品削除Webhook処理エラー:", error);
    
    if (error instanceof Error) {
      console.error("エラーメッセージ:", error.message);
      console.error("スタックトレース:", error.stack);
    }

    return json({ 
      error: "商品削除処理中にエラーが発生しました",
      details: error instanceof Error ? error.message : "不明なエラー"
    }, { status: 500 });
  }
};
