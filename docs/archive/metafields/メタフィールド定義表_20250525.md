# メタフィールド定義表

**作成日**: 2025年5月25日  
**目的**: メタフィールドの定義を分かりやすく整理

## 📊 メタフィールド定義一覧表

### **必要なメタフィールド（推奨15個）**

| 定義名（日本語） | パスキー | タイプ | 用途 | 値の例 | 編集可否 |
|:--|:--|:--|:--|:--|:--:|
| **商品グループ** | `rental.product_group` | single_line_text_field | 同じ形状・デザインの商品をグループ化 | "ベーシックソファ 1シーター" | ✅ |
| **レンタル商品基本情報** | `rental.basic_info` | json | システム用基本データ | {"productCode": "10101031", "detailCode": "001"} | ❌ |
| **レンタル料金設定** | `rental.pricing` | json | 価格計算用データ | {"basePrice": 8000} | ❌ |
| **バリエーションマッピング** | `rental.variant_mapping` | json | バリアントと日数の紐付け | {"variants": [...]} | ❌ |
| **バリエーションタイプ** | `rental.variation_type` | single_line_text_field | バリエーションの種類 | "rental_period" | ❌ |
| **レンタル状態** | `rental.status` | single_line_text_field | 商品の利用可能状態 | "available" | ✅ |
| **在庫場所** | `rental.location` | single_line_text_field | 保管場所 | "NY" | ✅ |
| **色** | `rental.color` | single_line_text_field | 商品の色 | "オフホワイト" | ✅ |
| **素材** | `rental.material` | single_line_text_field | 商品の素材 | "布" | ✅ |
| **高さ** | `product.height` | number_integer | 商品の高さ(cm) | 76 | ✅ |
| **幅** | `product.width` | number_integer | 商品の幅(cm) | 87 | ✅ |
| **奥行き** | `product.depth` | number_integer | 商品の奥行き(cm) | 74 | ✅ |
| **メンテナンス備考** | `rental.maintenance_notes` | multi_line_text_field | メンテナンス情報 | "クリーニング済み..." | ✅ |
| **予約備考** | `rental.booking_notes` | multi_line_text_field | 予約時の注意事項 | "重量物のため2名で..." | ✅ |
| **一般備考** | `rental.general_notes` | multi_line_text_field | 購入・廃棄情報など | "購入日: 2020/01/01..." | ✅ |

### **削除推奨メタフィールド（8個）**

| 定義名（日本語） | パスキー | 理由 | 代替方法 |
|:--|:--|:--|:--|
| **購入場所** | `rental.purchase_place` | 編集頻度低 | general_notesに記載 |
| **購入日** | `rental.purchase_date` | 編集頻度低 | general_notesに記載 |
| **購入価格** | `rental.purchase_price` | 編集頻度低 | general_notesに記載 |
| **廃棄済み** | `rental.is_disposed` | ステータスで管理可能 | statusを"disposed"に |
| **廃棄日** | `rental.disposal_date` | 編集頻度低 | general_notesに記載 |
| **廃棄理由** | `rental.disposal_reason` | 編集頻度低 | general_notesに記載 |
| **レンタル在庫アイテム** | `rental.inventory_items` | statusで十分 | rental.statusで管理 |
| **レンタル予約情報** | `rental.reservation_info` | Bookingテーブルで管理 | 不要 |

## 📝 メタフィールドの詳細説明

### **1. rental.product_group（商品グループ）**
- **用途**: 商品名から抽出した商品のグループ名
- **例**: 
  - "ベーシックソファ 1シーター"
  - "カリモクソファ"
  - "ホガースチェア"
- **理由**: SKUや型番では分かりにくいため、商品名ベースのグループ化

### **2. rental.status（レンタル状態）**
- **available** - 利用可能
- **maintenance** - メンテナンス中
- **reserved** - 予約済み
- **damaged** - 破損
- **disposed** - 廃棄済み（追加案）

### **3. rental.general_notes（一般備考）**
```
購入情報:
購入日: 2020/01/01
購入場所: 東京ショールーム
購入価格: 50,000円

メンテナンス履歴:
2024/12/15 - クリーニング実施
2025/01/10 - 脚部修理

廃棄情報:
廃棄日: 2025/05/01
廃棄理由: 経年劣化により使用不可
```

### **4. rental.variant_mapping（バリエーションマッピング）**
```json
{
  "variants": [
    {
      "variantId": "gid://shopify/ProductVariant/xxx",
      "rentalDays": 1,
      "title": "1日レンタル",
      "sku": "10101031-001-1D",
      "price": 8000
    },
    {
      "variantId": "gid://shopify/ProductVariant/yyy",
      "rentalDays": 2,
      "title": "2日レンタル",
      "sku": "10101031-001-2D",
      "price": 9600
    }
  ]
}
```

## 🔄 商品グループの活用方法

### **フロントエンドでの表示例**
```
商品詳細ページ:
┌─────────────────────────────┐
│ ベーシックソファ 1シーター #001  │
│                             │
│ 同じグループの商品:          │
│ ・ベーシックソファ 1シーター #002 │
│ ・ベーシックソファ 1シーター #003 │
└─────────────────────────────┘
```

### **グループ検索の実装**
```javascript
// 同じ商品グループの商品を検索
const relatedProducts = await prisma.product.findMany({
  where: {
    shop: session.shop,
    metadata: {
      path: ['productGroup'],
      equals: 'ベーシックソファ 1シーター'
    }
  }
});
```

## 💡 メタフィールド運用のポイント

1. **スタッフ編集可能なフィールド（11個）**
   - 単一行テキスト: product_group, status, location, color, material
   - 数値: height, width, depth
   - 複数行テキスト: maintenance_notes, booking_notes, general_notes

2. **システム自動管理フィールド（4個）**
   - JSON形式: basic_info, pricing, variant_mapping
   - 固定値: variation_type

3. **新商品登録時の流れ**
   - CSV一括登録 → 自動フィールド設定
   - 管理画面 → スタッフがproduct_group等を追加

これにより、実用的で管理しやすいメタフィールド構造になります。