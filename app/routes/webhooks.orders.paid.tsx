import { ActionFunctionArgs, json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { logger } from "../utils/logger";
import { BookingService } from "../services/booking.service";

/**
 * 注文支払い完了Webhook
 * POST: Shopifyからの注文支払い完了通知を処理
 *
 * 機能:
 * 1. 関連する予約を確定状態に更新
 * 2. 支払い状況を更新
 * 3. 在庫状態を更新
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  const webhookId = `webhook-orders-paid-${Date.now()}`;

  try {
    logger.info("注文支払い完了Webhook受信開始", { webhookId });

    // Webhookの検証
    const { session } = await authenticate.webhook(request);
    const shop = session.shop;

    // リクエストボディを取得
    const payload = await request.json();
    logger.info("注文支払い完了Webhook受信", {
      webhookId,
      orderId: payload.id,
      orderName: payload.name,
      shop,
      financialStatus: payload.financial_status,
      totalPrice: payload.total_price
    });

    // 注文IDを取得
    const orderId = payload.id.toString();
    if (!orderId) {
      logger.error("注文IDが見つかりません", { webhookId });
      return json({ error: "注文IDが見つかりません" }, { status: 400 });
    }

    // BookingServiceを初期化
    const bookingService = new BookingService();

    // 関連する予約を検索
    const relatedBookings = await findRelatedBookings(orderId, shop, webhookId);

    if (relatedBookings.length === 0) {
      logger.warn("関連する予約が見つかりません", {
        webhookId,
        orderId
      });
      // 予約がない場合でも注文データは更新
      await updateOrderPaymentStatus(payload, shop, webhookId);
      return json({ success: true, message: "予約なし - 注文のみ更新" });
    }

    // 各予約を確定状態に更新
    for (const booking of relatedBookings) {
      await processBookingPaymentCompletion(booking, payload, bookingService, webhookId);
    }

    // 注文データを更新
    await updateOrderPaymentStatus(payload, shop, webhookId);

    logger.info("注文支払い完了Webhook処理完了", {
      webhookId,
      orderId,
      bookingsUpdated: relatedBookings.length
    });

    return json({
      success: true,
      message: "支払い完了処理完了",
      bookingsUpdated: relatedBookings.length
    });
  } catch (error) {
    logger.error("注文支払い完了Webhook処理エラー", {
      webhookId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    return json({ error: "支払い完了処理エラー" }, { status: 500 });
  }
};

/**
 * 関連する予約を検索する
 */
async function findRelatedBookings(orderId: string, shop: string, webhookId: string) {
  try {
    // 1. Shopify注文IDで検索
    let bookings = await prisma.booking.findMany({
      where: {
        shopifyOrderId: orderId,
        shop
      },
      include: {
        product: {
          select: {
            id: true,
            title: true,
            sku: true
          }
        }
      }
    });

    // 2. 古いorderId形式でも検索
    if (bookings.length === 0) {
      bookings = await prisma.booking.findMany({
        where: {
          orderId: orderId,
          shop
        },
        include: {
          product: {
            select: {
              id: true,
              title: true,
              sku: true
            }
          }
        }
      });
    }

    logger.info("関連予約検索結果", {
      webhookId,
      orderId,
      bookingsFound: bookings.length,
      bookingIds: bookings.map(b => b.bookingId)
    });

    return bookings;
  } catch (error) {
    logger.error("関連予約検索エラー", {
      webhookId,
      orderId,
      error: error instanceof Error ? error.message : String(error)
    });
    return [];
  }
}

/**
 * 予約の支払い完了処理
 */
async function processBookingPaymentCompletion(
  booking: any,
  payload: any,
  bookingService: BookingService,
  webhookId: string
) {
  try {
    // 現在のステータスを確認
    const currentStatus = booking.status;
    let newStatus = currentStatus;

    // 支払い完了時のステータス決定
    if (currentStatus === "DRAFT" || currentStatus === "PROVISIONAL") {
      // 仮予約タイプの場合は仮予約のまま、本予約タイプの場合は確定
      newStatus = booking.bookingType === "PROVISIONAL" ? "PROVISIONAL" : "CONFIRMED";
    } else if (currentStatus === "CONFIRMED") {
      // 既に確定済みの場合はそのまま
      newStatus = "CONFIRMED";
    }

    // 予約ステータスを更新
    await bookingService.updateBookingStatus(booking.id, newStatus, {
      orderId: payload.id.toString(),
      orderName: payload.name,
      paymentStatus: "COMPLETED",
      notes: `支払い完了: ${payload.name} - 金額: ${payload.total_price}`
    });

    // 支払い情報を更新
    await bookingService.updatePaymentStatus(booking.id, "COMPLETED", {
      paymentMethod: "CREDIT_CARD", // Shopifyの支払い方法に応じて調整
      notes: `支払い完了: ${payload.name}`,
      paymentDate: new Date()
    });

    logger.info("予約支払い完了処理", {
      webhookId,
      bookingId: booking.bookingId,
      oldStatus: currentStatus,
      newStatus,
      orderId: payload.id,
      totalAmount: payload.total_price
    });
  } catch (error) {
    logger.error("予約支払い完了処理エラー", {
      webhookId,
      bookingId: booking.bookingId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * 注文の支払い状況を更新
 */
async function updateOrderPaymentStatus(payload: any, shop: string, webhookId: string) {
  try {
    await prisma.order.upsert({
      where: {
        shop_shopifyId: {
          shop,
          shopifyId: payload.id.toString()
        }
      },
      update: {
        paymentStatus: payload.financial_status,
        totalAmount: parseFloat(payload.total_price),
        syncStatus: "SYNCED",
        lastSyncedAt: new Date(),
        metadata: {
          ...((await prisma.order.findUnique({
            where: {
              shop_shopifyId: {
                shop,
                shopifyId: payload.id.toString()
              }
            },
            select: { metadata: true }
          }))?.metadata || {}),
          paymentCompletedAt: new Date().toISOString(),
          webhookId
        }
      },
      create: {
        shop,
        shopifyId: payload.id.toString(),
        orderNumber: payload.name,
        customerEmail: payload.email,
        customerName: `${payload.customer?.first_name || ''} ${payload.customer?.last_name || ''}`.trim(),
        totalAmount: parseFloat(payload.total_price),
        paymentStatus: payload.financial_status,
        syncStatus: "SYNCED",
        lastSyncedAt: new Date(),
        metadata: {
          fulfillmentStatus: payload.fulfillment_status,
          currency: payload.currency,
          paymentCompletedAt: new Date().toISOString(),
          webhookId,
          createdAt: payload.created_at
        }
      }
    });

    logger.info("注文支払い状況更新", {
      webhookId,
      orderId: payload.id,
      paymentStatus: payload.financial_status,
      totalAmount: payload.total_price
    });
  } catch (error) {
    logger.error("注文支払い状況更新エラー", {
      webhookId,
      orderId: payload.id,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}