import { PrismaClient } from '@prisma/client';
import { addDays } from 'date-fns';

const prisma = new PrismaClient();

const SHOP = 'peaces-test-block.myshopify.com';

// テスト用商品データ
const TEST_PRODUCTS = [
  {
    title: 'テスト商品1 - ソファ',
    description: 'テスト用のソファです。',
    price: 10000,
    shopifyId: 'test-product-1',
    sku: 'TEST-SOFA-001',
    status: 'AVAILABLE' as const,
    category: '家具',
    basicInfo: {
      manufacturer: 'テストメーカー',
      color: '黒',
      material: '木製',
      size: 'M',
      weight: '5kg'
    },
    depth: 50,
    height: 90,
    width: 45,
    weight: 5000,
  },
  {
    title: 'テスト商品2 - テーブル',
    description: 'テスト用のテーブルです。',
    price: 15000,
    shopifyId: 'test-product-2',
    sku: 'TEST-TABLE-001',
    status: 'AVAILABLE' as const,
    category: '家具',
    basicInfo: {
      manufacturer: 'テストメーカー',
      color: '白',
      material: '木製',
      size: 'L',
      weight: '10kg'
    },
    depth: 80,
    height: 75,
    width: 120,
    weight: 10000,
  },
  {
    title: 'テスト商品3 - 椅子',
    description: 'テスト用の椅子です。',
    price: 8000,
    shopifyId: 'test-product-3',
    sku: 'TEST-CHAIR-001',
    status: 'AVAILABLE' as const,
    category: '家具',
    basicInfo: {
      manufacturer: 'テストメーカー',
      color: 'グレー',
      material: 'ファブリック',
      size: 'S',
      weight: '3kg'
    },
    depth: 40,
    height: 85,
    width: 40,
    weight: 3000,
  },
];

// テスト用予約データ
const TEST_BOOKINGS = [
  {
    bookingId: 'TEST-BOOKING-001',
    customerEmail: '<EMAIL>',
    customerName: '田中 太郎',
    customerPhone: '090-1234-5678',
    customerAddress: '東京都渋谷区渋谷1-1-1',
    status: 'CONFIRMED' as const,
    bookingType: 'CONFIRMED' as const,
    priority: 1,
    totalAmount: 10000,
    depositAmount: 1000,
  },
  {
    bookingId: 'TEST-BOOKING-002',
    customerEmail: '<EMAIL>',
    customerName: '佐藤 花子',
    customerPhone: '090-8765-4321',
    customerAddress: '大阪府大阪市北区梅田2-2-2',
    status: 'PROVISIONAL' as const,
    bookingType: 'PROVISIONAL' as const,
    priority: 2,
    totalAmount: 15000,
    depositAmount: 1500,
  },
  {
    bookingId: 'TEST-BOOKING-003',
    customerEmail: '<EMAIL>',
    customerName: '山田 次郎',
    customerPhone: '090-2345-6789',
    customerAddress: '愛知県名古屋市中区栄3-3-3',
    status: 'CONFIRMED' as const,
    bookingType: 'CONFIRMED' as const,
    priority: 1,
    totalAmount: 8000,
    depositAmount: 800,
  },
];

async function createSimpleTestData() {
  try {
    console.log('簡単なテストデータの作成を開始します...');

    // 既存のテストデータをクリア
    console.log('既存のテストデータをクリアしています...');
    await prisma.booking.deleteMany({ where: { shop: SHOP } });
    await prisma.product.deleteMany({ where: { shop: SHOP } });

    // 商品データを作成
    console.log('商品データを作成しています...');
    const products = [];
    for (const productData of TEST_PRODUCTS) {
      const product = await prisma.product.create({
        data: {
          ...productData,
          shop: SHOP,
        },
      });
      products.push(product);
    }
    console.log(`${products.length}件の商品データを作成しました`);

    // 予約データを作成
    console.log('予約データを作成しています...');
    const today = new Date();
    const bookings = [];

    for (let i = 0; i < TEST_BOOKINGS.length; i++) {
      const bookingData = TEST_BOOKINGS[i];
      const product = products[i];
      const startDate = addDays(today, i + 1);
      const endDate = addDays(startDate, 3);

      const booking = await prisma.booking.create({
        data: {
          ...bookingData,
          productId: product.id,
          startDate: startDate,
          endDate: endDate,
          shop: SHOP,
        },
      });
      bookings.push(booking);
    }
    console.log(`${bookings.length}件の予約データを作成しました`);

    // 作成結果を表示
    console.log('\n=== 作成されたテストデータ ===');
    console.log(`商品: ${products.length}件`);
    console.log(`予約: ${bookings.length}件`);

    console.log('\n商品一覧:');
    products.forEach((product, index) => {
      console.log(`${index + 1}. ${product.title} (${product.sku})`);
    });

    console.log('\n予約一覧:');
    bookings.forEach((booking, index) => {
      console.log(`${index + 1}. ${booking.bookingId} - ${booking.customerName}`);
    });

    console.log('\n簡単なテストデータの作成が完了しました！');

    return { products, bookings };

  } catch (error) {
    console.error('テストデータ作成中にエラーが発生しました:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  createSimpleTestData();
}

export { createSimpleTestData };
