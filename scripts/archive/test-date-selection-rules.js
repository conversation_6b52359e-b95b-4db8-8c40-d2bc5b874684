/**
 * 日付選択ルールテストスクリプト
 *
 * このスクリプトは、UnifiedCalendarの日付選択ルールをテストします。
 * 実行方法: node scripts/test-date-selection-rules.js [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import { addDays, format, isSunday, subDays } from 'date-fns';
import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品情報を取得するクエリ
const GET_PRODUCT = gql`
  query getProduct($id: ID!) {
    product(id: $id) {
      id
      title
      status
      variants(first: 5) {
        edges {
          node {
            id
            title
            sku
            inventoryQuantity
            inventoryPolicy
          }
        }
      }
      metafields(first: 10) {
        edges {
          node {
            namespace
            key
            value
          }
        }
      }
    }
  }
`;

/**
 * 商品情報を取得する関数
 * @param {string} productId 商品ID
 * @returns {Promise<object>} 商品情報
 */
async function getProductInfo(productId) {
  try {
    // Shopify IDを正規化
    const shopifyId = productId.startsWith('gid://shopify/Product/')
      ? productId
      : `gid://shopify/Product/${productId}`;

    console.log(`商品情報を取得中... (Shopify ID: ${shopifyId})`);

    // Shopifyから商品情報を取得
    const result = await shopifyClient.request(GET_PRODUCT, {
      id: shopifyId
    });

    if (!result.product) {
      throw new Error(`商品ID ${productId} が見つかりません`);
    }

    const product = result.product;
    console.log(`商品情報を取得しました: ${product.title}`);

    // データベースから商品情報を取得
    const dbProduct = await prisma.product.findFirst({
      where: {
        shopifyId: productId.replace('gid://shopify/Product/', '')
      }
    });

    if (!dbProduct) {
      throw new Error(`商品ID ${productId} がデータベースに存在しません`);
    }

    return { shopifyProduct: product, dbProduct };
  } catch (error) {
    console.error('商品情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 予約情報を取得する関数
 * @param {string} productId 商品ID
 * @returns {Promise<object>} 予約情報
 */
async function getBookingInfo(productId) {
  try {
    // データベースから予約情報を取得
    const bookings = await prisma.booking.findMany({
      where: {
        productId,
        endDate: {
          gte: new Date()
        }
      },
      orderBy: {
        startDate: 'asc'
      }
    });

    console.log(`予約情報を取得しました: ${bookings.length}件`);
    return bookings;
  } catch (error) {
    console.error('予約情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 日曜日かどうかを判定する関数
 * @param {Date} date 日付
 * @returns {boolean} 日曜日かどうか
 */
function isSundayDate(date) {
  return isSunday(date);
}

/**
 * 祝日かどうかを判定する関数（簡易版）
 * @param {Date} date 日付
 * @returns {boolean} 祝日かどうか
 */
function isJapaneseHoliday(date) {
  // 簡易版のため、特定の日付のみ祝日として扱う
  const holidays = [
    '2025-01-01', // 元日
    '2025-01-13', // 成人の日
    '2025-02-11', // 建国記念日
    '2025-02-23', // 天皇誕生日
    '2025-03-21', // 春分の日
    '2025-04-29', // 昭和の日
    '2025-05-03', // 憲法記念日
    '2025-05-04', // みどりの日
    '2025-05-05', // こどもの日
    '2025-07-21', // 海の日
    '2025-08-11', // 山の日
    '2025-09-15', // 敬老の日
    '2025-09-23', // 秋分の日
    '2025-10-13', // スポーツの日
    '2025-11-03', // 文化の日
    '2025-11-23', // 勤労感謝の日
  ];

  return holidays.includes(format(date, 'yyyy-MM-dd'));
}

/**
 * 年末年始かどうかを判定する関数
 * @param {Date} date 日付
 * @returns {boolean} 年末年始かどうか
 */
function isNewYearHoliday(date) {
  const month = date.getMonth() + 1;
  const day = date.getDate();

  // 12/29〜1/3を年末年始として扱う
  return (month === 12 && day >= 29) || (month === 1 && day <= 3);
}

/**
 * 休業日かどうかを判定する関数
 * @param {Date} date 日付
 * @returns {boolean} 休業日かどうか
 */
function isClosedDay(date) {
  return isSundayDate(date) || isJapaneseHoliday(date) || isNewYearHoliday(date);
}

/**
 * 日付が予約済みかどうかを判定する関数
 * @param {Date} date 日付
 * @param {Array} bookings 予約情報
 * @returns {boolean} 予約済みかどうか
 */
function isDateBooked(date, bookings) {
  const dateStr = format(date, 'yyyy-MM-dd');

  return bookings.some(booking => {
    const startDateStr = format(booking.startDate, 'yyyy-MM-dd');
    const endDateStr = format(booking.endDate, 'yyyy-MM-dd');

    return dateStr >= startDateStr && dateStr <= endDateStr;
  });
}

/**
 * 日付が選択可能かどうかを判定する関数
 * @param {Date} date 日付
 * @param {Array} bookings 予約情報
 * @param {boolean} isProvisional 仮予約かどうか
 * @returns {boolean} 選択可能かどうか
 */
function isDateSelectable(date, bookings, isProvisional = false) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 過去の日付は選択不可
  if (date < today) {
    console.log(`${format(date, 'yyyy-MM-dd')} は過去の日付のため選択不可`);
    return false;
  }

  // 休業日は選択不可
  if (isClosedDay(date)) {
    console.log(`${format(date, 'yyyy-MM-dd')} は休業日のため選択不可`);
    return false;
  }

  // 予約済みの日付は選択不可
  if (isDateBooked(date, bookings)) {
    console.log(`${format(date, 'yyyy-MM-dd')} は予約済みのため選択不可`);
    return false;
  }

  // 仮予約の場合、7日以上先の日付のみ選択可能
  if (isProvisional) {
    const minDate = addDays(today, 7);
    if (date < minDate) {
      console.log(`${format(date, 'yyyy-MM-dd')} は仮予約の最小日数（7日）未満のため選択不可`);
      return false;
    }
  }

  // すべての条件を満たす場合は選択可能
  console.log(`${format(date, 'yyyy-MM-dd')} は選択可能`);
  return true;
}

/**
 * 日付選択ルールをテストする関数
 * @param {Array} bookings 予約情報
 * @returns {object} テスト結果
 */
function testDateSelectionRules(bookings) {
  console.log('日付選択ルールのテストを実行中...');

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 日曜日を取得（次の日曜日）
  const nextSunday = getNextSunday(today);
  console.log(`次の日曜日: ${format(nextSunday, 'yyyy-MM-dd')}`);

  // 祝日を取得
  const holiday = getNextHoliday();
  console.log(`祝日: ${format(holiday, 'yyyy-MM-dd')}`);

  // 年末年始を取得
  const newYear = getNewYearDate();
  console.log(`年末年始: ${format(newYear, 'yyyy-MM-dd')}`);

  // 予約済みの日付を取得
  let bookedDate = null;
  if (bookings.length > 0) {
    bookedDate = new Date(bookings[0].startDate);
    console.log(`予約済みの日付: ${format(bookedDate, 'yyyy-MM-dd')}`);
  }

  // 予約済みでない未来の日付を見つける
  let futureDateShort = addDays(today, 5);
  while (isDateBooked(futureDateShort, bookings) || isClosedDay(futureDateShort)) {
    futureDateShort = addDays(futureDateShort, 1);
  }

  let futureDateLong = addDays(today, 30);
  while (isDateBooked(futureDateLong, bookings) || isClosedDay(futureDateLong)) {
    futureDateLong = addDays(futureDateLong, 1);
  }

  // 仮予約用の日付（7日以上）を見つける
  let provisionalDate = addDays(today, 8);
  while (isDateBooked(provisionalDate, bookings) || isClosedDay(provisionalDate)) {
    provisionalDate = addDays(provisionalDate, 1);
  }

  console.log(`未来の日付（短期）: ${format(futureDateShort, 'yyyy-MM-dd')}`);
  console.log(`未来の日付（長期）: ${format(futureDateLong, 'yyyy-MM-dd')}`);
  console.log(`仮予約用の日付: ${format(provisionalDate, 'yyyy-MM-dd')}`);

  const testCases = [
    { name: '過去の日付', date: subDays(today, 1), expected: false },
    { name: '当日', date: today, expected: true },
    { name: '未来の日付（短期）', date: futureDateShort, expected: true },
    { name: '未来の日付（長期）', date: futureDateLong, expected: true },
    { name: '日曜日', date: nextSunday, expected: false },
    { name: '祝日', date: holiday, expected: false },
    { name: '年末年始', date: newYear, expected: false },
    { name: '仮予約（7日未満）', date: addDays(today, 5), expected: false, isProvisional: true },
    { name: '仮予約（7日以上）', date: provisionalDate, expected: true, isProvisional: true },
    { name: '仮予約（長期）', date: futureDateLong, expected: true, isProvisional: true },
  ];

  // 予約済みの日付をテストケースに追加
  if (bookings.length > 0) {
    const bookedDate = bookings[0].startDate;
    testCases.push({ name: '予約済みの日付', date: bookedDate, expected: false });
  }

  const results = testCases.map(testCase => {
    const result = isDateSelectable(testCase.date, bookings, testCase.isProvisional);
    const passed = result === testCase.expected;

    return {
      ...testCase,
      result,
      passed
    };
  });

  // テスト結果を表示
  console.log('\n=== 日付選択ルールのテスト結果 ===');
  results.forEach(result => {
    console.log(`${result.name}: ${format(result.date, 'yyyy-MM-dd')} - ${result.passed ? '成功' : '失敗'} (期待値: ${result.expected}, 結果: ${result.result})`);
  });

  // 全体の結果
  const allPassed = results.every(result => result.passed);
  console.log(`\n全体の結果: ${allPassed ? '成功' : '失敗'} (${results.filter(r => r.passed).length}/${results.length})`);

  return { success: allPassed, results };
}

/**
 * 次の日曜日を取得する関数
 * @param {Date} date 基準日
 * @returns {Date} 次の日曜日
 */
function getNextSunday(date) {
  let nextSunday = new Date(date);
  while (!isSunday(nextSunday)) {
    nextSunday = addDays(nextSunday, 1);
  }
  return nextSunday;
}

/**
 * 次の祝日を取得する関数（簡易版）
 * @returns {Date} 次の祝日
 */
function getNextHoliday() {
  // 簡易版のため、固定の祝日を返す
  return new Date(2025, 0, 1); // 2025年1月1日（元日）
}

/**
 * 年末年始の日付を取得する関数
 * @returns {Date} 年末年始の日付
 */
function getNewYearDate() {
  // 簡易版のため、固定の年末年始を返す
  return new Date(2024, 11, 29); // 2024年12月29日
}

/**
 * メイン関数
 */
async function main() {
  try {
    // コマンドライン引数から商品IDを取得
    const productId = process.argv[2];

    if (!productId) {
      console.error('商品IDが指定されていません。使用方法: node scripts/test-date-selection-rules.js [商品ID]');
      process.exit(1);
    }

    console.log(`商品ID ${productId} の日付選択ルールをテストします...`);

    // 商品情報を取得
    const product = await getProductInfo(productId);

    // 予約情報を取得
    const bookings = await getBookingInfo(product.dbProduct.id);

    // 日付選択ルールをテスト
    const testResult = testDateSelectionRules(bookings);

    if (testResult.success) {
      console.log('日付選択ルールのテストが成功しました！');
    } else {
      console.error('日付選択ルールのテストが失敗しました');
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
