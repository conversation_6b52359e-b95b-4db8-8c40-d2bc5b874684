const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

async function main() {
  // 問題の商品IDを指定
  const problematicProductId = "YOUR_PROBLEMATIC_PRODUCT_ID";

  // 関連する予約データを検索
  const bookings = await prisma.booking.findMany({
    where: {
      productId: problematicProductId,
    },
  });

  if (bookings.length === 0) {
    console.log("削除対象の予約データが見つかりませんでした");
    return;
  }

  console.log(`削除対象の予約データ: ${bookings.length}件`);

  // 予約データを削除
  const deleteResult = await prisma.booking.deleteMany({
    where: {
      productId: problematicProductId,
    },
  });

  console.log(`${deleteResult.count}件の予約データを削除しました`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
