#!/usr/bin/env node

/**
 * カテゴリAPIの動作確認スクリプト
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:8002';

async function testCategoryAPI() {
  console.log('=== カテゴリAPI動作確認 ===');
  
  try {
    // 1. カテゴリ一覧取得テスト
    console.log('\n1. カテゴリ一覧取得テスト');
    const listResponse = await fetch(`${BASE_URL}/api/categories?action=list`);
    
    if (listResponse.ok) {
      const listData = await listResponse.json();
      console.log(`✅ カテゴリ一覧取得成功: ${listData.categories?.length || 0}件`);
      
      if (listData.categories && listData.categories.length > 0) {
        console.log('上位5カテゴリ:');
        listData.categories.slice(0, 5).forEach(cat => {
          console.log(`  - ${cat.code}: ${cat.name}`);
        });
      }
    } else {
      console.log(`❌ カテゴリ一覧取得失敗: ${listResponse.status}`);
    }
    
    // 2. SKU検証テスト
    console.log('\n2. SKU検証テスト');
    const testSKUs = ['212-05-023', '201-10-001', '999-99-999', 'INVALID-SKU'];
    
    for (const sku of testSKUs) {
      const validateResponse = await fetch(`${BASE_URL}/api/categories?action=validate-sku&sku=${sku}`);
      
      if (validateResponse.ok) {
        const validateData = await validateResponse.json();
        const validation = validateData.validation;
        
        console.log(`SKU: ${sku}`);
        console.log(`  有効: ${validation.isValid ? '✅' : '❌'}`);
        console.log(`  カテゴリ存在: ${validation.categoryExists ? '✅' : '❌'}`);
        console.log(`  サブカテゴリ存在: ${validation.subCategoryExists ? '✅' : '❌'}`);
        
        if (validation.errors && validation.errors.length > 0) {
          console.log(`  エラー: ${validation.errors.join(', ')}`);
        }
      } else {
        console.log(`❌ SKU検証失敗 (${sku}): ${validateResponse.status}`);
      }
    }
    
    // 3. SKU生成テスト
    console.log('\n3. SKU生成テスト');
    const testCategories = [
      { categoryCode: '212', subCategoryCode: '05' },
      { categoryCode: '201', subCategoryCode: '10' },
      { categoryCode: '999', subCategoryCode: '01' }
    ];
    
    for (const { categoryCode, subCategoryCode } of testCategories) {
      const generateResponse = await fetch(
        `${BASE_URL}/api/categories?action=generate-sku&categoryCode=${categoryCode}&subCategoryCode=${subCategoryCode}`
      );
      
      if (generateResponse.ok) {
        const generateData = await generateResponse.json();
        console.log(`✅ SKU生成成功 (${categoryCode}-${subCategoryCode}): ${generateData.sku}`);
      } else {
        console.log(`❌ SKU生成失敗 (${categoryCode}-${subCategoryCode}): ${generateResponse.status}`);
      }
    }
    
    // 4. SKU解析テスト
    console.log('\n4. SKU解析テスト');
    const parseSKU = '212-05-023';
    const parseResponse = await fetch(`${BASE_URL}/api/categories?action=parse-sku&sku=${parseSKU}`);
    
    if (parseResponse.ok) {
      const parseData = await parseResponse.json();
      console.log(`✅ SKU解析成功 (${parseSKU}):`);
      console.log(`  カテゴリコード: ${parseData.skuStructure.categoryCode}`);
      console.log(`  サブカテゴリコード: ${parseData.skuStructure.subCategoryCode}`);
      console.log(`  連番: ${parseData.skuStructure.serialNumber}`);
      
      if (parseData.category) {
        console.log(`  カテゴリ名: ${parseData.category.name}`);
      }
      
      if (parseData.subCategory) {
        console.log(`  サブカテゴリ名: ${parseData.subCategory.name}`);
      }
    } else {
      console.log(`❌ SKU解析失敗 (${parseSKU}): ${parseResponse.status}`);
    }
    
    console.log('\n=== テスト完了 ===');
    
  } catch (error) {
    console.error('テスト実行エラー:', error.message);
  }
}

// 認証が必要な場合のテスト（実際のShopifyアプリ内でのテスト）
async function testBookingAggregateAPI() {
  console.log('\n=== 予約状況一括照会API動作確認 ===');
  
  try {
    // 注意: このテストは認証が必要なため、実際のShopifyアプリ内でのみ動作します
    console.log('このテストはShopifyアプリ内でのみ実行可能です。');
    console.log('ブラウザで以下のURLにアクセスしてテストしてください:');
    console.log('https://app.shopify-app-test.xyz/app/bookings/aggregate');
    
  } catch (error) {
    console.error('テスト実行エラー:', error.message);
  }
}

// メイン実行
async function main() {
  await testCategoryAPI();
  await testBookingAggregateAPI();
}

main();
