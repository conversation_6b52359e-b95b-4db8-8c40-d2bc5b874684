import {
  json,
  type ActionFunctionArgs,
  type LoaderFunctionArgs,
} from "@remix-run/node";
import { useActionData, useSubmit, useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  DatePicker,
  Button,
  Text,
  Box,
  BlockStack,
  Banner,
  Select,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { useState, useCallback } from "react";
import { format } from "date-fns";
import { ja } from "date-fns/locale";
import { prisma } from "../db.server";
import { authenticate } from "../shopify.server";
import PriceCalculator from "@components/Calendar/PriceCalculator";

interface ActionData {
  success?: boolean;
  message?: string;
  error?: string;
}

interface LoaderData {
  products: Array<{
    id: string;
    title: string;
    status: string;
  }>;
}

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const { admin } = await authenticate.admin(request);

    // 利用可能な商品のみを取得
    const products = await prisma.product.findMany({
      where: {
        status: "available",
      },
      select: {
        id: true,
        title: true,
        status: true,
      },
    });

    return json<LoaderData>({ products });
  } catch (error) {
    console.error("Error in loader:", error);
    return json<LoaderData>({ products: [] });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  try {
    const formData = await request.formData();
    const startDate = formData.get("startDate");
    const endDate = formData.get("endDate");
    const productId = formData.get("productId");

    if (!startDate || !endDate || !productId) {
      return json<ActionData>({ error: "商品、開始日、終了日は必須です" });
    }

    // 予約の重複チェック
    const existingBooking = await prisma.booking.findFirst({
      where: {
        productId: productId.toString(),
        OR: [
          {
            AND: [
              { startDate: { lte: new Date(startDate.toString()) } },
              { endDate: { gte: new Date(startDate.toString()) } },
            ],
          },
          {
            AND: [
              { startDate: { lte: new Date(endDate.toString()) } },
              { endDate: { gte: new Date(endDate.toString()) } },
            ],
          },
        ],
      },
    });

    if (existingBooking) {
      return json<ActionData>({
        error: "指定された期間は既に予約されています",
      });
    }

    // 予約を作成
    const booking = await prisma.booking.create({
      data: {
        bookingId: `test-${Date.now()}`,
        status: "draft",
        startDate: new Date(startDate.toString()),
        endDate: new Date(endDate.toString()),
        productId: productId.toString(),
      },
    });

    return json<ActionData>({
      success: true,
      message: `予約が作成されました。予約ID: ${booking.id}`,
    });
  } catch (error) {
    console.error("Booking creation error:", error);
    return json<ActionData>({
      error:
        error instanceof Error ? error.message : "予約の作成に失敗しました",
    });
  }
}

export default function BookingTestNew() {
  const { products } = useLoaderData<LoaderData>();
  const actionData = useActionData<ActionData>();
  const submit = useSubmit();
  const today = new Date();

  // 日付選択の状態
  const [{ month, year }, setDate] = useState({
    month: today.getMonth(),
    year: today.getFullYear(),
  });
  const [dateRange, setDateRange] = useState<{ start: Date; end: Date }>({
    start: today,
    end: today,
  });

  // 商品選択の状態
  const [selectedProductId, setSelectedProductId] = useState("");

  const handleMonthChange = useCallback(
    (month: number, year: number) => setDate({ month, year }),
    [],
  );

  return (
    <Page title="新規予約テスト" backAction={{ content: "戻る", url: "/app" }}>
      <TitleBar title="新規予約テスト" />

      <Layout>
        {actionData?.message && (
          <Layout.Section>
            <Banner title="成功" tone="success">
              <p>{actionData.message}</p>
            </Banner>
          </Layout.Section>
        )}

        {actionData?.error && (
          <Layout.Section>
            <Banner title="エラー" tone="critical">
              <p>{actionData.error}</p>
            </Banner>
          </Layout.Section>
        )}

        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                <Select
                  label="商品を選択"
                  options={[
                    { label: "商品を選択してください", value: "" },
                    ...products.map((product) => ({
                      label: product.title,
                      value: product.id,
                    })),
                  ]}
                  onChange={setSelectedProductId}
                  value={selectedProductId}
                />

                {selectedProductId && (
                  <>
                    <Text variant="headingMd" as="h2">
                      日付選択
                    </Text>

                    <Box paddingBlockStart="300" paddingBlockEnd="300">
                      <DatePicker
                        month={month}
                        year={year}
                        onChange={setDateRange}
                        onMonthChange={handleMonthChange}
                        selected={dateRange}
                        allowRange
                      />
                    </Box>

                    <Box
                      background="bg-surface-secondary"
                      padding="300"
                      borderRadius="100"
                    >
                      <BlockStack gap="200">
                        <Text as="p">
                          開始日:{" "}
                          {format(dateRange.start, "yyyy年MM月dd日 (EEE)", {
                            locale: ja,
                          })}
                        </Text>
                        <Text as="p">
                          終了日:{" "}
                          {format(dateRange.end, "yyyy年MM月dd日 (EEE)", {
                            locale: ja,
                          })}
                        </Text>
                      </BlockStack>
                    </Box>

                    <Box paddingBlockStart="400">
                      <Button
                        onClick={() => {
                          const formData = new FormData();
                          formData.append(
                            "startDate",
                            dateRange.start.toISOString(),
                          );
                          formData.append(
                            "endDate",
                            dateRange.end.toISOString(),
                          );
                          formData.append("productId", selectedProductId);
                          submit(formData, { method: "post" });
                        }}
                        variant="primary"
                      >
                        テスト予約を作成
                      </Button>
                    </Box>
                  </>
                )}
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
