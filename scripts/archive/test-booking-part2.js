/**
 * 予約システムテスト - パート2: 基本的な予約テスト
 * 
 * このスクリプトでは以下を行います：
 * 1. 単一商品の予約
 * 2. 同じ商品の別日程での予約
 * 3. 予約の重複チェック
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// テスト結果を保存する配列
const testResults = [];

/**
 * テスト結果を記録する関数
 */
function logTestResult(testName, success, details = {}) {
  const result = {
    testName,
    success,
    timestamp: new Date().toISOString(),
    ...details
  };
  
  testResults.push(result);
  
  // コンソールにも出力
  console.log(`[${success ? 'SUCCESS' : 'FAILURE'}] ${testName}`);
  if (details.message) {
    console.log(`  ${details.message}`);
  }
  if (details.error) {
    console.error(`  Error: ${details.error}`);
  }
}

/**
 * テスト用商品を取得する関数
 */
async function getTestProducts() {
  console.log("テスト用商品を取得しています...");
  
  try {
    const products = await prisma.product.findMany({
      where: {
        sku: {
          startsWith: 'TEST-'
        }
      }
    });
    
    console.log(`${products.length}件のテスト用商品が見つかりました`);
    
    if (products.length === 0) {
      console.log("テスト用商品が見つかりませんでした。先にパート1のテストを実行してください。");
    } else {
      products.forEach((product, index) => {
        console.log(`商品 ${index + 1}: ${product.title} (ID: ${product.id}, SKU: ${product.sku})`);
      });
    }
    
    return products;
  } catch (error) {
    console.error("テスト用商品の取得中にエラーが発生しました:", error);
    return [];
  }
}

/**
 * 予約を作成する関数
 */
async function createBooking(productId, startDate, endDate, customerEmail, status = "CONFIRMED") {
  try {
    const bookingId = `BOOK-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
    
    const booking = await prisma.booking.create({
      data: {
        productId,
        shopifyProductId: productId, // 同じIDを使用
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        status,
        bookingType: status === "CONFIRMED" ? "CONFIRMED" : "PROVISIONAL",
        customerEmail,
        customerName: "テストユーザー",
        shop: "peaces-test-block.myshopify.com",
        bookingId
      }
    });
    
    // 在庫カレンダーも更新
    const start = new Date(startDate);
    const end = new Date(endDate);
    const dateRange = [];
    
    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      dateRange.push(new Date(date));
    }
    
    for (const date of dateRange) {
      await prisma.inventoryCalendar.upsert({
        where: {
          shop_productId_date: {
            shop: "peaces-test-block.myshopify.com",
            productId,
            date
          }
        },
        update: {
          isAvailable: false,
          unavailableReason: "reserved",
          bookingId: booking.id
        },
        create: {
          shop: "peaces-test-block.myshopify.com",
          productId,
          shopifyProductId: productId,
          date,
          isAvailable: false,
          unavailableReason: "reserved",
          bookingId: booking.id
        }
      });
    }
    
    return booking;
  } catch (error) {
    console.error("予約作成エラー:", error);
    throw error;
  }
}

/**
 * 予約の重複をチェックする関数
 */
async function checkBookingOverlap(productId, startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  const overlappingDates = await prisma.inventoryCalendar.findMany({
    where: {
      productId,
      date: {
        gte: start,
        lte: end
      },
      isAvailable: false
    }
  });
  
  return overlappingDates.length > 0;
}

/**
 * テストケース1: 単一商品の予約
 */
async function testSingleProductBooking(product) {
  console.log(`\nテストケース1: 単一商品「${product.title}」の予約をテストしています...`);
  
  try {
    const startDate = "2025-06-01";
    const endDate = "2025-06-03";
    const customerEmail = "<EMAIL>";
    
    console.log(`予約期間: ${startDate} 〜 ${endDate}`);
    console.log(`顧客メール: ${customerEmail}`);
    
    const booking = await createBooking(
      product.id,
      startDate,
      endDate,
      customerEmail
    );
    
    console.log(`予約が作成されました (ID: ${booking.id}, 予約ID: ${booking.bookingId})`);
    
    logTestResult("単一商品の予約", true, {
      message: `商品「${product.title}」の予約 (${startDate} 〜 ${endDate}) が作成されました`,
      bookingId: booking.id
    });
    
    return booking;
  } catch (error) {
    console.error("単一商品の予約テストエラー:", error);
    
    logTestResult("単一商品の予約", false, {
      error: error.message
    });
    
    return null;
  }
}

/**
 * テストケース2: 同じ商品の別日程での予約
 */
async function testSameProductDifferentDates(product) {
  console.log(`\nテストケース2: 同じ商品「${product.title}」の別日程での予約をテストしています...`);
  
  try {
    const startDate = "2025-06-10";
    const endDate = "2025-06-12";
    const customerEmail = "<EMAIL>";
    
    console.log(`予約期間: ${startDate} 〜 ${endDate}`);
    console.log(`顧客メール: ${customerEmail}`);
    
    const booking = await createBooking(
      product.id,
      startDate,
      endDate,
      customerEmail
    );
    
    console.log(`予約が作成されました (ID: ${booking.id}, 予約ID: ${booking.bookingId})`);
    
    logTestResult("同じ商品の別日程での予約", true, {
      message: `商品「${product.title}」の予約 (${startDate} 〜 ${endDate}) が作成されました`,
      bookingId: booking.id
    });
    
    return booking;
  } catch (error) {
    console.error("同じ商品の別日程での予約テストエラー:", error);
    
    logTestResult("同じ商品の別日程での予約", false, {
      error: error.message
    });
    
    return null;
  }
}

/**
 * テストケース3: 予約の重複チェック
 */
async function testBookingOverlap(product, startDate, endDate) {
  console.log(`\nテストケース3: 予約の重複チェックをテストしています...`);
  
  try {
    console.log(`商品: ${product.title}`);
    console.log(`期間: ${startDate} 〜 ${endDate}`);
    
    const hasOverlap = await checkBookingOverlap(
      product.id,
      startDate,
      endDate
    );
    
    console.log(`重複チェック結果: ${hasOverlap ? '重複あり' : '重複なし'}`);
    
    logTestResult("予約の重複チェック", hasOverlap, {
      message: hasOverlap 
        ? "正しく重複を検出しました" 
        : "重複を検出できませんでした"
    });
    
    return hasOverlap;
  } catch (error) {
    console.error("予約の重複チェックテストエラー:", error);
    
    logTestResult("予約の重複チェック", false, {
      error: error.message
    });
    
    return false;
  }
}

/**
 * メイン関数
 */
async function runTests() {
  console.log("=== 予約システムテスト - パート2: 基本的な予約テスト ===\n");
  
  try {
    // テスト用商品を取得
    const products = await getTestProducts();
    
    if (products.length === 0) {
      console.error("テスト用商品が見つかりません。テストを中止します。");
      return;
    }
    
    // テスト用に最初の商品を使用
    const testProduct = products[0];
    
    // テストケース1: 単一商品の予約
    const booking1 = await testSingleProductBooking(testProduct);
    
    // テストケース2: 同じ商品の別日程での予約
    const booking2 = await testSameProductDifferentDates(testProduct);
    
    // テストケース3: 予約の重複チェック
    if (booking1) {
      const hasOverlap = await testBookingOverlap(
        testProduct,
        booking1.startDate.toISOString().split('T')[0],
        booking1.endDate.toISOString().split('T')[0]
      );
    }
    
    // テスト結果のサマリーを表示
    console.log("\n=== テスト結果サマリー ===");
    const successCount = testResults.filter(r => r.success).length;
    const failureCount = testResults.length - successCount;
    
    console.log(`実行したテスト数: ${testResults.length}`);
    console.log(`成功: ${successCount}`);
    console.log(`失敗: ${failureCount}`);
    console.log("============================\n");
    
    // 詳細なテスト結果を表示
    console.log("詳細なテスト結果:");
    testResults.forEach((result, index) => {
      console.log(`${index + 1}. [${result.success ? 'SUCCESS' : 'FAILURE'}] ${result.testName}`);
      if (result.message) {
        console.log(`   ${result.message}`);
      }
    });
    
  } catch (error) {
    console.error("テスト実行中にエラーが発生しました:", error);
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// テストを実行
runTests();
