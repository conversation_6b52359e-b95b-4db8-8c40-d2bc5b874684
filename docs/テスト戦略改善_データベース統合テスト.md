# テスト戦略改善: データベース統合テスト

## 問題の分析

### 今回発生した問題
- Prismaスキーマに `shopifyOrderId` と `shopifyOrderName` フィールドが追加されたが、データベースマイグレーションが実行されていなかった
- 既存のテストはモックを使用しており、実際のデータベース構造との整合性をチェックしていなかった
- テストデータとして作成された商品が実際のShopifyデータと連動していなかった

### なぜテストで発見されなかったか

1. **テストとデータベースの分離**
   - 既存のテストはモックを使用しており、実際のデータベースに接続していない
   - Prismaスキーマの整合性テストも、実際のデータベース構造ではなくモックで検証していた

2. **統合テストの不足**
   - 実際のデータベースを使用した統合テストが不足していた
   - データベースマイグレーション後の実際の動作確認が行われていなかった

3. **テストデータの管理不備**
   - データベースリセット後に必要なテストデータが自動で復元されない
   - 商品データの存在を前提としたテストが、データの存在確認を行っていない

## 改善策の実装

### 1. 実際のデータベースを使用する統合テスト

**ファイル**: `app/tests/booking/integration.test.ts`

```typescript
/**
 * 統合テスト - 実際のデータベースを使用
 * 
 * このテストは以下の機能をテストします：
 * 1. データベースの実際の構造とPrismaスキーマの整合性
 * 2. 実際のデータを使用した予約プロセス
 * 3. Shopify連携の動作確認
 */
```

**特徴**:
- 実際のPrismaClientを使用
- データベースの実際の構造をテスト
- テストデータの適切なクリーンアップ

### 2. Shopify連携の確認

**ファイル**: `scripts/test-shopify-connection.js`

```javascript
/**
 * Shopify Admin API接続テストスクリプト
 */
```

**機能**:
- Shopify Admin APIへの接続確認
- 実際の商品データの取得
- API権限の確認

### 3. 商品同期の改善

**ファイル**: `scripts/sync-products.js`

**改善点**:
- Shopify API 2025-01バージョンの使用
- 正規化されたショップ名の使用
- メタフィールドの適切な処理

## 新しいテスト戦略

### テストの階層化

1. **ユニットテスト** (既存)
   - 個別の関数やコンポーネントのテスト
   - モックを使用した高速なテスト

2. **統合テスト** (新規追加)
   - 実際のデータベースを使用
   - Prismaスキーマとデータベース構造の整合性確認
   - 実際のデータフローのテスト

3. **E2Eテスト** (既存)
   - ブラウザを使用した完全なユーザーフローのテスト

### テストデータ管理

#### 原則
- **実際のShopifyデータを使用**: モックデータではなく、実際のShopifyから同期されたデータを使用
- **テスト用データの分離**: テスト専用の商品やデータを明確に識別
- **自動クリーンアップ**: テスト後の適切なデータクリーンアップ

#### 実装
```javascript
// テストデータの作成
const testProduct = await prisma.product.create({
  data: {
    title: 'テスト統合商品',
    // ... 実際のShopifyデータ構造に準拠
  },
});

// テスト後のクリーンアップ
afterAll(async () => {
  // 外部キー制約を考慮した順序でクリーンアップ
  await prisma.booking.deleteMany({ /* ... */ });
  await prisma.product.delete({ /* ... */ });
});
```

### CI/CDパイプラインでの統合

1. **マイグレーション確認**
   ```bash
   npx prisma migrate deploy
   npx prisma db push --accept-data-loss
   ```

2. **統合テスト実行**
   ```bash
   npm test app/tests/booking/integration.test.ts
   ```

3. **Shopify連携確認**
   ```bash
   node scripts/test-shopify-connection.js
   ```

## 今後の改善点

### 1. テストデータベースの分離
- 本番データベースとは別のテスト専用データベースの使用
- テスト実行時の自動的なデータベースリセット

### 2. モニタリングの強化
- データベーススキーマの変更検知
- Shopify API接続状態の監視
- テストカバレッジの可視化

### 3. 自動化の拡張
- データベースマイグレーション後の自動テスト実行
- Shopify商品データの定期同期
- テスト結果の自動レポート生成

## 結論

今回の問題を通じて、以下の重要性が明確になりました：

1. **統合テストの必要性**: モックだけでは検出できない問題がある
2. **実際のデータの使用**: テストデータと本番データの整合性確保
3. **継続的な検証**: データベース構造とアプリケーションコードの同期確認

これらの改善により、今後同様の問題を事前に検出し、より信頼性の高いアプリケーションを構築できます。
