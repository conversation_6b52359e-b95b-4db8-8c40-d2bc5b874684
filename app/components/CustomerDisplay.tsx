import React, { useEffect, useState } from 'react';
import { Text, Spinner, Link, Tooltip, Icon } from '@shopify/polaris';
import { CustomerReferenceService, CustomerInfo } from '~/services/customer-reference.service';
import { InfoIcon, ExternalIcon } from '@shopify/polaris-icons';
import { useSubmit } from '@remix-run/react';

/**
 * 顧客表示コンポーネントのプロパティ
 */
interface CustomerDisplayProps {
  customerId?: string;
  fallbackName?: string;
  fallbackEmail?: string;
  session?: any;
  showEmail?: boolean;
  showDetails?: boolean;
  linkToCustomer?: boolean;
}

/**
 * 顧客表示コンポーネント
 * 顧客IDから顧客情報を取得して表示する
 *
 * @deprecated このコンポーネントは非推奨です。代わりに ShopifyCustomerDisplay コンポーネントを使用してください。
 * ShopifyCustomerDisplay コンポーネントは、より多くの機能を提供し、パフォーマンスも向上しています。
 *
 * 使用例:
 * ```tsx
 * <ShopifyCustomerDisplay
 *   customerId={customerId}
 *   fallbackName={fallbackName}
 *   fallbackEmail={fallbackEmail}
 *   shopifyShop={shopifyShop}
 *   showDetails={true}
 *   linkToShopify={true}
 * />
 * ```
 */
export function CustomerDisplay({
  customerId,
  fallbackName,
  fallbackEmail,
  session,
  showEmail = true,
  showDetails = false,
  linkToCustomer = false
}: CustomerDisplayProps) {
  // 状態
  const [customer, setCustomer] = useState<CustomerInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // クライアントサイドのみの処理を行うためのフラグ
  const [isClient, setIsClient] = useState(false);

  // クライアントサイドかどうかを判定
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 顧客情報を取得
  useEffect(() => {
    // サーバーサイドレンダリング時または顧客IDがない場合は何もしない
    if (!isClient || !customerId) {
      return;
    }

    // 顧客情報を取得
    const fetchCustomer = async () => {
      setLoading(true);
      setError(null);

      try {
        console.log(`CustomerDisplay: 顧客情報取得開始 ID=${customerId}`);

        // グローバルなセッション情報を取得
        const globalSession = typeof window !== 'undefined' ? window.ENV?.shopifySession : null;

        // セッション情報がない場合はエラー
        if (!globalSession && !session) {
          console.error('CustomerDisplay: セッション情報がありません');
          throw new Error('セッション情報がありません');
        }

        // 顧客参照サービスを初期化
        const customerService = new CustomerReferenceService(session || globalSession);

        // 顧客情報を取得
        const customerInfo = await customerService.getCustomerById(customerId);

        if (customerInfo) {
          console.log(`CustomerDisplay: 顧客情報取得成功`, customerInfo);
          // 顧客情報を設定
          setCustomer(customerInfo);
        } else {
          console.log(`CustomerDisplay: 顧客情報が見つかりませんでした ID=${customerId}`);
          setError('顧客情報が見つかりませんでした');
        }
      } catch (err) {
        console.error('CustomerDisplay: 顧客情報取得エラー:', err);
        setError('顧客情報の取得に失敗しました');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomer();
  }, [customerId, session, isClient]);

  // ローディング中
  if (loading) {
    return <Spinner size="small" />;
  }

  // エラー
  if (error) {
    return (
      <Tooltip content={error}>
        <Text tone="critical">
          {fallbackName || '不明な顧客'} <Icon source={InfoIcon} />
        </Text>
      </Tooltip>
    );
  }

  // 顧客情報がない場合はフォールバック値を表示
  if (!customer) {
    if (!fallbackName && !fallbackEmail) {
      return <Text tone="subdued">顧客情報なし</Text>;
    }

    return (
      <div>
        <Text>{fallbackName || '名前なし'}</Text>
        {showEmail && fallbackEmail && (
          <div>
            <Text tone="subdued" as="span">{fallbackEmail}</Text>
          </div>
        )}
      </div>
    );
  }

  // 顧客情報を表示
  const customerName = customer.name || `${customer.firstName} ${customer.lastName}`.trim() || '名前なし';
  const customerEmail = customer.email || fallbackEmail || '';

  // 詳細表示なし、リンクなしの場合
  if (!showDetails && !linkToCustomer) {
    return (
      <div>
        <Text>{customerName}</Text>
        {showEmail && customerEmail && (
          <div>
            <Text tone="subdued" as="span">{customerEmail}</Text>
          </div>
        )}
      </div>
    );
  }

  // 詳細表示なし、リンクありの場合
  if (!showDetails && linkToCustomer) {
    return (
      <div>
        <Link url={`/app/customers/${customer.shopifyId}`} external={false}>
          {customerName}
        </Link>
        {showEmail && customerEmail && (
          <div>
            <Text tone="subdued" as="span">{customerEmail}</Text>
          </div>
        )}
      </div>
    );
  }

  // 詳細表示あり、リンクなしの場合
  if (showDetails && !linkToCustomer) {
    return (
      <div>
        <Text fontWeight="bold">{customerName}</Text>
        {showEmail && customerEmail && (
          <div>
            <Text tone="subdued" as="span">{customerEmail}</Text>
          </div>
        )}
        {customer.phone && (
          <div>
            <Text tone="subdued" as="span">{customer.phone}</Text>
          </div>
        )}
        {customer.address && (
          <div>
            <Text tone="subdued" as="span">{customer.address}</Text>
          </div>
        )}
      </div>
    );
  }

  // 詳細表示あり、リンクありの場合
  return (
    <div>
      <Link url={`/app/customers/${customer.shopifyId}`} external={false}>
        {customerName} <Icon source={ExternalIcon} />
      </Link>
      {showEmail && customerEmail && (
        <div>
          <Text tone="subdued" as="span">{customerEmail}</Text>
        </div>
      )}
      {customer.phone && (
        <div>
          <Text tone="subdued" as="span">{customer.phone}</Text>
        </div>
      )}
      {customer.address && (
        <div>
          <Text tone="subdued" as="span">{customer.address}</Text>
        </div>
      )}
    </div>
  );
}
