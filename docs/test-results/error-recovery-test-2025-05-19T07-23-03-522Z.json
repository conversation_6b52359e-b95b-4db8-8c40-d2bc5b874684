{"timestamp": "2025-05-19T07:23:00.976Z", "duration": 2.545, "totalTests": 3, "successCount": 3, "failureCount": 0, "tests": [{"testName": "API障害からの回復テスト", "success": true, "timestamp": "2025-05-19T07:23:01.283Z", "message": "API障害から正常に回復しました", "error": "", "duration": 307}, {"testName": "ネットワーク切断からの回復テスト", "success": true, "timestamp": "2025-05-19T07:23:01.299Z", "message": "ネットワーク切断から正常に回復しました", "error": "", "duration": 15}, {"testName": "データ不整合の自動修復テスト", "success": true, "timestamp": "2025-05-19T07:23:03.514Z", "message": "データ不整合を正常に修復しました", "error": "", "duration": 2215}], "environment": {"nodeVersion": "v20.18.1", "os": "darwin arm64"}, "performance": {"averageTestDuration": 845.6666666666666, "slowestTest": {"name": "データ不整合の自動修復テスト", "duration": 2215}, "fastestTest": {"name": "ネットワーク切断からの回復テスト", "duration": 15}}}