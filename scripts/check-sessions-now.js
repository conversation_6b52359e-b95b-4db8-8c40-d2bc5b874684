import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkSessions() {
  try {
    console.log('セッションテーブルの確認を開始します...');
    
    // セッションの一覧を取得
    const sessions = await prisma.session.findMany();
    
    console.log(`セッション数: ${sessions.length}`);
    
    // セッションの詳細を表示
    sessions.forEach((session, index) => {
      console.log(`\nセッション ${index + 1}:`);
      console.log(`ID: ${session.id}`);
      console.log(`Shop: ${session.shop}`);
      console.log(`State: ${session.state}`);
      console.log(`Is Online: ${session.isOnline}`);
      console.log(`Expires: ${session.expires}`);
      
      // 有効期限が切れているかチェック
      const isExpired = session.expires && new Date(session.expires) < new Date();
      console.log(`有効期限切れ: ${isExpired ? 'はい' : 'いいえ'}`);
    });
    
    console.log('\nセッションテーブルの確認が完了しました！');
  } catch (error) {
    console.error('セッションテーブルの確認中にエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSessions();
