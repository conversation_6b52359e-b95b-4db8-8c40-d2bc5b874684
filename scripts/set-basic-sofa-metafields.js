import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品検索のクエリ
const GET_PRODUCT_BY_TITLE = gql`
  query getProductByTitle($query: String!) {
    products(first: 1, query: $query) {
      edges {
        node {
          id
          title
        }
      }
    }
  }
`;

// メタフィールドの設定
async function setJsonMetafield(productId, namespace, key, jsonValue) {
  const mutation = gql`
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    metafields: [
      {
        ownerId: productId,
        namespace,
        key,
        value: JSON.stringify(jsonValue),
        type: 'json',
      }
    ]
  };

  try {
    const result = await graphQLClient.request(mutation, variables);
    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', result.metafieldsSet.userErrors);
      return false;
    }
    return true;
  } catch (error) {
    console.error(`メタフィールド ${namespace}.${key} 設定エラー:`, error);
    return false;
  }
}

// タイトルで商品を検索（完全一致）
async function getProductByTitle(title) {
  try {
    const result = await graphQLClient.request(GET_PRODUCT_BY_TITLE, {
      query: `title:"${title}"`,
    });

    if (result.products.edges.length === 0) {
      console.error(`タイトル "${title}" の商品が見つかりませんでした`);
      return null;
    }

    // 完全一致を確認
    const matchedProduct = result.products.edges.find(edge => edge.node.title === title);
    if (!matchedProduct) {
      console.error(`タイトル "${title}" の商品が完全一致しませんでした`);
      return null;
    }

    return matchedProduct.node;
  } catch (error) {
    console.error('商品検索エラー:', error);
    return null;
  }
}

// 現在の日付から指定した日数後の日付を取得
function getDateAfterDays(days) {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return date.toISOString().split('T')[0];
}

// ベーシックソファのメタフィールドデータ
const basicSofaMetafields = {
  basic_info: {
    productCode: "10101007",
    detailCode: "001",
    kana: "ベーシックソファオフホワイト1シーターヤマナリ",
    dimensions: {
      width: 87,
      depth: 74,
      height: 76
    },
    seatDimensions: {
      width: 52,
      depth: 54,
      height: 40
    },
    material: "ファブリック",
    color: "オフホワイト",
    maker: "ヤマナリ",
    campaign: "通常商品",
    notes: "2台ともクリーニング済みですが全体的に黄ばみ発生（H30.12.15)"
  },
  pricing: {
    basePrice: 8000,
    depositRate: 0.1,
    discountRules: {
      day2_6_rate: 0.2,
      day7_plus_rate: 0.1
    },
    minimumDays: 1,
    maximumDays: 30
  },
  inventory_items: [
    {
      id: "item-001",
      sku: "10101007-001-1",
      status: "available",
      location: "NY",
      notes: "背面向かって右側うっすら黒いしみ"
    },
    {
      id: "item-002",
      sku: "10101007-001-2",
      status: "maintenance",
      location: "NY",
      notes: "向かって左アーム手前と正面左側に黄色い輪染み有"
    }
  ],
  reservation_info: [
    {
      itemId: "item-001",
      reservations: [
        {
          id: "reservation-001",
          startDate: getDateAfterDays(5),
          endDate: getDateAfterDays(8),
          status: "confirmed",
          customerName: "山田太郎",
          customerEmail: "<EMAIL>",
          orderId: "gid://shopify/Order/1001",
          orderLineItemId: "gid://shopify/LineItem/1001",
          notes: "特になし"
        },
        {
          id: "reservation-002",
          startDate: getDateAfterDays(15),
          endDate: getDateAfterDays(20),
          status: "pending",
          customerName: "佐藤花子",
          customerEmail: "<EMAIL>",
          orderId: "gid://shopify/Order/1002",
          orderLineItemId: "gid://shopify/LineItem/1002",
          notes: "配送希望"
        }
      ]
    },
    {
      itemId: "item-002",
      reservations: []
    }
  ]
};

// メイン処理
async function setBasicSofaMetafields() {
  console.log('ベーシックソファのメタフィールドを設定しています...');
  
  // 商品を検索
  const product = await getProductByTitle("【正しい実装】ベーシックソファ オフホワイト 1シーター");
  if (!product) {
    console.error('ベーシックソファの商品が見つかりませんでした。');
    return;
  }
  
  console.log(`商品を見つけました: ${product.title} (${product.id})`);
  
  // 基本情報を設定
  console.log('基本情報を設定中...');
  const basicInfoResult = await setJsonMetafield(
    product.id,
    'rental',
    'basic_info',
    basicSofaMetafields.basic_info
  );
  
  if (basicInfoResult) {
    console.log('基本情報を設定しました');
  } else {
    console.error('基本情報の設定に失敗しました');
  }
  
  // 料金設定を設定
  console.log('料金設定を設定中...');
  const pricingResult = await setJsonMetafield(
    product.id,
    'rental',
    'pricing',
    basicSofaMetafields.pricing
  );
  
  if (pricingResult) {
    console.log('料金設定を設定しました');
  } else {
    console.error('料金設定の設定に失敗しました');
  }
  
  // 在庫アイテム情報を設定
  console.log('在庫アイテム情報を設定中...');
  const inventoryItemsResult = await setJsonMetafield(
    product.id,
    'rental',
    'inventory_items',
    basicSofaMetafields.inventory_items
  );
  
  if (inventoryItemsResult) {
    console.log('在庫アイテム情報を設定しました');
  } else {
    console.error('在庫アイテム情報の設定に失敗しました');
  }
  
  // 予約情報を設定
  console.log('予約情報を設定中...');
  const reservationResult = await setJsonMetafield(
    product.id,
    'rental',
    'reservation_info',
    basicSofaMetafields.reservation_info
  );
  
  if (reservationResult) {
    console.log('予約情報を設定しました');
  } else {
    console.error('予約情報の設定に失敗しました');
  }
  
  console.log('\n=== 設定完了 ===');
}

// 実行
setBasicSofaMetafields();
