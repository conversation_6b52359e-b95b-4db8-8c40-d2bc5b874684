import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

// Shopify API設定
const SHOPIFY_SHOP = process.env.SHOPIFY_SHOP;
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

/**
 * 商品のバリアントと在庫情報を取得
 */
async function getProductVariantsWithInventory(productId: string) {
  const query = `
    query getProductVariants($id: ID!) {
      product(id: $id) {
        id
        title
        handle
        variants(first: 20) {
          edges {
            node {
              id
              title
              sku
              price
              inventoryQuantity
              inventoryItem {
                id
                tracked
                inventoryLevels(first: 10) {
                  edges {
                    node {
                      id
                      available
                      location {
                        id
                        name
                      }
                    }
                  }
                }
              }
            }
          }
        }
        metafields(first: 20) {
          edges {
            node {
              id
              namespace
              key
              value
              type
            }
          }
        }
      }
    }
  `;

  const response = await fetch(`https://${SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN!
    },
    body: JSON.stringify({ query, variables: { id: `gid://shopify/Product/${productId}` } })
  });

  const result = await response.json();
  return result.data?.product;
}

/**
 * 在庫を設定するMutation
 */
async function setInventoryQuantity(inventoryItemId: string, locationId: string, quantity: number) {
  const mutation = `
    mutation inventorySetOnHandQuantities($input: InventorySetOnHandQuantitiesInput!) {
      inventorySetOnHandQuantities(input: $input) {
        inventoryAdjustmentGroup {
          id
          reason
          changes {
            name
            delta
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const input = {
    reason: "correction",
    setQuantities: [{
      inventoryItemId: inventoryItemId,
      locationId: locationId,
      quantity: quantity
    }]
  };

  const response = await fetch(`https://${SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN!
    },
    body: JSON.stringify({ mutation, variables: { input } })
  });

  const result = await response.json();
  return result.data?.inventorySetOnHandQuantities;
}

/**
 * 在庫状況をチェックして修正
 */
async function checkAndFixInventory() {
  console.log('=== バリアント在庫チェック ===\n');

  try {
    // 最新の商品を取得
    const products = await prisma.product.findMany({
      where: {
        shop: `${SHOPIFY_SHOP?.replace('.myshopify.com', '')}.myshopify.com`,
        status: 'AVAILABLE'
      },
      take: 3,
      orderBy: { createdAt: 'desc' }
    });

    console.log(`${products.length}件の商品をチェックします\n`);

    for (const product of products) {
      console.log(`\n商品: ${product.title}`);
      console.log(`Shopify ID: ${product.shopifyId}`);

      const shopifyProduct = await getProductVariantsWithInventory(product.shopifyId);
      
      if (!shopifyProduct) {
        console.log('  ❌ Shopify商品が見つかりません');
        continue;
      }

      // メタフィールドを確認
      console.log('\n  📋 メタフィールド:');
      const metafields = shopifyProduct.metafields.edges.map((edge: any) => edge.node);
      const importantFields = ['variant_mapping', 'pricing', 'status', 'product_group'];
      
      importantFields.forEach(key => {
        const field = metafields.find((mf: any) => mf.key === key);
        if (field) {
          console.log(`    ✅ ${key}: ${field.type === 'json' ? '設定済み' : field.value}`);
        } else {
          console.log(`    ❌ ${key}: 未設定`);
        }
      });

      // バリアントと在庫を確認
      console.log('\n  📦 バリアント在庫状況:');
      const variants = shopifyProduct.variants.edges.map((edge: any) => edge.node);
      
      let needsFix = false;
      const inventoryUpdates = [];

      for (const variant of variants) {
        const inventoryLevels = variant.inventoryItem?.inventoryLevels?.edges || [];
        const totalInventory = inventoryLevels.reduce((sum: number, edge: any) => 
          sum + (edge.node.available || 0), 0
        );

        console.log(`\n    ${variant.title} (${variant.sku})`);
        console.log(`      価格: ¥${variant.price}`);
        console.log(`      在庫数: ${totalInventory}`);

        if (inventoryLevels.length > 0) {
          inventoryLevels.forEach((edge: any) => {
            const level = edge.node;
            console.log(`        - ${level.location.name}: ${level.available}個`);
            
            // 在庫が0の場合は修正対象
            if (level.available === 0) {
              needsFix = true;
              inventoryUpdates.push({
                variantTitle: variant.title,
                inventoryItemId: variant.inventoryItem.id,
                locationId: level.location.id,
                locationName: level.location.name
              });
            }
          });
        }
      }

      // 在庫修正の提案
      if (needsFix && inventoryUpdates.length > 0) {
        console.log('\n  ⚠️  在庫が0のバリアントが見つかりました');
        console.log('  推奨: 全バリアントの在庫を1に設定');
        
        // 修正を実行するかの確認（実際にはコメントアウト）
        console.log('\n  修正するには以下のコードを実行:');
        inventoryUpdates.forEach(update => {
          console.log(`  // await setInventoryQuantity('${update.inventoryItemId}', '${update.locationId}', 1);`);
        });
      } else {
        console.log('\n  ✅ 全バリアントに在庫があります');
      }
    }

    // 予約機能のテスト準備状況
    console.log('\n\n=== 予約機能テスト準備状況 ===');
    
    const readyProducts = products.filter(p => p.status === 'AVAILABLE');
    console.log(`\n予約可能な商品数: ${readyProducts.length}`);
    
    if (readyProducts.length > 0) {
      console.log('\n予約テストに使用できる商品:');
      readyProducts.forEach(product => {
        console.log(`  - ${product.title} (ID: ${product.id})`);
      });
      
      console.log('\n予約テストを実行するには:');
      console.log('  npx tsx scripts/create-test-booking.ts');
    }

  } catch (error) {
    console.error('エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 在庫設定のデモ関数
async function demonstrateInventoryFix() {
  console.log('\n\n=== 在庫設定方法のデモ ===');
  console.log(`
// 特定のバリアントの在庫を1に設定する方法:
const result = await setInventoryQuantity(
  'gid://shopify/InventoryItem/xxx',  // inventoryItemId
  'gid://shopify/Location/yyy',       // locationId
  1                                    // 数量
);

// 全バリアントの在庫を一括設定する場合は、
// 各バリアントに対して上記を実行
  `);
}

// メイン実行
async function main() {
  try {
    await checkAndFixInventory();
    await demonstrateInventoryFix();
  } catch (error) {
    console.error('エラー:', error);
  }
}

main();