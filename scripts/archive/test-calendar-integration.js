/**
 * カレンダーコンポーネントと日付選択の整合性テスト
 *
 * このスクリプトでは以下をテストします：
 * 1. カレンダーコンポーネントの日付選択ロジック
 * 2. 選択された日付の有効性検証
 * 3. 日付選択と料金計算の連携
 */

const { PrismaClient } = require('@prisma/client');

// モジュールを直接インポートできないため、モックデータを使用
// 実際のアプリケーションコードの代わりに、テスト用の関数を定義

/**
 * 開始日が選択可能かどうかを判定する関数（モック）
 */
function isStartDateSelectable(date, bookings) {
  // 日曜日は選択不可
  if (date.getDay() === 0) {
    return false;
  }

  // 予約済みの日は選択不可
  const isBooked = bookings.some(booking => {
    const startDate = new Date(booking.startDate);
    const endDate = new Date(booking.endDate);

    return date >= startDate && date <= endDate;
  });

  return !isBooked;
}

/**
 * 終了日が選択可能かどうかを判定する関数（モック）
 */
function isEndDateSelectable(date, startDate, bookings) {
  // 開始日より前の日付は選択不可
  if (date < startDate) {
    return false;
  }

  // 日曜日は選択不可
  if (date.getDay() === 0) {
    return false;
  }

  // 開始日から終了日までの間に予約済みの日があれば選択不可
  const dateRange = getDatesInRange({ start: startDate, end: date });
  return !dateRange.some(d => {
    return bookings.some(booking => {
      const bookingStartDate = new Date(booking.startDate);
      const bookingEndDate = new Date(booking.endDate);

      return d >= bookingStartDate && d <= bookingEndDate;
    });
  });
}

/**
 * 日付範囲内のすべての日付を取得する関数（モック）
 */
function getDatesInRange(range) {
  const dates = [];
  const currentDate = new Date(range.start);

  while (currentDate <= range.end) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
}

/**
 * 日付範囲の有効性を検証する関数（モック）
 */
function validateDateRange(startDate, endDate, bookings) {
  // 開始日が終了日より後の場合
  if (startDate > endDate) {
    return {
      valid: false,
      message: '開始日は終了日より前の日付を選択してください'
    };
  }

  // 開始日が日曜日の場合
  if (startDate.getDay() === 0) {
    return {
      valid: false,
      message: '開始日に日曜日は選択できません'
    };
  }

  // 終了日が日曜日の場合
  if (endDate.getDay() === 0) {
    return {
      valid: false,
      message: '終了日に日曜日は選択できません'
    };
  }

  // 予約済み日付との重複チェック
  const dateRange = getDatesInRange({ start: startDate, end: endDate });
  const hasBookedDate = dateRange.some(date => {
    return bookings.some(booking => {
      const bookingStartDate = new Date(booking.startDate);
      const bookingEndDate = new Date(booking.endDate);

      return date >= bookingStartDate && date <= bookingEndDate;
    });
  });

  if (hasBookedDate) {
    return {
      valid: false,
      message: '選択された期間に予約済みの日付が含まれています'
    };
  }

  return { valid: true };
}

/**
 * レンタル料金計算関数（モック）
 */
function calculateRentalPrice(startDate, endDate, basePrice) {
  // 日曜日を除いた日数を計算
  let effectiveDays = 0;
  const currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    if (currentDate.getDay() !== 0) {
      effectiveDays++;
    }

    currentDate.setDate(currentDate.getDate() + 1);
  }

  const totalDays = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

  let totalPrice = 0;
  const priceBreakdown = [];

  if (effectiveDays >= 1) {
    // 1日目は基本料金
    totalPrice += basePrice;
    priceBreakdown.push({
      day: 1,
      price: basePrice
    });

    // 2-6日目は基本料金の20%
    if (effectiveDays > 1) {
      const day2to6 = Math.min(5, effectiveDays - 1);
      const day2to6Price = basePrice * day2to6 * 0.2;
      totalPrice += day2to6Price;

      // 料金内訳に追加
      for (let day = 2; day <= Math.min(6, effectiveDays); day++) {
        priceBreakdown.push({
          day,
          price: basePrice * 0.2
        });
      }

      // 7日目以降は基本料金の10%
      if (effectiveDays > 6) {
        const day7PlusCount = effectiveDays - 6;
        const day7PlusPrice = basePrice * day7PlusCount * 0.1;
        totalPrice += day7PlusPrice;

        // 料金内訳に追加
        for (let day = 7; day <= effectiveDays; day++) {
          priceBreakdown.push({
            day,
            price: basePrice * 0.1
          });
        }
      }
    }
  }

  // 整数に丸める
  totalPrice = Math.round(totalPrice);

  // 税込み金額を計算
  const taxInclusivePrice = Math.round(totalPrice * 1.1);

  // デポジット金額を計算
  const depositAmount = Math.floor(totalPrice * 0.1);

  return {
    totalPrice,
    taxInclusivePrice,
    depositAmount,
    totalDays,
    effectiveDays,
    priceBreakdown
  };
}

const prisma = new PrismaClient();

// テスト結果を保存する配列
const testResults = [];

/**
 * テスト結果を記録する関数
 */
function logTestResult(testName, success, details = {}) {
  const result = {
    testName,
    success,
    timestamp: new Date().toISOString(),
    ...details
  };

  testResults.push(result);

  // コンソールにも出力
  console.log(`[${success ? 'SUCCESS' : 'FAILURE'}] ${testName}`);
  if (details.message) {
    console.log(`  ${details.message}`);
  }
  if (details.error) {
    console.error(`  Error: ${details.error}`);
  }
}

/**
 * 開始日選択のテスト
 */
async function testStartDateSelection() {
  console.log("\nテストケース1: 開始日選択のテスト");

  try {
    // 既存の予約を取得
    const bookings = await prisma.booking.findMany({
      where: {
        status: 'CONFIRMED'
      },
      take: 10
    });

    console.log(`既存の予約: ${bookings.length}件`);

    // 今後30日間の日付を生成
    const today = new Date();
    const dates = [];

    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(date);
    }

    // 各日付について開始日選択可能かどうかを判定
    const results = dates.map(date => {
      const isSelectable = isStartDateSelectable(date, bookings);

      // 日付をYYYY-MM-DD形式に変換
      const dateStr = date.toISOString().split('T')[0];

      console.log(`  ${dateStr}: 開始日として選択可能=${isSelectable}`);

      return {
        date,
        isSelectable
      };
    });

    // 選択可能な日付の数
    const selectableDatesCount = results.filter(result => result.isSelectable).length;

    logTestResult("開始日選択", selectableDatesCount > 0, {
      message: `${selectableDatesCount}/${dates.length}日が開始日として選択可能です`
    });

    return results;
  } catch (error) {
    logTestResult("開始日選択", false, {
      error: error.message
    });

    return [];
  }
}

/**
 * 終了日選択のテスト
 */
async function testEndDateSelection(startDateResults) {
  console.log("\nテストケース2: 終了日選択のテスト");

  try {
    // 既存の予約を取得
    const bookings = await prisma.booking.findMany({
      where: {
        status: 'CONFIRMED'
      },
      take: 10
    });

    // 選択可能な開始日を取得
    const selectableStartDates = startDateResults.filter(result => result.isSelectable);

    if (selectableStartDates.length === 0) {
      console.log("選択可能な開始日がありません");

      logTestResult("終了日選択", false, {
        message: "選択可能な開始日がありません"
      });

      return [];
    }

    // 最初の選択可能な開始日を使用
    const startDate = selectableStartDates[0].date;
    console.log(`開始日: ${startDate.toISOString().split('T')[0]}`);

    // 開始日から30日間の日付を生成
    const dates = [];

    for (let i = 0; i < 30; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      dates.push(date);
    }

    // 各日付について終了日選択可能かどうかを判定
    const results = dates.map(date => {
      const isSelectable = isEndDateSelectable(date, startDate, bookings);

      // 日付をYYYY-MM-DD形式に変換
      const dateStr = date.toISOString().split('T')[0];

      console.log(`  ${dateStr}: 終了日として選択可能=${isSelectable}`);

      return {
        date,
        isSelectable
      };
    });

    // 選択可能な日付の数
    const selectableDatesCount = results.filter(result => result.isSelectable).length;

    logTestResult("終了日選択", selectableDatesCount > 0, {
      message: `${selectableDatesCount}/${dates.length}日が終了日として選択可能です`
    });

    return {
      startDate,
      endDateResults: results
    };
  } catch (error) {
    logTestResult("終了日選択", false, {
      error: error.message
    });

    return null;
  }
}

/**
 * 日付範囲の有効性検証テスト
 */
async function testDateRangeValidation(selectionResults) {
  console.log("\nテストケース3: 日付範囲の有効性検証テスト");

  try {
    if (!selectionResults || !selectionResults.startDate || !selectionResults.endDateResults) {
      console.log("日付選択結果がありません");

      logTestResult("日付範囲の有効性検証", false, {
        message: "日付選択結果がありません"
      });

      return null;
    }

    // 既存の予約を取得
    const bookings = await prisma.booking.findMany({
      where: {
        status: 'CONFIRMED'
      },
      take: 10
    });

    const { startDate, endDateResults } = selectionResults;

    // 選択可能な終了日を取得
    const selectableEndDates = endDateResults.filter(result => result.isSelectable);

    if (selectableEndDates.length === 0) {
      console.log("選択可能な終了日がありません");

      logTestResult("日付範囲の有効性検証", false, {
        message: "選択可能な終了日がありません"
      });

      return null;
    }

    // 最初の選択可能な終了日を使用
    const endDate = selectableEndDates[0].date;

    console.log(`日付範囲: ${startDate.toISOString().split('T')[0]} 〜 ${endDate.toISOString().split('T')[0]}`);

    // 日付範囲の有効性を検証
    const validationResult = validateDateRange(startDate, endDate, bookings);

    console.log(`  有効性: ${validationResult.valid}`);
    if (!validationResult.valid && validationResult.message) {
      console.log(`  エラーメッセージ: ${validationResult.message}`);
    }

    // 日付範囲内の日付を取得
    const datesInRange = getDatesInRange({ start: startDate, end: endDate });
    console.log(`  日付範囲内の日数: ${datesInRange.length}日`);

    logTestResult("日付範囲の有効性検証", validationResult.valid, {
      message: validationResult.valid
        ? `日付範囲 ${startDate.toISOString().split('T')[0]} 〜 ${endDate.toISOString().split('T')[0]} は有効です`
        : `日付範囲 ${startDate.toISOString().split('T')[0]} 〜 ${endDate.toISOString().split('T')[0]} は無効です: ${validationResult.message}`
    });

    return {
      startDate,
      endDate,
      validationResult
    };
  } catch (error) {
    logTestResult("日付範囲の有効性検証", false, {
      error: error.message
    });

    return null;
  }
}

/**
 * 日付選択と料金計算の連携テスト
 */
async function testDatePriceIntegration(validationResults) {
  console.log("\nテストケース4: 日付選択と料金計算の連携テスト");

  try {
    if (!validationResults || !validationResults.startDate || !validationResults.endDate || !validationResults.validationResult.valid) {
      console.log("有効な日付範囲がありません");

      logTestResult("日付選択と料金計算の連携", false, {
        message: "有効な日付範囲がありません"
      });

      return null;
    }

    const { startDate, endDate } = validationResults;

    // テスト用の商品を取得
    const testProduct = await prisma.product.findFirst({
      where: {
        sku: {
          startsWith: 'TEST-'
        }
      }
    });

    if (!testProduct) {
      console.log("テスト用の商品が見つかりませんでした");

      logTestResult("日付選択と料金計算の連携", false, {
        message: "テスト用の商品が見つかりませんでした"
      });

      return null;
    }

    // 基本料金
    const basePrice = testProduct.price || 5000;

    // 料金計算
    const calculationResult = calculateRentalPrice(startDate, endDate, basePrice);

    console.log(`商品: ${testProduct.title} (基本料金: ${basePrice}円)`);
    console.log(`日付範囲: ${startDate.toISOString().split('T')[0]} 〜 ${endDate.toISOString().split('T')[0]}`);
    console.log(`計算結果: ${calculationResult.totalPrice}円 (税込: ${calculationResult.taxInclusivePrice}円)`);
    console.log(`有効日数: ${calculationResult.effectiveDays}日 / 総日数: ${calculationResult.totalDays}日`);

    // 料金内訳を表示
    console.log(`料金内訳:`);
    calculationResult.priceBreakdown.forEach(item => {
      console.log(`  ${item.day}日目: ${item.price}円`);
    });

    logTestResult("日付選択と料金計算の連携", true, {
      message: `日付範囲 ${startDate.toISOString().split('T')[0]} 〜 ${endDate.toISOString().split('T')[0]} の料金計算が完了しました: ${calculationResult.totalPrice}円`
    });

    return {
      startDate,
      endDate,
      product: testProduct,
      calculationResult
    };
  } catch (error) {
    logTestResult("日付選択と料金計算の連携", false, {
      error: error.message
    });

    return null;
  }
}

/**
 * メイン関数
 */
async function runTests() {
  console.log("=== カレンダーコンポーネントと日付選択の整合性テスト ===\n");

  try {
    // 開始日選択のテスト
    const startDateResults = await testStartDateSelection();

    // 終了日選択のテスト
    const endDateResults = await testEndDateSelection(startDateResults);

    // 日付範囲の有効性検証テスト
    const validationResults = await testDateRangeValidation(endDateResults);

    // 日付選択と料金計算の連携テスト
    const integrationResults = await testDatePriceIntegration(validationResults);

    // テスト結果のサマリーを表示
    console.log("\n=== テスト結果サマリー ===");
    const successCount = testResults.filter(r => r.success).length;
    const failureCount = testResults.length - successCount;

    console.log(`実行したテスト数: ${testResults.length}`);
    console.log(`成功: ${successCount}`);
    console.log(`失敗: ${failureCount}`);
    console.log("============================\n");

    // 詳細なテスト結果を表示
    console.log("詳細なテスト結果:");
    testResults.forEach((result, index) => {
      console.log(`${index + 1}. [${result.success ? 'SUCCESS' : 'FAILURE'}] ${result.testName}`);
      if (result.message) {
        console.log(`   ${result.message}`);
      }
    });

  } catch (error) {
    console.error("テスト実行中にエラーが発生しました:", error);
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// テストを実行
runTests();
