/**
 * 商品同期ジョブ
 * 
 * Shopifyの商品データをデータベースに定期的に同期するジョブ
 */

import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import { formatSyncError } from '../utils/sync/error-handler';

const prisma = new PrismaClient();

// GraphQL APIクライアントの設定
function getShopifyClient() {
  return new GraphQLClient(
    `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
    {
      headers: {
        'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
        'Content-Type': 'application/json',
      },
    }
  );
}

// 商品検索のGraphQLクエリ
const SEARCH_PRODUCTS = gql`
  query searchProducts($query: String!, $first: Int!, $after: String) {
    products(first: $first, after: $after, query: $query) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          handle
          status
          description
          variants(first: 5) {
            edges {
              node {
                id
                title
                sku
                price
                inventoryQuantity
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    }
  }
`;

/**
 * 商品同期ジョブを実行する関数
 * @returns 同期結果
 */
export async function syncProducts() {
  console.log('商品同期ジョブを開始します...');
  
  const result = {
    success: true,
    total: 0,
    created: 0,
    updated: 0,
    skipped: 0,
    errors: 0,
    errorDetails: [] as any[],
  };

  try {
    // 最終同期時刻を取得
    const lastSyncSetting = await prisma.appSettings.findFirst({
      where: {
        key: 'last_product_sync',
      },
    });

    const lastSyncTime = lastSyncSetting ? new Date(lastSyncSetting.value as string) : new Date(0);
    const now = new Date();
    
    // 最終同期から24時間以内なら同期をスキップ（テスト用に短い時間に設定可能）
    const syncInterval = process.env.SYNC_INTERVAL_HOURS ? parseInt(process.env.SYNC_INTERVAL_HOURS) : 24;
    const shouldSync = now.getTime() - lastSyncTime.getTime() > syncInterval * 60 * 60 * 1000;
    
    if (!shouldSync) {
      console.log(`前回の同期から${syncInterval}時間経過していないため、同期をスキップします。`);
      return {
        success: true,
        total: 0,
        created: 0,
        updated: 0,
        skipped: 0,
        errors: 0,
        message: `前回の同期から${syncInterval}時間経過していないため、同期をスキップしました。`,
      };
    }

    // Shopify GraphQL APIを使用して商品を検索
    const shopifyClient = getShopifyClient();
    let hasNextPage = true;
    let cursor = null;
    let allProducts: any[] = [];

    // ページネーションで全商品を取得
    while (hasNextPage) {
      const response = await shopifyClient.request(SEARCH_PRODUCTS, {
        query: 'status:active', // アクティブな商品のみ
        first: 50,
        after: cursor,
      });

      const products = response.products.edges.map((edge: any) => edge.node);
      allProducts = [...allProducts, ...products];
      
      hasNextPage = response.products.pageInfo.hasNextPage;
      cursor = response.products.pageInfo.endCursor;
      
      // 次のページがある場合は少し待機（API制限対策）
      if (hasNextPage) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    result.total = allProducts.length;
    console.log(`${allProducts.length}件の商品を取得しました。同期を開始します...`);

    // 各商品を処理
    for (const shopifyProduct of allProducts) {
      try {
        const shopifyId = shopifyProduct.id.replace('gid://shopify/Product/', '');
        
        // データベースに既存の商品があるか確認
        const existingProduct = await prisma.product.findFirst({
          where: {
            shopifyId,
          },
        });

        // バリアント情報を取得
        const variant = shopifyProduct.variants.edges[0]?.node;
        // SKUに商品IDを追加して一意性を確保
        const sku = variant?.sku ? `${variant.sku}-${shopifyId}` : `SKU-${shopifyId}`;
        const price = parseFloat(variant?.price || '0');
        
        // メタフィールドを解析
        const metafieldsData = shopifyProduct.metafields.edges.map((edge: any) => ({
          key: edge.node.key,
          value: edge.node.value,
          namespace: edge.node.namespace,
        }));
        
        // メタデータとして保存
        const metadata = {
          metafields: metafieldsData,
          lastSync: now.toISOString(),
        };
        
        // 基本情報を設定
        const basicInfo = {
          productCode: sku,
          status: shopifyProduct.status === 'ACTIVE' ? 'available' : 'unavailable',
          location: 'NY', // デフォルト値
        };

        if (existingProduct) {
          // 既存の商品を更新
          await prisma.product.update({
            where: {
              id: existingProduct.id,
            },
            data: {
              title: shopifyProduct.title,
              description: shopifyProduct.description,
              status: shopifyProduct.status === 'ACTIVE' ? 'AVAILABLE' : 'UNAVAILABLE',
              sku,
              price,
              metadata,
              basicInfo,
              lastSyncedAt: now,
              updatedAt: now,
            },
          });
          result.updated++;
        } else {
          // 新規商品を作成
          await prisma.product.create({
            data: {
              shop: process.env.SHOPIFY_SHOP || '',
              shopifyId,
              title: shopifyProduct.title,
              description: shopifyProduct.description,
              status: shopifyProduct.status === 'ACTIVE' ? 'AVAILABLE' : 'UNAVAILABLE',
              sku,
              price,
              metadata,
              basicInfo,
              lastSyncedAt: now,
            },
          });
          result.created++;
        }
      } catch (error) {
        console.error(`商品「${shopifyProduct.title}」の同期中にエラーが発生しました:`, error);
        result.errors++;
        result.errorDetails.push({
          title: shopifyProduct.title,
          id: shopifyProduct.id,
          error: formatSyncError(error),
        });
      }
    }

    // 最終同期時刻を更新
    await prisma.appSettings.upsert({
      where: {
        shop_key: {
          shop: process.env.SHOPIFY_SHOP || '',
          key: 'last_product_sync',
        },
      },
      update: {
        value: now.toISOString(),
      },
      create: {
        shop: process.env.SHOPIFY_SHOP || '',
        key: 'last_product_sync',
        value: now.toISOString(),
      },
    });

    // 同期ログを記録
    await prisma.syncLog.create({
      data: {
        shop: process.env.SHOPIFY_SHOP || '',
        syncType: 'PRODUCT',
        status: result.errors > 0 ? 'PARTIAL' : 'SUCCESS',
        created: result.created,
        updated: result.updated,
        skipped: result.skipped,
        errors: result.errors,
        errorMessage: result.errors > 0 ? JSON.stringify(result.errorDetails) : null,
        metadata: {
          total: result.total,
          created: result.created,
          updated: result.updated,
          skipped: result.skipped,
          errors: result.errors,
        },
        createdAt: now,
        completedAt: now,
      },
    });

    console.log('商品同期ジョブが完了しました。');
    console.log(`合計: ${result.total}件`);
    console.log(`作成: ${result.created}件`);
    console.log(`更新: ${result.updated}件`);
    console.log(`スキップ: ${result.skipped}件`);
    console.log(`エラー: ${result.errors}件`);

    return result;
  } catch (error) {
    console.error('商品同期ジョブでエラーが発生しました:', error);
    
    // 同期ログを記録
    await prisma.syncLog.create({
      data: {
        shop: process.env.SHOPIFY_SHOP || '',
        syncType: 'PRODUCT',
        status: 'ERROR',
        created: result.created,
        updated: result.updated,
        skipped: result.skipped,
        errors: 1,
        errorMessage: formatSyncError(error),
        metadata: {
          error: formatSyncError(error),
        },
        createdAt: new Date(),
      },
    });
    
    return {
      success: false,
      total: result.total,
      created: result.created,
      updated: result.updated,
      skipped: result.skipped,
      errors: result.errors + 1,
      error: formatSyncError(error),
    };
  } finally {
    await prisma.$disconnect();
  }
}
