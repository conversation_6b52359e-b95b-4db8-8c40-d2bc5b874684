import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品作成のミューテーション
const CREATE_PRODUCT_MUTATION = gql`
  mutation productCreate($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// バリアント作成のミューテーション
const CREATE_VARIANT_MUTATION = gql`
  mutation productVariantCreate($input: ProductVariantInput!) {
    productVariantCreate(input: $input) {
      productVariant {
        id
        title
        price
        sku
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// テスト用商品データ
const testProducts = [
  {
    title: "カリモクソファ モケットグリーン 1シーター",
    descriptionHtml: "カリモクソファ モケットグリーン 1シーター - テスト用商品（複数台）",
    vendor: "カリモク",
    productType: "ソファ",
    tags: ["テスト", "ソファ", "モケットグリーン", "複数台"],
    status: "ACTIVE",
    options: ["レンタル日数", "商品番号"],
    variants: [
      {
        price: "7000",
        sku: "10101031-001",
        options: ["1日レンタル", "1台目"],
        inventoryManagement: "SHOPIFY"
      },
      {
        price: "7000",
        sku: "10101031-002",
        options: ["1日レンタル", "2台目"],
        inventoryManagement: "SHOPIFY"
      }
    ]
  },
  {
    title: "プレーンソファ 1シーター",
    descriptionHtml: "プレーンソファ 1シーター - テスト用商品（複数台）",
    vendor: "不明",
    productType: "ソファ",
    tags: ["テスト", "ソファ", "ベージュ", "複数台"],
    status: "ACTIVE",
    options: ["レンタル日数", "商品番号"],
    variants: [
      {
        price: "8000",
        sku: "10101064-001",
        options: ["1日レンタル", "1台目"],
        inventoryManagement: "SHOPIFY"
      },
      {
        price: "8000",
        sku: "10101064-002",
        options: ["1日レンタル", "2台目"],
        inventoryManagement: "SHOPIFY"
      },
      {
        price: "8000",
        sku: "10101064-003",
        options: ["1日レンタル", "3台目"],
        inventoryManagement: "SHOPIFY"
      }
    ]
  }
];

// 追加バリアントデータ
const additionalVariants = [
  {
    productIndex: 0,
    baseVariants: [
      { title: "1台目", sku: "10101031-001" },
      { title: "2台目", sku: "10101031-002" }
    ],
    rentalDays: [
      { title: "2日レンタル", priceMultiplier: 1.2 },
      { title: "3日レンタル", priceMultiplier: 1.4 },
      { title: "4日レンタル", priceMultiplier: 1.6 },
      { title: "5日レンタル", priceMultiplier: 1.8 },
      { title: "6日レンタル", priceMultiplier: 2.0 },
      { title: "7日レンタル", priceMultiplier: 2.2 },
      { title: "8日以上レンタル", priceMultiplier: 2.3 }
    ]
  },
  {
    productIndex: 1,
    baseVariants: [
      { title: "1台目", sku: "10101064-001" },
      { title: "2台目", sku: "10101064-002" },
      { title: "3台目", sku: "10101064-003" }
    ],
    rentalDays: [
      { title: "2日レンタル", priceMultiplier: 1.2 },
      { title: "3日レンタル", priceMultiplier: 1.4 },
      { title: "4日レンタル", priceMultiplier: 1.6 },
      { title: "5日レンタル", priceMultiplier: 1.8 },
      { title: "6日レンタル", priceMultiplier: 2.0 },
      { title: "7日レンタル", priceMultiplier: 2.2 },
      { title: "8日以上レンタル", priceMultiplier: 2.3 }
    ]
  }
];

// 商品を作成する関数
async function createProduct(productData) {
  try {
    const variables = {
      input: productData
    };
    
    const result = await graphQLClient.request(CREATE_PRODUCT_MUTATION, variables);
    
    if (result.productCreate.userErrors.length > 0) {
      console.error('商品作成エラー:', result.productCreate.userErrors);
      return null;
    }
    
    return result.productCreate.product;
  } catch (error) {
    console.error('商品作成中にエラーが発生しました:', error);
    return null;
  }
}

// バリアントを作成する関数
async function createVariant(productId, variantData) {
  try {
    const variables = {
      input: {
        productId,
        ...variantData
      }
    };
    
    const result = await graphQLClient.request(CREATE_VARIANT_MUTATION, variables);
    
    if (result.productVariantCreate.userErrors.length > 0) {
      console.error('バリアント作成エラー:', result.productVariantCreate.userErrors);
      return null;
    }
    
    return result.productVariantCreate.productVariant;
  } catch (error) {
    console.error('バリアント作成中にエラーが発生しました:', error);
    return null;
  }
}

// メイン処理
async function createMultiItemsTestProducts() {
  console.log('複数台ある商品のテスト用商品を作成しています...');
  
  const createdProducts = [];
  
  for (const productData of testProducts) {
    console.log(`商品を作成中: ${productData.title}`);
    const product = await createProduct(productData);
    
    if (product) {
      console.log(`商品を作成しました: ${product.title} (ID: ${product.id})`);
      createdProducts.push(product);
    } else {
      console.error(`商品の作成に失敗しました: ${productData.title}`);
    }
  }
  
  // 追加バリアントの作成
  for (const { productIndex, baseVariants, rentalDays } of additionalVariants) {
    if (productIndex < createdProducts.length) {
      const product = createdProducts[productIndex];
      console.log(`${product.title} の追加バリアントを作成中...`);
      
      for (const baseVariant of baseVariants) {
        for (const rentalDay of rentalDays) {
          const basePrice = parseInt(testProducts[productIndex].variants.find(v => v.sku === baseVariant.sku).price);
          const price = Math.round(basePrice * rentalDay.priceMultiplier);
          const skuSuffix = rentalDay.title.includes('8日以上') ? '008' : `00${rentalDay.title.charAt(0)}`;
          const sku = `${baseVariant.sku.slice(0, -3)}${skuSuffix}`;
          
          console.log(`バリアントを作成中: ${rentalDay.title} - ${baseVariant.title} (${sku})`);
          
          const variantData = {
            options: [rentalDay.title, baseVariant.title],
            price: price.toString(),
            sku: sku,
            inventoryManagement: "SHOPIFY"
          };
          
          const variant = await createVariant(product.id, variantData);
          
          if (variant) {
            console.log(`バリアントを作成しました: ${variant.title} (ID: ${variant.id})`);
          } else {
            console.error(`バリアントの作成に失敗しました: ${rentalDay.title} - ${baseVariant.title}`);
          }
        }
      }
    }
  }
  
  console.log('\n=== 作成完了 ===');
  console.log(`${createdProducts.length}件の商品を作成しました`);
  
  if (createdProducts.length > 0) {
    console.log('\n作成された商品:');
    createdProducts.forEach(product => {
      console.log(`- ${product.title} (ID: ${product.id})`);
    });
    
    console.log('\nShopify管理画面で商品を確認: https://admin.shopify.com/store/peaces-test-block/products');
  }
}

// 実行
createMultiItemsTestProducts();
