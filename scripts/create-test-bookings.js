/**
 * テスト用予約データ作成スクリプト
 *
 * このスクリプトでは、テスト用の予約データを作成します。
 * 仮予約と本予約の両方を含み、様々なテストケースに対応できるようにします。
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// 環境変数を読み込む
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * テスト用の予約データを作成
 */
async function createTestBookings() {
  console.log("=== テスト用の予約データを作成 ===\n");

  try {
    // データベースから商品を取得
    const products = await prisma.product.findMany({
      where: {
        OR: [
          { sku: { startsWith: "TEST-" } },
          { title: { contains: "テスト商品" } }
        ]
      }
    });

    if (products.length === 0) {
      console.log("テスト商品が見つかりません");
      return;
    }

    console.log(`${products.length}件のテスト商品が見つかりました`);

    // 既存の予約を削除
    const deletedCount = await prisma.booking.deleteMany({
      where: {
        productId: {
          in: products.map(p => p.id)
        }
      }
    });

    console.log(`${deletedCount.count}件の既存の予約を削除しました`);

    // 現在の日付
    const now = new Date();

    // テスト用の予約データ
    const bookings = [
      // 本予約（近日）
      {
        productId: products[0].id,
        startDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 3),
        endDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 5),
        status: "CONFIRMED",
        customerName: "テスト顧客A",
        customerEmail: "<EMAIL>",
        totalAmount: "10000",
        depositAmount: "1000",
        bookingType: "CONFIRMED"
      },
      // 本予約（来月）
      {
        productId: products[0].id,
        startDate: new Date(now.getFullYear(), now.getMonth() + 1, 10),
        endDate: new Date(now.getFullYear(), now.getMonth() + 1, 15),
        status: "CONFIRMED",
        customerName: "テスト顧客B",
        customerEmail: "<EMAIL>",
        totalAmount: "15000",
        depositAmount: "1500",
        bookingType: "CONFIRMED"
      },
      // 仮予約（近日）
      {
        productId: products[1].id,
        startDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 7),
        endDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 10),
        status: "PROVISIONAL",
        customerName: "テスト顧客C",
        customerEmail: "<EMAIL>",
        totalAmount: "8000",
        depositAmount: "800",
        bookingType: "PROVISIONAL"
      },
      // 仮予約（来月）
      {
        productId: products[1].id,
        startDate: new Date(now.getFullYear(), now.getMonth() + 1, 20),
        endDate: new Date(now.getFullYear(), now.getMonth() + 1, 25),
        status: "PROVISIONAL",
        customerName: "テスト顧客D",
        customerEmail: "<EMAIL>",
        totalAmount: "12000",
        depositAmount: "1200",
        bookingType: "PROVISIONAL"
      },
      // 長期レンタル（8日以上）
      {
        productId: products[2].id,
        startDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 15),
        endDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 25),
        status: "CONFIRMED",
        customerName: "テスト顧客E",
        customerEmail: "<EMAIL>",
        totalAmount: "20000",
        depositAmount: "2000",
        bookingType: "CONFIRMED"
      },
      // キャンセル済み予約
      {
        productId: products[2].id,
        startDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 5),
        endDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 3),
        status: "CANCELLED",
        customerName: "テスト顧客F",
        customerEmail: "<EMAIL>",
        totalAmount: "6000",
        depositAmount: "600",
        bookingType: "CONFIRMED"
      }
    ];

    // 予約データを作成
    for (const booking of bookings) {
      const createdBooking = await prisma.booking.create({
        data: {
          productId: booking.productId,
          startDate: booking.startDate,
          endDate: booking.endDate,
          status: booking.status,
          customerName: booking.customerName,
          customerEmail: booking.customerEmail,
          bookingId: `BOOK-${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
          totalAmount: booking.totalAmount,
          depositAmount: booking.depositAmount,
          bookingType: booking.bookingType,
          shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com'
        }
      });

      console.log(`予約を作成しました: ${createdBooking.bookingId} (${booking.status})`);
      console.log(`- 商品ID: ${booking.productId}`);
      console.log(`- 期間: ${booking.startDate.toISOString().split('T')[0]} 〜 ${booking.endDate.toISOString().split('T')[0]}`);
      console.log(`- 顧客: ${booking.customerName} (${booking.customerEmail})`);
      console.log(`- 金額: ${booking.totalAmount}円 (デポジット: ${booking.depositAmount}円)`);
      console.log('');
    }

    console.log(`合計${bookings.length}件の予約データを作成しました`);
  } catch (error) {
    console.error("予約データの作成中にエラーが発生しました:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// メイン処理
async function main() {
  try {
    await createTestBookings();
    console.log("\n=== テスト用の予約データの作成が完了しました ===");
  } catch (error) {
    console.error("エラーが発生しました:", error);
  }
}

main();
