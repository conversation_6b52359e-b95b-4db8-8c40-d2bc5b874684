import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate, Link } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import { 
  Page, 
  LegacyCard, 
  Layout, 
  SettingToggle, 
  TextContainer, 
  Text,
  Button,
  FormLayout,
  TextField,
  Select,
  Banner,
  ButtonGroup,
  DescriptionList
} from '@shopify/polaris';
import { PrismaClient } from '@prisma/client';
import { useState } from 'react';

const prisma = new PrismaClient();

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  
  // アプリ設定を取得
  const settings = await prisma.appSettings.findMany({
    where: { shop: process.env.SHOPIFY_SHOP }
  });
  
  // 設定をキー別にマッピング
  const settingsMap: Record<string, any> = {};
  settings.forEach(setting => {
    settingsMap[setting.key] = setting.value;
  });
  
  return json({
    settings: settingsMap,
    defaultSettings: {
      company_name: process.env.COMPANY_NAME || 'Sample Company',
      company_address: process.env.COMPANY_ADDRESS || 'Tokyo, Japan',
      company_email: process.env.COMPANY_EMAIL || '<EMAIL>',
      deposit_rate: 0.1,
      max_provisional_bookings: 3,
      enable_delivery: true,
      enable_notifications: true,
      enable_auto_cancellation: true
    }
  });
}

export default function SettingsIndex() {
  const { settings, defaultSettings } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  
  // 設定値（存在しない場合はデフォルト値を使用）
  const getSettingValue = (key: string, defaultValue: any) => {
    return settings[key] !== undefined ? settings[key] : defaultValue;
  };
  
  // 状態管理
  const [companyName, setCompanyName] = useState(getSettingValue('company_name', defaultSettings.company_name));
  const [companyAddress, setCompanyAddress] = useState(getSettingValue('company_address', defaultSettings.company_address));
  const [companyEmail, setCompanyEmail] = useState(getSettingValue('company_email', defaultSettings.company_email));
  const [depositRate, setDepositRate] = useState(String(getSettingValue('deposit_rate', defaultSettings.deposit_rate)));
  const [maxProvisionalBookings, setMaxProvisionalBookings] = useState(String(getSettingValue('max_provisional_bookings', defaultSettings.max_provisional_bookings)));
  const [enableDelivery, setEnableDelivery] = useState(getSettingValue('enable_delivery', defaultSettings.enable_delivery));
  const [enableNotifications, setEnableNotifications] = useState(getSettingValue('enable_notifications', defaultSettings.enable_notifications));
  const [enableAutoCancellation, setEnableAutoCancellation] = useState(getSettingValue('enable_auto_cancellation', defaultSettings.enable_auto_cancellation));
  
  // 設定保存のサブミット処理
  const handleSubmit = () => {
    // 実際は保存APIを呼び出すが、ここでは簡易的に実装
    alert('設定の保存機能は開発中です');
  };
  
  const toggleFeature = (key: string, currentValue: boolean, setter: (value: boolean) => void) => {
    // 実際は保存APIを呼び出すが、ここでは簡易的に実装
    setter(!currentValue);
    alert(`設定の切り替え機能は開発中です：${key} = ${!currentValue}`);
  };
  
  // 各セクションの保存ボタン
  const saveButton = (
    <Button primary onClick={handleSubmit}>保存</Button>
  );
  
  return (
    <Page
      title="設定"
      primaryAction={
        <Button primary onClick={handleSubmit}>すべての設定を保存</Button>
      }
    >
      <Layout>
        <Layout.AnnotatedSection
          title="会社情報"
          description="メール通知などに使用される会社情報を設定します。"
        >
          <LegacyCard sectioned>
            <FormLayout>
              <TextField
                label="会社名"
                value={companyName}
                onChange={setCompanyName}
                helpText="メールテンプレートなどに使用されます"
              />
              <TextField
                label="会社住所"
                value={companyAddress}
                onChange={setCompanyAddress}
                multiline={2}
              />
              <TextField
                label="連絡先メールアドレス"
                value={companyEmail}
                onChange={setCompanyEmail}
                type="email"
              />
            </FormLayout>
          </LegacyCard>
        </Layout.AnnotatedSection>
        
        <Layout.AnnotatedSection
          title="予約設定"
          description="予約システムの基本設定を行います。"
        >
          <LegacyCard sectioned>
            <FormLayout>
              <Select
                label="デポジット率"
                options={[
                  { label: '10%', value: '0.1' },
                  { label: '15%', value: '0.15' },
                  { label: '20%', value: '0.2' },
                  { label: '30%', value: '0.3' },
                  { label: '50%', value: '0.5' }
                ]}
                value={depositRate}
                onChange={setDepositRate}
                helpText="仮予約時に必要なデポジットの割合"
              />
              <Select
                label="最大仮予約数"
                options={[
                  { label: '1件', value: '1' },
                  { label: '2件', value: '2' },
                  { label: '3件', value: '3' },
                  { label: '5件', value: '5' },
                  { label: '無制限', value: '0' }
                ]}
                value={maxProvisionalBookings}
                onChange={setMaxProvisionalBookings}
                helpText="1つの商品に対して受け付ける最大仮予約数"
              />
            </FormLayout>
          </LegacyCard>
        </Layout.AnnotatedSection>
        
        <Layout.AnnotatedSection
          title="機能の有効化"
          description="システムの機能を有効化または無効化します。"
        >
          <LegacyCard sectioned>
            <SettingToggle
              action={{
                content: enableDelivery ? '無効にする' : '有効にする',
                onAction: () => toggleFeature('enable_delivery', enableDelivery, setEnableDelivery)
              }}
              enabled={enableDelivery}
            >
              <TextContainer>
                <Text variant="headingMd">配送管理機能</Text>
                <Text variant="bodyMd">
                  商品の配送管理機能を有効または無効にします。無効にすると、配送情報の入力欄が表示されなくなります。
                </Text>
              </TextContainer>
            </SettingToggle>
          </LegacyCard>
          
          <LegacyCard sectioned>
            <SettingToggle
              action={{
                content: enableNotifications ? '無効にする' : '有効にする',
                onAction: () => toggleFeature('enable_notifications', enableNotifications, setEnableNotifications)
              }}
              enabled={enableNotifications}
            >
              <TextContainer>
                <Text variant="headingMd">自動通知機能</Text>
                <Text variant="bodyMd">
                  予約状態変更時の自動メール通知機能を有効または無効にします。
                </Text>
              </TextContainer>
            </SettingToggle>
          </LegacyCard>
          
          <LegacyCard sectioned>
            <SettingToggle
              action={{
                content: enableAutoCancellation ? '無効にする' : '有効にする',
                onAction: () => toggleFeature('enable_auto_cancellation', enableAutoCancellation, setEnableAutoCancellation)
              }}
              enabled={enableAutoCancellation}
            >
              <TextContainer>
                <Text variant="headingMd">自動キャンセル機能</Text>
                <Text variant="bodyMd">
                  期限を過ぎた仮予約の自動キャンセル機能を有効または無効にします。
                </Text>
              </TextContainer>
            </SettingToggle>
          </LegacyCard>
        </Layout.AnnotatedSection>
        
        <Layout.AnnotatedSection
          title="APIキー設定"
          description="外部サービス連携用のAPIキーを設定します。"
        >
          <LegacyCard sectioned>
            <Banner status="info">
              APIキー設定機能は開発中です。現在は環境変数で設定されたキーが使用されます。
            </Banner>
            
            <div style={{ marginTop: '16px' }}>
              <DescriptionList
                items={[
                  {
                    term: 'メール送信サービス',
                    description: process.env.EMAIL_PROVIDER || 'SMTP'
                  },
                  {
                    term: 'Holidays JP API',
                    description: process.env.HOLIDAYS_JP_API_KEY ? '設定済み' : '未設定'
                  }
                ]}
              />
            </div>
          </LegacyCard>
        </Layout.AnnotatedSection>
      </Layout>
      
      <div style={{ marginTop: '20px' }}>
        <Layout>
          <Layout.Section>
            <ButtonGroup>
              <Button url="/settings/templates">メールテンプレート設定</Button>
              <Button url="/settings/pricing">料金計算設定</Button>
              <Button url="/settings/holidays">祝日設定</Button>
            </ButtonGroup>
          </Layout.Section>
        </Layout>
      </div>
    </Page>
  );
}