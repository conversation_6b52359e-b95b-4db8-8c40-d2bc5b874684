import { Navigation } from '@shopify/polaris';
import {
  HomeIcon,
  OrderIcon,
  ProductIcon,
  InventoryIcon,
  DeliveryIcon,
  FinanceIcon,
  SettingsIcon,
  PersonIcon,
  StoreIcon
} from '@shopify/polaris-icons';
import { useLocation } from '@remix-run/react';

export function AdminNavigation() {
  const location = useLocation();
  const path = location.pathname;

  // Shopify管理画面のURLを取得
  const getShopifyAdminUrl = () => {
    if (typeof window !== 'undefined' && window.ENV?.shopifySession?.shop) {
      return `https://${window.ENV.shopifySession.shop}/admin/orders`;
    }
    return '#'; // フォールバック
  };

  return (
    <Navigation location={path}>
      <Navigation.Section
        items={[
          {
            url: '/app',
            label: 'ホーム',
            icon: HomeIcon,
            selected: path === '/app',
          },
          {
            url: '/app/bookings',
            label: '予約管理',
            icon: OrderIcon,
            selected: path.startsWith('/app/bookings'),
            subNavigationItems: [
              {
                url: '/app/bookings',
                label: '予約一覧',
              },
              {
                url: '/app/bookings/aggregate',
                label: '予約状況一括照会',
              },
              {
                url: '/app/bookings/new',
                label: '新規予約',
              },
            ],
          },
          {
            url: '/app/products',
            label: '商品管理',
            icon: ProductIcon,
            selected: path.startsWith('/app/products'),
            subNavigationItems: [
              {
                url: '/app/products',
                label: '商品一覧',
              },
              {
                url: '/app/products/new',
                label: '新規商品',
              },
              {
                url: '/app/products/sync',
                label: 'Shopify同期',
              },
            ],
          },
          {
            url: '/app/maintenance',
            label: 'メンテナンス管理',
            icon: InventoryIcon,
            selected: path.startsWith('/app/maintenance'),
            subNavigationItems: [
              {
                url: '/app/maintenance',
                label: 'メンテナンス一覧',
              },
              {
                url: '/app/maintenance/new',
                label: '新規メンテナンス',
              },
            ],
          },
          {
            url: '/app/delivery',
            label: '配送管理',
            icon: DeliveryIcon,
            selected: path.startsWith('/app/delivery'),
            subNavigationItems: [
              {
                url: '/app/delivery/picking',
                label: 'ピッキング登録',
              },
              {
                url: '/app/delivery/returns',
                label: '返却・金額確定',
              },
              {
                url: '/app/delivery/schedule',
                label: '配送一覧',
              },
              {
                url: '/app/delivery',
                label: '配送概要',
              },
            ],
          },
          {
            url: '/app/orders',
            label: '注文管理',
            icon: OrderIcon,
            selected: path.startsWith('/app/orders'),
            subNavigationItems: [
              {
                url: '/app/orders',
                label: '注文一覧',
              },
              {
                url: '/app/orders/sync',
                label: 'Shopify同期',
              },
            ],
          },
          {
            url: '/app/invoices',
            label: '請求書管理',
            icon: FinanceIcon,
            selected: path.startsWith('/app/invoices'),
            subNavigationItems: [
              {
                url: '/app/invoices',
                label: '請求書一覧',
              },
              {
                url: '/app/invoices/new',
                label: '新規請求書',
              },
            ],
          },
          {
            url: '/app/customers',
            label: '顧客管理',
            icon: PersonIcon,
            selected: path.startsWith('/app/customers'),
            subNavigationItems: [
              {
                url: '/app/customers',
                label: '顧客一覧',
              },
              {
                url: '/app/customers/new',
                label: '新規顧客',
              },
              {
                url: '/app/customers/sync',
                label: 'Shopify同期',
              },
            ],
          },
        ]}
      />
      <Navigation.Section
        title="設定"
        items={[
          {
            url: '/app/settings',
            label: '一般設定',
            icon: SettingsIcon,
            selected: path === '/app/settings',
          },
          {
            url: '/app/settings/staff',
            label: 'スタッフ管理',
            icon: SettingsIcon,
            selected: path === '/app/settings/staff',
          },
          {
            url: '/app/settings/shipping',
            label: '配送設定',
            icon: SettingsIcon,
            selected: path === '/app/settings/shipping',
          },
        ]}
      />
      <Navigation.Section
        title="外部リンク"
        items={[
          {
            url: getShopifyAdminUrl(),
            label: 'Shopify',
            icon: StoreIcon,
            external: true,
          },
        ]}
      />
    </Navigation>
  );
}
