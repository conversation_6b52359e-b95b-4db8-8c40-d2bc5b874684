import { Page, Card, Text, BlockStack } from "@shopify/polaris";

export default function IconTest() {
  return (
    <Page title="Polarisアイコンテスト">
      <Card>
        <BlockStack gap="400">
          <Text variant="headingMd">Polarisアイコンの使用方法</Text>
          <Text as="p">
            Polarisアイコンを使用するには、公式ドキュメントを参照してください：
          </Text>
          <Text as="p">
            <a href="https://polaris.shopify.com/icons" target="_blank" rel="noopener noreferrer">
              https://polaris.shopify.com/icons
            </a>
          </Text>
          <Text as="p">
            アイコンの使用例：
          </Text>
          <pre style={{ maxHeight: "400px", overflow: "auto" }}>
{`import { PlayIcon, PauseIcon, RefreshIcon } from "@shopify/polaris-icons";

// コンポーネント内で使用
<Button icon={PlayIcon}>再生</Button>`}
          </pre>
        </BlockStack>
      </Card>
    </Page>
  );
}
