{"timestamp": "2025-05-19T08:16:43.188Z", "duration": 7.046, "totalTests": 3, "successCount": 3, "failureCount": 0, "tests": [{"testName": "予約リクエスト #0", "success": true, "timestamp": "2025-05-19T08:16:50.235Z", "message": "予約リクエストが成功しました", "duration": 5910, "details": {"requestId": 0, "bookingId": "d8d6d187-8694-4286-94ca-6cd7a53f72de"}}, {"testName": "予約リクエスト #1", "success": true, "timestamp": "2025-05-19T08:16:50.235Z", "message": "予約リクエストが成功しました", "duration": 5585, "details": {"requestId": 1, "bookingId": "2729107b-ab35-4f95-9042-343b5b751faa"}}, {"testName": "予約リクエスト #2", "success": true, "timestamp": "2025-05-19T08:16:50.235Z", "message": "予約リクエストが成功しました", "duration": 6299, "details": {"requestId": 2, "bookingId": "1fe0fff3-2dd5-4324-9563-3769e7920a6a"}}], "environment": {"concurrentRequests": 3, "nodeVersion": "v20.18.1", "os": "darwin arm64", "cpuCores": 10}, "performance": {"averageTestDuration": 5931.333333333333, "slowestTest": {"name": "リクエスト #2", "duration": 6299}, "fastestTest": {"name": "リクエスト #1", "duration": 5585}, "throughput": 0.4257734885041158, "successRate": 100}}