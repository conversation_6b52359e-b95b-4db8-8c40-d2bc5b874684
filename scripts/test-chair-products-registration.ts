import { GraphQLClient } from 'graphql-request';
import * as fs from 'fs';
import { parse } from 'csv-parse/sync';
import * as dotenv from 'dotenv';

// 環境変数を読み込む
dotenv.config();

// 環境変数の型定義
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      SHOPIFY_SHOP: string;
      SHOPIFY_ADMIN_API_ACCESS_TOKEN: string;
    }
  }
}

// CSVデータの型定義
interface ChairProductCSV {
  商品基本マスタID: string;
  商品詳細マスタID: string;
  カテゴリマスタID: string;
  商品カテゴリ: string;
  商品コード: string;
  商品詳細コード: string;
  商品名: string;
  商品名フリガナ: string;
  商品詳細名称: string;
  品番: string;
  型番: string;
  レンタル単価: string;
  在庫数: string;
  タグ: string;
  在庫場所: string;
  サイズW: string;
  サイズD: string;
  サイズH: string;
  直径: string;
  特記事項: string;
  レンタル開始日: string;
  レンタル終了日: string;
  メーカー名: string;
  購入場所: string;
  購入店舗: string;
  購入金額: string;
  購入日: string;
  商品ステータス: string;
  備考: string;
  JANコード: string;
  ステータス: string;
  廃業区分: string;
  廃棄日: string;
}

// 商品グループの型定義
interface ProductGroup {
  productCode: string;
  productName: string;
  category: string;
  details: ChairProductCSV[];
}

// ショップ名を正規化
const shopName = (process.env.SHOPIFY_SHOP || 'peaces-test-block').replace('.myshopify.com', '');

const client = new GraphQLClient(
  `https://${shopName}.myshopify.com/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// GraphQL定義
const CREATE_PRODUCT_MUTATION = `
  mutation CreateProduct($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
        variants(first: 20) {
          nodes {
            id
            title
            sku
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;


const SET_METAFIELDS_MUTATION = `
  mutation SetMetafields($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        key
        namespace
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

const UPDATE_INVENTORY_MUTATION = `
  mutation UpdateInventory($inventoryItemId: ID!, $locationId: ID!, $available: Int!) {
    inventoryItemUpdate(
      id: $inventoryItemId
      input: {
        tracked: true
      }
    ) {
      inventoryItem {
        id
        tracked
      }
      userErrors {
        field
        message
      }
    }
    inventoryAdjustQuantities(
      input: {
        changes: [{
          inventoryItemId: $inventoryItemId
          locationId: $locationId
          delta: $available
        }]
      }
    ) {
      inventoryAdjustmentGroup {
        id
      }
      userErrors {
        field
        message
      }
    }
  }
`;

const GET_INVENTORY_ITEM_QUERY = `
  query GetInventoryItem($variantId: ID!) {
    productVariant(id: $variantId) {
      id
      inventoryItem {
        id
      }
    }
  }
`;

const GET_LOCATION_QUERY = `
  query GetLocation {
    locations(first: 1) {
      nodes {
        id
        name
      }
    }
  }
`;

// 価格計算関数
function calculateRentalPrice(basePrice: number, days: number): number {
  if (days === 1) return basePrice;
  if (days === 2) return Math.round(basePrice * 1.2);
  if (days === 3) return Math.round(basePrice * 1.4);
  if (days === 4) return Math.round(basePrice * 1.6);
  if (days === 5) return Math.round(basePrice * 1.8);
  if (days === 6) return Math.round(basePrice * 2.0);
  if (days === 7) return Math.round(basePrice * 2.1);
  return Math.round(basePrice * 0.1); // 8日以上の場合は1日あたり10%
}

// バリアント作成
function createVariants(productCode: string, detailCode: string, basePrice: number) {
  const variants = [];
  
  // 1-7日のレンタル
  for (let i = 1; i <= 7; i++) {
    variants.push({
      title: `${i}日レンタル`,
      sku: `${productCode}-${detailCode}-${i}D`,
      price: calculateRentalPrice(basePrice, i).toString(),
      inventoryManagement: 'SHOPIFY',
      inventoryPolicy: 'DENY',
      fulfillmentService: 'MANUAL',
      requiresShipping: true,
      taxable: true,
      weight: 0,
      weightUnit: 'KILOGRAMS',
    });
  }
  
  // 8日以上レンタル
  variants.push({
    title: '8日以上レンタル',
    sku: `${productCode}-${detailCode}-8D+`,
    price: calculateRentalPrice(basePrice, 8).toString(),
    inventoryManagement: 'SHOPIFY',
    inventoryPolicy: 'DENY',
    fulfillmentService: 'MANUAL',
    requiresShipping: true,
    taxable: true,
    weight: 0,
    weightUnit: 'KILOGRAMS',
  });
  
  // 仮予約
  variants.push({
    title: '仮予約',
    sku: `${productCode}-${detailCode}-PROV`,
    price: calculateRentalPrice(basePrice, 1).toString(),
    inventoryManagement: 'SHOPIFY',
    inventoryPolicy: 'DENY',
    fulfillmentService: 'MANUAL',
    requiresShipping: true,
    taxable: true,
    weight: 0,
    weightUnit: 'KILOGRAMS',
  });
  
  return variants;
}

// 商品登録
async function createProduct(group: ProductGroup, productIndex: number) {
  try {
    console.log(`\\n🛍️ 商品グループ登録開始: ${group.productName} (${group.details.length}個)`);
    
    const createdProducts = [];
    
    for (const detail of group.details) {
      const basePrice = parseInt(detail.レンタル単価) || 3000;
      const variants = createVariants(detail.商品コード, detail.商品詳細コード, basePrice);
      const tags = detail.タグ ? detail.タグ.split(',').map(t => t.trim()) : [];
      tags.push(group.category, `商品番号:${detail.商品コード}`, `詳細:${detail.商品詳細コード}`, `場所:${detail.在庫場所}`);
      
      const productInput = {
        title: `${detail.商品名} [${detail.商品詳細名称}]`,
        handle: `${detail.商品コード}-${detail.商品詳細コード}-test${productIndex}`,
        descriptionHtml: `
          <h3>${detail.商品名}</h3>
          <p><strong>商品番号:</strong> ${detail.商品コード}-${detail.商品詳細コード}</p>
          <p><strong>状態:</strong> ${detail.商品詳細名称}</p>
          <p><strong>サイズ:</strong> 幅${detail.サイズW}cm × 奥行${detail.サイズD}cm × 高さ${detail.サイズH}cm</p>
          <p><strong>カテゴリ:</strong> ${group.category}</p>
          <p><strong>保管場所:</strong> ${detail.在庫場所}</p>
          ${detail.備考 ? `<p><strong>備考:</strong> ${detail.備考}</p>` : ''}
        `,
        vendor: 'IZIZ RENTAL',
        productType: group.category,
        tags: tags,
        status: 'ACTIVE',
      };
      
      // 商品作成
      const productResult: any = await client.request(CREATE_PRODUCT_MUTATION, { input: productInput });
      
      if (productResult.productCreate.userErrors.length > 0) {
        console.error(`❌ 商品作成エラー:`, productResult.productCreate.userErrors);
        continue;
      }
      
      const product = productResult.productCreate.product;
      console.log(`✅ 商品作成成功: ${product.title}`);
      
      // バリアント作成
      for (let i = 0; i < variants.length; i++) {
        const variant = variants[i];
        const variantId = product.variants.nodes[0].id; // 初期バリアントを更新
        
        // バリアントメタフィールド設定
        const metafields = [
          {
            ownerId: variantId,
            namespace: 'rental',
            key: 'rental_period',
            type: 'number_integer',
            value: variant.title.includes('日') ? variant.title.match(/\\d+/)?.[0] || '1' : '1',
          },
          {
            ownerId: variantId,
            namespace: 'rental',
            key: 'is_provisional',
            type: 'boolean',
            value: variant.title === '仮予約' ? 'true' : 'false',
          },
        ];
        
        await client.request(SET_METAFIELDS_MUTATION, { metafields });
      }
      
      // 在庫設定
      const locationResult: any = await client.request(GET_LOCATION_QUERY);
      const locationId = locationResult.locations.nodes[0].id;
      
      for (const variantNode of product.variants.nodes) {
        const inventoryResult: any = await client.request(GET_INVENTORY_ITEM_QUERY, {
          variantId: variantNode.id,
        });
        
        const inventoryItemId = inventoryResult.productVariant.inventoryItem.id;
        const quantity = parseInt(detail.在庫数) || 1;
        
        await client.request(UPDATE_INVENTORY_MUTATION, {
          inventoryItemId,
          locationId,
          available: quantity,
        });
      }
      
      // 商品メタフィールド設定
      const productMetafields = [
        {
          ownerId: product.id,
          namespace: 'rental',
          key: 'base_price',
          type: 'number_integer',
          value: basePrice.toString(),
        },
        {
          ownerId: product.id,
          namespace: 'product',
          key: 'size_width',
          type: 'number_integer',
          value: detail.サイズW || '0',
        },
        {
          ownerId: product.id,
          namespace: 'product',
          key: 'size_depth',
          type: 'number_integer',
          value: detail.サイズD || '0',
        },
        {
          ownerId: product.id,
          namespace: 'product',
          key: 'size_height',
          type: 'number_integer',
          value: detail.サイズH || '0',
        },
        {
          ownerId: product.id,
          namespace: 'rental',
          key: 'inventory_items',
          type: 'json',
          value: JSON.stringify({
            locations: [{
              location_id: locationId,
              quantity: parseInt(detail.在庫数) || 1,
              location_name: detail.在庫場所,
            }],
            total_quantity: parseInt(detail.在庫数) || 1,
          }),
        },
      ];
      
      await client.request(SET_METAFIELDS_MUTATION, { metafields: productMetafields });
      
      createdProducts.push(product);
    }
    
    return createdProducts;
    
  } catch (error) {
    console.error('❌ 商品登録エラー:', error);
    return [];
  }
}

// メイン処理
async function main() {
  try {
    console.log('🚀 椅子商品登録テスト開始...');
    
    // CSVファイル読み込み
    const csvPath = '/Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp/master-data-csv/商品一覧_202505031504いす.csv';
    const csvContent = fs.readFileSync(csvPath, 'utf-8');
    const records: ChairProductCSV[] = parse(csvContent, {
      columns: true,
      skip_empty_lines: true,
    });
    
    // テスト用商品を選択
    const testProductCodes = ['10104099', '10301007', '10301009'];
    const selectedProducts = records.filter(record => 
      testProductCodes.includes(record.商品コード) && 
      record.ステータス === '1' &&
      record.廃業区分 === '0'
    );
    
    // 商品をグループ化
    const productGroups = new Map<string, ProductGroup>();
    
    for (const record of selectedProducts) {
      if (!productGroups.has(record.商品コード)) {
        productGroups.set(record.商品コード, {
          productCode: record.商品コード,
          productName: record.商品名,
          category: record.商品カテゴリ,
          details: [],
        });
      }
      productGroups.get(record.商品コード)!.details.push(record);
    }
    
    console.log(`📦 登録対象: ${productGroups.size}件の商品グループ`);
    
    // 商品登録
    let productIndex = 1;
    for (const group of productGroups.values()) {
      console.log(`\\n${productIndex}. ${group.productName} (${group.productCode})`);
      console.log(`   - バリエーション数: ${group.details.length}`);
      console.log(`   - カテゴリ: ${group.category}`);
      
      await createProduct(group, productIndex);
      productIndex++;
    }
    
    console.log('\\n🎉 全ての商品登録が完了しました！');
    
  } catch (error) {
    console.error('❌ エラー:', error);
    process.exit(1);
  }
}

// 実行
main();