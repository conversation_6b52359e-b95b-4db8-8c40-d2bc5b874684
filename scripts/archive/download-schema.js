const { writeFileSync, mkdirSync } = require("fs");
const { join } = require("path");
const { config } = require("dotenv");

config(); // .envファイルから環境変数を読み込む

const API_VERSION = "2025-01"; // shopify.app.tomlから取得したバージョン
const SCHEMA_URL = `https://shopify.dev/admin-graphql-direct-proxy/${API_VERSION}/schema.graphql`;
const OUTPUT_DIR = join(process.cwd(), "app", "graphql", "schema");
const OUTPUT_PATH = join(OUTPUT_DIR, "admin.graphql");

async function downloadSchema() {
  console.log(
    `Shopify Admin GraphQL スキーマ (${API_VERSION}) をダウンロード中...`,
  );
  console.log(`URL: ${SCHEMA_URL}`);

  try {
    const response = await fetch(SCHEMA_URL, {
      method: "GET",
      headers: {
        "Content-Type": "application/graphql",
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `スキーマのダウンロードに失敗しました: ${response.status} ${response.statusText}\n${errorText}`,
      );
    }

    const schema = await response.text();

    // 出力ディレクトリが存在しない場合は作成
    mkdirSync(OUTPUT_DIR, { recursive: true });

    // スキーマファイルに書き込み
    writeFileSync(OUTPUT_PATH, schema);
    console.log(`✅ GraphQLスキーマを ${OUTPUT_PATH} に保存しました`);
  } catch (error) {
    console.error(
      "❌ GraphQLスキーマのダウンロード中にエラーが発生しました:",
      error,
    );
    process.exit(1);
  }
}

downloadSchema();
