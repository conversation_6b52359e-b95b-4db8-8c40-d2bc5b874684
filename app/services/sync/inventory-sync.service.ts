/**
 * 在庫同期サービス
 * 
 * ShopifyとPrismaデータベース間の在庫情報を同期するサービス
 */

import { PrismaClient } from '@prisma/client';
import { authenticate } from '~/shopify.server';
import { formatSyncError } from '~/utils/sync/error-handler';

const prisma = new PrismaClient();

/**
 * 同期結果型
 */
interface SyncResult {
  success: boolean;
  entityType: string;
  entityId: string;
  operation: string;
  message?: string;
  errors?: any[];
  syncedAt: Date;
}

/**
 * 在庫同期サービス
 */
export class InventorySyncService {
  private admin: any;
  
  constructor(request: Request) {
    this.initialize(request);
  }
  
  /**
   * 非同期の初期化
   */
  private async initialize(request: Request) {
    // 認証と初期化
    const { admin } = await authenticate.admin(request);
    this.admin = admin;
  }
  
  /**
   * 商品の在庫情報をShopifyから同期
   */
  async syncInventoryFromShopify(shopifyProductId: string): Promise<SyncResult> {
    try {
      // 商品情報を取得
      const product = await prisma.product.findFirst({
        where: { shopifyId: shopifyProductId }
      });
      
      if (!product) {
        throw new Error(`商品 ID ${shopifyProductId} が見つかりません`);
      }
      
      // Shopify APIから在庫情報を取得
      const response = await this.admin.graphql(`
        query GetProductInventory($id: ID!) {
          product(id: $id) {
            id
            title
            variants(first: 10) {
              edges {
                node {
                  id
                  title
                  inventoryQuantity
                  inventoryItem {
                    id
                    inventoryLevels(first: 5) {
                      edges {
                        node {
                          id
                          available
                          location {
                            id
                            name
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      `, {
        variables: {
          id: `gid://shopify/Product/${shopifyProductId}`
        }
      });
      
      const data = await response.json();
      
      if (data.errors) {
        throw new Error(`Shopify API エラー: ${data.errors[0].message}`);
      }
      
      const shopifyProduct = data.data.product;
      
      if (!shopifyProduct) {
        throw new Error(`商品 ID ${shopifyProductId} が見つかりません`);
      }
      
      // バリアント情報を取得
      const variants = shopifyProduct.variants.edges.map(edge => edge.node);
      
      // 在庫情報を更新
      for (const variant of variants) {
        const variantId = this.extractIdFromGid(variant.id);
        const inventoryLevels = variant.inventoryItem?.inventoryLevels?.edges || [];
        
        // 各ロケーションの在庫情報を処理
        for (const levelEdge of inventoryLevels) {
          const level = levelEdge.node;
          const locationId = this.extractIdFromGid(level.location.id);
          const locationName = level.location.name;
          const available = level.available;
          
          // ロケーション情報を保存または更新
          await this.upsertLocation(locationId, locationName);
          
          // 在庫情報を更新
          await this.updateProductInventory(product.id, variantId, locationId, available);
        }
      }
      
      // 在庫カレンダーを更新
      await this.updateInventoryCalendar(product.id);
      
      // 同期ログを記録
      await this.logSync({
        entityType: 'inventory',
        entityId: product.id,
        operation: 'inventory_to_db',
        status: 'success',
        shopifyId: shopifyProductId,
        metadata: {
          title: shopifyProduct.title,
          variantsCount: variants.length
        }
      });
      
      return {
        success: true,
        entityType: 'inventory',
        entityId: product.id,
        operation: 'inventory_to_db',
        message: `商品 "${shopifyProduct.title}" の在庫同期が完了しました`,
        syncedAt: new Date()
      };
    } catch (error) {
      console.error(`在庫同期エラー (${shopifyProductId}):`, error);
      
      // 同期失敗を記録
      await this.logSync({
        entityType: 'inventory',
        entityId: '',
        operation: 'inventory_to_db',
        status: 'error',
        shopifyId: shopifyProductId,
        errorMessage: formatSyncError(error)
      });
      
      return {
        success: false,
        entityType: 'inventory',
        entityId: '',
        operation: 'inventory_to_db',
        message: `在庫同期エラー: ${formatSyncError(error)}`,
        errors: [formatSyncError(error)],
        syncedAt: new Date()
      };
    }
  }
  
  /**
   * ロケーション情報を保存または更新
   */
  private async upsertLocation(shopifyLocationId: string, name: string): Promise<void> {
    await prisma.location.upsert({
      where: {
        shop_shopifyId: {
          shop: process.env.SHOPIFY_SHOP || '',
          shopifyId: shopifyLocationId
        }
      },
      update: {
        name,
        updatedAt: new Date()
      },
      create: {
        shop: process.env.SHOPIFY_SHOP || '',
        shopifyId: shopifyLocationId,
        name,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
  }
  
  /**
   * 商品の在庫情報を更新
   */
  private async updateProductInventory(
    productId: string,
    variantId: string,
    locationId: string,
    available: number
  ): Promise<void> {
    // 商品情報を更新
    await prisma.product.update({
      where: { id: productId },
      data: {
        locationId,
        updatedAt: new Date()
      }
    });
    
    // 在庫情報をメタデータに保存
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });
    
    if (product) {
      const metadata = product.metadata || {};
      const inventory = metadata.inventory || {};
      
      // 在庫情報を更新
      inventory[locationId] = {
        available,
        updatedAt: new Date().toISOString()
      };
      
      // メタデータを更新
      metadata.inventory = inventory;
      
      await prisma.product.update({
        where: { id: productId },
        data: {
          metadata,
          updatedAt: new Date()
        }
      });
    }
  }
  
  /**
   * 在庫カレンダーを更新
   */
  private async updateInventoryCalendar(productId: string): Promise<void> {
    // 商品情報を取得
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });
    
    if (!product) {
      throw new Error(`商品 ID ${productId} が見つかりません`);
    }
    
    // 予約情報を取得
    const bookings = await prisma.booking.findMany({
      where: {
        productId,
        status: {
          in: ['PROVISIONAL', 'CONFIRMED']
        }
      }
    });
    
    // メンテナンス情報を取得
    const maintenances = await prisma.maintenance.findMany({
      where: {
        productId,
        endDate: {
          gte: new Date()
        }
      }
    });
    
    // 今日から3ヶ月分の在庫カレンダーを更新
    const today = new Date();
    const endDate = new Date(today);
    endDate.setMonth(today.getMonth() + 3);
    
    // 日付ごとに在庫状態を計算
    for (let date = new Date(today); date <= endDate; date.setDate(date.getDate() + 1)) {
      const currentDate = new Date(date);
      
      // 予約状況をチェック
      const isBooked = bookings.some(booking => {
        const startDate = new Date(booking.startDate);
        const endDate = new Date(booking.endDate);
        return currentDate >= startDate && currentDate <= endDate;
      });
      
      // メンテナンス状況をチェック
      const isInMaintenance = maintenances.some(maintenance => {
        const startDate = new Date(maintenance.startDate);
        const endDate = new Date(maintenance.endDate);
        return currentDate >= startDate && currentDate <= endDate;
      });
      
      // 在庫状態を決定
      const isAvailable = !isBooked && !isInMaintenance;
      
      // 在庫カレンダーを更新
      await prisma.inventoryCalendar.upsert({
        where: {
          shop_productId_date: {
            shop: process.env.SHOPIFY_SHOP || '',
            productId,
            date: currentDate
          }
        },
        update: {
          isAvailable,
          unavailableReason: isBooked ? 'BOOKED' : (isInMaintenance ? 'MAINTENANCE' : null),
          updatedAt: new Date()
        },
        create: {
          shop: process.env.SHOPIFY_SHOP || '',
          productId,
          date: currentDate,
          isAvailable,
          unavailableReason: isBooked ? 'BOOKED' : (isInMaintenance ? 'MAINTENANCE' : null),
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }
  }
  
  /**
   * ShopifyのGIDから数値IDを抽出する
   */
  private extractIdFromGid(gid: string): string {
    const match = gid.match(/gid:\/\/shopify\/\w+\/(\d+)/);
    return match ? match[1] : "";
  }
  
  /**
   * 同期ログを記録
   */
  private async logSync(data: {
    entityType: string;
    entityId: string;
    operation: string;
    status: string;
    shopifyId: string;
    errorMessage?: string;
    metadata?: any;
  }): Promise<void> {
    try {
      await prisma.syncLog.create({
        data: {
          shop: process.env.SHOPIFY_SHOP || '',
          entityType: data.entityType,
          entityId: data.entityId,
          operation: data.operation,
          status: data.status,
          errorMessage: data.errorMessage,
          metadata: {
            shopifyId: data.shopifyId,
            ...data.metadata
          },
          completedAt: data.status === 'success' ? new Date() : null
        }
      });
    } catch (error) {
      console.error('同期ログ記録エラー:', error);
    }
  }
}
