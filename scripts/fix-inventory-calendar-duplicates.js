/**
 * 在庫カレンダーの重複エントリ修正スクリプト
 */

import { PrismaClient } from '@prisma/client';
import { format } from 'date-fns';

const prisma = new PrismaClient();

async function fixInventoryCalendarDuplicates() {
  console.log('🔧 在庫カレンダーの重複エントリ修正を開始...\n');

  try {
    // 重複エントリを検索
    console.log('=== 重複エントリの検索 ===');
    const duplicates = await prisma.$queryRaw`
      SELECT
        shop,
        "productId",
        date,
        COUNT(*) as count
      FROM "inventory_calendar"
      GROUP BY shop, "productId", date
      HAVING COUNT(*) > 1
      ORDER BY "productId", date
    `;

    console.log(`重複エントリが見つかりました: ${duplicates.length}件`);

    if (duplicates.length === 0) {
      console.log('✅ 重複エントリはありません');
      return;
    }

    // 各重複エントリを処理
    for (const duplicate of duplicates) {
      const { shop, productId, date, count } = duplicate;
      console.log(`\n📅 処理中: ${productId} - ${format(new Date(date), 'yyyy-MM-dd')} (${count}件)`);

      // 該当する全エントリを取得
      const entries = await prisma.inventoryCalendar.findMany({
        where: {
          shop: shop,
          productId: productId,
          date: new Date(date)
        },
        orderBy: {
          createdAt: 'asc'
        }
      });

      console.log(`   取得したエントリ: ${entries.length}件`);

      if (entries.length <= 1) {
        continue;
      }

      // 最新のエントリを保持し、古いエントリを削除
      const keepEntry = entries[entries.length - 1]; // 最新のエントリ
      const deleteEntries = entries.slice(0, -1); // 古いエントリ

      console.log(`   保持: ${keepEntry.id} (${format(keepEntry.createdAt, 'yyyy-MM-dd HH:mm:ss')})`);
      console.log(`   削除: ${deleteEntries.length}件`);

      // 古いエントリを削除
      for (const entry of deleteEntries) {
        await prisma.inventoryCalendar.delete({
          where: { id: entry.id }
        });
        console.log(`     削除完了: ${entry.id}`);
      }
    }

    console.log('\n=== 修正後の確認 ===');

    // 修正後の重複チェック
    const remainingDuplicates = await prisma.$queryRaw`
      SELECT
        shop,
        "productId",
        date,
        COUNT(*) as count
      FROM "inventory_calendar"
      GROUP BY shop, "productId", date
      HAVING COUNT(*) > 1
    `;

    if (remainingDuplicates.length === 0) {
      console.log('✅ 重複エントリの修正が完了しました');
    } else {
      console.log(`❌ まだ${remainingDuplicates.length}件の重複が残っています`);
    }

    // TEST-TABLE-001とTEST-SOFA-001の状況を確認
    console.log('\n=== 対象商品の確認 ===');

    const testProducts = await prisma.product.findMany({
      where: {
        sku: { in: ['TEST-TABLE-001', 'TEST-SOFA-001'] }
      }
    });

    for (const product of testProducts) {
      console.log(`\n📦 ${product.sku}:`);

      const today = new Date();
      const futureDate = new Date();
      futureDate.setDate(today.getDate() + 7);

      const inventoryEntries = await prisma.inventoryCalendar.findMany({
        where: {
          productId: product.id,
          date: { gte: today, lte: futureDate }
        },
        orderBy: { date: 'asc' }
      });

      console.log(`   在庫カレンダー（今後7日間）: ${inventoryEntries.length}件`);
      inventoryEntries.forEach(entry => {
        const dateStr = format(entry.date, 'yyyy-MM-dd');
        const status = entry.isAvailable ? '✅ 利用可能' : '❌ 利用不可';
        const reason = entry.unavailableReason ? ` (${entry.unavailableReason})` : '';
        console.log(`     ${dateStr}: ${status}${reason}`);
      });
    }

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
fixInventoryCalendarDuplicates();
