/**
 * バリエーション（バリアント）の在庫管理テスト
 *
 * このスクリプトでは以下をテストします：
 * 1. バリエーションを持つ商品の在庫状況の確認
 * 2. 仮予約と本予約の動作確認
 * 3. バリエーションごとの在庫管理の確認
 */

import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト結果を保存する配列
const testResults = [];

/**
 * テスト結果を記録する関数
 */
function logTestResult(testName, success, details = {}) {
  const result = {
    testName,
    success,
    timestamp: new Date().toISOString(),
    ...details
  };

  testResults.push(result);

  // コンソールにも出力
  console.log(`[${success ? 'SUCCESS' : 'FAILURE'}] ${testName}`);
  if (details.message) {
    console.log(`  ${details.message}`);
  }
  if (details.error) {
    console.error(`  Error: ${details.error}`);
  }
}

/**
 * バリエーションを持つ商品を取得
 */
async function getProductsWithVariants() {
  console.log("\nテストケース1: バリエーションを持つ商品の取得");

  try {
    // 商品情報の取得
    const products = await prisma.product.findMany({
      where: {
        sku: {
          contains: '-'
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // バリエーションを持つ商品をフィルタリング
    const productsWithVariants = products.filter(product => {
      // SKUの形式がXXX-001, XXX-002のような形式かどうかを確認
      const skuPattern = /^[A-Z]+-\d{3}$/;
      return skuPattern.test(product.sku);
    });

    // TEST-VARIANTで始まる商品を優先的に取得
    const testVariants = products.filter(product =>
      product.sku.startsWith('TEST-VARIANT')
    );

    if (testVariants.length > 0) {
      console.log(`TEST-VARIANTで始まる商品を${testVariants.length}件見つけました`);
      return { products: testVariants, groups: { 'TEST-VARIANT': testVariants } };
    }

    console.log(`データベースから${productsWithVariants.length}件のバリエーションを持つ商品情報を取得しました`);
    console.log("商品一覧:");
    productsWithVariants.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.title} (ID: ${product.id})`);
      console.log(`    SKU: ${product.sku || 'なし'}`);
      console.log(`    価格: ${product.price || 'なし'}`);
    });

    // 同じベースSKUを持つ商品をグループ化
    const productGroups = {};
    productsWithVariants.forEach(product => {
      const baseSku = product.sku.split('-')[0];
      if (!productGroups[baseSku]) {
        productGroups[baseSku] = [];
      }
      productGroups[baseSku].push(product);
    });

    console.log("\n商品グループ:");
    Object.keys(productGroups).forEach(baseSku => {
      console.log(`  ${baseSku}: ${productGroups[baseSku].length}個のバリエーション`);
      productGroups[baseSku].forEach((product, index) => {
        console.log(`    ${index + 1}. ${product.title} (SKU: ${product.sku})`);
      });
    });

    logTestResult("バリエーションを持つ商品の取得", productsWithVariants.length > 0, {
      message: `データベースから${productsWithVariants.length}件のバリエーションを持つ商品情報を取得しました`
    });

    return { products: productsWithVariants, groups: productGroups };
  } catch (error) {
    logTestResult("バリエーションを持つ商品の取得", false, {
      error: error.message
    });

    return { products: [], groups: {} };
  }
}

/**
 * 仮予約の作成テスト
 */
async function testProvisionalBooking(products) {
  console.log("\nテストケース2: 仮予約の作成テスト");

  try {
    if (!products || products.length === 0) {
      logTestResult("仮予約の作成", false, {
        message: "テスト対象の商品がありません"
      });

      return null;
    }

    // テスト用の商品を選択
    const testProduct = products[0];

    console.log(`テスト対象商品: ${testProduct.title} (SKU: ${testProduct.sku})`);

    // 予約期間を設定（来月の1日から3日）
    const today = new Date();
    const nextMonth = today.getMonth() + 1;
    const year = today.getFullYear() + (nextMonth > 11 ? 1 : 0);
    const month = nextMonth > 11 ? 0 : nextMonth;

    const startDate = new Date(year, month, 1);
    const endDate = new Date(year, month, 3);

    // 予約IDを生成
    const bookingId = `TEST-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

    // 仮予約データを作成
    const booking = await prisma.booking.create({
      data: {
        productId: testProduct.id,
        startDate,
        endDate,
        status: 'PROVISIONAL',
        bookingType: 'PROVISIONAL',
        customerEmail: '<EMAIL>',
        customerName: 'テストユーザー',
        shop: 'peaces-test-block.myshopify.com',
        bookingId
      }
    });

    console.log(`仮予約を作成しました: ID=${booking.id}, 予約ID=${booking.bookingId}`);
    console.log(`期間: ${startDate.toISOString().split('T')[0]} 〜 ${endDate.toISOString().split('T')[0]}`);

    // 在庫カレンダーも更新
    const dateRange = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      dateRange.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    for (const date of dateRange) {
      await prisma.inventoryCalendar.upsert({
        where: {
          shop_productId_date: {
            shop: 'peaces-test-block.myshopify.com',
            productId: testProduct.id,
            date
          }
        },
        update: {
          isAvailable: false,
          unavailableReason: 'provisional',
          bookingId: booking.id
        },
        create: {
          shop: 'peaces-test-block.myshopify.com',
          productId: testProduct.id,
          shopifyProductId: testProduct.id,
          date,
          isAvailable: false,
          unavailableReason: 'provisional',
          bookingId: booking.id
        }
      });
    }

    console.log(`在庫カレンダーを更新しました: ${dateRange.length}日分`);

    // 在庫カレンダーを確認
    const calendars = await prisma.inventoryCalendar.findMany({
      where: {
        productId: testProduct.id,
        date: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    console.log(`予約期間の在庫カレンダー: ${calendars.length}件`);

    // 予約期間中のすべての日が仮予約済みになっているか確認
    const allDaysProvisional = calendars.every(calendar =>
      !calendar.isAvailable && calendar.unavailableReason === 'provisional'
    );

    // 予約IDが正しく設定されているか確認
    const allCalendarsHaveBookingId = calendars.every(calendar => calendar.bookingId === booking.id);

    const success = allDaysProvisional && allCalendarsHaveBookingId;

    logTestResult("仮予約の作成", success, {
      message: `予約期間中のすべての日が仮予約済み: ${allDaysProvisional}, 予約IDが正しく設定されている: ${allCalendarsHaveBookingId}`
    });

    return booking;
  } catch (error) {
    logTestResult("仮予約の作成", false, {
      error: error.message
    });

    return null;
  }
}

/**
 * 仮予約から本予約への変更テスト
 */
async function testConfirmBooking(provisionalBooking) {
  console.log("\nテストケース3: 仮予約から本予約への変更テスト");

  try {
    if (!provisionalBooking) {
      logTestResult("仮予約から本予約への変更", false, {
        message: "テスト対象の仮予約がありません"
      });

      return null;
    }

    console.log(`テスト対象仮予約: ID=${provisionalBooking.id}, 予約ID=${provisionalBooking.bookingId}`);

    // 仮予約を本予約に変更
    const confirmedBooking = await prisma.booking.update({
      where: {
        id: provisionalBooking.id
      },
      data: {
        status: 'CONFIRMED',
        bookingType: 'CONFIRMED',
        updatedAt: new Date()
      }
    });

    console.log(`仮予約を本予約に変更しました: ID=${confirmedBooking.id}, 予約ID=${confirmedBooking.bookingId}`);
    console.log(`ステータス: ${confirmedBooking.status}, タイプ: ${confirmedBooking.bookingType}`);

    // 在庫カレンダーも更新
    const startDate = new Date(provisionalBooking.startDate);
    const endDate = new Date(provisionalBooking.endDate);

    const dateRange = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      dateRange.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    for (const date of dateRange) {
      await prisma.inventoryCalendar.updateMany({
        where: {
          productId: provisionalBooking.productId,
          date,
          bookingId: provisionalBooking.id
        },
        data: {
          unavailableReason: 'reserved',
          updatedAt: new Date()
        }
      });
    }

    console.log(`在庫カレンダーを更新しました: ${dateRange.length}日分`);

    // 在庫カレンダーを確認
    const calendars = await prisma.inventoryCalendar.findMany({
      where: {
        productId: provisionalBooking.productId,
        date: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    console.log(`予約期間の在庫カレンダー: ${calendars.length}件`);

    // 予約期間中のすべての日が本予約済みになっているか確認
    const allDaysConfirmed = calendars.every(calendar =>
      !calendar.isAvailable && calendar.unavailableReason === 'reserved'
    );

    // 予約IDが正しく設定されているか確認
    const allCalendarsHaveBookingId = calendars.every(calendar => calendar.bookingId === provisionalBooking.id);

    const success = allDaysConfirmed && allCalendarsHaveBookingId;

    logTestResult("仮予約から本予約への変更", success, {
      message: `予約期間中のすべての日が本予約済み: ${allDaysConfirmed}, 予約IDが正しく設定されている: ${allCalendarsHaveBookingId}`
    });

    return confirmedBooking;
  } catch (error) {
    logTestResult("仮予約から本予約への変更", false, {
      error: error.message
    });

    return null;
  }
}

/**
 * バリエーションごとの在庫管理テスト
 */
async function testVariantInventory(productGroups) {
  console.log("\nテストケース4: バリエーションごとの在庫管理テスト");

  try {
    if (!productGroups || Object.keys(productGroups).length === 0) {
      logTestResult("バリエーションごとの在庫管理", false, {
        message: "テスト対象の商品グループがありません"
      });

      return false;
    }

    // テスト用の商品グループを選択
    const testGroupKey = Object.keys(productGroups)[0];
    const testGroup = productGroups[testGroupKey];

    if (testGroup.length < 2) {
      logTestResult("バリエーションごとの在庫管理", false, {
        message: "テスト対象の商品グループにバリエーションが2つ以上ありません"
      });

      return false;
    }

    console.log(`テスト対象商品グループ: ${testGroupKey} (${testGroup.length}個のバリエーション)`);
    testGroup.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.title} (SKU: ${product.sku})`);
    });

    // 予約期間を設定（来月の5日から7日）
    const today = new Date();
    const nextMonth = today.getMonth() + 1;
    const year = today.getFullYear() + (nextMonth > 11 ? 1 : 0);
    const month = nextMonth > 11 ? 0 : nextMonth;

    const startDate = new Date(year, month, 5);
    const endDate = new Date(year, month, 7);

    // 最初のバリエーションを予約
    const firstVariant = testGroup[0];
    const secondVariant = testGroup[1];

    console.log(`\n最初のバリエーションを予約: ${firstVariant.title} (SKU: ${firstVariant.sku})`);
    console.log(`期間: ${startDate.toISOString().split('T')[0]} 〜 ${endDate.toISOString().split('T')[0]}`);

    // 予約IDを生成
    const bookingId = `TEST-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

    // 予約データを作成
    const booking = await prisma.booking.create({
      data: {
        productId: firstVariant.id,
        startDate,
        endDate,
        status: 'CONFIRMED',
        bookingType: 'CONFIRMED',
        customerEmail: '<EMAIL>',
        customerName: 'テストユーザー',
        shop: 'peaces-test-block.myshopify.com',
        bookingId
      }
    });

    console.log(`予約を作成しました: ID=${booking.id}, 予約ID=${booking.bookingId}`);

    // 在庫カレンダーも更新
    const dateRange = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      dateRange.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    for (const date of dateRange) {
      await prisma.inventoryCalendar.upsert({
        where: {
          shop_productId_date: {
            shop: 'peaces-test-block.myshopify.com',
            productId: firstVariant.id,
            date
          }
        },
        update: {
          isAvailable: false,
          unavailableReason: 'reserved',
          bookingId: booking.id
        },
        create: {
          shop: 'peaces-test-block.myshopify.com',
          productId: firstVariant.id,
          shopifyProductId: firstVariant.id,
          date,
          isAvailable: false,
          unavailableReason: 'reserved',
          bookingId: booking.id
        }
      });
    }

    console.log(`在庫カレンダーを更新しました: ${dateRange.length}日分`);

    // 最初のバリエーションの在庫カレンダーを確認
    const firstVariantCalendars = await prisma.inventoryCalendar.findMany({
      where: {
        productId: firstVariant.id,
        date: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    console.log(`最初のバリエーションの予約期間の在庫カレンダー: ${firstVariantCalendars.length}件`);

    // 最初のバリエーションの予約期間中のすべての日が予約済みになっているか確認
    const firstVariantAllDaysReserved = firstVariantCalendars.every(calendar =>
      !calendar.isAvailable && calendar.unavailableReason === 'reserved'
    );

    console.log(`最初のバリエーションの予約期間中のすべての日が予約済み: ${firstVariantAllDaysReserved}`);

    // 2番目のバリエーションの在庫カレンダーを確認
    const secondVariantCalendars = await prisma.inventoryCalendar.findMany({
      where: {
        productId: secondVariant.id,
        date: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    console.log(`2番目のバリエーションの予約期間の在庫カレンダー: ${secondVariantCalendars.length}件`);

    // 2番目のバリエーションが予約されていないことを確認
    const secondVariantAvailable = secondVariantCalendars.length === 0 ||
      secondVariantCalendars.every(calendar => calendar.isAvailable);

    console.log(`2番目のバリエーションが予約されていない: ${secondVariantAvailable}`);

    // 2番目のバリエーションも同じ期間で予約可能か確認
    console.log(`\n2番目のバリエーションも同じ期間で予約可能か確認: ${secondVariant.title} (SKU: ${secondVariant.sku})`);

    // 予約IDを生成
    const secondBookingId = `TEST-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

    // 予約データを作成
    const secondBooking = await prisma.booking.create({
      data: {
        productId: secondVariant.id,
        startDate,
        endDate,
        status: 'CONFIRMED',
        bookingType: 'CONFIRMED',
        customerEmail: '<EMAIL>',
        customerName: 'テストユーザー2',
        shop: 'peaces-test-block.myshopify.com',
        bookingId: secondBookingId
      }
    });

    console.log(`2番目のバリエーションの予約を作成しました: ID=${secondBooking.id}, 予約ID=${secondBooking.bookingId}`);

    // 在庫カレンダーも更新
    for (const date of dateRange) {
      await prisma.inventoryCalendar.upsert({
        where: {
          shop_productId_date: {
            shop: 'peaces-test-block.myshopify.com',
            productId: secondVariant.id,
            date
          }
        },
        update: {
          isAvailable: false,
          unavailableReason: 'reserved',
          bookingId: secondBooking.id
        },
        create: {
          shop: 'peaces-test-block.myshopify.com',
          productId: secondVariant.id,
          shopifyProductId: secondVariant.id,
          date,
          isAvailable: false,
          unavailableReason: 'reserved',
          bookingId: secondBooking.id
        }
      });
    }

    console.log(`2番目のバリエーションの在庫カレンダーを更新しました: ${dateRange.length}日分`);

    // 2番目のバリエーションの在庫カレンダーを確認
    const updatedSecondVariantCalendars = await prisma.inventoryCalendar.findMany({
      where: {
        productId: secondVariant.id,
        date: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    console.log(`2番目のバリエーションの更新後の予約期間の在庫カレンダー: ${updatedSecondVariantCalendars.length}件`);

    // 2番目のバリエーションの予約期間中のすべての日が予約済みになっているか確認
    const secondVariantAllDaysReserved = updatedSecondVariantCalendars.every(calendar =>
      !calendar.isAvailable && calendar.unavailableReason === 'reserved'
    );

    console.log(`2番目のバリエーションの予約期間中のすべての日が予約済み: ${secondVariantAllDaysReserved}`);

    // 両方のバリエーションが同じ期間で予約できることを確認
    const success = firstVariantAllDaysReserved && secondVariantAllDaysReserved;

    logTestResult("バリエーションごとの在庫管理", success, {
      message: `両方のバリエーションが同じ期間で予約できる: ${success}`
    });

    return success;
  } catch (error) {
    logTestResult("バリエーションごとの在庫管理", false, {
      error: error.message
    });

    return false;
  }
}

/**
 * メイン関数
 */
async function runTests() {
  console.log("=== バリエーション（バリアント）の在庫管理テスト ===\n");

  try {
    // バリエーションを持つ商品を取得
    const { products, groups } = await getProductsWithVariants();

    // 仮予約の作成テスト
    const provisionalBooking = await testProvisionalBooking(products);

    // 仮予約から本予約への変更テスト
    const confirmedBooking = await testConfirmBooking(provisionalBooking);

    // バリエーションごとの在庫管理テスト
    await testVariantInventory(groups);

    // テスト結果のサマリーを表示
    console.log("\n=== テスト結果サマリー ===");
    const successCount = testResults.filter(r => r.success).length;
    const failureCount = testResults.length - successCount;

    console.log(`実行したテスト数: ${testResults.length}`);
    console.log(`成功: ${successCount}`);
    console.log(`失敗: ${failureCount}`);
    console.log("============================\n");

    // 詳細なテスト結果を表示
    console.log("詳細なテスト結果:");
    testResults.forEach((result, index) => {
      console.log(`${index + 1}. [${result.success ? 'SUCCESS' : 'FAILURE'}] ${result.testName}`);
      if (result.message) {
        console.log(`   ${result.message}`);
      }
    });

  } catch (error) {
    console.error("テスト実行中にエラーが発生しました:", error);
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// テストを実行
runTests();
