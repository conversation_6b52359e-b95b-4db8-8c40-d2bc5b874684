import React, { useState } from 'react';
import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import {
  Page,
  LegacyCard,
  ResourceList,
  ResourceItem,
  Filters,
  Badge,
  EmptyState,
  Text,
  Pagination
} from '@shopify/polaris';
import { prisma } from '../db.server';
import { MaintenanceStatus } from '@prisma/client';

// ステータスに応じたバッジ設定
const statusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  SCHEDULED: { tone: 'attention', label: '予定' },
  IN_PROGRESS: { tone: 'warning', label: '進行中' },
  COMPLETED: { tone: 'success', label: '完了' },
  CANCELLED: { tone: 'critical', label: 'キャンセル' }
};

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);

  // クエリパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  const limit = parseInt(url.searchParams.get('limit') || '50', 10);
  const status = url.searchParams.get('status') || undefined;
  const query = url.searchParams.get('query') || undefined;
  const startDate = url.searchParams.get('startDate') || undefined;
  const endDate = url.searchParams.get('endDate') || undefined;

  // 検索条件を構築
  const where: any = {};

  if (status) {
    where.status = status as MaintenanceStatus;
  }

  if (query) {
    where.OR = [
      {
        product: {
          title: { contains: query, mode: 'insensitive' }
        }
      },
      {
        product: {
          sku: { contains: query }
        }
      },
      { notes: { contains: query, mode: 'insensitive' } }
    ];
  }

  if (startDate) {
    where.startDate = { gte: new Date(startDate) };
  }

  if (endDate) {
    where.endDate = { lte: new Date(endDate) };
  }

  // メンテナンスデータを取得
  const maintenanceRecords = await prisma.maintenance.findMany({
    where,
    include: {
      product: true
    },
    orderBy: [
      { status: 'asc' },
      { startDate: 'asc' }
    ],
    skip: (page - 1) * limit,
    take: limit
  });

  // 総件数を取得
  const total = await prisma.maintenance.count({ where });

  console.log('メンテナンス検索結果:', {
    recordsCount: maintenanceRecords.length,
    total,
    where
  });

  return json({
    maintenanceRecords: maintenanceRecords.map(record => ({
      id: record.id,
      productId: record.productId,
      productTitle: record.product.title,
      productSku: record.product.sku,
      status: record.status,
      maintenanceType: record.type,
      description: record.notes || '',
      startDate: record.startDate.toISOString(),
      endDate: record.endDate?.toISOString() || record.startDate.toISOString(),
      cost: '0', // Maintenanceテーブルにはcostフィールドがないため
      assignedTo: '', // MaintenanceテーブルにはassignedToフィールドがないため
      notes: record.notes || ''
    })),
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}

export default function MaintenanceIndex() {
  const { maintenanceRecords, pagination } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');

  // メンテナンス詳細ページへ移動
  const handleMaintenanceClick = (maintenanceId: string) => {
    console.log('handleMaintenanceClick 呼び出し:', maintenanceId);
    console.log('navigate 関数:', typeof navigate);

    // Remixのnavigateを試す
    try {
      navigate(`/app/maintenance/${maintenanceId}`);
      console.log('navigate 実行完了');
    } catch (error) {
      console.error('navigate エラー:', error);
      // フォールバック: window.location.href を使用
      window.location.href = `/app/maintenance/${maintenanceId}?shop=peaces-test-block.myshopify.com`;
    }
  };

  // 強制的な遷移用の関数
  const forceNavigate = (path: string) => {
    console.log('forceNavigate 呼び出し:', path);
    window.location.href = `${path}?shop=peaces-test-block.myshopify.com`;
  };

  // ステータスフィルターを適用
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    const params = new URLSearchParams(window.location.search);

    if (value) {
      params.set('status', value);
    } else {
      params.delete('status');
    }

    navigate(`/app/maintenance?${params.toString()}`);
  };

  // 検索クエリを適用
  const handleSearchSubmit = () => {
    const params = new URLSearchParams(window.location.search);

    if (searchQuery) {
      params.set('query', searchQuery);
    } else {
      params.delete('query');
    }

    navigate(`/app/maintenance?${params.toString()}`);
  };

  // ページネーション処理
  const handlePaginationChange = (newPage: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set('page', newPage.toString());
    navigate(`/app/maintenance?${params.toString()}`);
  };

  // 日付をフォーマット
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  // ステータス変更処理
  const handleStatusChange = async (id: string, newStatus: string) => {
    try {
      const response = await fetch('/api/maintenance/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id,
          status: newStatus
        })
      });

      if (response.ok) {
        // ページをリロードして最新データを表示
        window.location.reload();
      } else {
        console.error('ステータス更新に失敗しました');
      }
    } catch (error) {
      console.error('ステータス更新エラー:', error);
    }
  };

  // フィルターオプションを生成
  const filters = [
    {
      key: 'status',
      label: 'ステータス',
      filter: (
        <select
          value={statusFilter}
          onChange={(e) => handleStatusFilterChange(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="">すべて</option>
          <option value="SCHEDULED">予定</option>
          <option value="IN_PROGRESS">進行中</option>
          <option value="COMPLETED">完了</option>
          <option value="CANCELLED">キャンセル</option>
        </select>
      ),
      shortcut: true,
    },
    {
      key: 'startDate',
      label: '開始日',
      filter: (
        <div>
          <input
            type="date"
            onChange={(e) => {
              const params = new URLSearchParams(window.location.search);
              if (e.target.value) {
                params.set('startDate', e.target.value);
              } else {
                params.delete('startDate');
              }
              navigate(`/app/maintenance?${params.toString()}`);
            }}
            style={{ width: '100%', padding: '8px' }}
          />
        </div>
      ),
    },
    {
      key: 'endDate',
      label: '終了日',
      filter: (
        <div>
          <input
            type="date"
            onChange={(e) => {
              const params = new URLSearchParams(window.location.search);
              if (e.target.value) {
                params.set('endDate', e.target.value);
              } else {
                params.delete('endDate');
              }
              navigate(`/app/maintenance?${params.toString()}`);
            }}
            style={{ width: '100%', padding: '8px' }}
          />
        </div>
      ),
    },
  ];

  return (
    <Page
      title="メンテナンス管理"
      primaryAction={{
        content: "新規メンテナンス",
        url: "/app/maintenance/new"
      }}
    >
      <LegacyCard>
        <ResourceList
          resourceName={{ singular: 'メンテナンス', plural: 'メンテナンス' }}
          items={maintenanceRecords}
          renderItem={(record) => {
            const { id, productTitle, productSku, status, maintenanceType, description, startDate, endDate, assignedTo, cost } = record;
            const badge = statusBadgeMap[status] || { tone: 'new', label: status };

            const shortcutActions = [
              {
                content: '詳細',
                onAction: () => {
                  console.log('詳細ボタンクリック:', id);
                  forceNavigate(`/app/maintenance/${id}`);
                },
              },
              {
                content: '編集',
                onAction: () => {
                  console.log('編集ボタンクリック:', id);
                  forceNavigate(`/app/maintenance/${id}/edit`);
                },
              }
            ];

            if (status === 'SCHEDULED') {
              shortcutActions.push({
                content: '開始',
                onAction: () => {
                  console.log('開始ボタンクリック:', id);
                  handleStatusChange(id, 'IN_PROGRESS');
                },
              });
            }

            if (status === 'IN_PROGRESS') {
              shortcutActions.push({
                content: '完了',
                onAction: () => {
                  console.log('完了ボタンクリック:', id);
                  handleStatusChange(id, 'COMPLETED');
                },
              });
            }

            return (
              <ResourceItem
                id={id}
                onClick={() => {
                  console.log('ResourceItem クリック:', id);
                  handleMaintenanceClick(id);
                }}
                shortcutActions={shortcutActions}
                persistActions
              >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <Text
                      as="h3"
                      variant="bodyMd"
                      fontWeight="bold"
                      style={{ cursor: 'pointer', color: '#1976d2' }}
                    >
                      {productTitle} (SKU: {productSku})
                    </Text>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodyMd">
                        {maintenanceType}: {description}
                      </Text>
                    </div>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        期間: {formatDate(startDate)} 〜 {formatDate(endDate)}
                      </Text>
                    </div>
                    {assignedTo && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          担当者: {assignedTo}
                        </Text>
                      </div>
                    )}
                    {cost && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          費用: ¥{parseInt(cost).toLocaleString()}
                        </Text>
                      </div>
                    )}
                  </div>
                  <div>
                    <Badge tone={badge.tone}>{badge.label}</Badge>
                  </div>
                </div>
              </ResourceItem>
            );
          }}
          filterControl={
            <Filters
              queryValue={searchQuery}
              filters={filters}
              onQueryChange={setSearchQuery}
              onQueryClear={() => setSearchQuery('')}
              onClearAll={() => {
                setSearchQuery('');
                setStatusFilter('');
                navigate('/app/maintenance');
              }}
              onQueryBlur={handleSearchSubmit}
              hideQueryField={false}
            />
          }
          emptyState={
            <EmptyState
              heading="メンテナンス記録がありません"
              image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
            >
              <p>メンテナンス記録を作成して、商品のメンテナンス状況を管理しましょう。</p>
            </EmptyState>
          }
        />
        {pagination.totalPages > 1 && (
          <div style={{ padding: '16px', display: 'flex', justifyContent: 'center' }}>
            <Pagination
              hasPrevious={pagination.page > 1}
              onPrevious={() => handlePaginationChange(pagination.page - 1)}
              hasNext={pagination.page < pagination.totalPages}
              onNext={() => handlePaginationChange(pagination.page + 1)}
            />
          </div>
        )}
      </LegacyCard>
      <div style={{ marginTop: '16px', textAlign: 'right' }}>
        <Text as="p" variant="bodySm" tone="subdued">
          合計 {pagination.total} 件のメンテナンス記録 ( {pagination.page} / {pagination.totalPages} ページ )
        </Text>
      </div>
    </Page>
  );
}
