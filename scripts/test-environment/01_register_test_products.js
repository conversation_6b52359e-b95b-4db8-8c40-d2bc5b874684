#!/usr/bin/env node

/**
 * テスト用商品登録スクリプト
 *
 * 正しいデータの流れに従う: Shopify → Prisma
 * 1. Shopifyに商品・バリアント・メタフィールドを作成
 * 2. Prismaデータベースに同期
 * 3. カテゴリマスタと連動
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { PrismaClient } from '@prisma/client';
import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const prisma = new PrismaClient();

// Shopify API設定
const SHOPIFY_SHOP = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

if (!SHOPIFY_ACCESS_TOKEN) {
  console.error('❌ SHOPIFY_ADMIN_API_ACCESS_TOKEN が設定されていません');
  process.exit(1);
}

/**
 * ソファCSVファイルを読み込んで商品データを生成
 */
function loadSofaProductsFromCSV() {
  const csvPath = path.join(__dirname, '../../master-data-csv/商品一覧_202505031329-sofa.csv');

  if (!fs.existsSync(csvPath)) {
    console.warn('ソファCSVファイルが見つかりません:', csvPath);
    return [];
  }

  const csvContent = fs.readFileSync(csvPath, 'utf-8');
  const lines = csvContent.split('\n').filter(line => line.trim());

  if (lines.length < 2) {
    console.warn('CSVファイルにデータがありません');
    return [];
  }

  // ヘッダー行をスキップして最初の5件のソファを取得（重複SKU除外）
  const products = [];
  const usedSKUs = new Set(); // 重複チェック用

  for (let i = 1; i <= Math.min(6, lines.length - 1); i++) {
    const line = lines[i];
    const columns = line.split(',').map(col => col.replace(/"/g, '').trim());

    // CSVの列構造に基づいてデータを抽出
    const [
      productBasicMasterId, productDetailMasterId, categoryMasterId, productCategory,
      productCode, productDetailCode, productName, productNameKana, productDetailName,
      partNumber, modelNumber, rentalPrice, stockQuantity, tags, stockLocation,
      sizeW, sizeD, sizeH, diameter, specialNotes, rentalStartDate, rentalEndDate,
      manufacturerName, purchaseLocation, purchaseStore, purchaseAmount, purchaseDate,
      productStatus, notes, janCode, status, disposalFlag, disposalDate, remarks
    ] = columns;

    // 商品ステータスが1（利用可能）で、在庫場所がNYまたはPRのもののみ
    if (productStatus === '1' && (stockLocation === 'NY' || stockLocation === 'PR')) {
      const sku = productCode || `SOFA-${i}`;

      // 重複SKUチェック
      if (usedSKUs.has(sku)) {
        console.log(`⚠️ 重複SKUをスキップ: ${sku}`);
        continue;
      }
      usedSKUs.add(sku);

      const basePrice = parseInt(rentalPrice) || 5000;

      products.push({
        sku,
        title: productName || `ソファ ${i}`,
        handle: `sofa-${sku.toLowerCase().replace(/[^a-z0-9]/g, '-')}-${Date.now()}`,
        basePrice,
        csvData: {
          menu: 'FURNITURE',
          majorCategory: 'ソファ',
          minorCategory: productCategory || 'その他ソファ',
          name: productName || `ソファ ${i}`,
          modelNumber: sku,
          sizeW: parseInt(sizeW) || 0,
          sizeD: parseInt(sizeD) || 0,
          sizeH: parseInt(sizeH) || 0,
          sizeSH: 0,
          sizeOther: specialNotes || '',
          color: '',
          colorOther: '',
          material: '',
          other: notes || '',
          stockQuantity: parseInt(stockQuantity) || 1,
          rentalFee1Day: basePrice,
          rentalFee1Night2Days: Math.floor(basePrice * 1.2),
          rentalFee2Nights3Days: Math.floor(basePrice * 1.4),
          rentalFee3Nights4Days: Math.floor(basePrice * 1.6),
          publicStatus: '公開',
          newStatus: 'あり',
          campaign: 'ソファコレクション'
        },
        location: stockLocation,
        status: 'available'
      });
    }
  }

  return products;
}

// ソファCSVからテスト商品データを生成
const TEST_PRODUCTS = loadSofaProductsFromCSV();

/**
 * Shopify GraphQL APIを呼び出し
 */
async function callShopifyGraphQL(query, variables = {}) {
  const response = await fetch(`https://${SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN
    },
    body: JSON.stringify({ query, variables })
  });

  const data = await response.json();

  if (data.errors) {
    console.error('GraphQLエラー:', JSON.stringify(data.errors, null, 2));
    throw new Error('Shopify API エラー');
  }

  return data.data;
}

/**
 * カテゴリマスタからカテゴリ情報を取得
 */
async function getCategoryInfo(majorCategory, minorCategory) {
  try {
    const shop = SHOPIFY_SHOP;

    // CSVのカテゴリ名からカテゴリマスタのコードにマッピング
    const categoryMapping = {
      'ニュアンスオブジェ': '201',
      '花器': '212',
      'キッチン': '201',
      'コスメ': '206',
      'キャンドル': '201',
      'オブジェ': '211',
      'ガーデン・アウトドア': '104'
    };

    const subCategoryMapping = {
      'その他オブジェ': '06',
      'ゴールド・シルバー': '01',
      'コスメボックス・トレー': '08',
      '銀食器': '07',
      'キャンドルスタンド': '05',
      'BBQグッズ': '10'
    };

    // メジャーカテゴリから最初のカテゴリを取得
    const firstMajorCategory = majorCategory.split(';')[0];
    const categoryCode = categoryMapping[firstMajorCategory] || '201';

    // マイナーカテゴリから最初のサブカテゴリを取得
    const firstMinorCategory = minorCategory.split(';')[0];
    const subCategoryCode = subCategoryMapping[firstMinorCategory] || '06';

    // カテゴリマスタから実際のデータを取得
    const category = await prisma.categoryMaster.findUnique({
      where: {
        shop_code: {
          shop,
          code: categoryCode
        }
      }
    });

    const subCategory = await prisma.subCategoryMaster.findUnique({
      where: {
        shop_code_parentCategoryCode: {
          shop,
          code: subCategoryCode,
          parentCategoryCode: categoryCode
        }
      }
    });

    return {
      categoryCode: category?.code || categoryCode,
      categoryName: category?.name || firstMajorCategory,
      subCategoryCode: subCategory?.code || subCategoryCode,
      subCategoryName: subCategory?.name || firstMinorCategory
    };

  } catch (error) {
    console.warn(`カテゴリ情報取得エラー: ${error.message}`);
    return {
      categoryCode: '201',
      categoryName: 'ニュアンスオブジェ',
      subCategoryCode: '06',
      subCategoryName: 'その他オブジェ'
    };
  }
}

/**
 * レンタル期間バリアントの価格計算
 * 1日: 100%
 * 2-7日: 基本料金 + (基本料金 × 0.2 × (日数-1))
 * 8日以上: 7日分の料金（基本料金 + 基本料金 × 0.2 × 6）
 */
function calculateVariantPrice(basePrice, days) {
  if (days === 1) return basePrice;
  if (days >= 2 && days <= 7) return Math.floor(basePrice + (basePrice * 0.2 * (days - 1)));
  if (days >= 8) return Math.floor(basePrice + (basePrice * 0.2 * 6)); // 8日以上は7日分の料金
  return basePrice;
}

/**
 * 仮予約価格の計算
 */
function calculateProvisionalPrice(basePrice) {
  return Math.floor(basePrice * 0.1); // 10%
}

/**
 * 商品バリアントを作成（Shopify用データ構造）
 */
function createProductVariants(productData) {
  const { sku, title, basePrice } = productData;
  const variants = [];

  // 1-7日レンタルバリアント
  for (let days = 1; days <= 7; days++) {
    variants.push({
      sku: `${sku}-${days}D`,
      title: `${days}日レンタル`,
      price: calculateVariantPrice(basePrice, days),
      inventory: 1,
      option1: `${days}日レンタル`
    });
  }

  // 8日以上レンタルバリアント
  variants.push({
    sku: `${sku}-8D+`,
    title: '8日以上レンタル',
    price: calculateVariantPrice(basePrice, 8),
    inventory: 1,
    option1: '8日以上レンタル'
  });

  // 仮予約バリアント
  variants.push({
    sku: `${sku}-PROV`,
    title: '仮予約',
    price: calculateProvisionalPrice(basePrice),
    inventory: 1,
    option1: '仮予約'
  });

  return variants;
}

/**
 * ロケーション名からShopify Location IDを取得
 */
function getLocationId(locationName) {
  const locationMap = {
    'NY': 'gid://shopify/Location/77253673128',
    'PR': 'gid://shopify/Location/77235978408'
  };
  return locationMap[locationName] || locationMap['NY']; // デフォルトはNY
}

/**
 * Shopifyに商品を作成（正しいAPIスキーマ使用）
 */
async function createShopifyProduct(productData) {
  const { sku, title, handle, basePrice, csvData, location, status } = productData;

  // カテゴリ情報を取得
  const categoryInfo = await getCategoryInfo(csvData.majorCategory, csvData.minorCategory);

  // 在庫場所のロケーションIDを取得
  const locationId = getLocationId(location);

  // メタフィールド作成（既存定義に合わせて）
  const metafields = [
    // レンタル商品基本情報
    {
      namespace: 'rental',
      key: 'basic_info',
      value: JSON.stringify({
        productCode: sku,
        detailCode: sku,
        kana: csvData.name || title,
        ...csvData,
        categoryMasterCode: categoryInfo.categoryCode,
        categoryMasterName: categoryInfo.categoryName,
        subCategoryMasterCode: categoryInfo.subCategoryCode,
        subCategoryMasterName: categoryInfo.subCategoryName
      }),
      type: 'json'
    },
    // レンタル料金設定
    {
      namespace: 'rental',
      key: 'pricing',
      value: JSON.stringify({
        basePrice: basePrice,
        depositRate: 0.1,
        discountRules: {
          day2_7_rate: 0.2,
          day8_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30,
        variantPrices: {
          '1day': calculateVariantPrice(basePrice, 1),
          '2day': calculateVariantPrice(basePrice, 2),
          '3day': calculateVariantPrice(basePrice, 3),
          '4day': calculateVariantPrice(basePrice, 4),
          '5day': calculateVariantPrice(basePrice, 5),
          '6day': calculateVariantPrice(basePrice, 6),
          '7day': calculateVariantPrice(basePrice, 7),
          '8plus': calculateVariantPrice(basePrice, 8)
        }
      }),
      type: 'json'
    },
    // 在庫場所（独立したメタフィールド）
    {
      namespace: 'rental',
      key: 'location',
      value: location,
      type: 'single_line_text_field'
    },
    // レンタル状態（独立したメタフィールド）
    {
      namespace: 'rental',
      key: 'status',
      value: status,
      type: 'single_line_text_field'
    },
    // バリエーションタイプ
    {
      namespace: 'rental',
      key: 'variation_type',
      value: 'レンタル日数',
      type: 'single_line_text_field'
    },
    // 予約タイプ（デフォルトは本予約）
    {
      namespace: 'rental',
      key: 'booking_type',
      value: 'regular',
      type: 'single_line_text_field'
    }
  ];

  // 1. 基本商品を作成（オプション値を事前定義）
  const productMutation = `
    mutation productCreate($product: ProductCreateInput!) {
      productCreate(product: $product) {
        product {
          id
          title
          handle
          variants(first: 1) {
            edges {
              node {
                id
                sku
                price
              }
            }
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const productVariables = {
    product: {
      title,
      handle,
      tags: ['テスト', 'レンタル商品', csvData.menu],
      productType: 'レンタル商品',
      vendor: 'PEACES',
      metafields,
      productOptions: [
        {
          name: 'レンタル期間',
          position: 1,
          values: [
            { name: '1日レンタル' },
            { name: '2日レンタル' },
            { name: '3日レンタル' },
            { name: '4日レンタル' },
            { name: '5日レンタル' },
            { name: '6日レンタル' },
            { name: '7日レンタル' },
            { name: '8日以上レンタル' }
          ]
        }
      ]
    }
  };

  const productResult = await callShopifyGraphQL(productMutation, productVariables);

  if (productResult.productCreate.userErrors.length > 0) {
    throw new Error(`Shopify商品作成エラー: ${JSON.stringify(productResult.productCreate.userErrors)}`);
  }

  const product = productResult.productCreate.product;
  const defaultVariantId = product.variants.edges[0].node.id;
  console.log(`✅ Shopify商品作成: ${sku} - ${title}`);

  // 2. デフォルトバリアント（1日レンタル）を更新
  const updateDefaultVariantMutation = `
    mutation productVariantsBulkUpdate($productId: ID!, $variants: [ProductVariantsBulkInput!]!) {
      productVariantsBulkUpdate(productId: $productId, variants: $variants) {
        productVariants {
          id
          sku
          price
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const defaultVariantVariables = {
    productId: product.id,
    variants: [
      {
        id: defaultVariantId,
        price: calculateVariantPrice(basePrice, 1).toString(),
        inventoryItem: {
          sku: `${sku}-1D`,
          tracked: true
        }
      }
    ]
  };

  const defaultVariantResult = await callShopifyGraphQL(updateDefaultVariantMutation, defaultVariantVariables);

  if (defaultVariantResult.productVariantsBulkUpdate.userErrors.length > 0) {
    console.warn(`デフォルトバリアント更新警告: ${JSON.stringify(defaultVariantResult.productVariantsBulkUpdate.userErrors)}`);
  } else {
    console.log(`✅ デフォルトバリアント更新完了: 1日レンタル`);
  }

  // 3. inventoryItemIdを取得
  const getInventoryItemQuery = `
    query getVariantInventoryItem($id: ID!) {
      productVariant(id: $id) {
        inventoryItem {
          id
        }
      }
    }
  `;

  const inventoryItemResult = await callShopifyGraphQL(getInventoryItemQuery, { id: defaultVariantId });
  const inventoryItemId = inventoryItemResult.productVariant?.inventoryItem?.id;

  if (inventoryItemId) {
    // 4a. 在庫アイテムをロケーションでアクティベート
    const activateMutation = `
      mutation inventoryActivate($inventoryItemId: ID!, $locationId: ID!, $available: Int) {
        inventoryActivate(inventoryItemId: $inventoryItemId, locationId: $locationId, available: $available) {
          inventoryLevel {
            id
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    try {
      const activateResult = await callShopifyGraphQL(activateMutation, {
        inventoryItemId: inventoryItemId,
        locationId: locationId,
        available: status === 'available' ? csvData.stockQuantity : 0
      });

      if (activateResult.inventoryActivate.userErrors.length > 0) {
        console.warn(`在庫アクティベート警告: ${JSON.stringify(activateResult.inventoryActivate.userErrors)}`);

        // アクティベートに失敗した場合、従来の方法を試す
        const inventoryMutation = `
          mutation inventorySetQuantities($input: InventorySetQuantitiesInput!) {
            inventorySetQuantities(input: $input) {
              inventoryAdjustmentGroup {
                id
              }
              userErrors {
                field
                message
              }
            }
          }
        `;

        const inventoryVariables = {
          input: {
            reason: "correction",
            name: "available",
            ignoreCompareQuantity: true,
            quantities: [
              {
                inventoryItemId: inventoryItemId,
                locationId: locationId,
                quantity: status === 'available' ? csvData.stockQuantity : 0
              }
            ]
          }
        };

        const inventoryResult = await callShopifyGraphQL(inventoryMutation, inventoryVariables);
        if (inventoryResult.inventorySetQuantities.userErrors.length > 0) {
          console.warn(`在庫設定警告: ${JSON.stringify(inventoryResult.inventorySetQuantities.userErrors)}`);
        } else {
          console.log(`✅ 在庫設定完了（代替方法）: ${csvData.stockQuantity}個`);
        }
      } else {
        console.log(`✅ 在庫アクティベート完了: ${csvData.stockQuantity}個`);
      }
    } catch (error) {
      console.warn(`在庫設定エラー: ${error.message}`);
    }
  } else {
    console.warn('inventoryItemIdが取得できませんでした');
  }

  // 4. 追加バリアント（2-8日）を作成
  const additionalVariants = [];

  for (let days = 2; days <= 7; days++) {
    additionalVariants.push({
      price: calculateVariantPrice(basePrice, days).toString(),
      inventoryItem: {
        sku: `${sku}-${days}D`,
        tracked: true
      },
      optionValues: [
        {
          optionName: 'レンタル期間',
          name: `${days}日レンタル`
        }
      ]
    });
  }

  // 8日以上バリアント
  additionalVariants.push({
    price: calculateVariantPrice(basePrice, 8).toString(),
    inventoryItem: {
      sku: `${sku}-8D+`,
      tracked: true
    },
    optionValues: [
      {
        optionName: 'レンタル期間',
        name: '8日以上レンタル'
      }
    ]
  });

  // 追加バリアント作成
  const additionalVariantMutation = `
    mutation productVariantsBulkCreate($productId: ID!, $variants: [ProductVariantsBulkInput!]!) {
      productVariantsBulkCreate(productId: $productId, variants: $variants) {
        productVariants {
          id
          sku
          price
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const additionalVariantVariables = {
    productId: product.id,
    variants: additionalVariants
  };

  const additionalVariantResult = await callShopifyGraphQL(additionalVariantMutation, additionalVariantVariables);

  if (additionalVariantResult.productVariantsBulkCreate.userErrors.length > 0) {
    console.warn(`追加バリアント作成警告: ${JSON.stringify(additionalVariantResult.productVariantsBulkCreate.userErrors)}`);
  } else {
    console.log(`✅ 追加バリアント作成完了: ${additionalVariants.length}個`);

    // 追加バリアントの在庫設定（改善版）
    const createdVariants = additionalVariantResult.productVariantsBulkCreate.productVariants;
    console.log(`📦 追加バリアントの在庫設定開始: ${createdVariants.length}個`);

    for (const variant of createdVariants) {
      try {
        const variantInventoryResult = await callShopifyGraphQL(getInventoryItemQuery, { id: variant.id });
        const variantInventoryItemId = variantInventoryResult.productVariant?.inventoryItem?.id;

        if (variantInventoryItemId) {
          // まずアクティベートを試す
          try {
            const variantActivateMutation = `
              mutation inventoryActivate($inventoryItemId: ID!, $locationId: ID!, $available: Int) {
                inventoryActivate(inventoryItemId: $inventoryItemId, locationId: $locationId, available: $available) {
                  inventoryLevel {
                    id
                  }
                  userErrors {
                    field
                    message
                  }
                }
              }
            `;

            const variantActivateResult = await callShopifyGraphQL(variantActivateMutation, {
              inventoryItemId: variantInventoryItemId,
              locationId: locationId,
              available: status === 'available' ? csvData.stockQuantity : 0
            });

            if (variantActivateResult.inventoryActivate.userErrors.length === 0) {
              console.log(`✅ バリアント在庫アクティベート完了: ${variant.sku}`);
            } else {
              console.warn(`バリアント在庫アクティベート警告 (${variant.sku}): ${JSON.stringify(variantActivateResult.inventoryActivate.userErrors)}`);
            }
          } catch (activateError) {
            console.warn(`バリアント在庫アクティベートエラー (${variant.sku}): ${activateError.message}`);
          }
        } else {
          console.warn(`inventoryItemId取得失敗: ${variant.sku}`);
        }
      } catch (error) {
        console.warn(`バリアント在庫設定エラー: ${variant.sku} - ${error.message}`);
      }
    }
  }

  // 5. 全バリアントを取得してマッピングを作成
  const allVariantsQuery = `
    query getProductVariants($id: ID!) {
      product(id: $id) {
        variants(first: 10) {
          edges {
            node {
              id
              sku
              price
              selectedOptions {
                name
                value
              }
            }
          }
        }
      }
    }
  `;

  const allVariantsResult = await callShopifyGraphQL(allVariantsQuery, { id: product.id });
  const allVariants = allVariantsResult.product.variants.edges.map(edge => edge.node);

  // バリアントマッピングを作成
  const variantMapping = {};
  allVariants.forEach(variant => {
    const optionValue = variant.selectedOptions.find(opt => opt.name === 'レンタル期間')?.value;
    if (optionValue) {
      if (optionValue === '1日レンタル') variantMapping['1day'] = variant.id;
      else if (optionValue === '2日レンタル') variantMapping['2day'] = variant.id;
      else if (optionValue === '3日レンタル') variantMapping['3day'] = variant.id;
      else if (optionValue === '4日レンタル') variantMapping['4day'] = variant.id;
      else if (optionValue === '5日レンタル') variantMapping['5day'] = variant.id;
      else if (optionValue === '6日レンタル') variantMapping['6day'] = variant.id;
      else if (optionValue === '7日レンタル') variantMapping['7day'] = variant.id;
      else if (optionValue === '8日以上レンタル') variantMapping['8plus'] = variant.id;
    }
  });

  // バリアントマッピングメタフィールドを追加
  const mappingMutation = `
    mutation productUpdate($input: ProductInput!) {
      productUpdate(input: $input) {
        product {
          id
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const mappingVariables = {
    input: {
      id: product.id,
      metafields: [
        {
          namespace: 'rental',
          key: 'variant_mapping',
          value: JSON.stringify(variantMapping),
          type: 'json'
        }
      ]
    }
  };

  const mappingResult = await callShopifyGraphQL(mappingMutation, mappingVariables);

  if (mappingResult.productUpdate.userErrors.length > 0) {
    console.warn(`バリアントマッピング設定警告: ${JSON.stringify(mappingResult.productUpdate.userErrors)}`);
  } else {
    console.log(`✅ バリアントマッピング設定完了: ${Object.keys(variantMapping).length}個`);
  }

  return {
    ...product,
    variants: allVariants,
    variantMapping
  };
}

/**
 * Prismaに商品を同期
 */
async function syncProductToPrisma(shopifyProduct, productData) {
  const { sku, title, basePrice, csvData, location, status } = productData;
  const categoryInfo = await getCategoryInfo(csvData.majorCategory, csvData.minorCategory);

  // basicInfo構築
  const basicInfo = {
    ...csvData,
    categoryMasterCode: categoryInfo.categoryCode,
    categoryMasterName: categoryInfo.categoryName,
    subCategoryMasterCode: categoryInfo.subCategoryCode,
    subCategoryMasterName: categoryInfo.subCategoryName,
    location: location,
    status: status
  };

  const product = await prisma.product.create({
    data: {
      shop: SHOPIFY_SHOP,
      shopifyId: shopifyProduct.id,
      sku,
      title,
      price: basePrice,
      status: status === 'maintenance' ? 'MAINTENANCE' : 'AVAILABLE',
      basicInfo: JSON.stringify(basicInfo),
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });

  console.log(`✅ Prisma同期完了: ${sku}`);
  return product;
}

/**
 * テスト用商品を一括登録（本格運用版: Shopify → Prisma）
 */
async function registerTestProducts() {
  console.log('=== 本格運用版商品登録システム ===');
  console.log('データの流れ: Shopify → Prisma');
  console.log(`対象ショップ: ${SHOPIFY_SHOP}`);
  console.log(`登録予定商品数: ${TEST_PRODUCTS.length}件`);
  console.log(`実行時刻: ${new Date().toLocaleString('ja-JP')}`);

  const startTime = Date.now();
  let successCount = 0;
  let errorCount = 0;

  try {
    await prisma.$connect();
    console.log('✅ データベース接続成功');

    // 既存のテスト商品を削除
    console.log('\n既存のテスト商品を削除中...');
    const testSKUs = TEST_PRODUCTS.map(p => p.sku);

    // 関連する予約データを削除
    try {
      await prisma.booking.deleteMany({
        where: {
          shop: SHOPIFY_SHOP,
          product: {
            sku: { in: testSKUs }
          }
        }
      });
    } catch (error) {
      console.log('予約データ削除スキップ（テーブルが存在しない可能性）');
    }

    // 商品を削除
    const deletedCount = await prisma.product.deleteMany({
      where: {
        shop: SHOPIFY_SHOP,
        sku: { in: testSKUs }
      }
    });

    console.log(`🗑️ 既存商品削除完了: ${deletedCount.count}件`);

    // 新しい商品を登録（Shopify → Prisma）
    console.log('\n新しいテスト商品を登録中...');
    const registeredProducts = [];

    for (const productData of TEST_PRODUCTS) {
      console.log(`\n📦 商品登録中: ${productData.sku} - ${productData.title}`);

      try {
        // 1. Shopifyに商品作成
        const shopifyProduct = await createShopifyProduct(productData);

        // 2. Prismaに同期
        const prismaProduct = await syncProductToPrisma(shopifyProduct, productData);

        registeredProducts.push(prismaProduct);
        console.log(`✅ 商品登録成功: ${productData.sku}`);
        successCount++;

      } catch (error) {
        console.error(`❌ 商品登録エラー (${productData.sku}):`, error.message);
        errorCount++;
        continue; // 次の商品に進む
      }
    }

    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);

    console.log(`\n=== 登録完了統計 ===`);
    console.log(`✅ 成功: ${successCount}件`);
    console.log(`❌ 失敗: ${errorCount}件`);
    console.log(`⏱️ 実行時間: ${duration}秒`);
    console.log(`📊 成功率: ${Math.round((successCount / TEST_PRODUCTS.length) * 100)}%`);

    return registeredProducts;

  } catch (error) {
    console.error('❌ エラー:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * メイン処理
 */
async function main() {
  try {
    const products = await registerTestProducts();

    console.log('\n=== 登録完了 ===');
    console.log('次のステップ:');
    console.log('1. Shopifyに商品とバリアントを同期');
    console.log('2. テスト用予約データの作成');
    console.log('3. 価格計算テストの実行');
    console.log('4. 予約システムの動作確認');

  } catch (error) {
    console.error('テスト商品登録に失敗しました:', error);
    process.exit(1);
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
