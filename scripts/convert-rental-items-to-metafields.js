import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// csv-parse/syncがない場合の代替処理
function parseCsv(content) {
  const lines = content.split('\n');
  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
  
  const records = [];
  for (let i = 1; i < lines.length; i++) {
    if (!lines[i].trim()) continue;
    
    // CSVの行を適切に分割（引用符内のカンマを考慮）
    const row = lines[i];
    const values = [];
    let inQuotes = false;
    let currentValue = '';
    
    for (let j = 0; j < row.length; j++) {
      const char = row[j];
      
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        values.push(currentValue.trim().replace(/"/g, ''));
        currentValue = '';
      } else {
        currentValue += char;
      }
    }
    
    // 最後の値を追加
    values.push(currentValue.trim().replace(/"/g, ''));
    
    // レコードを作成
    const record = {};
    headers.forEach((header, index) => {
      if (index < values.length) {
        record[header] = values[index];
      } else {
        record[header] = '';
      }
    });
    
    records.push(record);
  }
  
  return records;
}

// CSVファイルの読み込み
function readCSV(filePath) {
  try {
    const content = fs.readFileSync(filePath, { encoding: 'utf-8' });
    // 自作のCSVパーサーを使用
    const records = parseCsv(content);
    return records;
  } catch (error) {
    console.error('CSVファイルの読み込みエラー:', error);
    throw error;
  }
}

// メタフィールド用CSVの作成
function createMetafieldsCSV(inputRecords, outputPath) {
  // ヘッダー行
  const headers = [
    'sku',
    'height',
    'width',
    'depth',
    'weight',
    'material',
    'color',
    'location',
    'status'
  ];
  
  // データ行の作成
  const rows = inputRecords.map(record => {
    // SKUの取得（型番を使用）
    const sku = record['型番'] || '';
    
    // 各フィールドの取得
    const height = record['サイズH'] || '0';
    const width = record['サイズW'] || '0';
    const depth = record['サイズD'] || '0';
    const weight = ''; // 重量情報がない場合は空欄
    const material = record['素材'] || '';
    const color = record['色'] || '';
    const location = 'NY'; // デフォルト値
    const status = record['公開ステータス(公開/非公開)'] === '公開' ? 'available' : 'unavailable';
    
    // CSV行の作成
    return [
      sku,
      height,
      width,
      depth,
      weight,
      material,
      color,
      location,
      status
    ].join(',');
  });
  
  // ヘッダーと行を結合してCSVを作成
  const csvContent = [headers.join(','), ...rows].join('\n');
  
  // ファイルに書き込み
  fs.writeFileSync(outputPath, csvContent);
  
  return rows.length;
}

// メイン処理
function convertRentalItemsToMetafields(inputPath, outputPath) {
  try {
    console.log(`CSVファイルを読み込み中: ${inputPath}`);
    const records = readCSV(inputPath);
    console.log(`${records.length}件のレコードを読み込みました`);
    
    // 型番が空でないレコードのみを抽出
    const validRecords = records.filter(record => record['型番'] && record['型番'].trim() !== '');
    console.log(`有効なレコード数: ${validRecords.length}件`);
    
    // メタフィールド用CSVの作成
    const count = createMetafieldsCSV(validRecords, outputPath);
    console.log(`メタフィールド用CSVを作成しました: ${outputPath} (${count}件)`);
  } catch (error) {
    console.error('処理中にエラーが発生しました:', error);
  }
}

// コマンドライン引数からファイルパスを取得
const inputPath = process.argv[2];
const outputPath = process.argv[3] || 'metafields-output.csv';

if (!inputPath) {
  console.error('使用方法: node convert-rental-items-to-metafields.js <入力CSVパス> [出力CSVパス]');
  process.exit(1);
}

// 実行
convertRentalItemsToMetafields(inputPath, outputPath);
