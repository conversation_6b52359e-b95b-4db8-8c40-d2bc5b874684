# エッジケースと境界値テストレポート

## テスト概要

このレポートは、年末年始や連休などの特殊期間の予約、予約期間の最大・最小値、在庫数ゼロの状態からの回復、無効なデータ入力時の処理のテスト結果をまとめたものです。

テスト実行日時: 2025/5/19 16:13:19
実行時間: 25.08秒

## テスト環境

- Node.js バージョン: v20.18.1
- OS: darwin arm64

## テスト結果

- 合計テスト数: 10
- 成功: 7
- 失敗: 3
- 成功率: 70%

## パフォーマンス分析

- 平均テスト実行時間: 2332.80ミリ秒
- 最も遅いテスト: 予約期間の最大値テスト (14186.00ミリ秒)
- 最も速いテスト: 予約をキャンセルして在庫を回復するテスト (0.00ミリ秒)

## 詳細結果

### テストケース1: 年末年始の予約テスト

- 結果: ✅ 成功
- メッセージ: 年末年始の予約が正常に作成されました

- 実行時間: 4467.00ミリ秒
- 詳細:
```json
{
  "bookingId": "954763e0-bf5b-459b-ae36-a2baf07e9dea",
  "period": "年末年始",
  "startDate": "2025-12-29",
  "endDate": "2026-01-03"
}
```

### テストケース2: 予約期間の最小値テスト

- 結果: ✅ 成功
- メッセージ: 最小予約期間（1日）の予約が正常に作成されました

- 実行時間: 977.00ミリ秒
- 詳細:
```json
{
  "bookingId": "c5a33d37-620b-4896-9af7-660b8e98aef7",
  "durationDays": 1
}
```

### テストケース3: 予約期間の最大値テスト

- 結果: ✅ 成功
- メッセージ: 最大予約期間（30日）の予約が正常に作成されました

- 実行時間: 14186.00ミリ秒
- 詳細:
```json
{
  "bookingId": "fb17c07e-fd58-4393-be45-55693dfeba19",
  "durationDays": 30
}
```

### テストケース4: 過去の日付での予約テスト

- 結果: ✅ 成功
- メッセージ: 過去の日付での予約は正しくエラーになりました
- エラー: 過去の日付での予約がエラーにならずに成功してしまいました
- 実行時間: 2321.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース5: 終了日が開始日より前の予約テスト

- 結果: ✅ 成功
- メッセージ: 終了日が開始日より前の予約は正しくエラーになりました
- エラー: 終了日が開始日より前の予約がエラーにならずに成功してしまいました
- 実行時間: 467.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース6: 無効なメールアドレスでの予約テスト

- 結果: ✅ 成功
- メッセージ: 無効なメールアドレスでの予約は正しくエラーになりました

- 実行時間: 225.00ミリ秒
- 詳細:
```json
{
  "expectedError": true,
  "invalidEmail": "invalid-email"
}
```

### テストケース7: 在庫をゼロにするテスト

- 結果: ❌ 失敗
- メッセージ: 在庫をゼロにすることに失敗しました
- エラー: カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-08, 2025-06-09, 2025-06-10, 2025-06-11
- 実行時間: 233.00ミリ秒
- 詳細:
```json
{
  "bookingId": ""
}
```

### テストケース8: 在庫がゼロの状態で予約を試みるテスト

- 結果: ✅ 成功
- メッセージ: 在庫がゼロの状態での予約は正しくエラーになりました

- 実行時間: 226.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース9: 予約をキャンセルして在庫を回復するテスト

- 結果: ❌ 失敗
- メッセージ: 予約をキャンセルして在庫を回復することに失敗しました
- エラー: テスト1で作成した予約IDがありません
- 実行時間: 不明
- 詳細:
```json
{
  "bookingId": ""
}
```

### テストケース10: 在庫が回復した状態で予約を試みるテスト

- 結果: ❌ 失敗
- メッセージ: 在庫が回復した状態での予約に失敗しました
- エラー: カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-08, 2025-06-09, 2025-06-10, 2025-06-11
- 実行時間: 226.00ミリ秒
- 詳細:
```json
{
  "bookingId": ""
}
```

