const { GraphQLClient, gql } = require('graphql-request');
const dotenv = require('dotenv');

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品検索のクエリ
const GET_PRODUCTS_QUERY = gql`
  query getProducts($query: String!, $first: Int!, $after: String) {
    products(first: $first, after: $after, query: $query) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          handle
          vendor
          productType
          tags
          variants(first: 50) {
            edges {
              node {
                id
                title
                sku
                price
                inventoryQuantity
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    }
  }
`;

// 商品のタグを更新するミューテーション
const UPDATE_PRODUCT_TAGS_MUTATION = gql`
  mutation productUpdate($input: ProductInput!) {
    productUpdate(input: $input) {
      product {
        id
        tags
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールドの設定
async function setJsonMetafield(productId, namespace, key, jsonValue) {
  const mutation = gql`
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    metafields: [
      {
        ownerId: productId,
        namespace,
        key,
        value: JSON.stringify(jsonValue),
        type: 'json',
      }
    ]
  };

  try {
    const result = await graphQLClient.request(mutation, variables);
    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', result.metafieldsSet.userErrors);
      return false;
    }
    return true;
  } catch (error) {
    console.error(`メタフィールド ${namespace}.${key} 設定エラー:`, error);
    return false;
  }
}

// 商品を検索する関数
async function getProducts(query = '', limit = 50) {
  const products = [];
  let hasNextPage = true;
  let cursor = null;

  while (hasNextPage) {
    try {
      const variables = {
        query,
        first: limit,
        after: cursor
      };

      const result = await graphQLClient.request(GET_PRODUCTS_QUERY, variables);

      const edges = result.products.edges;
      products.push(...edges.map(edge => edge.node));

      hasNextPage = result.products.pageInfo.hasNextPage;
      cursor = result.products.pageInfo.endCursor;
    } catch (error) {
      console.error('商品検索エラー:', error);
      hasNextPage = false;
    }
  }

  return products;
}

// 商品のタグを更新する関数
async function updateProductTags(productId, tags) {
  try {
    const variables = {
      input: {
        id: productId,
        tags
      }
    };

    const result = await graphQLClient.request(UPDATE_PRODUCT_TAGS_MUTATION, variables);

    if (result.productUpdate.userErrors.length > 0) {
      console.error('商品タグ更新エラー:', result.productUpdate.userErrors);
      return false;
    }

    return true;
  } catch (error) {
    console.error('商品タグ更新中にエラーが発生しました:', error);
    return false;
  }
}

// 現在の日付から指定した日数後の日付を取得
function getDateAfterDays(days) {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return date.toISOString().split('T')[0];
}

// テスト用の予約情報を生成する関数
function generateTestReservations(product) {
  const variants = product.variants.edges.map(edge => edge.node);
  const baseVariant = variants.find(v =>
    v.title.includes('1日') || v.title === 'Default Title'
  );

  if (!baseVariant) return null;

  // 商品タイトルから状況を判断
  const title = product.title.toLowerCase();

  // 在庫アイテム情報を取得
  const inventoryItemsMetafield = product.metafields.edges.find(
    edge => edge.node.namespace === 'rental' && edge.node.key === 'inventory_items'
  );

  if (!inventoryItemsMetafield) return null;

  const inventoryItems = JSON.parse(inventoryItemsMetafield.node.value);

  // 予約情報を生成
  const reservationInfo = [];

  for (let i = 0; i < inventoryItems.length; i++) {
    const item = inventoryItems[i];
    const reservations = [];

    // 商品ごとに異なる予約状況を生成
    if (title.includes('ベーシックソファ') && i === 0) {
      // 確定予約と仮予約
      reservations.push({
        id: `reservation-${item.id}-001`,
        startDate: getDateAfterDays(5),
        endDate: getDateAfterDays(8),
        status: 'confirmed',
        customerName: '山田太郎',
        customerEmail: '<EMAIL>',
        orderId: 'gid://shopify/Order/1001',
        orderLineItemId: 'gid://shopify/LineItem/1001',
        notes: '特になし'
      });

      reservations.push({
        id: `reservation-${item.id}-002`,
        startDate: getDateAfterDays(15),
        endDate: getDateAfterDays(20),
        status: 'pending',
        customerName: '佐藤花子',
        customerEmail: '<EMAIL>',
        orderId: 'gid://shopify/Order/1002',
        orderLineItemId: 'gid://shopify/LineItem/1002',
        notes: '配送希望'
      });
    } else if (title.includes('ウイングソファ')) {
      // 長期レンタル
      reservations.push({
        id: `reservation-${item.id}-003`,
        startDate: getDateAfterDays(10),
        endDate: getDateAfterDays(25),
        status: 'confirmed',
        customerName: '鈴木一郎',
        customerEmail: '<EMAIL>',
        orderId: 'gid://shopify/Order/1003',
        orderLineItemId: 'gid://shopify/LineItem/1003',
        notes: '長期レンタル'
      });
    } else if (title.includes('カリモクソファ') && i === 0) {
      // 短期レンタルと休日を挟んだレンタル
      reservations.push({
        id: `reservation-${item.id}-004`,
        startDate: getDateAfterDays(3),
        endDate: getDateAfterDays(4),
        status: 'confirmed',
        customerName: '田中次郎',
        customerEmail: '<EMAIL>',
        orderId: 'gid://shopify/Order/1004',
        orderLineItemId: 'gid://shopify/LineItem/1004',
        notes: '短期レンタル'
      });

      reservations.push({
        id: `reservation-${item.id}-005`,
        startDate: getDateAfterDays(7),
        endDate: getDateAfterDays(9),
        status: 'confirmed',
        customerName: '高橋三郎',
        customerEmail: '<EMAIL>',
        orderId: 'gid://shopify/Order/1005',
        orderLineItemId: 'gid://shopify/LineItem/1005',
        notes: '休日を挟んだレンタル'
      });
    } else if (title.includes('カリモクソファ') && i === 1) {
      // 破損状態の商品の仮予約
      reservations.push({
        id: `reservation-${item.id}-006`,
        startDate: getDateAfterDays(20),
        endDate: getDateAfterDays(30),
        status: 'pending',
        customerName: '伊藤四郎',
        customerEmail: '<EMAIL>',
        orderId: 'gid://shopify/Order/1006',
        orderLineItemId: 'gid://shopify/LineItem/1006',
        notes: '仮予約'
      });
    } else if (title.includes('プレーンソファ') && i === 0) {
      // 連続した予約
      reservations.push({
        id: `reservation-${item.id}-007`,
        startDate: getDateAfterDays(1),
        endDate: getDateAfterDays(3),
        status: 'confirmed',
        customerName: '中村五郎',
        customerEmail: '<EMAIL>',
        orderId: 'gid://shopify/Order/1007',
        orderLineItemId: 'gid://shopify/LineItem/1007',
        notes: '直近の予約'
      });

      reservations.push({
        id: `reservation-${item.id}-008`,
        startDate: getDateAfterDays(5),
        endDate: getDateAfterDays(10),
        status: 'confirmed',
        customerName: '小林六郎',
        customerEmail: '<EMAIL>',
        orderId: 'gid://shopify/Order/1008',
        orderLineItemId: 'gid://shopify/LineItem/1008',
        notes: '連続した予約'
      });
    } else if (title.includes('プレーンソファ') && i === 2) {
      // 仮予約と長期レンタル
      reservations.push({
        id: `reservation-${item.id}-009`,
        startDate: getDateAfterDays(15),
        endDate: getDateAfterDays(20),
        status: 'pending',
        customerName: '加藤七郎',
        customerEmail: '<EMAIL>',
        orderId: 'gid://shopify/Order/1009',
        orderLineItemId: 'gid://shopify/LineItem/1009',
        notes: '仮予約'
      });

      reservations.push({
        id: `reservation-${item.id}-010`,
        startDate: getDateAfterDays(25),
        endDate: getDateAfterDays(35),
        status: 'confirmed',
        customerName: '松本八郎',
        customerEmail: '<EMAIL>',
        orderId: 'gid://shopify/Order/1010',
        orderLineItemId: 'gid://shopify/LineItem/1010',
        notes: '長期レンタル'
      });
    }

    reservationInfo.push({
      itemId: item.id,
      reservations
    });
  }

  return reservationInfo;
}

// 在庫アイテムの状態を更新する関数
function updateInventoryItemStatus(product) {
  // 在庫アイテム情報を取得
  const inventoryItemsMetafield = product.metafields.edges.find(
    edge => edge.node.namespace === 'rental' && edge.node.key === 'inventory_items'
  );

  if (!inventoryItemsMetafield) return null;

  const inventoryItems = JSON.parse(inventoryItemsMetafield.node.value);

  // 商品タイトルから状況を判断
  const title = product.title.toLowerCase();

  // 在庫アイテムの状態を更新
  for (let i = 0; i < inventoryItems.length; i++) {
    if (title.includes('ベーシックソファ') && i === 1) {
      // 2台目はメンテナンス中
      inventoryItems[i].status = 'maintenance';
    } else if (title.includes('カリモクソファ') && i === 1) {
      // 2台目は破損状態
      inventoryItems[i].status = 'damaged';
    } else if (title.includes('プレーンソファ') && i === 2) {
      // 3台目は利用不可
      inventoryItems[i].status = 'unavailable';
    } else {
      // その他は利用可能
      inventoryItems[i].status = 'available';
    }
  }

  return inventoryItems;
}

// 商品のタグを予約状況に基づいて更新する関数
function generateReservationTags(product, reservationInfo) {
  const tags = product.tags.split(', ').filter(tag => !tag.startsWith('rental-'));

  // 予約状況に基づいてタグを生成
  for (const item of reservationInfo) {
    for (const reservation of item.reservations) {
      // 予約日のタグ
      tags.push(`rental-reserved-${reservation.startDate}`);
      tags.push(`rental-reserved-${reservation.endDate}`);

      // ステータスのタグ
      tags.push(`rental-status-${reservation.status}`);
    }
  }

  return tags.join(', ');
}

// テスト環境を準備する関数
async function prepareTestEnvironment(product) {
  console.log(`商品 ${product.title} のテスト環境を準備中...`);

  // 在庫アイテムの状態を更新
  const updatedInventoryItems = updateInventoryItemStatus(product);

  if (!updatedInventoryItems) {
    console.error(`商品 ${product.title} の在庫アイテム情報が見つかりません`);
    return false;
  }

  // 在庫アイテム情報を更新
  const inventoryItemsResult = await setJsonMetafield(
    product.id,
    'rental',
    'inventory_items',
    updatedInventoryItems
  );

  if (!inventoryItemsResult) {
    console.error(`商品 ${product.title} の在庫アイテム情報の更新に失敗しました`);
    return false;
  }

  // テスト用の予約情報を生成
  const reservationInfo = generateTestReservations(product);

  if (!reservationInfo) {
    console.error(`商品 ${product.title} の予約情報の生成に失敗しました`);
    return false;
  }

  // 予約情報を更新
  const reservationInfoResult = await setJsonMetafield(
    product.id,
    'rental',
    'reservation_info',
    reservationInfo
  );

  if (!reservationInfoResult) {
    console.error(`商品 ${product.title} の予約情報の更新に失敗しました`);
    return false;
  }

  // 予約状況に基づいてタグを更新
  const tags = generateReservationTags(product, reservationInfo);

  const tagsResult = await updateProductTags(product.id, tags);

  if (!tagsResult) {
    console.error(`商品 ${product.title} のタグの更新に失敗しました`);
    return false;
  }

  console.log(`商品 ${product.title} のテスト環境の準備が完了しました`);
  return true;
}

// メイン処理
async function main() {
  try {
    // テスト用商品を検索
    console.log('テスト用商品を検索中...');
    const products = await getProducts('title:【テスト】', 10);
    console.log(`${products.length}件のテスト用商品が見つかりました`);

    // 各商品のテスト環境を準備
    let successCount = 0;
    for (const product of products) {
      console.log(`\n商品 ${product.title} の処理を開始...`);

      // テスト環境を準備
      const success = await prepareTestEnvironment(product);

      if (success) {
        console.log(`商品 ${product.title} のテスト環境の準備が完了しました`);
        successCount++;
      } else {
        console.error(`商品 ${product.title} のテスト環境の準備に失敗しました`);
      }
    }

    console.log(`\n処理が完了しました。${successCount}/${products.length}件の商品のテスト環境を準備しました。`);
  } catch (error) {
    console.error('エラーが発生しました:', error);
  }
}

// 実行
main();
