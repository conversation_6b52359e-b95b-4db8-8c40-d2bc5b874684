/**
 * テストデータクリーンアップスクリプト
 *
 * 関連データの制約を考慮して、正しい順序でテストデータを削除します
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();
const SHOPIFY_SHOP = process.env.SHOPIFY_SHOP;

/**
 * テストデータを安全に削除
 */
async function cleanupTestData() {
  console.log('🧹 === テストデータクリーンアップ開始 ===');
  console.log(`対象ショップ: ${SHOPIFY_SHOP}`);
  console.log(`実行時刻: ${new Date().toLocaleString('ja-JP')}`);

  const startTime = Date.now();
  let totalDeleted = 0;

  try {
    await prisma.$connect();
    console.log('✅ データベース接続成功');

    // 1. TEST-で始まる予約データを取得
    console.log('\n=== 1. テスト予約データの確認 ===');
    const testBookings = await prisma.booking.findMany({
      where: {
        bookingId: {
          startsWith: 'TEST-'
        }
      },
      include: {
        deliverySchedules: true
      }
    });

    console.log(`📦 対象予約数: ${testBookings.length}件`);

    if (testBookings.length === 0) {
      console.log('✅ 削除対象のテストデータはありません');
      return;
    }

    // 詳細表示
    testBookings.forEach(booking => {
      console.log(`  - ${booking.bookingId}: 配送スケジュール ${booking.deliverySchedules.length}件`);
    });

    // 2. 関連する配送スケジュールを削除
    console.log('\n=== 2. 配送スケジュールの削除 ===');
    let totalSchedulesDeleted = 0;

    for (const booking of testBookings) {
      if (booking.deliverySchedules.length > 0) {
        const deletedSchedules = await prisma.deliverySchedule.deleteMany({
          where: {
            bookingId: booking.id
          }
        });
        console.log(`  🚚 ${booking.bookingId}: ${deletedSchedules.count}件削除`);
        totalSchedulesDeleted += deletedSchedules.count;
      }
    }

    console.log(`✅ 配送スケジュール削除完了: ${totalSchedulesDeleted}件`);

    // 3. 予約データを削除
    console.log('\n=== 3. 予約データの削除 ===');
    const deletedBookings = await prisma.booking.deleteMany({
      where: {
        bookingId: {
          startsWith: 'TEST-'
        }
      }
    });

    console.log(`✅ 予約データ削除完了: ${deletedBookings.count}件`);
    totalDeleted += deletedBookings.count;

    // 4. テスト商品の削除（オプション）
    console.log('\n=== 4. テスト商品の確認 ===');
    const testProducts = await prisma.product.findMany({
      where: {
        shop: SHOPIFY_SHOP,
        OR: [
          { sku: { startsWith: 'TEST-' } },
          { title: { contains: 'テスト' } }
        ]
      }
    });

    console.log(`📦 テスト商品数: ${testProducts.length}件`);

    if (testProducts.length > 0) {
      console.log('テスト商品一覧:');
      testProducts.forEach(product => {
        console.log(`  - ${product.sku}: ${product.title}`);
      });

      console.log('\n⚠️ テスト商品は手動で確認してから削除してください');
      console.log('削除する場合は以下のコマンドを実行:');
      console.log('node scripts/test-environment/04_cleanup_test_products.js');
    }

    // 5. 在庫カレンダーのテストデータ削除
    console.log('\n=== 5. 在庫カレンダーのテストデータ削除 ===');
    const deletedInventoryCalendar = await prisma.inventoryCalendar.deleteMany({
      where: {
        shop: SHOPIFY_SHOP,
        bookingId: {
          startsWith: 'TEST-'
        }
      }
    });

    console.log(`✅ 在庫カレンダー削除完了: ${deletedInventoryCalendar.count}件`);

    // 結果サマリー
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);

    console.log('\n🎯 === クリーンアップ結果サマリー ===');
    console.log(`🚚 配送スケジュール削除: ${totalSchedulesDeleted}件`);
    console.log(`📦 予約データ削除: ${deletedBookings.count}件`);
    console.log(`📅 在庫カレンダー削除: ${deletedInventoryCalendar.count}件`);
    console.log(`⏱️ 実行時間: ${duration}秒`);
    console.log(`📊 総削除件数: ${totalSchedulesDeleted + deletedBookings.count + deletedInventoryCalendar.count}件`);

    console.log('\n✅ テストデータクリーンアップ完了！');

  } catch (error) {
    console.error('❌ クリーンアップエラー:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
    console.log('📋 データベース接続終了');
  }
}

/**
 * 特定の予約IDのデータを削除
 */
async function cleanupSpecificBooking(bookingId) {
  console.log(`🎯 特定予約のクリーンアップ: ${bookingId}`);

  try {
    await prisma.$connect();

    // 予約データを取得
    const booking = await prisma.booking.findUnique({
      where: { bookingId },
      include: { deliverySchedules: true }
    });

    if (!booking) {
      console.log(`❌ 予約が見つかりません: ${bookingId}`);
      return false;
    }

    console.log(`📦 予約: ${booking.bookingId}`);
    console.log(`🚚 配送スケジュール: ${booking.deliverySchedules.length}件`);

    // 配送スケジュールを削除
    if (booking.deliverySchedules.length > 0) {
      const deletedSchedules = await prisma.deliverySchedule.deleteMany({
        where: { bookingId: booking.id }
      });
      console.log(`✅ 配送スケジュール削除: ${deletedSchedules.count}件`);
    }

    // 予約を削除
    await prisma.booking.delete({
      where: { id: booking.id }
    });
    console.log(`✅ 予約削除完了: ${bookingId}`);

    return true;
  } catch (error) {
    console.error(`❌ 削除エラー (${bookingId}):`, error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * データ整合性チェック
 */
async function checkDataIntegrity() {
  console.log('🔍 === データ整合性チェック ===');

  try {
    await prisma.$connect();

    // 孤立した配送スケジュールをチェック
    const allSchedules = await prisma.deliverySchedule.findMany({
      include: {
        booking: true
      }
    });

    const orphanedSchedules = allSchedules.filter(schedule => !schedule.booking);

    if (orphanedSchedules.length > 0) {
      console.log(`⚠️ 孤立した配送スケジュール: ${orphanedSchedules.length}件`);
      orphanedSchedules.forEach(schedule => {
        console.log(`  - ID: ${schedule.id}, BookingID: ${schedule.bookingId}`);
      });
    } else {
      console.log('✅ 孤立した配送スケジュールはありません');
    }

    // 関連データのない予約をチェック
    const allBookings = await prisma.booking.findMany({
      include: {
        product: true
      }
    });

    const bookingsWithoutProducts = allBookings.filter(booking => !booking.product);

    if (bookingsWithoutProducts.length > 0) {
      console.log(`⚠️ 商品が関連付けられていない予約: ${bookingsWithoutProducts.length}件`);
    } else {
      console.log('✅ すべての予約に商品が関連付けられています');
    }

    console.log('✅ データ整合性チェック完了');

  } catch (error) {
    console.error('❌ 整合性チェックエラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// コマンドライン引数の処理
const args = process.argv.slice(2);
const command = args[0];

async function main() {
  try {
    switch (command) {
      case 'all':
        await cleanupTestData();
        break;
      case 'booking':
        const bookingId = args[1];
        if (!bookingId) {
          console.error('使用方法: node cleanup_test_data.js booking [予約ID]');
          process.exit(1);
        }
        await cleanupSpecificBooking(bookingId);
        break;
      case 'check':
        await checkDataIntegrity();
        break;
      default:
        console.log('🧹 テストデータクリーンアップツール');
        console.log('');
        console.log('使用方法:');
        console.log('  node cleanup_test_data.js all          # 全テストデータを削除');
        console.log('  node cleanup_test_data.js booking [ID] # 特定の予約を削除');
        console.log('  node cleanup_test_data.js check        # データ整合性チェック');
        console.log('');
        await cleanupTestData();
        break;
    }
  } catch (error) {
    console.error('実行エラー:', error);
    process.exit(1);
  }
}

// スクリプト実行
main().catch(console.error);
