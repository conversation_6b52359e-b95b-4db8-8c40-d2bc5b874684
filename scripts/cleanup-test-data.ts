/**
 * テストデータのクリーンアップスクリプト
 * 
 * このスクリプトは、テスト中に作成されたデータをクリーンアップします。
 * - テスト予約データの削除
 * - テスト商品データの削除
 * - 在庫カレンダーデータの削除
 * 
 * 実行方法: npx tsx scripts/cleanup-test-data.ts
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * テストデータをクリーンアップする関数
 */
async function cleanupTestData() {
  try {
    console.log('\n===== テストデータのクリーンアップ =====');
    
    // テスト中に作成された予約を削除
    const bookings = await prisma.booking.findMany({
      where: {
        metadata: {
          path: ['testCreated'],
          equals: true
        }
      }
    });
    
    if (bookings.length > 0) {
      console.log(`${bookings.length}件のテスト予約を削除します...`);
      
      // 予約情報を表示
      for (const booking of bookings) {
        console.log(`- 予約ID: ${booking.id}, 期間: ${format(new Date(booking.startDate), 'yyyy/MM/dd', { locale: ja })} ～ ${format(new Date(booking.endDate), 'yyyy/MM/dd', { locale: ja })}`);
      }
      
      // 予約を削除
      await prisma.booking.deleteMany({
        where: {
          id: {
            in: bookings.map(b => b.id)
          }
        }
      });
      
      console.log('テスト予約が削除されました');
    } else {
      console.log('削除対象のテスト予約はありません');
    }
    
    // テスト中に作成された商品を取得
    const products = await prisma.product.findMany({
      where: {
        metadata: {
          path: ['testCreated'],
          equals: true
        }
      }
    });
    
    if (products.length > 0) {
      console.log(`${products.length}件のテスト商品を削除します...`);
      
      // 商品情報を表示
      for (const product of products) {
        console.log(`- 商品ID: ${product.id}, 商品名: ${product.title}, ShopifyID: ${product.shopifyId}`);
      }
      
      // 在庫カレンダーを削除
      for (const product of products) {
        const calendarCount = await prisma.inventoryCalendar.count({
          where: {
            productId: product.id
          }
        });
        
        if (calendarCount > 0) {
          console.log(`- 商品ID ${product.id} の在庫カレンダー ${calendarCount}件を削除します`);
          
          await prisma.inventoryCalendar.deleteMany({
            where: {
              productId: product.id
            }
          });
        }
      }
      
      // 商品を削除
      await prisma.product.deleteMany({
        where: {
          id: {
            in: products.map(p => p.id)
          }
        }
      });
      
      console.log('テスト商品が削除されました');
    } else {
      console.log('削除対象のテスト商品はありません');
    }
    
    console.log('\nクリーンアップが完了しました');
    return true;
  } catch (error) {
    console.error('テストデータクリーンアップエラー:', error);
    return false;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('テストデータのクリーンアップを開始します...');
    
    // テストデータをクリーンアップ
    const result = await cleanupTestData();
    
    if (result) {
      console.log('クリーンアップが成功しました');
    } else {
      console.error('クリーンアップに失敗しました');
      process.exit(1);
    }
  } catch (error) {
    console.error('クリーンアップ中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main().catch(e => {
  console.error('クリーンアップ中に予期せぬエラーが発生しました:', e);
  process.exit(1);
});
