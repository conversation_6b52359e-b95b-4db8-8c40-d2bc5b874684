# メタフィールド管理ツール

## 概要

メタフィールド管理ツール（`scripts/metafields-manager.js`）は、Shopifyストアのメタフィールドを効率的に管理するための統合スクリプトです。このツールを使用することで、メタフィールド定義の作成、確認、値の設定・取得、バックアップ・復元などの操作を簡単に行うことができます。

## 前提条件

- Node.js (v18以上)
- npm (v9以上)
- Shopify Admin API アクセストークン
- 以下の環境変数が設定されていること:
  - `SHOPIFY_SHOP`: Shopifyストアのドメイン（例: `your-store.myshopify.com`）
  - `SHOPIFY_ADMIN_API_ACCESS_TOKEN`: Shopify Admin APIのアクセストークン

## インストール

必要なパッケージをインストールします。

```bash
npm install graphql-request dotenv chalk
```

## 使用方法

```bash
node scripts/metafields-manager.js <コマンド> [オプション]
```

### コマンド

- `setup`: メタフィールド定義を作成します
- `check`: メタフィールド定義を確認します
- `set`: メタフィールド値を設定します
- `get`: メタフィールド値を取得します
- `backup`: メタフィールドをバックアップします
- `restore`: メタフィールドを復元します
- `help`: ヘルプを表示します

### オプション

- `--id=<ID>`: 商品ID（`set`, `get`コマンドで必須）
- `--namespace=<名前空間>`: メタフィールドの名前空間（`set`, `get`コマンドで必須）
- `--key=<キー>`: メタフィールドのキー（`set`, `get`コマンドで必須）
- `--value=<値>`: メタフィールドの値（`set`コマンドで必須、`json`と排他）
- `--json=<JSON>`: JSONメタフィールドの値（`set`コマンドでjson型の場合に必須）
- `--type=<タイプ>`: メタフィールドのタイプ（`set`コマンドでオプション、デフォルトは`single_line_text_field`）
- `--file=<ファイルパス>`: バックアップファイルのパス（`restore`コマンドで必須）

## 使用例

### メタフィールド定義の作成

レンタル商品管理に必要なメタフィールド定義を作成します。

```bash
node scripts/metafields-manager.js setup
```

このコマンドは以下のメタフィールド定義を作成します:

- `rental.basic_info` (JSON): レンタル商品の基本情報（商品コード、状態など）
- `rental.pricing` (JSON): レンタル商品の料金設定（基本料金、割引率など）
- `rental.status` (single_line_text_field): レンタル商品の現在の状態
- `rental.location` (single_line_text_field): レンタル商品の保管場所

### メタフィールド定義の確認

現在のメタフィールド定義を確認します。

```bash
node scripts/metafields-manager.js check
```

### メタフィールド値の設定

#### テキストメタフィールドの設定

```bash
node scripts/metafields-manager.js set --id=gid://shopify/Product/1234567890 --namespace=rental --key=status --value=available
```

#### JSONメタフィールドの設定

```bash
node scripts/metafields-manager.js set --id=gid://shopify/Product/1234567890 --namespace=rental --key=basic_info --json='{"productCode":"ABC123","status":"available"}'
```

### メタフィールド値の取得

```bash
node scripts/metafields-manager.js get --id=gid://shopify/Product/1234567890 --namespace=rental --key=status
```

### メタフィールドのバックアップ

#### 全てのメタフィールド定義のバックアップ

```bash
node scripts/metafields-manager.js backup
```

#### 特定の名前空間のメタフィールド定義のバックアップ

```bash
node scripts/metafields-manager.js backup --namespace=rental
```

バックアップファイルは `backups` ディレクトリに保存されます。

### メタフィールドの復元

```bash
node scripts/metafields-manager.js restore --file=backups/metafield-definitions-rental-2025-04-21.json
```

## 一括処理の例

### 全ての商品のステータスを「利用可能」に設定

```bash
# 商品IDのリストを取得するスクリプトを実行
node scripts/get-product-ids.js > product-ids.txt

# 各商品に対してメタフィールドを設定
cat product-ids.txt | while read id; do
  node scripts/metafields-manager.js set --id=$id --namespace=rental --key=status --value=available
done
```

### 全ての商品の基本情報を設定

```bash
# 商品IDと商品コードのリストを取得するスクリプトを実行
node scripts/get-product-info.js > product-info.csv

# 各商品に対してJSONメタフィールドを設定
cat product-info.csv | while IFS=, read id code; do
  node scripts/metafields-manager.js set --id=$id --namespace=rental --key=basic_info --json="{\"productCode\":\"$code\",\"status\":\"available\"}"
done
```

## トラブルシューティング

### エラー: 認証に失敗しました

環境変数 `SHOPIFY_ADMIN_API_ACCESS_TOKEN` が正しく設定されているか確認してください。

### エラー: メタフィールド定義の作成に失敗しました

- Shopify Admin APIのスコープに `write_products` が含まれているか確認してください。
- 同じ名前空間とキーを持つメタフィールド定義が既に存在する場合は、既存の定義を削除するか、別の名前空間またはキーを使用してください。

### エラー: メタフィールドの設定に失敗しました

- 商品IDが正しいか確認してください。
- JSONメタフィールドの場合、JSON形式が正しいか確認してください。

## 注意事項

- 本番環境で使用する前に、必ずテスト環境でスクリプトの動作を確認してください。
- メタフィールド定義の変更は、既存のメタフィールド値に影響を与える可能性があります。
- バックアップを定期的に取得することをお勧めします。
