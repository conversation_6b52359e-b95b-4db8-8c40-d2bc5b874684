#!/usr/bin/env node

/**
 * カテゴリシステムのデバッグスクリプト
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function debugCategorySystem() {
  console.log('=== カテゴリシステムデバッグ ===');
  
  const shop = 'ease-next-temp.myshopify.com';
  
  try {
    // 1. データベース接続確認
    await prisma.$connect();
    console.log('✅ データベース接続成功');
    
    // 2. カテゴリマスタ確認
    const categoryCount = await prisma.categoryMaster.count({
      where: { shop }
    });
    console.log(`📊 カテゴリマスタ件数: ${categoryCount}件`);
    
    if (categoryCount === 0) {
      console.log('❌ カテゴリマスタが存在しません');
      
      // 全ショップのカテゴリマスタを確認
      const allCategories = await prisma.categoryMaster.findMany({
        select: { shop: true, code: true, name: true }
      });
      console.log(`全ショップのカテゴリマスタ: ${allCategories.length}件`);
      
      if (allCategories.length > 0) {
        console.log('登録されているショップ:');
        const shops = [...new Set(allCategories.map(c => c.shop))];
        shops.forEach(s => console.log(`  - ${s}`));
      }
    } else {
      // カテゴリマスタの詳細確認
      const categories = await prisma.categoryMaster.findMany({
        where: { shop },
        take: 5,
        orderBy: { code: 'asc' },
        include: {
          subCategories: {
            take: 3,
            orderBy: { code: 'asc' }
          }
        }
      });
      
      console.log('\n📋 カテゴリマスタサンプル:');
      categories.forEach(cat => {
        console.log(`  ${cat.code}: ${cat.name} (アクティブ: ${cat.isActive})`);
        cat.subCategories.forEach(sub => {
          console.log(`    └ ${sub.code}: ${sub.name}`);
        });
      });
    }
    
    // 3. サブカテゴリマスタ確認
    const subCategoryCount = await prisma.subCategoryMaster.count({
      where: { shop }
    });
    console.log(`📊 サブカテゴリマスタ件数: ${subCategoryCount}件`);
    
    // 4. 商品データ確認
    const productCount = await prisma.product.count({
      where: { shop }
    });
    console.log(`📊 商品データ件数: ${productCount}件`);
    
    if (productCount > 0) {
      const sampleProducts = await prisma.product.findMany({
        where: { shop },
        take: 5,
        select: { sku: true, title: true, basicInfo: true }
      });
      
      console.log('\n📦 商品データサンプル:');
      sampleProducts.forEach(product => {
        console.log(`  SKU: ${product.sku}, タイトル: ${product.title}`);
        if (product.basicInfo) {
          console.log(`    basicInfo: ${JSON.stringify(product.basicInfo)}`);
        }
      });
    }
    
    // 5. 予約データ確認
    const bookingCount = await prisma.booking.count({
      where: { shop }
    });
    console.log(`📊 予約データ件数: ${bookingCount}件`);
    
    if (bookingCount > 0) {
      const sampleBookings = await prisma.booking.findMany({
        where: { shop },
        take: 5,
        select: { 
          id: true, 
          productId: true, 
          startDate: true, 
          endDate: true, 
          status: true,
          product: {
            select: { sku: true, title: true }
          }
        }
      });
      
      console.log('\n📅 予約データサンプル:');
      sampleBookings.forEach(booking => {
        console.log(`  ID: ${booking.id}, 商品: ${booking.product?.sku}, ステータス: ${booking.status}`);
        console.log(`    期間: ${booking.startDate.toISOString().split('T')[0]} 〜 ${booking.endDate.toISOString().split('T')[0]}`);
      });
    }
    
    console.log('\n=== デバッグ完了 ===');
    
  } catch (error) {
    console.error('❌ エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugCategorySystem();
