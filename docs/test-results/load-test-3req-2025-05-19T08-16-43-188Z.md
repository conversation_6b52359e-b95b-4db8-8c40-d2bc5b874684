# 負荷テストと同時実行テストレポート

## テスト概要

このレポートは、多数の同時予約リクエストの処理、同一商品への複数同時アクセス時の競合解決、高負荷時のパフォーマンスのテスト結果をまとめたものです。

テスト実行日時: 2025/5/19 17:16:43
実行時間: 7.05秒

## テスト環境

- 同時リクエスト数: 3
- Node.js バージョン: v20.18.1
- OS: darwin arm64
- CPUコア数: 10

## テスト結果

- 合計テスト数: 3
- 成功: 3
- 失敗: 0
- 成功率: 100%

## パフォーマンス分析

- スループット: 0.43 リクエスト/秒
- 平均テスト実行時間: 5931.33ミリ秒
- 最も遅いテスト: リクエスト #2 (6299.00ミリ秒)
- 最も速いテスト: リクエスト #1 (5585.00ミリ秒)

## 詳細結果

### リクエスト1

- 結果: ✅ 成功
- メッセージ: 予約リクエストが成功しました

- 実行時間: 5910.00ミリ秒
- 詳細:
```json
{
  "requestId": 0,
  "bookingId": "d8d6d187-8694-4286-94ca-6cd7a53f72de"
}
```

### リクエスト1

- 結果: ✅ 成功
- メッセージ: 予約リクエストが成功しました

- 実行時間: 5585.00ミリ秒
- 詳細:
```json
{
  "requestId": 1,
  "bookingId": "2729107b-ab35-4f95-9042-343b5b751faa"
}
```

### リクエスト2

- 結果: ✅ 成功
- メッセージ: 予約リクエストが成功しました

- 実行時間: 6299.00ミリ秒
- 詳細:
```json
{
  "requestId": 2,
  "bookingId": "1fe0fff3-2dd5-4324-9563-3769e7920a6a"
}
```

