import { json } from "@remix-run/node";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { Page, Layout, Card, Text, BlockStack, Button, Banner, Box, Link } from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { useEffect } from "react";

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  return json({
    message: "DatePickerコンポーネントの修正"
  });
}

export default function FixDatePicker() {
  const { message } = useLoaderData<typeof loader>();

  useEffect(() => {
    // DatePickerコンポーネントの修正手順を表示
    console.log('DatePickerコンポーネントの修正手順を表示します');
  }, []);

  return (
    <Page title="DatePicker修正" backAction={{ content: "戻る", url: "/app" }}>
      <TitleBar title="DatePicker修正" />
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="p">{message}</Text>
              <Text as="p">このページでは、DatePickerコンポーネントの修正手順を説明します。</Text>
              
              <Banner tone="warning">
                <Text as="p">現在、4月の予約データが6月のカレンダーにも表示されてしまう問題が発生しています。</Text>
                <Text as="p">この問題は、日付要素を特定する際に月を考慮していないことが原因です。</Text>
              </Banner>
              
              <Box paddingBlockStart="300">
                <BlockStack gap="300">
                  <Text variant="headingMd" as="h3">修正手順</Text>
                  
                  <Text as="p">1. app.booking-test.tsxファイルを開きます。</Text>
                  <Text as="p">2. DatePickerコンポーネントからrenderDayLabel属性を削除します。</Text>
                  <Text as="p">3. 以下のコードに置き換えます：</Text>
                  
                  <Box background="bg-surface-secondary" padding="400" borderRadius="100">
                    <pre style={{ whiteSpace: 'pre-wrap', overflowWrap: 'break-word' }}>
{`<DatePicker
  month={month}
  year={year}
  onChange={setDateRange}
  onMonthChange={handleMonthChange}
  selected={dateRange}
  allowRange
  disableDatesBefore={new Date()}
  disableSpecificDates={disabledDates}
  weekStartsOn={0} /* 日曜始まりに設定 */
/>`}
                    </pre>
                  </Box>
                  
                  <Text as="p">4. 修正後、カレンダーの月表示問題修正ページにアクセスして、カレンダーの月表示問題を修正します。</Text>
                  
                  <Box paddingBlockStart="300">
                    <BlockStack gap="200">
                      <Button url="/app/fix-calendar-month" primary>
                        カレンダーの月表示問題修正ページへ
                      </Button>
                      <Button url="/app/booking-test">
                        予約テストページに戻る
                      </Button>
                    </BlockStack>
                  </Box>
                </BlockStack>
              </Box>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
