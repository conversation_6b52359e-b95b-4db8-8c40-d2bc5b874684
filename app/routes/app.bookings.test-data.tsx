import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useActionData, useLoaderData, useSubmit, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Button,
  Text,
  BlockStack,
  Box,
  InlineStack,
  Banner,
  List,
} from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { v4 as uuidv4 } from "uuid";

export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  // 商品データを取得
  const products = await prisma.product.findMany({
    where: { shop },
    select: {
      id: true,
      title: true,
      sku: true,
    },
    take: 10,
  });

  return json({
    products,
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  const formData = await request.formData();
  const action = formData.get("action") as string || "create_bookings";

  try {
    // 商品データを取得
    let products = await prisma.product.findMany({
      where: { shop },
      select: {
        id: true,
        title: true,
        sku: true,
      },
      take: 10,
    });

    // 商品データがない場合は、テスト用の商品データを作成
    if (products.length === 0 && action === "create_products") {
      const testProducts = [];
      const productNames = ["ソファ", "テーブル", "椅子", "ベッド", "デスク", "本棚", "テレビ台", "ダイニングセット", "照明", "カーテン"];

      for (let i = 0; i < 10; i++) {
        const sku = `TEST-${100 + i}`;
        const product = await prisma.product.create({
          data: {
            shop,
            title: `テスト商品: ${productNames[i]}`,
            sku,
            shopifyId: `test-${i}`,
            price: 5000 + (i * 1000),
            status: "AVAILABLE",
            defaultRentalDuration: 3,
          },
        });

        testProducts.push(product);
      }

      return json({
        success: true,
        message: `${testProducts.length}件のテスト商品データを作成しました。`,
        products: testProducts,
      });
    }

    // 商品データがない場合は、予約データを作成できない
    if (products.length === 0 && action === "create_bookings") {
      return json({
        success: false,
        message: "テスト予約データを作成するには、まず商品データが必要です。「商品データを作成」ボタンをクリックしてください。",
      });
    }

    // テスト用の予約データを作成
    const testBookings = [];
    const statuses = ["DRAFT", "PROVISIONAL", "CONFIRMED", "CANCELLED", "COMPLETED"];
    const customerNames = ["山田太郎", "佐藤花子", "鈴木一郎", "田中美咲", "高橋健太"];
    const customerEmails = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"];

    for (let i = 0; i < 10; i++) {
      const product = products[i % products.length];
      const startDate = new Date();
      startDate.setDate(startDate.getDate() + i * 3);
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 3);

      const booking = await prisma.booking.create({
        data: {
          shop,
          productId: product.id,
          bookingId: `TEST-${uuidv4().substring(0, 8)}`,
          startDate,
          endDate,
          status: statuses[i % statuses.length],
          customerName: customerNames[i % customerNames.length],
          customerEmail: customerEmails[i % customerEmails.length],
          totalAmount: 10000 + (i * 1000),
          priority: 1,
          bookingType: "PROVISIONAL",
          paymentStatus: "PENDING",
          depositPaid: false,
          notifyOnStatusChange: false,
        },
      });

      testBookings.push(booking);
    }

    return json({
      success: true,
      message: `${testBookings.length}件のテスト予約データを作成しました。`,
      bookings: testBookings,
    });
  } catch (error) {
    console.error("テストデータ作成エラー:", error);
    return json({
      success: false,
      message: "テストデータの作成中にエラーが発生しました。",
      error: error.message,
    });
  }
}

export default function TestDataPage() {
  const { products } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();

  const handleCreateProducts = () => {
    const formData = new FormData();
    formData.append("action", "create_products");
    submit(formData, { method: "post" });
  };

  const handleCreateBookings = () => {
    const formData = new FormData();
    formData.append("action", "create_bookings");
    submit(formData, { method: "post" });
  };

  return (
    <Page
      title="テストデータ作成"
      subtitle="テスト用のデータを作成します"
      backAction={{ content: "予約一覧に戻る", url: "/app/bookings" }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                <Text as="p">
                  このページでは、テスト用のデータを作成できます。まず商品データを作成し、その後予約データを作成してください。
                </Text>

                {products.length === 0 ? (
                  <Banner title="商品データがありません" tone="warning">
                    <p>テストデータを作成するには、まず「商品データを作成」ボタンをクリックして商品データを作成してください。</p>
                  </Banner>
                ) : (
                  <Box paddingBlockStart="400">
                    <Text as="h3" variant="headingMd">利用可能な商品（最大10件）</Text>
                    <List type="bullet">
                      {products.map((product) => (
                        <List.Item key={product.id}>
                          {product.title} ({product.sku})
                        </List.Item>
                      ))}
                    </List>
                  </Box>
                )}

                {actionData && (
                  <Banner
                    title={actionData.success ? "成功" : "エラー"}
                    tone={actionData.success ? "success" : "critical"}
                  >
                    <p>{actionData.message}</p>
                    {actionData.error && <p>エラー詳細: {actionData.error}</p>}
                  </Banner>
                )}

                <Box paddingBlockStart="400">
                  <InlineStack gap="400" align="start">
                    <Form method="post">
                      <Button
                        primary={products.length === 0}
                        onClick={handleCreateProducts}
                      >
                        商品データを作成
                      </Button>
                    </Form>

                    <Form method="post">
                      <Button
                        primary={products.length > 0}
                        disabled={products.length === 0}
                        onClick={handleCreateBookings}
                      >
                        予約データを作成
                      </Button>
                    </Form>
                  </InlineStack>
                </Box>
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
