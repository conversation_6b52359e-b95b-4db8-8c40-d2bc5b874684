import { prisma } from "~/db.server";
import { logger } from "~/utils/logger";
import { BookingService } from "./booking.service";

/**
 * 注文ステータス同期サービス
 * 
 * Shopify注文と予約システム間の双方向同期を管理
 */
export class OrderStatusSyncService {
  private bookingService: BookingService;

  constructor() {
    this.bookingService = new BookingService();
  }

  /**
   * 予約ステータス変更時にShopify注文を更新
   */
  async syncBookingStatusToShopify(
    bookingId: string, 
    newStatus: string, 
    shopifyAdmin: any,
    options: {
      notes?: string;
      reason?: string;
    } = {}
  ) {
    try {
      logger.info("予約ステータスのShopify同期開始", { 
        bookingId, 
        newStatus 
      });

      // 予約情報を取得
      const booking = await prisma.booking.findUnique({
        where: { id: bookingId },
        include: {
          product: {
            select: {
              id: true,
              title: true,
              sku: true
            }
          }
        }
      });

      if (!booking) {
        throw new Error(`予約が見つかりません: ${bookingId}`);
      }

      if (!booking.shopifyOrderId) {
        logger.warn("Shopify注文IDが設定されていません", { 
          bookingId: booking.bookingId 
        });
        return { success: false, message: "Shopify注文IDなし" };
      }

      // Shopify注文の更新処理
      const updateResult = await this.updateShopifyOrderStatus(
        booking.shopifyOrderId,
        newStatus,
        shopifyAdmin,
        {
          bookingId: booking.bookingId,
          productTitle: booking.product?.title,
          notes: options.notes,
          reason: options.reason
        }
      );

      logger.info("予約ステータスのShopify同期完了", {
        bookingId: booking.bookingId,
        shopifyOrderId: booking.shopifyOrderId,
        newStatus,
        success: updateResult.success
      });

      return updateResult;
    } catch (error) {
      logger.error("予約ステータスのShopify同期エラー", {
        bookingId,
        newStatus,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Shopify注文ステータスを更新
   */
  private async updateShopifyOrderStatus(
    shopifyOrderId: string,
    bookingStatus: string,
    shopifyAdmin: any,
    options: {
      bookingId?: string;
      productTitle?: string;
      notes?: string;
      reason?: string;
    } = {}
  ) {
    try {
      // 予約ステータスに応じたShopify注文の処理を決定
      let shopifyAction = null;
      let noteText = "";

      switch (bookingStatus) {
        case "CONFIRMED":
          // 予約確定時は注文にノートを追加
          noteText = `予約確定: ${options.bookingId} - ${options.productTitle}`;
          if (options.notes) {
            noteText += ` (${options.notes})`;
          }
          break;

        case "CANCELLED":
          // 予約キャンセル時は注文をキャンセル
          shopifyAction = "cancel";
          noteText = `予約キャンセル: ${options.bookingId} - ${options.productTitle}`;
          if (options.reason) {
            noteText += ` (理由: ${options.reason})`;
          }
          break;

        case "COMPLETED":
          // 予約完了時は注文を完了
          shopifyAction = "fulfill";
          noteText = `予約完了: ${options.bookingId} - ${options.productTitle}`;
          break;

        default:
          // その他のステータス変更はノートのみ追加
          noteText = `予約ステータス変更: ${bookingStatus} - ${options.bookingId}`;
          break;
      }

      // Shopify注文にノートを追加
      if (noteText) {
        await this.addNoteToShopifyOrder(shopifyOrderId, noteText, shopifyAdmin);
      }

      // 必要に応じて注文の状態を変更
      if (shopifyAction === "cancel") {
        await this.cancelShopifyOrder(shopifyOrderId, shopifyAdmin, options.reason);
      } else if (shopifyAction === "fulfill") {
        await this.fulfillShopifyOrder(shopifyOrderId, shopifyAdmin);
      }

      return { success: true, message: "Shopify注文更新完了" };
    } catch (error) {
      logger.error("Shopify注文ステータス更新エラー", {
        shopifyOrderId,
        bookingStatus,
        error: error instanceof Error ? error.message : String(error)
      });
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  }

  /**
   * Shopify注文にノートを追加
   */
  private async addNoteToShopifyOrder(
    shopifyOrderId: string, 
    note: string, 
    shopifyAdmin: any
  ) {
    try {
      const mutation = `
        mutation orderUpdate($input: OrderInput!) {
          orderUpdate(input: $input) {
            order {
              id
              note
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        input: {
          id: `gid://shopify/Order/${shopifyOrderId}`,
          note: note
        }
      };

      const response = await shopifyAdmin.graphql(mutation, { variables });
      const result = await response.json();

      if (result.data?.orderUpdate?.userErrors?.length > 0) {
        throw new Error(`Shopify注文ノート追加エラー: ${result.data.orderUpdate.userErrors[0].message}`);
      }

      logger.info("Shopify注文ノート追加完了", {
        shopifyOrderId,
        note
      });
    } catch (error) {
      logger.error("Shopify注文ノート追加エラー", {
        shopifyOrderId,
        note,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Shopify注文をキャンセル
   */
  private async cancelShopifyOrder(
    shopifyOrderId: string, 
    shopifyAdmin: any, 
    reason?: string
  ) {
    try {
      const mutation = `
        mutation orderCancel($orderId: ID!, $reason: OrderCancelReason) {
          orderCancel(orderId: $orderId, reason: $reason) {
            order {
              id
              cancelledAt
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        orderId: `gid://shopify/Order/${shopifyOrderId}`,
        reason: reason || "OTHER"
      };

      const response = await shopifyAdmin.graphql(mutation, { variables });
      const result = await response.json();

      if (result.data?.orderCancel?.userErrors?.length > 0) {
        throw new Error(`Shopify注文キャンセルエラー: ${result.data.orderCancel.userErrors[0].message}`);
      }

      logger.info("Shopify注文キャンセル完了", {
        shopifyOrderId,
        reason
      });
    } catch (error) {
      logger.error("Shopify注文キャンセルエラー", {
        shopifyOrderId,
        reason,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Shopify注文を完了
   */
  private async fulfillShopifyOrder(shopifyOrderId: string, shopifyAdmin: any) {
    try {
      // まず注文の詳細を取得
      const orderQuery = `
        query getOrder($id: ID!) {
          order(id: $id) {
            id
            fulfillmentOrders(first: 10) {
              edges {
                node {
                  id
                  status
                }
              }
            }
          }
        }
      `;

      const orderResponse = await shopifyAdmin.graphql(orderQuery, {
        variables: { id: `gid://shopify/Order/${shopifyOrderId}` }
      });
      const orderResult = await orderResponse.json();

      const fulfillmentOrders = orderResult.data?.order?.fulfillmentOrders?.edges || [];
      
      // 未完了のフルフィルメント注文を完了
      for (const edge of fulfillmentOrders) {
        const fulfillmentOrder = edge.node;
        
        if (fulfillmentOrder.status === "OPEN") {
          const fulfillMutation = `
            mutation fulfillmentCreateV2($fulfillment: FulfillmentV2Input!) {
              fulfillmentCreateV2(fulfillment: $fulfillment) {
                fulfillment {
                  id
                  status
                }
                userErrors {
                  field
                  message
                }
              }
            }
          `;

          const fulfillVariables = {
            fulfillment: {
              fulfillmentOrderId: fulfillmentOrder.id,
              trackingInfo: {
                company: "レンタル完了"
              }
            }
          };

          const fulfillResponse = await shopifyAdmin.graphql(fulfillMutation, { 
            variables: fulfillVariables 
          });
          const fulfillResult = await fulfillResponse.json();

          if (fulfillResult.data?.fulfillmentCreateV2?.userErrors?.length > 0) {
            throw new Error(`フルフィルメント作成エラー: ${fulfillResult.data.fulfillmentCreateV2.userErrors[0].message}`);
          }
        }
      }

      logger.info("Shopify注文完了処理完了", {
        shopifyOrderId
      });
    } catch (error) {
      logger.error("Shopify注文完了処理エラー", {
        shopifyOrderId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 注文と予約の同期状況を確認
   */
  async checkSyncStatus(shop: string) {
    try {
      // 同期が必要な予約を検索
      const unsyncedBookings = await prisma.booking.findMany({
        where: {
          shop,
          shopifyOrderId: {
            not: null
          },
          // 最近更新されたが同期されていない予約
          updatedAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24時間以内
          }
        },
        include: {
          product: {
            select: {
              id: true,
              title: true,
              sku: true
            }
          }
        },
        take: 100
      });

      logger.info("同期状況確認", {
        shop,
        unsyncedBookingsCount: unsyncedBookings.length
      });

      return {
        unsyncedBookings,
        totalCount: unsyncedBookings.length
      };
    } catch (error) {
      logger.error("同期状況確認エラー", {
        shop,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }
}
