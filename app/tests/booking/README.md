# 予約システムテスト

このディレクトリには、予約システムのテストが含まれています。これらのテストは、予約システムの機能が正しく動作することを確認するために使用されます。

## テストの種類

1. **予約の重複チェックテスト** (`booking-availability.test.ts`)
   - 同じ商品の同じ期間に複数の予約ができないこと
   - 予約期間が重複する場合に適切なエラーが返されること
   - 「貸出不可能な商品も表示する」チェックボックスが正しく機能すること

2. **日本時間処理テスト** (`japan-time.test.ts`)
   - 日本時間でのフォーマット
   - 日本時間での日付操作
   - 日本時間での休業日判定

3. **Prismaスキーマ整合性テスト** (`prisma-schema.test.ts`)
   - Prismaスキーマとデータベーススキーマの整合性
   - Prismaクエリの正確性
   - メタデータフィールドの使用

## テストの実行方法

### 個別のテストを実行する

```bash
# 予約の重複チェックテストを実行
npx vitest run booking-availability.test.ts

# 日本時間処理テストを実行
npx vitest run japan-time.test.ts

# Prismaスキーマ整合性テストを実行
npx vitest run prisma-schema.test.ts
```

### すべてのテストを実行する

```bash
# すべてのテストを実行
./run-tests.sh
```

## テストの追加方法

新しいテストを追加する場合は、以下の手順に従ってください：

1. 新しいテストファイルを作成する（例：`new-feature.test.ts`）
2. テストファイルに適切なテストケースを追加する
3. `run-tests.sh`スクリプトの`TEST_FILES`配列に新しいテストファイルを追加する

## テストの注意点

- テストは実際のデータベースに接続せず、モックを使用して行われます
- テストは独立しており、他のテストの結果に影響されません
- テストは自動化されており、CI/CDパイプラインで実行できます

## 問題が発生した場合

テストが失敗した場合は、以下の点を確認してください：

1. テストが期待する動作と実際の動作が一致しているか
2. テストのモックが正しく設定されているか
3. テストが依存するコードが変更されていないか

問題が解決しない場合は、テストのエラーメッセージを確認し、必要に応じてテストコードを修正してください。