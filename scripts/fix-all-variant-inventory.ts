import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();

/**
 * 全商品の全バリアントの在庫を1に設定するスクリプト
 * レンタル商品は各バリアントごとに在庫を1に設定する必要がある
 */

interface InventoryLevel {
  id: string;
  location: {
    id: string;
    name: string;
  };
  quantities: Array<{
    name: string;
    quantity: number;
  }>;
}

interface ProductVariant {
  id: string;
  sku: string;
  displayName: string;
  inventoryItem: {
    id: string;
    inventoryLevels: {
      edges: Array<{
        node: InventoryLevel;
      }>;
    };
  };
}

async function fixAllVariantInventory() {
  console.log("=== 全バリアント在庫修正スクリプト開始 ===\n");

  // Shopify API設定
  const config = {
    shop: process.env.SHOPIFY_SHOP || 'peaces-test-block',
    apiSecretKey: process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
  };

  // 設定の検証
  if (!config.apiSecretKey) {
    console.error('エラー: SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません。');
    process.exit(1);
  }

  // ショップ名を正規化
  const shopName = config.shop.replace('.myshopify.com', '');

  // Shopify GraphQL APIクライアントの設定
  const client = new GraphQLClient(
    `https://${shopName}.myshopify.com/admin/api/2025-01/graphql.json`,
    {
      headers: {
        'X-Shopify-Access-Token': config.apiSecretKey,
        'Content-Type': 'application/json',
      },
    }
  );

  try {
    // 1. 全商品とバリアントを取得
    console.log("1. 商品情報を取得中...");
    const productsQuery = `
      query GetAllProductsWithInventory {
        products(first: 250) {
          edges {
            node {
              id
              title
              handle
              variants(first: 20) {
                edges {
                  node {
                    id
                    sku
                    displayName
                    inventoryItem {
                      id
                      inventoryLevels(first: 10) {
                        edges {
                          node {
                            id
                            location {
                              id
                              name
                            }
                            quantities(names: ["available"]) {
                              name
                              quantity
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;

    const productsData = await client.request(productsQuery);
    
    if (!productsData || !productsData.products) {
      console.error("商品データが取得できませんでした");
      return;
    }

    const products = productsData.products.edges;
    console.log(`✓ ${products.length}個の商品を取得しました\n`);

    // 2. 各バリアントの在庫を修正
    console.log("2. バリアントの在庫を修正中...");
    let totalVariants = 0;
    let fixedVariants = 0;
    let errors = 0;

    for (const { node: product } of products) {
      console.log(`\n商品: ${product.title}`);
      
      for (const { node: variant } of product.variants.edges) {
        totalVariants++;
        console.log(`  バリアント: ${variant.displayName} (SKU: ${variant.sku})`);
        
        if (!variant.inventoryItem?.inventoryLevels?.edges?.length) {
          console.log(`    ⚠️  在庫レベル情報がありません`);
          continue;
        }

        for (const { node: level } of variant.inventoryItem.inventoryLevels.edges) {
          const currentQuantity = level.quantities.find(q => q.name === "available")?.quantity || 0;
          
          if (currentQuantity === 0) {
            // 在庫を1に設定
            const mutation = `
              mutation inventorySetQuantities($input: InventorySetQuantitiesInput!) {
                inventorySetQuantities(input: $input) {
                  inventoryAdjustmentGroup {
                    reason
                    changes {
                      name
                      delta
                    }
                  }
                  userErrors {
                    field
                    message
                  }
                }
              }
            `;

            const variables = {
              input: {
                reason: "correction",
                name: "available",
                ignoreCompareQuantity: true,
                quantities: [
                  {
                    inventoryItemId: variant.inventoryItem.id,
                    locationId: level.location.id,
                    quantity: 1
                  }
                ]
              }
            };

            try {
              const data = await client.request(mutation, variables);
              
              if (data.inventorySetQuantities?.userErrors?.length > 0) {
                console.log(`    ❌ エラー:`, data.inventorySetQuantities.userErrors);
                errors++;
              } else {
                console.log(`    ✓ 在庫を0→1に修正しました (${level.location.name})`);
                fixedVariants++;
              }
            } catch (error) {
              console.log(`    ❌ 修正エラー:`, error.message);
              errors++;
            }
          } else {
            console.log(`    ✓ 既に在庫あり: ${currentQuantity} (${level.location.name})`);
          }
        }
      }
    }

    // 3. 結果サマリー
    console.log("\n=== 修正結果サマリー ===");
    console.log(`総バリアント数: ${totalVariants}`);
    console.log(`修正成功: ${fixedVariants}`);
    console.log(`エラー: ${errors}`);
    console.log(`修正不要: ${totalVariants - fixedVariants - errors}`);

    // 4. データベースの在庫カレンダーは存在しないためスキップ

  } catch (error) {
    console.error("スクリプトエラー:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
fixAllVariantInventory().catch((error) => {
  console.error("実行エラー:", error);
  process.exit(1);
});