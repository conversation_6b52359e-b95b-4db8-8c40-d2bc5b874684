(()=>{var te=Object.defineProperty;var U=Object.getOwnPropertySymbols;var re=Object.prototype.hasOwnProperty,ne=Object.prototype.propertyIsEnumerable;var G=(e,t,n)=>t in e?te(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,b=(e,t)=>{for(var n in t||(t={}))re.call(t,n)&&G(e,n,t[n]);if(U)for(var n of U(t))ne.call(t,n)&&G(e,n,t[n]);return e};var $=(e,t,n)=>new Promise((o,r)=>{var c=f=>{try{s(n.next(f))}catch(d){r(d)}},p=f=>{try{s(n.throw(f))}catch(d){r(d)}},s=f=>f.done?o(f.value):Promise.resolve(f.value).then(c,p);s((n=n.apply(e,t)).next())});function E(e){if(e==null||typeof e!="object")return!1;let t=Object.getPrototypeOf(e);return t==null||t===Object.prototype}function T(e){return e!=null&&e.kind===3}var N="__current",B={},se=[];function L(e,{strict:t=!0,components:n}={}){let o=0,r={strict:t,mounted:!1,channel:e,children:se,nodes:new WeakSet,parents:new WeakMap,tops:new WeakMap,components:new WeakMap,fragments:new WeakMap};t&&Object.freeze(n);let c={kind:0,options:t?Object.freeze({strict:t,components:n}):{strict:t,components:n},get children(){return r.children},createComponent(p,...s){if(n&&n.indexOf(p)<0)throw new Error(`Unsupported component: ${p}`);let[f,d,...i]=s,a=f!=null?f:{},m=[],h={};if(f)for(let l of Object.keys(f))l!=="children"&&(h[l]=k(V(f[l])));if(d)if(Array.isArray(d))for(let l of d)m.push(u(l,c));else{m.push(u(d,c));for(let l of i)m.push(u(l,c))}let g=`${o++}`,C={externalProps:t?Object.freeze(a):a,internalProps:h,children:t?Object.freeze(m):m},x=b({kind:1,get children(){return C.children},get props(){return C.externalProps},get remoteProps(){return C.internalProps},remove:()=>X(x),updateProps:l=>fe(x,l,C,r),append:(...l)=>S(x,l.map(y=>u(y,c)),C,r),appendChild:l=>M(x,u(l,c),C,r),removeChild:l=>j(x,l,C,r),replaceChildren:(...l)=>D(x,l.map(y=>u(y,c)),C,r),insertBefore:(l,y)=>R(x,u(l,c),y,C,r),insertChildBefore:(l,y)=>R(x,u(l,c),y,C,r)},B);r.components.set(x,C),Object.defineProperty(x,"type",{value:p,configurable:!1,writable:!1,enumerable:!0}),F(x,r),z(x,g,c);for(let l of C.children)I(x,l,r);return x},createText(p=""){let s=`${o++}`,f={text:p},d=a=>pe(i,a,f,r),i=b({kind:2,get text(){return f.text},update:d,updateText:d,remove:()=>X(i)},B);return F(i,r),z(i,s,c),i},createFragment(){let p=`${o++}`,s={children:t?Object.freeze([]):[]},f=b({kind:3,get children(){return s.children},append:(...d)=>S(f,d.map(i=>u(i,c)),s,r),appendChild:d=>M(f,u(d,c),s,r),removeChild:d=>j(f,d,s,r),replaceChildren:(...d)=>D(f,d.map(i=>u(i,c)),s,r),insertBefore:(d,i)=>R(f,u(d,c),i,s,r),insertChildBefore:(d,i)=>R(f,u(d,c),i,s,r)},B);return r.fragments.set(f,s),F(f,r),z(f,p,c),f},append:(...p)=>S(c,p.map(s=>u(s,c)),r,r),appendChild:p=>M(c,u(p,c),r,r),replaceChildren:(...p)=>D(c,p.map(s=>u(s,c)),r,r),removeChild:p=>j(c,p,r,r),insertBefore:(p,s)=>R(c,u(p,c),s,r,r),insertChildBefore:(p,s)=>R(c,u(p,c),s,r,r),mount(){return r.mounted?Promise.resolve():(r.mounted=!0,Promise.resolve(e(0,r.children.map(_))))}};return c}function ce(e,{tops:t}){var n;return((n=t.get(e))===null||n===void 0?void 0:n.kind)===0}function q(e,t){let n=o=>{if("children"in o)for(let r of o.children)t(r),n(r)};n(e)}function A(e,t,{remote:n,local:o}){let{mounted:r,channel:c}=t;r&&(e.kind===0||ce(e,t))&&n(c),o()}function pe(e,t,n,o){return A(e,o,{remote:r=>r(3,e.id,t),local:()=>{n.text=t}})}var O=Symbol("ignore");function fe(e,t,n,o){let{strict:r}=o,{internalProps:c,externalProps:p}=n,s={},f=[],d=!1;for(let i of Object.keys(t)){if(i==="children")continue;let a=p[i],m=t[i],h=c[i],g=V(m);if(h===g&&(g==null||typeof g!="object"))continue;let[C,x]=H(h,g);x&&f.push(...x),C!==O&&(d=!0,s[i]=C,T(a)&&K(a,o),T(m)&&I(e,m,o))}return A(e,o,{remote:i=>{d&&i(4,e.id,s)},local:()=>{let i=b(b({},p),t);n.externalProps=r?Object.freeze(i):i,n.internalProps=b(b({},n.internalProps),s);for(let[a,m]of f)a[N]=m}})}function H(e,t,n=new Set){return n.has(e)?[O]:typeof e=="function"&&N in e?(n.add(e),[typeof t=="function"?O:k(t),[[e,t]]]):Array.isArray(e)?(n.add(e),me(e,t,n)):E(e)&&!T(e)?(n.add(e),ae(e,t,n)):[e===t?O:t]}function k(e,t=new Map){let n=t.get(e);if(n)return n;if(T(e))return t.set(e,e),e;if(Array.isArray(e)){let o=[];t.set(e,o);for(let r of e)o.push(k(r,t));return o}if(E(e)){let o={};t.set(e,o);for(let r of Object.keys(e))o[r]=k(e[r],t);return o}if(typeof e=="function"){let o=(...r)=>o[N](...r);return Object.defineProperty(o,N,{enumerable:!1,configurable:!1,writable:!0,value:e}),t.set(e,o),o}return e}function P(e,t=new Set){if(!t.has(e)){if(t.add(e),Array.isArray(e))return e.reduce((n,o)=>{let r=P(o,t);return r?[...n,...r]:n},[]);if(E(e))return Object.keys(e).reduce((n,o)=>{let r=P(e[o],t);return r?[...n,...r]:n},[]);if(typeof e=="function")return N in e?[e]:void 0}}function X(e){var t;(t=e.parent)===null||t===void 0||t.removeChild(e)}function S(e,t,n,o){for(let r of t)M(e,r,n,o)}function M(e,t,n,o){var r;let{nodes:c,strict:p}=o;if(!c.has(t))throw new Error("Cannot append a node that was not created by this remote root");let s=t.parent,f=(r=s==null?void 0:s.children.indexOf(t))!==null&&r!==void 0?r:-1;return A(e,o,{remote:d=>{d(1,e.id,f<0?e.children.length:e.children.length-1,_(t),s?s.id:!1)},local:()=>{I(e,t,o);let d;if(s){let i=v(s,o),a=[...i.children];a.splice(f,1),s===e?d=a:(i.children=p?Object.freeze(a):a,d=[...n.children])}else d=[...n.children];d.push(t),n.children=p?Object.freeze(d):d}})}function D(e,t,n,o){for(let r of e.children)j(e,r,n,o);S(e,t,n,o)}function j(e,t,n,o){let{strict:r}=o,c=e.children.indexOf(t);if(c!==-1)return A(e,o,{remote:p=>p(2,e.id,c),local:()=>{K(t,o);let p=[...n.children];p.splice(p.indexOf(t),1),n.children=r?Object.freeze(p):p}})}function R(e,t,n,o,r){var c;let{strict:p,nodes:s}=r;if(!s.has(t))throw new Error("Cannot insert a node that was not created by this remote root");let f=t.parent,d=(c=f==null?void 0:f.children.indexOf(t))!==null&&c!==void 0?c:-1;return A(e,r,{remote:i=>{let a=n==null?e.children.length-1:e.children.indexOf(n);i(1,e.id,a<d||d<0?a:a-1,_(t),f?f.id:!1)},local:()=>{I(e,t,r);let i;if(f){let a=v(f,r),m=[...a.children];m.splice(d,1),f===e?i=m:(a.children=p?Object.freeze(m):m,i=[...o.children])}else i=[...o.children];n==null?i.push(t):i.splice(i.indexOf(n),0,t),o.children=p?Object.freeze(i):i}})}function u(e,t){return typeof e=="string"?t.createText(e):e}function I(e,t,n){let{tops:o,parents:r}=n,c=e.kind===0?e:o.get(e);o.set(t,c),r.set(t,e),J(t,n),q(t,p=>{o.set(p,c),J(p,n)})}function J(e,t){if(e.kind!==1)return;let n=e.props;n&&Object.values(n).forEach(o=>{T(o)&&I(e,o,t)})}function K(e,t){let{tops:n,parents:o}=t;n.delete(e),o.delete(e),q(e,r=>{n.delete(r),Q(r,t)}),Q(e,t)}function Q(e,t){if(e.kind!==1)return;let n=e.remoteProps;for(let o of Object.keys(n!=null?n:{})){let r=n[o];T(r)&&K(r,t)}}function F(e,{parents:t,tops:n,nodes:o}){o.add(e),Object.defineProperty(e,"parent",{get(){return t.get(e)},configurable:!0,enumerable:!0}),Object.defineProperty(e,"top",{get(){return n.get(e)},configurable:!0,enumerable:!0})}function _(e){return e.kind===2?{id:e.id,kind:e.kind,text:e.text}:{id:e.id,kind:e.kind,type:e.type,props:e.remoteProps,children:e.children.map(t=>_(t))}}function V(e){return T(e)?de(e):e}function de(e){return{id:e.id,kind:e.kind,get children(){return e.children.map(t=>_(t))}}}function v(e,t){return e.kind===0?t:e.kind===3?t.fragments.get(e):t.components.get(e)}function z(e,t,n){Object.defineProperty(e,"id",{value:t,configurable:!0,writable:!1,enumerable:!1}),Object.defineProperty(e,"root",{value:n,configurable:!0,writable:!1,enumerable:!1})}function ae(e,t,n){if(!E(t)){var o;return[k(t),(o=P(e))===null||o===void 0?void 0:o.map(s=>[s,void 0])]}let r=!1,c=[],p={};for(let s in e){let f=e[s];if(!(s in t)){r=!0;let m=P(f);m&&c.push(...m.map(h=>[h,void 0]))}let d=t[s],[i,a]=H(f,d,n);a&&c.push(...a),i!==O&&(r=!0,p[s]=i)}for(let s in t)s in p||(r=!0,p[s]=k(t[s]));return[r?p:O,c]}function me(e,t,n){if(!Array.isArray(t)){var o;return[k(t),(o=P(e))===null||o===void 0?void 0:o.map(i=>[i,void 0])]}let r=!1,c=[],p=t.length,s=e.length,f=Math.max(s,p),d=[];for(let i=0;i<f;i++){let a=e[i],m=t[i];if(i<p){if(i>=s){r=!0,d[i]=k(m);continue}let[h,g]=H(a,m,n);if(g&&c.push(...g),h===O){d[i]=a;continue}r=!0,d[i]=h}else{r=!0;let h=P(a);h&&c.push(...h.map(g=>[g,void 0]))}}return[r?d:O,c]}function ee(){return(t,n)=>{var o;function r(...c){return $(this,null,function*(){if(c.length===1)return n(...c);let[{channel:p,components:s},f]=c,d=L(p,{components:s,strict:!0}),i=n(d,f);return typeof i=="object"&&i!=null&&"then"in i&&(i=yield i),d.mount(),i})}return(o=globalThis.shopify)===null||o===void 0||o.extend(t,r),r}}var W=ee();var Ke=W("product.block.render",(e,{shop:t,i18n:n})=>{let o=e.createComponent("BlockStack",{spacing:"base"}),r=e.createComponent("Heading",{},"\u4E88\u7D04\u72B6\u6CC1\u30AB\u30EC\u30F3\u30C0\u30FC");o.appendChild(r);let c=e.createComponent("Text",{},"\u3053\u306E\u5546\u54C1\u306E\u4E88\u7D04\u72B6\u6CC1\u3092\u78BA\u8A8D\u3067\u304D\u307E\u3059\u3002");o.appendChild(c);let p=e.createComponent("Box",{border:"base",borderRadius:"base",padding:"base",background:"surface-subdued"}),s=e.createComponent("BlockStack",{spacing:"tight"}),f=e.createComponent("Text",{},"\u4E88\u7D04\u72B6\u6CC1\u8868\u793A\uFF08\u30B5\u30F3\u30D7\u30EB\uFF09");s.appendChild(f);let d=e.createComponent("Box",{padding:"base",background:"success-subdued",borderRadius:"base"});d.appendChild(e.createComponent("Text",{},"\u672C\u4E88\u7D04: 2023/12/10 \u301C 2023/12/12")),s.appendChild(d);let i=e.createComponent("Box",{padding:"base",background:"warning-subdued",borderRadius:"base"});i.appendChild(e.createComponent("Text",{},"\u4EEE\u4E88\u7D04: 2023/12/20 \u301C 2023/12/22")),s.appendChild(i);let a=e.createComponent("Box",{padding:"base",background:"neutral-subdued",borderRadius:"base"});a.appendChild(e.createComponent("Text",{},"\u30E1\u30F3\u30C6\u30CA\u30F3\u30B9: 2023/12/25 \u301C 2023/12/27")),s.appendChild(a),p.appendChild(s),o.appendChild(p);let m=e.createComponent("Text",{size:"small"},"\u203B \u4E88\u7D04\u72B6\u6CC1\u306F\u968F\u6642\u66F4\u65B0\u3055\u308C\u307E\u3059\u3002\u6700\u65B0\u306E\u60C5\u5831\u306F\u4E88\u7D04\u6642\u306B\u78BA\u8A8D\u3057\u3066\u304F\u3060\u3055\u3044\u3002");o.appendChild(m);let h=e.createComponent("InlineStack",{spacing:"tight",alignment:"center"}),g=e.createComponent("Button",{kind:"primary",onPress:()=>{console.log("\u4E88\u7D04\u30DC\u30BF\u30F3\u304C\u30AF\u30EA\u30C3\u30AF\u3055\u308C\u307E\u3057\u305F")}},"\u3053\u306E\u5546\u54C1\u3092\u4E88\u7D04\u3059\u308B");h.appendChild(g),o.appendChild(h),e.appendChild(o)});})();
//# sourceMappingURL=product-availability-block.js.map
