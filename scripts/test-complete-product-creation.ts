/**
 * 完全なメタフィールド対応商品登録テスト
 * CSVから選択した商品を完全な情報で登録
 */

import { CompleteProductCreatorService, CSVProductData } from '../app/services/shopify/complete-product-creator.service';
import { GraphQLClient } from 'graphql-request';
import * as dotenv from 'dotenv';

// .envファイルを読み込み
dotenv.config();

class CompleteProductCreationTester {
  private productCreator: CompleteProductCreatorService;
  private results: Array<{
    modelNumber: string;
    name: string;
    shopifyProductId?: string;
    success: boolean;
    createdVariants: number;
    errors: string[];
  }> = [];

  constructor() {
    this.productCreator = new CompleteProductCreatorService();
  }

  /**
   * CSVから選択した商品で完全登録テスト
   */
  async testCompleteProductCreation(): Promise<void> {
    console.log('=== 完全商品登録テスト開始 ===\n');

    try {
      // 環境変数から設定を取得
      const shopifyShop = process.env.SHOPIFY_SHOP;
      const shopifyAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

      if (!shopifyShop || !shopifyAccessToken) {
        console.error('❌ 環境変数が設定されていません');
        return;
      }

      // GraphQLClientを直接作成
      const graphqlClient = new GraphQLClient(
        `https://${shopifyShop}/admin/api/2025-01/graphql.json`,
        {
          headers: {
            'X-Shopify-Access-Token': shopifyAccessToken,
            'Content-Type': 'application/json',
          },
        }
      );

      // adminオブジェクトを作成
      const admin = {
        graphql: async (query: string, variables?: any) => {
          const result = await graphqlClient.request(query, variables);
          return {
            json: () => Promise.resolve({ data: result })
          };
        }
      };

      // CSVから選択したテスト商品（1つのみでテスト）
      const testProducts: CSVProductData[] = [
        // 花器（シルバー）- 完全なメタフィールドテスト用
        {
          menu: 'PROP',
          majorCategory: 'ニュアンスオブジェ;花器',
          minorCategory: 'その他オブジェ;ゴールド・シルバー',
          name: '花器　シルバー　穴開きボトル型',
          modelNumber: '212-05-023',
          sizeW: '14',
          sizeD: '0',
          sizeH: '31',
          sizeSH: '0',
          sizeOther: '',
          color: 'シルバー',
          colorOther: '',
          material: '',
          other: '',
          stock: '1',
          rentalPrice1Day: '1500',
          rentalPrice2Days: '1800',
          rentalPrice3Days: '2100',
          rentalPrice4Days: '2400',
          publicStatus: '公開',
          newStatus: 'あり',
          campaign: '2025 NEW ITEMS'
        }
      ];

      console.log(`📦 テスト商品数: ${testProducts.length}\n`);

      // 各商品を登録してテスト
      for (const product of testProducts) {
        await this.createAndTestCompleteProduct(admin, product);
      }

      this.displayResults();

    } catch (error) {
      console.error('❌ テスト実行エラー:', error);
    }
  }

  /**
   * 単一商品を完全登録してテスト
   */
  private async createAndTestCompleteProduct(admin: any, product: CSVProductData): Promise<void> {
    console.log(`--- ${product.name} ---`);
    console.log(`型番: ${product.modelNumber}`);
    console.log(`カテゴリ: ${product.menu} > ${product.majorCategory} > ${product.minorCategory}`);
    console.log(`価格: ¥${parseInt(product.rentalPrice1Day).toLocaleString()}`);

    const result = {
      modelNumber: product.modelNumber,
      name: product.name,
      success: false,
      createdVariants: 0,
      errors: []
    };

    try {
      // 完全商品作成を実行
      console.log('完全商品作成を実行中...');

      const creationResult = await this.productCreator.createCompleteProduct(admin, product, {
        location: 'NY', // NYロケーションで統一
        createProvisionalVariants: false
      });

      result.shopifyProductId = creationResult.shopifyProductId;
      result.createdVariants = creationResult.createdVariants;
      result.success = creationResult.success;

      if (creationResult.success) {
        console.log(`✅ 完全商品作成成功`);
        console.log(`  - Shopify ID: ${creationResult.shopifyProductId}`);
        console.log(`  - 作成バリエーション数: ${creationResult.createdVariants}`);
        console.log(`  - メタフィールド設定: ${creationResult.metafieldsSet ? '成功' : '失敗'}`);
        console.log(`  - 在庫設定: ${creationResult.inventoryUpdated ? '成功' : '失敗'}`);

        // 期待されるSKU形式を表示
        console.log(`  - 期待されるSKU: ${product.modelNumber}-1D, ${product.modelNumber}-2D...`);
        console.log(`  - 商品URL: https://peaces-test-block.myshopify.com/admin/products/${creationResult.shopifyProductId}`);
      } else {
        console.log(`⚠️ 完全商品作成に問題: ${creationResult.errors.length}個のエラー`);
        result.errors.push(...creationResult.errors);
      }

    } catch (error) {
      console.error(`❌ エラー: ${error.message}`);
      result.errors.push(error.message);
    }

    this.results.push(result);
    console.log('');
  }

  /**
   * テスト結果を表示
   */
  private displayResults(): void {
    console.log('=== 完全商品登録テスト結果サマリー ===');

    const totalProducts = this.results.length;
    const successProducts = this.results.filter(r => r.success).length;
    const totalVariantsCreated = this.results.reduce((sum, r) => sum + r.createdVariants, 0);
    const errorProducts = this.results.filter(r => !r.success).length;

    console.log(`処理した商品数: ${totalProducts}`);
    console.log(`成功: ${successProducts}`);
    console.log(`失敗: ${errorProducts}`);
    console.log(`作成したバリエーション総数: ${totalVariantsCreated}`);

    console.log('\n詳細結果:');
    this.results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${result.name}`);
      console.log(`  型番: ${result.modelNumber}`);
      if (result.shopifyProductId) {
        console.log(`  Shopify ID: ${result.shopifyProductId}`);
      }
      console.log(`  作成バリエーション数: ${result.createdVariants}`);
      if (result.errors.length > 0) {
        console.log(`  エラー: ${result.errors.join(', ')}`);
      }
    });

    if (successProducts > 0) {
      console.log('\n✅ 完全商品登録テスト成功！');
      console.log('🎯 確認ポイント:');
      console.log('1. SKU形式: [型番]-1D, [型番]-2D... になっているか');
      console.log('2. メタフィールド: 全ての情報が正しく設定されているか');
      console.log('3. ロケーション: NYに設定されているか');
      console.log('4. 在庫: 各バリエーションに1個設定されているか');
      console.log('5. 価格: 正しい料金計算になっているか');
      console.log('\nShopify管理画面で作成された商品を確認してください。');
    } else {
      console.log('\n❌ 完全商品登録テスト失敗。エラーを確認して修正してください。');
    }

    console.log('\n=== テスト完了 ===');
  }

  /**
   * 作成したテスト商品を削除
   */
  async cleanupTestProducts(): Promise<void> {
    console.log('=== テスト商品のクリーンアップ ===\n');

    try {
      // 環境変数から設定を取得
      const shopifyShop = process.env.SHOPIFY_SHOP;
      const shopifyAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

      if (!shopifyShop || !shopifyAccessToken) {
        console.error('❌ 環境変数が設定されていません');
        return;
      }

      // GraphQLClientを直接作成
      const graphqlClient = new GraphQLClient(
        `https://${shopifyShop}/admin/api/2025-01/graphql.json`,
        {
          headers: {
            'X-Shopify-Access-Token': shopifyAccessToken,
            'Content-Type': 'application/json',
          },
        }
      );

      // adminオブジェクトを作成
      const admin = {
        graphql: async (query: string, variables?: any) => {
          const result = await graphqlClient.request(query, variables);
          return {
            json: () => Promise.resolve({ data: result })
          };
        }
      };

      for (const result of this.results) {
        if (result.success && result.shopifyProductId) {
          try {
            console.log(`削除中: ${result.name} (${result.shopifyProductId})`);

            await admin.graphql(`
              mutation productDelete($input: ProductDeleteInput!) {
                productDelete(input: $input) {
                  deletedProductId
                  userErrors {
                    field
                    message
                  }
                }
              }
            `, {
              input: {
                id: `gid://shopify/Product/${result.shopifyProductId}`
              }
            });

            console.log(`✅ 削除完了: ${result.name}`);

          } catch (error) {
            console.error(`❌ 削除エラー (${result.name}): ${error.message}`);
          }

          // API制限を避けるため少し待機
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      console.log('\n=== クリーンアップ完了 ===');

    } catch (error) {
      console.error('❌ クリーンアップエラー:', error);
    }
  }
}

// メイン実行
async function main() {
  const tester = new CompleteProductCreationTester();

  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'test':
      await tester.testCompleteProductCreation();
      break;
    case 'cleanup':
      await tester.cleanupTestProducts();
      break;
    default:
      console.log('使用方法:');
      console.log('  npm run test-complete-products test     # 完全商品登録テスト実行');
      console.log('  npm run test-complete-products cleanup  # テスト商品削除');
      break;
  }
}

if (import.meta.url === new URL(import.meta.url).href) {
  main().catch(console.error);
}

export { CompleteProductCreationTester };
