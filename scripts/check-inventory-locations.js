/**
 * 在庫ロケーション確認スクリプト
 *
 * このスクリプトは、Shopifyの在庫ロケーションを確認します。
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 在庫ロケーション取得のGraphQLクエリ
const GET_LOCATIONS_QUERY = gql`
  query getLocations {
    locations(first: 10) {
      edges {
        node {
          id
          name
          isActive
          address {
            formatted
          }
        }
      }
    }
  }
`;

// 在庫ロケーションを取得する関数
async function getLocations() {
  try {
    const result = await client.request(GET_LOCATIONS_QUERY);
    return result.locations.edges.map(edge => edge.node);
  } catch (error) {
    console.error('在庫ロケーション取得中にエラーが発生しました:', error);
    return [];
  }
}

// メイン処理
async function main() {
  console.log('在庫ロケーションを確認しています...');
  
  // 在庫ロケーションを取得
  const locations = await getLocations();
  
  if (locations.length === 0) {
    console.log('在庫ロケーションが見つかりませんでした。');
    return;
  }
  
  console.log(`${locations.length}件の在庫ロケーションが見つかりました。`);
  
  // 在庫ロケーションを表示
  for (const location of locations) {
    console.log(`ID: ${location.id}`);
    console.log(`名前: ${location.name}`);
    console.log(`アクティブ: ${location.isActive ? 'はい' : 'いいえ'}`);
    console.log(`住所: ${location.address?.formatted || '未設定'}`);
    console.log('---');
  }
}

// スクリプト実行
main().catch(error => {
  console.error('スクリプト実行中にエラーが発生しました:', error);
  process.exit(1);
});
