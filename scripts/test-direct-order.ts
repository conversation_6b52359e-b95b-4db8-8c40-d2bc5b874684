/**
 * 直接Shopify注文を作成するテストスクリプト
 *
 * このスクリプトは、Shopify GraphQL APIを直接呼び出して注文を作成します。
 *
 * 実行方法: npx tsx scripts/test-direct-order.ts [予約ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';
import { GraphQLClient } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト設定
const config = {
  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
  
  // Shopify APIアクセストークン
  shopifyAccessToken: process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
  
  // Shopify API バージョン
  apiVersion: '2025-01'
};

// 注文作成ミューテーション（通常注文）
const CREATE_ORDER = `
  mutation orderCreate($input: OrderInput!) {
    orderCreate(input: $input) {
      order {
        id
        name
        totalPrice
        customer {
          id
          email
        }
        lineItems(first: 10) {
          edges {
            node {
              title
              quantity
              originalUnitPrice
              variant {
                id
                title
              }
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// ドラフト注文作成ミューテーション（バックアップ用）
const CREATE_DRAFT_ORDER = `
  mutation draftOrderCreate($input: DraftOrderInput!) {
    draftOrderCreate(input: $input) {
      draftOrder {
        id
        name
        totalPrice
        customer {
          id
          email
        }
        lineItems(first: 10) {
          edges {
            node {
              title
              quantity
              originalUnitPrice
              variant {
                id
                title
              }
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// ドラフト注文完了ミューテーション（バックアップ用）
const COMPLETE_DRAFT_ORDER = `
  mutation draftOrderComplete($id: ID!, $paymentPending: Boolean) {
    draftOrderComplete(id: $id, paymentPending: $paymentPending) {
      draftOrder {
        id
        order {
          id
          name
          totalPrice
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * 予約情報を表示する関数
 */
async function displayBookingInfo(bookingId: string) {
  try {
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        product: true
      }
    });

    if (!booking) {
      console.error(`予約ID ${bookingId} が見つかりません`);
      return null;
    }

    console.log('----- 予約情報 -----');
    console.log(`ID: ${booking.id}`);
    console.log(`予約ID: ${booking.bookingId}`);
    console.log(`開始日: ${format(booking.startDate, 'yyyy/MM/dd', { locale: ja })}`);
    console.log(`終了日: ${format(booking.endDate, 'yyyy/MM/dd', { locale: ja })}`);
    console.log(`ステータス: ${booking.status}`);
    console.log(`予約タイプ: ${booking.bookingType}`);
    console.log(`顧客名: ${booking.customerName}`);
    console.log(`顧客メール: ${booking.customerEmail}`);
    console.log(`顧客ID: ${booking.customerId || 'なし'}`);
    console.log(`商品: ${booking.product?.title || 'なし'} (ID: ${booking.productId})`);
    console.log(`Shopify注文ID: ${booking.shopifyOrderId || 'なし'}`);
    console.log(`Shopify注文番号: ${booking.shopifyOrderName || 'なし'}`);
    console.log('-------------------');

    return booking;
  } catch (error) {
    console.error('予約情報表示エラー:', error);
    return null;
  }
}

/**
 * 最新の予約を取得する関数
 */
async function getLatestBooking() {
  try {
    const booking = await prisma.booking.findFirst({
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!booking) {
      console.error('予約が見つかりません');
      return null;
    }

    return booking;
  } catch (error) {
    console.error('最新予約取得エラー:', error);
    return null;
  }
}

/**
 * 予約からShopify注文を作成する関数
 */
async function createOrderFromBooking(bookingId: string) {
  try {
    console.log('予約から注文を作成中...');

    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        product: true
      }
    });

    if (!booking) {
      throw new Error(`予約ID ${bookingId} が見つかりません`);
    }

    console.log(`予約情報を取得しました: ${booking.bookingId}`);

    // 顧客IDを正規化
    const customerId = booking.customerId?.startsWith('gid://shopify/Customer/')
      ? booking.customerId
      : `gid://shopify/Customer/${booking.customerId}`;

    // 予約期間を計算
    const startDate = format(booking.startDate, 'yyyy-MM-dd');
    const endDate = format(booking.endDate, 'yyyy-MM-dd');
    const rentalPeriod = `${startDate} 〜 ${endDate}`;

    // 注文作成用の入力データを作成
    const orderInput = {
      customerId: customerId,
      lineItems: [
        {
          // variantIdまたはtitle+originalUnitPriceのいずれかが必要
          title: `${booking.product?.title} (レンタル: ${rentalPeriod})`,
          originalUnitPrice: booking.bookingType === 'PROVISIONAL' 
            ? booking.depositAmount?.toString() // 仮予約の場合はデポジット金額
            : booking.totalAmount?.toString(), // 確定予約の場合は全額
          quantity: 1,
          taxable: true,
          requiresShipping: false,
          // バリアントIDがある場合は指定
          variantId: booking.variantId || null
        }
      ],
      tags: ['rental', booking.bookingType.toLowerCase(), `booking-${booking.bookingId}`],
      note: `予約ID: ${booking.bookingId}\n予約期間: ${rentalPeriod}\n備考: ${booking.notes || 'なし'}`
    };

    console.log('注文作成データ:', JSON.stringify(orderInput, null, 2));

    // GraphQLクライアントを初期化
    if (!config.shopifyAccessToken) {
      throw new Error('SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません');
    }

    const graphQLClient = new GraphQLClient(
      `https://${config.shop}/admin/api/${config.apiVersion}/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': config.shopifyAccessToken,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('GraphQL呼び出しを実行します (注文作成)...');
    
    // 通常注文を作成
    try {
      const response = await graphQLClient.request(CREATE_ORDER, {
        input: orderInput
      });

      console.log('GraphQL応答を受信しました');
      console.log('GraphQL応答内容:', JSON.stringify(response, null, 2));

      if (response.orderCreate?.userErrors?.length > 0) {
        console.error('注文作成エラー:', response.orderCreate.userErrors);
        throw new Error(`注文の作成中にエラーが発生しました: ${JSON.stringify(response.orderCreate.userErrors)}`);
      }

      // 予約情報を更新（注文IDを保存）
      const orderId = response.orderCreate.order.id;
      const orderName = response.orderCreate.order.name;
      
      await prisma.booking.update({
        where: { id: booking.id },
        data: {
          shopifyOrderId: orderId,
          shopifyOrderName: orderName
        }
      });

      console.log(`予約情報を更新しました: 注文ID ${orderId}, 注文番号 ${orderName}`);

      return {
        success: true,
        orderId,
        orderName
      };
    } catch (orderError) {
      console.error('通常注文作成エラー:', orderError);
      console.log('ドラフト注文作成にフォールバックします...');
      
      // 通常注文が失敗した場合、ドラフト注文にフォールバック
      const draftOrderInput = {
        ...orderInput,
        lineItems: orderInput.lineItems.map(item => ({
          ...item,
          // ドラフト注文ではoriginalUnitPriceではなくpriceを使用
          price: item.originalUnitPrice,
          originalUnitPrice: undefined
        }))
      };
      
      const draftResponse = await graphQLClient.request(CREATE_DRAFT_ORDER, {
        input: draftOrderInput
      });

      console.log('ドラフト注文応答:', JSON.stringify(draftResponse, null, 2));

      if (draftResponse.draftOrderCreate?.userErrors?.length > 0) {
        console.error('ドラフト注文作成エラー:', draftResponse.draftOrderCreate.userErrors);
        throw new Error(`ドラフト注文の作成中にエラーが発生しました: ${JSON.stringify(draftResponse.draftOrderCreate.userErrors)}`);
      }

      // 作成されたドラフト注文を完了状態にする
      const draftOrderId = draftResponse.draftOrderCreate.draftOrder.id;
      console.log(`ドラフト注文ID: ${draftOrderId} を完了状態にします`);

      const completeResponse = await graphQLClient.request(COMPLETE_DRAFT_ORDER, {
        id: draftOrderId,
        paymentPending: booking.bookingType === 'PROVISIONAL' // 仮予約の場合は支払い保留
      });

      console.log('ドラフト注文完了応答:', JSON.stringify(completeResponse, null, 2));

      if (completeResponse.draftOrderComplete?.userErrors?.length > 0) {
        console.error('ドラフト注文完了エラー:', completeResponse.draftOrderComplete.userErrors);
        throw new Error(`ドラフト注文の完了中にエラーが発生しました: ${JSON.stringify(completeResponse.draftOrderComplete.userErrors)}`);
      }

      // 予約情報を更新（注文IDを保存）
      const orderId = completeResponse.draftOrderComplete.draftOrder.order.id;
      const orderName = completeResponse.draftOrderComplete.draftOrder.order.name;
      
      await prisma.booking.update({
        where: { id: booking.id },
        data: {
          shopifyOrderId: orderId,
          shopifyOrderName: orderName
        }
      });

      console.log(`予約情報を更新しました: 注文ID ${orderId}, 注文番号 ${orderName}`);

      return {
        success: true,
        orderId,
        orderName,
        isDraftOrder: true
      };
    }
  } catch (error) {
    console.error('注文作成エラー:', error);
    if (error instanceof Error) {
      console.error('エラーメッセージ:', error.message);
      console.error('スタックトレース:', error.stack);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : '不明なエラー'
    };
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('予約からShopify注文を作成するテストを開始します...');
    console.log('環境変数:');
    console.log('- SHOPIFY_SHOP:', config.shop);
    console.log('- SHOPIFY_ADMIN_API_ACCESS_TOKEN:', config.shopifyAccessToken ? '設定されています' : '設定されていません');
    console.log('- API バージョン:', config.apiVersion);

    // コマンドライン引数から予約IDを取得
    const bookingId = process.argv[2];

    // 予約IDが指定されていない場合は最新の予約を使用
    let targetBookingId = bookingId;
    if (!targetBookingId) {
      console.log('予約IDが指定されていないため、最新の予約を使用します');
      const latestBooking = await getLatestBooking();
      if (!latestBooking) {
        console.error('予約が見つかりません。予約を作成してから再試行してください。');
        process.exit(1);
      }
      targetBookingId = latestBooking.id;
      console.log(`最新の予約ID: ${targetBookingId}`);
    }

    // 予約情報を表示
    console.log('\n----- 予約情報 -----');
    const booking = await displayBookingInfo(targetBookingId);
    
    if (!booking) {
      console.error('予約情報の取得に失敗しました');
      process.exit(1);
    }

    // 予約からShopify注文を作成
    console.log('\n----- 注文作成処理を実行中... -----');
    const result = await createOrderFromBooking(targetBookingId);
    
    if (result.success) {
      console.log('注文作成が成功しました');
      console.log(`注文ID: ${result.orderId}`);
      console.log(`注文番号: ${result.orderName}`);
      
      // 更新された予約情報を表示
      console.log('\n----- 更新後の予約情報 -----');
      await displayBookingInfo(targetBookingId);
      
      console.log('\nテストが成功しました！');
    } else {
      console.error('注文作成に失敗しました');
      console.error(`エラー: ${result.error || 'エラー詳細なし'}`);
      console.error('\nテストが失敗しました。');
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    if (error instanceof Error) {
      console.error('エラーメッセージ:', error.message);
      console.error('スタックトレース:', error.stack);
    }
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
