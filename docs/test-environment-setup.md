# テスト環境のセットアップガイド

このドキュメントでは、Shopify と Prisma の連携テストを実行するためのテスト環境のセットアップ方法を説明します。

## 目次

1. [前提条件](#前提条件)
2. [テスト環境の設定](#テスト環境の設定)
3. [テストの実行](#テストの実行)
4. [トラブルシューティング](#トラブルシューティング)

## 前提条件

テスト環境をセットアップする前に、以下の前提条件を満たしていることを確認してください：

- Node.js v20.18.1 以上がインストールされていること
- npm v10.2.3 以上がインストールされていること
- PostgreSQL データベースが利用可能であること
- Shopify Partner アカウントがあること（実際の API を使用する場合）
- Shopify API キーとシークレットが取得済みであること（実際の API を使用する場合）

## テスト環境の設定

### 1. 環境変数の設定

テスト環境の設定は、`.env.test` ファイルで行います。このファイルは、プロジェクトのルートディレクトリに配置します。

`.env.test.example` ファイルをコピーして `.env.test` ファイルを作成し、必要な情報を設定してください：

```bash
cp .env.test.example .env.test
```

`.env.test` ファイルを編集して、以下の情報を設定します：

```
# Shopify API 設定
SHOPIFY_API_KEY=your_api_key
SHOPIFY_API_SECRET=your_api_secret
SHOPIFY_SHOP=your-shop.myshopify.com
SHOPIFY_ADMIN_API_ACCESS_TOKEN=your_access_token
SHOPIFY_API_VERSION=2024-01

# テスト設定
TEST_PRODUCT_ID=123456789
TEST_BOOKING_DAYS=3
TEST_CUSTOMER_EMAIL=<EMAIL>
TEST_CUSTOMER_NAME=テスト顧客
USE_LOCAL_MODE=true

# データベース設定
DATABASE_URL=postgresql://username:password@localhost:5432/test_database
```

> **注意**: `.env.test` ファイルには機密情報が含まれるため、Git リポジトリにコミットしないでください。`.gitignore` ファイルに `.env.test` を追加してください。

### 2. テストデータベースのセットアップ

テスト用のデータベースを作成し、スキーマを適用します：

```bash
# テスト用のデータベースを作成
createdb test_database

# スキーマを適用
npx prisma db push --accept-data-loss
```

### 3. 依存パッケージのインストール

テストに必要な依存パッケージをインストールします：

```bash
npm install
npm install --save-dev uuid node-fetch lru-cache @types/uuid @types/node-fetch
```

## テストの実行

### 基本テスト

基本的な連携テストを実行するには、以下のコマンドを使用します：

```bash
npm run test:shopify-prisma-integration
```

### ローカルモードでのテスト

Shopify API を使用せずにローカルモードでテストを実行するには、以下のコマンドを使用します：

```bash
npm run test:shopify-prisma-integration -- --local
```

または、`.env.test` ファイルで `USE_LOCAL_MODE=true` を設定します。

### エラーケーステスト

エラーケースのテストを実行するには、以下のコマンドを使用します：

```bash
npm run test:shopify-prisma-integration -- --error-cases
```

### バリエーションテスト

バリエーション商品のテストを実行するには、以下のコマンドを使用します：

```bash
npm run test:shopify-prisma-integration -- --variants
```

### すべてのテストを実行

すべてのテストを実行するには、以下のコマンドを使用します：

```bash
npm run test:all
```

### テストデータのクリーンアップ

テストデータをクリーンアップするには、以下のコマンドを使用します：

```bash
npm run test:cleanup
```

### テスト結果レポートの生成

テスト結果レポートを生成するには、以下のコマンドを使用します：

```bash
npm run test:report
```

## トラブルシューティング

### Shopify API エラー

Shopify API に関連するエラーが発生した場合は、以下を確認してください：

- API キーとシークレットが正しく設定されているか
- アクセストークンが有効か
- API バージョンが正しいか
- レート制限に達していないか

### データベースエラー

データベースに関連するエラーが発生した場合は、以下を確認してください：

- データベース接続文字列が正しいか
- データベースが起動しているか
- 必要なテーブルが存在するか

### テストスクリプトエラー

テストスクリプトに関連するエラーが発生した場合は、以下を確認してください：

- 必要な依存パッケージがインストールされているか
- Node.js のバージョンが互換性があるか
- TypeScript のバージョンが互換性があるか

### デバッグモード

デバッグモードでテストを実行するには、`.env.test` ファイルで `DEBUG=true` を設定します。これにより、より詳細なログが出力されます。

```
DEBUG=true
LOG_LEVEL=debug
```

また、以下のコマンドでデバッガを有効にしてテストを実行することもできます：

```bash
npx tsx --inspect scripts/test-shopify-prisma-integration.ts
```
