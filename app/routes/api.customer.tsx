import { json } from "@remix-run/node";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { logger } from "../utils/logger";
import { GraphQLClient, gql } from 'graphql-request';

/**
 * 顧客情報取得API
 * GET: 顧客IDから顧客情報を取得
 * POST: 顧客検索を実行
 */
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Shopify認証
    const { admin, session } = await authenticate.admin(request);
    const shop = session.shop;
    const accessToken = session.accessToken;

    // URLパラメータを取得
    const url = new URL(request.url);
    const customerId = url.searchParams.get("id");

    if (!customerId) {
      return json({ error: "顧客IDが指定されていません" }, { status: 400 });
    }

    // GraphQLクライアントの初期化
    const client = new GraphQLClient(
      `https://${shop}/admin/api/2024-01/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': accessToken,
          'Content-Type': 'application/json',
        },
      }
    );

    // GID形式でない場合はGID形式に変換
    const gid = customerId.startsWith('gid://')
      ? customerId
      : `gid://shopify/Customer/${customerId}`;

    // GraphQLクエリ
    const GET_CUSTOMER_QUERY = gql`
      query getCustomer($id: ID!) {
        customer(id: $id) {
          id
          firstName
          lastName
          email
          phone
          createdAt
          updatedAt
          defaultAddress {
            address1
            address2
            city
            province
            zip
            country
          }
        }
      }
    `;

    // APIリクエスト
    const response = await client.request(GET_CUSTOMER_QUERY, {
      id: gid
    });

    // レスポンスから顧客情報を取得
    if (response && response.customer) {
      const customer = response.customer;
      const defaultAddress = customer.defaultAddress;

      // 住所を整形
      let address = '';
      if (defaultAddress) {
        address = [
          defaultAddress.address1,
          defaultAddress.address2,
          defaultAddress.city,
          defaultAddress.province,
          defaultAddress.zip,
          defaultAddress.country
        ].filter(Boolean).join(' ');
      }

      // 顧客情報を整形
      const customerInfo = {
        id: gid,
        shopifyId: gid.replace('gid://shopify/Customer/', ''),
        firstName: customer.firstName || '',
        lastName: customer.lastName || '',
        name: `${customer.lastName || ''} ${customer.firstName || ''}`.trim(),
        email: customer.email || '',
        phone: customer.phone || '',
        address,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt
      };

      return json({ customer: customerInfo });
    } else {
      return json({ error: "顧客が見つかりませんでした" }, { status: 404 });
    }
  } catch (error) {
    logger.error("顧客情報取得エラー:", error);
    return json(
      { error: "顧客情報の取得中にエラーが発生しました", details: String(error) },
      { status: 500 }
    );
  }
}

/**
 * 顧客検索API
 */
export async function action({ request }: ActionFunctionArgs) {
  try {
    // Shopify認証
    const { admin, session } = await authenticate.admin(request);
    const shop = session.shop;
    const accessToken = session.accessToken;

    // リクエストボディを取得
    const body = await request.json();
    const { query, limit = 10 } = body;

    if (!query) {
      return json({ error: "検索クエリが指定されていません" }, { status: 400 });
    }

    // GraphQLクライアントの初期化
    const client = new GraphQLClient(
      `https://${shop}/admin/api/2024-01/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': accessToken,
          'Content-Type': 'application/json',
        },
      }
    );

    // GraphQLクエリ
    const SEARCH_CUSTOMERS_QUERY = gql`
      query searchCustomers($query: String!, $first: Int!) {
        customers(first: $first, query: $query) {
          edges {
            node {
              id
              firstName
              lastName
              email
              phone
              createdAt
              updatedAt
              defaultAddress {
                address1
                address2
                city
                province
                zip
                country
              }
            }
          }
        }
      }
    `;

    // APIリクエスト
    const response = await client.request(SEARCH_CUSTOMERS_QUERY, {
      query,
      first: limit
    });

    // レスポンスから顧客情報を取得
    if (response && response.customers && response.customers.edges) {
      const customers = response.customers.edges.map((edge: any) => {
        const customer = edge.node;
        const defaultAddress = customer.defaultAddress;

        // 住所を整形
        let address = '';
        if (defaultAddress) {
          address = [
            defaultAddress.address1,
            defaultAddress.address2,
            defaultAddress.city,
            defaultAddress.province,
            defaultAddress.zip,
            defaultAddress.country
          ].filter(Boolean).join(' ');
        }

        // 顧客情報を整形
        return {
          id: customer.id,
          shopifyId: customer.id.replace('gid://shopify/Customer/', ''),
          firstName: customer.firstName || '',
          lastName: customer.lastName || '',
          name: `${customer.lastName || ''} ${customer.firstName || ''}`.trim(),
          email: customer.email || '',
          phone: customer.phone || '',
          address,
          createdAt: customer.createdAt,
          updatedAt: customer.updatedAt
        };
      });

      return json({ customers });
    } else {
      return json({ customers: [] });
    }
  } catch (error) {
    logger.error("顧客検索エラー:", error);
    return json(
      { error: "顧客検索中にエラーが発生しました", details: String(error) },
      { status: 500 }
    );
  }
}
