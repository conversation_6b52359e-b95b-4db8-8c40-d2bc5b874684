{% comment %}
/**************************************************
* DO NOT MODIFY THIS FILE!!!                      *
* IT WILL BE OVERWRITTEN BY UPDATES FROM GROWAVE! *
**************************************************/
{% endcomment %}

#widget-fave-html .ssw-fave-btn {
    width: 100%;
    line-height: 32px;
}

#widget-fave-html .ssw-fave-btn-content {
    width: 100%;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-radius: 0;
    color: #141414;
    border-color: #141414;
}

#widget-fave-html .ssw-fave-btn-counter,
#widget-fave-html .ssw-fave-btn-counter:before {
    border-radius: 0;
    color: #141414;
    border-color: #141414;
}

#ssw-widget-recommends-html .ssw-reviews-header .ssw-stars-averg {
    float: left;
}

#ssw-widget-recommends-html .ssw-reviews-header .ssw-span12 {
    text-align: right;
}

#ssw-widget-recommends-html .ssw-reviews-header .ssw-question-add-review,
#ssw-widget-recommends-html .ssw-reviews-header .ssw-ask-question-link,
.ssw-control-group .btn {
    background-color: #141414 !important;
    border-color: #141414 !important;
    color: #fff !important;
}

#ssw-widget-recommends-html .ssw-reviews-header .ssw-question-add-review:hover,
#ssw-widget-recommends-html .ssw-reviews-header .ssw-ask-question-link:hover,
.ssw-control-group .btn:hover {
    background-color: #fff !important;
    border-color: #141414 !important;
    color: #141414 !important;
}

.review-wrapp-btn {
    display: flex;
    justify-content: flex-end;
}

.ssw-add-recommend {
    order: 2;
}

.ssw-faveiticon {
    position: relative;
    display: flex;
    height: 100%;
    align-items: center;
    padding-left: 16px;
    padding-right: 16px;
}

.ssw-faveiticon i {
    margin-right: 3px;
}

.ssw-link-fave-menu i {
    font-size: 22px;
    margin-right: 2px;
}

.ssw-link-fave-menu {
    display: flex;
    align-items: center;
}

#ssw-topauth .ssw-tprofile {
    display: block;
}

#ssw-topauth .ssw-dropdown-menu li:before {
    display: none;
}

@media all and (min-width: 1025px) {
    .ssw-link-fave-menu {
        top: 2px;
        position: relative;
    }
    .ssw-link-fave-menu i {
        font-size: 19px;
    }
}

.ssw-nav li:before {
    display: none!important;
}

.ssw-edit-profile-page .ssw-nav > li.ssw-active > a,
.ssw-edit-profile-page .ssw-nav > li.ssw-active > a:hover {
    color: #000!important;
}

.ssw-nav > li.ssw-active > a.ssw-product-reviews-title,
.ssw-nav > li.ssw-active > a.ssw-product-reviews-title:hover,
.ssw-nav > li.ssw-active > a.ssw-product-comments-title,
.ssw-nav > li.ssw-active > a.ssw-product-comments-title:hover {
    color: #141414 !important;
    border-bottom-color: #141414 !important;
}

@media (max-width: 768px) {
    .ssw-page {
        padding: 0px 15px;
    }
}

#my-wishlist #ssw-page {
    margin: 20px auto;
}

.ssw-dropdown-menu li:before,
.ssw-page li:before{
    content: unset !important;
}