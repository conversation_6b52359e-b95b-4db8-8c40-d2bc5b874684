/**
 * 予約検索機能テストスクリプト
 * 
 * このスクリプトは、予約検索機能が正しく動作するかをテストします。
 * 様々な検索パターン（スペースあり/なし、姓名の順序など）をテストします。
 * 
 * 使用方法:
 * npm run test:booking-search
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// 検索テスト関数
async function testSearch(query: string, shop: string = 'peaces-test-block.myshopify.com') {
  console.log(`\n=== 検索テスト: "${query}" ===`);
  
  try {
    // 検索クエリをトリミングして小文字に変換
    const trimmedQuery = query.trim().toLowerCase();
    
    // スペースを削除したクエリも作成
    const noSpaceQuery = trimmedQuery.replace(/\s+/g, '');
    
    // 検索クエリのバリエーションを生成
    const queryVariations = new Set<string>();
    queryVariations.add(trimmedQuery); // 元のクエリ
    queryVariations.add(noSpaceQuery); // スペースなしクエリ
    
    // 名前の部分を抽出（日本語名対応）
    const nameParts = trimmedQuery.match(/[\p{L}\p{N}]+/gu) || [];
    
    // 名前の部分も検索バリエーションに追加
    nameParts.forEach(part => {
      if (part.length > 1) { // 1文字の部分は除外（ノイズになりやすい）
        queryVariations.add(part);
      }
    });
    
    // 姓名の順序を入れ替えたバリエーションも追加
    if (nameParts.length === 2) {
      // 「佐藤 花子」→「花子 佐藤」のようなバリエーションを追加
      queryVariations.add(`${nameParts[1]} ${nameParts[0]}`);
      queryVariations.add(`${nameParts[1]}${nameParts[0]}`); // スペースなし
    }
    
    console.log('検索バリエーション:', Array.from(queryVariations));
    
    // 検索条件を構築
    const searchConditions: any[] = [];
    
    // 各バリエーションで顧客名、メール、IDを検索
    queryVariations.forEach(q => {
      if (q.length > 1) { // 短すぎるクエリは除外
        searchConditions.push(
          { customerName: { contains: q, mode: 'insensitive' } },
          { customerEmail: { contains: q, mode: 'insensitive' } }
        );
      }
    });
    
    // 元のクエリと完全一致する場合の条件も追加
    searchConditions.push(
      { customerName: { equals: trimmedQuery, mode: 'insensitive' } },
      { customerName: { equals: noSpaceQuery, mode: 'insensitive' } },
      { customerId: { contains: trimmedQuery, mode: 'insensitive' } },
      { bookingId: { contains: trimmedQuery, mode: 'insensitive' } }
    );
    
    // 名前の部分一致検索（AND条件）
    if (nameParts.length > 1) {
      const namePartsCondition = nameParts
        .filter(part => part.length > 1) // 1文字の部分は除外
        .map(part => ({
          customerName: { contains: part, mode: 'insensitive' }
        }));
      
      if (namePartsCondition.length > 0) {
        searchConditions.push({
          AND: namePartsCondition
        });
      }
    }
    
    // 商品検索条件
    const productSearchCondition = {
      product: {
        OR: [
          { title: { contains: trimmedQuery, mode: 'insensitive' } },
          { sku: { contains: trimmedQuery, mode: 'insensitive' } },
          { description: { contains: trimmedQuery, mode: 'insensitive' } },
          {
            basicInfo: {
              path: ['productCode'],
              string_contains: trimmedQuery,
            }
          }
        ]
      }
    };
    
    // 最終的な検索条件を設定
    const whereCondition: any = {
      shop,
      OR: [
        ...searchConditions,
        productSearchCondition
      ]
    };
    
    // 検索実行
    const bookings = await prisma.booking.findMany({
      where: whereCondition,
      include: {
        product: {
          select: {
            title: true,
            sku: true,
          },
        },
      },
      take: 10, // 最大10件取得
    });
    
    console.log(`検索結果: ${bookings.length}件の予約が見つかりました`);
    
    // 検索結果を表示
    if (bookings.length > 0) {
      bookings.forEach((booking, index) => {
        console.log(`\n[${index + 1}] 予約情報:`);
        console.log(`ID: ${booking.id}`);
        console.log(`予約ID: ${booking.bookingId || 'なし'}`);
        console.log(`顧客ID: ${booking.customerId || 'なし'}`);
        console.log(`顧客名: ${booking.customerName || 'なし'}`);
        console.log(`顧客メール: ${booking.customerEmail || 'なし'}`);
        console.log(`商品: ${booking.product?.title || 'なし'} (${booking.product?.sku || 'なし'})`);
        console.log(`ステータス: ${booking.status}`);
      });
    }
    
    // アプリケーション側でのフィルタリングをシミュレート
    if (noSpaceQuery !== trimmedQuery && noSpaceQuery.length > 2) {
      console.log(`\nアプリケーション側フィルタリングのシミュレーション: "${noSpaceQuery}"`);
      
      const filteredBookings = bookings.filter(booking => {
        // 顧客名からスペースを削除して比較
        const customerNameNoSpace = (booking.customerName || '').replace(/\s+/g, '').toLowerCase();
        
        // スペースなしクエリが顧客名に含まれているか確認
        return customerNameNoSpace.includes(noSpaceQuery);
      });
      
      console.log(`追加フィルタリング結果: ${filteredBookings.length}件`);
      
      if (filteredBookings.length > 0 && filteredBookings.length !== bookings.length) {
        console.log('追加フィルタリングで見つかった予約:');
        filteredBookings.forEach((booking, index) => {
          console.log(`- ${booking.customerName} (ID: ${booking.id})`);
        });
      }
    }
    
    return bookings;
  } catch (error) {
    console.error('検索エラー:', error);
    return [];
  }
}

// メイン関数
async function main() {
  console.log('予約検索機能テストを開始します...');
  
  // テストケース
  const testQueries = [
    '佐藤 花子',
    '佐藤花子',
    '花子 佐藤',
    '花子佐藤',
    '佐藤',
    '花子',
    '8418608414888', // 顧客ID
    '<EMAIL>', // メールアドレス
    '鈴木 一郎',
    '鈴木一郎',
    '<EMAIL>'
  ];
  
  // 各テストケースを実行
  for (const query of testQueries) {
    await testSearch(query);
  }
  
  console.log('\n予約検索機能テストが完了しました');
}

// スクリプトを実行
main()
  .then(async () => {
    await prisma.$disconnect();
    process.exit(0);
  })
  .catch(async (error) => {
    console.error('予期しないエラーが発生しました:', error);
    await prisma.$disconnect();
    process.exit(1);
  });
