import { ActionFunctionArgs, json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { logger } from "../utils/logger";
import { BookingService } from "../services/booking.service";

/**
 * 注文キャンセルWebhook
 * POST: Shopifyからの注文キャンセル通知を処理
 * 
 * 機能:
 * 1. 関連する予約をキャンセル状態に更新
 * 2. 在庫を復元
 * 3. 支払い状況を更新
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  const webhookId = `webhook-orders-cancelled-${Date.now()}`;
  
  try {
    logger.info("注文キャンセルWebhook受信開始", { webhookId });

    // Webhookの検証
    const { session } = await authenticate.webhook(request);
    const shop = session.shop;

    // リクエストボディを取得
    const payload = await request.json();
    logger.info("注文キャンセルWebhook受信", { 
      webhookId, 
      orderId: payload.id,
      orderName: payload.name,
      shop,
      cancelledAt: payload.cancelled_at,
      cancelReason: payload.cancel_reason,
      financialStatus: payload.financial_status
    });

    // 注文IDを取得
    const orderId = payload.id.toString();
    if (!orderId) {
      logger.error("注文IDが見つかりません", { webhookId });
      return json({ error: "注文IDが見つかりません" }, { status: 400 });
    }

    // BookingServiceを初期化
    const bookingService = new BookingService();

    // 関連する予約を検索
    const relatedBookings = await findRelatedBookings(orderId, shop, webhookId);

    if (relatedBookings.length === 0) {
      logger.warn("関連する予約が見つかりません", { 
        webhookId, 
        orderId 
      });
      // 予約がない場合でも注文データは更新
      await updateOrderCancelStatus(payload, shop, webhookId);
      return json({ success: true, message: "予約なし - 注文のみ更新" });
    }

    // 各予約をキャンセル状態に更新
    for (const booking of relatedBookings) {
      await processBookingCancellation(booking, payload, bookingService, webhookId);
    }

    // 注文データを更新
    await updateOrderCancelStatus(payload, shop, webhookId);

    logger.info("注文キャンセルWebhook処理完了", { 
      webhookId, 
      orderId,
      bookingsCancelled: relatedBookings.length
    });

    return json({ 
      success: true, 
      message: "注文キャンセル処理完了",
      bookingsCancelled: relatedBookings.length
    });
  } catch (error) {
    logger.error("注文キャンセルWebhook処理エラー", { 
      webhookId, 
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    return json({ error: "注文キャンセル処理エラー" }, { status: 500 });
  }
};

/**
 * 関連する予約を検索する
 */
async function findRelatedBookings(orderId: string, shop: string, webhookId: string) {
  try {
    // 1. Shopify注文IDで検索
    let bookings = await prisma.booking.findMany({
      where: {
        shopifyOrderId: orderId,
        shop,
        status: {
          not: "CANCELLED" // 既にキャンセル済みの予約は除外
        }
      },
      include: {
        product: {
          select: {
            id: true,
            title: true,
            sku: true
          }
        }
      }
    });

    // 2. 古いorderId形式でも検索
    if (bookings.length === 0) {
      bookings = await prisma.booking.findMany({
        where: {
          orderId: orderId,
          shop,
          status: {
            not: "CANCELLED"
          }
        },
        include: {
          product: {
            select: {
              id: true,
              title: true,
              sku: true
            }
          }
        }
      });
    }

    logger.info("関連予約検索結果", {
      webhookId,
      orderId,
      bookingsFound: bookings.length,
      bookingIds: bookings.map(b => b.bookingId)
    });

    return bookings;
  } catch (error) {
    logger.error("関連予約検索エラー", {
      webhookId,
      orderId,
      error: error instanceof Error ? error.message : String(error)
    });
    return [];
  }
}

/**
 * 予約のキャンセル処理
 */
async function processBookingCancellation(
  booking: any, 
  payload: any, 
  bookingService: BookingService, 
  webhookId: string
) {
  try {
    const currentStatus = booking.status;
    const cancelReason = payload.cancel_reason || "注文キャンセル";

    // 予約をキャンセル状態に更新
    await bookingService.updateBookingStatus(booking.id, "CANCELLED", {
      orderId: payload.id.toString(),
      orderName: payload.name,
      paymentStatus: "REFUNDED",
      notes: `注文キャンセル: ${payload.name} - 理由: ${cancelReason}`
    });

    // 支払い情報を更新（返金処理）
    if (booking.paymentStatus === "COMPLETED" || booking.paymentStatus === "PARTIALLY_PAID") {
      await bookingService.updatePaymentStatus(booking.id, "REFUNDED", {
        notes: `注文キャンセルによる返金: ${payload.name}`,
        paymentDate: new Date()
      });
    }

    // 在庫復元処理
    await restoreInventoryForBooking(booking, webhookId);

    logger.info("予約キャンセル処理", {
      webhookId,
      bookingId: booking.bookingId,
      oldStatus: currentStatus,
      newStatus: "CANCELLED",
      orderId: payload.id,
      cancelReason
    });
  } catch (error) {
    logger.error("予約キャンセル処理エラー", {
      webhookId,
      bookingId: booking.bookingId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * 予約の在庫復元処理
 */
async function restoreInventoryForBooking(booking: any, webhookId: string) {
  try {
    // 在庫カレンダーから予約期間を削除
    const startDate = new Date(booking.startDate);
    const endDate = new Date(booking.endDate);
    
    // 予約期間の各日付について在庫を復元
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      await prisma.inventoryCalendar.deleteMany({
        where: {
          productId: booking.productId,
          date: {
            gte: new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate()),
            lt: new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() + 1)
          },
          bookingId: booking.bookingId
        }
      });
      
      currentDate.setDate(currentDate.getDate() + 1);
    }

    logger.info("在庫復元完了", {
      webhookId,
      bookingId: booking.bookingId,
      productId: booking.productId,
      startDate: booking.startDate,
      endDate: booking.endDate
    });
  } catch (error) {
    logger.error("在庫復元エラー", {
      webhookId,
      bookingId: booking.bookingId,
      error: error instanceof Error ? error.message : String(error)
    });
    // 在庫復元エラーは警告レベルとして処理を続行
  }
}

/**
 * 注文のキャンセル状況を更新
 */
async function updateOrderCancelStatus(payload: any, shop: string, webhookId: string) {
  try {
    await prisma.order.upsert({
      where: { 
        shop_shopifyId: {
          shop,
          shopifyId: payload.id.toString()
        }
      },
      update: {
        paymentStatus: payload.financial_status,
        syncStatus: "SYNCED",
        lastSyncedAt: new Date(),
        metadata: {
          ...((await prisma.order.findUnique({
            where: { 
              shop_shopifyId: {
                shop,
                shopifyId: payload.id.toString()
              }
            },
            select: { metadata: true }
          }))?.metadata || {}),
          cancelledAt: payload.cancelled_at,
          cancelReason: payload.cancel_reason,
          webhookId
        }
      },
      create: {
        shop,
        shopifyId: payload.id.toString(),
        orderNumber: payload.name,
        customerEmail: payload.email,
        customerName: `${payload.customer?.first_name || ''} ${payload.customer?.last_name || ''}`.trim(),
        totalAmount: parseFloat(payload.total_price),
        paymentStatus: payload.financial_status,
        syncStatus: "SYNCED",
        lastSyncedAt: new Date(),
        metadata: {
          fulfillmentStatus: payload.fulfillment_status,
          currency: payload.currency,
          cancelledAt: payload.cancelled_at,
          cancelReason: payload.cancel_reason,
          webhookId,
          createdAt: payload.created_at
        }
      }
    });

    logger.info("注文キャンセル状況更新", {
      webhookId,
      orderId: payload.id,
      cancelledAt: payload.cancelled_at,
      cancelReason: payload.cancel_reason
    });
  } catch (error) {
    logger.error("注文キャンセル状況更新エラー", {
      webhookId,
      orderId: payload.id,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}
