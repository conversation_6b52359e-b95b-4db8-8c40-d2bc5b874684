/**
 * GraphQL App用 在庫確認クエリ
 *
 * 画像の商品の在庫状況を確認し、NYロケーションに在庫を設定する方法を提供
 */

// 1. 商品の詳細情報と在庫状況を確認
const CHECK_PRODUCT_INVENTORY = `
query getProductInventory($id: ID!) {
  product(id: $id) {
    id
    title
    handle
    variants(first: 20) {
      edges {
        node {
          id
          title
          sku
          price
          inventoryItem {
            id
            tracked
            inventoryLevels(first: 10) {
              edges {
                node {
                  id
                  available
                  location {
                    id
                    name
                  }
                }
              }
            }
          }
        }
      }
    }
    metafields(first: 10, namespace: "rental") {
      edges {
        node {
          key
          value
          type
        }
      }
    }
  }
}
`;

// 変数（実際の商品IDに変更してください）
const CHECK_PRODUCT_VARIABLES = {
  "id": "gid://shopify/Product/YOUR_PRODUCT_ID_HERE"
};

// 2. ロケーション一覧を取得
const GET_LOCATIONS = `
query getLocations {
  locations(first: 10) {
    edges {
      node {
        id
        name
        address {
          formatted
        }
      }
    }
  }
}
`;

// 3. 在庫数量を設定（正しいmutation）
const SET_INVENTORY_QUANTITIES = `
mutation inventorySetQuantities($input: InventorySetQuantitiesInput!) {
  inventorySetQuantities(input: $input) {
    inventoryAdjustmentGroup {
      id
      changes {
        name
        delta
        quantityAfterChange
      }
    }
    userErrors {
      field
      message
    }
  }
}
`;

// 在庫設定用変数（実際のInventoryItemIDとLocationIDに変更してください）
const SET_INVENTORY_QUANTITIES_VARIABLES = {
  "input": {
    "name": "available",
    "reason": "correction",
    "referenceDocumentUri": "manual-inventory-setup",
    "ignoreCompareQuantity": true,
    "quantities": [
      {
        "inventoryItemId": "gid://shopify/InventoryItem/YOUR_INVENTORY_ITEM_ID_1",
        "locationId": "gid://shopify/Location/YOUR_LOCATION_ID_HERE",
        "quantity": 1
      },
      {
        "inventoryItemId": "gid://shopify/InventoryItem/YOUR_INVENTORY_ITEM_ID_2",
        "locationId": "gid://shopify/Location/YOUR_LOCATION_ID_HERE",
        "quantity": 1
      }
      // 必要に応じて他のバリエーションも追加
    ]
  }
};

// 4. 個別の在庫アイテムを設定
const SET_SINGLE_INVENTORY = `
mutation inventoryAdjustQuantity($input: InventoryAdjustQuantityInput!) {
  inventoryAdjustQuantity(input: $input) {
    inventoryLevel {
      id
      available
      item {
        id
        sku
      }
      location {
        id
        name
      }
    }
    userErrors {
      field
      message
    }
  }
}
`;

// 個別在庫設定用変数
const SET_SINGLE_INVENTORY_VARIABLES = {
  "input": {
    "inventoryItemId": "gid://shopify/InventoryItem/YOUR_INVENTORY_ITEM_ID",
    "locationId": "gid://shopify/Location/YOUR_LOCATION_ID",
    "availableDelta": 1
  }
};

// 5. 最近作成された商品を確認
const GET_RECENT_PRODUCTS = `
query getRecentProducts {
  products(first: 5, reverse: true) {
    edges {
      node {
        id
        title
        handle
        createdAt
        variants(first: 10) {
          edges {
            node {
              id
              title
              sku
              inventoryItem {
                id
                tracked
                inventoryLevels(first: 5) {
                  edges {
                    node {
                      id
                      available
                      location {
                        id
                        name
                      }
                    }
                  }
                }
              }
            }
          }
        }
        metafields(first: 5, namespace: "rental") {
          edges {
            node {
              key
              value
            }
          }
        }
      }
    }
  }
}
`;

/**
 * 使用手順:
 *
 * ステップ1: GET_LOCATIONS でロケーション一覧を確認
 * - NYロケーションのIDを取得
 *
 * ステップ2: GET_RECENT_PRODUCTS で最近作成された商品を確認
 * - 対象商品のIDとInventoryItemIDを取得
 *
 * ステップ3: CHECK_PRODUCT_INVENTORY で特定商品の在庫状況を詳細確認
 * - YOUR_PRODUCT_ID_HERE を実際の商品IDに変更
 *
 * ステップ4: SET_INVENTORY_QUANTITIES または SET_SINGLE_INVENTORY で在庫を設定
 * - YOUR_LOCATION_ID_HERE をNYロケーションのIDに変更
 * - YOUR_INVENTORY_ITEM_ID を実際のInventoryItemIDに変更
 *
 * 注意事項:
 * - write_inventory スコープが必要
 * - 在庫設定前に商品とバリエーションが存在している必要がある
 * - availableDelta は現在の在庫からの増減値（1 = 1個追加）
 */

console.log('GraphQL App用在庫確認・設定クエリが準備されました');
console.log('上記のクエリをShopify GraphQL Appで順番に実行してください');
