/**
 * 実際のSKUを使用したテスト予約作成スクリプト
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

dotenv.config();
const prisma = new PrismaClient();

const SHOP = 'ease-next-temp.myshopify.com';

// テスト用顧客データ
const TEST_CUSTOMERS = [
  {
    shopifyId: 'test-customer-1',
    email: '<EMAIL>',
    firstName: '太郎',
    lastName: '田中',
    phone: '090-1234-5678'
  },
  {
    shopifyId: 'test-customer-2', 
    email: '<EMAIL>',
    firstName: '花子',
    lastName: '佐藤',
    phone: '090-2345-6789'
  },
  {
    shopifyId: 'test-customer-3',
    email: '<EMAIL>',
    firstName: '一郎',
    lastName: '鈴木',
    phone: '090-3456-7890'
  }
];

/**
 * 顧客を作成
 */
async function createCustomer(customerData: any) {
  try {
    const existing = await prisma.customer.findFirst({
      where: {
        shop: SHOP,
        email: customerData.email
      }
    });

    if (existing) {
      console.log(`既存顧客: ${customerData.lastName} ${customerData.firstName}`);
      return existing;
    }

    const customer = await prisma.customer.create({
      data: {
        shop: SHOP,
        shopifyId: customerData.shopifyId,
        email: customerData.email,
        firstName: customerData.firstName,
        lastName: customerData.lastName,
        phone: customerData.phone
      }
    });

    console.log(`✅ 顧客作成: ${customerData.lastName} ${customerData.firstName}`);
    return customer;
  } catch (error) {
    console.error(`❌ 顧客作成エラー:`, error);
    return null;
  }
}

/**
 * テスト予約を作成
 */
async function createTestBookings() {
  console.log('\n=== テスト予約作成 ===');

  // 商品を取得
  const products = await prisma.product.findMany({
    where: { shop: SHOP },
    orderBy: { createdAt: 'asc' }
  });

  if (products.length === 0) {
    throw new Error('商品が見つかりません。先に商品を作成してください。');
  }

  // 顧客を作成
  const customers = [];
  for (const customerData of TEST_CUSTOMERS) {
    const customer = await createCustomer(customerData);
    if (customer) customers.push(customer);
  }

  const today = new Date();
  const bookings = [];

  // 1. 確定予約（3日後から5日間）
  const booking1 = await prisma.booking.create({
    data: {
      shop: SHOP,
      productId: products[0].id,
      customerId: customers[0].id,
      startDate: new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000),
      endDate: new Date(today.getTime() + 8 * 24 * 60 * 60 * 1000),
      status: 'confirmed',
      customerName: `${customers[0].lastName} ${customers[0].firstName}`,
      customerEmail: customers[0].email,
      customerPhone: customers[0].phone || '',
      totalAmount: '7500',
      depositAmount: '750',
      depositPaid: true,
      bookingId: `BK-${Date.now()}-001`,
      notes: '確定予約のテストデータ（5日間レンタル）',
      rentalDays: 5
    }
  });
  bookings.push(booking1);
  console.log(`✅ 確定予約作成: ${products[0].sku} - 5日間`);

  // 2. 仮予約（10日後から3日間）
  const booking2 = await prisma.booking.create({
    data: {
      shop: SHOP,
      productId: products[1].id,
      customerId: customers[1].id,
      startDate: new Date(today.getTime() + 10 * 24 * 60 * 60 * 1000),
      endDate: new Date(today.getTime() + 13 * 24 * 60 * 60 * 1000),
      status: 'provisional',
      customerName: `${customers[1].lastName} ${customers[1].firstName}`,
      customerEmail: customers[1].email,
      customerPhone: customers[1].phone || '',
      totalAmount: '4200',
      depositAmount: '420',
      depositPaid: true,
      bookingId: `BK-${Date.now()}-002`,
      notes: '仮予約のテストデータ（3日間レンタル）',
      rentalDays: 3
    }
  });
  bookings.push(booking2);
  console.log(`✅ 仮予約作成: ${products[1].sku} - 3日間`);

  // 3. 1日レンタル（明日）
  const booking3 = await prisma.booking.create({
    data: {
      shop: SHOP,
      productId: products[2].id,
      customerId: customers[2].id,
      startDate: new Date(today.getTime() + 1 * 24 * 60 * 60 * 1000),
      endDate: new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000),
      status: 'confirmed',
      customerName: `${customers[2].lastName} ${customers[2].firstName}`,
      customerEmail: customers[2].email,
      customerPhone: customers[2].phone || '',
      totalAmount: '2000',
      depositAmount: '200',
      depositPaid: true,
      bookingId: `BK-${Date.now()}-003`,
      notes: '1日レンタルのテストデータ',
      rentalDays: 1
    }
  });
  bookings.push(booking3);
  console.log(`✅ 1日レンタル作成: ${products[2].sku} - 1日間`);

  // 4. 長期レンタル（15日後から10日間）
  const booking4 = await prisma.booking.create({
    data: {
      shop: SHOP,
      productId: products[4].id,
      customerId: customers[0].id,
      startDate: new Date(today.getTime() + 15 * 24 * 60 * 60 * 1000),
      endDate: new Date(today.getTime() + 25 * 24 * 60 * 60 * 1000),
      status: 'confirmed',
      customerName: `${customers[0].lastName} ${customers[0].firstName}`,
      customerEmail: customers[0].email,
      customerPhone: customers[0].phone || '',
      totalAmount: '2640',
      depositAmount: '264',
      depositPaid: true,
      bookingId: `BK-${Date.now()}-004`,
      notes: '長期レンタル（10日間）のテストデータ',
      rentalDays: 10
    }
  });
  bookings.push(booking4);
  console.log(`✅ 長期レンタル作成: ${products[4].sku} - 10日間`);

  return bookings;
}

/**
 * メイン処理
 */
async function main() {
  console.log('🏗️ === 実際のSKUを使用したテスト予約作成 ===');
  console.log(`対象ショップ: ${SHOP}`);
  console.log(`実行日時: ${new Date().toLocaleString('ja-JP')}`);

  try {
    await prisma.$connect();
    console.log('✅ データベース接続成功');

    // 既存の予約を削除
    const deletedBookings = await prisma.booking.deleteMany({
      where: { shop: SHOP }
    });
    console.log(`🗑️ 既存予約削除: ${deletedBookings.count}件`);

    // 既存の顧客を削除
    const deletedCustomers = await prisma.customer.deleteMany({
      where: { shop: SHOP }
    });
    console.log(`🗑️ 既存顧客削除: ${deletedCustomers.count}件`);

    // テスト予約を作成
    const bookings = await createTestBookings();

    console.log('\n🎉 === テスト予約作成完了 ===');
    console.log(`✅ 作成した予約: ${bookings.length}件`);

    console.log('\n📅 作成された予約:');
    for (let i = 0; i < bookings.length; i++) {
      const booking = bookings[i];
      const product = await prisma.product.findUnique({
        where: { id: booking.productId }
      });
      console.log(`  ${i + 1}. ${product?.sku}: ${booking.status} - ${booking.rentalDays}日間 (${booking.customerName})`);
    }

    console.log('\n🔗 次のステップ:');
    console.log('1. /app/bookings/aggregate でカテゴリフィルタリングをテスト');
    console.log('2. 予約作成・変更・キャンセルの動作確認');
    console.log('3. 価格計算の正確性確認');
    console.log('4. 在庫管理の動作確認');

  } catch (error) {
    console.error('❌ エラー:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
