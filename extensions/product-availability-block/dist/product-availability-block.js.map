{"version": 3, "sources": ["../../../../node_modules/@remote-ui/rpc/build/esm/memory.mjs", "../../../../node_modules/@remote-ui/core/build/esm/utilities.mjs", "../../../../node_modules/@remote-ui/core/build/esm/root.mjs", "../../../../node_modules/@shopify/ui-extensions/build/esm/utilities/registration.mjs", "../../../../node_modules/@shopify/ui-extensions/build/esm/surfaces/checkout/extension.mjs", "../../../../extensions/product-availability-block/src/BlockExtension.jsx"], "sourceRoot": "/Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp/extensions/product-availability-block/src", "sourcesContent": ["import { RETAINED_BY, RETAIN_METHOD, RELEASE_METHOD } from './types.mjs';\nexport { RELEASE_METHOD, RETAINED_BY, RETAIN_METHOD } from './types.mjs';\n\nclass StackFrame {\n  constructor() {\n    this.memoryManaged = new Set();\n  }\n\n  add(memoryManageable) {\n    this.memoryManaged.add(memoryManageable);\n    memoryManageable[RETAINED_BY].add(this);\n    memoryManageable[RETAIN_METHOD]();\n  }\n\n  release() {\n    for (const memoryManaged of this.memoryManaged) {\n      memoryManaged[RETAINED_BY].delete(this);\n      memoryManaged[RELEASE_METHOD]();\n    }\n\n    this.memoryManaged.clear();\n  }\n\n}\nfunction isMemoryManageable(value) {\n  return Boolean(value && value[RETAIN_METHOD] && value[RELEASE_METHOD]);\n}\nfunction retain(value, {\n  deep = true\n} = {}) {\n  return retainInternal(value, deep, new Map());\n}\n\nfunction retainInternal(value, deep, seen) {\n  const seenValue = seen.get(value);\n  if (seenValue != null) return seenValue;\n  const canRetain = isMemoryManageable(value);\n\n  if (canRetain) {\n    value[RETAIN_METHOD]();\n  }\n\n  seen.set(value, canRetain);\n\n  if (deep) {\n    if (Array.isArray(value)) {\n      const nestedCanRetain = value.reduce((canRetain, item) => retainInternal(item, deep, seen) || canRetain, canRetain);\n      seen.set(value, nestedCanRetain);\n      return nestedCanRetain;\n    }\n\n    if (isBasicObject(value)) {\n      const nestedCanRetain = Object.keys(value).reduce((canRetain, key) => retainInternal(value[key], deep, seen) || canRetain, canRetain);\n      seen.set(value, nestedCanRetain);\n      return nestedCanRetain;\n    }\n  }\n\n  seen.set(value, canRetain);\n  return canRetain;\n}\n\nfunction release(value, {\n  deep = true\n} = {}) {\n  return releaseInternal(value, deep, new Map());\n}\nfunction releaseInternal(value, deep, seen) {\n  const seenValue = seen.get(value);\n  if (seenValue != null) return seenValue;\n  const canRelease = isMemoryManageable(value);\n\n  if (canRelease) {\n    value[RELEASE_METHOD]();\n  }\n\n  seen.set(value, canRelease);\n\n  if (deep) {\n    if (Array.isArray(value)) {\n      const nestedCanRelease = value.reduce((canRelease, item) => releaseInternal(item, deep, seen) || canRelease, canRelease);\n      seen.set(value, nestedCanRelease);\n      return nestedCanRelease;\n    }\n\n    if (isBasicObject(value)) {\n      const nestedCanRelease = Object.keys(value).reduce((canRelease, key) => releaseInternal(value[key], deep, seen) || canRelease, canRelease);\n      seen.set(value, nestedCanRelease);\n      return nestedCanRelease;\n    }\n  }\n\n  return canRelease;\n}\nfunction isBasicObject(value) {\n  if (value == null || typeof value !== 'object') return false;\n  const prototype = Object.getPrototypeOf(value);\n  return prototype == null || prototype === Object.prototype;\n}\n\nexport { StackFrame, isBasicObject, isMemoryManageable, release, releaseInternal, retain };\n", "import { KIND_COMPONENT, KIND_TEXT, KIND_FRAGMENT } from './types.mjs';\n\nfunction isRemoteComponent(child) {\n  return child != null && child.kind === KIND_COMPONENT;\n}\nfunction isRemoteText(child) {\n  return child != null && child.kind === KIND_TEXT;\n}\nfunction isRemoteFragment(object) {\n  return object != null && object.kind === KIND_FRAGMENT;\n}\n\nexport { isRemoteComponent, isRemoteFragment, isRemoteText };\n", "import { isBasicObject } from '@remote-ui/rpc';\nimport { KIND_ROOT, ACTION_MOUNT, ACTION_INSERT_CHILD, KIND_TEXT, ACTION_REMOVE_CHILD, KIND_COMPONENT, KIND_FRAGMENT, ACTION_UPDATE_PROPS, ACTION_UPDATE_TEXT } from './types.mjs';\nimport { isRemoteFragment } from './utilities.mjs';\n\nconst FUNCTION_CURRENT_IMPLEMENTATION_KEY = '__current';\nconst EMPTY_OBJECT = {};\nconst EMPTY_ARRAY = [];\nfunction createRemoteRoot(channel, {\n  strict = true,\n  components\n} = {}) {\n  let currentId = 0;\n  const rootInternals = {\n    strict,\n    mounted: false,\n    channel,\n    children: EMPTY_ARRAY,\n    nodes: new WeakSet(),\n    parents: new WeakMap(),\n    tops: new WeakMap(),\n    components: new WeakMap(),\n    fragments: new WeakMap()\n  };\n  if (strict) Object.freeze(components);\n  const remoteRoot = {\n    kind: KIND_ROOT,\n    options: strict ? Object.freeze({\n      strict,\n      components\n    }) : {\n      strict,\n      components\n    },\n\n    get children() {\n      return rootInternals.children;\n    },\n\n    createComponent(type, ...rest) {\n      if (components && components.indexOf(type) < 0) {\n        throw new Error(`Unsupported component: ${type}`);\n      }\n\n      const [initialProps, initialChildren, ...moreChildren] = rest;\n      const normalizedInitialProps = initialProps !== null && initialProps !== void 0 ? initialProps : {};\n      const normalizedInitialChildren = [];\n      const normalizedInternalProps = {};\n\n      if (initialProps) {\n        for (const key of Object.keys(initialProps)) {\n          // \"children\" as a prop can be extremely confusing with the \"children\" of\n          // a component. In React, a \"child\" can be anything, but once it reaches\n          // a host environment (like this remote `Root`), we want \"children\" to have\n          // only one meaning: the actual, resolved children components and text.\n          //\n          // To enforce this, we delete any prop named \"children\". We don’t take a copy\n          // of the props for performance, so a user calling this function must do so\n          // with an object that can handle being mutated.\n          if (key === 'children') continue;\n          normalizedInternalProps[key] = makeValueHotSwappable(serializeProp(initialProps[key]));\n        }\n      }\n\n      if (initialChildren) {\n        if (Array.isArray(initialChildren)) {\n          for (const child of initialChildren) {\n            normalizedInitialChildren.push(normalizeChild(child, remoteRoot));\n          }\n        } else {\n          normalizedInitialChildren.push(normalizeChild(initialChildren, remoteRoot)); // The complex tuple type of `rest` makes it so `moreChildren` is\n          // incorrectly inferred as potentially being the props of the component,\n          // lazy casting since we know it will be an array of child elements\n          // (or empty).\n\n          for (const child of moreChildren) {\n            normalizedInitialChildren.push(normalizeChild(child, remoteRoot));\n          }\n        }\n      }\n\n      const id = `${currentId++}`;\n      const internals = {\n        externalProps: strict ? Object.freeze(normalizedInitialProps) : normalizedInitialProps,\n        internalProps: normalizedInternalProps,\n        children: strict ? Object.freeze(normalizedInitialChildren) : normalizedInitialChildren\n      };\n      const component = {\n        kind: KIND_COMPONENT,\n\n        get children() {\n          return internals.children;\n        },\n\n        get props() {\n          return internals.externalProps;\n        },\n\n        get remoteProps() {\n          return internals.internalProps;\n        },\n\n        remove: () => remove(component),\n        updateProps: newProps => updateProps(component, newProps, internals, rootInternals),\n        append: (...children) => append(component, children.map(child => normalizeChild(child, remoteRoot)), internals, rootInternals),\n        appendChild: child => appendChild(component, normalizeChild(child, remoteRoot), internals, rootInternals),\n        removeChild: child => removeChild(component, child, internals, rootInternals),\n        replaceChildren: (...children) => replaceChildren(component, children.map(child => normalizeChild(child, remoteRoot)), internals, rootInternals),\n        insertBefore: (child, before) => insertBefore(component, normalizeChild(child, remoteRoot), before, internals, rootInternals),\n        insertChildBefore: (child, before) => insertBefore(component, normalizeChild(child, remoteRoot), before, internals, rootInternals),\n        // Just satisfying the type definition, since we need to write\n        // some properties manually, which we do below. If we just `as any`\n        // the whole object, we lose the implicit argument types for the\n        // methods above.\n        ...EMPTY_OBJECT\n      };\n      rootInternals.components.set(component, internals);\n      Object.defineProperty(component, 'type', {\n        value: type,\n        configurable: false,\n        writable: false,\n        enumerable: true\n      });\n      makePartOfTree(component, rootInternals);\n      makeRemote(component, id, remoteRoot);\n\n      for (const child of internals.children) {\n        moveNodeToContainer(component, child, rootInternals);\n      }\n\n      return component;\n    },\n\n    createText(content = '') {\n      const id = `${currentId++}`;\n      const internals = {\n        text: content\n      };\n\n      const update = newText => updateText(text, newText, internals, rootInternals);\n\n      const text = {\n        kind: KIND_TEXT,\n\n        get text() {\n          return internals.text;\n        },\n\n        update,\n        updateText: update,\n        remove: () => remove(text),\n        // Just satisfying the type definition, since we need to write\n        // some properties manually.\n        ...EMPTY_OBJECT\n      };\n      makePartOfTree(text, rootInternals);\n      makeRemote(text, id, remoteRoot);\n      return text;\n    },\n\n    createFragment() {\n      const id = `${currentId++}`;\n      const internals = {\n        children: strict ? Object.freeze([]) : []\n      };\n      const fragment = {\n        kind: KIND_FRAGMENT,\n\n        get children() {\n          return internals.children;\n        },\n\n        append: (...children) => append(fragment, children.map(child => normalizeChild(child, remoteRoot)), internals, rootInternals),\n        appendChild: child => appendChild(fragment, normalizeChild(child, remoteRoot), internals, rootInternals),\n        removeChild: child => removeChild(fragment, child, internals, rootInternals),\n        replaceChildren: (...children) => replaceChildren(fragment, children.map(child => normalizeChild(child, remoteRoot)), internals, rootInternals),\n        insertBefore: (child, before) => insertBefore(fragment, normalizeChild(child, remoteRoot), before, internals, rootInternals),\n        insertChildBefore: (child, before) => insertBefore(fragment, normalizeChild(child, remoteRoot), before, internals, rootInternals),\n        // Just satisfying the type definition, since we need to write\n        // some properties manually.\n        ...EMPTY_OBJECT\n      };\n      rootInternals.fragments.set(fragment, internals);\n      makePartOfTree(fragment, rootInternals);\n      makeRemote(fragment, id, remoteRoot);\n      return fragment;\n    },\n\n    append: (...children) => append(remoteRoot, children.map(child => normalizeChild(child, remoteRoot)), rootInternals, rootInternals),\n    appendChild: child => appendChild(remoteRoot, normalizeChild(child, remoteRoot), rootInternals, rootInternals),\n    replaceChildren: (...children) => replaceChildren(remoteRoot, children.map(child => normalizeChild(child, remoteRoot)), rootInternals, rootInternals),\n    removeChild: child => removeChild(remoteRoot, child, rootInternals, rootInternals),\n    insertBefore: (child, before) => insertBefore(remoteRoot, normalizeChild(child, remoteRoot), before, rootInternals, rootInternals),\n    insertChildBefore: (child, before) => insertBefore(remoteRoot, normalizeChild(child, remoteRoot), before, rootInternals, rootInternals),\n\n    mount() {\n      if (rootInternals.mounted) return Promise.resolve();\n      rootInternals.mounted = true;\n      return Promise.resolve(channel(ACTION_MOUNT, rootInternals.children.map(serializeChild)));\n    }\n\n  };\n  return remoteRoot;\n}\n\nfunction connected(element, {\n  tops\n}) {\n  var _tops$get;\n\n  return ((_tops$get = tops.get(element)) === null || _tops$get === void 0 ? void 0 : _tops$get.kind) === KIND_ROOT;\n}\n\nfunction allDescendants(element, withEach) {\n  const recurse = element => {\n    if ('children' in element) {\n      for (const child of element.children) {\n        withEach(child);\n        recurse(child);\n      }\n    }\n  };\n\n  recurse(element);\n}\n\nfunction perform(element, rootInternals, {\n  remote,\n  local\n}) {\n  const {\n    mounted,\n    channel\n  } = rootInternals;\n\n  if (mounted && (element.kind === KIND_ROOT || connected(element, rootInternals))) {\n    // should only create context once async queue is cleared\n    remote(channel); // technically, we should be waiting for the remote update to apply,\n    // then apply it locally. The implementation below is too naive because\n    // it allows local updates to get out of sync with remote ones.\n    // if (remoteResult == null || !('then' in remoteResult)) {\n    //   local();\n    //   return;\n    // } else {\n    //   return remoteResult.then(() => {\n    //     local();\n    //   });\n    // }\n  }\n\n  local();\n}\n\nfunction updateText(text, newText, internals, rootInternals) {\n  return perform(text, rootInternals, {\n    remote: channel => channel(ACTION_UPDATE_TEXT, text.id, newText),\n    local: () => {\n      internals.text = newText;\n    }\n  });\n}\n\nconst IGNORE = Symbol('ignore');\n\nfunction updateProps(component, newProps, internals, rootInternals) {\n  const {\n    strict\n  } = rootInternals;\n  const {\n    internalProps: currentProps,\n    externalProps: currentExternalProps\n  } = internals;\n  const normalizedNewProps = {};\n  const hotSwapFunctions = [];\n  let hasRemoteChange = false;\n\n  for (const key of Object.keys(newProps)) {\n    // See notes above for why we treat `children` as a reserved prop.\n    if (key === 'children') continue;\n    const currentExternalValue = currentExternalProps[key];\n    const newExternalValue = newProps[key];\n    const currentValue = currentProps[key];\n    const newValue = serializeProp(newExternalValue); // Bail out if we have equal, primitive types\n\n    if (currentValue === newValue && (newValue == null || typeof newValue !== 'object')) {\n      continue;\n    }\n\n    const [value, hotSwaps] = tryHotSwappingValues(currentValue, newValue);\n\n    if (hotSwaps) {\n      hotSwapFunctions.push(...hotSwaps);\n    }\n\n    if (value === IGNORE) continue;\n    hasRemoteChange = true;\n    normalizedNewProps[key] = value;\n\n    if (isRemoteFragment(currentExternalValue)) {\n      removeNodeFromContainer(currentExternalValue, rootInternals);\n    }\n\n    if (isRemoteFragment(newExternalValue)) {\n      moveNodeToContainer(component, newExternalValue, rootInternals);\n    }\n  }\n\n  return perform(component, rootInternals, {\n    remote: channel => {\n      if (hasRemoteChange) {\n        channel(ACTION_UPDATE_PROPS, component.id, normalizedNewProps);\n      }\n    },\n    local: () => {\n      const mergedExternalProps = { ...currentExternalProps,\n        ...newProps\n      };\n      internals.externalProps = strict ? Object.freeze(mergedExternalProps) : mergedExternalProps;\n      internals.internalProps = { ...internals.internalProps,\n        ...normalizedNewProps\n      };\n\n      for (const [hotSwappable, newValue] of hotSwapFunctions) {\n        hotSwappable[FUNCTION_CURRENT_IMPLEMENTATION_KEY] = newValue;\n      }\n    }\n  });\n} // Imagine the following remote-ui components we might render in a remote context:\n//\n// const root = createRemoteRoot();\n// const {value, onChange, onPress} = getPropsForValue();\n//\n// const textField = root.createComponent('TextField', {value, onChange});\n// const button = root.createComponent('Button', {onPress});\n//\n// root.append(textField);\n// root.append(button);\n//\n// function getPropsForValue(value = '') {\n//   return {\n//     value,\n//     onChange: () => {\n//       const {value, onChange, onPress} = getPropsForValue();\n//       textField.updateProps({value, onChange});\n//       button.updateProps({onPress});\n//     },\n//     onPress: () => console.log(value),\n//   };\n// }\n//\n//\n// In this example, assume that the `TextField` `onChange` prop is run on blur.\n// If this were running on the host, the following steps would happen if you pressed\n// on the button:\n//\n// 1. The text field blurs, and so calls `onChange()` with its current value, which\n//    then calls `setValue()` with the updated value.\n// 2. We synchronously update the `value`, `onChange`, and `onPress` props to point at\n//    the most current `value`.\n// 3. Handling blur is finished, so the browser now handles the click by calling the\n//    (newly-updated) `Button` `onPress()`, which logs out the new value.\n//\n// Because remote-ui reproduces a UI tree asynchronously from the remote context, the\n// steps above run in a different order:\n//\n// 1. The text field blurs, and so calls `onChange()` with its current value.\n// 2. Handling blur is finished **from the perspective of the main thread**, so the\n//    browser now handles the click by calling the (original) `Button` `onPress()`, which\n//    logs out the **initial** value.\n// 3. In the remote context, we receive the `onChange()` call, which calls updates the props\n//    on the `Button` and `TextField` to be based on the new `value`, but by now it’s\n//    already too late for `onPress` — the old version has already been called!\n//\n// As you can see, the timing issue introduced by the asynchronous nature of remote-ui\n// can cause “old props” to be called from the main thread. This example may seem like\n// an unusual pattern, and it is if you are using `@remote-ui/core` directly; you’d generally\n// keep a mutable reference to the state, instead of closing over the state with new props.\n// However, abstractions on top of `@remote-ui/core`, like the React reconciler in\n// `@remote-ui/react`, work almost entirely by closing over state, so this issue is\n// much more common with those declarative libraries.\n//\n// To protect against this, we handle function props a bit differently. When we have a\n// function prop, we replace it with a new function that calls the original. However,\n// we make the original mutable, by making it a property on the function itself. When\n// this function subsequently updates, we don’t send the update to the main thread (as\n// we just saw, this can often be \"too late\" to be of any use). Instead, we swap out\n// the mutable reference to the current implementation of the function prop, which can\n// be done synchronously. In the example above, this would all happen synchronously in\n// the remote context; in our handling of `TextField onChange()`, we update `Button onPress()`,\n// and swap out the implementations. Now, when the main thread attempts to call `Button onPress()`,\n// it instead calls our wrapper around the function, which can refer to, and call, the\n// most recently-applied implementation, instead of directly calling the old implementation.\n\n\nfunction tryHotSwappingValues(currentValue, newValue, seen = new Set()) {\n  if (seen.has(currentValue)) {\n    return [IGNORE];\n  }\n\n  if (typeof currentValue === 'function' && FUNCTION_CURRENT_IMPLEMENTATION_KEY in currentValue) {\n    seen.add(currentValue);\n    const result = [typeof newValue === 'function' ? IGNORE : makeValueHotSwappable(newValue), [[currentValue, newValue]]];\n    return result;\n  }\n\n  if (Array.isArray(currentValue)) {\n    seen.add(currentValue);\n    const result = tryHotSwappingArrayValues(currentValue, newValue, seen);\n    return result;\n  }\n\n  if (isBasicObject(currentValue) && !isRemoteFragment(currentValue)) {\n    seen.add(currentValue);\n    const result = tryHotSwappingObjectValues(currentValue, newValue, seen);\n    return result;\n  }\n\n  const result = [currentValue === newValue ? IGNORE : newValue];\n  return result;\n}\n\nfunction makeValueHotSwappable(value, seen = new Map()) {\n  const seenValue = seen.get(value);\n  if (seenValue) return seenValue;\n\n  if (isRemoteFragment(value)) {\n    seen.set(value, value);\n    return value;\n  }\n\n  if (Array.isArray(value)) {\n    const result = [];\n    seen.set(value, result);\n\n    for (const nested of value) {\n      result.push(makeValueHotSwappable(nested, seen));\n    }\n\n    return result;\n  }\n\n  if (isBasicObject(value)) {\n    const result = {};\n    seen.set(value, result);\n\n    for (const key of Object.keys(value)) {\n      result[key] = makeValueHotSwappable(value[key], seen);\n    }\n\n    return result;\n  }\n\n  if (typeof value === 'function') {\n    const wrappedFunction = (...args) => {\n      return wrappedFunction[FUNCTION_CURRENT_IMPLEMENTATION_KEY](...args);\n    };\n\n    Object.defineProperty(wrappedFunction, FUNCTION_CURRENT_IMPLEMENTATION_KEY, {\n      enumerable: false,\n      configurable: false,\n      writable: true,\n      value\n    });\n    seen.set(value, wrappedFunction);\n    return wrappedFunction;\n  }\n\n  return value;\n}\n\nfunction collectNestedHotSwappableValues(value, seen = new Set()) {\n  if (seen.has(value)) return undefined;\n  seen.add(value);\n\n  if (Array.isArray(value)) {\n    return value.reduce((all, element) => {\n      const nested = collectNestedHotSwappableValues(element, seen);\n      return nested ? [...all, ...nested] : all;\n    }, []);\n  }\n\n  if (isBasicObject(value)) {\n    return Object.keys(value).reduce((all, key) => {\n      const nested = collectNestedHotSwappableValues(value[key], seen);\n      return nested ? [...all, ...nested] : all;\n    }, []);\n  }\n\n  if (typeof value === 'function') {\n    return FUNCTION_CURRENT_IMPLEMENTATION_KEY in value ? [value] : undefined;\n  }\n\n  return undefined;\n}\n\nfunction remove(child) {\n  var _child$parent;\n\n  (_child$parent = child.parent) === null || _child$parent === void 0 ? void 0 : _child$parent.removeChild(child);\n}\n\nfunction append(container, children, internals, rootInternals) {\n  for (const child of children) {\n    appendChild(container, child, internals, rootInternals);\n  }\n}\n\nfunction appendChild(container, child, internals, rootInternals) {\n  var _currentParent$childr;\n\n  const {\n    nodes,\n    strict\n  } = rootInternals;\n\n  if (!nodes.has(child)) {\n    throw new Error(`Cannot append a node that was not created by this remote root`);\n  }\n\n  const currentParent = child.parent;\n  const existingIndex = (_currentParent$childr = currentParent === null || currentParent === void 0 ? void 0 : currentParent.children.indexOf(child)) !== null && _currentParent$childr !== void 0 ? _currentParent$childr : -1;\n  return perform(container, rootInternals, {\n    remote: channel => {\n      channel(ACTION_INSERT_CHILD, container.id, existingIndex < 0 ? container.children.length : container.children.length - 1, serializeChild(child), currentParent ? currentParent.id : false);\n    },\n    local: () => {\n      moveNodeToContainer(container, child, rootInternals);\n      let newChildren;\n\n      if (currentParent) {\n        const currentInternals = getCurrentInternals(currentParent, rootInternals);\n        const currentChildren = [...currentInternals.children];\n        currentChildren.splice(existingIndex, 1);\n\n        if (currentParent === container) {\n          newChildren = currentChildren;\n        } else {\n          currentInternals.children = strict ? Object.freeze(currentChildren) : currentChildren;\n          newChildren = [...internals.children];\n        }\n      } else {\n        newChildren = [...internals.children];\n      }\n\n      newChildren.push(child);\n      internals.children = strict ? Object.freeze(newChildren) : newChildren;\n    }\n  });\n}\n\nfunction replaceChildren(container, children, internals, rootInternals) {\n  for (const child of container.children) {\n    removeChild(container, child, internals, rootInternals);\n  }\n\n  append(container, children, internals, rootInternals);\n} // there is a problem with this, because when multiple children\n// are removed, there is no guarantee the messages will arrive in the\n// order we need them to on the host side (it depends how React\n// calls our reconciler). If it calls with, for example, the removal of\n// the second last item, then the removal of the last item, it will fail\n// because the indexes moved around.\n//\n// Might need to send the removed child ID, or find out if we\n// can collect removals into a single update.\n\n\nfunction removeChild(container, child, internals, rootInternals) {\n  const {\n    strict\n  } = rootInternals;\n  const childIndex = container.children.indexOf(child);\n\n  if (childIndex === -1) {\n    return undefined;\n  }\n\n  return perform(container, rootInternals, {\n    remote: channel => channel(ACTION_REMOVE_CHILD, container.id, childIndex),\n    local: () => {\n      removeNodeFromContainer(child, rootInternals);\n      const newChildren = [...internals.children];\n      newChildren.splice(newChildren.indexOf(child), 1);\n      internals.children = strict ? Object.freeze(newChildren) : newChildren;\n    }\n  });\n}\n\nfunction insertBefore(container, child, before, internals, rootInternals) {\n  var _currentParent$childr2;\n\n  const {\n    strict,\n    nodes\n  } = rootInternals;\n\n  if (!nodes.has(child)) {\n    throw new Error(`Cannot insert a node that was not created by this remote root`);\n  }\n\n  const currentParent = child.parent;\n  const existingIndex = (_currentParent$childr2 = currentParent === null || currentParent === void 0 ? void 0 : currentParent.children.indexOf(child)) !== null && _currentParent$childr2 !== void 0 ? _currentParent$childr2 : -1;\n  return perform(container, rootInternals, {\n    remote: channel => {\n      const beforeIndex = before == null ? container.children.length - 1 : container.children.indexOf(before);\n      channel(ACTION_INSERT_CHILD, container.id, beforeIndex < existingIndex || existingIndex < 0 ? beforeIndex : beforeIndex - 1, serializeChild(child), currentParent ? currentParent.id : false);\n    },\n    local: () => {\n      moveNodeToContainer(container, child, rootInternals);\n      let newChildren;\n\n      if (currentParent) {\n        const currentInternals = getCurrentInternals(currentParent, rootInternals);\n        const currentChildren = [...currentInternals.children];\n        currentChildren.splice(existingIndex, 1);\n\n        if (currentParent === container) {\n          newChildren = currentChildren;\n        } else {\n          currentInternals.children = strict ? Object.freeze(currentChildren) : currentChildren;\n          newChildren = [...internals.children];\n        }\n      } else {\n        newChildren = [...internals.children];\n      }\n\n      if (before == null) {\n        newChildren.push(child);\n      } else {\n        newChildren.splice(newChildren.indexOf(before), 0, child);\n      }\n\n      internals.children = strict ? Object.freeze(newChildren) : newChildren;\n    }\n  });\n}\n\nfunction normalizeChild(child, root) {\n  return typeof child === 'string' ? root.createText(child) : child;\n}\n\nfunction moveNodeToContainer(container, node, rootInternals) {\n  const {\n    tops,\n    parents\n  } = rootInternals;\n  const newTop = container.kind === KIND_ROOT ? container : tops.get(container);\n  tops.set(node, newTop);\n  parents.set(node, container);\n  moveFragmentToContainer(node, rootInternals);\n  allDescendants(node, descendant => {\n    tops.set(descendant, newTop);\n    moveFragmentToContainer(descendant, rootInternals);\n  });\n}\n\nfunction moveFragmentToContainer(node, rootInternals) {\n  if (node.kind !== KIND_COMPONENT) return;\n  const props = node.props;\n  if (!props) return;\n  Object.values(props).forEach(prop => {\n    if (!isRemoteFragment(prop)) return;\n    moveNodeToContainer(node, prop, rootInternals);\n  });\n}\n\nfunction removeNodeFromContainer(node, rootInternals) {\n  const {\n    tops,\n    parents\n  } = rootInternals;\n  tops.delete(node);\n  parents.delete(node);\n  allDescendants(node, descendant => {\n    tops.delete(descendant);\n    removeFragmentFromContainer(descendant, rootInternals);\n  });\n  removeFragmentFromContainer(node, rootInternals);\n}\n\nfunction removeFragmentFromContainer(node, rootInternals) {\n  if (node.kind !== KIND_COMPONENT) return;\n  const props = node.remoteProps;\n\n  for (const key of Object.keys(props !== null && props !== void 0 ? props : {})) {\n    const prop = props[key];\n    if (!isRemoteFragment(prop)) continue;\n    removeNodeFromContainer(prop, rootInternals);\n  }\n}\n\nfunction makePartOfTree(node, {\n  parents,\n  tops,\n  nodes\n}) {\n  nodes.add(node);\n  Object.defineProperty(node, 'parent', {\n    get() {\n      return parents.get(node);\n    },\n\n    configurable: true,\n    enumerable: true\n  });\n  Object.defineProperty(node, 'top', {\n    get() {\n      return tops.get(node);\n    },\n\n    configurable: true,\n    enumerable: true\n  });\n}\n\nfunction serializeChild(value) {\n  return value.kind === KIND_TEXT ? {\n    id: value.id,\n    kind: value.kind,\n    text: value.text\n  } : {\n    id: value.id,\n    kind: value.kind,\n    type: value.type,\n    props: value.remoteProps,\n    children: value.children.map(child => serializeChild(child))\n  };\n}\n\nfunction serializeProp(prop) {\n  if (isRemoteFragment(prop)) {\n    return serializeFragment(prop);\n  }\n\n  return prop;\n}\n\nfunction serializeFragment(value) {\n  return {\n    id: value.id,\n    kind: value.kind,\n\n    get children() {\n      return value.children.map(child => serializeChild(child));\n    }\n\n  };\n}\n\nfunction getCurrentInternals(currentParent, rootInternals) {\n  if (currentParent.kind === KIND_ROOT) {\n    return rootInternals;\n  }\n\n  if (currentParent.kind === KIND_FRAGMENT) {\n    return rootInternals.fragments.get(currentParent);\n  }\n\n  return rootInternals.components.get(currentParent);\n}\n\nfunction makeRemote(value, id, root) {\n  Object.defineProperty(value, 'id', {\n    value: id,\n    configurable: true,\n    writable: false,\n    enumerable: false\n  });\n  Object.defineProperty(value, 'root', {\n    value: root,\n    configurable: true,\n    writable: false,\n    enumerable: false\n  });\n}\n\nfunction tryHotSwappingObjectValues(currentValue, newValue, seen) {\n  if (!isBasicObject(newValue)) {\n    var _collectNestedHotSwap;\n\n    return [makeValueHotSwappable(newValue), (_collectNestedHotSwap = collectNestedHotSwappableValues(currentValue)) === null || _collectNestedHotSwap === void 0 ? void 0 : _collectNestedHotSwap.map(hotSwappable => [hotSwappable, undefined])];\n  }\n\n  let hasChanged = false;\n  const hotSwaps = [];\n  const normalizedNewValue = {}; // eslint-disable-next-line guard-for-in\n\n  for (const key in currentValue) {\n    const currentObjectValue = currentValue[key];\n\n    if (!(key in newValue)) {\n      hasChanged = true;\n      const nestedHotSwappables = collectNestedHotSwappableValues(currentObjectValue);\n\n      if (nestedHotSwappables) {\n        hotSwaps.push(...nestedHotSwappables.map(hotSwappable => [hotSwappable, undefined]));\n      }\n    }\n\n    const newObjectValue = newValue[key];\n    const [updatedValue, elementHotSwaps] = tryHotSwappingValues(currentObjectValue, newObjectValue, seen);\n\n    if (elementHotSwaps) {\n      hotSwaps.push(...elementHotSwaps);\n    }\n\n    if (updatedValue !== IGNORE) {\n      hasChanged = true;\n      normalizedNewValue[key] = updatedValue;\n    }\n  }\n\n  for (const key in newValue) {\n    if (key in normalizedNewValue) continue;\n    hasChanged = true;\n    normalizedNewValue[key] = makeValueHotSwappable(newValue[key]);\n  }\n\n  return [hasChanged ? normalizedNewValue : IGNORE, hotSwaps];\n}\n\nfunction tryHotSwappingArrayValues(currentValue, newValue, seen) {\n  if (!Array.isArray(newValue)) {\n    var _collectNestedHotSwap2;\n\n    return [makeValueHotSwappable(newValue), (_collectNestedHotSwap2 = collectNestedHotSwappableValues(currentValue)) === null || _collectNestedHotSwap2 === void 0 ? void 0 : _collectNestedHotSwap2.map(hotSwappable => [hotSwappable, undefined])];\n  }\n\n  let hasChanged = false;\n  const hotSwaps = [];\n  const newLength = newValue.length;\n  const currentLength = currentValue.length;\n  const maxLength = Math.max(currentLength, newLength);\n  const normalizedNewValue = [];\n\n  for (let i = 0; i < maxLength; i++) {\n    const currentArrayValue = currentValue[i];\n    const newArrayValue = newValue[i];\n\n    if (i < newLength) {\n      if (i >= currentLength) {\n        hasChanged = true;\n        normalizedNewValue[i] = makeValueHotSwappable(newArrayValue);\n        continue;\n      }\n\n      const [updatedValue, elementHotSwaps] = tryHotSwappingValues(currentArrayValue, newArrayValue, seen);\n      if (elementHotSwaps) hotSwaps.push(...elementHotSwaps);\n\n      if (updatedValue === IGNORE) {\n        normalizedNewValue[i] = currentArrayValue;\n        continue;\n      }\n\n      hasChanged = true;\n      normalizedNewValue[i] = updatedValue;\n    } else {\n      hasChanged = true;\n      const nestedHotSwappables = collectNestedHotSwappableValues(currentArrayValue);\n\n      if (nestedHotSwappables) {\n        hotSwaps.push(...nestedHotSwappables.map(hotSwappable => [hotSwappable, undefined]));\n      }\n    }\n  }\n\n  return [hasChanged ? normalizedNewValue : IGNORE, hotSwaps];\n}\n\nexport { createRemoteRoot };\n", "import { createRemoteRoot } from '@remote-ui/core';\n\n/**\n * This function takes an extension function that is expecting a `RemoteRoot` as its\n * first argument, and returns a new function that accepts a `RemoteChannel` instead.\n * This is a convenience that allows the raw UI extension API to only expose the simpler\n * `RemoteChannel` type, while allowing the extension to use the more powerful `RemoteRoot`,\n * provided by a version of `@remote-ui/core` that the extension controls.\n */\nfunction createExtensionRegistrationFunction() {\n  const extensionWrapper = (target, implementation) => {\n    var _shopify;\n    async function extension(...args) {\n      // Rendering extensions have two arguments. Non-rendering extensions don’t have\n      // a `RemoteChannel` that needs to be normalized, so we can just pass the arguments\n      // through.\n      if (args.length === 1) {\n        return implementation(...args);\n      }\n      const [{\n        channel,\n        components\n      }, api] = args;\n      const root = createRemoteRoot(channel, {\n        components,\n        strict: true\n      });\n      let renderResult = implementation(root, api);\n      if (typeof renderResult === 'object' && renderResult != null && 'then' in renderResult) {\n        renderResult = await renderResult;\n      }\n      root.mount();\n      return renderResult;\n    }\n    (_shopify = globalThis.shopify) === null || _shopify === void 0 ? void 0 : _shopify.extend(target, extension);\n    return extension;\n  };\n  return extensionWrapper;\n}\n\nexport { createExtensionRegistrationFunction };\n", "import { createExtensionRegistrationFunction } from '../../utilities/registration.mjs';\n\nconst extension = createExtensionRegistrationFunction();\n\n/**\n * Registers your UI Extension to run for the selected extension target.\n *\n * @param target The extension target you are registering for.\n *\n * @param implementation The function that will be called when Checkout begins rendering\n * your extension. This function is called with the API checkout provided to your\n * extension.\n *\n * @deprecated This is deprecated, use `extension` instead.\n */\nconst extend = extension;\n\nexport { extend, extension };\n", "import { extension } from '@shopify/ui-extensions/checkout';\n\n// アプリブロックのエントリーポイント\nexport default extension('product.block.render', (root, { shop, i18n }) => {\n  // 商品予約状況表示ブロックを作成\n  const container = root.createComponent('BlockStack', { spacing: 'base' });\n\n  // タイトル\n  const title = root.createComponent('Heading', {}, '予約状況カレンダー');\n  container.appendChild(title);\n\n  // 説明文\n  const description = root.createComponent('Text', {}, 'この商品の予約状況を確認できます。');\n  container.appendChild(description);\n\n  // 予約状況表示ボックス\n  const box = root.createComponent('Box', {\n    border: 'base',\n    borderRadius: 'base',\n    padding: 'base',\n    background: 'surface-subdued'\n  });\n\n  // 予約状況リスト\n  const bookingsList = root.createComponent('BlockStack', { spacing: 'tight' });\n\n  // 予約状況タイトル\n  const bookingsTitle = root.createComponent('Text', {}, '予約状況表示（サンプル）');\n  bookingsList.appendChild(bookingsTitle);\n\n  // 本予約サンプル\n  const confirmedBooking = root.createComponent('Box', {\n    padding: 'base',\n    background: 'success-subdued',\n    borderRadius: 'base'\n  });\n  confirmedBooking.appendChild(\n    root.createComponent('Text', {}, '本予約: 2023/12/10 〜 2023/12/12')\n  );\n  bookingsList.appendChild(confirmedBooking);\n\n  // 仮予約サンプル\n  const provisionalBooking = root.createComponent('Box', {\n    padding: 'base',\n    background: 'warning-subdued',\n    borderRadius: 'base'\n  });\n  provisionalBooking.appendChild(\n    root.createComponent('Text', {}, '仮予約: 2023/12/20 〜 2023/12/22')\n  );\n  bookingsList.appendChild(provisionalBooking);\n\n  // メンテナンスサンプル\n  const maintenance = root.createComponent('Box', {\n    padding: 'base',\n    background: 'neutral-subdued',\n    borderRadius: 'base'\n  });\n  maintenance.appendChild(\n    root.createComponent('Text', {}, 'メンテナンス: 2023/12/25 〜 2023/12/27')\n  );\n  bookingsList.appendChild(maintenance);\n\n  // ボックスに予約状況リストを追加\n  box.appendChild(bookingsList);\n\n  // コンテナにボックスを追加\n  container.appendChild(box);\n\n  // 注意書き\n  const note = root.createComponent('Text', { size: 'small' }, '※ 予約状況は随時更新されます。最新の情報は予約時に確認してください。');\n  container.appendChild(note);\n\n  // 予約ボタン\n  const buttonContainer = root.createComponent('InlineStack', { spacing: 'tight', alignment: 'center' });\n  const button = root.createComponent('Button', {\n    kind: 'primary',\n    onPress: () => {\n      console.log('予約ボタンがクリックされました');\n    }\n  }, 'この商品を予約する');\n  buttonContainer.appendChild(button);\n  container.appendChild(buttonContainer);\n\n  // ルートに追加\n  root.appendChild(container);\n});\n"], "mappings": "kjBA8FA,SAASA,EAAcC,EAAO,CAC5B,GAAIA,GAAS,MAAQ,OAAOA,GAAU,SAAU,MAAO,GACvD,IAAMC,EAAY,OAAO,eAAeD,CAAK,EAC7C,OAAOC,GAAa,MAAQA,IAAc,OAAO,SACnD,CC1FA,SAASC,EAAiBC,EAAQ,CAChC,OAAOA,GAAU,MAAQA,EAAO,OAAS,CAC3C,CCNA,IAAMC,EAAsC,YACtCC,EAAe,CAAC,EAChBC,GAAc,CAAC,EACrB,SAASC,EAAiBC,EAAS,CACjC,OAAAC,EAAS,GACT,WAAAC,CACF,EAAI,CAAC,EAAG,CACN,IAAIC,EAAY,EACVC,EAAgB,CACpB,OAAAH,EACA,QAAS,GACT,QAAAD,EACA,SAAUF,GACV,MAAO,IAAI,QACX,QAAS,IAAI,QACb,KAAM,IAAI,QACV,WAAY,IAAI,QAChB,UAAW,IAAI,OACjB,EACIG,GAAQ,OAAO,OAAOC,CAAU,EACpC,IAAMG,EAAa,CACjB,KAAM,EACN,QAASJ,EAAS,OAAO,OAAO,CAC9B,OAAAA,EACA,WAAAC,CACF,CAAC,EAAI,CACH,OAAAD,EACA,WAAAC,CACF,EAEA,IAAI,UAAW,CACb,OAAOE,EAAc,QACvB,EAEA,gBAAgBE,KAASC,EAAM,CAC7B,GAAIL,GAAcA,EAAW,QAAQI,CAAI,EAAI,EAC3C,MAAM,IAAI,MAAM,0BAA0BA,CAAI,EAAE,EAGlD,GAAM,CAACE,EAAcC,EAAiB,GAAGC,CAAY,EAAIH,EACnDI,EAAyBH,GAAiB,KAAkCA,EAAe,CAAC,EAC5FI,EAA4B,CAAC,EAC7BC,EAA0B,CAAC,EAEjC,GAAIL,EACF,QAAWM,KAAO,OAAO,KAAKN,CAAY,EASpCM,IAAQ,aACZD,EAAwBC,CAAG,EAAIC,EAAsBC,EAAcR,EAAaM,CAAG,CAAC,CAAC,GAIzF,GAAIL,EACF,GAAI,MAAM,QAAQA,CAAe,EAC/B,QAAWQ,KAASR,EAClBG,EAA0B,KAAKM,EAAeD,EAAOZ,CAAU,CAAC,MAE7D,CACLO,EAA0B,KAAKM,EAAeT,EAAiBJ,CAAU,CAAC,EAK1E,QAAWY,KAASP,EAClBE,EAA0B,KAAKM,EAAeD,EAAOZ,CAAU,CAAC,CAEpE,CAGF,IAAMc,EAAK,GAAGhB,GAAW,GACnBiB,EAAY,CAChB,cAAenB,EAAS,OAAO,OAAOU,CAAsB,EAAIA,EAChE,cAAeE,EACf,SAAUZ,EAAS,OAAO,OAAOW,CAAyB,EAAIA,CAChE,EACMS,EAAYC,EAAA,CAChB,KAAM,EAEN,IAAI,UAAW,CACb,OAAOF,EAAU,QACnB,EAEA,IAAI,OAAQ,CACV,OAAOA,EAAU,aACnB,EAEA,IAAI,aAAc,CAChB,OAAOA,EAAU,aACnB,EAEA,OAAQ,IAAMG,EAAOF,CAAS,EAC9B,YAAaG,GAAYC,GAAYJ,EAAWG,EAAUJ,EAAWhB,CAAa,EAClF,OAAQ,IAAIsB,IAAaC,EAAON,EAAWK,EAAS,IAAIT,GAASC,EAAeD,EAAOZ,CAAU,CAAC,EAAGe,EAAWhB,CAAa,EAC7H,YAAaa,GAASW,EAAYP,EAAWH,EAAeD,EAAOZ,CAAU,EAAGe,EAAWhB,CAAa,EACxG,YAAaa,GAASY,EAAYR,EAAWJ,EAAOG,EAAWhB,CAAa,EAC5E,gBAAiB,IAAIsB,IAAaI,EAAgBT,EAAWK,EAAS,IAAIT,GAASC,EAAeD,EAAOZ,CAAU,CAAC,EAAGe,EAAWhB,CAAa,EAC/I,aAAc,CAACa,EAAOc,IAAWC,EAAaX,EAAWH,EAAeD,EAAOZ,CAAU,EAAG0B,EAAQX,EAAWhB,CAAa,EAC5H,kBAAmB,CAACa,EAAOc,IAAWC,EAAaX,EAAWH,EAAeD,EAAOZ,CAAU,EAAG0B,EAAQX,EAAWhB,CAAa,GAK9HP,GAELO,EAAc,WAAW,IAAIiB,EAAWD,CAAS,EACjD,OAAO,eAAeC,EAAW,OAAQ,CACvC,MAAOf,EACP,aAAc,GACd,SAAU,GACV,WAAY,EACd,CAAC,EACD2B,EAAeZ,EAAWjB,CAAa,EACvC8B,EAAWb,EAAWF,EAAId,CAAU,EAEpC,QAAWY,KAASG,EAAU,SAC5Be,EAAoBd,EAAWJ,EAAOb,CAAa,EAGrD,OAAOiB,CACT,EAEA,WAAWe,EAAU,GAAI,CACvB,IAAMjB,EAAK,GAAGhB,GAAW,GACnBiB,EAAY,CAChB,KAAMgB,CACR,EAEMC,EAASC,GAAWC,GAAWC,EAAMF,EAASlB,EAAWhB,CAAa,EAEtEoC,EAAOlB,EAAA,CACX,KAAM,EAEN,IAAI,MAAO,CACT,OAAOF,EAAU,IACnB,EAEA,OAAAiB,EACA,WAAYA,EACZ,OAAQ,IAAMd,EAAOiB,CAAI,GAGtB3C,GAEL,OAAAoC,EAAeO,EAAMpC,CAAa,EAClC8B,EAAWM,EAAMrB,EAAId,CAAU,EACxBmC,CACT,EAEA,gBAAiB,CACf,IAAMrB,EAAK,GAAGhB,GAAW,GACnBiB,EAAY,CAChB,SAAUnB,EAAS,OAAO,OAAO,CAAC,CAAC,EAAI,CAAC,CAC1C,EACMwC,EAAWnB,EAAA,CACf,KAAM,EAEN,IAAI,UAAW,CACb,OAAOF,EAAU,QACnB,EAEA,OAAQ,IAAIM,IAAaC,EAAOc,EAAUf,EAAS,IAAIT,GAASC,EAAeD,EAAOZ,CAAU,CAAC,EAAGe,EAAWhB,CAAa,EAC5H,YAAaa,GAASW,EAAYa,EAAUvB,EAAeD,EAAOZ,CAAU,EAAGe,EAAWhB,CAAa,EACvG,YAAaa,GAASY,EAAYY,EAAUxB,EAAOG,EAAWhB,CAAa,EAC3E,gBAAiB,IAAIsB,IAAaI,EAAgBW,EAAUf,EAAS,IAAIT,GAASC,EAAeD,EAAOZ,CAAU,CAAC,EAAGe,EAAWhB,CAAa,EAC9I,aAAc,CAACa,EAAOc,IAAWC,EAAaS,EAAUvB,EAAeD,EAAOZ,CAAU,EAAG0B,EAAQX,EAAWhB,CAAa,EAC3H,kBAAmB,CAACa,EAAOc,IAAWC,EAAaS,EAAUvB,EAAeD,EAAOZ,CAAU,EAAG0B,EAAQX,EAAWhB,CAAa,GAG7HP,GAEL,OAAAO,EAAc,UAAU,IAAIqC,EAAUrB,CAAS,EAC/Ca,EAAeQ,EAAUrC,CAAa,EACtC8B,EAAWO,EAAUtB,EAAId,CAAU,EAC5BoC,CACT,EAEA,OAAQ,IAAIf,IAAaC,EAAOtB,EAAYqB,EAAS,IAAIT,GAASC,EAAeD,EAAOZ,CAAU,CAAC,EAAGD,EAAeA,CAAa,EAClI,YAAaa,GAASW,EAAYvB,EAAYa,EAAeD,EAAOZ,CAAU,EAAGD,EAAeA,CAAa,EAC7G,gBAAiB,IAAIsB,IAAaI,EAAgBzB,EAAYqB,EAAS,IAAIT,GAASC,EAAeD,EAAOZ,CAAU,CAAC,EAAGD,EAAeA,CAAa,EACpJ,YAAaa,GAASY,EAAYxB,EAAYY,EAAOb,EAAeA,CAAa,EACjF,aAAc,CAACa,EAAOc,IAAWC,EAAa3B,EAAYa,EAAeD,EAAOZ,CAAU,EAAG0B,EAAQ3B,EAAeA,CAAa,EACjI,kBAAmB,CAACa,EAAOc,IAAWC,EAAa3B,EAAYa,EAAeD,EAAOZ,CAAU,EAAG0B,EAAQ3B,EAAeA,CAAa,EAEtI,OAAQ,CACN,OAAIA,EAAc,QAAgB,QAAQ,QAAQ,GAClDA,EAAc,QAAU,GACjB,QAAQ,QAAQJ,EAAQ,EAAcI,EAAc,SAAS,IAAIsC,CAAc,CAAC,CAAC,EAC1F,CAEF,EACA,OAAOrC,CACT,CAEA,SAASsC,GAAUC,EAAS,CAC1B,KAAAC,CACF,EAAG,CACD,IAAIC,EAEJ,QAASA,EAAYD,EAAK,IAAID,CAAO,KAAO,MAAQE,IAAc,OAAS,OAASA,EAAU,QAAU,CAC1G,CAEA,SAASC,EAAeH,EAASI,EAAU,CACzC,IAAMC,EAAUL,GAAW,CACzB,GAAI,aAAcA,EAChB,QAAW3B,KAAS2B,EAAQ,SAC1BI,EAAS/B,CAAK,EACdgC,EAAQhC,CAAK,CAGnB,EAEAgC,EAAQL,CAAO,CACjB,CAEA,SAASM,EAAQN,EAASxC,EAAe,CACvC,OAAA+C,EACA,MAAAC,CACF,EAAG,CACD,GAAM,CACJ,QAAAC,EACA,QAAArD,CACF,EAAII,EAEAiD,IAAYT,EAAQ,OAAS,GAAaD,GAAUC,EAASxC,CAAa,IAE5E+C,EAAOnD,CAAO,EAahBoD,EAAM,CACR,CAEA,SAASb,GAAWC,EAAMF,EAASlB,EAAWhB,EAAe,CAC3D,OAAO8C,EAAQV,EAAMpC,EAAe,CAClC,OAAQJ,GAAWA,EAAQ,EAAoBwC,EAAK,GAAIF,CAAO,EAC/D,MAAO,IAAM,CACXlB,EAAU,KAAOkB,CACnB,CACF,CAAC,CACH,CAEA,IAAMgB,EAAS,OAAO,QAAQ,EAE9B,SAAS7B,GAAYJ,EAAWG,EAAUJ,EAAWhB,EAAe,CAClE,GAAM,CACJ,OAAAH,CACF,EAAIG,EACE,CACJ,cAAemD,EACf,cAAeC,CACjB,EAAIpC,EACEqC,EAAqB,CAAC,EACtBC,EAAmB,CAAC,EACtBC,EAAkB,GAEtB,QAAW7C,KAAO,OAAO,KAAKU,CAAQ,EAAG,CAEvC,GAAIV,IAAQ,WAAY,SACxB,IAAM8C,EAAuBJ,EAAqB1C,CAAG,EAC/C+C,EAAmBrC,EAASV,CAAG,EAC/BgD,EAAeP,EAAazC,CAAG,EAC/BiD,EAAW/C,EAAc6C,CAAgB,EAE/C,GAAIC,IAAiBC,IAAaA,GAAY,MAAQ,OAAOA,GAAa,UACxE,SAGF,GAAM,CAACC,EAAOC,CAAQ,EAAIC,EAAqBJ,EAAcC,CAAQ,EAEjEE,GACFP,EAAiB,KAAK,GAAGO,CAAQ,EAG/BD,IAAUV,IACdK,EAAkB,GAClBF,EAAmB3C,CAAG,EAAIkD,EAEtBG,EAAiBP,CAAoB,GACvCQ,EAAwBR,EAAsBxD,CAAa,EAGzD+D,EAAiBN,CAAgB,GACnC1B,EAAoBd,EAAWwC,EAAkBzD,CAAa,EAElE,CAEA,OAAO8C,EAAQ7B,EAAWjB,EAAe,CACvC,OAAQJ,GAAW,CACb2D,GACF3D,EAAQ,EAAqBqB,EAAU,GAAIoC,CAAkB,CAEjE,EACA,MAAO,IAAM,CACX,IAAMY,EAAsB/C,IAAA,GAAKkC,GAC5BhC,GAELJ,EAAU,cAAgBnB,EAAS,OAAO,OAAOoE,CAAmB,EAAIA,EACxEjD,EAAU,cAAgBE,IAAA,GAAKF,EAAU,eACpCqC,GAGL,OAAW,CAACa,EAAcP,CAAQ,IAAKL,EACrCY,EAAa1E,CAAmC,EAAImE,CAExD,CACF,CAAC,CACH,CAmEA,SAASG,EAAqBJ,EAAcC,EAAUQ,EAAO,IAAI,IAAO,CACtE,OAAIA,EAAK,IAAIT,CAAY,EAChB,CAACR,CAAM,EAGZ,OAAOQ,GAAiB,YAAclE,KAAuCkE,GAC/ES,EAAK,IAAIT,CAAY,EACN,CAAC,OAAOC,GAAa,WAAaT,EAASvC,EAAsBgD,CAAQ,EAAG,CAAC,CAACD,EAAcC,CAAQ,CAAC,CAAC,GAInH,MAAM,QAAQD,CAAY,GAC5BS,EAAK,IAAIT,CAAY,EACNU,GAA0BV,EAAcC,EAAUQ,CAAI,GAInEE,EAAcX,CAAY,GAAK,CAACK,EAAiBL,CAAY,GAC/DS,EAAK,IAAIT,CAAY,EACNY,GAA2BZ,EAAcC,EAAUQ,CAAI,GAIzD,CAACT,IAAiBC,EAAWT,EAASS,CAAQ,CAE/D,CAEA,SAAShD,EAAsBiD,EAAOO,EAAO,IAAI,IAAO,CACtD,IAAMI,EAAYJ,EAAK,IAAIP,CAAK,EAChC,GAAIW,EAAW,OAAOA,EAEtB,GAAIR,EAAiBH,CAAK,EACxB,OAAAO,EAAK,IAAIP,EAAOA,CAAK,EACdA,EAGT,GAAI,MAAM,QAAQA,CAAK,EAAG,CACxB,IAAMY,EAAS,CAAC,EAChBL,EAAK,IAAIP,EAAOY,CAAM,EAEtB,QAAWC,KAAUb,EACnBY,EAAO,KAAK7D,EAAsB8D,EAAQN,CAAI,CAAC,EAGjD,OAAOK,CACT,CAEA,GAAIH,EAAcT,CAAK,EAAG,CACxB,IAAMY,EAAS,CAAC,EAChBL,EAAK,IAAIP,EAAOY,CAAM,EAEtB,QAAW9D,KAAO,OAAO,KAAKkD,CAAK,EACjCY,EAAO9D,CAAG,EAAIC,EAAsBiD,EAAMlD,CAAG,EAAGyD,CAAI,EAGtD,OAAOK,CACT,CAEA,GAAI,OAAOZ,GAAU,WAAY,CAC/B,IAAMc,EAAkB,IAAIC,IACnBD,EAAgBlF,CAAmC,EAAE,GAAGmF,CAAI,EAGrE,cAAO,eAAeD,EAAiBlF,EAAqC,CAC1E,WAAY,GACZ,aAAc,GACd,SAAU,GACV,MAAAoE,CACF,CAAC,EACDO,EAAK,IAAIP,EAAOc,CAAe,EACxBA,CACT,CAEA,OAAOd,CACT,CAEA,SAASgB,EAAgChB,EAAOO,EAAO,IAAI,IAAO,CAChE,GAAI,CAAAA,EAAK,IAAIP,CAAK,EAGlB,IAFAO,EAAK,IAAIP,CAAK,EAEV,MAAM,QAAQA,CAAK,EACrB,OAAOA,EAAM,OAAO,CAACiB,EAAKrC,IAAY,CACpC,IAAMiC,EAASG,EAAgCpC,EAAS2B,CAAI,EAC5D,OAAOM,EAAS,CAAC,GAAGI,EAAK,GAAGJ,CAAM,EAAII,CACxC,EAAG,CAAC,CAAC,EAGP,GAAIR,EAAcT,CAAK,EACrB,OAAO,OAAO,KAAKA,CAAK,EAAE,OAAO,CAACiB,EAAKnE,IAAQ,CAC7C,IAAM+D,EAASG,EAAgChB,EAAMlD,CAAG,EAAGyD,CAAI,EAC/D,OAAOM,EAAS,CAAC,GAAGI,EAAK,GAAGJ,CAAM,EAAII,CACxC,EAAG,CAAC,CAAC,EAGP,GAAI,OAAOjB,GAAU,WACnB,OAAOpE,KAAuCoE,EAAQ,CAACA,CAAK,EAAI,OAIpE,CAEA,SAASzC,EAAON,EAAO,CACrB,IAAIiE,GAEHA,EAAgBjE,EAAM,UAAY,MAAQiE,IAAkB,QAAkBA,EAAc,YAAYjE,CAAK,CAChH,CAEA,SAASU,EAAOwD,EAAWzD,EAAUN,EAAWhB,EAAe,CAC7D,QAAWa,KAASS,EAClBE,EAAYuD,EAAWlE,EAAOG,EAAWhB,CAAa,CAE1D,CAEA,SAASwB,EAAYuD,EAAWlE,EAAOG,EAAWhB,EAAe,CAC/D,IAAIgF,EAEJ,GAAM,CACJ,MAAAC,EACA,OAAApF,CACF,EAAIG,EAEJ,GAAI,CAACiF,EAAM,IAAIpE,CAAK,EAClB,MAAM,IAAI,MAAM,+DAA+D,EAGjF,IAAMqE,EAAgBrE,EAAM,OACtBsE,GAAiBH,EAAwBE,GAAkB,KAAmC,OAASA,EAAc,SAAS,QAAQrE,CAAK,KAAO,MAAQmE,IAA0B,OAASA,EAAwB,GAC3N,OAAOlC,EAAQiC,EAAW/E,EAAe,CACvC,OAAQJ,GAAW,CACjBA,EAAQ,EAAqBmF,EAAU,GAAII,EAAgB,EAAIJ,EAAU,SAAS,OAASA,EAAU,SAAS,OAAS,EAAGzC,EAAezB,CAAK,EAAGqE,EAAgBA,EAAc,GAAK,EAAK,CAC3L,EACA,MAAO,IAAM,CACXnD,EAAoBgD,EAAWlE,EAAOb,CAAa,EACnD,IAAIoF,EAEJ,GAAIF,EAAe,CACjB,IAAMG,EAAmBC,EAAoBJ,EAAelF,CAAa,EACnEuF,EAAkB,CAAC,GAAGF,EAAiB,QAAQ,EACrDE,EAAgB,OAAOJ,EAAe,CAAC,EAEnCD,IAAkBH,EACpBK,EAAcG,GAEdF,EAAiB,SAAWxF,EAAS,OAAO,OAAO0F,CAAe,EAAIA,EACtEH,EAAc,CAAC,GAAGpE,EAAU,QAAQ,EAExC,MACEoE,EAAc,CAAC,GAAGpE,EAAU,QAAQ,EAGtCoE,EAAY,KAAKvE,CAAK,EACtBG,EAAU,SAAWnB,EAAS,OAAO,OAAOuF,CAAW,EAAIA,CAC7D,CACF,CAAC,CACH,CAEA,SAAS1D,EAAgBqD,EAAWzD,EAAUN,EAAWhB,EAAe,CACtE,QAAWa,KAASkE,EAAU,SAC5BtD,EAAYsD,EAAWlE,EAAOG,EAAWhB,CAAa,EAGxDuB,EAAOwD,EAAWzD,EAAUN,EAAWhB,CAAa,CACtD,CAWA,SAASyB,EAAYsD,EAAWlE,EAAOG,EAAWhB,EAAe,CAC/D,GAAM,CACJ,OAAAH,CACF,EAAIG,EACEwF,EAAaT,EAAU,SAAS,QAAQlE,CAAK,EAEnD,GAAI2E,IAAe,GAInB,OAAO1C,EAAQiC,EAAW/E,EAAe,CACvC,OAAQJ,GAAWA,EAAQ,EAAqBmF,EAAU,GAAIS,CAAU,EACxE,MAAO,IAAM,CACXxB,EAAwBnD,EAAOb,CAAa,EAC5C,IAAMoF,EAAc,CAAC,GAAGpE,EAAU,QAAQ,EAC1CoE,EAAY,OAAOA,EAAY,QAAQvE,CAAK,EAAG,CAAC,EAChDG,EAAU,SAAWnB,EAAS,OAAO,OAAOuF,CAAW,EAAIA,CAC7D,CACF,CAAC,CACH,CAEA,SAASxD,EAAamD,EAAWlE,EAAOc,EAAQX,EAAWhB,EAAe,CACxE,IAAIyF,EAEJ,GAAM,CACJ,OAAA5F,EACA,MAAAoF,CACF,EAAIjF,EAEJ,GAAI,CAACiF,EAAM,IAAIpE,CAAK,EAClB,MAAM,IAAI,MAAM,+DAA+D,EAGjF,IAAMqE,EAAgBrE,EAAM,OACtBsE,GAAiBM,EAAyBP,GAAkB,KAAmC,OAASA,EAAc,SAAS,QAAQrE,CAAK,KAAO,MAAQ4E,IAA2B,OAASA,EAAyB,GAC9N,OAAO3C,EAAQiC,EAAW/E,EAAe,CACvC,OAAQJ,GAAW,CACjB,IAAM8F,EAAc/D,GAAU,KAAOoD,EAAU,SAAS,OAAS,EAAIA,EAAU,SAAS,QAAQpD,CAAM,EACtG/B,EAAQ,EAAqBmF,EAAU,GAAIW,EAAcP,GAAiBA,EAAgB,EAAIO,EAAcA,EAAc,EAAGpD,EAAezB,CAAK,EAAGqE,EAAgBA,EAAc,GAAK,EAAK,CAC9L,EACA,MAAO,IAAM,CACXnD,EAAoBgD,EAAWlE,EAAOb,CAAa,EACnD,IAAIoF,EAEJ,GAAIF,EAAe,CACjB,IAAMG,EAAmBC,EAAoBJ,EAAelF,CAAa,EACnEuF,EAAkB,CAAC,GAAGF,EAAiB,QAAQ,EACrDE,EAAgB,OAAOJ,EAAe,CAAC,EAEnCD,IAAkBH,EACpBK,EAAcG,GAEdF,EAAiB,SAAWxF,EAAS,OAAO,OAAO0F,CAAe,EAAIA,EACtEH,EAAc,CAAC,GAAGpE,EAAU,QAAQ,EAExC,MACEoE,EAAc,CAAC,GAAGpE,EAAU,QAAQ,EAGlCW,GAAU,KACZyD,EAAY,KAAKvE,CAAK,EAEtBuE,EAAY,OAAOA,EAAY,QAAQzD,CAAM,EAAG,EAAGd,CAAK,EAG1DG,EAAU,SAAWnB,EAAS,OAAO,OAAOuF,CAAW,EAAIA,CAC7D,CACF,CAAC,CACH,CAEA,SAAStE,EAAeD,EAAO8E,EAAM,CACnC,OAAO,OAAO9E,GAAU,SAAW8E,EAAK,WAAW9E,CAAK,EAAIA,CAC9D,CAEA,SAASkB,EAAoBgD,EAAWa,EAAM5F,EAAe,CAC3D,GAAM,CACJ,KAAAyC,EACA,QAAAoD,CACF,EAAI7F,EACE8F,EAASf,EAAU,OAAS,EAAYA,EAAYtC,EAAK,IAAIsC,CAAS,EAC5EtC,EAAK,IAAImD,EAAME,CAAM,EACrBD,EAAQ,IAAID,EAAMb,CAAS,EAC3BgB,EAAwBH,EAAM5F,CAAa,EAC3C2C,EAAeiD,EAAMI,GAAc,CACjCvD,EAAK,IAAIuD,EAAYF,CAAM,EAC3BC,EAAwBC,EAAYhG,CAAa,CACnD,CAAC,CACH,CAEA,SAAS+F,EAAwBH,EAAM5F,EAAe,CACpD,GAAI4F,EAAK,OAAS,EAAgB,OAClC,IAAMK,EAAQL,EAAK,MACdK,GACL,OAAO,OAAOA,CAAK,EAAE,QAAQC,GAAQ,CAC9BnC,EAAiBmC,CAAI,GAC1BnE,EAAoB6D,EAAMM,EAAMlG,CAAa,CAC/C,CAAC,CACH,CAEA,SAASgE,EAAwB4B,EAAM5F,EAAe,CACpD,GAAM,CACJ,KAAAyC,EACA,QAAAoD,CACF,EAAI7F,EACJyC,EAAK,OAAOmD,CAAI,EAChBC,EAAQ,OAAOD,CAAI,EACnBjD,EAAeiD,EAAMI,GAAc,CACjCvD,EAAK,OAAOuD,CAAU,EACtBG,EAA4BH,EAAYhG,CAAa,CACvD,CAAC,EACDmG,EAA4BP,EAAM5F,CAAa,CACjD,CAEA,SAASmG,EAA4BP,EAAM5F,EAAe,CACxD,GAAI4F,EAAK,OAAS,EAAgB,OAClC,IAAMK,EAAQL,EAAK,YAEnB,QAAWlF,KAAO,OAAO,KAAKuF,GAAU,KAA2BA,EAAQ,CAAC,CAAC,EAAG,CAC9E,IAAMC,EAAOD,EAAMvF,CAAG,EACjBqD,EAAiBmC,CAAI,GAC1BlC,EAAwBkC,EAAMlG,CAAa,CAC7C,CACF,CAEA,SAAS6B,EAAe+D,EAAM,CAC5B,QAAAC,EACA,KAAApD,EACA,MAAAwC,CACF,EAAG,CACDA,EAAM,IAAIW,CAAI,EACd,OAAO,eAAeA,EAAM,SAAU,CACpC,KAAM,CACJ,OAAOC,EAAQ,IAAID,CAAI,CACzB,EAEA,aAAc,GACd,WAAY,EACd,CAAC,EACD,OAAO,eAAeA,EAAM,MAAO,CACjC,KAAM,CACJ,OAAOnD,EAAK,IAAImD,CAAI,CACtB,EAEA,aAAc,GACd,WAAY,EACd,CAAC,CACH,CAEA,SAAStD,EAAesB,EAAO,CAC7B,OAAOA,EAAM,OAAS,EAAY,CAChC,GAAIA,EAAM,GACV,KAAMA,EAAM,KACZ,KAAMA,EAAM,IACd,EAAI,CACF,GAAIA,EAAM,GACV,KAAMA,EAAM,KACZ,KAAMA,EAAM,KACZ,MAAOA,EAAM,YACb,SAAUA,EAAM,SAAS,IAAI/C,GAASyB,EAAezB,CAAK,CAAC,CAC7D,CACF,CAEA,SAASD,EAAcsF,EAAM,CAC3B,OAAInC,EAAiBmC,CAAI,EAChBE,GAAkBF,CAAI,EAGxBA,CACT,CAEA,SAASE,GAAkBxC,EAAO,CAChC,MAAO,CACL,GAAIA,EAAM,GACV,KAAMA,EAAM,KAEZ,IAAI,UAAW,CACb,OAAOA,EAAM,SAAS,IAAI/C,GAASyB,EAAezB,CAAK,CAAC,CAC1D,CAEF,CACF,CAEA,SAASyE,EAAoBJ,EAAelF,EAAe,CACzD,OAAIkF,EAAc,OAAS,EAClBlF,EAGLkF,EAAc,OAAS,EAClBlF,EAAc,UAAU,IAAIkF,CAAa,EAG3ClF,EAAc,WAAW,IAAIkF,CAAa,CACnD,CAEA,SAASpD,EAAW8B,EAAO7C,EAAI4E,EAAM,CACnC,OAAO,eAAe/B,EAAO,KAAM,CACjC,MAAO7C,EACP,aAAc,GACd,SAAU,GACV,WAAY,EACd,CAAC,EACD,OAAO,eAAe6C,EAAO,OAAQ,CACnC,MAAO+B,EACP,aAAc,GACd,SAAU,GACV,WAAY,EACd,CAAC,CACH,CAEA,SAASrB,GAA2BZ,EAAcC,EAAUQ,EAAM,CAChE,GAAI,CAACE,EAAcV,CAAQ,EAAG,CAC5B,IAAI0C,EAEJ,MAAO,CAAC1F,EAAsBgD,CAAQ,GAAI0C,EAAwBzB,EAAgClB,CAAY,KAAO,MAAQ2C,IAA0B,OAAS,OAASA,EAAsB,IAAInC,GAAgB,CAACA,EAAc,MAAS,CAAC,CAAC,CAC/O,CAEA,IAAIoC,EAAa,GACXzC,EAAW,CAAC,EACZ0C,EAAqB,CAAC,EAE5B,QAAW7F,KAAOgD,EAAc,CAC9B,IAAM8C,EAAqB9C,EAAahD,CAAG,EAE3C,GAAI,EAAEA,KAAOiD,GAAW,CACtB2C,EAAa,GACb,IAAMG,EAAsB7B,EAAgC4B,CAAkB,EAE1EC,GACF5C,EAAS,KAAK,GAAG4C,EAAoB,IAAIvC,GAAgB,CAACA,EAAc,MAAS,CAAC,CAAC,CAEvF,CAEA,IAAMwC,EAAiB/C,EAASjD,CAAG,EAC7B,CAACiG,EAAcC,CAAe,EAAI9C,EAAqB0C,EAAoBE,EAAgBvC,CAAI,EAEjGyC,GACF/C,EAAS,KAAK,GAAG+C,CAAe,EAG9BD,IAAiBzD,IACnBoD,EAAa,GACbC,EAAmB7F,CAAG,EAAIiG,EAE9B,CAEA,QAAWjG,KAAOiD,EACZjD,KAAO6F,IACXD,EAAa,GACbC,EAAmB7F,CAAG,EAAIC,EAAsBgD,EAASjD,CAAG,CAAC,GAG/D,MAAO,CAAC4F,EAAaC,EAAqBrD,EAAQW,CAAQ,CAC5D,CAEA,SAASO,GAA0BV,EAAcC,EAAUQ,EAAM,CAC/D,GAAI,CAAC,MAAM,QAAQR,CAAQ,EAAG,CAC5B,IAAIkD,EAEJ,MAAO,CAAClG,EAAsBgD,CAAQ,GAAIkD,EAAyBjC,EAAgClB,CAAY,KAAO,MAAQmD,IAA2B,OAAS,OAASA,EAAuB,IAAI3C,GAAgB,CAACA,EAAc,MAAS,CAAC,CAAC,CAClP,CAEA,IAAIoC,EAAa,GACXzC,EAAW,CAAC,EACZiD,EAAYnD,EAAS,OACrBoD,EAAgBrD,EAAa,OAC7BsD,EAAY,KAAK,IAAID,EAAeD,CAAS,EAC7CP,EAAqB,CAAC,EAE5B,QAAS,EAAI,EAAG,EAAIS,EAAW,IAAK,CAClC,IAAMC,EAAoBvD,EAAa,CAAC,EAClCwD,EAAgBvD,EAAS,CAAC,EAEhC,GAAI,EAAImD,EAAW,CACjB,GAAI,GAAKC,EAAe,CACtBT,EAAa,GACbC,EAAmB,CAAC,EAAI5F,EAAsBuG,CAAa,EAC3D,QACF,CAEA,GAAM,CAACP,EAAcC,CAAe,EAAI9C,EAAqBmD,EAAmBC,EAAe/C,CAAI,EAGnG,GAFIyC,GAAiB/C,EAAS,KAAK,GAAG+C,CAAe,EAEjDD,IAAiBzD,EAAQ,CAC3BqD,EAAmB,CAAC,EAAIU,EACxB,QACF,CAEAX,EAAa,GACbC,EAAmB,CAAC,EAAII,CAC1B,KAAO,CACLL,EAAa,GACb,IAAMG,EAAsB7B,EAAgCqC,CAAiB,EAEzER,GACF5C,EAAS,KAAK,GAAG4C,EAAoB,IAAIvC,GAAgB,CAACA,EAAc,MAAS,CAAC,CAAC,CAEvF,CACF,CAEA,MAAO,CAACoC,EAAaC,EAAqBrD,EAAQW,CAAQ,CAC5D,CCz1BA,SAASsD,IAAsC,CA4B7C,MA3ByB,CAACC,EAAQC,IAAmB,CACnD,IAAIC,EACJ,SAAeC,KAAaC,EAAM,QAAAC,EAAA,sBAIhC,GAAID,EAAK,SAAW,EAClB,OAAOH,EAAe,GAAGG,CAAI,EAE/B,GAAM,CAAC,CACL,QAAAE,EACA,WAAAC,CACF,EAAGC,CAAG,EAAIJ,EACJK,EAAOC,EAAiBJ,EAAS,CACrC,WAAAC,EACA,OAAQ,EACV,CAAC,EACGI,EAAeV,EAAeQ,EAAMD,CAAG,EAC3C,OAAI,OAAOG,GAAiB,UAAYA,GAAgB,MAAQ,SAAUA,IACxEA,EAAe,MAAMA,GAEvBF,EAAK,MAAM,EACJE,CACT,GACA,OAACT,EAAW,WAAW,WAAa,MAAQA,IAAa,QAAkBA,EAAS,OAAOF,EAAQG,CAAS,EACrGA,CACT,CAEF,CCpCA,IAAMS,EAAYC,GAAoC,ECCtD,IAAOC,GAAQC,EAAU,uBAAwB,CAACC,EAAM,CAAE,KAAAC,EAAM,KAAAC,CAAK,IAAM,CAEzE,IAAMC,EAAYH,EAAK,gBAAgB,aAAc,CAAE,QAAS,MAAO,CAAC,EAGlEI,EAAQJ,EAAK,gBAAgB,UAAW,CAAC,EAAG,wDAAW,EAC7DG,EAAU,YAAYC,CAAK,EAG3B,IAAMC,EAAcL,EAAK,gBAAgB,OAAQ,CAAC,EAAG,wGAAmB,EACxEG,EAAU,YAAYE,CAAW,EAGjC,IAAMC,EAAMN,EAAK,gBAAgB,MAAO,CACtC,OAAQ,OACR,aAAc,OACd,QAAS,OACT,WAAY,iBACd,CAAC,EAGKO,EAAeP,EAAK,gBAAgB,aAAc,CAAE,QAAS,OAAQ,CAAC,EAGtEQ,EAAgBR,EAAK,gBAAgB,OAAQ,CAAC,EAAG,0EAAc,EACrEO,EAAa,YAAYC,CAAa,EAGtC,IAAMC,EAAmBT,EAAK,gBAAgB,MAAO,CACnD,QAAS,OACT,WAAY,kBACZ,aAAc,MAChB,CAAC,EACDS,EAAiB,YACfT,EAAK,gBAAgB,OAAQ,CAAC,EAAG,kDAA8B,CACjE,EACAO,EAAa,YAAYE,CAAgB,EAGzC,IAAMC,EAAqBV,EAAK,gBAAgB,MAAO,CACrD,QAAS,OACT,WAAY,kBACZ,aAAc,MAChB,CAAC,EACDU,EAAmB,YACjBV,EAAK,gBAAgB,OAAQ,CAAC,EAAG,kDAA8B,CACjE,EACAO,EAAa,YAAYG,CAAkB,EAG3C,IAAMC,EAAcX,EAAK,gBAAgB,MAAO,CAC9C,QAAS,OACT,WAAY,kBACZ,aAAc,MAChB,CAAC,EACDW,EAAY,YACVX,EAAK,gBAAgB,OAAQ,CAAC,EAAG,oEAAiC,CACpE,EACAO,EAAa,YAAYI,CAAW,EAGpCL,EAAI,YAAYC,CAAY,EAG5BJ,EAAU,YAAYG,CAAG,EAGzB,IAAMM,EAAOZ,EAAK,gBAAgB,OAAQ,CAAE,KAAM,OAAQ,EAAG,+MAAqC,EAClGG,EAAU,YAAYS,CAAI,EAG1B,IAAMC,EAAkBb,EAAK,gBAAgB,cAAe,CAAE,QAAS,QAAS,UAAW,QAAS,CAAC,EAC/Fc,EAASd,EAAK,gBAAgB,SAAU,CAC5C,KAAM,UACN,QAAS,IAAM,CACb,QAAQ,IAAI,4FAAiB,CAC/B,CACF,EAAG,wDAAW,EACda,EAAgB,YAAYC,CAAM,EAClCX,EAAU,YAAYU,CAAe,EAGrCb,EAAK,YAAYG,CAAS,CAC5B,CAAC", "names": ["isBasicObject", "value", "prototype", "isRemoteFragment", "object", "FUNCTION_CURRENT_IMPLEMENTATION_KEY", "EMPTY_OBJECT", "EMPTY_ARRAY", "createRemoteRoot", "channel", "strict", "components", "currentId", "rootInternals", "remoteRoot", "type", "rest", "initialProps", "initialChildren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "normalizedInitialProps", "normalizedInitialChildren", "normalizedInternalProps", "key", "makeValueHotSwappable", "serializeProp", "child", "normalize<PERSON><PERSON><PERSON>", "id", "internals", "component", "__spreadValues", "remove", "newProps", "updateProps", "children", "append", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "before", "insertBefore", "makePartOfTree", "makeRemote", "moveNodeToContainer", "content", "update", "newText", "updateText", "text", "fragment", "serializeChild", "connected", "element", "tops", "_tops$get", "allDescendants", "with<PERSON><PERSON>", "recurse", "perform", "remote", "local", "mounted", "IGNORE", "currentProps", "currentExternalProps", "normalizedNewProps", "hotSwapFunctions", "hasRemoteChange", "currentExternalValue", "newExternalValue", "currentValue", "newValue", "value", "hotSwaps", "tryHotSwappingValues", "isRemoteFragment", "removeNodeFromContainer", "mergedExternalProps", "hotSwappable", "seen", "tryHotSwappingArrayValues", "isBasicObject", "tryHotSwappingObjectValues", "seenValue", "result", "nested", "wrappedFunction", "args", "collectNestedHotSwappableValues", "all", "_child$parent", "container", "_currentParent$childr", "nodes", "currentParent", "existingIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentInternals", "getCurrentInternals", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childIndex", "_currentParent$childr2", "beforeIndex", "root", "node", "parents", "newTop", "moveFragmentToContainer", "descendant", "props", "prop", "removeFragmentFromContainer", "serializeFragment", "_collectNestedHotSwap", "has<PERSON><PERSON>ed", "normalizedNewValue", "currentObjectValue", "nestedHotSwappables", "newObjectValue", "updatedValue", "elementHotSwaps", "_collectNestedHotSwap2", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "currentArrayValue", "newArrayValue", "createExtensionRegistrationFunction", "target", "implementation", "_shopify", "extension", "args", "__async", "channel", "components", "api", "root", "createRemoteRoot", "renderResult", "extension", "createExtensionRegistrationFunction", "BlockExtension_default", "extension", "root", "shop", "i18n", "container", "title", "description", "box", "bookingsList", "bookingsTitle", "confirmedBooking", "provisionalBooking", "maintenance", "note", "buttonContainer", "button"]}