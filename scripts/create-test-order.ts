/**
 * テスト注文作成スクリプト
 *
 * このスクリプトは、テスト用の注文をShopifyに直接作成します。
 * 予約システムを経由せずに、Shopify Admin APIを使用して注文を作成します。
 *
 * 使用方法:
 * npx tsx scripts/create-test-order.ts --customer [顧客ID] --product [商品ID] [--variant [バリアントID]]
 * 
 * オプション:
 * --customer: 顧客のShopify ID（必須）
 * --product: 商品のShopify ID（必須）
 * --variant: バリアントのShopify ID（オプション）
 */

import dotenv from 'dotenv';
import { authenticate } from '../app/shopify.server';
import { parseArgs } from 'node:util';
import { format } from 'date-fns';

// 環境変数の読み込み
dotenv.config();

/**
 * メイン処理
 */
async function main() {
  try {
    // コマンドライン引数を解析
    const args = parseArgs({
      options: {
        customer: { type: 'string' },
        product: { type: 'string' },
        variant: { type: 'string' }
      },
      strict: false
    });

    const customerId = args.values.customer;
    const productId = args.values.product;
    const variantId = args.values.variant;

    if (!customerId || !productId) {
      console.error('顧客IDと商品IDを指定してください');
      console.error('使用方法: npx tsx scripts/create-test-order.ts --customer [顧客ID] --product [商品ID] [--variant [バリアントID]]');
      process.exit(1);
    }

    console.log('テスト注文作成を開始します...');
    console.log('- 顧客ID:', customerId);
    console.log('- 商品ID:', productId);
    console.log('- バリアントID:', variantId || '指定なし');

    // Shopify Admin APIを初期化
    console.log('Shopify Admin APIを初期化中...');
    
    // リクエストオブジェクトを作成（ダミー）
    const headers = new Headers();
    headers.append('Content-Type', 'application/json');
    
    const request = new Request('https://example.com', {
      method: 'GET',
      headers: headers
    });
    
    // authenticate.adminを呼び出す
    const { admin } = await authenticate.admin(request);
    
    console.log('Shopify Admin APIの初期化が完了しました');

    // 顧客IDを正規化
    const normalizedCustomerId = customerId.startsWith('gid://shopify/Customer/')
      ? customerId
      : `gid://shopify/Customer/${customerId}`;

    // 商品IDを正規化
    const normalizedProductId = productId.startsWith('gid://shopify/Product/')
      ? productId
      : `gid://shopify/Product/${productId}`;

    // バリアントIDを正規化
    const normalizedVariantId = variantId
      ? (variantId.startsWith('gid://shopify/ProductVariant/')
          ? variantId
          : `gid://shopify/ProductVariant/${variantId}`)
      : null;

    console.log('正規化されたID:');
    console.log('- 顧客ID:', normalizedCustomerId);
    console.log('- 商品ID:', normalizedProductId);
    console.log('- バリアントID:', normalizedVariantId || '指定なし');

    // 注文作成用の入力データを作成
    const startDate = format(new Date(), 'yyyy-MM-dd');
    const endDate = format(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd');
    const rentalPeriod = `${startDate} 〜 ${endDate}`;

    const orderInput = {
      customerId: normalizedCustomerId,
      lineItems: [
        {
          title: `テストレンタル商品 (レンタル: ${rentalPeriod})`,
          quantity: 1,
          taxable: true,
          requiresShipping: false,
          variantId: normalizedVariantId,
          priceSet: {
            shopMoney: {
              amount: "5000",
              currencyCode: "JPY"
            }
          }
        }
      ],
      tags: ['rental', 'test', 'confirmed'],
      note: `テスト注文\n予約期間: ${rentalPeriod}\n備考: テスト用の注文です`
    };

    console.log('注文作成データ:', JSON.stringify(orderInput, null, 2));

    // GraphQLミューテーションを実行
    const CREATE_ORDER = `
      mutation orderCreate($order: OrderCreateOrderInput!) {
        orderCreate(order: $order) {
          order {
            id
            name
            totalPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            customer {
              id
              email
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    console.log('GraphQL呼び出しを実行します...');
    
    const response = await admin.graphql(
      CREATE_ORDER,
      {
        variables: {
          order: orderInput
        }
      }
    );

    console.log('GraphQL応答を受信しました');
    const result = await response.json();
    console.log('注文作成結果:', JSON.stringify(result, null, 2));

    if (result.errors) {
      console.error('GraphQLエラー:', result.errors);
      process.exit(1);
    }

    if (result.data?.orderCreate?.userErrors?.length > 0) {
      console.error('注文作成エラー:', result.data.orderCreate.userErrors);
      process.exit(1);
    }

    const orderId = result.data?.orderCreate?.order?.id;
    const orderName = result.data?.orderCreate?.order?.name;

    console.log('注文が正常に作成されました:');
    console.log('- 注文ID:', orderId);
    console.log('- 注文番号:', orderName);

    console.log('処理が完了しました');
    process.exit(0);
  } catch (error) {
    console.error('エラーが発生しました:', error);
    process.exit(1);
  }
}

// スクリプトを実行
main();
