import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { ShopifyCustomerService } from "../services/shopify.customer.service";

/**
 * 顧客同期API
 * POST /api/customers/sync - 顧客データを同期
 */
export async function action({ request }: ActionFunctionArgs) {
  // 認証チェック
  const { admin, session } = await authenticate.admin(request);
  
  try {
    // リクエストメソッドをチェック
    if (request.method !== "POST") {
      return json({ success: false, message: "Method not allowed" }, { status: 405 });
    }

    // Shopify顧客サービスを初期化
    const customerService = new ShopifyCustomerService(session);
    
    // 顧客データを同期
    const syncResults = await customerService.syncCustomersWithDatabase(session.shop);
    
    return json({
      success: true,
      message: "顧客データを同期しました",
      results: syncResults
    });
  } catch (error) {
    console.error("顧客同期エラー:", error);
    return json({
      success: false,
      message: "顧客データの同期中にエラーが発生しました",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
