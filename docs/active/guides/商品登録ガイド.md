# 商品登録ガイド

**最終更新日**: 2025/05/17 18:30

## 目次

1. [概要](#概要)
2. [商品登録の流れ](#商品登録の流れ)
3. [CSVによる一括登録](#csvによる一括登録)
4. [メタフィールドの設定](#メタフィールドの設定)
5. [在庫管理と予約状況](#在庫管理と予約状況)
   - [在庫管理の考え方](#在庫管理の考え方)
   - [在庫設定の自動化](#在庫設定の自動化)
   - [予約状況の管理](#予約状況の管理)
6. [テスト方法](#テスト方法)
7. [トラブルシューティング](#トラブルシューティング)

## 概要

本ドキュメントでは、Shopifyを使用したレンタル商品の登録方法について説明します。特に、2万点の商品を効率的に登録し、メタフィールドを設定して、レンタル予約システムと連携させる方法に焦点を当てています。

## 商品登録の流れ

レンタル商品の登録は以下の流れで行います：

1. **CSVによる一括登録**：基本的な商品情報をCSVでインポート
2. **メタフィールドの設定**：レンタル管理に必要なメタフィールドを設定
3. **在庫設定**：在庫数と在庫状態を設定
4. **予約情報の初期化**：予約情報の初期データを設定
5. **テスト**：登録した商品の予約テストを実施

## CSVによる一括登録

### CSVファイルの準備

CSVファイルには以下の項目を含める必要があります：

| 項目 | 説明 | 例 |
|------|------|-----|
| Handle | 商品のハンドル（URL） | basic-sofa-white |
| Title | 商品名 | ベーシックソファ オフホワイト 1シーター |
| Body HTML | 商品説明 | <p>シンプルなデザインの1人掛けソファ</p> |
| Vendor | ベンダー | ヤマナリ |
| Product Type | 商品タイプ | ソファ |
| Tags | タグ（カンマ区切り） | ソファ,オフホワイト,1シーター |
| Published | 公開状態 | TRUE |
| Option1 Name | オプション1名 | レンタル日数 |
| Option1 Value | オプション1値 | 1日レンタル |
| Option2 Name | オプション2名 | Title |
| Option2 Value | オプション2値 | Default Title |
| Variant SKU | バリアントSKU | 10101007-001 |
| Variant Price | バリアント価格 | 8000 |
| Variant Inventory Tracker | 在庫管理 | shopify |
| Variant Inventory Qty | 在庫数 | 1 |
| Variant Inventory Policy | 在庫切れ時の対応 | deny |
| Variant Fulfillment Service | フルフィルメントサービス | manual |
| Variant Requires Shipping | 配送必要 | TRUE |
| Variant Taxable | 課税対象 | TRUE |
| Image Src | 画像URL | https://example.com/images/sofa.jpg |

### CSVファイルのインポート手順

1. Shopify管理画面にログイン
2. 「商品」 > 「すべての商品」に移動
3. 「インポート」ボタンをクリック
4. CSVファイルをアップロード
5. インポート設定を確認し、「インポート商品」をクリック

### 大量データのインポートに関する注意点

- **分割インポート**：2万点のデータは一度に処理できない場合があるため、1,000件程度に分割してインポートすることを推奨
- **画像の事前アップロード**：画像は事前にCDNやShopifyのファイルにアップロードしておくと処理が速くなる
- **バックアップ**：インポート前に既存データのバックアップを取得しておく
- **テスト**：本番環境にインポートする前に、テスト環境で少量のデータでテストを行う

## メタフィールドの設定

レンタル商品管理に必要なメタフィールドは以下の通りです：

### 基本情報（rental.basic_info）

JSONタイプのメタフィールドで、以下の情報を含みます：

```json
{
  "productCode": "10101007",
  "detailCode": "001",
  "kana": "ベーシックソファオフホワイト1シーターヤマナリ",
  "dimensions": {
    "width": 87,
    "depth": 74,
    "height": 76
  },
  "seatDimensions": {
    "width": 52,
    "depth": 54,
    "height": 40
  },
  "material": "ファブリック",
  "color": "オフホワイト",
  "maker": "ヤマナリ",
  "campaign": "通常商品",
  "notes": "クリーニング済みですが全体的に黄ばみ発生（H30.12.15)"
}
```

### 料金設定（rental.pricing）

JSONタイプのメタフィールドで、以下の情報を含みます：

```json
{
  "basePrice": 8000,
  "depositRate": 0.1,
  "discountRules": {
    "day2_6_rate": 0.2,
    "day7_plus_rate": 0.1
  },
  "minimumDays": 1,
  "maximumDays": 30
}
```

### 在庫アイテム情報（rental.inventory_items）

JSONタイプのメタフィールドで、以下の情報を含みます：

```json
[
  {
    "id": "item-001",
    "sku": "10101007-001-1",
    "status": "available",
    "location": "NY",
    "notes": "背面向かって右側うっすら黒いしみ"
  },
  {
    "id": "item-002",
    "sku": "10101007-001-2",
    "status": "maintenance",
    "location": "NY",
    "notes": "向かって左アーム手前と正面左側に黄色い輪染み有"
  }
]
```

### 予約情報（rental.reservation_info）

JSONタイプのメタフィールドで、以下の情報を含みます：

```json
[
  {
    "itemId": "item-001",
    "reservations": [
      {
        "id": "reservation-001",
        "startDate": "2025-05-22",
        "endDate": "2025-05-25",
        "status": "confirmed",
        "customerName": "山田太郎",
        "customerEmail": "<EMAIL>",
        "orderId": "gid://shopify/Order/1001",
        "orderLineItemId": "gid://shopify/LineItem/1001",
        "notes": "特になし"
      }
    ]
  },
  {
    "itemId": "item-002",
    "reservations": []
  }
]
```

### メタフィールドの一括設定

メタフィールドを一括設定するには、以下の方法があります：

1. **GraphQL Admin APIを使用**：
   - スクリプトを作成してGraphQL APIを使用
   - 商品IDとメタフィールド値を指定して一括更新

2. **CSVインポート後のスクリプト実行**：
   - CSVインポート後に専用スクリプトを実行
   - 商品IDとメタフィールド値をマッピングして更新

3. **Shopify Flow（無料プラン対応）**：
   - 商品作成/更新時にメタフィールドを自動設定
   - 特定の条件に基づいてメタフィールドを更新

## 在庫管理と予約状況

### 在庫管理の考え方

レンタル商品の在庫管理は以下の考え方で行います：

1. **在庫数**：各商品の在庫数は基本的に1（同一商品でも個別に管理）
2. **在庫状態**：以下の状態を管理
   - `available`：利用可能
   - `maintenance`：メンテナンス中
   - `damaged`：破損状態
   - `unavailable`：利用不可

### 在庫設定の自動化

在庫設定を自動化するスクリプトを使用して、商品の在庫状態を一括で設定することができます。

#### 在庫設定の基本ルール

在庫設定は以下のルールに従って自動化されます：

1. **在庫の場所**：メタフィールド `rental.location` または `rental.inventory_items[0].location` の値に基づいて、「PR」または「NY」に設定されます。
2. **基本在庫数**：通常の商品は在庫数1で設定されます。
3. **バリアント**：バリアントがある場合は、各バリアントごとに在庫数1で設定されます。
4. **商品状態**：商品状態が「廃棄済み」または「メンテナンス中」の場合は、在庫数0で設定されます。

#### 在庫設定スクリプトの実行

以下のコマンドを実行して、在庫設定を自動化します：

```bash
cd /Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp
export PATH="/usr/local/opt/node@20/bin:$PATH"
node scripts/update-inventory-settings.js
```

このスクリプトは以下の処理を行います：

1. Shopifyの在庫ロケーションを取得する
2. すべての商品とそのバリアントを取得する
3. 各商品の状態と在庫場所を確認する
4. 商品状態に基づいて適切な在庫数を設定する
5. 在庫場所（PR/NY）に基づいて適切なロケーションに在庫を設定する

#### 在庫設定の確認

在庫設定が完了したら、Shopify管理画面で在庫状態を確認してください。以下の点を確認します：

1. 各商品の在庫数が正しく設定されているか
2. 各商品の在庫場所が正しく設定されているか
3. 廃棄済みまたはメンテナンス中の商品の在庫数が0になっているか

### 予約状況の管理

予約状況は以下の情報を管理します：

1. **予約ステータス**：
   - `pending`：仮予約
   - `confirmed`：確定予約
   - `cancelled`：キャンセル済み

2. **予約期間**：
   - 開始日
   - 終了日
   - 休日を挟む場合の処理

3. **予約情報**：
   - 顧客情報
   - 注文情報
   - 備考

### 予約情報の初期化

商品登録後は、予約情報を初期化する必要があります。予約情報の初期化は以下の手順で行います：

1. **予約情報初期化スクリプトの実行**：
   ```bash
   cd /Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp
   export PATH="/usr/local/opt/node@20/bin:$PATH"
   node scripts/initialize-booking-data.js [商品ID]
   ```

2. **初期化内容**：
   - データベース上の既存予約データの削除
   - Shopifyメタフィールドの初期化
   - 初期予約状態の設定（利用可能状態）

3. **初期化の確認**：
   - Shopify管理画面でメタフィールドを確認
   - データベースで予約情報が削除されていることを確認

## テスト方法

商品登録後は、以下のテストを実施して正常に機能することを確認します。詳細なテスト手順は「商品登録テスト手順書.md」を参照してください。

### テスト準備

テストを実行する前に、以下の準備を行います：

1. **環境変数の設定**：
   ```bash
   # .env ファイル
   SHOPIFY_SHOP=peaces-test-block.myshopify.com
   SHOPIFY_API_KEY=your_api_key
   SHOPIFY_API_SECRET=your_api_secret
   SHOPIFY_ADMIN_API_ACCESS_TOKEN=your_admin_api_access_token
   ```

2. **テスト用スクリプトの準備**：
   ```bash
   cd /Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp
   export PATH="/usr/local/opt/node@20/bin:$PATH"
   ```

### 基本テスト

1. **商品表示テスト**：
   - 商品ページが正しく表示されるか
   - メタフィールド情報が正しく表示されるか
   ```bash
   node scripts/check-product.js [商品ID]
   ```

2. **予約テスト**：
   - カレンダーが正しく表示されるか
   - 日付選択が正しく機能するか
   - 料金計算が正しく行われるか
   ```bash
   node scripts/test-booking-reservation.js [商品ID] basic
   ```

3. **在庫テスト**：
   - 在庫状態に応じて予約可否が正しく制御されるか
   - 予約済み期間が正しくブロックされるか
   ```bash
   node scripts/test-product-availability.js [商品ID]
   ```

### 特殊ケーステスト

以下の特殊ケースもテストします：

1. **仮予約・確定予約テスト**：
   - 仮予約が正しく作成されるか
   - 確定予約への変更が正しく行われるか
   ```bash
   # 仮予約テスト
   node scripts/test-booking-reservation.js [商品ID] provisional

   # 確定予約テスト
   node scripts/test-booking-reservation.js [商品ID] confirmed
   ```

2. **休日を挟んだレンタルテスト**：
   - 休日を挟んだ予約が正しく処理されるか
   - 料金計算が正しく行われるか
   ```bash
   node scripts/test-booking-reservation.js [商品ID] holiday
   ```

3. **長期・短期レンタルテスト**：
   - 長期レンタル（8日以上）の料金計算が正しいか
   - 短期レンタル（1-2日）の料金計算が正しいか
   ```bash
   node scripts/test-booking-reservation.js [商品ID] longterm
   ```

4. **複数予約テスト**：
   - 連続した予約が正しく処理されるか
   - 予約間の空き期間が正しく表示されるか
   ```bash
   node scripts/test-multiple-bookings.js [商品ID]
   ```

5. **状態変更テスト**：
   - メンテナンス状態への変更が正しく反映されるか
   - 破損状態への変更が正しく反映されるか
   ```bash
   node scripts/test-status-change.js [商品ID]
   ```

### テスト結果の検証

テスト結果は以下の方法で検証します：

1. **コンソール出力**：各テストスクリプトの実行結果を確認
2. **Shopify管理画面**：メタフィールドや在庫状態の変更を確認
3. **データベース**：予約情報や商品情報の変更を確認
4. **フロントエンド**：実際の商品ページで予約機能を確認

## トラブルシューティング

### よくある問題と解決策

1. **CSVインポートエラー**：
   - CSVファイルの文字コードがUTF-8になっているか確認
   - 必須項目が正しく入力されているか確認
   - 特殊文字（カンマ、引用符など）が適切にエスケープされているか確認

2. **メタフィールド更新エラー**：
   - JSONの形式が正しいか確認
   - 必須項目が含まれているか確認
   - APIリクエストの形式が正しいか確認

3. **在庫表示の問題**：
   - 在庫設定が正しく行われているか確認
   - 在庫数が正しく設定されているか確認
   - 在庫ポリシーが正しく設定されているか確認

4. **予約カレンダーの問題**：
   - メタフィールドが正しく設定されているか確認
   - 予約情報のフォーマットが正しいか確認
   - 日付形式が正しいか確認（YYYY-MM-DD形式）

### サポート情報

問題が解決しない場合は、以下の方法でサポートを受けることができます：

1. **開発者サポート**：
   - 開発者チームに問い合わせ
   - GitHub Issuesで問題を報告

2. **Shopifyサポート**：
   - Shopifyヘルプセンターで情報を検索
   - Shopifyサポートに問い合わせ

3. **コミュニティサポート**：
   - Shopifyコミュニティフォーラムで質問
   - Shopifyパートナーに相談
