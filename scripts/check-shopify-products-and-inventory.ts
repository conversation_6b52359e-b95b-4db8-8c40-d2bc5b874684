import dotenv from 'dotenv';

dotenv.config();

// Shopify API設定
const SHOPIFY_SHOP = process.env.SHOPIFY_SHOP;
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

/**
 * Shopifyの商品一覧を取得
 */
async function getShopifyProducts() {
  const query = `
    query getProducts {
      products(first: 5, reverse: true) {
        edges {
          node {
            id
            title
            handle
            status
            totalInventory
            variants(first: 20) {
              edges {
                node {
                  id
                  title
                  sku
                  price
                  inventoryQuantity
                  inventoryItem {
                    id
                    tracked
                    inventoryLevels(first: 5) {
                      edges {
                        node {
                          id
                          quantities(names: ["available"]) {
                            quantity
                          }
                          location {
                            id
                            name
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
            metafields(first: 20) {
              edges {
                node {
                  namespace
                  key
                  value
                  type
                }
              }
            }
          }
        }
      }
      locations(first: 10) {
        edges {
          node {
            id
            name
            isActive
          }
        }
      }
    }
  `;

  const response = await fetch(`https://${SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN!
    },
    body: JSON.stringify({ query })
  });

  const result = await response.json();
  
  if (result.errors) {
    console.error('GraphQLエラー:', result.errors);
    throw new Error('GraphQL query failed');
  }
  
  return result.data;
}

/**
 * 在庫更新の方法を表示
 */
function showInventoryUpdateInstructions(productTitle: string, variants: any[]) {
  console.log(`\n📝 "${productTitle}" の在庫を設定する方法:\n`);
  
  variants.forEach(variant => {
    if (variant.inventoryItem && variant.inventoryItem.inventoryLevels.edges.length > 0) {
      const location = variant.inventoryItem.inventoryLevels.edges[0].node.location;
      console.log(`// ${variant.title} (${variant.sku})`);
      console.log(`await setInventoryQuantity(`);
      console.log(`  '${variant.inventoryItem.id}',`);
      console.log(`  '${location.id}',`);
      console.log(`  1`);
      console.log(`);\n`);
    }
  });
}

async function main() {
  console.log('=== Shopify商品と在庫状況の確認 ===\n');

  try {
    const data = await getShopifyProducts();
    
    // ロケーション情報
    console.log('📍 利用可能なロケーション:');
    data.locations.edges.forEach((edge: any) => {
      const location = edge.node;
      console.log(`  - ${location.name} (ID: ${location.id}) ${location.isActive ? '✅' : '❌'}`);
    });

    // 商品情報
    console.log('\n\n📦 Shopify商品一覧:');
    
    let productCount = 0;
    let needsInventoryFix = false;

    data.products.edges.forEach((edge: any) => {
      const product = edge.node;
      productCount++;
      
      console.log(`\n${productCount}. ${product.title}`);
      console.log(`   ID: ${product.id}`);
      console.log(`   ハンドル: ${product.handle}`);
      console.log(`   ステータス: ${product.status}`);
      console.log(`   総在庫数: ${product.totalInventory}`);
      
      // メタフィールド確認
      const metafields = product.metafields.edges.map((e: any) => e.node);
      const importantMetafields = ['variant_mapping', 'pricing', 'status', 'product_group', 'general_notes'];
      
      console.log('\n   メタフィールド:');
      importantMetafields.forEach(key => {
        const field = metafields.find((mf: any) => mf.key === key);
        if (field) {
          if (field.type === 'json') {
            console.log(`     ✅ ${key} (JSON)`);
          } else {
            console.log(`     ✅ ${key}: ${field.value?.substring(0, 50)}${field.value?.length > 50 ? '...' : ''}`);
          }
        } else {
          console.log(`     ❌ ${key}: 未設定`);
        }
      });
      
      // バリアント情報
      console.log('\n   バリアント:');
      const variants = product.variants.edges.map((e: any) => e.node);
      
      variants.forEach((variant: any) => {
        console.log(`\n     ${variant.title}`);
        console.log(`       SKU: ${variant.sku}`);
        console.log(`       価格: ¥${variant.price}`);
        console.log(`       在庫数: ${variant.inventoryQuantity}`);
        
        if (variant.inventoryItem && variant.inventoryItem.inventoryLevels.edges.length > 0) {
          variant.inventoryItem.inventoryLevels.edges.forEach((levelEdge: any) => {
            const level = levelEdge.node;
            const available = level.quantities?.[0]?.quantity || 0;
            console.log(`       ${level.location.name}: ${available}個`);
            
            if (available === 0) {
              needsInventoryFix = true;
            }
          });
        }
      });
      
      // 在庫が0のバリアントがある場合、修正方法を表示
      const hasZeroInventory = variants.some((v: any) => 
        v.inventoryItem?.inventoryLevels?.edges?.some((e: any) => {
          const available = e.node.quantities?.[0]?.quantity || 0;
          return available === 0;
        })
      );
      
      if (hasZeroInventory) {
        showInventoryUpdateInstructions(product.title, variants);
      }
    });

    // サマリー
    console.log('\n\n=== サマリー ===');
    console.log(`商品数: ${productCount}`);
    console.log(`在庫修正が必要: ${needsInventoryFix ? 'はい' : 'いいえ'}`);
    
    if (needsInventoryFix) {
      console.log('\n⚠️  一部のバリアントで在庫が0になっています。');
      console.log('レンタル商品は各バリアントの在庫を1に設定する必要があります。');
      console.log('\n在庫を更新するには、上記のコードを使用してください。');
    }

    // 予約テストの準備状況
    console.log('\n\n=== 予約テスト準備チェック ===');
    
    const activeProducts = data.products.edges.filter((e: any) => e.node.status === 'ACTIVE');
    console.log(`✅ アクティブな商品: ${activeProducts.length}件`);
    
    const productsWithMetafields = data.products.edges.filter((e: any) => {
      const metafields = e.node.metafields.edges;
      return metafields.some((mf: any) => mf.node.key === 'pricing' || mf.node.key === 'variant_mapping');
    });
    console.log(`✅ メタフィールド設定済み: ${productsWithMetafields.length}件`);
    
    const productsWithInventory = data.products.edges.filter((e: any) => 
      e.node.totalInventory > 0
    );
    console.log(`✅ 在庫あり: ${productsWithInventory.length}件`);
    
    if (activeProducts.length > 0 && productsWithInventory.length > 0) {
      console.log('\n✅ 予約テストを実行できます！');
      console.log('\n実行コマンド:');
      console.log('  npx tsx scripts/create-test-booking.ts');
    } else {
      console.log('\n❌ 予約テストの前に以下を確認してください:');
      if (activeProducts.length === 0) console.log('  - アクティブな商品を作成');
      if (productsWithInventory.length === 0) console.log('  - 在庫を設定');
    }

  } catch (error) {
    console.error('エラー:', error);
  }
}

main();