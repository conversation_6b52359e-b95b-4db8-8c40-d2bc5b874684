/**
 * 予約テスト用商品作成スクリプト
 *
 * このスクリプトは、予約テスト用の商品を2つデータベースに作成します。
 * 実行方法: npx tsx scripts/create-test-products-for-booking.ts
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト設定
const config = {
  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',

  // テスト用の商品情報
  products: [
    {
      shopifyId: `123456789${Math.floor(Math.random() * 1000)}`,
      title: 'テスト商品1（ソファ）',
      sku: 'TEST-SOFA-001',
      price: 10000,
      status: 'AVAILABLE',
      locationId: 'NY',
      basicInfo: JSON.stringify({
        productCode: 'TEST-SOFA',
        detailCode: '001',
        kana: 'テストソファ',
        location: 'NY',
        status: 'available',
        category: 'ソファ',
        subCategory: 'シングルソファ'
      }),
      pricing: JSON.stringify({
        basePrice: 10000,
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30
      })
    },
    {
      shopifyId: `987654321${Math.floor(Math.random() * 1000)}`,
      title: 'テスト商品2（テーブル）',
      sku: 'TEST-TABLE-001',
      price: 8000,
      status: 'AVAILABLE',
      locationId: 'NY',
      basicInfo: JSON.stringify({
        productCode: 'TEST-TABLE',
        detailCode: '001',
        kana: 'テストテーブル',
        location: 'NY',
        status: 'available',
        category: 'テーブル',
        subCategory: 'ダイニングテーブル'
      }),
      pricing: JSON.stringify({
        basePrice: 8000,
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30
      })
    }
  ]
};

/**
 * テスト用商品を作成する関数
 */
async function createTestProduct(productData: any) {
  try {
    console.log(`テスト用商品「${productData.title}」を作成中...`);

    // 既存の商品を確認
    const existingProduct = await prisma.product.findFirst({
      where: {
        shop: config.shop,
        sku: productData.sku
      }
    });

    if (existingProduct) {
      console.log('既存のテスト用商品が見つかりました:');
      console.log(`商品ID: ${existingProduct.id}`);
      console.log(`ShopifyID: ${existingProduct.shopifyId}`);
      console.log(`商品名: ${existingProduct.title}`);
      console.log(`SKU: ${existingProduct.sku}`);

      return existingProduct;
    }

    // 商品データを作成
    const product = await prisma.product.create({
      data: {
        id: uuidv4(),
        shop: config.shop,
        shopifyId: productData.shopifyId,
        title: productData.title,
        sku: productData.sku,
        price: productData.price,
        status: productData.status,
        locationId: productData.locationId,
        basicInfo: productData.basicInfo,
        pricing: productData.pricing,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('テスト用商品を作成しました:');
    console.log(`商品ID: ${product.id}`);
    console.log(`ShopifyID: ${product.shopifyId}`);
    console.log(`商品名: ${product.title}`);
    console.log(`SKU: ${product.sku}`);

    return product;
  } catch (error) {
    console.error('テスト用商品作成エラー:', error);
    throw error;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('予約テスト用商品作成スクリプトを開始します...');

    const createdProducts = [];

    // テスト用商品を作成
    for (const productData of config.products) {
      const product = await createTestProduct(productData);
      createdProducts.push(product);
    }

    console.log('\n===== テスト用商品情報 =====');

    for (let i = 0; i < createdProducts.length; i++) {
      const product = createdProducts[i];

      console.log(`\n----- 商品${i + 1} -----`);
      console.log(`商品ID: ${product.id}`);
      console.log(`ShopifyID: ${product.shopifyId}`);
      console.log(`商品名: ${product.title}`);
      console.log(`SKU: ${product.sku}`);
      console.log(`価格: ${product.price}円`);
      console.log(`ステータス: ${product.status}`);
      console.log(`ロケーション: ${product.locationId}`);
    }

    console.log('\n以下のコマンドでテストを実行できます:');
    console.log(`npm run test:date-conflict ${createdProducts[0].shopifyId}`);
    console.log(`npm run test:holiday-pricing ${createdProducts[0].shopifyId}`);
    console.log(`npm run test:multi-product ${createdProducts[0].shopifyId} ${createdProducts[1].shopifyId}`);
  } catch (error) {
    console.error('スクリプト実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
