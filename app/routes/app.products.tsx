import React, { useState } from 'react';
import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate } from '@remix-run/react'; // Link を削除
import { authenticate } from '../shopify.server';
import {
  Page,
  LegacyCard,
  ResourceList,
  ResourceItem,
  Filters,
  Badge,
  EmptyState,
  Text,
  Pagination,
  // Button, ButtonGroup, Tooltip を削除
} from '@shopify/polaris';
import { CalendarIcon, ExportIcon, ImportIcon } from '@shopify/polaris-icons';
import { prisma } from '../db.server';
import { formatSkuWithHyphens } from '../utils/sku-formatter.js';

// 商品ステータスに応じたバッジ設定
const productStatusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  ACTIVE: { tone: 'success', label: '有効' },
  MAINTENANCE: { tone: 'attention', label: 'メンテナンス中' },
  INACTIVE: { tone: 'critical', label: '無効' },
  ARCHIVED: { tone: 'info', label: 'アーカイブ' }
};

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);

  // クエリパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  const limit = parseInt(url.searchParams.get('limit') || '50', 10);
  const status = url.searchParams.get('status') || undefined;
  // const category = url.searchParams.get('category') || undefined; // 未使用のためコメントアウト
  const query = url.searchParams.get('query') || undefined;

  // 検索条件を構築
  const where: any = {};

  if (status) {
    where.status = status;
  }

  // if (category) { // category フィルターを一時的にコメントアウト
  //   where.category = category;
  // }

  if (query) {
    where.OR = [
      { title: { contains: query, mode: 'insensitive' } },
      { sku: { contains: query } },
      { description: { contains: query, mode: 'insensitive' } },
      // { tags: { has: query } } // tags フィルターを一時的にコメントアウト
    ];
  }

  // 商品データを取得
  const products = await prisma.product.findMany({
    where,
    select: {
      id: true,
      shopifyId: true,
      title: true,
      sku: true,
      status: true,
      // category: true, // Productモデルに存在しないため削除
      price: true,
      // dailyPrice: true, // Productモデルに存在しないため削除
      // weeklyPrice: true, // Productモデルに存在しないため削除
      // monthlyPrice: true, // Productモデルに存在しないため削除
      // inventoryQuantity: true, // Productモデルに存在しないため削除
      locationId: true,
      // tags: true, // Productモデルに存在しないため削除
      updatedAt: true,
      // variants: { select: { id: true } }, // Productモデルに存在しないため削除
      maintenances: { // maintenanceRecords から maintenances に修正
        select: { startDate: true },
        where: {
          endDate: {
            gte: new Date()
          }
        },
        orderBy: {
          startDate: 'asc'
        },
        take: 1
      },
      bookings: {
        select: { startDate: true },
        where: {
          status: {
            in: ['CONFIRMED', 'PROVISIONAL']
          },
          endDate: {
            gte: new Date()
          }
        },
        orderBy: {
          startDate: 'asc'
        },
        take: 1
      }
    },
    orderBy: [
      { updatedAt: 'desc' }
    ],
    skip: (page - 1) * limit,
    take: limit
  });

  // 総件数を取得
  const total = await prisma.product.count({ where });

  // カテゴリ一覧を取得 // categoryフィールドが存在しないため一時的にコメントアウト
  // const categories = await prisma.product.groupBy({
  //   by: ['title'], // 仮にtitleでグループ化 (本来はcategory)
  //   _count: {
  //     title: true // 仮にtitleでカウント (本来はcategory)
  //   },
  //   orderBy: {
  //     title: 'asc' // 仮にtitleでソート (本来はcategory)
  //   }
  // });

  return json({
    products: products.map(product => ({
      id: product.id,
      shopifyId: product.shopifyId,
      title: product.title,
      sku: product.sku,
      status: product.status,
      // category: product.category || '', // Productモデルに存在しないためコメントアウト
      price: product.price?.toString() || '',
      // dailyPrice: product.dailyPrice?.toString() || '', // Productモデルに存在しないためコメントアウト
      // weeklyPrice: product.weeklyPrice?.toString() || '', // Productモデルに存在しないためコメントアウト
      // monthlyPrice: product.monthlyPrice?.toString() || '', // Productモデルに存在しないためコメントアウト
      // inventoryQuantity: product.inventoryQuantity || 0, // Productモデルに存在しないためコメントアウト
      location: product.locationId || '',
      // tags: product.tags || [], // Productモデルに存在しないためコメントアウト
      variantsCount: 0, // variantsが存在しないため0に設定（要仕様確認）
      nextMaintenance: product.maintenances?.length > 0  // オプショナルチェイニング追加
        ? product.maintenances[0].startDate.toISOString()
        : null,
      nextBooking: product.bookings?.length > 0 // オプショナルチェイニング追加
        ? product.bookings[0].startDate.toISOString()
        : null,
      updatedAt: product.updatedAt.toISOString()
    })),
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    },
    categories: [] // categories.map(c => ({ // categoryフィールドが存在しないため一時的に空配列
      // value: c.title || '', // 仮にtitleを使用 (本来はcategory)
      // count: c._count.title // 仮にtitleを使用 (本来はcategory)
    // }))
  });
}

export default function ProductsIndex() {
  const { products, pagination /*, categories */ } = useLoaderData<typeof loader>(); // categories を一時的にコメントアウト
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  // const [categoryFilter, setCategoryFilter] = useState(''); // category フィルターを一時的にコメントアウト

  // 商品詳細ページへ移動
  const handleProductClick = (productId: string) => {
    navigate(`/app/products/${productId}`);
  };

  // ステータスフィルターを適用
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    const params = new URLSearchParams(window.location.search);

    if (value) {
      params.set('status', value);
    } else {
      params.delete('status');
    }

    navigate(`/app/products?${params.toString()}`);
  };

  // カテゴリフィルターを適用 // category フィルターを一時的にコメントアウト
  // const handleCategoryFilterChange = (value: string) => {
  //   setCategoryFilter(value);
  //   const params = new URLSearchParams(window.location.search);

  //   if (value) {
  //     params.set('category', value);
  //   } else {
  //     params.delete('category');
  //   }

  //   navigate(`/app/products?${params.toString()}`);
  // };

  // 検索クエリを適用
  const handleSearchSubmit = () => {
    const params = new URLSearchParams(window.location.search);

    if (searchQuery) {
      params.set('query', searchQuery);
    } else {
      params.delete('query');
    }

    navigate(`/app/products?${params.toString()}`);
  };

  // ページネーション処理
  const handlePaginationChange = (newPage: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set('page', newPage.toString());
    navigate(`/app/products?${params.toString()}`);
  };

  // 日付をフォーマット
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  // フィルターオプションを生成
  const filters = [
    {
      key: 'status',
      label: 'ステータス',
      filter: (
        <select
          value={statusFilter}
          onChange={(e) => handleStatusFilterChange(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="">すべて</option>
          <option value="ACTIVE">有効</option>
          <option value="MAINTENANCE">メンテナンス中</option>
          <option value="INACTIVE">無効</option>
          <option value="ARCHIVED">アーカイブ</option>
        </select>
      ),
      shortcut: true,
    },
    // { // category フィルターを一時的にコメントアウト
    //   key: 'category',
    //   label: 'カテゴリ',
    //   filter: (
    //     <select
    //       value={categoryFilter}
    //       onChange={(e) => handleCategoryFilterChange(e.target.value)}
    //       style={{ width: '100%', padding: '8px' }}
    //     >
    //       <option value="">すべて</option>
    //       {categories.map(category => (
    //         <option key={category.value} value={category.value}>
    //           {category.value || '未分類'} ({category.count})
    //         </option>
    //       ))}
    //     </select>
    //   ),
    //   shortcut: true,
    // }
  ];

  return (
    <Page
      title="商品管理"
      primaryAction={{
        content: "新規商品",
        url: "/app/products/new"
      }}
      secondaryActions={[
        {
          content: "カレンダー表示",
          icon: CalendarIcon,
          url: "/app/products/calendar"
        },
        {
          content: "インポート",
          icon: ImportIcon,
          url: "/app/products/import"
        },
        {
          content: "エクスポート",
          icon: ExportIcon,
          url: "/app/products/export"
        }
      ]}
    >
      <LegacyCard>
        <ResourceList
          resourceName={{ singular: '商品', plural: '商品' }}
          items={products}
          renderItem={(product) => {
            const {
              id,
              title,
              sku,
              status,
              // category, // Productモデルに存在しないためコメントアウト
              price,
              // dailyPrice, // Productモデルに存在しないためコメントアウト
              // inventoryQuantity, // Productモデルに存在しないためコメントアウト
              location,
              nextMaintenance,
              nextBooking,
              variantsCount
            } = product;

            const badge = productStatusBadgeMap[status] || { tone: 'new', label: status };

            const shortcutActions = [
              {
                content: '詳細',
                url: `/app/products/${id}`,
              },
              {
                content: '編集',
                url: `/app/products/edit/${id}`,
              },
              {
                content: 'メンテナンス登録',
                url: `/app/maintenance/new?productId=${id}`,
              }
            ];

            return (
              <ResourceItem
                id={id}
                onClick={() => handleProductClick(id)}
                shortcutActions={shortcutActions}
                persistActions
              >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <Text as="h3" variant="bodyMd" fontWeight="bold">
                      {title}
                    </Text>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodyMd">
                        SKU: {formatSkuWithHyphens(sku)}
                      </Text>
                    </div>
                    <div style={{ marginTop: '4px', display: 'flex', gap: '8px' }}>
                      {/* {category && ( // Productモデルに存在しないためコメントアウト
                        <Text as="p" variant="bodySm" tone="subdued">
                          カテゴリ: {category}
                        </Text>
                      )} */}
                      {location && (
                        <Text as="p" variant="bodySm" tone="subdued">
                          場所: {location}
                        </Text>
                      )}
                      {variantsCount > 0 && (
                        <Text as="p" variant="bodySm" tone="subdued">
                          バリエーション: {variantsCount}
                        </Text>
                      )}
                    </div>
                    <div style={{ marginTop: '4px', display: 'flex', gap: '8px' }}>
                      {price && (
                        <Text as="p" variant="bodySm" tone="subdued">
                          価格: ¥{parseInt(price).toLocaleString()}
                        </Text>
                      )}
                      {/* {dailyPrice && ( // Productモデルに存在しないためコメントアウト
                        <Text as="p" variant="bodySm" tone="subdued">
                          日額: ¥{parseInt(dailyPrice).toLocaleString()}/日
                        </Text>
                      )} */}
                      {/* <Text as="p" variant="bodySm" tone="subdued"> // Productモデルに存在しないためコメントアウト
                        在庫: {inventoryQuantity}
                      </Text> */}
                    </div>
                    <div style={{ marginTop: '4px', display: 'flex', gap: '8px' }}>
                      {nextMaintenance && (
                        <Text as="p" variant="bodySm" tone="subdued">
                          次回メンテナンス: {formatDate(nextMaintenance)}
                        </Text>
                      )}
                      {nextBooking && (
                        <Text as="p" variant="bodySm" tone="subdued">
                          次回予約: {formatDate(nextBooking)}
                        </Text>
                      )}
                    </div>
                  </div>
                  <div>
                    <Badge tone={badge.tone}>{badge.label}</Badge>
                  </div>
                </div>
              </ResourceItem>
            );
          }}
          filterControl={
            <Filters
              queryValue={searchQuery}
              filters={filters}
              onQueryChange={setSearchQuery}
              onQueryClear={() => setSearchQuery('')}
              onClearAll={() => {
                setSearchQuery('');
                setStatusFilter('');
                // setCategoryFilter(''); // category フィルターを一時的にコメントアウト
                navigate('/app/products');
              }}
              onQueryBlur={handleSearchSubmit}
              hideQueryField={false}
            />
          }
          emptyState={
            <EmptyState
              heading="商品がありません"
              image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
            >
              <p>商品を登録して、レンタル商品を管理しましょう。</p>
            </EmptyState>
          }
        />
        {pagination.totalPages > 1 && (
          <div style={{ padding: '16px', display: 'flex', justifyContent: 'center' }}>
            <Pagination
              hasPrevious={pagination.page > 1}
              onPrevious={() => handlePaginationChange(pagination.page - 1)}
              hasNext={pagination.page < pagination.totalPages}
              onNext={() => handlePaginationChange(pagination.page + 1)}
            />
          </div>
        )}
      </LegacyCard>
      <div style={{ marginTop: '16px', textAlign: 'right' }}>
        <Text as="p" variant="bodySm" tone="subdued">
          合計 {pagination.total} 件の商品 ( {pagination.page} / {pagination.totalPages} ページ )
        </Text>
      </div>
    </Page>
  );
}
