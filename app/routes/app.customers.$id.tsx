import { json, type LoaderFunctionArgs, ActionFunctionArgs, redirect } from "@remix-run/node";
import { useLoaderData, useSubmit, useNavigate } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  BlockStack,
  Button,
  Badge,
  InlineStack,
  Banner,
  Modal,
  TextField,
  Divider,
  DescriptionList,
  Link,
  ButtonGroup,
  Icon,
  Tooltip,
  FormLayout
} from "@shopify/polaris";
import { DeleteIcon, EditIcon, OrderIcon } from "@shopify/polaris-icons";
import { useState, useCallback } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";

/**
 * 顧客詳細ページのローダー
 */
export async function loader({ request, params }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  const { id } = params;

  if (!id) {
    return redirect("/app/customers");
  }

  try {
    // 顧客データを取得
    const customer = await prisma.customer.findFirst({
      where: {
        id,
        shop,
      },
    });

    if (!customer) {
      return json({
        customer: null,
        bookings: [],
        error: "顧客データが見つかりません",
      });
    }

    // 顧客に関連する予約を別途取得
    // 名前またはメールアドレスで検索
    const bookings = await prisma.booking.findMany({
      where: {
        shop,
        OR: [
          { customerName: { contains: customer.name, mode: 'insensitive' } },
          { customerEmail: customer.email || "" }
        ]
      },
      select: {
        id: true,
        bookingId: true,
        startDate: true,
        endDate: true,
        status: true,
        productId: true,
        product: {
          select: {
            title: true,
            sku: true,
          },
        },
      },
      orderBy: {
        startDate: 'desc',
      },
      take: 5, // 最新の5件のみ取得
    });

    // 予約データをフォーマット
    const formattedBookings = bookings.map(booking => {
      return {
        id: booking.id,
        bookingId: booking.bookingId || "",
        startDate: booking.startDate ? new Date(booking.startDate).toLocaleDateString() : "",
        endDate: booking.endDate ? new Date(booking.endDate).toLocaleDateString() : "",
        status: booking.status,
        productTitle: booking.product?.title || "",
        productSku: booking.product?.sku || "",
      };
    });

    return json({
      customer: {
        ...customer,
        createdAt: customer.createdAt ? new Date(customer.createdAt).toISOString() : null,
        updatedAt: customer.updatedAt ? new Date(customer.updatedAt).toISOString() : null,
      },
      bookings: formattedBookings,
      shopifyShop: process.env.SHOPIFY_SHOP || shop,
      error: null,
    });
  } catch (error) {
    console.error("顧客データ取得エラー:", error);
    return json({
      customer: null,
      bookings: [],
      shopifyShop: process.env.SHOPIFY_SHOP || shop,
      error: "顧客データの取得中にエラーが発生しました",
    });
  }
}

/**
 * 顧客詳細ページのアクション
 */
export async function action({ request, params }: ActionFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  const { id } = params;

  if (!id) {
    return redirect("/app/customers");
  }

  const formData = await request.formData();
  const action = formData.get("action") as string;

  try {
    // 顧客データを取得
    const customer = await prisma.customer.findFirst({
      where: {
        id,
        shop,
      },
    });

    if (!customer) {
      return json({
        success: false,
        error: "顧客データが見つかりません",
      });
    }

    // アクションに応じた処理
    switch (action) {
      case "delete":
        // 顧客を削除
        await prisma.customer.delete({
          where: {
            id,
          },
        });
        return redirect("/app/customers");

      case "update":
        // 顧客情報を更新
        const name = formData.get("name") as string;
        const email = formData.get("email") as string;
        const phone = formData.get("phone") as string;
        const address = formData.get("address") as string;
        const notes = formData.get("notes") as string;

        if (!name) {
          return json({
            success: false,
            error: "顧客名は必須項目です",
          });
        }

        await prisma.customer.update({
          where: {
            id,
          },
          data: {
            name,
            email,
            phone,
            address,
            notes,
            updatedAt: new Date(),
          },
        });

        return json({
          success: true,
          message: "顧客情報を更新しました",
        });

      default:
        return json({
          success: false,
          error: "無効なアクションです",
        });
    }
  } catch (error) {
    console.error("顧客データ更新エラー:", error);
    return json({
      success: false,
      error: "顧客データの更新中にエラーが発生しました",
    });
  }
}

/**
 * 顧客詳細ページ
 */
export default function CustomerDetailPage() {
  const { customer, bookings, shopifyShop, error } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const navigate = useNavigate();

  // モーダルの状態
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);

  // 編集フォームの状態
  const [editName, setEditName] = useState(customer?.name || "");
  const [editEmail, setEditEmail] = useState(customer?.email || "");
  const [editPhone, setEditPhone] = useState(customer?.phone || "");
  const [editAddress, setEditAddress] = useState(customer?.address || "");
  const [editNotes, setEditNotes] = useState(customer?.notes || "");
  const [submitError, setSubmitError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 日時をフォーマット
  const formatDateTime = (dateString: string | null) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleString("ja-JP", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // 顧客を削除
  const handleDelete = () => {
    const formData = new FormData();
    formData.append("action", "delete");
    submit(formData, { method: "post" });
    setDeleteModalOpen(false);
  };

  // 顧客情報を更新
  const handleUpdate = useCallback(() => {
    if (!editName) {
      setSubmitError("顧客名を入力してください");
      return;
    }

    setIsSubmitting(true);
    setSubmitError("");

    const formData = new FormData();
    formData.append("action", "update");
    formData.append("name", editName);
    formData.append("email", editEmail);
    formData.append("phone", editPhone);
    formData.append("address", editAddress);
    formData.append("notes", editNotes);

    submit(formData, { method: "post" });
    setEditModalOpen(false);
    setIsSubmitting(false);
  }, [editName, editEmail, editPhone, editAddress, editNotes, submit]);

  // 編集モーダルを開く
  const openEditModal = () => {
    setEditName(customer?.name || "");
    setEditEmail(customer?.email || "");
    setEditPhone(customer?.phone || "");
    setEditAddress(customer?.address || "");
    setEditNotes(customer?.notes || "");
    setSubmitError("");
    setEditModalOpen(true);
  };

  // 予約ステータスに応じたバッジを生成
  const getStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case "DRAFT":
        return <Badge tone="info">ドラフト</Badge>;
      case "PROVISIONAL":
        return <Badge tone="attention">仮予約</Badge>;
      case "CONFIRMED":
        return <Badge tone="success">確定</Badge>;
      case "CANCELLED":
        return <Badge tone="critical">キャンセル</Badge>;
      case "COMPLETED":
        return <Badge tone="success">完了</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  if (error || !customer) {
    return (
      <Page
        title="顧客詳細"
        backAction={{ content: "顧客一覧に戻る", url: "/app/customers" }}
      >
        <Layout>
          <Layout.Section>
            <Card>
              <Box padding="400">
                <Banner tone="critical">
                  <p>{error || "顧客データが見つかりません"}</p>
                </Banner>
              </Box>
            </Card>
          </Layout.Section>
        </Layout>
      </Page>
    );
  }

  return (
    <Page
      title={`顧客詳細: ${customer.name}`}
      backAction={{ content: "顧客一覧に戻る", url: "/app/customers" }}
      secondaryActions={[
        {
          content: "予約一覧",
          icon: OrderIcon,
          url: `/app/bookings?q=${encodeURIComponent(customer.name)}`,
        },
      ]}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                <InlineStack gap="400" align="space-between" blockAlign="center">
                  <Text variant="headingMd" as="h2">
                    顧客情報
                  </Text>
                  <ButtonGroup>
                    {customer.shopifyId && (
                      <Button
                        url={`https://${shopifyShop}/admin/customers/${customer.shopifyId}`}
                        external
                      >
                        Shopifyで表示
                      </Button>
                    )}
                    <Button
                      onClick={openEditModal}
                      icon={EditIcon}
                    >
                      編集
                    </Button>
                    <Button
                      tone="critical"
                      onClick={() => setDeleteModalOpen(true)}
                      icon={DeleteIcon}
                    >
                      削除
                    </Button>
                  </ButtonGroup>
                </InlineStack>

                <Divider />

                <DescriptionList
                  items={[
                    {
                      term: "顧客名",
                      description: customer.name || "未設定",
                    },
                    {
                      term: "メールアドレス",
                      description: customer.email || "未設定",
                    },
                    {
                      term: "電話番号",
                      description: customer.phone || "未設定",
                    },
                    {
                      term: "住所",
                      description: customer.address || "未設定",
                    },
                    {
                      term: "Shopify顧客ID",
                      description: customer.shopifyId || "未設定",
                    },
                    {
                      term: "備考",
                      description: customer.notes || "なし",
                    },
                    {
                      term: "登録日時",
                      description: formatDateTime(customer.createdAt),
                    },
                    {
                      term: "更新日時",
                      description: formatDateTime(customer.updatedAt),
                    },
                  ]}
                />
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                <InlineStack gap="400" align="space-between" blockAlign="center">
                  <Text variant="headingMd" as="h2">
                    最近の予約
                  </Text>
                  <Button url={`/app/bookings?q=${encodeURIComponent(customer.name)}`}>
                    すべての予約を表示
                  </Button>
                </InlineStack>

                <Divider />

                {bookings.length === 0 ? (
                  <Box padding="400" textAlign="center">
                    <Text as="p" color="subdued">
                      予約データがありません
                    </Text>
                  </Box>
                ) : (
                  <BlockStack gap="400">
                    {bookings.map((booking) => (
                      <Card key={booking.id}>
                        <Box padding="400">
                          <InlineStack gap="400" align="space-between" blockAlign="center">
                            <BlockStack gap="200">
                              <Text variant="headingMd" as="h3">
                                {booking.productTitle} ({booking.productSku})
                              </Text>
                              <Text as="p">
                                {booking.startDate} 〜 {booking.endDate}
                              </Text>
                              <Text as="p">
                                ステータス: {getStatusBadge(booking.status)}
                              </Text>
                            </BlockStack>
                            <Button url={`/app/bookings/${booking.id}`}>
                              詳細を表示
                            </Button>
                          </InlineStack>
                        </Box>
                      </Card>
                    ))}
                  </BlockStack>
                )}
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>

      {/* 削除確認モーダル */}
      <Modal
        open={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="顧客を削除"
        primaryAction={{
          content: "削除",
          destructive: true,
          onAction: handleDelete,
        }}
        secondaryActions={[
          {
            content: "キャンセル",
            onAction: () => setDeleteModalOpen(false),
          },
        ]}
      >
        <Modal.Section>
          <Text as="p">
            この顧客を削除してもよろしいですか？この操作は取り消せません。
          </Text>
        </Modal.Section>
      </Modal>

      {/* 編集モーダル */}
      <Modal
        open={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title="顧客情報を編集"
        primaryAction={{
          content: "保存",
          onAction: handleUpdate,
          loading: isSubmitting,
        }}
        secondaryActions={[
          {
            content: "キャンセル",
            onAction: () => setEditModalOpen(false),
          },
        ]}
      >
        <Modal.Section>
          <BlockStack gap="400">
            {submitError && (
              <Banner tone="critical">
                <p>{submitError}</p>
              </Banner>
            )}

            <FormLayout>
              <TextField
                label="顧客名 *"
                value={editName}
                onChange={setEditName}
                autoComplete="off"
                required
              />

              <TextField
                label="メールアドレス"
                value={editEmail}
                onChange={setEditEmail}
                type="email"
                autoComplete="off"
              />

              <TextField
                label="電話番号"
                value={editPhone}
                onChange={setEditPhone}
                autoComplete="off"
              />

              <TextField
                label="住所"
                value={editAddress}
                onChange={setEditAddress}
                multiline={2}
                autoComplete="off"
              />

              <TextField
                label="備考"
                value={editNotes}
                onChange={setEditNotes}
                multiline={3}
                autoComplete="off"
              />
            </FormLayout>
          </BlockStack>
        </Modal.Section>
      </Modal>
    </Page>
  );
}
