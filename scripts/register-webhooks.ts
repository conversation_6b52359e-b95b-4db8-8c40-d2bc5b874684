/**
 * Webhookを登録するスクリプト
 * 
 * このスクリプトは、Shopify Admin APIを使用してWebhookを登録します。
 * 商品、顧客、注文の作成・更新・削除に関するWebhookを登録します。
 * 
 * 使用方法:
 * npx tsx scripts/register-webhooks.ts
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// Webhookサブスクリプションを作成するミューテーション
const CREATE_WEBHOOK_SUBSCRIPTION = gql`
  mutation webhookSubscriptionCreate($topic: WebhookSubscriptionTopic!, $webhookSubscription: WebhookSubscriptionInput!) {
    webhookSubscriptionCreate(topic: $topic, webhookSubscription: $webhookSubscription) {
      webhookSubscription {
        id
        topic
        endpoint {
          __typename
          ... on WebhookHttpEndpoint {
            callbackUrl
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * Webhookを登録する
 */
async function registerWebhook(topic: string, callbackUrl: string) {
  try {
    console.log(`「${topic}」のWebhookを登録中...`);
    
    const result = await client.request(CREATE_WEBHOOK_SUBSCRIPTION, {
      topic,
      webhookSubscription: {
        callbackUrl,
        format: "JSON"
      }
    });
    
    if (result.webhookSubscriptionCreate.userErrors.length > 0) {
      console.error(`「${topic}」のWebhook登録中にエラーが発生しました:`, result.webhookSubscriptionCreate.userErrors);
      return false;
    }
    
    console.log(`「${topic}」のWebhookを登録しました: ${result.webhookSubscriptionCreate.webhookSubscription.id}`);
    return true;
  } catch (error) {
    console.error(`「${topic}」のWebhook登録中にエラーが発生しました:`, error);
    return false;
  }
}

/**
 * メインの実行関数
 */
async function main() {
  try {
    console.log('Webhookの登録を開始します...');
    
    // アプリのホスト名を取得
    const appHost = process.env.HOST || 'https://shopify-app-test.xyz';
    
    // 登録するWebhookのトピックとURLのマッピング
    const webhooks = [
      // 商品関連
      { topic: 'PRODUCTS_CREATE', path: '/webhooks/products/create' },
      { topic: 'PRODUCTS_UPDATE', path: '/webhooks/products/update' },
      { topic: 'PRODUCTS_DELETE', path: '/webhooks/products/delete' },
      
      // 顧客関連
      { topic: 'CUSTOMERS_CREATE', path: '/webhooks/customers/create' },
      { topic: 'CUSTOMERS_UPDATE', path: '/webhooks/customers/update' },
      { topic: 'CUSTOMERS_DELETE', path: '/webhooks/customers/delete' },
      
      // 注文関連
      { topic: 'ORDERS_CREATE', path: '/webhooks/orders/create' },
      { topic: 'ORDERS_UPDATED', path: '/webhooks/orders/update' },
      { topic: 'ORDERS_CANCELLED', path: '/webhooks/orders/cancel' },
      
      // 下書き注文関連
      { topic: 'DRAFT_ORDERS_CREATE', path: '/webhooks/draft-orders/create' },
      { topic: 'DRAFT_ORDERS_UPDATE', path: '/webhooks/draft-orders/update' },
      { topic: 'DRAFT_ORDERS_DELETE', path: '/webhooks/draft-orders/delete' },
      
      // 在庫関連
      { topic: 'INVENTORY_LEVELS_UPDATE', path: '/webhooks/inventory/update' },
      
      // メタフィールド関連
      { topic: 'METAFIELDS_CREATE', path: '/webhooks/metafields/create' },
      { topic: 'METAFIELDS_UPDATE', path: '/webhooks/metafields/update' },
      { topic: 'METAFIELDS_DELETE', path: '/webhooks/metafields/delete' }
    ];
    
    // Webhookを登録
    const results = [];
    
    for (const webhook of webhooks) {
      const callbackUrl = `${appHost}${webhook.path}`;
      const success = await registerWebhook(webhook.topic, callbackUrl);
      results.push({ topic: webhook.topic, success });
    }
    
    // 結果の表示
    console.log('\n=== Webhook登録結果 ===');
    
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;
    
    console.log(`成功: ${successCount}件`);
    console.log(`失敗: ${failureCount}件`);
    
    if (failureCount > 0) {
      console.log('\n失敗したWebhook:');
      results.filter(r => !r.success).forEach(r => {
        console.log(`- ${r.topic}`);
      });
    }
    
    console.log('\nWebhookの登録が完了しました');
  } catch (error) {
    console.error('Webhook登録中にエラーが発生しました:', error);
  }
}

// スクリプトを実行
main()
  .then(() => console.log('\nスクリプトの実行が完了しました'))
  .catch((error) => console.error('予期しないエラーが発生しました:', error));
