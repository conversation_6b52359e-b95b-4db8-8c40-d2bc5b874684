// セッションテーブル確認用スクリプト
import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

dotenv.config();

async function checkSessions() {
  const prisma = new PrismaClient({
    log: ['query', 'info', 'warn', 'error'],
  });

  try {
    console.log('データベース接続を確認中...');
    await prisma.$connect();
    console.log('データベース接続成功');

    // セッションテーブルの構造を確認
    console.log('\nセッションテーブルの構造を確認中...');
    const tableInfo = await prisma.$queryRaw`
      SELECT column_name, data_type, column_default, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'sessions'
      ORDER BY ordinal_position;
    `;
    console.table(tableInfo);

    // セッションの総数を確認
    console.log('\nセッションの総数を確認中...');
    const sessionCount = await prisma.session.count();
    console.log(`セッション総数: ${sessionCount}`);

    // 特定のセッションを検索
    console.log('\n特定のセッションを検索中...');
    const offlineSession = await prisma.session.findUnique({
      where: {
        id: 'offline_peaces-test-block.myshopify.com',
      },
    });

    if (offlineSession) {
      console.log('オフラインセッションが見つかりました:');
      console.log(offlineSession);
    } else {
      console.log('オフラインセッションが見つかりませんでした');
      
      // 既存のセッションを確認
      console.log('\n既存のセッションを確認中...');
      const sessions = await prisma.session.findMany({
        take: 5,
      });
      
      if (sessions.length > 0) {
        console.log('最初の5つのセッション:');
        sessions.forEach((session, index) => {
          console.log(`\nセッション ${index + 1}:`);
          console.log(`ID: ${session.id}`);
          console.log(`Shop: ${session.shop}`);
          console.log(`State: ${session.state}`);
          console.log(`Expires: ${session.expires}`);
        });
      } else {
        console.log('セッションが見つかりませんでした');
      }
    }
  } catch (error) {
    console.error('エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSessions();
