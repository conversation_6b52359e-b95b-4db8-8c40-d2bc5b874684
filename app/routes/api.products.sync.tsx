import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { ShopifyProductService } from "../services/shopify.product.service";

/**
 * 商品同期API
 * POST /api/products/sync - 商品データを同期
 */
export async function action({ request }: ActionFunctionArgs) {
  // 認証チェック
  const { admin, session } = await authenticate.admin(request);
  
  try {
    // リクエストメソッドをチェック
    if (request.method !== "POST") {
      return json({ success: false, message: "Method not allowed" }, { status: 405 });
    }

    // Shopify商品サービスを初期化
    const productService = new ShopifyProductService(session);
    
    // 商品データを同期
    const syncResults = await productService.syncProductsWithDatabase(session.shop);
    
    return json({
      success: true,
      message: "商品データを同期しました",
      results: syncResults
    });
  } catch (error) {
    console.error("商品同期エラー:", error);
    return json({
      success: false,
      message: "商品データの同期中にエラーが発生しました",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
