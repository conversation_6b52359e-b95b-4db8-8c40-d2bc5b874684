import { json, type LoaderFunctionArgs } from '@remix-run/node';
import { useLoaderData, useSubmit } from '@remix-run/react';
import { useState, useCallback, useEffect } from 'react';
import { authenticate } from '../shopify.server';
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  BlockStack,
  Banner,
} from '@shopify/polaris';
import * as Polaris from '@shopify/polaris';
const { useToast } = Polaris;
import { PickingListManager } from '../components/Shipping/PickingListManager';
import { exportPickingListPDF } from '../utils/pdf/pdf-utils';
import { formatLocalDate } from '../utils/date/date-utils';

/**
 * ピッキングリスト管理画面
 */
export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);

  // スタッフ一覧を取得（実際の実装ではデータベースから取得）
  const staffList = [
    { id: 'staff1', name: '山田太郎' },
    { id: 'staff2', name: '佐藤次郎' },
    { id: 'staff3', name: '鈴木三郎' },
  ];

  return json({
    staffList,
  });
}

export default function PickingListPage() {
  const { staffList } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const { show } = useToast();

  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [pickingList, setPickingList] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // 日付変更時の処理
  const handleDateChange = useCallback(async (date: Date) => {
    setSelectedDate(date);
    await fetchPickingList(date);
  }, []);

  // ピッキングリストを取得
  const fetchPickingList = async (date: Date) => {
    setIsLoading(true);
    try {
      const formattedDate = formatLocalDate(date);
      const response = await fetch(`/api/shipping/picking-list?date=${formattedDate}`);
      const data = await response.json();

      if (data.success) {
        setPickingList(data.pickingList || []);
      } else {
        show({
          content: data.message || 'ピッキングリストの取得に失敗しました',
          error: true,
        });
        setPickingList([]);
      }
    } catch (error) {
      console.error('Error fetching picking list:', error);
      show({
        content: 'ピッキングリストの取得中にエラーが発生しました',
        error: true,
      });
      setPickingList([]);
    } finally {
      setIsLoading(false);
    }
  };

  // ステータス更新
  const handleUpdateStatus = async (id: string, status: string, staffId?: string, notes?: string) => {
    try {
      const response = await fetch(`/api/shipping/picking-list/${id}/update-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pickingStatus: status,
          pickingStaffId: staffId,
          notes,
        }),
      });

      const data = await response.json();

      if (data.success) {
        show({
          content: 'ステータスを更新しました',
        });
        // リストを再取得
        await fetchPickingList(selectedDate);
      } else {
        show({
          content: data.message || 'ステータスの更新に失敗しました',
          error: true,
        });
      }
    } catch (error) {
      console.error('Error updating status:', error);
      show({
        content: 'ステータスの更新中にエラーが発生しました',
        error: true,
      });
    }
  };

  // PDF出力
  const handleExportPDF = async (date: string) => {
    try {
      await exportPickingListPDF(date);
      show({
        content: 'PDFを出力しました',
      });
    } catch (error) {
      console.error('Error exporting PDF:', error);
      show({
        content: 'PDFの出力中にエラーが発生しました',
        error: true,
      });
    }
  };

  // 初回レンダリング時にピッキングリストを取得
  useEffect(() => {
    fetchPickingList(selectedDate);
  }, []);

  // スタッフリストをセレクトボックス用に変換
  const staffOptions = staffList.map((staff) => ({
    label: staff.name,
    value: staff.id,
  }));

  return (
    <Page
      title="ピッキングリスト管理"
      backAction={{ content: '配送管理へ戻る', url: '/app/shipping' }}
    >
      <Layout>
        <Layout.Section>
          <PickingListManager
            onDateChange={handleDateChange}
            onExportPDF={handleExportPDF}
            onUpdateStatus={handleUpdateStatus}
            pickingList={pickingList}
            selectedDate={selectedDate}
            isLoading={isLoading}
            staffList={staffOptions}
          />
        </Layout.Section>
      </Layout>
    </Page>
  );
}
