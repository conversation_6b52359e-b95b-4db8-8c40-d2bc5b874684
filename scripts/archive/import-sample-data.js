import { PrismaClient } from "@prisma/client";
import { readFileSync } from "fs";

const prisma = new PrismaClient();

async function importData() {
  try {
    console.log("データのインポートを開始します...");

    // バックアップファイルの読み込み
    const backupData = JSON.parse(
      readFileSync("backup_2025-03-30T03-58-39-629Z.json", "utf-8"),
    );

    // セッションデータのインポート
    if (backupData.sessions?.length > 0) {
      const sessions = backupData.sessions.map((session) => {
        const { onlineAccessInfo, createdAt, updatedAt, ...rest } = session;
        return rest;
      });

      await prisma.session.createMany({
        data: sessions,
        skipDuplicates: true,
      });
      console.log(
        `セッションデータを${backupData.sessions.length}件インポートしました`,
      );
    }

    // 商品データのインポート
    // 商品データのインポート
    if (backupData.products?.length > 0) {
      const products = backupData.products.map((product) => {
        return {
          title: product.title || "Untitled Product",
          price: 0, // デフォルト価格を設定
          description: product.description || null,
          status: "active",
        };
      });

      await prisma.product.createMany({
        data: products,
        skipDuplicates: true,
      });
      console.log(
        `商品データを${backupData.products.length}件インポートしました`,
      );
    }
    console.log("データのインポートが完了しました");
  } catch (error) {
    console.error("インポート中にエラーが発生しました:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトの実行
importData();
