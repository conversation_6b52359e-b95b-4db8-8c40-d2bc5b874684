import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';

const prisma = new PrismaClient();

interface DeliveryDestinationData {
  destinationCode: string;
  name: string;
  nameKana: string;
  postalCode?: string;
  address?: string;
  phone?: string;
  fax?: string;
  website?: string;
  notes?: string;
}

async function importDeliveryDestinations() {
  try {
    console.log('配送先マスタのインポートを開始します...');

    // CSVファイルのパス
    const csvFilePath = path.join(process.cwd(), 'master-data-csv/other-master-data/配送先一覧_202505192113.csv');
    
    if (!fs.existsSync(csvFilePath)) {
      console.error(`CSVファイルが見つかりません: ${csvFilePath}`);
      return;
    }

    const destinations: DeliveryDestinationData[] = [];

    // CSVファイルを読み込み
    await new Promise<void>((resolve, reject) => {
      fs.createReadStream(csvFilePath)
        .pipe(csv())
        .on('data', (row) => {
          const destination: DeliveryDestinationData = {
            destinationCode: row['配送先コード'] || '',
            name: row['配送先名'] || '',
            nameKana: row['配送先名カナ'] || '',
            postalCode: row['郵便番号'] || undefined,
            address: row['住所'] || undefined,
            phone: row['電話番号'] || undefined,
            fax: row['FAX番号'] || undefined,
            website: row['ホームページ'] || undefined,
            notes: row['備考'] || undefined
          };

          // 必須項目がある場合のみ追加
          if (destination.destinationCode && destination.name) {
            // 無効な電話番号をクリーンアップ
            if (destination.phone === '------------' || destination.phone === '00-0000-0000') {
              destination.phone = undefined;
            }
            
            // 無効な郵便番号をクリーンアップ
            if (destination.postalCode === '000-0000') {
              destination.postalCode = undefined;
            }

            // 空の値をundefinedに変換
            Object.keys(destination).forEach(key => {
              if (destination[key as keyof DeliveryDestinationData] === '') {
                destination[key as keyof DeliveryDestinationData] = undefined;
              }
            });

            destinations.push(destination);
          }
        })
        .on('end', resolve)
        .on('error', reject);
    });

    console.log(`${destinations.length}件の配送先データを読み込みました`);

    const shop = 'development.myshopify.com'; // 開発環境用

    // 既存データをクリア（開発環境のみ）
    if (process.env.NODE_ENV === 'development') {
      await prisma.deliveryDestination.deleteMany({
        where: { shop }
      });
      console.log('既存の配送先データをクリアしました');
    }

    // データをインポート
    let importedCount = 0;
    for (const destination of destinations) {
      try {
        await prisma.deliveryDestination.upsert({
          where: { 
            shop_destinationCode: {
              shop: shop,
              destinationCode: destination.destinationCode
            }
          },
          update: {
            name: destination.name,
            nameKana: destination.nameKana,
            postalCode: destination.postalCode,
            address: destination.address,
            phone: destination.phone,
            fax: destination.fax,
            website: destination.website,
            notes: destination.notes,
            isActive: true,
          },
          create: {
            shop: shop,
            destinationCode: destination.destinationCode,
            name: destination.name,
            nameKana: destination.nameKana,
            postalCode: destination.postalCode,
            address: destination.address,
            phone: destination.phone,
            fax: destination.fax,
            website: destination.website,
            notes: destination.notes,
            isActive: true,
          },
        });
        importedCount++;
      } catch (error) {
        console.error(`配送先コード ${destination.destinationCode} のインポートでエラー:`, error);
      }
    }

    console.log(`${importedCount}件の配送先データをインポートしました`);

    // インポート結果を確認
    const importedDestinations = await prisma.deliveryDestination.findMany({
      where: { shop },
      orderBy: { destinationCode: 'asc' },
      take: 10 // 最初の10件のみ表示
    });

    console.log('\n=== インポート結果（最初の10件） ===');
    importedDestinations.forEach((destination, index) => {
      console.log(`${index + 1}. ${destination.destinationCode}: ${destination.name}`);
      if (destination.address) {
        console.log(`   住所: ${destination.address}`);
      }
      if (destination.phone) {
        console.log(`   電話: ${destination.phone}`);
      }
      if (destination.notes) {
        const shortNotes = destination.notes.length > 50 
          ? destination.notes.substring(0, 50) + '...' 
          : destination.notes;
        console.log(`   備考: ${shortNotes}`);
      }
      console.log('');
    });

    // 統計情報
    const totalCount = await prisma.deliveryDestination.count({ where: { shop } });
    const withPhoneCount = await prisma.deliveryDestination.count({ 
      where: { shop, phone: { not: null } } 
    });
    const withNotesCount = await prisma.deliveryDestination.count({ 
      where: { shop, notes: { not: null } } 
    });

    console.log('=== 統計情報 ===');
    console.log(`総配送先数: ${totalCount}件`);
    console.log(`電話番号あり: ${withPhoneCount}件`);
    console.log(`備考あり: ${withNotesCount}件`);

  } catch (error) {
    console.error('配送先マスタのインポートでエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  importDeliveryDestinations();
}

export { importDeliveryDestinations };
