/**
 * 仮予約期限切れチェックサービス
 *
 * 期限切れの仮予約を自動的にキャンセルするスケジュールタスク
 */

import { PrismaClient } from '@prisma/client';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';
import { BookingService } from '../booking.service';
import { InventoryCalendarService } from '../inventory-calendar.service';
// 相対パスに修正
import { sendEmail } from '../../utils/email';
import { logger } from '../../utils/logger';

const prisma = new PrismaClient();

/**
 * 仮予約期限切れチェックサービス
 */
export class ProvisionalExpirationService {
  private bookingService: BookingService;
  private inventoryCalendarService: InventoryCalendarService;

  constructor() {
    this.bookingService = new BookingService();
    this.inventoryCalendarService = new InventoryCalendarService();
  }

  /**
   * 期限切れの仮予約をチェックして自動キャンセル
   */
  async checkExpiredProvisionalBookings(): Promise<{
    success: boolean;
    processedCount: number;
    errors: string[];
  }> {
    const errors: string[] = [];
    let processedCount = 0;

    try {
      logger.info('期限切れの仮予約をチェック中...');
      const now = new Date();

      // 期限切れの在庫カレンダーエントリを検索
      const expiredCalendarEntries = await prisma.inventoryCalendar.findMany({
        where: {
          bookingType: 'PROVISIONAL',
          expiresAt: {
            lt: now
          },
          isAvailable: false,
          unavailableReason: 'BOOKED'
        },
        orderBy: {
          expiresAt: 'asc'
        }
      });

      if (expiredCalendarEntries.length === 0) {
        logger.info('期限切れの仮予約はありません');
        return { success: true, processedCount: 0, errors: [] };
      }

      logger.info(`${expiredCalendarEntries.length}件の期限切れ仮予約エントリが見つかりました`);

      // 期限切れの予約IDを収集（重複を除去）
      const expiredBookingIds = [...new Set(
        expiredCalendarEntries
          .filter(entry => entry.bookingId)
          .map(entry => entry.bookingId as string)
      )];

      logger.info(`${expiredBookingIds.length}件の期限切れ仮予約が見つかりました`);

      // 各予約をキャンセル
      for (const bookingId of expiredBookingIds) {
        try {
          // 予約情報を取得
          const booking = await prisma.booking.findUnique({
            where: { id: bookingId },
            include: {
              product: true
            }
          });

          if (!booking) {
            logger.warn(`予約ID ${bookingId} が見つかりません`);
            errors.push(`予約ID ${bookingId} が見つかりません`);
            continue;
          }

          // 既にキャンセルされている場合はスキップ
          if (booking.status === 'CANCELLED' || booking.status === 'COMPLETED') {
            logger.info(`予約ID ${bookingId} は既にキャンセルまたは完了済みです`);
            continue;
          }

          // 仮予約でない場合はスキップ
          if (booking.bookingType !== 'PROVISIONAL') {
            logger.info(`予約ID ${bookingId} は仮予約ではありません`);
            continue;
          }

          logger.info(`期限切れの仮予約をキャンセル: ${bookingId} (${format(booking.startDate, 'yyyy/MM/dd')} 〜 ${format(booking.endDate, 'yyyy/MM/dd')})`);

          // 予約をキャンセル
          const cancelResult = await this.bookingService.cancelBooking(bookingId, {
            reason: '仮予約期限切れによる自動キャンセル',
            notes: `仮予約の有効期限（${format(booking.startDate, 'yyyy/MM/dd')}の7日前）が切れたため、自動的にキャンセルされました。`
          });

          if (cancelResult.success) {
            processedCount++;
            logger.info(`予約ID ${bookingId} のキャンセルが完了しました`);

            // 顧客にメール通知
            if (booking.customerEmail) {
              try {
                const emailResult = await sendEmail({
                  to: booking.customerEmail,
                  subject: `【お知らせ】仮予約が期限切れとなりました（予約番号: ${booking.bookingId}）`,
                  text: `
${booking.customerName || "お客様"}

いつもご利用ありがとうございます。

あなたの仮予約（予約番号: ${booking.bookingId}）が期限切れとなり、自動的にキャンセルされました。

■ 予約情報
予約番号: ${booking.bookingId}
商品名: ${booking.product.title}
予約期間: ${format(booking.startDate, 'yyyy年MM月dd日', { locale: ja })} 〜 ${format(booking.endDate, 'yyyy年MM月dd日', { locale: ja })}

引き続きご利用をご希望の場合は、改めてご予約をお願いいたします。

ご不明な点がございましたら、お気軽にお問い合わせください。

よろしくお願いいたします。
                  `,
                  html: `
<p>${booking.customerName || "お客様"}</p>

<p>いつもご利用ありがとうございます。</p>

<p>あなたの仮予約（予約番号: ${booking.bookingId}）が期限切れとなり、自動的にキャンセルされました。</p>

<h3>予約情報</h3>
<ul>
  <li>予約番号: ${booking.bookingId}</li>
  <li>商品名: ${booking.product.title}</li>
  <li>予約期間: ${format(booking.startDate, 'yyyy年MM月dd日', { locale: ja })} 〜 ${format(booking.endDate, 'yyyy年MM月dd日', { locale: ja })}</li>
</ul>

<p>引き続きご利用をご希望の場合は、改めてご予約をお願いいたします。</p>

<p>ご不明な点がございましたら、お気軽にお問い合わせください。</p>

<p>よろしくお願いいたします。</p>
                  `
                });

                logger.info(`顧客への通知メール送信: ${booking.customerEmail}`, emailResult);
              } catch (emailError) {
                logger.error(`顧客への通知メール送信エラー (${booking.id}):`, emailError);
                // メール送信エラーは処理を続行
              }
            }
          } else {
            logger.error(`予約ID ${bookingId} のキャンセルに失敗しました: ${cancelResult.error}`);
            errors.push(`予約ID ${bookingId} のキャンセルに失敗しました: ${cancelResult.error}`);
          }
        } catch (error) {
          logger.error(`予約ID ${bookingId} の処理中にエラーが発生しました:`, error);
          errors.push(`予約ID ${bookingId} の処理中にエラーが発生しました: ${error.message}`);
        }
      }

      // 管理者にまとめて通知
      if (processedCount > 0) {
        try {
          const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";

          const emailResult = await sendEmail({
            to: adminEmail,
            subject: `【通知】${processedCount}件の仮予約が期限切れでキャンセルされました`,
            text: `
${processedCount}件の仮予約が期限切れとなり、自動的にキャンセルされました。

管理画面から確認をお願いします。
            `,
            html: `
<h2>${processedCount}件の仮予約が期限切れとなり、自動的にキャンセルされました。</h2>

<p>管理画面から確認をお願いします。</p>
            `
          });

          logger.info(`管理者への通知メール送信: ${adminEmail}`, emailResult);
        } catch (emailError) {
          logger.error('管理者への通知メール送信エラー:', emailError);
          // メール送信エラーは処理を続行
        }
      }

      logger.info(`期限切れ仮予約の処理が完了しました: ${processedCount}件処理, ${errors.length}件エラー`);
      return { success: true, processedCount, errors };
    } catch (error) {
      logger.error('期限切れ仮予約チェック中にエラーが発生しました:', error);
      errors.push(`期限切れ仮予約チェック中にエラーが発生しました: ${error.message}`);
      return { success: false, processedCount, errors };
    }
  }
}
