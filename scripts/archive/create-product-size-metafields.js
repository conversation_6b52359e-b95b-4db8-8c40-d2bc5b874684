import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Shopify GraphQL APIのエンドポイント
const endpoint = `https://${process.env.SHOPIFY_SHOP}/admin/api/2023-10/graphql.json`;

// GraphQLクライアントの初期化
const client = new GraphQLClient(endpoint, {
  headers: {
    'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
    'Content-Type': 'application/json',
  },
});

// メタフィールド定義を作成するGraphQLミューテーション
const CREATE_METAFIELD_DEFINITION = gql`
  mutation metafieldDefinitionCreate($definition: MetafieldDefinitionInput!) {
    metafieldDefinitionCreate(definition: $definition) {
      createdDefinition {
        id
        name
        namespace
        key
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 商品サイズのメタフィールド定義を作成する関数
async function createProductSizeMetafields() {
  try {
    // 高さ (mm) のメタフィールド定義
    const heightDefinition = {
      name: "高さ",
      namespace: "product",
      key: "height",
      description: "商品の高さ (mm単位)",
      type: "number_integer",
      ownerType: "PRODUCT",
      validations: [
        {
          name: "min",
          value: "0"
        },
        {
          name: "max",
          value: "10000" // 10m = 10,000mm
        }
      ],
      visibleToStorefrontApi: true
    };

    // 幅 (mm) のメタフィールド定義
    const widthDefinition = {
      name: "幅",
      namespace: "product",
      key: "width",
      description: "商品の幅 (mm単位)",
      type: "number_integer",
      ownerType: "PRODUCT",
      validations: [
        {
          name: "min",
          value: "0"
        },
        {
          name: "max",
          value: "10000" // 10m = 10,000mm
        }
      ],
      visibleToStorefrontApi: true
    };

    // 奥行き (mm) のメタフィールド定義
    const depthDefinition = {
      name: "奥行き",
      namespace: "product",
      key: "depth",
      description: "商品の奥行き (mm単位)",
      type: "number_integer",
      ownerType: "PRODUCT",
      validations: [
        {
          name: "min",
          value: "0"
        },
        {
          name: "max",
          value: "10000" // 10m = 10,000mm
        }
      ],
      visibleToStorefrontApi: true
    };

    // 重量 (g) のメタフィールド定義
    const weightDefinition = {
      name: "重量",
      namespace: "product",
      key: "weight",
      description: "商品の重量 (g単位)",
      type: "number_integer",
      ownerType: "PRODUCT",
      validations: [
        {
          name: "min",
          value: "0"
        },
        {
          name: "max",
          value: "100000" // 100kg = 100,000g
        }
      ],
      visibleToStorefrontApi: true
    };

    // 各メタフィールド定義を作成
    console.log('高さのメタフィールド定義を作成中...');
    const heightResult = await client.request(CREATE_METAFIELD_DEFINITION, {
      definition: heightDefinition
    });
    console.log('高さのメタフィールド定義が作成されました:', heightResult.metafieldDefinitionCreate.createdDefinition?.id || 'エラー');

    console.log('幅のメタフィールド定義を作成中...');
    const widthResult = await client.request(CREATE_METAFIELD_DEFINITION, {
      definition: widthDefinition
    });
    console.log('幅のメタフィールド定義が作成されました:', widthResult.metafieldDefinitionCreate.createdDefinition?.id || 'エラー');

    console.log('奥行きのメタフィールド定義を作成中...');
    const depthResult = await client.request(CREATE_METAFIELD_DEFINITION, {
      definition: depthDefinition
    });
    console.log('奥行きのメタフィールド定義が作成されました:', depthResult.metafieldDefinitionCreate.createdDefinition?.id || 'エラー');

    console.log('重量のメタフィールド定義を作成中...');
    const weightResult = await client.request(CREATE_METAFIELD_DEFINITION, {
      definition: weightDefinition
    });
    console.log('重量のメタフィールド定義が作成されました:', weightResult.metafieldDefinitionCreate.createdDefinition?.id || 'エラー');

    // エラーチェック
    const checkErrors = (result, name) => {
      if (result.metafieldDefinitionCreate.userErrors.length > 0) {
        console.error(`${name}のメタフィールド定義作成中にエラーが発生しました:`, result.metafieldDefinitionCreate.userErrors);
      }
    };

    checkErrors(heightResult, '高さ');
    checkErrors(widthResult, '幅');
    checkErrors(depthResult, '奥行き');
    checkErrors(weightResult, '重量');

    console.log('すべてのメタフィールド定義が作成されました。');
  } catch (error) {
    console.error('エラーが発生しました:', error);
  }
}

// スクリプトの実行
createProductSizeMetafields();
