import React, { useState, useMemo } from 'react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isWithinInterval, isSameDay, addMonths, subMonths } from 'date-fns';
import { ja } from 'date-fns/locale';
import './RentalCalendar.css';

interface Booking {
  id: string;
  startDate: Date;
  endDate: Date;
  type: 'confirmed' | 'provisional';
}

interface MaintenancePeriod {
  id: string;
  startDate: Date;
  endDate: Date;
}

interface RentalCalendarProps {
  bookings?: Booking[];
  maintenancePeriods?: MaintenancePeriod[];
  selectedRange?: { start: Date | null; end: Date | null };
  onDateSelect?: (date: Date) => void;
  onRangeSelect?: (range: { start: Date; end: Date }) => void;
  minDate?: Date;
  maxDate?: Date;
  numberOfMonths?: number;
}

export const RentalCalendar: React.FC<RentalCalendarProps> = ({
  bookings = [],
  maintenancePeriods = [],
  selectedRange,
  onDateSelect,
  onRangeSelect,
  minDate,
  maxDate,
  numberOfMonths = 2,
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [hoveredDate, setHoveredDate] = useState<Date | null>(null);
  const [selecting, setSelecting] = useState(false);
  const [tempRange, setTempRange] = useState<{ start: Date | null; end: Date | null }>({ start: null, end: null });

  // 日本の祝日（簡易版）
  const getJapaneseHolidays = (year: number): Date[] => {
    return [
      new Date(year, 0, 1),    // 元日
      new Date(year, 1, 11),   // 建国記念日
      new Date(year, 2, 21),   // 春分の日
      new Date(year, 4, 3),    // 憲法記念日
      new Date(year, 4, 4),    // みどりの日
      new Date(year, 4, 5),    // こどもの日
      new Date(year, 7, 11),   // 山の日
      new Date(year, 8, 23),   // 秋分の日
      new Date(year, 10, 3),   // 文化の日
      new Date(year, 10, 23),  // 勤労感謝の日
      new Date(year, 11, 23),  // 天皇誕生日
    ];
  };

  // 日付の状態を判定
  const getDateStatus = (date: Date) => {
    const status = {
      isToday: isSameDay(date, new Date()),
      isSunday: date.getDay() === 0,
      isHoliday: false,
      isBooked: false,
      bookingType: null as 'confirmed' | 'provisional' | null,
      isInMaintenance: false,
      isSelected: false,
      isInRange: false,
      isDisabled: false,
    };

    // 祝日判定
    const holidays = getJapaneseHolidays(date.getFullYear());
    status.isHoliday = holidays.some(holiday => isSameDay(holiday, date));

    // 予約状態判定
    const booking = bookings.find(b => 
      isWithinInterval(date, { start: b.startDate, end: b.endDate })
    );
    if (booking) {
      status.isBooked = true;
      status.bookingType = booking.type;
    }

    // メンテナンス期間判定
    status.isInMaintenance = maintenancePeriods.some(period =>
      isWithinInterval(date, { start: period.startDate, end: period.endDate })
    );

    // 選択範囲判定
    if (selectedRange?.start && selectedRange?.end) {
      status.isSelected = isSameDay(date, selectedRange.start) || isSameDay(date, selectedRange.end);
      status.isInRange = isWithinInterval(date, { start: selectedRange.start, end: selectedRange.end });
    } else if (tempRange.start && tempRange.end) {
      status.isSelected = isSameDay(date, tempRange.start) || isSameDay(date, tempRange.end);
      status.isInRange = isWithinInterval(date, { start: tempRange.start, end: tempRange.end });
    }

    // 選択可能判定
    if (minDate && date < minDate) status.isDisabled = true;
    if (maxDate && date > maxDate) status.isDisabled = true;
    if (status.isBooked || status.isInMaintenance) status.isDisabled = true;

    return status;
  };

  // 日付クラス名生成
  const getDateClassName = (date: Date, status: ReturnType<typeof getDateStatus>) => {
    const classes = ['rental-calendar__day'];
    
    if (status.isToday) classes.push('rental-calendar__day--today');
    if (status.isSunday || status.isHoliday) classes.push('rental-calendar__day--holiday');
    if (status.isBooked) {
      classes.push('rental-calendar__day--booked');
      if (status.bookingType === 'confirmed') {
        classes.push('rental-calendar__day--confirmed');
      } else {
        classes.push('rental-calendar__day--provisional');
      }
    }
    if (status.isInMaintenance) classes.push('rental-calendar__day--maintenance');
    if (status.isSelected) classes.push('rental-calendar__day--selected');
    if (status.isInRange) classes.push('rental-calendar__day--in-range');
    if (status.isDisabled) classes.push('rental-calendar__day--disabled');
    
    return classes.join(' ');
  };

  // 日付クリックハンドラー
  const handleDateClick = (date: Date, status: ReturnType<typeof getDateStatus>) => {
    if (status.isDisabled) return;

    if (onRangeSelect) {
      if (!selecting || !tempRange.start) {
        setSelecting(true);
        setTempRange({ start: date, end: null });
      } else {
        const range = {
          start: tempRange.start < date ? tempRange.start : date,
          end: tempRange.start < date ? date : tempRange.start,
        };
        onRangeSelect(range);
        setSelecting(false);
        setTempRange({ start: null, end: null });
      }
    } else if (onDateSelect) {
      onDateSelect(date);
    }
  };

  // 月のカレンダーをレンダリング
  const renderMonth = (monthDate: Date, monthIndex: number) => {
    const start = startOfMonth(monthDate);
    const end = endOfMonth(monthDate);
    const days = eachDayOfInterval({ start, end });
    
    // 月の最初の日の曜日を取得（日曜日を週の始まりとする）
    const startDayOfWeek = start.getDay();
    
    // 前月の日付で埋める
    const prevMonthDays = [];
    for (let i = startDayOfWeek - 1; i >= 0; i--) {
      const date = new Date(start);
      date.setDate(date.getDate() - i - 1);
      prevMonthDays.push(date);
    }
    
    // 次月の日付で埋める
    const totalCells = 42; // 6週間分
    const nextMonthDays = [];
    const currentCells = prevMonthDays.length + days.length;
    for (let i = 1; i <= totalCells - currentCells; i++) {
      const date = new Date(end);
      date.setDate(date.getDate() + i);
      nextMonthDays.push(date);
    }

    const allDays = [...prevMonthDays, ...days, ...nextMonthDays];

    return (
      <div key={monthIndex} className="rental-calendar__month">
        <h3 className="rental-calendar__month-title">
          {format(monthDate, 'yyyy年M月', { locale: ja })}
        </h3>
        <table className="rental-calendar__table">
          <thead>
            <tr>
              {['日', '月', '火', '水', '木', '金', '土'].map((day, index) => (
                <th key={day} className={index === 0 ? 'rental-calendar__weekday--sunday' : ''}>
                  {day}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {Array.from({ length: 6 }).map((_, weekIndex) => (
              <tr key={weekIndex}>
                {allDays.slice(weekIndex * 7, (weekIndex + 1) * 7).map((date, dayIndex) => {
                  const status = getDateStatus(date);
                  const isCurrentMonth = date.getMonth() === monthDate.getMonth();
                  
                  return (
                    <td key={dayIndex} className={!isCurrentMonth ? 'rental-calendar__cell--other-month' : ''}>
                      <button
                        className={getDateClassName(date, status)}
                        onClick={() => handleDateClick(date, status)}
                        onMouseEnter={() => setHoveredDate(date)}
                        onMouseLeave={() => setHoveredDate(null)}
                        disabled={status.isDisabled}
                        aria-label={format(date, 'yyyy年M月d日', { locale: ja })}
                      >
                        <span className="rental-calendar__day-number">{date.getDate()}</span>
                        {status.isBooked && (
                          <span className="rental-calendar__marker">
                            {status.bookingType === 'confirmed' ? '予' : '仮'}
                          </span>
                        )}
                        {status.isInMaintenance && (
                          <span className="rental-calendar__marker">メ</span>
                        )}
                        {(status.isSunday || status.isHoliday) && !status.isBooked && !status.isInMaintenance && (
                          <span className="rental-calendar__marker">休</span>
                        )}
                      </button>
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  // 表示する月のリストを生成
  const monthsToDisplay = useMemo(() => {
    const months = [];
    for (let i = 0; i < numberOfMonths; i++) {
      months.push(addMonths(currentMonth, i));
    }
    return months;
  }, [currentMonth, numberOfMonths]);

  return (
    <div className="rental-calendar">
      <div className="rental-calendar__header">
        <button
          className="rental-calendar__nav-button"
          onClick={() => setCurrentMonth(subMonths(currentMonth, 1))}
          aria-label="前月"
        >
          ←
        </button>
        <button
          className="rental-calendar__nav-button"
          onClick={() => setCurrentMonth(addMonths(currentMonth, 1))}
          aria-label="次月"
        >
          →
        </button>
      </div>
      
      <div className="rental-calendar__months">
        {monthsToDisplay.map((month, index) => renderMonth(month, index))}
      </div>
      
      <div className="rental-calendar__legend">
        <div className="rental-calendar__legend-item">
          <span className="rental-calendar__legend-marker rental-calendar__legend-marker--confirmed">予</span>
          <span>確定予約</span>
        </div>
        <div className="rental-calendar__legend-item">
          <span className="rental-calendar__legend-marker rental-calendar__legend-marker--provisional">仮</span>
          <span>仮予約</span>
        </div>
        <div className="rental-calendar__legend-item">
          <span className="rental-calendar__legend-marker rental-calendar__legend-marker--maintenance">メ</span>
          <span>メンテナンス</span>
        </div>
        <div className="rental-calendar__legend-item">
          <span className="rental-calendar__legend-marker rental-calendar__legend-marker--holiday">休</span>
          <span>休業日</span>
        </div>
      </div>
    </div>
  );
};