<!doctype html>
{%- if shop.locale == 'ar' or shop.locale == 'he' -%}
<!--[if IE 9]> <html class="ie9 no-js supports-no-cookies" lang="{{ shop.locale }}" dir="rtl"> <![endif]-->
<!--[if (gt IE 9)|!(IE)]><!--> <html class="no-js supports-no-cookies" lang="{{ shop.locale }}" dir="rtl"> <!--<![endif]-->
{%- else -%}
<!--[if IE 9]> <html class="ie9 no-js supports-no-cookies" lang="{{ shop.locale }}"{% if settings.layout_enable_rtl %} dir="rtl"{% endif %}> <![endif]-->
<!--[if (gt IE 9)|!(IE)]><!--> <html class="no-js supports-no-cookies" lang="{{ shop.locale }}"{% if settings.layout_enable_rtl %} dir="rtl"{% endif %}> <!--<![endif]-->
{%- endif -%}
<head>
  {% capture get_content_for_header %}{{ content_for_header }}{% endcapture %}
  {% include 'head' %}
</head>
<body{% render 'layout-get-body-attributes' %}>
{% include 'layout-get-includes-start' %}
{% include 'password-page-header' %}
<main role="main" id="MainContent">
  <div class="container">
    {{ content_for_layout }}
  </div>
</main>
{% section 'password-page-footer' %}
{% include 'popups' %}
{% include 'layout-get-includes-end' %}
</body>
</html>


