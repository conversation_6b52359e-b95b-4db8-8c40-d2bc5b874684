# メタフィールド実用的改善案

**作成日**: 2025年5月25日  
**目的**: スタッフが実際に使いやすいメタフィールド構造の提案

## 🔍 現状の問題点

1. **JSONフィールドはスタッフが編集困難**
   - Shopify管理画面でJSONを直接編集するのは現実的でない
   - ミスが発生しやすい

2. **重複する可能性のあるフィールド**
   - `rental.status`（単一行テキスト）: 商品の利用可能状態
   - `inventory_items`（JSON）: 在庫アイテムの詳細情報
   - 用途が異なるが混乱を招く可能性

## 📋 実用的なメタフィールド構成案

### **基本情報（スタッフ編集可能）**

#### 1. **商品識別情報**
- `rental.product_code` (single_line_text_field) - 型番
- `rental.detail_code` (single_line_text_field) - 個体番号
- `rental.color` (single_line_text_field) - 色
- `rental.material` (single_line_text_field) - 素材

#### 2. **在庫・状態管理**
- `rental.status` (single_line_text_field) - 利用可能状態
  - 値: "available", "maintenance", "reserved", "damaged"
- `rental.location` (single_line_text_field) - 保管場所
  - 値: "NY", "PR", "WAREHOUSE_A"

#### 3. **商品仕様**
- `product.height` (number_integer) - 高さ(cm)
- `product.width` (number_integer) - 幅(cm)
- `product.depth` (number_integer) - 奥行き(cm)

#### 4. **メモ・備考（複数行テキスト）**
- `rental.maintenance_notes` (multi_line_text_field) - メンテナンス備考
- `rental.booking_notes` (multi_line_text_field) - 予約時の注意事項
- `rental.general_notes` (multi_line_text_field) **【新規】** - 一般備考
  ```
  用途：
  - 購入情報: 購入日: 2020/01/01, 購入場所: 東京, 購入価格: 50,000円
  - 廃棄情報: 廃棄日: 2025/05/01, 理由: 破損のため
  - その他メモ
  ```

### **システム管理用（JSON - 自動設定）**

#### 5. **価格設定**
- `rental.pricing` (JSON) - 料金計算用データ
  ```json
  {
    "basePrice": 8000,
    "discountRules": {
      "day2_6_rate": 0.2,
      "day7_plus_rate": 0.1
    }
  }
  ```

#### 6. **バリアント管理**
- `rental.variant_mapping` (JSON) - バリアントマッピング
  ```json
  {
    "variants": [
      {
        "variantId": "gid://shopify/ProductVariant/xxx",
        "rentalDays": 1,
        "title": "1日レンタル",
        "sku": "10101031-001-1D",
        "price": 8000
      }
    ]
  }
  ```

#### 7. **基本情報（CSV一括登録用）**
- `rental.basic_info` (JSON) - システム情報
  ```json
  {
    "productCode": "10101031",
    "detailCode": "001",
    "kana": "カリモクソファ",
    "importedAt": "2025-05-25",
    "sourceFile": "商品一覧_202505031329-sofa.csv"
  }
  ```

## 🔧 inventory_itemsとrental.statusの違い

### **rental.status（推奨）**
- **用途**: 現在の商品状態を簡潔に表示
- **値**: "available", "maintenance", "reserved", "damaged"
- **編集**: スタッフが管理画面で直接変更可能
- **表示**: 商品一覧で即座に確認可能

### **inventory_items（廃止推奨）**
- **用途**: 複数の在庫アイテムの詳細管理（複雑）
- **問題**: JSONなので編集が困難
- **代替案**: rental.statusとrental.general_notesで十分

## 📊 variant_mappingの活用方法

### **テスト実装案**

```javascript
// 1. 商品作成時に自動生成
async function generateVariantMapping(product) {
  const variants = await getProductVariants(product.id);
  const mapping = {
    variants: variants.map(v => ({
      variantId: v.id,
      rentalDays: extractDaysFromTitle(v.title),
      title: v.title,
      sku: v.sku,
      price: v.price
    }))
  };
  
  await setMetafield(product.id, 'rental', 'variant_mapping', mapping);
}

// 2. 予約時に適切なバリアントを選択
async function selectVariantForBooking(productId, rentalDays) {
  const mapping = await getMetafield(productId, 'rental', 'variant_mapping');
  
  // 8日以上は8D+バリアントを使用
  const targetDays = rentalDays > 8 ? '8plus' : rentalDays;
  
  return mapping.variants.find(v => v.rentalDays === targetDays);
}
```

### **活用メリット**
1. バリアントIDと日数の関連付けが明確
2. 予約時の自動バリアント選択が可能
3. 価格計算の一元管理

## 📝 新商品登録フロー

### **1. CSV一括登録（初期登録）**
```csv
商品コード,詳細コード,商品名,カナ,カラー,素材,価格,場所,高さ,幅,奥行き,備考
10101031,001,カリモクソファ,カリモクソファ,モケットグリーン,布,8000,NY,70,61,66,2シーター1台あり
```

### **2. 自動設定されるメタフィールド**
- basic_info（JSON）
- pricing（JSON）
- variant_mapping（JSON）
- variation_type: "rental_period"

### **3. スタッフが管理画面で設定**
- status: "available"
- maintenance_notes: 必要に応じて
- booking_notes: 必要に応じて
- general_notes: 購入情報など

## 🎯 最終的なメタフィールド数

### **スタッフ編集可能（11個）**
1. product_code, detail_code
2. color, material
3. status, location
4. height, width, depth
5. maintenance_notes, booking_notes, general_notes

### **システム自動管理（4個）**
1. basic_info
2. pricing
3. variant_mapping
4. variation_type

**合計: 15個**（管理しやすい構成）

## 💡 重要ポイント

1. **JSONは自動設定のみ**
   - スタッフは単一行・複数行テキストのみ編集
   - 複雑な情報はgeneral_notesに自由記述

2. **inventory_itemsは廃止**
   - rental.statusで十分
   - 詳細情報はgeneral_notesに記載

3. **variant_mappingは有用**
   - 予約時の自動化に活用
   - ただし自動生成・自動利用のみ

4. **新商品登録はCSV + 管理画面**
   - CSVで基本情報を一括登録
   - 細かい調整は管理画面で