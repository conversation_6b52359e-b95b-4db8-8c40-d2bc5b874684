/**
 * 顧客同期状況確認スクリプト
 *
 * このスクリプトは、Shopifyの顧客データとPrismaデータベースの顧客データの同期状況を確認します。
 */

import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

/**
 * 顧客データを確認する関数
 * @param searchTerm 検索キーワード
 */
async function checkCustomers(searchTerm: string) {
  try {
    console.log(`Shopifyで「${searchTerm}」を検索中...`);

    // 顧客検索のGraphQLクエリ
    const SEARCH_CUSTOMERS = gql`
      query searchCustomers($query: String!, $first: Int!) {
        customers(first: $first, query: $query) {
          edges {
            node {
              id
              firstName
              lastName
              email
              phone
              createdAt
              updatedAt
            }
          }
        }
      }
    `;

    // Shopify GraphQL APIを使用して顧客を検索
    const response = await shopifyClient.request(SEARCH_CUSTOMERS, {
      query: searchTerm,
      first: 10
    });

    // 検索結果を表示
    const customers = response.customers.edges;
    console.log(`Shopifyで ${customers.length} 件の顧客が見つかりました`);

    if (customers.length > 0) {
      console.log('\n=== Shopify顧客一覧 ===');
      customers.forEach((edge: any, index: number) => {
        const customer = edge.node;
        console.log(`${index + 1}. ${customer.firstName} ${customer.lastName}`);
        console.log(`   ID: ${customer.id}`);
        console.log(`   Email: ${customer.email}`);
        console.log(`   電話: ${customer.phone || 'なし'}`);
        console.log(`   作成日時: ${customer.createdAt}`);
        console.log('---');
      });
    }

    // Prismaデータベースで同じ顧客を検索
    console.log(`\nデータベースで「${searchTerm}」を検索中...`);
    const dbCustomers = await prisma.customer.findMany({
      where: {
        OR: [
          {
            name: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
          {
            email: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
        ],
      },
    });

    console.log(`データベースで ${dbCustomers.length} 件の顧客が見つかりました`);

    if (dbCustomers.length > 0) {
      console.log('\n=== データベース顧客一覧 ===');
      dbCustomers.forEach((customer, index) => {
        console.log(`${index + 1}. ${customer.name}`);
        console.log(`   ID: ${customer.id}`);
        console.log(`   Shopify ID: ${customer.shopifyId || 'なし'}`);
        console.log(`   Email: ${customer.email}`);
        console.log(`   電話: ${customer.phone || 'なし'}`);
        console.log(`   作成日時: ${customer.createdAt}`);
        console.log('---');
      });
    }

    // 同期状況の確認
    console.log('\n=== 同期状況 ===');
    if (customers.length === 0 && dbCustomers.length === 0) {
      console.log('顧客が見つかりませんでした。');
    } else if (customers.length > 0 && dbCustomers.length === 0) {
      console.log('Shopifyには顧客が存在しますが、データベースには同期されていません。');
      console.log('顧客同期を実行する必要があります。');
    } else if (customers.length === 0 && dbCustomers.length > 0) {
      console.log('データベースには顧客が存在しますが、Shopifyには存在しません。');
      console.log('データベースのクリーンアップが必要かもしれません。');
    } else {
      console.log('Shopifyとデータベースの両方に顧客が存在します。');

      // ShopifyとDBの顧客IDを比較
      const shopifyIds = customers.map((edge: any) => edge.node.id.replace('gid://shopify/Customer/', ''));
      const dbShopifyIds = dbCustomers.map(customer => customer.shopifyId).filter(id => id);

      const inShopifyNotInDb = shopifyIds.filter(id => !dbShopifyIds.includes(id));
      const inDbNotInShopify = dbShopifyIds.filter(id => !shopifyIds.includes(id));

      if (inShopifyNotInDb.length > 0) {
        console.log('\n以下のShopify顧客はデータベースに同期されていません:');
        inShopifyNotInDb.forEach(id => {
          const customer = customers.find((edge: any) => edge.node.id.replace('gid://shopify/Customer/', '') === id);
          if (customer) {
            console.log(`- ${customer.node.firstName} ${customer.node.lastName} (ID: ${id})`);
          }
        });
      }

      if (inDbNotInShopify.length > 0) {
        console.log('\n以下のデータベース顧客はShopifyに存在しません:');
        inDbNotInShopify.forEach(id => {
          const customer = dbCustomers.find(c => c.shopifyId === id);
          if (customer) {
            console.log(`- ${customer.name} (Shopify ID: ${id})`);
          }
        });
      }
    }

  } catch (error) {
    console.error('エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// コマンドライン引数から検索キーワードを取得
const searchTerm = process.argv[2] || '';

// 顧客検索を実行
checkCustomers(searchTerm)
  .then(() => console.log('検索完了'))
  .catch(error => console.error('検索エラー:', error));
