/**
 * Shopify顧客参照テスト
 *
 * このスクリプトは、顧客IDのみを保存し、必要時にShopify APIから顧客情報を
 * 取得する方式が実行可能かを検証します。
 *
 * テスト内容:
 * 1. 顧客IDから顧客情報（名前、メール等）を取得
 * 2. 顧客名やメールで検索して顧客を見つける
 * 3. 取得・検索のパフォーマンスを測定
 * 4. キャッシュ機能の効果測定
 * 5. 顧客ID形式の互換性テスト
 *
 * 実行方法:
 * ```
 * npm run test:customer-reference
 * ```
 */

import { CustomerReferenceService, CustomerInfo } from '../app/services/customer-reference.service';
import dotenv from 'dotenv';
import { performance } from 'perf_hooks';
import fetch from 'node-fetch';

// 環境変数の読み込み
dotenv.config();

// テスト設定
const TEST_ITERATIONS = 5; // 繰り返し回数（平均パフォーマンス測定用）
const CUSTOMER_LIMIT = 10; // 取得する顧客数

/**
 * 顧客参照サービスのテスト用クラス
 * テスト用に一部メソッドをオーバーライド
 */
class TestCustomerReferenceService extends CustomerReferenceService {
  constructor() {
    // テスト用のダミーセッションを作成
    const dummySession = {
      shop: process.env.SHOPIFY_SHOP || '',
      accessToken: process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      state: '',
      isOnline: true,
      scope: ''
    };

    super(dummySession as any);
  }
}

// 環境設定の読み込み
const config = {
  shop: process.env.SHOPIFY_SHOP || '',
  accessToken: process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
  apiVersion: process.env.SHOPIFY_API_VERSION || '2024-01'
};

// メイン実行関数
async function runTests() {
  console.log('=== Shopify顧客参照テスト開始 ===');
  console.log(`テスト環境: ${process.env.SHOPIFY_SHOP}`);

  try {
    // 顧客参照サービスの初期化
    const customerService = new TestCustomerReferenceService();

    // テスト1: 顧客一覧を取得して最初の数件をテスト対象とする
    console.log('\n--- テスト1: 顧客一覧取得 ---');
    const customers = await getCustomersList(CUSTOMER_LIMIT);

    if (!customers || customers.length === 0) {
      throw new Error('テスト用の顧客データが見つかりません。Shopifyストアに顧客が登録されているか確認してください。');
    }

    console.log(`${customers.length}件の顧客データを取得しました。`);
    console.log('最初の顧客:', {
      id: customers[0].id,
      name: `${customers[0].firstName} ${customers[0].lastName}`,
      email: customers[0].email
    });

    // テスト2: 顧客IDから顧客情報を取得
    console.log('\n--- テスト2: 顧客IDから情報取得 ---');
    const testCustomer = customers[0];
    const customerId = testCustomer.id.replace('gid://shopify/Customer/', '');

    console.log(`テスト対象顧客: ${testCustomer.firstName} ${testCustomer.lastName} (ID: ${customerId})`);

    // キャッシュなしで取得（初回）
    console.log('\n初回取得（キャッシュなし）:');
    const startTime1 = performance.now();
    const customerData1 = await customerService.getCustomerById(customerId, true); // forceRefresh=true
    const endTime1 = performance.now();

    console.log(`取得時間: ${(endTime1 - startTime1).toFixed(2)}ms`);
    console.log('取得データ:', JSON.stringify(customerData1, null, 2));

    // キャッシュありで複数回取得（パフォーマンステスト）
    console.log('\nキャッシュありで複数回取得:');
    let totalTime = 0;

    for (let i = 0; i < TEST_ITERATIONS; i++) {
      const startTime = performance.now();
      await customerService.getCustomerById(customerId);
      const endTime = performance.now();
      const elapsed = endTime - startTime;

      console.log(`  取得 ${i+1}: ${elapsed.toFixed(2)}ms`);
      totalTime += elapsed;
    }

    console.log(`平均取得時間: ${(totalTime / TEST_ITERATIONS).toFixed(2)}ms`);

    // テスト3: 顧客名で検索
    console.log('\n--- テスト3: 顧客名で検索 ---');
    const searchName = `${testCustomer.firstName} ${testCustomer.lastName}`;
    console.log(`検索キーワード: "${searchName}"`);

    const startTimeSearch1 = performance.now();
    const searchResults1 = await customerService.searchCustomers(searchName);
    const endTimeSearch1 = performance.now();

    console.log(`検索時間: ${(endTimeSearch1 - startTimeSearch1).toFixed(2)}ms`);
    console.log(`検索結果: ${searchResults1.length}件`);

    if (searchResults1.length > 0) {
      console.log('最初の検索結果:', JSON.stringify(searchResults1[0], null, 2));
    }

    // テスト4: メールアドレスで検索
    console.log('\n--- テスト4: メールアドレスで検索 ---');
    const searchEmail = testCustomer.email;
    console.log(`検索キーワード: "${searchEmail}"`);

    const startTimeSearch2 = performance.now();
    const searchResults2 = await customerService.searchCustomers(searchEmail);
    const endTimeSearch2 = performance.now();

    console.log(`検索時間: ${(endTimeSearch2 - startTimeSearch2).toFixed(2)}ms`);
    console.log(`検索結果: ${searchResults2.length}件`);

    if (searchResults2.length > 0) {
      console.log('最初の検索結果:', JSON.stringify(searchResults2[0], null, 2));
    }

    // テスト4-2: スペースなしの日本語名で検索
    if (testCustomer.firstName && testCustomer.lastName) {
      console.log('\n--- テスト4-2: スペースなしの日本語名で検索 ---');
      const noSpaceName = `${testCustomer.lastName}${testCustomer.firstName}`;
      console.log(`検索キーワード: "${noSpaceName}"`);

      const startTimeSearch3 = performance.now();
      const searchResults3 = await customerService.searchCustomers(noSpaceName);
      const endTimeSearch3 = performance.now();

      console.log(`検索時間: ${(endTimeSearch3 - startTimeSearch3).toFixed(2)}ms`);
      console.log(`検索結果: ${searchResults3.length}件`);

      if (searchResults3.length > 0) {
        console.log('最初の検索結果:', JSON.stringify(searchResults3[0], null, 2));
      }
    }

    // テスト5: 顧客IDの形式テスト
    console.log('\n--- テスト5: 顧客ID形式テスト ---');

    // 通常の数値ID
    console.log('数値IDでの取得:');
    const startTimeId1 = performance.now();
    const idResult1 = await customerService.getCustomerById(customerId);
    const endTimeId1 = performance.now();
    console.log(`取得時間: ${(endTimeId1 - startTimeId1).toFixed(2)}ms`);
    console.log(`結果: ${idResult1 ? '成功' : '失敗'}`);

    // GID形式
    console.log('GID形式での取得:');
    const gid = `gid://shopify/Customer/${customerId}`;
    const startTimeId2 = performance.now();
    const idResult2 = await customerService.getCustomerById(gid);
    const endTimeId2 = performance.now();
    console.log(`取得時間: ${(endTimeId2 - startTimeId2).toFixed(2)}ms`);
    console.log(`結果: ${idResult2 ? '成功' : '失敗'}`);

    console.log('\n=== テスト完了 ===');
    console.log('結果: すべてのテストが正常に完了しました。');
    console.log('顧客IDから顧客情報の取得、および顧客名・メールでの検索が正常に機能しています。');

  } catch (error) {
    console.error('\n=== テストエラー ===');
    console.error('エラー詳細:', error);
  }
}

// 顧客一覧を取得
async function getCustomersList(limit: number): Promise<any[]> {
  try {
    const query = `
      query {
        customers(first: ${limit}) {
          edges {
            node {
              id
              firstName
              lastName
              email
              phone
            }
          }
        }
      }
    `;

    const response = await fetch(`https://${config.shop}/admin/api/${config.apiVersion}/graphql.json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': config.accessToken
      },
      body: JSON.stringify({ query })
    });

    const responseJson = await response.json();

    if (responseJson.errors) {
      throw new Error(`GraphQL エラー: ${JSON.stringify(responseJson.errors)}`);
    }

    return responseJson.data.customers.edges.map((edge: any) => edge.node);
  } catch (error) {
    console.error('顧客一覧取得エラー:', error);
    throw error;
  }
}

// テスト実行
runTests().catch(console.error);
