/**
 * 商品表示フィルタリングテストスクリプト
 * 
 * メンテナンス中商品の表示状況をテスト
 */

import { PrismaClient } from '@prisma/client';
import { format } from 'date-fns';

const prisma = new PrismaClient();

async function testProductDisplayFiltering() {
  console.log('🔍 商品表示フィルタリングテストを開始...\n');

  try {
    // 全商品を取得（商品一覧ページと同じクエリ）
    console.log('=== 商品一覧ページのクエリ再現 ===');
    
    const products = await prisma.product.findMany({
      select: {
        id: true,
        shopifyId: true,
        title: true,
        sku: true,
        status: true,
        price: true,
        locationId: true,
        updatedAt: true,
        maintenances: {
          select: { startDate: true },
          where: {
            endDate: {
              gte: new Date()
            }
          },
          orderBy: {
            startDate: 'asc'
          },
          take: 1
        },
        bookings: {
          select: { startDate: true },
          where: {
            status: { in: ['PROVISIONAL', 'CONFIRMED'] },
            endDate: {
              gte: new Date()
            }
          },
          orderBy: {
            startDate: 'asc'
          },
          take: 1
        }
      },
      orderBy: [
        { status: 'asc' },
        { updatedAt: 'desc' }
      ]
    });

    console.log(`取得した商品数: ${products.length}件\n`);

    // 各商品の詳細を表示
    products.forEach((product, index) => {
      console.log(`${index + 1}. ${product.title} (${product.sku})`);
      console.log(`   ID: ${product.id}`);
      console.log(`   Shopify ID: ${product.shopifyId}`);
      console.log(`   ステータス: ${product.status}`);
      console.log(`   価格: ¥${product.price ? parseInt(product.price).toLocaleString() : '未設定'}`);
      console.log(`   ロケーション: ${product.locationId || '未設定'}`);
      
      if (product.maintenances && product.maintenances.length > 0) {
        console.log(`   次回メンテナンス: ${format(product.maintenances[0].startDate, 'yyyy-MM-dd')}`);
      } else {
        console.log(`   次回メンテナンス: なし`);
      }
      
      if (product.bookings && product.bookings.length > 0) {
        console.log(`   次回予約: ${format(product.bookings[0].startDate, 'yyyy-MM-dd')}`);
      } else {
        console.log(`   次回予約: なし`);
      }
      
      console.log('');
    });

    // 特定商品の詳細チェック
    console.log('=== 特定商品の詳細チェック ===');
    
    const targetSkus = ['TEST-TABLE-001', 'TEST-SOFA-001'];
    
    for (const sku of targetSkus) {
      console.log(`\n📦 ${sku} の詳細:`);
      
      const product = products.find(p => p.sku === sku);
      
      if (!product) {
        console.log(`❌ 商品が見つかりません`);
        continue;
      }
      
      console.log(`✅ 商品が見つかりました`);
      console.log(`   ステータス: ${product.status}`);
      
      // メンテナンス状況を詳細チェック
      const allMaintenances = await prisma.maintenance.findMany({
        where: { productId: product.id },
        orderBy: { createdAt: 'desc' }
      });
      
      console.log(`   メンテナンス記録: ${allMaintenances.length}件`);
      allMaintenances.forEach((maintenance, index) => {
        console.log(`     ${index + 1}. ステータス: ${maintenance.status}`);
        console.log(`        期間: ${format(maintenance.startDate, 'yyyy-MM-dd')} 〜 ${maintenance.endDate ? format(maintenance.endDate, 'yyyy-MM-dd') : '未設定'}`);
      });
      
      // 予約状況を詳細チェック
      const allBookings = await prisma.booking.findMany({
        where: { 
          productId: product.id,
          status: { in: ['PROVISIONAL', 'CONFIRMED'] }
        },
        orderBy: { startDate: 'asc' }
      });
      
      console.log(`   予約記録: ${allBookings.length}件`);
      allBookings.forEach((booking, index) => {
        console.log(`     ${index + 1}. ステータス: ${booking.status}`);
        console.log(`        期間: ${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')}`);
        console.log(`        顧客: ${booking.customerName}`);
      });
      
      // 表示判定
      console.log(`\n🔍 表示判定:`);
      
      // 基本的な表示条件
      const shouldDisplay = product.status !== 'ARCHIVED' && product.status !== 'DELETED';
      console.log(`   基本表示条件: ${shouldDisplay ? '✅ 表示される' : '❌ 表示されない'}`);
      
      // メンテナンス中の表示条件
      const activeMaintenances = allMaintenances.filter(m => m.status === 'IN_PROGRESS');
      if (activeMaintenances.length > 0) {
        console.log(`   メンテナンス中: ${activeMaintenances.length}件`);
        console.log(`   メンテナンス中でも表示される: ${product.status === 'AVAILABLE' ? '✅ はい' : '❌ いいえ'}`);
      }
      
      // 予約中の表示条件
      if (allBookings.length > 0) {
        console.log(`   予約中: ${allBookings.length}件`);
        console.log(`   予約中でも表示される: ✅ はい`);
      }
    }

    // フィルター別の商品数を集計
    console.log('\n=== フィルター別商品数 ===');
    
    const statusCounts = {};
    products.forEach(product => {
      statusCounts[product.status] = (statusCounts[product.status] || 0) + 1;
    });
    
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`${status}: ${count}件`);
    });
    
    // メンテナンス中商品の集計
    const maintenanceProducts = products.filter(product => 
      product.maintenances && product.maintenances.length > 0
    );
    
    console.log(`\n次回メンテナンスがある商品: ${maintenanceProducts.length}件`);
    
    // 実際にメンテナンス中の商品
    const inMaintenanceProducts = [];
    for (const product of products) {
      const activeMaintenances = await prisma.maintenance.findMany({
        where: {
          productId: product.id,
          status: 'IN_PROGRESS'
        }
      });
      
      if (activeMaintenances.length > 0) {
        inMaintenanceProducts.push({
          ...product,
          activeMaintenances: activeMaintenances.length
        });
      }
    }
    
    console.log(`実際にメンテナンス中の商品: ${inMaintenanceProducts.length}件`);
    inMaintenanceProducts.forEach(product => {
      console.log(`  - ${product.title} (${product.sku}) - ステータス: ${product.status}`);
    });

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
testProductDisplayFiltering();
