import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate, Link, useParams } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import { 
  Page, 
  LegacyCard, 
  Badge, 
  <PERSON>ton, 
  Text,
  ButtonGroup,
  Layout,
  Banner,
  DescriptionList,
  Tooltip,
  Modal,
  LegacyStack,
  Box
} from '@shopify/polaris';
import { PrismaClient } from '@prisma/client';
import { formatLocalDate, calculateDaysBetween } from '../utils/date/date-utils';
import { useState } from 'react';
import { BookingPriorityList } from '../components/Booking/PriorityDisplay';
import { CancellationService } from '../services/booking/cancellation.service';
import { EmailService } from '../services/notification/email.service';

const prisma = new PrismaClient();

// ステータスに応じたバッジ設定
const statusBadgeMap: Record<string, { status: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  draft: { status: 'attention', label: '仮予約' },
  reserved: { status: 'success', label: '本予約' },
  completed: { status: 'info', label: '完了' },
  cancelled: { status: 'critical', label: 'キャンセル' }
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  
  const { id } = params;
  
  if (!id) {
    throw new Response('予約IDが指定されていません', { status: 400 });
  }
  
  // 予約データを取得
  const booking = await prisma.reservation.findUnique({
    where: { id },
    include: {
      product: true
    }
  });
  
  if (!booking) {
    throw new Response('予約が見つかりません', { status: 404 });
  }
  
  // 同じ期間の他の予約を取得（重複予約チェック用）
  const overlappingBookings = await prisma.reservation.findMany({
    where: {
      productId: booking.productId,
      id: { not: id },
      startDate: { lte: booking.endDate },
      endDate: { gte: booking.startDate }
    },
    orderBy: [
      { status: 'asc' },
      { priority: 'asc' }
    ]
  });
  
  // 通知履歴を取得
  const notifications = await prisma.notification.findMany({
    where: { bookingId: id },
    orderBy: { createdAt: 'desc' }
  });
  
  return json({
    booking: {
      id: booking.id,
      customerName: booking.customerName,
      customerEmail: booking.customerEmail,
      status: booking.status,
      startDate: booking.startDate.toISOString(),
      endDate: booking.endDate.toISOString(),
      priority: booking.priority,
      depositAmount: booking.depositAmount?.toString(),
      totalAmount: booking.totalAmount?.toString(),
      depositPaid: booking.depositPaid,
      productTitle: booking.product.title,
      productId: booking.product.id,
      shopifyProductId: booking.product.shopifyId,
      orderId: booking.orderId,
      metadata: booking.metadata,
      createdAt: booking.createdAt.toISOString(),
      updatedAt: booking.updatedAt.toISOString()
    },
    rentalDays: calculateDaysBetween(booking.startDate, booking.endDate),
    overlappingBookings: overlappingBookings.map(b => ({
      id: b.id,
      status: b.status,
      priority: b.priority,
      startDate: b.startDate.toISOString(),
      endDate: b.endDate.toISOString(),
      customerName: b.customerName,
      customerEmail: b.customerEmail
    })),
    notifications: notifications.map(n => ({
      id: n.id,
      type: n.type,
      recipientEmail: n.recipientEmail,
      success: n.success,
      messageId: n.messageId,
      errorMessage: n.errorMessage,
      createdAt: n.createdAt.toISOString()
    })),
    canCancel: ['draft', 'reserved'].includes(booking.status),
    canConfirm: booking.status === 'draft',
    canComplete: booking.status === 'reserved',
    canSendReminder: booking.status === 'draft',
    cancellationInfo: booking.metadata?.cancellationInfo
  });
}

export default function BookingDetail() {
  const { 
    booking, 
    rentalDays, 
    overlappingBookings, 
    notifications, 
    canCancel, 
    canConfirm, 
    canComplete,
    canSendReminder,
    cancellationInfo
  } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const params = useParams();
  
  const [showNotificationHistory, setShowNotificationHistory] = useState(false);
  
  // 予約ステータスに応じたバッジを表示
  const badge = statusBadgeMap[booking.status] || { status: 'new', label: booking.status };
  
  // 仮予約に対する優先順位バッジ
  const priorityBadge = booking.status === 'draft' && booking.priority > 1 ? (
    <Badge status="warning">第{booking.priority}優先</Badge>
  ) : null;
  
  // 残金金額を計算
  const remainingAmount = 
    parseFloat(booking.totalAmount || '0') - parseFloat(booking.depositAmount || '0');
  
  // メール送信処理
  const handleSendEmail = (type: string) => {
    // /api/notifications/send へ POST リクエスト
    const formData = new FormData();
    formData.append('type', type);
    formData.append('bookingId', booking.id);
    
    fetch('/api/notifications/send', {
      method: 'POST',
      body: formData
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          alert('メールが送信されました');
          // 画面をリロード
          navigate(`/bookings/${booking.id}`, { replace: true });
        } else {
          alert(`メール送信エラー: ${data.error}`);
        }
      })
      .catch(error => {
        alert(`エラー: ${error.message}`);
      });
  };
  
  return (
    <Page
      title="予約詳細"
      breadcrumbs={[{ content: '予約一覧', url: '/bookings' }]}
      titleMetadata={<Badge status={badge.status}>{badge.label}</Badge>}
      subtitle={`予約ID: ${booking.id}`}
      primaryAction={
        <ButtonGroup>
          <Button onClick={() => navigate(`/bookings/${booking.id}/edit`)}>編集</Button>
          {canCancel && (
            <Button destructive url={`/bookings/cancel/${booking.id}`}>キャンセル</Button>
          )}
        </ButtonGroup>
      }
      secondaryActions={[
        {
          content: '通知履歴',
          onAction: () => setShowNotificationHistory(true)
        }
      ]}
    >
      <Layout>
        <Layout.Section>
          <LegacyCard title="予約情報" sectioned>
            <DescriptionList
              items={[
                {
                  term: '顧客名',
                  description: booking.customerName || '未設定'
                },
                {
                  term: 'メールアドレス',
                  description: booking.customerEmail || '未設定'
                },
                {
                  term: 'レンタル期間',
                  description: `${formatLocalDate(booking.startDate)} 〜 ${formatLocalDate(booking.endDate)}（${rentalDays}日間）`
                },
                {
                  term: '予約ステータス',
                  description: (
                    <LegacyStack spacing="tight">
                      <Badge status={badge.status}>{badge.label}</Badge>
                      {priorityBadge}
                    </LegacyStack>
                  )
                },
                {
                  term: '予約作成日',
                  description: formatLocalDate(booking.createdAt)
                },
                {
                  term: '最終更新日',
                  description: formatLocalDate(booking.updatedAt)
                }
              ]}
            />
          </LegacyCard>

          <LegacyCard title="商品情報" sectioned>
            <DescriptionList
              items={[
                {
                  term: '商品名',
                  description: booking.productTitle
                },
                {
                  term: '商品ID',
                  description: (
                    <Link to={`/products/${booking.productId}`}>
                      {booking.productId}
                    </Link>
                  )
                },
                {
                  term: 'ShopifyリンクID',
                  description: (
                    <Link to={`https://${process.env.SHOPIFY_SHOP}/admin/products/${booking.shopifyProductId}`} target="_blank">
                      {booking.shopifyProductId}
                    </Link>
                  )
                }
              ]}
            />
          </LegacyCard>
          
          {booking.status === 'reserved' && (
            <LegacyCard title="注文情報" sectioned>
              <DescriptionList
                items={[
                  {
                    term: '注文ID',
                    description: booking.orderId ? (
                      <Link to={`https://${process.env.SHOPIFY_SHOP}/admin/orders/${booking.orderId}`} target="_blank">
                        {booking.orderId}
                      </Link>
                    ) : '未設定'
                  },
                  {
                    term: '配送情報',
                    description: booking.metadata?.shippingAddress || '配送なし'
                  },
                  {
                    term: '配送希望日時',
                    description: booking.metadata?.deliveryPreference || '指定なし'
                  },
                  {
                    term: '備考',
                    description: booking.metadata?.customerNotes || 'なし'
                  }
                ]}
              />
            </LegacyCard>
          )}
          
          {booking.status === 'cancelled' && cancellationInfo && (
            <LegacyCard title="キャンセル情報" sectioned>
              <DescriptionList
                items={[
                  {
                    term: 'キャンセル日時',
                    description: formatLocalDate(cancellationInfo.cancelledAt)
                  },
                  {
                    term: 'キャンセル理由',
                    description: cancellationInfo.reason || '未指定'
                  },
                  {
                    term: 'キャンセル料',
                    description: `${parseInt(cancellationInfo.cancellationFee || '0').toLocaleString()}円`
                  },
                  {
                    term: '返金額',
                    description: `${parseInt(cancellationInfo.refundAmount || '0').toLocaleString()}円`
                  }
                ]}
              />
            </LegacyCard>
          )}
          
          {overlappingBookings.length > 0 && booking.status !== 'cancelled' && (
            <LegacyCard title="重複予約" sectioned>
              <BookingPriorityList
                bookings={overlappingBookings.map(b => ({
                  id: b.id,
                  status: b.status as any,
                  priority: b.priority,
                  startDate: b.startDate,
                  endDate: b.endDate,
                  customerName: b.customerName,
                  customerEmail: b.customerEmail
                }))}
                highlightId={booking.id}
                showEmpty={true}
              />
            </LegacyCard>
          )}
        </Layout.Section>
        
        <Layout.Section secondary>
          <LegacyCard title="料金情報" sectioned>
            <DescriptionList
              items={[
                {
                  term: '合計金額',
                  description: `${parseInt(booking.totalAmount || '0').toLocaleString()}円`
                },
                {
                  term: 'デポジット金額',
                  description: `${parseInt(booking.depositAmount || '0').toLocaleString()}円`
                },
                {
                  term: 'デポジット支払い状態',
                  description: booking.depositPaid ? (
                    <Badge status="success">支払い済み</Badge>
                  ) : (
                    <Badge>未払い</Badge>
                  )
                },
                ...(booking.status === 'reserved' ? [
                  {
                    term: '残金',
                    description: `${remainingAmount.toLocaleString()}円`
                  }
                ] : [])
              ]}
            />
          </LegacyCard>
          
          <LegacyCard title="操作" sectioned>
            <ButtonGroup vertical>
              {canConfirm && (
                <Button primary url={`/bookings/confirm/${booking.id}`}>本予約に変更</Button>
              )}
              
              {canComplete && (
                <Button url={`/bookings/complete/${booking.id}`}>レンタル完了にする</Button>
              )}
              
              <Button url={`/bookings/${booking.id}/edit`}>情報を編集</Button>
              
              {canCancel && (
                <Button destructive url={`/bookings/cancel/${booking.id}`}>キャンセル</Button>
              )}
            </ButtonGroup>
          </LegacyCard>
          
          <LegacyCard title="顧客通知" sectioned>
            <ButtonGroup vertical>
              {booking.status === 'draft' && (
                <Button onClick={() => handleSendEmail('provisional_confirmation')}>
                  仮予約確認メール送信
                </Button>
              )}
              
              {booking.status === 'reserved' && (
                <Button onClick={() => handleSendEmail('booking_confirmation')}>
                  本予約確認メール送信
                </Button>
              )}
              
              {canSendReminder && (
                <Button onClick={() => handleSendEmail('reminder')}>
                  期限リマインダー送信
                </Button>
              )}
              
              <Button url={`/notifications/custom?bookingId=${booking.id}`}>
                カスタムメール送信
              </Button>
            </ButtonGroup>
            
            <div style={{ marginTop: '16px' }}>
              <Button plain onClick={() => setShowNotificationHistory(true)}>
                通知履歴を表示
              </Button>
            </div>
          </LegacyCard>
        </Layout.Section>
      </Layout>
      
      {/* 通知履歴モーダル */}
      <Modal
        open={showNotificationHistory}
        onClose={() => setShowNotificationHistory(false)}
        title="通知履歴"
        primaryAction={{
          content: '閉じる',
          onAction: () => setShowNotificationHistory(false)
        }}
      >
        <Modal.Section>
          {notifications.length === 0 ? (
            <Banner status="info">通知履歴がありません</Banner>
          ) : (
            <div>
              {notifications.map((notification) => {
                const notificationTypeMap: Record<string, string> = {
                  provisional_confirmation: '仮予約確認',
                  booking_confirmation: '本予約確認',
                  cancellation: 'キャンセル通知',
                  reminder: '期限リマインダー',
                  custom: 'カスタム通知'
                };
                
                const typeName = notificationTypeMap[notification.type] || notification.type;
                
                return (
                  <div key={notification.id} style={{ marginBottom: '16px', borderBottom: '1px solid #ddd', paddingBottom: '16px' }}>
                    <LegacyStack>
                      <LegacyStack.Item fill>
                        <Text variant="headingMd">{typeName}</Text>
                        <Text variant="bodySm">送信先: {notification.recipientEmail}</Text>
                        <Text variant="bodySm">日時: {formatLocalDate(notification.createdAt)}</Text>
                      </LegacyStack.Item>
                      <LegacyStack.Item>
                        {notification.success ? (
                          <Badge status="success">成功</Badge>
                        ) : (
                          <Badge status="critical">失敗</Badge>
                        )}
                      </LegacyStack.Item>
                    </LegacyStack>
                    
                    {notification.errorMessage && (
                      <Box paddingTop="2">
                        <Banner status="critical">
                          {notification.errorMessage}
                        </Banner>
                      </Box>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </Modal.Section>
      </Modal>
    </Page>
  );
}