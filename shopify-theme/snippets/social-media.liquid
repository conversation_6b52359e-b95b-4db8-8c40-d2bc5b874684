{%- assign icon_class = null -%}
{%- assign type = type | default: 'row' -%}
{%- assign social_enable_tooltip = social_enable_tooltip | default: settings.tooltips_enable -%}
{%- if settings.tooltips_enable != true -%}
    {%- assign social_enable_tooltip = false %}
{%- endif -%}
<div class="social-media d-flex flex-wrap{% if type == 'column' %} flex-lg-column align-items-lg-start{% endif %} justify-content-center justify-content-lg-start">
    {% capture link_class %}mb-6 mx-10 mx-sm-15 mx-lg-0 mr-lg-{% if size == 'large' %}15{% elsif size == 'large-2' %}30{% elsif size == 'middle' %}15{% else %}10{% endif %}{% endcapture %}
    {% if type == 'column' %}{% capture icon_class %}mr-3{% endcapture %}{% endif %}
    {% if size == 'middle' %}{% capture icon_class %}{{ icon_class }}{% if icon_class != null %} {% endif %}icon-fs-md{% endcapture %}{% endif %}
    {% if size == 'large' %}{% capture icon_class %}{{ icon_class }}{% if icon_class != null %} {% endif %}icon-fs-lg{% endcapture %}{% endif %}
    {% if size == 'large-2' %}{% capture icon_class %}{{ icon_class }}{% if icon_class != null %} {% endif %}icon-fs-lg{% endcapture %}{% endif %}
    {% if icon_class != null %}{% capture icon_class %} class="{{ icon_class }}"{% endcapture %}{% endif %}
    {%- if block.settings.show_facebook != false and settings.social_facebook != blank -%}
        <a href="{{ settings.social_facebook }}" target="_blank" class="social-media__facebook d-flex align-items-center {{ link_class }}"
            {% if social_enable_tooltip and type == 'row' %}
                data-js-tooltip
                data-tippy-content="{{ 'general.social.names.facebook' | t }}"
                data-tippy-placement="top"
                data-tippy-distance="6"
            {%- endif -%}
        >
            <i{{ icon_class }}>{% render 'icon-social-facebook' %}</i>{% if type == 'column' %} <span class="d-none d-lg-inline">{{ 'general.social.names.facebook' | t }}</span>{% endif %}
        </a>
    {%- endif -%}
    {%- if block.settings.show_twitter != false and settings.social_twitter != blank -%}
        <a href="{{ settings.social_twitter }}" target="_blank" class="social-media__twitter d-flex align-items-center {{ link_class }}"
            {% if social_enable_tooltip and type == 'row' %}
                data-js-tooltip
                data-tippy-content="{{ 'general.social.names.twitter' | t }}"
                data-tippy-placement="top"
                data-tippy-distance="6"
            {%- endif -%}
        >
            <i{{ icon_class }}>{% render 'icon-social-twitter' %}</i>{% if type == 'column' %} <span class="d-none d-lg-inline">{{ 'general.social.names.twitter' | t }}</span>{% endif %}
        </a>
    {%- endif -%}
    {%- if block.settings.show_instagram != false and settings.social_instagram != blank -%}
        <a href="{{ settings.social_instagram }}" target="_blank" class="social-media__instagram d-flex align-items-center {{ link_class }}"
            {% if social_enable_tooltip and type == 'row' %}
                data-js-tooltip
                data-tippy-content="{{ 'general.social.names.instagram' | t }}"
                data-tippy-placement="top"
                data-tippy-distance="6"
            {%- endif -%}
        >
            <i{{ icon_class }}>{% render 'icon-social-instagram' %}</i>{% if type == 'column' %} <span class="d-none d-lg-inline">{{ 'general.social.names.instagram' | t }}</span>{% endif %}
        </a>
    {%- endif -%}
    {%- if block.settings.show_pinterest != false and settings.social_pinterest != blank -%}
        <a href="{{ settings.social_pinterest }}" target="_blank" class="social-media__pinterest d-flex align-items-center {{ link_class }}"
            {% if social_enable_tooltip and type == 'row' %}
                data-js-tooltip
                data-tippy-content="{{ 'general.social.names.pinterest' | t }}"
                data-tippy-placement="top"
                data-tippy-distance="6"
            {%- endif -%}
        >
            <i{{ icon_class }}>{% render 'icon-social-pinterest' %}</i>{% if type == 'column' %} <span class="d-none d-lg-inline">{{ 'general.social.names.pinterest' | t }}</span>{% endif %}
        </a>
    {%- endif -%}
    {%- if block.settings.show_youtube != false and settings.social_youtube != blank -%}
        <a href="{{ settings.social_youtube }}" target="_blank" class="social-media__youtube d-flex align-items-center {{ link_class }}"
            {% if social_enable_tooltip and type == 'row' %}
                data-js-tooltip
                data-tippy-content="{{ 'general.social.names.youtube' | t }}"
                data-tippy-placement="top"
                data-tippy-distance="6"
            {%- endif -%}
        >
            <i{{ icon_class }}>{% render 'icon-social-youtube' %}</i>{% if type == 'column' %} <span class="d-none d-lg-inline">{{ 'general.social.names.youtube' | t }}</span>{% endif %}
        </a>
    {%- endif -%}
    {%- if block.settings.show_behance != false and settings.social_behance != blank -%}
        <a href="{{ settings.social_behance }}" target="_blank" class="social-media__behance d-flex align-items-center {{ link_class }}"
            {%- if social_enable_tooltip and type == 'row' %}
                data-js-tooltip
                data-tippy-content="{{ 'general.social.names.behance' | t }}"
                data-tippy-placement="top"
                data-tippy-distance="6"
            {%- endif -%}
        >
            <i{{ icon_class }}>{% render 'icon-social-behance' %}</i>{% if type == 'column' %} <span class="d-none d-lg-inline">{{ 'general.social.names.behance' | t }}</span>{% endif %}
        </a>
    {%- endif -%}
    {%- if block.settings.show_skype != false and settings.social_skype != blank -%}
        <a href="{{ settings.social_skype }}" target="_blank" class="social-media__skype d-flex align-items-center {{ link_class }}"
            {% if social_enable_tooltip and type == 'row' %}
                data-js-tooltip
                data-tippy-content="{{ 'general.social.names.skype' | t }}"
                data-tippy-placement="top"
                data-tippy-distance="6"
            {%- endif -%}
        >
            <i{{ icon_class }}>{% render 'icon-social-skype' %}</i>{% if type == 'column' %} <span class="d-none d-lg-inline">{{ 'general.social.names.skype' | t }}</span>{% endif %}
        </a>
    {%- endif -%}
    {%- if block.settings.show_line != false and settings.social_line != blank -%}
        <a href="{{ settings.social_line }}" target="_blank" class="social-media__line d-flex align-items-center {{ link_class }}"
            {% if social_enable_tooltip and type == 'row' %}
                data-js-tooltip
                data-tippy-content="{{ 'general.social.names.line' | t }}"
                data-tippy-placement="top"
                data-tippy-distance="6"
            {%- endif -%}
        >
            <i{{ icon_class }}>{% render 'icon-social-line' %}</i>{% if type == 'column' %} <span class="d-none d-lg-inline">{{ 'general.social.names.line' | t }}</span>{% endif %}
        </a>
    {%- endif -%}
    {%- if block.settings.show_tiktok != false and settings.social_tiktok != blank -%}
        <a href="{{ settings.social_tiktok }}" target="_blank" class="social-media__tiktok d-flex align-items-center {{ link_class }}"
            {% if social_enable_tooltip and type == 'row' %}
                data-js-tooltip
                data-tippy-content="{{ 'general.social.names.tiktok' | t }}"
                data-tippy-placement="top"
                data-tippy-distance="6"
            {%- endif -%}
        >
            <i{{ icon_class }}>{% render 'icon-social-tiktok' %}</i>{% if type == 'column' %} <span class="d-none d-lg-inline">{{ 'general.social.names.tiktok' | t }}</span>{% endif %}
        </a>
    {%- endif -%}
</div>