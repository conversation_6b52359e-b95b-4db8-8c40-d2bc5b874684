{"timestamp": "2025-05-19T07:23:20.686Z", "duration": 26.547, "totalTests": 10, "successCount": 7, "failureCount": 3, "tests": [{"testName": "年末年始の予約テスト", "success": true, "timestamp": "2025-05-19T07:23:26.245Z", "message": "年末年始の予約が正常に作成されました", "error": "", "duration": 4818, "details": {"bookingId": "eb5d1853-5087-4e1d-8ca0-6831d598fb97", "period": "年末年始", "startDate": "2025-12-29", "endDate": "2026-01-03"}}, {"testName": "予約期間の最小値テスト", "success": true, "timestamp": "2025-05-19T07:23:27.339Z", "message": "最小予約期間（1日）の予約が正常に作成されました", "error": "", "duration": 1094, "details": {"bookingId": "3cfb504b-5342-4af5-bc55-a0527f0785ef", "durationDays": 1}}, {"testName": "予約期間の最大値テスト", "success": true, "timestamp": "2025-05-19T07:23:42.166Z", "message": "最大予約期間（30日）の予約が正常に作成されました", "error": "", "duration": 14827, "details": {"bookingId": "f6aee8e7-b473-4ed7-bfd4-01c5722b0935", "durationDays": 30}}, {"testName": "過去の日付での予約テスト", "success": true, "timestamp": "2025-05-19T07:23:45.706Z", "message": "過去の日付での予約は正しくエラーになりました", "error": "過去の日付での予約がエラーにならずに成功してしまいました", "duration": 2481, "details": {"expectedError": true}}, {"testName": "終了日が開始日より前の予約テスト", "success": true, "timestamp": "2025-05-19T07:23:46.266Z", "message": "終了日が開始日より前の予約は正しくエラーになりました", "error": "終了日が開始日より前の予約がエラーにならずに成功してしまいました", "duration": 560, "details": {"expectedError": true}}, {"testName": "無効なメールアドレスでの予約テスト", "success": true, "timestamp": "2025-05-19T07:23:46.502Z", "message": "無効なメールアドレスでの予約は正しくエラーになりました", "error": "", "duration": 236, "details": {"expectedError": true, "invalidEmail": "invalid-email"}}, {"testName": "在庫をゼロにするテスト", "success": false, "timestamp": "2025-05-19T07:23:46.746Z", "message": "在庫をゼロにすることに失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-28, 2025-06-29, 2025-06-30, 2025-07-01", "duration": 243, "details": {"bookingId": ""}}, {"testName": "在庫がゼロの状態で予約を試みるテスト", "success": true, "timestamp": "2025-05-19T07:23:46.982Z", "message": "在庫がゼロの状態での予約は正しくエラーになりました", "error": "", "duration": 236, "details": {"expectedError": true}}, {"testName": "予約をキャンセルして在庫を回復するテスト", "success": false, "timestamp": "2025-05-19T07:23:46.983Z", "message": "予約をキャンセルして在庫を回復することに失敗しました", "error": "テスト1で作成した予約IDがありません", "duration": 1, "details": {"bookingId": ""}}, {"testName": "在庫が回復した状態で予約を試みるテスト", "success": false, "timestamp": "2025-05-19T07:23:47.233Z", "message": "在庫が回復した状態での予約に失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-28, 2025-06-29, 2025-06-30, 2025-07-01", "duration": 244, "details": {"bookingId": ""}}], "environment": {"nodeVersion": "v20.18.1", "os": "darwin arm64"}, "performance": {"averageTestDuration": 2474, "slowestTest": {"name": "予約期間の最大値テスト", "duration": 14827}, "fastestTest": {"name": "予約をキャンセルして在庫を回復するテスト", "duration": 1}}}