# 商品登録テスト手順書

**最終更新日**: 2025/05/17

## 目次

1. [概要](#概要)
2. [テスト環境の準備](#テスト環境の準備)
3. [基本テスト](#基本テスト)
   - [商品表示テスト](#商品表示テスト)
   - [予約テスト](#予約テスト)
   - [在庫テスト](#在庫テスト)
4. [特殊ケーステスト](#特殊ケーステスト)
   - [仮予約・確定予約テスト](#仮予約確定予約テスト)
   - [休日を挟んだレンタルテスト](#休日を挟んだレンタルテスト)
   - [長期・短期レンタルテスト](#長期短期レンタルテスト)
   - [複数予約テスト](#複数予約テスト)
   - [状態変更テスト](#状態変更テスト)
5. [テスト結果の検証方法](#テスト結果の検証方法)
6. [トラブルシューティング](#トラブルシューティング)

## 概要

本ドキュメントでは、Shopifyを使用したレンタル商品の登録後のテスト方法について説明します。商品登録後に正しく機能するかを確認するための手順を提供します。

## テスト環境の準備

### 必要なツール

- Shopify管理画面へのアクセス権限
- 開発環境（Node.js、npm）
- テスト用の商品ID（テスト対象の商品）

### 環境変数の設定

テストを実行する前に、以下の環境変数を設定してください：

```bash
# .env ファイル
SHOPIFY_SHOP=peaces-test-block.myshopify.com
SHOPIFY_API_KEY=your_api_key
SHOPIFY_API_SECRET=your_api_secret
SHOPIFY_ADMIN_API_ACCESS_TOKEN=your_admin_api_access_token
```

### テスト用スクリプトの準備

以下のスクリプトを使用してテストを実行します：

1. 予約情報初期化スクリプト
   ```bash
   node scripts/initialize-booking-data.js [商品ID]
   ```

2. 商品テストスクリプト
   ```bash
   node scripts/check-product.js [商品ID]
   ```

3. 予約テストスクリプト
   ```bash
   node scripts/test-booking-process.js [商品ID]
   ```

## 基本テスト

### 商品表示テスト

商品ページが正しく表示されるかを確認します。

#### 手順

1. Shopify管理画面で商品を確認
   - 管理画面にログイン
   - 「商品」 > 「すべての商品」に移動
   - テスト対象の商品を選択
   - 以下の項目を確認：
     - 商品名
     - 価格
     - 在庫状態
     - メタフィールド情報

2. フロントエンドで商品ページを確認
   - ストアフロントにアクセス
   - テスト対象の商品ページを開く
   - 以下の項目を確認：
     - 商品名が正しく表示されているか
     - 価格が正しく表示されているか
     - 商品画像が表示されているか
     - 商品説明が表示されているか
     - レンタル関連情報が表示されているか

3. 商品情報スクリプトで確認
   ```bash
   node scripts/check-product.js [商品ID]
   ```
   - 出力結果で以下を確認：
     - Shopify商品情報
     - メタフィールド情報
     - データベース商品情報

### 予約テスト

予約機能が正しく動作するかを確認します。

#### 手順

1. カレンダー表示の確認
   - 商品ページでカレンダーが表示されるか
   - 日付選択が可能か
   - 予約済み日程が正しく表示されるか

2. 日付選択テスト
   - 開始日を選択
   - 終了日を選択
   - 選択した日程が正しく表示されるか

3. 料金計算テスト
   - 選択した日程に基づいて料金が正しく計算されるか
   - 割引ルールが適用されるか

4. カートへの追加テスト
   - 「カートに追加」ボタンをクリック
   - カートページに遷移するか
   - カート内に商品が正しく追加されているか

### 在庫テスト

在庫状態に応じて予約可否が正しく制御されるかを確認します。

#### 手順

1. 在庫状態の確認
   - 管理画面で在庫数を確認
   - 在庫場所（PR/NY）が正しく設定されているか

2. 在庫数変更テスト
   - 在庫数を0に変更
   - 商品ページをリロード
   - 予約ボタンが無効になっているか確認

3. 在庫数復元テスト
   - 在庫数を1に戻す
   - 商品ページをリロード
   - 予約ボタンが有効になっているか確認

4. 在庫状態変更テスト
   - メタフィールドで商品状態を「メンテナンス中」に変更
   - 商品ページをリロード
   - 予約ができないことを確認

## 特殊ケーステスト

### 仮予約・確定予約テスト

仮予約と確定予約の処理が正しく行われるかを確認します。

#### 手順

1. 仮予約テスト
   ```bash
   node scripts/test-booking-process.js [商品ID] provisional
   ```
   - 仮予約が正しく作成されるか
   - データベースに予約情報が保存されるか
   - メタフィールドに予約情報が反映されるか

2. 確定予約テスト
   ```bash
   node scripts/test-booking-process.js [商品ID] confirmed
   ```
   - 確定予約が正しく作成されるか
   - データベースに予約情報が保存されるか
   - メタフィールドに予約情報が反映されるか

3. 予約状態変更テスト
   - 仮予約から確定予約への変更
   - 確定予約からキャンセル状態への変更

### 休日を挟んだレンタルテスト

休日を挟んだ予約が正しく処理されるかを確認します。

#### 手順

1. 休日を含む日程選択
   - 日曜日を含む日程を選択
   - 祝日を含む日程を選択
   - 年末年始を含む日程を選択

2. 料金計算確認
   - 休日を含む場合の料金計算が正しいか
   - 休日のカウント方法が正しいか

### 長期・短期レンタルテスト

レンタル期間に応じた料金計算が正しく行われるかを確認します。

#### 手順

1. 短期レンタルテスト（1-2日）
   - 1日間のレンタルを選択
   - 2日間のレンタルを選択
   - 料金計算が正しいか確認

2. 中期レンタルテスト（3-7日）
   - 3日間のレンタルを選択
   - 7日間のレンタルを選択
   - 割引率が正しく適用されるか確認

3. 長期レンタルテスト（8日以上）
   - 8日間のレンタルを選択
   - 14日間のレンタルを選択
   - 30日間のレンタルを選択
   - 長期割引率が正しく適用されるか確認

### 複数予約テスト

連続した予約や複数の予約が正しく処理されるかを確認します。

#### 手順

1. 連続予約テスト
   - 1つ目の予約を作成
   - 1つ目の予約の直後の日程で2つ目の予約を作成
   - 両方の予約が正しく処理されるか確認

2. 重複予約テスト
   - 既存の予約と重複する日程で予約を試みる
   - エラーメッセージが表示されるか確認

3. 優先順位テスト
   - 同じ日程で複数の仮予約を作成
   - 優先順位が正しく設定されるか確認

### 状態変更テスト

商品状態の変更が予約に与える影響を確認します。

#### 手順

1. メンテナンス状態テスト
   - 商品状態を「メンテナンス中」に変更
   - 予約カレンダーでメンテナンス期間が表示されるか確認
   - メンテナンス期間中の予約ができないことを確認

2. 廃棄状態テスト
   - 商品状態を「廃棄済み」に変更
   - 商品が予約できなくなることを確認

3. 状態復帰テスト
   - 商品状態を「利用可能」に戻す
   - 予約が再び可能になることを確認

## テスト結果の検証方法

テスト結果を検証するための方法を説明します。

### データベース検証

```bash
# 商品情報の確認
node scripts/check-product.js [商品ID]

# 予約情報の確認
node scripts/check-bookings.js [商品ID]
```

### Shopify管理画面での検証

1. メタフィールド情報の確認
   - 管理画面 > 商品 > 対象商品 > メタフィールド
   - `rental.bookings` メタフィールドの内容を確認

2. 注文情報の確認
   - 管理画面 > 注文 > 対象注文
   - 注文に関連する予約情報を確認

### フロントエンド検証

1. 商品ページでの確認
   - カレンダー表示が正しいか
   - 予約済み日程が正しく表示されるか

2. カートページでの確認
   - 予約情報が正しく表示されるか
   - 料金計算が正しいか

## トラブルシューティング

テスト中に発生する可能性のある問題と解決策を説明します。

### よくある問題と解決策

1. **予約情報が表示されない**
   - メタフィールドが正しく設定されているか確認
   - データベースに予約情報が保存されているか確認
   - キャッシュをクリアして再試行

2. **料金計算が正しくない**
   - `rental.pricing` メタフィールドの設定を確認
   - 割引ルールが正しく設定されているか確認
   - 休日の計算ロジックが正しく機能しているか確認

3. **在庫状態が反映されない**
   - 在庫設定が正しく行われているか確認
   - 在庫同期が正しく機能しているか確認
   - キャッシュをクリアして再試行

4. **予約が作成できない**
   - エラーメッセージを確認
   - データベース接続が正常か確認
   - APIキーと権限が正しいか確認

### エラーログの確認方法

```bash
# アプリケーションログの確認
npm run logs

# データベースログの確認
npx prisma studio
```

### テストデータのリセット方法

```bash
# 予約情報の初期化
node scripts/initialize-booking-data.js [商品ID]

# テストデータの生成
node scripts/generate-test-data.js
```
