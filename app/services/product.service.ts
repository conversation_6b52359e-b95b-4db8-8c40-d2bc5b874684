/**
 * 商品サービス
 *
 * 商品データの取得、キャッシュ、同期などの機能を提供します。
 */

import { prisma } from "~/db.server";
import { logger } from "~/utils/logger";
import { LRUCache } from "lru-cache";
import { authenticate } from "~/shopify.server";

// キャッシュの設定
const CACHE_MAX_SIZE = 1000; // 最大キャッシュ数
const CACHE_TTL = 1000 * 60 * 15; // キャッシュの有効期限（15分）

// 商品データのキャッシュ
const productCache = new LRUCache<string, any>({
  max: CACHE_MAX_SIZE,
  ttl: CACHE_TTL,
});

/**
 * 商品サービスクラス
 */
export class ProductService {
  private shop: string;
  private admin: any;

  constructor(shop: string, admin?: any) {
    this.shop = shop;
    this.admin = admin;
  }

  /**
   * 商品IDから商品データを取得
   * @param productId 商品ID
   * @param forceRefresh キャッシュを無視して再取得するかどうか
   * @returns 商品データ
   */
  async getProductById(productId: string, forceRefresh = false): Promise<any> {
    try {
      // キャッシュキーを生成
      const cacheKey = `product:${productId}`;

      // キャッシュをチェック（強制更新でない場合）
      if (!forceRefresh) {
        const cachedProduct = productCache.get(cacheKey);
        if (cachedProduct) {
          logger.debug(`商品キャッシュヒット: ${productId}`);
          return cachedProduct;
        }
      }

      // データベースから商品を取得
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          maintenances: {
            where: {
              endDate: { gte: new Date() },
            },
            orderBy: { startDate: "asc" },
          },
        },
      });

      if (!product) {
        logger.warn(`商品が見つかりません: ${productId}`);
        return null;
      }

      // キャッシュに保存
      productCache.set(cacheKey, product);
      return product;
    } catch (error) {
      logger.error(`商品取得エラー: ${productId}`, error);
      throw error;
    }
  }

  /**
   * Shopify IDから商品データを取得
   * @param shopifyId Shopify ID
   * @param forceRefresh キャッシュを無視して再取得するかどうか
   * @returns 商品データ
   */
  async getProductByShopifyId(shopifyId: string, forceRefresh = false): Promise<any> {
    try {
      // キャッシュキーを生成
      const cacheKey = `product:shopify:${shopifyId}`;

      // キャッシュをチェック（強制更新でない場合）
      if (!forceRefresh) {
        const cachedProduct = productCache.get(cacheKey);
        if (cachedProduct) {
          logger.debug(`商品キャッシュヒット (Shopify ID): ${shopifyId}`);
          return cachedProduct;
        }
      }

      // データベースから商品を取得
      const product = await prisma.product.findFirst({
        where: { shopifyId, shop: this.shop },
        include: {
          maintenances: {
            where: {
              endDate: { gte: new Date() },
            },
            orderBy: { startDate: "asc" },
          },
        },
      });

      if (!product) {
        logger.warn(`商品が見つかりません (Shopify ID): ${shopifyId}`);
        return null;
      }

      // キャッシュに保存
      productCache.set(cacheKey, product);
      // IDでもキャッシュ
      productCache.set(`product:${product.id}`, product);
      return product;
    } catch (error) {
      logger.error(`商品取得エラー (Shopify ID): ${shopifyId}`, error);
      throw error;
    }
  }

  /**
   * SKUから商品データを取得
   * @param sku SKU
   * @param forceRefresh キャッシュを無視して再取得するかどうか
   * @returns 商品データ
   */
  async getProductBySku(sku: string, forceRefresh = false): Promise<any> {
    try {
      // キャッシュキーを生成
      const cacheKey = `product:sku:${sku}`;

      // キャッシュをチェック（強制更新でない場合）
      if (!forceRefresh) {
        const cachedProduct = productCache.get(cacheKey);
        if (cachedProduct) {
          logger.debug(`商品キャッシュヒット (SKU): ${sku}`);
          return cachedProduct;
        }
      }

      // データベースから商品を取得
      const product = await prisma.product.findFirst({
        where: { sku, shop: this.shop },
        include: {
          maintenances: {
            where: {
              endDate: { gte: new Date() },
            },
            orderBy: { startDate: "asc" },
          },
        },
      });

      if (!product) {
        logger.warn(`商品が見つかりません (SKU): ${sku}`);
        return null;
      }

      // キャッシュに保存
      productCache.set(cacheKey, product);
      // IDでもキャッシュ
      productCache.set(`product:${product.id}`, product);
      return product;
    } catch (error) {
      logger.error(`商品取得エラー (SKU): ${sku}`, error);
      throw error;
    }
  }

  /**
   * 商品データを更新
   * @param productId 商品ID
   * @param data 更新データ
   * @returns 更新された商品データ
   */
  async updateProduct(productId: string, data: any): Promise<any> {
    try {
      // データベースの商品を更新
      const updatedProduct = await prisma.product.update({
        where: { id: productId },
        data: {
          ...data,
          updatedAt: new Date(),
        },
        include: {
          maintenances: {
            where: {
              endDate: { gte: new Date() },
            },
            orderBy: { startDate: "asc" },
          },
        },
      });

      // キャッシュを更新
      productCache.set(`product:${productId}`, updatedProduct);
      if (updatedProduct.shopifyId) {
        productCache.set(`product:shopify:${updatedProduct.shopifyId}`, updatedProduct);
      }
      if (updatedProduct.sku) {
        productCache.set(`product:sku:${updatedProduct.sku}`, updatedProduct);
      }

      return updatedProduct;
    } catch (error) {
      logger.error(`商品更新エラー: ${productId}`, error);
      throw error;
    }
  }

  /**
   * 商品データのキャッシュを削除
   * @param productId 商品ID
   */
  invalidateProductCache(productId: string): void {
    try {
      // 商品データを取得
      prisma.product.findUnique({
        where: { id: productId },
        select: { id: true, shopifyId: true, sku: true },
      }).then(product => {
        if (product) {
          // 各キャッシュキーを削除
          productCache.delete(`product:${productId}`);
          if (product.shopifyId) {
            productCache.delete(`product:shopify:${product.shopifyId}`);
          }
          if (product.sku) {
            productCache.delete(`product:sku:${product.sku}`);
          }
          logger.debug(`商品キャッシュを削除: ${productId}`);
        }
      }).catch(error => {
        logger.error(`商品キャッシュ削除エラー: ${productId}`, error);
      });
    } catch (error) {
      logger.error(`商品キャッシュ削除エラー: ${productId}`, error);
    }
  }

  /**
   * すべての商品データのキャッシュを削除
   */
  clearAllProductCache(): void {
    productCache.clear();
    logger.debug("すべての商品キャッシュを削除しました");
  }

  /**
   * 商品データをShopifyと同期
   * @param productId 商品ID
   * @returns 同期結果
   */
  async syncProductWithShopify(productId: string): Promise<any> {
    try {
      if (!this.admin) {
        throw new Error("Shopify Admin APIが初期化されていません");
      }

      // データベースから商品を取得
      const product = await prisma.product.findUnique({
        where: { id: productId },
      });

      if (!product || !product.shopifyId) {
        throw new Error(`商品が見つからないか、Shopify IDがありません: ${productId}`);
      }

      // Shopifyから商品データを取得
      const shopifyProductId = product.shopifyId.replace("gid://shopify/Product/", "");
      const response = await this.admin.graphql(
        `#graphql
        query getProduct($id: ID!) {
          product(id: $id) {
            id
            title
            handle
            description
            productType
            tags
            status
            variants(first: 10) {
              edges {
                node {
                  id
                  title
                  price
                  sku
                  inventoryQuantity
                }
              }
            }
            metafields(first: 20, namespace: "rental") {
              edges {
                node {
                  id
                  namespace
                  key
                  value
                  type
                }
              }
            }
          }
        }`,
        {
          variables: {
            id: `gid://shopify/Product/${shopifyProductId}`,
          },
        }
      );

      const responseJson = await response.json();
      const shopifyProduct = responseJson.data.product;

      // 商品データを更新
      const updatedProduct = await this.updateProduct(productId, {
        title: shopifyProduct.title,
        // その他の更新フィールド
      });

      // キャッシュを更新
      this.invalidateProductCache(productId);

      return {
        success: true,
        product: updatedProduct,
      };
    } catch (error) {
      logger.error(`商品同期エラー: ${productId}`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }
}

/**
 * 商品サービスのインスタンスを取得
 * @param request リクエスト
 * @returns 商品サービスのインスタンス
 */
export async function getProductService(request: Request): Promise<ProductService> {
  try {
    const { admin, session } = await authenticate.admin(request);
    return new ProductService(session.shop, admin);
  } catch (error) {
    logger.error("商品サービスの初期化エラー", error);
    throw error;
  }
}
