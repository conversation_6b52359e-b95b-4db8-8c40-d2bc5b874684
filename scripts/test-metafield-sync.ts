/**
 * メタフィールド同期テスト
 *
 * このスクリプトは、Shopifyのメタフィールドとの同期機能をテストします。
 * 実行方法: npx tsx scripts/test-metafield-sync.ts [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';
import { DbSyncService } from '../app/services/sync/db-sync.service';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト設定
const config = {
  // テストする商品のShopify ID
  productId: process.argv[2] || '123456789', // コマンドライン引数から取得、または既定値を使用
  
  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com'
};

/**
 * 商品情報を取得する関数
 */
async function getProductInfo(shopifyProductId: string) {
  try {
    // Prismaから商品情報を取得
    const product = await prisma.product.findFirst({
      where: { shopifyId: shopifyProductId }
    });
    
    if (!product) {
      console.error(`商品ID ${shopifyProductId} が見つかりません`);
      return null;
    }
    
    return product;
  } catch (error) {
    console.error('商品情報取得エラー:', error);
    return null;
  }
}

/**
 * 商品情報を表示する関数
 */
async function displayProductInfo(productId: string) {
  try {
    // 商品情報を取得
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });
    
    if (!product) {
      console.error(`商品ID ${productId} が見つかりません`);
      return;
    }
    
    console.log('\n===== 商品情報 =====');
    console.log(`商品ID: ${product.id}`);
    console.log(`ShopifyID: ${product.shopifyId}`);
    console.log(`商品名: ${product.title}`);
    console.log(`SKU: ${product.sku}`);
    console.log(`ステータス: ${product.status}`);
    console.log(`価格: ${product.price}円`);
    console.log(`最終同期日時: ${product.lastSyncedAt ? format(new Date(product.lastSyncedAt), 'yyyy/MM/dd HH:mm:ss', { locale: ja }) : '未同期'}`);
    
    // 基本情報を表示
    if (product.basicInfo) {
      try {
        const basicInfo = typeof product.basicInfo === 'string' 
          ? JSON.parse(product.basicInfo as string) 
          : product.basicInfo;
        
        console.log('\n----- 基本情報 -----');
        console.log(JSON.stringify(basicInfo, null, 2));
      } catch (error) {
        console.warn('基本情報の解析に失敗しました:', error);
      }
    }
    
    // 料金情報を表示
    if (product.pricing) {
      try {
        const pricing = typeof product.pricing === 'string' 
          ? JSON.parse(product.pricing as string) 
          : product.pricing;
        
        console.log('\n----- 料金情報 -----');
        console.log(JSON.stringify(pricing, null, 2));
      } catch (error) {
        console.warn('料金情報の解析に失敗しました:', error);
      }
    }
    
    // メタデータを表示
    if (product.metadata) {
      console.log('\n----- メタデータ -----');
      console.log(JSON.stringify(product.metadata, null, 2));
    }
  } catch (error) {
    console.error('商品情報表示エラー:', error);
  }
}

/**
 * メタフィールド同期をテストする関数
 */
async function testMetafieldSync(shopifyProductId: string) {
  try {
    console.log(`商品ID ${shopifyProductId} のメタフィールド同期テストを実行します...`);
    
    // 商品情報を取得
    const product = await getProductInfo(shopifyProductId);
    
    if (!product) {
      return false;
    }
    
    // 同期前の商品情報を表示
    console.log('\n----- 同期前の商品情報 -----');
    await displayProductInfo(product.id);
    
    // モックリクエストオブジェクトを作成
    const mockRequest = new Request(`https://${config.shop}/admin/api/2024-01/graphql.json`);
    
    // DbSyncServiceを初期化
    const dbSyncService = new DbSyncService(mockRequest);
    
    // メタフィールドを同期
    console.log('\n----- メタフィールド同期を実行中... -----');
    
    try {
      const syncResult = await dbSyncService.syncProductFromShopify(shopifyProductId);
      
      if (syncResult.success) {
        console.log('メタフィールド同期が成功しました');
        console.log(`メッセージ: ${syncResult.message}`);
      } else {
        console.error('メタフィールド同期が失敗しました');
        console.error(`エラー: ${syncResult.message}`);
        return false;
      }
    } catch (error) {
      console.error('メタフィールド同期エラー:', error);
      return false;
    }
    
    // 同期後の商品情報を表示
    console.log('\n----- 同期後の商品情報 -----');
    await displayProductInfo(product.id);
    
    // 同期ログを表示
    console.log('\n----- 同期ログ -----');
    const syncLogs = await prisma.syncLog.findMany({
      where: {
        entityType: 'product',
        entityId: product.id
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });
    
    if (syncLogs.length === 0) {
      console.log('同期ログがありません');
    } else {
      for (const log of syncLogs) {
        console.log(`日時: ${format(new Date(log.createdAt), 'yyyy/MM/dd HH:mm:ss', { locale: ja })}`);
        console.log(`操作: ${log.operation}`);
        console.log(`ステータス: ${log.status}`);
        console.log(`メッセージ: ${log.errorMessage || '成功'}`);
        console.log('---');
      }
    }
    
    return true;
  } catch (error) {
    console.error('メタフィールド同期テストエラー:', error);
    return false;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('メタフィールド同期テストを開始します...');
    
    // コマンドライン引数から商品IDを取得
    const shopifyProductId = config.productId;
    
    // メタフィールド同期をテスト
    const testResult = await testMetafieldSync(shopifyProductId);
    
    // テスト結果のサマリー
    console.log('\n===== テスト結果サマリー =====');
    console.log(`メタフィールド同期テスト: ${testResult ? '成功' : '失敗'}`);
    
    if (testResult) {
      console.log('\nテストが成功しました！');
    } else {
      console.error('\nテストが失敗しました。');
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
