/**
 * 商品詳細ページでのレンタル機能を管理するJavaScript
 *
 * このスクリプトは、商品詳細ページでの日付選択、料金計算、
 * カートへの追加機能を提供します。
 */

class ProductRental {
  constructor(options = {}) {
    // 設定を読み込んでから初期化
    this.loadConfig().then(() => {
      // オプションの設定
      this.options = {
        productId: options.productId || '',
        variantId: options.variantId || '',
        basePrice: parseFloat(options.basePrice || 0),
        productTitle: options.productTitle || '',
        variantTitle: options.variantTitle || '',
        productImage: options.productImage || '',
        apiEndpoint: `/apps/ease-next-temp/check-availability`,
        ...options
      };

      // 状態の初期化
      this.state = {
        startDate: null,
        endDate: null,
        rentalDays: 0,
        totalPrice: 0,
        depositAmount: 0,
        isAvailable: false,
        isLoading: false,
        errorMessage: ''
      };

      // DOM要素の参照
      this.elements = {
        startDateInput: document.getElementById('rental-start-date'),
        endDateInput: document.getElementById('rental-end-date'),
        rentalDaysElement: document.getElementById('rental-days-count'),
        totalPriceElement: document.getElementById('rental-price-amount'),
        depositElement: document.getElementById('deposit-amount'),
        addToCartButton: document.getElementById('add-to-cart-rental'),
        errorMessageElement: document.getElementById('error-message'),
        loadingIndicator: document.getElementById('loading-indicator')
      };

      // イベントリスナーの設定
      this.setupEventListeners();

      // 日付選択フィールドの初期化を遅延させて強制的に行う
      setTimeout(() => {
        this.initDateFields();
      }, 500);

      console.log("ProductRental initialized with config:", this.config);
    }).catch(error => {
      console.error("Failed to load config:", error);
      // フォールバック設定を使用して初期化
      this.initWithFallbackConfig(options);
    });
  }

  /**
   * フォールバック設定で初期化
   */
  initWithFallbackConfig(options) {
    // フォールバック設定
    this.config = {
      appUrl: 'https://app.shopify-app-test.xyz',
      environment: 'development'
    };

    // オプションの設定
    this.options = {
      productId: options.productId || '',
      variantId: options.variantId || '',
      basePrice: parseFloat(options.basePrice || 0),
      productTitle: options.productTitle || '',
      variantTitle: options.variantTitle || '',
      productImage: options.productImage || '',
      apiEndpoint: `/apps/ease-next-temp/check-availability`,
      ...options
    };

    // 状態の初期化
    this.state = {
      startDate: null,
      endDate: null,
      rentalDays: 0,
      totalPrice: 0,
      depositAmount: 0,
      isAvailable: false,
      isLoading: false,
      errorMessage: ''
    };

    // DOM要素の参照
    this.elements = {
      startDateInput: document.getElementById('rental-start-date'),
      endDateInput: document.getElementById('rental-end-date'),
      rentalDaysElement: document.getElementById('rental-days-count'),
      totalPriceElement: document.getElementById('rental-price-amount'),
      depositElement: document.getElementById('deposit-amount'),
      addToCartButton: document.getElementById('add-to-cart-rental'),
      errorMessageElement: document.getElementById('error-message'),
      loadingIndicator: document.getElementById('loading-indicator')
    };

    // イベントリスナーの設定
    this.setupEventListeners();

    // 日付選択フィールドの初期化を遅延させて強制的に行う
    setTimeout(() => {
      this.initDateFields();
    }, 500);

    console.log("ProductRental initialized with fallback config:", this.config);
  }

  /**
   * 設定を読み込む
   */
  async loadConfig() {
    try {
      // テーマから環境設定を取得
      if (window.shopConfig) {
        this.config = window.shopConfig;
        console.log("Config loaded from theme:", this.config);
        return;
      }

      // テーマから設定が取得できない場合はフォールバック設定を使用
      this.config = {
        appUrl: 'https://app.shopify-app-test.xyz',
        environment: 'development'
      };
      console.log("Using fallback config:", this.config);
    } catch (error) {
      console.error("Error loading config:", error);
      // フォールバック設定
      this.config = {
        appUrl: 'https://app.shopify-app-test.xyz',
        environment: 'development'
      };
      console.log("Using fallback config:", this.config);
    }
  }

  /**
   * 現在の日付を取得（システム時間から直接取得）
   */
  getCurrentDate() {
    // システム時間から直接取得
    const now = new Date();

    // 現在の日付をログに出力
    console.log('システム時間から取得した現在の日付:', {
      date: now,
      year: now.getFullYear(),
      month: now.getMonth() + 1,
      day: now.getDate(),
      toString: now.toString(),
      toISOString: now.toISOString()
    });

    return now;
  }

  /**
   * 日付選択フィールドの初期化
   */
  initDateFields() {
    if (this.elements.startDateInput && this.elements.endDateInput) {
      // 現在の日付を取得して設定（システム時間から直接取得）
      const today = this.getCurrentDate();
      const yyyy = today.getFullYear();
      const mm = String(today.getMonth() + 1).padStart(2, '0');
      const dd = String(today.getDate()).padStart(2, '0');
      const formattedToday = `${yyyy}-${mm}-${dd}`;

      // 元のmin属性を確認
      const originalStartDateMin = this.elements.startDateInput.getAttribute('min');
      const originalEndDateMin = this.elements.endDateInput.getAttribute('min');

      console.log('元のmin属性:', {
        startDateInputMin: originalStartDateMin,
        endDateInputMin: originalEndDateMin
      });

      // min属性を削除
      this.elements.startDateInput.removeAttribute('min');
      this.elements.endDateInput.removeAttribute('min');

      // min属性を現在の日付に設定（複数の方法で設定）
      // 方法1: 直接プロパティとして設定
      this.elements.startDateInput.min = formattedToday;
      this.elements.endDateInput.min = formattedToday;

      // 方法2: setAttribute メソッドを使用
      this.elements.startDateInput.setAttribute('min', formattedToday);
      this.elements.endDateInput.setAttribute('min', formattedToday);

      // 方法3: defaultValue を設定
      this.elements.startDateInput.defaultValue = formattedToday;

      // 日付選択フィールドの設定をログに出力
      console.log('日付選択フィールドの初期化:', {
        today: formattedToday,
        startDateMin: this.elements.startDateInput.getAttribute('min'),
        endDateInputMin: this.elements.endDateInput.getAttribute('min')
      });

      // MutationObserverを使用して属性の変更を監視
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'min') {
            const newValue = mutation.target.getAttribute('min');
            if (newValue !== formattedToday) {
              console.log('min属性が変更されました:', {
                element: mutation.target.id,
                oldValue: formattedToday,
                newValue: newValue
              });

              // 再度設定
              mutation.target.setAttribute('min', formattedToday);
            }
          }
        });
      });

      // 監視を開始
      observer.observe(this.elements.startDateInput, { attributes: true, attributeFilter: ['min'] });
      observer.observe(this.elements.endDateInput, { attributes: true, attributeFilter: ['min'] });

      // カスタム日付選択UIを作成
      this.createCustomDatePicker();
    }
  }

  /**
   * カスタム日付選択UIを作成
   */
  createCustomDatePicker() {
    if (!this.elements.startDateInput || !this.elements.endDateInput) {
      return;
    }

    // 現在の日付を取得（システム時間から直接取得）
    const today = this.getCurrentDate();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0');
    const dd = String(today.getDate()).padStart(2, '0');
    const formattedToday = `${yyyy}-${mm}-${dd}`;

    // カスタム日付選択UIのコンテナを作成
    const startDateContainer = document.createElement('div');
    startDateContainer.className = 'custom-date-picker';
    startDateContainer.style.position = 'relative';
    startDateContainer.style.display = 'inline-block';
    startDateContainer.style.marginBottom = '10px';

    const endDateContainer = document.createElement('div');
    endDateContainer.className = 'custom-date-picker';
    endDateContainer.style.position = 'relative';
    endDateContainer.style.display = 'inline-block';
    endDateContainer.style.marginBottom = '10px';

    // ラベルを作成
    const startDateLabel = document.createElement('label');
    startDateLabel.textContent = '開始日';
    startDateLabel.style.display = 'block';
    startDateLabel.style.marginBottom = '5px';
    startDateLabel.style.fontWeight = 'bold';

    const endDateLabel = document.createElement('label');
    endDateLabel.textContent = '終了日';
    endDateLabel.style.display = 'block';
    endDateLabel.style.marginBottom = '5px';
    endDateLabel.style.fontWeight = 'bold';

    // テキスト入力フィールドを作成
    const startDateText = document.createElement('input');
    startDateText.type = 'text';
    startDateText.id = 'rental-start-date-text';
    startDateText.placeholder = '開始日 (YYYY-MM-DD)';
    startDateText.value = this.elements.startDateInput.value || formattedToday;
    startDateText.style.width = '150px';
    startDateText.style.padding = '8px';
    startDateText.style.border = '1px solid #ccc';
    startDateText.style.borderRadius = '4px';

    const endDateText = document.createElement('input');
    endDateText.type = 'text';
    endDateText.id = 'rental-end-date-text';
    endDateText.placeholder = '終了日 (YYYY-MM-DD)';
    endDateText.value = this.elements.endDateInput.value || formattedToday;
    endDateText.style.width = '150px';
    endDateText.style.padding = '8px';
    endDateText.style.border = '1px solid #ccc';
    endDateText.style.borderRadius = '4px';

    // イベントリスナーを設定
    startDateText.addEventListener('change', () => {
      // 日付の形式をチェック
      const datePattern = /^\d{4}-\d{2}-\d{2}$/;
      if (datePattern.test(startDateText.value)) {
        this.elements.startDateInput.value = startDateText.value;
        this.handleDateChange();
      } else {
        alert('日付の形式が正しくありません。YYYY-MM-DD形式で入力してください。');
        startDateText.value = this.elements.startDateInput.value || formattedToday;
      }
    });

    endDateText.addEventListener('change', () => {
      // 日付の形式をチェック
      const datePattern = /^\d{4}-\d{2}-\d{2}$/;
      if (datePattern.test(endDateText.value)) {
        this.elements.endDateInput.value = endDateText.value;
        this.handleDateChange();
      } else {
        alert('日付の形式が正しくありません。YYYY-MM-DD形式で入力してください。');
        endDateText.value = this.elements.endDateInput.value || formattedToday;
      }
    });

    // 元の日付選択フィールドを非表示にする
    this.elements.startDateInput.style.display = 'none';
    this.elements.endDateInput.style.display = 'none';

    // カスタム日付選択UIを追加
    startDateContainer.appendChild(startDateLabel);
    startDateContainer.appendChild(startDateText);
    endDateContainer.appendChild(endDateLabel);
    endDateContainer.appendChild(endDateText);

    this.elements.startDateInput.parentNode.insertBefore(startDateContainer, this.elements.startDateInput);
    this.elements.endDateInput.parentNode.insertBefore(endDateContainer, this.elements.endDateInput);

    console.log('カスタム日付選択UIを作成しました');
  }

  /**
   * イベントリスナーの設定
   */
  setupEventListeners() {
    // 日付入力のイベントリスナー
    if (this.elements.startDateInput && this.elements.endDateInput) {
      // イベントリスナーを設定
      this.elements.startDateInput.addEventListener('change', () => this.handleDateChange());
      this.elements.endDateInput.addEventListener('change', () => this.handleDateChange());
    }

    // カートに追加ボタンのイベントリスナー
    if (this.elements.addToCartButton) {
      this.elements.addToCartButton.addEventListener('click', () => this.handleAddToCart());
    }
  }

  /**
   * 日付変更時の処理
   */
  handleDateChange() {
    // 入力値の取得
    const startDateStr = this.elements.startDateInput.value;
    const endDateStr = this.elements.endDateInput.value;

    // 日付のバリデーション
    if (!startDateStr || !endDateStr) {
      this.updateState({
        startDate: null,
        endDate: null,
        rentalDays: 0,
        totalPrice: 0,
        depositAmount: 0,
        isAvailable: false,
        errorMessage: '開始日と終了日を選択してください'
      });
      this.updateUI();
      return;
    }

    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);

    // 終了日が開始日より前の場合
    if (endDate < startDate) {
      this.updateState({
        startDate: startDate,
        endDate: null,
        rentalDays: 0,
        totalPrice: 0,
        depositAmount: 0,
        isAvailable: false,
        errorMessage: '終了日は開始日より後の日付を選択してください'
      });
      this.updateUI();
      return;
    }

    // レンタル日数の計算
    const rentalDays = this.calculateRentalDays(startDate, endDate);

    // 料金の計算
    const { totalPrice, depositAmount } = this.calculatePrice(rentalDays);

    // 状態の更新
    this.updateState({
      startDate: startDate,
      endDate: endDate,
      rentalDays: rentalDays,
      totalPrice: totalPrice,
      depositAmount: depositAmount,
      isAvailable: true,
      errorMessage: ''
    });

    // 在庫状況の確認
    this.checkAvailability();
  }

  /**
   * レンタル日数の計算
   */
  calculateRentalDays(startDate, endDate) {
    // 日曜日と祝日を除外した営業日数を計算
    // 簡易版: 日数の差を計算
    const diffTime = Math.abs(endDate - startDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // 初日も含める
    return diffDays;
  }

  /**
   * 料金の計算
   */
  calculatePrice(days) {
    if (days <= 0 || !this.options.basePrice) {
      return { totalPrice: 0, depositAmount: 0 };
    }

    let totalPrice = 0;

    // 1日目: 100%
    totalPrice += this.options.basePrice;

    // 2-6日目: 20%
    const days2to6 = Math.min(days - 1, 5);
    if (days2to6 > 0) {
      totalPrice += this.options.basePrice * 0.2 * days2to6;
    }

    // 7日目以降: 10%
    const days7plus = Math.max(days - 6, 0);
    if (days7plus > 0) {
      totalPrice += this.options.basePrice * 0.1 * days7plus;
    }

    // デポジット: 基本料金の10%
    const depositAmount = Math.round(this.options.basePrice * 0.1);

    return {
      totalPrice: Math.round(totalPrice),
      depositAmount: depositAmount
    };
  }

  /**
   * 在庫状況の確認
   */
  async checkAvailability() {
    if (!this.state.startDate || !this.state.endDate) {
      return;
    }

    this.updateState({ isLoading: true, errorMessage: '' });
    this.updateUI();

    // APIエンドポイントとリクエストボディをログに出力
    const requestBody = {
      productId: this.options.productId,
      variantId: this.options.variantId,
      startDate: this.formatDate(this.state.startDate),
      endDate: this.formatDate(this.state.endDate)
    };

    console.log('在庫確認APIリクエスト:', {
      endpoint: this.options.apiEndpoint,
      body: requestBody
    });

    try {
      const response = await fetch(this.options.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      console.log('在庫確認APIレスポンス:', {
        status: response.status,
        statusText: response.statusText
      });

      const data = await response.json();
      console.log('在庫確認APIデータ:', data);

      if (!data.success) {
        this.updateState({
          isAvailable: false,
          errorMessage: data.error || '選択した期間は予約できません'
        });
      } else {
        this.updateState({
          isAvailable: data.available,
          errorMessage: data.available ? '' : '選択した期間は予約できません'
        });
      }
    } catch (error) {
      console.error('在庫確認エラー:', error);
      this.updateState({
        isAvailable: false,
        errorMessage: '在庫状況の確認中にエラーが発生しました'
      });
    } finally {
      this.updateState({ isLoading: false });
      this.updateUI();
    }
  }

  /**
   * カートに追加
   */
  async handleAddToCart() {
    // 日付と在庫のチェック
    if (!this.state.startDate || !this.state.endDate) {
      this.updateState({ errorMessage: 'レンタル期間を選択してください' });
      this.updateUI();
      return;
    }

    if (!this.state.isAvailable) {
      this.updateState({ errorMessage: '選択した期間は予約できません' });
      this.updateUI();
      return;
    }

    // ローディング表示
    this.updateState({ isLoading: true, errorMessage: '' });
    this.updateUI();

    try {
      // Shopifyの標準カートに追加するためのデータを準備
      const formData = {
        'items': [{
          'id': this.options.variantId,
          'quantity': 1,
          'properties': {
            'レンタル開始日': this.formatDate(this.state.startDate),
            'レンタル終了日': this.formatDate(this.state.endDate),
            'レンタル日数': this.state.rentalDays,
            '予約タイプ': 'CONFIRMED',
            'デポジット': `¥${this.state.depositAmount.toLocaleString()}`
          }
        }]
      };

      console.log('Shopifyカートに追加するデータ:', formData);

      // Shopifyのカート追加APIを呼び出す
      const response = await fetch('/cart/add.js', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();
      console.log('Shopifyカート追加レスポンス:', data);

      if (response.ok) {
        // 成功メッセージを表示
        alert('商品がカートに追加されました');

        // カートページに遷移するかどうかを確認
        if (confirm('カートページに移動しますか？')) {
          window.location.href = '/cart';
        }
      } else {
        throw new Error(data.description || 'カートへの追加に失敗しました');
      }
    } catch (error) {
      console.error('カート追加エラー:', error);
      this.updateState({ errorMessage: 'カートへの追加中にエラーが発生しました' });
    } finally {
      this.updateState({ isLoading: false });
      this.updateUI();
    }
  }

  /**
   * 状態の更新
   */
  updateState(newState) {
    this.state = { ...this.state, ...newState };
  }

  /**
   * UIの更新
   */
  updateUI() {
    // レンタル日数の表示
    if (this.elements.rentalDaysElement) {
      this.elements.rentalDaysElement.textContent = this.state.rentalDays;
    }

    // 料金の表示
    if (this.elements.totalPriceElement) {
      this.elements.totalPriceElement.textContent = this.formatPrice(this.state.totalPrice);
    }

    // デポジットの表示
    if (this.elements.depositElement) {
      this.elements.depositElement.textContent = this.formatPrice(this.state.depositAmount);
    }

    // エラーメッセージの表示
    if (this.elements.errorMessageElement) {
      this.elements.errorMessageElement.textContent = this.state.errorMessage;
      this.elements.errorMessageElement.style.display = this.state.errorMessage ? 'block' : 'none';
    }

    // ローディングインジケーターの表示
    if (this.elements.loadingIndicator) {
      this.elements.loadingIndicator.style.display = this.state.isLoading ? 'block' : 'none';
    }

    // カートに追加ボタンの有効/無効
    if (this.elements.addToCartButton) {
      this.elements.addToCartButton.disabled = !this.state.isAvailable || this.state.isLoading;
    }
  }

  /**
   * 日付のフォーマット (YYYY-MM-DD)
   */
  formatDate(date) {
    if (!date) return '';
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * 金額のフォーマット
   */
  formatPrice(price) {
    return '¥' + parseInt(price).toLocaleString();
  }
}

// グローバルオブジェクトとして公開
window.ProductRental = ProductRental;
