import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import { 
  Page, 
  LegacyCard, 
  ResourceList, 
  ResourceItem, 
  Filters, 
  Badge, 
  EmptyState, 
  Text,
  Pagination
} from '@shopify/polaris';
import { useState } from 'react';
import { prisma } from '../db.server';
import { MaintenanceStatus, MaintenanceType } from '@prisma/client';

// ステータスに応じたバッジ設定
const statusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  SCHEDULED: { tone: 'info', label: '予定' },
  IN_PROGRESS: { tone: 'attention', label: '進行中' },
  COMPLETED: { tone: 'success', label: '完了' },
  CANCELLED: { tone: 'critical', label: 'キャンセル' }
};

// メンテナンスタイプに応じたラベル
const typeLabels: Record<string, string> = {
  REGULAR_INSPECTION: '定期点検',
  REPAIR: '修理',
  CLEANING: 'クリーニング',
  OTHER: 'その他'
};

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  
  // クエリパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  const limit = parseInt(url.searchParams.get('limit') || '50', 10);
  const status = url.searchParams.get('status') || undefined;
  const type = url.searchParams.get('type') || undefined;
  const query = url.searchParams.get('query') || undefined;
  
  // 検索条件を構築
  const where: any = {};
  
  if (status) {
    where.status = status as MaintenanceStatus;
  }
  
  if (type) {
    where.type = type as MaintenanceType;
  }
  
  if (query) {
    where.OR = [
      { notes: { contains: query, mode: 'insensitive' } },
      { 
        product: { 
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { sku: { contains: query } }
          ]
        }
      }
    ];
  }
  
  // メンテナンスデータを取得
  const maintenances = await prisma.maintenance.findMany({
    where,
    include: {
      product: true
    },
    orderBy: [
      { status: 'asc' },
      { startDate: 'asc' }
    ],
    skip: (page - 1) * limit,
    take: limit
  });
  
  // 総件数を取得
  const total = await prisma.maintenance.count({ where });
  
  return json({
    maintenances: maintenances.map(maintenance => ({
      id: maintenance.id,
      productId: maintenance.productId,
      productTitle: maintenance.product.title,
      productSku: maintenance.product.sku,
      status: maintenance.status,
      type: maintenance.type,
      startDate: maintenance.startDate.toISOString(),
      endDate: maintenance.endDate?.toISOString() || null,
      completionDate: maintenance.completionDate?.toISOString() || null,
      notes: maintenance.notes || '',
      createdAt: maintenance.createdAt.toISOString(),
      updatedAt: maintenance.updatedAt.toISOString()
    })),
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}

export default function MaintenanceIndex() {
  const { maintenances, pagination } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  
  // メンテナンス詳細ページへ移動
  const handleMaintenanceClick = (maintenanceId: string) => {
    navigate(`/maintenance/${maintenanceId}`);
  };
  
  // ステータスフィルターを適用
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    const params = new URLSearchParams(window.location.search);
    
    if (value) {
      params.set('status', value);
    } else {
      params.delete('status');
    }
    
    navigate(`/maintenance?${params.toString()}`);
  };
  
  // タイプフィルターを適用
  const handleTypeFilterChange = (value: string) => {
    setTypeFilter(value);
    const params = new URLSearchParams(window.location.search);
    
    if (value) {
      params.set('type', value);
    } else {
      params.delete('type');
    }
    
    navigate(`/maintenance?${params.toString()}`);
  };
  
  // 検索クエリを適用
  const handleSearchSubmit = () => {
    const params = new URLSearchParams(window.location.search);
    
    if (searchQuery) {
      params.set('query', searchQuery);
    } else {
      params.delete('query');
    }
    
    navigate(`/maintenance?${params.toString()}`);
  };
  
  // ページネーション処理
  const handlePaginationChange = (newPage: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set('page', newPage.toString());
    navigate(`/maintenance?${params.toString()}`);
  };
  
  // 日付をフォーマット
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };
  
  // フィルターオプションを生成
  const filters = [
    {
      key: 'status',
      label: 'ステータス',
      filter: (
        <select
          value={statusFilter}
          onChange={(e) => handleStatusFilterChange(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="">すべて</option>
          <option value="SCHEDULED">予定</option>
          <option value="IN_PROGRESS">進行中</option>
          <option value="COMPLETED">完了</option>
          <option value="CANCELLED">キャンセル</option>
        </select>
      ),
      shortcut: true,
    },
    {
      key: 'type',
      label: 'タイプ',
      filter: (
        <select
          value={typeFilter}
          onChange={(e) => handleTypeFilterChange(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="">すべて</option>
          <option value="REGULAR_INSPECTION">定期点検</option>
          <option value="REPAIR">修理</option>
          <option value="CLEANING">クリーニング</option>
          <option value="OTHER">その他</option>
        </select>
      ),
      shortcut: true,
    }
  ];
  
  return (
    <Page
      title="メンテナンス管理"
      primaryAction={{
        content: "新規メンテナンス",
        url: "/maintenance/new"
      }}
    >
      <LegacyCard>
        <ResourceList
          resourceName={{ singular: 'メンテナンス', plural: 'メンテナンス' }}
          items={maintenances}
          renderItem={(maintenance) => {
            const { id, productTitle, productSku, status, type, startDate, endDate, completionDate, notes } = maintenance;
            const badge = statusBadgeMap[status] || { tone: 'new', label: status };
            const typeLabel = typeLabels[type] || type;
            
            const shortcutActions = [
              {
                content: '詳細',
                url: `/maintenance/${id}`,
              },
              {
                content: '編集',
                url: `/maintenance/edit/${id}`,
              }
            ];
            
            if (status === 'SCHEDULED' || status === 'IN_PROGRESS') {
              shortcutActions.push({
                content: '完了',
                url: `/maintenance/complete/${id}`,
              });
            }
            
            return (
              <ResourceItem
                id={id}
                onClick={() => handleMaintenanceClick(id)}
                shortcutActions={shortcutActions}
                persistActions
              >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <Text as="h3" variant="bodyMd" fontWeight="bold">
                      {productTitle}
                    </Text>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        商品コード: {productSku}
                      </Text>
                    </div>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        タイプ: {typeLabel}
                      </Text>
                    </div>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        期間: {formatDate(startDate)} 〜 {formatDate(endDate)}
                      </Text>
                    </div>
                    {completionDate && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          完了日: {formatDate(completionDate)}
                        </Text>
                      </div>
                    )}
                    {notes && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          備考: {notes.length > 50 ? `${notes.substring(0, 50)}...` : notes}
                        </Text>
                      </div>
                    )}
                  </div>
                  <div>
                    <Badge tone={badge.tone}>{badge.label}</Badge>
                  </div>
                </div>
              </ResourceItem>
            );
          }}
          filterControl={
            <Filters
              queryValue={searchQuery}
              filters={filters}
              onQueryChange={setSearchQuery}
              onQueryClear={() => setSearchQuery('')}
              onClearAll={() => {
                setSearchQuery('');
                setStatusFilter('');
                setTypeFilter('');
                navigate('/maintenance');
              }}
              onQueryBlur={handleSearchSubmit}
              hideQueryField={false}
            />
          }
          emptyState={
            <EmptyState
              heading="メンテナンス情報がありません"
              image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
            >
              <p>メンテナンス情報を登録して、商品のメンテナンス状況を管理しましょう。</p>
            </EmptyState>
          }
        />
        {pagination.totalPages > 1 && (
          <div style={{ padding: '16px', display: 'flex', justifyContent: 'center' }}>
            <Pagination
              hasPrevious={pagination.page > 1}
              onPrevious={() => handlePaginationChange(pagination.page - 1)}
              hasNext={pagination.page < pagination.totalPages}
              onNext={() => handlePaginationChange(pagination.page + 1)}
            />
          </div>
        )}
      </LegacyCard>
      <div style={{ marginTop: '16px', textAlign: 'right' }}>
        <Text as="p" variant="bodySm" tone="subdued">
          合計 {pagination.total} 件のメンテナンス ( {pagination.page} / {pagination.totalPages} ページ )
        </Text>
      </div>
    </Page>
  );
}
