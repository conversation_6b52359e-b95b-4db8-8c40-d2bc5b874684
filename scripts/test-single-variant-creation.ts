/**
 * 単一商品でのバリエーション作成テスト（在庫設定なし）
 */

import { authenticate } from '../app/shopify.server';
import { VariantAutoCreatorService } from '../app/services/shopify/variant-auto-creator.service';

async function testSingleVariantCreation() {
  console.log('=== 単一商品バリエーション作成テスト開始 ===');

  try {
    const { admin } = await authenticate.admin(new Request('http://localhost'));
    const variantService = new VariantAutoCreatorService();

    // テスト商品データ
    const testProduct = {
      title: 'テスト商品 バリエーション作成確認',
      handle: 'test-variant-creation',
      productType: 'グラス',
      vendor: 'テストベンダー',
      tags: ['テスト', 'レンタル'],
      status: 'ACTIVE',
      variants: [{
        title: 'デフォルト',
        price: '1500',
        sku: 'TEST-VAR-001',
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY'
      }]
    };

    // 1. 商品作成
    console.log('1. テスト商品を作成中...');
    const createResponse = await admin.graphql(`
      mutation productCreate($input: ProductInput!) {
        productCreate(input: $input) {
          product {
            id
            title
            handle
            variants(first: 1) {
              edges {
                node {
                  id
                  title
                  price
                  sku
                }
              }
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `, {
      input: testProduct
    });

    const createData = await createResponse.json();
    
    if (createData.errors || createData.data.productCreate.userErrors.length > 0) {
      throw new Error(`商品作成エラー: ${JSON.stringify(createData.errors || createData.data.productCreate.userErrors)}`);
    }

    const product = createData.data.productCreate.product;
    const productId = product.id;
    const shopifyProductId = productId.split('/').pop();
    console.log(`✅ 商品作成成功: ${shopifyProductId}`);
    console.log(`商品名: ${product.title}`);
    console.log(`初期バリエーション: ${product.variants.edges[0].node.title} - ¥${product.variants.edges[0].node.price}`);

    // 2. バリエーション自動作成（在庫設定なし）
    console.log('\n2. バリエーション自動作成をテスト中...');
    
    const result = await variantService.createMissingVariants(admin, shopifyProductId, {
      basePrice: 1500,
      productStatus: 'available',
      location: 'NY',
      createProvisionalVariants: false
    });

    console.log('\n=== バリエーション作成結果 ===');
    console.log(`作成されたバリエーション数: ${result.createdVariants.length}`);
    console.log(`在庫設定: ${result.inventoryUpdated ? '成功' : 'スキップ'}`);
    console.log(`成功: ${result.success}`);
    
    if (result.errors.length > 0) {
      console.log('エラー:');
      result.errors.forEach(error => console.log(`  - ${error}`));
    }

    // 3. 最終的なバリエーション確認
    console.log('\n3. 最終バリエーション一覧を確認中...');
    const finalResponse = await admin.graphql(`
      query getProductVariants($id: ID!) {
        product(id: $id) {
          title
          variants(first: 20) {
            edges {
              node {
                id
                title
                price
                sku
                inventoryItem {
                  id
                  tracked
                }
              }
            }
          }
        }
      }
    `, {
      id: productId
    });

    const finalData = await finalResponse.json();
    
    if (finalData.errors) {
      throw new Error(`最終確認エラー: ${JSON.stringify(finalData.errors)}`);
    }

    const finalProduct = finalData.data.product;
    console.log(`\n📦 最終商品: ${finalProduct.title}`);
    console.log(`総バリエーション数: ${finalProduct.variants.edges.length}`);
    
    finalProduct.variants.edges.forEach((variantEdge: any, index: number) => {
      const variant = variantEdge.node;
      console.log(`  ${index + 1}. ${variant.title}: ¥${variant.price} (SKU: ${variant.sku})`);
    });

    // 4. クリーンアップ
    console.log('\n4. テスト商品を削除中...');
    await admin.graphql(`
      mutation productDelete($input: ProductDeleteInput!) {
        productDelete(input: $input) {
          deletedProductId
          userErrors {
            field
            message
          }
        }
      }
    `, {
      input: {
        id: productId
      }
    });

    console.log('✅ テスト商品削除完了');

    // 5. 結果サマリー
    console.log('\n=== テスト結果サマリー ===');
    if (result.success && result.createdVariants.length === 8) {
      console.log('🎉 バリエーション作成テスト成功！');
      console.log('✅ 8個のレンタル期間バリエーションが正常に作成されました');
      console.log('⚠️ 在庫設定は手動で行う必要があります');
    } else {
      console.log('❌ バリエーション作成テストに問題があります');
    }

  } catch (error) {
    console.error('❌ テストエラー:', error);
  }

  console.log('\n=== 単一商品バリエーション作成テスト完了 ===');
}

// スクリプト実行
if (import.meta.url === new URL(import.meta.url).href) {
  testSingleVariantCreation().catch(console.error);
}

export { testSingleVariantCreation };
