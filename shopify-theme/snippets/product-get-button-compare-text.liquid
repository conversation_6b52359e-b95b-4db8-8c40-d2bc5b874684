{%- if customer and customer.metafields.compare -%}
    {%- for item in customer.metafields.compare -%}
        {%- assign compare_id = item[0] | abs -%}
        {%- assign compare_handle = item[1] -%}
        {%- if current_variant_id == compare_id and product.handle == compare_handle -%}
            {%- assign present_in_compare = true -%}
        {%- endif -%}
    {%- endfor -%}
{%- endif -%}
<a href="/account" class="btn btn--text btn--status px-md-0 js-store-lists-add-compare"
    {% if present_in_compare %} data-button-status="added"{% endif %}>
    <span class="d-flex flex-center"><i class="mr-5 mb-1">{% render 'icon-theme-039' %}</i>{{ 'products.product.add_to_compare.title' | t }}</span>
    <span class="d-flex flex-center" data-button-content="added"><i class="mr-5 mb-1">{% render 'icon-theme-235' %}</i>{{ 'products.product.add_to_compare.added' | t }}</span>
</a>
{%- assign present_in_compare = false -%}
