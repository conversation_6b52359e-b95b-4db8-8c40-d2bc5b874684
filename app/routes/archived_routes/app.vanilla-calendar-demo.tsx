/**
 * Vanilla Calendar デモページ
 *
 * Vanilla Calendar を使用したカレンダーコンポーネントのデモページです。
 * 現在は無効化されています。
 */
/* 無効化 - 元の DatePicker に戻すため

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  BlockStack,
  Select,
  Box,
  Banner,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { useState, useCallback } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { VanillaCalendar } from "../components/Calendar/VanillaCalendar";
import { formatJapaneseDateWithWeekday } from "../components/Calendar/VanillaCalendar";

// ローダーデータの型定義
interface LoaderData {
  products: Array<{
    id: string;
    title: string;
    sku: string;
    status: string;
  }>;
  bookings: Array<{
    id: string;
    productId: string;
    startDate: string;
    endDate: string;
    status: string;
    bookingType: string;
    priority: number;
    customerEmail: string | null;
    customerName: string | null;
    product: {
      id: string;
      title: string;
      sku: string;
    };
  }>;
}

// ローダー関数
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);

  // 商品データを取得
  const products = await prisma.product.findMany({
    where: {
      shop: session.shop,
    },
    select: {
      id: true,
      title: true,
      sku: true,
      status: true,
    },
  });

  // 予約データを取得
  const bookings = await prisma.booking.findMany({
    where: {
      shop: session.shop,
    },
    select: {
      id: true,
      productId: true,
      startDate: true,
      endDate: true,
      status: true,
      bookingType: true,
      priority: true,
      customerEmail: true,
      customerName: true,
      product: {
        select: {
          id: true,
          title: true,
          sku: true,
        },
      },
    },
  });

  return json<LoaderData>({
    products,
    bookings,
  });
}

// メインコンポーネント
/* export default function VanillaCalendarDemo() {
  const { products, bookings } = useLoaderData<LoaderData>();

  // 選択された商品ID
  const [selectedProductId, setSelectedProductId] = useState<string>("");

  // 選択された日付範囲
  const [selectedRange, setSelectedRange] = useState<{
    startDate: Date | null;
    endDate: Date | null;
  }>({
    startDate: null,
    endDate: null,
  });

  // 商品選択ハンドラー
  const handleProductChange = useCallback((value: string) => {
    setSelectedProductId(value);
    setSelectedRange({ startDate: null, endDate: null });
  }, []);

  // 日付範囲選択ハンドラー
  const handleRangeSelect = useCallback((startDate: Date, endDate: Date) => {
    setSelectedRange({ startDate, endDate });
  }, []);

  // 選択された商品に関連する予約のみをフィルタリング
  const filteredBookings = selectedProductId
    ? bookings.filter((booking) => booking.productId === selectedProductId)
    : bookings;

  // 日付文字列をDate型に変換する関数
  const convertBookingDates = (bookings: any[]) => {
    return bookings.map(booking => ({
      ...booking,
      startDate: booking.startDate ? new Date(booking.startDate) : null,
      endDate: booking.endDate ? new Date(booking.endDate) : null,
    }));
  };

  // 商品選択オプション
  const productOptions = [
    { label: "商品を選択", value: "" },
    ...products.map((product) => ({
      label: product.title,
      value: product.id,
    })),
  ];

  return (
    <Page title="Vanilla Calendar デモ" backAction={{ content: "戻る", url: "/app" }}>
      <TitleBar title="Vanilla Calendar デモ" />

      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text variant="headingMd" as="h2">
                Vanilla Calendar デモ
              </Text>

              <Text as="p">
                このページでは、vanilla-calendar-pro ライブラリを使用したカレンダーコンポーネントのデモを表示しています。
                日付選択、予約管理、カレンダー表示に関するすべてのロジックが一元管理されています。
              </Text>

              <Select
                label="商品を選択"
                options={productOptions}
                value={selectedProductId}
                onChange={handleProductChange}
                placeholder="商品を選択してください"
              />
            </BlockStack>
          </Card>
        </Layout.Section>

        {selectedProductId && (
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">
                  予約カレンダー
                </Text>

                <VanillaCalendar
                  bookings={convertBookingDates(filteredBookings)}
                  settings={{
                    excludeSundays: true,
                    excludeHolidays: true,
                    excludeNewYear: true,
                    minRentalDays: 1,
                    maxRentalDays: 30,
                  }}
                  minDate={new Date()}
                  maxDate={new Date(new Date().setFullYear(new Date().getFullYear() + 1))}
                  onRangeSelect={handleRangeSelect}
                  showSelectedDates={true}
                  showLegend={true}
                  showValidationErrors={true}
                />
              </BlockStack>
            </Card>
          </Layout.Section>
        )}

        {selectedRange.startDate && selectedRange.endDate && (
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">
                  選択された日付範囲
                </Text>

                <Box background="bg-surface-success" padding="300" borderRadius="100">
                  <BlockStack gap="200">
                    <Text as="p">
                      開始日: {formatJapaneseDateWithWeekday(selectedRange.startDate)}
                    </Text>
                    <Text as="p">
                      終了日: {formatJapaneseDateWithWeekday(selectedRange.endDate)}
                    </Text>
                    <Text as="p">
                      期間: {Math.round((selectedRange.endDate.getTime() - selectedRange.startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1}日間
                    </Text>
                  </BlockStack>
                </Box>

                <Banner tone="info">
                  <Text as="p">
                    選択された日付範囲は、バックエンドとフロントエンドで共通して使用できます。
                    この情報を使用して、予約の作成や更新を行うことができます。
                  </Text>
                </Banner>
              </BlockStack>
            </Card>
          </Layout.Section>
        )}

        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text variant="headingMd" as="h2">
                使用方法
              </Text>

              <Text as="p">
                Vanilla Calendar コンポーネントは、以下のファイルで実装されています：
              </Text>

              <Box background="bg-surface-secondary" padding="300" borderRadius="100">
                <BlockStack gap="200">
                  <Text as="p" fontWeight="bold">
                    コンポーネント:
                  </Text>
                  <Text as="p" fontWeight="medium">
                    app/components/Calendar/VanillaCalendar.tsx
                  </Text>
                  <Text as="p">
                    vanilla-calendar-pro ライブラリをベースにしたカスタマイズ可能なカレンダーコンポーネントです。
                    予約データの表示、日付選択、バリデーションなどの機能を提供します。
                  </Text>
                </BlockStack>
              </Box>

              <Box background="bg-surface-secondary" padding="300" borderRadius="100">
                <BlockStack gap="200">
                  <Text as="p" fontWeight="bold">
                    スタイル:
                  </Text>
                  <Text as="p" fontWeight="medium">
                    app/styles/vanilla-calendar-custom.css
                  </Text>
                  <Text as="p">
                    Vanilla Calendar のカスタムスタイルを定義しています。
                    予約状態や祝日などの表示スタイルをカスタマイズできます。
                  </Text>
                </BlockStack>
              </Box>

              <Box background="bg-surface-secondary" padding="300" borderRadius="100">
                <BlockStack gap="200">
                  <Text as="p" fontWeight="bold">
                    使用例:
                  </Text>
                  <Text as="p" fontWeight="medium">
                    app/routes/app.vanilla-calendar-demo.tsx
                  </Text>
                  <Text as="p">
                    Vanilla Calendar コンポーネントの使用例を示すページです。
                    商品選択、予約データの表示、日付範囲の選択などの機能を実装しています。
                  </Text>
                </BlockStack>
              </Box>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
} */
