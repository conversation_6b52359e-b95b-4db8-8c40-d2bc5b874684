/**
 * variation_type メタフィールド修正スクリプト
 *
 * 現在レンタル日数が入っているvariation_typeを
 * 正しいバリエーションタイプ分類に修正
 */

import { PrismaClient } from '@prisma/client';
import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();
const SHOPIFY_SHOP = process.env.SHOPIFY_SHOP;
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

/**
 * Shopify GraphQL APIを呼び出し
 */
async function callShopifyGraphQL(query, variables = {}) {
  const response = await fetch(`https://${SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN
    },
    body: JSON.stringify({ query, variables })
  });

  const data = await response.json();
  if (data.errors) {
    throw new Error(`Shopify API エラー: ${JSON.stringify(data.errors)}`);
  }
  return data.data;
}

/**
 * 商品のメタフィールドを取得
 */
async function getProductMetafields(productId) {
  const query = `
    query getProduct($id: ID!) {
      product(id: $id) {
        id
        title
        metafields(first: 50) {
          edges {
            node {
              id
              namespace
              key
              value
              type
            }
          }
        }
      }
    }
  `;

  return await callShopifyGraphQL(query, { id: productId });
}

/**
 * メタフィールドを更新
 */
async function updateMetafield(metafieldId, newValue, productId) {
  const mutation = `
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const metafields = [{
    ownerId: productId,
    namespace: "rental",
    key: "variation_type",
    value: newValue,
    type: "single_line_text_field"
  }];

  return await callShopifyGraphQL(mutation, { metafields });
}

/**
 * SKUからバリエーションタイプを推定
 */
function determineVariationType(sku, title) {
  // レンタル商品は基本的にレンタル期間バリエーション
  if (sku && (sku.includes('-1D') || sku.includes('-2D') || sku.includes('レンタル'))) {
    return 'rental_period';
  }

  // 色に関するキーワード
  const colorKeywords = ['ベージュ', 'ホワイト', 'ブラック', 'ブラウン', 'グレー', 'レッド', 'ブルー'];
  if (colorKeywords.some(keyword => title.includes(keyword))) {
    return 'color';
  }

  // サイズに関するキーワード
  const sizeKeywords = ['1シーター', '2シーター', '3シーター', 'S', 'M', 'L'];
  if (sizeKeywords.some(keyword => title.includes(keyword))) {
    return 'size';
  }

  // 素材に関するキーワード
  const materialKeywords = ['ガラス', 'シルバー', '木製', 'スチール', 'ファブリック'];
  if (materialKeywords.some(keyword => title.includes(keyword))) {
    return 'material';
  }

  // デフォルトはレンタル期間
  return 'rental_period';
}

/**
 * variation_typeの現在の値を分析
 */
async function analyzeVariationType() {
  console.log('🔍 === variation_type 現状分析 ===');
  console.log(`対象ショップ: ${SHOPIFY_SHOP}`);
  console.log(`実行時刻: ${new Date().toLocaleString('ja-JP')}`);
  console.log('');

  try {
    // Prismaから商品データを取得
    const products = await prisma.product.findMany({
      where: {
        shop: SHOPIFY_SHOP
      },
      select: {
        id: true,
        shopifyId: true,
        title: true,
        sku: true
      }
    });

    console.log(`対象商品数: ${products.length}件`);
    console.log('');

    const analysisResults = [];

    for (const product of products) {
      try {
        console.log(`分析中: ${product.sku} - ${product.title}`);

        // Shopify IDをGlobal ID形式に変換
        let globalId = product.shopifyId;
        if (!globalId.startsWith('gid://shopify/Product/')) {
          globalId = `gid://shopify/Product/${globalId}`;
        }

        // Shopifyからメタフィールドを取得
        const shopifyData = await getProductMetafields(globalId);
        const shopifyProduct = shopifyData.product;

        // variation_typeメタフィールドを探す
        const variationTypeField = shopifyProduct.metafields.edges.find(edge =>
          edge.node.key === 'variation_type'
        );

        if (variationTypeField) {
          const currentValue = variationTypeField.node.value;
          const recommendedValue = determineVariationType(product.sku, product.title);

          console.log(`   現在の値: ${currentValue}`);
          console.log(`   推奨値: ${recommendedValue}`);

          const needsUpdate = currentValue !== recommendedValue;
          console.log(`   更新必要: ${needsUpdate ? 'Yes' : 'No'}`);

          analysisResults.push({
            productId: globalId,
            sku: product.sku,
            title: product.title,
            metafieldId: variationTypeField.node.id,
            currentValue,
            recommendedValue,
            needsUpdate
          });
        } else {
          console.log('   variation_type: 未設定');

          const recommendedValue = determineVariationType(product.sku, product.title);
          console.log(`   推奨値: ${recommendedValue}`);

          analysisResults.push({
            productId: globalId,
            sku: product.sku,
            title: product.title,
            metafieldId: null,
            currentValue: null,
            recommendedValue,
            needsUpdate: true
          });
        }

        console.log('');

      } catch (error) {
        console.log(`   ❌ 分析エラー: ${error.message}`);
      }
    }

    // 分析結果サマリー
    console.log('📊 === 分析結果サマリー ===');
    const needsUpdateCount = analysisResults.filter(r => r.needsUpdate).length;
    const hasMetafieldCount = analysisResults.filter(r => r.metafieldId).length;

    console.log(`総商品数: ${analysisResults.length}件`);
    console.log(`variation_type設定済み: ${hasMetafieldCount}件`);
    console.log(`更新が必要: ${needsUpdateCount}件`);
    console.log('');

    // 推奨値の分布
    const valueDistribution = {};
    analysisResults.forEach(result => {
      valueDistribution[result.recommendedValue] = (valueDistribution[result.recommendedValue] || 0) + 1;
    });

    console.log('推奨値の分布:');
    Object.entries(valueDistribution).forEach(([value, count]) => {
      console.log(`   ${value}: ${count}件`);
    });

    return analysisResults;

  } catch (error) {
    console.error('❌ 分析エラー:', error);
    return [];
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * variation_typeを修正
 */
async function fixVariationType() {
  console.log('🛠️ === variation_type 修正実行 ===');

  try {
    // まず分析を実行
    const analysisResults = await analyzeVariationType();
    const needsUpdate = analysisResults.filter(r => r.needsUpdate);

    if (needsUpdate.length === 0) {
      console.log('✅ 修正が必要な商品はありません');
      return;
    }

    console.log(`修正対象: ${needsUpdate.length}件`);
    console.log('');

    let successCount = 0;
    let errorCount = 0;

    for (const item of needsUpdate) {
      try {
        console.log(`修正中: ${item.sku} - ${item.title}`);
        console.log(`   ${item.currentValue || '未設定'} → ${item.recommendedValue}`);

        if (item.metafieldId) {
          // 既存のメタフィールドを更新
          const result = await updateMetafield(item.metafieldId, item.recommendedValue, item.productId);

          if (result.metafieldsSet.userErrors.length > 0) {
            console.log(`   ❌ 更新エラー: ${JSON.stringify(result.metafieldsSet.userErrors)}`);
            errorCount++;
          } else {
            console.log(`   ✅ 更新成功`);
            successCount++;
          }
        } else {
          // 新規メタフィールドを作成
          const result = await updateMetafield(null, item.recommendedValue, item.productId);

          if (result.metafieldsSet.userErrors.length > 0) {
            console.log(`   ❌ 作成エラー: ${JSON.stringify(result.metafieldsSet.userErrors)}`);
            errorCount++;
          } else {
            console.log(`   ✅ 作成成功`);
            successCount++;
          }
        }

      } catch (error) {
        console.log(`   ❌ 処理エラー: ${error.message}`);
        errorCount++;
      }

      console.log('');
    }

    // 結果サマリー
    console.log('🎯 === 修正結果サマリー ===');
    console.log(`✅ 成功: ${successCount}件`);
    console.log(`❌ エラー: ${errorCount}件`);
    console.log(`📊 成功率: ${Math.round((successCount / (successCount + errorCount)) * 100)}%`);

    if (successCount > 0) {
      console.log('');
      console.log('🎉 variation_type修正が完了しました！');
      console.log('');
      console.log('📋 次のステップ:');
      console.log('1. Shopify管理画面でメタフィールドを確認');
      console.log('2. variant_mappingの構造改善を実行');
      console.log('3. 商品登録フローでの自動設定を実装');
    }

  } catch (error) {
    console.error('❌ 修正エラー:', error);
  }
}

// コマンドライン引数の処理
const args = process.argv.slice(2);
const command = args[0];

async function main() {
  try {
    switch (command) {
      case 'analyze':
        await analyzeVariationType();
        break;
      case 'fix':
        await fixVariationType();
        break;
      default:
        console.log('🏷️ variation_type修正ツール');
        console.log('');
        console.log('使用方法:');
        console.log('  node fix-variation-type.js analyze  # 現状分析');
        console.log('  node fix-variation-type.js fix      # 修正実行');
        console.log('');
        console.log('デフォルトで分析を実行します...');
        await analyzeVariationType();
        break;
    }
  } catch (error) {
    console.error('実行エラー:', error);
    process.exit(1);
  }
}

// スクリプト実行
main().catch(console.error);
