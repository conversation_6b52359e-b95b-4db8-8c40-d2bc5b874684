/**
 * 予約の重複チェックユーティリティ
 * 
 * このモジュールは、予約の重複をチェックするための関数を提供します。
 */

import { PrismaClient } from '@prisma/client';
import { format } from 'date-fns';

/**
 * 予約の重複をチェックする関数
 * @param prisma Prismaクライアント
 * @param productId 商品ID
 * @param startDate 開始日
 * @param endDate 終了日
 * @param variantId バリアントID（オプション）
 * @param excludeBookingId 除外する予約ID（更新時に使用）
 * @returns 重複チェック結果
 */
export async function checkBookingOverlap(
  prisma: PrismaClient,
  productId: string,
  startDate: Date,
  endDate: Date,
  variantId?: string | null,
  excludeBookingId?: string
) {
  try {
    console.log(`商品ID ${productId} の予約重複チェックを実行します...`);
    console.log(`期間: ${format(startDate, 'yyyy-MM-dd')} 〜 ${format(endDate, 'yyyy-MM-dd')}`);
    
    if (variantId) {
      console.log(`バリアントID: ${variantId}`);
    }

    // 重複する予約を検索するクエリ条件
    const query: any = {
      productId,
      status: {
        in: ['PROVISIONAL', 'CONFIRMED']
      },
      AND: [
        {
          startDate: {
            lte: endDate,
          },
          endDate: {
            gte: startDate,
          },
        },
      ],
    };

    // バリアントIDが指定されている場合は、同じバリアントの予約のみをチェック
    if (variantId) {
      query.variantId = variantId;
    }

    // 除外する予約IDが指定されている場合（更新時）
    if (excludeBookingId) {
      query.NOT = {
        bookingId: excludeBookingId
      };
    }

    // 重複する予約を検索
    const overlappingBookings = await prisma.booking.findMany({
      where: query,
    });

    if (overlappingBookings.length > 0) {
      console.log(`重複する予約が ${overlappingBookings.length} 件見つかりました:`);
      overlappingBookings.forEach((booking, index) => {
        console.log(`${index + 1}. 予約ID: ${booking.bookingId}, 期間: ${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')}`);
      });
      return { hasOverlap: true, overlappingBookings };
    } else {
      console.log('重複する予約はありません');
      return { hasOverlap: false, overlappingBookings: [] };
    }
  } catch (error) {
    console.error('予約重複チェック中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 予約の重複をチェックし、重複がある場合はエラーを投げる関数
 * @param prisma Prismaクライアント
 * @param productId 商品ID
 * @param startDate 開始日
 * @param endDate 終了日
 * @param variantId バリアントID（オプション）
 * @param excludeBookingId 除外する予約ID（更新時に使用）
 * @throws 重複がある場合はエラーを投げる
 */
export async function validateNoBookingOverlap(
  prisma: PrismaClient,
  productId: string,
  startDate: Date,
  endDate: Date,
  variantId?: string | null,
  excludeBookingId?: string
) {
  const overlapResult = await checkBookingOverlap(
    prisma,
    productId,
    startDate,
    endDate,
    variantId,
    excludeBookingId
  );

  if (overlapResult.hasOverlap) {
    throw new Error('指定された期間は既に予約されています');
  }
}
