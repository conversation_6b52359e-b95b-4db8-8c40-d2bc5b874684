/**
 * テスト商品クリーンアップスクリプト
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanupTestProducts() {
  console.log('🧹 テスト商品クリーンアップを開始...\n');

  try {
    // Shopifyに存在しないテスト商品を特定
    const testProducts = await prisma.product.findMany({
      where: {
        OR: [
          { shopifyId: { startsWith: 'test-' } },
          { shopifyId: { startsWith: 'shopify-test-' } },
          { sku: { startsWith: 'TEST-' } }
        ]
      },
      include: {
        bookings: true,
        maintenances: true,
        inventoryCalendars: true
      }
    });

    console.log(`テスト商品: ${testProducts.length}件\n`);

    if (testProducts.length === 0) {
      console.log('✅ クリーンアップが必要なテスト商品はありません');
      return;
    }

    for (const product of testProducts) {
      console.log(`🔍 ${product.title} (${product.sku})`);
      console.log(`   Shopify ID: ${product.shopifyId}`);
      console.log(`   予約: ${product.bookings.length}件`);
      console.log(`   メンテナンス: ${product.maintenances.length}件`);
      console.log(`   在庫カレンダー: ${product.inventoryCalendars.length}件`);

      // 関連データがある場合は警告
      if (product.bookings.length > 0 || product.maintenances.length > 0) {
        console.log(`   ⚠️ 関連データがあるため削除をスキップします`);
        console.log('');
        continue;
      }

      // 関連データを削除
      console.log(`   🗑️ 削除中...`);

      // 在庫カレンダーを削除
      if (product.inventoryCalendars.length > 0) {
        await prisma.inventoryCalendar.deleteMany({
          where: { productId: product.id }
        });
        console.log(`   📅 在庫カレンダー削除: ${product.inventoryCalendars.length}件`);
      }

      // 商品を削除
      await prisma.product.delete({
        where: { id: product.id }
      });

      console.log(`   ✅ 商品削除完了`);
      console.log('');
    }

    // クリーンアップ後の状況確認
    console.log('=== クリーンアップ後の状況 ===');
    const remainingProducts = await prisma.product.count();
    const remainingTestProducts = await prisma.product.count({
      where: {
        OR: [
          { shopifyId: { startsWith: 'test-' } },
          { shopifyId: { startsWith: 'shopify-test-' } },
          { sku: { startsWith: 'TEST-' } }
        ]
      }
    });

    console.log(`総商品数: ${remainingProducts}件`);
    console.log(`残りのテスト商品: ${remainingTestProducts}件`);

    if (remainingTestProducts === 0) {
      console.log('✅ すべてのテスト商品をクリーンアップしました');
    }

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
cleanupTestProducts();
