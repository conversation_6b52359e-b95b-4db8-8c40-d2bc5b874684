#!/usr/bin/env node

/**
 * テスト用予約データ作成スクリプト
 * 
 * 様々なテストシナリオに対応する予約データを作成
 */

import { PrismaClient } from '@prisma/client';
import { addDays, format } from 'date-fns';

const prisma = new PrismaClient();

// テスト用予約データのパターン
const BOOKING_PATTERNS = [
  {
    sku: '212-05-023',
    scenarios: [
      {
        type: 'confirmed',
        startDate: addDays(new Date(), 5),
        endDate: addDays(new Date(), 8),
        customerName: '田中 太郎',
        customerEmail: '<EMAIL>',
        notes: '確定予約のテストデータ'
      },
      {
        type: 'provisional',
        startDate: addDays(new Date(), 15),
        endDate: addDays(new Date(), 17),
        customerName: '佐藤 花子',
        customerEmail: '<EMAIL>',
        notes: '仮予約のテストデータ'
      },
      {
        type: 'confirmed',
        startDate: addDays(new Date(), 25),
        endDate: addDays(new Date(), 32), // 8日以上のテスト
        customerName: '鈴木 一郎',
        customerEmail: '<EMAIL>',
        notes: '8日以上レンタルのテストデータ'
      }
    ]
  },
  {
    sku: '201-07-107',
    scenarios: [
      {
        type: 'confirmed',
        startDate: addDays(new Date(), 3),
        endDate: addDays(new Date(), 5),
        customerName: '山田 美咲',
        customerEmail: '<EMAIL>',
        notes: '短期レンタルのテストデータ'
      },
      {
        type: 'provisional',
        startDate: addDays(new Date(), 10),
        endDate: addDays(new Date(), 16),
        customerName: '高橋 健太',
        customerEmail: '<EMAIL>',
        notes: '1週間レンタルの仮予約テストデータ'
      }
    ]
  },
  {
    sku: '201-05-797',
    scenarios: [
      {
        type: 'confirmed',
        startDate: addDays(new Date(), 1),
        endDate: addDays(new Date(), 2), // 1日レンタル
        customerName: '伊藤 真理',
        customerEmail: '<EMAIL>',
        notes: '1日レンタルのテストデータ'
      },
      {
        type: 'cancelled',
        startDate: addDays(new Date(), 20),
        endDate: addDays(new Date(), 22),
        customerName: '渡辺 大輔',
        customerEmail: '<EMAIL>',
        notes: 'キャンセルされた予約のテストデータ'
      }
    ]
  },
  {
    sku: '206-02-320',
    scenarios: [
      {
        type: 'confirmed',
        startDate: addDays(new Date(), 7),
        endDate: addDays(new Date(), 11),
        customerName: '中村 由美',
        customerEmail: '<EMAIL>',
        notes: 'PR拠点商品のテストデータ'
      }
    ]
  }
];

/**
 * 予約データを作成
 */
async function createBooking(shop, productId, scenario) {
  const { type, startDate, endDate, customerName, customerEmail, notes } = scenario;
  
  // 日数計算
  const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
  
  // 予約データを作成
  const booking = await prisma.booking.create({
    data: {
      shop,
      productId,
      startDate,
      endDate,
      status: type,
      customerName,
      customerEmail,
      notes,
      rentalDays: days,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
  
  console.log(`✅ 予約作成: ${customerName} - ${format(startDate, 'yyyy-MM-dd')} 〜 ${format(endDate, 'yyyy-MM-dd')} (${type})`);
  return booking;
}

/**
 * 重複予約のテストケースを作成
 */
async function createConflictBookings(shop) {
  console.log('\n=== 重複予約テストケース作成 ===');
  
  // 同じ商品、同じ期間での重複予約を試行
  const product = await prisma.product.findFirst({
    where: { shop, sku: '212-05-023' }
  });
  
  if (!product) {
    console.log('❌ テスト商品が見つかりません');
    return;
  }
  
  const conflictDate = addDays(new Date(), 40);
  
  try {
    // 1つ目の予約（成功するはず）
    const booking1 = await createBooking(shop, product.id, {
      type: 'confirmed',
      startDate: conflictDate,
      endDate: addDays(conflictDate, 2),
      customerName: '重複テスト 太郎',
      customerEmail: '<EMAIL>',
      notes: '重複テスト用の最初の予約'
    });
    
    // 2つ目の予約（重複エラーになるはず）
    try {
      const booking2 = await createBooking(shop, product.id, {
        type: 'confirmed',
        startDate: addDays(conflictDate, 1),
        endDate: addDays(conflictDate, 3),
        customerName: '重複テスト 花子',
        customerEmail: '<EMAIL>',
        notes: '重複テスト用の2つ目の予約（エラーになるはず）'
      });
      
      console.log('⚠️ 重複予約が作成されました（本来はエラーになるべき）');
    } catch (error) {
      console.log('✅ 重複予約が正しく拒否されました');
    }
    
  } catch (error) {
    console.error('❌ 重複テスト中にエラー:', error.message);
  }
}

/**
 * ステータス変更のテストケースを作成
 */
async function createStatusChangeTests(shop) {
  console.log('\n=== ステータス変更テストケース作成 ===');
  
  const product = await prisma.product.findFirst({
    where: { shop, sku: '201-07-107' }
  });
  
  if (!product) {
    console.log('❌ テスト商品が見つかりません');
    return;
  }
  
  // 仮予約 → 確定予約のテスト
  const provisionalBooking = await createBooking(shop, product.id, {
    type: 'provisional',
    startDate: addDays(new Date(), 50),
    endDate: addDays(new Date(), 53),
    customerName: 'ステータス変更 太郎',
    customerEmail: '<EMAIL>',
    notes: '仮予約→確定予約のテストデータ'
  });
  
  // 確定予約 → キャンセルのテスト
  const confirmedBooking = await createBooking(shop, product.id, {
    type: 'confirmed',
    startDate: addDays(new Date(), 60),
    endDate: addDays(new Date(), 62),
    customerName: 'ステータス変更 花子',
    customerEmail: '<EMAIL>',
    notes: '確定予約→キャンセルのテストデータ'
  });
  
  console.log(`📝 ステータス変更テスト用予約ID: ${provisionalBooking.id}, ${confirmedBooking.id}`);
}

/**
 * 価格計算テスト用の予約を作成
 */
async function createPriceCalculationTests(shop) {
  console.log('\n=== 価格計算テストケース作成 ===');
  
  const product = await prisma.product.findFirst({
    where: { shop, sku: '201-05-797' }
  });
  
  if (!product) {
    console.log('❌ テスト商品が見つかりません');
    return;
  }
  
  // 様々な日数での価格計算テスト
  const priceTestCases = [
    { days: 1, description: '1日レンタル（100%）' },
    { days: 3, description: '3日レンタル（20%×3日）' },
    { days: 7, description: '7日レンタル（20%×7日）' },
    { days: 10, description: '10日レンタル（20%×7日 + 10%×3日）' },
    { days: 15, description: '15日レンタル（20%×7日 + 10%×8日）' }
  ];
  
  for (let i = 0; i < priceTestCases.length; i++) {
    const testCase = priceTestCases[i];
    const startDate = addDays(new Date(), 70 + (i * 20));
    const endDate = addDays(startDate, testCase.days);
    
    await createBooking(shop, product.id, {
      type: 'confirmed',
      startDate,
      endDate,
      customerName: `価格テスト ${i + 1}`,
      customerEmail: `price${i + 1}@example.com`,
      notes: `価格計算テスト: ${testCase.description}`
    });
  }
}

/**
 * テスト用予約データを一括作成
 */
async function createTestBookings() {
  const shop = 'ease-next-temp.myshopify.com';
  
  console.log('=== テスト用予約データ作成開始 ===');
  console.log(`対象ショップ: ${shop}`);
  
  try {
    await prisma.$connect();
    console.log('✅ データベース接続成功');
    
    // 既存のテスト予約データを削除
    console.log('\n既存のテスト予約データを削除中...');
    const deletedBookings = await prisma.booking.deleteMany({
      where: {
        shop,
        customerEmail: { contains: 'example.com' }
      }
    });
    console.log(`🗑️ 既存予約削除完了: ${deletedBookings.count}件`);
    
    // 基本的な予約パターンを作成
    console.log('\n基本的な予約パターンを作成中...');
    let totalBookings = 0;
    
    for (const pattern of BOOKING_PATTERNS) {
      const product = await prisma.product.findFirst({
        where: { shop, sku: pattern.sku }
      });
      
      if (!product) {
        console.log(`⚠️ 商品が見つかりません: ${pattern.sku}`);
        continue;
      }
      
      console.log(`\n📦 商品: ${pattern.sku}`);
      
      for (const scenario of pattern.scenarios) {
        await createBooking(shop, product.id, scenario);
        totalBookings++;
      }
    }
    
    // 特殊なテストケースを作成
    await createConflictBookings(shop);
    await createStatusChangeTests(shop);
    await createPriceCalculationTests(shop);
    
    console.log(`\n✅ 予約データ作成完了: 基本予約 ${totalBookings}件 + 特殊テストケース`);
    
    // 作成された予約の概要を表示
    const bookingSummary = await prisma.booking.groupBy({
      by: ['status'],
      where: { shop },
      _count: { status: true }
    });
    
    console.log('\n📊 予約データ概要:');
    bookingSummary.forEach(summary => {
      console.log(`  ${summary.status}: ${summary._count.status}件`);
    });
    
  } catch (error) {
    console.error('❌ エラー:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * メイン処理
 */
async function main() {
  try {
    await createTestBookings();
    
    console.log('\n=== 予約データ作成完了 ===');
    console.log('作成されたテストケース:');
    console.log('1. 基本的な予約パターン（確定・仮予約・キャンセル）');
    console.log('2. 重複予約の検証');
    console.log('3. ステータス変更のテスト');
    console.log('4. 価格計算のテスト（1日〜15日）');
    console.log('5. 複数拠点（NY・PR）のテスト');
    
  } catch (error) {
    console.error('テスト予約データ作成に失敗しました:', error);
    process.exit(1);
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
