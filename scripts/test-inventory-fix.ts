/**
 * 既存商品の在庫設定修正テスト
 */

import { authenticate } from '../app/shopify.server';

async function testInventoryFix() {
  console.log('=== 既存商品在庫設定修正テスト開始 ===');

  try {
    const { admin } = await authenticate.admin(new Request('http://localhost'));

    // 最近作成された商品を取得
    const response = await admin.graphql(`
      query getRecentProducts {
        products(first: 1, reverse: true) {
          edges {
            node {
              id
              title
              variants(first: 10) {
                edges {
                  node {
                    id
                    title
                    sku
                    inventoryItem {
                      id
                      tracked
                      inventoryLevels(first: 5) {
                        edges {
                          node {
                            id
                            available
                            location {
                              id
                              name
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    `);

    const data = await response.json();
    
    if (data.errors) {
      throw new Error(`GraphQL エラー: ${JSON.stringify(data.errors)}`);
    }

    const products = data.data.products.edges;
    if (products.length === 0) {
      throw new Error('テスト対象の商品が見つかりません');
    }

    const product = products[0].node;
    console.log(`\n📦 テスト対象商品: ${product.title}`);
    console.log(`商品ID: ${product.id}`);

    // ロケーション一覧を取得
    console.log('\n1. ロケーション一覧を取得中...');
    const locationResponse = await admin.graphql(`
      query getLocations {
        locations(first: 5) {
          edges {
            node {
              id
              name
            }
          }
        }
      }
    `);

    const locationData = await locationResponse.json();
    if (locationData.errors) {
      throw new Error(`ロケーション取得エラー: ${JSON.stringify(locationData.errors)}`);
    }

    const locations = locationData.data.locations.edges;
    console.log('利用可能なロケーション:');
    locations.forEach((locationEdge: any) => {
      const location = locationEdge.node;
      console.log(`  📍 ${location.name}: ${location.id}`);
    });

    const defaultLocationId = locations[0].node.id;
    console.log(`\n使用するロケーション: ${locations[0].node.name} (${defaultLocationId})`);

    // 各バリエーションの在庫を設定
    console.log('\n2. 各バリエーションの在庫を設定中...');
    
    for (const variantEdge of product.variants.edges) {
      const variant = variantEdge.node;
      const inventoryItemId = variant.inventoryItem.id;
      
      console.log(`\n  バリエーション: ${variant.title} (SKU: ${variant.sku})`);
      console.log(`  InventoryItemID: ${inventoryItemId}`);

      // 現在の在庫状況を表示
      const inventoryLevels = variant.inventoryItem.inventoryLevels.edges;
      console.log(`  現在の在庫:`);
      if (inventoryLevels.length === 0) {
        console.log(`    ⚠️ 在庫レベルが設定されていません`);
      } else {
        inventoryLevels.forEach((levelEdge: any) => {
          const level = levelEdge.node;
          console.log(`    📍 ${level.location.name}: ${level.available}個`);
        });
      }

      // 在庫を1に設定
      try {
        const inventoryResponse = await admin.graphql(`
          mutation inventorySetQuantities($input: InventorySetQuantitiesInput!) {
            inventorySetQuantities(input: $input) {
              inventoryAdjustmentGroup {
                id
                changes {
                  name
                  delta
                  quantityAfterChange
                }
              }
              userErrors {
                field
                message
              }
            }
          }
        `, {
          input: {
            name: 'available',
            reason: 'correction',
            referenceDocumentUri: `inventory-fix-test`,
            ignoreCompareQuantity: true,
            quantities: [{
              inventoryItemId: inventoryItemId,
              locationId: defaultLocationId,
              quantity: 1
            }]
          }
        });

        const inventoryResult = await inventoryResponse.json();
        
        if (inventoryResult.errors) {
          console.log(`    ❌ 在庫設定エラー: ${JSON.stringify(inventoryResult.errors)}`);
        } else if (inventoryResult.data.inventorySetQuantities.userErrors.length > 0) {
          console.log(`    ❌ ユーザーエラー: ${JSON.stringify(inventoryResult.data.inventorySetQuantities.userErrors)}`);
        } else {
          console.log(`    ✅ 在庫設定成功`);
          const changes = inventoryResult.data.inventorySetQuantities.inventoryAdjustmentGroup.changes;
          changes.forEach((change: any) => {
            console.log(`      ${change.name}: ${change.quantityAfterChange}個 (変更: ${change.delta > 0 ? '+' : ''}${change.delta})`);
          });
        }
      } catch (error) {
        console.log(`    ❌ 在庫設定例外: ${error.message}`);
      }
    }

    // 3. 設定後の在庫状況を確認
    console.log('\n3. 設定後の在庫状況を確認中...');
    const finalResponse = await admin.graphql(`
      query getProductInventory($id: ID!) {
        product(id: $id) {
          title
          variants(first: 20) {
            edges {
              node {
                id
                title
                sku
                inventoryItem {
                  id
                  inventoryLevels(first: 5) {
                    edges {
                      node {
                        id
                        available
                        location {
                          id
                          name
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    `, {
      id: product.id
    });

    const finalData = await finalResponse.json();
    
    if (finalData.errors) {
      throw new Error(`最終確認エラー: ${JSON.stringify(finalData.errors)}`);
    }

    const finalProduct = finalData.data.product;
    console.log(`\n📦 最終在庫状況: ${finalProduct.title}`);
    
    finalProduct.variants.edges.forEach((variantEdge: any, index: number) => {
      const variant = variantEdge.node;
      console.log(`\n  ${index + 1}. ${variant.title} (SKU: ${variant.sku})`);
      
      const inventoryLevels = variant.inventoryItem.inventoryLevels.edges;
      if (inventoryLevels.length === 0) {
        console.log(`     ⚠️ 在庫レベルが設定されていません`);
      } else {
        inventoryLevels.forEach((levelEdge: any) => {
          const level = levelEdge.node;
          console.log(`     📍 ${level.location.name}: ${level.available}個`);
        });
      }
    });

  } catch (error) {
    console.error('❌ テストエラー:', error);
  }

  console.log('\n=== 既存商品在庫設定修正テスト完了 ===');
}

// スクリプト実行
if (import.meta.url === new URL(import.meta.url).href) {
  testInventoryFix().catch(console.error);
}

export { testInventoryFix };
