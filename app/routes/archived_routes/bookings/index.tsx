import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import { 
  Page, 
  LegacyCard, 
  ResourceList, 
  ResourceItem, 
  Filters, 
  Badge, 
  EmptyState, 
  Text,
  Pagination
} from '@shopify/polaris';
import { BookingStatus } from '@prisma/client';
import { formatLocalDate } from '../utils/date/date-utils';
import { useState } from 'react';
import { prisma } from '../db.server';

// ステータスに応じたバッジ設定
const statusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  DRAFT: { tone: 'attention', label: '仮予約' },
  PROVISIONAL: { tone: 'attention', label: '仮予約' },
  CONFIRMED: { tone: 'success', label: '本予約' },
  COMPLETED: { tone: 'info', label: '完了' },
  CANCELLED: { tone: 'critical', label: 'キャンセル' }
};

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  
  // クエリパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  const limit = parseInt(url.searchParams.get('limit') || '50', 10);
  const status = url.searchParams.get('status') || undefined;
  const query = url.searchParams.get('query') || undefined;
  const startDate = url.searchParams.get('startDate') || undefined;
  const endDate = url.searchParams.get('endDate') || undefined;
  
  // 検索条件を構築
  const where: any = {};
  
  if (status) {
    where.status = status as BookingStatus;
  }
  
  if (query) {
    where.OR = [
      { customerName: { contains: query, mode: 'insensitive' } },
      { customerEmail: { contains: query, mode: 'insensitive' } },
      { orderId: { contains: query } },
      { orderName: { contains: query } },
      { id: { contains: query } }
    ];
  }
  
  if (startDate) {
    where.startDate = { gte: new Date(startDate) };
  }
  
  if (endDate) {
    where.endDate = { lte: new Date(endDate) };
  }
  
  // 予約データを取得
  const bookings = await prisma.booking.findMany({
    where,
    include: {
      product: true
    },
    orderBy: [
      { status: 'asc' },
      { startDate: 'asc' }
    ],
    skip: (page - 1) * limit,
    take: limit
  });
  
  // 総件数を取得
  const total = await prisma.booking.count({ where });
  
  return json({
    bookings: bookings.map(booking => ({
      id: booking.id,
      customerName: booking.customerName,
      customerEmail: booking.customerEmail,
      status: booking.status,
      startDate: booking.startDate.toISOString(),
      endDate: booking.endDate.toISOString(),
      priority: booking.priority,
      depositAmount: booking.depositAmount?.toString(),
      totalAmount: booking.totalAmount?.toString(),
      productTitle: booking.product.title,
      productId: booking.productId,
      orderId: booking.orderId,
      orderName: booking.orderName,
      paymentStatus: booking.paymentStatus
    })),
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}

export default function BookingsIndex() {
  const { bookings, pagination } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  
  // 予約詳細ページへ移動
  const handleBookingClick = (bookingId: string) => {
    navigate(`/bookings/${bookingId}`);
  };
  
  // ステータスフィルターを適用
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    const params = new URLSearchParams(window.location.search);
    
    if (value) {
      params.set('status', value);
    } else {
      params.delete('status');
    }
    
    navigate(`/bookings?${params.toString()}`);
  };
  
  // 検索クエリを適用
  const handleSearchSubmit = () => {
    const params = new URLSearchParams(window.location.search);
    
    if (searchQuery) {
      params.set('query', searchQuery);
    } else {
      params.delete('query');
    }
    
    navigate(`/bookings?${params.toString()}`);
  };
  
  // ページネーション処理
  const handlePaginationChange = (newPage: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set('page', newPage.toString());
    navigate(`/bookings?${params.toString()}`);
  };
  
  // フィルターオプションを生成
  const filters = [
    {
      key: 'status',
      label: 'ステータス',
      filter: (
        <select
          value={statusFilter}
          onChange={(e) => handleStatusFilterChange(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="">すべて</option>
          <option value="DRAFT">仮予約（ドラフト）</option>
          <option value="PROVISIONAL">仮予約</option>
          <option value="CONFIRMED">本予約</option>
          <option value="COMPLETED">完了</option>
          <option value="CANCELLED">キャンセル</option>
        </select>
      ),
      shortcut: true,
    },
    {
      key: 'startDate',
      label: '開始日',
      filter: (
        <div>
          <input
            type="date"
            onChange={(e) => {
              const params = new URLSearchParams(window.location.search);
              if (e.target.value) {
                params.set('startDate', e.target.value);
              } else {
                params.delete('startDate');
              }
              navigate(`/bookings?${params.toString()}`);
            }}
            style={{ width: '100%', padding: '8px' }}
          />
        </div>
      ),
    },
    {
      key: 'endDate',
      label: '終了日',
      filter: (
        <div>
          <input
            type="date"
            onChange={(e) => {
              const params = new URLSearchParams(window.location.search);
              if (e.target.value) {
                params.set('endDate', e.target.value);
              } else {
                params.delete('endDate');
              }
              navigate(`/bookings?${params.toString()}`);
            }}
            style={{ width: '100%', padding: '8px' }}
          />
        </div>
      ),
    },
  ];
  
  return (
    <Page
      title="予約管理"
      primaryAction={{
        content: "新規予約",
        url: "/bookings/new"
      }}
    >
      <LegacyCard>
        <ResourceList
          resourceName={{ singular: '予約', plural: '予約' }}
          items={bookings}
          renderItem={(booking) => {
            const { id, customerName, customerEmail, status, startDate, endDate, productTitle, priority, orderId, orderName, paymentStatus } = booking;
            const badge = statusBadgeMap[status] || { tone: 'new', label: status };
            
            const priorityBadge = (status === 'DRAFT' || status === 'PROVISIONAL') && priority > 1 ? (
              <Badge tone="warning">{`第${priority.toString()}優先`}</Badge>
            ) : null;
            
            const shortcutActions = [
              {
                content: '詳細',
                url: `/bookings/${id}`,
              },
            ];
            
            if (status === 'DRAFT' || status === 'PROVISIONAL') {
              shortcutActions.push({
                content: '本予約に変更',
                url: `/bookings/confirm/${id}`,
              });
            }
            
            if (status !== 'CANCELLED' && status !== 'COMPLETED') {
              shortcutActions.push({
                content: 'キャンセル',
                url: `/bookings/cancel/${id}`,
              });
            }
            
            return (
              <ResourceItem
                id={id}
                onClick={() => handleBookingClick(id)}
                shortcutActions={shortcutActions}
                persistActions
              >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <Text as="h3" variant="bodyMd" fontWeight="bold">
                      {customerName || customerEmail}
                    </Text>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodyMd">{productTitle}</Text>
                    </div>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        {formatLocalDate(startDate)} 〜 {formatLocalDate(endDate)}
                      </Text>
                    </div>
                    {orderId && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          注文: {orderName || orderId}
                        </Text>
                      </div>
                    )}
                    {paymentStatus && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          支払い状況: {paymentStatus === 'COMPLETED' ? '完了' : 
                                      paymentStatus === 'PENDING' ? '未払い' : 
                                      paymentStatus === 'REFUNDED' ? '返金済み' : 
                                      paymentStatus === 'FAILED' ? '失敗' : paymentStatus}
                        </Text>
                      </div>
                    )}
                  </div>
                  <div style={{ display: 'flex', gap: '8px' }}>
                    <Badge tone={badge.tone}>{badge.label}</Badge>
                    {priorityBadge}
                  </div>
                </div>
              </ResourceItem>
            );
          }}
          filterControl={
            <Filters
              queryValue={searchQuery}
              filters={filters}
              onQueryChange={setSearchQuery}
              onQueryClear={() => setSearchQuery('')}
              onClearAll={() => {
                setSearchQuery('');
                setStatusFilter('');
                navigate('/bookings');
              }}
              onQueryBlur={handleSearchSubmit}
              hideQueryField={false}
            />
          }
          emptyState={
            <EmptyState
              heading="予約がありません"
              image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
            >
              <p>予約を作成して、レンタル商品の予約を管理しましょう。</p>
            </EmptyState>
          }
        />
        {pagination.totalPages > 1 && (
          <div style={{ padding: '16px', display: 'flex', justifyContent: 'center' }}>
            <Pagination
              hasPrevious={pagination.page > 1}
              onPrevious={() => handlePaginationChange(pagination.page - 1)}
              hasNext={pagination.page < pagination.totalPages}
              onNext={() => handlePaginationChange(pagination.page + 1)}
            />
          </div>
        )}
      </LegacyCard>
      <div style={{ marginTop: '16px', textAlign: 'right' }}>
        <Text as="p" variant="bodySm" tone="subdued">
          合計 {pagination.total} 件の予約 ( {pagination.page} / {pagination.totalPages} ページ )
        </Text>
      </div>
    </Page>
  );
}
