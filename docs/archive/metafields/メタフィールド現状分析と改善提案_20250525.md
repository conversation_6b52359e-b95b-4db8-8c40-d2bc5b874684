# メタフィールド現状分析と改善提案

**作成日**: 2025年5月25日  
**目的**: メタフィールドの実際の使用状況を分析し、改善提案を行う

## 📊 現状分析結果

### **実際に使用されているメタフィールド**
調査した商品で実際に設定されているのは以下の7つのみ：
- `rental.basic_info` ✅
- `rental.pricing` ✅
- `rental.status` ✅
- `rental.location` ✅
- `rental.maintenance_notes` ✅
- `rental.last_maintenance_date` ✅
- `rental.booking_notes` ✅

### **重要だが未使用のメタフィールド**
- `rental.variation_type` ❌
- `rental.variant_mapping` ❌
- `rental.inventory_items` ❌
- `rental.reservation_info` ❌

## 🔍 各メタフィールドの本来の用途

### 1. **variation_type**
**誤解**: グループ管理用  
**実際の用途**: バリエーションの種類を示す（"rental_period", "color", "size"など）  
**現状**: 未設定  
**提案**: 全商品に "rental_period" を設定すべき

### 2. **variant_mapping**
**用途**: バリアントIDとレンタル日数のマッピング  
**構造例**:
```json
{
  "variants": [
    {
      "variantId": "gid://shopify/ProductVariant/xxx",
      "rentalDays": 1,
      "title": "1日レンタル",
      "sku": "101-01-008-1D"
    }
  ]
}
```
**現状**: 未設定  
**問題**: バリアントと日数の関係が不明確

### 3. **inventory_items**
**用途**: 在庫アイテムの詳細情報  
**構造例**:
```json
[
  {
    "itemId": "10101007-1",
    "condition": "良好",
    "lastInspectionDate": "2025-05-25",
    "maintenanceNotes": "クリーニング済み",
    "status": "available"
  }
]
```
**現状**: 未設定  
**問題**: 商品の状態管理ができていない

### 4. **reservation_info**
**用途**: 予約情報のキャッシュ  
**構造例**:
```json
{
  "reservations": [],
  "maintenanceSchedule": [],
  "availabilityRules": {
    "minimumNotice": 1,
    "maximumAdvance": 180,
    "blackoutDates": []
  }
}
```
**現状**: 未設定  
**注意**: 実際の予約はBookingテーブルで管理

## 🎯 グループ管理の実現方法

### **現在のグループ管理の誤解**
- `variation_type`をグループ管理に使おうとしていた
- しかし、これはバリエーションの種類（レンタル期間、色、サイズ）を示すもの

### **正しいグループ管理の実装**

#### **方法1: basic_infoを活用（推奨）**
```json
{
  "productCode": "10101007",
  "detailCode": "1",
  "groupCode": "BASIC-SOFA-1S",  // グループコード追加
  "groupName": "ベーシックソファ 1シーター",
  "kana": "ﾍﾞｰｼｯｸｿﾌｧ1ｼｰﾀｰ",
  "location": "NY",
  "status": "available"
}
```

#### **方法2: 新規メタフィールド追加**
`rental.product_group`として定義：
```json
{
  "groupCode": "BASIC-SOFA-1S",
  "groupName": "ベーシックソファ 1シーター",
  "groupType": "model",  // model, color, size
  "relatedProducts": ["10101007-1", "10101007-2"]
}
```

## 📋 在庫管理の仕組み

### **現在の在庫管理**
1. **Shopifyレベル**: 各バリアント（1日、2日...）の在庫を1に設定
2. **Prismaレベル**: Bookingテーブルで期間管理
3. **問題点**: メタフィールドが活用されていない

### **改善案**
1. `inventory_items`に実際の商品状態を記録
2. `reservation_info`は不要（Bookingテーブルで十分）
3. 在庫数は常に1（ユニーク商品）で問題なし

## 🛠️ 実装優先順位

### **Phase 1: 既存メタフィールドの有効活用**
1. 全商品に`variation_type: "rental_period"`を設定
2. `variant_mapping`を自動生成して設定
3. `inventory_items`に商品状態を記録

### **Phase 2: グループ管理の実装**
1. `basic_info`にgroupCode/groupNameを追加
2. または`product_group`メタフィールドを新規作成

### **Phase 3: 不要メタフィールドの削除**
- purchase_place, purchase_date, purchase_price
- is_disposed, disposal_date, disposal_reason
- reservation_info（Bookingテーブルで管理）

## 💡 推奨アクション

1. **即座に実行すべき**
   - `variation_type`を全商品に設定
   - `variant_mapping`の自動生成スクリプト作成
   - `inventory_items`の初期データ設定

2. **段階的に実装**
   - グループ管理機能の追加
   - フロントエンドでのグループ表示

3. **将来的に検討**
   - 不要メタフィールドの削除
   - メタフィールド定義の整理

これにより、メタフィールドが本来の用途で活用され、商品管理が効率化されます。