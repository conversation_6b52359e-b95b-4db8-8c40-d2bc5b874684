{% include 'breadcrumbs' %}
<div class="reset-password pb-60">
  <div class="container">
    <h1 class="h3 mt-30 mb-40 text-center">{{ 'customer.reset_password.title' | t }}</h1>
    {% capture email_with_html %}<h6 class="d-inline m-0">{{ email }}</h6>{% endcapture %}
    <div class="mb-20">{{ 'customer.reset_password.subtext' | t: email: email | replace: email, email_with_html }}</div>
    {% form 'reset_customer_password' %}
      <label for="ResetPassword" class="label-required">{{ 'customer.reset_password.password_title' | t }}</label>
      <input type="password"
        name="customer[password]"
        id="ResetPassword"
        class="{% if form.errors contains 'password' %}input-error{% endif %}"
        placeholder="{{ 'customer.reset_password.password_placeholder' | t }}"
        required="required">
      <label for="PasswordConfirmation" class="label-required">{{ 'customer.reset_password.password_confirm_title' | t }}</label>
      <input type="password"
        name="customer[password_confirmation]"
        id="PasswordConfirmation"
        class="{% if form.errors contains 'password_confirmation' %}input-error{% endif %}"
        placeholder="{{ 'customer.reset_password.password_confirm_placeholder' | t }}"
        required="required">
      {% include 'form-get-message' %}
      <input type="submit" class="btn btn--full btn--secondary" value="{{ 'customer.reset_password.submit' | t }}">
    {% endform %}
  </div>
</div>


{% schema %}
{
    "name": "t:sections.reset_password.name",
    "settings": []
}
{% endschema %}