/**
 * テストデータ作成スクリプト
 * 
 * 使用方法:
 * npx ts-node scripts/create-test-data.ts
 */

import { PrismaClient } from '@prisma/client';
import { randomUUID } from 'crypto';

const prisma = new PrismaClient();

// ショップドメイン
const SHOP = 'peaces-test-block.myshopify.com';

// 現在の日付
const NOW = new Date();

// 日付ユーティリティ関数
const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

// テスト商品データ
const TEST_PRODUCTS = [
  {
    title: 'テスト商品1 - 椅子',
    description: 'テスト用の椅子です。',
    price: 1000,
    shopifyId: '**********',
    sku: 'TEST-CHAIR-001',
    status: 'AVAILABLE',
    category: '家具',
    imageUrl: 'https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png',
    basicInfo: {
      manufacturer: 'テストメーカー',
      color: '黒',
      material: '木製',
      size: 'M',
      weight: '5kg',
    },
    depth: 50,
    height: 90,
    width: 45,
    weight: 5000,
  },
  {
    title: 'テスト商品2 - テーブル',
    description: 'テスト用のテーブルです。',
    price: 2000,
    shopifyId: '2222222222',
    sku: 'TEST-TABLE-001',
    status: 'AVAILABLE',
    category: '家具',
    imageUrl: 'https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png',
    basicInfo: {
      manufacturer: 'テストメーカー',
      color: '茶',
      material: '木製',
      size: 'L',
      weight: '10kg',
    },
    depth: 120,
    height: 70,
    width: 80,
    weight: 10000,
  },
  {
    title: 'テスト商品3 - ソファ',
    description: 'テスト用のソファです。',
    price: 3000,
    shopifyId: '**********',
    sku: 'TEST-SOFA-001',
    status: 'AVAILABLE',
    category: '家具',
    imageUrl: 'https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png',
    basicInfo: {
      manufacturer: 'テストメーカー',
      color: '灰',
      material: '布製',
      size: 'L',
      weight: '15kg',
    },
    depth: 80,
    height: 85,
    width: 180,
    weight: 15000,
  },
];

// テスト顧客データ
const TEST_CUSTOMERS = [
  {
    name: '山田 太郎',
    email: '<EMAIL>',
    phone: '090-1234-5678',
    address: '東京都渋谷区1-1-1',
    shopifyId: '**********',
    notes: 'テスト顧客1',
  },
  {
    name: '佐藤 花子',
    email: '<EMAIL>',
    phone: '090-8765-4321',
    address: '東京都新宿区2-2-2',
    shopifyId: '2222222222',
    notes: 'テスト顧客2',
  },
  {
    name: '鈴木 一郎',
    email: '<EMAIL>',
    phone: '090-9876-5432',
    address: '東京都中野区3-3-3',
    shopifyId: '**********',
    notes: 'テスト顧客3',
  },
];

// テスト予約データ
const createTestBookings = (productIds: string[], customerEmails: string[]) => {
  const bookings = [];
  
  // 商品1の予約 (確定予約)
  bookings.push({
    productId: productIds[0],
    startDate: addDays(NOW, 7),
    endDate: addDays(NOW, 10),
    customerEmail: customerEmails[0],
    customerName: '山田 太郎',
    customerPhone: '090-1234-5678',
    customerAddress: '東京都渋谷区1-1-1',
    totalAmount: 3000,
    depositAmount: 1000,
    depositPaid: true,
    bookingId: `BK-${randomUUID().substring(0, 8)}`,
    shop: SHOP,
    bookingType: 'CONFIRMED',
    status: 'CONFIRMED',
    paymentStatus: 'COMPLETED',
    paymentMethod: 'CREDIT_CARD',
    paymentDate: NOW,
    notes: 'テスト予約1',
    orderId: '**********',
    orderName: '#1001',
  });
  
  // 商品2の予約 (仮予約)
  bookings.push({
    productId: productIds[1],
    startDate: addDays(NOW, 14),
    endDate: addDays(NOW, 17),
    customerEmail: customerEmails[1],
    customerName: '佐藤 花子',
    customerPhone: '090-8765-4321',
    customerAddress: '東京都新宿区2-2-2',
    totalAmount: 6000,
    depositAmount: 600,
    depositPaid: false,
    bookingId: `BK-${randomUUID().substring(0, 8)}`,
    shop: SHOP,
    bookingType: 'PROVISIONAL',
    status: 'PROVISIONAL',
    paymentStatus: 'PENDING',
    notes: 'テスト予約2',
    expiresAt: addDays(NOW, 3),
  });
  
  // 商品3の予約 (確定予約、将来の日付)
  bookings.push({
    productId: productIds[2],
    startDate: addDays(NOW, 21),
    endDate: addDays(NOW, 28),
    customerEmail: customerEmails[2],
    customerName: '鈴木 一郎',
    customerPhone: '090-9876-5432',
    customerAddress: '東京都中野区3-3-3',
    totalAmount: 21000,
    depositAmount: 2100,
    depositPaid: true,
    bookingId: `BK-${randomUUID().substring(0, 8)}`,
    shop: SHOP,
    bookingType: 'CONFIRMED',
    status: 'CONFIRMED',
    paymentStatus: 'COMPLETED',
    paymentMethod: 'BANK_TRANSFER',
    paymentDate: addDays(NOW, -1),
    notes: 'テスト予約3',
    orderId: '**********',
    orderName: '#1003',
  });
  
  return bookings;
};

// テスト注文データ
const createTestOrders = (bookingIds: string[]) => {
  const orders = [
    {
      shop: SHOP,
      shopifyId: '**********',
      orderNumber: '#1001',
      customerEmail: '<EMAIL>',
      customerName: '山田 太郎',
      startDate: addDays(NOW, 7),
      endDate: addDays(NOW, 10),
      reservationType: 'CONFIRMED',
      reservationId: bookingIds[0],
      totalAmount: 3000,
      paymentStatus: 'PAID',
      syncStatus: 'SYNCED',
      lastSyncedAt: NOW,
      metadata: {
        order: {
          id: '**********',
          name: '#1001',
        },
        metafields: [
          {
            key: 'reservation_id',
            value: bookingIds[0],
          },
          {
            key: 'reservation_type',
            value: 'CONFIRMED',
          },
        ],
      },
    },
    {
      shop: SHOP,
      shopifyId: '**********',
      orderNumber: '#1003',
      customerEmail: '<EMAIL>',
      customerName: '鈴木 一郎',
      startDate: addDays(NOW, 21),
      endDate: addDays(NOW, 28),
      reservationType: 'CONFIRMED',
      reservationId: bookingIds[2],
      totalAmount: 21000,
      paymentStatus: 'PAID',
      syncStatus: 'SYNCED',
      lastSyncedAt: NOW,
      metadata: {
        order: {
          id: '**********',
          name: '#1003',
        },
        metafields: [
          {
            key: 'reservation_id',
            value: bookingIds[2],
          },
          {
            key: 'reservation_type',
            value: 'CONFIRMED',
          },
        ],
      },
    },
  ];
  
  return orders;
};

// 同期ログデータ
const createSyncLogs = () => {
  return [
    {
      shop: SHOP,
      syncType: 'PRODUCT',
      status: 'SUCCESS',
      created: 3,
      updated: 0,
      skipped: 0,
      errors: 0,
      createdAt: addDays(NOW, -1),
      completedAt: addDays(NOW, -1),
    },
    {
      shop: SHOP,
      syncType: 'ORDER',
      status: 'SUCCESS',
      created: 2,
      updated: 0,
      skipped: 0,
      errors: 0,
      createdAt: addDays(NOW, -1),
      completedAt: addDays(NOW, -1),
    },
    {
      shop: SHOP,
      syncType: 'CUSTOMER',
      status: 'SUCCESS',
      created: 3,
      updated: 0,
      skipped: 0,
      errors: 0,
      createdAt: addDays(NOW, -1),
      completedAt: addDays(NOW, -1),
    },
  ];
};

// メイン関数
async function main() {
  try {
    console.log('テストデータの作成を開始します...');
    
    // 既存データの削除
    console.log('既存データを削除中...');
    await prisma.booking.deleteMany({ where: { shop: SHOP } });
    await prisma.order.deleteMany({ where: { shop: SHOP } });
    await prisma.customer.deleteMany({ where: { shop: SHOP } });
    await prisma.product.deleteMany({ where: { shop: SHOP } });
    await prisma.syncLog.deleteMany({ where: { shop: SHOP } });
    
    // 商品データの作成
    console.log('商品データを作成中...');
    const productPromises = TEST_PRODUCTS.map(product => 
      prisma.product.create({
        data: {
          ...product,
          shop: SHOP,
          createdAt: NOW,
          updatedAt: NOW,
        },
      })
    );
    const products = await Promise.all(productPromises);
    const productIds = products.map(p => p.id);
    
    // 顧客データの作成
    console.log('顧客データを作成中...');
    const customerPromises = TEST_CUSTOMERS.map(customer => 
      prisma.customer.create({
        data: {
          ...customer,
          shop: SHOP,
          createdAt: NOW,
          updatedAt: NOW,
        },
      })
    );
    const customers = await Promise.all(customerPromises);
    const customerEmails = customers.map(c => c.email || '');
    
    // 予約データの作成
    console.log('予約データを作成中...');
    const bookingsData = createTestBookings(productIds, customerEmails);
    const bookingPromises = bookingsData.map(booking => 
      prisma.booking.create({
        data: {
          ...booking,
          createdAt: NOW,
          updatedAt: NOW,
        },
      })
    );
    const bookings = await Promise.all(bookingPromises);
    const bookingIds = bookings.map(b => b.bookingId);
    
    // 注文データの作成
    console.log('注文データを作成中...');
    const ordersData = createTestOrders(bookingIds);
    const orderPromises = ordersData.map(order => 
      prisma.order.create({
        data: {
          ...order,
          createdAt: NOW,
          updatedAt: NOW,
        },
      })
    );
    await Promise.all(orderPromises);
    
    // 同期ログの作成
    console.log('同期ログを作成中...');
    const syncLogsData = createSyncLogs();
    const syncLogPromises = syncLogsData.map(log => 
      prisma.syncLog.create({
        data: {
          ...log,
        },
      })
    );
    await Promise.all(syncLogPromises);
    
    console.log('テストデータの作成が完了しました！');
    console.log(`商品: ${products.length}件`);
    console.log(`顧客: ${customers.length}件`);
    console.log(`予約: ${bookings.length}件`);
    console.log(`注文: ${ordersData.length}件`);
    console.log(`同期ログ: ${syncLogsData.length}件`);
    
  } catch (error) {
    console.error('テストデータの作成中にエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
