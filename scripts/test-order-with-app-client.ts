/**
 * アプリケーションのAPIクライアントを使用した注文作成テストスクリプト
 *
 * このスクリプトは、アプリケーションの認証フローを経由して注文を作成します。
 * これにより、正しいオフライントークンが使用され、write_ordersスコープが機能するかをテストします。
 *
 * 実行方法: npx tsx scripts/test-order-with-app-client.ts
 */

import dotenv from 'dotenv';
import { ApiVersion } from '@shopify/shopify-api';
import { shopifyApp } from '@shopify/shopify-app-remix/server';
import { PrismaSessionStorage } from '@shopify/shopify-app-session-storage-prisma';
import { PrismaClient } from '@prisma/client';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// Shopifyアプリの初期化
const shopify = shopifyApp({
  apiKey: process.env.SHOPIFY_API_KEY || '',
  apiSecretKey: process.env.SHOPIFY_API_SECRET || '',
  apiVersion: ApiVersion.January25,
  scopes: process.env.SCOPES?.split(',') || [],
  appUrl: process.env.SHOPIFY_APP_URL || '',
  sessionStorage: new PrismaSessionStorage(prisma),
  isEmbeddedApp: false,
  useOnlineTokens: false,
});

// 通常注文作成ミューテーション
const CREATE_ORDER = `
  mutation orderCreate($order: OrderCreateOrderInput!) {
    orderCreate(order: $order) {
      order {
        id
        name
        totalPrice
        customer {
          id
          email
        }
        lineItems(first: 10) {
          edges {
            node {
              title
              quantity
              originalUnitPrice
              variant {
                id
                title
              }
            }
          }
        }
        metafields(first: 10) {
          edges {
            node {
              namespace
              key
              value
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// ドラフト注文作成ミューテーション
const CREATE_DRAFT_ORDER = `
  mutation draftOrderCreate($input: DraftOrderInput!) {
    draftOrderCreate(input: $input) {
      draftOrder {
        id
        name
        totalPrice
        customer {
          id
          email
        }
        lineItems(first: 10) {
          edges {
            node {
              title
              quantity
              originalUnitPrice
              variant {
                id
                title
              }
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// アプリケーションのAPIクライアントを使用した注文作成テスト
async function testOrderWithAppClient() {
  try {
    console.log('アプリケーションのAPIクライアントを使用した注文作成テストを開始します...');

    // セッションを取得
    const sessions = await shopify.sessionStorage.findSessionsByShop(process.env.SHOPIFY_SHOP || '');

    if (sessions.length === 0) {
      throw new Error('セッションが見つかりません。アプリケーションが正しく認証されていることを確認してください。');
    }

    // 最新のセッションを使用
    const session = sessions[0];
    console.log(`セッション情報: Shop=${session.shop}, アクセストークン=${session.accessToken.substring(0, 10)}...`);

    // 顧客IDを取得（環境変数から）
    const customerId = process.env.TEST_CUSTOMER_ID;
    if (!customerId) {
      throw new Error('TEST_CUSTOMER_ID が設定されていません');
    }

    // 商品IDを取得（環境変数から）
    const productId = process.env.TEST_PRODUCT_ID;
    const variantId = process.env.TEST_VARIANT_ID;
    if (!productId || !variantId) {
      throw new Error('TEST_PRODUCT_ID または TEST_VARIANT_ID が設定されていません');
    }

    console.log(`テスト情報: 顧客ID=${customerId}, 商品ID=${productId}, バリアントID=${variantId}`);

    // 顧客IDを正規化
    const normalizedCustomerId = customerId.startsWith('gid://shopify/Customer/')
      ? customerId
      : `gid://shopify/Customer/${customerId}`;

    // 現在の日時を取得
    const now = new Date();
    const startDate = new Date(now);
    startDate.setDate(startDate.getDate() + 1); // 明日から

    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 3); // 3日間

    // 日付をフォーマット
    const formatDate = (date: Date) => {
      return date.toISOString().split('T')[0];
    };

    const rentalPeriod = `${formatDate(startDate)} 〜 ${formatDate(endDate)}`;

    // 1. 通常注文作成を試みる
    try {
      console.log('1. 通常注文作成を試みます...');

      // 通常注文作成用の入力データを作成
      const orderInput = {
        customerId: normalizedCustomerId,
        lineItems: [
          {
            title: `テスト商品 (レンタル: ${rentalPeriod})`,
            quantity: 1,
            variantId: variantId,
            taxable: true,
            requiresShipping: false
          }
        ],
        tags: ['rental', 'test', 'app-client'],
        note: `テスト予約\n予約期間: ${rentalPeriod}\n備考: アプリクライアントテスト`,
        metafields: [
          {
            namespace: "custom",
            key: "booking_id",
            value: "test-app-" + Date.now(),
            type: "single_line_text_field"
          },
          {
            namespace: "custom",
            key: "booking_number",
            value: "APP-" + Math.floor(Math.random() * 10000),
            type: "single_line_text_field"
          },
          {
            namespace: "custom",
            key: "booking_type",
            value: "CONFIRMED",
            type: "single_line_text_field"
          },
          {
            namespace: "custom",
            key: "rental_period",
            value: rentalPeriod,
            type: "single_line_text_field"
          }
        ]
      };

      console.log('通常注文作成データ:', JSON.stringify(orderInput, null, 2));

      // GraphQLクライアントを使用して通常注文を作成
      const adminApiClient = shopify.admin.graphql.client({ session });
      const orderResponse = await adminApiClient.request(CREATE_ORDER, {
        variables: {
          order: orderInput
        }
      });

      console.log('通常注文作成応答:', JSON.stringify(orderResponse, null, 2));

      const orderData = orderResponse;

      if (orderData.data?.orderCreate?.userErrors?.length > 0) {
        console.error('通常注文作成エラー:', orderData.data.orderCreate.userErrors);
        throw new Error(`通常注文の作成中にエラーが発生しました: ${JSON.stringify(orderData.data.orderCreate.userErrors)}`);
      }

      // 注文IDを取得
      const orderId = orderData.data.orderCreate.order.id;
      const orderName = orderData.data.orderCreate.order.name;

      console.log(`注文が作成されました: 注文ID ${orderId}, 注文番号 ${orderName}`);
      console.log(`Shopify管理画面で確認: https://${process.env.SHOPIFY_SHOP}/admin/orders/${orderName.replace('#', '')}`);

      return {
        success: true,
        method: 'direct_order',
        orderId,
        orderName
      };
    } catch (directOrderError) {
      console.error('通常注文作成に失敗しました:', directOrderError);
      console.log('2. ドラフト注文作成を試みます...');

      // 2. 通常注文作成に失敗した場合、ドラフト注文作成を試みる
      // ドラフト注文用の入力データを作成
      const draftOrderInput = {
        customerId: normalizedCustomerId,
        lineItems: [
          {
            title: `テスト商品 (レンタル: ${rentalPeriod})`,
            quantity: 1,
            variantId: variantId,
            taxable: true,
            requiresShipping: false
          }
        ],
        tags: ['rental', 'test', 'app-client', 'draft'],
        note: `テスト予約\n予約期間: ${rentalPeriod}\n備考: アプリクライアントテスト（ドラフト）`,
        metafields: [
          {
            namespace: "custom",
            key: "booking_id",
            value: "test-app-draft-" + Date.now(),
            type: "single_line_text_field"
          },
          {
            namespace: "custom",
            key: "booking_number",
            value: "APP-DRAFT-" + Math.floor(Math.random() * 10000),
            type: "single_line_text_field"
          },
          {
            namespace: "custom",
            key: "booking_type",
            value: "CONFIRMED",
            type: "single_line_text_field"
          },
          {
            namespace: "custom",
            key: "rental_period",
            value: rentalPeriod,
            type: "single_line_text_field"
          }
        ]
      };

      console.log('ドラフト注文作成データ:', JSON.stringify(draftOrderInput, null, 2));

      // GraphQLクライアントを使用してドラフト注文を作成
      const adminApiClient = shopify.admin.graphql.client({ session });
      const draftResponse = await adminApiClient.request(CREATE_DRAFT_ORDER, {
        variables: {
          input: draftOrderInput
        }
      });

      console.log('ドラフト注文作成応答:', JSON.stringify(draftResponse, null, 2));

      const draftData = draftResponse;

      if (draftData.data?.draftOrderCreate?.userErrors?.length > 0) {
        console.error('ドラフト注文作成エラー:', draftData.data.draftOrderCreate.userErrors);
        throw new Error(`ドラフト注文の作成中にエラーが発生しました: ${JSON.stringify(draftData.data.draftOrderCreate.userErrors)}`);
      }

      // ドラフト注文IDを取得
      const draftOrderId = draftData.data.draftOrderCreate.draftOrder.id;
      const draftOrderName = draftData.data.draftOrderCreate.draftOrder.name;

      console.log(`ドラフト注文が作成されました: ドラフト注文ID ${draftOrderId}, ドラフト注文番号 ${draftOrderName}`);
      console.log(`Shopify管理画面で確認: https://${process.env.SHOPIFY_SHOP}/admin/draft_orders/${draftOrderName.replace('#D', '')}`);

      return {
        success: true,
        method: 'draft_order',
        draftOrderId,
        draftOrderName
      };
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    throw error;
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// スクリプトを実行
testOrderWithAppClient()
  .then((result) => {
    console.log('テストが完了しました:', result);
    process.exit(0);
  })
  .catch((error) => {
    console.error('テストが失敗しました:', error);
    process.exit(1);
  });
