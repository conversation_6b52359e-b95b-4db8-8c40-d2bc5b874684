# レンタル商品在庫管理ロジック（現状版）

## 概要

このドキュメントでは、レンタル商品ECシステムにおける現在の在庫管理ロジックについて説明します。ユニーク商品の管理方法、在庫状態の変更タイミング、メンテナンス状態の管理について解説します。

## 基本方針

### ユニーク商品の管理

本システムでは、すべての商品はユニークであり、在庫数は0か1（利用不可または利用可能）で管理しています。

- **在庫1（isAvailable = true）**: 商品がレンタル可能な状態
- **在庫0（isAvailable = false）**: 商品がレンタル不可能な状態

バリエーション（例：同じ椅子の色違い）は、それぞれ独立した商品としてSKUで管理しています。

### 在庫0となる条件

以下のいずれかの条件に該当する場合、商品の在庫は0（レンタル不可）となります：

1. **予約中**: 他の顧客によって予約されている
2. **メンテナンス中**: 点検・修理・クリーニングなどのメンテナンス中
3. **その他の理由**: 展示用、社内利用中、一時的に取り扱い停止など

### 在庫状態の変更タイミング

1. **予約確定時**: 予約期間中の在庫を0に設定
2. **予約キャンセル時**: 該当予約の期間中の在庫を1に戻す
3. **メンテナンス設定時**: メンテナンス期間中の在庫を0に設定
4. **メンテナンス完了時**: 該当期間の在庫を1に戻す（他の予約がない場合）

## 現在の実装方法

### 在庫カレンダーモデル

在庫状態を日付ごとに管理するための`InventoryCalendar`モデルを使用しています：

```typescript
// prisma/schema.prisma
model InventoryCalendar {
  id                String          @id @default(cuid())
  shop              String
  shopifyProductId  String          // Shopify商品ID
  date              DateTime        // 対象日
  isAvailable       Boolean         @default(true) // 利用可能フラグ
  unavailableReason String?         // 利用不可の理由（予約中、メンテナンス中など）

  // タイムスタンプ
  createdAt         DateTime        @default(now()) @map("created_at")
  updatedAt         DateTime        @updatedAt @map("updated_at")

  @@unique([shop, shopifyProductId, date])
  @@index([shop, date])
  @@map("inventory_calendar")
}
```

### レンタル可能期間モデル

商品ごとのレンタル可能期間を管理するための`RentalAvailability`モデルを使用しています：

```typescript
// prisma/schema.prisma
model RentalAvailability {
  id                String          @id @default(cuid())
  shop              String
  shopifyProductId  String          // Shopify商品ID
  startDate         DateTime        // レンタル可能開始日
  endDate           DateTime        // レンタル可能終了日
  isAvailable       Boolean         @default(true) // 利用可能フラグ
  notes             String?         // 備考

  // タイムスタンプ
  createdAt         DateTime        @default(now()) @map("created_at")
  updatedAt         DateTime        @updatedAt @map("updated_at")

  @@index([shop, shopifyProductId])
  @@index([startDate, endDate])
  @@map("rental_availabilities")
}
```

### メンテナンス状態の管理

メンテナンス状態は以下のメタフィールドで管理しています：

- `rental.メンテナンス状態`: メンテナンス状態
- `rental.最終メンテナンス日`: 最終メンテナンス日
- `rental.次回メンテナンス日`: 次回メンテナンス予定日
- `rental.メンテナンス備考`: メンテナンス備考

### 在庫チェックロジック

指定された期間に商品が利用可能かどうかをチェックする関数：

```typescript
/**
 * 指定された期間に商品が利用可能かチェックする関数
 * @param productId 商品ID
 * @param startDate 開始日
 * @param endDate 終了日
 * @returns 利用可能かどうか
 */
export async function isDateRangeAvailable(
  productId: string,
  startDate: Date,
  endDate: Date
): Promise<boolean> {
  try {
    // レンタル可能期間をチェック
    const availability = await prisma.rentalAvailability.findFirst({
      where: {
        shopifyProductId: productId,
        startDate: { lte: startDate },
        endDate: { gte: endDate },
        isAvailable: true
      }
    });

    if (!availability) {
      return false; // レンタル可能期間外
    }

    // 日付範囲内の在庫カレンダーを取得
    const inventoryCalendars = await prisma.inventoryCalendar.findMany({
      where: {
        shopifyProductId: productId,
        date: {
          gte: startDate,
          lte: endDate
        },
        isAvailable: false // 利用不可の日があるかチェック
      }
    });

    // 利用不可の日があれば、その期間は予約不可
    if (inventoryCalendars.length > 0) {
      return false;
    }

    return true; // 利用可能
  } catch (error) {
    console.error('Failed to check date range availability:', error);
    return false;
  }
}
```

### 在庫更新ロジック

予約確定時やキャンセル時に在庫状態を更新する関数：

```typescript
/**
 * 予約確定時に在庫を更新する関数
 * @param booking 予約情報
 */
export async function updateInventoryForBooking(booking: Booking): Promise<void> {
  try {
    const { productId, startDate, endDate, status } = booking;
    
    // 予約期間の日付範囲を取得
    const dateRange = getDateRange(startDate, endDate);
    
    // 予約がキャンセルされた場合
    const isCancel = status === 'CANCELLED';
    
    // 各日の在庫を更新
    for (const date of dateRange) {
      // 在庫カレンダーを取得または作成
      let inventory = await prisma.inventoryCalendar.findFirst({
        where: {
          shopifyProductId: productId,
          date: date
        }
      });
      
      if (!inventory) {
        // 在庫カレンダーが存在しない場合は作成
        inventory = await prisma.inventoryCalendar.create({
          data: {
            shopifyProductId: productId,
            date: date,
            isAvailable: true
          }
        });
      }
      
      // 在庫を更新
      await prisma.inventoryCalendar.update({
        where: { id: inventory.id },
        data: {
          isAvailable: isCancel ? true : false,
          unavailableReason: isCancel ? null : 'reserved'
        }
      });
    }
    
    // Shopify在庫も更新
    await updateShopifyInventory(productId, !isCancel);
  } catch (error) {
    console.error('Failed to update inventory for booking:', error);
    throw new Error(`Failed to update inventory: ${error.message}`);
  }
}

/**
 * Shopify在庫を更新する関数
 * @param productId 商品ID
 * @param decrease 在庫を減らすかどうか
 */
async function updateShopifyInventory(
  productId: string, 
  decrease: boolean
): Promise<void> {
  try {
    // Shopify Admin APIを使用して在庫を更新
    // ...
  } catch (error) {
    console.error('Failed to update Shopify inventory:', error);
    throw new Error(`Failed to update Shopify inventory: ${error.message}`);
  }
}
```

### メンテナンス状態更新ロジック

メンテナンス状態を更新する関数：

```typescript
/**
 * メンテナンス状態を更新する関数
 * @param productId 商品ID
 * @param status メンテナンス状態
 * @param lastMaintenanceDate 最終メンテナンス日
 * @param nextMaintenanceDate 次回メンテナンス予定日
 * @param notes メンテナンス備考
 */
export async function updateMaintenanceStatus(
  productId: string,
  status: string,
  lastMaintenanceDate?: Date,
  nextMaintenanceDate?: Date,
  notes?: string
): Promise<void> {
  try {
    // メタフィールドを更新
    await updateProductMetafields(
      productId,
      'メンテナンス状態',
      status
    );
    
    if (lastMaintenanceDate) {
      await updateProductMetafields(
        productId,
        '最終メンテナンス日',
        formatDate(lastMaintenanceDate)
      );
    }
    
    if (nextMaintenanceDate) {
      await updateProductMetafields(
        productId,
        '次回メンテナンス日',
        formatDate(nextMaintenanceDate)
      );
    }
    
    if (notes) {
      await updateProductMetafields(
        productId,
        'メンテナンス備考',
        notes
      );
    }
    
    // メンテナンス中の場合は在庫を更新
    if (status !== '良好') {
      // 現在から次回メンテナンス日までの在庫を更新
      const startDate = new Date();
      const endDate = nextMaintenanceDate || new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000); // デフォルトは1週間後
      
      const dateRange = getDateRange(startDate, endDate);
      
      for (const date of dateRange) {
        // 在庫カレンダーを取得または作成
        let inventory = await prisma.inventoryCalendar.findFirst({
          where: {
            shopifyProductId: productId,
            date: date
          }
        });
        
        if (!inventory) {
          // 在庫カレンダーが存在しない場合は作成
          inventory = await prisma.inventoryCalendar.create({
            data: {
              shopifyProductId: productId,
              date: date,
              isAvailable: true
            }
          });
        }
        
        // 在庫を更新（予約がない場合のみ）
        if (inventory.unavailableReason !== 'reserved') {
          await prisma.inventoryCalendar.update({
            where: { id: inventory.id },
            data: {
              isAvailable: false,
              unavailableReason: 'maintenance'
            }
          });
        }
      }
    }
  } catch (error) {
    console.error('Failed to update maintenance status:', error);
    throw new Error(`Failed to update maintenance status: ${error.message}`);
  }
}
```

## 在庫状態の視覚化

### カレンダーUIでの表示

現在、datepickerを使用して在庫状態を視覚化する機能を開発中です。この機能により、以下が可能になります：

- 利用可能日と利用不可日の視覚的な区別
- 予約中の日とメンテナンス中の日の区別
- 日付選択時の在庫状態の確認

```typescript
// 在庫状態に応じた色分け
const getDateCellColor = (date: Date, inventoryStatus: any) => {
  if (!inventoryStatus) {
    return 'gray'; // データなし
  }
  
  if (!inventoryStatus.isAvailable) {
    if (inventoryStatus.unavailableReason === 'maintenance') {
      return 'orange'; // メンテナンス中
    } else if (inventoryStatus.unavailableReason === 'reserved') {
      return 'red'; // 予約済み
    } else {
      return 'gray'; // その他の理由
    }
  }
  
  return 'green'; // 利用可能
};
```

## バリエーション管理

### 現在のバリエーション管理方法

バリエーションは、Shopifyの標準的なバリエーション機能ではなく、個別のSKUを持つ独立した商品として管理しています。これにより、各バリエーションを独立した在庫として扱うことができます。

例えば、同じデザインの椅子でも色違いの場合：
- デザイナーズチェア（ブラック）: SKU: CHAIR-A-BLK
- デザイナーズチェア（ホワイト）: SKU: CHAIR-A-WHT

この方法により、各バリエーションの在庫状態を個別に管理できます。

## 今後の拡張可能性

現在の在庫管理ロジックは基本的な機能を提供していますが、将来的には以下の拡張が考えられます：

1. **在庫状態の詳細化**:
   - メンテナンス状態の細分化（軽微な修理、大規模修理など）
   - 予約状態の詳細化（仮予約中、本予約中など）

2. **在庫カレンダーの視覚化強化**:
   - 色分けによる状態表示の強化
   - ホバー時の詳細情報表示

3. **メンテナンス履歴の管理**:
   - メンテナンス履歴の詳細記録
   - メンテナンス種類の分類（定期点検、修理、クリーニングなど）

4. **メンテナンス予定の管理**:
   - メンテナンス予定の詳細管理
   - メンテナンス担当者の割り当て

ただし、これらの拡張は現時点では優先度が低く、必要に応じて実装を検討します。当面は現在の在庫管理ロジックを維持し、datepickerによる視覚化機能の開発を優先します。

## まとめ

このドキュメントでは、レンタル商品ECシステムにおける現在の在庫管理ロジックについて説明しました。ユニーク商品の管理、在庫状態の変更タイミング、メンテナンス状態の管理について解説しました。

現在の在庫管理ロジックは、基本的なレンタル商品管理に必要な機能を提供しており、今後の拡張も可能な設計となっています。特に、各商品を在庫1→0で管理する方針は、レンタル商品の特性に適しており、効率的な在庫管理を実現しています。
