/*!
 * jquery.instagramFeed
 *
 * @version 1.2.0
 *
 * <AUTHOR> <<EMAIL>>
 * @contributor c<PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * https://github.com/jsanahuja/jquery.instagramFeed
 *
 */
if(!window.jQuery.fn.is_theme_jquery){window.jQueryBackup=window.jQuery;window.jQuery=window.jQueryTheme;}(function($){(function(a){function b(a){return a.replace(/[&<>"'`=\/]/g,function(a){return l[a]})}function c(a){return"undefined"!=typeof a.node.edge_media_to_caption.edges[0]&&"undefined"!=typeof a.node.edge_media_to_caption.edges[0].node&&"undefined"!=typeof a.node.edge_media_to_caption.edges[0].node.text&&null!==a.node.edge_media_to_caption.edges[0].node.text?a.node.edge_media_to_caption.edges[0].node.text:"undefined"!=typeof a.node.title&&null!==a.node.title&&0!=a.node.title.length?a.node.title:!("undefined"==typeof a.node.accessibility_caption||null===a.node.accessibility_caption||0==a.node.accessibility_caption.length)&&a.node.accessibility_caption}function d(a,b){var c=b||!1;if(!b&&0<a.cache_time){var d=localStorage.getItem(a.cache_time_key);null!==d&&parseInt(d)+60000*a.cache_time>new Date().getTime()&&(c=!0)}if(c){var e=localStorage.getItem(a.cache_data_key);if(null!==e)return JSON.parse(e)}return!1}function e(a,b){var c=localStorage.getItem(a.cache_time_key),d=0!=a.cache_time&&(null===c||parseInt(c)+60000*a.cache_time>new Date().getTime());d&&(localStorage.setItem(a.cache_data_key,JSON.stringify(b)),localStorage.setItem(a.cache_time_key,new Date().getTime()))}function f(a,b){switch(a){case"username":case"tag":case"location":try{b=b.split("window._sharedData = ")[1].split("</script>")[0]}catch(a){return!1}return b=JSON.parse(b.substr(0,b.length-1)),b=b.entry_data.ProfilePage||b.entry_data.TagPage||b.entry_data.LocationsPage,"undefined"!=typeof b&&(b[0].graphql.user||b[0].graphql.hashtag||b[0].graphql.location);break;case"userid":return"undefined"!=typeof b.data.user&&b.data.user;}}function g(b,c,d,h,i,j){var k;i&&j&&(k="https://images"+~~(3333*Math.random())+"-focus-opensocial.googleusercontent.com/gadgets/proxy?container=none&url="+b),a.get(k||b,function(a){var b=f(c,a);!1===b?h(!1):h(b)}).fail(function(a){1<d?(console.warn("Instagram Feed: Request failed, "+(d-1)+" tries left. Retrying..."),g(b,c,d-1,h,i,!j)):h(!1,a)})}function h(a,b){var c=d(a,!1);if(!1!==c)b(c);else{var f;switch(a.type){case"username":f=a.host+a.id+"/";break;case"tag":f=a.host+"explore/tags/"+a.id+"/";break;case"location":f=a.host+"explore/locations/"+a.id+"/";break;case"userid":f=a.host+"graphql/query/?query_id=17888483320059182&variables={\"id\":\""+a.id+"\",\"first\":"+a.items+",\"after\":null}";}g(f,a.type,a.max_tries,function(c,f){!1===c?"undefined"==typeof f?a.on_error("Instagram Feed: It looks like the profile you are trying to fetch is age restricted. See https://github.com/jsanahuja/InstagramFeed/issues/26",3):(c=d(a,!0),!1===c?a.on_error("Instagram Feed: Unable to fetch the given user/tag. Instagram responded with the status code: "+f.status,5):b(c)):(e(a,c),b(c))},a.host===j.host&&"userid"!=a.type,!1)}}function i(d,e){var f,g="";if(d.styling){var h=(100-2*d.margin*d.items_per_row)/d.items_per_row;f={profile_container:" style=\"text-align:center;\"",profile_image:" style=\"border-radius:10em;width:15%;max-width:125px;min-width:50px;\"",profile_name:" style=\"font-size:1.2em;\"",profile_biography:" style=\"font-size:1em;\"",gallery_image:" style=\"width:100%;\"",gallery_image_link:" style=\"width:"+h+"%; margin:"+d.margin+"%;position:relative; display: inline-block; height: 100%;\""},d.display_captions&&(g+="<style>                    a[data-caption]:hover::after {                        content: attr(data-caption);                        text-align: center;                        font-size: 0.8rem;                        color: black;                        position: absolute;                        left: 0;                        right: 0;                        bottom: 0;                        padding: 1%;                        max-height: 100%;                        overflow-y: auto;                        overflow-x: hidden;                        background-color: hsla(0, 100%, 100%, 0.8);                    }                </style>")}else f={profile_container:"",profile_image:"",profile_name:"",profile_biography:"",gallery_image:"",gallery_image_link:""};if(d.display_profile&&"userid"!==d.type&&(g+="<div class=\"instagram_profile\""+f.profile_container+">",g+="<img class=\"instagram_profile_image\" src=\""+e.profile_pic_url+"\" alt=\""+("tag"==d.type?e.name+" tag pic":e.username+" profile pic")+"\""+f.profile_image+(d.lazy_load?" loading=\"lazy\"":"")+" />","tag"==d.type?g+="<p class=\"instagram_tag\""+f.profile_name+"><a href=\"https://www.instagram.com/explore/tags/"+d.tag+"/\" rel=\"noopener\" target=\"_blank\">#"+d.tag+"</a></p>":"username"==d.type?(g+="<p class='instagram_username'"+f.profile_name+">@"+e.full_name+" (<a href='https://www.instagram.com/"+d.username+"/' rel='noopener' target='_blank'>@"+d.username+"</a>)</p>",d.display_biography&&(g+="<p class='instagram_biography'"+f.profile_biography+">"+e.biography+"</p>")):"location"==d.type&&(g+="<p class='instagram_location'"+f.profile_name+"><a href='https://www.instagram.com/explore/locations/"+d.location+"/' rel='noopener' target='_blank'>"+e.name+"</a></p>"),g+="</div>"),d.display_gallery)if("undefined"!=typeof e.is_private&&!0===e.is_private)g+="<p class=\"instagram_private\"><strong>This profile is private</strong></p>";else{var j="undefined"==typeof k[d.image_size]?k[640]:k[d.image_size],l=(e.edge_owner_to_timeline_media||e.edge_hashtag_to_media||e.edge_location_to_media).edges,m=l.length>d.items?d.items:l.length;g+="<div class='instagram_gallery'>";for(var n=0;n<m;n++){var o,p,q="https://www.instagram.com/p/"+l[n].node.shortcode,r=c(l[n],e);switch(!1===r&&(r=("userid"==d.type?"":d.id)+" image"),r=b(r),l[n].node.__typename){case"GraphSidecar":p="sidecar",o=l[n].node.thumbnail_resources[j].src;break;case"GraphVideo":p="video",o=l[n].node.thumbnail_src;break;default:p="image",o=l[n].node.thumbnail_resources[j].src;}g+="<a href=\""+q+"\""+(d.display_captions?" data-caption=\""+r+"\"":"")+" class=\"instagram-"+p+"\" rel=\"noopener\" target=\"_blank\""+f.gallery_image_link+">",g+="<img"+(d.lazy_load?" loading=\"lazy\"":"")+" src=\""+o+"\" alt=\""+r+"\""+f.gallery_image+" />",g+="</a>"}g+="</div>"}if(d.display_igtv&&"undefined"!=typeof e.edge_felix_video_timeline){var s=e.edge_felix_video_timeline.edges,m=s.length>d.items?d.items:s.length;if(0<s.length){g+="<div class=\"instagram_igtv\">";for(var n=0;n<m;n++){var q="https://www.instagram.com/p/"+s[n].node.shortcode,r=c(s[n],e);!1===r&&(r=("userid"==d.type?"":d.id)+" image"),r=b(r),g+="<a href=\""+q+"\""+(d.display_captions?" data-caption=\""+r+"\"":"")+" rel=\"noopener\" target=\"_blank\""+f.gallery_image_link+">",g+="<img"+(d.lazy_load?" loading=\"lazy\"":"")+" src=\""+s[n].node.thumbnail_src+"\" alt=\""+r+"\""+f.gallery_image+" />",g+="</a>"}g+="</div>"}}a(d.container).html(g)}var j={host:"https://www.instagram.com/",username:"",tag:"",user_id:"",location:"",container:"",display_profile:!0,display_biography:!0,display_gallery:!0,display_captions:!1,display_igtv:!1,max_tries:8,callback:null,styling:!0,items:8,items_per_row:4,margin:.5,image_size:640,lazy_load:!1,cache_time:360,on_error:console.error},k={150:0,240:1,320:2,480:3,640:4},l={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"};a.instagramFeed=function(b){var c=a.fn.extend({},j,b);return""==c.username&&""==c.tag&&""==c.user_id&&""==c.location?(c.on_error("Instagram Feed: Error, no username, tag or user_id defined.",1),!1):("undefined"!=typeof b.display_profile&&b.display_profile&&""!=c.user_id&&console.warn("Instagram Feed: 'display_profile' is not available using 'user_id' (GraphQL API)"),"undefined"!=typeof b.display_biography&&b.display_biography&&(""!=c.tag||""!=c.location||""!=c.user_id)&&console.warn("Instagram Feed: 'display_biography' is not available unless you are loading an user ('username' parameter)"),"undefined"!=typeof c.get_data&&console.warn("Instagram Feed: options.get_data is deprecated, options.callback is always called if defined"),null==c.callback&&""==c.container)?(c.on_error("Instagram Feed: Error, neither container found nor callback defined.",2),!1):(""==c.username?""==c.tag?""==c.location?(c.type="userid",c.id=c.user_id):(c.type="location",c.id=c.location):(c.type="tag",c.id=c.tag):(c.type="username",c.id=c.username),c.cache_data_key="instagramFeed_"+c.type+"_"+c.id,c.cache_time_key=c.cache_data_key+"_time",h(c,function(a){""!=c.container&&i(c,a),null!=c.callback&&c.callback(a)}),!0)}})(jQuery);})(jQueryTheme);if(window.jQueryBackup){window.jQuery=window.jQueryBackup;window.jQueryBackup=undefined;}