/**
 * カテゴリマスタ管理サービス
 * 
 * 商品カテゴリとサブカテゴリの管理、SKU生成、検証機能を提供
 */

import { prisma } from "~/db.server";

export interface CategoryMasterData {
  id: string;
  shop: string;
  code: string;
  name: string;
  parentCode?: string;
  level: number;
  displayOrder: number;
  isActive: boolean;
  description?: string;
  subCategories?: SubCategoryMasterData[];
}

export interface SubCategoryMasterData {
  id: string;
  shop: string;
  code: string;
  name: string;
  parentCategoryCode: string;
  isActive: boolean;
  displayOrder: number;
  description?: string;
}

export interface SKUStructure {
  categoryCode: string;
  subCategoryCode: string;
  serialNumber: string;
}

export class CategoryMasterService {
  /**
   * ショップのカテゴリマスタ一覧を取得
   */
  async getCategoriesByShop(shop: string): Promise<CategoryMasterData[]> {
    const categories = await prisma.categoryMaster.findMany({
      where: { shop },
      include: {
        subCategories: {
          where: { isActive: true },
          orderBy: { displayOrder: 'asc' }
        }
      },
      orderBy: { displayOrder: 'asc' }
    });

    return categories;
  }

  /**
   * アクティブなカテゴリのみを取得
   */
  async getActiveCategoriesByShop(shop: string): Promise<CategoryMasterData[]> {
    const categories = await prisma.categoryMaster.findMany({
      where: { 
        shop,
        isActive: true 
      },
      include: {
        subCategories: {
          where: { isActive: true },
          orderBy: { displayOrder: 'asc' }
        }
      },
      orderBy: { displayOrder: 'asc' }
    });

    return categories;
  }

  /**
   * カテゴリコードでカテゴリを取得
   */
  async getCategoryByCode(shop: string, code: string): Promise<CategoryMasterData | null> {
    const category = await prisma.categoryMaster.findUnique({
      where: { 
        shop_code: { shop, code }
      },
      include: {
        subCategories: {
          where: { isActive: true },
          orderBy: { displayOrder: 'asc' }
        }
      }
    });

    return category;
  }

  /**
   * サブカテゴリを取得
   */
  async getSubCategoryByCode(
    shop: string, 
    categoryCode: string, 
    subCategoryCode: string
  ): Promise<SubCategoryMasterData | null> {
    const subCategory = await prisma.subCategoryMaster.findUnique({
      where: {
        shop_code_parentCategoryCode: {
          shop,
          code: subCategoryCode,
          parentCategoryCode: categoryCode
        }
      }
    });

    return subCategory;
  }

  /**
   * SKUを解析してカテゴリ情報を取得
   */
  parseSKU(sku: string): SKUStructure | null {
    const pattern = /^(\d{3})-(\d{2})-(\d{3})$/;
    const match = sku.match(pattern);
    
    if (!match) return null;
    
    return {
      categoryCode: match[1],
      subCategoryCode: match[2],
      serialNumber: match[3]
    };
  }

  /**
   * SKUの妥当性を検証
   */
  async validateSKU(shop: string, sku: string): Promise<{
    isValid: boolean;
    categoryExists: boolean;
    subCategoryExists: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];
    let categoryExists = false;
    let subCategoryExists = false;

    // SKU形式チェック
    const skuStructure = this.parseSKU(sku);
    if (!skuStructure) {
      errors.push('SKU形式が正しくありません。形式: XXX-XX-XXX');
      return { isValid: false, categoryExists, subCategoryExists, errors };
    }

    const { categoryCode, subCategoryCode } = skuStructure;

    // カテゴリ存在チェック
    const category = await this.getCategoryByCode(shop, categoryCode);
    if (category) {
      categoryExists = true;
    } else {
      errors.push(`カテゴリコード ${categoryCode} が存在しません`);
    }

    // サブカテゴリ存在チェック
    if (categoryExists) {
      const subCategory = await this.getSubCategoryByCode(shop, categoryCode, subCategoryCode);
      if (subCategory) {
        subCategoryExists = true;
      } else {
        errors.push(`サブカテゴリコード ${subCategoryCode} が存在しません`);
      }
    }

    return {
      isValid: errors.length === 0,
      categoryExists,
      subCategoryExists,
      errors
    };
  }

  /**
   * 次のSKUを生成
   */
  async generateNextSKU(shop: string, categoryCode: string, subCategoryCode: string): Promise<string> {
    // カテゴリとサブカテゴリの存在確認
    const validation = await this.validateSKU(shop, `${categoryCode}-${subCategoryCode}-001`);
    if (!validation.categoryExists || !validation.subCategoryExists) {
      throw new Error(`カテゴリまたはサブカテゴリが存在しません: ${categoryCode}-${subCategoryCode}`);
    }

    // 既存の最大連番を取得
    const lastProduct = await prisma.product.findFirst({
      where: {
        shop,
        sku: { startsWith: `${categoryCode}-${subCategoryCode}-` }
      },
      orderBy: { sku: 'desc' }
    });

    let nextSerial = 1;
    if (lastProduct) {
      const skuStructure = this.parseSKU(lastProduct.sku);
      if (skuStructure) {
        nextSerial = parseInt(skuStructure.serialNumber) + 1;
      }
    }

    return `${categoryCode}-${subCategoryCode}-${nextSerial.toString().padStart(3, '0')}`;
  }

  /**
   * カテゴリマスタを作成
   */
  async createCategory(data: {
    shop: string;
    code: string;
    name: string;
    parentCode?: string;
    level?: number;
    displayOrder?: number;
    description?: string;
  }): Promise<CategoryMasterData> {
    const category = await prisma.categoryMaster.create({
      data: {
        shop: data.shop,
        code: data.code,
        name: data.name,
        parentCode: data.parentCode,
        level: data.level || 1,
        displayOrder: data.displayOrder || 0,
        description: data.description
      },
      include: {
        subCategories: true
      }
    });

    return category;
  }

  /**
   * サブカテゴリマスタを作成
   */
  async createSubCategory(data: {
    shop: string;
    code: string;
    name: string;
    parentCategoryCode: string;
    displayOrder?: number;
    description?: string;
  }): Promise<SubCategoryMasterData> {
    const subCategory = await prisma.subCategoryMaster.create({
      data: {
        shop: data.shop,
        code: data.code,
        name: data.name,
        parentCategoryCode: data.parentCategoryCode,
        displayOrder: data.displayOrder || 0,
        description: data.description
      }
    });

    return subCategory;
  }

  /**
   * カテゴリマスタを更新
   */
  async updateCategory(
    shop: string, 
    code: string, 
    data: Partial<CategoryMasterData>
  ): Promise<CategoryMasterData> {
    const category = await prisma.categoryMaster.update({
      where: { shop_code: { shop, code } },
      data: {
        name: data.name,
        parentCode: data.parentCode,
        level: data.level,
        displayOrder: data.displayOrder,
        isActive: data.isActive,
        description: data.description
      },
      include: {
        subCategories: true
      }
    });

    return category;
  }

  /**
   * サブカテゴリマスタを更新
   */
  async updateSubCategory(
    shop: string,
    categoryCode: string,
    subCategoryCode: string,
    data: Partial<SubCategoryMasterData>
  ): Promise<SubCategoryMasterData> {
    const subCategory = await prisma.subCategoryMaster.update({
      where: {
        shop_code_parentCategoryCode: {
          shop,
          code: subCategoryCode,
          parentCategoryCode: categoryCode
        }
      },
      data: {
        name: data.name,
        displayOrder: data.displayOrder,
        isActive: data.isActive,
        description: data.description
      }
    });

    return subCategory;
  }

  /**
   * 商品のカテゴリ情報を更新
   */
  async updateProductCategory(shop: string, sku: string): Promise<void> {
    const skuStructure = this.parseSKU(sku);
    if (!skuStructure) return;

    const category = await this.getCategoryByCode(shop, skuStructure.categoryCode);
    if (!category) return;

    await prisma.product.updateMany({
      where: { shop, sku },
      data: {
        basicInfo: {
          // 既存のbasicInfoを保持しつつカテゴリ情報を更新
          // 実際の実装では既存データとマージする必要がある
        }
      }
    });
  }
}
