# 複雑な住所要件への対応 - 実践的なアプローチ

## 実際のビジネスケース

### ケース1: 請求先≠依頼者
```
依頼者: 個人スタイリスト（田中さん）
請求先: 所属事務所（株式会社クリエイティブ）
配送先: 撮影スタジオ（渋谷スタジオA）
```

### ケース2: 複数配送先
```
依頼者: イベント企画会社
請求先: 同じ会社
配送先: 
  - 1日目：東京ビッグサイト
  - 2日目：幕張メッセ  
  - 3日目：パシフィコ横浜
```

### ケース3: 代理店経由
```
エンドユーザー: 撮影会社A
代理店: 機材レンタル代理店B
請求先: 代理店B
配送先: 撮影会社A
```

## 対応戦略：段階的アプローチ

### レベル1: Shopify標準 + 工夫（80%のケース）

#### 1.1 Customer Notes活用
```typescript
// Shopify Customer.note に追加情報を保存
const customerNote = `
請求先情報:
会社名: 株式会社クリエイティブ
部署: 経理部
担当: 山田太郎
住所: 東京都港区...

配送先候補:
1. 渋谷スタジオA（東京都渋谷区...）
2. 新宿スタジオB（東京都新宿区...）
3. 自宅（デフォルト住所）
`;

await shopify.rest.Customer.save({
  session,
  id: customerId,
  note: customerNote
});
```

#### 1.2 Order Note Attributes活用
```typescript
// 注文時に具体的な請求先・配送先を指定
const draftOrder = new shopify.rest.DraftOrder({
  session,
  customer: { id: customerId },
  shipping_address: selectedShippingAddress,
  note_attributes: [
    { name: "請求先会社", value: "株式会社クリエイティブ" },
    { name: "請求先部署", value: "経理部" },
    { name: "請求先担当者", value: "山田太郎" },
    { name: "請求先住所", value: "東京都港区..." },
    { name: "配送場所名", value: "渋谷スタジオA" },
    { name: "配送時間帯", value: "午前中" }
  ]
});
```

### レベル2: Prisma拡張（15%のケース）

#### 2.1 顧客別アドレスプール
```prisma
model CustomerAddressPool {
  id           String  @id @default(cuid())
  shop         String
  customerId   String  // Shopify顧客ID
  addressType  String  // 'billing' | 'shipping'
  name         String  // 「本社」「渋谷スタジオ」「幕張会場」
  
  // 基本住所情報
  companyName  String?
  department   String?
  contactName  String?
  postalCode   String?
  prefecture   String?
  city         String?
  address      String?
  phone        String?
  email        String?
  
  // 配送特有情報
  deliveryNotes String?
  timeSlot     String?
  accessInfo   String? // 「2F受付で呼び出し」など
  
  isActive     Boolean @default(true)
  isDefault    Boolean @default(false)
  
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  @@index([shop, customerId, addressType])
  @@map("customer_address_pool")
}
```

#### 2.2 注文時の住所スナップショット
```prisma
model OrderAddressSnapshot {
  id                String @id @default(cuid())
  shop              String
  orderId           String // Shopify注文ID
  bookingId         String? // 予約ID（あれば）
  
  // 実際に使用された住所情報
  billingSnapshot   Json   // 請求先の完全な情報
  shippingSnapshot  Json   // 配送先の完全な情報
  
  createdAt         DateTime @default(now())
  
  @@index([shop, orderId])
  @@map("order_address_snapshots")
}
```

### レベル3: 高度な管理（5%のケース）

#### 3.1 組織構造管理
```prisma
model Organization {
  id              String @id @default(cuid())
  shop            String
  name            String // 「株式会社クリエイティブ」
  parentOrgId     String? // 親組織
  orgType         String  // 'company' | 'department' | 'branch'
  
  // 組織の基本情報
  businessAddress Json?   // 本社所在地
  billingAddress  Json?   // 請求先住所
  taxNumber       String? // 法人番号
  
  members         OrganizationMember[]
  addresses       OrganizationAddress[]
  
  @@index([shop])
  @@map("organizations")
}

model OrganizationMember {
  id             String @id @default(cuid())
  organizationId String
  customerId     String // Shopify顧客ID
  role           String // 'admin' | 'member' | 'viewer'
  canOrder       Boolean @default(false)
  canApproveBilling Boolean @default(false)
  
  organization   Organization @relation(fields: [organizationId], references: [id])
  
  @@index([organizationId, customerId])
  @@map("organization_members")
}
```

## 実装パターン

### パターンA: シンプル拡張（推奨）

#### UI実装
```typescript
// app/components/AddressSelector.tsx
export function AddressSelector({ customerId, type, onSelect }) {
  const [addresses, setAddresses] = useState([]);
  const [showCustomForm, setShowCustomForm] = useState(false);
  
  // 1. Shopify標準住所を取得
  // 2. Prismaの拡張住所を取得
  // 3. 統合リストを表示
  
  return (
    <div className="address-selector">
      <h4>{type === 'billing' ? '請求先' : '配送先'}を選択</h4>
      
      {addresses.map(addr => (
        <div key={addr.id} className="address-option">
          <input 
            type="radio" 
            name={`${type}-address`}
            value={addr.id}
            onChange={() => onSelect(addr)}
          />
          <label>
            <strong>{addr.name}</strong><br/>
            {addr.companyName && <span>{addr.companyName}<br/></span>}
            {addr.department && <span>{addr.department}<br/></span>}
            <span>{addr.fullAddress}</span>
          </label>
        </div>
      ))}
      
      <div className="address-option">
        <input 
          type="radio" 
          name={`${type}-address`}
          value="custom"
          onChange={() => setShowCustomForm(true)}
        />
        <label>この注文専用の{type === 'billing' ? '請求先' : '配送先'}を入力</label>
      </div>
      
      {showCustomForm && (
        <CustomAddressForm 
          type={type}
          onSave={handleCustomAddress}
          onCancel={() => setShowCustomForm(false)}
        />
      )}
    </div>
  );
}
```

#### 注文作成フロー
```typescript
// app/routes/app.booking.new.tsx
export default function NewBooking() {
  const [selectedBilling, setSelectedBilling] = useState(null);
  const [selectedShipping, setSelectedShipping] = useState(null);
  
  const handleCreateOrder = async () => {
    // 1. Draft Orderを作成（Shopify標準住所使用）
    const draftOrder = await createDraftOrder({
      customerId,
      shippingAddress: selectedShipping.shopifyAddress || getDefaultAddress(),
      lineItems
    });
    
    // 2. 詳細住所情報をスナップショットとして保存
    await saveAddressSnapshot({
      orderId: draftOrder.id,
      billingSnapshot: selectedBilling,
      shippingSnapshot: selectedShipping
    });
    
    // 3. 予約データに関連付け
    await createBooking({
      orderId: draftOrder.id,
      billingAddressId: selectedBilling.id,
      shippingAddressId: selectedShipping.id
    });
  };
}
```

### パターンB: ワンタイム住所（注文専用）

```typescript
// 注文毎に新しい住所を入力（保存しない）
export function OneTimeAddressForm({ type, onSave }) {
  const [address, setAddress] = useState({
    companyName: '',
    department: '',
    contactName: '',
    phone: '',
    postalCode: '',
    address: '',
    notes: ''
  });
  
  const handleSave = () => {
    // 住所をCustomerAddressPoolには保存せず、
    // 注文のnote_attributesまたはOrderAddressSnapshotにのみ保存
    onSave({
      ...address,
      isOneTime: true, // ワンタイムフラグ
      displayName: `${address.companyName} ${address.department || ''}`
    });
  };
  
  return (
    <form onSubmit={handleSave}>
      <h4>この注文専用の{type === 'billing' ? '請求先' : '配送先'}</h4>
      {/* 住所入力フォーム */}
    </form>
  );
}
```

## 段階的実装スケジュール

### Week 1: 基本拡張
```typescript
// CustomerAddressPool テーブル作成
// 基本的なCRUD API実装
// 管理画面での住所登録機能
```

### Week 2: 注文連携
```typescript
// AddressSelector コンポーネント
// OneTimeAddressForm コンポーネント
// OrderAddressSnapshot 機能
```

### Week 3: 配送管理統合
```typescript
// 配送一覧画面での詳細住所表示
// ピッキングリストでの配送先詳細
// 配送指示書の生成
```

### Week 4: 高度な機能
```typescript
// 住所の再利用提案
// 配送先候補の自動保存
// 組織別住所管理（必要に応じて）
```

## 実際の運用例

### 企業顧客（スタイリスト事務所）の住所設定

```typescript
const customerAddresses = {
  // Shopify標準（デフォルト）
  defaultAddress: {
    company: "株式会社クリエイティブ",
    address1: "東京都港区南青山1-1-1",
    first_name: "田中",
    last_name: "花子"
  },
  
  // Prisma拡張
  billingAddresses: [
    {
      name: "本社経理部",
      companyName: "株式会社クリエイティブ",
      department: "経理部",
      contactName: "山田太郎",
      isDefault: true
    }
  ],
  
  shippingAddresses: [
    {
      name: "渋谷スタジオA",
      companyName: "渋谷撮影スタジオ",
      address: "東京都渋谷区...",
      contactName: "佐藤次郎",
      timeSlot: "9-12",
      accessInfo: "2F受付で呼び出し"
    },
    {
      name: "新宿スタジオB", 
      companyName: "新宿フォトスタジオ",
      address: "東京都新宿区...",
      contactName: "鈴木三郎",
      timeSlot: "14-18"
    }
  ]
};
```

このアプローチなら：
- ✅ 90%のシンプルなケースはShopify標準で対応
- ✅ 複雑なケースは段階的にPrisma拡張
- ✅ 注文毎の特別な住所にも対応
- ✅ 将来の組織管理にも拡張可能

どのレベルから実装を始めますか？