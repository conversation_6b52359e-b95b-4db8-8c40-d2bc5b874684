/**
 * 予約から注文を作成するスクリプト
 *
 * このスクリプトは、既存の予約情報からShopify注文を作成します。
 * 予約IDを指定して実行すると、その予約情報を元に注文を作成します。
 *
 * 使用方法:
 * npx tsx scripts/create-order-from-booking.ts --id [予約ID]
 *
 * オプション:
 * --id: 予約ID（必須）
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { authenticate } from '../app/shopify.server';
import { createOrderFromBooking } from '../app/utils/booking/order-creator';
import { parseArgs } from 'node:util';
import { format } from 'date-fns';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * Shopify Admin APIを初期化する関数
 */
async function initializeShopifyAdmin() {
  // 環境変数からShopify情報を取得
  const shopifyShop = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';
  const shopifyAdminApiAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

  if (!shopifyAdminApiAccessToken) {
    console.error('SHOPIFY_ADMIN_API_ACCESS_TOKEN環境変数が設定されていません');
    process.exit(1);
  }

  console.log('Shopify情報:');
  console.log('- Shop:', shopifyShop);
  console.log('- Access Token:', shopifyAdminApiAccessToken.substring(0, 5) + '...');

  // リクエストオブジェクトを作成（ダミー）
  const headers = new Headers();
  headers.append('Content-Type', 'application/json');
  headers.append('X-Shopify-Access-Token', shopifyAdminApiAccessToken);

  const request = new Request(`https://${shopifyShop}/admin/api/2025-01/graphql.json`, {
    method: 'GET',
    headers: headers
  });

  try {
    // authenticate.adminを呼び出す
    const { admin } = await authenticate.admin(request);
    console.log('Shopify Admin APIの初期化が完了しました');

    // adminオブジェクトを返す
    return admin;
  } catch (authError) {
    console.error('Shopify Admin API認証エラー:', authError);

    // 代替手段として、直接GraphQLクライアントを作成
    console.log('代替手段としてGraphQLクライアントを直接作成します...');

    // 簡易的なGraphQLクライアントを作成
    const admin = {
      graphql: async (query: string, options: any) => {
        const response = await fetch(`https://${shopifyShop}/admin/api/2025-01/graphql.json`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': shopifyAdminApiAccessToken
          },
          body: JSON.stringify({
            query,
            variables: options.variables
          })
        });

        return response;
      }
    };

    console.log('代替GraphQLクライアントの作成が完了しました');
    return admin;
  }
}

/**
 * 予約情報を表示する関数
 */
async function displayBookingInfo(bookingId: string) {
  try {
    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        product: true
      }
    });

    if (!booking) {
      console.error(`予約ID ${bookingId} が見つかりません`);
      return null;
    }

    // 予約情報を表示
    console.log('予約情報:');
    console.log('- 予約ID:', booking.id);
    console.log('- 予約番号:', booking.bookingId);
    console.log('- 予約タイプ:', booking.bookingType);
    console.log('- 開始日:', format(booking.startDate, 'yyyy/MM/dd'));
    console.log('- 終了日:', format(booking.endDate, 'yyyy/MM/dd'));
    console.log('- 顧客名:', booking.customerName);
    console.log('- 顧客メール:', booking.customerEmail);
    console.log('- 顧客ID:', booking.customerId);

    if (booking.product) {
      console.log('- 商品名:', booking.product.title);
      console.log('- 商品ID:', booking.product.id);
      console.log('- Shopify商品ID:', booking.product.shopifyId);
    } else {
      console.log('- 商品情報: 見つかりません');
      console.log('- 商品ID (予約から):', booking.productId || 'なし');
    }

    console.log('- 合計金額:', booking.totalAmount);
    console.log('- 注文ID:', booking.orderId || 'なし');
    console.log('- 注文番号:', booking.orderName || 'なし');

    return booking;
  } catch (error) {
    console.error('予約情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * メイン処理
 */
async function main() {
  try {
    // コマンドライン引数を解析
    const args = parseArgs({
      options: {
        id: { type: 'string' }
      },
      strict: false
    });

    const bookingId = args.values.id;

    if (!bookingId) {
      console.error('予約IDを指定してください');
      console.error('使用方法: npx tsx scripts/create-order-from-booking.ts --id [予約ID]');
      process.exit(1);
    }

    console.log(`予約ID ${bookingId} から注文を作成します...`);

    // 予約情報を表示
    const booking = await displayBookingInfo(bookingId);

    if (!booking) {
      process.exit(1);
    }

    // 既に注文が関連付けられているかチェック
    if (booking.orderId) {
      console.log(`予約ID ${bookingId} には既に注文ID ${booking.orderId} が関連付けられています。`);
      console.log('既存の注文を削除してから再実行するか、別の予約IDを指定してください。');
      process.exit(1);
    }

    // 商品情報が存在するか確認
    if (!booking.product) {
      console.log('警告: 予約に関連付けられた商品情報が見つかりません。');
      console.log('商品情報を取得して関連付けます...');

      // 商品情報を取得
      if (booking.productId) {
        try {
          const product = await prisma.product.findUnique({
            where: { id: booking.productId }
          });

          if (product) {
            console.log('商品情報を取得しました:');
            console.log('- 商品名:', product.title);
            console.log('- 商品ID:', product.id);
            console.log('- Shopify商品ID:', product.shopifyId);

            // 商品情報を予約に関連付け
            booking.product = product;
          } else {
            console.error(`商品ID ${booking.productId} が見つかりません。`);
            console.log('注文作成を続行しますが、エラーが発生する可能性があります。');
          }
        } catch (error) {
          console.error('商品情報の取得中にエラーが発生しました:', error);
          console.log('注文作成を続行しますが、エラーが発生する可能性があります。');
        }
      } else {
        console.error('予約に商品IDが関連付けられていません。');
        console.log('注文を作成するには商品情報が必要です。');
        process.exit(1);
      }
    }

    // Shopify Admin APIを初期化
    console.log('Shopify Admin APIを初期化中...');

    // 注文を作成
    console.log('注文作成を開始します...');

    // Shopify Admin APIを初期化
    const admin = await initializeShopifyAdmin();

    const result = await createOrderFromBooking(
      prisma,
      admin,
      bookingId,
      3 // リトライ回数
    );

    console.log('注文作成結果:', result);

    if (result.success) {
      console.log('注文が正常に作成されました:');
      console.log('- 注文ID:', result.orderId);
      console.log('- 注文番号:', result.orderName);
      console.log('- ドラフト注文:', result.isDraftOrder ? 'はい' : 'いいえ');
    } else {
      console.error('注文の作成に失敗しました:', result.error);
    }

    console.log('処理が完了しました');
    process.exit(0);
  } catch (error) {
    console.error('エラーが発生しました:', error);
    process.exit(1);
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
