#!/usr/bin/env node

/**
 * CloudflareのURLをShopify App設定に自動的に更新するスクリプト
 * 使用方法: node update-url.js https://新しいURL.trycloudflare.com
 */

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// 引数からURLを取得
const newUrl = process.argv[2];

if (!newUrl) {
  console.error("エラー: URLが指定されていません");
  console.log(
    "使用方法: node update-url.js https://新しいURL.trycloudflare.com",
  );
  process.exit(1);
}

// URLが有効かチェック
if (!newUrl.startsWith("https://") || !newUrl.includes(".trycloudflare.com")) {
  console.error(
    "エラー: URLが無効です。https://から始まるtrycloudflare.comのURLを指定してください",
  );
  process.exit(1);
}

// .envファイルの更新
const envPath = path.join(__dirname, ".env");
try {
  let envContent = fs.readFileSync(envPath, "utf8");

  // HOST値を更新
  envContent = envContent.replace(
    /HOST=["']?https:\/\/.*\.trycloudflare\.com["']?/,
    `HOST="${newUrl}"`,
  );

  fs.writeFileSync(envPath, envContent);
  console.log("✅ .envファイルのHOST値を更新しました");
} catch (err) {
  console.error(".envファイルの更新に失敗しました:", err);
}

// shopify.app.tomlの更新
const tomlPath = path.join(__dirname, "shopify.app.toml");
try {
  let tomlContent = fs.readFileSync(tomlPath, "utf8");

  // application_urlを更新
  tomlContent = tomlContent.replace(
    /application_url = "https:\/\/.*\.trycloudflare\.com"/,
    `application_url = "${newUrl}"`,
  );

  // redirect_urlsを更新
  const redirectUrlRegex = /(https:\/\/.*?\.trycloudflare\.com\/[^"]*)/g;
  const matches = tomlContent.match(redirectUrlRegex) || [];

  if (matches.length > 0) {
    // 古いドメイン部分を抽出
    const oldDomain = matches[0].match(
      /(https:\/\/.*?\.trycloudflare\.com)/,
    )[1];

    // すべてのリダイレクトURLを更新
    tomlContent = tomlContent.replace(new RegExp(oldDomain, "g"), newUrl);
    console.log(`✅ リダイレクトURLを更新しました (${matches.length}件)`);
  }

  fs.writeFileSync(tomlPath, tomlContent);
  console.log("✅ shopify.app.tomlのURLを更新しました");
} catch (err) {
  console.error("shopify.app.tomlの更新に失敗しました:", err);
}

// Shopify CLIを使ってアプリ設定を更新するかどうか確認
console.log("\n✨ ファイルの更新が完了しました");
console.log("Shopify CLIを使ってアプリ設定も更新しますか？(y/n)");

// stdin入力を待つ関数
function prompt() {
  return new Promise((resolve) => {
    process.stdin.once("data", (data) => {
      resolve(data.toString().trim().toLowerCase());
    });
  });
}

// 非同期処理
(async () => {
  const answer = await prompt();

  if (answer === "y" || answer === "yes") {
    try {
      console.log("Shopify CLIでアプリ設定を更新しています...");
      execSync("shopify app deploy", { stdio: "inherit" });
      console.log("✅ アプリ設定の更新が完了しました");
    } catch (err) {
      console.error(
        "Shopify CLIでの更新に失敗しました。手動で以下のコマンドを実行してください:",
      );
      console.log("  shopify app deploy");
    }
  } else {
    console.log(
      "アプリ設定は更新されていません。必要に応じて手動で以下のコマンドを実行してください:",
    );
    console.log("  shopify app deploy");
  }

  process.exit(0);
})();
