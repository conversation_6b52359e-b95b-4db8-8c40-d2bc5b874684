import { json, type ActionFunctionArgs } from "@remix-run/node";
import { prisma } from "../db.server";
import { authenticate } from "../shopify.server";
import { VariantAutoCreatorService } from "../services/shopify/variant-auto-creator.service";

/**
 * 商品更新Webhookハンドラー
 *
 * Shopifyから商品更新のwebhookを受け取り、必要な処理を行います。
 * - 商品のメタフィールド更新を検知
 * - メンテナンス状態の変更を処理
 * - 在庫カレンダーを更新
 */
export async function action({ request }: ActionFunctionArgs) {
  try {
    // Shopify認証（payloadを含む）
    const { admin, session, payload } = await authenticate.webhook(request);

    console.log("商品更新webhook受信:", payload.id);

    // 商品IDの取得
    const productId = payload.id;
    const shop = session.shop;

    // バリエーション自動作成チェック
    try {
      console.log("バリエーション不足チェックを開始...");

      const variantCreator = VariantAutoCreatorService.getInstance();
      const basePrice = await variantCreator.getProductBasePrice(admin, productId);

      if (basePrice > 0) {
        const variantResult = await variantCreator.createMissingVariants(admin, productId, {
          basePrice,
          createDays: [1, 2, 3, 4, 5, 6, 7], // 1-7日のバリエーション
          createProvisionalVariants: false, // 仮予約バリエーションは作成しない
          productStatus: 'available', // デフォルトでavailable
          location: 'NY', // デフォルト在庫場所
          sku: payload.variants?.[0]?.sku || payload.handle || ''
        });

        if (variantResult.createdVariants.length > 0) {
          console.log(`バリエーション自動作成: ${variantResult.createdVariants.length}個作成`);
        }

        if (variantResult.errors.length > 0) {
          console.error("バリエーション作成エラー:", variantResult.errors);
        }
      }
    } catch (variantError) {
      console.error("バリエーション自動作成エラー:", variantError);
      // バリエーション作成エラーでもWebhook処理は継続
    }

    // メタフィールドの取得
    const metafields = await admin.graphql(`
      query {
        product(id: "gid://shopify/Product/${productId}") {
          metafields(first: 10) {
            edges {
              node {
                namespace
                key
                value
              }
            }
          }
        }
      }
    `);

    const metafieldsData = await metafields.json();
    const metafieldEdges = metafieldsData.data.product.metafields.edges;

    // メンテナンス状態のメタフィールドを検索
    const maintenanceStatusMetafield = metafieldEdges.find(
      (edge: any) => edge.node.namespace === 'rental' && edge.node.key === 'maintenance_status'
    );

    // メンテナンス状態が存在する場合
    if (maintenanceStatusMetafield) {
      const status = maintenanceStatusMetafield.node.value;

      // メンテナンス中または修理中に変更された場合
      if (status === 'メンテナンス中' || status === '修理中') {
        // Neonデータベースにメンテナンスレコードを作成
        const maintenance = await prisma.maintenance.create({
          data: {
            shop,
            shopifyProductId: productId,
            type: status === 'メンテナンス中' ? '定期点検' : '修理',
            startDate: new Date(),
            status: 'in_progress',
            notes: '自動作成: Shopifyのメンテナンス状態変更による'
          }
        });

        // 在庫カレンダーを更新（今日から30日間をメンテナンス中に設定）
        const today = new Date();
        const dates = Array.from({length: 30}, (_, i) => {
          const date = new Date(today);
          date.setDate(today.getDate() + i);
          return date;
        });

        for (const date of dates) {
          await prisma.inventoryCalendar.upsert({
            where: {
              shop_shopifyProductId_date: {
                shop,
                shopifyProductId: productId,
                date
              }
            },
            update: {
              isAvailable: false,
              unavailableReason: status === 'メンテナンス中' ? 'maintenance' : 'repair',
              maintenanceId: maintenance.id
            },
            create: {
              shop,
              shopifyProductId: productId,
              date,
              isAvailable: false,
              unavailableReason: status === 'メンテナンス中' ? 'maintenance' : 'repair',
              maintenanceId: maintenance.id
            }
          });
        }

        console.log(`商品ID ${productId} をメンテナンス状態に更新しました: ${status}`);
      }

      // 良好に変更された場合（メンテナンス完了）
      if (status === '良好') {
        // 進行中のメンテナンスを完了状態に
        const activeMaintenances = await prisma.maintenance.findMany({
          where: {
            shopifyProductId: productId,
            status: 'in_progress'
          }
        });

        for (const maintenance of activeMaintenances) {
          await prisma.maintenance.update({
            where: { id: maintenance.id },
            data: {
              status: 'completed',
              endDate: new Date(),
              completionDate: new Date()
            }
          });
        }

        // 在庫カレンダーを更新（メンテナンス理由の予約不可を解除）
        await prisma.inventoryCalendar.updateMany({
          where: {
            shopifyProductId: productId,
            unavailableReason: { in: ['maintenance', 'repair'] }
          },
          data: {
            isAvailable: true,
            unavailableReason: null,
            maintenanceId: null
          }
        });

        console.log(`商品ID ${productId} のメンテナンスを完了しました`);
      }
    }

    return json({ success: true });
  } catch (error) {
    console.error("商品更新webhookエラー:", error);
    return json({ error: error.message }, { status: 500 });
  }
}
