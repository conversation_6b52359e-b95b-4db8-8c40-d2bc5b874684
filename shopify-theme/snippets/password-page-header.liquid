{%- if settings.logo_types == 'svg' and settings.logo_mobile_svg_file != blank -%}
    {%- assign need_mobile_logo = true -%}
{%- elsif settings.logo_types == 'image' and settings.logo_mobile_image != blank -%}
    {%- assign need_mobile_logo = true -%}
{%- endif -%}
<header id="header" class="header position-relative d-flex align-items-center p-20">
    {%- if need_mobile_logo -%}
        <div class="header__logo d-flex d-lg-none align-items-center" style="width: {{ settings.logo_mobile_width }}px;">
            {% include 'logo' svg: settings.logo_mobile_svg_file image: settings.logo_mobile_image width: settings.logo_mobile_width %}
        </div>
    {%- endif -%}
    <div class="header__logo align-items-center {% if need_mobile_logo %}d-none d-lg-flex{% else %}d-flex{% endif %}" style="width: {{ settings.logo_width }}px;">
        {% include 'logo' svg: settings.logo_svg_file image: settings.logo_image width: settings.logo_width tag: 'h1' %}
    </div>
    <div class="position-lg-relative py-5 ml-auto js-dropdown">
        <div class="header__btn-password cursor-pointer js-popup-button" data-js-dropdown-button data-js-popup-button="password">
            <i>{% render 'icon-theme-157' %}</i>
        </div>
        <div class="header__password header__dropdown dropdown position-lg-absolute d-none top-lg-100 right-lg-0 mt-0" data-js-dropdown>
            <div class="w-100 px-15 pb-30 p-lg-20" data-js-position-desktop="password">
                <div class="js-position" data-js-position-name="password">
                    {% form 'storefront_password', class: 'mb-0' %}
                    <label for="PopupAccountSubscribe" class="label-required">{{ 'layout.password_page_header.dropdown_label' | t }}</label>
                    <input type="password"
                           class="mb-10"
                           name="password"
                           id="Password"
                           placeholder="{{ 'layout.password_page_header.dropdown_password_placeholder' | t }}"
                           required="required">
                    <input type="submit" name="commit" class="btn btn--full mb-0" value="{{ 'layout.password_page_header.dropdown_button_text' | t }}">
                    {% include 'form-get-message' %}
                    {% endform %}
                </div>
            </div>
        </div>
    </div>
</header>