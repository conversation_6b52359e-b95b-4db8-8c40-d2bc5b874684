/**
 * テスト用商品のShopify商品ページURLを取得するスクリプト
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

const prisma = new PrismaClient();

async function main() {
  try {
    const shop = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';
    
    console.log('🔍 テスト用商品を検索中...');
    
    // テスト用商品を検索
    const testProducts = await prisma.product.findMany({
      where: {
        shop,
        OR: [
          { title: { contains: 'テスト' } },
          { title: { contains: 'Test' } },
          { title: { contains: 'test' } },
          { sku: { contains: 'TEST' } },
          { sku: { contains: 'test' } }
        ]
      },
      select: {
        id: true,
        shopifyId: true,
        title: true,
        sku: true,
        handle: true,
        status: true
      },
      take: 5
    });

    if (testProducts.length === 0) {
      console.log('❌ テスト用商品が見つかりません');
      
      // 利用可能な商品を表示
      const availableProducts = await prisma.product.findMany({
        where: { shop },
        select: {
          id: true,
          shopifyId: true,
          title: true,
          sku: true,
          handle: true,
          status: true
        },
        take: 5
      });

      if (availableProducts.length > 0) {
        console.log('\n📦 利用可能な商品:');
        availableProducts.forEach((product, index) => {
          const productUrl = `https://${shop}/products/${product.handle}`;
          console.log(`${index + 1}. ${product.title} (${product.sku})`);
          console.log(`   URL: ${productUrl}`);
          console.log(`   Status: ${product.status}`);
          console.log('');
        });
      }
      return;
    }

    console.log('✅ テスト用商品が見つかりました:\n');

    testProducts.forEach((product, index) => {
      const productUrl = `https://${shop}/products/${product.handle}`;
      console.log(`${index + 1}. ${product.title} (${product.sku})`);
      console.log(`   URL: ${productUrl}`);
      console.log(`   Status: ${product.status}`);
      console.log(`   Shopify ID: ${product.shopifyId}`);
      console.log('');
    });

    // 最初のテスト商品のURLを強調表示
    if (testProducts.length > 0) {
      const firstProduct = testProducts[0];
      const productUrl = `https://${shop}/products/${firstProduct.handle}`;
      
      console.log('🎯 推奨テスト商品:');
      console.log(`   商品名: ${firstProduct.title}`);
      console.log(`   URL: ${productUrl}`);
      console.log('');
      console.log('📋 テスト手順:');
      console.log('1. 上記URLにアクセス');
      console.log('2. 日付選択機能の動作確認');
      console.log('3. カート追加機能の確認');
      console.log('4. チェックアウトから注文作成');
      console.log('5. Prismaデータベースでの同期確認');
    }

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => console.log('\n✅ スクリプト実行完了'))
  .catch((error) => console.error('❌ 予期しないエラー:', error));
