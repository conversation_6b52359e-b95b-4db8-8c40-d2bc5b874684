{% include 'global-variables' %}
{%- assign template_layout = settings.product_info_layout -%}
{%- if product.tags contains 'gallery-layout-1' -%}
    {%- assign template_layout = '1' -%}
{%- elsif product.tags contains 'gallery-layout-2' -%}
    {%- assign template_layout = '2' -%}
{%- elsif product.tags contains 'gallery-layout-3' -%}
    {%- assign template_layout = '3' -%}
{%- elsif product.tags contains 'gallery-layout-4' -%}
    {%- assign template_layout = '4' -%}
{%- elsif product.tags contains 'gallery-layout-5' -%}
    {%- assign template_layout = '5' -%}
{%- endif -%}
{%- assign sidebar = section.settings.sidebar -%}
{%- if template_layout == '3' or template_layout == '4' -%}
	{%- assign enable_sticky_sidebar = true -%}
{%- else -%}
	{%- assign enable_sticky_sidebar = section.settings.enable_sticky_sidebar -%}
{%- endif -%}
{% capture container_class %}{% if sidebar != 'off' %} product-page__container--with-sidebar{% endif %}{% if sidebar == 'left' %} order-xl-1{% endif %}{% endcapture %}
{% capture sidebar_class %}{% if sidebar == 'left' %} product-page__sidebar--left pr-xl-30 mr-xl-30{% elsif sidebar == 'right' %} product-page__sidebar--right pl-xl-30 ml-xl-30{% endif %}{% endcapture %}
{% capture sidebar_html %}
	{%- if sidebar != 'off' -%}
		<div class="product-page__sidebar{{ sidebar_class }}{% if enable_sticky_sidebar %} sticky-sidebar-xl js-sticky-sidebar{% endif %}" data-js-sticky-sidebar-media-xl>
			{%- if enable_sticky_sidebar -%}
				<div data-js-sticky-sidebar-body>
			{%- endif -%}
				<div data-js-position-desktop="sidebar">
					{% include 'product-page-get-sidebar' %}
				</div>
			{%- if enable_sticky_sidebar -%}
				</div>
			{%- endif -%}
			</div>
	{%- endif -%}
{% endcapture %}
{% include 'product-res-variables' %}
{% capture content_margins %}pt-{{ offset_product_page_content_margin_top_d }} pb-{{ offset_product_page_content_margin_bottom_d }}{% endcapture %}
<div class="product-page {{ content_margins }}">
	<div class="container{% if sidebar != 'off' %} d-xl-flex{% endif %}">
		{%- if template_layout == '1' or template_layout == '5' -%}
			<div class="product-page__container{{ container_class }}">
				<div class="product-page__main">
					{% include 'product-page-get-main' %}
				</div>
				{%- if section.settings.show_tabs -%}
					<div class="product-page__tabs mt-30">
						{% include 'product-page-get-tabs' %}
					</div>
				{%- endif -%}
			</div>
			{{ sidebar_html }}
		{%- else -%}
			<div class="product-page__container{{ container_class }}">
				<div class="product-page__main">
					{% include 'product-page-get-main' %}
				</div>
			</div>
			{{ sidebar_html }}
		{%- endif -%}
	</div>
</div>
{%- if enable_sticky_sidebar -%}
	<script>
		theme.AssetsLoader.require('scripts', 'sticky_sidebar');
	</script>
{%- endif -%}
{%- if section.settings.tabs_type == 'tabs' -%}
	<script>
		theme.AssetsLoader.onUserAction(function() {
			theme.AssetsLoader.require('scripts', 'tabs');
		});
	</script>
{%- endif -%}

<script type="application/ld+json">
  {{ product | structured_data }}
</script>

{% comment %}
<script type="application/ld+json">
{
"@context": "http://schema.org/",
"@type": "Product",
"name": {{ product.title | json }},
"url": {{ shop.url | append: product.url | json }},
{%- if product.featured_media -%}
	{%- assign media_size = product.featured_media.preview_image.width | append: 'x' -%}
	"image": [
	{{ product.featured_media | img_url: media_size | prepend: "https:" | json }}
	],
{%- endif -%}
"description": {{ product.description | strip_html | json }},
{%- if current_variant.sku != blank -%}
	"sku": {{ current_variant.sku | json }},
{%- endif -%}
"brand": {
	"@type": "Brand",
	"name": {{ product.vendor | json }}
},
"offers": [
	{%- for variant in product.variants -%}
		{%- if prices -%}
			{%- assign prices = prices -%}
			{%- if prices contains variant.price -%}
				{%- continue -%}
			{%- endif -%}
		,
		{%- endif -%}
		{
			"@type" : "Offer",
			{%- if variant.sku != blank -%}
			"sku": {{ variant.sku | json }},
			{%- endif -%}
			"availability" : "http://schema.org/{% if product.available %}InStock{% else %}OutOfStock{% endif %}",
			"price" : {{ variant.price | divided_by: 100.00 | json }},
			"priceCurrency" : {{ cart.currency.iso_code | json }},
			"url" : {{ shop.url | append: variant.url | json }}
		}
		{% capture prices %}{{ prices }}{% if prices %}|{% endif %}{{ variant.price }}{% endcapture %}
	{%- endfor -%}
]
}
</script>
{% endcomment %}

<script>
  document.addEventListener('DOMContentLoaded', function() {
    try {
      // Get current product information
      const currentProduct = {
        id: {{ product.id | json }},
        title: {{ product.title | json }},
        url: {{ product.url | json }},
        image: {{ product.featured_image | img_url: '600x' | json }},
        variants: [
          {%- for variant in product.variants -%}
            {
              "id": {{ variant.id | json }},
              "price": {{ variant.price | money | json }}
            }{% unless forloop.last %},{% endunless %}
          {%- endfor -%}
        ]
      };

      // Get existing products from localStorage
      let viewedProducts = JSON.parse(localStorage.getItem('recentlyViewedProducts')) || [];

      // Remove if same product already exists
      viewedProducts = viewedProducts.filter(product => product.id !== currentProduct.id);

      // Add new product to beginning
      viewedProducts.unshift(currentProduct);

      // Keep maximum 20 items
      if (viewedProducts.length > 20) {
        viewedProducts = viewedProducts.slice(0, 20);
      }

      // Save to localStorage
      localStorage.setItem('recentlyViewedProducts', JSON.stringify(viewedProducts));

      // For debugging
      console.log('Added product to history:', currentProduct.title);
      console.log('Current history:', viewedProducts);
    } catch (e) {
      console.error('Failed to save product history:', e);
    }
  });
</script>

{%- for block in section.blocks -%}
    {%- if block.type == 'rental_form' -%}
        <script src="{{ 'product-rental.js' | asset_url }}" defer></script>
    {%- endif -%}
    {%- if block.type == 'rental_calendar' -%}
        {% render 'product-rental-calendar', product: product %}
    {%- endif -%}
{%- endfor -%}

{% schema %}
{
    "name": "t:sections.product_page.name",
    "settings": [
        {
            "type": "header",
            "content": "t:sections.product_page.settings.header__1.content"
        },
        {
            "type": "select",
            "id": "gallery_size",
            "label": "t:sections.product_page.settings.gallery_size.label",
            "default": "6",
            "options": [
                {
                    "value": "4",
                    "label": "t:sections.product_page.settings.gallery_size.option__1.label"
                },
                {
                    "value": "5",
                    "label": "t:sections.product_page.settings.gallery_size.option__2.label"
                },
                {
                    "value": "6",
                    "label": "t:sections.product_page.settings.gallery_size.option__3.label"
                },
                {
                    "value": "7",
                    "label": "t:sections.product_page.settings.gallery_size.option__4.label"
                },
                {
                    "value": "8",
                    "label": "t:sections.product_page.settings.gallery_size.option__5.label"
                }
            ]
        },
        {
            "type": "select",
            "id": "sidebar",
            "label": "t:sections.product_page.settings.sidebar.label",
            "info": "t:sections.product_page.settings.sidebar.info",
            "default": "off",
            "options": [
                {
                    "value": "off",
                    "label": "t:sections.product_page.settings.sidebar.option__1.label"
                },
                {
                    "value": "left",
                    "label": "t:sections.product_page.settings.sidebar.option__2.label"
                },
                {
                    "value": "right",
                    "label": "t:sections.product_page.settings.sidebar.option__3.label"
                }
            ]
        },
        {
            "type": "checkbox",
            "id": "enable_sticky_sidebar",
            "label": "t:sections.product_page.settings.enable_sticky_sidebar.label",
            "default": true
        },
        {
            "type": "header",
            "content": "t:sections.product_page.settings.header__2.content"
        },
        {
            "type": "checkbox",
            "id": "show_tabs",
            "label": "t:sections.product_page.settings.show_tabs.label",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_tab_description",
            "label": "t:sections.product_page.settings.show_tab_description.label",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "scrolling_to_opened_tab",
            "label": "t:sections.product_page.settings.scrolling_to_opened_tab.label",
            "info": "t:sections.product_page.settings.scrolling_to_opened_tab.info",
            "default": false
        },
        {
            "type": "select",
            "id": "tabs_type",
            "label": "t:sections.product_page.settings.tabs_type.label",
            "default": "tabs",
            "info": "t:sections.product_page.settings.tabs_type.info",
            "options": [
                {
                    "value": "tabs",
                    "label": "t:sections.product_page.settings.tabs_type.option__1.label"
                },
                {
                    "value": "sheet",
                    "label": "t:sections.product_page.settings.tabs_type.option__2.label"
                }
            ]
        },
        {
            "type": "paragraph",
            "content": "t:sections.product_page.settings.paragraph__1.content"
        },
        {
            "type": "header",
            "content": "t:sections.product_page.settings.header__3.content"
        },
        {
            "type": "paragraph",
            "content": "t:sections.product_page.settings.paragraph__2.content"
        }
    ],
    "blocks": [
        {
            "type": "collections",
            "name": "t:sections.product_page.blocks.collections.name",
            "limit": 1,
            "settings": []
        },
        {
            "type": "title",
            "name": "t:sections.product_page.blocks.title.name",
            "limit": 1,
            "settings": []
        },
        {
            "type": "details",
            "name": "t:sections.product_page.blocks.details.name",
            "limit": 1,
            "settings": [
                {
                    "type": "paragraph",
                    "content": "t:sections.product_page.blocks.details.settings.paragraph__1.content"
                }
            ]
        },
        {
            "type": "price",
            "name": "t:sections.product_page.blocks.price.name",
            "limit": 1,
            "settings": [
                {
                    "type": "paragraph",
                    "content": "t:sections.product_page.blocks.price.settings.paragraph__1.content"
                }
            ]
        },
        {
            "type": "icon_with_text",
            "name": "t:sections.product_page.blocks.icon_with_text.name",
            "settings": [
                {
                    "type": "select",
                    "id": "style",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.style.label",
                    "default": "inline",
                    "options": [
                        {
                            "value": "inline",
                            "label": "t:sections.product_page.blocks.icon_with_text.settings.style.option__1.label"
                        },
                        {
                            "value": "tile",
                            "label": "t:sections.product_page.blocks.icon_with_text.settings.style.option__2.label"
                        }
                    ]
                },
                {
                    "type": "header",
                    "content": "t:sections.product_page.blocks.icon_with_text.settings.header__1.content"
                },
                {
                    "type": "text",
                    "id": "icon_svg_1",
                    "default": "theme-127",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.icon_svg_1.label"
                },
                {
                    "type": "image_picker",
                    "id": "icon_image_1",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.icon_image_1.label"
                },
                {
                    "type": "richtext",
                    "id": "text_1",
                    "default": "<p>安心の品質保証:チェックアウトにお進みください</p>", //"<p>Warrenty. No code needed, just head for checkout!</p>"
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.text_1.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.product_page.blocks.icon_with_text.settings.header__2.content"
                },
                {
                    "type": "text",
                    "id": "icon_svg_2",
                    "default": "theme-142",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.icon_svg_2.label"
                },
                {
                    "type": "image_picker",
                    "id": "icon_image_2",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.icon_image_2.label"
                },
                {
                    "type": "richtext",
                    "id": "text_2",
                    "default": "<p>無料配送 : 営業日の翌日配送!</p>" , // "<p>Free shipping. All orders are dispatched the next business day!</p>",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.text_2.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.product_page.blocks.icon_with_text.settings.header__3.content"
                },
                {
                    "type": "text",
                    "id": "icon_svg_3",
                    "default": "theme-144",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.icon_svg_3.label"
                },
                {
                    "type": "image_picker",
                    "id": "icon_image_3",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.icon_image_3.label"
                },
                {
                    "type": "richtext",
                    "id": "text_3",
                    "default": "<p>安心の価格 : 14日間の返品保証</p>" , // "<p>We will beat any price. We back all products with a 14 days guarantee.</p>",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.text_3.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.product_page.blocks.icon_with_text.settings.header__4.content"
                },
                {
                    "type": "text",
                    "id": "icon_svg_4",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.icon_svg_4.label"
                },
                {
                    "type": "image_picker",
                    "id": "icon_image_4",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.icon_image_4.label"
                },
                {
                    "type": "richtext",
                    "id": "text_4",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.text_4.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.product_page.blocks.icon_with_text.settings.header__5.content"
                },
                {
                    "type": "text",
                    "id": "icon_svg_5",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.icon_svg_5.label"
                },
                {
                    "type": "image_picker",
                    "id": "icon_image_5",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.icon_image_5.label"
                },
                {
                    "type": "richtext",
                    "id": "text_5",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.text_5.label"
                },
                {
                    "type": "header",
                    "content": "t:sections.product_page.blocks.icon_with_text.settings.header__6.content"
                },
                {
                    "type": "text",
                    "id": "icon_svg_6",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.icon_svg_6.label"
                },
                {
                    "type": "image_picker",
                    "id": "icon_image_6",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.icon_image_6.label"
                },
                {
                    "type": "richtext",
                    "id": "text_6",
                    "label": "t:sections.product_page.blocks.icon_with_text.settings.text_6.label"
                }
            ]
        },
        {
            "type": "description",
            "name": "t:sections.product_page.blocks.description.name",
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "t:sections.product_page.blocks.description.settings.title.label"
                },
                {
                    "type": "richtext",
                    "id": "content",
                    "label": "t:sections.product_page.blocks.description.settings.content.label",
                    "info": "t:sections.product_page.blocks.description.settings.content.info"
                }
            ]
        },
        {
            "type": "text",
            "name": "t:sections.product_page.blocks.text.name",
            "settings": [
                {
                    "type": "richtext",
                    "id": "content",
                    "label": "t:sections.product_page.blocks.text.settings.content.label"
                }
            ]
        },
        {
            "type": "countdown",
            "name": "t:sections.product_page.blocks.countdown.name",
            "limit": 1,
            "settings": []
        },
        {
            "type": "stock_countdown",
            "name": "t:sections.product_page.blocks.stock_countdown.name",
            "limit": 1,
            "settings": [
                {
                    "type": "paragraph",
                    "content": "t:sections.product_page.blocks.stock_countdown.settings.paragraph__1.content"
                }
            ]
        },
        {
            "type": "delivery_countdown",
            "name": "t:sections.product_page.blocks.delivery_countdown.name",
            "limit": 1,
            "settings": [
                {
                    "type": "paragraph",
                    "content": "t:sections.product_page.blocks.delivery_countdown.settings.paragraph__1.content"
                }
            ]
        },
        {
            "type": "border",
            "name": "t:sections.product_page.blocks.border.name",
            "settings": []
        },
        {
            "type": "options",
            "name": "t:sections.product_page.blocks.options.name",
            "limit": 1,
            "settings": [
                {
                    "type": "paragraph",
                    "content": "t:sections.product_page.blocks.options.settings.paragraph__1.content"
                }
            ]
        },
        {
            "type": "popups",
            "name": "t:sections.product_page.blocks.popups.name",
            "limit": 1,
            "settings": [
                {
                    "type": "paragraph",
                    "content": "t:sections.product_page.blocks.popups.settings.paragraph__1.content"
                }
            ]
        },
        {
            "type": "notes",
            "name": "t:sections.product_page.blocks.notes.name",
            "limit": 1,
            "settings": []
        },
        {
            "type": "buttons_quantity",
            "name": "t:sections.product_page.blocks.buttons_quantity.name",
            "limit": 1,
            "settings": [
                {
                    "type": "paragraph",
                    "content": "t:sections.product_page.blocks.buttons_quantity.settings.paragraph__1.content"
                }
            ]
        },
        {
            "type": "free_shipping",
            "name": "t:sections.product_page.blocks.free_shipping.name",
            "limit": 1,
            "settings": [
                {
                    "type": "paragraph",
                    "content": "t:sections.product_page.blocks.free_shipping.settings.paragraph__1.content"
                }
            ]
        },
        {
            "type": "pickup_availability",
            "name": "t:sections.product_page.blocks.pickup_availability.name",
            "limit": 1,
            "settings": [
                {
                    "type": "paragraph",
                    "content": "t:sections.product_page.blocks.pickup_availability.settings.paragraph__1.content"
                }
            ]
        },
        {
            "type": "complementary_products",
            "name": "t:sections.product_page.blocks.complementary_products.name",
            "limit": 1,
            "settings": [
                {
                    "type": "paragraph",
                    "content": "t:sections.product_page.blocks.complementary_products.settings.paragraph__1.content"
                }
            ]
        },
        {
            "type": "payments",
            "name": "t:sections.product_page.blocks.payments.name",
            "limit": 1,
            "settings": []
        },
        {
            "type": "social_share_buttons",
            "name": "t:sections.product_page.blocks.social_share_buttons.name",
            "limit": 1,
            "settings": [
                {
                    "type": "paragraph",
                    "content": "t:sections.product_page.blocks.social_share_buttons.settings.paragraph__1.content"
                }
            ]
        },
        {
            "type": "tab_custom",
            "name": "t:sections.product_page.blocks.tab_custom.name",
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "t:sections.product_page.blocks.tab_custom.settings.title.label",
                    "default": "カスタムタブ" // "Custom tab"
                },
                {
                    "type": "richtext",
                    "id": "content",
                    "label": "t:sections.product_page.blocks.tab_custom.settings.content.label",
                    "default": "<p>テキストコンテンツをこちらに入力してください</p>" // "<p>Text content of Tab goes here!</p><p>You can make&nbsp;text <strong>bold</strong>, <em>italic</em> or add <a href=\"/blogs/news\" title=\"News\">link</a> using rich text editor.</p><p></p>"
                },
                {
                    "type": "paragraph",
                    "content": "t:sections.product_page.blocks.tab_custom.settings.paragraph__1.content"
                },
                {
                    "type": "page",
                    "id": "page_content",
                    "label": "t:sections.product_page.blocks.tab_custom.settings.page_content.label",
                    "info": "t:sections.product_page.blocks.tab_custom.settings.page_content.info"
                }
            ]
        },
        {
            "type": "tab_custom_liquid",
            "name": "t:sections.product_page.blocks.tab_custom_liquid.name",
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "t:sections.product_page.blocks.tab_custom_liquid.settings.title.label",
                    "default": "カスタムLiquid"// "Custom Liquid"
                },
                {
                    "type": "liquid",
                    "id": "custom_liquid",
                    "label": "t:sections.product_page.blocks.tab_custom_liquid.settings.custom_liquid.label"
                }
            ]
        },
        {
            "type": "sidebar_custom_html",
            "name": "t:sections.product_page.blocks.sidebar_custom_html.name",
            "settings": [
                {
                    "type": "page",
                    "id": "page_content",
                    "label": "t:sections.product_page.blocks.sidebar_custom_html.settings.page_content.label",
                    "info": "t:sections.product_page.blocks.sidebar_custom_html.settings.page_content.info"
                }
            ]
        },
        {
            "type": "sidebar_products",
            "name": "t:sections.product_page.blocks.sidebar_products.name",
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "t:sections.product_page.blocks.sidebar_products.settings.title.label",
                    "default": "特集商品" // "FEATURED PRODUCTS"
                },
                {
                    "type": "collection",
                    "id": "collection",
                    "label": "t:sections.product_page.blocks.sidebar_products.settings.collection.label"
                },
                {
                    "type": "range",
                    "id": "max_count",
                    "min": 0,
                    "max": 10,
                    "step": 1,
                    "unit": { "en": "pds", "ja": "商品"},
                    "label": "t:sections.product_page.blocks.sidebar_products.settings.max_count.label",
                    "info": "t:sections.product_page.blocks.sidebar_products.settings.max_count.info",
                    "default": 3
                },
                {
                    "type": "select",
                    "id": "product_type",
                    "label": "t:sections.product_page.blocks.sidebar_products.settings.product_type.label",
                    "default": "short",
                    "options": [
                        {
                            "value": "short",
                            "label": "t:sections.product_page.blocks.sidebar_products.settings.product_type.option__1.label"
                        },
                        {
                            "value": "full",
                            "label": "t:sections.product_page.blocks.sidebar_products.settings.product_type.option__2.label"
                        }
                    ]
                }
            ]
        },
        {
            "type": "custom_liquid",
            "name": "t:sections.product_page.blocks.custom_liquid.name",
            "settings": [
                {
                    "type": "liquid",
                    "id": "custom_liquid",
                    "label": "t:sections.product_page.blocks.custom_liquid.settings.custom_liquid.label",
                    "info": "t:sections.product_page.blocks.custom_liquid.settings.custom_liquid.info"
                }
            ]
        },
        {
            "type": "rental_form",
            "name": "レンタル予約フォーム",
            "limit": 1,
            "settings": [
                {
                    "type": "paragraph",
                    "content": "商品のレンタル予約フォームを表示します。"
                }
            ]
        },
        {
            "type": "rental_calendar",
            "name": "レンタルカレンダー",
            "limit": 1,
            "settings": [
                {
                    "type": "paragraph",
                    "content": "商品のレンタル予約カレンダーを表示します。予約状況を確認しながら日付を選択できます。"
                }
            ]
        },
        {
            "type": "@app"
        }
    ],
    "default": {
        "blocks": [
            {
                "type": "collections"
            },
            {
                "type": "title"
            },
            {
                "type": "details"
            },
            {
                "type": "price"
            },
            {
                "type": "icon_with_text"
            },
            {
                "type": "description"
            },
            {
                "type": "countdown"
            },
            {
                "type": "stock_countdown"
            },
            {
                "type": "delivery_countdown"
            },
            {
                "type": "border"
            },
            {
                "type": "options"
            },
            {
                "type": "popups"
            },
            {
                "type": "notes"
            },
            {
                "type": "buttons_quantity"
            },
            {
                "type": "free_shipping"
            },
            {
                "type": "pickup_availability"
            },
            {
                "type": "complementary_products"
            },
            {
                "type": "payments"
            },
            {
                "type": "social_share_buttons"
            }
        ]
    }
}
{% endschema %}
