/**
 * Shopify商品一覧を確認するスクリプト
 */

import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

dotenv.config();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品一覧を取得するクエリ
const GET_PRODUCTS = gql`
  query getProducts($first: Int!) {
    products(first: $first) {
      edges {
        node {
          id
          title
          status
          handle
          variants(first: 5) {
            edges {
              node {
                id
                title
                sku
                inventoryQuantity
              }
            }
          }
        }
      }
    }
  }
`;

async function main() {
  try {
    console.log('🔍 Shopify商品一覧を取得中...');

    const result = await shopifyClient.request(GET_PRODUCTS, {
      first: 20
    });

    const products = result.products.edges.map(edge => edge.node);
    console.log(`📦 ${products.length}件の商品を取得しました\n`);

    products.forEach((product, index) => {
      const shopifyId = product.id.replace('gid://shopify/Product/', '');
      console.log(`${index + 1}. ${product.title}`);
      console.log(`   ID: ${shopifyId}`);
      console.log(`   Handle: ${product.handle}`);
      console.log(`   Status: ${product.status}`);
      
      if (product.variants.edges.length > 0) {
        console.log(`   Variants:`);
        product.variants.edges.forEach((variant, vIndex) => {
          console.log(`     ${vIndex + 1}. ${variant.node.title} (SKU: ${variant.node.sku})`);
        });
      }
      console.log('');
    });

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  }
}

main();
