/**
 * Webhookテスト用の商品作成スクリプト
 *
 * このスクリプトは、Webhookテスト用の商品をShopifyストアに作成します。
 * 商品には複数のバリエーション（レンタル日数）が含まれます。
 * Webhookが正しく動作し、メタフィールドが自動的に設定されるかテストします。
 */

import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品作成のGraphQLクエリ
const CREATE_PRODUCT = gql`
  mutation productCreate($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// バリアント作成のGraphQLクエリ
const CREATE_VARIANT = gql`
  mutation productVariantCreate($input: ProductVariantInput!) {
    productVariantCreate(input: $input) {
      productVariant {
        id
        title
        price
        sku
        inventoryItem {
          id
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// バリアント更新のGraphQLクエリ
const UPDATE_VARIANT = gql`
  mutation productUpdate($productId: ID!, $variantId: ID!, $price: Money!, $title: String!, $sku: String!) {
    productUpdate(input: {
      id: $productId,
      variants: {
        id: $variantId,
        price: $price,
        title: $title,
        sku: $sku
      }
    }) {
      product {
        id
        variants(first: 1) {
          edges {
            node {
              id
              title
              price
              sku
              inventoryItem {
                id
              }
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 商品の詳細を取得するGraphQLクエリ
const GET_PRODUCT = gql`
  query getProduct($id: ID!) {
    product(id: $id) {
      id
      title
      variants(first: 10) {
        edges {
          node {
            id
            title
            sku
            price
            inventoryItem {
              id
            }
          }
        }
      }
    }
  }
`;

// 在庫設定のGraphQLクエリ
const SET_INVENTORY = gql`
  mutation inventoryActivate($inventoryItemId: ID!, $locationId: ID!, $available: Int!) {
    inventoryActivate(inventoryItemId: $inventoryItemId, locationId: $locationId, available: $available) {
      inventoryLevel {
        id
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// テスト用商品データ
const TEST_PRODUCT = {
  title: "Webhookテスト用レンタル商品",
  description: "これはWebhookテスト用のレンタル商品です。自動的にメタフィールドが設定されるかテストします。",
  vendor: "テスト",
  productType: "レンタル商品",
  tags: ["テスト", "レンタル", "Webhook"],
  basePrice: 5000,
  sku: "TEST-WH-001",
  inventory: 1,
  variants: [
    {
      title: "1日レンタル",
      price: 5000,
      sku: "TEST-WH-001-1day"
    },
    {
      title: "2日レンタル",
      price: 6000,
      sku: "TEST-WH-001-2day"
    },
    {
      title: "3日レンタル",
      price: 7000,
      sku: "TEST-WH-001-3day"
    },
    {
      title: "4日レンタル",
      price: 8000,
      sku: "TEST-WH-001-4day"
    },
    {
      title: "5日レンタル",
      price: 9000,
      sku: "TEST-WH-001-5day"
    },
    {
      title: "6日レンタル",
      price: 10000,
      sku: "TEST-WH-001-6day"
    },
    {
      title: "7日レンタル",
      price: 11000,
      sku: "TEST-WH-001-7day"
    },
    {
      title: "8日以上レンタル",
      price: 11500, // 基本料金5000円 + (5000円×0.2×6日) + (5000円×0.1×1日) = 5000 + 6000 + 500 = 11500円
      sku: "TEST-WH-001-8plus"
    }
  ]
};

// 商品を作成する関数
async function createTestProduct() {
  try {
    console.log(`商品を作成中: ${TEST_PRODUCT.title}`);

    // 商品を作成（オプションと最初のバリアントを含む）
    const productResult = await client.request(CREATE_PRODUCT, {
      input: {
        title: TEST_PRODUCT.title,
        descriptionHtml: TEST_PRODUCT.description,
        vendor: TEST_PRODUCT.vendor,
        productType: TEST_PRODUCT.productType,
        tags: TEST_PRODUCT.tags,
        status: 'ACTIVE',
        options: ["レンタル日数"],
        variants: [
          {
            price: TEST_PRODUCT.variants[0].price.toString(),
            sku: TEST_PRODUCT.variants[0].sku,
            options: [TEST_PRODUCT.variants[0].title],
            inventoryManagement: 'SHOPIFY'
          }
        ]
      }
    });

    if (productResult.productCreate.userErrors.length > 0) {
      console.error(`商品作成エラー: ${JSON.stringify(productResult.productCreate.userErrors)}`);
      return null;
    }

    const productId = productResult.productCreate.product.id;
    console.log(`商品を作成しました: ${TEST_PRODUCT.title} (ID: ${productId})`);

    // 商品の詳細を取得して、デフォルトバリアントのIDを取得
    const productDetails = await client.request(GET_PRODUCT, {
      id: productId
    });

    const defaultVariant = productDetails.product.variants.edges[0].node;
    console.log(`デフォルトバリアント: ${defaultVariant.title} (ID: ${defaultVariant.id})`);

    // 最初のバリアントを更新
    await client.request(UPDATE_VARIANT, {
      productId: productId,
      variantId: defaultVariant.id,
      price: TEST_PRODUCT.variants[0].price.toString(),
      title: TEST_PRODUCT.variants[0].title,
      sku: TEST_PRODUCT.variants[0].sku
    });

    console.log(`バリアントを更新しました: ${TEST_PRODUCT.variants[0].title}`);

    // 在庫設定はスキップ（権限の問題があるため）
    console.log(`在庫設定はスキップします: ${TEST_PRODUCT.variants[0].title}`);

    /*
    // 在庫を設定
    if (defaultVariant.inventoryItem?.id) {
      // ロケーションIDを取得（実際の環境に合わせて調整が必要）
      const locationId = "gid://shopify/Location/76366348584"; // 実際のロケーションIDに置き換える

      await client.request(SET_INVENTORY, {
        inventoryItemId: defaultVariant.inventoryItem.id,
        locationId: locationId,
        available: TEST_PRODUCT.inventory
      });

      console.log(`在庫を設定しました: ${TEST_PRODUCT.inventory}個`);
    }
    */

    // 残りのバリアントを作成
    const variants = [defaultVariant];

    for (let i = 1; i < TEST_PRODUCT.variants.length; i++) {
      const variant = TEST_PRODUCT.variants[i];
      console.log(`バリアントを作成中: ${variant.title}`);

      const variantResult = await client.request(CREATE_VARIANT, {
        input: {
          productId: productId,
          price: variant.price.toString(),
          options: [variant.title],
          sku: variant.sku,
          inventoryManagement: 'SHOPIFY'
        }
      });

      if (variantResult.productVariantCreate.userErrors.length > 0) {
        console.error(`バリアント作成エラー: ${JSON.stringify(variantResult.productVariantCreate.userErrors)}`);
        continue;
      }

      const variantId = variantResult.productVariantCreate.productVariant.id;
      const inventoryItemId = variantResult.productVariantCreate.productVariant.inventoryItem?.id;

      console.log(`バリアントを作成しました: ${variant.title} (ID: ${variantId})`);

      variants.push({
        id: variantId,
        title: variant.title,
        sku: variant.sku,
        inventoryItemId
      });

      // 在庫設定はスキップ（権限の問題があるため）
      console.log(`在庫設定はスキップします: ${variant.title}`);

      /*
      // 在庫を設定
      if (inventoryItemId) {
        const inventoryResult = await client.request(SET_INVENTORY, {
          inventoryItemId: inventoryItemId,
          locationId: "gid://shopify/Location/76366348584",
          available: TEST_PRODUCT.inventory
        });

        if (inventoryResult.inventoryActivate.userErrors.length > 0) {
          console.error(`在庫設定エラー: ${JSON.stringify(inventoryResult.inventoryActivate.userErrors)}`);
        } else {
          console.log(`在庫を設定しました: ${TEST_PRODUCT.inventory}個`);
        }
      }
      */

      // API制限を回避するための短い待機
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('\n作成完了:');
    console.log(`商品: ${TEST_PRODUCT.title}`);
    console.log(`バリアント: ${variants.length}個`);

    console.log('\nWebhookが正しく動作し、メタフィールドが自動的に設定されるか確認してください。');
    console.log('Shopify管理画面で商品を確認: https://admin.shopify.com/store/peaces-test-block/products');

    return {
      id: productId,
      title: TEST_PRODUCT.title,
      variants
    };
  } catch (error) {
    console.error(`商品作成中にエラーが発生しました: ${error.message}`);
    console.error(error);
    return null;
  }
}

// スクリプト実行
createTestProduct().catch(error => {
  console.error('スクリプト実行中にエラーが発生しました:', error);
  process.exit(1);
});
