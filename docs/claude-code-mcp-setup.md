# Claude Code MCPサーバー設定ガイド

**作成日**: 2025年5月25日  
**目的**: <PERSON> CodeでMCP（Model Context Protocol）サーバーを設定する方法

## MCPとは

MCP（Model Context Protocol）は、AIモデルが外部ツールやデータソースと対話するためのプロトコルです。MCPサーバーを設定することで、<PERSON> Codeに追加機能を提供できます。

## 現在の状況

現在、Claude Codeは以下のビルトインMCPサーバーをサポートしています：
- ファイルシステムアクセス
- ウェブ検索
- ウェブページ取得

カスタムMCPサーバー（Shopify Dev MCPなど）の追加は、現時点ではサポートされていません。

## 代替手段

### 1. ローカルツールの活用

Shopify開発においては、プロジェクト内の既存ツールを活用することで、MCPサーバーと同等の機能を実現できます：

```bash
# GraphQLスキーマの検索
zcat data/admin_schema_2025-01.json.gz | jq '.data.__schema.types[] | select(.name == "Product")'

# メタフィールドの確認
node scripts/check-product-metafields-usage.ts

# GraphQLクエリの実行
node scripts/run-graphql-query.js
```

### 2. Shopify CLIの活用

Shopify CLIは多くの開発タスクを自動化できます：

```bash
# アプリ情報の確認
shopify app info

# 拡張機能の生成
shopify app generate extension

# Webhookのトリガー（テスト用）
shopify app webhook trigger
```

### 3. プロジェクト内スクリプト

このプロジェクトには、Shopify開発を支援する多数のスクリプトが含まれています：

- `scripts/create-complete-product-from-data.ts` - 商品作成
- `scripts/check-product-metafields.js` - メタフィールド確認
- `scripts/test-shopify-prisma-integration.ts` - 統合テスト

## 今後の展望

Claude Codeが将来的にカスタムMCPサーバーをサポートした場合、以下の設定が可能になる可能性があります：

```json
// 将来的な設定例（現在は使用不可）
{
  "mcpServers": {
    "shopify-dev": {
      "command": "node",
      "args": ["/path/to/shopify-dev-mcp/dist/index.js"]
    }
  }
}
```

## 推奨ワークフロー

現在は、以下のワークフローを推奨します：

1. **開発開始時**
   ```bash
   # Cloudflareトンネルを起動
   cloudflared tunnel run shopify-app-tunnel
   
   # 開発サーバーを起動
   npm run dev:fixed-tunnel
   ```

2. **GraphQL開発時**
   ```bash
   # スキーマを確認
   zcat data/admin_schema_2025-01.json.gz | jq '.data.__schema.types[].name' | sort | less
   
   # 既存の実装を参照
   grep -r "admin.graphql" app/services/
   ```

3. **テスト実行時**
   ```bash
   # 統合テストの実行
   npm run test:shopify-prisma-integration
   
   # 特定のテストを実行
   npx vitest run path/to/test.ts
   ```

## 関連ドキュメント

- [Shopify Dev MCP Server セットアップガイド](./shopify-dev-mcp-setup.md)
- [Claude CodeでShopify開発支援を活用する方法](./shopify-dev-mcp-claude-code.md)
- [CLAUDE.md](../CLAUDE.md) - プロジェクト固有のガイダンス

---

本ドキュメントはClaude Codeの機能拡張に合わせて更新されます。
最終更新: 2025年5月25日