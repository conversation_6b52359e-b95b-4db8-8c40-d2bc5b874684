/**
 * Prismaスキーマ整合性テスト
 *
 * このテストは以下の機能をテストします：
 * 1. Prismaスキーマとデータベーススキーマの整合性
 * 2. Prismaクエリの正確性
 * 3. メタデータフィールドの使用
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { prisma } from '../../db.server';

// Prismaのモック
vi.mock('../../db.server', () => ({
  prisma: {
    booking: {
      findMany: vi.fn(),
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
    },
    product: {
      findMany: vi.fn(),
      findUnique: vi.fn(),
    },
  },
}));

describe('Prismaスキーマ整合性', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('予約クエリ', () => {
    it('予約一覧取得時に正しいフィールドを選択する', async () => {
      // モックの戻り値を設定
      (prisma.booking.findMany as any).mockResolvedValue([]);

      // 予約一覧を取得する関数を呼び出す
      await getBookings();

      // findManyが呼び出されたことを確認
      expect(prisma.booking.findMany).toHaveBeenCalled();

      // 呼び出しパラメータを取得
      const callArgs = (prisma.booking.findMany as any).mock.calls[0][0];

      // 必要なフィールドが含まれていることを確認
      expect(callArgs.select).toHaveProperty('id', true);
      expect(callArgs.select).toHaveProperty('productId', true);
      expect(callArgs.select).toHaveProperty('startDate', true);
      expect(callArgs.select).toHaveProperty('endDate', true);
      expect(callArgs.select).toHaveProperty('metadata', true);

      // shopifyOrderIdとshopifyOrderNameが含まれていないことを確認
      expect(callArgs.select).not.toHaveProperty('shopifyOrderId');
      expect(callArgs.select).not.toHaveProperty('shopifyOrderName');
    });

    it('予約詳細取得時に正しいフィールドを選択する', async () => {
      // モックの戻り値を設定
      (prisma.booking.findUnique as any).mockResolvedValue(null);

      // 予約詳細を取得する関数を呼び出す
      await getBookingById('test-id');

      // findUniqueが呼び出されたことを確認
      expect(prisma.booking.findUnique).toHaveBeenCalled();

      // 呼び出しパラメータを取得
      const callArgs = (prisma.booking.findUnique as any).mock.calls[0][0];

      // 必要なフィールドが含まれていることを確認
      expect(callArgs.select).toHaveProperty('id', true);
      expect(callArgs.select).toHaveProperty('productId', true);
      expect(callArgs.select).toHaveProperty('startDate', true);
      expect(callArgs.select).toHaveProperty('endDate', true);
      expect(callArgs.select).toHaveProperty('metadata', true);

      // shopifyOrderIdとshopifyOrderNameが含まれていないことを確認
      expect(callArgs.select).not.toHaveProperty('shopifyOrderId');
      expect(callArgs.select).not.toHaveProperty('shopifyOrderName');
    });

    it('予約作成時にShopify注文情報をメタデータに保存する', async () => {
      // モックの戻り値を設定
      (prisma.booking.create as any).mockResolvedValue({
        id: 'new-booking-id',
        metadata: {
          shopifyOrderId: '12345',
          shopifyOrderName: '#1001',
        },
      });

      // 予約を作成する関数を呼び出す
      const booking = await createBooking({
        productId: 'product-id',
        startDate: new Date(),
        endDate: new Date(),
        shopifyOrderId: '12345',
        shopifyOrderName: '#1001',
      });

      // createが正しいパラメータで呼び出されたことを確認
      expect(prisma.booking.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            metadata: expect.objectContaining({
              shopifyOrderId: '12345',
              shopifyOrderName: '#1001',
            }),
          }),
        })
      );

      // 戻り値が正しいことを確認
      expect(booking.metadata).toEqual(
        expect.objectContaining({
          shopifyOrderId: '12345',
          shopifyOrderName: '#1001',
        })
      );
    });

    it('予約更新時にShopify注文情報をメタデータに保存する', async () => {
      // モックの戻り値を設定
      (prisma.booking.update as any).mockResolvedValue({
        id: 'booking-id',
        metadata: {
          shopifyOrderId: '12345',
          shopifyOrderName: '#1001',
        },
      });

      // 予約を更新する関数を呼び出す
      const booking = await updateBooking('booking-id', {
        shopifyOrderId: '12345',
        shopifyOrderName: '#1001',
      });

      // updateが正しいパラメータで呼び出されたことを確認
      expect(prisma.booking.update).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            metadata: expect.objectContaining({
              shopifyOrderId: '12345',
              shopifyOrderName: '#1001',
            }),
          }),
        })
      );

      // 戻り値が正しいことを確認
      expect(booking.metadata).toEqual(
        expect.objectContaining({
          shopifyOrderId: '12345',
          shopifyOrderName: '#1001',
        })
      );
    });
  });
});

// テスト用の関数
async function getBookings() {
  return prisma.booking.findMany({
    select: {
      id: true,
      productId: true,
      startDate: true,
      endDate: true,
      status: true,
      bookingType: true,
      customerName: true,
      customerEmail: true,
      totalAmount: true,
      depositAmount: true,
      metadata: true,
    },
  });
}

async function getBookingById(id: string) {
  return prisma.booking.findUnique({
    where: { id },
    select: {
      id: true,
      productId: true,
      startDate: true,
      endDate: true,
      status: true,
      bookingType: true,
      customerName: true,
      customerEmail: true,
      totalAmount: true,
      depositAmount: true,
      metadata: true,
    },
  });
}

async function createBooking(data: any) {
  return prisma.booking.create({
    data: {
      productId: data.productId,
      startDate: data.startDate,
      endDate: data.endDate,
      metadata: {
        shopifyOrderId: data.shopifyOrderId,
        shopifyOrderName: data.shopifyOrderName,
      },
    },
  });
}

async function updateBooking(id: string, data: any) {
  return prisma.booking.update({
    where: { id },
    data: {
      metadata: {
        shopifyOrderId: data.shopifyOrderId,
        shopifyOrderName: data.shopifyOrderName,
      },
    },
  });
}