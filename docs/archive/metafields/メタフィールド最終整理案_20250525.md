# メタフィールド最終整理案

**作成日**: 2025年5月25日  
**目的**: 商品グループ管理とメタフィールドの最適化

## 📊 商品グループ管理の現状

### **CSVデータから判明したこと**

1. **型番（商品コード）によるグループ管理**
   - 例: 10101031-001, 10101031-002（同じカリモクソファの個体違い）
   - 商品コード5桁目まで同じ = 同一商品グループ

2. **カラー違いは商品名に含まれる**
   - ホガースチェア ベルベットピンクシート
   - ダマスクホワイトシートチェア
   - チッペンチェア クリスマスカラー

3. **現在の管理体系**
   ```
   商品コード（型番） + 詳細コード = ユニーク商品
   10101031          + 001        = カリモクソファ #1
   10101031          + 002        = カリモクソファ #2
   ```

## 🎯 メタフィールド整理の最終案

### **必須メタフィールド（15個）**

#### **商品情報系（7個）**
1. `rental.basic_info` (JSON) ✅
   ```json
   {
     "productCode": "10101031",      // 型番（グループ識別用）
     "detailCode": "001",            // 個体番号
     "kana": "カリモクソファ",
     "location": "NY",
     "status": "available",
     "color": "モケットグリーン"      // 商品名から抽出
   }
   ```

2. `rental.pricing` (JSON) ✅
3. `product.height` ✅
4. `product.width` ✅
5. `product.depth` ✅
6. `rental.status` ✅
7. `rental.location` ✅

#### **バリアント管理系（3個）**
8. `rental.variant_mapping` (JSON) ⚠️ **要設定**
   - 各バリアントとレンタル日数のマッピング
   - 現在未設定の商品が多い

9. `rental.variation_type` ❓ **再検討必要**
   - 本来: "rental_period", "color", "size"などの分類
   - 提案: 全商品"rental_period"で統一なら不要かも

10. `rental.inventory_items` (JSON) ⚠️ **要設定**
    - 商品の状態管理
    - 現在未設定

#### **運用情報系（5個）**
11. `rental.color` ✅
12. `rental.material` ✅
13. `rental.maintenance_notes` ✅
14. `rental.booking_notes` ✅
15. `rental.booking_type` ⚠️ **再検討**
    - 全商品同じなら不要

### **整理対象メタフィールド（8個）**

#### **購入情報系（3個）→ basic_infoに統合**
- `purchase_place` → basic_info.purchaseInfo.place
- `purchase_date` → basic_info.purchaseInfo.date
- `purchase_price` → basic_info.purchaseInfo.price

**理由**: 購入価格は回収計算で使用される可能性があるため、削除せずJSON内に保持

#### **廃棄情報系（3個）→ 商品アーカイブで管理**
- `is_disposed` → 商品ステータスを"disposed"に
- `disposal_date` → basic_info.disposalInfo.date
- `disposal_reason` → basic_info.disposalInfo.reason

**理由**: 廃棄商品はShopifyでアーカイブ、情報はJSON内に保持

#### **その他（2個）**
- `manufacturer` → basic_info.manufacturer（ほぼ未使用）
- `reservation_info` → 削除（Bookingテーブルで管理）

## 🔧 グループ管理の実装方法

### **型番ベースのグループ管理**

1. **商品コード（型番）でグループ化**
   ```javascript
   // 同じ型番の商品を検索
   const groupCode = product.basicInfo.productCode;
   const relatedProducts = await prisma.product.findMany({
     where: {
       basicInfo: {
         path: ['productCode'],
         equals: groupCode
       }
     }
   });
   ```

2. **フロントエンドでの表示**
   - 商品詳細ページに「同じ商品の他の在庫」セクション
   - 型番が同じで詳細コードが異なる商品をリスト表示

3. **variation_typeの扱い**
   - **Option A**: 削除（全商品レンタル期間バリエーションのみ）
   - **Option B**: "rental_period"固定で維持（将来の拡張性）
   - **推奨**: Option B（データ構造の一貫性維持）

## 📝 実装手順

### **Phase 1: 既存メタフィールドの補完**
```javascript
// 1. variant_mappingの自動生成
// 2. inventory_itemsの初期設定
// 3. variation_typeを"rental_period"に統一
```

### **Phase 2: 購入・廃棄情報の移行**
```javascript
// basic_infoの拡張
{
  "productCode": "10101031",
  "detailCode": "001",
  // ... 既存フィールド
  "purchaseInfo": {
    "date": "2020-01-01",
    "place": "東京",
    "price": 50000
  },
  "disposalInfo": null  // 廃棄時に設定
}
```

### **Phase 3: 不要メタフィールドの削除**
- manufacturer（使用率低）
- reservation_info（Bookingで管理）
- booking_type（全商品同じなら）

## 🎯 最終的なメタフィールド構成

### **コア機能（10個）**
1. basic_info（グループ管理含む）
2. pricing
3. height, width, depth
4. status, location
5. variant_mapping
6. inventory_items
7. maintenance_notes

### **オプション（5個）**
1. color, material
2. booking_notes
3. variation_type（将来性のため維持）
4. booking_type（要検討）

**合計: 15個**（現在の23個から8個削減）

## 💡 重要ポイント

1. **型番によるグループ管理は既に可能**
   - productCodeで識別
   - 追加のgroup_infoメタフィールドは不要

2. **variation_typeは将来の拡張性のため維持推奨**
   - 今後、色違いやサイズ違いの管理が必要になる可能性

3. **購入価格は保持すべき**
   - 回収計算で使用される
   - basic_info内にJSONとして保存

4. **inventory_itemsとvariant_mappingは要設定**
   - 現在未設定の商品が多い
   - システムの完全性のため必要