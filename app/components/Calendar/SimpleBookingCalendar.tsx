/**
 * シンプルな予約カレンダーコンポーネント
 * 商品選択と予約状況表示に特化
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Text,
  Card,
  BlockStack,
  Select,
  Badge,
  InlineStack,
  Banner,
  Spinner
} from '@shopify/polaris';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';

// 予約データの型定義
interface Booking {
  id: string;
  productId: string;
  startDate: Date;
  endDate: Date;
  status: string;
  bookingType: string;
  customerName: string | null;
  customerEmail: string | null;
}

// 商品データの型定義
interface Product {
  id: string;
  title: string;
  sku: string;
  status: string;
}

// コンポーネントのProps
interface SimpleBookingCalendarProps {
  products: Product[];
  bookings: Booking[];
  onProductSelect?: (productId: string) => void;
  onDateRangeSelect?: (startDate: Date, endDate: Date) => void;
}

export function SimpleBookingCalendar({
  products,
  bookings,
  onProductSelect,
  onDateRangeSelect
}: SimpleBookingCalendarProps) {
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  // 選択された商品の予約をフィルタリング
  const selectedProductBookings = selectedProductId
    ? bookings.filter(booking => booking.productId === selectedProductId)
    : [];

  // 商品選択ハンドラー
  const handleProductChange = (value: string) => {
    setSelectedProductId(value);
    setStartDate('');
    setEndDate('');
    if (onProductSelect) {
      onProductSelect(value);
    }
  };

  // 日付選択ハンドラー
  const handleDateChange = () => {
    if (startDate && endDate && onDateRangeSelect) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      if (start <= end) {
        onDateRangeSelect(start, end);
      }
    }
  };

  // 日付が変更されたときに自動的にコールバックを呼び出し
  useEffect(() => {
    handleDateChange();
  }, [startDate, endDate]);

  // 日付が予約済みかチェック
  const isDateRangeConflicting = (checkStart: Date, checkEnd: Date): boolean => {
    return selectedProductBookings.some(booking => {
      const bookingStart = new Date(booking.startDate);
      const bookingEnd = new Date(booking.endDate);
      
      // 日付の重複をチェック
      return (
        (checkStart >= bookingStart && checkStart <= bookingEnd) ||
        (checkEnd >= bookingStart && checkEnd <= bookingEnd) ||
        (checkStart <= bookingStart && checkEnd >= bookingEnd)
      );
    });
  };

  // 商品選択オプション
  const productOptions = [
    { label: '商品を選択してください', value: '' },
    ...products.map(product => ({
      label: `${product.title} (${product.sku})`,
      value: product.id
    }))
  ];

  // 選択された商品の情報
  const selectedProduct = products.find(p => p.id === selectedProductId);

  return (
    <Card>
      <BlockStack gap="400">
        <Text variant="headingMd" as="h2">
          予約カレンダー
        </Text>

        {/* 商品選択 */}
        <Select
          label="商品を選択"
          options={productOptions}
          value={selectedProductId}
          onChange={handleProductChange}
          placeholder="商品を選択してください"
        />

        {/* 選択された商品の情報 */}
        {selectedProduct && (
          <Box background="bg-surface-secondary" padding="300" borderRadius="100">
            <BlockStack gap="200">
              <Text variant="headingSm" as="h3">選択された商品</Text>
              <Text as="p"><strong>商品名:</strong> {selectedProduct.title}</Text>
              <Text as="p"><strong>SKU:</strong> {selectedProduct.sku}</Text>
              <Text as="p"><strong>ステータス:</strong> {selectedProduct.status}</Text>
            </BlockStack>
          </Box>
        )}

        {/* 現在の予約状況 */}
        {selectedProductId && (
          <Box>
            <BlockStack gap="300">
              <Text variant="headingSm" as="h3">
                現在の予約状況 ({selectedProductBookings.length}件)
              </Text>
              
              {selectedProductBookings.length === 0 ? (
                <Banner tone="success">
                  <Text as="p">この商品には現在予約が入っていません。</Text>
                </Banner>
              ) : (
                <BlockStack gap="200">
                  {selectedProductBookings.map(booking => (
                    <Box key={booking.id} background="bg-surface-secondary" padding="300" borderRadius="100">
                      <InlineStack gap="200" align="space-between">
                        <BlockStack gap="100">
                          <Text as="p">
                            <strong>期間:</strong> {format(new Date(booking.startDate), 'yyyy年MM月dd日', { locale: ja })} 
                            ～ {format(new Date(booking.endDate), 'yyyy年MM月dd日', { locale: ja })}
                          </Text>
                          {booking.customerName && (
                            <Text as="p"><strong>顧客:</strong> {booking.customerName}</Text>
                          )}
                        </BlockStack>
                        <BlockStack gap="100">
                          <Badge tone={booking.status === 'CONFIRMED' ? 'success' : 'warning'}>
                            {booking.status === 'CONFIRMED' ? '確定' : '仮予約'}
                          </Badge>
                          <Badge tone={booking.bookingType === 'CONFIRMED' ? 'success' : 'attention'}>
                            {booking.bookingType === 'CONFIRMED' ? '本予約' : '仮予約'}
                          </Badge>
                        </BlockStack>
                      </InlineStack>
                    </Box>
                  ))}
                </BlockStack>
              )}
            </BlockStack>
          </Box>
        )}

        {/* 日付選択 */}
        {selectedProductId && (
          <Box>
            <BlockStack gap="300">
              <Text variant="headingSm" as="h3">新しい予約の日付を選択</Text>
              
              <InlineStack gap="300">
                <Box>
                  <label htmlFor="start-date">
                    <Text as="span">開始日</Text>
                  </label>
                  <input
                    id="start-date"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    min={format(new Date(), 'yyyy-MM-dd')}
                    style={{
                      width: '100%',
                      padding: '8px',
                      border: '1px solid #ccc',
                      borderRadius: '4px',
                      marginTop: '4px'
                    }}
                  />
                </Box>
                
                <Box>
                  <label htmlFor="end-date">
                    <Text as="span">終了日</Text>
                  </label>
                  <input
                    id="end-date"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    min={startDate || format(new Date(), 'yyyy-MM-dd')}
                    style={{
                      width: '100%',
                      padding: '8px',
                      border: '1px solid #ccc',
                      borderRadius: '4px',
                      marginTop: '4px'
                    }}
                  />
                </Box>
              </InlineStack>

              {/* 日付選択の結果 */}
              {startDate && endDate && (
                <Box>
                  {(() => {
                    const start = new Date(startDate);
                    const end = new Date(endDate);
                    const isConflicting = isDateRangeConflicting(start, end);
                    const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;

                    return (
                      <Banner tone={isConflicting ? 'critical' : 'success'}>
                        <BlockStack gap="200">
                          <Text as="p">
                            <strong>選択期間:</strong> {format(start, 'yyyy年MM月dd日', { locale: ja })} 
                            ～ {format(end, 'yyyy年MM月dd日', { locale: ja })} ({days}日間)
                          </Text>
                          {isConflicting ? (
                            <Text as="p" tone="critical">
                              ⚠️ この期間は既存の予約と重複しています。別の日程を選択してください。
                            </Text>
                          ) : (
                            <Text as="p" tone="success">
                              ✅ この期間は予約可能です。
                            </Text>
                          )}
                        </BlockStack>
                      </Banner>
                    );
                  })()}
                </Box>
              )}
            </BlockStack>
          </Box>
        )}
      </BlockStack>
    </Card>
  );
}
