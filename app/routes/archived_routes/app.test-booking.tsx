/**
 * テスト用予約フォーム
 *
 * このページは開発・テスト用の簡易予約フォームを提供します。
 * 商品選択、日付選択、顧客情報入力、予約作成の機能を含みます。
 */

import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, useSubmit, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  BlockStack,
  Text,
  Select,
  DatePicker,
  TextField,
  Button,
  Banner,
  InlineStack,
  Box,
  Divider,
  RadioButton,
  Spinner
} from "@shopify/polaris";
import { useState, useCallback, useEffect } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { isDateRangeAvailable } from "../utils/booking/booking-utils-prisma";
import { v4 as uuidv4 } from 'uuid';

/**
 * ローダー関数
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // 商品データを取得
    const products = await prisma.product.findMany({
      where: { shop },
      select: {
        id: true,
        title: true,
        sku: true,
        status: true,
      },
      orderBy: { title: 'asc' },
    });

    // 予約データを取得（最新10件）
    const bookings = await prisma.booking.findMany({
      where: { shop },
      select: {
        id: true,
        bookingId: true,
        productId: true,
        startDate: true,
        endDate: true,
        status: true,
        bookingType: true,
        customerEmail: true,
        customerName: true,
        product: {
          select: {
            id: true,
            title: true,
            sku: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
    });

    // Date型を文字列に変換
    const serializedBookings = bookings.map(b => ({
      ...b,
      startDate: b.startDate.toISOString(),
      endDate: b.endDate.toISOString(),
    }));

    return json({
      products,
      bookings: serializedBookings,
    });
  } catch (error) {
    console.error("データ取得エラー:", error);
    return json({
      products: [],
      bookings: [],
      error: "データの取得中にエラーが発生しました",
    });
  }
}

/**
 * アクション関数
 */
export async function action({ request }: ActionFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    const formData = await request.formData();
    const intent = formData.get("intent") as string;

    if (intent === "create-booking") {
      const productId = formData.get("productId") as string;
      const startDate = formData.get("startDate") as string;
      const endDate = formData.get("endDate") as string;
      const customerEmail = formData.get("customerEmail") as string;
      const customerName = formData.get("customerName") as string;
      const bookingType = formData.get("bookingType") as string;
      const createDraftOrder = formData.get("createDraftOrder") === "true";

      // 必須項目の検証
      if (!productId || !startDate || !endDate || !customerEmail) {
        return json({
          success: false,
          error: "必須項目が入力されていません",
        });
      }

      // 日付範囲が利用可能か確認
      const isAvailable = await isDateRangeAvailable(
        productId,
        new Date(startDate),
        new Date(endDate)
      );

      if (!isAvailable) {
        return json({
          success: false,
          error: "選択した日付範囲は既に予約されています",
        });
      }

      // 予約ID生成
      const bookingId = `BOOK-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

      // ドラフトオーダーの作成（オプション）
      let draftOrderId = null;
      let draftOrderName = null;

      if (createDraftOrder) {
        // 商品情報の取得
        const product = await prisma.product.findUnique({
          where: { id: productId },
          select: { title: true, sku: true, shopifyId: true },
        });

        if (!product) {
          return json({
            success: false,
            error: "商品情報の取得に失敗しました",
          });
        }

        // 実際の環境ではvariantIdはフロントエンドから送信されるか、
        // 別のテーブルから取得する必要があります

        // GraphQLで商品情報を取得して有効なバリアントIDを取得
        // 商品IDがgid://shopify/Product/形式でない場合は変換する
        let productGid = product.shopifyId;
        if (!productGid.startsWith('gid://')) {
          productGid = `gid://shopify/Product/${productGid}`;
        }

        console.log("商品GID:", productGid);

        try {
          const productResponse = await admin.graphql(`
            query getProduct($id: ID!) {
              product(id: $id) {
                id
                title
                variants(first: 1) {
                  edges {
                    node {
                      id
                      title
                      sku
                    }
                  }
                }
              }
            }
          `, {
            variables: {
              id: productGid
            }
          });

          const productData = await productResponse.json();

          if (productData.errors) {
            console.error("GraphQLエラー:", productData.errors);
            return json({
              success: false,
              error: `商品情報の取得に失敗しました: ${productData.errors[0]?.message || 'GraphQLエラー'}`,
            });
          }

          if (!productData.data?.product) {
            console.error("商品が見つかりません:", product.shopifyId);
            return json({
              success: false,
              error: `商品情報の取得に失敗しました: 商品ID ${product.shopifyId} が見つかりません`,
            });
          }

          if (!productData.data.product.variants.edges.length) {
            console.error("バリアントが見つかりません:", product.shopifyId);
            return json({
              success: false,
              error: "商品情報の取得に失敗しました: バリアントが見つかりません",
            });
          }

          // 最初のバリアントを使用
          const variantId = productData.data.product.variants.edges[0].node.id.replace('gid://shopify/ProductVariant/', '');
          const variantGid = `gid://shopify/ProductVariant/${variantId}`;
        } catch (error) {
          console.error("GraphQL API呼び出しエラー:", error);
          return json({
            success: false,
            error: `商品情報の取得中にエラーが発生しました: ${error instanceof Error ? error.message : String(error)}`,
          });
        }

        try {
          // レンタル日数の計算
          const startDateObj = new Date(startDate);
          const endDateObj = new Date(endDate);
          const rentalDays = Math.round((endDateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24)) + 1;

          // ドラフトオーダー作成
          const draftOrderResponse = await admin.graphql(`
            mutation draftOrderCreate($input: DraftOrderInput!) {
              draftOrderCreate(input: $input) {
                draftOrder {
                  id
                  name
                  totalPrice
                }
                userErrors {
                  field
                  message
                }
              }
            }
          `, {
            variables: {
              input: {
                lineItems: [
                  {
                    variantId: variantGid,
                    quantity: 1,
                    customAttributes: [
                      { key: "レンタル開始日", value: startDate },
                      { key: "レンタル終了日", value: endDate },
                      { key: "レンタル日数", value: rentalDays.toString() }
                    ]
                  }
                ],
                customAttributes: [
                  { key: "レンタルタイプ", value: bookingType === "CONFIRMED" ? "確定予約" : "仮予約" },
                  { key: "予約ID", value: bookingId }
                ],
                email: customerEmail,
                note: `レンタル予約: ${startDate}～${endDate}`,
                shippingLine: {
                  title: "レンタル配送",
                  price: "0.00"
                }
              }
            }
          });

          const draftOrderData = await draftOrderResponse.json();

          if (draftOrderData.errors || draftOrderData.data.draftOrderCreate.userErrors.length > 0) {
            console.error("ドラフトオーダー作成エラー:", draftOrderData.errors || draftOrderData.data.draftOrderCreate.userErrors);
            return json({
              success: false,
              error: "ドラフトオーダーの作成に失敗しました",
            });
          }

          draftOrderId = draftOrderData.data.draftOrderCreate.draftOrder.id.replace('gid://shopify/DraftOrder/', '');
          draftOrderName = draftOrderData.data.draftOrderCreate.draftOrder.name;
        } catch (error) {
          console.error("ドラフトオーダー作成エラー:", error);
          return json({
            success: false,
            error: `ドラフトオーダーの作成中にエラーが発生しました: ${error instanceof Error ? error.message : String(error)}`,
          });
        }
      }

      // 予約データをデータベースに保存
      const booking = await prisma.booking.create({
        data: {
          id: uuidv4(),
          bookingId,
          shop,
          productId,
          orderId: draftOrderId,
          status: bookingType as any,
          bookingType: bookingType as any,
          startDate: new Date(startDate),
          endDate: new Date(endDate),
          customerEmail,
          customerName,
          createdAt: new Date(),
          updatedAt: new Date(),
          transactionId: uuidv4(),
          priority: 1
        }
      });

      return json({
        success: true,
        booking: {
          id: booking.id,
          bookingId: booking.bookingId,
          status: booking.status,
          startDate: booking.startDate,
          endDate: booking.endDate,
          customerEmail: booking.customerEmail,
          customerName: booking.customerName
        },
        draftOrder: draftOrderId ? {
          id: draftOrderId,
          name: draftOrderName
        } : null
      });
    }

    return json({
      success: false,
      error: "不明な操作が指定されました",
    });
  } catch (error) {
    console.error("アクションエラー:", error);
    return json({
      success: false,
      error: "処理中にエラーが発生しました: " + error.message,
    });
  }
}

/**
 * テスト用予約フォームコンポーネント
 */
export default function TestBookingPage() {
  const { products, bookings, error } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();

  // 状態管理
  const [selectedProductId, setSelectedProductId] = useState("");
  const [selectedDates, setSelectedDates] = useState({
    start: new Date(),
    end: new Date(new Date().setDate(new Date().getDate() + 2))
  });
  const [customerEmail, setCustomerEmail] = useState("<EMAIL>");
  const [customerName, setCustomerName] = useState("テストユーザー");
  const [bookingType, setBookingType] = useState("PROVISIONAL");
  const [createDraftOrder, setCreateDraftOrder] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [month, setMonth] = useState(new Date().getMonth());
  const [year, setYear] = useState(new Date().getFullYear());

  // 商品選択ハンドラー
  const handleProductChange = useCallback((value: string) => {
    setSelectedProductId(value);
  }, []);

  // 日付選択ハンドラー
  const handleDateChange = useCallback(({ start, end }: { start: Date; end: Date }) => {
    setSelectedDates({ start, end });
  }, []);

  // 月変更ハンドラー
  const handleMonthChange = useCallback((month: number, year: number) => {
    setMonth(month);
    setYear(year);
  }, []);

  // 予約タイプ変更ハンドラー
  const handleBookingTypeChange = useCallback((value: string) => {
    setBookingType(value);
  }, []);

  // フォーム送信ハンドラー
  const handleSubmit = useCallback(() => {
    if (!selectedProductId || !customerEmail) {
      alert("商品とメールアドレスは必須です");
      return;
    }

    setIsSubmitting(true);

    const formData = new FormData();
    formData.append("intent", "create-booking");
    formData.append("productId", selectedProductId);
    formData.append("startDate", selectedDates.start.toISOString());
    formData.append("endDate", selectedDates.end.toISOString());
    formData.append("customerEmail", customerEmail);
    formData.append("customerName", customerName);
    formData.append("bookingType", bookingType);
    formData.append("createDraftOrder", createDraftOrder.toString());

    submit(formData, { method: "post" });
  }, [
    selectedProductId,
    selectedDates,
    customerEmail,
    customerName,
    bookingType,
    createDraftOrder,
    submit
  ]);

  // アクションデータが変更されたときに送信中状態をリセット
  useEffect(() => {
    if (actionData) {
      setIsSubmitting(false);
    }
  }, [actionData]);

  // 商品選択オプション
  const productOptions = [
    { label: "商品を選択", value: "" },
    ...products.map(product => ({
      label: `${product.title} (${product.sku || "SKUなし"})`,
      value: product.id
    }))
  ];

  return (
    <Page
      title="テスト用予約フォーム"
      subtitle="開発・テスト用の簡易予約フォーム"
      backAction={{ content: "戻る", url: "/app" }}
    >
      <Layout>
        {/* エラーメッセージ */}
        {(error || actionData?.error) && (
          <Layout.Section>
            <Banner
              title="エラー"
              tone="critical"
            >
              <p>{error || actionData?.error}</p>
            </Banner>
          </Layout.Section>
        )}

        {/* 成功メッセージ */}
        {actionData?.success && (
          <Layout.Section>
            <Banner
              title="予約が作成されました"
              tone="success"
            >
              <BlockStack gap="200">
                <Text>予約ID: {actionData.booking.bookingId}</Text>
                <Text>ステータス: {actionData.booking.status}</Text>
                <Text>期間: {new Date(actionData.booking.startDate).toLocaleDateString()} 〜 {new Date(actionData.booking.endDate).toLocaleDateString()}</Text>
                {actionData.draftOrder && (
                  <Text>ドラフトオーダー: {actionData.draftOrder.name}</Text>
                )}
              </BlockStack>
            </Banner>
          </Layout.Section>
        )}

        <Layout.Section>
          <Card>
            <BlockStack gap="500">
              <Text variant="headingMd" as="h2">予約情報入力</Text>

              <Select
                label="商品"
                options={productOptions}
                value={selectedProductId}
                onChange={handleProductChange}
                helpText="予約する商品を選択してください"
                requiredIndicator
              />

              <Box>
                <Text variant="headingMd" as="h3">レンタル期間</Text>
                <DatePicker
                  month={month}
                  year={year}
                  onChange={handleDateChange}
                  onMonthChange={handleMonthChange}
                  selected={{
                    start: selectedDates.start,
                    end: selectedDates.end
                  }}
                  allowRange
                />
                <Box paddingBlockStart="300">
                  <Text>
                    選択期間: {selectedDates.start.toLocaleDateString()} 〜 {selectedDates.end.toLocaleDateString()}
                    （{Math.round((selectedDates.end.getTime() - selectedDates.start.getTime()) / (1000 * 60 * 60 * 24)) + 1}日間）
                  </Text>
                </Box>
              </Box>

              <Divider />

              <TextField
                label="メールアドレス"
                type="email"
                value={customerEmail}
                onChange={setCustomerEmail}
                helpText="顧客のメールアドレスを入力してください"
                requiredIndicator
                autoComplete="email"
              />

              <TextField
                label="顧客名"
                value={customerName}
                onChange={setCustomerName}
                helpText="顧客の名前を入力してください"
                autoComplete="name"
              />

              <Box>
                <Text variant="headingMd" as="h3">予約タイプ</Text>
                <BlockStack gap="200">
                  <RadioButton
                    label="仮予約"
                    checked={bookingType === "PROVISIONAL"}
                    id="provisional"
                    name="bookingType"
                    onChange={() => handleBookingTypeChange("PROVISIONAL")}
                  />
                  <RadioButton
                    label="確定予約"
                    checked={bookingType === "CONFIRMED"}
                    id="confirmed"
                    name="bookingType"
                    onChange={() => handleBookingTypeChange("CONFIRMED")}
                  />
                </BlockStack>
              </Box>

              <Box>
                <Text variant="headingMd" as="h3">ドラフトオーダー</Text>
                <BlockStack gap="200">
                  <RadioButton
                    label="ドラフトオーダーを作成する"
                    checked={createDraftOrder}
                    id="create-draft-order-yes"
                    name="createDraftOrder"
                    onChange={() => setCreateDraftOrder(true)}
                  />
                  <RadioButton
                    label="ドラフトオーダーを作成しない"
                    checked={!createDraftOrder}
                    id="create-draft-order-no"
                    name="createDraftOrder"
                    onChange={() => setCreateDraftOrder(false)}
                  />
                </BlockStack>
              </Box>

              <Box>
                <Button
                  primary
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <InlineStack gap="200" align="center">
                      <Spinner size="small" />
                      <span>処理中...</span>
                    </InlineStack>
                  ) : (
                    "予約を作成"
                  )}
                </Button>
              </Box>
            </BlockStack>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text variant="headingMd" as="h2">最近の予約（最新10件）</Text>

              {bookings.length === 0 ? (
                <Text>予約データがありません</Text>
              ) : (
                <BlockStack gap="300">
                  {bookings.map(booking => (
                    <Box
                      key={booking.id}
                      padding="300"
                      borderWidth="025"
                      borderRadius="100"
                      borderColor="border"
                    >
                      <BlockStack gap="200">
                        <InlineStack gap="200" align="space-between">
                          <Text variant="headingSm" as="h3">
                            {booking.bookingId || booking.id.substring(0, 8)}
                          </Text>
                          <Text>
                            ステータス: {booking.status}
                          </Text>
                        </InlineStack>
                        <Text>
                          商品: {booking.product?.title || "不明"}
                        </Text>
                        <Text>
                          期間: {new Date(booking.startDate).toLocaleDateString()} 〜 {new Date(booking.endDate).toLocaleDateString()}
                        </Text>
                        <Text>
                          顧客: {booking.customerName || "名前なし"} ({booking.customerEmail || "メールなし"})
                        </Text>
                      </BlockStack>
                    </Box>
                  ))}
                </BlockStack>
              )}
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
