import { json } from "@remix-run/node";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { Page, Layout, Card, Text, BlockStack, Button } from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { useEffect } from "react";

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  return json({
    message: "カレンダーの表示問題を修正します"
  });
}

export default function FixCalendar() {
  const { message } = useLoaderData<typeof loader>();

  useEffect(() => {
    // カレンダーのスタイルをリセットする関数
    const resetCalendarStyles = () => {
      console.log('カレンダーのスタイルをリセットします');

      // すべての日付要素を取得
      const dateElements = document.querySelectorAll('.Polaris-DatePicker__Day span');
      console.log(`日付要素を ${dateElements.length} 個見つけました`);

      // 各要素のスタイルをリセット
      dateElements.forEach((spanElement) => {
        // 予約マークを削除
        const bookingMark = spanElement.querySelector('.booking-mark');
        if (bookingMark) {
          bookingMark.remove();
          console.log('予約マークを削除しました');
        }

        // スタイルをリセット
        const htmlElement = spanElement as HTMLElement;
        if (htmlElement.style) {
          htmlElement.style.color = '';
          htmlElement.style.position = '';
          htmlElement.style.textDecoration = '';
          console.log('スタイルをリセットしました');
        }
      });

      console.log('カレンダーのスタイルをリセットしました');
    };

    // 予約データを削除する関数
    const deleteAllBookings = async () => {
      try {
        console.log('すべての予約を削除します');
        const response = await fetch('/app/delete-all-bookings');
        if (response.ok) {
          console.log('すべての予約を削除しました');
          // 予約削除後にカレンダーのスタイルをリセット
          resetCalendarStyles();
          // 予約テストページにリダイレクト
          window.location.href = '/app/booking-test';
        } else {
          console.error('予約の削除に失敗しました');
        }
      } catch (error) {
        console.error('エラーが発生しました:', error);
      }
    };

    // ページ読み込み時にカレンダーのスタイルをリセット
    resetCalendarStyles();

    // グローバルオブジェクトに関数を追加
    window.resetCalendarStyles = resetCalendarStyles;
    window.deleteAllBookings = deleteAllBookings;
  }, []);

  const handleDeleteAllBookings = () => {
    if (window.confirm('すべての予約を削除します。よろしいですか？')) {
      // @ts-ignore
      window.deleteAllBookings();
    }
  };

  const handleResetCalendarStyles = () => {
    // @ts-ignore
    window.resetCalendarStyles();
  };

  return (
    <Page title="カレンダー修正" backAction={{ content: "戻る", url: "/app" }}>
      <TitleBar title="カレンダー修正" />
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="p">{message}</Text>
              <Text as="p">このページでは、カレンダーの表示問題を修正するための機能を提供します。</Text>
              <BlockStack gap="200">
                <Button onClick={handleResetCalendarStyles} primary>
                  カレンダーのスタイルをリセット
                </Button>
                <Button onClick={handleDeleteAllBookings} tone="critical">
                  すべての予約を削除
                </Button>
                <Button url="/app/booking-test">
                  予約テストページに戻る
                </Button>
              </BlockStack>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
