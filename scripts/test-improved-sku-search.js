/**
 * 改善されたSKU検索機能のテストスクリプト
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// クライアントサイドのSKU検索ロジック（JavaScript版）
function matchesSkuSearch(product, searchQuery) {
  if (!searchQuery || searchQuery.trim() === '') {
    return true;
  }

  const trimmedQuery = searchQuery.trim().toLowerCase();
  const productSku = product.sku.toLowerCase();
  const productTitle = product.title.toLowerCase();

  // タイトル検索
  if (productTitle.includes(trimmedQuery)) {
    return true;
  }

  // 1. 完全一致検索
  if (productSku === trimmedQuery) {
    return true;
  }

  // 2. 部分一致検索
  if (productSku.includes(trimmedQuery)) {
    return true;
  }

  // 3. 基本SKU形式の検索（201-07-107 形式）
  if (isBasicSkuFormat(trimmedQuery)) {
    if (productSku.startsWith(trimmedQuery + '-')) {
      return true;
    }
  }

  // 4. ハイフンなし形式の検索
  if (isNumericOnly(trimmedQuery) && trimmedQuery.length >= 8) {
    const formattedSku = formatNumericToSku(trimmedQuery);
    if (formattedSku) {
      const formattedSkuLower = formattedSku.toLowerCase();
      if (productSku === formattedSkuLower || productSku.startsWith(formattedSkuLower + '-')) {
        return true;
      }
    }
  }

  // 5. 前方10文字での検索
  if (trimmedQuery.length > 10) {
    const prefix = trimmedQuery.substring(0, 10);
    if (isBasicSkuFormat(prefix)) {
      if (productSku.startsWith(prefix.toLowerCase())) {
        return true;
      }
    }
  }

  return false;
}

function isBasicSkuFormat(sku) {
  const basicSkuPattern = /^\d{3}-\d{2}-\d{3}$/;
  return basicSkuPattern.test(sku);
}

function isNumericOnly(str) {
  return /^\d+$/.test(str);
}

function formatNumericToSku(numericStr) {
  if (numericStr.length === 8) {
    return `${numericStr.substring(0, 3)}-${numericStr.substring(3, 5)}-${numericStr.substring(5, 8)}`;
  }
  
  if (numericStr.length >= 8) {
    const first8 = numericStr.substring(0, 8);
    return `${first8.substring(0, 3)}-${first8.substring(3, 5)}-${first8.substring(5, 8)}`;
  }
  
  return null;
}

function filterProductsBySkuSearch(products, searchQuery) {
  if (!searchQuery || searchQuery.trim() === '') {
    return products;
  }

  return products.filter(product => matchesSkuSearch(product, searchQuery));
}

async function testImprovedSkuSearch() {
  console.log('🧪 改善されたSKU検索機能テストを開始...\n');

  try {
    // データベースから実際の商品を取得
    const products = await prisma.product.findMany({
      select: {
        id: true,
        title: true,
        sku: true,
        shopifyId: true
      },
      take: 20
    });

    console.log(`=== データベース商品一覧 (${products.length}件) ===`);
    products.forEach((product, index) => {
      console.log(`${index + 1}. ${product.title} (${product.sku})`);
    });
    console.log('');

    // テストケース
    const testQueries = [
      '201-07-107',    // 基本SKU形式
      '20107107',      // ハイフンなし数字
      '201-07-107-1D', // バリアント付きSKU
      '201',           // 部分SKU
      '107',           // 末尾部分
      'シルバー',       // タイトル検索
      '212-05-023',    // 別の基本SKU
      '21205023'       // 別のハイフンなし数字
    ];

    console.log('=== SKU検索テスト結果 ===');
    testQueries.forEach(query => {
      console.log(`\n検索クエリ: "${query}"`);
      
      const matchedProducts = filterProductsBySkuSearch(products, query);
      
      if (matchedProducts.length > 0) {
        console.log(`  マッチした商品: ${matchedProducts.length}件`);
        matchedProducts.forEach((product, index) => {
          console.log(`    ${index + 1}. ${product.title} (${product.sku})`);
        });
      } else {
        console.log('  マッチした商品: なし');
      }
    });

    // 特定の商品での詳細テスト
    console.log('\n=== 特定商品での詳細テスト ===');
    const targetProduct = products.find(p => p.sku.includes('201-07-107'));
    
    if (targetProduct) {
      console.log(`対象商品: ${targetProduct.title} (${targetProduct.sku})`);
      
      const detailTestQueries = [
        '201-07-107',
        '20107107',
        '201-07-107-1D',
        '201-07-107-2D',
        '201',
        '107',
        'シルバートレー'
      ];

      detailTestQueries.forEach(query => {
        const matches = matchesSkuSearch(targetProduct, query);
        console.log(`  "${query}" → ${matches ? '✅ マッチ' : '❌ 非マッチ'}`);
      });
    } else {
      console.log('201-07-107を含む商品が見つかりませんでした');
    }

    // パフォーマンステスト
    console.log('\n=== パフォーマンステスト ===');
    const startTime = Date.now();
    
    for (let i = 0; i < 1000; i++) {
      filterProductsBySkuSearch(products, '201-07-107');
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`1000回の検索実行時間: ${duration}ms`);
    console.log(`平均実行時間: ${(duration / 1000).toFixed(2)}ms/回`);

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
testImprovedSkuSearch();
