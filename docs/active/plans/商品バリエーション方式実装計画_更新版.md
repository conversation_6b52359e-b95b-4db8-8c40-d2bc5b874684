# 商品バリエーション方式実装計画（更新版）

## 概要

レンタル商品の料金計算をドラフトオーダー方式から商品バリエーション方式に変更する計画です。この方式では、レンタル日数に応じた8種類のバリエーションを各商品に設定し、標準的なShopifyカートとチェックアウトフローを使用します。また、レンタル商品の状態管理を在庫ユニットレベルで行うための新たな仕組みを導入します。

## 背景

現在のドラフトオーダー方式では、以下の問題が発生しています：

1. APIエンドポイントへのリクエストでCORSエラーが発生
2. 認証の問題によりドラフトオーダーの作成に失敗
3. チェックアウトページへのリダイレクトが正常に機能しない

また、現在の実装では以下の課題も存在します：

1. 同じ商品の色違いやサイズ違いなどの実際のバリエーションと、レンタル日数による価格バリエーションの二重管理が困難
2. 商品のレンタル状態（available、maintenance、damaged、unavailable）が商品ごとに管理されており、物理的な在庫ユニットごとの管理ができない

これらの問題を解決するため、より標準的なShopify機能を活用した商品バリエーション方式と、在庫ユニットレベルでの状態管理システムに切り替えます。

## 改良点

従来の計画から以下の改良を加えます：

1. **物理的な在庫ユニットの管理**:
   - 商品のメタフィールドに在庫ユニット情報をJSON形式で保存
   - 各ユニットには一意のIDと状態（available、maintenance、damaged、unavailable）を設定
   - ユニットレベルで状態とメンテナンス履歴を管理

2. **階層的商品管理構造**:
   - マスター商品（グループ）→ 実物バリエーション（色・サイズなど）→ レンタル日数バリエーション（価格）
   - 複数の実物バリエーションを1つの商品として表示しつつ、バックエンドでは個別に管理

3. **統合ユーザーインターフェース**:
   - コレクションページではシリーズごとに統合表示
   - 商品詳細ページでは色やサイズなどのバリエーションを直感的に選択可能
   - 日付選択に基づいて適切なレンタル日数バリエーションを自動選択

## 料金計算ロジック

レンタル料金は以下のルールに基づいて計算されます：

1. **1日目**: 基本料金の100%
2. **2〜7日目**: 各日基本料金の20%
3. **8日目以降**: 各日基本料金の10%
4. **仮予約**: 基本料金の10%

これに基づき、以下の8種類のバリエーションを設定します：

1. **1日レンタル**: 基本料金の100%
2. **2日レンタル**: 基本料金の100% + 20% = 120%
3. **3日レンタル**: 基本料金の100% + 20% + 20% = 140%
4. **4日レンタル**: 基本料金の100% + 20% + 20% + 20% = 160%
5. **5日レンタル**: 基本料金の100% + 20% + 20% + 20% + 20% = 180%
6. **6日レンタル**: 基本料金の100% + 20% + 20% + 20% + 20% + 20% = 200%
7. **7日レンタル**: 基本料金の100% + 20% + 20% + 20% + 20% + 20% + 20% = 220%
8. **8日以上のレンタル**: 7日レンタルを基本として、8日目以降は1日あたり基本料金の10%を追加料金として計算

## 日数計算ロジック

レンタル日数の計算では、以下の日を除外します：

1. **日曜日**: 営業日数に含めない
2. **指定休業日**: 営業日数に含めない
3. **年末年始(12/29〜1/4)**: 営業日数に含めない

これにより、実際のレンタル期間（開始日から終了日まで）と営業日数（料金計算の基準となる日数）が異なる場合があります。

## データモデル

### 在庫ユニット管理

```javascript
// メタフィールド: rental.inventory_units
{
  "units": [
    {
      "unit_id": "TABLE-001-01",
      "status": "available", // available, maintenance, damaged, unavailable
      "location": "Tokyo",
      "last_maintenance_date": "2024-05-01",
      "notes": "新品同様の状態"
    },
    {
      "unit_id": "TABLE-001-02",
      "status": "maintenance",
      "location": "Tokyo",
      "last_maintenance_date": "2024-04-15",
      "notes": "脚部の修理中"
    }
  ]
}
```

### 商品階層とシリーズ管理

```javascript
// 実物バリエーション商品のメタフィールド
{
  "rental.series_code": "TABLE-001",
  "rental.variation_type": "color",
  "rental.variation_value": "black"
}
```

### 在庫カレンダー（データベース）

```javascript
// 在庫カレンダーモデル
model InventoryCalendar {
  id                String          @id @default(cuid())
  shop              String
  shopifyProductId  String          // Shopify商品ID
  unitId            String          // 在庫ユニットID
  date              DateTime        // 対象日
  isAvailable       Boolean         @default(true) // 利用可能フラグ
  unavailableReason String?         // 利用不可の理由（予約中、メンテナンス中など）

  // タイムスタンプ
  createdAt         DateTime        @default(now()) @map("created_at")
  updatedAt         DateTime        @updatedAt @map("updated_at")

  @@unique([shop, shopifyProductId, unitId, date])
  @@index([shop, date])
  @@map("inventory_calendar")
}
```

## 実装計画

### 1. データモデル更新

1. **メタフィールド定義の更新**:
   - `rental.inventory_units` JSONメタフィールドを新設
   - `rental.series_code`、`rental.variation_type`、`rental.variation_value` メタフィールドを追加

2. **データベーススキーマの更新**:
   - 在庫カレンダーモデルに `unitId` フィールドを追加
   - インベントリユニットモデルを新設

### 2. APIエンドポイント実装

1. **在庫ユニット管理API**:
   - 在庫ユニットの状態更新
   - メンテナンス履歴の記録

2. **在庫カレンダー管理API**:
   - 在庫ユニットの利用可能日管理
   - 予約状況とメンテナンス予定の反映

3. **商品バリエーション管理API**:
   - 商品階層とシリーズ情報の管理
   - レンタル日数バリエーションの自動設定

### 3. フロントエンド実装

1. **コレクションページ更新**:
   - シリーズごとに商品をグループ化して表示
   - バリエーション情報の簡潔な表示（色スウォッチなど）

2. **商品詳細ページ更新**:
   - バリエーション選択UIの実装（色・サイズなど）
   - 日付選択と在庫確認の連携
   - レンタル日数に基づくバリエーション自動選択

3. **カート機能の更新**:
   - 標準的なShopifyカートの利用
   - レンタル情報のカートアイテムプロパティへの保存

### 4. 管理画面実装

1. **在庫ユニット管理画面**:
   - ユニット一覧と状態管理
   - QRコード/バーコードスキャン機能

2. **メンテナンス管理画面**:
   - メンテナンススケジュール管理
   - メンテナンス履歴の記録

## 実装ステップ

### フェーズ1: 基本実装

1. **データモデルの更新** (1週間)
   - メタフィールド定義更新
   - データベーススキーマ更新
   - 既存データの移行スクリプト作成

2. **在庫ユニット管理API実装** (1週間)
   - 基本的なCRUD操作の実装
   - Shopifyメタフィールドとの同期機能

3. **フロントエンド基本機能実装** (2週間)
   - 商品詳細ページの更新
   - 日付選択と在庫確認機能
   - カート機能の更新

### フェーズ2: 拡張機能

4. **管理画面実装** (2週間)
   - 在庫ユニット管理画面
   - バーコード/QRコードスキャン機能

5. **カレンダーと予約管理の統合** (1週間)
   - 在庫カレンダーの視覚化
   - 予約とメンテナンスの統合表示

6. **データ同期と整合性機能** (1週間)
   - 定期的な整合性チェック
   - エラー検出と修復機能

### フェーズ3: 最適化とテスト

7. **パフォーマンス最適化** (1週間)
   - キャッシング実装
   - APIリクエスト最適化

8. **テストと検証** (2週間)
   - 単体テスト
   - 統合テスト
   - 負荷テスト

## 留意点

1. **データ移行計画**:
   - 既存の商品データを新しい構造に移行する計画を慎重に立てる
   - データ移行中のサービス影響を最小限に抑える

2. **運用への配慮**:
   - 管理者向けのドキュメント作成
   - トレーニングセッションの実施

3. **パフォーマンスのモニタリング**:
   - APIリクエスト数とレスポンス時間の監視
   - データサイズの増加傾向の把握

## メリット

1. **より柔軟な在庫管理**:
   - 物理的な在庫ユニットごとの状態管理が可能に
   - 詳細なメンテナンス履歴の追跡

2. **シンプルなユーザー体験**:
   - 標準的なShopifyのカート機能を使用
   - 直感的なバリエーション選択

3. **拡張性の向上**:
   - 将来的な機能追加がしやすい構造
   - データモデルの拡張が容易

## デメリット

1. **実装の複雑性**:
   - より多くのコード変更が必要
   - テストの複雑化

2. **パフォーマンスへの懸念**:
   - メタフィールドのJSONデータサイズ増加
   - データベースクエリの増加

3. **運用の複雑化**:
   - 管理者の学習コスト
   - データ不整合発生時のトラブルシューティング

## 結論

商品バリエーション方式への移行と在庫ユニットレベルでの状態管理の導入により、現在の課題を解決し、より柔軟で拡張性の高いシステムを構築することが可能です。実装の複雑性は増しますが、長期的な保守性と機能拡張性を考慮すると、この方向性が最適と考えられます。

段階的な実装アプローチを取り、各フェーズごとにテストと検証を行うことで、リスクを最小限に抑えながら移行を進めます。