{"timestamp": "2025-05-19T08:13:05.021Z", "duration": 32.272, "totalTests": 10, "successCount": 8, "failureCount": 2, "tests": [{"testName": "年末年始の予約テスト", "success": true, "timestamp": "2025-05-19T08:13:10.742Z", "message": "年末年始の予約が正常に作成されました", "error": "", "duration": 4624, "details": {"bookingId": "0644cf13-693e-4392-9196-4434e48a052f", "period": "年末年始", "startDate": "2025-12-29", "endDate": "2026-01-03"}}, {"testName": "予約期間の最小値テスト", "success": true, "timestamp": "2025-05-19T08:13:11.767Z", "message": "最小予約期間（1日）の予約が正常に作成されました", "error": "", "duration": 1024, "details": {"bookingId": "47f0bc0c-d11d-4022-a924-b1ead4f8584b", "durationDays": 1}}, {"testName": "予約期間の最大値テスト", "success": true, "timestamp": "2025-05-19T08:13:25.644Z", "message": "最大予約期間（30日）の予約が正常に作成されました", "error": "", "duration": 13877, "details": {"bookingId": "de58b153-328f-4b66-9044-8ab5f74a3a10", "durationDays": 30}}, {"testName": "過去の日付での予約テスト", "success": true, "timestamp": "2025-05-19T08:13:29.038Z", "message": "過去の日付での予約は正しくエラーになりました", "error": "過去の日付での予約がエラーにならずに成功してしまいました", "duration": 2363, "details": {"expectedError": true}}, {"testName": "終了日が開始日より前の予約テスト", "success": true, "timestamp": "2025-05-19T08:13:29.563Z", "message": "終了日が開始日より前の予約は正しくエラーになりました", "error": "終了日が開始日より前の予約がエラーにならずに成功してしまいました", "duration": 525, "details": {"expectedError": true}}, {"testName": "無効なメールアドレスでの予約テスト", "success": true, "timestamp": "2025-05-19T08:13:29.795Z", "message": "無効なメールアドレスでの予約は正しくエラーになりました", "error": "", "duration": 232, "details": {"expectedError": true, "invalidEmail": "invalid-email"}}, {"testName": "在庫をゼロにするテスト", "success": false, "timestamp": "2025-05-19T08:13:32.229Z", "message": "在庫をゼロにすることに失敗しました", "error": "在庫カレンダーが見つかりません: 2025-08-27", "duration": 2429, "details": {"bookingId": "c3200a54-bb9d-4143-97c0-e90f36bffa9f"}}, {"testName": "在庫がゼロの状態で予約を試みるテスト", "success": true, "timestamp": "2025-05-19T08:13:32.455Z", "message": "在庫がゼロの状態での予約は正しくエラーになりました", "error": "", "duration": 226, "details": {"expectedError": true}}, {"testName": "予約をキャンセルして在庫を回復するテスト", "success": false, "timestamp": "2025-05-19T08:13:34.631Z", "message": "予約をキャンセルして在庫を回復することに失敗しました", "error": "在庫カレンダーが見つかりません: 2025-08-27", "duration": 2176, "details": {"bookingId": "c3200a54-bb9d-4143-97c0-e90f36bffa9f"}}, {"testName": "在庫が回復した状態で予約を試みるテスト", "success": true, "timestamp": "2025-05-19T08:13:37.000Z", "message": "在庫が回復した状態での予約が正常に作成されました", "error": "", "duration": 2369, "details": {"bookingId": "4c5e0a9f-7d0f-4c8c-8809-8dd22fb80da4"}}], "environment": {"nodeVersion": "v20.18.1", "os": "darwin arm64"}, "performance": {"averageTestDuration": 2984.5, "slowestTest": {"name": "予約期間の最大値テスト", "duration": 13877}, "fastestTest": {"name": "在庫がゼロの状態で予約を試みるテスト", "duration": 226}}}