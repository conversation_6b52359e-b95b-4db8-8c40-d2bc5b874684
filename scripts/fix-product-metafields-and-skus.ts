import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();

// Shopify API設定
const config = {
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block',
  apiSecretKey: process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
};

// 設定の検証
if (!config.apiSecretKey) {
  console.error('エラー: SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません。');
  process.exit(1);
}

// ショップ名を正規化
const shopName = config.shop.replace('.myshopify.com', '');

// Shopify GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${shopName}.myshopify.com/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': config.apiSecretKey,
      'Content-Type': 'application/json',
    },
  }
);

// デフォルトのバリアントマッピング
const DEFAULT_VARIANT_MAPPING = {
  "1D": { price: 1500, deposit: 0 },
  "2D": { price: 2250, deposit: 0 },
  "3D": { price: 3000, deposit: 0 },
  "4D": { price: 3750, deposit: 0 },
  "5D": { price: 4500, deposit: 0 },
  "6D": { price: 5250, deposit: 0 },
  "7D": { price: 6000, deposit: 0 },
  "8D+": { price: 6000, deposit: 0 },
  "PROV": { price: 0, deposit: 0 }
};

// デフォルトの価格設定
const DEFAULT_PRICING = {
  base_price: 1500,
  daily_rate: 750,
  weekly_rate: 6000,
  monthly_rate: 20000,
  deposit: 0,
  purchase_price: 15000
};

async function fixProductMetafieldsAndSkus() {
  console.log("=== 商品メタフィールドとSKU修正スクリプト開始 ===\n");

  try {
    // 1. 全商品を取得
    console.log("1. 商品情報を取得中...");
    const productsQuery = `
      query GetAllProducts {
        products(first: 250) {
          edges {
            node {
              id
              title
              handle
              variants(first: 20) {
                edges {
                  node {
                    id
                    sku
                    displayName
                    title
                  }
                }
              }
              metafields(first: 20) {
                edges {
                  node {
                    id
                    namespace
                    key
                    value
                  }
                }
              }
            }
          }
        }
      }
    `;

    const productsData = await client.request(productsQuery);
    
    if (!productsData || !productsData.products) {
      console.error("商品データが取得できませんでした");
      return;
    }

    const products = productsData.products.edges;
    console.log(`✓ ${products.length}個の商品を取得しました\n`);

    // 2. 各商品のメタフィールドとSKUを修正
    console.log("2. メタフィールドとSKUを修正中...\n");
    
    for (const { node: product } of products) {
      console.log(`商品: ${product.title}`);
      
      // ハンドルから商品コードを抽出
      const match = product.handle.match(/(\d{8})/);
      const productCode = match ? match[1] : product.handle;
      console.log(`  商品コード: ${productCode}`);
      
      // 既存のメタフィールドをチェック
      const existingMetafields = {};
      for (const { node: metafield } of product.metafields.edges) {
        if (metafield.namespace === 'custom') {
          existingMetafields[metafield.key] = metafield;
        }
      }

      // 必要なメタフィールドを設定
      const metafieldsToSet = [];
      
      // variant_mapping
      if (!existingMetafields.variant_mapping) {
        metafieldsToSet.push({
          namespace: 'custom',
          key: 'variant_mapping',
          value: JSON.stringify(DEFAULT_VARIANT_MAPPING),
          type: 'json'
        });
        console.log("  ✓ variant_mappingを追加");
      }
      
      // pricing
      if (!existingMetafields.pricing) {
        metafieldsToSet.push({
          namespace: 'custom',
          key: 'pricing',
          value: JSON.stringify(DEFAULT_PRICING),
          type: 'json'
        });
        console.log("  ✓ pricingを追加");
      }
      
      // status
      if (!existingMetafields.status) {
        metafieldsToSet.push({
          namespace: 'custom',
          key: 'status',
          value: 'available',
          type: 'single_line_text_field'
        });
        console.log("  ✓ statusを追加");
      }
      
      // product_group
      if (!existingMetafields.product_group) {
        metafieldsToSet.push({
          namespace: 'custom',
          key: 'product_group',
          value: productCode,
          type: 'single_line_text_field'
        });
        console.log("  ✓ product_groupを追加");
      }
      
      // general_notes
      if (!existingMetafields.general_notes) {
        metafieldsToSet.push({
          namespace: 'custom',
          key: 'general_notes',
          value: 'レンタル商品',
          type: 'multi_line_text_field'
        });
        console.log("  ✓ general_notesを追加");
      }

      // メタフィールドを設定
      if (metafieldsToSet.length > 0) {
        const metafieldMutation = `
          mutation productUpdate($input: ProductInput!) {
            productUpdate(input: $input) {
              product {
                id
              }
              userErrors {
                field
                message
              }
            }
          }
        `;

        const metafieldVariables = {
          input: {
            id: product.id,
            metafields: metafieldsToSet
          }
        };

        try {
          const metafieldResult = await client.request(metafieldMutation, metafieldVariables);
          if (metafieldResult.productUpdate?.userErrors?.length > 0) {
            console.log(`  ❌ メタフィールドエラー:`, metafieldResult.productUpdate.userErrors);
          } else {
            console.log(`  ✅ ${metafieldsToSet.length}個のメタフィールドを設定しました`);
          }
        } catch (error) {
          console.log(`  ❌ メタフィールド設定エラー:`, error.message);
        }
      }

      // バリアントのSKUを設定
      console.log("  バリアントのSKU設定:");
      for (const { node: variant } of product.variants.edges) {
        if (!variant.sku) {
          // バリアントタイトルから期間を抽出
          let variantSuffix = '1D'; // デフォルト
          if (variant.title.includes('1日')) {
            variantSuffix = '1D';
          } else if (variant.title.includes('2日')) {
            variantSuffix = '2D';
          } else if (variant.title.includes('3日')) {
            variantSuffix = '3D';
          } else if (variant.title.includes('4日')) {
            variantSuffix = '4D';
          } else if (variant.title.includes('5日')) {
            variantSuffix = '5D';
          } else if (variant.title.includes('6日')) {
            variantSuffix = '6D';
          } else if (variant.title.includes('7日')) {
            variantSuffix = '7D';
          } else if (variant.title.includes('8日以上')) {
            variantSuffix = '8D+';
          } else if (variant.title.includes('仮予約')) {
            variantSuffix = 'PROV';
          }

          const newSku = `${productCode}-001-${variantSuffix}`;
          
          const skuMutation = `
            mutation productVariantUpdate($input: ProductVariantInput!) {
              productVariantUpdate(input: $input) {
                productVariant {
                  id
                  sku
                }
                userErrors {
                  field
                  message
                }
              }
            }
          `;

          const skuVariables = {
            input: {
              id: variant.id,
              sku: newSku
            }
          };

          try {
            const skuResult = await client.request(skuMutation, skuVariables);
            if (skuResult.productVariantUpdate?.userErrors?.length > 0) {
              console.log(`    ❌ SKUエラー (${variant.title}):`, skuResult.productVariantUpdate.userErrors);
            } else {
              console.log(`    ✅ SKU設定: ${newSku} (${variant.title})`);
            }
          } catch (error) {
            console.log(`    ❌ SKU設定エラー:`, error.message);
          }
        } else {
          console.log(`    ✓ SKU既存: ${variant.sku} (${variant.title})`);
        }
      }
      
      console.log(""); // 空行
    }

    console.log("=== 修正完了 ===");
    
  } catch (error) {
    console.error("スクリプトエラー:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
fixProductMetafieldsAndSkus().catch((error) => {
  console.error("実行エラー:", error);
  process.exit(1);
});