/**
 * 料金計算ユーティリティ
 *
 * レンタル料金の計算に関する関数を提供します
 */

import { differenceInDays, addDays, isSunday, isEqual, format } from 'date-fns';

/**
 * レンタル日数を計算する
 *
 * @param startDate 開始日
 * @param endDate 終了日
 * @returns レンタル日数
 */
export function calculateRentalDays(startDate: Date, endDate: Date): number {
  // 日付が同じ場合は1日とする
  if (isEqual(startDate, endDate)) {
    return 1;
  }

  // 日数の差を計算（終了日も含める）
  return differenceInDays(endDate, startDate) + 1;
}

/**
 * レンタル料金を計算する
 *
 * @param startDate 開始日
 * @param endDate 終了日
 * @param basePrice 基本料金
 * @param pricingInfo 料金設定情報（オプション）
 * @param holidayConfig 休日設定（オプション）
 * @returns 計算されたレンタル料金
 */
export function calculateRentalPrice(
  startDate: Date,
  endDate: Date,
  basePrice: number,
  pricingInfo?: any,
  holidayConfig?: {
    holidays?: Date[],
    specialDays?: Array<{date: Date, rate: number, name: string}>
  }
): number {
  try {
    console.log('=== 料金計算開始 ===');
    console.log('開始日:', startDate);
    console.log('終了日:', endDate);
    console.log('基本料金:', basePrice);

    // レンタル日数を計算
    const days = calculateRentalDays(startDate, endDate);
    console.log('レンタル日数:', days);

    // 料金設定から割引率を取得
    const day2To6Rate = pricingInfo?.discountRules?.day2_6_rate || 0.2; // デフォルト: 20%
    const day7PlusRate = pricingInfo?.discountRules?.day7_plus_rate || 0.1; // デフォルト: 10%
    console.log('2-6日目料率:', day2To6Rate);
    console.log('7日目以降料率:', day7PlusRate);

    // 基本料金（1日目）
    let totalPrice = basePrice;
    console.log('1日目料金:', totalPrice);

    // 2日目以降の料金を計算
    if (days > 1) {
      // 2-6日目: 基本料金 × 日数 × 料率
      const day2to6Count = Math.min(days - 1, 5); // 最大5日間（2日目〜6日目）
      console.log('2-6日目の日数:', day2to6Count);

      for (let i = 0; i < day2to6Count; i++) {
        const additionalPrice = basePrice * day2To6Rate;
        console.log(`${i + 2}日目料金:`, additionalPrice);
        totalPrice += additionalPrice;
      }

      // 7日目以降: 基本料金 × 日数 × 料率
      if (days > 6) {
        const day7PlusCount = days - 6;
        console.log('7日目以降の日数:', day7PlusCount);

        for (let i = 0; i < day7PlusCount; i++) {
          const additionalPrice = basePrice * day7PlusRate;
          console.log(`${i + 7}日目料金:`, additionalPrice);
          totalPrice += additionalPrice;
        }
      }
    }

    console.log('計算前の合計:', totalPrice);

    // 特別料金日の処理
    if (holidayConfig?.specialDays && holidayConfig.specialDays.length > 0) {
      // 日付範囲内の各日をチェック
      let currentDate = new Date(startDate);
      const endDateValue = new Date(endDate);

      // 特別料金の追加分
      let specialDaysSurcharge = 0;

      while (currentDate <= endDateValue) {
        // 特別料金日かどうかをチェック
        const specialDay = holidayConfig.specialDays.find(day => {
          // 日付を文字列形式で比較
          const currentDateStr = format(currentDate, 'yyyy-MM-dd');
          const specialDateStr = day.date instanceof Date
            ? format(day.date, 'yyyy-MM-dd')
            : format(new Date(day.date), 'yyyy-MM-dd');

          return currentDateStr === specialDateStr;
        });

        if (specialDay) {
          // 特別料金日の場合、基本料金に特別料金率を適用（差分のみ）
          const dayRate = specialDay.rate || 1.5; // デフォルト1.5倍
          specialDaysSurcharge += basePrice * (dayRate - 1);
        }

        // 次の日に進む
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // 特別料金の追加分を加算
      totalPrice += specialDaysSurcharge;
    }

    const finalPrice = Math.round(totalPrice);
    console.log('最終料金:', finalPrice);
    console.log('=== 料金計算終了 ===');

    return finalPrice;
  } catch (error) {
    console.error('料金計算エラー:', error);
    // エラーが発生した場合はデフォルト料金を返す
    const fallbackPrice = basePrice * calculateRentalDays(startDate, endDate);
    console.log('フォールバック料金:', fallbackPrice);
    return fallbackPrice;
  }
}

/**
 * デポジット金額を計算する
 *
 * @param totalPrice 合計金額
 * @param depositRate デポジット率（デフォルト: 0.1 = 10%）
 * @returns デポジット金額
 */
export function calculateDeposit(totalPrice: number, depositRate: number = 0.1): number {
  return Math.round(totalPrice * depositRate);
}

/**
 * 残額を計算する
 *
 * @param totalPrice 合計金額
 * @param depositAmount デポジット金額
 * @returns 残額
 */
export function calculateRemainingAmount(totalPrice: number, depositAmount: number): number {
  return totalPrice - depositAmount;
}

/**
 * 日付が予約可能かどうかを判定する
 *
 * @param date 判定する日付
 * @returns 予約可能な場合true
 */
export function isDateBookable(date: Date): boolean {
  // 日曜日は予約開始日・終了日として選択不可
  if (isSunday(date)) {
    return false;
  }

  // 過去の日付は選択不可
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  if (date < today) {
    return false;
  }

  // TODO: 祝日チェックを追加

  return true;
}

/**
 * 日付範囲が予約可能かどうかを判定する
 *
 * @param startDate 開始日
 * @param endDate 終了日
 * @returns 予約可能な場合true
 */
export function isDateRangeBookable(startDate: Date, endDate: Date): boolean {
  // 開始日と終了日が予約可能かチェック
  if (!isDateBookable(startDate) || !isDateBookable(endDate)) {
    return false;
  }

  // 開始日が終了日より後の場合はNG
  if (startDate > endDate) {
    return false;
  }

  return true;
}
