# エッジケースと境界値テストレポート

## テスト概要

このレポートは、年末年始や連休などの特殊期間の予約、予約期間の最大・最小値、在庫数ゼロの状態からの回復、無効なデータ入力時の処理のテスト結果をまとめたものです。

テスト実行日時: 2025/5/19 17:13:05
実行時間: 32.27秒

## テスト環境

- Node.js バージョン: v20.18.1
- OS: darwin arm64

## テスト結果

- 合計テスト数: 10
- 成功: 8
- 失敗: 2
- 成功率: 80%

## パフォーマンス分析

- 平均テスト実行時間: 2984.50ミリ秒
- 最も遅いテスト: 予約期間の最大値テスト (13877.00ミリ秒)
- 最も速いテスト: 在庫がゼロの状態で予約を試みるテスト (226.00ミリ秒)

## 詳細結果

### テストケース1: 年末年始の予約テスト

- 結果: ✅ 成功
- メッセージ: 年末年始の予約が正常に作成されました

- 実行時間: 4624.00ミリ秒
- 詳細:
```json
{
  "bookingId": "0644cf13-693e-4392-9196-4434e48a052f",
  "period": "年末年始",
  "startDate": "2025-12-29",
  "endDate": "2026-01-03"
}
```

### テストケース2: 予約期間の最小値テスト

- 結果: ✅ 成功
- メッセージ: 最小予約期間（1日）の予約が正常に作成されました

- 実行時間: 1024.00ミリ秒
- 詳細:
```json
{
  "bookingId": "47f0bc0c-d11d-4022-a924-b1ead4f8584b",
  "durationDays": 1
}
```

### テストケース3: 予約期間の最大値テスト

- 結果: ✅ 成功
- メッセージ: 最大予約期間（30日）の予約が正常に作成されました

- 実行時間: 13877.00ミリ秒
- 詳細:
```json
{
  "bookingId": "de58b153-328f-4b66-9044-8ab5f74a3a10",
  "durationDays": 30
}
```

### テストケース4: 過去の日付での予約テスト

- 結果: ✅ 成功
- メッセージ: 過去の日付での予約は正しくエラーになりました
- エラー: 過去の日付での予約がエラーにならずに成功してしまいました
- 実行時間: 2363.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース5: 終了日が開始日より前の予約テスト

- 結果: ✅ 成功
- メッセージ: 終了日が開始日より前の予約は正しくエラーになりました
- エラー: 終了日が開始日より前の予約がエラーにならずに成功してしまいました
- 実行時間: 525.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース6: 無効なメールアドレスでの予約テスト

- 結果: ✅ 成功
- メッセージ: 無効なメールアドレスでの予約は正しくエラーになりました

- 実行時間: 232.00ミリ秒
- 詳細:
```json
{
  "expectedError": true,
  "invalidEmail": "invalid-email"
}
```

### テストケース7: 在庫をゼロにするテスト

- 結果: ❌ 失敗
- メッセージ: 在庫をゼロにすることに失敗しました
- エラー: 在庫カレンダーが見つかりません: 2025-08-27
- 実行時間: 2429.00ミリ秒
- 詳細:
```json
{
  "bookingId": "c3200a54-bb9d-4143-97c0-e90f36bffa9f"
}
```

### テストケース8: 在庫がゼロの状態で予約を試みるテスト

- 結果: ✅ 成功
- メッセージ: 在庫がゼロの状態での予約は正しくエラーになりました

- 実行時間: 226.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース9: 予約をキャンセルして在庫を回復するテスト

- 結果: ❌ 失敗
- メッセージ: 予約をキャンセルして在庫を回復することに失敗しました
- エラー: 在庫カレンダーが見つかりません: 2025-08-27
- 実行時間: 2176.00ミリ秒
- 詳細:
```json
{
  "bookingId": "c3200a54-bb9d-4143-97c0-e90f36bffa9f"
}
```

### テストケース10: 在庫が回復した状態で予約を試みるテスト

- 結果: ✅ 成功
- メッセージ: 在庫が回復した状態での予約が正常に作成されました

- 実行時間: 2369.00ミリ秒
- 詳細:
```json
{
  "bookingId": "4c5e0a9f-7d0f-4c8c-8809-8dd22fb80da4"
}
```

