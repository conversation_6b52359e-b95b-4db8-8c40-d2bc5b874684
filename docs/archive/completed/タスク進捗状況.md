# Shopifyレンタル商品ECシステム タスク進捗状況


**最終更新日**: 2025-05-09

## 優先度：最高（即時対応）

### 日程管理のデータベース移行
- [x] レンタル可能期間モデル（RentalAvailability）の実装
- [x] 在庫カレンダーモデル（InventoryCalendar）の実装
- [x] 既存データの移行スクリプトの作成と実行

### 予約管理APIの更新
- [x] 新しいモデルを使用した予約管理APIの更新
- [x] 利用可能状況確認APIの更新
- [x] レンタル可能期間管理APIの実装
- [x] 在庫カレンダー管理APIの実装

### カレンダーコンポーネントの更新
- [x] 新しいAPIを使用したカレンダーコンポーネントの更新
- [x] 在庫状況の視覚化機能の強化

### メタフィールドの整理と移行
- [x] JSONメタフィールドから単純なメタフィールドへの移行
- [x] 使用中のJSONメタフィールド（基本情報、料金設定）の維持
- [x] 移行スクリプトの作成

### 統一カレンダーコンポーネントの実装
- [x] 統一カレンダーコンポーネントの基本実装
- [x] 予約状況表示の改善
- [x] 日付選択UIの改善
- [x] モバイル対応の強化
- [x] テーマアプリブロックでの予約状況表示の実装
- [x] APIとの連携による実際の予約データの表示

### APIエンドポイントの整備
- [x] 予約データの取得・作成・更新・削除用APIの実装
- [x] 在庫状態の取得・更新用APIの実装
- [x] バリエーション情報取得APIの実装
- [x] 商品予約状況取得APIの実装
- [x] テスト用予約データ作成APIの実装
- [x] エラーハンドリングの改善

### APIテスト
- [x] 商品予約状況取得APIのテスト実装
- [x] テスト用予約データ作成スクリプトの実装
- [x] APIテスト用スクリプトの実装
- [x] テーマアプリブロックとAPIの連携テスト

### 配送管理機能の実装
- [ ] 配送スケジュール管理APIの実装
- [ ] ピッキングリスト生成機能の実装
- [ ] 返品管理機能の実装
- [ ] 配送料金計算機能の実装
- [ ] Google Sheets連携機能の実装

## 優先度：高（1-2週間以内）

### 管理画面の実装
- [x] 予約一覧・詳細画面
- [ ] 予約ステータス変更機能
- [ ] 予約検索・フィルタリング機能
- [ ] レンタル可能期間管理画面
- [ ] 在庫カレンダー管理画面

### 管理画面の実装
- [x] 予約一覧・詳細画面
- [ ] 予約ステータス変更機能
- [ ] 予約検索・フィルタリング機能
- [ ] レンタル可能期間管理画面
- [ ] 在庫カレンダー管理画面
- [ ] 配送管理画面

### 料金計算ロジックの実装
- [x] 特殊料金計算ユーティリティ
- [x] デポジット計算ユーティリティ
- [x] 料金計算フック
- [x] フロントエンドとバックエンドで共通の料金計算ロジックの統一
- [x] 包括的なテストスクリプトの作成と実行

### Shopify連携機能の実装
- [x] Draft Orders APIを使用した注文作成
- [ ] 注文と予約の連携
- [ ] 注文ステータス変更時の予約ステータス更新

## 優先度：中（1ヶ月以内）

### 通知システムの実装
- [x] メール送信基盤
- [x] メールテンプレート
- [x] メール送信サービス
- [x] 通知API

### レポート機能の実装
- [ ] 予約状況レポート
- [ ] 売上レポート
- [ ] 商品利用率レポート

### ユーザー認証・権限管理
- [x] 管理者権限設定
- [ ] スタッフ権限設定
- [ ] 権限に基づく機能制限

## 優先度：低（時間があれば）

### 複数請求先・配送先の管理機能
- [ ] 複数請求先管理モデルの実装
- [ ] 複数配送先管理モデルの実装
- [ ] 顧客管理画面での複数請求先・配送先管理UIの実装
- [ ] ユーザー画面での請求先・配送先の追加・削除機能の実装
- [ ] 注文作成時の請求先選択機能の実装
- [ ] 配送スケジュール作成時の配送先選択機能の実装

### 多言語対応
- [ ] 日本語・英語の切り替え
- [ ] 言語ファイルの整備

### モバイル対応の強化
- [x] レスポンシブデザインの最適化
- [ ] モバイル特有の機能追加

### パフォーマンス最適化
- [ ] クエリの最適化
- [ ] キャッシュ戦略の実装
- [ ] ページロード時間の短縮

## 技術的負債解消

### コードリファクタリング
- [x] 重複コードの排除
- [x] コンポーネントの再利用性向上
- [x] 型定義の強化

### テスト強化
- [x] 単体テストの追加（料金計算ロジック）
- [x] 統合テストの追加（日程管理と料金計算の連携）
- [ ] E2Eテストの追加
- [x] すべての組み合わせをテストする包括的なスクリプトの作成
- [x] 祝日判定ロジックのテスト追加
- [x] 祝日を挟んだ予約の料金計算テスト追加
- [x] 休日を挟んだレンタルテスト追加
- [x] 長期・短期レンタルテスト追加

### ドキュメント整備
- [x] API仕様書の更新
- [ ] コードコメントの充実
- [x] 開発者向けガイドの作成
- [x] Shopifyアプリのデプロイに関するトラブルシューティングドキュメント
- [x] Shopify CLIコマンド参照ガイド（YAML形式とMarkdown形式）
- [x] freee連携機能の実装計画ドキュメント

## 完了したタスク

### プロジェクト初期設定
- [x] Remix.jsプロジェクト初期設定
- [x] Shopify App用設定ファイル作成
- [x] 基本環境変数設定
- [x] Prismaスキーマ初期設定
- [x] Tailwind CSS設定

### 基本UI実装
- [x] レンタルカレンダーコンポーネント
- [x] 日付範囲選択コンポーネント
- [x] カレンダー操作フック
- [x] 料金計算表示コンポーネント
- [x] 利用可能状態インジケーター

### 基本API実装
- [x] 空き状況確認API
- [x] レンタル料金計算API
- [x] デポジット計算API
- [x] 予約作成API
- [x] 仮予約作成API

### 検索機能実装
- [x] 請求書検索フォームの実装
- [x] 請求書検索結果表示の実装
- [x] 検索ロジックの実装

## 次に取り組むべきタスク

1. **日程管理のデータベース移行**
   - レンタル可能期間モデル（RentalAvailability）の実装
     - データモデルの詳細設計
     - Prismaスキーマの更新
     - マイグレーションスクリプトの作成
   - 在庫カレンダーモデル（InventoryCalendar）の実装
     - データモデルの詳細設計
     - Prismaスキーマの更新
     - マイグレーションスクリプトの作成
   - 既存データの移行スクリプトの作成と実行
     - メタフィールドからデータベースへの移行スクリプト
     - データ整合性の検証

2. **予約管理APIの更新**
   - 新しいモデルを使用した予約管理APIの更新
     - 予約作成・更新・削除APIの更新
     - 予約検索APIの更新
     - 予約状態管理APIの更新
   - 利用可能状況確認APIの更新
     - 新しいデータモデルに基づく利用可能状況確認ロジックの実装
     - パフォーマンス最適化
   - レンタル可能期間管理APIの実装
   - 在庫カレンダー管理APIの実装

3. **カレンダーUIの検証と実装**
   - カレンダーライブラリの検証
     - Polarisのdatepicker
     - react-big-calendar
     - FullCalendar
     - react-calendar
     - データ連携テスト
   - 最適なカレンダーUIの選定と実装
     - 予約状況の視覚化
     - 日付範囲選択機能
     - 在庫状態の表示

4. **メタフィールドの整理と移行** (完了)
   - [x] JSONメタフィールドから単純なメタフィールドへの移行
   - [x] 使用中のJSONメタフィールド（基本情報、料金設定）の維持
   - [x] 移行スクリプトの作成と実行

5. **配送管理機能の実装**
   - 配送スケジュール管理APIの実装
     - 配送スケジュール一覧取得API
     - 配送スケジュール詳細取得API
     - 配送スケジュール作成・更新・削除API
     - 配送スケジュール検索・フィルタリング機能
   - ピッキングリスト生成機能の実装
     - 日付別ピッキングリスト取得API
     - ピッキング状態更新API
     - ピッキングリストPDF出力機能
     - ピッキング管理画面の実装

## インフラストラクチャ構成の決定

サーバー・データベース構成について、以下の理由から**Neon PostgreSQL + Render/Cloudflare**の構成を維持することを決定しました：

1. **既存の投資保護**
   - 既に複雑なPrismaスキーマが実装されており、Gadget.ioへの移行には大幅な再設計が必要

2. **カスタマイズ要件**
   - レンタル商品ECシステムには特殊な料金計算や予約管理など、カスタマイズが必要な機能が多い

3. **コスト効率**
   - 現在の構成は使用量に応じたスケーリングが可能で、初期コストを抑えられる

4. **技術的独立性**
   - 特定のプラットフォームに依存せず、将来の拡張性を確保できる

詳細は[README.md - プロジェクト概要.md](./README.md - プロジェクト概要.md)と[開発の始め方ガイド.md](.開発の始め方ガイド.md)に記載しました。

## 最近の進捗

- 祝日判定ロジックのテストを追加 (2025-05-09)
- 祝日を挟んだ予約の料金計算テストを追加 (2025-05-09)
- 休日を挟んだレンタルテストを追加 (2025-05-09)
- 長期・短期レンタルテストを追加 (2025-05-09)
- 予約システム本番環境展開手順書を作成 (2025-05-09)
- 包括的なレンタルテスト実行スクリプトを作成 (2025-05-09)
- フロントエンドとバックエンドで共通の料金計算ロジックを統一 (2025-05-01)
- すべての組み合わせをテストする包括的なスクリプトを作成 (2025-05-01)
- 予約の重複チェック機能を修正 (2025-05-01)
- 仮予約・本予約の選択機能を追加 (2025-05-01)
- 料金計算ロジックの単体テストを追加 (2025-05-01)
- 日程管理と料金計算の連携テストを追加 (2025-05-01)
- freee連携機能の実装計画ドキュメントを作成 (2024-04-30)
- freee連携のPoCスクリプトを作成 (2024-04-30)
- Shopify CLIコマンド参照ガイドを作成（YAML形式とMarkdown形式） (2024-04-30)
- Shopifyアプリのデプロイに関するトラブルシューティングドキュメントを作成 (2024-04-30)
- カレンダーUIの改善を実施（モバイル対応、アクセシビリティ向上、色のコントラスト改善） (2024-04-16)
- UnifiedCalendarコンポーネントを拡張し、DisplayCalendarとSimpleCalendarの機能を統合 (2024-04-15)
- 表示モード（表示専用/選択可能）の切り替え機能を追加 (2024-04-15)
- メンテナンス期間の表示に対応 (2024-04-15)
- 複数月表示に対応 (2024-04-15)
- カレンダーの月切り替え時にマーカーが消える問題を修正 (2024-04-10)
- カレンダーマーカーの表示問題を修正（属性セレクタを使用） (2024-04-10)
- カレンダー改善タスクの進捗状況と次のタスク候補をドキュメント化 (2024-04-10)
- ドラフトオーダーAPIの実装 (2024-03-21)
- レンタル日程選択ブロックの実装 (2024-03-20)
- 料金計算ロジックの修正 (2024-03-20)
- 予約管理画面の実装 (2024-03-19)
- 日程管理のデータベース移行の実装完了 (2024-03-15)
- インフラストラクチャ構成の検討と決定 (2024-03-10)
- 配送管理機能の実装計画の策定 (2024-03-05)
- 配送管理データモデルの設計と実装 (2024-03-01)
- 配送料金モデルのサイズベース対応 (2024-02-25)
- 商品サイズメタフィールドの設計と実装 (2024-02-20)
- メタフィールド移行スクリプトの作成 (2024-02-15)
- 請求書検索機能の実装 (2024-02-10)
- ドキュメントの大規模更新 (2024-02-05)

## 今後の課題

1. **日程管理のデータベース移行** (完了)
   - [x] レンタル可能期間モデル（RentalAvailability）の実装
   - [x] 在庫カレンダーモデル（InventoryCalendar）の実装
   - [x] 既存データの移行スクリプトの作成と実行

2. **予約管理APIの更新** (完了)
   - [x] 新しいモデルを使用した予約管理APIの更新
   - [x] 利用可能状況確認APIの更新
   - [x] レンタル可能期間管理APIの実装
   - [x] 在庫カレンダー管理APIの実装

3. **カレンダーUIの検証と実装** (完了)
   - [x] カレンダーライブラリの検証と選定
   - [x] 予約状況の視覚化機能の実装
   - [x] 在庫状態の表示機能の実装

4. **メタフィールドの整理** (完了)
   - [x] JSONの使用を最小限に抑え、単純なフィールドを優先
   - [x] 移行スクリプトの作成と実行

5. **Shopify連携の強化** (部分完了)
   - [x] Draft Orders APIを使用した注文作成
   - [ ] 注文と予約の連携
   - [ ] 配送料金のShopify商品連携

6. **管理画面の実装** (部分完了)
   - [x] 予約管理画面
   - [ ] 在庫カレンダー管理画面
   - [ ] 配送管理画面
   - [ ] レンタル状況管理画面
   - [ ] 入金状況管理画面
   - [ ] 顧客管理画面
   - [ ] レポート・分析画面
   - [ ] 設定画面

7. **フロントエンド機能の拡張**
   - [ ] コレクションページでの日程によるフィルター機能
     - [ ] 日付選択UIの実装
     - [ ] 選択日程に基づく商品フィルタリングロジックの実装
     - [ ] フィルター結果の表示UI
   - [ ] カートページでの日程変更機能
     - [ ] カート内商品の日程変更UI
     - [ ] 日程変更に伴う料金再計算機能
     - [ ] 変更内容の保存と反映
   - [ ] 商品ページでのレンタル予約状況カレンダー表示
     - [ ] 商品ごとの予約状況取得API
     - [ ] カレンダー表示コンポーネントの実装
     - [ ] 予約済み期間の視覚的表示

8. **マイアカウントでの複数請求先・配送先管理機能**
   - [ ] 請求先の追加・編集・削除機能（最大3件）
     - [ ] 請求先フォームの実装
     - [ ] 請求先データの保存と取得API
     - [ ] 請求先一覧表示と選択機能
   - [ ] 配送先の追加・編集・削除機能（最大3件）
     - [ ] 配送先フォームの実装
     - [ ] 配送先データの保存と取得API
     - [ ] 配送先一覧表示と選択機能
   - [ ] デフォルト請求先・配送先の設定機能

9. **配送管理機能の実装**
   - [ ] 配送スケジュール管理APIの実装
   - [ ] ピッキングリスト生成機能の実装
   - [ ] 返品管理機能の実装
   - [ ] 配送料金計算機能の実装
   - [ ] Google Sheets連携機能の実装

10. **freeeとの連携機能**
    - [ ] freee連携のPoC（概念実証）
      - [ ] freee APIの調査と認証方法の確認
        - [ ] OAuth2.0認証フローの実装
        - [ ] アクセストークンとリフレッシュトークンの管理
      - [ ] 簡易的な連携スクリプトの作成
        - [ ] 請求書API（/invoices）の呼び出しテスト
        - [ ] 会計API（/companies, /deals）の呼び出しテスト
      - [ ] 請求書作成と取得のテスト
        - [ ] 請求書テンプレートの取得と選択
        - [ ] 請求書データの構築と送信
      - [ ] 注文IDと請求IDの関連付けテスト
        - [ ] 関連付けデータモデルの設計
        - [ ] 関連付け情報の保存と取得
    - [ ] テスト用APIエンドポイントの実装
      - [ ] freee連携用のサービスクラスの作成
        - [ ] FreeeService.tsの実装
        - [ ] 認証情報の安全な管理方法の実装
      - [ ] APIエンドポイントの実装
        - [ ] 請求書作成エンドポイント
        - [ ] 請求書取得エンドポイント
        - [ ] 請求書ステータス更新エンドポイント
      - [ ] エラーハンドリングの実装
        - [ ] API呼び出しエラーの処理
        - [ ] リトライ機能の実装
    - [ ] 注文・請求連携フローの実装
      - [ ] レンタル注文と配送料注文の関連付け
        - [ ] 同一顧客の複数注文の検出
        - [ ] 関連注文の一括処理機能
      - [ ] 複数注文の一括請求機能
        - [ ] 請求書データの構築ロジック
        - [ ] 請求書明細の自動生成
      - [ ] 請求ステータスの同期機能
        - [ ] 定期的な請求ステータス確認
        - [ ] 入金状況の自動更新
    - [ ] 請求管理ダッシュボード
      - [ ] 請求状況の一覧表示
        - [ ] フィルタリングと検索機能
        - [ ] ステータス別表示
      - [ ] 入金状況の管理
        - [ ] 入金確認機能
        - [ ] 未入金アラート機能
      - [ ] 請求書の生成と送信
        - [ ] PDFダウンロード機能
        - [ ] メール送信機能

11. **ユーザーテストとフィードバック収集**
    - [ ] 実際のユーザーによるテスト
    - [ ] フィードバックに基づく改善
