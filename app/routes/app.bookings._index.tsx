/**
 * 新しい予約管理ページ
 * 改良された検索機能（日本語名前のスペース問題を解決）を実装
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  BlockStack,
  DataTable,
  Button,
  Badge,
  Pagination,
  EmptyState,
  TextField,
  ButtonGroup,
  InlineStack,
  Icon,
  Banner
} from "@shopify/polaris";
import { SearchIcon } from "@shopify/polaris-icons";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { useState } from "react";
import { ShopifyCustomerDisplay } from "../components/ShopifyCustomerDisplay";

// 検索クエリの種類を判定する関数
function detectSearchType(query: string) {
  if (!query || query.trim() === "") return 'none';

  const trimmedQuery = query.trim();

  // Shopify注文番号の判定（#で始まるか、数字のみ）
  if (trimmedQuery.startsWith('#') || /^\d+$/.test(trimmedQuery)) {
    return 'order';
  }

  // 商品SKUの判定（英数字とハイフンの組み合わせ）
  if (/^[A-Z0-9\-]+$/i.test(trimmedQuery) && trimmedQuery.includes('-')) {
    return 'product';
  }

  // メールアドレスの判定
  if (trimmedQuery.includes('@')) {
    return 'customer';
  }

  // 日本語文字が含まれている場合は顧客名検索
  if (/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(trimmedQuery)) {
    return 'customer';
  }

  // その他は汎用検索
  return 'general';
}

// 改良された検索条件を構築する関数（簡略化版）
function buildImprovedSearchCondition(query: string, shop: string): any {
  if (!query || query.trim() === "") {
    // 検索クエリがない場合は当日の予約を表示
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

    return {
      shop,
      OR: [
        {
          startDate: {
            gte: startOfDay,
            lte: endOfDay
          }
        },
        {
          endDate: {
            gte: startOfDay,
            lte: endOfDay
          }
        }
      ]
    };
  }

  const trimmedQuery = query.trim();

  // シンプルな検索条件
  return {
    shop,
    OR: [
      { customerEmail: { contains: trimmedQuery, mode: 'insensitive' } },
      { customerId: { contains: trimmedQuery, mode: 'insensitive' } },
      { bookingId: { contains: trimmedQuery, mode: 'insensitive' } },
      { shopifyOrderName: { contains: trimmedQuery, mode: 'insensitive' } },
      { shopifyOrderId: { contains: trimmedQuery, mode: 'insensitive' } },
      { customerName: { contains: trimmedQuery, mode: 'insensitive' } },
      { customerName: { contains: trimmedQuery.replace(/\s+/g, ''), mode: 'insensitive' } }
    ]
  };
}

// Loader関数
export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get("page") || "1");
  const pageSize = 20;
  const searchQuery = url.searchParams.get("q") || "";
  const statusFilter = url.searchParams.get("status") || "";
  const startDateFilter = url.searchParams.get("startDate") || "";
  const endDateFilter = url.searchParams.get("endDate") || "";

  try {
    console.log(`予約検索: "${searchQuery}", ステータス: "${statusFilter}"`);
    console.log(`検索タイプ: ${detectSearchType(searchQuery)}`);

    // 基本的な検索条件を構築
    let whereCondition = buildImprovedSearchCondition(searchQuery, shop);
    console.log('検索条件:', JSON.stringify(whereCondition, null, 2));

    // ステータスフィルターを追加
    if (statusFilter) {
      whereCondition = {
        ...whereCondition,
        status: statusFilter
      };
    }

    // 日付フィルターを追加
    if (startDateFilter) {
      whereCondition = {
        ...whereCondition,
        startDate: {
          gte: new Date(startDateFilter + 'T00:00:00.000Z')
        }
      };
    }

    if (endDateFilter) {
      whereCondition = {
        ...whereCondition,
        endDate: {
          lte: new Date(endDateFilter + 'T23:59:59.999Z')
        }
      };
    }

    // 予約データを取得
    const bookings = await prisma.booking.findMany({
      where: whereCondition,
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * pageSize,
      take: pageSize
    });

    // 総件数を取得
    const totalBookings = await prisma.booking.count({
      where: whereCondition
    });

    // データを整形
    const formattedBookings = bookings.map(booking => {
      const formatDate = (date: Date | null) => {
        if (!date) return "";
        return new Date(date).toLocaleDateString('ja-JP');
      };

      const calculateRentalDays = (start: Date | null, end: Date | null) => {
        if (!start || !end) return 0;
        const diffTime = Math.abs(end.getTime() - start.getTime());
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      };

      return {
        id: booking.id,
        bookingId: booking.bookingId || booking.id.substring(0, 8),
        productTitle: '商品情報なし',
        sku: 'SKU不明',
        productStatus: 'UNKNOWN',
        shopifyProductId: null,
        startDate: formatDate(booking.startDate),
        endDate: formatDate(booking.endDate),
        rentalDays: calculateRentalDays(booking.startDate, booking.endDate),
        status: booking.status,
        paymentStatus: booking.paymentStatus || 'PENDING',
        customerName: booking.customerName || '',
        customerEmail: booking.customerEmail || '',
        customerId: booking.customerId || '',
        totalAmount: booking.totalAmount ? `¥${booking.totalAmount.toLocaleString()}` : '',
        createdAt: formatDate(booking.createdAt),
        orderId: booking.shopifyOrderId || null,
        orderName: booking.shopifyOrderName || null
      };
    });

    // ページネーション情報
    const totalPages = Math.ceil(totalBookings / pageSize);
    const pagination = {
      currentPage: page,
      totalPages,
      totalItems: totalBookings,
      hasNext: page < totalPages,
      hasPrevious: page > 1
    };

    // フィルター情報
    const filters = {
      query: searchQuery,
      status: statusFilter,
      startDate: startDateFilter,
      endDate: endDateFilter
    };

    console.log(`検索結果: ${formattedBookings.length}件 / 総件数: ${totalBookings}件`);

    return json({
      bookings: formattedBookings,
      pagination,
      filters,
      shopifyShop: shop,
      success: url.searchParams.get("success") === "true",
      count: parseInt(url.searchParams.get("count") || "0")
    });

  } catch (error) {
    console.error("予約データ取得エラー:", error);
    return json({
      bookings: [],
      pagination: { currentPage: 1, totalPages: 0, totalItems: 0, hasNext: false, hasPrevious: false },
      filters: { query: searchQuery, status: statusFilter, startDate: startDateFilter, endDate: endDateFilter },
      shopifyShop: shop,
      error: "予約データの取得中にエラーが発生しました"
    });
  }
}

// メインコンポーネント
// 検索タイプのラベルを取得する関数
function getSearchTypeLabel(searchType: string): string {
  switch (searchType) {
    case 'order': return '注文番号検索';
    case 'product': return '商品検索';
    case 'customer': return '顧客検索';
    case 'general': return '汎用検索';
    case 'none': return '当日の予約表示';
    default: return '検索';
  }
}

export default function BookingsIndex() {
  const loaderData = useLoaderData<typeof loader>();
  const [searchValue, setSearchValue] = useState(loaderData.filters.query || "");
  const [startDate, setStartDate] = useState(loaderData.filters.startDate || "");
  const [endDate, setEndDate] = useState(loaderData.filters.endDate || "");

  const { bookings, pagination, filters, shopifyShop } = loaderData;
  const error = 'error' in loaderData ? loaderData.error : null;

  // ステータスバッジを生成（ドキュメント分析後）
  const getStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case "PROVISIONAL":
        return <Badge tone="attention">仮予約</Badge>;
      case "CONFIRMED":
        return <Badge tone="success">本予約</Badge>;
      case "IN_PROGRESS":
        return <Badge tone="info">進行中</Badge>;
      case "COMPLETED":
        return <Badge tone="success">完了</Badge>;
      case "CANCELLED":
        return <Badge tone="critical">キャンセル</Badge>;
      case "EXPIRED":
        return <Badge tone="warning">期限切れ</Badge>;
      case "DRAFT":
        // DRAFTは仮予約として表示（レガシー対応）
        return <Badge tone="attention">仮予約</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // 支払いステータスバッジを生成
  const getPaymentStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case "PENDING":
        return <Badge tone="attention">未払い</Badge>;
      case "COMPLETED":
        return <Badge tone="success">支払済</Badge>;
      case "REFUNDED":
        return <Badge tone="info">返金済</Badge>;
      case "FAILED":
        return <Badge tone="critical">失敗</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // 商品ステータスバッジを生成
  const getProductStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case "AVAILABLE":
        return <Badge tone="success">利用可能</Badge>;
      case "MAINTENANCE":
        return <Badge tone="attention">メンテナンス中</Badge>;
      case "DAMAGED":
        return <Badge tone="critical">破損</Badge>;
      case "UNAVAILABLE":
        return <Badge tone="warning">利用不可</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // 検索処理
  const handleSearch = (query?: string) => {
    const url = new URL(window.location.href);
    const searchTerm = query !== undefined ? query : searchValue;

    if (searchTerm && searchTerm !== "") {
      url.searchParams.set("q", searchTerm);
    } else {
      url.searchParams.delete("q");
    }

    url.searchParams.set("page", "1");
    window.location.href = url.toString();
  };

  // ステータスフィルター処理
  const handleStatusChange = (status: string) => {
    const url = new URL(window.location.href);
    if (status) {
      url.searchParams.set("status", status);
    } else {
      url.searchParams.delete("status");
    }
    url.searchParams.set("page", "1");
    window.location.href = url.toString();
  };

  // ページネーション処理
  const handlePageChange = (page: number) => {
    const url = new URL(window.location.href);
    url.searchParams.set("page", page.toString());
    window.location.href = url.toString();
  };

  // 日付フィルター処理
  const handleDateRangeChange = () => {
    const url = new URL(window.location.href);

    if (startDate) {
      url.searchParams.set("startDate", startDate);
    } else {
      url.searchParams.delete("startDate");
    }

    if (endDate) {
      url.searchParams.set("endDate", endDate);
    } else {
      url.searchParams.delete("endDate");
    }

    url.searchParams.set("page", "1");
    window.location.href = url.toString();
  };

  // フィルタークリア処理
  const handleClearFilters = () => {
    const url = new URL(window.location.href);
    url.searchParams.delete("q");
    url.searchParams.delete("status");
    url.searchParams.delete("startDate");
    url.searchParams.delete("endDate");
    url.searchParams.set("page", "1");
    window.location.href = url.toString();
  };

  // データテーブルの行を生成
  const rows = bookings.map(booking => [
    <Link key={`link-${booking.id}`} to={`/app/bookings/${booking.id}`}>
      {booking.bookingId}
    </Link>,
    <Text key={`sku-${booking.id}`} as="span" fontWeight="bold">
      {booking.sku}
    </Text>,
    <Text key={`title-${booking.id}`} as="span">{booking.productTitle}</Text>,
    getProductStatusBadge(booking.productStatus),
    booking.startDate,
    booking.endDate,
    <Text key={`days-${booking.id}`} as="span" fontWeight="bold">{booking.rentalDays}日</Text>,
    booking.totalAmount,
    getStatusBadge(booking.status),
    getPaymentStatusBadge(booking.paymentStatus),
    booking.orderId ? (
      <a
        href={`https://${shopifyShop}/admin/orders/${booking.orderId.replace('gid://shopify/Order/', '')}`}
        target="_blank"
        rel="noopener noreferrer"
        style={{ textDecoration: 'underline', color: '#2C6ECB' }}
      >
        {booking.orderName || booking.orderId.replace('gid://shopify/Order/', '')}
      </a>
    ) : (
      "未作成"
    ),
    booking.customerId ? (
      <ShopifyCustomerDisplay
        key={`customer-${booking.id}`}
        customerId={booking.customerId}
        fallbackName={booking.customerName}
        fallbackEmail={booking.customerEmail}
        shopifyShop={shopifyShop}
        linkToShopify={true}
      />
    ) : (
      booking.customerName || "不明"
    ),
    booking.createdAt,
  ]);

  // ステータスフィルターオプション（ドキュメント分析後）
  const statusOptions = [
    { label: "すべて", value: "" },
    { label: "仮予約", value: "PROVISIONAL" },
    { label: "本予約", value: "CONFIRMED" },
    { label: "進行中", value: "IN_PROGRESS" },
    { label: "完了", value: "COMPLETED" },
    { label: "キャンセル", value: "CANCELLED" },
    { label: "期限切れ", value: "EXPIRED" },
    // 注: DRAFTは削除（PROVISIONALと重複のため）
  ];

  return (
    <div className="booking-management-page">
      <Page
          title="予約一覧"
          subtitle="改良された検索機能付きレンタル予約管理"
          backAction={{ content: "ホーム", url: "/app" }}
          primaryAction={{
            content: "新規予約",
            url: "/app/bookings/new",
          }}
        >
        <Layout>
          <Layout.Section>
            <Card>
              <Box padding="400">
                <BlockStack gap="400">
                {/* エラー・成功メッセージ */}
                {error && (
                  <Banner tone="critical">
                    <Text as="p">{error}</Text>
                  </Banner>
                )}

                {/* 成功メッセージは削除（不要） */}

                {/* 検索セクション */}
                <Box paddingBlockEnd="400">
                  <InlineStack gap="400" align="start" blockAlign="center">
                    <div style={{ flex: 1 }}>
                      <form
                        onSubmit={(e) => {
                          e.preventDefault();
                          handleSearch(searchValue);
                        }}
                        style={{ display: 'flex', gap: '8px' }}
                      >
                        <div style={{ flex: 1 }}>
                          <TextField
                            label="検索（改良版）"
                            type="search"
                            value={searchValue}
                            placeholder="商品名、SKU、顧客名、メール、注文番号、予約IDで検索"
                            prefix={<Icon source={SearchIcon} />}
                            onClearButtonClick={() => {
                              setSearchValue("");
                              handleSearch("");
                            }}
                            onChange={(value) => {
                              setSearchValue(value);
                            }}
                            autoComplete="off"
                            helpText={
                              searchValue ?
                                `${getSearchTypeLabel(detectSearchType(searchValue))} | 日本語名前は「佐藤花子」「佐藤 花子」どちらでも検索可能` :
                                "検索なしの場合は当日の予約を表示します。Enterキーで検索開始"
                            }
                          />
                        </div>
                        <div style={{ alignSelf: 'end', marginBottom: '20px' }}>
                          <Button
                            variant="primary"
                            onClick={() => handleSearch(searchValue)}
                            submit
                          >
                            検索
                          </Button>
                        </div>
                      </form>
                    </div>

                    {/* ステータスフィルター（シンプル版） */}
                    <div style={{ minWidth: "200px" }}>
                      <div style={{ marginBottom: "8px" }}>
                        <Text as="span" variant="bodyMd" fontWeight="medium">ステータス</Text>
                      </div>
                      <select
                        value={filters.status}
                        onChange={(e) => handleStatusChange(e.target.value)}
                        style={{
                          width: "100%",
                          padding: "8px 12px",
                          borderRadius: "6px",
                          border: "1px solid #d1d5db",
                          fontSize: "14px",
                          backgroundColor: "white"
                        }}
                      >
                        {statusOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </InlineStack>
                </Box>

                {/* 日付フィルター */}
                <Box paddingBlockEnd="400">
                  <Card>
                    <Box padding="300">
                      <BlockStack gap="300">
                        <Text as="h3" variant="headingMd">貸出期間で絞り込み</Text>
                        <InlineStack gap="300" wrap={false}>
                          <div style={{ flex: 1 }}>
                            <TextField
                              label="開始日"
                              type="date"
                              value={startDate}
                              onChange={(value) => setStartDate(value)}
                              autoComplete="off"
                            />
                          </div>
                          <div style={{ marginTop: '24px', padding: '0 8px' }}>
                            <Text as="span">〜</Text>
                          </div>
                          <div style={{ flex: 1 }}>
                            <TextField
                              label="終了日"
                              type="date"
                              value={endDate}
                              onChange={(value) => setEndDate(value)}
                              autoComplete="off"
                            />
                          </div>
                          <div style={{ marginTop: '24px' }}>
                            <ButtonGroup>
                              <Button onClick={handleDateRangeChange}>絞り込み</Button>
                              <Button onClick={handleClearFilters}>全クリア</Button>
                            </ButtonGroup>
                          </div>
                        </InlineStack>
                      </BlockStack>
                    </Box>
                  </Card>
                </Box>

                {/* 検索結果表示 */}
                {bookings.length === 0 ? (
                  <EmptyState
                    heading="予約データがありません"
                    image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                  >
                    {filters.query ? (
                      <>
                        <p>検索キーワード「{filters.query}」に一致する予約データが見つかりませんでした。</p>
                        <p>改良された検索機能により、日本語名前のスペースあり・なしに対応しています。</p>
                        <p>検索対象: 商品名、SKU、顧客名、メールアドレス、顧客ID、予約ID</p>
                        <div style={{ marginTop: '16px' }}>
                          <ButtonGroup>
                            <Button onClick={handleClearFilters}>
                              検索条件をクリア
                            </Button>
                            <Button url="/app/bookings/new" variant="primary">
                              新規予約を作成
                            </Button>
                          </ButtonGroup>
                        </div>
                      </>
                    ) : (
                      <>
                        <p>予約データがありません。新しい予約を作成してください。</p>
                        <div style={{ marginTop: '16px' }}>
                          <Button variant="primary" url="/app/bookings/new">
                            新規予約を作成
                          </Button>
                        </div>
                      </>
                    )}
                  </EmptyState>
                ) : (
                  <DataTable
                    columnContentTypes={[
                      "text", "text", "text", "text", "text", "text",
                      "text", "text", "text", "text", "text", "text", "text"
                    ]}
                    headings={[
                      "予約ID", "SKU", "商品名", "商品状態", "開始日", "終了日",
                      "日数", "金額", "予約状態", "支払状態", "注文情報", "顧客名", "作成日時"
                    ]}
                    rows={rows}
                    hoverable
                    increasedTableDensity
                    truncate
                  />
                )}

                {/* ページネーション */}
                {pagination.totalPages > 1 && (
                  <Box paddingBlockStart="400">
                    <Pagination
                      hasPrevious={pagination.hasPrevious}
                      onPrevious={() => handlePageChange(pagination.currentPage - 1)}
                      hasNext={pagination.hasNext}
                      onNext={() => handlePageChange(pagination.currentPage + 1)}
                    />
                    <div style={{ textAlign: "center", marginTop: "10px" }}>
                      <Text as="span">
                        全{pagination.totalItems}件中 {(pagination.currentPage - 1) * 20 + 1}-{Math.min(pagination.currentPage * 20, pagination.totalItems)}件を表示
                      </Text>
                    </div>
                  </Box>
                )}
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
    </div>
  );
}
