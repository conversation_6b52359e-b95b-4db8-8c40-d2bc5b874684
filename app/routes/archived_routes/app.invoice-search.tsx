import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useNavigate } from "@remix-run/react";
import { useState, useEffect, useCallback } from "react";
import { authenticate } from "../shopify.server";
import { Page, Layout, Card, Text, Button, TextField, Select, FormLayout } from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import TestDataManager from "../components/InvoiceSearch/TestDataManager";
import InvoiceSearchResults from "../components/InvoiceSearch/InvoiceSearchResults";
import useInvoiceSearch from "~/hooks/useInvoiceSearch";

/**
 * 伝票検索画面
 */
export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);

  // 検索条件の初期値などを取得する場合はここで処理
  return json({
    // 必要に応じて初期データを追加
  });
}

/**
 * 伝票検索画面のコンポーネント
 */
export default function InvoiceSearch() {
  const navigate = useNavigate();
  const { results, isLoading, performSearch, clearSearch } = useInvoiceSearch();

  // テストデータ管理の表示状態
  const [showTestDataManager, setShowTestDataManager] = useState(false);

  // 検索フォームの状態
  const [invoiceNo, setInvoiceNo] = useState("");
  const [customerName, setCustomerName] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [status, setStatus] = useState("");
  const [rentalStartDate, setRentalStartDate] = useState("");
  const [rentalEndDate, setRentalEndDate] = useState("");
  const [returnStartDate, setReturnStartDate] = useState("");
  const [returnEndDate, setReturnEndDate] = useState("");
  const [minAmount, setMinAmount] = useState("");
  const [maxAmount, setMaxAmount] = useState("");

  // ステータスの選択肢
  const statusOptions = [
    { label: "すべて", value: "" },
    { label: "未支払い", value: "unpaid" },
    { label: "支払い済み", value: "paid" },
    { label: "キャンセル", value: "canceled" },
    { label: "返却済み", value: "returned" },
  ];

  // 検索実行
  const handleSearch = useCallback(() => {
    performSearch({
      invoiceNo,
      customerName,
      companyName,
      status,
      rentalStartDate,
      rentalEndDate,
      returnStartDate,
      returnEndDate,
      minAmount: minAmount ? parseInt(minAmount, 10) : undefined,
      maxAmount: maxAmount ? parseInt(maxAmount, 10) : undefined,
    });
  }, [performSearch, invoiceNo, customerName, companyName, status, rentalStartDate, rentalEndDate, returnStartDate, returnEndDate, minAmount, maxAmount]);

  // フォームクリア
  const handleClear = useCallback(() => {
    setInvoiceNo("");
    setCustomerName("");
    setCompanyName("");
    setStatus("");
    setRentalStartDate("");
    setRentalEndDate("");
    setReturnStartDate("");
    setReturnEndDate("");
    setMinAmount("");
    setMaxAmount("");
    clearSearch();
  }, [clearSearch]);

  // テストデータ生成成功時のハンドラ
  const handleGenerateSuccess = useCallback((count: number) => {
    console.log('テストデータ生成成功:', count, '件');
    // 検索を実行して新しいテストデータを表示
    performSearch({});
  }, [performSearch]);

  // テストデータ削除成功時のハンドラ
  const handleDeleteSuccess = useCallback(() => {
    // 検索結果をクリア
    clearSearch();
  }, [clearSearch]);

  // 初期表示時には検索を実行しない
  // コメントアウトしたコードは初期表示時に自動的に検索を実行する場合に使用
  /*
  useEffect(() => {
    console.log('コンポーネントがマウントされました');
    performSearch({});
  }, [performSearch]);
  */

  return (
    <Page
      title="伝票検索"
      primaryAction={{
        content: "新規伝票作成",
        onAction: () => navigate("/app/invoice-search/new"),
      }}
    >
      <TitleBar title="伝票検索" />

      <Layout>
        {/* テストデータ管理 */}
        <Layout.Section>
          <Card>
            <div style={{ padding: "16px" }}>
              <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "16px" }}>
                <Text variant="headingMd" as="h2">
                  テストデータ管理
                </Text>
                <Button
                  onClick={() => setShowTestDataManager(!showTestDataManager)}
                  variant="primary"
                >
                  {showTestDataManager ? "テストデータ管理を閉じる" : "テストデータ管理を開く"}
                </Button>
              </div>
              {!showTestDataManager && (
                <Text variant="bodyMd" as="p">
                  テストデータ管理を開いて、テストデータの生成や削除を行うことができます。
                </Text>
              )}
            </div>
          </Card>
        </Layout.Section>

        {showTestDataManager && (
          <Layout.Section>
            <TestDataManager
              onGenerateSuccess={handleGenerateSuccess}
              onDeleteSuccess={handleDeleteSuccess}
            />
          </Layout.Section>
        )}

        {/* 検索フォーム */}
        <Layout.Section>
          <Card>
            <div style={{ padding: "16px" }}>
              <Text variant="headingMd" as="h2">
                検索条件
              </Text>
              <div style={{ marginTop: "16px" }}>
                <FormLayout>
                  {/* 基本情報 */}
                  <FormLayout.Group>
                    <TextField
                      label="伝票番号"
                      value={invoiceNo}
                      onChange={setInvoiceNo}
                      autoComplete="off"
                    />
                    <TextField
                      label="顧客名"
                      value={customerName}
                      onChange={setCustomerName}
                      autoComplete="off"
                    />
                  </FormLayout.Group>

                  <FormLayout.Group>
                    <TextField
                      label="会社名"
                      value={companyName}
                      onChange={setCompanyName}
                      autoComplete="off"
                    />
                    <Select
                      label="ステータス"
                      options={statusOptions}
                      value={status}
                      onChange={setStatus}
                    />
                  </FormLayout.Group>

                  {/* 貸出日範囲 */}
                  <Text variant="headingSm" as="h3">
                    貸出日範囲
                  </Text>
                  <FormLayout.Group>
                    <TextField
                      label="開始日"
                      type="date"
                      value={rentalStartDate}
                      onChange={setRentalStartDate}
                    />
                    <TextField
                      label="終了日"
                      type="date"
                      value={rentalEndDate}
                      onChange={setRentalEndDate}
                    />
                  </FormLayout.Group>

                  {/* 返却日範囲 */}
                  <Text variant="headingSm" as="h3">
                    返却日範囲
                  </Text>
                  <FormLayout.Group>
                    <TextField
                      label="開始日"
                      type="date"
                      value={returnStartDate}
                      onChange={setReturnStartDate}
                    />
                    <TextField
                      label="終了日"
                      type="date"
                      value={returnEndDate}
                      onChange={setReturnEndDate}
                    />
                  </FormLayout.Group>

                  {/* 金額範囲 */}
                  <Text variant="headingSm" as="h3">
                    金額範囲
                  </Text>
                  <FormLayout.Group>
                    <TextField
                      label="最小金額"
                      type="number"
                      value={minAmount}
                      onChange={setMinAmount}
                      suffix="円"
                    />
                    <TextField
                      label="最大金額"
                      type="number"
                      value={maxAmount}
                      onChange={setMaxAmount}
                      suffix="円"
                    />
                  </FormLayout.Group>

                  <div style={{ display: "flex", gap: "8px", marginTop: "16px" }}>
                    <Button primary onClick={handleSearch}>検索</Button>
                    <Button onClick={handleClear}>クリア</Button>
                  </div>
                </FormLayout>
              </div>
            </div>
          </Card>
        </Layout.Section>

        {/* 検索結果 */}
        <Layout.Section>
          <InvoiceSearchResults
            results={results}
            isLoading={isLoading}
            onViewDetails={(invoiceId) => navigate(`/app/invoice-search/${invoiceId}`)}
          />
        </Layout.Section>
      </Layout>
    </Page>
  );
}
