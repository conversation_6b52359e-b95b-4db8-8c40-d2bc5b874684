/**
 * Shopify顧客API接続テストスクリプト
 * 
 * このスクリプトは、Shopify Admin APIを使用して顧客情報を取得し、
 * 顧客IDや検索クエリが正しく機能しているかをテストします。
 * 
 * 使用方法:
 * npm run test:customer-api
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// 顧客IDを指定して顧客情報を取得するGraphQLクエリ
const GET_CUSTOMER_BY_ID = gql`
  query getCustomer($id: ID!) {
    customer(id: $id) {
      id
      firstName
      lastName
      email
      phone
      createdAt
      updatedAt
      defaultAddress {
        address1
        address2
        city
        province
        zip
        country
      }
    }
  }
`;

// 顧客を検索するGraphQLクエリ
const SEARCH_CUSTOMERS = gql`
  query searchCustomers($query: String!, $first: Int!) {
    customers(first: $first, query: $query) {
      edges {
        node {
          id
          firstName
          lastName
          email
          phone
        }
      }
    }
  }
`;

// 顧客IDから顧客情報を取得する関数
async function getCustomerById(customerId: string) {
  console.log(`\n=== 顧客ID検索テスト: ${customerId} ===`);
  
  try {
    // 数値のみの場合はGID形式に変換
    const gid = customerId.startsWith('gid://') 
      ? customerId 
      : `gid://shopify/Customer/${customerId}`;
    
    console.log(`変換後のGID: ${gid}`);
    
    // APIリクエスト
    const response = await client.request(GET_CUSTOMER_BY_ID, {
      id: gid
    });
    
    console.log('APIレスポンス:', JSON.stringify(response, null, 2));
    
    if (response.customer) {
      console.log('顧客情報取得成功:');
      console.log(`ID: ${response.customer.id}`);
      console.log(`名前: ${response.customer.firstName} ${response.customer.lastName}`);
      console.log(`メール: ${response.customer.email}`);
      return response.customer;
    } else {
      console.log('顧客情報が見つかりませんでした');
      return null;
    }
  } catch (error: any) {
    console.error('エラー:', error.message);
    if (error.response?.errors) {
      console.error('GraphQLエラー:', JSON.stringify(error.response.errors, null, 2));
    }
    return null;
  }
}

// 顧客を検索する関数
async function searchCustomers(query: string, limit = 10) {
  console.log(`\n=== 顧客検索テスト: "${query}" ===`);
  
  try {
    // APIリクエスト
    const response = await client.request(SEARCH_CUSTOMERS, {
      query,
      first: limit
    });
    
    const customers = response.customers.edges;
    
    console.log(`${customers.length}件の顧客が見つかりました`);
    
    if (customers.length > 0) {
      customers.forEach((edge: any, index: number) => {
        const customer = edge.node;
        console.log(`\n[${index + 1}] 顧客情報:`);
        console.log(`ID: ${customer.id}`);
        console.log(`名前: ${customer.firstName} ${customer.lastName}`);
        console.log(`メール: ${customer.email}`);
      });
    }
    
    return customers;
  } catch (error: any) {
    console.error('エラー:', error.message);
    if (error.response?.errors) {
      console.error('GraphQLエラー:', JSON.stringify(error.response.errors, null, 2));
    }
    return [];
  }
}

// 全顧客を取得する関数
async function getAllCustomers(limit = 10) {
  console.log(`\n=== 全顧客取得テスト (最大${limit}件) ===`);
  
  try {
    // APIリクエスト
    const response = await client.request(SEARCH_CUSTOMERS, {
      query: '',
      first: limit
    });
    
    const customers = response.customers.edges;
    
    console.log(`${customers.length}件の顧客が見つかりました`);
    
    if (customers.length > 0) {
      customers.forEach((edge: any, index: number) => {
        const customer = edge.node;
        console.log(`\n[${index + 1}] 顧客情報:`);
        console.log(`ID: ${customer.id}`);
        console.log(`数値ID: ${customer.id.replace('gid://shopify/Customer/', '')}`);
        console.log(`名前: ${customer.firstName} ${customer.lastName}`);
        console.log(`メール: ${customer.email}`);
      });
    }
    
    return customers;
  } catch (error: any) {
    console.error('エラー:', error.message);
    if (error.response?.errors) {
      console.error('GraphQLエラー:', JSON.stringify(error.response.errors, null, 2));
    }
    return [];
  }
}

// メイン関数
async function main() {
  console.log('Shopify顧客API接続テストを開始します...');
  console.log(`ショップ: ${process.env.SHOPIFY_SHOP}`);
  
  // テスト1: 全顧客を取得
  await getAllCustomers(5);
  
  // テスト2: 顧客IDで検索
  const testIds = [
    '8418608414888',
    'gid://shopify/Customer/8418608414888',
    '8418608480424',
    'gid://shopify/Customer/8418608480424'
  ];
  
  for (const id of testIds) {
    await getCustomerById(id);
  }
  
  // テスト3: 顧客名で検索
  const testQueries = [
    '佐藤 花子',
    '佐藤花子',
    '鈴木 一郎',
    '鈴木一郎',
    'sato.hanako',
    'suzuki.ichiro'
  ];
  
  for (const query of testQueries) {
    await searchCustomers(query);
  }
  
  console.log('\nShopify顧客API接続テストが完了しました');
}

// スクリプトを実行
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('予期しないエラーが発生しました:', error);
    process.exit(1);
  });
