/**
 * ロガーサービス
 * 
 * アプリケーション全体で使用するロギング機能を提供します。
 * 開発環境ではコンソールに出力し、本番環境では将来的に外部サービスに送信することも可能です。
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogContext {
  [key: string]: any;
}

class Logger {
  private isDevelopment: boolean;

  constructor() {
    this.isDevelopment = process.env.NODE_ENV !== 'production';
  }

  /**
   * デバッグレベルのログを出力します
   */
  debug(message: string, context?: LogContext): void {
    this.log('debug', message, context);
  }

  /**
   * 情報レベルのログを出力します
   */
  info(message: string, context?: LogContext): void {
    this.log('info', message, context);
  }

  /**
   * 警告レベルのログを出力します
   */
  warn(message: string, context?: LogContext): void {
    this.log('warn', message, context);
  }

  /**
   * エラーレベルのログを出力します
   */
  error(message: string, context?: LogContext): void {
    this.log('error', message, context);
  }

  /**
   * ログを出力します
   */
  private log(level: LogLevel, message: string, context?: LogContext): void {
    const timestamp = new Date().toISOString();
    const formattedMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
    
    // 開発環境ではコンソールに出力
    if (this.isDevelopment) {
      switch (level) {
        case 'debug':
          console.debug(formattedMessage, context || '');
          break;
        case 'info':
          console.info(formattedMessage, context || '');
          break;
        case 'warn':
          console.warn(formattedMessage, context || '');
          break;
        case 'error':
          console.error(formattedMessage, context || '');
          break;
      }
    } else {
      // 本番環境では将来的に外部サービスに送信することも可能
      // 例: Sentry, Datadog, CloudWatch Logs など
      
      // 現時点では単純にコンソールに出力
      if (level === 'error' || level === 'warn') {
        console.error(formattedMessage, context || '');
      }
    }
  }
}

// シングルトンインスタンスをエクスポート
export const logger = new Logger();
