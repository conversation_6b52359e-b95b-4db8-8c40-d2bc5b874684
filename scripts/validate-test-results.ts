/**
 * テスト結果検証スクリプト
 * 
 * このスクリプトは、テスト結果JSONファイルを読み込み、期待される結果と比較して検証します。
 * 検証結果はMarkdownレポートとして出力されます。
 * 
 * 使用方法:
 * npx tsx scripts/validate-test-results.ts [JSONファイルパス] [期待結果JSONファイルパス]
 * 
 * 例:
 * npx tsx scripts/validate-test-results.ts docs/test-results/shopify-prisma-integration-2025-05-20T12-34-56.json scripts/expected-results/shopify-prisma-integration.json
 */

import * as fs from 'fs';
import * as path from 'path';
import { validateTestResults, loadTestResults, ValidationResult } from '../app/tests/utils/test-result-validator';
import { generateMarkdownReport, generateHtmlReport } from '../app/tests/utils/test-reporter';

// コマンドライン引数の処理
const args = process.argv.slice(2);
const testResultsPath = args[0];
const expectedResultsPath = args[1];

// 色付きログ出力のための定数
const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
};

/**
 * 期待結果ファイルを読み込む関数
 * @param filePath 期待結果ファイルのパス
 * @returns 期待結果オブジェクト
 */
function loadExpectedResults(filePath: string): Record<string, any> {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    throw new Error(`期待結果の読み込みに失敗しました: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 検証結果をMarkdownレポートとして生成する関数
 * @param validationResults 検証結果の配列
 * @param outputPath 出力先のファイルパス
 * @returns 生成されたファイルのパス
 */
function generateValidationReport(validationResults: ValidationResult[], outputPath: string): string {
  try {
    const validCount = validationResults.filter(r => r.valid).length;
    const invalidCount = validationResults.length - validCount;
    
    let markdown = `# テスト結果検証レポート

## 検証概要

このレポートは、テスト結果の自動検証結果をまとめたものです。

検証実行日時: ${new Date().toLocaleString('ja-JP')}

## 検証結果

- 合計検証数: ${validationResults.length}
- 成功: ${validCount}
- 失敗: ${invalidCount}
- 成功率: ${Math.round((validCount / validationResults.length) * 100)}%

## 詳細結果

`;

    // 各検証結果の詳細を追加
    validationResults.forEach((result, index) => {
      markdown += `### 検証${index + 1}: ${result.message}

- 結果: ${result.valid ? '✅ 成功' : '❌ 失敗'}
`;

      if (result.details) {
        markdown += `- 詳細:\n\`\`\`json\n${JSON.stringify(result.details, null, 2)}\n\`\`\`\n\n`;
      }
    });

    // レポートファイルに書き込み
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(outputPath, markdown);
    
    return outputPath;
  } catch (error) {
    throw new Error(`検証レポートの生成に失敗しました: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * メイン関数
 */
async function main() {
  console.log(`${COLORS.MAGENTA}=== テスト結果検証 ===${COLORS.RESET}`);
  console.log(`開始時刻: ${new Date().toLocaleString()}`);

  try {
    // 引数の検証
    if (!testResultsPath) {
      console.error(`${COLORS.RED}エラー: テスト結果JSONファイルのパスを指定してください${COLORS.RESET}`);
      console.log('使用方法: npx tsx scripts/validate-test-results.ts [JSONファイルパス] [期待結果JSONファイルパス]');
      process.exit(1);
    }

    if (!expectedResultsPath) {
      console.error(`${COLORS.RED}エラー: 期待結果JSONファイルのパスを指定してください${COLORS.RESET}`);
      console.log('使用方法: npx tsx scripts/validate-test-results.ts [JSONファイルパス] [期待結果JSONファイルパス]');
      process.exit(1);
    }

    // テスト結果の読み込み
    console.log(`\n${COLORS.CYAN}テスト結果を読み込んでいます: ${testResultsPath}${COLORS.RESET}`);
    const testResults = loadTestResults(testResultsPath);
    console.log(`テスト結果を読み込みました: ${testResults.tests.length}件のテスト`);

    // 期待結果の読み込み
    console.log(`\n${COLORS.CYAN}期待結果を読み込んでいます: ${expectedResultsPath}${COLORS.RESET}`);
    const expectedResults = loadExpectedResults(expectedResultsPath);
    console.log(`期待結果を読み込みました: ${expectedResults.tests?.length || 0}件のテスト`);

    // テスト結果の検証
    console.log(`\n${COLORS.CYAN}テスト結果を検証しています...${COLORS.RESET}`);
    const validationResults = validateTestResults(testResults, expectedResults);

    // 検証結果の集計
    const validCount = validationResults.filter(r => r.valid).length;
    const invalidCount = validationResults.length - validCount;

    console.log(`\n${COLORS.MAGENTA}=== 検証結果サマリー ===${COLORS.RESET}`);
    console.log(`${COLORS.CYAN}合計: ${validationResults.length} 検証${COLORS.RESET}`);
    console.log(`${COLORS.GREEN}成功: ${validCount} 検証${COLORS.RESET}`);
    console.log(`${COLORS.RED}失敗: ${invalidCount} 検証${COLORS.RESET}`);

    // 検証結果のMarkdownレポートを生成
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const validationReportPath = path.join(process.cwd(), `docs/test-results/validation-report-${timestamp}.md`);
    const validationReport = generateValidationReport(validationResults, validationReportPath);
    console.log(`\n検証結果レポートを生成しました: ${validationReport}`);

    // 検証結果を含むHTMLレポートを生成
    const htmlReportPath = path.join(process.cwd(), `docs/test-results/test-results-with-validation-${timestamp}.html`);
    generateHtmlReport(testResults, htmlReportPath, validationResults);
    console.log(`検証結果を含むHTMLレポートを生成しました: ${htmlReportPath}`);

    console.log(`\n${COLORS.GREEN}テスト結果検証が完了しました${COLORS.RESET}`);
    console.log(`終了時刻: ${new Date().toLocaleString()}`);

    // 終了コードを設定（失敗があれば1、なければ0）
    process.exit(invalidCount > 0 ? 1 : 0);
  } catch (error) {
    console.error(`\n${COLORS.RED}エラーが発生しました:${COLORS.RESET}`);
    console.error(error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// スクリプトの実行
main().catch(error => {
  console.error(`\n${COLORS.RED}予期しないエラーが発生しました:${COLORS.RESET}`);
  console.error(error instanceof Error ? error.stack || error.message : String(error));
  process.exit(1);
});
