/**
 * 重複注文の発生原因を調査するためのテストスクリプト
 *
 * このスクリプトは以下のテストを行います：
 * 1. 既存の注文情報を取得して現状を確認
 * 2. 同じ予約IDで複数回注文作成を試みる
 * 3. 各ステップでの詳細なログを記録
 * 4. 結果を比較して重複が発生する条件を特定
 *
 * 実行方法: npx tsx scripts/test-duplicate-orders.ts
 */

import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import { createOrderFromBooking } from '../app/utils/booking/order-creator';
import { AdminApiContext } from '@shopify/shopify-app-remix/server';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの初期化
const createGraphQLClient = (accessToken: string) => {
  return new GraphQLClient(
    `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
    {
      headers: {
        'X-Shopify-Access-Token': accessToken,
        'Content-Type': 'application/json',
      },
    }
  );
};

// 注文を取得するGraphQLクエリ
const GET_ORDERS = gql`
  query getOrders($query: String, $first: Int) {
    orders(query: $query, first: $first) {
      edges {
        node {
          id
          name
          createdAt
          tags
          customer {
            id
            firstName
            lastName
            email
          }
          lineItems(first: 5) {
            edges {
              node {
                title
                quantity
                variant {
                  id
                  title
                }
              }
            }
          }
          metafields(first: 10) {
            edges {
              node {
                namespace
                key
                value
              }
            }
          }
        }
      }
    }
  }
`;

// 予約情報を取得する関数
async function getBookingById(bookingId: string) {
  try {
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        product: true
      }
    });

    if (!booking) {
      throw new Error(`予約ID ${bookingId} が見つかりません`);
    }

    return booking;
  } catch (error) {
    console.error('予約情報の取得に失敗しました:', error);
    throw error;
  }
}

// 特定のタグを持つ注文を取得する関数
async function getOrdersByTag(tag: string, accessToken: string) {
  try {
    console.log(`タグ "${tag}" を持つ注文を検索中...`);

    const client = createGraphQLClient(accessToken);
    const result = await client.request(GET_ORDERS, {
      query: `tag:${tag}`,
      first: 10
    });

    return result.orders.edges.map((edge: any) => edge.node);
  } catch (error) {
    console.error('注文の取得に失敗しました:', error);
    throw error;
  }
}

// 注文作成をシミュレートする関数
async function simulateOrderCreation(bookingId: string, accessToken: string, iteration: number) {
  try {
    console.log(`\n===== 注文作成シミュレーション #${iteration} =====`);
    console.log(`予約ID: ${bookingId}`);

    // 予約情報を取得
    const booking = await getBookingById(bookingId);
    console.log(`予約情報: ${booking.bookingId}, 商品: ${booking.product.title}`);

    // 既存の注文を確認
    const existingOrders = await getOrdersByTag(`booking-${booking.bookingId}`, accessToken);
    console.log(`既存の注文数: ${existingOrders.length}`);

    if (existingOrders.length > 0) {
      console.log('既存の注文:');
      existingOrders.forEach((order: any, index: number) => {
        console.log(`  ${index + 1}. 注文番号: ${order.name}, 作成日時: ${order.createdAt}, タグ: ${order.tags}`);
      });
    }

    // AdminApiContextをモック
    const mockAdminApi: AdminApiContext = {
      graphql: async (query: string, options?: any) => {
        const client = createGraphQLClient(accessToken);
        const variables = options?.variables || {};

        // GraphQLリクエストの詳細をログ
        console.log('GraphQLリクエスト:', { query: query.substring(0, 100) + '...', variables });

        try {
          const response = await client.request(query, variables);
          return {
            json: async () => response
          };
        } catch (error) {
          console.error('GraphQLリクエストエラー:', error);
          throw error;
        }
      }
    };

    // 注文作成を実行
    console.log('注文作成を実行中...');
    const startTime = Date.now();

    const result = await createOrderFromBooking(
      prisma,
      mockAdminApi,
      bookingId,
      3 // リトライ回数
    );

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`注文作成結果 (${duration}ms):`);
    console.log(JSON.stringify(result, null, 2));

    // 作成後の注文を確認
    const updatedOrders = await getOrdersByTag(`booking-${booking.bookingId}`, accessToken);
    console.log(`作成後の注文数: ${updatedOrders.length}`);

    if (updatedOrders.length > 0) {
      console.log('作成後の注文:');
      updatedOrders.forEach((order: any, index: number) => {
        console.log(`  ${index + 1}. 注文番号: ${order.name}, 作成日時: ${order.createdAt}, タグ: ${order.tags}`);
      });
    }

    // 新しく作成された注文があるかチェック
    const newOrders = updatedOrders.filter(
      (updatedOrder: any) => !existingOrders.some(
        (existingOrder: any) => existingOrder.id === updatedOrder.id
      )
    );

    console.log(`新しく作成された注文数: ${newOrders.length}`);

    if (newOrders.length > 0) {
      console.log('新しい注文:');
      newOrders.forEach((order: any, index: number) => {
        console.log(`  ${index + 1}. 注文番号: ${order.name}, 作成日時: ${order.createdAt}, タグ: ${order.tags}`);
      });
    }

    return {
      iteration,
      result,
      existingOrdersCount: existingOrders.length,
      updatedOrdersCount: updatedOrders.length,
      newOrdersCount: newOrders.length,
      duration
    };
  } catch (error) {
    console.error(`シミュレーション #${iteration} でエラーが発生しました:`, error);
    return {
      iteration,
      error: error.message,
      existingOrdersCount: 0,
      updatedOrdersCount: 0,
      newOrdersCount: 0,
      duration: 0
    };
  }
}

// メイン実行関数
async function testDuplicateOrders() {
  try {
    console.log('重複注文の調査を開始します...');
    console.log('環境変数:', {
      SHOPIFY_SHOP: process.env.SHOPIFY_SHOP,
      TEST_BOOKING_ID: process.env.TEST_BOOKING_ID
    });

    // セッションを取得
    const session = await prisma.session.findUnique({
      where: {
        id: `offline_${process.env.SHOPIFY_SHOP}`
      }
    });

    if (!session) {
      throw new Error('セッションが見つかりません。アプリケーションが正しく認証されていることを確認してください。');
    }

    console.log(`セッション情報: Shop=${session.shop}, アクセストークン=${session.accessToken.substring(0, 10)}...`);

    // テスト用の予約IDを取得（環境変数から、または最新の予約を使用）
    let bookingId = process.env.TEST_BOOKING_ID;

    if (!bookingId) {
      console.log('環境変数からTEST_BOOKING_IDが見つかりません。最新の予約を使用します。');

      const latestBooking = await prisma.booking.findFirst({
        where: {
          shop: process.env.SHOPIFY_SHOP,
          shopifyOrderId: null // 注文がまだ関連付けられていない予約
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      if (!latestBooking) {
        throw new Error('テスト用の予約が見つかりません。新しい予約を作成してください。');
      }

      bookingId = latestBooking.id;
      console.log(`最新の予約を使用します: ID=${bookingId}, 予約番号=${latestBooking.bookingId}`);
    }

    // 同じ予約IDで複数回注文作成を試みる
    const iterations = 3; // テスト回数
    const results = [];

    for (let i = 1; i <= iterations; i++) {
      const result = await simulateOrderCreation(bookingId, session.accessToken, i);
      results.push(result);

      // 各テストの間に少し待機
      if (i < iterations) {
        console.log(`次のテストまで3秒待機中...`);
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }

    // 結果のサマリーを表示
    console.log('\n===== テスト結果サマリー =====');
    console.table(results);

    // 重複が発生したかどうかを分析
    const totalNewOrders = results.reduce((sum, r) => sum + r.newOrdersCount, 0);

    if (totalNewOrders > 1) {
      console.log(`\n⚠️ 重複注文が検出されました: 同じ予約に対して ${totalNewOrders} 件の注文が作成されました`);
      console.log('考えられる原因:');
      console.log('1. 注文作成前に既存の注文をチェックするロジックがない');
      console.log('2. 同じ予約IDに対する重複チェックが不十分');
      console.log('3. リトライロジックが新しい注文を作成している');
    } else if (totalNewOrders === 1) {
      console.log('\n✅ 重複注文は検出されませんでした: 正しく1件の注文のみが作成されました');
    } else {
      console.log('\n❓ 注文が作成されませんでした: 既に注文が存在するか、エラーが発生した可能性があります');
    }

    return results;
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    throw error;
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// スクリプトを実行
console.log('テストを開始します...');
testDuplicateOrders()
  .then((results) => {
    console.log('テストが完了しました');
    console.log('結果:', JSON.stringify(results, null, 2));
    process.exit(0);
  })
  .catch((error) => {
    console.error('テストが失敗しました:', error);
    console.error('エラーの詳細:', error.stack);
    process.exit(1);
  });
