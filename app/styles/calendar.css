/* 統合カレンダースタイル
 * 複数のカレンダースタイルファイルを1つに統合したもの
 */

/* カレンダー全体のスタイル */
.unified-calendar {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

/* カレンダータイトルのスタイル */
.Polaris-Text {
  text-align: left;
}

/* ===== 基本スタイル ===== */

/* 月名と年の表示スタイル */
.Polaris-DatePicker__Month {
  font-size: 1rem;
}

/* 月名と年の表示スタイル */
.Polaris-DatePicker__Title {
  gap: 0.5rem;
  text-align: center;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

/* 曜日の表示スタイル */
.Polaris-DatePicker__Weekday {
  text-align: center;
  font-weight: 500;
  padding: 0.5rem 0;
  font-size: 0.9rem;
}

/* DatePickerコンポーネント全体のスタイル */
.Polaris-DatePicker {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

/* カレンダーヘッダーのスタイル */
.Polaris-DatePicker__Header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 1rem;
}

/* 日付の数字を中央揃え */
.Polaris-DatePicker__Day span {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
}

/* Polaris DatePickerのカスタマイズ */
.Polaris-DatePicker__Day {
  position: relative;
  height: 36px;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

/* カレンダーのセルのスタイル */
.Polaris-DatePicker__Week-Cell {
  padding: 0;
  height: 36px;
}

/* ===== 選択状態のスタイル ===== */

/* 選択中の日付の背景色 */
.Polaris-DatePicker__Day--selected,
.Polaris-DatePicker__Day--selected:hover,
.Polaris-DatePicker__Day--selected:focus,
.Polaris-DatePicker__Day--selected:active {
  background-color: #202223;
  color: white;
  z-index: 10;
}

/* 選択中の日付のテキスト色を白にする */
.Polaris-DatePicker__Day--selected span,
.Polaris-DatePicker__Day--selected:hover span,
.Polaris-DatePicker__Day--selected:focus span,
.Polaris-DatePicker__Day--selected:active span {
  color: white;
}

/* 選択範囲内の日付の背景色 */
.Polaris-DatePicker__Day--inRange {
  background-color: rgba(32, 34, 35, 0.1);
  z-index: 1;
}

/* 無効な日付のスタイル */
.Polaris-DatePicker__Day--disabled {
  color: #8c9196;
  cursor: not-allowed;
}

/* ホバー状態の改善 */
.Polaris-DatePicker__Day:hover {
  background-color: #f9fafb;
}

/* 仮予約のホバー時 */
.Polaris-DatePicker__Day[data-provisional="true"]:hover {
  background-color: rgba(255, 184, 0, 0.2) !important;
}

/* 本予約のホバー時 */
.Polaris-DatePicker__Day[data-confirmed="true"]:hover {
  background-color: rgba(0, 128, 96, 0.2) !important;
}

/* メンテナンスのホバー時 */
.Polaris-DatePicker__Day[data-maintenance="true"]:hover {
  background-color: rgba(171, 171, 171, 0.5) !important;
}

/* 選択中の日付のホバー状態 */
.Polaris-DatePicker__Day--selected:hover {
  background-color: #202223;
}

/* 選択中の日付の::afterの色を白にする */
.Polaris-DatePicker__Day--selected span::after {
  color: white;
}

/* ===== 日付タイプのスタイル ===== */

/* 休業日のスタイル */
/* .Polaris-DatePicker__Day[data-closed="true"] span {
  color: #bf0711 !important;
  opacity: 0.6 !important;
  position: relative;
} */

/* 休業日に「休」を追加 */
/* .Polaris-DatePicker__Day[data-closed="true"] span::after {
  content: "休";
  font-size: 0.7em;
  position: absolute;
  bottom: -11px;
  left: 50%;
  transform: translateX(-50%);
  color: #bf0711;
  opacity: 0.6;
  font-weight: bold;
} */

/* 予約済み日付の共通スタイル */
/* .Polaris-DatePicker__Day[data-booked="true"] span {
  font-weight: 500 !important;
  position: relative;
} */

/* 仮予約のスタイル */
.Polaris-DatePicker__Day[data-provisional="true"] {
  background-color: rgba(255, 184, 0, 0.2) !important;
}

.Polaris-DatePicker__Day[data-provisional="true"] span {
  color: #d82c0d;
  position: relative;
}

/* 仮予約に「仮」を追加 */
.Polaris-DatePicker__Day[data-provisional="true"] span::after {
  content: "仮";
  font-size: 0.7em;
  position: absolute;
  top: 2px;
  right: 2px;
  color: #d82c0d;
  font-weight: bold;
}

/* 本予約のスタイル */
.Polaris-DatePicker__Day[data-confirmed="true"] {
  background-color: rgba(0, 128, 96, 0.2) !important;
}

.Polaris-DatePicker__Day[data-confirmed="true"] span {
  color: #00848e;
  position: relative;
}

/* メンテナンスのスタイル */
.Polaris-DatePicker__Day[data-maintenance="true"] {
  background-color: rgba(171, 171, 171, 0.5) !important;
}

.Polaris-DatePicker__Day[data-maintenance="true"] span {
  color: #637381;
  position: relative;
}

/* 本予約に「予」を追加 */
.Polaris-DatePicker__Day[data-confirmed="true"] span::after {
  content: "予";
  font-size: 0.7em;
  position: absolute;
  top: 2px;
  right: 2px;
  color: #00848e;
  font-weight: bold;
}

/* メンテナンスに「メ」を追加 */
.Polaris-DatePicker__Day[data-maintenance="true"] span::after {
  content: "メ";
  font-size: 0.7em;
  position: absolute;
  top: 2px;
  right: 2px;
  color: #637381;
  font-weight: bold;
}

/* ===== Vanilla Calendar スタイル ===== */

.vanilla-calendar {
  max-width: 100%;
  border: 1px solid #e4e5e7;
  border-radius: 8px;
  padding: 16px;
  background-color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
}

.vanilla-calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.vanilla-calendar-header__content {
  font-size: 16px;
  font-weight: 600;
}

.vanilla-calendar-arrow {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  font-size: 16px;
}

.vanilla-calendar-day {
  position: relative;
  padding-bottom: 10px;
}

.vanilla-calendar-day__btn {
  border-radius: 4px;
}

/* 予約済み日付のスタイル */
.booked-date .vanilla-calendar-day__btn {
  text-decoration: line-through;
}

.provisional-booking .vanilla-calendar-day__btn {
  color: #d82c0d;
}

.confirmed-booking .vanilla-calendar-day__btn {
  color: #00848e;
}

/* 祝日のスタイル */
.holiday .vanilla-calendar-day__btn {
  color: #d82c0d;
}

/* 日曜日のスタイル */
.sunday .vanilla-calendar-day__btn {
  color: #d82c0d;
}

/* マーカーのスタイル */
.booking-marker,
.holiday-marker {
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  font-weight: bold;
}

/* 選択された日付のスタイル */
.vanilla-calendar-day__btn_selected {
  background-color: #008060 !important;
  color: #ffffff !important;
}

/* 今日の日付のスタイル */
.vanilla-calendar-day__btn_today {
  background-color: #f2f2f2;
}

/* 選択不可の日付のスタイル */
.vanilla-calendar-day__btn_disabled {
  color: #d3d3d3 !important;
  cursor: not-allowed;
}

/* 範囲選択のスタイル */
.vanilla-calendar-day__btn_selected-intermediate {
  background-color: rgba(0, 128, 96, 0.2) !important;
}

/* カレンダー凡例と選択日付表示のスタイル */
.unified-calendar .Polaris-Box {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  text-align: center;
}

/* 凡例のテキストを中央揃え */
.unified-calendar .Polaris-BlockStack {
  width: 100%;
  text-align: center;
}

/* カレンダー凡例のスタイル */
.unified-calendar .Polaris-Box {
  width: 100%;
  margin: 0 auto;
}

.unified-calendar .Polaris-BlockStack {
  width: 100%;
}

/* ===== 複数月表示のスタイル ===== */

/* 複数月表示のコンテナ */
.multi-month {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;
  width: 100%;
}

/* 個別の月カレンダー */
.calendar-month {
  flex: 1;
  min-width: 300px;
  max-width: 400px;
}

/* レスポンシブ対応 */
@media (max-width: 768px) {
  .multi-month {
    flex-direction: column;
    gap: 1rem;
  }

  .calendar-month {
    min-width: 100%;
    max-width: 100%;
  }
}
