/**
 * 予約同期状況確認スクリプト
 *
 * このスクリプトは、Shopifyの注文データとPrismaデータベースの予約データの同期状況を確認します。
 */

import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

/**
 * 予約データを確認する関数
 * @param searchTerm 検索キーワード
 */
async function checkBookings(searchTerm: string) {
  try {
    console.log(`Shopifyで「${searchTerm}」を検索中...`);

    // 注文検索のGraphQLクエリ
    const SEARCH_ORDERS = gql`
      query searchOrders($query: String!, $first: Int!) {
        orders(first: $first, query: $query) {
          edges {
            node {
              id
              name
              createdAt
              customer {
                id
                firstName
                lastName
                email
              }
              lineItems(first: 5) {
                edges {
                  node {
                    title
                    variant {
                      id
                      title
                    }
                    customAttributes {
                      key
                      value
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;

    // Shopify GraphQL APIを使用して注文を検索
    const response = await shopifyClient.request(SEARCH_ORDERS, {
      query: searchTerm,
      first: 10
    });

    // 検索結果を表示
    const orders = response.orders.edges;
    console.log(`Shopifyで ${orders.length} 件の注文が見つかりました`);

    // レンタル予約の注文のみをフィルタリング
    const rentalOrders = orders.filter((edge: any) => {
      const lineItems = edge.node.lineItems.edges;
      return lineItems.some((item: any) => {
        const customAttributes = item.node.customAttributes;
        return customAttributes.some((attr: any) => 
          attr.key === 'レンタル開始日' || attr.key === 'レンタル終了日' || attr.key === 'レンタルタイプ'
        );
      });
    });

    console.log(`そのうち ${rentalOrders.length} 件がレンタル予約の注文です`);

    if (rentalOrders.length > 0) {
      console.log('\n=== Shopifyレンタル予約注文一覧 ===');
      rentalOrders.forEach((edge: any, index: number) => {
        const order = edge.node;
        console.log(`${index + 1}. 注文番号: ${order.name}`);
        console.log(`   ID: ${order.id}`);
        console.log(`   顧客: ${order.customer ? `${order.customer.firstName} ${order.customer.lastName}` : 'なし'}`);
        console.log(`   作成日時: ${order.createdAt}`);
        
        // レンタル情報を表示
        const lineItems = order.lineItems.edges;
        lineItems.forEach((item: any, i: number) => {
          const lineItem = item.node;
          console.log(`   商品${i + 1}: ${lineItem.title}`);
          
          // レンタル属性を表示
          const rentalStartDate = lineItem.customAttributes.find((attr: any) => attr.key === 'レンタル開始日');
          const rentalEndDate = lineItem.customAttributes.find((attr: any) => attr.key === 'レンタル終了日');
          const rentalType = lineItem.customAttributes.find((attr: any) => attr.key === 'レンタルタイプ');
          
          if (rentalStartDate) {
            console.log(`     レンタル開始日: ${rentalStartDate.value}`);
          }
          if (rentalEndDate) {
            console.log(`     レンタル終了日: ${rentalEndDate.value}`);
          }
          if (rentalType) {
            console.log(`     レンタルタイプ: ${rentalType.value}`);
          }
        });
        console.log('---');
      });
    }

    // Prismaデータベースで予約を検索
    console.log(`\nデータベースで「${searchTerm}」を検索中...`);
    const dbBookings = await prisma.booking.findMany({
      where: {
        OR: [
          {
            customerName: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
          {
            orderName: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
          {
            bookingId: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
        ],
      },
    });

    console.log(`データベースで ${dbBookings.length} 件の予約が見つかりました`);

    if (dbBookings.length > 0) {
      console.log('\n=== データベース予約一覧 ===');
      dbBookings.forEach((booking, index) => {
        console.log(`${index + 1}. 予約ID: ${booking.bookingId}`);
        console.log(`   ID: ${booking.id}`);
        console.log(`   注文番号: ${booking.orderName || 'なし'}`);
        console.log(`   顧客: ${booking.customerName}`);
        console.log(`   レンタル期間: ${booking.startDate.toISOString().split('T')[0]} 〜 ${booking.endDate.toISOString().split('T')[0]}`);
        console.log(`   ステータス: ${booking.status}`);
        console.log(`   支払い状態: ${booking.paymentStatus}`);
        console.log('---');
      });
    }

    // 同期状況の確認
    console.log('\n=== 同期状況 ===');
    if (rentalOrders.length === 0 && dbBookings.length === 0) {
      console.log('レンタル予約が見つかりませんでした。');
    } else if (rentalOrders.length > 0 && dbBookings.length === 0) {
      console.log('Shopifyにはレンタル予約注文が存在しますが、データベースには同期されていません。');
      console.log('予約同期を実行する必要があります。');
    } else if (rentalOrders.length === 0 && dbBookings.length > 0) {
      console.log('データベースには予約が存在しますが、Shopifyには対応する注文が見つかりません。');
      console.log('データベースのクリーンアップが必要かもしれません。');
    } else {
      console.log('Shopifyとデータベースの両方に予約データが存在します。');

      // ShopifyとDBの注文IDを比較
      const shopifyOrderIds = rentalOrders.map((edge: any) => edge.node.id.replace('gid://shopify/Order/', ''));
      const dbOrderIds = dbBookings.map(booking => booking.orderId).filter(id => id);

      const inShopifyNotInDb = shopifyOrderIds.filter(id => !dbOrderIds.includes(id));
      const inDbNotInShopify = dbOrderIds.filter(id => !shopifyOrderIds.includes(id));

      if (inShopifyNotInDb.length > 0) {
        console.log('\n以下のShopify注文はデータベースに同期されていません:');
        inShopifyNotInDb.forEach(id => {
          const order = rentalOrders.find((edge: any) => edge.node.id.replace('gid://shopify/Order/', '') === id);
          if (order) {
            console.log(`- 注文番号: ${order.node.name} (ID: ${id})`);
          }
        });
      }

      if (inDbNotInShopify.length > 0) {
        console.log('\n以下のデータベース予約はShopifyに対応する注文が見つかりません:');
        inDbNotInShopify.forEach(id => {
          const booking = dbBookings.find(b => b.orderId === id);
          if (booking) {
            console.log(`- 予約ID: ${booking.bookingId} (注文ID: ${id})`);
          }
        });
      }
    }

  } catch (error) {
    console.error('エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// コマンドライン引数から検索キーワードを取得
const searchTerm = process.argv[2] || '';

// 予約検索を実行
checkBookings(searchTerm)
  .then(() => console.log('検索完了'))
  .catch(error => console.error('検索エラー:', error));
