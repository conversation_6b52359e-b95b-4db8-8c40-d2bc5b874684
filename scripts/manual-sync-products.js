/**
 * 商品データを手動で同期するスクリプト
 * 
 * このスクリプトは、ShopifyからPrismaデータベースに商品データを手動で同期します。
 * Webhookが正常に動作していない場合や、既存の商品データを一括で同期したい場合に使用します。
 */

import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

dotenv.config();
const prisma = new PrismaClient();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品を取得するクエリ
const GET_PRODUCTS = gql`
  query getProducts($first: Int!, $after: String) {
    products(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          handle
          status
          variants(first: 10) {
            edges {
              node {
                id
                title
                sku
                price
                inventoryQuantity
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
              }
            }
          }
        }
      }
    }
  }
`;

// SKUから商品コードと詳細コードを抽出する関数
function extractCodesFromSKU(sku) {
  if (!sku) return { productCode: '', detailCode: '' };
  
  const parts = sku.split('-');
  if (parts.length >= 2) {
    // 最後の部分を詳細コード、それ以外を商品コードとする
    const detailCode = parts.pop();
    const productCode = parts.join('-');
    return { productCode, detailCode };
  }
  
  return { productCode: sku, detailCode: '' };
}

// Shopifyのステータスをアプリのステータスに変換する関数
function mapShopifyStatusToProductStatus(shopifyStatus, metafieldStatus) {
  if (metafieldStatus === 'maintenance') return 'MAINTENANCE';
  if (metafieldStatus === 'damaged') return 'DAMAGED';
  if (metafieldStatus === 'unavailable') return 'UNAVAILABLE';
  
  return shopifyStatus === 'ACTIVE' ? 'AVAILABLE' : 'UNAVAILABLE';
}

// メタフィールドから値を取得する関数
function getMetafieldValue(metafields, namespace, key) {
  const metafield = metafields.find(
    m => m.node.namespace === namespace && m.node.key === key
  );
  
  if (!metafield) return null;
  
  try {
    // JSON形式の場合はパースする
    return JSON.parse(metafield.node.value);
  } catch (e) {
    // 通常の文字列の場合はそのまま返す
    return metafield.node.value;
  }
}

// 商品をデータベースに同期する関数
async function syncProductToDatabase(shopifyProduct) {
  try {
    const shopifyId = shopifyProduct.id.replace('gid://shopify/Product/', '');
    const metafields = shopifyProduct.metafields.edges;
    const variants = shopifyProduct.variants.edges.map(edge => edge.node);
    
    // 基本バリアントを取得（1日レンタルまたはDefault Title）
    const baseVariant = variants.find(v => v.title.includes('1日') || v.title === 'Default Title') || variants[0];
    
    if (!baseVariant) {
      console.error(`商品「${shopifyProduct.title}」にバリアントがありません`);
      return false;
    }
    
    // SKUを取得
    const sku = baseVariant.sku || `SKU-${shopifyId}`;
    
    // 価格を取得
    const price = parseFloat(baseVariant.price) || 0;
    
    // メタフィールドから基本情報を取得
    const basicInfoMetafield = getMetafieldValue(metafields, 'rental', 'basic_info');
    const statusMetafield = getMetafieldValue(metafields, 'rental', 'status');
    const locationMetafield = getMetafieldValue(metafields, 'rental', 'location');
    
    // 基本情報がない場合はデフォルト値を設定
    const basicInfo = basicInfoMetafield || {
      productCode: extractCodesFromSKU(sku).productCode,
      detailCode: extractCodesFromSKU(sku).detailCode,
      kana: shopifyProduct.title
    };
    
    // ステータスを取得
    const status = mapShopifyStatusToProductStatus(shopifyProduct.status, statusMetafield);
    
    // 在庫場所を取得
    const locationId = locationMetafield || 'NY';
    
    // データベースから既存の商品を検索
    const existingProduct = await prisma.product.findFirst({
      where: { shopifyId }
    });
    
    if (existingProduct) {
      // 既存の商品を更新
      await prisma.product.update({
        where: { id: existingProduct.id },
        data: {
          title: shopifyProduct.title,
          sku,
          price,
          locationId,
          status,
          basicInfo,
          syncStatus: 'synced',
          lastSyncedAt: new Date(),
          updatedAt: new Date()
        }
      });
      
      console.log(`商品「${shopifyProduct.title}」を更新しました`);
      return true;
    } else {
      // 新規商品を作成
      await prisma.product.create({
        data: {
          shop: process.env.SHOPIFY_SHOP || '',
          shopifyId,
          title: shopifyProduct.title,
          sku,
          price,
          locationId,
          status,
          basicInfo,
          syncStatus: 'synced',
          lastSyncedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      
      console.log(`商品「${shopifyProduct.title}」を作成しました`);
      return true;
    }
  } catch (error) {
    console.error(`商品「${shopifyProduct.title}」の同期中にエラーが発生しました:`, error);
    return false;
  }
}

// すべての商品を取得して同期する関数
async function syncAllProducts() {
  try {
    console.log('商品データを取得しています...');
    
    let hasNextPage = true;
    let cursor = null;
    let allProducts = [];
    
    // ページネーションを使用してすべての商品を取得
    while (hasNextPage) {
      const result = await client.request(GET_PRODUCTS, {
        first: 50,
        after: cursor
      });
      
      const products = result.products.edges.map(edge => edge.node);
      allProducts = allProducts.concat(products);
      
      hasNextPage = result.products.pageInfo.hasNextPage;
      cursor = result.products.pageInfo.endCursor;
      
      console.log(`${products.length}件の商品を取得しました。合計: ${allProducts.length}件`);
      
      if (hasNextPage) {
        // APIレート制限対策
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log(`合計${allProducts.length}件の商品を同期します...`);
    
    // 各商品をデータベースに同期
    let successCount = 0;
    for (const product of allProducts) {
      console.log(`\n商品「${product.title}」の同期を開始...`);
      
      const success = await syncProductToDatabase(product);
      
      if (success) {
        console.log(`商品「${product.title}」の同期が完了しました`);
        successCount++;
      } else {
        console.error(`商品「${product.title}」の同期に失敗しました`);
      }
      
      // APIレート制限対策
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log(`\n同期が完了しました。${successCount}/${allProducts.length}件の商品を同期しました。`);
  } catch (error) {
    console.error('エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
syncAllProducts();
