<!-- snippets/product-get-options.liquid -->
{%- unless product.has_only_default_variant -%}
    {% capture image_size_placeholder %}_{width}x.{% endcapture %}
    {%- assign options_type_circle_color = settings.product_options_type_circle_color | split: '|' | handleize -%}
    {%- assign options_type_circle_image = settings.product_options_type_circle_image | split: '|' | handleize -%}
    {%- assign options_type_square_image = settings.product_options_type_square_image | split: '|' | handleize -%}
    {%- assign options_type_square_color = settings.product_options_type_square_color | split: '|' | handleize -%}
    {%- assign options_type_select = settings.product_options_type_select | split: '|' | handleize -%}
    {%- assign options_type_large_text = settings.product_options_type_large_text | split: '|' | handleize -%}
    {%- assign auto_selected_options = settings.product_auto_selected_options | split: '|' | handleize -%}
    {%- assign options_show_title = options_show_title | default: false -%}
    {%- assign options_type = options_type | default: 1 -%}
    {% capture options_type_class %}
        {%- case options_type -%}
        {%- when 1 %}product-options--type-collection
        {%- when 2 %}product-options--type-page
        {%- when 3 %}product-options--type-footbar
        {%- endcase -%}
    {% endcapture %}
    <div class="product-options {{ options_type_class }}{% if centered %} product-options--centered{% endif %}{% if mobile_centered %} product-options--mobile-centered{% endif %}{% if mobile_start %} product-options--mobile-start{% endif %}{% unless trigger %} js-product-options{% endunless %}" data-js-product-options{% if options_type == 1 and settings.product_variant_change_on_hover %} data-js-options-onhover{% endif %}{% if change_history %} data-js-change-history{% endif %}>
        {%- assign options_limit = 3 -%}
        {%- if options_type == 1 and settings.product_options_listing_visibility != 'all' -%}
            {%- assign options_limit = settings.product_options_listing_visibility | abs -%}
            {%- assign enable_select_options_button = false -%}
            {%- assign options_with_values_size = product.options_with_values | size -%}
            {%- if options_with_values_size > options_limit -%}
                {%- assign enable_select_options_button = true -%}
            {%- endif -%}
        {%- endif -%}
        {%- for option in product.options_with_values -%}
            {%- assign label_value = null -%}
            {% capture option_section_html %}
                {%- assign option_index0 = forloop.index0 -%}
                <div{% if forloop.index > options_limit %} class="d-none"{% endif %} data-section-container>
                    <label{% unless options_show_title %} class="d-none"{% endunless %}><span>{{ option.name }}:</span><span class="ml-5" data-label-value></span></label>
                    {%- assign clear_name = option.name | handleize -%}
                    {%- if product.selected_variant.id == blank and settings.product_variant_auto_select != 'enable' -%}
                        {%- assign disable_auto_select = false -%}
                        {%- if settings.product_variant_auto_select == 'disable' -%}
                            {%- assign disable_auto_select = true -%}
                        {%- elsif settings.product_variant_auto_select == 'first' and forloop.index != 1 -%}
                            {%- assign disable_auto_select = true -%}
                        {%- elsif settings.product_variant_auto_select == 'first_n_second' and forloop.index != 1 and forloop.index != 2 -%}
                            {%- assign disable_auto_select = true -%}
                        {%- endif -%}
                        {%- if auto_selected_options contains clear_name -%}
                            {%- assign disable_auto_select = false -%}
                        {%- endif -%}
                    {%- endif -%}
                    {%- if disable_auto_select -%}
                        {%- assign enable_select_variant_button = true -%}
                    {%- endif -%}
                    {%- if options_type_circle_color contains clear_name -%}
                        <div class="product-options__section d-flex flex-wrap px-2" data-style="color" data-property="{% render 'product-get-options-strip-symbols' with value: option.name %}"{% if disable_auto_select %} data-disable-auto-select{% endif %}>
                            {%- for value in option.values -%}
                                {% include 'product-get-options-res-status' with option_index0: option_index0 %}
                                <div class="product-options__value product-options__value--circle standard-color-{{ value | handleize }} rounded-circle text-hide cursor-pointer{% if disabled_value %} disabled{% elsif hidden_value %} disabled-hidden{% endif %}{% if active_value %} active{% endif %}"
                                    data-js-option-value
                                    data-value="{% render 'product-get-options-strip-symbols' with value: value %}"
                                    {{ trigger_attr }}
                                     data-scale="2"
                                    {%- if settings.tooltips_enable and product_option_enable_tooltips %}
                                        data-js-tooltip
                                        data-tippy-content="{{ value | replace: '<', '&lt;' | replace: '>', '&gt;' }}"
                                        data-tippy-placement="top"
                                        data-tippy-distance="6"
                                    {%- endif -%}
                                >{{ value | replace: '<', '&lt;' | replace: '>', '&gt;' }}<span></span></div>
                            {%- endfor -%}
                        </div>
                    {%- elsif options_type_circle_image contains clear_name -%}
                        <div class="product-options__section d-flex flex-wrap px-2" data-style="circle-image" data-property="{% render 'product-get-options-strip-symbols' with value: option.name %}"{% if disable_auto_select %} data-disable-auto-select{% endif %}>
                            {%- for value in option.values -%}
                                {% include 'product-get-options-res-status' with option_index0: option_index0 %}
                                {% include 'product-get-options-res-image' %}
                                <div class="product-options__value product-options__value--circle rounded-circle text-hide cursor-pointer{% if disabled_value %} disabled{% elsif hidden_value %} disabled-hidden{% endif %}{% if active_value %} active{% endif %} lazyload"
                                     data-js-option-value
                                     data-value="{% render 'product-get-options-strip-symbols' with value: value %}"
                                     {{ trigger_attr }}
                                    {%- if settings.product_format_pjpg -%}
                                        data-master="{{ option_image | img_url: '1x1', format: 'pjpg' | replace: '_1x1.', image_size_placeholder }}"
                                        data-bg="{{ option_image | img_url: '48x', format: 'pjpg' }}"
                                    {% else -%}
                                        data-master="{{ option_image | img_url: '1x1' | replace: '_1x1.', image_size_placeholder }}"
                                        data-bg="{{ option_image | img_url: '48x' }}"
                                    {% endif -%}
                                     data-scale="2"
                                    {%- if settings.tooltips_enable and product_option_enable_tooltips %}
                                        data-js-tooltip
                                        data-tippy-content="{{ value | replace: '<', '&lt;' | replace: '>', '&gt;' }}"
                                        data-tippy-placement="top"
                                        data-tippy-distance="6"
                                    {%- endif -%}
                                >{{ value | replace: '<', '&lt;' | replace: '>', '&gt;' }}<span></span></div>
                            {%- endfor -%}
                        </div>
                    {%- elsif options_type_square_color contains clear_name -%}
                        <div class="product-options__section d-flex flex-wrap" data-style="image" data-property="{% render 'product-get-options-strip-symbols' with value: option.name %}"{% if disable_auto_select %} data-disable-auto-select{% endif %}>
                            {%- for value in option.values -%}
                                {% include 'product-get-options-res-status' with option_index0: option_index0 %}
                                <div class="product-options__value product-options__value--square standard-color-{{ value | handleize }} text-hide cursor-pointer{% if disabled_value %} disabled{% elsif hidden_value %} disabled-hidden{% endif %}{% if active_value %} active{% endif %}"
                                     data-js-option-value
                                     data-value="{% render 'product-get-options-strip-symbols' with value: value %}"
                                    {{ trigger_attr }}
                                     data-scale="2"
                                    {%- if settings.tooltips_enable and product_option_enable_tooltips %}
                                        data-js-tooltip
                                        data-tippy-content="{{ value | replace: '<', '&lt;' | replace: '>', '&gt;' }}"
                                        data-tippy-placement="top"
                                        data-tippy-distance="6"
                                    {%- endif -%}
                                >{{ value | replace: '<', '&lt;' | replace: '>', '&gt;' }}<span></span></div>
                            {%- endfor -%}
                        </div>
                    {%- elsif options_type_square_image contains clear_name -%}
                        <div class="product-options__section d-flex flex-wrap" data-style="image" data-property="{% render 'product-get-options-strip-symbols' with value: option.name %}"{% if disable_auto_select %} data-disable-auto-select{% endif %}>
                            {%- for value in option.values -%}
                                {% include 'product-get-options-res-status' with option_index0: option_index0 %}
                                {% include 'product-get-options-res-image' %}
                                <div class="product-options__value product-options__value--square text-hide cursor-pointer{% if disabled_value %} disabled{% elsif hidden_value %} disabled-hidden{% endif %}{% if active_value %} active{% endif %} lazyload"
                                     data-js-option-value
                                     data-value="{% render 'product-get-options-strip-symbols' with value: value %}"
                                     {{ trigger_attr }}
                                        {%- if settings.product_format_pjpg -%}
                                            data-master="{{ option_image | img_url: '1x1', format: 'pjpg' | replace: '_1x1.', image_size_placeholder }}"
                                            data-bg="{{ option_image | img_url: '92x', format: 'pjpg' }}"
                                        {% else -%}
                                            data-master="{{ option_image | img_url: '1x1' | replace: '_1x1.', image_size_placeholder }}"
                                            data-bg="{{ option_image | img_url: '92x' }}"
                                        {% endif -%}
                                     data-scale="2"
                                    {%- if settings.tooltips_enable and product_option_enable_tooltips %}
                                        data-js-tooltip
                                        data-tippy-content="{{ value | replace: '<', '&lt;' | replace: '>', '&gt;' }}"
                                        data-tippy-placement="top"
                                        data-tippy-distance="6"
                                    {%- endif -%}
                                >{{ value | replace: '<', '&lt;' | replace: '>', '&gt;' }}<span></span></div>
                            {%- endfor -%}
                        </div>
                    {%- elsif options_type_large_text contains clear_name -%}
                        <div class="product-options__section d-flex flex-wrap" data-style="large-text" data-property="{% render 'product-get-options-strip-symbols' with value: option.name %}"{% if disable_auto_select %} data-disable-auto-select{% endif %}>
                            {%- for value in option.values -%}
                                {% include 'product-get-options-res-status' with option_index0: option_index0 %}
                                <div class="product-options__value product-options__value--large-text d-flex flex-center border cursor-pointer{% if disabled_value %} disabled{% elsif hidden_value %} disabled-hidden{% endif %}{% if active_value %} active{% endif %}"
                                     data-js-option-value
                                     data-value="{% render 'product-get-options-strip-symbols' with value: value %}"
                                     {{ trigger_attr }}
                                     data-scale="2"
                                >{{ value | replace: '<', '&lt;' | replace: '>', '&gt;' }}<span></span></div>
                            {%- endfor -%}
                        </div>
                    {%- elsif options_type_select contains clear_name -%}
                        <div class="select position-relative js-dropdown js-select">
                            {%- assign select_dropdown_html = '' -%}
                            <div data-js-dropdown-button>
                                <select class="product-options__section" data-style="select" data-property="{% render 'product-get-options-strip-symbols' with value: option.name %}" data-js-option-select{% if disable_auto_select %} data-disable-auto-select{% endif %}>
                                    {%- if enable_select_options_button -%}
                                        <option selected="selected" disabled="disabled">{{ default_title }}</option>
                                    {%- endif -%}
                                    {%- for value in option.values -%}
                                        {% include 'product-get-options-res-status' with option_index0: option_index0 %}
                                        <option{% if active_value and enable_select_options_button != true %} selected="selected"{% endif %} data-value="{% render 'product-get-options-strip-symbols' with value: value %}" value="{% render 'product-get-options-strip-symbols' with value: value %}"{% if disabled_value %} disabled="disabled"{% elsif hidden_value %} data-disabled-hidden="disabled"{% endif %}>{{ value | replace: '<', '&lt;' | replace: '>', '&gt;' }}</option>
                                        {% capture select_dropdown_html %}
                                            {{ select_dropdown_html }}
                                            <span{% if active_value and enable_select_options_button != true %} class="selected"{% endif %} data-value="{% render 'product-get-options-strip-symbols' with value: value %}"{% if disabled_value %} disabled="disabled"{% elsif hidden_value %} data-disabled-hidden="disabled"{% endif %} {{ trigger_attr }}>{{ value | replace: '<', '&lt;' | replace: '>', '&gt;' }}</span>
                                        {% endcapture %}
                                    {%- endfor -%}
                                </select>
                                <i class="d-none d-lg-block position-absolute right-0 mr-10">{% render 'icon-theme-229' %}</i>
                            </div>
                            <div class="select__dropdown dropdown d-none position-lg-absolute {% if select_dropdown_position == 'top' %}bottom{% else %}top{% endif %}-lg-100 left-lg-0" data-js-dropdown data-js-select-dropdown{% if disable_auto_select %} data-dropdown-unselected{% endif %}>
                                <div class="px-15 pb-30 py-lg-15">
                                    {{ select_dropdown_html }}
                                </div>
                            </div>
                        </div>
                    {%- else -%}
                        <div class="product-options__section d-flex flex-wrap" data-style="text" data-property="{% render 'product-get-options-strip-symbols' with value: option.name %}"{% if disable_auto_select %} data-disable-auto-select{% endif %}>
                            {%- for value in option.values -%}
                                {% include 'product-get-options-res-status' with option_index0: option_index0 %}
                                <div class="product-options__value product-options__value--text d-flex flex-center border cursor-pointer{% if disabled_value %} disabled{% elsif hidden_value %} disabled-hidden{% endif %}{% if active_value %} active{% endif %}"
                                     data-js-option-value
                                     data-value="{% render 'product-get-options-strip-symbols' with value: value %}"
                                     {{ trigger_attr }}
                                     data-scale="2"
                                >{{ value | replace: '<', '&lt;' | replace: '>', '&gt;' }}<span></span></div>
                            {%- endfor -%}
                        </div>
                    {%- endif -%}
                </div>
            {% endcapture %}
            {%- if label_value -%}
                {%- assign label_value_replace = 'data-label-value>' | append: label_value -%}
                {%- assign option_section_html = option_section_html | replace: 'data-label-value>', label_value_replace -%}
            {%- endif -%}
            {{ option_section_html }}
        {%- endfor -%}
    </div>
{%- endunless -%}
{%- assign option_section_html = null -%}
{%- assign change_history = false -%}
{%- assign trigger = null -%}
{%- assign trigger_id = null -%}