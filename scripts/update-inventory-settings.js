/**
 * 在庫設定自動化スクリプト
 *
 * このスクリプトは、Shopifyの商品在庫設定を自動化します。
 * - 在庫の場所は「PR」または「NY」
 * - 基本的な在庫数は1
 * - バリアントがある場合はバリアントごとに在庫1
 * - 商品状態が「廃棄済み」または「メンテナンス中」の場合は在庫0
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 在庫ロケーション取得のGraphQLクエリ
const GET_LOCATIONS_QUERY = gql`
  query getLocations {
    locations(first: 10) {
      edges {
        node {
          id
          name
          isActive
        }
      }
    }
  }
`;

// 商品取得のGraphQLクエリ
const GET_PRODUCTS_QUERY = gql`
  query getProducts($first: Int!, $after: String) {
    products(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          handle
          variants(first: 20) {
            edges {
              node {
                id
                title
                sku
                inventoryQuantity
                inventoryItem {
                  id
                }
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    }
  }
`;

// 在庫数更新のGraphQLミューテーション
const UPDATE_INVENTORY_QUANTITY = gql`
  mutation inventoryBulkAdjustQuantityAtLocation($inventoryItemAdjustments: [InventoryAdjustItemInput!]!, $locationId: ID!) {
    inventoryBulkAdjustQuantityAtLocation(
      inventoryItemAdjustments: $inventoryItemAdjustments,
      locationId: $locationId
    ) {
      inventoryLevels {
        available
        inventoryItem {
          id
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 在庫ロケーションを取得する関数
async function getLocations() {
  try {
    const result = await client.request(GET_LOCATIONS_QUERY);
    return result.locations.edges.map(edge => edge.node);
  } catch (error) {
    console.error('在庫ロケーション取得中にエラーが発生しました:', error);
    return [];
  }
}

// 商品を取得する関数
async function getProducts(first = 50, after = null) {
  try {
    const result = await client.request(GET_PRODUCTS_QUERY, {
      first,
      after
    });
    return result.products;
  } catch (error) {
    console.error('商品取得中にエラーが発生しました:', error);
    return { edges: [], pageInfo: { hasNextPage: false } };
  }
}

// 在庫数を更新する関数
async function updateInventoryQuantities(inventoryItemAdjustments, locationId) {
  try {
    if (inventoryItemAdjustments.length === 0) {
      console.log('更新する在庫アイテムがありません。');
      return true;
    }

    const result = await client.request(UPDATE_INVENTORY_QUANTITY, {
      inventoryItemAdjustments,
      locationId
    });

    if (result.inventoryBulkAdjustQuantityAtLocation.userErrors.length > 0) {
      console.error('在庫数更新中にエラーが発生しました:', result.inventoryBulkAdjustQuantityAtLocation.userErrors);
      return false;
    }

    console.log(`${inventoryItemAdjustments.length}件の在庫数を更新しました。`);
    return true;
  } catch (error) {
    console.error('在庫数更新中にエラーが発生しました:', error);
    return false;
  }
}

// 商品の状態を取得する関数
function getProductStatus(metafields) {
  // rental.status メタフィールドを検索
  const statusMetafield = metafields.find(
    metafield => metafield.namespace === 'rental' && metafield.key === 'status'
  );

  if (statusMetafield) {
    return statusMetafield.value;
  }

  // rental.is_disposed メタフィールドを検索
  const isDisposedMetafield = metafields.find(
    metafield => metafield.namespace === 'rental' && metafield.key === 'is_disposed'
  );

  if (isDisposedMetafield && (isDisposedMetafield.value === 'true' || isDisposedMetafield.value === 'TRUE')) {
    return 'unavailable';
  }

  // rental.basic_info メタフィールドを検索
  const basicInfoMetafield = metafields.find(
    metafield => metafield.namespace === 'rental' && metafield.key === 'basic_info'
  );

  if (basicInfoMetafield) {
    try {
      const basicInfo = JSON.parse(basicInfoMetafield.value);
      if (basicInfo.status) {
        return basicInfo.status;
      }
    } catch (error) {
      console.error('basic_infoのパース中にエラーが発生しました:', error);
    }
  }

  // rental.inventory_items メタフィールドを検索
  const inventoryItemsMetafield = metafields.find(
    metafield => metafield.namespace === 'rental' && metafield.key === 'inventory_items'
  );

  if (inventoryItemsMetafield) {
    try {
      const inventoryItems = JSON.parse(inventoryItemsMetafield.value);
      if (inventoryItems && inventoryItems.length > 0) {
        return inventoryItems[0].status;
      }
    } catch (error) {
      console.error('inventory_itemsのパース中にエラーが発生しました:', error);
    }
  }

  return 'available'; // デフォルトは利用可能
}

// 商品の在庫場所を取得する関数
function getProductLocation(metafields) {
  // rental.location メタフィールドを検索
  const locationMetafield = metafields.find(
    metafield => metafield.namespace === 'rental' && metafield.key === 'location'
  );

  if (locationMetafield) {
    return locationMetafield.value;
  }

  // rental.inventory_items メタフィールドを検索
  const inventoryItemsMetafield = metafields.find(
    metafield => metafield.namespace === 'rental' && metafield.key === 'inventory_items'
  );

  if (inventoryItemsMetafield) {
    try {
      const inventoryItems = JSON.parse(inventoryItemsMetafield.value);
      if (inventoryItems && inventoryItems.length > 0 && inventoryItems[0].location) {
        return inventoryItems[0].location;
      }
    } catch (error) {
      console.error('inventory_itemsのパース中にエラーが発生しました:', error);
    }
  }

  return 'NY'; // デフォルトはNY
}

// メイン処理
async function main() {
  console.log('在庫設定の自動化を開始します...');

  // 在庫ロケーションを取得
  const locations = await getLocations();
  if (locations.length === 0) {
    console.error('在庫ロケーションが見つかりませんでした。');
    return;
  }

  console.log(`${locations.length}件の在庫ロケーションが見つかりました。`);
  
  // PR と NY のロケーションIDを取得
  const prLocation = locations.find(location => location.name.includes('PR'));
  const nyLocation = locations.find(location => location.name.includes('NY'));
  
  if (!prLocation && !nyLocation) {
    console.error('PR または NY の在庫ロケーションが見つかりませんでした。');
    console.log('利用可能なロケーション:');
    locations.forEach(location => {
      console.log(`- ${location.name} (${location.id})`);
    });
    
    // デフォルトロケーションを使用
    console.log('デフォルトロケーションを使用します。');
  }

  // 商品を取得して在庫を更新
  let hasNextPage = true;
  let cursor = null;
  let processedCount = 0;
  
  while (hasNextPage) {
    const products = await getProducts(50, cursor);
    
    if (products.edges.length === 0) {
      break;
    }
    
    console.log(`${products.edges.length}件の商品を処理中...`);
    
    // PR と NY のロケーションごとに在庫調整リストを作成
    const prAdjustments = [];
    const nyAdjustments = [];
    
    for (const edge of products.edges) {
      const product = edge.node;
      const metafields = product.metafields.edges.map(edge => edge.node);
      
      // 商品の状態を取得
      const status = getProductStatus(metafields);
      
      // 商品の在庫場所を取得
      const location = getProductLocation(metafields);
      
      // 在庫数を決定（廃棄済みまたはメンテナンス中は0、それ以外は1）
      const quantity = (status === 'unavailable' || status === 'maintenance') ? 0 : 1;
      
      // 各バリアントの在庫を更新
      for (const variantEdge of product.variants.edges) {
        const variant = variantEdge.node;
        
        // 現在の在庫数と目標在庫数の差分を計算
        const currentQuantity = variant.inventoryQuantity || 0;
        const adjustment = quantity - currentQuantity;
        
        // 調整が必要な場合のみリストに追加
        if (adjustment !== 0) {
          const adjustmentItem = {
            inventoryItemId: variant.inventoryItem.id,
            availableDelta: adjustment
          };
          
          if (location === 'PR') {
            prAdjustments.push(adjustmentItem);
          } else {
            nyAdjustments.push(adjustmentItem);
          }
        }
      }
    }
    
    // PR ロケーションの在庫を更新
    if (prLocation && prAdjustments.length > 0) {
      console.log(`PR ロケーションの在庫を更新中... (${prAdjustments.length}件)`);
      await updateInventoryQuantities(prAdjustments, prLocation.id);
    }
    
    // NY ロケーションの在庫を更新
    if (nyLocation && nyAdjustments.length > 0) {
      console.log(`NY ロケーションの在庫を更新中... (${nyAdjustments.length}件)`);
      await updateInventoryQuantities(nyAdjustments, nyLocation.id);
    }
    
    // デフォルトロケーションを使用する場合
    if (!prLocation && !nyLocation && (prAdjustments.length > 0 || nyAdjustments.length > 0)) {
      const defaultLocation = locations[0];
      console.log(`デフォルトロケーション (${defaultLocation.name}) の在庫を更新中... (${prAdjustments.length + nyAdjustments.length}件)`);
      await updateInventoryQuantities([...prAdjustments, ...nyAdjustments], defaultLocation.id);
    }
    
    processedCount += products.edges.length;
    console.log(`${processedCount}件の商品を処理しました。`);
    
    // 次のページがあるか確認
    hasNextPage = products.pageInfo.hasNextPage;
    cursor = products.pageInfo.endCursor;
  }
  
  console.log('在庫設定の自動化が完了しました。');
}

// スクリプト実行
main().catch(error => {
  console.error('スクリプト実行中にエラーが発生しました:', error);
  process.exit(1);
});
