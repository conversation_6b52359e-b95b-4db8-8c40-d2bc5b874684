/**
 * 包括的な予約テストスクリプト
 * 
 * このスクリプトは、商品情報、顧客情報の削除や新規作成を含めた
 * あらゆるパターンの予約テストを実行します。
 */

const { PrismaClient } = require('@prisma/client');
const { v4: uuidv4 } = require('uuid');
const prisma = new PrismaClient();

// テスト用のShopify Admin APIクライアントモック
const adminApiMock = {
  graphql: async (query, variables) => {
    console.log('GraphQL API呼び出し:', query.substring(0, 50) + '...');
    return {
      json: async () => ({
        data: {
          draftOrderCreate: {
            draftOrder: {
              id: `gid://shopify/DraftOrder/${uuidv4()}`,
              name: `#D${Math.floor(Math.random() * 10000)}`,
              totalPrice: '10000',
              invoiceUrl: `https://example.com/invoice/${uuidv4()}`
            },
            userErrors: []
          }
        }
      })
    };
  }
};

// テスト用の商品データ
const testProducts = [
  {
    title: 'テスト商品1',
    sku: 'TEST-001',
    price: 10000,
    status: 'ACTIVE'
  },
  {
    title: 'テスト商品2',
    sku: 'TEST-002',
    price: 15000,
    status: 'ACTIVE'
  },
  {
    title: 'テスト商品3',
    sku: 'TEST-003',
    price: 20000,
    status: 'MAINTENANCE'
  }
];

// テスト用の顧客データ
const testCustomers = [
  {
    email: '<EMAIL>',
    name: 'テスト顧客1'
  },
  {
    email: '<EMAIL>',
    name: 'テスト顧客2'
  }
];

// 日付ユーティリティ関数
function addDays(date, days) {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

// 予約サービスのインポート（実際のファイルパスに合わせて調整）
const { createBookingWithDraftOrder, cancelBooking } = require('../app/services/booking.service');

/**
 * データベースをクリーンアップする
 */
async function cleanupDatabase() {
  console.log('データベースのクリーンアップを開始...');
  
  // 予約情報を削除
  await prisma.booking.deleteMany({
    where: {
      shop: 'test-shop.myshopify.com'
    }
  });
  
  // 在庫カレンダーを削除
  await prisma.inventoryCalendar.deleteMany({
    where: {
      shop: 'test-shop.myshopify.com'
    }
  });
  
  // 商品情報を削除
  await prisma.product.deleteMany({
    where: {
      shop: 'test-shop.myshopify.com'
    }
  });
  
  console.log('データベースのクリーンアップが完了しました');
}

/**
 * テスト用の商品を作成する
 */
async function createTestProducts() {
  console.log('テスト用の商品を作成中...');
  
  const products = [];
  
  for (const productData of testProducts) {
    const product = await prisma.product.create({
      data: {
        ...productData,
        id: uuidv4(),
        shop: 'test-shop.myshopify.com',
        shopifyId: `gid://shopify/Product/${Math.floor(Math.random() * 1000000)}`,
        metadata: {
          testProduct: true
        }
      }
    });
    
    products.push(product);
    console.log(`商品を作成しました: ${product.title} (ID: ${product.id})`);
  }
  
  return products;
}

/**
 * 予約を作成する
 */
async function createBooking(productId, startDate, endDate, customerEmail, customerName) {
  console.log(`予約を作成中... 商品ID: ${productId}, 期間: ${startDate.toISOString()} - ${endDate.toISOString()}`);
  
  const result = await createBookingWithDraftOrder({
    shop: 'test-shop.myshopify.com',
    productId,
    variantId: '1', // テスト用の固定値
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    customerEmail,
    customerName,
    notes: 'テスト予約',
    bookingType: 'PROVISIONAL',
    admin: adminApiMock
  });
  
  if (result.success) {
    console.log(`予約が成功しました: ${result.booking.bookingId}`);
  } else {
    console.error(`予約が失敗しました: ${result.error}`);
  }
  
  return result;
}

/**
 * 予約をキャンセルする
 */
async function cancelTestBooking(bookingId) {
  console.log(`予約をキャンセル中... 予約ID: ${bookingId}`);
  
  const result = await cancelBooking(bookingId, {
    cancelReason: 'テストキャンセル',
    notes: 'テスト用のキャンセル',
    admin: adminApiMock,
    shop: 'test-shop.myshopify.com'
  });
  
  if (result.success) {
    console.log(`予約のキャンセルが成功しました: ${bookingId}`);
  } else {
    console.error(`予約のキャンセルが失敗しました: ${result.error}`);
  }
  
  return result;
}

/**
 * 商品を削除する
 */
async function deleteProduct(productId) {
  console.log(`商品を削除中... 商品ID: ${productId}`);
  
  await prisma.product.delete({
    where: {
      id: productId
    }
  });
  
  console.log(`商品を削除しました: ${productId}`);
}

/**
 * メインのテスト実行関数
 */
async function runTests() {
  try {
    console.log('包括的な予約テストを開始します...');
    
    // 1. データベースのクリーンアップ
    await cleanupDatabase();
    
    // 2. テスト用の商品を作成
    const products = await createTestProducts();
    
    // 3. 既存商品の予約テスト
    console.log('\nテストケース1: 既存商品の予約');
    const startDate1 = addDays(new Date(), 7);
    const endDate1 = addDays(new Date(), 10);
    const booking1 = await createBooking(
      products[0].id,
      startDate1,
      endDate1,
      testCustomers[0].email,
      testCustomers[0].name
    );
    
    // 4. 新規作成商品の予約テスト
    console.log('\nテストケース2: 新規作成商品の予約');
    const newProduct = await prisma.product.create({
      data: {
        id: uuidv4(),
        shop: 'test-shop.myshopify.com',
        title: '新規テスト商品',
        sku: 'NEW-TEST-001',
        price: 25000,
        status: 'ACTIVE',
        shopifyId: `gid://shopify/Product/${Math.floor(Math.random() * 1000000)}`,
        metadata: {
          testProduct: true
        }
      }
    });
    console.log(`新規商品を作成しました: ${newProduct.title} (ID: ${newProduct.id})`);
    
    const startDate2 = addDays(new Date(), 14);
    const endDate2 = addDays(new Date(), 17);
    const booking2 = await createBooking(
      newProduct.id,
      startDate2,
      endDate2,
      testCustomers[1].email,
      testCustomers[1].name
    );
    
    // 5. 同じ日程での重複予約テスト
    console.log('\nテストケース3: 同じ日程での重複予約');
    const booking3 = await createBooking(
      products[0].id,
      startDate1,
      endDate1,
      testCustomers[1].email,
      testCustomers[1].name
    );
    
    // 6. 異なる日程での同一商品予約テスト
    console.log('\nテストケース4: 異なる日程での同一商品予約');
    const startDate3 = addDays(new Date(), 21);
    const endDate3 = addDays(new Date(), 24);
    const booking4 = await createBooking(
      products[0].id,
      startDate3,
      endDate3,
      testCustomers[0].email,
      testCustomers[0].name
    );
    
    // 7. 商品の削除と削除後の予約試行テスト
    console.log('\nテストケース5: 商品の削除と削除後の予約試行');
    await deleteProduct(products[1].id);
    
    const startDate4 = addDays(new Date(), 28);
    const endDate4 = addDays(new Date(), 31);
    const booking5 = await createBooking(
      products[1].id,
      startDate4,
      endDate4,
      testCustomers[0].email,
      testCustomers[0].name
    );
    
    // 8. 予約のキャンセルテスト
    console.log('\nテストケース6: 予約のキャンセル');
    if (booking1.success) {
      const cancelResult = await cancelTestBooking(booking1.booking.id);
    }
    
    // 9. キャンセルした予約と同じ日程での新規予約テスト
    console.log('\nテストケース7: キャンセルした予約と同じ日程での新規予約');
    const booking6 = await createBooking(
      products[0].id,
      startDate1,
      endDate1,
      testCustomers[1].email,
      testCustomers[1].name
    );
    
    // 10. 存在しない商品IDでの予約テスト
    console.log('\nテストケース8: 存在しない商品IDでの予約');
    const nonExistentProductId = uuidv4();
    const booking7 = await createBooking(
      nonExistentProductId,
      startDate1,
      endDate1,
      testCustomers[0].email,
      testCustomers[0].name
    );
    
    // 11. 空の商品IDでの予約テスト
    console.log('\nテストケース9: 空の商品IDでの予約');
    const booking8 = await createBooking(
      '',
      startDate1,
      endDate1,
      testCustomers[0].email,
      testCustomers[0].name
    );
    
    // 12. 複数商品の同時予約テスト（実際のアプリケーションでは実装されていない可能性があるため、コメントアウト）
    /*
    console.log('\nテストケース10: 複数商品の同時予約');
    const multipleBookingResult = await createMultipleBookings([
      {
        productId: products[0].id,
        startDate: addDays(new Date(), 35),
        endDate: addDays(new Date(), 38)
      },
      {
        productId: newProduct.id,
        startDate: addDays(new Date(), 35),
        endDate: addDays(new Date(), 38)
      }
    ], testCustomers[0].email, testCustomers[0].name);
    console.log('複数商品の同時予約結果:', multipleBookingResult);
    */
    
    console.log('\nすべてのテストが完了しました');
    
    // テスト結果のサマリーを表示
    console.log('\n===== テスト結果サマリー =====');
    console.log('テストケース1 (既存商品の予約):', booking1.success ? '成功' : '失敗');
    console.log('テストケース2 (新規作成商品の予約):', booking2.success ? '成功' : '失敗');
    console.log('テストケース3 (同じ日程での重複予約):', booking3.success ? '成功' : '失敗');
    console.log('テストケース4 (異なる日程での同一商品予約):', booking4.success ? '成功' : '失敗');
    console.log('テストケース5 (商品の削除と削除後の予約試行):', booking5.success ? '成功' : '失敗');
    console.log('テストケース7 (キャンセルした予約と同じ日程での新規予約):', booking6.success ? '成功' : '失敗');
    console.log('テストケース8 (存在しない商品IDでの予約):', booking7.success ? '成功' : '失敗');
    console.log('テストケース9 (空の商品IDでの予約):', booking8.success ? '成功' : '失敗');
    
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// テストを実行
runTests().catch(e => {
  console.error('テスト実行中に予期せぬエラーが発生しました:', e);
  process.exit(1);
});
