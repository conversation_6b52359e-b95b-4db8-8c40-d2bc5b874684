/**
 * 予約キャンセルテストスクリプト
 *
 * このスクリプトは、予約のキャンセル機能をテストします。
 * 実行方法: node scripts/test-booking-cancel.js [商品ID] [予約ID] [キャンセルタイプ]
 * キャンセルタイプ: status（ステータス変更）, delete（完全削除）
 */

import { PrismaClient } from '@prisma/client';
import { format } from 'date-fns';
import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品情報を取得するクエリ
const GET_PRODUCT = gql`
  query getProduct($id: ID!) {
    product(id: $id) {
      id
      title
      status
      variants(first: 5) {
        edges {
          node {
            id
            title
            sku
            inventoryQuantity
            inventoryPolicy
          }
        }
      }
      metafields(first: 10) {
        edges {
          node {
            namespace
            key
            value
          }
        }
      }
    }
  }
`;

// メタフィールドを更新するミューテーション
const UPDATE_METAFIELD = gql`
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * 商品情報を取得する関数
 * @param {string} productId 商品ID
 * @returns {Promise<object>} 商品情報
 */
async function getProductInfo(productId) {
  try {
    // Shopify IDを正規化
    const shopifyId = productId.startsWith('gid://shopify/Product/')
      ? productId
      : `gid://shopify/Product/${productId}`;

    console.log(`商品情報を取得中... (Shopify ID: ${shopifyId})`);

    // Shopifyから商品情報を取得
    const result = await shopifyClient.request(GET_PRODUCT, {
      id: shopifyId
    });

    if (!result.product) {
      throw new Error(`商品ID ${productId} が見つかりません`);
    }

    const product = result.product;
    console.log(`商品情報を取得しました: ${product.title}`);

    // データベースから商品情報を取得
    const dbProduct = await prisma.product.findFirst({
      where: {
        shopifyId: productId.replace('gid://shopify/Product/', '')
      }
    });

    if (!dbProduct) {
      throw new Error(`商品ID ${productId} がデータベースに存在しません`);
    }

    return { shopifyProduct: product, dbProduct };
  } catch (error) {
    console.error('商品情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 予約情報を取得する関数
 * @param {string} bookingId 予約ID
 * @returns {Promise<object>} 予約情報
 */
async function getBookingInfo(bookingId) {
  try {
    // データベースから予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: {
        id: bookingId
      }
    });

    if (!booking) {
      throw new Error(`予約ID ${bookingId} が見つかりません`);
    }

    console.log(`予約情報を取得しました: ${booking.bookingId}`);
    return booking;
  } catch (error) {
    console.error('予約情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 予約情報をメタフィールド用のフォーマットに変換する関数
 * @param {Array} bookings 予約情報
 * @returns {object} メタフィールド用の予約情報
 */
function formatBookingsForMetafield(bookings) {
  // 予約情報をメタフィールド用のフォーマットに変換
  const formattedBookings = bookings.map(booking => ({
    id: booking.bookingId,
    startDate: format(booking.startDate, 'yyyy-MM-dd'),
    endDate: format(booking.endDate, 'yyyy-MM-dd'),
    status: booking.status,
    customerName: booking.customerName,
    customerEmail: booking.customerEmail
  }));

  // メタフィールド用のデータ構造
  const metafieldData = {
    bookings: formattedBookings,
    availability: {
      rentalStatus: 'available',
      startDate: format(new Date(), 'yyyy-MM-dd'),
      maintenanceDates: [],
      blockedDates: []
    }
  };

  return metafieldData;
}

/**
 * 予約情報をShopifyメタフィールドに更新する関数
 * @param {string} productId 商品ID
 * @param {Array} bookings 予約情報
 * @returns {Promise<object>} 更新結果
 */
async function updateBookingMetafield(productId, bookings) {
  try {
    console.log('予約情報メタフィールドを更新中...');

    // Shopify IDを正規化
    const shopifyId = productId.startsWith('gid://shopify/Product/')
      ? productId
      : `gid://shopify/Product/${productId}`;

    // 予約情報をメタフィールド用のフォーマットに変換
    const metafieldData = formatBookingsForMetafield(bookings);

    // メタフィールドの設定
    const result = await shopifyClient.request(UPDATE_METAFIELD, {
      metafields: [
        {
          ownerId: shopifyId,
          namespace: 'rental',
          key: 'bookings',
          value: JSON.stringify(metafieldData),
          type: 'json'
        }
      ]
    });

    if (result.metafieldsSet.userErrors.length > 0) {
      throw new Error(`メタフィールドの設定中にエラーが発生しました: ${JSON.stringify(result.metafieldsSet.userErrors)}`);
    }

    console.log('予約情報メタフィールドを更新しました');
    return result.metafieldsSet.metafields[0];
  } catch (error) {
    console.error('予約情報メタフィールドの更新中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 予約をキャンセルする関数（ステータス変更）
 * @param {string} bookingId 予約ID
 * @returns {Promise<object>} キャンセル結果
 */
async function cancelBookingByStatus(bookingId) {
  try {
    console.log(`予約ID ${bookingId} をキャンセル中...（ステータス変更）`);

    // 予約情報を取得
    const booking = await getBookingInfo(bookingId);

    // ステータスを「CANCELLED」に変更
    const cancelledBooking = await prisma.booking.update({
      where: {
        id: bookingId
      },
      data: {
        status: 'CANCELLED',
        updatedAt: new Date()
      }
    });

    console.log('予約をキャンセルしました:');
    console.log(`予約ID: ${cancelledBooking.bookingId}`);
    console.log(`旧ステータス: ${booking.status}`);
    console.log(`新ステータス: ${cancelledBooking.status}`);

    // 商品の全予約情報を取得（キャンセルされた予約は除外）
    const allBookings = await prisma.booking.findMany({
      where: {
        productId: booking.productId,
        status: {
          in: ['PROVISIONAL', 'CONFIRMED']
        }
      }
    });

    // Shopifyメタフィールドを更新
    try {
      // 商品情報を取得
      const product = await prisma.product.findUnique({
        where: {
          id: booking.productId
        }
      });

      if (product) {
        await updateBookingMetafield(`gid://shopify/Product/${product.shopifyId}`, allBookings);
        console.log('Shopifyメタフィールドを更新しました');
      }
    } catch (metafieldError) {
      console.error('Shopifyメタフィールドの更新中にエラーが発生しました:', metafieldError);
      // メタフィールドの更新に失敗しても、予約のキャンセル自体は成功とする
    }

    return { success: true, booking: cancelledBooking };
  } catch (error) {
    console.error('予約キャンセル中にエラーが発生しました:', error);
    return { success: false, error };
  }
}

/**
 * 予約を完全に削除する関数
 * @param {string} bookingId 予約ID
 * @returns {Promise<object>} 削除結果
 */
async function deleteBooking(bookingId) {
  try {
    console.log(`予約ID ${bookingId} を削除中...（完全削除）`);

    // 予約情報を取得
    const booking = await getBookingInfo(bookingId);
    const productId = booking.productId;

    // 予約を削除
    await prisma.booking.delete({
      where: {
        id: bookingId
      }
    });

    console.log('予約を削除しました:');
    console.log(`予約ID: ${booking.bookingId}`);
    console.log(`商品ID: ${booking.productId}`);
    console.log(`期間: ${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')}`);

    // 商品の全予約情報を取得（削除された予約は含まれない）
    const allBookings = await prisma.booking.findMany({
      where: {
        productId,
        status: {
          in: ['PROVISIONAL', 'CONFIRMED']
        }
      }
    });

    // Shopifyメタフィールドを更新
    try {
      // 商品情報を取得
      const product = await prisma.product.findUnique({
        where: {
          id: productId
        }
      });

      if (product) {
        await updateBookingMetafield(`gid://shopify/Product/${product.shopifyId}`, allBookings);
        console.log('Shopifyメタフィールドを更新しました');
      }
    } catch (metafieldError) {
      console.error('Shopifyメタフィールドの更新中にエラーが発生しました:', metafieldError);
      // メタフィールドの更新に失敗しても、予約の削除自体は成功とする
    }

    return { success: true };
  } catch (error) {
    console.error('予約削除中にエラーが発生しました:', error);
    return { success: false, error };
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    // コマンドライン引数から商品ID、予約ID、キャンセルタイプを取得
    const productId = process.argv[2];
    const bookingId = process.argv[3];
    const cancelType = process.argv[4] || 'status';

    if (!productId || !bookingId) {
      console.error('商品IDまたは予約IDが指定されていません。使用方法: node scripts/test-booking-cancel.js [商品ID] [予約ID] [キャンセルタイプ]');
      process.exit(1);
    }

    console.log(`商品ID ${productId} の予約ID ${bookingId} をキャンセルします...`);
    console.log(`キャンセルタイプ: ${cancelType}`);

    // 商品情報を取得
    const product = await getProductInfo(productId);

    // キャンセルタイプに応じたテストを実行
    let testResult;
    switch (cancelType) {
      case 'status':
        testResult = await cancelBookingByStatus(bookingId);
        break;
      case 'delete':
        testResult = await deleteBooking(bookingId);
        break;
      default:
        console.error(`未知のキャンセルタイプです: ${cancelType}`);
        process.exit(1);
    }

    if (testResult.success) {
      console.log('予約キャンセルテストが成功しました！');
    } else {
      console.error('予約キャンセルテストが失敗しました:', testResult.error);
      process.exit(1);
    }
  } catch (error) {
    console.error('予約キャンセルテスト中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
