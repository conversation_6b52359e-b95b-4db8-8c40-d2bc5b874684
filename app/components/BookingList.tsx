/**
 * 予約一覧コンポーネント
 * 
 * 管理画面で使用する予約一覧コンポーネント
 * 仮予約と本予約の区別表示を改善
 */

import { useEffect, useState } from 'react';
import { 
  ResourceList, 
  ResourceItem, 
  Text, 
  Badge, 
  ButtonGroup, 
  Button,
  Spinner,
  EmptyState,
  InlineStack,
  Box,
  Card,
  Pagination,
  TextField,
  Select,
  Filters,
  LegacyStack
} from '@shopify/polaris';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';

// 予約タイプ
type BookingType = 'all' | 'provisional' | 'confirmed';

// 予約ステータス
type BookingStatus = 'PROVISIONAL' | 'CONFIRMED' | 'CANCELLED' | 'COMPLETED';

// 予約情報
interface Booking {
  id: string;
  bookingId: string;
  productId: string;
  productTitle: string;
  startDate: string;
  endDate: string;
  customerName: string | null;
  customerEmail: string | null;
  customerPhone: string | null;
  bookingType: 'PROVISIONAL' | 'CONFIRMED';
  status: BookingStatus;
  expiresAt: string | null;
  createdAt: string;
  updatedAt: string;
}

// コンポーネントのProps
interface BookingListProps {
  type: BookingType;
}

/**
 * 予約一覧コンポーネント
 */
export default function BookingList({ type }: BookingListProps) {
  // 状態
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortOrder, setSortOrder] = useState('desc');

  // 予約データを取得
  useEffect(() => {
    const fetchBookings = async () => {
      try {
        setLoading(true);
        setError(null);

        // APIからデータを取得（実際の実装ではここでAPIを呼び出す）
        // ここではモックデータを使用
        const mockData: Booking[] = [
          {
            id: '1',
            bookingId: 'B-20250501-001',
            productId: 'p1',
            productTitle: 'ソファ A',
            startDate: '2025-05-10T00:00:00.000Z',
            endDate: '2025-05-15T00:00:00.000Z',
            customerName: '山田太郎',
            customerEmail: '<EMAIL>',
            customerPhone: '090-1234-5678',
            bookingType: 'CONFIRMED',
            status: 'CONFIRMED',
            expiresAt: null,
            createdAt: '2025-05-01T10:00:00.000Z',
            updatedAt: '2025-05-01T10:00:00.000Z'
          },
          {
            id: '2',
            bookingId: 'B-20250502-001',
            productId: 'p2',
            productTitle: 'テーブル B',
            startDate: '2025-05-12T00:00:00.000Z',
            endDate: '2025-05-14T00:00:00.000Z',
            customerName: '佐藤花子',
            customerEmail: '<EMAIL>',
            customerPhone: '090-8765-4321',
            bookingType: 'PROVISIONAL',
            status: 'PROVISIONAL',
            expiresAt: '2025-05-05T00:00:00.000Z',
            createdAt: '2025-05-02T11:00:00.000Z',
            updatedAt: '2025-05-02T11:00:00.000Z'
          },
          {
            id: '3',
            bookingId: 'B-20250503-001',
            productId: 'p3',
            productTitle: 'チェア C',
            startDate: '2025-05-20T00:00:00.000Z',
            endDate: '2025-05-25T00:00:00.000Z',
            customerName: '鈴木一郎',
            customerEmail: '<EMAIL>',
            customerPhone: '090-2345-6789',
            bookingType: 'CONFIRMED',
            status: 'CONFIRMED',
            expiresAt: null,
            createdAt: '2025-05-03T12:00:00.000Z',
            updatedAt: '2025-05-03T12:00:00.000Z'
          }
        ];

        // 予約タイプでフィルタリング
        let filteredData = mockData;
        if (type === 'provisional') {
          filteredData = mockData.filter(booking => booking.bookingType === 'PROVISIONAL');
        } else if (type === 'confirmed') {
          filteredData = mockData.filter(booking => booking.bookingType === 'CONFIRMED');
        }

        setBookings(filteredData);
        setLoading(false);
      } catch (err) {
        console.error('予約データの取得中にエラーが発生しました:', err);
        setError('予約データの取得中にエラーが発生しました');
        setLoading(false);
      }
    };

    fetchBookings();
  }, [type]);

  // 検索とフィルタリング
  const filteredBookings = bookings.filter(booking => {
    // 検索条件
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = 
      booking.bookingId.toLowerCase().includes(searchLower) ||
      booking.productTitle.toLowerCase().includes(searchLower) ||
      (booking.customerName && booking.customerName.toLowerCase().includes(searchLower)) ||
      (booking.customerEmail && booking.customerEmail.toLowerCase().includes(searchLower));
    
    // ステータスフィルター
    const matchesStatus = 
      statusFilter === 'all' || 
      booking.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // ソート
  const sortedBookings = [...filteredBookings].sort((a, b) => {
    const dateA = new Date(a.startDate).getTime();
    const dateB = new Date(b.startDate).getTime();
    return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
  });

  // ページネーション
  const totalPages = Math.ceil(sortedBookings.length / itemsPerPage);
  const paginatedBookings = sortedBookings.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // 予約ステータスに応じたバッジを取得
  const getStatusBadge = (status: BookingStatus, bookingType: 'PROVISIONAL' | 'CONFIRMED') => {
    switch (status) {
      case 'PROVISIONAL':
        return <Badge status="attention">仮予約</Badge>;
      case 'CONFIRMED':
        return <Badge status="success">本予約</Badge>;
      case 'CANCELLED':
        return <Badge status="critical">キャンセル</Badge>;
      case 'COMPLETED':
        return <Badge status="info">完了</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // 期限切れ日が近い場合の警告バッジ
  const getExpirationBadge = (expiresAt: string | null) => {
    if (!expiresAt) return null;
    
    const expirationDate = new Date(expiresAt);
    const now = new Date();
    const daysUntilExpiration = Math.ceil((expirationDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysUntilExpiration < 0) {
      return <Badge status="critical">期限切れ</Badge>;
    } else if (daysUntilExpiration <= 3) {
      return <Badge status="warning">期限間近 ({daysUntilExpiration}日)</Badge>;
    }
    
    return null;
  };

  // ローディング中
  if (loading) {
    return (
      <Box padding="400" textAlign="center">
        <Spinner size="large" />
        <div style={{ marginTop: '1rem' }}>
          <Text as="p">予約データを読み込み中...</Text>
        </div>
      </Box>
    );
  }

  // エラー
  if (error) {
    return (
      <Box padding="400">
        <Card>
          <Card.Section>
            <EmptyState
              heading="エラーが発生しました"
              image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
            >
              <p>{error}</p>
              <Button primary onClick={() => window.location.reload()}>再読み込み</Button>
            </EmptyState>
          </Card.Section>
        </Card>
      </Box>
    );
  }

  // 予約がない場合
  if (bookings.length === 0) {
    return (
      <Box padding="400">
        <Card>
          <Card.Section>
            <EmptyState
              heading={`${type === 'provisional' ? '仮予約' : type === 'confirmed' ? '本予約' : '予約'}がありません`}
              image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
            >
              <p>現在、表示できる予約はありません。</p>
              <Button primary url="/admin/bookings/new">新規予約を作成</Button>
            </EmptyState>
          </Card.Section>
        </Card>
      </Box>
    );
  }

  return (
    <div>
      {/* 検索とフィルター */}
      <Box paddingBlockEnd="400">
        <Card>
          <Card.Section>
            <LegacyStack distribution="fillEvenly">
              <TextField
                label="検索"
                value={searchTerm}
                onChange={setSearchTerm}
                placeholder="予約番号、商品名、顧客名など"
                clearButton
                onClearButtonClick={() => setSearchTerm('')}
              />
              <Select
                label="ステータス"
                options={[
                  { label: 'すべて', value: 'all' },
                  { label: '仮予約', value: 'PROVISIONAL' },
                  { label: '本予約', value: 'CONFIRMED' },
                  { label: 'キャンセル', value: 'CANCELLED' },
                  { label: '完了', value: 'COMPLETED' }
                ]}
                value={statusFilter}
                onChange={setStatusFilter}
              />
              <Select
                label="並び順"
                options={[
                  { label: '開始日（新しい順）', value: 'desc' },
                  { label: '開始日（古い順）', value: 'asc' }
                ]}
                value={sortOrder}
                onChange={setSortOrder}
              />
            </LegacyStack>
          </Card.Section>
        </Card>
      </Box>

      {/* 予約一覧 */}
      <ResourceList
        resourceName={{ singular: '予約', plural: '予約' }}
        items={paginatedBookings}
        renderItem={(booking) => {
          const { id, bookingId, productTitle, startDate, endDate, customerName, customerEmail, bookingType, status, expiresAt } = booking;
          
          return (
            <ResourceItem
              id={id}
              url={`/admin/bookings/${id}`}
              accessibilityLabel={`予約 ${bookingId} の詳細を表示`}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <div>
                  <Text variant="headingMd" as="h3">
                    {bookingId} {getStatusBadge(status, bookingType)} {expiresAt && getExpirationBadge(expiresAt)}
                  </Text>
                  <Text variant="bodyMd" as="p">
                    商品: {productTitle}
                  </Text>
                  <Text variant="bodyMd" as="p">
                    期間: {format(new Date(startDate), 'yyyy/MM/dd', { locale: ja })} 〜 {format(new Date(endDate), 'yyyy/MM/dd', { locale: ja })}
                  </Text>
                  <Text variant="bodyMd" as="p">
                    顧客: {customerName || '不明'} ({customerEmail || '不明'})
                  </Text>
                  {expiresAt && (
                    <Text variant="bodyMd" as="p">
                      期限: {format(new Date(expiresAt), 'yyyy/MM/dd', { locale: ja })}
                    </Text>
                  )}
                </div>
                
                <ButtonGroup>
                  <Button url={`/admin/bookings/${id}`}>詳細</Button>
                  {status === 'PROVISIONAL' && (
                    <Button primary url={`/admin/bookings/${id}/confirm`}>本予約に変更</Button>
                  )}
                </ButtonGroup>
              </div>
            </ResourceItem>
          );
        }}
      />

      {/* ページネーション */}
      {totalPages > 1 && (
        <Box paddingBlockStart="400" textAlign="center">
          <Pagination
            hasPrevious={currentPage > 1}
            onPrevious={() => setCurrentPage(currentPage - 1)}
            hasNext={currentPage < totalPages}
            onNext={() => setCurrentPage(currentPage + 1)}
          />
          <div style={{ marginTop: '0.5rem' }}>
            <Text as="span" variant="bodyMd">
              {currentPage} / {totalPages} ページ（全 {filteredBookings.length} 件）
            </Text>
          </div>
        </Box>
      )}
    </div>
  );
}
