#!/usr/bin/env node

/**
 * 全テスト実行スクリプト
 * 
 * 商品登録 → 予約作成 → 包括テストを順次実行
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * スクリプトを実行
 */
function runScript(scriptPath, description) {
  return new Promise((resolve, reject) => {
    console.log(`\n🚀 ${description} 開始...`);
    console.log(`実行スクリプト: ${scriptPath}`);
    
    const child = spawn('node', [scriptPath], {
      stdio: 'inherit',
      cwd: path.dirname(scriptPath)
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${description} 完了`);
        resolve(code);
      } else {
        console.error(`❌ ${description} 失敗 (終了コード: ${code})`);
        reject(new Error(`${description} failed with code ${code}`));
      }
    });
    
    child.on('error', (error) => {
      console.error(`❌ ${description} 実行エラー:`, error);
      reject(error);
    });
  });
}

/**
 * 全テストを順次実行
 */
async function runAllTests() {
  console.log('🧪 === 全テスト実行開始 ===');
  console.log(`実行日時: ${new Date().toLocaleString('ja-JP')}`);
  
  const scripts = [
    {
      path: path.join(__dirname, '01_register_test_products.js'),
      description: 'テスト用商品登録'
    },
    {
      path: path.join(__dirname, '02_create_test_bookings.js'),
      description: 'テスト用予約データ作成'
    },
    {
      path: path.join(__dirname, '03_comprehensive_test.js'),
      description: '包括的システムテスト'
    }
  ];
  
  try {
    for (const script of scripts) {
      await runScript(script.path, script.description);
      
      // 各スクリプト間で少し待機
      console.log('⏳ 次のテストまで3秒待機...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    console.log('\n🎉 === 全テスト完了 ===');
    console.log('✅ 全てのテストスクリプトが正常に実行されました');
    
    console.log('\n📋 次の手動確認項目:');
    console.log('1. ブラウザで /app/bookings/aggregate にアクセス');
    console.log('2. カテゴリドロップダウンが正しく表示されることを確認');
    console.log('3. カテゴリフィルタリングが動作することを確認');
    console.log('4. 予約データが正しく表示されることを確認');
    console.log('5. 商品の在庫状況が正しく反映されることを確認');
    
    console.log('\n🔗 確認用URL:');
    console.log('- 予約状況一括照会: https://app.shopify-app-test.xyz/app/bookings/aggregate');
    console.log('- カテゴリテストページ: https://app.shopify-app-test.xyz/app/test-categories');
    
  } catch (error) {
    console.error('\n❌ テスト実行中にエラーが発生しました:', error.message);
    console.log('\n🔧 トラブルシューティング:');
    console.log('1. データベース接続を確認してください');
    console.log('2. Prismaスキーマが最新であることを確認してください');
    console.log('3. 必要な依存関係がインストールされていることを確認してください');
    
    process.exit(1);
  }
}

/**
 * 個別テスト実行（オプション）
 */
async function runIndividualTest(testName) {
  const testMap = {
    'products': '01_register_test_products.js',
    'bookings': '02_create_test_bookings.js',
    'comprehensive': '03_comprehensive_test.js'
  };
  
  const scriptFile = testMap[testName];
  if (!scriptFile) {
    console.error(`❌ 不明なテスト名: ${testName}`);
    console.log('利用可能なテスト:');
    Object.keys(testMap).forEach(key => {
      console.log(`  - ${key}`);
    });
    process.exit(1);
  }
  
  const scriptPath = path.join(__dirname, scriptFile);
  const description = `個別テスト: ${testName}`;
  
  try {
    await runScript(scriptPath, description);
    console.log(`✅ ${description} 完了`);
  } catch (error) {
    console.error(`❌ ${description} 失敗:`, error.message);
    process.exit(1);
  }
}

/**
 * メイン処理
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    // 全テスト実行
    await runAllTests();
  } else if (args[0] === '--test' && args[1]) {
    // 個別テスト実行
    await runIndividualTest(args[1]);
  } else {
    console.log('使用方法:');
    console.log('  node run_all_tests.js                 # 全テスト実行');
    console.log('  node run_all_tests.js --test products # 商品登録テストのみ');
    console.log('  node run_all_tests.js --test bookings # 予約作成テストのみ');
    console.log('  node run_all_tests.js --test comprehensive # 包括テストのみ');
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
