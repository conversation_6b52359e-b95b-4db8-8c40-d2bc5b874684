/**
 * 予約情報の初期化スクリプト
 *
 * このスクリプトは、商品の予約情報を初期化します。
 * 実行方法: node scripts/initialize-booking-data.js [商品ID]
 */

import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import { v4 as uuidv4 } from 'uuid';
import { addDays, format, subDays } from 'date-fns';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品情報を取得するクエリ
const GET_PRODUCT = gql`
  query getProduct($id: ID!) {
    product(id: $id) {
      id
      title
      status
      variants(first: 5) {
        edges {
          node {
            id
            title
            sku
            inventoryQuantity
            inventoryPolicy
          }
        }
      }
      metafields(first: 10) {
        edges {
          node {
            namespace
            key
            value
          }
        }
      }
    }
  }
`;

// メタフィールドを更新するミューテーション
const UPDATE_METAFIELD = gql`
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * 商品情報を取得する関数
 * @param {string} productId 商品ID
 * @returns {Promise<object>} 商品情報
 */
async function getProductInfo(productId) {
  try {
    // Shopify IDを正規化
    const shopifyId = productId.startsWith('gid://shopify/Product/')
      ? productId
      : `gid://shopify/Product/${productId}`;

    console.log(`商品情報を取得中... (Shopify ID: ${shopifyId})`);

    // Shopifyから商品情報を取得
    const result = await shopifyClient.request(GET_PRODUCT, {
      id: shopifyId
    });

    if (!result.product) {
      throw new Error(`商品ID ${productId} が見つかりません`);
    }

    const product = result.product;
    console.log(`商品情報を取得しました: ${product.title}`);

    // データベースから商品情報を取得
    const dbProduct = await prisma.product.findFirst({
      where: {
        shopifyId: productId.replace('gid://shopify/Product/', '')
      }
    });

    if (!dbProduct) {
      throw new Error(`商品ID ${productId} がデータベースに存在しません`);
    }

    return { shopifyProduct: product, dbProduct };
  } catch (error) {
    console.error('商品情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 予約情報の初期データを作成する関数
 * @param {object} product 商品情報
 * @returns {object} 予約情報の初期データ
 */
function createInitialBookingData(product) {
  // 現在の日付
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 初期予約データ
  const initialBookingData = {
    bookings: []
  };

  // 利用可能状態の設定
  initialBookingData.availability = {
    rentalStatus: 'available',
    startDate: format(today, 'yyyy-MM-dd'),
    maintenanceDates: [],
    blockedDates: []
  };

  return initialBookingData;
}

/**
 * 予約情報をShopifyメタフィールドに設定する関数
 * @param {string} productId 商品ID
 * @param {object} bookingData 予約データ
 * @returns {Promise<object>} 更新結果
 */
async function setBookingMetafield(productId, bookingData) {
  try {
    console.log('予約情報メタフィールドを設定中...');

    // Shopify IDを正規化
    const shopifyId = productId.startsWith('gid://shopify/Product/')
      ? productId
      : `gid://shopify/Product/${productId}`;

    // メタフィールドの設定
    const result = await shopifyClient.request(UPDATE_METAFIELD, {
      metafields: [
        {
          ownerId: shopifyId,
          namespace: 'rental',
          key: 'bookings',
          value: JSON.stringify(bookingData),
          type: 'json'
        }
      ]
    });

    if (result.metafieldsSet.userErrors.length > 0) {
      throw new Error(`メタフィールドの設定中にエラーが発生しました: ${JSON.stringify(result.metafieldsSet.userErrors)}`);
    }

    console.log('予約情報メタフィールドを設定しました');
    return result.metafieldsSet.metafields[0];
  } catch (error) {
    console.error('予約情報メタフィールドの設定中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 予約情報をデータベースに初期化する関数
 * @param {object} dbProduct データベース上の商品情報
 * @returns {Promise<void>}
 */
async function initializeBookingInDatabase(dbProduct) {
  try {
    console.log('データベース上の予約情報を初期化中...');

    // 既存の予約データを削除
    await prisma.booking.deleteMany({
      where: {
        productId: dbProduct.id
      }
    });

    console.log('既存の予約データを削除しました');
  } catch (error) {
    console.error('データベース上の予約情報の初期化中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    // コマンドライン引数から商品IDを取得
    const productId = process.argv[2];

    if (!productId) {
      console.error('商品IDが指定されていません。使用方法: node scripts/initialize-booking-data.js [商品ID]');
      process.exit(1);
    }

    console.log(`商品ID ${productId} の予約情報を初期化します...`);

    // 商品情報を取得
    const { shopifyProduct, dbProduct } = await getProductInfo(productId);

    // 予約情報の初期データを作成
    const initialBookingData = createInitialBookingData(shopifyProduct);

    // データベース上の予約情報を初期化
    await initializeBookingInDatabase(dbProduct);

    // Shopifyメタフィールドに予約情報を設定
    await setBookingMetafield(shopifyProduct.id, initialBookingData);

    console.log('予約情報の初期化が完了しました！');
  } catch (error) {
    console.error('予約情報の初期化中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
