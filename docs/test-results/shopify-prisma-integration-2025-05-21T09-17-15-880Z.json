{"timestamp": "2025-05-21T09:17:15.880Z", "duration": 0, "totalTests": 8, "successCount": 5, "failureCount": 3, "testMode": "basic", "environment": {"shop": "peaces-test-block.myshopify.com", "productId": "gid://shopify/Product/8977061544104", "customer": {"email": "<EMAIL>", "name": "テスト顧客"}, "bookingDays": 3, "args": ["--local"], "nodeVersion": "v20.18.1", "os": "darwin arm64", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tests": [{"testName": "商品同期", "success": true, "timestamp": "2025-05-21T09:17:15.880Z", "message": "商品データが正常に同期されました"}, {"testName": "在庫カレンダー更新", "success": true, "timestamp": "2025-05-21T09:17:15.880Z", "message": "在庫カレンダーが正常に更新されました"}, {"testName": "予約作成", "success": true, "timestamp": "2025-05-21T09:17:15.880Z", "message": "予約が正常に作成されました"}, {"testName": "注文連携", "success": true, "timestamp": "2025-05-21T09:17:15.880Z", "message": "注文が予約と正常に関連付けられました"}, {"testName": "予約タイプテスト", "success": true, "timestamp": "2025-05-21T09:17:15.880Z", "message": "予約タイプ（仮予約・本予約）の管理が正常に機能しました"}, {"testName": "商品バリエーション", "success": false, "timestamp": "2025-05-21T09:17:15.880Z", "message": "バリエーション商品のテストに失敗しました"}, {"testName": "エラーケース", "success": false, "timestamp": "2025-05-21T09:17:15.880Z", "message": "エラーケースのテストに失敗しました"}, {"testName": "テストデータのクリーンアップ", "success": false, "timestamp": "2025-05-21T09:17:15.880Z", "message": "テストデータのクリーンアップに失敗しました"}]}