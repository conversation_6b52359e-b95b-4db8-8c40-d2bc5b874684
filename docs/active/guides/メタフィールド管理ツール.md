# メタフィールド管理ツール

**更新日**: 2025年5月25日  
**目的**: メタフィールドの運用・管理に使用するツールとコマンドの一覧

## 📋 概要

Shopifyメタフィールドを効率的に管理するためのツール群です。
商品登録、更新、確認、一括処理等の操作を簡単に実行できます。

## 🛠️ ツール一覧

### 1. メタフィールド定義管理

#### 定義の作成・更新
```bash
# メタフィールド定義を作成
node scripts/create-metafield-definitions.js

# 既存定義の確認
node scripts/list-metafield-definitions.js
```

### 2. 商品メタフィールド操作

#### 個別商品の操作
```bash
# メタフィールドの確認
node scripts/check-product-metafields.js [productId]

# メタフィールドの設定
node scripts/set-product-metafields.js [productId]

# 商品グループの設定
node scripts/update-product-group.js [productId] "グループ名"
```

#### 一括操作
```bash
# CSVからメタフィールドを一括インポート
node scripts/import-metafields-from-csv.js [csvFile]

# 全商品のメタフィールドを更新
node scripts/update-all-product-metafields.js

# メタフィールドの一括削除
node scripts/cleanup-metafields.js
```

### 3. 検証・確認ツール

```bash
# メタフィールドの使用状況を調査
node scripts/check-product-metafields-usage.ts

# 商品とメタフィールドの整合性チェック
node scripts/validate-product-metafields.js

# メタフィールドのエクスポート
node scripts/export-metafields.js > metafields-backup.json
```

### 4. 商品登録ツール

```bash
# 完全な商品データを作成（メタフィールド込み）
node scripts/create-complete-product-from-data.ts

# テスト商品の作成
node scripts/create-test-product-with-metafields.js

# CSVから商品を一括登録
node scripts/import-products-from-csv.js [csvFile]
```

## 📝 使用例

### 例1: 新商品の登録

```bash
# 1. 商品データを準備（CSV or JSON）
# 2. 商品を作成
node scripts/create-complete-product-from-data.ts

# 3. 作成された商品のメタフィールドを確認
node scripts/check-product-metafields.js gid://shopify/Product/123456

# 4. 必要に応じて商品グループを設定
node scripts/update-product-group.js gid://shopify/Product/123456 "ベーシックソファ 1シーター"
```

### 例2: 既存商品のメタフィールド更新

```bash
# 1. 現在の状態を確認
node scripts/check-product-metafields-usage.ts

# 2. CSVファイルで更新データを準備
# metafields-update.csv:
# productId,product_group,status,location
# 123456,"ベーシックソファ",available,NY
# 123457,"カリモクソファ",maintenance,外部倉庫

# 3. 一括更新を実行
node scripts/import-metafields-from-csv.js metafields-update.csv
```

### 例3: メタフィールドの診断

```bash
# 全体の使用状況を確認
node scripts/check-product-metafields-usage.ts > metafields-report.txt

# 問題のある商品を特定
node scripts/validate-product-metafields.js

# 不要なメタフィールドをクリーンアップ
node scripts/cleanup-metafields.js --dry-run  # まず確認
node scripts/cleanup-metafields.js             # 実行
```

## 🔧 高度な使用方法

### カスタムスクリプトの作成

```javascript
// scripts/custom-metafield-operation.js
const { updateProductMetafield } = require('./lib/metafield-utils');

async function customOperation() {
  // 特定条件の商品を検索
  const products = await getProductsByCategory('ソファ');
  
  // 各商品のメタフィールドを更新
  for (const product of products) {
    await updateProductMetafield(product.id, {
      namespace: 'rental',
      key: 'maintenance_notes',
      value: '定期メンテナンス実施済み: ' + new Date().toISOString()
    });
  }
}

customOperation();
```

### バッチ処理の実装

```bash
# 日次バッチ処理
#!/bin/bash
# scripts/daily-metafield-maintenance.sh

echo "メタフィールド日次メンテナンス開始: $(date)"

# 1. バックアップ
node scripts/export-metafields.js > "backups/metafields-$(date +%Y%m%d).json"

# 2. 整合性チェック
node scripts/validate-product-metafields.js

# 3. ステータス更新
node scripts/update-product-status.js

echo "メタフィールド日次メンテナンス完了: $(date)"
```

## ⚠️ 注意事項

1. **本番環境での実行前に必ずテスト環境で確認**
2. **一括更新前にはバックアップを取得**
3. **APIレート制限に注意（Shopify: 2リクエスト/秒）**
4. **JSON型メタフィールドは正しいフォーマットで保存**

## 📚 関連ドキュメント

- [メタフィールド定義 最新版](../specifications/メタフィールド定義_最新版_20250525.md)
- [メタフィールド実装ガイド](./メタフィールド実装ガイド.md)
- [商品登録フロー](../guides/商品登録フロー.md)

---

本ドキュメントは運用の実態に合わせて継続的に更新されます。
最終更新: 2025年5月25日