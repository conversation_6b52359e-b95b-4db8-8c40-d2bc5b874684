/**
 * セッショントークンを使用した注文作成テストスクリプト
 *
 * このスクリプトは、データベースからセッショントークンを取得して注文を作成します。
 * これにより、正しいオフライントークンが使用され、write_ordersスコープが機能するかをテストします。
 *
 * 実行方法: npx tsx scripts/test-order-with-session-token.ts
 */

import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// 通常注文作成ミューテーション
const CREATE_ORDER = gql`
  mutation orderCreate($order: OrderCreateOrderInput!) {
    orderCreate(order: $order) {
      order {
        id
        name
        totalPrice
        customer {
          id
          email
        }
        lineItems(first: 10) {
          edges {
            node {
              title
              quantity
              originalUnitPrice
              variant {
                id
                title
              }
            }
          }
        }
        metafields(first: 10) {
          edges {
            node {
              namespace
              key
              value
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// ドラフト注文作成ミューテーション
const CREATE_DRAFT_ORDER = gql`
  mutation draftOrderCreate($input: DraftOrderInput!) {
    draftOrderCreate(input: $input) {
      draftOrder {
        id
        name
        totalPrice
        customer {
          id
          email
        }
        lineItems(first: 10) {
          edges {
            node {
              title
              quantity
              originalUnitPrice
              variant {
                id
                title
              }
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// セッショントークンを使用した注文作成テスト
async function testOrderWithSessionToken() {
  try {
    console.log('セッショントークンを使用した注文作成テストを開始します...');

    console.log('データベースからセッションを取得中...');

    // データベースからセッションを取得
    const sessions = await prisma.session.findMany({
      where: {
        shop: process.env.SHOPIFY_SHOP
      },
      orderBy: {
        id: 'desc'
      },
      take: 1
    });

    console.log(`セッション検索結果: ${sessions.length}件見つかりました`);

    if (sessions.length === 0) {
      throw new Error('セッションが見つかりません。アプリケーションが正しく認証されていることを確認してください。');
    }

    // 最新のセッションを使用
    const session = sessions[0];
    console.log(`セッション情報: Shop=${session.shop}, アクセストークン=${session.accessToken.substring(0, 10)}...`);

    // Shopify GraphQL APIクライアントの初期化
    const client = new GraphQLClient(
      `https://${session.shop}/admin/api/2025-01/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': session.accessToken,
          'Content-Type': 'application/json',
        },
      }
    );

    // 顧客IDを取得（環境変数から）
    const customerId = process.env.TEST_CUSTOMER_ID;
    if (!customerId) {
      throw new Error('TEST_CUSTOMER_ID が設定されていません');
    }

    // 商品IDを取得（環境変数から）
    const productId = process.env.TEST_PRODUCT_ID;
    const variantId = process.env.TEST_VARIANT_ID;
    if (!productId || !variantId) {
      throw new Error('TEST_PRODUCT_ID または TEST_VARIANT_ID が設定されていません');
    }

    console.log(`テスト情報: 顧客ID=${customerId}, 商品ID=${productId}, バリアントID=${variantId}`);

    // 顧客IDを正規化
    const normalizedCustomerId = customerId.startsWith('gid://shopify/Customer/')
      ? customerId
      : `gid://shopify/Customer/${customerId}`;

    // 現在の日時を取得
    const now = new Date();
    const startDate = new Date(now);
    startDate.setDate(startDate.getDate() + 1); // 明日から

    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 3); // 3日間

    // 日付をフォーマット
    const formatDate = (date: Date) => {
      return date.toISOString().split('T')[0];
    };

    const rentalPeriod = `${formatDate(startDate)} 〜 ${formatDate(endDate)}`;

    // 1. 通常注文作成を試みる
    try {
      console.log('1. 通常注文作成を試みます...');

      // 通常注文作成用の入力データを作成
      const orderInput = {
        customerId: normalizedCustomerId,
        lineItems: [
          {
            title: `テスト商品 (レンタル: ${rentalPeriod})`,
            quantity: 1,
            variantId: variantId,
            taxable: true,
            requiresShipping: false
          }
        ],
        tags: ['rental', 'test', 'session-token'],
        note: `テスト予約\n予約期間: ${rentalPeriod}\n備考: セッショントークンテスト`,
        metafields: [
          {
            namespace: "custom",
            key: "booking_id",
            value: "test-session-" + Date.now(),
            type: "single_line_text_field"
          },
          {
            namespace: "custom",
            key: "booking_number",
            value: "SESSION-" + Math.floor(Math.random() * 10000),
            type: "single_line_text_field"
          },
          {
            namespace: "custom",
            key: "booking_type",
            value: "CONFIRMED",
            type: "single_line_text_field"
          },
          {
            namespace: "custom",
            key: "rental_period",
            value: rentalPeriod,
            type: "single_line_text_field"
          }
        ]
      };

      console.log('通常注文作成データ:', JSON.stringify(orderInput, null, 2));

      // 通常注文を作成
      const orderResponse = await client.request(CREATE_ORDER, {
        order: orderInput
      });

      console.log('通常注文作成応答:', JSON.stringify(orderResponse, null, 2));

      if (orderResponse.orderCreate.userErrors.length > 0) {
        console.error('通常注文作成エラー:', orderResponse.orderCreate.userErrors);
        throw new Error(`通常注文の作成中にエラーが発生しました: ${JSON.stringify(orderResponse.orderCreate.userErrors)}`);
      }

      // 注文IDを取得
      const orderId = orderResponse.orderCreate.order.id;
      const orderName = orderResponse.orderCreate.order.name;

      console.log(`注文が作成されました: 注文ID ${orderId}, 注文番号 ${orderName}`);
      console.log(`Shopify管理画面で確認: https://${session.shop}/admin/orders/${orderName.replace('#', '')}`);

      return {
        success: true,
        method: 'direct_order',
        orderId,
        orderName
      };
    } catch (directOrderError) {
      console.error('通常注文作成に失敗しました:', directOrderError);
      console.log('2. ドラフト注文作成を試みます...');

      // 2. 通常注文作成に失敗した場合、ドラフト注文作成を試みる
      // ドラフト注文用の入力データを作成
      const draftOrderInput = {
        customerId: normalizedCustomerId,
        lineItems: [
          {
            title: `テスト商品 (レンタル: ${rentalPeriod})`,
            quantity: 1,
            variantId: variantId,
            taxable: true,
            requiresShipping: false
          }
        ],
        tags: ['rental', 'test', 'session-token', 'draft'],
        note: `テスト予約\n予約期間: ${rentalPeriod}\n備考: セッショントークンテスト（ドラフト）`,
        metafields: [
          {
            namespace: "custom",
            key: "booking_id",
            value: "test-session-draft-" + Date.now(),
            type: "single_line_text_field"
          },
          {
            namespace: "custom",
            key: "booking_number",
            value: "SESSION-DRAFT-" + Math.floor(Math.random() * 10000),
            type: "single_line_text_field"
          },
          {
            namespace: "custom",
            key: "booking_type",
            value: "CONFIRMED",
            type: "single_line_text_field"
          },
          {
            namespace: "custom",
            key: "rental_period",
            value: rentalPeriod,
            type: "single_line_text_field"
          }
        ]
      };

      console.log('ドラフト注文作成データ:', JSON.stringify(draftOrderInput, null, 2));

      // ドラフト注文を作成
      const draftResponse = await client.request(CREATE_DRAFT_ORDER, {
        input: draftOrderInput
      });

      console.log('ドラフト注文作成応答:', JSON.stringify(draftResponse, null, 2));

      if (draftResponse.draftOrderCreate.userErrors.length > 0) {
        console.error('ドラフト注文作成エラー:', draftResponse.draftOrderCreate.userErrors);
        throw new Error(`ドラフト注文の作成中にエラーが発生しました: ${JSON.stringify(draftResponse.draftOrderCreate.userErrors)}`);
      }

      // ドラフト注文IDを取得
      const draftOrderId = draftResponse.draftOrderCreate.draftOrder.id;
      const draftOrderName = draftResponse.draftOrderCreate.draftOrder.name;

      console.log(`ドラフト注文が作成されました: ドラフト注文ID ${draftOrderId}, ドラフト注文番号 ${draftOrderName}`);
      console.log(`Shopify管理画面で確認: https://${session.shop}/admin/draft_orders/${draftOrderName.replace('#D', '')}`);

      return {
        success: true,
        method: 'draft_order',
        draftOrderId,
        draftOrderName
      };
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    throw error;
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// スクリプトを実行
testOrderWithSessionToken()
  .then((result) => {
    console.log('テストが完了しました:', result);
    process.exit(0);
  })
  .catch((error) => {
    console.error('テストが失敗しました:', error);
    process.exit(1);
  });
