/**
 * Shopify注文作成APIテストスクリプト
 *
 * このスクリプトは、Shopify GraphQL APIを直接呼び出して注文を作成します。
 * 実際のAPIリクエストとレスポンスを確認するためのものです。
 *
 * 実行方法: npx tsx scripts/test-order-api.ts
 */

import dotenv from 'dotenv';
import { GraphQLClient } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// テスト設定
const config = {
  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',

  // Shopify APIアクセストークン
  shopifyAccessToken: process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,

  // Shopify API バージョン
  apiVersion: '2025-01'
};

// 注文作成ミューテーション（通常注文）
const CREATE_ORDER = `
  mutation orderCreate($input: OrderCreateOrderInput!) {
    orderCreate(order: $input) {
      order {
        id
        name
        totalPrice
        customer {
          id
          email
        }
        lineItems(first: 10) {
          edges {
            node {
              title
              quantity
              variant {
                id
                title
              }
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// ドラフト注文作成ミューテーション
const CREATE_DRAFT_ORDER = `
  mutation draftOrderCreate($input: DraftOrderInput!) {
    draftOrderCreate(input: $input) {
      draftOrder {
        id
        name
        totalPrice
        customer {
          id
          email
        }
        lineItems(first: 10) {
          edges {
            node {
              title
              quantity
              variant {
                id
                title
              }
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('Shopify注文作成APIテストを開始します...');
    console.log('環境変数:');
    console.log('- SHOPIFY_SHOP:', config.shop);
    console.log('- SHOPIFY_ADMIN_API_ACCESS_TOKEN:', config.shopifyAccessToken ? '設定されています' : '設定されていません');
    console.log('- API バージョン:', config.apiVersion);

    // GraphQLクライアントを初期化
    if (!config.shopifyAccessToken) {
      throw new Error('SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません');
    }

    const graphQLClient = new GraphQLClient(
      `https://${config.shop}/admin/api/${config.apiVersion}/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': config.shopifyAccessToken,
          'Content-Type': 'application/json',
        },
      }
    );

    // 顧客ID
    const customerId = 'gid://shopify/Customer/8420600119464'; // 佐藤花子さん

    // 通常注文作成テスト
    console.log('\n----- 通常注文作成テスト -----');

    // 注文作成用の入力データを作成
    const orderInput = {
      customerId: customerId,
      lineItems: [
        {
          title: "テスト商品 (レンタル: 2025-05-30 〜 2025-05-31)",
          quantity: 1,
          taxable: true,
          requiresShipping: false,
          priceSet: {
            shopMoney: {
              amount: "5000",
              currencyCode: "JPY"
            }
          }
        }
      ],
      tags: ['rental', 'confirmed', 'test-api'],
      note: "APIテスト用の注文です"
    };

    console.log('注文作成データ:', JSON.stringify(orderInput, null, 2));

    try {
      console.log('通常注文作成を試みます...');
      const response = await graphQLClient.request(CREATE_ORDER, {
        input: orderInput
      });

      console.log('GraphQL応答を受信しました');
      console.log('GraphQL応答内容:', JSON.stringify(response, null, 2));

      if (response.orderCreate?.userErrors?.length > 0) {
        console.error('注文作成エラー:', response.orderCreate.userErrors);
        throw new Error(`注文の作成中にエラーが発生しました: ${JSON.stringify(response.orderCreate.userErrors)}`);
      }

      console.log('注文が正常に作成されました');
      console.log(`注文ID: ${response.orderCreate.order.id}`);
      console.log(`注文番号: ${response.orderCreate.order.name}`);
    } catch (orderError) {
      console.error('通常注文作成エラー:', orderError);
      console.log('ドラフト注文作成にフォールバックします...');

      // ドラフト注文作成テスト
      console.log('\n----- ドラフト注文作成テスト -----');

      // 注文作成用の入力データを作成
      const draftOrderInput = {
        customerId: customerId,
        lineItems: [
          {
            title: "テスト商品 (レンタル: 2025-05-30 〜 2025-05-31)",
            quantity: 1,
            taxable: true,
            requiresShipping: false,
            originalUnitPriceWithCurrency: {
              amount: "5000",
              currencyCode: "JPY"
            }
          }
        ],
        tags: ['rental', 'confirmed', 'test-api'],
        note: "APIテスト用のドラフト注文です"
      };

      console.log('ドラフト注文作成データ:', JSON.stringify(draftOrderInput, null, 2));

      try {
        const draftResponse = await graphQLClient.request(CREATE_DRAFT_ORDER, {
          input: draftOrderInput
        });

        console.log('GraphQL応答を受信しました');
        console.log('GraphQL応答内容:', JSON.stringify(draftResponse, null, 2));

        if (draftResponse.draftOrderCreate?.userErrors?.length > 0) {
          console.error('ドラフト注文作成エラー:', draftResponse.draftOrderCreate.userErrors);
          throw new Error(`ドラフト注文の作成中にエラーが発生しました: ${JSON.stringify(draftResponse.draftOrderCreate.userErrors)}`);
        }

        console.log('ドラフト注文が正常に作成されました');
        console.log(`ドラフト注文ID: ${draftResponse.draftOrderCreate.draftOrder.id}`);
        console.log(`ドラフト注文番号: ${draftResponse.draftOrderCreate.draftOrder.name}`);
      } catch (draftOrderError) {
        console.error('ドラフト注文作成エラー:', draftOrderError);
        throw draftOrderError;
      }
    }

    console.log('\nテストが完了しました！');
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    if (error instanceof Error) {
      console.error('エラーメッセージ:', error.message);
      console.error('スタックトレース:', error.stack);
    }
    process.exit(1);
  }
}

// スクリプトを実行
main();
