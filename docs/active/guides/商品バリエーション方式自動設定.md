# 商品バリエーション方式の自動設定

## 概要

商品バリエーション方式の実装において、商品登録後に自動的にメタフィールドを設定する仕組みを構築します。この文書では、Shopify Webhookを使用した自動設定の実装方法について説明します。

## 実装方法

### 1. Webhookを活用した自動設定

Shopify Webhookを使用して、商品の作成・更新時に自動的にメタフィールドを設定します。

#### 1.1 Webhookの設定

以下のWebhookを設定します：

- `products/create`: 商品作成時に発火
- `products/update`: 商品更新時に発火

これらのWebhookは、アプリの`/webhooks/products`エンドポイントにリクエストを送信します。

#### 1.2 Webhookハンドラーの実装

Webhookハンドラーでは、以下の処理を行います：

1. 商品データを受け取る
2. Shopify GraphQL APIを使用して商品の詳細情報を取得
3. Prismaデータベースに商品情報を保存または更新
4. 必要なメタフィールドを設定

### 2. Prismaとの連携

Prismaデータベースと連携して、商品情報を保存・管理します。

#### 2.1 商品情報の保存

商品情報は以下のように保存します：

- `shopifyId`: Shopifyの商品ID
- `title`: 商品名
- `sku`: SKU（最初のバリエーションのSKU）
- `price`: 基本価格（1日レンタルの価格）
- `basicInfo`: 商品の基本情報（JSON形式）
- `pricing`: 料金設定（JSON形式）

#### 2.2 メタフィールドの設定

以下のメタフィールドを自動的に設定します：

1. `rental.variant_mapping`: 日数とバリエーションIDのマッピング
2. `rental.pricing`: 料金設定（`variantPrices`フィールドを追加）
3. `rental.variation_type`: バリエーションタイプ（「レンタル日数」固定）

### 3. バリエーション命名規則

バリエーションの自動識別のために、以下の命名規則を採用します：

- 1日レンタル: "商品名 - 1日" または "商品名 - 1day"
- 2日レンタル: "商品名 - 2日" または "商品名 - 2day"
- ...
- 8日以上レンタル: "商品名 - 8日以上" または "商品名 - 8plus"
- 仮予約: "商品名 - 仮予約" または "商品名 - provisional"

### 4. SKU命名規則

SKUの命名規則は以下の通りです：

```
[基本SKU]-[物理バリエーション識別子]-[日数識別子]
```

例:
- 212-05-023-BK001-1D（ブラック-001の1日レンタル）
- 212-05-023-BK001-2D（ブラック-001の2日レンタル）

## 実装手順

### 1. Webhookハンドラーの作成

`app/routes/webhooks.products.jsx`ファイルを作成し、Webhookハンドラーを実装します。

### 2. Webhook検証ユーティリティの作成

`app/utils/webhook-verification.server.js`ファイルを作成し、Webhook検証ロジックを実装します。

### 3. Webhookの登録

`app/shopify.server.js`ファイルを修正し、必要なWebhookを登録します。

### 4. 環境変数の設定

以下の環境変数を設定します：

- `SHOPIFY_API_KEY`: Shopify APIキー
- `SHOPIFY_API_SECRET`: Shopify APIシークレット
- `SHOPIFY_SHOP`: ショップのドメイン（例: `your-store.myshopify.com`）
- `SHOPIFY_ADMIN_API_ACCESS_TOKEN`: Admin API アクセストークン

## 注意点

1. **バリエーション命名規則の統一**:
   - バリエーション名は上記の命名規則に従って設定する必要があります
   - 命名規則に従わない場合、自動マッピングが機能しません

2. **SKU命名規則の統一**:
   - SKUは上記の命名規則に従って設定する必要があります
   - 命名規則に従わない場合、商品コードと詳細コードの抽出が正しく行われません

3. **API制限**:
   - Shopify APIには呼び出し制限があります
   - 大量の商品を一度に処理する場合は、API制限に注意してください

4. **エラーハンドリング**:
   - Webhook処理中にエラーが発生した場合、ログに記録されます
   - 定期的にログを確認し、エラーが発生していないか確認してください

## トラブルシューティング

1. **メタフィールドが設定されない**:
   - バリエーション名が命名規則に従っているか確認
   - Webhookが正しく設定されているか確認
   - ログでエラーメッセージを確認

2. **バリエーションマッピングが不完全**:
   - すべてのバリエーション（1日〜8日以上）が正しく命名されているか確認
   - バリエーション名に日本語とローマ字が混在していないか確認

3. **Prismaデータベースに商品が保存されない**:
   - データベース接続が正しく設定されているか確認
   - Prismaスキーマが最新かどうか確認

## まとめ

Shopify Webhookを活用することで、商品登録後に自動的にメタフィールドを設定する仕組みを構築できます。これにより、商品バリエーション方式の実装が効率化され、手動でのメタフィールド設定が不要になります。

バリエーション命名規則とSKU命名規則を統一することで、自動設定の精度が向上します。また、Prismaデータベースと連携することで、商品情報の一元管理が可能になります。
