/**
 * 商品バリエーション方式に対応したテスト予約データ作成スクリプト
 *
 * このスクリプトは、商品バリエーション方式に対応したテスト予約データを作成します。
 * 各商品には8種類のバリエーション（1日〜7日、8日以上）と仮予約用のバリエーションが設定されています。
 *
 * 使用方法:
 * npx tsx scripts/create-variant-based-bookings.ts
 */

import { PrismaClient, BookingStatus, BookingType, PaymentMethod, PaymentStatus } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// ショップドメイン
const SHOP = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';

// 現在の日付
const NOW = new Date();

// 日付ユーティリティ関数
const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

const subtractDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() - days);
  return result;
};

// 商品を取得するクエリ
const GET_PRODUCTS = gql`
  query getProducts($first: Int!) {
    products(first: $first) {
      edges {
        node {
          id
          title
          variants(first: 10) {
            edges {
              node {
                id
                title
                price
                inventoryQuantity
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
              }
            }
          }
        }
      }
    }
  }
`;

// 下書き注文を作成するミューテーション
const CREATE_DRAFT_ORDER = gql`
  mutation draftOrderCreate($input: DraftOrderInput!) {
    draftOrderCreate(input: $input) {
      draftOrder {
        id
        name
        totalPrice
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 下書き注文を完了するミューテーション
const COMPLETE_DRAFT_ORDER = gql`
  mutation draftOrderComplete($id: ID!, $paymentPending: Boolean) {
    draftOrderComplete(id: $id, paymentPending: $paymentPending) {
      draftOrder {
        id
        name
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 顧客を取得するクエリ
const GET_CUSTOMERS = gql`
  query getCustomers($first: Int!) {
    customers(first: $first) {
      edges {
        node {
          id
          firstName
          lastName
          email
          phone
        }
      }
    }
  }
`;

/**
 * 商品データを取得する
 */
async function getProducts() {
  console.log('商品データを取得中...');

  try {
    const result = await client.request(GET_PRODUCTS, {
      first: 10
    });

    const products = result.products.edges.map((edge: any) => {
      const product = edge.node;
      const variants = product.variants.edges.map((variantEdge: any) => variantEdge.node);

      // メタフィールドを解析
      const metafields: any = {};
      product.metafields.edges.forEach((metafieldEdge: any) => {
        const metafield = metafieldEdge.node;
        if (!metafields[metafield.namespace]) {
          metafields[metafield.namespace] = {};
        }

        try {
          metafields[metafield.namespace][metafield.key] = JSON.parse(metafield.value);
        } catch (e) {
          metafields[metafield.namespace][metafield.key] = metafield.value;
        }
      });

      return {
        ...product,
        variants,
        metafields
      };
    });

    if (products.length === 0) {
      throw new Error('商品データが見つかりません。先に商品データを作成してください。');
    }

    console.log(`${products.length}件の商品が見つかりました`);
    return products;
  } catch (error) {
    console.error('商品データの取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 顧客データを取得する
 */
async function getCustomers() {
  console.log('顧客データを取得中...');

  try {
    const result = await client.request(GET_CUSTOMERS, {
      first: 10
    });

    const customers = result.customers.edges.map((edge: any) => edge.node);

    if (customers.length === 0) {
      throw new Error('顧客データが見つかりません。先に顧客データを作成してください。');
    }

    console.log(`${customers.length}件の顧客が見つかりました`);
    return customers;
  } catch (error) {
    console.error('顧客データの取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * レンタル日数に基づいて適切なバリエーションを取得する
 */
function getVariantForRentalDays(product: any, days: number, isProvisional: boolean = false) {
  if (isProvisional) {
    // 仮予約バリエーションを探す
    return product.variants.find((variant: any) =>
      variant.title.includes('仮予約') || variant.title.includes('provisional')
    );
  }

  // 日数に応じたバリエーションを探す
  if (days >= 8) {
    return product.variants.find((variant: any) =>
      variant.title.includes('8日以上') || variant.title.includes('8plus')
    );
  }

  return product.variants.find((variant: any) =>
    variant.title.includes(`${days}日`) || variant.title.includes(`${days}day`)
  );
}

/**
 * Shopifyに下書き注文を作成する
 */
async function createShopifyOrder(customer: any, product: any, variant: any, startDate: Date, endDate: Date, isProvisional: boolean = false) {
  try {
    const customerName = `${customer.firstName} ${customer.lastName}`;
    const customerId = customer.id;

    // レンタル日数を計算
    const days = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    // 下書き注文作成
    const draftOrderInput = {
      customerId,
      lineItems: [
        {
          variantId: variant.id,
          quantity: 1,
          customAttributes: [
            { key: "レンタル開始日", value: startDate.toISOString().split('T')[0] },
            { key: "レンタル終了日", value: endDate.toISOString().split('T')[0] },
            { key: "レンタル日数", value: days.toString() },
            { key: "レンタルタイプ", value: isProvisional ? "仮予約" : "本予約" }
          ]
        }
      ],
      tags: ["テスト", "レンタル", isProvisional ? "仮予約" : "本予約"],
      note: `${customerName}様の${isProvisional ? '仮予約' : '本予約'} (${startDate.toISOString().split('T')[0]} 〜 ${endDate.toISOString().split('T')[0]})`
    };

    const result = await client.request(CREATE_DRAFT_ORDER, {
      input: draftOrderInput
    });

    if (result.draftOrderCreate.userErrors.length > 0) {
      console.error('下書き注文作成中にエラーが発生しました:', result.draftOrderCreate.userErrors);
      return null;
    }

    const draftOrderId = result.draftOrderCreate.draftOrder.id;
    const draftOrderName = result.draftOrderCreate.draftOrder.name;
    const totalPrice = parseFloat(result.draftOrderCreate.draftOrder.totalPrice || '0');

    console.log(`下書き注文を作成しました: ${draftOrderName} (${draftOrderId})`);

    // 下書き注文を完了する
    try {
      const completeResult = await client.request(COMPLETE_DRAFT_ORDER, {
        id: draftOrderId,
        paymentPending: false
      });

      if (completeResult.draftOrderComplete.userErrors.length > 0) {
        console.error('下書き注文完了中にエラーが発生しました:', completeResult.draftOrderComplete.userErrors);
        // 下書き注文IDを返す（完了できなくても）
        return {
          orderId: draftOrderId,
          orderName: draftOrderName,
          totalPrice
        };
      }

      console.log(`下書き注文を完了しました: ${draftOrderName} (${draftOrderId})`);
    } catch (error) {
      console.error('下書き注文完了中にエラーが発生しました:', error);
      // 下書き注文IDを返す（完了できなくても）
      return {
        orderId: draftOrderId,
        orderName: draftOrderName,
        totalPrice
      };
    }

    return {
      orderId: draftOrderId,
      orderName: draftOrderName,
      totalPrice
    };
  } catch (error) {
    console.error('Shopify注文作成中にエラーが発生しました:', error);
    return null;
  }
}

/**
 * Prismaに予約データを作成する
 */
async function createPrismaBooking(
  product: any,
  customer: any,
  startDate: Date,
  endDate: Date,
  status: BookingStatus,
  bookingType: BookingType,
  orderInfo: any = null
) {
  try {
    const customerName = `${customer.firstName} ${customer.lastName}`;
    const customerId = customer.id.replace('gid://shopify/Customer/', '');
    const shopifyProductId = product.id.replace('gid://shopify/Product/', '');

    // レンタル日数を計算
    const days = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    // 基本料金（1日目）
    const basePrice = product.variants[0].price ? parseFloat(product.variants[0].price) : 5000;

    // 料金計算
    let totalAmount = Math.min(basePrice, 5000); // 最大5000円に制限
    if (days > 1) {
      const day2to6Count = Math.min(days - 1, 5);
      totalAmount += totalAmount * 0.2 * day2to6Count;
    }
    if (days > 6) {
      const day7PlusCount = days - 6;
      totalAmount += totalAmount * 0.1 * day7PlusCount;
    }

    // デポジット金額（10%）
    const depositAmount = Math.round(totalAmount * 0.1);

    // 予約データを作成
    const booking = await prisma.booking.create({
      data: {
        productId: product.id,
        shopifyProductId,
        startDate,
        endDate,
        customerId,
        customerEmail: customer.email,
        customerName,
        customerPhone: customer.phone || '',
        totalAmount: totalAmount.toString(),
        depositAmount: depositAmount.toString(),
        depositPaid: status !== BookingStatus.CANCELLED,
        bookingId: `BK-${uuidv4().substring(0, 8).toUpperCase()}`,
        shop: SHOP,
        bookingType,
        status,
        paymentStatus: status === BookingStatus.CANCELLED ? PaymentStatus.REFUNDED :
                      (status === BookingStatus.PROVISIONAL ? PaymentStatus.PARTIALLY_PAID : PaymentStatus.COMPLETED),
        paymentMethod: status === BookingStatus.CANCELLED ? null : PaymentMethod.CREDIT_CARD,
        paymentDate: status === BookingStatus.CANCELLED ? null : new Date(),
        notes: `${customerName}様の${bookingType === BookingType.PROVISIONAL ? '仮予約' : '本予約'}`,
        orderId: orderInfo?.orderId || null,
        orderName: orderInfo?.orderName || null,
      }
    });

    console.log(`予約を作成しました: ${booking.bookingId} (${status})`);
    return booking;
  } catch (error) {
    console.error('Prisma予約作成中にエラーが発生しました:', error);
    return null;
  }
}

/**
 * メインの実行関数
 */
async function main() {
  try {
    console.log('商品バリエーション方式のテスト予約データの作成を開始します...');

    // 1. 商品データを取得
    const products = await getProducts();

    // 2. 顧客データを取得
    const customers = await getCustomers();

    // 3. 既存の予約データを削除
    const deletedBookings = await prisma.booking.deleteMany({
      where: {
        shop: SHOP
      }
    });
    console.log(`${deletedBookings.count}件の既存予約を削除しました`);

    // 4. 予約データを作成
    const bookings = [];

    // 4.1 過去の予約（完了済み）- 1ヶ月前
    const pastCompletedStartDate = subtractDays(NOW, 35);
    const pastCompletedEndDate = subtractDays(NOW, 30);
    const pastCompletedDays = 6;
    const pastCompletedVariant = getVariantForRentalDays(products[0], pastCompletedDays);

    const pastCompletedBooking = await createPrismaBooking(
      products[0],
      customers[0],
      pastCompletedStartDate,
      pastCompletedEndDate,
      BookingStatus.COMPLETED,
      BookingType.CONFIRMED
    );
    bookings.push(pastCompletedBooking);

    // 4.2 過去の予約（キャンセル済み）- 2週間前
    const pastCancelledStartDate = subtractDays(NOW, 20);
    const pastCancelledEndDate = subtractDays(NOW, 18);
    const pastCancelledDays = 3;
    const pastCancelledVariant = getVariantForRentalDays(products[1], pastCancelledDays);

    const pastCancelledBooking = await createPrismaBooking(
      products[1],
      customers[1],
      pastCancelledStartDate,
      pastCancelledEndDate,
      BookingStatus.CANCELLED,
      BookingType.CONFIRMED
    );
    bookings.push(pastCancelledBooking);

    // 4.3 現在進行中の予約
    const currentStartDate = subtractDays(NOW, 2);
    const currentEndDate = addDays(NOW, 2);
    const currentDays = 5;
    const currentVariant = getVariantForRentalDays(products[2], currentDays);

    // Shopifyに注文を作成
    const currentOrderInfo = await createShopifyOrder(
      customers[2],
      products[2],
      currentVariant,
      currentStartDate,
      currentEndDate
    );

    const currentBooking = await createPrismaBooking(
      products[2],
      customers[2],
      currentStartDate,
      currentEndDate,
      BookingStatus.CONFIRMED,
      BookingType.CONFIRMED,
      currentOrderInfo
    );
    bookings.push(currentBooking);

    // 4.4 未来の予約（確定）- 1週間後
    const futureConfirmedStartDate = addDays(NOW, 7);
    const futureConfirmedEndDate = addDays(NOW, 10);
    const futureConfirmedDays = 4;
    const futureConfirmedVariant = getVariantForRentalDays(products[3], futureConfirmedDays);

    // Shopifyに注文を作成
    const futureConfirmedOrderInfo = await createShopifyOrder(
      customers[0],
      products[3],
      futureConfirmedVariant,
      futureConfirmedStartDate,
      futureConfirmedEndDate
    );

    const futureConfirmedBooking = await createPrismaBooking(
      products[3],
      customers[0],
      futureConfirmedStartDate,
      futureConfirmedEndDate,
      BookingStatus.CONFIRMED,
      BookingType.CONFIRMED,
      futureConfirmedOrderInfo
    );
    bookings.push(futureConfirmedBooking);

    // 4.5 未来の予約（仮予約）- 2週間後
    const futureProvisionalStartDate = addDays(NOW, 14);
    const futureProvisionalEndDate = addDays(NOW, 16);
    const futureProvisionalDays = 3;
    const futureProvisionalVariant = getVariantForRentalDays(products[4], futureProvisionalDays, true);

    // Shopifyに注文を作成（仮予約）
    const futureProvisionalOrderInfo = await createShopifyOrder(
      customers[1],
      products[4],
      futureProvisionalVariant,
      futureProvisionalStartDate,
      futureProvisionalEndDate,
      true
    );

    const futureProvisionalBooking = await createPrismaBooking(
      products[4],
      customers[1],
      futureProvisionalStartDate,
      futureProvisionalEndDate,
      BookingStatus.PROVISIONAL,
      BookingType.PROVISIONAL,
      futureProvisionalOrderInfo
    );
    bookings.push(futureProvisionalBooking);

    // 4.6 未来の予約（キャンセル済み）- 3週間後
    const futureCancelledStartDate = addDays(NOW, 21);
    const futureCancelledEndDate = addDays(NOW, 23);
    const futureCancelledDays = 3;
    const futureCancelledVariant = getVariantForRentalDays(products[0], futureCancelledDays);

    const futureCancelledBooking = await createPrismaBooking(
      products[0],
      customers[2],
      futureCancelledStartDate,
      futureCancelledEndDate,
      BookingStatus.CANCELLED,
      BookingType.CONFIRMED
    );
    bookings.push(futureCancelledBooking);

    console.log('\n=== テスト予約データ作成結果 ===');
    console.log(`作成した予約: ${bookings.filter(b => b !== null).length}件`);
    console.log('=============================\n');

    console.log('テスト予約データの作成が完了しました');
  } catch (error) {
    console.error('テスト予約データ作成中にエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('予期しないエラーが発生しました:', error);
    process.exit(1);
  });
