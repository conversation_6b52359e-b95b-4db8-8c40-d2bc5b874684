/**
 * 統一料金計算サービス
 *
 * アプリ全体で一貫した料金計算を提供します
 */

import { Decimal } from '@prisma/client/runtime/library';
import { calculateRentalDays } from '~/utils/pricing';

export interface PricingConfig {
  basePrice: number;
  discountRules?: {
    day2_6_rate?: number;  // 2-6日目の料率（デフォルト: 0.2）
    day7_plus_rate?: number; // 7日目以降の料率（デフォルト: 0.1）
  };
  depositRate?: number; // デポジット率（デフォルト: 0.1）
  taxRate?: number; // 税率（デフォルト: 0.1）
  variantPrices?: Record<number, number>; // バリエーション価格（日数 → 価格）
  useVariantPrices?: boolean; // バリエーション価格を使用するか
  provisionalRate?: number; // 仮予約料率（デフォルト: 0.1 = 10%）
  isProvisional?: boolean; // 仮予約かどうか
}

export interface PricingResult {
  totalPrice: number;
  taxInclusivePrice: number;
  depositAmount: number;
  rentalDays: number;
  priceBreakdown: Array<{
    day: number;
    price: number;
    rate: number;
    description: string;
  }>;
  provisionalPrice?: number; // 仮予約価格
  fullPrice?: number; // 正規価格（仮予約の場合）
  isProvisional: boolean; // 仮予約かどうか
  metadata: {
    startDate: string;
    endDate: string;
    basePrice: number;
    config: PricingConfig;
  };
}

export class UnifiedPricingService {
  private static instance: UnifiedPricingService;

  private constructor() {}

  public static getInstance(): UnifiedPricingService {
    if (!UnifiedPricingService.instance) {
      UnifiedPricingService.instance = new UnifiedPricingService();
    }
    return UnifiedPricingService.instance;
  }

  /**
   * 料金を計算する（整合性チェック付き）
   */
  public calculatePrice(
    startDate: Date,
    endDate: Date,
    basePrice: number | Decimal,
    config?: Partial<PricingConfig>
  ): PricingResult {
    try {
      // 基本価格を数値に変換
      const numericBasePrice = this.convertToNumber(basePrice);

      // 設定のデフォルト値を設定
      const pricingConfig: PricingConfig = {
        basePrice: numericBasePrice,
        discountRules: {
          day2_6_rate: config?.discountRules?.day2_6_rate ?? 0.2,
          day7_plus_rate: config?.discountRules?.day7_plus_rate ?? 0.1,
        },
        depositRate: config?.depositRate ?? 0.1,
        taxRate: config?.taxRate ?? 0.1,
        provisionalRate: config?.provisionalRate ?? 0.1,
        isProvisional: config?.isProvisional ?? false,
      };

      console.log('=== 統一料金計算開始 ===');
      console.log('開始日:', startDate.toISOString().split('T')[0]);
      console.log('終了日:', endDate.toISOString().split('T')[0]);
      console.log('基本料金:', numericBasePrice);
      console.log('設定:', pricingConfig);

      // レンタル日数を計算
      const rentalDays = calculateRentalDays(startDate, endDate);
      console.log('レンタル日数:', rentalDays);

      if (rentalDays <= 0) {
        throw new Error('レンタル日数が0以下です');
      }

      // 料金計算（バリエーション価格または独自計算）
      const { totalPrice: baseTotal, priceBreakdown } = pricingConfig.useVariantPrices && pricingConfig.variantPrices
        ? this.calculateWithVariantPrices(rentalDays, pricingConfig)
        : this.calculateDailyPrices(rentalDays, pricingConfig);

      // 仮予約の場合は料金を10%に調整
      let finalPrice = baseTotal;
      let provisionalPrice: number | undefined;
      let fullPrice: number | undefined;

      if (pricingConfig.isProvisional) {
        provisionalPrice = Math.round(baseTotal * pricingConfig.provisionalRate);
        fullPrice = baseTotal;
        finalPrice = provisionalPrice;
        console.log(`仮予約料金: ${provisionalPrice}円 (正規料金: ${fullPrice}円の${(pricingConfig.provisionalRate * 100)}%)`);
      }

      // 税込み価格を計算
      const taxInclusivePrice = Math.round(finalPrice * (1 + pricingConfig.taxRate));

      // デポジット金額を計算
      const depositAmount = Math.round(finalPrice * pricingConfig.depositRate);

      const result: PricingResult = {
        totalPrice: Math.round(finalPrice),
        taxInclusivePrice,
        depositAmount,
        rentalDays,
        priceBreakdown,
        provisionalPrice,
        fullPrice,
        isProvisional: pricingConfig.isProvisional,
        metadata: {
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate.toISOString().split('T')[0],
          basePrice: numericBasePrice,
          config: pricingConfig,
        },
      };

      console.log('計算結果:', result);
      console.log('=== 統一料金計算終了 ===');

      return result;
    } catch (error) {
      console.error('料金計算エラー:', error);
      throw new Error(`料金計算に失敗しました: ${error.message}`);
    }
  }

  /**
   * バリエーション価格を使用して料金を計算する
   */
  private calculateWithVariantPrices(
    rentalDays: number,
    config: PricingConfig
  ): { totalPrice: number; priceBreakdown: PricingResult['priceBreakdown'] } {
    console.log('バリエーション価格を使用した計算を開始');

    // バリエーション価格が設定されている場合はそれを使用
    if (config.variantPrices && config.variantPrices[rentalDays]) {
      const totalPrice = config.variantPrices[rentalDays];
      const priceBreakdown: PricingResult['priceBreakdown'] = [{
        day: rentalDays,
        price: totalPrice,
        rate: 1.0,
        description: `${rentalDays}日間バリエーション価格`
      }];

      console.log(`${rentalDays}日間のバリエーション価格: ${totalPrice}円`);
      return { totalPrice, priceBreakdown };
    }

    // バリエーション価格がない場合は独自計算にフォールバック
    console.log(`${rentalDays}日間のバリエーション価格が見つからないため、独自計算を使用`);
    return this.calculateDailyPrices(rentalDays, config);
  }

  /**
   * 日別料金を計算する
   */
  private calculateDailyPrices(
    rentalDays: number,
    config: PricingConfig
  ): { totalPrice: number; priceBreakdown: PricingResult['priceBreakdown'] } {
    let totalPrice = 0;
    const priceBreakdown: PricingResult['priceBreakdown'] = [];

    for (let day = 1; day <= rentalDays; day++) {
      let dayPrice: number;
      let rate: number;
      let description: string;

      if (day === 1) {
        // 1日目: 基本料金
        dayPrice = config.basePrice;
        rate = 1.0;
        description = '基本料金';
      } else if (day <= 6) {
        // 2-6日目: 基本料金 × 20%
        dayPrice = config.basePrice * config.discountRules.day2_6_rate;
        rate = config.discountRules.day2_6_rate;
        description = '2-6日目料金';
      } else {
        // 7日目以降: 基本料金 × 10%
        dayPrice = config.basePrice * config.discountRules.day7_plus_rate;
        rate = config.discountRules.day7_plus_rate;
        description = '7日目以降料金';
      }

      totalPrice += dayPrice;
      priceBreakdown.push({
        day,
        price: Math.round(dayPrice),
        rate,
        description,
      });

      console.log(`${day}日目: ${Math.round(dayPrice)}円 (${description})`);
    }

    return { totalPrice, priceBreakdown };
  }

  /**
   * Decimal型または数値を数値に変換する
   */
  private convertToNumber(value: number | Decimal): number {
    if (typeof value === 'number') {
      return value;
    }

    if (value && typeof value === 'object' && 'toNumber' in value) {
      return value.toNumber();
    }

    if (value && typeof value === 'object' && 'toString' in value) {
      return Number(value.toString());
    }

    return Number(value);
  }

  /**
   * 料金計算結果をフォーマットする
   */
  public formatPrice(amount: number): string {
    return new Intl.NumberFormat('ja-JP', {
      style: 'currency',
      currency: 'JPY',
    }).format(amount);
  }

  /**
   * 料金計算結果のサマリーを生成する
   */
  public generatePricingSummary(result: PricingResult): string {
    const lines = [
      `レンタル期間: ${result.metadata.startDate} 〜 ${result.metadata.endDate}`,
      `レンタル日数: ${result.rentalDays}日間`,
      `基本料金: ${this.formatPrice(result.metadata.basePrice)}`,
      '',
      '料金内訳:',
      ...result.priceBreakdown.map(item =>
        `  ${item.day}日目: ${this.formatPrice(item.price)} (${item.description})`
      ),
      '',
      `小計: ${this.formatPrice(result.totalPrice)}`,
      `税込: ${this.formatPrice(result.taxInclusivePrice)}`,
      `デポジット: ${this.formatPrice(result.depositAmount)}`,
    ];

    return lines.join('\n');
  }

  /**
   * 仮予約料金を計算する
   */
  public calculateProvisionalPrice(
    startDate: Date,
    endDate: Date,
    basePrice: number | Decimal,
    config?: Partial<PricingConfig>
  ): PricingResult {
    return this.calculatePrice(startDate, endDate, basePrice, {
      ...config,
      isProvisional: true,
    });
  }

  /**
   * 正規予約料金を計算する
   */
  public calculateConfirmedPrice(
    startDate: Date,
    endDate: Date,
    basePrice: number | Decimal,
    config?: Partial<PricingConfig>
  ): PricingResult {
    return this.calculatePrice(startDate, endDate, basePrice, {
      ...config,
      isProvisional: false,
    });
  }

  /**
   * 仮予約から正規予約への差額を計算する
   */
  public calculateUpgradePrice(
    startDate: Date,
    endDate: Date,
    basePrice: number | Decimal,
    config?: Partial<PricingConfig>
  ): {
    provisionalPrice: number;
    confirmedPrice: number;
    upgradeAmount: number;
    upgradeTax: number;
    totalUpgradeAmount: number;
  } {
    const provisional = this.calculateProvisionalPrice(startDate, endDate, basePrice, config);
    const confirmed = this.calculateConfirmedPrice(startDate, endDate, basePrice, config);

    const upgradeAmount = confirmed.totalPrice - provisional.totalPrice;
    const upgradeTax = confirmed.taxInclusivePrice - provisional.taxInclusivePrice;
    const totalUpgradeAmount = upgradeTax;

    return {
      provisionalPrice: provisional.totalPrice,
      confirmedPrice: confirmed.totalPrice,
      upgradeAmount,
      upgradeTax,
      totalUpgradeAmount,
    };
  }

  /**
   * 複数商品の料金を計算する
   */
  public calculateMultipleProducts(
    products: Array<{
      startDate: Date;
      endDate: Date;
      basePrice: number | Decimal;
      config?: Partial<PricingConfig>;
    }>
  ): {
    totalPrice: number;
    taxInclusivePrice: number;
    depositAmount: number;
    products: PricingResult[];
  } {
    const productResults = products.map(product =>
      this.calculatePrice(
        product.startDate,
        product.endDate,
        product.basePrice,
        product.config
      )
    );

    const totalPrice = productResults.reduce((sum, result) => sum + result.totalPrice, 0);
    const taxInclusivePrice = productResults.reduce((sum, result) => sum + result.taxInclusivePrice, 0);
    const depositAmount = productResults.reduce((sum, result) => sum + result.depositAmount, 0);

    return {
      totalPrice,
      taxInclusivePrice,
      depositAmount,
      products: productResults,
    };
  }
}
