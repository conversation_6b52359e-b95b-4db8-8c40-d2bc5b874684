# テスト商品作成手順

**最終更新日**: 2025/05/17

## 目次

1. [概要](#概要)
2. [テスト商品の作成](#テスト商品の作成)
3. [メタフィールドの設定](#メタフィールドの設定)
4. [テスト商品一覧](#テスト商品一覧)

## 概要

本ドキュメントでは、レンタル予約システムのテストに必要なテスト商品を作成する手順を説明します。テスト商品は、様々なレンタル状況（仮予約、確定予約、休日を挟んだレンタルなど）をテストするために使用します。

## テスト商品の作成

### 1. Shopify管理画面にログイン

1. [Shopify管理画面](https://peaces-test-block.myshopify.com/admin)にログインします。

### 2. 商品の作成

1. 「商品」 > 「すべての商品」に移動します。
2. 「商品を追加」ボタンをクリックします。
3. 以下の情報を入力します：
   - **タイトル**: 「【テスト】ベーシックソファ オフホワイト 1シーター」
   - **説明**: 「テスト用のベーシックソファです。」
   - **メディア**: 適当な画像をアップロードします。
   - **価格**: 8000
   - **SKU**: 10101007-001-001
   - **在庫管理**: 有効
   - **数量**: 1
   - **バリエーション**: 「オプションを追加」をクリックし、「レンタル日数」を追加します。
     - 1日レンタル: 8000円
     - 2日レンタル: 9600円
     - 3日レンタル: 11200円
     - 4日レンタル: 12800円
     - 5日レンタル: 14400円
     - 6日レンタル: 16000円
     - 7日レンタル: 17600円
     - 8日以上レンタル: 18400円
   - **タグ**: テスト,レンタル商品
   - **商品タイプ**: レンタル商品
   - **ベンダー**: テスト
4. 「保存」ボタンをクリックします。

### 3. バリエーションのSKUを設定

1. 作成した商品の編集画面で、各バリエーションのSKUを設定します：
   - 1日レンタル: 10101007-001-001
   - 2日レンタル: 10101007-001-002
   - 3日レンタル: 10101007-001-003
   - 4日レンタル: 10101007-001-004
   - 5日レンタル: 10101007-001-005
   - 6日レンタル: 10101007-001-006
   - 7日レンタル: 10101007-001-007
   - 8日以上レンタル: 10101007-001-008
2. 「保存」ボタンをクリックします。

## メタフィールドの設定

### 1. メタフィールド定義の確認

1. 「設定」 > 「カスタムデータ」に移動します。
2. 以下のメタフィールド定義が存在することを確認します：
   - **rental.basic_info**: JSON型
   - **rental.pricing**: JSON型
   - **rental.inventory_items**: JSON型
   - **rental.reservation_info**: JSON型

### 2. メタフィールドの設定

1. 作成した商品の編集画面で、「メタフィールド」セクションに移動します。
2. 各メタフィールドを以下のように設定します：

#### rental.basic_info

```json
{
  "productCode": "10101007",
  "detailCode": "001",
  "kana": "ベーシックソファオフホワイト1シーター",
  "dimensions": {
    "width": 87,
    "depth": 74,
    "height": 76
  },
  "seatDimensions": {
    "width": 52,
    "depth": 54,
    "height": 40
  },
  "material": "ファブリック",
  "color": "オフホワイト",
  "maker": "ヤマナリ",
  "campaign": "通常商品",
  "notes": "クリーニング済みですが全体的に黄ばみ発生（H30.12.15)"
}
```

#### rental.pricing

```json
{
  "basePrice": 8000,
  "depositRate": 0.1,
  "discountRules": {
    "day2_6_rate": 0.2,
    "day7_plus_rate": 0.1
  },
  "minimumDays": 1,
  "maximumDays": 30
}
```

#### rental.inventory_items

```json
[
  {
    "id": "item-10101007-001-1",
    "sku": "10101007-001-1",
    "status": "available",
    "location": "NY",
    "notes": "背面向かって右側うっすら黒いしみ"
  },
  {
    "id": "item-10101007-001-2",
    "sku": "10101007-001-2",
    "status": "maintenance",
    "location": "NY",
    "notes": "向かって左アーム手前と正面左側に黄色い輪染み有"
  }
]
```

#### rental.reservation_info

```json
[
  {
    "itemId": "item-10101007-001-1",
    "reservations": [
      {
        "id": "reservation-001",
        "startDate": "2025-05-22",
        "endDate": "2025-05-25",
        "status": "confirmed",
        "customerName": "山田太郎",
        "customerEmail": "<EMAIL>",
        "orderId": "gid://shopify/Order/1001",
        "orderLineItemId": "gid://shopify/LineItem/1001",
        "notes": "特になし"
      }
    ]
  },
  {
    "itemId": "item-10101007-001-2",
    "reservations": []
  }
]
```

3. 「保存」ボタンをクリックします。

## テスト商品一覧

以下のテスト商品を作成してください。各商品のメタフィールドは、上記の手順を参考に設定してください。

### 1. ベーシックソファ

- **タイトル**: 【テスト】ベーシックソファ オフホワイト 1シーター
- **SKU**: 10101007-001-001
- **価格**: 8000円
- **在庫アイテム**: 2台（1台目は利用可能、2台目はメンテナンス中）
- **予約状況**: 1台目は5/22-5/25に確定予約あり

### 2. ウイングソファ

- **タイトル**: 【テスト】ウイングソファ アンティークグリーン 1シーター
- **SKU**: 10101008-001-001
- **価格**: 10000円
- **在庫アイテム**: 1台（利用可能）
- **予約状況**: 5/10-5/25に長期レンタルの確定予約あり

### 3. カリモクソファ

- **タイトル**: 【テスト】カリモクソファ モケットグリーン 1シーター
- **SKU**: 10101009-001-001
- **価格**: 12000円
- **在庫アイテム**: 2台（1台目は利用可能、2台目は破損状態）
- **予約状況**: 
  - 1台目: 5/3-5/4に短期レンタル、5/7-5/9に休日を挟んだレンタル
  - 2台目: 5/20-5/30に仮予約あり

### 4. プレーンソファ

- **タイトル**: 【テスト】プレーンソファ 1シーター
- **SKU**: 10101010-001-001
- **価格**: 9000円
- **在庫アイテム**: 3台（1台目と2台目は利用可能、3台目は利用不可）
- **予約状況**:
  - 1台目: 5/1-5/3と5/5-5/10に連続した予約あり
  - 2台目: 5/15-5/20に仮予約、5/25-6/5に長期レンタルあり
  - 3台目: 予約なし（長期貸出中）
