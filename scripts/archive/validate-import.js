#!/usr/bin/env node

/**
 * インポートされたデータの整合性を検証するスクリプト
 * 
 * 使用方法:
 * ```
 * node validate-import.js --original=existing-data.csv
 * ```
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const { PrismaClient } = require('@prisma/client');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const { GraphQLClient, gql } = require('graphql-request');
const dotenv = require('dotenv');

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// コマンドライン引数を解析
const argv = yargs(hideBin(process.argv))
  .option('original', {
    alias: 'o',
    description: '元のCSVファイルのパス',
    type: 'string',
    demandOption: true
  })
  .option('report', {
    alias: 'r',
    description: '検証レポートの出力先',
    type: 'string',
    default: 'validation-report.json'
  })
  .help()
  .alias('help', 'h')
  .argv;

// Shopify APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2023-10/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// ファイルパス
const originalFile = argv.original;
const reportFile = argv.report;

/**
 * 検証処理のメイン関数
 */
async function validateImport() {
  console.log('インポートデータの検証を開始します');
  console.log(`元データファイル: ${originalFile}`);
  
  try {
    // CSVファイルが存在するか確認
    if (!fs.existsSync(originalFile)) {
      throw new Error(`元データファイルが見つかりません: ${originalFile}`);
    }
    
    // 元データを読み込み
    const originalData = await readCsvFile(originalFile);
    console.log(`元データ: ${originalData.length}件`);
    
    // 検証レポート
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        originalCount: originalData.length,
        shopifyCount: 0,
        databaseCount: 0,
        matchedCount: 0,
        missingInShopify: 0,
        missingInDatabase: 0,
        dataDiscrepancies: 0
      },
      details: []
    };
    
    // 各商品を検証
    for (let i = 0; i < originalData.length; i++) {
      const product = originalData[i];
      const productCode = product['商品コード'];
      
      console.log(`[${i + 1}/${originalData.length}] 商品 ${productCode} を検証中...`);
      
      // 検証結果
      const result = {
        productCode,
        name: product['商品名'],
        inShopify: false,
        inDatabase: false,
        discrepancies: []
      };
      
      // Shopifyの商品を検索
      const shopifyProduct = await findShopifyProductBySku(productCode);
      
      if (shopifyProduct) {
        result.inShopify = true;
        result.shopifyId = shopifyProduct.id;
        
        // メタフィールドを取得
        const metafields = await getShopifyProductMetafields(shopifyProduct.id);
        
        // 基本情報メタフィールドをチェック
        const basicInfo = metafields.find(m => 
          m.namespace === 'rental' && m.key === 'basic_info'
        );
        
        if (basicInfo) {
          const basicInfoData = JSON.parse(basicInfo.value);
          
          // 商品コードが一致するか確認
          if (basicInfoData.productCode !== productCode) {
            result.discrepancies.push({
              field: 'basic_info.productCode',
              expected: productCode,
              actual: basicInfoData.productCode
            });
          }
          
          // カナが一致するか確認
          if (product['商品名フリガナ'] && 
              basicInfoData.kana !== product['商品名フリガナ']) {
            result.discrepancies.push({
              field: 'basic_info.kana',
              expected: product['商品名フリガナ'],
              actual: basicInfoData.kana
            });
          }
          
          // 在庫場所が一致するか確認
          if (product['在庫場所'] && 
              basicInfoData.location !== product['在庫場所']) {
            result.discrepancies.push({
              field: 'basic_info.location',
              expected: product['在庫場所'],
              actual: basicInfoData.location
            });
          }
        } else {
          result.discrepancies.push({
            field: 'basic_info',
            issue: 'メタフィールドが存在しません'
          });
        }
      } else {
        result.inShopify = false;
        report.summary.missingInShopify++;
      }
      
      // データベースの商品を検索
      const dbProduct = await prisma.product.findFirst({
        where: {
          sku: productCode
        }
      });
      
      if (dbProduct) {
        result.inDatabase = true;
        result.databaseId = dbProduct.id;
        
        // メタデータをチェック
        if (dbProduct.metadata) {
          const metadata = dbProduct.metadata;
          
          // 基本情報をチェック
          if (metadata.basicInfo) {
            // 商品コードが一致するか確認
            if (metadata.basicInfo.productCode !== productCode) {
              result.discrepancies.push({
                field: 'db.basicInfo.productCode',
                expected: productCode,
                actual: metadata.basicInfo.productCode
              });
            }
            
            // カナが一致するか確認
            if (product['商品名フリガナ'] && 
                metadata.basicInfo.kana !== product['商品名フリガナ']) {
              result.discrepancies.push({
                field: 'db.basicInfo.kana',
                expected: product['商品名フリガナ'],
                actual: metadata.basicInfo.kana
              });
            }
            
            // 在庫場所が一致するか確認
            if (product['在庫場所'] && 
                metadata.basicInfo.location !== product['在庫場所']) {
              result.discrepancies.push({
                field: 'db.basicInfo.location',
                expected: product['在庫場所'],
                actual: metadata.basicInfo.location
              });
            }
          } else {
            result.discrepancies.push({
              field: 'db.basicInfo',
              issue: 'メタデータが存在しません'
            });
          }
        } else {
          result.discrepancies.push({
            field: 'db.metadata',
            issue: 'メタデータが存在しません'
          });
        }
      } else {
        result.inDatabase = false;
        report.summary.missingInDatabase++;
      }
      
      // 完全一致しているかどうか
      result.matched = result.inShopify && result.inDatabase && result.discrepancies.length === 0;
      
      if (result.matched) {
        report.summary.matchedCount++;
      } else if (result.discrepancies.length > 0) {
        report.summary.dataDiscrepancies++;
      }
      
      report.details.push(result);
      
      // APIレート制限を回避するため少し待機
      if (i < originalData.length - 1 && i % 10 === 9) {
        await sleep(1000);
      }
    }
    
    // 総数を計算
    report.summary.shopifyCount = report.details.filter(d => d.inShopify).length;
    report.summary.databaseCount = report.details.filter(d => d.inDatabase).length;
    
    // レポートを出力
    fs.writeFileSync(
      reportFile,
      JSON.stringify(report, null, 2),
      'utf8'
    );
    
    // サマリー表示
    console.log('\n検証完了');
    console.log(`元データ件数: ${report.summary.originalCount}`);
    console.log(`Shopifyに存在: ${report.summary.shopifyCount}`);
    console.log(`データベースに存在: ${report.summary.databaseCount}`);
    console.log(`完全一致: ${report.summary.matchedCount}`);
    console.log(`Shopifyに未登録: ${report.summary.missingInShopify}`);
    console.log(`データベースに未登録: ${report.summary.missingInDatabase}`);
    console.log(`データ不一致: ${report.summary.dataDiscrepancies}`);
    console.log(`詳細レポート: ${reportFile}`);
    
    // 整合性チェック
    const consistencyStatus = report.summary.matchedCount === report.summary.originalCount
      ? '✅ 完全に整合性が取れています'
      : '❌ 一部のデータに不整合があります';
    
    console.log(`\n状態: ${consistencyStatus}`);
    
  } catch (err) {
    console.error('検証処理中にエラーが発生しました:', err);
    process.exit(1);
  } finally {
    // Prismaクライアントを終了
    await prisma.$disconnect();
  }
}

/**
 * CSVファイルを読み込み
 */
async function readCsvFile(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (err) => reject(err));
  });
}

/**
 * SKUから商品を検索
 */
async function findShopifyProductBySku(sku) {
  try {
    const query = gql`
      query GetProductByVariantSku($query: String!, $first: Int!) {
        products(first: $first, query: $query) {
          edges {
            node {
              id
              title
              variants(first: 1) {
                edges {
                  node {
                    id
                    sku
                  }
                }
              }
            }
          }
        }
      }
    `;
    
    const response = await shopifyClient.request(query, {
      query: `sku:${sku}`,
      first: 1
    });
    
    const products = response.products.edges;
    
    if (products.length === 0) {
      return null;
    }
    
    return products[0].node;
  } catch (err) {
    console.error(`商品検索エラー (${sku}):`, err.message);
    return null;
  }
}

/**
 * 商品のメタフィールドを取得
 */
async function getShopifyProductMetafields(productId) {
  try {
    const query = gql`
      query GetProductMetafields($productId: ID!) {
        product(id: $productId) {
          metafields(first: 50) {
            edges {
              node {
                id
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    `;
    
    const response = await shopifyClient.request(query, {
      productId
    });
    
    return response.product.metafields.edges.map(edge => edge.node);
  } catch (err) {
    console.error(`メタフィールド取得エラー (${productId}):`, err.message);
    return [];
  }
}

/**
 * 指定ミリ秒スリープする
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 検証処理を実行
validateImport().catch(err => {
  console.error('検証処理中に致命的なエラーが発生しました:', err);
  process.exit(1);
});