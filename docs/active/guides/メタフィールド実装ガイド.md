# メタフィールド実装ガイド

**作成日**: 2025年5月25日  
**目的**: 開発者向けメタフィールド実装の詳細ガイド

## 📚 目次

1. [メタフィールドの基本概念](#メタフィールドの基本概念)
2. [実装パターン](#実装パターン)
3. [コード例](#コード例)
4. [トラブルシューティング](#トラブルシューティング)
5. [ベストプラクティス](#ベストプラクティス)

## メタフィールドの基本概念

### Shopifyメタフィールドとは
Shopifyの商品、バリアント、顧客等に追加できるカスタムフィールドです。
標準フィールドでは表現できない情報を柔軟に管理できます。

### 本システムでの役割
- **商品のグループ管理**: 同じ形状・デザインの商品を論理的にグループ化
- **レンタル情報管理**: 料金、期間、在庫状態等のレンタル特有情報
- **拡張情報保存**: メンテナンス履歴、備考等の追加情報

## 実装パターン

### 1. メタフィールド定義の作成

```javascript
// scripts/create-metafield-definitions.js
const metafieldDefinitions = [
  {
    name: "商品グループ",
    namespace: "rental",
    key: "product_group",
    type: "single_line_text_field",
    description: "同じ形状・デザインの商品をグループ化するための名称",
    ownerType: "PRODUCT",
    validations: []
  },
  {
    name: "レンタル料金設定",
    namespace: "rental", 
    key: "pricing",
    type: "json",
    description: "日数別レンタル料金の計算用データ",
    ownerType: "PRODUCT",
    validations: [
      {
        name: "json_schema",
        value: JSON.stringify({
          type: "object",
          properties: {
            basePrice: { type: "number" },
            extraDayRate: { type: "number" }
          },
          required: ["basePrice"]
        })
      }
    ]
  }
];
```

### 2. 商品作成時のメタフィールド設定

```typescript
// scripts/create-complete-product-from-data.ts
async function createProductWithMetafields(productData: ProductData) {
  const mutation = `
    mutation CreateProduct($input: ProductInput!) {
      productCreate(input: $input) {
        product {
          id
          title
          metafields(first: 10) {
            edges {
              node {
                id
                key
                value
              }
            }
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const input = {
    title: productData.title,
    productType: productData.categoryName,
    vendor: "レンタル商品",
    metafields: [
      {
        namespace: "rental",
        key: "basic_info",
        type: "json",
        value: JSON.stringify({
          productCode: productData.productCode,
          detailCode: productData.detailCode,
          categoryId: productData.categoryId,
          categoryName: productData.categoryName,
          itemNumber: productData.itemNumber
        })
      },
      {
        namespace: "rental",
        key: "pricing",
        type: "json",
        value: JSON.stringify({
          basePrice: productData.price,
          extraDayRate: 0.2
        })
      },
      {
        namespace: "rental",
        key: "status",
        type: "single_line_text_field",
        value: "available"
      },
      {
        namespace: "rental",
        key: "location",
        type: "single_line_text_field",
        value: productData.location
      }
    ]
  };

  // GraphQL実行
  const response = await shopifyClient.query({
    data: { query: mutation, variables: { input } }
  });

  return response.body.data.productCreate;
}
```

### 3. メタフィールドの読み取り

```typescript
// app/services/product.service.ts
export class ProductService {
  async getProductWithMetafields(productId: string) {
    const query = `
      query GetProduct($id: ID!) {
        product(id: $id) {
          id
          title
          handle
          variants(first: 10) {
            edges {
              node {
                id
                title
                sku
                price
              }
            }
          }
          metafield(namespace: "rental", key: "basic_info") {
            value
            type
          }
          pricing: metafield(namespace: "rental", key: "pricing") {
            value
            type
          }
          status: metafield(namespace: "rental", key: "status") {
            value
          }
          productGroup: metafield(namespace: "rental", key: "product_group") {
            value
          }
        }
      }
    `;

    const response = await shopifyClient.query({
      data: { query, variables: { id: productId } }
    });

    const product = response.body.data.product;
    
    // JSONメタフィールドのパース
    if (product.metafield?.value) {
      product.basicInfo = JSON.parse(product.metafield.value);
    }
    if (product.pricing?.value) {
      product.pricingData = JSON.parse(product.pricing.value);
    }

    return product;
  }
}
```

### 4. メタフィールドの更新

```typescript
// app/services/product-metafield.service.ts
export class ProductMetafieldService {
  async updateProductGroup(productId: string, groupName: string) {
    const mutation = `
      mutation UpdateMetafield($input: ProductInput!) {
        productUpdate(input: $input) {
          product {
            id
            metafield(namespace: "rental", key: "product_group") {
              value
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const input = {
      id: productId,
      metafields: [
        {
          namespace: "rental",
          key: "product_group",
          type: "single_line_text_field",
          value: groupName
        }
      ]
    };

    return await shopifyClient.query({
      data: { query: mutation, variables: { input } }
    });
  }

  async updateStatus(productId: string, status: string) {
    // 同様の実装
  }
}
```

### 5. バリアントマッピングの管理

```typescript
// app/services/variant-mapping.service.ts
export class VariantMappingService {
  async updateVariantMapping(productId: string, variants: any[]) {
    const variantMapping = {
      variants: variants.map(v => ({
        variantId: v.id,
        rentalDays: v.rentalDays,
        title: v.title,
        sku: v.sku,
        price: v.price
      }))
    };

    const mutation = `
      mutation UpdateVariantMapping($input: ProductInput!) {
        productUpdate(input: $input) {
          product {
            id
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const input = {
      id: productId,
      metafields: [
        {
          namespace: "rental",
          key: "variant_mapping",
          type: "json",
          value: JSON.stringify(variantMapping)
        }
      ]
    };

    return await shopifyClient.query({
      data: { query: mutation, variables: { input } }
    });
  }
}
```

## コード例

### 商品グループによる検索

```typescript
// app/routes/api.products.search.tsx
export async function searchProductsByGroup(groupName: string) {
  // Prismaでメタフィールドを検索
  const products = await prisma.product.findMany({
    where: {
      metadata: {
        path: ['rental', 'product_group'],
        equals: groupName
      }
    },
    include: {
      variants: true
    }
  });

  return products;
}
```

### 価格計算での使用

```typescript
// app/services/pricing/unified-pricing.service.ts
export class UnifiedPricingService {
  calculateRentalPrice(product: any, rentalDays: number): number {
    // メタフィールドから価格情報を取得
    const pricingMetafield = product.metafields?.find(
      (mf: any) => mf.namespace === 'rental' && mf.key === 'pricing'
    );

    if (!pricingMetafield) {
      throw new Error('価格情報が設定されていません');
    }

    const pricingData = JSON.parse(pricingMetafield.value);
    const { basePrice, extraDayRate = 0.2 } = pricingData;

    // 1日目は基本料金、2日目以降は追加料金
    if (rentalDays === 1) {
      return basePrice;
    }

    return Math.round(basePrice * (1 + (rentalDays - 1) * extraDayRate));
  }
}
```

### 在庫状態チェック

```typescript
// app/services/booking-validation.service.ts
export class BookingValidationService {
  async checkProductAvailability(productId: string, startDate: Date, endDate: Date) {
    const product = await this.productService.getProductWithMetafields(productId);
    
    // ステータスチェック
    if (product.status?.value !== 'available') {
      return {
        available: false,
        reason: `商品は${product.status?.value}状態です`
      };
    }

    // 既存予約との競合チェック
    const conflicts = await this.checkBookingConflicts(productId, startDate, endDate);
    
    return {
      available: conflicts.length === 0,
      conflicts
    };
  }
}
```

## トラブルシューティング

### よくある問題と解決策

#### 1. メタフィールドが見つからない
```typescript
// 問題: metafieldがnullになる
const metafield = product.metafield(namespace: "rental", key: "pricing");
// metafield = null

// 解決策: metafieldsリストから検索
const metafields = product.metafields.edges.map(e => e.node);
const pricingField = metafields.find(
  mf => mf.namespace === 'rental' && mf.key === 'pricing'
);
```

#### 2. JSON型メタフィールドのパースエラー
```typescript
// エラーハンドリングを追加
try {
  const data = JSON.parse(metafield.value);
} catch (error) {
  console.error('メタフィールドのパースに失敗:', error);
  // デフォルト値を使用
  const data = { basePrice: 0, extraDayRate: 0.2 };
}
```

#### 3. メタフィールドの更新が反映されない
```typescript
// 解決策: 明示的にIDを指定
const input = {
  id: productId, // 必須
  metafields: [
    {
      id: metafieldId, // 既存の場合はIDも指定
      value: newValue
    }
  ]
};
```

## ベストプラクティス

### 1. メタフィールドの命名規則
- 名前空間: 機能単位で統一（`rental`, `product`）
- キー: snake_case形式で意味が明確な名前
- 日本語定義名: 管理画面で分かりやすい名前

### 2. パフォーマンスの最適化
```typescript
// ❌ 悪い例: N+1問題
for (const product of products) {
  const metafields = await getProductMetafields(product.id);
}

// ✅ 良い例: バッチ取得
const query = `
  query GetProducts($ids: [ID!]!) {
    nodes(ids: $ids) {
      ... on Product {
        id
        metafields(first: 20) {
          edges {
            node {
              namespace
              key
              value
            }
          }
        }
      }
    }
  }
`;
```

### 3. エラーハンドリング
```typescript
// メタフィールド操作の堅牢な実装
export async function safeGetMetafieldValue(
  product: any,
  namespace: string,
  key: string,
  defaultValue: any = null
) {
  try {
    const metafield = product.metafields?.edges?.find(
      (edge: any) => edge.node.namespace === namespace && edge.node.key === key
    );

    if (!metafield) return defaultValue;

    // JSON型の場合はパース
    if (metafield.node.type === 'json') {
      return JSON.parse(metafield.node.value);
    }

    return metafield.node.value;
  } catch (error) {
    console.error(`メタフィールド取得エラー (${namespace}.${key}):`, error);
    return defaultValue;
  }
}
```

### 4. テストの実装
```typescript
// tests/metafields.test.ts
describe('メタフィールド操作', () => {
  it('商品グループの設定と取得', async () => {
    const productId = 'gid://shopify/Product/123';
    const groupName = 'ベーシックソファ 1シーター';

    // 設定
    await productMetafieldService.updateProductGroup(productId, groupName);

    // 取得
    const product = await productService.getProductWithMetafields(productId);
    expect(product.productGroup?.value).toBe(groupName);
  });

  it('価格計算メタフィールドの動作確認', async () => {
    const pricingData = {
      basePrice: 8000,
      extraDayRate: 0.2
    };

    const price1Day = pricingService.calculatePrice(pricingData, 1);
    expect(price1Day).toBe(8000);

    const price3Days = pricingService.calculatePrice(pricingData, 3);
    expect(price3Days).toBe(11200); // 8000 * 1.4
  });
});
```

## 📚 関連リソース

- [メタフィールド定義 最新版](../specifications/メタフィールド定義_最新版_20250525.md)
- [Shopify Metafields API](https://shopify.dev/docs/api/admin-graphql/latest/objects/Metafield)
- [商品同期実装ガイド](../../shopify-prisma-integration-guide.md)

---

本ガイドは実装の進化に合わせて継続的に更新されます。
最終更新: 2025年5月25日