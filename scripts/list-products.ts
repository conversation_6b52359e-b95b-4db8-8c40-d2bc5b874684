/**
 * 商品一覧取得スクリプト
 *
 * このスクリプトは、Prismaデータベースから商品一覧を取得します。
 * 実行方法: npx tsx scripts/list-products.ts
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * 商品一覧を取得する関数
 */
async function getProducts() {
  try {
    console.log('商品一覧を取得中...');

    // 商品一覧を取得
    const products = await prisma.product.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      take: 20 // 最新の20件を取得
    });

    console.log(`商品一覧を取得しました: ${products.length}件`);

    // 商品情報を表示
    console.log('\n----- 商品一覧 -----');
    products.forEach((product, index) => {
      console.log(`商品 ${index + 1}:`);
      console.log(`  ID: ${product.id}`);
      console.log(`  Shopify ID: ${product.shopifyId}`);
      console.log(`  タイトル: ${product.title}`);
      console.log(`  SKU: ${product.sku}`);
      console.log(`  価格: ${product.price}`);
      console.log(`  ステータス: ${product.status}`);
      console.log('---');
    });

    return products;
  } catch (error) {
    console.error('商品一覧取得エラー:', error);
    return [];
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('商品一覧取得を開始します...');

    // 商品一覧を取得
    const products = await getProducts();

    if (products.length === 0) {
      console.log('商品が見つかりませんでした');
    } else {
      console.log(`\n合計 ${products.length} 件の商品が見つかりました`);
    }

    console.log('\n商品一覧取得が完了しました');
  } catch (error) {
    console.error('実行中にエラーが発生しました:');
    console.error(error);
    if (error instanceof Error) {
      console.error('エラーメッセージ:', error.message);
      console.error('スタックトレース:', error.stack);
    }
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
