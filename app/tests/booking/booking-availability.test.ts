/**
 * 予約の重複チェックテスト
 *
 * このテストは以下の機能をテストします：
 * 1. 同じ商品の同じ期間に複数の予約ができないこと
 * 2. 予約期間が重複する場合に適切なエラーが返されること
 * 3. 「貸出不可能な商品も表示する」チェックボックスが正しく機能すること
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { prisma } from '../../db.server';

// モック用のデータ
const mockBookings = [
  {
    id: 'booking1',
    productId: 'product1',
    startDate: new Date('2025-05-01'),
    endDate: new Date('2025-05-05'),
    status: 'CONFIRMED',
  },
  {
    id: 'booking2',
    productId: 'product2',
    startDate: new Date('2025-05-10'),
    endDate: new Date('2025-05-15'),
    status: 'PROVISIONAL',
  },
];

// Prismaのモック
vi.mock('../../db.server', () => ({
  prisma: {
    booking: {
      findMany: vi.fn().mockResolvedValue(mockBookings),
      create: vi.fn().mockImplementation((data) => Promise.resolve({ ...data.data, id: 'new-booking-id' })),
    },
  },
}));

describe('予約の重複チェック', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('利用可能な期間の商品は予約可能と判定される', async () => {
    const productId = 'product1';
    const startDate = new Date('2025-05-06');
    const endDate = new Date('2025-05-09');

    const result = isProductAvailable(productId, startDate, endDate, mockBookings);

    expect(result).toBe(true);
  });

  it('予約済みの期間と重複する場合は予約不可と判定される', async () => {
    const productId = 'product1';
    const startDate = new Date('2025-05-03');
    const endDate = new Date('2025-05-07');

    const result = isProductAvailable(productId, startDate, endDate, mockBookings);

    expect(result).toBe(false);
  });

  it('予約期間が完全に含まれる場合は予約不可と判定される', async () => {
    const productId = 'product1';
    const startDate = new Date('2025-05-02');
    const endDate = new Date('2025-05-04');

    const result = isProductAvailable(productId, startDate, endDate, mockBookings);

    expect(result).toBe(false);
  });

  it('予約期間が完全に包含する場合は予約不可と判定される', async () => {
    const productId = 'product1';
    const startDate = new Date('2025-04-30');
    const endDate = new Date('2025-05-06');

    const result = isProductAvailable(productId, startDate, endDate, mockBookings);

    expect(result).toBe(false);
  });

  it('異なる商品の場合は予約可能と判定される', async () => {
    const productId = 'product3';
    const startDate = new Date('2025-05-01');
    const endDate = new Date('2025-05-05');

    const result = isProductAvailable(productId, startDate, endDate, mockBookings);

    expect(result).toBe(true);
  });

  it('キャンセルされた予約は考慮されない', async () => {
    const cancelledBookings = [
      ...mockBookings,
      {
        id: 'booking3',
        productId: 'product3',
        startDate: new Date('2025-05-01'),
        endDate: new Date('2025-05-05'),
        status: 'CANCELLED',
      },
    ];

    const productId = 'product3';
    const startDate = new Date('2025-05-01');
    const endDate = new Date('2025-05-05');

    const result = isProductAvailable(productId, startDate, endDate, cancelledBookings);

    expect(result).toBe(true);
  });
});

// isProductAvailable関数のモック実装（テスト用）
function isProductAvailable(
  productId: string,
  startDate: Date,
  endDate: Date,
  bookings: any[]
): boolean {
  // 予約データから該当商品の予約を抽出
  const productBookings = bookings.filter(
    (booking) =>
      booking.productId === productId &&
      (booking.status === 'CONFIRMED' || booking.status === 'PROVISIONAL')
  );

  // 予約期間が重複するかチェック
  for (const booking of productBookings) {
    const bookingStart = new Date(booking.startDate);
    const bookingEnd = new Date(booking.endDate);

    // 期間が重複する場合
    if (
      (startDate <= bookingEnd && endDate >= bookingStart) ||
      (bookingStart <= endDate && bookingEnd >= startDate)
    ) {
      return false; // 利用不可
    }
  }

  return true; // 利用可能
}