import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createMaintenanceForShopifyProducts() {
  try {
    // まず全ての商品を取得
    const allProducts = await prisma.product.findMany({
      where: {
        shop: 'peaces-test-block.myshopify.com'
      },
      take: 10,
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`全商品数: ${allProducts.length}`);

    // Shopify IDがある商品をフィルタリング
    const shopifyProducts = allProducts.filter(product => product.shopifyId !== null);

    // もしShopify商品がない場合は、全商品を使用
    const productsToUse = shopifyProducts.length > 0 ? shopifyProducts : allProducts.slice(0, 5);

    console.log(`Shopify商品数: ${shopifyProducts.length}`);
    console.log(`使用する商品数: ${productsToUse.length}`);

    if (productsToUse.length === 0) {
      console.log('商品が見つかりません。');
      return;
    }

    // 商品情報を表示
    productsToUse.forEach((product, index) => {
      console.log(`${index + 1}. ${product.title} (SKU: ${product.sku}) - Shopify ID: ${product.shopifyId || 'なし'}`);
    });

    // 既存のメンテナンスデータを確認
    const existingMaintenance = await prisma.maintenance.findMany();
    console.log(`既存のメンテナンス数: ${existingMaintenance.length}`);

    // テスト用メンテナンスデータを作成
    const maintenanceData = [];

    // 最初の商品: 定期点検（予定）
    if (productsToUse[0]) {
      maintenanceData.push({
        shop: 'peaces-test-block.myshopify.com',
        productId: productsToUse[0].id,
        shopifyProductId: productsToUse[0].shopifyId || 'test-product-1',
        type: 'REGULAR_INSPECTION',
        status: 'SCHEDULED',
        startDate: new Date('2025-01-25'),
        endDate: new Date('2025-01-26'),
        notes: `${productsToUse[0].title}の定期点検予定`
      });
    }

    // 2番目の商品: 修理（進行中）
    if (productsToUse[1]) {
      maintenanceData.push({
        shop: 'peaces-test-block.myshopify.com',
        productId: productsToUse[1].id,
        shopifyProductId: productsToUse[1].shopifyId || 'test-product-2',
        type: 'REPAIR',
        status: 'IN_PROGRESS',
        startDate: new Date('2025-01-20'),
        endDate: new Date('2025-01-22'),
        notes: `${productsToUse[1].title}の修理作業中`
      });
    }

    // 3番目の商品: クリーニング（完了）
    if (productsToUse[2]) {
      maintenanceData.push({
        shop: 'peaces-test-block.myshopify.com',
        productId: productsToUse[2].id,
        shopifyProductId: productsToUse[2].shopifyId || 'test-product-3',
        type: 'CLEANING',
        status: 'COMPLETED',
        startDate: new Date('2025-01-15'),
        endDate: new Date('2025-01-16'),
        notes: `${productsToUse[2].title}のクリーニング完了`
      });
    }

    // 4番目の商品: その他（キャンセル）
    if (productsToUse[3]) {
      maintenanceData.push({
        shop: 'peaces-test-block.myshopify.com',
        productId: productsToUse[3].id,
        shopifyProductId: productsToUse[3].shopifyId || 'test-product-4',
        type: 'OTHER',
        status: 'CANCELLED',
        startDate: new Date('2025-01-10'),
        endDate: new Date('2025-01-12'),
        notes: `${productsToUse[3].title}のメンテナンス作業キャンセル`
      });
    }

    console.log('\nメンテナンスデータを作成中...');

    for (const data of maintenanceData) {
      const maintenance = await prisma.maintenance.create({
        data,
        include: {
          product: true
        }
      });

      console.log(`✓ メンテナンス作成: ${maintenance.product.title} (${maintenance.type} - ${maintenance.status})`);
    }

    console.log('\n✅ Shopify商品に対するメンテナンスデータの作成が完了しました。');
    console.log(`作成されたメンテナンス数: ${maintenanceData.length}`);

  } catch (error) {
    console.error('❌ エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createMaintenanceForShopifyProducts();
