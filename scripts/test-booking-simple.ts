import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();

// Shopify API設定
const config = {
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block',
  apiSecretKey: process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
};

// ショップ名を正規化
const shopName = config.shop.replace('.myshopify.com', '');

// Shopify GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${shopName}.myshopify.com/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': config.apiSecretKey,
      'Content-Type': 'application/json',
    },
  }
);

async function testSimpleBooking() {
  console.log("=== 簡易予約テスト開始 ===\n");

  try {
    // 1. テスト用の商品を取得（バリアントがある商品）
    console.log("1. テスト用商品の取得...");
    const productsQuery = `
      query GetProducts {
        products(first: 5) {
          edges {
            node {
              id
              title
              handle
              status
              variants(first: 10) {
                edges {
                  node {
                    id
                    title
                    sku
                    price
                    inventoryItem {
                      id
                      inventoryLevels(first: 5) {
                        edges {
                          node {
                            location {
                              id
                              name
                            }
                            quantities(names: ["available"]) {
                              name
                              quantity
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
              metafields(first: 10) {
                edges {
                  node {
                    namespace
                    key
                    value
                  }
                }
              }
            }
          }
        }
      }
    `;

    const productsData = await client.request(productsQuery);
    
    // バリアントがある商品を探す
    let testProduct = null;
    let testVariant = null;
    
    for (const edge of productsData.products.edges) {
      const product = edge.node;
      if (product.variants.edges.length > 1 || 
          (product.variants.edges.length === 1 && product.variants.edges[0].node.title !== "Default Title")) {
        testProduct = product;
        // 在庫がある最初のバリアントを使用
        for (const variantEdge of product.variants.edges) {
          const variant = variantEdge.node;
          const inventoryLevel = variant.inventoryItem?.inventoryLevels?.edges?.[0]?.node;
          const quantity = inventoryLevel?.quantities?.find(q => q.name === "available")?.quantity || 0;
          if (quantity > 0) {
            testVariant = variant;
            break;
          }
        }
        if (testVariant) break;
      }
    }
    
    if (!testProduct || !testVariant) {
      console.error("テスト可能な商品が見つかりません（バリアントと在庫が必要）");
      return;
    }

    console.log(`✓ 商品: ${testProduct.title}`);
    console.log(`  バリアント: ${testVariant.title} (在庫あり)\n`);

    // 2. 予約を作成
    console.log("2. 予約を作成...");
    const startDate = new Date('2025-03-01');
    const endDate = new Date('2025-03-03');
    
    const bookingId = `TEST-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const booking = await prisma.booking.create({
      data: {
        bookingId: bookingId,
        shop: shopName,
        product: {
          connect: {
            shop_shopifyId: {
              shop: shopName,
              shopifyId: testProduct.id
            }
          }
        },
        variantId: testVariant.id,
        productTitle: testProduct.title,
        variantTitle: testVariant.title,
        customerName: "テスト太郎",
        customerEmail: "<EMAIL>",
        customerPhone: "090-1234-5678",
        customerId: "test-customer-001",
        startDate: startDate,
        endDate: endDate,
        quantity: 1,
        totalAmount: parseFloat(testVariant.price) * 3, // 3日間
        status: "CONFIRMED",
        notes: "簡易予約テスト"
      }
    });
    
    console.log(`✓ 予約作成成功: ${booking.id}`);
    console.log(`  期間: ${startDate.toISOString().split('T')[0]} ～ ${endDate.toISOString().split('T')[0]}`);
    console.log(`  料金: ¥${booking.totalPrice}\n`);

    // 3. 作成した予約を確認
    console.log("3. 予約情報の確認...");
    const createdBooking = await prisma.booking.findUnique({
      where: { id: booking.id }
    });

    if (createdBooking) {
      console.log("✓ 予約情報:");
      console.log(`  ID: ${createdBooking.id}`);
      console.log(`  商品: ${createdBooking.productTitle}`);
      console.log(`  バリアント: ${createdBooking.variantTitle}`);
      console.log(`  顧客: ${createdBooking.customerName}`);
      console.log(`  状態: ${createdBooking.status}\n`);
    }

    // 4. 重複予約のテスト
    console.log("4. 重複予約のテスト...");
    try {
      const duplicateBookingId = `TEST-DUP-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      const duplicateBooking = await prisma.booking.create({
        data: {
          bookingId: duplicateBookingId,
          shop: shopName,
          productId: testProduct.id,
          variantId: testVariant.id,
          productTitle: testProduct.title,
          variantTitle: testVariant.title,
          customerName: "重複テスト",
          customerEmail: "<EMAIL>",
          customerPhone: "090-9876-5432",
          customerId: "test-customer-002",
          startDate: new Date('2025-03-02'), // 期間が重複
          endDate: new Date('2025-03-04'),
          quantity: 1,
          totalAmount: parseFloat(testVariant.price) * 3,
          status: "CONFIRMED",
          notes: "重複テスト"
        }
      });
      console.log(`⚠️  重複予約が作成されました: ${duplicateBooking.id}`);
      console.log("   ※本来はエラーになるべきです");
    } catch (error) {
      console.log("✓ 期待通り重複エラーが発生しました");
    }

    // 5. クリーンアップ
    console.log("\n5. テストデータのクリーンアップ...");
    const deletedCount = await prisma.booking.deleteMany({
      where: {
        OR: [
          { notes: "簡易予約テスト" },
          { notes: "重複テスト" }
        ]
      }
    });
    console.log(`✓ ${deletedCount.count}件の予約を削除しました`);

  } catch (error) {
    console.error("テストエラー:", error);
  } finally {
    await prisma.$disconnect();
  }

  console.log("\n=== テスト完了 ===");
}

// スクリプト実行
testSimpleBooking().catch((error) => {
  console.error("実行エラー:", error);
  process.exit(1);
});