import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  FormLayout,
  TextField,
  Button,
  Select,
  Text,
  BlockStack,
  InlineStack,
  Banner,
  Divider,
  Checkbox,
} from "@shopify/polaris";
import { useState } from "react";
import { authenticate } from "../shopify.server";

// 商品作成用のGraphQLミューテーション
const CREATE_PRODUCT_MUTATION = `
  mutation productCreate($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// バリエーション作成用のGraphQLミューテーション
const CREATE_VARIANT_MUTATION = `
  mutation productVariantCreate($input: ProductVariantInput!) {
    productVariantCreate(input: $input) {
      productVariant {
        id
        title
        price
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// ローダー関数
export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  
  return json({
    locations: [
      { label: "NY", value: "NY" },
      { label: "PR", value: "PR" },
    ],
  });
};

// アクション関数
export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin } = await authenticate.admin(request);
  const formData = await request.formData();
  
  const title = formData.get("title") as string;
  const description = formData.get("description") as string;
  const sku = formData.get("sku") as string;
  const location = formData.get("location") as string;
  const basePrice = parseFloat(formData.get("basePrice") as string);
  const addVariants = formData.get("addVariants") === "true";
  
  try {
    // 商品を作成
    const productResponse = await admin.graphql(CREATE_PRODUCT_MUTATION, {
      variables: {
        input: {
          title,
          descriptionHtml: description,
          status: "ACTIVE",
          vendor: "レンタルショップ",
          productType: "レンタル商品",
          variants: [
            {
              price: basePrice.toString(),
              sku: sku,
              title: "1日レンタル",
              inventoryQuantities: {
                locationId: "gid://shopify/Location/1",
                availableQuantity: 1
              }
            }
          ]
        }
      }
    });
    
    const productData = await productResponse.json();
    
    if (productData.data.productCreate.userErrors.length > 0) {
      return json({ 
        errors: productData.data.productCreate.userErrors,
        success: false
      });
    }
    
    const productId = productData.data.productCreate.product.id;
    
    // バリエーションを追加する場合
    if (addVariants) {
      const variantTitles = [
        "2日レンタル",
        "3日レンタル",
        "4日レンタル",
        "5日レンタル",
        "6日レンタル",
        "7日レンタル",
        "8日以上レンタル"
      ];
      
      const variantPrices = [
        Math.round(basePrice + basePrice * 0.2),
        Math.round(basePrice + basePrice * 0.2 * 2),
        Math.round(basePrice + basePrice * 0.2 * 3),
        Math.round(basePrice + basePrice * 0.2 * 4),
        Math.round(basePrice + basePrice * 0.2 * 5),
        Math.round(basePrice + basePrice * 0.2 * 6),
        Math.round(basePrice + basePrice * 0.2 * 6)
      ];
      
      // バリエーションを順番に作成
      for (let i = 0; i < variantTitles.length; i++) {
        await admin.graphql(CREATE_VARIANT_MUTATION, {
          variables: {
            input: {
              productId,
              price: variantPrices[i].toString(),
              title: variantTitles[i],
              sku: `${sku}-${i+2}day`,
              inventoryQuantities: {
                locationId: "gid://shopify/Location/1",
                availableQuantity: 1
              }
            }
          }
        });
      }
    }
    
    return redirect("/app/products");
  } catch (error) {
    console.error("商品作成エラー:", error);
    return json({ 
      errors: [{ message: "商品の作成中にエラーが発生しました。" }],
      success: false
    });
  }
};

// 商品作成フォームコンポーネント
export default function CreateProduct() {
  const { locations } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  
  const [addVariants, setAddVariants] = useState(true);
  
  return (
    <Page
      title="新規商品登録"
      backAction={{ content: "商品一覧に戻る", url: "/app/products" }}
    >
      <Layout>
        <Layout.Section>
          {actionData?.errors && (
            <Banner tone="critical">
              <BlockStack gap="200">
                <Text as="h2" fontWeight="medium">エラーが発生しました</Text>
                <ul>
                  {actionData.errors.map((error, index) => (
                    <li key={index}>{error.message}</li>
                  ))}
                </ul>
              </BlockStack>
            </Banner>
          )}
          
          <Card>
            <Form method="post">
              <FormLayout>
                <Text variant="headingMd" as="h2">基本情報</Text>
                
                <TextField
                  label="商品名"
                  name="title"
                  type="text"
                  autoComplete="off"
                  required
                />
                
                <TextField
                  label="商品説明"
                  name="description"
                  multiline={4}
                  autoComplete="off"
                />
                
                <TextField
                  label="SKU"
                  name="sku"
                  type="text"
                  helpText="例: 212-05-023"
                  autoComplete="off"
                  required
                />
                
                <Select
                  label="保管場所"
                  name="location"
                  options={locations}
                  required
                />
                
                <TextField
                  label="基本価格 (1日レンタル)"
                  name="basePrice"
                  type="number"
                  min="0"
                  step="100"
                  suffix="円"
                  autoComplete="off"
                  required
                />
                
                <Divider />
                
                <Checkbox
                  label="レンタル日数バリエーションを自動追加する"
                  checked={addVariants}
                  onChange={setAddVariants}
                  name="addVariants"
                  value={addVariants.toString()}
                />
                
                {addVariants && (
                  <Banner tone="info">
                    <Text>
                      以下のバリエーションが自動的に追加されます：
                      <ul>
                        <li>1日レンタル（基本価格）</li>
                        <li>2日レンタル（基本価格 + 20%）</li>
                        <li>3日レンタル（基本価格 + 40%）</li>
                        <li>4日レンタル（基本価格 + 60%）</li>
                        <li>5日レンタル（基本価格 + 80%）</li>
                        <li>6日レンタル（基本価格 + 100%）</li>
                        <li>7日レンタル（基本価格 + 120%）</li>
                        <li>8日以上レンタル（基本価格 + 120%）</li>
                      </ul>
                    </Text>
                  </Banner>
                )}
                
                <InlineStack align="end">
                  <Button submit primary loading={isSubmitting}>
                    商品を登録
                  </Button>
                </InlineStack>
              </FormLayout>
            </Form>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
