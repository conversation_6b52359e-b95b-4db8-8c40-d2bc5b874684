# テストデータ作成手順書

## 概要

このドキュメントでは、バックエンド管理画面開発のためのテストデータを作成する具体的な手順を説明します。既存のデータをクリーンアップし、新しいテストデータを作成する方法を詳細に解説します。

## 前提条件

- Node.js v20.18.1以上がインストールされていること
- プロジェクトの依存関係がインストールされていること（`npm install`を実行済み）
- Shopify APIキーとアクセストークンが設定されていること（`.env`ファイル）
- Prismaデータベースが設定されていること

## 1. 環境設定の確認

### 1.1 環境変数の確認

`.env`ファイルに以下の設定があることを確認します：

```
SHOPIFY_SHOP=peaces-test-block.myshopify.com
SHOPIFY_API_KEY=your_api_key
SHOPIFY_API_SECRET=your_api_secret
SHOPIFY_ADMIN_API_ACCESS_TOKEN=your_admin_api_access_token
SHOPIFY_API_VERSION=2024-01
```

### 1.2 データベース接続の確認

```bash
npx prisma db pull
```

## 2. データのクリーンアップ

### 2.1 Shopifyデータの削除

#### 手動削除方法
1. Shopify管理画面にログイン
2. 商品 > すべての商品から、テスト用商品を選択して削除
3. 顧客 > すべての顧客から、テスト用顧客を選択して削除
4. 注文 > すべての注文から、テスト用注文を選択して削除（必要に応じて）

#### APIを使用した削除方法
```bash
node scripts/test-products-setup.js
```

### 2.2 Prismaデータベースのクリーンアップ

```bash
node scripts/reset-database.js
```

## 3. テスト商品データの作成

### 3.1 基本的なテスト商品の作成

```bash
node scripts/test-products-setup.js
```

このスクリプトは以下の商品を作成します：
- テスト商品A（家具）
- テスト商品B（電化製品）
- テスト商品C（食器）

各商品には以下のバリエーションが作成されます：
- 1日レンタル
- 2日レンタル
- 3日レンタル
- 4日レンタル
- 5日レンタル
- 6日レンタル
- 7日レンタル
- 8日以上レンタル

### 3.2 追加のサンプル商品作成

```bash
node scripts/create-iziz-sample-products.js
```

このスクリプトは、iziz.co.jpを参考にした以下のような商品を作成します：
- ソファ類
- テーブル類
- チェア類
- ラグ・マット類

各商品には以下のメタフィールドが設定されます：
- basic_info（基本情報）
- pricing（料金設定）
- inventory_items（在庫アイテム情報）

### 3.3 バリエーション商品の作成

```bash
node scripts/create-test-products.js
```

このスクリプトは、複数のバリエーションを持つ商品を作成します：
- ベーシックソファ（色・サイズのバリエーション）
- ダイニングテーブル（サイズのバリエーション）

## 4. テスト顧客データの作成

### 4.1 顧客データの作成

```bash
node scripts/create-test-data.ts
```

このスクリプトは以下のような顧客データを作成します：
- 山田太郎（スペースあり）
- 佐藤花子（スペースなし）
- 鈴木一郎（スペースあり）
- TanakaJiro（英語名）

## 5. テスト予約データの作成

### 5.1 予約データの作成

```bash
node scripts/create-test-data.ts
```

このスクリプトは以下のような予約データを作成します：
- 確定予約（CONFIRMED）
- 仮予約（PROVISIONAL）
- キャンセル済み予約（CANCELLED）
- 完了済み予約（COMPLETED）

### 5.2 特定商品の予約データ作成

```bash
node scripts/initialize-booking-data.js [商品ID]
```

## 6. データの同期と検証

### 6.1 Shopify-Prisma同期テスト

```bash
npx tsx scripts/test-shopify-prisma-integration.ts
```

このスクリプトは以下をテストします：
- 商品データの同期
- 在庫カレンダーの更新
- 予約データの作成
- 注文データとの連携

### 6.2 予約システムテスト

```bash
npx tsx scripts/test-booking-system.js
```

このスクリプトは以下をテストします：
- 予約の作成
- 予約の更新
- 予約のキャンセル
- 予約の重複チェック

## 7. 管理画面での確認

### 7.1 商品管理画面

1. `/app/products`にアクセス
2. 商品一覧が表示されることを確認
3. 商品詳細画面で情報が正しく表示されることを確認
4. 商品検索・フィルタリングが機能することを確認

### 7.2 予約管理画面

1. `/app/bookings`にアクセス
2. 予約一覧が表示されることを確認
3. 予約詳細画面で情報が正しく表示されることを確認
4. 予約検索・フィルタリングが機能することを確認
5. 予約カレンダーが正しく表示されることを確認

### 7.3 顧客管理画面

1. `/app/customers`にアクセス
2. 顧客一覧が表示されることを確認
3. 顧客詳細画面で情報が正しく表示されることを確認
4. 顧客検索が機能することを確認（日本語名での検索を含む）

## 8. トラブルシューティング

### 8.1 スクリプト実行エラー

- Node.jsのバージョンを確認（v20.18.1以上推奨）
- 依存関係が正しくインストールされているか確認
- 環境変数が正しく設定されているか確認

### 8.2 Shopify API関連のエラー

- アクセストークンの有効期限を確認
- APIリクエスト制限に達していないか確認
- APIバージョンが正しいか確認（2024-01推奨）

### 8.3 データベース関連のエラー

- Prismaスキーマが最新かどうか確認
- データベース接続が正常か確認
- マイグレーションが必要かどうか確認

## 9. 補足情報

- テストデータは定期的にクリーンアップすることをお勧めします
- 実際の運用環境でテストを行う場合は、本番データに影響を与えないよう注意してください
- 各スクリプトのオプションや引数については、スクリプトファイル内のコメントを参照してください
