# 商品バリエーション方式テスト手順

## 概要

このドキュメントでは、商品バリエーション方式の実装をテストするための手順を説明します。テスト環境のセットアップから各機能のテスト方法まで、詳細に記載しています。

## テスト環境のセットアップ

### 1. テスト環境の一括セットアップ

以下のコマンドを実行すると、テスト環境が一括でセットアップされます：

```bash
npm run setup:variant-test-env
```

このコマンドは以下の処理を順番に実行します：

1. 既存のテスト商品を削除
2. 新しい仕様に沿ったテスト商品を追加
3. 商品バリエーションを設定
4. テスト用の予約データを作成

### 2. 個別のセットアップコマンド

必要に応じて、以下の個別コマンドを実行することもできます：

```bash
# テスト商品のセットアップ（削除と作成）
npm run setup:variant-test-products

# 商品バリエーションの設定
npm run setup:product-variants

# テスト用予約データの作成
npm run create:test-bookings
```

## テスト項目

### 1. 日付選択に基づくバリエーション自動選択

#### テスト手順

1. テスト商品の詳細ページにアクセス
2. 日付選択カレンダーで開始日と終了日を選択
3. 選択した日数に基づいて適切なバリエーションが自動選択されることを確認

#### 確認ポイント

- 1日選択：「1日」バリエーションが選択される
- 2日選択：「2日」バリエーションが選択される
- ...
- 7日選択：「7日」バリエーションが選択される
- 8日以上選択：「8日以上」バリエーションが選択される
- 仮予約選択：「仮予約」バリエーションが選択される（仮予約ボタンをクリックした場合）

### 2. カートへの追加と予約情報の表示

#### テスト手順

1. テスト商品の詳細ページで日付を選択
2. 「カートに追加」ボタンをクリック
3. カートページで予約情報が正しく表示されることを確認

#### 確認ポイント

- レンタル期間（開始日と終了日）が表示される
- レンタル日数が表示される
- 計算された料金が表示される
- 選択されたバリエーションが表示される

### 3. 仮予約と本予約の管理

#### テスト手順

1. 管理画面の予約カレンダーにアクセス
2. 仮予約と本予約が正しく表示されることを確認
3. 仮予約を本予約に変更するテスト
4. 予約のキャンセルテスト
5. 「仮予約」バリエーションを使用した仮予約テスト

#### 確認ポイント

- 仮予約と本予約が異なる色で表示される
- 仮予約から本予約への変更が正常に処理される
- 予約のキャンセルが正常に処理される
- 「仮予約」バリエーションを選択すると、基本料金の10%の金額で仮予約が作成される
- 仮予約から本予約に変更する際に、適切な日数のバリエーションに変更され、正しい金額が計算される

### 4. 長期レンタル（8日以上）の処理

#### テスト手順

1. テスト商品の詳細ページで8日以上の期間を選択
2. 「カートに追加」ボタンをクリック
3. カートページで料金が正しく計算されていることを確認

#### 確認ポイント

- 「8日以上」バリエーションが選択される
- 料金が正しく計算される（7日レンタル料金 + 8日目以降の追加料金）

### 5. 予約カレンダーでの予約状況表示

#### テスト手順

1. テスト商品の詳細ページにアクセス
2. 予約カレンダーで既存の予約が正しく表示されることを確認

#### 確認ポイント

- 本予約の日付が「予」として表示される
- 仮予約の日付が「仮」として表示される
- 予約済みの日付は選択できない

### 6. チェックアウトフロー

#### テスト手順

1. テスト商品をカートに追加
2. チェックアウトボタンをクリック
3. チェックアウトページで予約情報が正しく表示されることを確認
4. 注文を完了

#### 確認ポイント

- チェックアウトページで予約情報が表示される
- 注文が正常に処理される
- 注文完了後、予約データが正しく保存される

## テスト用データ

### テスト商品

以下の3つのテスト商品が作成されます：

1. **テスト商品A（家具）**
   - 基本価格: 10,000円
   - SKU: TEST-FURNITURE-A

2. **テスト商品B（電化製品）**
   - 基本価格: 5,000円
   - SKU: TEST-ELECTRONICS-B

3. **テスト商品C（食器）**
   - 基本価格: 3,000円
   - SKU: TEST-TABLEWARE-C

### テスト予約

以下の予約データが作成されます：

1. **本予約（近日）**
   - 商品: テスト商品A
   - 期間: 現在から3日後〜5日後
   - 顧客: テスト顧客A

2. **本予約（来月）**
   - 商品: テスト商品A
   - 期間: 来月10日〜15日
   - 顧客: テスト顧客B

3. **仮予約（近日）**
   - 商品: テスト商品B
   - 期間: 現在から7日後〜10日後
   - 顧客: テスト顧客C

4. **仮予約（来月）**
   - 商品: テスト商品B
   - 期間: 来月20日〜25日
   - 顧客: テスト顧客D

5. **長期レンタル（8日以上）**
   - 商品: テスト商品C
   - 期間: 現在から15日後〜25日後
   - 顧客: テスト顧客E

6. **キャンセル済み予約**
   - 商品: テスト商品C
   - 期間: 過去の日付
   - 顧客: テスト顧客F

## トラブルシューティング

### テスト商品が表示されない場合

1. Shopify管理画面で商品が正しく作成されているか確認
2. データベースに商品が登録されているか確認
3. 必要に応じて `npm run setup:variant-test-products` を再実行

### 予約データが表示されない場合

1. データベースに予約データが登録されているか確認
2. 必要に応じて `npm run create:test-bookings` を再実行

### バリエーションが自動選択されない場合

1. ブラウザのコンソールでエラーメッセージを確認
2. `date_picker.liquid` ファイルが正しく修正されているか確認
