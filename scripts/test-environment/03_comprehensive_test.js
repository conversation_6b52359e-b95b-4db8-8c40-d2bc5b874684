#!/usr/bin/env node

/**
 * 包括的テストスクリプト
 * 
 * 全ての機能（予約・商品登録・価格計算・カテゴリ・在庫）をテスト
 */

import { PrismaClient } from '@prisma/client';
import { CategoryMasterService } from '../../app/services/category-master.service.js';
import { addDays, format } from 'date-fns';

const prisma = new PrismaClient();
const categoryService = new CategoryMasterService();

/**
 * 商品登録テスト
 */
async function testProductRegistration(shop) {
  console.log('\n=== 商品登録テスト ===');
  
  try {
    // 商品数確認
    const productCount = await prisma.product.count({ where: { shop } });
    console.log(`✅ 登録商品数: ${productCount}件`);
    
    // 各商品の詳細確認
    const products = await prisma.product.findMany({
      where: { shop },
      select: {
        sku: true,
        title: true,
        status: true,
        inventory: true,
        basicInfo: true
      }
    });
    
    console.log('\n📦 商品一覧:');
    products.forEach(product => {
      console.log(`  ${product.sku}: ${product.title}`);
      console.log(`    ステータス: ${product.status}, 在庫: ${product.inventory}`);
      if (product.basicInfo) {
        const basicInfo = product.basicInfo as any;
        console.log(`    カテゴリ: ${basicInfo.category}, ベース価格: ¥${basicInfo.basePrice}`);
      }
    });
    
    return { success: true, count: productCount };
    
  } catch (error) {
    console.error('❌ 商品登録テストエラー:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * カテゴリマスタテスト
 */
async function testCategoryMaster(shop) {
  console.log('\n=== カテゴリマスタテスト ===');
  
  try {
    // カテゴリ一覧取得
    const categories = await categoryService.getActiveCategoriesByShop(shop);
    console.log(`✅ アクティブカテゴリ数: ${categories.length}件`);
    
    // SKU解析テスト
    const testSKUs = ['212-05-023', '201-07-107', '211-12-954', '206-02-320'];
    console.log('\n🔍 SKU解析テスト:');
    
    for (const sku of testSKUs) {
      const skuStructure = categoryService.parseSKU(sku);
      if (skuStructure) {
        const validation = await categoryService.validateSKU(shop, sku);
        console.log(`  ${sku}:`);
        console.log(`    カテゴリコード: ${skuStructure.categoryCode}`);
        console.log(`    サブカテゴリコード: ${skuStructure.subCategoryCode}`);
        console.log(`    有効性: ${validation.isValid ? '✅' : '❌'}`);
        
        if (validation.categoryExists) {
          const category = await categoryService.getCategoryByCode(shop, skuStructure.categoryCode);
          console.log(`    カテゴリ名: ${category?.name}`);
        }
      } else {
        console.log(`  ${sku}: ❌ SKU形式エラー`);
      }
    }
    
    // SKU生成テスト
    console.log('\n🔧 SKU生成テスト:');
    const generateTests = [
      { categoryCode: '212', subCategoryCode: '05' },
      { categoryCode: '201', subCategoryCode: '07' }
    ];
    
    for (const test of generateTests) {
      try {
        const newSKU = await categoryService.generateNextSKU(shop, test.categoryCode, test.subCategoryCode);
        console.log(`  ${test.categoryCode}-${test.subCategoryCode}: ${newSKU} ✅`);
      } catch (error) {
        console.log(`  ${test.categoryCode}-${test.subCategoryCode}: ❌ ${error.message}`);
      }
    }
    
    return { success: true, categoryCount: categories.length };
    
  } catch (error) {
    console.error('❌ カテゴリマスタテストエラー:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * 価格計算テスト
 */
async function testPriceCalculation() {
  console.log('\n=== 価格計算テスト ===');
  
  const basePrice = 1500; // テスト用ベース価格
  
  const testCases = [
    { days: 1, expected: 1500, description: '1日レンタル（100%）' },
    { days: 2, expected: 1800, description: '2日レンタル（100% + 20%）' },
    { days: 3, expected: 2100, description: '3日レンタル（100% + 20%×2）' },
    { days: 7, expected: 3300, description: '7日レンタル（100% + 20%×6）' },
    { days: 8, expected: 3450, description: '8日レンタル（100% + 20%×6 + 10%×1）' },
    { days: 10, expected: 3750, description: '10日レンタル（100% + 20%×6 + 10%×3）' }
  ];
  
  console.log(`ベース価格: ¥${basePrice}`);
  console.log('\n💰 価格計算結果:');
  
  let allPassed = true;
  
  testCases.forEach(testCase => {
    const calculated = calculateRentalPrice(basePrice, testCase.days);
    const passed = calculated === testCase.expected;
    
    console.log(`  ${testCase.days}日: ¥${calculated} ${passed ? '✅' : '❌'} (期待値: ¥${testCase.expected})`);
    console.log(`    ${testCase.description}`);
    
    if (!passed) allPassed = false;
  });
  
  // 仮予約価格テスト
  const provisionalPrice = Math.floor(basePrice * 0.1);
  console.log(`\n🔄 仮予約価格: ¥${provisionalPrice} (ベース価格の10%)`);
  
  return { success: allPassed, testCount: testCases.length };
}

/**
 * レンタル価格計算関数
 */
function calculateRentalPrice(basePrice, days) {
  if (days === 1) return basePrice;
  if (days >= 2 && days <= 7) {
    return basePrice + Math.floor(basePrice * 0.2 * (days - 1));
  }
  if (days >= 8) {
    const base7Days = basePrice + Math.floor(basePrice * 0.2 * 6); // 7日分
    const additionalDays = days - 7;
    return base7Days + Math.floor(basePrice * 0.1 * additionalDays);
  }
  return basePrice;
}

/**
 * 予約システムテスト
 */
async function testBookingSystem(shop) {
  console.log('\n=== 予約システムテスト ===');
  
  try {
    // 予約数確認
    const bookingCount = await prisma.booking.count({ where: { shop } });
    console.log(`✅ 総予約数: ${bookingCount}件`);
    
    // ステータス別集計
    const statusSummary = await prisma.booking.groupBy({
      by: ['status'],
      where: { shop },
      _count: { status: true }
    });
    
    console.log('\n📊 ステータス別予約数:');
    statusSummary.forEach(summary => {
      console.log(`  ${summary.status}: ${summary._count.status}件`);
    });
    
    // 重複チェックテスト
    console.log('\n🔍 重複予約チェック:');
    const conflicts = await findBookingConflicts(shop);
    if (conflicts.length === 0) {
      console.log('  ✅ 重複予約なし');
    } else {
      console.log(`  ⚠️ 重複予約発見: ${conflicts.length}件`);
      conflicts.forEach(conflict => {
        console.log(`    商品ID: ${conflict.productId}, 期間: ${conflict.period}`);
      });
    }
    
    // 今後の予約確認
    const upcomingBookings = await prisma.booking.findMany({
      where: {
        shop,
        startDate: { gte: new Date() },
        status: { in: ['confirmed', 'provisional'] }
      },
      include: {
        product: { select: { sku: true, title: true } }
      },
      orderBy: { startDate: 'asc' },
      take: 5
    });
    
    console.log('\n📅 今後の予約（上位5件）:');
    upcomingBookings.forEach(booking => {
      console.log(`  ${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')}`);
      console.log(`    ${booking.product?.sku}: ${booking.customerName} (${booking.status})`);
    });
    
    return { success: true, bookingCount, conflicts: conflicts.length };
    
  } catch (error) {
    console.error('❌ 予約システムテストエラー:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * 予約重複を検出
 */
async function findBookingConflicts(shop) {
  const bookings = await prisma.booking.findMany({
    where: {
      shop,
      status: { in: ['confirmed', 'provisional'] }
    },
    orderBy: [{ productId: 'asc' }, { startDate: 'asc' }]
  });
  
  const conflicts = [];
  
  for (let i = 0; i < bookings.length - 1; i++) {
    const current = bookings[i];
    const next = bookings[i + 1];
    
    if (current.productId === next.productId) {
      // 同じ商品での重複チェック
      if (current.endDate > next.startDate) {
        conflicts.push({
          productId: current.productId,
          period: `${format(current.startDate, 'yyyy-MM-dd')} 〜 ${format(next.endDate, 'yyyy-MM-dd')}`
        });
      }
    }
  }
  
  return conflicts;
}

/**
 * 在庫管理テスト
 */
async function testInventoryManagement(shop) {
  console.log('\n=== 在庫管理テスト ===');
  
  try {
    // 在庫状況確認
    const inventorySummary = await prisma.product.groupBy({
      by: ['status'],
      where: { shop },
      _count: { status: true },
      _sum: { inventory: true }
    });
    
    console.log('📦 在庫状況:');
    inventorySummary.forEach(summary => {
      console.log(`  ${summary.status}: ${summary._count.status}商品, 総在庫: ${summary._sum.inventory}`);
    });
    
    // メンテナンス中商品の確認
    const maintenanceProducts = await prisma.product.findMany({
      where: { shop, status: 'maintenance' },
      select: { sku: true, title: true, inventory: true }
    });
    
    if (maintenanceProducts.length > 0) {
      console.log('\n🔧 メンテナンス中商品:');
      maintenanceProducts.forEach(product => {
        console.log(`  ${product.sku}: ${product.title} (在庫: ${product.inventory})`);
      });
    }
    
    // 在庫切れ商品の確認
    const outOfStockProducts = await prisma.product.findMany({
      where: { shop, inventory: 0 },
      select: { sku: true, title: true, status: true }
    });
    
    if (outOfStockProducts.length > 0) {
      console.log('\n📭 在庫切れ商品:');
      outOfStockProducts.forEach(product => {
        console.log(`  ${product.sku}: ${product.title} (${product.status})`);
      });
    }
    
    return { 
      success: true, 
      totalProducts: inventorySummary.reduce((sum, item) => sum + item._count.status, 0),
      maintenanceCount: maintenanceProducts.length,
      outOfStockCount: outOfStockProducts.length
    };
    
  } catch (error) {
    console.error('❌ 在庫管理テストエラー:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * メイン包括テスト
 */
async function runComprehensiveTest() {
  const shop = 'ease-next-temp.myshopify.com';
  
  console.log('🧪 === 包括的システムテスト開始 ===');
  console.log(`対象ショップ: ${shop}`);
  console.log(`テスト実行日時: ${new Date().toLocaleString('ja-JP')}`);
  
  const results = {};
  
  try {
    await prisma.$connect();
    console.log('✅ データベース接続成功');
    
    // 各テストを実行
    results.productRegistration = await testProductRegistration(shop);
    results.categoryMaster = await testCategoryMaster(shop);
    results.priceCalculation = await testPriceCalculation();
    results.bookingSystem = await testBookingSystem(shop);
    results.inventoryManagement = await testInventoryManagement(shop);
    
    // 結果サマリー
    console.log('\n🎯 === テスト結果サマリー ===');
    
    const testResults = [
      { name: '商品登録', result: results.productRegistration },
      { name: 'カテゴリマスタ', result: results.categoryMaster },
      { name: '価格計算', result: results.priceCalculation },
      { name: '予約システム', result: results.bookingSystem },
      { name: '在庫管理', result: results.inventoryManagement }
    ];
    
    let passedTests = 0;
    testResults.forEach(test => {
      const status = test.result.success ? '✅ PASS' : '❌ FAIL';
      console.log(`  ${test.name}: ${status}`);
      if (test.result.success) passedTests++;
    });
    
    console.log(`\n📊 テスト成功率: ${passedTests}/${testResults.length} (${Math.round(passedTests / testResults.length * 100)}%)`);
    
    if (passedTests === testResults.length) {
      console.log('🎉 全てのテストが成功しました！');
    } else {
      console.log('⚠️ 一部のテストが失敗しました。詳細を確認してください。');
    }
    
  } catch (error) {
    console.error('❌ テスト実行エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
  
  return results;
}

/**
 * メイン処理
 */
async function main() {
  try {
    const results = await runComprehensiveTest();
    
    console.log('\n=== テスト完了 ===');
    console.log('次のステップ:');
    console.log('1. /app/bookings/aggregate でカテゴリフィルタリングをテスト');
    console.log('2. 予約作成・変更・キャンセルの動作確認');
    console.log('3. 価格計算の正確性確認');
    console.log('4. 在庫管理の動作確認');
    
  } catch (error) {
    console.error('包括テストに失敗しました:', error);
    process.exit(1);
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
