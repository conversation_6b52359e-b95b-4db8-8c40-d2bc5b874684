# レンタル商品メタフィールド構造

## 概要

このドキュメントでは、レンタル商品ECシステムで使用しているShopifyメタフィールドの構造と使用方法について説明します。メタフィールドは商品の追加情報を管理するために使用され、レンタル固有の情報を保存します。

**重要**: このドキュメントは、Shopifyとneonデータベースの連携を考慮した最適なメタフィールド構造を定義しています。AIが間違えないよう、また開発者が正確に実装できるよう、詳細に記述しています。

## 設計方針

以下の方針に基づいてメタフィールド構造を設計しています：

1. **シンプルさ優先**: 必要最小限のメタフィールドのみを定義
2. **人の管理が必要な項目**: 独立したシンプルなメタフィールドとして定義
3. **システム管理の項目**: JSONまたはNeonデータベースで管理
4. **重複の排除**: 同じ情報を複数の場所で管理しない
5. **責任の分離**:
   - Shopify: 商品の静的情報と基本的な状態
   - Neon: 動的な情報、履歴、予約情報

## メタフィールド構造

### 1. 人が直接管理する項目（独立したメタフィールド）

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| rental | maintenance_status | 単一行のテキスト | 商品のメンテナンス状態 | 商品の物理的状態を管理（良好、メンテナンス中、修理中、廃棄済み） |
| rental | location | 単一行のテキスト | 商品の保管場所 | 在庫の物理的な保管場所を管理（東京倉庫、大阪倉庫など） |
| rental | maintenance_notes | 複数行のテキスト | メンテナンスに関する備考 | メンテナンス作業の詳細や注意点を記録 |
| rental | last_maintenance_date | 日付 | 最後にメンテナンスを行った日 | メンテナンス履歴の管理に使用 |

### 2. システムで管理する項目（JSON形式）

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| rental | basic_info | JSON | レンタル商品の基本情報 | 商品コード、フリガナなどの基本データを管理 |
| rental | pricing | JSON | レンタル料金設定 | 基本料金、割引率などの料金計算用データを管理 |

### 3. 寸法情報（独立したメタフィールド）

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| product | width | 整数 | 商品の幅 | 寸法情報の管理（mm単位） |
| product | depth | 整数 | 商品の奥行き | 寸法情報の管理（mm単位） |
| product | height | 整数 | 商品の高さ | 寸法情報の管理（mm単位） |

### 4. 購入・廃棄情報（独立したメタフィールド）

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| rental | purchase_price | 小数 | 商品の購入価格 | 原価管理に使用 |
| rental | purchase_date | 日付 | 商品の購入日 | 資産管理に使用 |
| rental | purchase_place | 単一行のテキスト | 商品の購入場所 | 購入履歴の管理に使用 |
| rental | manufacturer | 単一行のテキスト | 商品のメーカー | メーカー情報の管理に使用 |
| rental | is_disposed | boolean | 廃棄済みフラグ | 廃棄済み商品の管理に使用 |
| rental | disposal_date | 日付 | 商品の廃棄日 | 廃棄履歴の管理に使用 |
| rental | disposal_reason | 複数行のテキスト | 商品を廃棄した理由 | 廃棄理由の記録に使用 |

### 5. 商品属性情報（独立したメタフィールド）

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| rental | material | 単一行のテキスト | 素材情報 | 商品の素材を管理 |
| rental | color | 単一行のテキスト | 色情報 | 商品の色を管理 |
| rental | designer | 単一行のテキスト | デザイナー情報 | 商品のデザイナーを管理 |

### 6. バリエーション管理（独立したメタフィールド）

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| rental | variation_group | 単一行のテキスト | バリエーショングループID | 関連商品のグループ化に使用 |
| rental | variation_type | 単一行のテキスト | バリエーションタイプ | バリエーションの種類（色、サイズなど）を管理 |

### 7. Neonデータベースで管理する項目（Shopifyには最小限の情報のみ保存）

| 名前空間 | キー | タイプ | 説明 | 使用目的 |
|---------|-----|-------|------|--------|
| rental | status | 単一行のテキスト | 商品の利用可能状態 | Neonデータベースの在庫状態と同期するための補助情報 |
| rental | rental_dates | JSON | レンタル期間情報 | Neonデータベースとの同期用（詳細はNeonで管理） |
| rental | bookings | JSON | 予約情報一覧 | Neonデータベースとの同期用（詳細はNeonで管理） |
| rental | next_maintenance_date | 日付 | 次回メンテナンス予定日 | Neonデータベースとの同期用（詳細はNeonで管理） |

## メタフィールドの詳細

### 1. 人が直接管理する項目

#### rental.maintenance_status

商品のメンテナンス状態を管理するメタフィールドです。

- **タイプ**: 単一行のテキスト
- **推奨値**:
  - 「良好」: 通常使用可能な状態
  - 「メンテナンス中」: 定期点検中の状態
  - 「修理中」: 故障や損傷により修理中の状態
  - 「廃棄済み」: 使用不可で廃棄予定の状態

#### rental.location

商品の保管場所を管理するメタフィールドです。

- **タイプ**: 単一行のテキスト
- **例**: 「東京倉庫」、「大阪倉庫」など

#### rental.maintenance_notes

メンテナンスに関する備考を記録するメタフィールドです。

- **タイプ**: 複数行のテキスト
- **例**: 「2024/4/1: 脚部のネジを締め直した。次回は全体の清掃が必要。」

#### rental.last_maintenance_date

最後にメンテナンスを行った日付を記録するメタフィールドです。

- **タイプ**: 日付
- **形式**: YYYY-MM-DD

### 2. システムで管理する項目

#### rental.basic_info (JSON)

レンタル商品の基本情報を保存するJSONフィールドです。

```json
{
  "productCode": "CHAIR-001",
  "productNameKana": "デザイナーズチェア",
  "kana": "デザイナーズチェア"
}
```

**注意**: `location`と`status`は独立したメタフィールドとして管理するため、basic_infoからは除外しています。

#### rental.pricing (JSON)

レンタル料金の設定を保存するJSONフィールドです。

```json
{
  "basePrice": 5000,
  "depositRate": 0.1,
  "discountRules": {
    "day2_6_rate": 0.2,
    "day7_plus_rate": 0.1
  },
  "minimumDays": 1,
  "maximumDays": 30
}
```

## バリエーション管理

商品のバリエーションは、Shopifyの標準的なバリエーション機能ではなく、個別のSKUを持つ独立した商品として管理しています。これにより、各バリエーションを独立した在庫として扱うことができます。

例えば、同じデザインの椅子でも色違いの場合：
- デザイナーズチェア（ブラック）: SKU: CHAIR-A-BLK
- デザイナーズチェア（ホワイト）: SKU: CHAIR-A-WHT

## メタフィールドの使用方法

### 商品登録時

新しい商品を登録する際は、以下の手順でメタフィールドを設定します：

1. Shopify管理画面で商品を作成
2. 基本情報（名前、説明、価格、画像など）を入力
3. 「メタフィールド」セクションで必要なメタフィールドを設定
   - 基本情報（JSON）
   - 料金設定（JSON）
   - 寸法情報
   - 購入情報
   - メンテナンス情報（必要に応じて）

### プログラムからのアクセス

アプリケーションからメタフィールドにアクセスする方法：

```typescript
// Shopify Admin APIを使用してメタフィールドを取得
const getProductMetafields = async (productId: string) => {
  const client = new Shopify.Clients.Rest(shop, accessToken);

  const response = await client.get({
    path: `products/${productId}/metafields`,
  });

  return response.body.metafields;
};

// メタフィールドを更新
const updateProductMetafield = async (
  productId: string,
  metafieldId: string,
  value: string
) => {
  const client = new Shopify.Clients.Rest(shop, accessToken);

  await client.put({
    path: `products/${productId}/metafields/${metafieldId}`,
    data: {
      metafield: {
        id: metafieldId,
        value: value,
      },
    },
  });
};
```

## 今後の拡張可能性

現在のメタフィールド構造は、基本的なレンタル商品管理に必要な情報を網羅しています。将来的には以下の拡張が考えられますが、現時点では優先度は低いと判断しています：

1. **素材情報**: 現在はタグ管理で対応可能
2. **色情報**: 現在はタグ管理で対応可能
3. **デザイナー情報**: 現在はタグ管理で対応可能
4. **バリエーショングループ**: 現在はSKUで管理可能

## まとめ

このドキュメントでは、レンタル商品ECシステムで使用しているShopifyメタフィールドの構造と使用方法について説明しました。現在のメタフィールド構造は、基本的なレンタル商品管理に必要な情報を網羅しており、今後の拡張も可能な設計となっています。

メタフィールドは、Shopifyの標準機能では管理できないレンタル固有の情報を保存するために重要な役割を果たしています。適切なメタフィールド設計により、レンタル商品の管理が効率化されます。
