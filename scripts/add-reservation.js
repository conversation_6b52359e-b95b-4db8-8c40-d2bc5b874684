import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品検索のクエリ
const GET_PRODUCT_BY_TITLE = gql`
  query getProductByTitle($query: String!) {
    products(first: 1, query: $query) {
      edges {
        node {
          id
          title
          metafields(first: 10) {
            edges {
              node {
                id
                namespace
                key
                value
                type
              }
            }
          }
          variants(first: 50) {
            edges {
              node {
                id
                sku
                title
              }
            }
          }
        }
      }
    }
  }
`;

// メタフィールドの設定
async function setJsonMetafield(productId, namespace, key, jsonValue) {
  const mutation = gql`
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    metafields: [
      {
        ownerId: productId,
        namespace,
        key,
        value: JSON.stringify(jsonValue),
        type: 'json',
      }
    ]
  };

  try {
    const result = await graphQLClient.request(mutation, variables);
    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', result.metafieldsSet.userErrors);
      return false;
    }
    return true;
  } catch (error) {
    console.error(`メタフィールド ${namespace}.${key} 設定エラー:`, error);
    return false;
  }
}

// タイトルで商品を検索（完全一致）
async function getProductByTitle(title) {
  try {
    const result = await graphQLClient.request(GET_PRODUCT_BY_TITLE, {
      query: `title:"${title}"`,
    });

    if (result.products.edges.length === 0) {
      console.error(`タイトル "${title}" の商品が見つかりませんでした`);
      return null;
    }

    // 完全一致を確認
    const matchedProduct = result.products.edges.find(edge => edge.node.title === title);
    if (!matchedProduct) {
      console.error(`タイトル "${title}" の商品が完全一致しませんでした`);
      return null;
    }

    return matchedProduct.node;
  } catch (error) {
    console.error('商品検索エラー:', error);
    return null;
  }
}

// 特定の日付の予約状況を確認
function checkDateAvailability(reservations, date) {
  const checkDate = new Date(date);

  for (const reservation of reservations) {
    const startDate = new Date(reservation.startDate);
    const endDate = new Date(reservation.endDate);

    if (checkDate >= startDate && checkDate <= endDate) {
      return {
        available: false,
        reservation: reservation
      };
    }
  }

  return {
    available: true,
    reservation: null
  };
}

// 特定の期間の予約状況を確認
function checkPeriodAvailability(reservations, startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);

  // 期間内の各日付をチェック
  for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
    const dateStr = date.toISOString().split('T')[0];
    const availability = checkDateAvailability(reservations, dateStr);

    if (!availability.available) {
      return {
        available: false,
        conflictingReservation: availability.reservation
      };
    }
  }

  return {
    available: true,
    conflictingReservation: null
  };
}

// 予約を追加
async function addReservation(productTitle, sku, startDate, endDate, customerName, customerEmail, notes) {
  console.log(`\n=== ${productTitle} (${sku}) に予約を追加 ===`);
  console.log(`期間: ${startDate} 〜 ${endDate}`);
  console.log(`顧客: ${customerName} (${customerEmail})`);

  // 商品を検索
  const product = await getProductByTitle(productTitle);
  if (!product) {
    console.error(`${productTitle} の商品が見つかりませんでした。`);
    return false;
  }

  console.log(`商品を見つけました: ${product.title} (${product.id})`);

  // メタフィールドから予約情報を取得
  const metafields = product.metafields.edges.map(edge => edge.node);

  // 予約情報を取得
  const reservationMetafield = metafields.find(
    meta => meta.namespace === 'rental' && meta.key === 'reservation_info'
  );

  if (!reservationMetafield) {
    console.error('予約情報が見つかりませんでした。');
    return false;
  }

  // 在庫情報を取得
  const inventoryMetafield = metafields.find(
    meta => meta.namespace === 'rental' && meta.key === 'inventory_info'
  );

  if (!inventoryMetafield) {
    console.error('在庫情報が見つかりませんでした。');
    return false;
  }

  // 予約情報をパース
  const reservationInfo = JSON.parse(reservationMetafield.value);

  // 在庫情報をパース
  const inventoryInfo = JSON.parse(inventoryMetafield.value);

  // 該当するSKUの予約情報を取得
  const skuItem = reservationInfo.find(item => item.sku === sku);
  if (!skuItem) {
    console.error(`SKU ${sku} の予約情報が見つかりませんでした。`);
    return false;
  }

  // 予約可能かチェック
  const availability = checkPeriodAvailability(skuItem.reservations, startDate, endDate);
  if (!availability.available) {
    console.error('指定された期間は予約できません。');
    console.error(`競合する予約: ${availability.conflictingReservation.id} (${availability.conflictingReservation.startDate} 〜 ${availability.conflictingReservation.endDate})`);
    return false;
  }

  // 新しい予約を作成
  const reservationId = `reservation-${Math.floor(Math.random() * 100000)}`;
  const newReservation = {
    id: reservationId,
    startDate,
    endDate,
    status: 'pending',
    customerName,
    customerEmail,
    orderId: `gid://shopify/Order/${Math.floor(Math.random() * 10000)}`,
    orderLineItemId: `gid://shopify/LineItem/${Math.floor(Math.random() * 10000)}`,
    notes: notes || '特になし'
  };

  // 予約を追加
  skuItem.reservations.push(newReservation);

  // 予約情報を更新
  const reservationResult = await setJsonMetafield(
    product.id,
    'rental',
    'reservation_info',
    reservationInfo
  );

  if (!reservationResult) {
    console.error('予約情報の更新に失敗しました。');
    return false;
  }

  // 在庫情報を更新
  const skuInventory = inventoryInfo[sku] || {
    available: 1,
    onHand: 1,
    reserved: 0,
    physicalCount: 1,
    lastUpdated: new Date().toISOString()
  };

  skuInventory.reserved = skuItem.reservations.length;
  skuInventory.available = skuInventory.onHand - skuInventory.reserved;
  skuInventory.lastUpdated = new Date().toISOString();

  inventoryInfo[sku] = skuInventory;

  const inventoryResult = await setJsonMetafield(
    product.id,
    'rental',
    'inventory_info',
    inventoryInfo
  );

  if (!inventoryResult) {
    console.error('在庫情報の更新に失敗しました。');
    return false;
  }

  console.log('予約を追加しました。');
  console.log(`予約ID: ${newReservation.id}`);

  return true;
}

// メイン処理
async function main() {
  // プレーンソファの2台目に予約を追加
  const success = await addReservation(
    'プレーンソファ 1シーター',
    '10101064-002',
    '2025-06-01',
    '2025-06-05',
    '中村五郎',
    '<EMAIL>',
    '配送希望、午前中に配達'
  );

  if (success) {
    console.log('\n予約が正常に追加されました。');
  } else {
    console.error('\n予約の追加に失敗しました。');
  }
}

// 実行
main();
