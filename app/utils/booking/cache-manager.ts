/**
 * キャッシュマネージャー
 * 
 * このモジュールは、予約情報のキャッシュを管理するための関数を提供します。
 */

import { Booking } from '@prisma/client';

// キャッシュの有効期限（ミリ秒）
const CACHE_TTL = 5 * 60 * 1000; // 5分

// キャッシュストア
interface CacheItem<T> {
  data: T;
  timestamp: number;
}

interface CacheStore {
  [key: string]: CacheItem<any>;
}

const cache: CacheStore = {};

/**
 * キャッシュキーを生成する関数
 * @param prefix キープレフィックス
 * @param params キーパラメータ
 * @returns キャッシュキー
 */
export function generateCacheKey(prefix: string, params: Record<string, any>): string {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}=${JSON.stringify(params[key])}`)
    .join('&');
  
  return `${prefix}:${sortedParams}`;
}

/**
 * キャッシュからデータを取得する関数
 * @param key キャッシュキー
 * @returns キャッシュデータ（存在しない場合はnull）
 */
export function getFromCache<T>(key: string): T | null {
  const cacheItem = cache[key];
  
  if (!cacheItem) {
    return null;
  }
  
  // キャッシュの有効期限をチェック
  const now = Date.now();
  if (now - cacheItem.timestamp > CACHE_TTL) {
    // キャッシュが期限切れの場合は削除
    delete cache[key];
    return null;
  }
  
  return cacheItem.data;
}

/**
 * データをキャッシュに保存する関数
 * @param key キャッシュキー
 * @param data キャッシュデータ
 */
export function saveToCache<T>(key: string, data: T): void {
  cache[key] = {
    data,
    timestamp: Date.now()
  };
}

/**
 * キャッシュを削除する関数
 * @param key キャッシュキー
 */
export function removeFromCache(key: string): void {
  delete cache[key];
}

/**
 * キャッシュをクリアする関数
 * @param prefix キープレフィックス（指定した場合、そのプレフィックスのキャッシュのみをクリア）
 */
export function clearCache(prefix?: string): void {
  if (prefix) {
    // 指定したプレフィックスのキャッシュのみをクリア
    Object.keys(cache).forEach(key => {
      if (key.startsWith(`${prefix}:`)) {
        delete cache[key];
      }
    });
  } else {
    // 全てのキャッシュをクリア
    Object.keys(cache).forEach(key => {
      delete cache[key];
    });
  }
}

/**
 * 予約情報のキャッシュキーを生成する関数
 * @param productId 商品ID
 * @param includeStatuses 含めるステータス
 * @returns キャッシュキー
 */
export function generateBookingsCacheKey(productId: string, includeStatuses: string[] = ['PROVISIONAL', 'CONFIRMED']): string {
  return generateCacheKey('bookings', { productId, includeStatuses });
}

/**
 * 予約情報をキャッシュから取得する関数
 * @param productId 商品ID
 * @param includeStatuses 含めるステータス
 * @returns 予約情報（存在しない場合はnull）
 */
export function getBookingsFromCache(productId: string, includeStatuses: string[] = ['PROVISIONAL', 'CONFIRMED']): Booking[] | null {
  const cacheKey = generateBookingsCacheKey(productId, includeStatuses);
  return getFromCache<Booking[]>(cacheKey);
}

/**
 * 予約情報をキャッシュに保存する関数
 * @param productId 商品ID
 * @param bookings 予約情報
 * @param includeStatuses 含めるステータス
 */
export function saveBookingsToCache(productId: string, bookings: Booking[], includeStatuses: string[] = ['PROVISIONAL', 'CONFIRMED']): void {
  const cacheKey = generateBookingsCacheKey(productId, includeStatuses);
  saveToCache<Booking[]>(cacheKey, bookings);
}

/**
 * 予約情報のキャッシュを削除する関数
 * @param productId 商品ID
 */
export function invalidateBookingsCache(productId: string): void {
  clearCache(`bookings:${productId}`);
}
