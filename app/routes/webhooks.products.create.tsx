import { json, type ActionFunctionArgs } from "@remix-run/node";
import { prisma } from "../db.server";
import { authenticate } from "../shopify.server";
import { VariantAutoCreatorService } from "../services/shopify/variant-auto-creator.service";

/**
 * 商品作成Webhookハンドラー
 *
 * Shopifyから商品作成のwebhookを受け取り、必要な処理を行います。
 * - 新商品登録時に初期在庫カレンダーを作成
 */
export async function action({ request }: ActionFunctionArgs) {
  try {
    console.log("========== 商品作成Webhook受信開始 ==========");

    // Shopify認証（payloadを含む）
    const { admin, session, payload } = await authenticate.webhook(request);

    console.log("Webhookペイロード:", JSON.stringify(payload, null, 2));
    console.log("セッション取得成功:", session.shop);

    // 商品IDの取得
    const productId = payload.id;
    const shop = session.shop;
    console.log("商品作成webhook受信:", productId);

    // 初期在庫カレンダーを作成（90日分）
    const today = new Date();
    const dates = Array.from({length: 90}, (_, i) => {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      return date;
    });

    // 商品IDを取得
    try {
      // 商品をデータベースに保存
      const dbProduct = await prisma.product.upsert({
        where: {
          shop_shopifyId: {
            shop: shop,
            shopifyId: productId
          }
        },
        update: {
          title: payload.title || "不明な商品",
          sku: payload.variants && payload.variants.length > 0 ? payload.variants[0].sku : '',
          price: payload.variants && payload.variants.length > 0 ? parseFloat(payload.variants[0].price) : 0,
          updatedAt: new Date()
        },
        create: {
          shopifyId: productId,
          title: payload.title || "不明な商品",
          sku: payload.variants && payload.variants.length > 0 ? payload.variants[0].sku : '',
          price: payload.variants && payload.variants.length > 0 ? parseFloat(payload.variants[0].price) : 0,
          status: 'AVAILABLE',
          shop: shop,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      console.log(`商品をデータベースに保存しました: ${dbProduct.id}`);

      // バリエーション自動作成
      try {
        console.log("バリエーション自動作成を開始...");

        const variantCreator = VariantAutoCreatorService.getInstance();
        const basePrice = parseFloat(payload.variants?.[0]?.price || '0');

        if (basePrice > 0) {
          const variantResult = await variantCreator.createMissingVariants(admin, productId, {
            basePrice,
            createDays: [1, 2, 3, 4, 5, 6, 7], // 1-7日のバリエーション
            createProvisionalVariants: false, // 仮予約バリエーションは作成しない
            productStatus: 'available', // デフォルトでavailable
            location: 'NY', // デフォルト在庫場所
            sku: payload.variants?.[0]?.sku || payload.handle || ''
          });

          console.log(`バリエーション作成結果: 成功=${variantResult.createdVariants.length}, 失敗=${variantResult.errors.length}`);

          if (variantResult.errors.length > 0) {
            console.error("バリエーション作成エラー:", variantResult.errors);
          }
        } else {
          console.warn("基本価格が0のため、バリエーション作成をスキップしました");
        }
      } catch (variantError) {
        console.error("バリエーション自動作成エラー:", variantError);
        // バリエーション作成エラーでもWebhook処理は継続
      }

      // バッチ処理で在庫カレンダーを作成
      const batchSize = 10;
      for (let i = 0; i < dates.length; i += batchSize) {
        const batch = dates.slice(i, i + batchSize);

        await Promise.all(batch.map(date =>
          prisma.inventoryCalendar.create({
            data: {
              shop,
              productId: dbProduct.id,
              shopifyProductId: productId,
              date,
              isAvailable: true
            }
          })
        ));

      // API制限を回避するための待機
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log(`商品ID ${productId} の初期在庫カレンダーを作成しました（90日分）`);

    return json({ success: true });
    } catch (innerError) {
      console.error("商品データベース保存エラー:", innerError);
      return json({ error: innerError.message }, { status: 500 });
    }
  } catch (error) {
    console.error("商品作成webhookエラー:", error);
    return json({ error: error.message }, { status: 500 });
  }
}
