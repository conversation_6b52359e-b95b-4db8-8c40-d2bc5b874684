/**
 * カート連携テストスクリプト
 *
 * このスクリプトは、カート連携機能をテストします。
 * 実行方法: npx tsx scripts/test-cart-integration.ts [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';
import { addDays, format } from 'date-fns';
import { ja } from 'date-fns/locale';
import { CartService } from '../app/services/cart.service';
import { BookingService } from '../app/services/booking.service';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト設定
const config = {
  // テストする商品のShopify ID
  productId: process.argv[2] || '123456789', // コマンドライン引数から取得、または既定値を使用

  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',

  // テスト用の顧客情報
  customer: {
    email: '<EMAIL>',
    name: 'テストユーザー',
    phone: '090-1234-5678',
    address: '東京都渋谷区'
  },

  // 予約日数（今日から何日後に予約するか）
  startDaysFromNow: 7,
  durationDays: 3
};

/**
 * 商品情報を取得する関数
 */
async function getProductInfo(shopifyProductId: string) {
  try {
    // Prismaから商品情報を取得
    const product = await prisma.product.findFirst({
      where: { shopifyId: shopifyProductId }
    });

    if (!product) {
      console.error(`商品ID ${shopifyProductId} が見つかりません`);
      return null;
    }

    return product;
  } catch (error) {
    console.error('商品情報取得エラー:', error);
    return null;
  }
}

/**
 * 予約情報を表示する関数
 */
async function displayBookingInfo(bookingId: string) {
  try {
    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        product: true
      }
    });

    if (!booking) {
      console.error(`予約ID ${bookingId} が見つかりません`);
      return;
    }

    console.log('\n===== 予約情報 =====');
    console.log(`予約ID: ${booking.id}`);
    console.log(`予約番号: ${booking.bookingId}`);
    console.log(`商品名: ${booking.product.title}`);
    console.log(`SKU: ${booking.product.sku}`);
    console.log(`期間: ${format(new Date(booking.startDate), 'yyyy/MM/dd')} 〜 ${format(new Date(booking.endDate), 'yyyy/MM/dd')}`);
    console.log(`ステータス: ${booking.status}`);
    console.log(`予約タイプ: ${booking.bookingType}`);
    console.log(`支払い状態: ${booking.paymentStatus}`);
    console.log(`顧客名: ${booking.customerName}`);
    console.log(`顧客メール: ${booking.customerEmail}`);
    console.log(`金額: ${booking.totalAmount}円`);
    console.log(`注文ID: ${booking.orderId || '未設定'}`);
    console.log(`注文名: ${booking.orderName || '未設定'}`);
    console.log(`作成日時: ${format(new Date(booking.createdAt), 'yyyy/MM/dd HH:mm:ss')}`);
    console.log(`更新日時: ${format(new Date(booking.updatedAt), 'yyyy/MM/dd HH:mm:ss')}`);
  } catch (error) {
    console.error('予約情報表示エラー:', error);
  }
}

/**
 * カートに商品を追加する関数
 */
async function addToCart(productId: string, startDate: Date, endDate: Date) {
  try {
    console.log(`\nカートに商品を追加中... 商品ID: ${productId}`);
    console.log(`期間: ${format(startDate, 'yyyy/MM/dd')} 〜 ${format(endDate, 'yyyy/MM/dd')}`);

    // 商品情報を取得
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!product) {
      throw new Error(`商品 ID ${productId} が見つかりません`);
    }

    // カートサービスのモックリクエストを作成
    const mockRequest = {
      headers: new Headers({
        'host': config.shop
      })
    } as Request;

    // カートサービスを初期化
    const cartService = new CartService(mockRequest);

    // カートに商品を追加
    const cartData = {
      productId: product.id,
      shopifyProductId: product.shopifyId,
      variantId: '1', // テスト用の固定値
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      customerEmail: config.customer.email,
      customerName: config.customer.name,
      customerPhone: config.customer.phone,
      customerAddress: config.customer.address,
      bookingType: 'PROVISIONAL',
      notes: 'テスト予約'
    };

    const result = await cartService.addToCart(cartData);

    console.log('カートに商品を追加しました:');
    console.log(`予約ID: ${result.bookingId}`);
    console.log(`カートURL: ${result.cartUrl}`);

    return result;
  } catch (error) {
    console.error('カート追加エラー:', error);
    return null;
  }
}

/**
 * 予約ステータスを更新する関数
 */
async function updateBookingStatus(bookingId: string, status: string) {
  try {
    console.log(`\n予約ステータスを更新中... 予約ID: ${bookingId}, 新ステータス: ${status}`);

    // 予約サービスを初期化
    const bookingService = new BookingService();

    // 予約ステータスを更新
    const result = await bookingService.updateBookingStatus(bookingId, status);

    console.log('予約ステータスを更新しました');

    return result;
  } catch (error) {
    console.error('予約ステータス更新エラー:', error);
    return null;
  }
}

/**
 * 注文作成をシミュレートする関数
 */
async function simulateOrderCreation(bookingId: string) {
  try {
    console.log(`\n注文作成をシミュレート中... 予約ID: ${bookingId}`);

    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId }
    });

    if (!booking) {
      throw new Error(`予約ID ${bookingId} が見つかりません`);
    }

    // 注文IDを生成
    const orderId = `${Date.now()}`;
    const orderName = `#${Math.floor(Math.random() * 10000)}`;

    // 予約情報を更新
    const updatedBooking = await prisma.booking.update({
      where: { id: bookingId },
      data: {
        orderId,
        orderName,
        status: 'CONFIRMED',
        bookingType: 'CONFIRMED',
        paymentStatus: 'PENDING',
        updatedAt: new Date()
      }
    });

    console.log('注文を作成しました:');
    console.log(`注文ID: ${orderId}`);
    console.log(`注文名: ${orderName}`);

    return updatedBooking;
  } catch (error) {
    console.error('注文作成シミュレーションエラー:', error);
    return null;
  }
}

/**
 * 支払い完了をシミュレートする関数
 */
async function simulatePaymentCompletion(bookingId: string) {
  try {
    console.log(`\n支払い完了をシミュレート中... 予約ID: ${bookingId}`);

    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId }
    });

    if (!booking) {
      throw new Error(`予約ID ${bookingId} が見つかりません`);
    }

    // BookingServiceを使用して支払い状態を更新
    const bookingService = new BookingService();
    const result = await bookingService.updatePaymentStatus(bookingId, 'COMPLETED', {
      paymentMethod: 'CREDIT_CARD',
      paymentDate: new Date()
    });

    console.log('支払いが完了しました');

    return result.booking;
  } catch (error) {
    console.error('支払い完了シミュレーションエラー:', error);
    return null;
  }
}

/**
 * 予約キャンセルをシミュレートする関数
 */
async function simulateBookingCancellation(bookingId: string) {
  try {
    console.log(`\n予約キャンセルをシミュレート中... 予約ID: ${bookingId}`);

    // 予約サービスを初期化
    const bookingService = new BookingService();

    // 予約をキャンセル
    const result = await bookingService.cancelBooking(bookingId);

    console.log('予約をキャンセルしました');

    return result;
  } catch (error) {
    console.error('予約キャンセルシミュレーションエラー:', error);
    return null;
  }
}

/**
 * カート連携をテストする関数
 */
async function testCartIntegration(shopifyProductId: string) {
  try {
    console.log(`商品ID ${shopifyProductId} のカート連携テストを実行します...`);

    // 商品情報を取得
    const product = await getProductInfo(shopifyProductId);

    if (!product) {
      return false;
    }

    // テスト用の日程を設定
    const today = new Date();
    const startDate = addDays(today, config.startDaysFromNow);
    const endDate = addDays(startDate, config.durationDays - 1);

    // テスト1: カートに商品を追加
    console.log('\n----- テスト1: カートに商品を追加 -----');
    const cartResult = await addToCart(product.id, startDate, endDate);

    if (!cartResult || !cartResult.bookingId) {
      console.error('カートへの商品追加に失敗しました');
      return false;
    }

    // 予約情報を表示
    await displayBookingInfo(cartResult.bookingId);

    // テスト2: 注文作成をシミュレート
    console.log('\n----- テスト2: 注文作成をシミュレート -----');
    const orderResult = await simulateOrderCreation(cartResult.bookingId);

    if (!orderResult) {
      console.error('注文作成シミュレーションに失敗しました');
      return false;
    }

    // 更新された予約情報を表示
    await displayBookingInfo(cartResult.bookingId);

    // テスト3: 支払い完了をシミュレート
    console.log('\n----- テスト3: 支払い完了をシミュレート -----');
    const paymentResult = await simulatePaymentCompletion(cartResult.bookingId);

    if (!paymentResult) {
      console.error('支払い完了シミュレーションに失敗しました');
      return false;
    }

    // 更新された予約情報を表示
    await displayBookingInfo(cartResult.bookingId);

    // テスト4: 予約キャンセルをシミュレート
    console.log('\n----- テスト4: 予約キャンセルをシミュレート -----');
    const cancellationResult = await simulateBookingCancellation(cartResult.bookingId);

    if (!cancellationResult) {
      console.error('予約キャンセルシミュレーションに失敗しました');
      return false;
    }

    // 更新された予約情報を表示
    await displayBookingInfo(cartResult.bookingId);

    return true;
  } catch (error) {
    console.error('カート連携テストエラー:', error);
    return false;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('カート連携テストを開始します...');

    // コマンドライン引数から商品IDを取得
    const shopifyProductId = config.productId;

    // カート連携をテスト
    const testResult = await testCartIntegration(shopifyProductId);

    // テスト結果のサマリー
    console.log('\n===== テスト結果サマリー =====');
    console.log(`カート連携テスト: ${testResult ? '成功' : '失敗'}`);

    if (testResult) {
      console.log('\nテストが成功しました！');
    } else {
      console.error('\nテストが失敗しました。');
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
