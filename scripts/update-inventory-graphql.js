/**
 * 商品の在庫を更新するためのGraphQLミューテーション
 * 
 * このスクリプトは、Shopify GraphQL Appで実行するためのクエリとミューテーションを提供します。
 * 商品IDを元に、バリアントとInventoryItemIDを取得し、在庫を更新します。
 */

// 1. 最初に商品のバリアントとInventoryItemIDを取得するクエリ
const getProductVariantsQuery = `
query getProductVariants($productId: ID!) {
  product(id: $productId) {
    id
    title
    variants(first: 10) {
      edges {
        node {
          id
          title
          inventoryQuantity
          inventoryItem {
            id
          }
        }
      }
    }
  }
}
`;

// 商品ID変数（例：花器　シルバー　穴開きボトル型）
const productVariablesExample = {
  "productId": "gid://shopify/Product/8977061544104"
};

// 2. 在庫を更新するミューテーション
const updateInventoryMutation = `
mutation inventoryAdjustQuantities($input: InventoryAdjustQuantitiesInput!) {
  inventoryAdjustQuantities(input: $input) {
    inventoryAdjustmentGroup {
      id
      createdAt
    }
    userErrors {
      field
      message
    }
  }
}
`;

// 在庫を1に設定するための変数テンプレート
// 実際のInventoryItemIDは、getProductVariantsQueryの結果から取得して置き換えてください
const updateInventoryVariablesTemplate = {
  "input": {
    "reason": "在庫調整",
    "name": "テスト用在庫設定",
    "changes": [
      // ここにInventoryItemIDと在庫数の変更を追加
    ]
  }
};

// 3. 複数の商品の在庫を一度に取得するクエリ
const getBulkProductsQuery = `
query getBulkProducts($ids: [ID!]!) {
  nodes(ids: $ids) {
    ... on Product {
      id
      title
      variants(first: 10) {
        edges {
          node {
            id
            title
            inventoryQuantity
            inventoryItem {
              id
            }
          }
        }
      }
    }
  }
}
`;

// 複数の商品ID変数
const bulkProductVariables = {
  "ids": [
    "gid://shopify/Product/8977061544104", // 花器　シルバー　穴開きボトル型
    "gid://shopify/Product/8977061642408", // 花器　逆Ｙ字　シルバー
    "gid://shopify/Product/8977061740712", // オブジェ　コネクトサークル　ゴールド×クリスタル
    "gid://shopify/Product/8977061839016", // オブジェ　トリプルリング　ゴールド
    "gid://shopify/Product/8977061937320", // オブジェ　タイドロープ　ゴールド
    "gid://shopify/Product/8977062035624", // U字ボウル　ゴールド
    "gid://shopify/Product/8977062133928", // 舟形ボウル　ゴールド　内側ホワイト
    "gid://shopify/Product/8977062232232", // ペンスタンド　イエローリボン
    "gid://shopify/Product/8977062363304", // コンパクトミラー　ゴールド×ブルー
    "gid://shopify/Product/8977062494376"  // コスメトレー　コンポート型　ホワイトレース
  ]
};

// 4. 商品のメタフィールドを取得するクエリ
const getProductMetafieldsQuery = `
query getProductMetafields($productId: ID!) {
  product(id: $productId) {
    id
    title
    metafields(first: 20) {
      edges {
        node {
          id
          namespace
          key
          value
        }
      }
    }
  }
}
`;

// 商品のメタフィールドを取得するための変数
const productMetafieldsVariables = {
  "productId": "gid://shopify/Product/8977061544104" // 花器　シルバー　穴開きボトル型
};

// 5. 商品のメタフィールドを更新するミューテーション
const updateMetafieldsMutation = `
mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
  metafieldsSet(metafields: $metafields) {
    metafields {
      id
      namespace
      key
      value
    }
    userErrors {
      field
      message
    }
  }
}
`;

// 商品のメタフィールドを更新するための変数
const updateMetafieldsVariables = {
  "metafields": [
    {
      "ownerId": "gid://shopify/Product/8977061544104", // 花器　シルバー　穴開きボトル型
      "namespace": "rental",
      "key": "status",
      "value": "available",
      "type": "single_line_text_field"
    }
  ]
};

// 6. 8日以上のレンタル料金計算のテスト関数
function calculateRentalPrice(basePrice, days) {
  let totalPrice = basePrice; // 1日目は基本料金
  
  if (days > 1) {
    const day2to6Count = Math.min(days - 1, 5); // 最大5日間（2日目〜6日目）
    totalPrice += basePrice * 0.2 * day2to6Count;
  }
  
  if (days > 6) {
    const day7PlusCount = days - 6;
    totalPrice += basePrice * 0.1 * day7PlusCount;
  }
  
  return Math.round(totalPrice);
}

// 料金計算のテスト例
const testPriceCalculation = () => {
  const basePrice = 10000;
  
  console.log(`基本料金: ${basePrice}円`);
  console.log(`1日間レンタル: ${calculateRentalPrice(basePrice, 1)}円`); // 10000円
  console.log(`2日間レンタル: ${calculateRentalPrice(basePrice, 2)}円`); // 12000円
  console.log(`3日間レンタル: ${calculateRentalPrice(basePrice, 3)}円`); // 14000円
  console.log(`7日間レンタル: ${calculateRentalPrice(basePrice, 7)}円`); // 21000円
  console.log(`8日間レンタル: ${calculateRentalPrice(basePrice, 8)}円`); // 22000円
  console.log(`9日間レンタル: ${calculateRentalPrice(basePrice, 9)}円`); // 23000円
  console.log(`10日間レンタル: ${calculateRentalPrice(basePrice, 10)}円`); // 24000円
};

// 使用方法
// 1. Shopify管理画面 > Apps > GraphiQL App を開く
// 2. まず、getBulkProductsQueryを実行して、すべての商品のバリアントとInventoryItemIDを取得
// 3. 取得したInventoryItemIDを使用して、updateInventoryMutationの変数を作成
// 4. updateInventoryMutationを実行して、在庫を更新
// 5. 必要に応じて、getProductMetafieldsQueryを実行して、商品のメタフィールドを確認
// 6. 必要に応じて、updateMetafieldsMutationを実行して、商品のメタフィールドを更新
// 7. JavaScriptコンソールでtestPriceCalculation()を実行して、料金計算をテスト

// 在庫更新の例（getBulkProductsQueryの結果から作成）
// 以下は、すべての商品の最初のバリアント（1日レンタル）の在庫を1に設定する例です
const updateAllProductsInventoryExample = {
  "input": {
    "reason": "在庫調整",
    "name": "テスト用在庫設定",
    "changes": [
      // 花器　シルバー　穴開きボトル型（1日レンタル）
      {
        "inventoryItemId": "gid://shopify/InventoryItem/47887775465640",
        "availableDelta": 1,
        "locationId": "gid://shopify/Location/1"
      },
      // 花器　逆Ｙ字　シルバー（1日レンタル）
      {
        "inventoryItemId": "gid://shopify/InventoryItem/47887775760552",
        "availableDelta": 1,
        "locationId": "gid://shopify/Location/1"
      },
      // オブジェ　コネクトサークル　ゴールド×クリスタル（1日レンタル）
      {
        "inventoryItemId": "gid://shopify/InventoryItem/47887776055464",
        "availableDelta": 1,
        "locationId": "gid://shopify/Location/1"
      }
      // 他の商品も同様に追加
    ]
  }
};
