/**
 * 注文ステータス同期システムのテストスクリプト
 * 
 * このスクリプトは、Shopify注文ステータス同期システムの動作をテストします。
 * 
 * 使用方法:
 * npx tsx scripts/test-order-status-sync.ts
 */

import { PrismaClient } from '@prisma/client';
import { OrderStatusSyncService } from '../app/services/order-status-sync.service';
import { InventoryRestoreService } from '../app/services/inventory-restore.service';
import { BookingService } from '../app/services/booking.service';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

const prisma = new PrismaClient();

interface TestResult {
  testName: string;
  success: boolean;
  message: string;
  details?: any;
  error?: string;
}

/**
 * メインのテスト実行関数
 */
async function main() {
  console.log('========================================');
  console.log('注文ステータス同期システムテスト開始');
  console.log('========================================\n');

  const results: TestResult[] = [];
  const shop = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';

  try {
    // 1. サービスの初期化テスト
    results.push(await testServiceInitialization());

    // 2. 予約作成テスト
    const testBooking = await testCreateTestBooking(shop);
    if (testBooking.success) {
      results.push(testBooking);

      // 3. 予約ステータス更新テスト
      results.push(await testBookingStatusUpdate(testBooking.details.bookingId));

      // 4. 在庫復元テスト
      results.push(await testInventoryRestore(testBooking.details.bookingId));

      // 5. 同期状況確認テスト
      results.push(await testSyncStatusCheck(shop));

      // 6. テストデータクリーンアップ
      results.push(await testCleanupTestData(testBooking.details.bookingId));
    } else {
      results.push(testBooking);
    }

    // 7. エラーハンドリングテスト
    results.push(await testErrorHandling());

  } catch (error) {
    console.error('テスト実行中に予期しないエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }

  // テスト結果の表示
  displayTestResults(results);
}

/**
 * サービスの初期化テスト
 */
async function testServiceInitialization(): Promise<TestResult> {
  try {
    const orderStatusSyncService = new OrderStatusSyncService();
    const inventoryRestoreService = new InventoryRestoreService();
    const bookingService = new BookingService();

    return {
      testName: 'サービス初期化テスト',
      success: true,
      message: 'すべてのサービスが正常に初期化されました'
    };
  } catch (error) {
    return {
      testName: 'サービス初期化テスト',
      success: false,
      message: 'サービス初期化に失敗しました',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * テスト用予約作成
 */
async function testCreateTestBooking(shop: string): Promise<TestResult> {
  try {
    // テスト用商品を検索
    const testProduct = await prisma.product.findFirst({
      where: { shop },
      select: { id: true, title: true, sku: true }
    });

    if (!testProduct) {
      return {
        testName: 'テスト用予約作成',
        success: false,
        message: 'テスト用商品が見つかりません'
      };
    }

    // テスト用予約を作成
    const startDate = new Date();
    startDate.setDate(startDate.getDate() + 1); // 明日から
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 2); // 3日間

    const booking = await prisma.booking.create({
      data: {
        productId: testProduct.id,
        startDate,
        endDate,
        customerEmail: '<EMAIL>',
        customerName: 'テストユーザー',
        totalAmount: 5000,
        depositAmount: 500,
        bookingId: `TEST-${Date.now()}`,
        shop,
        bookingType: 'CONFIRMED',
        status: 'DRAFT',
        paymentStatus: 'PENDING',
        notes: '注文ステータス同期テスト用予約',
        shopifyOrderId: `TEST-ORDER-${Date.now()}`,
        shopifyOrderName: `#TEST-${Date.now()}`
      }
    });

    return {
      testName: 'テスト用予約作成',
      success: true,
      message: 'テスト用予約が正常に作成されました',
      details: {
        bookingId: booking.id,
        bookingCode: booking.bookingId,
        productTitle: testProduct.title
      }
    };
  } catch (error) {
    return {
      testName: 'テスト用予約作成',
      success: false,
      message: 'テスト用予約の作成に失敗しました',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * 予約ステータス更新テスト
 */
async function testBookingStatusUpdate(bookingId: string): Promise<TestResult> {
  try {
    const bookingService = new BookingService();

    // 予約を確定状態に更新
    const updateResult = await bookingService.updateBookingStatus(bookingId, 'CONFIRMED', {
      notes: 'テスト: 予約確定',
      paymentStatus: 'COMPLETED'
    });

    if (!updateResult.success) {
      return {
        testName: '予約ステータス更新テスト',
        success: false,
        message: '予約ステータス更新に失敗しました',
        error: updateResult.error
      };
    }

    // 更新後の予約を確認
    const updatedBooking = await prisma.booking.findUnique({
      where: { id: bookingId },
      select: { status: true, paymentStatus: true }
    });

    const isStatusCorrect = updatedBooking?.status === 'CONFIRMED';
    const isPaymentStatusCorrect = updatedBooking?.paymentStatus === 'COMPLETED';

    return {
      testName: '予約ステータス更新テスト',
      success: isStatusCorrect && isPaymentStatusCorrect,
      message: isStatusCorrect && isPaymentStatusCorrect 
        ? '予約ステータスが正常に更新されました'
        : 'ステータス更新に問題があります',
      details: {
        expectedStatus: 'CONFIRMED',
        actualStatus: updatedBooking?.status,
        expectedPaymentStatus: 'COMPLETED',
        actualPaymentStatus: updatedBooking?.paymentStatus
      }
    };
  } catch (error) {
    return {
      testName: '予約ステータス更新テスト',
      success: false,
      message: '予約ステータス更新テスト中にエラーが発生しました',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * 在庫復元テスト
 */
async function testInventoryRestore(bookingId: string): Promise<TestResult> {
  try {
    const inventoryRestoreService = new InventoryRestoreService();

    // 予約をキャンセル状態に変更
    await prisma.booking.update({
      where: { id: bookingId },
      data: { 
        status: 'CANCELLED',
        updatedAt: new Date()
      }
    });

    // 在庫復元を実行
    const restoreResult = await inventoryRestoreService.restoreInventoryForBooking(bookingId, {
      reason: 'テスト用キャンセル',
      notes: '在庫復元テスト'
    });

    return {
      testName: '在庫復元テスト',
      success: restoreResult.success,
      message: restoreResult.success 
        ? '在庫復元が正常に実行されました'
        : '在庫復元に失敗しました',
      details: restoreResult
    };
  } catch (error) {
    return {
      testName: '在庫復元テスト',
      success: false,
      message: '在庫復元テスト中にエラーが発生しました',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * 同期状況確認テスト
 */
async function testSyncStatusCheck(shop: string): Promise<TestResult> {
  try {
    const orderStatusSyncService = new OrderStatusSyncService();
    const inventoryRestoreService = new InventoryRestoreService();

    // 同期状況を確認
    const syncStatus = await orderStatusSyncService.checkSyncStatus(shop);
    const inventoryStatus = await inventoryRestoreService.checkInventoryRestoreStatus(shop, 1);

    return {
      testName: '同期状況確認テスト',
      success: true,
      message: '同期状況の確認が正常に実行されました',
      details: {
        syncStatus,
        inventoryStatus
      }
    };
  } catch (error) {
    return {
      testName: '同期状況確認テスト',
      success: false,
      message: '同期状況確認テスト中にエラーが発生しました',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * エラーハンドリングテスト
 */
async function testErrorHandling(): Promise<TestResult> {
  try {
    const inventoryRestoreService = new InventoryRestoreService();

    // 存在しない予約IDで在庫復元を試行
    try {
      await inventoryRestoreService.restoreInventoryForBooking('non-existent-id');
      return {
        testName: 'エラーハンドリングテスト',
        success: false,
        message: 'エラーが期待されましたが、正常に処理されました'
      };
    } catch (error) {
      // エラーが発生することが期待される
      return {
        testName: 'エラーハンドリングテスト',
        success: true,
        message: '期待通りエラーが発生し、適切にハンドリングされました',
        details: {
          errorMessage: error instanceof Error ? error.message : String(error)
        }
      };
    }
  } catch (error) {
    return {
      testName: 'エラーハンドリングテスト',
      success: false,
      message: 'エラーハンドリングテスト中に予期しないエラーが発生しました',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * テストデータクリーンアップ
 */
async function testCleanupTestData(bookingId: string): Promise<TestResult> {
  try {
    // テスト用予約を削除
    await prisma.booking.delete({
      where: { id: bookingId }
    });

    return {
      testName: 'テストデータクリーンアップ',
      success: true,
      message: 'テストデータが正常にクリーンアップされました'
    };
  } catch (error) {
    return {
      testName: 'テストデータクリーンアップ',
      success: false,
      message: 'テストデータのクリーンアップに失敗しました',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * テスト結果の表示
 */
function displayTestResults(results: TestResult[]) {
  console.log('\n========================================');
  console.log('テスト結果');
  console.log('========================================\n');

  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;

  results.forEach((result, index) => {
    const status = result.success ? '✅ 成功' : '❌ 失敗';
    console.log(`${index + 1}. ${result.testName}: ${status}`);
    console.log(`   ${result.message}`);
    
    if (result.error) {
      console.log(`   エラー: ${result.error}`);
    }
    
    if (result.details) {
      console.log(`   詳細: ${JSON.stringify(result.details, null, 2)}`);
    }
    
    console.log('');
  });

  console.log('========================================');
  console.log(`テスト完了: ${successCount}/${totalCount} 成功`);
  console.log('========================================\n');

  if (successCount === totalCount) {
    console.log('🎉 すべてのテストが成功しました！');
  } else {
    console.log('⚠️  一部のテストが失敗しました。詳細を確認してください。');
  }
}

// スクリプトを実行
main()
  .then(() => console.log('\nテストスクリプトの実行が完了しました'))
  .catch((error) => console.error('予期しないエラーが発生しました:', error));
