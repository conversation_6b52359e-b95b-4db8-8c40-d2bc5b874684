/**
 * Shopify注文メタフィールド定義スクリプト
 * 
 * このスクリプトは、レンタル商品ECシステムに必要な注文メタフィールドをShopifyストアに定義します。
 * Admin APIを使用して実行する必要があります。
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 注文メタフィールド定義の一覧
const ORDER_METAFIELDS = [
  {
    namespace: 'rental',
    key: 'start_date',
    name: 'Rental Start Date',
    description: 'レンタル開始日',
    type: 'date',
    validations: [],
    visibleToStorefrontApi: true,
  },
  {
    namespace: 'rental',
    key: 'end_date',
    name: 'Rental End Date',
    description: 'レンタル終了日',
    type: 'date',
    validations: [],
    visibleToStorefrontApi: true,
  },
  {
    namespace: 'rental',
    key: 'reservation_type',
    name: 'Reservation Type',
    description: '予約タイプ（仮予約または本予約）',
    type: 'single_line_text_field',
    validations: [
      {
        name: 'allowed_values',
        value: JSON.stringify(['provisional', 'confirmed']),
      },
    ],
    visibleToStorefrontApi: true,
  },
  {
    namespace: 'rental',
    key: 'reservation_id',
    name: 'Reservation ID',
    description: '関連する予約ID',
    type: 'single_line_text_field',
    validations: [],
    visibleToStorefrontApi: false,
  },
  {
    namespace: 'rental',
    key: 'business_days',
    name: 'Business Days',
    description: '営業日数（料金計算に使用）',
    type: 'number_integer',
    validations: [],
    visibleToStorefrontApi: false,
  },
  {
    namespace: 'rental',
    key: 'deposit_amount',
    name: 'Deposit Amount',
    description: 'デポジット金額',
    type: 'number_decimal',
    validations: [],
    visibleToStorefrontApi: true,
  },
  {
    namespace: 'rental',
    key: 'deposit_paid',
    name: 'Deposit Paid',
    description: 'デポジット支払い状態',
    type: 'boolean',
    validations: [],
    visibleToStorefrontApi: false,
  },
  {
    namespace: 'rental',
    key: 'cancellation_info',
    name: 'Cancellation Information',
    description: 'キャンセル情報（キャンセル日、理由、返金額など）',
    type: 'json',
    validations: [],
    visibleToStorefrontApi: false,
  },
];

// メタフィールド定義作成のGraphQLクエリ
const CREATE_METAFIELD_DEFINITION = gql`
  mutation CreateMetafieldDefinition($definition: MetafieldDefinitionInput!) {
    metafieldDefinitionCreate(definition: $definition) {
      createdDefinition {
        id
        name
        namespace
        key
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// メインの実行関数
async function setupOrderMetafieldDefinitions() {
  console.log('注文メタフィールド定義セットアップを開始します...');

  for (const definition of ORDER_METAFIELDS) {
    try {
      console.log(`メタフィールド定義を作成: ${definition.namespace}.${definition.key}`);
      
      const variables = {
        definition: {
          name: definition.name,
          namespace: definition.namespace,
          key: definition.key,
          description: definition.description,
          type: definition.type,
          ownerType: 'ORDER',
          validations: definition.validations,
          visibleToStorefrontApi: definition.visibleToStorefrontApi,
        },
      };

      const result = await client.request(CREATE_METAFIELD_DEFINITION, variables);
      
      if (result.metafieldDefinitionCreate.userErrors.length > 0) {
        console.error(`エラー: ${JSON.stringify(result.metafieldDefinitionCreate.userErrors, null, 2)}`);
      } else {
        console.log(`成功: ${definition.namespace}.${definition.key} を作成しました`);
      }
    } catch (error) {
      console.error(`メタフィールド定義の作成中にエラーが発生しました: ${error.message}`);
      // 既に存在する場合は続行
      if (error.message.includes('already exists')) {
        console.log(`メタフィールド定義 ${definition.namespace}.${definition.key} は既に存在します`);
      }
    }
    
    // API制限を回避するための短い待機
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log('注文メタフィールド定義セットアップが完了しました');
}

// スクリプト実行
setupOrderMetafieldDefinitions().catch(error => {
  console.error('メタフィールド定義セットアップに失敗しました:', error);
  process.exit(1);
});