import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function resetSessions() {
  try {
    console.log('セッションテーブルのリセットを開始します...');
    
    // 既存のセッションを削除
    const deletedCount = await prisma.session.deleteMany({});
    console.log(`${deletedCount.count}件のセッションを削除しました。`);
    
    console.log('セッションテーブルのリセットが完了しました！');
    console.log('アプリを再インストールして、新しいセッションを作成してください。');
  } catch (error) {
    console.error('セッションテーブルのリセット中にエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetSessions();
