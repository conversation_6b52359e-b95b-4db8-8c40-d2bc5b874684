#!/usr/bin/env node

/**
 * デプロイ後にURLを自動取得して設定を更新するスクリプト
 */

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

// メイン処理
async function main() {
  try {
    // 1. まずアプリをデプロイ
    console.log("🚀 アプリをデプロイしています...");
    execSync("shopify app deploy", { stdio: "inherit" });

    // 2. デプロイ後のURLを取得
    console.log("🔍 デプロイされたURLを取得しています...");
    const deployInfo = execSync("shopify app info", { encoding: "utf8" });

    // URLを正規表現で抽出
    const urlMatch = deployInfo.match(
      /URL:\s+(https:\/\/[a-z0-9-]+\.trycloudflare\.com)/i,
    );

    if (!urlMatch || !urlMatch[1]) {
      console.error("❌ デプロイURLが見つかりませんでした。");
      console.log("以下の手順で手動で更新してください:");
      console.log("1. shopify app info コマンドでURLを確認");
      console.log("2. node scripts/update-url.js <新しいURL> を実行");
      process.exit(1);
    }

    const newUrl = urlMatch[1];
    console.log(`✅ 新しいURLを取得しました: ${newUrl}`);

    // 3. 設定ファイルを更新
    updateConfigFiles(newUrl);

    console.log("✨ デプロイと設定更新が完了しました！");
    console.log(`🚀 アプリは ${newUrl} で実行中です`);
  } catch (error) {
    console.error("エラーが発生しました:", error.message);
    process.exit(1);
  }
}

// 設定ファイルを更新する関数
function updateConfigFiles(newUrl) {
  console.log("📝 設定ファイルを更新しています...");

  // .envファイルの更新
  const envPath = path.join(process.cwd(), ".env");
  if (fs.existsSync(envPath)) {
    let envContent = fs.readFileSync(envPath, "utf8");

    // HOST値を更新
    envContent = envContent.replace(
      /HOST=["']?https:\/\/.*?\.trycloudflare\.com["']?/,
      `HOST="${newUrl}"`,
    );

    fs.writeFileSync(envPath, envContent);
    console.log("✅ .envファイルのHOST値を更新しました");
  } else {
    console.log("⚠️ .envファイルが見つかりません");
  }

  // shopify.app.tomlの更新
  const tomlPath = path.join(process.cwd(), "shopify.app.toml");
  if (fs.existsSync(tomlPath)) {
    let tomlContent = fs.readFileSync(tomlPath, "utf8");

    // application_urlを更新
    tomlContent = tomlContent.replace(
      /application_url = "https:\/\/.*?\.trycloudflare\.com"/,
      `application_url = "${newUrl}"`,
    );

    // redirect_urlsを更新
    const redirectUrlRegex = /(https:\/\/.*?\.trycloudflare\.com\/[^"]*)/g;
    const matches = tomlContent.match(redirectUrlRegex) || [];

    if (matches.length > 0) {
      // 古いドメイン部分を抽出
      const oldDomain = matches[0].match(
        /(https:\/\/.*?\.trycloudflare\.com)/,
      )[1];

      // すべてのリダイレクトURLを更新
      tomlContent = tomlContent.replace(new RegExp(oldDomain, "g"), newUrl);
      console.log(`✅ リダイレクトURLを更新しました (${matches.length}件)`);
    }

    fs.writeFileSync(tomlPath, tomlContent);
    console.log("✅ shopify.app.tomlのURLを更新しました");
  } else {
    console.log("⚠️ shopify.app.tomlファイルが見つかりません");
  }
}

// スクリプト実行
main().catch((error) => {
  console.error("予期せぬエラーが発生しました:", error);
  process.exit(1);
});
