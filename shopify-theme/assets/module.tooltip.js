/*plugin tippy*/

if(!window.jQuery.fn.is_theme_jquery){window.jQueryBackup=window.jQuery;window.jQuery=window.jQueryTheme;}(function($){(function(e,t){'object'==typeof exports&&'undefined'!=typeof module?module.exports=t():'function'==typeof define&&define.amd?define(t):e.tippy=t()})(this,function(){'use strict';function e(e){return e&&'[object Function]'==={}.toString.call(e)}function t(e,t){if(1!==e.nodeType)return[];var a=getComputedStyle(e,null);return t?a[t]:a}function a(e){return'HTML'===e.nodeName?e:e.parentNode||e.host}function r(e){if(!e)return document.body;switch(e.nodeName){case'HTML':case'BODY':return e.ownerDocument.body;case'#document':return e.body;}var p=t(e),o=p.overflow,i=p.overflowX,n=p.overflowY;return /(auto|scroll|overlay)/.test(o+n+i)?e:r(a(e))}function p(e){return 11===e?fe:10===e?he:fe||he}function o(e){if(!e)return document.documentElement;for(var a=p(10)?document.body:null,r=e.offsetParent;r===a&&e.nextElementSibling;)r=(e=e.nextElementSibling).offsetParent;var i=r&&r.nodeName;return i&&'BODY'!==i&&'HTML'!==i?-1!==['TD','TABLE'].indexOf(r.nodeName)&&'static'===t(r,'position')?o(r):r:e?e.ownerDocument.documentElement:document.documentElement}function n(e){var t=e.nodeName;return'BODY'!==t&&('HTML'===t||o(e.firstElementChild)===e)}function s(e){return null===e.parentNode?e:s(e.parentNode)}function l(e,t){if(!e||!e.nodeType||!t||!t.nodeType)return document.documentElement;var a=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,r=a?e:t,p=a?t:e,i=document.createRange();i.setStart(r,0),i.setEnd(p,0);var d=i.commonAncestorContainer;if(e!==d&&t!==d||r.contains(p))return n(d)?d:o(d);var c=s(e);return c.host?l(c.host,t):l(e,s(t).host)}function d(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:'top',a='top'===t?'scrollTop':'scrollLeft',r=e.nodeName;if('BODY'===r||'HTML'===r){var p=e.ownerDocument.documentElement,o=e.ownerDocument.scrollingElement||p;return o[a]}return e[a]}function c(e,t){var a=!!(2<arguments.length&&void 0!==arguments[2])&&arguments[2],r=d(t,'top'),p=d(t,'left'),o=a?-1:1;return e.top+=r*o,e.bottom+=r*o,e.left+=p*o,e.right+=p*o,e}function m(e,t){var a='x'===t?'Left':'Top',r='Left'===a?'Right':'Bottom';return parseFloat(e['border'+a+'Width'],10)+parseFloat(e['border'+r+'Width'],10)}function f(e,t,a,r){return ae(t['offset'+e],t['scroll'+e],a['client'+e],a['offset'+e],a['scroll'+e],p(10)?parseInt(a['offset'+e])+parseInt(r['margin'+('Height'===e?'Top':'Left')])+parseInt(r['margin'+('Height'===e?'Bottom':'Right')]):0)}function h(e){var t=e.body,a=e.documentElement,r=p(10)&&getComputedStyle(a);return{height:f('Height',t,a,r),width:f('Width',t,a,r)}}function b(e){return ge({},e,{right:e.left+e.width,bottom:e.top+e.height})}function u(e){var a={};try{if(p(10)){a=e.getBoundingClientRect();var r=d(e,'top'),o=d(e,'left');a.top+=r,a.left+=o,a.bottom+=r,a.right+=o}else a=e.getBoundingClientRect()}catch(t){}var i={left:a.left,top:a.top,width:a.right-a.left,height:a.bottom-a.top},n='HTML'===e.nodeName?h(e.ownerDocument):{},s=n.width||e.clientWidth||i.right-i.left,l=n.height||e.clientHeight||i.bottom-i.top,c=e.offsetWidth-s,f=e.offsetHeight-l;if(c||f){var y=t(e);c-=m(y,'x'),f-=m(y,'y'),i.width-=c,i.height-=f}return b(i)}function y(e,a){var o=!!(2<arguments.length&&void 0!==arguments[2])&&arguments[2],i=p(10),n='HTML'===a.nodeName,s=u(e),l=u(a),d=r(e),m=t(a),f=parseFloat(m.borderTopWidth,10),h=parseFloat(m.borderLeftWidth,10);o&&n&&(l.top=ae(l.top,0),l.left=ae(l.left,0));var y=b({top:s.top-l.top-f,left:s.left-l.left-h,width:s.width,height:s.height});if(y.marginTop=0,y.marginLeft=0,!i&&n){var g=parseFloat(m.marginTop,10),x=parseFloat(m.marginLeft,10);y.top-=f-g,y.bottom-=f-g,y.left-=h-x,y.right-=h-x,y.marginTop=g,y.marginLeft=x}return(i&&!o?a.contains(d):a===d&&'BODY'!==d.nodeName)&&(y=c(y,a)),y}function g(e){var t=!!(1<arguments.length&&void 0!==arguments[1])&&arguments[1],a=e.ownerDocument.documentElement,r=y(e,a),p=ae(a.clientWidth,window.innerWidth||0),o=ae(a.clientHeight,window.innerHeight||0),i=t?0:d(a),n=t?0:d(a,'left'),s={top:i-r.top+r.marginTop,left:n-r.left+r.marginLeft,width:p,height:o};return b(s)}function x(e){var r=e.nodeName;return'BODY'!==r&&'HTML'!==r&&('fixed'===t(e,'position')||x(a(e)))}function w(e){if(!e||!e.parentElement||p())return document.documentElement;for(var a=e.parentElement;a&&'none'===t(a,'transform');)a=a.parentElement;return a||document.documentElement}function v(e,t,p,o){var i=!!(4<arguments.length&&void 0!==arguments[4])&&arguments[4],n={top:0,left:0},s=i?w(e):l(e,t);if('viewport'===o)n=g(s,i);else{var d;'scrollParent'===o?(d=r(a(t)),'BODY'===d.nodeName&&(d=e.ownerDocument.documentElement)):'window'===o?d=e.ownerDocument.documentElement:d=o;var c=y(d,s,i);if('HTML'===d.nodeName&&!x(s)){var m=h(e.ownerDocument),f=m.height,b=m.width;n.top+=c.top-c.marginTop,n.bottom=f+c.top,n.left+=c.left-c.marginLeft,n.right=b+c.left}else n=c}p=p||0;var u='number'==typeof p;return n.left+=u?p:p.left||0,n.top+=u?p:p.top||0,n.right-=u?p:p.right||0,n.bottom-=u?p:p.bottom||0,n}function k(e){var t=e.width,a=e.height;return t*a}function E(e,t,a,r,p){var o=5<arguments.length&&void 0!==arguments[5]?arguments[5]:0;if(-1===e.indexOf('auto'))return e;var i=v(a,r,o,p),n={top:{width:i.width,height:t.top-i.top},right:{width:i.right-t.right,height:i.height},bottom:{width:i.width,height:i.bottom-t.bottom},left:{width:t.left-i.left,height:i.height}},s=Object.keys(n).map(function(e){return ge({key:e},n[e],{area:k(n[e])})}).sort(function(e,t){return t.area-e.area}),l=s.filter(function(e){var t=e.width,r=e.height;return t>=a.clientWidth&&r>=a.clientHeight}),d=0<l.length?l[0].key:s[0].key,c=e.split('-')[1];return d+(c?'-'+c:'')}function O(e,t,a){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null,p=r?w(t):l(t,a);return y(a,p,r)}function L(e){var t=getComputedStyle(e),a=parseFloat(t.marginTop)+parseFloat(t.marginBottom),r=parseFloat(t.marginLeft)+parseFloat(t.marginRight),p={width:e.offsetWidth+r,height:e.offsetHeight+a};return p}function C(e){var t={left:'right',right:'left',bottom:'top',top:'bottom'};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function T(e,t,a){a=a.split('-')[0];var r=L(e),p={width:r.width,height:r.height},o=-1!==['right','left'].indexOf(a),i=o?'top':'left',n=o?'left':'top',s=o?'height':'width',l=o?'width':'height';return p[i]=t[i]+t[s]/2-r[s]/2,p[n]=a===n?t[n]-r[l]:t[C(n)],p}function A(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function Y(e,t,a){if(Array.prototype.findIndex)return e.findIndex(function(e){return e[t]===a});var r=A(e,function(e){return e[t]===a});return e.indexOf(r)}function P(t,a,r){var p=void 0===r?t:t.slice(0,Y(t,'name',r));return p.forEach(function(t){t['function']&&console.warn('`modifier.function` is deprecated, use `modifier.fn`!');var r=t['function']||t.fn;t.enabled&&e(r)&&(a.offsets.popper=b(a.offsets.popper),a.offsets.reference=b(a.offsets.reference),a=r(a,t))}),a}function S(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=O(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=E(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=T(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?'fixed':'absolute',e=P(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}function D(e,t){return e.some(function(e){var a=e.name,r=e.enabled;return r&&a===t})}function X(e){for(var t=[!1,'ms','Webkit','Moz','O'],a=e.charAt(0).toUpperCase()+e.slice(1),r=0;r<t.length;r++){var p=t[r],o=p?''+p+a:e;if('undefined'!=typeof document.body.style[o])return o}return null}function I(){return this.state.isDestroyed=!0,D(this.modifiers,'applyStyle')&&(this.popper.removeAttribute('x-placement'),this.popper.style.position='',this.popper.style.top='',this.popper.style.left='',this.popper.style.right='',this.popper.style.bottom='',this.popper.style.willChange='',this.popper.style[X('transform')]=''),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function N(e){var t=e.ownerDocument;return t?t.defaultView:window}function H(e,t,a,p){var o='BODY'===e.nodeName,i=o?e.ownerDocument.defaultView:e;i.addEventListener(t,a,{passive:!0}),o||H(r(i.parentNode),t,a,p),p.push(i)}function R(e,t,a,p){a.updateBound=p,N(e).addEventListener('resize',a.updateBound,{passive:!0});var o=r(e);return H(o,'scroll',a.updateBound,a.scrollParents),a.scrollElement=o,a.eventsEnabled=!0,a}function B(){this.state.eventsEnabled||(this.state=R(this.reference,this.options,this.state,this.scheduleUpdate))}function W(e,t){return N(e).removeEventListener('resize',t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener('scroll',t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t}function z(){this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=W(this.reference,this.state))}function M(e){return''!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function _(e,t){Object.keys(t).forEach(function(a){var r='';-1!==['width','height','top','right','bottom','left'].indexOf(a)&&M(t[a])&&(r='px'),e.style[a]=t[a]+r})}function U(e,t){Object.keys(t).forEach(function(a){var r=t[a];!1===r?e.removeAttribute(a):e.setAttribute(a,t[a])})}function F(e,t,a){var r=A(e,function(e){var a=e.name;return a===t}),p=!!r&&e.some(function(e){return e.name===a&&e.enabled&&e.order<r.order});if(!p){var o='`'+t+'`';console.warn('`'+a+'`'+' modifier is required by '+o+' modifier in order to work, be sure to include it before '+o+'!')}return p}function V(e){return'end'===e?'start':'start'===e?'end':e}function q(e){var t=!!(1<arguments.length&&void 0!==arguments[1])&&arguments[1],a=we.indexOf(e),r=we.slice(a+1).concat(we.slice(0,a));return t?r.reverse():r}function j(e,t,a,r){var p=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),o=+p[1],i=p[2];if(!o)return e;if(0===i.indexOf('%')){var n;switch(i){case'%p':n=a;break;case'%':case'%r':default:n=r;}var s=b(n);return s[t]/100*o}if('vh'===i||'vw'===i){var l;return l='vh'===i?ae(document.documentElement.clientHeight,window.innerHeight||0):ae(document.documentElement.clientWidth,window.innerWidth||0),l/100*o}return o}function K(e,t,a,r){var p=[0,0],o=-1!==['right','left'].indexOf(r),i=e.split(/(\+|\-)/).map(function(e){return e.trim()}),n=i.indexOf(A(i,function(e){return-1!==e.search(/,|\s/)}));i[n]&&-1===i[n].indexOf(',')&&console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');var s=/\s*,\s*|\s+/,l=-1===n?[i]:[i.slice(0,n).concat([i[n].split(s)[0]]),[i[n].split(s)[1]].concat(i.slice(n+1))];return l=l.map(function(e,r){var p=(1===r?!o:o)?'height':'width',i=!1;return e.reduce(function(e,t){return''===e[e.length-1]&&-1!==['+','-'].indexOf(t)?(e[e.length-1]=t,i=!0,e):i?(e[e.length-1]+=t,i=!1,e):e.concat(t)},[]).map(function(e){return j(e,p,t,a)})}),l.forEach(function(e,t){e.forEach(function(a,r){M(a)&&(p[t]+=a*('-'===e[r-1]?-1:1))})}),p}function G(e,t){var a=t.offset,r=e.placement,p=e.offsets,o=p.popper,i=p.reference,n=r.split('-')[0],s=void 0;return s=M(+a)?[+a,0]:K(a,o,i,n),'left'===n?(o.top+=s[0],o.left-=s[1]):'right'===n?(o.top+=s[0],o.left+=s[1]):'top'===n?(o.left+=s[0],o.top-=s[1]):'bottom'===n&&(o.left+=s[0],o.top+=s[1]),e.popper=o,e}function Q(e){document.addEventListener('click',Ct,e),document.addEventListener('touchstart',Et),window.addEventListener('blur',Tt),window.addEventListener('resize',At),!vt&&(navigator.maxTouchPoints||navigator.msMaxTouchPoints)&&document.addEventListener('pointerdown',Et)}function Z(e,t){function a(){lt(function(){z=!1})}function r(e){var t=I=e,a=t.clientX,r=t.clientY;if(j.popperInstance){var p=j.reference.getBoundingClientRect(),o=j.props.followCursor,i='horizontal'===o,n='vertical'===o;j.popperInstance.reference={getBoundingClientRect:function(){return{width:0,height:0,top:i?p.top:r,bottom:i?p.bottom:r,left:n?p.left:a,right:n?p.right:a}},clientWidth:0,clientHeight:0},j.popperInstance.scheduleUpdate()}}function p(e){var t=Qe(e.target,j.props.target);if(t&&!t._tippy){var a=j.props.content;a&&(Z(t,oe({},j.props,{content:a,target:'',showOnInit:!0})),o(e))}}function o(e){if(C(),!j.state.isVisible){if(j.props.target)return p(e);if(R=!0,j.props.wait)return j.props.wait(j,e);g()&&(q.arrow&&(q.arrow.style.margin='0'),document.addEventListener('mousemove',r));var t=Re(j.props.delay,0,ie.delay);t?N=setTimeout(function(){A()},t):A()}}function i(){if(C(),!j.state.isVisible)return n();R=!1;var e=Re(j.props.delay,1,ie.delay);e?H=setTimeout(function(){j.state.isVisible&&Y()},e):Y()}function n(){document.removeEventListener('mousemove',r),I=null}function s(){document.body.removeEventListener('mouseleave',i),document.removeEventListener('mousemove',_)}function l(e){!j.state.isEnabled||b(e)||(!j.state.isVisible&&(X=e),'click'===e.type&&!1!==j.props.hideOnClick&&j.state.isVisible?i():o(e))}function d(e){var t=Ze(e.target,function(e){return e._tippy}),a=Qe(e.target,Ee.POPPER)===j.popper,r=t===j.reference;a||r||dt(mt(j.popper),j.popper.getBoundingClientRect(),e,j.props)&&(s(),i())}function c(e){return b(e)?void 0:j.props.interactive?(document.body.addEventListener('mouseleave',i),void document.addEventListener('mousemove',_)):void i()}function m(e){if(e.target===j.reference){if(j.props.interactive){if(!e.relatedTarget)return;if(Qe(e.relatedTarget,Ee.POPPER))return}i()}}function f(e){Qe(e.target,j.props.target)&&o(e)}function h(e){Qe(e.target,j.props.target)&&i()}function b(e){var t=-1<e.type.indexOf('touch'),a=vt&&kt&&j.props.touchHold&&!t,r=kt&&!j.props.touchHold&&t;return a||r}function y(){var e=j.popperChildren.tooltip,t=j.props.popperOptions,a=Ee['round'===j.props.arrowType?'ROUND_ARROW':'ARROW'],r=e.querySelector(a),p=oe({placement:j.props.placement},t||{},{modifiers:oe({},t?t.modifiers:{},{arrow:oe({element:a},t&&t.modifiers?t.modifiers.arrow:{}),flip:oe({enabled:j.props.flip,padding:j.props.distance+5,behavior:j.props.flipBehavior},t&&t.modifiers?t.modifiers.flip:{}),offset:oe({offset:j.props.offset},t&&t.modifiers?t.modifiers.offset:{})}),onCreate:function(){e.style[mt(j.popper)]=ct(j.props.distance,ie.distance),r&&j.props.arrowTransform&&ot(r,j.props.arrowTransform)},onUpdate:function(){var t=e.style;t.top='',t.bottom='',t.left='',t.right='',t[mt(j.popper)]=ct(j.props.distance,ie.distance),r&&j.props.arrowTransform&&ot(r,j.props.arrowTransform)}}),n=new MutationObserver(function(){j.popperInstance.update()});return n.observe(j.popper,{childList:!0,subtree:!0}),D&&D.disconnect(),D=n,M||(M=!0,j.popper.addEventListener('mouseenter',function(e){j.props.interactive&&j.state.isVisible&&'mouseenter'===X.type&&o(e)}),j.popper.addEventListener('mouseleave',function(e){j.props.interactive&&'mouseenter'===X.type&&0===j.props.interactiveDebounce&&dt(mt(j.popper),j.popper.getBoundingClientRect(),e,j.props)&&i()})),new ke(j.reference,j.popper,p)}function u(e){if(j.popperInstance?(!g()&&j.popperInstance.scheduleUpdate(),j.props.livePlacement&&!g()&&j.popperInstance.enableEventListeners()):(j.popperInstance=y(),!j.props.livePlacement&&j.popperInstance.disableEventListeners()),j.popperInstance.reference=j.reference,g()){j.popperChildren.arrow&&(j.popperChildren.arrow.style.margin='');var t=Re(j.props.delay,0,ie.delay);X.type&&r(t&&I?I:X)}st(j.popperInstance,e),j.props.appendTo.contains(j.popper)||(j.props.appendTo.appendChild(j.popper),U=!0)}function g(){return j.props.followCursor&&!kt&&'focus'!==X.type}function x(){Pe([j.popper],xt?0:j.props.updateDuration);var e=function e(){j.popperInstance&&j.popperInstance.scheduleUpdate(),U?requestAnimationFrame(e):Pe([j.popper],0)};e()}function w(e,t){k(e,function(){!j.state.isVisible&&j.props.appendTo.contains(j.popper)&&t()})}function v(e,t){k(e,t)}function k(e,t){if(0===e)return t();var a=j.popperChildren.tooltip,r=function r(p){p.target===a&&(ht(a,'remove',r),t())};ht(a,'remove',B),ht(a,'add',r),B=r}function E(e,t,a){j.reference.addEventListener(e,t),a.push({eventType:e,handler:t})}function O(){W=j.props.trigger.trim().split(' ').reduce(function(e,t){return'manual'===t?e:(j.props.target?'mouseenter'===t?(E('mouseover',f,e),E('mouseout',h,e)):'focus'===t?(E('focusin',f,e),E('focusout',h,e)):'click'===t?E(t,f,e):void 0:(E(t,l,e),j.props.touchHold&&(E('touchstart',l,e),E('touchend',c,e)),'mouseenter'===t?E('mouseleave',c,e):'focus'===t?E(xt?'focusout':'blur',m,e):void 0),e)},[])}function L(){W.forEach(function(e){var t=e.eventType,a=e.handler;j.reference.removeEventListener(t,a)})}function C(){clearTimeout(N),clearTimeout(H)}function T(e){yt(e,ie);var t=j.props,a=ft(j.reference,oe({},j.props,e,{performance:!0}));a.performance=e.performance||t.performance,j.props=a,('trigger'in e||'touchHold'in e)&&(L(),O()),'interactiveDebounce'in e&&(s(),_=bt(d,e.interactiveDebounce)),Ve(j.popper,t,a),j.popperChildren=Se(j.popper),j.popperInstance&&(j.popperInstance.destroy(),j.popperInstance=y(),!j.state.isVisible&&j.popperInstance.disableEventListeners())}function A(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:Re(j.props.duration,0,ie.duration[0]);return j.state.isDestroyed||!j.state.isEnabled||kt&&!j.props.touch?void 0:j.reference.isVirtual||document.documentElement.contains(j.reference)?j.reference.hasAttribute('disabled')?void 0:z?void(z=!1):void(!1===j.props.onShow(j)||(j.popper.style.visibility='visible',j.state.isVisible=!0,Pe([j.popper,j.popperChildren.tooltip,j.popperChildren.backdrop],0),u(function(){j.state.isVisible&&(!g()&&j.popperInstance.update(),Pe([j.popperChildren.tooltip,j.popperChildren.backdrop,j.popperChildren.content],e),j.popperChildren.backdrop&&(j.popperChildren.content.style.transitionDelay=ee(e/6)+'ms'),j.props.interactive&&j.reference.classList.add('tippy-active'),j.props.sticky&&x(),it([j.popperChildren.tooltip,j.popperChildren.backdrop,j.popperChildren.content],'visible'),v(e,function(){0===j.props.updateDuration&&j.popperChildren.tooltip.classList.add('tippy-notransition'),j.props.interactive&&-1<['focus','click'].indexOf(X.type)&&$e(j.popper),j.reference.setAttribute('aria-describedby',j.popper.id),j.props.onShown(j)}))}))):P()}function Y(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:Re(j.props.duration,1,ie.duration[1]);j.state.isDestroyed||!j.state.isEnabled||!1===j.props.onHide(j)||(0===j.props.updateDuration&&j.popperChildren.tooltip.classList.remove('tippy-notransition'),j.props.interactive&&j.reference.classList.remove('tippy-active'),j.popper.style.visibility='hidden',j.state.isVisible=!1,Pe([j.popperChildren.tooltip,j.popperChildren.backdrop,j.popperChildren.content],e),it([j.popperChildren.tooltip,j.popperChildren.backdrop,j.popperChildren.content],'hidden'),j.props.interactive&&!z&&-1<['focus','click'].indexOf(X.type)&&('focus'===X.type&&(z=!0),$e(j.reference)),w(e,function(){R||n(),j.reference.removeAttribute('aria-describedby'),j.popperInstance.disableEventListeners(),j.props.appendTo.removeChild(j.popper),U=!1,j.props.onHidden(j)}))}function P(e){j.state.isDestroyed||(j.state.isVisible&&Y(0),L(),j.reference.removeEventListener('click',a),delete j.reference._tippy,j.props.target&&e&&Te(j.reference.querySelectorAll(j.props.target)).forEach(function(e){return e._tippy&&e._tippy.destroy()}),j.popperInstance&&j.popperInstance.destroy(),D&&D.disconnect(),j.state.isDestroyed=!0)}var S=ft(e,t);if(!S.multiple&&e._tippy)return null;var D=null,X={},I=null,N=0,H=0,R=!1,B=function(){},W=[],z=!1,M=!1,_=0<S.interactiveDebounce?bt(d,S.interactiveDebounce):d,U=!1,F=Yt++,V=Fe(F,S),q=Se(V),j={id:F,reference:e,popper:V,popperChildren:q,popperInstance:null,props:S,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1},clearDelayTimeouts:C,set:T,setContent:function(e){T({content:e})},show:A,hide:Y,enable:function(){j.state.isEnabled=!0},disable:function(){j.state.isEnabled=!1},destroy:P};return O(),e.addEventListener('click',a),S.lazy||(j.popperInstance=y(),j.popperInstance.disableEventListeners()),S.showOnInit&&setTimeout(o,20),!S.a11y||S.target||Ye(e)||e.setAttribute('tabindex','0'),e._tippy=j,V._tippy=j,j}function $(e,t,a){yt(t,ie),Pt||(Q(St),Pt=!0);var r=oe({},ie,t);De(e)&&Ke(e);var p=Ne(e),o=p[0],i=(a&&o?[o]:p).reduce(function(e,t){var a=t&&Z(t,r);return a&&e.push(a),e},[]);return{targets:e,props:r,instances:i,destroyAll:function(){this.instances.forEach(function(e){e.destroy()}),this.instances=[]}}}for(var J=Math.min,ee=Math.round,te=Math.floor,ae=Math.max,re='.tippy-iOS{cursor:pointer!important}.tippy-notransition{transition:none!important}.tippy-popper{-webkit-perspective:700px;perspective:700px;z-index:9999;outline:0;transition-timing-function:cubic-bezier(.165,.84,.44,1);pointer-events:none;line-height:1.4}.tippy-popper[x-placement^=top] .tippy-backdrop{border-radius:40% 40% 0 0}.tippy-popper[x-placement^=top] .tippy-roundarrow{bottom:-8px;-webkit-transform-origin:50% 0;transform-origin:50% 0}.tippy-popper[x-placement^=top] .tippy-roundarrow svg{position:absolute;left:0;-webkit-transform:rotate(180deg);transform:rotate(180deg)}.tippy-popper[x-placement^=top] .tippy-arrow{border-top:8px solid #333;border-right:8px solid transparent;border-left:8px solid transparent;bottom:-7px;margin:0 6px;-webkit-transform-origin:50% 0;transform-origin:50% 0}.tippy-popper[x-placement^=top] .tippy-backdrop{-webkit-transform-origin:0 25%;transform-origin:0 25%}.tippy-popper[x-placement^=top] .tippy-backdrop[data-state=visible]{-webkit-transform:scale(1) translate(-50%,-55%);transform:scale(1) translate(-50%,-55%);opacity:1}.tippy-popper[x-placement^=top] .tippy-backdrop[data-state=hidden]{-webkit-transform:scale(.2) translate(-50%,-45%);transform:scale(.2) translate(-50%,-45%);opacity:0}.tippy-popper[x-placement^=top] [data-animation=shift-toward][data-state=visible]{opacity:1;-webkit-transform:translateY(-10px);transform:translateY(-10px)}.tippy-popper[x-placement^=top] [data-animation=shift-toward][data-state=hidden]{opacity:0;-webkit-transform:translateY(-20px);transform:translateY(-20px)}.tippy-popper[x-placement^=top] [data-animation=perspective]{-webkit-transform-origin:bottom;transform-origin:bottom}.tippy-popper[x-placement^=top] [data-animation=perspective][data-state=visible]{opacity:1;-webkit-transform:translateY(-10px) rotateX(0);transform:translateY(-10px) rotateX(0)}.tippy-popper[x-placement^=top] [data-animation=perspective][data-state=hidden]{opacity:0;-webkit-transform:translateY(0) rotateX(60deg);transform:translateY(0) rotateX(60deg)}.tippy-popper[x-placement^=top] [data-animation=fade][data-state=visible]{opacity:1;-webkit-transform:translateY(-10px);transform:translateY(-10px)}.tippy-popper[x-placement^=top] [data-animation=fade][data-state=hidden]{opacity:0;-webkit-transform:translateY(-10px);transform:translateY(-10px)}.tippy-popper[x-placement^=top] [data-animation=shift-away][data-state=visible]{opacity:1;-webkit-transform:translateY(-10px);transform:translateY(-10px)}.tippy-popper[x-placement^=top] [data-animation=shift-away][data-state=hidden]{opacity:0;-webkit-transform:translateY(0);transform:translateY(0)}.tippy-popper[x-placement^=top] [data-animation=scale][data-state=visible]{opacity:1;-webkit-transform:translateY(-10px) scale(1);transform:translateY(-10px) scale(1)}.tippy-popper[x-placement^=top] [data-animation=scale][data-state=hidden]{opacity:0;-webkit-transform:translateY(0) scale(.5);transform:translateY(0) scale(.5)}.tippy-popper[x-placement^=bottom] .tippy-backdrop{border-radius:0 0 30% 30%}.tippy-popper[x-placement^=bottom] .tippy-roundarrow{top:-8px;-webkit-transform-origin:50% 100%;transform-origin:50% 100%}.tippy-popper[x-placement^=bottom] .tippy-roundarrow svg{position:absolute;left:0;-webkit-transform:rotate(0);transform:rotate(0)}.tippy-popper[x-placement^=bottom] .tippy-arrow{border-bottom:8px solid #333;border-right:8px solid transparent;border-left:8px solid transparent;top:-7px;margin:0 6px;-webkit-transform-origin:50% 100%;transform-origin:50% 100%}.tippy-popper[x-placement^=bottom] .tippy-backdrop{-webkit-transform-origin:0 -50%;transform-origin:0 -50%}.tippy-popper[x-placement^=bottom] .tippy-backdrop[data-state=visible]{-webkit-transform:scale(1) translate(-50%,-45%);transform:scale(1) translate(-50%,-45%);opacity:1}.tippy-popper[x-placement^=bottom] .tippy-backdrop[data-state=hidden]{-webkit-transform:scale(.2) translate(-50%);transform:scale(.2) translate(-50%);opacity:0}.tippy-popper[x-placement^=bottom] [data-animation=shift-toward][data-state=visible]{opacity:1;-webkit-transform:translateY(10px);transform:translateY(10px)}.tippy-popper[x-placement^=bottom] [data-animation=shift-toward][data-state=hidden]{opacity:0;-webkit-transform:translateY(20px);transform:translateY(20px)}.tippy-popper[x-placement^=bottom] [data-animation=perspective]{-webkit-transform-origin:top;transform-origin:top}.tippy-popper[x-placement^=bottom] [data-animation=perspective][data-state=visible]{opacity:1;-webkit-transform:translateY(10px) rotateX(0);transform:translateY(10px) rotateX(0)}.tippy-popper[x-placement^=bottom] [data-animation=perspective][data-state=hidden]{opacity:0;-webkit-transform:translateY(0) rotateX(-60deg);transform:translateY(0) rotateX(-60deg)}.tippy-popper[x-placement^=bottom] [data-animation=fade][data-state=visible]{opacity:1;-webkit-transform:translateY(10px);transform:translateY(10px)}.tippy-popper[x-placement^=bottom] [data-animation=fade][data-state=hidden]{opacity:0;-webkit-transform:translateY(10px);transform:translateY(10px)}.tippy-popper[x-placement^=bottom] [data-animation=shift-away][data-state=visible]{opacity:1;-webkit-transform:translateY(10px);transform:translateY(10px)}.tippy-popper[x-placement^=bottom] [data-animation=shift-away][data-state=hidden]{opacity:0;-webkit-transform:translateY(0);transform:translateY(0)}.tippy-popper[x-placement^=bottom] [data-animation=scale][data-state=visible]{opacity:1;-webkit-transform:translateY(10px) scale(1);transform:translateY(10px) scale(1)}.tippy-popper[x-placement^=bottom] [data-animation=scale][data-state=hidden]{opacity:0;-webkit-transform:translateY(0) scale(.5);transform:translateY(0) scale(.5)}.tippy-popper[x-placement^=left] .tippy-backdrop{border-radius:50% 0 0 50%}.tippy-popper[x-placement^=left] .tippy-roundarrow{right:-16px;-webkit-transform-origin:33.33333333% 50%;transform-origin:33.33333333% 50%}.tippy-popper[x-placement^=left] .tippy-roundarrow svg{position:absolute;left:0;-webkit-transform:rotate(90deg);transform:rotate(90deg)}.tippy-popper[x-placement^=left] .tippy-arrow{border-left:8px solid #333;border-top:8px solid transparent;border-bottom:8px solid transparent;right:-7px;margin:3px 0;-webkit-transform-origin:0 50%;transform-origin:0 50%}.tippy-popper[x-placement^=left] .tippy-backdrop{-webkit-transform-origin:50% 0;transform-origin:50% 0}.tippy-popper[x-placement^=left] .tippy-backdrop[data-state=visible]{-webkit-transform:scale(1) translate(-50%,-50%);transform:scale(1) translate(-50%,-50%);opacity:1}.tippy-popper[x-placement^=left] .tippy-backdrop[data-state=hidden]{-webkit-transform:scale(.2) translate(-75%,-50%);transform:scale(.2) translate(-75%,-50%);opacity:0}.tippy-popper[x-placement^=left] [data-animation=shift-toward][data-state=visible]{opacity:1;-webkit-transform:translateX(-10px);transform:translateX(-10px)}.tippy-popper[x-placement^=left] [data-animation=shift-toward][data-state=hidden]{opacity:0;-webkit-transform:translateX(-20px);transform:translateX(-20px)}.tippy-popper[x-placement^=left] [data-animation=perspective]{-webkit-transform-origin:right;transform-origin:right}.tippy-popper[x-placement^=left] [data-animation=perspective][data-state=visible]{opacity:1;-webkit-transform:translateX(-10px) rotateY(0);transform:translateX(-10px) rotateY(0)}.tippy-popper[x-placement^=left] [data-animation=perspective][data-state=hidden]{opacity:0;-webkit-transform:translateX(0) rotateY(-60deg);transform:translateX(0) rotateY(-60deg)}.tippy-popper[x-placement^=left] [data-animation=fade][data-state=visible]{opacity:1;-webkit-transform:translateX(-10px);transform:translateX(-10px)}.tippy-popper[x-placement^=left] [data-animation=fade][data-state=hidden]{opacity:0;-webkit-transform:translateX(-10px);transform:translateX(-10px)}.tippy-popper[x-placement^=left] [data-animation=shift-away][data-state=visible]{opacity:1;-webkit-transform:translateX(-10px);transform:translateX(-10px)}.tippy-popper[x-placement^=left] [data-animation=shift-away][data-state=hidden]{opacity:0;-webkit-transform:translateX(0);transform:translateX(0)}.tippy-popper[x-placement^=left] [data-animation=scale][data-state=visible]{opacity:1;-webkit-transform:translateX(-10px) scale(1);transform:translateX(-10px) scale(1)}.tippy-popper[x-placement^=left] [data-animation=scale][data-state=hidden]{opacity:0;-webkit-transform:translateX(0) scale(.5);transform:translateX(0) scale(.5)}.tippy-popper[x-placement^=right] .tippy-backdrop{border-radius:0 50% 50% 0}.tippy-popper[x-placement^=right] .tippy-roundarrow{left:-16px;-webkit-transform-origin:66.66666666% 50%;transform-origin:66.66666666% 50%}.tippy-popper[x-placement^=right] .tippy-roundarrow svg{position:absolute;left:0;-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}.tippy-popper[x-placement^=right] .tippy-arrow{border-right:8px solid #333;border-top:8px solid transparent;border-bottom:8px solid transparent;left:-7px;margin:3px 0;-webkit-transform-origin:100% 50%;transform-origin:100% 50%}.tippy-popper[x-placement^=right] .tippy-backdrop{-webkit-transform-origin:-50% 0;transform-origin:-50% 0}.tippy-popper[x-placement^=right] .tippy-backdrop[data-state=visible]{-webkit-transform:scale(1) translate(-50%,-50%);transform:scale(1) translate(-50%,-50%);opacity:1}.tippy-popper[x-placement^=right] .tippy-backdrop[data-state=hidden]{-webkit-transform:scale(.2) translate(-25%,-50%);transform:scale(.2) translate(-25%,-50%);opacity:0}.tippy-popper[x-placement^=right] [data-animation=shift-toward][data-state=visible]{opacity:1;-webkit-transform:translateX(10px);transform:translateX(10px)}.tippy-popper[x-placement^=right] [data-animation=shift-toward][data-state=hidden]{opacity:0;-webkit-transform:translateX(20px);transform:translateX(20px)}.tippy-popper[x-placement^=right] [data-animation=perspective]{-webkit-transform-origin:left;transform-origin:left}.tippy-popper[x-placement^=right] [data-animation=perspective][data-state=visible]{opacity:1;-webkit-transform:translateX(10px) rotateY(0);transform:translateX(10px) rotateY(0)}.tippy-popper[x-placement^=right] [data-animation=perspective][data-state=hidden]{opacity:0;-webkit-transform:translateX(0) rotateY(60deg);transform:translateX(0) rotateY(60deg)}.tippy-popper[x-placement^=right] [data-animation=fade][data-state=visible]{opacity:1;-webkit-transform:translateX(10px);transform:translateX(10px)}.tippy-popper[x-placement^=right] [data-animation=fade][data-state=hidden]{opacity:0;-webkit-transform:translateX(10px);transform:translateX(10px)}.tippy-popper[x-placement^=right] [data-animation=shift-away][data-state=visible]{opacity:1;-webkit-transform:translateX(10px);transform:translateX(10px)}.tippy-popper[x-placement^=right] [data-animation=shift-away][data-state=hidden]{opacity:0;-webkit-transform:translateX(0);transform:translateX(0)}.tippy-popper[x-placement^=right] [data-animation=scale][data-state=visible]{opacity:1;-webkit-transform:translateX(10px) scale(1);transform:translateX(10px) scale(1)}.tippy-popper[x-placement^=right] [data-animation=scale][data-state=hidden]{opacity:0;-webkit-transform:translateX(0) scale(.5);transform:translateX(0) scale(.5)}.tippy-tooltip{position:relative;color:#fff;border-radius:4px;font-size:.9rem;padding:.3rem .6rem;max-width:350px;text-align:center;will-change:transform;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;background-color:#333}.tippy-tooltip[data-size=small]{padding:.2rem .4rem;font-size:.75rem}.tippy-tooltip[data-size=large]{padding:.4rem .8rem;font-size:1rem}.tippy-tooltip[data-animatefill]{overflow:hidden;background-color:transparent}.tippy-tooltip[data-interactive],.tippy-tooltip[data-interactive] path{pointer-events:auto}.tippy-tooltip[data-inertia][data-state=visible]{transition-timing-function:cubic-bezier(.53,2,.36,.85)}.tippy-tooltip[data-inertia][data-state=hidden]{transition-timing-function:ease}.tippy-arrow,.tippy-roundarrow{position:absolute;width:0;height:0}.tippy-roundarrow{width:24px;height:8px;fill:#333;pointer-events:none}.tippy-backdrop{position:absolute;will-change:transform;background-color:#333;border-radius:50%;width:calc(110% + 2rem);left:50%;top:50%;z-index:-1;transition:all cubic-bezier(.46,.1,.52,.98);-webkit-backface-visibility:hidden;backface-visibility:hidden}.tippy-backdrop:after{content:"";float:left;padding-top:100%}.tippy-backdrop+.tippy-content{transition-property:opacity}.tippy-backdrop+.tippy-content[data-state=visible]{opacity:1}.tippy-backdrop+.tippy-content[data-state=hidden]{opacity:0}@media (max-width:360px){.tippy-popper{max-width:96%;max-width:calc(100% - 20px)}}',pe='3.0.5',oe=Object.assign||function(e){for(var t,a=1;a<arguments.length;a++)for(var r in t=arguments[a],t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},ie={a11y:!0,content:'',placement:'top',livePlacement:!0,trigger:'mouseenter focus',hideOnClick:!0,animation:'shift-away',animateFill:!0,arrow:!1,delay:[0,20],duration:[325,275],interactive:!1,interactiveBorder:2,interactiveDebounce:0,theme:'dark',size:'regular',distance:10,offset:0,multiple:!1,followCursor:!1,inertia:!1,updateDuration:200,sticky:!1,appendTo:function(){return document.body},zIndex:9999,touchHold:!1,performance:!1,flip:!0,flipBehavior:'flip',arrowType:'sharp',arrowTransform:'',target:'',allowHTML:!0,showOnInit:!1,popperOptions:{},lazy:!0,touch:!0,wait:null,shouldPopperHideOnBlur:function(){return!0},onShow:function(){},onShown:function(){},onHide:function(){},onHidden:function(){}},ne=function(e){ie=oe({},ie,e)},se='undefined'!=typeof window&&'undefined'!=typeof document,le=['Edge','Trident','Firefox'],de=0,ce=0;ce<le.length;ce+=1)if(se&&0<=navigator.userAgent.indexOf(le[ce])){de=1;break}var i=se&&window.Promise,me=i?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},de))}},fe=se&&!!(window.MSInputMethodContext&&document.documentMode),he=se&&/MSIE 10/.test(navigator.userAgent),be=function(e,t){if(!(e instanceof t))throw new TypeError('Cannot call a class as a function')},ye=function(){function e(e,t){for(var a,r=0;r<t.length;r++)a=t[r],a.enumerable=a.enumerable||!1,a.configurable=!0,'value'in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),ue=function(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e},ge=Object.assign||function(e){for(var t,a=1;a<arguments.length;a++)for(var r in t=arguments[a],t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},xe=['auto-start','auto','auto-end','top-start','top','top-end','right-start','right','right-end','bottom-end','bottom','bottom-start','left-end','left','left-start'],we=xe.slice(3),ve={FLIP:'flip',CLOCKWISE:'clockwise',COUNTERCLOCKWISE:'counterclockwise'},ke=function(){function t(a,r){var p=this,o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};be(this,t),this.scheduleUpdate=function(){return requestAnimationFrame(p.update)},this.update=me(this.update.bind(this)),this.options=ge({},t.Defaults,o),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=a&&a.jquery?a[0]:a,this.popper=r&&r.jquery?r[0]:r,this.options.modifiers={},Object.keys(ge({},t.Defaults.modifiers,o.modifiers)).forEach(function(e){p.options.modifiers[e]=ge({},t.Defaults.modifiers[e]||{},o.modifiers?o.modifiers[e]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return ge({name:e},p.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(t){t.enabled&&e(t.onLoad)&&t.onLoad(p.reference,p.popper,p.options,t,p.state)}),this.update();var i=this.options.eventsEnabled;i&&this.enableEventListeners(),this.state.eventsEnabled=i}return ye(t,[{key:'update',value:function(){return S.call(this)}},{key:'destroy',value:function(){return I.call(this)}},{key:'enableEventListeners',value:function(){return B.call(this)}},{key:'disableEventListeners',value:function(){return z.call(this)}}]),t}();ke.Utils=('undefined'==typeof window?global:window).PopperUtils,ke.placements=xe,ke.Defaults={placement:'bottom',positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t=e.placement,a=t.split('-')[0],r=t.split('-')[1];if(r){var p=e.offsets,o=p.reference,i=p.popper,n=-1!==['bottom','top'].indexOf(a),s=n?'left':'top',l=n?'width':'height',d={start:ue({},s,o[s]),end:ue({},s,o[s]+o[l]-i[l])};e.offsets.popper=ge({},i,d[r])}return e}},offset:{order:200,enabled:!0,fn:G,offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,t){var a=t.boundariesElement||o(e.instance.popper);e.instance.reference===a&&(a=o(a));var r=X('transform'),p=e.instance.popper.style,i=p.top,n=p.left,s=p[r];p.top='',p.left='',p[r]='';var l=v(e.instance.popper,e.instance.reference,t.padding,a,e.positionFixed);p.top=i,p.left=n,p[r]=s,t.boundaries=l;var d=t.priority,c=e.offsets.popper,m={primary:function(e){var a=c[e];return c[e]<l[e]&&!t.escapeWithReference&&(a=ae(c[e],l[e])),ue({},e,a)},secondary:function(e){var a='right'===e?'left':'top',r=c[a];return c[e]>l[e]&&!t.escapeWithReference&&(r=J(c[a],l[e]-('right'===e?c.width:c.height))),ue({},a,r)}};return d.forEach(function(e){var t=-1===['left','top'].indexOf(e)?'secondary':'primary';c=ge({},c,m[t](e))}),e.offsets.popper=c,e},priority:['left','right','top','bottom'],padding:5,boundariesElement:'scrollParent'},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,a=t.popper,r=t.reference,p=e.placement.split('-')[0],o=te,i=-1!==['top','bottom'].indexOf(p),n=i?'right':'bottom',s=i?'left':'top',l=i?'width':'height';return a[n]<o(r[s])&&(e.offsets.popper[s]=o(r[s])-a[l]),a[s]>o(r[n])&&(e.offsets.popper[s]=o(r[n])),e}},arrow:{order:500,enabled:!0,fn:function(e,a){var r;if(!F(e.instance.modifiers,'arrow','keepTogether'))return e;var p=a.element;if('string'==typeof p){if(p=e.instance.popper.querySelector(p),!p)return e;}else if(!e.instance.popper.contains(p))return console.warn('WARNING: `arrow.element` must be child of its popper element!'),e;var o=e.placement.split('-')[0],i=e.offsets,n=i.popper,s=i.reference,l=-1!==['left','right'].indexOf(o),d=l?'height':'width',c=l?'Top':'Left',m=c.toLowerCase(),f=l?'left':'top',h=l?'bottom':'right',y=L(p)[d];s[h]-y<n[m]&&(e.offsets.popper[m]-=n[m]-(s[h]-y)),s[m]+y>n[h]&&(e.offsets.popper[m]+=s[m]+y-n[h]),e.offsets.popper=b(e.offsets.popper);var u=s[m]+s[d]/2-y/2,g=t(e.instance.popper),x=parseFloat(g['margin'+c],10),w=parseFloat(g['border'+c+'Width'],10),v=u-e.offsets.popper[m]-x-w;return v=ae(J(n[d]-y,v),0),e.arrowElement=p,e.offsets.arrow=(r={},ue(r,m,ee(v)),ue(r,f,''),r),e},element:'[x-arrow]'},flip:{order:600,enabled:!0,fn:function(e,t){if(D(e.instance.modifiers,'inner'))return e;if(e.flipped&&e.placement===e.originalPlacement)return e;var a=v(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement,e.positionFixed),r=e.placement.split('-')[0],p=C(r),o=e.placement.split('-')[1]||'',i=[];switch(t.behavior){case ve.FLIP:i=[r,p];break;case ve.CLOCKWISE:i=q(r);break;case ve.COUNTERCLOCKWISE:i=q(r,!0);break;default:i=t.behavior;}return i.forEach(function(n,s){if(r!==n||i.length===s+1)return e;r=e.placement.split('-')[0],p=C(r);var l=e.offsets.popper,d=e.offsets.reference,c=te,m='left'===r&&c(l.right)>c(d.left)||'right'===r&&c(l.left)<c(d.right)||'top'===r&&c(l.bottom)>c(d.top)||'bottom'===r&&c(l.top)<c(d.bottom),f=c(l.left)<c(a.left),h=c(l.right)>c(a.right),b=c(l.top)<c(a.top),y=c(l.bottom)>c(a.bottom),u='left'===r&&f||'right'===r&&h||'top'===r&&b||'bottom'===r&&y,g=-1!==['top','bottom'].indexOf(r),x=!!t.flipVariations&&(g&&'start'===o&&f||g&&'end'===o&&h||!g&&'start'===o&&b||!g&&'end'===o&&y);(m||u||x)&&(e.flipped=!0,(m||u)&&(r=i[s+1]),x&&(o=V(o)),e.placement=r+(o?'-'+o:''),e.offsets.popper=ge({},e.offsets.popper,T(e.instance.popper,e.offsets.reference,e.placement)),e=P(e.instance.modifiers,e,'flip'))}),e},behavior:'flip',padding:5,boundariesElement:'viewport'},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,a=t.split('-')[0],r=e.offsets,p=r.popper,o=r.reference,i=-1!==['left','right'].indexOf(a),n=-1===['top','left'].indexOf(a);return p[i?'left':'top']=o[a]-(n?p[i?'width':'height']:0),e.placement=C(t),e.offsets.popper=b(p),e}},hide:{order:800,enabled:!0,fn:function(e){if(!F(e.instance.modifiers,'hide','preventOverflow'))return e;var t=e.offsets.reference,a=A(e.instance.modifiers,function(e){return'preventOverflow'===e.name}).boundaries;if(t.bottom<a.top||t.left>a.right||t.top>a.bottom||t.right<a.left){if(!0===e.hide)return e;e.hide=!0,e.attributes['x-out-of-boundaries']=''}else{if(!1===e.hide)return e;e.hide=!1,e.attributes['x-out-of-boundaries']=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var a=t.x,r=t.y,p=e.offsets.popper,i=A(e.instance.modifiers,function(e){return'applyStyle'===e.name}).gpuAcceleration;void 0!==i&&console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');var n=void 0===i?t.gpuAcceleration:i,s=o(e.instance.popper),l=u(s),d={position:p.position},c={left:te(p.left),top:ee(p.top),bottom:ee(p.bottom),right:te(p.right)},m='bottom'===a?'top':'bottom',f='right'===r?'left':'right',h=X('transform'),b=void 0,y=void 0;if(y='bottom'==m?'HTML'===s.nodeName?-s.clientHeight+c.bottom:-l.height+c.bottom:c.top,b='right'==f?'HTML'===s.nodeName?-s.clientWidth+c.right:-l.width+c.right:c.left,n&&h)d[h]='translate3d('+b+'px, '+y+'px, 0)',d[m]=0,d[f]=0,d.willChange='transform';else{var g='bottom'==m?-1:1,x='right'==f?-1:1;d[m]=y*g,d[f]=b*x,d.willChange=m+', '+f}var w={"x-placement":e.placement};return e.attributes=ge({},w,e.attributes),e.styles=ge({},d,e.styles),e.arrowStyles=ge({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:'bottom',y:'right'},applyStyle:{order:900,enabled:!0,fn:function(e){return _(e.instance.popper,e.styles),U(e.instance.popper,e.attributes),e.arrowElement&&Object.keys(e.arrowStyles).length&&_(e.arrowElement,e.arrowStyles),e},onLoad:function(e,t,a,r,p){var o=O(p,t,e,a.positionFixed),i=E(a.placement,o,t,e,a.modifiers.flip.boundariesElement,a.modifiers.flip.padding);return t.setAttribute('x-placement',i),_(t,{position:a.positionFixed?'fixed':'absolute'}),a},gpuAcceleration:void 0}}};var Ee={POPPER:'.tippy-popper',TOOLTIP:'.tippy-tooltip',CONTENT:'.tippy-content',BACKDROP:'.tippy-backdrop',ARROW:'.tippy-arrow',ROUND_ARROW:'.tippy-roundarrow'},Oe={x:!0},Le='undefined'!=typeof window,Ce=Le&&'MutationObserver'in window,Te=function(e){return[].slice.call(e)},Ae=function(e,t){t.content instanceof Element?(Ie(e,''),e.appendChild(t.content)):e[t.allowHTML?'innerHTML':'textContent']=t.content},Ye=function(e){return!(e instanceof Element)||Ge.call(e,'a[href],area[href],button,details,input,textarea,select,iframe,[tabindex]')&&!e.hasAttribute('disabled')},Pe=function(e,t){e.filter(Boolean).forEach(function(e){e.style.transitionDuration=t+'ms'})},Se=function(e){var t=function(t){return e.querySelector(t)};return{tooltip:t(Ee.TOOLTIP),backdrop:t(Ee.BACKDROP),content:t(Ee.CONTENT),arrow:t(Ee.ARROW)||t(Ee.ROUND_ARROW)}},De=function(e){return'[object Object]'==={}.toString.call(e)},Xe=function(){return document.createElement('div')},Ie=function(e,t){e[Oe.x&&'innerHTML']=t instanceof Element?t[Oe.x&&'innerHTML']:t},Ne=function(e){if(e instanceof Element||De(e))return[e];if(e instanceof NodeList)return Te(e);if(Array.isArray(e))return e;try{return Te(document.querySelectorAll(e))}catch(t){return[]}},He=function(e){return!isNaN(e)&&!isNaN(parseFloat(e))},Re=function(e,t,a){if(Array.isArray(e)){var r=e[t];return null==r?a:r}return e},Be=function(e){var t=Xe();return'round'===e?(t.className='tippy-roundarrow',Ie(t,'<svg viewBox="0 0 24 8" xmlns="http://www.w3.org/2000/svg"><path d="M3 8s2.021-.015 5.253-4.218C9.584 2.051 10.797 1.007 12 1c1.203-.007 2.416 1.035 3.761 2.782C19.012 8.005 21 8 21 8H3z"/></svg>')):t.className='tippy-arrow',t},We=function(){var e=Xe();return e.className='tippy-backdrop',e.setAttribute('data-state','hidden'),e},ze=function(e,t){e.setAttribute('tabindex','-1'),t.setAttribute('data-interactive','')},Me=function(e,t){e.removeAttribute('tabindex'),t.removeAttribute('data-interactive')},_e=function(e){e.setAttribute('data-inertia','')},Ue=function(e){e.removeAttribute('data-inertia')},Fe=function(e,t){var a=Xe();a.className='tippy-popper',a.setAttribute('role','tooltip'),a.id='tippy-'+e,a.style.zIndex=t.zIndex;var r=Xe();r.className='tippy-tooltip',r.setAttribute('data-size',t.size),r.setAttribute('data-animation',t.animation),r.setAttribute('data-state','hidden'),t.theme.split(' ').forEach(function(e){r.classList.add(e+'-theme')});var p=Xe();return p.className='tippy-content',p.setAttribute('data-state','hidden'),t.interactive&&ze(a,r),t.arrow&&r.appendChild(Be(t.arrowType)),t.animateFill&&(r.appendChild(We()),r.setAttribute('data-animatefill','')),t.inertia&&r.setAttribute('data-inertia',''),Ae(p,t),r.appendChild(p),a.appendChild(r),a.addEventListener('focusout',function(t){t.relatedTarget&&a._tippy&&!Ze(t.relatedTarget,function(e){return e===a})&&t.relatedTarget!==a._tippy.reference&&a._tippy.props.shouldPopperHideOnBlur(t)&&a._tippy.hide()}),a},Ve=function(e,t,a){var r=Se(e),p=r.tooltip,o=r.content,i=r.backdrop,n=r.arrow;e.style.zIndex=a.zIndex,p.setAttribute('data-size',a.size),p.setAttribute('data-animation',a.animation),t.content!==a.content&&Ae(o,a),!t.animateFill&&a.animateFill?(p.appendChild(We()),p.setAttribute('data-animatefill','')):t.animateFill&&!a.animateFill&&(p.removeChild(i),p.removeAttribute('data-animatefill')),!t.arrow&&a.arrow?p.appendChild(Be(a.arrowType)):t.arrow&&!a.arrow&&p.removeChild(n),t.arrow&&a.arrow&&t.arrowType!==a.arrowType&&p.replaceChild(Be(a.arrowType),n),!t.interactive&&a.interactive?ze(e,p):t.interactive&&!a.interactive&&Me(e,p),!t.inertia&&a.inertia?_e(p):t.inertia&&!a.inertia&&Ue(p),t.theme!==a.theme&&(t.theme.split(' ').forEach(function(e){p.classList.remove(e+'-theme')}),a.theme.split(' ').forEach(function(e){p.classList.add(e+'-theme')}))},qe=function(e){Te(document.querySelectorAll(Ee.POPPER)).forEach(function(t){var a=t._tippy;a&&!0===a.props.hideOnClick&&(!e||t!==e.popper)&&a.hide()})},je=function(e){return Object.keys(ie).reduce(function(t,a){var r=(e.getAttribute('data-tippy-'+a)||'').trim();return r?(t[a]='true'===r||'false'!==r&&(He(r)?+r:'target'!==a&&'['===r[0]?JSON.parse(r):r),t):t},{})},Ke=function(e){var t={isVirtual:!0,attributes:e.attributes||{},setAttribute:function(t,a){e.attributes[t]=a},getAttribute:function(t){return e.attributes[t]},removeAttribute:function(t){delete e.attributes[t]},hasAttribute:function(t){return t in e.attributes},addEventListener:function(){},removeEventListener:function(){},classList:{classNames:{},add:function(t){e.classList.classNames[t]=!0},remove:function(t){delete e.classList.classNames[t]},contains:function(t){return t in e.classList.classNames}}};for(var a in t)e[a]=t[a];return e},Ge=function(){if(Le){var t=Element.prototype;return t.matches||t.matchesSelector||t.webkitMatchesSelector||t.mozMatchesSelector||t.msMatchesSelector}}(),Qe=function(e,t){return(Element.prototype.closest||function(e){for(var t=this;t;){if(Ge.call(t,e))return t;t=t.parentElement}}).call(e,t)},Ze=function(e,t){for(;e;){if(t(e))return e;e=e.parentElement}},$e=function(e){var t=window.scrollX||window.pageXOffset,a=window.scrollY||window.pageYOffset;e.focus(),scroll(t,a)},Je=function(e){void e.offsetHeight},et=function(e,t){return(t?e:{X:'Y',Y:'X'}[e])||''},tt=function(e,t,r,p){var o=t[0],i=t[1];if(!o&&!i)return'';var n={scale:function(){return i?r?o+', '+i:i+', '+o:''+o}(),translate:function(){return i?r?p?o+'px, '+-i+'px':o+'px, '+i+'px':p?-i+'px, '+o+'px':i+'px, '+o+'px':p?-o+'px':o+'px'}()};return n[e]},at=function(e,t){var a=e.match(new RegExp(t+'([XY])'));return a?a[1]:''},rt=function(e,t){var a=e.match(t);return a?a[1].split(',').map(parseFloat):[]},pt={translate:/translateX?Y?\(([^)]+)\)/,scale:/scaleX?Y?\(([^)]+)\)/},ot=function(e,t){var a=mt(Qe(e,Ee.POPPER)),r='top'===a||'bottom'===a,p='right'===a||'bottom'===a,o={translate:{axis:at(t,'translate'),numbers:rt(t,pt.translate)},scale:{axis:at(t,'scale'),numbers:rt(t,pt.scale)}},i=t.replace(pt.translate,'translate'+et(o.translate.axis,r)+'('+tt('translate',o.translate.numbers,r,p)+')').replace(pt.scale,'scale'+et(o.scale.axis,r)+'('+tt('scale',o.scale.numbers,r,p)+')');e.style[nt('transform')]=i},it=function(e,t){e.filter(Boolean).forEach(function(e){e.setAttribute('data-state',t)})},nt=function(e){for(var t=['','webkit'],a=e[0].toUpperCase()+e.slice(1),r=0;r<t.length;r++){var p=t[r],o=p?p+a:e;if('undefined'!=typeof document.body.style[o])return o}return null},st=function(e,t){var a=e.popper,r=e.options,p=r.onCreate,o=r.onUpdate;r.onCreate=r.onUpdate=function(){Je(a),t(),o(),r.onCreate=p,r.onUpdate=o}},lt=function(e){setTimeout(e,1)},dt=function(e,t,a,r){if(!e)return!0;var p=a.clientX,o=a.clientY,i=r.interactiveBorder,n=r.distance,s=t.top-o>('top'===e?i+n:i),l=o-t.bottom>('bottom'===e?i+n:i),d=t.left-p>('left'===e?i+n:i),c=p-t.right>('right'===e?i+n:i);return s||l||d||c},ct=function(e,t){return-(e-t)+'px'},mt=function(e){var t=e.getAttribute('x-placement');return t?t.split('-')[0]:''},ft=function(e,t){var a=oe({},t,t.performance?{}:je(e));return a.arrow&&(a.animateFill=!1),'function'==typeof a.appendTo&&(a.appendTo=t.appendTo(e)),'function'==typeof a.content&&(a.content=t.content(e)),a},ht=function(e,t,a){e[t+'EventListener']('transitionend',a)},bt=function(e,t){var a;return function(){var r=this,p=arguments;clearTimeout(a),a=setTimeout(function(){return e.apply(r,p)},t)}},yt=function(e,t){for(var a in e||{})if(!(a in t))throw Error('[tippy]: `'+a+'` is not a valid option')},ut=Le?navigator:{},gt=Le?window:{},xt=/MSIE |Trident\//.test(ut.userAgent),wt=/iPhone|iPad|iPod/.test(ut.platform)&&!gt.MSStream,vt='ontouchstart'in gt,kt=!1,Et=function(){kt||(kt=!0,wt&&document.body.classList.add('tippy-iOS'),window.performance&&document.addEventListener('mousemove',Lt))},Ot=0,Lt=function e(){var t=performance.now();20>t-Ot&&(kt=!1,document.removeEventListener('mousemove',e),!wt&&document.body.classList.remove('tippy-iOS')),Ot=t},Ct=function(e){var t=e.target;if(!(t instanceof Element))return qe();var a=Qe(t,Ee.POPPER);if(!(a&&a._tippy&&a._tippy.props.interactive)){var r=Ze(t,function(e){return e._tippy&&e._tippy.reference===e});if(r){var p=r._tippy,o=-1<p.props.trigger.indexOf('click');if(kt||o)return qe(p);if(!0!==p.props.hideOnClick||o)return;p.clearDelayTimeouts()}qe()}},Tt=function(){var e=document,t=e.activeElement;t&&t.blur&&t._tippy&&t.blur()},At=function(){Te(document.querySelectorAll(Ee.POPPER)).forEach(function(e){var t=e._tippy;t.props.livePlacement||t.popperInstance.scheduleUpdate()})},Yt=1,Pt=!1,St=!1;$.version=pe,$.defaults=ie,$.one=function(e,t){return $(e,t,!0).instances[0]},$.setDefaults=function(e){ne(e),$.defaults=ie},$.disableAnimations=function(){$.setDefaults({duration:0,updateDuration:0,animateFill:!1})},$.hideAllPoppers=qe,$.useCapture=function(){St=!0};return Le&&setTimeout(function(){Te(document.querySelectorAll('[data-tippy]')).forEach(function(e){var t=e.getAttribute('data-tippy');t&&$(e,{content:t})})}),function(e){if(Ce){var t=document.createElement('style');t.type='text/css',t.textContent=e,document.head.insertBefore(t,document.head.firstChild)}}(re),$});})(jQueryTheme);if(window.jQueryBackup){window.jQuery=window.jQueryBackup;window.jQueryBackup=undefined;}

/*plugin popper*/
/*
 Copyright (C) Federico Zivolo 2018
 Distributed under the MIT License (license terms are at http://opensource.org/licenses/MIT).
 */
if(!window.jQuery.fn.is_theme_jquery){window.jQueryBackup=window.jQuery;window.jQuery=window.jQueryTheme;}(function($){(function(e,t){'object'==typeof exports&&'undefined'!=typeof module?module.exports=t():'function'==typeof define&&define.amd?define(t):e.Popper=t()})(this,function(){'use strict';function e(e){return e&&'[object Function]'==={}.toString.call(e)}function t(e,t){if(1!==e.nodeType)return[];var o=getComputedStyle(e,null);return t?o[t]:o}function o(e){return'HTML'===e.nodeName?e:e.parentNode||e.host}function n(e){if(!e)return document.body;switch(e.nodeName){case'HTML':case'BODY':return e.ownerDocument.body;case'#document':return e.body;}var i=t(e),r=i.overflow,p=i.overflowX,s=i.overflowY;return /(auto|scroll|overlay)/.test(r+s+p)?e:n(o(e))}function r(e){return 11===e?re:10===e?pe:re||pe}function p(e){if(!e)return document.documentElement;for(var o=r(10)?document.body:null,n=e.offsetParent;n===o&&e.nextElementSibling;)n=(e=e.nextElementSibling).offsetParent;var i=n&&n.nodeName;return i&&'BODY'!==i&&'HTML'!==i?-1!==['TD','TABLE'].indexOf(n.nodeName)&&'static'===t(n,'position')?p(n):n:e?e.ownerDocument.documentElement:document.documentElement}function s(e){var t=e.nodeName;return'BODY'!==t&&('HTML'===t||p(e.firstElementChild)===e)}function d(e){return null===e.parentNode?e:d(e.parentNode)}function a(e,t){if(!e||!e.nodeType||!t||!t.nodeType)return document.documentElement;var o=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,n=o?e:t,i=o?t:e,r=document.createRange();r.setStart(n,0),r.setEnd(i,0);var l=r.commonAncestorContainer;if(e!==l&&t!==l||n.contains(i))return s(l)?l:p(l);var f=d(e);return f.host?a(f.host,t):a(e,d(t).host)}function l(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:'top',o='top'===t?'scrollTop':'scrollLeft',n=e.nodeName;if('BODY'===n||'HTML'===n){var i=e.ownerDocument.documentElement,r=e.ownerDocument.scrollingElement||i;return r[o]}return e[o]}function f(e,t){var o=2<arguments.length&&void 0!==arguments[2]&&arguments[2],n=l(t,'top'),i=l(t,'left'),r=o?-1:1;return e.top+=n*r,e.bottom+=n*r,e.left+=i*r,e.right+=i*r,e}function m(e,t){var o='x'===t?'Left':'Top',n='Left'==o?'Right':'Bottom';return parseFloat(e['border'+o+'Width'],10)+parseFloat(e['border'+n+'Width'],10)}function h(e,t,o,n){return J(t['offset'+e],t['scroll'+e],o['client'+e],o['offset'+e],o['scroll'+e],r(10)?parseInt(o['offset'+e])+parseInt(n['margin'+('Height'===e?'Top':'Left')])+parseInt(n['margin'+('Height'===e?'Bottom':'Right')]):0)}function c(e){var t=e.body,o=e.documentElement,n=r(10)&&getComputedStyle(o);return{height:h('Height',t,o,n),width:h('Width',t,o,n)}}function g(e){return le({},e,{right:e.left+e.width,bottom:e.top+e.height})}function u(e){var o={};try{if(r(10)){o=e.getBoundingClientRect();var n=l(e,'top'),i=l(e,'left');o.top+=n,o.left+=i,o.bottom+=n,o.right+=i}else o=e.getBoundingClientRect()}catch(t){}var p={left:o.left,top:o.top,width:o.right-o.left,height:o.bottom-o.top},s='HTML'===e.nodeName?c(e.ownerDocument):{},d=s.width||e.clientWidth||p.right-p.left,a=s.height||e.clientHeight||p.bottom-p.top,f=e.offsetWidth-d,h=e.offsetHeight-a;if(f||h){var u=t(e);f-=m(u,'x'),h-=m(u,'y'),p.width-=f,p.height-=h}return g(p)}function b(e,o){var i=2<arguments.length&&void 0!==arguments[2]&&arguments[2],p=r(10),s='HTML'===o.nodeName,d=u(e),a=u(o),l=n(e),m=t(o),h=parseFloat(m.borderTopWidth,10),c=parseFloat(m.borderLeftWidth,10);i&&s&&(a.top=J(a.top,0),a.left=J(a.left,0));var b=g({top:d.top-a.top-h,left:d.left-a.left-c,width:d.width,height:d.height});if(b.marginTop=0,b.marginLeft=0,!p&&s){var y=parseFloat(m.marginTop,10),w=parseFloat(m.marginLeft,10);b.top-=h-y,b.bottom-=h-y,b.left-=c-w,b.right-=c-w,b.marginTop=y,b.marginLeft=w}return(p&&!i?o.contains(l):o===l&&'BODY'!==l.nodeName)&&(b=f(b,o)),b}function y(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],o=e.ownerDocument.documentElement,n=b(e,o),i=J(o.clientWidth,window.innerWidth||0),r=J(o.clientHeight,window.innerHeight||0),p=t?0:l(o),s=t?0:l(o,'left'),d={top:p-n.top+n.marginTop,left:s-n.left+n.marginLeft,width:i,height:r};return g(d)}function w(e){var n=e.nodeName;return'BODY'===n||'HTML'===n?!1:'fixed'===t(e,'position')||w(o(e))}function E(e){if(!e||!e.parentElement||r())return document.documentElement;for(var o=e.parentElement;o&&'none'===t(o,'transform');)o=o.parentElement;return o||document.documentElement}function v(e,t,i,r){var p=4<arguments.length&&void 0!==arguments[4]&&arguments[4],s={top:0,left:0},d=p?E(e):a(e,t);if('viewport'===r)s=y(d,p);else{var l;'scrollParent'===r?(l=n(o(t)),'BODY'===l.nodeName&&(l=e.ownerDocument.documentElement)):'window'===r?l=e.ownerDocument.documentElement:l=r;var f=b(l,d,p);if('HTML'===l.nodeName&&!w(d)){var m=c(e.ownerDocument),h=m.height,g=m.width;s.top+=f.top-f.marginTop,s.bottom=h+f.top,s.left+=f.left-f.marginLeft,s.right=g+f.left}else s=f}i=i||0;var u='number'==typeof i;return s.left+=u?i:i.left||0,s.top+=u?i:i.top||0,s.right-=u?i:i.right||0,s.bottom-=u?i:i.bottom||0,s}function x(e){var t=e.width,o=e.height;return t*o}function O(e,t,o,n,i){var r=5<arguments.length&&void 0!==arguments[5]?arguments[5]:0;if(-1===e.indexOf('auto'))return e;var p=v(o,n,r,i),s={top:{width:p.width,height:t.top-p.top},right:{width:p.right-t.right,height:p.height},bottom:{width:p.width,height:p.bottom-t.bottom},left:{width:t.left-p.left,height:p.height}},d=Object.keys(s).map(function(e){return le({key:e},s[e],{area:x(s[e])})}).sort(function(e,t){return t.area-e.area}),a=d.filter(function(e){var t=e.width,n=e.height;return t>=o.clientWidth&&n>=o.clientHeight}),l=0<a.length?a[0].key:d[0].key,f=e.split('-')[1];return l+(f?'-'+f:'')}function L(e,t,o){var n=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null,i=n?E(t):a(t,o);return b(o,i,n)}function S(e){var t=getComputedStyle(e),o=parseFloat(t.marginTop)+parseFloat(t.marginBottom),n=parseFloat(t.marginLeft)+parseFloat(t.marginRight),i={width:e.offsetWidth+n,height:e.offsetHeight+o};return i}function T(e){var t={left:'right',right:'left',bottom:'top',top:'bottom'};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function D(e,t,o){o=o.split('-')[0];var n=S(e),i={width:n.width,height:n.height},r=-1!==['right','left'].indexOf(o),p=r?'top':'left',s=r?'left':'top',d=r?'height':'width',a=r?'width':'height';return i[p]=t[p]+t[d]/2-n[d]/2,i[s]=o===s?t[s]-n[a]:t[T(s)],i}function C(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function N(e,t,o){if(Array.prototype.findIndex)return e.findIndex(function(e){return e[t]===o});var n=C(e,function(e){return e[t]===o});return e.indexOf(n)}function P(t,o,n){var i=void 0===n?t:t.slice(0,N(t,'name',n));return i.forEach(function(t){t['function']&&console.warn('`modifier.function` is deprecated, use `modifier.fn`!');var n=t['function']||t.fn;t.enabled&&e(n)&&(o.offsets.popper=g(o.offsets.popper),o.offsets.reference=g(o.offsets.reference),o=n(o,t))}),o}function k(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=L(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=O(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=D(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?'fixed':'absolute',e=P(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}function W(e,t){return e.some(function(e){var o=e.name,n=e.enabled;return n&&o===t})}function H(e){for(var t=[!1,'ms','Webkit','Moz','O'],o=e.charAt(0).toUpperCase()+e.slice(1),n=0;n<t.length;n++){var i=t[n],r=i?''+i+o:e;if('undefined'!=typeof document.body.style[r])return r}return null}function B(){return this.state.isDestroyed=!0,W(this.modifiers,'applyStyle')&&(this.popper.removeAttribute('x-placement'),this.popper.style.position='',this.popper.style.top='',this.popper.style.left='',this.popper.style.right='',this.popper.style.bottom='',this.popper.style.willChange='',this.popper.style[H('transform')]=''),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function A(e){var t=e.ownerDocument;return t?t.defaultView:window}function M(e,t,o,i){var r='BODY'===e.nodeName,p=r?e.ownerDocument.defaultView:e;p.addEventListener(t,o,{passive:!0}),r||M(n(p.parentNode),t,o,i),i.push(p)}function F(e,t,o,i){o.updateBound=i,A(e).addEventListener('resize',o.updateBound,{passive:!0});var r=n(e);return M(r,'scroll',o.updateBound,o.scrollParents),o.scrollElement=r,o.eventsEnabled=!0,o}function I(){this.state.eventsEnabled||(this.state=F(this.reference,this.options,this.state,this.scheduleUpdate))}function R(e,t){return A(e).removeEventListener('resize',t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener('scroll',t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t}function U(){this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=R(this.reference,this.state))}function Y(e){return''!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function j(e,t){Object.keys(t).forEach(function(o){var n='';-1!==['width','height','top','right','bottom','left'].indexOf(o)&&Y(t[o])&&(n='px'),e.style[o]=t[o]+n})}function K(e,t){Object.keys(t).forEach(function(o){var n=t[o];!1===n?e.removeAttribute(o):e.setAttribute(o,t[o])})}function q(e,t,o){var n=C(e,function(e){var o=e.name;return o===t}),i=!!n&&e.some(function(e){return e.name===o&&e.enabled&&e.order<n.order});if(!i){var r='`'+t+'`';console.warn('`'+o+'`'+' modifier is required by '+r+' modifier in order to work, be sure to include it before '+r+'!')}return i}function G(e){return'end'===e?'start':'start'===e?'end':e}function V(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],o=me.indexOf(e),n=me.slice(o+1).concat(me.slice(0,o));return t?n.reverse():n}function z(e,t,o,n){var i=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),r=+i[1],p=i[2];if(!r)return e;if(0===p.indexOf('%')){var s;switch(p){case'%p':s=o;break;case'%':case'%r':default:s=n;}var d=g(s);return d[t]/100*r}if('vh'===p||'vw'===p){var a;return a='vh'===p?J(document.documentElement.clientHeight,window.innerHeight||0):J(document.documentElement.clientWidth,window.innerWidth||0),a/100*r}return r}function _(e,t,o,n){var i=[0,0],r=-1!==['right','left'].indexOf(n),p=e.split(/(\+|\-)/).map(function(e){return e.trim()}),s=p.indexOf(C(p,function(e){return-1!==e.search(/,|\s/)}));p[s]&&-1===p[s].indexOf(',')&&console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');var d=/\s*,\s*|\s+/,a=-1===s?[p]:[p.slice(0,s).concat([p[s].split(d)[0]]),[p[s].split(d)[1]].concat(p.slice(s+1))];return a=a.map(function(e,n){var i=(1===n?!r:r)?'height':'width',p=!1;return e.reduce(function(e,t){return''===e[e.length-1]&&-1!==['+','-'].indexOf(t)?(e[e.length-1]=t,p=!0,e):p?(e[e.length-1]+=t,p=!1,e):e.concat(t)},[]).map(function(e){return z(e,i,t,o)})}),a.forEach(function(e,t){e.forEach(function(o,n){Y(o)&&(i[t]+=o*('-'===e[n-1]?-1:1))})}),i}function X(e,t){var o,n=t.offset,i=e.placement,r=e.offsets,p=r.popper,s=r.reference,d=i.split('-')[0];return o=Y(+n)?[+n,0]:_(n,p,s,d),'left'===d?(p.top+=o[0],p.left-=o[1]):'right'===d?(p.top+=o[0],p.left+=o[1]):'top'===d?(p.left+=o[0],p.top-=o[1]):'bottom'===d&&(p.left+=o[0],p.top+=o[1]),e.popper=p,e}for(var Q=Math.min,Z=Math.round,$=Math.floor,J=Math.max,ee='undefined'!=typeof window&&'undefined'!=typeof document,te=['Edge','Trident','Firefox'],oe=0,ne=0;ne<te.length;ne+=1)if(ee&&0<=navigator.userAgent.indexOf(te[ne])){oe=1;break}var i=ee&&window.Promise,ie=i?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},oe))}},re=ee&&!!(window.MSInputMethodContext&&document.documentMode),pe=ee&&/MSIE 10/.test(navigator.userAgent),se=function(e,t){if(!(e instanceof t))throw new TypeError('Cannot call a class as a function')},de=function(){function e(e,t){for(var o,n=0;n<t.length;n++)o=t[n],o.enumerable=o.enumerable||!1,o.configurable=!0,'value'in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}return function(t,o,n){return o&&e(t.prototype,o),n&&e(t,n),t}}(),ae=function(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e},le=Object.assign||function(e){for(var t,o=1;o<arguments.length;o++)for(var n in t=arguments[o],t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},fe=['auto-start','auto','auto-end','top-start','top','top-end','right-start','right','right-end','bottom-end','bottom','bottom-start','left-end','left','left-start'],me=fe.slice(3),he={FLIP:'flip',CLOCKWISE:'clockwise',COUNTERCLOCKWISE:'counterclockwise'},ce=function(){function t(o,n){var i=this,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};se(this,t),this.scheduleUpdate=function(){return requestAnimationFrame(i.update)},this.update=ie(this.update.bind(this)),this.options=le({},t.Defaults,r),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=o&&o.jquery?o[0]:o,this.popper=n&&n.jquery?n[0]:n,this.options.modifiers={},Object.keys(le({},t.Defaults.modifiers,r.modifiers)).forEach(function(e){i.options.modifiers[e]=le({},t.Defaults.modifiers[e]||{},r.modifiers?r.modifiers[e]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return le({name:e},i.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(t){t.enabled&&e(t.onLoad)&&t.onLoad(i.reference,i.popper,i.options,t,i.state)}),this.update();var p=this.options.eventsEnabled;p&&this.enableEventListeners(),this.state.eventsEnabled=p}return de(t,[{key:'update',value:function(){return k.call(this)}},{key:'destroy',value:function(){return B.call(this)}},{key:'enableEventListeners',value:function(){return I.call(this)}},{key:'disableEventListeners',value:function(){return U.call(this)}}]),t}();return ce.Utils=('undefined'==typeof window?global:window).PopperUtils,ce.placements=fe,ce.Defaults={placement:'bottom',positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t=e.placement,o=t.split('-')[0],n=t.split('-')[1];if(n){var i=e.offsets,r=i.reference,p=i.popper,s=-1!==['bottom','top'].indexOf(o),d=s?'left':'top',a=s?'width':'height',l={start:ae({},d,r[d]),end:ae({},d,r[d]+r[a]-p[a])};e.offsets.popper=le({},p,l[n])}return e}},offset:{order:200,enabled:!0,fn:X,offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,t){var o=t.boundariesElement||p(e.instance.popper);e.instance.reference===o&&(o=p(o));var n=H('transform'),i=e.instance.popper.style,r=i.top,s=i.left,d=i[n];i.top='',i.left='',i[n]='';var a=v(e.instance.popper,e.instance.reference,t.padding,o,e.positionFixed);i.top=r,i.left=s,i[n]=d,t.boundaries=a;var l=t.priority,f=e.offsets.popper,m={primary:function(e){var o=f[e];return f[e]<a[e]&&!t.escapeWithReference&&(o=J(f[e],a[e])),ae({},e,o)},secondary:function(e){var o='right'===e?'left':'top',n=f[o];return f[e]>a[e]&&!t.escapeWithReference&&(n=Q(f[o],a[e]-('right'===e?f.width:f.height))),ae({},o,n)}};return l.forEach(function(e){var t=-1===['left','top'].indexOf(e)?'secondary':'primary';f=le({},f,m[t](e))}),e.offsets.popper=f,e},priority:['left','right','top','bottom'],padding:5,boundariesElement:'scrollParent'},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,o=t.popper,n=t.reference,i=e.placement.split('-')[0],r=$,p=-1!==['top','bottom'].indexOf(i),s=p?'right':'bottom',d=p?'left':'top',a=p?'width':'height';return o[s]<r(n[d])&&(e.offsets.popper[d]=r(n[d])-o[a]),o[d]>r(n[s])&&(e.offsets.popper[d]=r(n[s])),e}},arrow:{order:500,enabled:!0,fn:function(e,o){var n;if(!q(e.instance.modifiers,'arrow','keepTogether'))return e;var i=o.element;if('string'==typeof i){if(i=e.instance.popper.querySelector(i),!i)return e;}else if(!e.instance.popper.contains(i))return console.warn('WARNING: `arrow.element` must be child of its popper element!'),e;var r=e.placement.split('-')[0],p=e.offsets,s=p.popper,d=p.reference,a=-1!==['left','right'].indexOf(r),l=a?'height':'width',f=a?'Top':'Left',m=f.toLowerCase(),h=a?'left':'top',c=a?'bottom':'right',u=S(i)[l];d[c]-u<s[m]&&(e.offsets.popper[m]-=s[m]-(d[c]-u)),d[m]+u>s[c]&&(e.offsets.popper[m]+=d[m]+u-s[c]),e.offsets.popper=g(e.offsets.popper);var b=d[m]+d[l]/2-u/2,y=t(e.instance.popper),w=parseFloat(y['margin'+f],10),E=parseFloat(y['border'+f+'Width'],10),v=b-e.offsets.popper[m]-w-E;return v=J(Q(s[l]-u,v),0),e.arrowElement=i,e.offsets.arrow=(n={},ae(n,m,Z(v)),ae(n,h,''),n),e},element:'[x-arrow]'},flip:{order:600,enabled:!0,fn:function(e,t){if(W(e.instance.modifiers,'inner'))return e;if(e.flipped&&e.placement===e.originalPlacement)return e;var o=v(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement,e.positionFixed),n=e.placement.split('-')[0],i=T(n),r=e.placement.split('-')[1]||'',p=[];switch(t.behavior){case he.FLIP:p=[n,i];break;case he.CLOCKWISE:p=V(n);break;case he.COUNTERCLOCKWISE:p=V(n,!0);break;default:p=t.behavior;}return p.forEach(function(s,d){if(n!==s||p.length===d+1)return e;n=e.placement.split('-')[0],i=T(n);var a=e.offsets.popper,l=e.offsets.reference,f=$,m='left'===n&&f(a.right)>f(l.left)||'right'===n&&f(a.left)<f(l.right)||'top'===n&&f(a.bottom)>f(l.top)||'bottom'===n&&f(a.top)<f(l.bottom),h=f(a.left)<f(o.left),c=f(a.right)>f(o.right),g=f(a.top)<f(o.top),u=f(a.bottom)>f(o.bottom),b='left'===n&&h||'right'===n&&c||'top'===n&&g||'bottom'===n&&u,y=-1!==['top','bottom'].indexOf(n),w=!!t.flipVariations&&(y&&'start'===r&&h||y&&'end'===r&&c||!y&&'start'===r&&g||!y&&'end'===r&&u);(m||b||w)&&(e.flipped=!0,(m||b)&&(n=p[d+1]),w&&(r=G(r)),e.placement=n+(r?'-'+r:''),e.offsets.popper=le({},e.offsets.popper,D(e.instance.popper,e.offsets.reference,e.placement)),e=P(e.instance.modifiers,e,'flip'))}),e},behavior:'flip',padding:5,boundariesElement:'viewport'},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,o=t.split('-')[0],n=e.offsets,i=n.popper,r=n.reference,p=-1!==['left','right'].indexOf(o),s=-1===['top','left'].indexOf(o);return i[p?'left':'top']=r[o]-(s?i[p?'width':'height']:0),e.placement=T(t),e.offsets.popper=g(i),e}},hide:{order:800,enabled:!0,fn:function(e){if(!q(e.instance.modifiers,'hide','preventOverflow'))return e;var t=e.offsets.reference,o=C(e.instance.modifiers,function(e){return'preventOverflow'===e.name}).boundaries;if(t.bottom<o.top||t.left>o.right||t.top>o.bottom||t.right<o.left){if(!0===e.hide)return e;e.hide=!0,e.attributes['x-out-of-boundaries']=''}else{if(!1===e.hide)return e;e.hide=!1,e.attributes['x-out-of-boundaries']=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var o=t.x,n=t.y,i=e.offsets.popper,r=C(e.instance.modifiers,function(e){return'applyStyle'===e.name}).gpuAcceleration;void 0!==r&&console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');var s,d,a=void 0===r?t.gpuAcceleration:r,l=p(e.instance.popper),f=u(l),m={position:i.position},h={left:$(i.left),top:Z(i.top),bottom:Z(i.bottom),right:$(i.right)},c='bottom'===o?'top':'bottom',g='right'===n?'left':'right',b=H('transform');if(d='bottom'==c?'HTML'===l.nodeName?-l.clientHeight+h.bottom:-f.height+h.bottom:h.top,s='right'==g?'HTML'===l.nodeName?-l.clientWidth+h.right:-f.width+h.right:h.left,a&&b)m[b]='translate3d('+s+'px, '+d+'px, 0)',m[c]=0,m[g]=0,m.willChange='transform';else{var y='bottom'==c?-1:1,w='right'==g?-1:1;m[c]=d*y,m[g]=s*w,m.willChange=c+', '+g}var E={"x-placement":e.placement};return e.attributes=le({},E,e.attributes),e.styles=le({},m,e.styles),e.arrowStyles=le({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:'bottom',y:'right'},applyStyle:{order:900,enabled:!0,fn:function(e){return j(e.instance.popper,e.styles),K(e.instance.popper,e.attributes),e.arrowElement&&Object.keys(e.arrowStyles).length&&j(e.arrowElement,e.arrowStyles),e},onLoad:function(e,t,o,n,i){var r=L(i,t,e,o.positionFixed),p=O(o.placement,r,t,e,o.modifiers.flip.boundariesElement,o.modifiers.flip.padding);return t.setAttribute('x-placement',p),j(t,{position:o.positionFixed?'fixed':'absolute'}),o},gpuAcceleration:void 0}}},ce});})(jQueryTheme);if(window.jQueryBackup){window.jQuery=window.jQueryBackup;window.jQueryBackup=undefined;}

(function($){

    'use strict';

    function Tooltip() {
        this.params = {
            size: 'small',
            arrow: true,
            animation: 'fade',
            inertia: false,
            duration: [200, 0],
            delay: 0,
            theme: 'named'
        };

        this.load();
    };

    Tooltip.prototype = $.extend({}, Tooltip.prototype, {
        load: function () {
            this.params = $.extend(this.params, {
                animation: window.theme.animations.tooltip.type,
                inertia: window.theme.animations.tooltip.inertia,
                touch: false
            });

            this.init();
        },
        init: function (obj) {
            this.params = $.extend(this.params, {
                duration: [window.theme.animations.tooltip.show_duration * 1000, window.theme.animations.tooltip.hide_duration * 1000]
            });

            if(obj) {
                this.params = $.extend(this.params, obj); 
            }

            this.api = tippy('[data-js-tooltip]', this.params);
        },
        reinit: function (obj) {
            this.destroy();
            this.init(obj);
        },
        destroy: function () {
            if(this.api) {
                this.api.destroyAll();
                this.api = null;
            }
        }
    });
    
    theme.AssetsLoader.onPageLoaded(function() {
        theme.Tooltip = new Tooltip;
    });
})(jQueryTheme);