import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function resetDatabase() {
  console.log('データベースのリセットを開始します...');

  try {
    // 依存関係のあるテーブルから順番に削除
    console.log('DeliveryScheduleテーブルをクリアしています...');
    await prisma.deliverySchedule.deleteMany({});

    console.log('Bookingテーブルをクリアしています...');
    await prisma.booking.deleteMany({});

    console.log('InventoryCalendarテーブルをクリアしています...');
    await prisma.inventoryCalendar.deleteMany({});

    console.log('RentalAvailabilityテーブルをクリアしています...');
    await prisma.rentalAvailability.deleteMany({});

    console.log('BlackoutDateテーブルをクリアしています...');
    await prisma.blackoutDate.deleteMany({});

    console.log('AvailableDatesテーブルをクリアしています...');
    await prisma.availableDates.deleteMany({});

    console.log('Maintenanceテーブルをクリアしています...');
    await prisma.maintenance.deleteMany({});

    console.log('Productテーブルをクリアしています...');
    await prisma.product.deleteMany({});

    console.log('Orderテーブルをクリアしています...');
    await prisma.order.deleteMany({});

    console.log('Invoiceテーブルをクリアしています...');
    await prisma.invoice.deleteMany({});

    console.log('ShippingCarrierテーブルをクリアしています...');
    await prisma.shippingCarrier.deleteMany({});

    console.log('ShippingFeeテーブルをクリアしています...');
    await prisma.shippingFee.deleteMany({});

    console.log('ShippingDestinationテーブルをクリアしています...');
    await prisma.shippingDestination.deleteMany({});

    console.log('Staffテーブルをクリアしています...');
    await prisma.staff.deleteMany({});

    console.log('SyncLogテーブルをクリアしています...');
    await prisma.syncLog.deleteMany({});

    console.log('AppSettingsテーブルをクリアしています...');
    await prisma.appSettings.deleteMany({});

    console.log('test_invoicesテーブルをクリアしています...');
    await prisma.test_invoices.deleteMany({});

    console.log('booking_status_changesテーブルをクリアしています...');
    await prisma.booking_status_changes.deleteMany({});

    // Sessionテーブルは残しておく（アプリの認証情報を保持するため）
    // console.log('Sessionテーブルをクリアしています...');
    // await prisma.session.deleteMany({});

    console.log('データベースのリセットが完了しました！');
  } catch (error) {
    console.error('データベースのリセット中にエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetDatabase();
