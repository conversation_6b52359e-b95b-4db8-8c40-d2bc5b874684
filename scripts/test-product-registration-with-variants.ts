/**
 * 商品登録 & バリエーション自動作成テストスクリプト
 *
 * CSVから数点の商品をShopifyに登録し、バリエーション自動作成機能をテストします
 */

import { authenticate } from '../app/shopify.server';
import { VariantAutoCreatorService } from '../app/services/shopify/variant-auto-creator.service';
import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

interface TestProduct {
  sku: string;
  title: string;
  price: number;
  description: string;
  location: string;
}

class ProductRegistrationTester {
  private variantCreator: VariantAutoCreatorService;
  private results: Array<{
    sku: string;
    title: string;
    shopifyProductId?: string;
    success: boolean;
    variantsCreated: number;
    errors: string[];
  }> = [];

  constructor() {
    this.variantCreator = VariantAutoCreatorService.getInstance();
  }

  /**
   * CSVから選択した商品をテスト登録
   */
  async testProductRegistration(): Promise<void> {
    console.log('=== 商品登録 & バリエーション自動作成テスト開始 ===\n');

    try {
      // 環境変数から設定を取得
      const shopifyShop = process.env.SHOPIFY_SHOP;
      const shopifyAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

      if (!shopifyShop || !shopifyAccessToken) {
        console.error('❌ 環境変数 SHOPIFY_SHOP または SHOPIFY_ADMIN_API_ACCESS_TOKEN が設定されていません');
        return;
      }

      console.log(`✅ Shopify設定取得成功: ${shopifyShop}`);

      // GraphQLClientを直接作成
      const graphqlClient = new GraphQLClient(
        `https://${shopifyShop}/admin/api/2025-01/graphql.json`,
        {
          headers: {
            'X-Shopify-Access-Token': shopifyAccessToken,
            'Content-Type': 'application/json',
          },
        }
      );

      // adminオブジェクトを作成
      const admin = {
        graphql: async (query: string, variables?: any) => {
          const result = await graphqlClient.request(query, variables);
          return {
            json: () => Promise.resolve({ data: result })
          };
        }
      };

      // テスト用商品を選択（CSVから抜粋）
      const testProducts: TestProduct[] = [
        {
          sku: '20101001-001',
          title: 'カクテルグラス クリアガラス H16',
          price: 300,
          description: 'カクテル用のクリアガラス製グラス。高さ16cm、直径10cm。',
          location: 'PR'
        },
        {
          sku: '20101002-001',
          title: 'デザートグラス クリアガラス H11',
          price: 300,
          description: 'デザート用のクリアガラス製グラス。高さ11cm、直径8cm。',
          location: 'PR'
        },
        {
          sku: '20101004-001',
          title: 'グラス（基本タイプ）',
          price: 500,
          description: '汎用性の高い基本的なグラス。様々な用途に使用可能。',
          location: 'NY'
        },
        {
          sku: '20101005-001',
          title: 'パフェグラス クリアガラス ストライプカット',
          price: 500,
          description: 'パフェ用のストライプカット入りクリアガラス製グラス。',
          location: 'PR'
        },
        {
          sku: '20101017-001',
          title: 'ワイングラス マスカット柄',
          price: 900,
          description: 'マスカット柄の装飾が施されたワイングラス。',
          location: 'NY'
        }
      ];

      console.log(`📦 テスト商品数: ${testProducts.length}\n`);

      // 各商品を登録してテスト
      for (const product of testProducts) {
        await this.registerAndTestProduct(admin, product, shopifyShop);

        // API制限を避けるため少し待機
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      this.displayResults();

    } catch (error) {
      console.error('❌ テスト実行エラー:', error);
    } finally {
      await prisma.$disconnect();
    }
  }

  /**
   * 単一商品を登録してテスト
   */
  private async registerAndTestProduct(admin: any, product: TestProduct, shopName: string): Promise<void> {
    console.log(`--- ${product.title} ---`);
    console.log(`SKU: ${product.sku}`);
    console.log(`価格: ¥${product.price.toLocaleString()}`);

    const result = {
      sku: product.sku,
      title: product.title,
      success: false,
      variantsCreated: 0,
      errors: []
    };

    try {
      // 1. Shopifyに商品を作成（バリエーションなし）
      console.log('1. Shopifyに商品を作成中...');

      const productResponse = await admin.graphql(`
        mutation productCreate($input: ProductInput!) {
          productCreate(input: $input) {
            product {
              id
              title
              handle
              variants(first: 10) {
                edges {
                  node {
                    id
                    title
                    price
                    sku
                  }
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `, {
        input: {
          title: product.title,
          descriptionHtml: product.description,
          vendor: 'レンタル商品',
          productType: 'キッチン用品',
          tags: ['テスト商品', 'グラス', 'キッチン']
        }
      });

      const productData = await productResponse.json();

      if (productData.errors) {
        throw new Error(`GraphQL エラー: ${JSON.stringify(productData.errors)}`);
      }

      if (productData.data.productCreate.userErrors.length > 0) {
        throw new Error(`商品作成エラー: ${JSON.stringify(productData.data.productCreate.userErrors)}`);
      }

      const createdProduct = productData.data.productCreate.product;
      const shopifyProductId = createdProduct.id.replace('gid://shopify/Product/', '');
      result.shopifyProductId = shopifyProductId;

      console.log(`✅ 商品作成成功: ${shopifyProductId}`);
      console.log(`初期バリエーション数: ${createdProduct.variants.edges.length}`);

      // 1.5. デフォルトバリエーションの価格とSKUを更新（スキップ）
      console.log('1.5. デフォルトバリエーション更新をスキップ（後でバリエーション自動作成で対応）');

      // 2. バリエーション自動作成をテスト
      console.log('2. バリエーション自動作成をテスト中...');

      const variantResult = await this.variantCreator.createMissingVariants(admin, shopifyProductId, {
        basePrice: product.price,
        createDays: [1, 2, 3, 4, 5, 6, 7],
        createProvisionalVariants: false,
        productStatus: 'available', // テスト商品はavailable
        location: product.location, // CSVから取得した場所
        sku: product.sku
      });

      result.variantsCreated = variantResult.createdVariants.length;

      if (variantResult.success) {
        console.log(`✅ バリエーション作成成功: ${variantResult.createdVariants.length}個`);

        variantResult.createdVariants.forEach(variant => {
          console.log(`  - ${variant.title}: ¥${variant.price.toLocaleString()}`);
        });

        console.log(`✅ メタフィールド設定: ${variantResult.metafieldsSet ? '成功' : '失敗'}`);
        console.log(`✅ 在庫更新: ${variantResult.inventoryUpdated ? '成功' : '失敗'}`);
      } else {
        console.log(`⚠️ バリエーション作成に問題: ${variantResult.errors.length}個のエラー`);
        result.errors.push(...variantResult.errors);
      }

      if (variantResult.skippedVariants.length > 0) {
        console.log(`⚠️ スキップされたバリエーション: ${variantResult.skippedVariants.length}個`);
        variantResult.skippedVariants.forEach(skipped => {
          console.log(`  - ${skipped.days}日: ${skipped.reason}`);
        });
      }

      // 3. 最終的なバリエーション数を確認
      console.log('3. 最終バリエーション数を確認中...');

      const finalProductResponse = await admin.graphql(`
        query getProduct($id: ID!) {
          product(id: $id) {
            variants(first: 20) {
              edges {
                node {
                  id
                  title
                  price
                  sku
                }
              }
            }
          }
        }
      `, {
        id: `gid://shopify/Product/${shopifyProductId}`
      });

      const finalProductData = await finalProductResponse.json();
      const finalVariants = finalProductData.data.product.variants.edges;

      console.log(`✅ 最終バリエーション数: ${finalVariants.length}`);
      console.log('バリエーション一覧:');
      finalVariants.forEach((edge: any, index: number) => {
        const variant = edge.node;
        console.log(`  ${index + 1}. ${variant.title}: ¥${parseFloat(variant.price).toLocaleString()} (SKU: ${variant.sku})`);
      });

      // 4. データベースに保存（Webhookの代わり）
      console.log('4. データベースに保存中...');

      await prisma.product.upsert({
        where: {
          shop_shopifyId: {
            shop: shopName,
            shopifyId: shopifyProductId
          }
        },
        update: {
          title: product.title,
          sku: product.sku,
          price: product.price,
          updatedAt: new Date()
        },
        create: {
          shopifyId: shopifyProductId,
          title: product.title,
          sku: product.sku,
          price: product.price,
          status: 'AVAILABLE',
          shop: shopName,
          locationId: product.location,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      console.log('✅ データベース保存完了');

      result.success = true;

    } catch (error) {
      console.error(`❌ エラー: ${error.message}`);
      result.errors.push(error.message);
    }

    this.results.push(result);
    console.log('');
  }

  /**
   * テスト結果を表示
   */
  private displayResults(): void {
    console.log('=== テスト結果サマリー ===');

    const totalProducts = this.results.length;
    const successProducts = this.results.filter(r => r.success).length;
    const totalVariantsCreated = this.results.reduce((sum, r) => sum + r.variantsCreated, 0);
    const errorProducts = this.results.filter(r => !r.success).length;

    console.log(`処理した商品数: ${totalProducts}`);
    console.log(`成功: ${successProducts}`);
    console.log(`失敗: ${errorProducts}`);
    console.log(`作成したバリエーション総数: ${totalVariantsCreated}`);

    console.log('\n詳細結果:');
    this.results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${result.title}`);
      console.log(`  SKU: ${result.sku}`);
      if (result.shopifyProductId) {
        console.log(`  Shopify ID: ${result.shopifyProductId}`);
      }
      console.log(`  作成バリエーション数: ${result.variantsCreated}`);
      if (result.errors.length > 0) {
        console.log(`  エラー: ${result.errors.join(', ')}`);
      }
    });

    if (successProducts > 0) {
      console.log('\n✅ テスト成功！バリエーション自動作成機能が正常に動作しています。');
      console.log('Shopify管理画面で作成された商品とバリエーションを確認してください。');
    } else {
      console.log('\n❌ テスト失敗。エラーを確認して修正してください。');
    }

    console.log('\n=== テスト完了 ===');
  }

  /**
   * 作成したテスト商品を削除
   */
  async cleanupTestProducts(): Promise<void> {
    console.log('=== テスト商品のクリーンアップ ===\n');

    try {
      // 環境変数から設定を取得
      const shopifyShop = process.env.SHOPIFY_SHOP;
      const shopifyAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

      if (!shopifyShop || !shopifyAccessToken) {
        console.error('❌ 環境変数が設定されていません');
        return;
      }

      // GraphQLClientを直接作成
      const graphqlClient = new GraphQLClient(
        `https://${shopifyShop}/admin/api/2025-01/graphql.json`,
        {
          headers: {
            'X-Shopify-Access-Token': shopifyAccessToken,
            'Content-Type': 'application/json',
          },
        }
      );

      // adminオブジェクトを作成
      const admin = {
        graphql: async (query: string, variables?: any) => {
          const result = await graphqlClient.request(query, variables);
          return {
            json: () => Promise.resolve({ data: result })
          };
        }
      };

      for (const result of this.results) {
        if (result.success && result.shopifyProductId) {
          try {
            console.log(`削除中: ${result.title} (${result.shopifyProductId})`);

            await admin.graphql(`
              mutation productDelete($input: ProductDeleteInput!) {
                productDelete(input: $input) {
                  deletedProductId
                  userErrors {
                    field
                    message
                  }
                }
              }
            `, {
              input: {
                id: `gid://shopify/Product/${result.shopifyProductId}`
              }
            });

            // データベースからも削除
            await prisma.product.deleteMany({
              where: {
                shopifyId: result.shopifyProductId
              }
            });

            console.log(`✅ 削除完了: ${result.title}`);

          } catch (error) {
            console.error(`❌ 削除エラー (${result.title}): ${error.message}`);
          }

          // API制限を避けるため少し待機
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      console.log('\n=== クリーンアップ完了 ===');

    } catch (error) {
      console.error('❌ クリーンアップエラー:', error);
    } finally {
      await prisma.$disconnect();
    }
  }
}

// メイン実行
async function main() {
  const tester = new ProductRegistrationTester();

  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'test':
      await tester.testProductRegistration();
      break;
    case 'cleanup':
      await tester.cleanupTestProducts();
      break;
    default:
      console.log('使用方法:');
      console.log('  npm run test-product-registration test     # 商品登録テスト実行');
      console.log('  npm run test-product-registration cleanup  # テスト商品削除');
      break;
  }
}

if (import.meta.url === new URL(import.meta.url).href) {
  main().catch(console.error);
}

export { ProductRegistrationTester };
