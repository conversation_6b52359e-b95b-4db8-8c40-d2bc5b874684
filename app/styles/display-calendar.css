/* DisplayCalendar用のグローバルCSS */

.display-calendar {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.display-calendar__title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

/* カレンダー凡例のスタイル */
.display-calendar__legend {
  margin-top: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.display-calendar__legend-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
}

.display-calendar__legend-color {
  width: 1rem;
  height: 1rem;
  border-radius: 2px;
}

/* 複数月カレンダーのスタイル */
.display-calendar__multi-month {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.display-calendar__month {
  flex: 1;
  min-width: 280px;
}

@media (max-width: 768px) {
  .display-calendar__multi-month {
    flex-direction: column;
  }
}

/* <PERSON><PERSON> DatePickerのカスタマイズ */
.Polaris-DatePicker {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

.Polaris-DatePicker__Month {
  width: 100%;
}

.Polaris-DatePicker__Day {
  position: relative;
  height: 36px;
  width: 36px;
  margin: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.Polaris-DatePicker__Day span {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
}

/* 日曜日のスタイル */
.Polaris-DatePicker__Day.display-calendar__cell--sunday {
  background-color: rgba(253, 201, 201, 0.3) !important;
  color: #bf0711 !important;
  opacity: 0.6 !important;
  position: relative !important;
}

.Polaris-DatePicker__Day.display-calendar__cell--sunday span {
  position: relative !important;
}

.Polaris-DatePicker__Day.display-calendar__cell--sunday span::after {
  content: "休" !important;
  position: absolute !important;
  top: 15px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  font-size: 0.6rem !important;
  color: #bf0711 !important;
  font-weight: normal !important;
  z-index: 10 !important;
  display: block !important;
}

/* 祝日のスタイル */
.Polaris-DatePicker__Day.display-calendar__cell--holiday {
  background-color: rgba(253, 201, 201, 0.3) !important;
  color: #bf0711 !important;
  opacity: 0.6 !important;
  position: relative !important;
}

.Polaris-DatePicker__Day.display-calendar__cell--holiday::before {
  content: "休" !important;
  position: absolute !important;
  top: 10px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  font-size: 0.6rem !important;
  color: #bf0711 !important;
  font-weight: normal !important;
  z-index: 10 !important;
}

/* 年末年始のスタイル */
.Polaris-DatePicker__Day.display-calendar__cell--newyear {
  background-color: rgba(253, 201, 201, 0.3) !important;
  color: #bf0711 !important;
  opacity: 0.6 !important;
  position: relative !important;
}

.Polaris-DatePicker__Day.display-calendar__cell--newyear::before {
  content: "休" !important;
  position: absolute !important;
  top: 10px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  font-size: 0.6rem !important;
  color: #bf0711 !important;
  font-weight: normal !important;
  z-index: 10 !important;
}

/* メンテナンス中のスタイル */
.Polaris-DatePicker__Day.display-calendar__cell--maintenance {
  background-color: rgba(171, 171, 171, 0.5) !important;
  color: #637381 !important;
  opacity: 0.8 !important;
  position: relative !important;
}

.Polaris-DatePicker__Day.display-calendar__cell--maintenance::before {
  content: "メ" !important;
  position: absolute !important;
  top: 10px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  font-size: 0.6rem !important;
  color: #637381 !important;
  font-weight: normal !important;
  z-index: 10 !important;
}

/* 仮予約のスタイル */
.Polaris-DatePicker__Day.display-calendar__cell--provisional {
  background-color: rgba(255, 184, 0, 0.2) !important;
  position: relative !important;
}

.Polaris-DatePicker__Day.display-calendar__cell--provisional::before {
  content: "仮" !important;
  position: absolute !important;
  top: 10px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  font-size: 0.6rem !important;
  color: #d82c0d !important;
  font-weight: normal !important;
  z-index: 10 !important;
}

/* 本予約のスタイル */
.Polaris-DatePicker__Day.display-calendar__cell--confirmed {
  background-color: rgba(0, 128, 96, 0.2) !important;
  position: relative !important;
}

.Polaris-DatePicker__Day.display-calendar__cell--confirmed::before {
  content: "予" !important;
  position: absolute !important;
  top: 10px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  font-size: 0.6rem !important;
  color: #00848e !important;
  font-weight: normal !important;
  z-index: 10 !important;
}

/* 日付マーカーのスタイル */
.display-calendar__date-marker {
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
}

/* マーカーの色 */
.display-calendar__marker--sunday {
  background-color: #bf0711;
}

.display-calendar__marker--holiday {
  background-color: #bf0711;
}

.display-calendar__marker--newyear {
  background-color: #bf0711;
}

.display-calendar__marker--provisional {
  background-color: #ffb800;
}

.display-calendar__marker--confirmed {
  background-color: #008060;
}

/* ホバー時のスタイル */
.Polaris-DatePicker__Day.display-calendar__cell--sunday:hover,
.Polaris-DatePicker__Day.display-calendar__cell--holiday:hover,
.Polaris-DatePicker__Day.display-calendar__cell--newyear:hover {
  background-color: rgba(253, 201, 201, 0.3) !important;
}

.Polaris-DatePicker__Day.display-calendar__cell--sunday:hover::before,
.Polaris-DatePicker__Day.display-calendar__cell--holiday:hover::before,
.Polaris-DatePicker__Day.display-calendar__cell--newyear:hover::before {
  content: "休" !important;
  display: block !important;
}

.Polaris-DatePicker__Day.display-calendar__cell--provisional:hover {
  background-color: rgba(255, 184, 0, 0.2) !important;
}

.Polaris-DatePicker__Day.display-calendar__cell--provisional:hover::before {
  content: "仮" !important;
  display: block !important;
}

.Polaris-DatePicker__Day.display-calendar__cell--confirmed:hover {
  background-color: rgba(0, 128, 96, 0.2) !important;
}

.Polaris-DatePicker__Day.display-calendar__cell--confirmed:hover::before {
  content: "予" !important;
  display: block !important;
}

.Polaris-DatePicker__Day.display-calendar__cell--maintenance:hover {
  background-color: rgba(171, 171, 171, 0.5) !important;
}

.Polaris-DatePicker__Day.display-calendar__cell--maintenance:hover::before {
  content: "メ" !important;
  display: block !important;
}

/* 属性セレクタを使用したスタイル */
.Polaris-DatePicker__Day[data-closed="true"] {
  background-color: rgba(253, 201, 201, 0.3) !important;
  color: #bf0711 !important;
  opacity: 0.6 !important;
  position: relative !important;
}

.Polaris-DatePicker__Day[data-closed="true"] span {
  position: relative !important;
}

.Polaris-DatePicker__Day[data-closed="true"] span::after {
  content: "休" !important;
  position: absolute !important;
  top: 15px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  font-size: 0.6rem !important;
  color: #bf0711 !important;
  font-weight: normal !important;
  z-index: 10 !important;
  display: block !important;
}

.Polaris-DatePicker__Day[data-provisional="true"] {
  background-color: rgba(255, 184, 0, 0.2) !important;
  position: relative !important;
}

.Polaris-DatePicker__Day[data-provisional="true"] span {
  position: relative !important;
}

.Polaris-DatePicker__Day[data-provisional="true"] span::after {
  content: "仮" !important;
  position: absolute !important;
  top: 15px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  font-size: 0.6rem !important;
  color: #d82c0d !important;
  font-weight: normal !important;
  z-index: 10 !important;
  display: block !important;
}

.Polaris-DatePicker__Day[data-confirmed="true"] {
  background-color: rgba(0, 128, 96, 0.2) !important;
  position: relative !important;
}

.Polaris-DatePicker__Day[data-confirmed="true"] span {
  position: relative !important;
}

.Polaris-DatePicker__Day[data-confirmed="true"] span::after {
  content: "予" !important;
  position: absolute !important;
  top: 15px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  font-size: 0.6rem !important;
  color: #00848e !important;
  font-weight: normal !important;
  z-index: 10 !important;
  display: block !important;
}

.Polaris-DatePicker__Day[data-maintenance="true"] {
  background-color: rgba(171, 171, 171, 0.5) !important;
  color: #637381 !important;
  opacity: 0.8 !important;
  position: relative !important;
}

.Polaris-DatePicker__Day[data-maintenance="true"] span {
  position: relative !important;
}

.Polaris-DatePicker__Day[data-maintenance="true"] span::after {
  content: "メ" !important;
  position: absolute !important;
  top: 15px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  font-size: 0.6rem !important;
  color: #637381 !important;
  font-weight: normal !important;
  z-index: 10 !important;
  display: block !important;
}
