# Shopify と Prisma の連携ガイド

このドキュメントは、Shopify と Prisma データベースの連携に関する情報をまとめたものです。テスト結果に基づいて、アプリケーションの制限事項や注意点を記載しています。

## 目次

1. [概要](#概要)
2. [連携の仕組み](#連携の仕組み)
3. [制限事項と注意点](#制限事項と注意点)
4. [テスト方法](#テスト方法)
5. [トラブルシューティング](#トラブルシューティング)

## 概要

このアプリケーションでは、Shopify の商品データと Prisma データベースを連携させ、レンタル予約システムを実現しています。主な連携ポイントは以下の通りです：

- Shopify の商品データを Prisma データベースに同期
- 予約データを Prisma データベースに保存
- 予約データと Shopify の注文データを関連付け

## 連携の仕組み

### 商品同期

1. Shopify Admin API を使用して商品データを取得
2. 取得したデータを Prisma データベースに保存
3. メタフィールドを使用して、レンタル固有の情報を保存

### 在庫管理

1. 商品の在庫状況を Prisma データベースで管理
2. 予約状況に基づいて在庫カレンダーを更新
3. 在庫カレンダーに基づいて、予約可能な日付を表示

### 予約と注文の連携

1. 予約データを Prisma データベースに保存
2. Shopify の注文データと予約データを関連付け
3. 注文ステータスに基づいて、予約ステータスを更新

## 制限事項と注意点

テスト結果に基づいて、以下の制限事項や注意点があります：

### Shopify API 連携

- **メタフィールドの型**: Shopify API でメタフィールドを作成する際は、`type: 'json'` を使用してください。`json_string` は非推奨です。
- **API アクセストークン**: 環境変数 `SHOPIFY_ADMIN_API_ACCESS_TOKEN` を設定する必要があります。
- **API レート制限**: Shopify API にはレート制限があります。短時間に多数のリクエストを送信すると、一時的にブロックされる可能性があります。

### 予約バリデーション

- **過去の日付**: 現在のバージョンでは、過去の日付での予約作成を防止するバリデーションが追加されています。
- **重複予約**: 同じ商品の同じ期間での重複予約を防止するバリデーションが追加されています。
- **バリデーションのスキップ**: テスト目的で `skipValidation` フラグを使用すると、バリデーションをスキップできますが、本番環境では使用しないでください。

### 商品バリエーション

- **バリエーション対応**: 商品バリエーション（サイズ、色など）に対応していますが、各バリエーションは独立した在庫として扱われます。
- **バリエーションの予約**: バリエーションごとに予約を作成する必要があります。複数のバリエーションを一度に予約することはできません。

## テスト方法

テストスクリプト `scripts/test-shopify-prisma-integration.ts` を使用して、Shopify と Prisma の連携をテストできます。

### 基本的な使い方

```bash
# 基本テスト
npm run test:shopify-prisma-integration

# クリーンアップあり
npm run test:shopify-prisma-integration -- --cleanup

# エラーケースのみ
npm run test:shopify-prisma-integration -- --error-cases

# バリエーションのみ
npm run test:shopify-prisma-integration -- --variants

# ローカルモード（Shopify API を使用しない）
npm run test:shopify-prisma-integration -- --local

# 特定の商品 ID を指定
npm run test:shopify-prisma-integration -- 123456789
```

### 環境設定

テスト環境の設定は、`.env.test` ファイルで行うことができます。以下は設定例です：

```
# Shopify API 設定
SHOPIFY_API_KEY=your_api_key
SHOPIFY_API_SECRET=your_api_secret
SHOPIFY_SHOP=your-shop.myshopify.com
SHOPIFY_ADMIN_API_ACCESS_TOKEN=your_access_token
SHOPIFY_API_VERSION=2024-01

# テスト設定
TEST_PRODUCT_ID=123456789
TEST_BOOKING_DAYS=3
TEST_CUSTOMER_EMAIL=<EMAIL>
TEST_CUSTOMER_NAME=テスト顧客
USE_LOCAL_MODE=false
```

## トラブルシューティング

### Shopify API エラー

- **認証エラー**: API キーとアクセストークンが正しく設定されているか確認してください。
- **メタフィールド型エラー**: メタフィールドの型が `json` になっているか確認してください。
- **レート制限**: API リクエストの頻度を下げるか、リトライ機能を使用してください。

### 予約エラー

- **バリデーションエラー**: 予約日付が過去の日付でないか、重複予約がないか確認してください。
- **在庫不足**: 商品の在庫状況を確認してください。
- **データベースエラー**: Prisma データベースの接続設定を確認してください。

### テストスクリプトエラー

- **環境変数エラー**: `.env.test` ファイルが正しく設定されているか確認してください。
- **API 接続エラー**: インターネット接続とファイアウォール設定を確認してください。
- **タイムアウト**: `config.testMode.timeoutMs` の値を増やしてください。

## 今後の改善点

1. **バリデーションの強化**: より厳密な予約バリデーションを実装する
2. **エラーハンドリングの改善**: より詳細なエラーメッセージを提供する
3. **パフォーマンスの最適化**: API 呼び出しの回数を減らす
4. **テストカバレッジの向上**: より多くのエッジケースをテストする
5. **ドキュメントの充実**: より詳細な使用方法と例を提供する
