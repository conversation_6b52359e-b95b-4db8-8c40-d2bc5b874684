# Shopify-Prisma同期実装状況

このドキュメントでは、ShopifyとPrismaデータベース間のデータ同期の現在の実装状況と改善点について説明します。

## 目次

1. [現在の実装状況](#現在の実装状況)
2. [問題点と課題](#問題点と課題)
3. [セキュリティとプライバシー対応状況](#セキュリティとプライバシー対応状況)
4. [改善提案](#改善提案)
5. [優先実装項目](#優先実装項目)
6. [テスト計画](#テスト計画)

## 現在の実装状況

### 実装済みの同期機能

| 機能 | 実装状況 | 実装ファイル | 備考 |
|-----|---------|------------|------|
| 商品作成Webhook | ✅ 実装済み | `app/routes/webhooks.products.jsx`<br>`app/routes/webhooks.products.create.tsx` | 商品作成時にPrismaに同期 |
| 商品更新Webhook | ✅ 実装済み | `app/routes/webhooks.products.jsx`<br>`app/routes/webhooks.products.update.tsx` | 商品更新時にPrismaに同期 |
| 商品削除Webhook | ❌ 未実装 | - | 商品削除時の処理が未実装 |
| 手動同期機能 | ✅ 実装済み | `app/services/sync/db-sync.service.ts` | 管理画面からの手動同期 |
| 定期同期ジョブ | ✅ 実装済み | `app/jobs/syncProducts.server.ts` | スケジュールされた同期ジョブ |
| 在庫同期 | ⚠️ 部分実装 | `app/services/sync/inventory-sync.service.ts` | 在庫データの同期 |
| メタフィールド同期 | ✅ 実装済み | `app/services/sync/metafield-sync.service.ts` | メタフィールドの同期 |
| 注文同期 | ✅ 実装済み | `app/routes/webhooks.orders.jsx` | 注文データの同期 |
| 顧客参照 | ⚠️ 部分実装 | `app/services/customer.service.ts` | 顧客IDによる参照 |
| 同期ログ記録 | ✅ 実装済み | `app/services/sync/db-sync.service.ts` | 同期結果のログ記録 |
| 同期エラー処理 | ⚠️ 部分実装 | `app/services/error-handling.service.ts` | エラー処理と再試行 |

### 同期テスト機能

| テスト機能 | 実装状況 | 実装ファイル | 備考 |
|----------|---------|------------|------|
| 商品同期テスト | ✅ 実装済み | `scripts/test-shopify-prisma-integration.ts` | 商品同期の総合テスト |
| バリエーションテスト | ✅ 実装済み | `scripts/test-shopify-prisma-integration.ts` | 商品バリエーションのテスト |
| エラーケーステスト | ✅ 実装済み | `scripts/test-shopify-prisma-integration.ts` | エラー処理のテスト |
| 個別商品同期テスト | ✅ 実装済み | `scripts/sync-product-to-db.ts` | 個別商品の同期テスト |

## 問題点と課題

### 1. 同期タイミングの問題

- **Webhookの信頼性**: Shopify Webhookが失敗または遅延する可能性がある
- **同期漏れ**: 一部の商品や更新が同期されない場合がある
- **商品削除の検出**: 商品削除Webhookが未実装で、削除された商品が検出できない

### 2. データ整合性の問題

- **SKUの一意性**: ShopifyでSKUが変更された場合、Prismaでの照合が困難
- **バリエーション同期**: 商品バリエーションの同期が不完全
- **メタフィールド形式**: メタフィールドの形式変換に問題がある場合がある

### 3. パフォーマンスの問題

- **同期処理の遅延**: 大量の商品を同期する際のパフォーマンス問題
- **API制限**: Shopify API制限に達する可能性
- **同時実行**: 複数の同期処理が同時に実行される場合の競合

### 4. セキュリティとプライバシーの問題

- **顧客データの過剰同期**: 必要以上の顧客情報がPrismaに保存されている
- **支払い情報の保護**: 支払い情報の適切な保護が不十分
- **アクセス制御**: 同期データへのアクセス制御が不十分

## セキュリティとプライバシー対応状況

### 顧客データの最小化

| 対応項目 | 実装状況 | 備考 |
|---------|---------|------|
| 顧客IDのみの保存 | ⚠️ 部分実装 | 一部の顧客情報が不要にPrismaに保存されている |
| 必要時のみAPI参照 | ⚠️ 部分実装 | オンデマンド参照の仕組みが不完全 |
| 個人情報のマスキング | ❌ 未実装 | 管理画面での個人情報マスキングが未実装 |

### 支払い情報の保護

| 対応項目 | 実装状況 | 備考 |
|---------|---------|------|
| ステータスのみ同期 | ⚠️ 部分実装 | 一部の支払い詳細が同期されている |
| 決済詳細の非同期 | ⚠️ 部分実装 | 決済詳細の保存制限が不完全 |
| トークン参照 | ❌ 未実装 | トークンベースの参照が未実装 |

### データアクセス制御

| 対応項目 | 実装状況 | 備考 |
|---------|---------|------|
| ロールベースアクセス | ⚠️ 部分実装 | 基本的なロール制御のみ実装 |
| 監査ログ | ❌ 未実装 | データアクセスの監査ログが未実装 |
| データ暗号化 | ❌ 未実装 | 保存データの暗号化が未実装 |

## 改善提案

### 1. 同期タイミングの改善

- **Webhookバックアップ**: Webhookが失敗した場合のバックアップメカニズムの実装
- **商品削除Webhook**: 商品削除Webhookの実装と処理
- **定期的な完全同期**: すべての商品を定期的に完全同期する機能の強化

### 2. データ整合性の改善

- **複数の照合方法**: ShopifyID、SKU、タイトルなど複数の方法による照合
- **元のSKU保存**: 元のSKUを別フィールドに保存し、照合に使用
- **バリエーション同期強化**: バリエーション同期の完全実装
- **メタフィールド検証**: メタフィールドの形式検証と修正

### 3. パフォーマンスの改善

- **バッチ処理**: 同期処理のバッチ化
- **差分同期**: 変更されたフィールドのみを更新
- **API最適化**: API呼び出しの最適化とキャッシュ
- **同期キュー**: 優先度ベースの同期キューの実装

### 4. セキュリティとプライバシーの改善

- **顧客データ最小化**: 顧客IDのみを保存し、他の情報はオンデマンドで取得
- **支払い情報保護強化**: 支払い状態のみを同期し、詳細情報は参照のみ
- **アクセス制御強化**: ロールベースのアクセス制御と監査ログの実装
- **データ暗号化**: 保存データの暗号化（特に機密情報）

## 優先実装項目

以下の項目を優先的に実装することを提案します：

1. **不要データ同期の削減**
   - 商品詳細、画像URL、コレクション情報など不要なデータの同期を停止
   - 必要最小限のデータ（ID、タイトル、SKU、ステータス）のみを同期する仕組みへの移行

2. **顧客データ最小化の実装**
   - 既存の顧客データをIDのみに削減
   - 顧客情報参照サービスの実装
   - 個人情報表示時のマスキング機能

3. **商品削除Webhookの実装**
   - 商品がShopifyで削除された時の処理を実装
   - 削除された商品を適切に処理（論理削除または物理削除）

4. **元のSKU保存機能**
   - ShopifyでSKUが変更されても元のSKUを保持
   - 元のSKUを使用した照合機能の実装

5. **同期エラー通知システム**
   - 重大な同期エラーが発生した場合の通知
   - エラーダッシュボードの実装

6. **オンデマンド参照の実装**
   - 必要時のみShopify APIを呼び出す効率的な仕組みの実装
   - API呼び出し回数の最適化

## テスト計画

### 1. 単体テスト

- **同期サービスのテスト**: 各同期サービスの単体テスト
- **Webhookハンドラーのテスト**: Webhookハンドラーの単体テスト
- **エラー処理のテスト**: エラー処理と再試行メカニズムのテスト
- **セキュリティ機能のテスト**: データ保護機能のテスト

### 2. 統合テスト

- **エンドツーエンドテスト**: Shopifyから商品作成→Prisma同期→予約作成のフロー
- **同期フローテスト**: 各同期フローの統合テスト
- **エラーケーステスト**: 様々なエラーケースのテスト
- **プライバシー保護テスト**: 顧客データ保護の検証

### 3. 負荷テスト

- **大量データテスト**: 大量の商品データでの同期テスト
- **並行処理テスト**: 複数の同期処理が同時に実行される場合のテスト
- **長期安定性テスト**: 長期間の同期処理の安定性テスト

### 4. セキュリティテスト

- **アクセス制御テスト**: 権限に基づくアクセス制御のテスト
- **データ保護テスト**: 機密データの保護メカニズムのテスト
- **監査ログテスト**: データアクセスログの検証
