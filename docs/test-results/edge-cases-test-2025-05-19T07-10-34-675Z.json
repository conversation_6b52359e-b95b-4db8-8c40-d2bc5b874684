{"timestamp": "2025-05-19T07:10:34.675Z", "duration": 25.162, "totalTests": 10, "successCount": 7, "failureCount": 3, "tests": [{"testName": "年末年始の予約テスト", "success": true, "timestamp": "2025-05-19T07:10:40.064Z", "message": "年末年始の予約が正常に作成されました", "error": "", "duration": 4509, "details": {"bookingId": "89708220-bb9b-4364-ad90-949043969e72", "period": "年末年始", "startDate": "2025-12-29", "endDate": "2026-01-03"}}, {"testName": "予約期間の最小値テスト", "success": true, "timestamp": "2025-05-19T07:10:41.049Z", "message": "最小予約期間（1日）の予約が正常に作成されました", "error": "", "duration": 985, "details": {"bookingId": "e371aab9-1d69-45bf-a6f3-f5e60c96b34e", "durationDays": 1}}, {"testName": "予約期間の最大値テスト", "success": true, "timestamp": "2025-05-19T07:10:55.103Z", "message": "最大予約期間（30日）の予約が正常に作成されました", "error": "", "duration": 14054, "details": {"bookingId": "355087fe-fb4a-49fc-87f1-7548fc512393", "durationDays": 30}}, {"testName": "過去の日付での予約テスト", "success": true, "timestamp": "2025-05-19T07:10:58.462Z", "message": "過去の日付での予約は正しくエラーになりました", "error": "過去の日付での予約がエラーにならずに成功してしまいました", "duration": 2333, "details": {"expectedError": true}}, {"testName": "終了日が開始日より前の予約テスト", "success": true, "timestamp": "2025-05-19T07:10:58.918Z", "message": "終了日が開始日より前の予約は正しくエラーになりました", "error": "終了日が開始日より前の予約がエラーにならずに成功してしまいました", "duration": 456, "details": {"expectedError": true}}, {"testName": "無効なメールアドレスでの予約テスト", "success": true, "timestamp": "2025-05-19T07:10:59.144Z", "message": "無効なメールアドレスでの予約は正しくエラーになりました", "error": "", "duration": 226, "details": {"expectedError": true, "invalidEmail": "invalid-email"}}, {"testName": "在庫をゼロにするテスト", "success": false, "timestamp": "2025-05-19T07:10:59.377Z", "message": "在庫をゼロにすることに失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-08, 2025-06-09, 2025-06-10, 2025-06-11", "duration": 233, "details": {"bookingId": ""}}, {"testName": "在庫がゼロの状態で予約を試みるテスト", "success": true, "timestamp": "2025-05-19T07:10:59.600Z", "message": "在庫がゼロの状態での予約は正しくエラーになりました", "error": "", "duration": 223, "details": {"expectedError": true}}, {"testName": "予約をキャンセルして在庫を回復するテスト", "success": false, "timestamp": "2025-05-19T07:10:59.600Z", "message": "予約をキャンセルして在庫を回復することに失敗しました", "error": "テスト1で作成した予約IDがありません", "duration": 0, "details": {"bookingId": ""}}, {"testName": "在庫が回復した状態で予約を試みるテスト", "success": false, "timestamp": "2025-05-19T07:10:59.837Z", "message": "在庫が回復した状態での予約に失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-08, 2025-06-09, 2025-06-10, 2025-06-11", "duration": 237, "details": {"bookingId": ""}}], "environment": {"nodeVersion": "v20.18.1", "os": "darwin arm64"}, "performance": {"averageTestDuration": 2325.6, "slowestTest": {"name": "予約期間の最大値テスト", "duration": 14054}, "fastestTest": {"name": "予約をキャンセルして在庫を回復するテスト", "duration": 0}}}