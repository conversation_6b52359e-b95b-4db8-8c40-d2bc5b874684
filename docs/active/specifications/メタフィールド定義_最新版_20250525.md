# メタフィールド定義 最新版

**更新日**: 2025年5月25日  
**バージョン**: 2.0  
**目的**: 実装に基づいた最新のメタフィールド定義と使用ガイド

## 📋 概要

本ドキュメントは、レンタル商品管理システムで使用するShopifyメタフィールドの最新定義をまとめたものです。
実装状況と運用の実態に基づいて整理・最適化されています。

## 🏗️ メタフィールド体系

### 命名規則
- **名前空間**: `rental`（レンタル関連）、`product`（商品基本情報）
- **キー**: snake_case形式（例: `product_group`, `basic_info`）
- **タイプ**: Shopifyの標準タイプを使用

## 📊 メタフィールド定義一覧

### 1. 必須メタフィールド（システム管理）

| 定義名 | パスキー | タイプ | 用途 | 自動設定 |
|:--|:--|:--|:--|:--:|
| **レンタル商品基本情報** | `rental.basic_info` | json | 商品コード、詳細コード等の基本データ | ✅ |
| **レンタル料金設定** | `rental.pricing` | json | 日数別価格計算用データ | ✅ |
| **バリエーションマッピング** | `rental.variant_mapping` | json | バリアントとレンタル期間の紐付け | ✅ |
| **バリエーションタイプ** | `rental.variation_type` | single_line_text_field | バリエーション種類（固定値: rental_period） | ✅ |

### 2. 商品管理用メタフィールド（スタッフ編集可）

| 定義名 | パスキー | タイプ | 用途 | 編集画面 |
|:--|:--|:--|:--|:--:|
| **商品グループ** | `rental.product_group` | single_line_text_field | 同じ形状・デザインの商品をグループ化 | ✅ |
| **レンタル状態** | `rental.status` | single_line_text_field | 商品の利用可能状態 | ✅ |
| **在庫場所** | `rental.location` | single_line_text_field | 保管場所（NY, 外部倉庫等） | ✅ |
| **色** | `rental.color` | single_line_text_field | 商品の色情報 | ✅ |
| **素材** | `rental.material` | single_line_text_field | 商品の素材情報 | ✅ |

### 3. 商品サイズ情報

| 定義名 | パスキー | タイプ | 用途 | 編集画面 |
|:--|:--|:--|:--|:--:|
| **高さ** | `product.height` | number_integer | 商品の高さ（cm） | ✅ |
| **幅** | `product.width` | number_integer | 商品の幅（cm） | ✅ |
| **奥行き** | `product.depth` | number_integer | 商品の奥行き（cm） | ✅ |

### 4. 備考・メモフィールド

| 定義名 | パスキー | タイプ | 用途 | 編集画面 |
|:--|:--|:--|:--|:--:|
| **メンテナンス備考** | `rental.maintenance_notes` | multi_line_text_field | メンテナンス履歴・状態記録 | ✅ |
| **予約備考** | `rental.booking_notes` | multi_line_text_field | 予約時の注意事項・配送情報 | ✅ |
| **一般備考** | `rental.general_notes` | multi_line_text_field | 購入情報・廃棄情報等 | ✅ |

## 🔧 メタフィールドの詳細仕様

### 1. rental.basic_info（レンタル商品基本情報）
```json
{
  "productCode": "10101031",     // 商品コード（6桁）
  "detailCode": "001",           // 詳細コード（3桁）
  "categoryId": 3,               // カテゴリID
  "categoryName": "ソファ",      // カテゴリ名
  "itemNumber": 1                // アイテム番号
}
```

### 2. rental.pricing（レンタル料金設定）
```json
{
  "basePrice": 8000,             // 基本価格（1日料金）
  "extraDayRate": 0.2            // 追加日料金率（20%）
}
```

### 3. rental.variant_mapping（バリエーションマッピング）
```json
{
  "variants": [
    {
      "variantId": "gid://shopify/ProductVariant/xxx",
      "rentalDays": 1,
      "title": "1日レンタル",
      "sku": "10101031-001-1D",
      "price": 8000
    },
    {
      "variantId": "gid://shopify/ProductVariant/yyy",
      "rentalDays": 2,
      "title": "2日レンタル",
      "sku": "10101031-001-2D",
      "price": 9600
    },
    {
      "variantId": "gid://shopify/ProductVariant/zzz",
      "rentalDays": 0,
      "title": "仮予約",
      "sku": "10101031-001-PROV",
      "price": 0
    }
  ]
}
```

### 4. rental.status（レンタル状態）の値
- `available` - 利用可能
- `maintenance` - メンテナンス中
- `reserved` - 予約済み
- `damaged` - 破損
- `disposed` - 廃棄済み

### 5. rental.general_notes（一般備考）の記載例
```
購入情報:
購入日: 2020/01/01
購入場所: 東京ショールーム
購入価格: 50,000円

履歴:
2024/12/15 - クリーニング実施
2025/01/10 - 脚部修理

廃棄情報:
廃棄日: 2025/05/01
廃棄理由: 経年劣化により使用不可
```

## 🚀 実装での使用方法

### 商品登録時の自動設定（scripts/create-complete-product-from-data.ts）
```typescript
// メタフィールドの自動設定
const metafields = [
  {
    namespace: "rental",
    key: "basic_info",
    type: "json",
    value: JSON.stringify({
      productCode: data.productCode,
      detailCode: data.detailCode,
      categoryId: data.categoryId,
      categoryName: data.categoryName,
      itemNumber: data.itemNumber
    })
  },
  {
    namespace: "rental",
    key: "pricing",
    type: "json",
    value: JSON.stringify({
      basePrice: data.price,
      extraDayRate: 0.2
    })
  },
  {
    namespace: "rental",
    key: "variation_type",
    type: "single_line_text_field",
    value: "rental_period"
  }
];
```

### 商品検索での活用（app/services/booking-validation.service.ts）
```typescript
// 商品グループで関連商品を検索
const relatedProducts = await prisma.product.findMany({
  where: {
    shop: session.shop,
    metadata: {
      path: ['rental', 'product_group'],
      equals: targetProductGroup
    }
  }
});
```

### 価格計算での使用（app/services/pricing/unified-pricing.service.ts）
```typescript
// メタフィールドから価格情報を取得
const pricingData = product.metadata?.rental?.pricing;
const basePrice = pricingData?.basePrice || 0;
const extraDayRate = pricingData?.extraDayRate || 0.2;

// レンタル日数に応じた価格計算
const calculatePrice = (days: number): number => {
  if (days === 1) return basePrice;
  return Math.round(basePrice * (1 + (days - 1) * extraDayRate));
};
```

## 📝 運用ガイドライン

### 1. 新商品登録フロー
1. CSVでの一括登録 → システムが必須メタフィールドを自動設定
2. 管理画面で商品編集 → スタッフが商品グループ等を追加入力
3. バリアント作成 → システムが variant_mapping を自動更新

### 2. 商品グループの活用
- 同じデザイン・形状の商品には同じ商品グループ名を設定
- 例: "ベーシックソファ 1シーター", "カリモクソファ", "ホガースチェア"
- グループ単位での在庫確認や関連商品表示に活用

### 3. ステータス管理
- `rental.status` で商品の現在状態を管理
- メンテナンス時は `maintenance` に変更
- 廃棄時は `disposed` に変更し、詳細を `general_notes` に記録

### 4. メタフィールドの編集権限
- **システム管理フィールド**: 自動更新のみ（手動編集不可）
- **スタッフ編集可能フィールド**: Shopify管理画面から編集可能

## 🔄 今後の拡張予定

1. **在庫管理の高度化**
   - 複数拠点の在庫管理対応
   - リアルタイム在庫同期

2. **グループ管理の強化**
   - グループ単位での価格設定
   - グループ内商品の自動切り替え

3. **メタフィールドの最適化**
   - 使用頻度の低いフィールドの整理
   - パフォーマンス改善

## 📚 関連ドキュメント

- [SKU体系とバリアント対応](./SKU体系とバリアント対応_20250525.md)
- [商品表示・価格計算修正](./商品表示・価格計算修正_20250117.md)
- [Shopify商品同期仕様](../../../shopify-prisma-integration-guide.md)

---

本ドキュメントは実装の進化に合わせて継続的に更新されます。
最終更新: 2025年5月25日