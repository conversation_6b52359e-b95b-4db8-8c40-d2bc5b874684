/**
 * 予約情報のメタフィールド更新ユーティリティ
 *
 * このモジュールは、予約情報をShopifyメタフィールドに更新するための関数を提供します。
 */

import { Booking, PrismaClient } from '@prisma/client';
import { format } from 'date-fns';
import { handleError, retry } from './error-handler';
import { AdminApiContext } from '@shopify/shopify-app-remix/server';

// メタフィールドを更新するミューテーション
const UPDATE_METAFIELD = `
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールドを取得するクエリ
const GET_METAFIELD = `
  query getMetafield($ownerId: ID!, $namespace: String!, $key: String!) {
    product(id: $ownerId) {
      metafield(namespace: $namespace, key: $key) {
        id
        namespace
        key
        value
      }
    }
  }
`;

// GraphQLクライアント関数は削除し、代わりにShopify adminオブジェクトを使用

/**
 * 予約情報をメタフィールド用のフォーマットに変換する関数
 * @param bookings 予約情報
 * @returns メタフィールド用の予約情報
 */
function formatBookingsForMetafield(bookings: Booking[]): any {
  // バリアント別に予約をグループ化
  const bookingsByVariant: Record<string, any[]> = {};

  bookings.forEach(booking => {
    const variantId = booking.variantId || 'default';
    if (!bookingsByVariant[variantId]) {
      bookingsByVariant[variantId] = [];
    }

    bookingsByVariant[variantId].push({
      id: booking.bookingId,
      startDate: format(booking.startDate, 'yyyy-MM-dd'),
      endDate: format(booking.endDate, 'yyyy-MM-dd'),
      status: booking.status,
      type: booking.bookingType,
      customerName: booking.customerName,
      customerEmail: booking.customerEmail
    });
  });

  // バリアント情報を構築
  const variants: Record<string, any> = {};

  Object.entries(bookingsByVariant).forEach(([variantId, variantBookings]) => {
    variants[variantId] = {
      status: variantBookings.some(b => b.status === 'CONFIRMED') ? 'unavailable' : 'available',
      reservations: variantBookings
    };
  });

  // 従来の形式との互換性のために、すべての予約も含める
  const allBookings = bookings.map(booking => ({
    id: booking.bookingId,
    startDate: format(booking.startDate, 'yyyy-MM-dd'),
    endDate: format(booking.endDate, 'yyyy-MM-dd'),
    status: booking.status,
    customerName: booking.customerName,
    customerEmail: booking.customerEmail,
    variantId: booking.variantId
  }));

  // メタフィールド用のデータ構造
  const metafieldData = {
    // 従来の形式との互換性のために残す
    bookings: allBookings,
    // 新しい構造化データ
    status: Object.values(variants).some((v: any) => v.status === 'unavailable') ? 'unavailable' : 'available',
    lastUpdated: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
    variants,
    availability: {
      rentalStatus: 'available',
      startDate: format(new Date(), 'yyyy-MM-dd'),
      maintenanceDates: [],
      blockedDates: []
    }
  };

  return metafieldData;
}

/**
 * 現在のメタフィールド値を取得する関数
 * @param admin Shopify管理APIコンテキスト
 * @param productId 商品ID
 * @returns 現在のメタフィールド値
 */
async function getCurrentMetafieldValue(
  admin: AdminApiContext,
  productId: string
): Promise<any> {
  try {
    // Shopify IDを正規化
    const shopifyId = productId.startsWith('gid://shopify/Product/')
      ? productId
      : `gid://shopify/Product/${productId}`;

    // メタフィールドを取得
    const response = await admin.graphql(
      GET_METAFIELD,
      {
        variables: {
          ownerId: shopifyId,
          namespace: 'rental',
          key: 'bookings'
        }
      }
    );

    const result = await response.json();

    if (result.data?.product?.metafield) {
      return JSON.parse(result.data.product.metafield.value);
    }

    return null;
  } catch (error) {
    console.error('メタフィールドの取得中にエラーが発生しました:', error);
    return null;
  }
}

/**
 * メタフィールド値が変更されたかどうかを判定する関数
 * @param currentValue 現在の値
 * @param newValue 新しい値
 * @returns 変更されたかどうか
 */
function hasMetafieldValueChanged(currentValue: any, newValue: any): boolean {
  if (!currentValue) return true;

  // bookingsの比較
  const currentBookings = currentValue.bookings || [];
  const newBookings = newValue.bookings || [];

  if (currentBookings.length !== newBookings.length) return true;

  // 予約IDでソート
  const sortedCurrentBookings = [...currentBookings].sort((a, b) => a.id.localeCompare(b.id));
  const sortedNewBookings = [...newBookings].sort((a, b) => a.id.localeCompare(b.id));

  // 各予約を比較
  for (let i = 0; i < sortedCurrentBookings.length; i++) {
    const current = sortedCurrentBookings[i];
    const next = sortedNewBookings[i];

    if (
      current.id !== next.id ||
      current.startDate !== next.startDate ||
      current.endDate !== next.endDate ||
      current.status !== next.status
    ) {
      return true;
    }
  }

  return false;
}

/**
 * 予約情報をShopifyメタフィールドに更新する関数
 * @param prisma Prismaクライアント
 * @param admin Shopify管理APIコンテキスト
 * @param productId 商品ID
 * @param forceUpdate 強制更新するかどうか
 * @param retryCount リトライ回数
 * @returns 更新結果
 */
export async function updateBookingMetafield(
  prisma: PrismaClient,
  admin: AdminApiContext,
  productId: string,
  forceUpdate: boolean = false,
  retryCount: number = 3
): Promise<any> {
  try {
    console.log('予約情報メタフィールドを更新中...');

    // Shopify IDを正規化
    const shopifyId = productId.startsWith('gid://shopify/Product/')
      ? productId
      : `gid://shopify/Product/${productId}`;

    // 商品の全予約情報を取得（有効な予約のみ）
    const bookings = await prisma.booking.findMany({
      where: {
        productId,
        status: {
          in: ['PROVISIONAL', 'CONFIRMED']
        }
      },
      orderBy: {
        startDate: 'asc'
      }
    });

    console.log(`予約情報を取得しました: ${bookings.length}件`);

    // 予約情報をメタフィールド用のフォーマットに変換
    const metafieldData = formatBookingsForMetafield(bookings);

    // 現在のメタフィールド値を取得
    const currentMetafieldValue = forceUpdate ? null : await getCurrentMetafieldValue(admin, shopifyId);

    // 値が変更されていない場合は更新しない
    if (!forceUpdate && !hasMetafieldValueChanged(currentMetafieldValue, metafieldData)) {
      console.log('メタフィールドの値に変更がないため、更新をスキップします');
      return { success: true, updated: false };
    }

    // メタフィールドの設定（リトライ機能付き）
    console.log('メタフィールド更新を実行します:');
    console.log('- shopifyId:', shopifyId);
    console.log('- namespace:', 'rental');
    console.log('- key:', 'bookings');
    console.log('- metafieldData:', JSON.stringify(metafieldData, null, 2));

    const result = await retry(
      async () => {
        console.log('GraphQL呼び出しを実行します...');
        try {
          console.log('リクエスト内容:');
          console.log('- ミューテーション:', UPDATE_METAFIELD);
          console.log('- 変数:', JSON.stringify({
            metafields: [
              {
                ownerId: shopifyId,
                namespace: 'rental',
                key: 'bookings',
                value: JSON.stringify(metafieldData),
                type: 'json'
              }
            ]
          }, null, 2));

          const response = await admin.graphql(
            UPDATE_METAFIELD,
            {
              variables: {
                metafields: [
                  {
                    ownerId: shopifyId,
                    namespace: 'rental',
                    key: 'bookings',
                    value: JSON.stringify(metafieldData),
                    type: 'json'
                  }
                ]
              }
            }
          );

          console.log('GraphQL応答を受信しました');
          const updateResult = await response.json();
          console.log('GraphQL応答内容:', JSON.stringify(updateResult, null, 2));

          if (updateResult.errors) {
            console.error('GraphQLエラー:', updateResult.errors);
            throw new Error(`GraphQLエラー: ${JSON.stringify(updateResult.errors)}`);
          }

          if (updateResult.data?.metafieldsSet?.userErrors?.length > 0) {
            console.error('メタフィールド更新エラー:', updateResult.data.metafieldsSet.userErrors);
            throw new Error(`メタフィールドの設定中にエラーが発生しました: ${JSON.stringify(updateResult.data.metafieldsSet.userErrors)}`);
          }

          return updateResult.data;
        } catch (graphqlError) {
          console.error('GraphQL呼び出しエラー:', graphqlError);
          if (graphqlError instanceof Error) {
            console.error('エラーメッセージ:', graphqlError.message);
            console.error('スタックトレース:', graphqlError.stack);
          }
          throw graphqlError;
        }
      },
      retryCount,
      1000, // 1秒後にリトライ
      2, // 指数バックオフ（1秒、2秒、4秒...）
      async (error, attempt) => {
        // 特定のエラーの場合はリトライしない
        if (error.message) {
          // 認証エラー
          if (error.message.includes('Unauthorized') || error.message.includes('401')) {
            console.error('認証エラーが発生しました。リトライをスキップします。');
            return false;
          }

          // 無効なIDエラー
          if (error.message.includes('Invalid global id')) {
            console.error('無効なIDが指定されました。リトライをスキップします。');
            return false;
          }

          // リソースが見つからないエラー
          if (error.message.includes('not found') || error.message.includes('404')) {
            console.error('リソースが見つかりません。リトライをスキップします。');
            return false;
          }
        }

        // その他のエラーはリトライする
        return true;
      }
    );

    console.log('予約情報メタフィールドを更新しました');

    // 更新成功
    return {
      success: true,
      updated: true,
      metafield: result?.metafieldsSet?.metafields?.[0] || null
    };
  } catch (error) {
    // 詳細なエラー情報を記録
    handleError(error, {
      operation: 'updateBookingMetafield',
      productId,
      forceUpdate,
      retryCount
    }, true); // エラー通知を送信

    throw error;
  }
}
