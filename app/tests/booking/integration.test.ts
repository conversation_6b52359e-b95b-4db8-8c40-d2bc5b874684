/**
 * 統合テスト - 実際のデータベースを使用
 *
 * このテストは以下の機能をテストします：
 * 1. データベースの実際の構造とPrismaスキーマの整合性
 * 2. 実際のデータを使用した予約プロセス
 * 3. Shopify連携の動作確認
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

describe('統合テスト - データベース連携', () => {
  let testProduct: any;
  let testBookings: any[] = [];

  beforeAll(async () => {
    try {
      // 既存のテスト商品に関連する予約を削除
      const existingProduct = await prisma.product.findFirst({
        where: { shopifyId: 'integration-test-product' }
      });

      if (existingProduct) {
        await prisma.booking.deleteMany({
          where: { productId: existingProduct.id }
        });

        await prisma.product.delete({
          where: { id: existingProduct.id }
        });
      }

      // テスト用商品を作成
      testProduct = await prisma.product.create({
        data: {
          title: 'テスト統合商品',
          description: '統合テスト用の商品です',
          price: 10000,
          shop: 'test-shop',
          shopifyId: 'integration-test-product',
          sku: 'INT-TEST-001',
          status: 'AVAILABLE',
        },
      });
    } catch (error) {
      console.error('テスト準備エラー:', error);
      throw error;
    }
  });

  afterAll(async () => {
    try {
      // テストデータをクリーンアップ（外部キー制約を考慮して順序を調整）

      // 1. まず、テスト商品に関連するすべての予約を削除
      if (testProduct) {
        await prisma.booking.deleteMany({
          where: {
            productId: testProduct.id
          }
        });
      }

      // 2. 明示的に作成したテスト予約も削除
      if (testBookings.length > 0) {
        await prisma.booking.deleteMany({
          where: {
            id: { in: testBookings.map(b => b.id) }
          }
        });
      }

      // 3. 最後に商品を削除
      if (testProduct) {
        await prisma.product.delete({
          where: { id: testProduct.id }
        });
      }

      console.log('テストデータのクリーンアップが完了しました');
    } catch (error) {
      console.error('テストクリーンアップエラー:', error);
      // エラーが発生してもテストを失敗させない
    } finally {
      await prisma.$disconnect();
    }
  });

  beforeEach(() => {
    // 各テスト前にテストデータをリセット
    testBookings = [];
  });

  describe('Prismaスキーマ整合性', () => {
    it('shopifyOrderIdとshopifyOrderNameフィールドが存在する', async () => {
      const booking = await prisma.booking.create({
        data: {
          bookingId: 'integration-test-booking-1',
          productId: testProduct.id,
          startDate: new Date('2025-06-01'),
          endDate: new Date('2025-06-03'),
          customerName: '統合テスト顧客',
          customerEmail: '<EMAIL>',
          status: 'CONFIRMED',
          shop: 'test-shop',
          shopifyOrderId: 'test-order-123',
          shopifyOrderName: '#TEST-001',
        },
      });

      testBookings.push(booking);

      expect(booking.shopifyOrderId).toBe('test-order-123');
      expect(booking.shopifyOrderName).toBe('#TEST-001');
    });

    it('予約の重複チェックが正しく動作する', async () => {
      // 最初の予約を作成
      const booking1 = await prisma.booking.create({
        data: {
          bookingId: 'integration-test-booking-2',
          productId: testProduct.id,
          startDate: new Date('2025-06-10'),
          endDate: new Date('2025-06-12'),
          customerName: '統合テスト顧客1',
          customerEmail: '<EMAIL>',
          status: 'CONFIRMED',
          shop: 'test-shop',
        },
      });

      testBookings.push(booking1);

      // 重複する期間の予約を検索
      const conflictingBookings = await prisma.booking.findMany({
        where: {
          productId: testProduct.id,
          status: { in: ['CONFIRMED', 'PROVISIONAL'] },
          OR: [
            {
              AND: [
                { startDate: { lte: new Date('2025-06-11') } },
                { endDate: { gte: new Date('2025-06-11') } },
              ],
            },
          ],
        },
      });

      expect(conflictingBookings.length).toBe(1);
      expect(conflictingBookings[0].id).toBe(booking1.id);
    });

    it('メタデータフィールドが正しく保存される', async () => {
      const booking = await prisma.booking.create({
        data: {
          bookingId: 'integration-test-booking-3',
          productId: testProduct.id,
          startDate: new Date('2025-06-20'),
          endDate: new Date('2025-06-22'),
          customerName: '統合テスト顧客2',
          customerEmail: '<EMAIL>',
          status: 'PROVISIONAL',
          shop: 'test-shop',
          metadata: {
            shopifyOrderId: 'meta-order-456',
            shopifyOrderName: '#META-002',
            customField: 'テストデータ',
          },
        },
      });

      testBookings.push(booking);

      expect(booking.metadata).toEqual({
        shopifyOrderId: 'meta-order-456',
        shopifyOrderName: '#META-002',
        customField: 'テストデータ',
      });
    });
  });

  describe('商品データの存在確認', () => {
    it('テスト用商品が正しく作成されている', async () => {
      expect(testProduct).toBeDefined();
      expect(testProduct.title).toBe('テスト統合商品');
      expect(testProduct.shopifyId).toBe('integration-test-product');
    });

    it('商品検索が正しく動作する', async () => {
      const foundProduct = await prisma.product.findFirst({
        where: { shopifyId: 'integration-test-product' }
      });

      expect(foundProduct).toBeDefined();
      expect(foundProduct?.id).toBe(testProduct.id);
    });
  });

  describe('日本時間処理', () => {
    it('日付が正しく保存される', async () => {
      const startDate = new Date('2025-07-01T00:00:00.000Z');
      const endDate = new Date('2025-07-03T00:00:00.000Z');

      const booking = await prisma.booking.create({
        data: {
          bookingId: 'integration-test-booking-4',
          productId: testProduct.id,
          startDate,
          endDate,
          customerName: '統合テスト顧客3',
          customerEmail: '<EMAIL>',
          status: 'CONFIRMED',
          shop: 'test-shop',
        },
      });

      testBookings.push(booking);

      expect(booking.startDate.getTime()).toBe(startDate.getTime());
      expect(booking.endDate.getTime()).toBe(endDate.getTime());
    });
  });
});