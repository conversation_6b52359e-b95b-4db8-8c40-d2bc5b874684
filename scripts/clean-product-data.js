/**
 * 商品データクレンジングスクリプト
 *
 * このスクリプトは、CSVファイルから商品データを読み込み、
 * データをクレンジングして、Shopify商品登録用のCSVファイルを生成します。
 */

import fs from 'fs';
import path from 'path';
import { parse } from 'csv-parse/sync';
import { stringify } from 'csv-stringify/sync';

// 設定
const INPUT_RENTAL_ITEMS_PATH = 'master-data-csv/rental_items_20250509_153839.csv';
const INPUT_SOFA_PATH = 'master-data-csv/商品一覧_202505031329-sofa.csv';
const OUTPUT_PATH = 'master-data-csv/cleaned_products.csv';

// CSVファイルを読み込む関数
function readCsvFile(filePath, encoding = 'utf8') {
  try {
    const fileContent = fs.readFileSync(filePath, encoding);
    const records = parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
      relax_quotes: true,
      relax_column_count: true
    });
    return records;
  } catch (error) {
    console.error(`CSVファイル ${filePath} の読み込み中にエラーが発生しました:`, error);
    return [];
  }
}

// 半角カタカナを全角カタカナに変換する関数
function hankakuToZenkaku(text) {
  if (!text) return '';

  const hankakuMap = {
    'ｱ': 'ア', 'ｲ': 'イ', 'ｳ': 'ウ', 'ｴ': 'エ', 'ｵ': 'オ',
    'ｶ': 'カ', 'ｷ': 'キ', 'ｸ': 'ク', 'ｹ': 'ケ', 'ｺ': 'コ',
    'ｻ': 'サ', 'ｼ': 'シ', 'ｽ': 'ス', 'ｾ': 'セ', 'ｿ': 'ソ',
    'ﾀ': 'タ', 'ﾁ': 'チ', 'ﾂ': 'ツ', 'ﾃ': 'テ', 'ﾄ': 'ト',
    'ﾅ': 'ナ', 'ﾆ': 'ニ', 'ﾇ': 'ヌ', 'ﾈ': 'ネ', 'ﾉ': 'ノ',
    'ﾊ': 'ハ', 'ﾋ': 'ヒ', 'ﾌ': 'フ', 'ﾍ': 'ヘ', 'ﾎ': 'ホ',
    'ﾏ': 'マ', 'ﾐ': 'ミ', 'ﾑ': 'ム', 'ﾒ': 'メ', 'ﾓ': 'モ',
    'ﾔ': 'ヤ', 'ﾕ': 'ユ', 'ﾖ': 'ヨ',
    'ﾗ': 'ラ', 'ﾘ': 'リ', 'ﾙ': 'ル', 'ﾚ': 'レ', 'ﾛ': 'ロ',
    'ﾜ': 'ワ', 'ｦ': 'ヲ', 'ﾝ': 'ン',
    'ｧ': 'ァ', 'ｨ': 'ィ', 'ｩ': 'ゥ', 'ｪ': 'ェ', 'ｫ': 'ォ',
    'ｯ': 'ッ', 'ｬ': 'ャ', 'ｭ': 'ュ', 'ｮ': 'ョ',
    'ｰ': 'ー', '･': '・', '､': '、', '｡': '。',
    '｢': '「', '｣': '」', '(': '（', ')': '）',
    '!': '！', '?': '？', '[': '［', ']': '］'
  };

  return text.split('').map(char => hankakuMap[char] || char).join('');
}

// 商品名から特定のプレフィックスやサフィックスを削除する関数
function cleanProductName(name) {
  if (!name) return '';

  // 【】内のテキストを削除
  name = name.replace(/【.*?】/g, '');

  // 数字_や《》内のテキストを削除
  name = name.replace(/\d+_/g, '');
  name = name.replace(/《.*?》/g, '');

  // ●や◇販売価格￥\d+◇を削除
  name = name.replace(/●/g, '');
  name = name.replace(/◇販売価格￥\d+◇/g, '');

  // 先頭と末尾の空白を削除
  name = name.trim();

  return name;
}

// SKUを正規化する関数
function normalizeSku(sku, productCode, detailCode, variantIndex) {
  if (!sku) {
    if (productCode && detailCode) {
      return `${productCode}-${detailCode}-${String(variantIndex).padStart(3, '0')}`;
    }
    return '';
  }

  // 既に正規化されているSKUはそのまま返す
  if (sku.match(/^\d+-\d+-\d+$/)) {
    return sku;
  }

  // 基本的なSKUパターン: 商品コード-詳細コード
  const match = sku.match(/^(\d+)-(\d+)/);
  if (match) {
    return `${match[1]}-${match[2]}-${String(variantIndex).padStart(3, '0')}`;
  }

  return sku;
}

// 商品データをクレンジングする関数
function cleanProductData(rentalItems, sofaItems) {
  const cleanedProducts = [];
  const processedSkus = new Set();

  // rental_items_20250509_153839.csvのデータを処理
  for (const item of rentalItems) {
    // 家具小道具メニューが「FURNITURE」のアイテムのみ処理
    if (item['家具小道具メニュー'] !== 'FURNITURE') continue;

    const title = cleanProductName(item['名前']);
    const titleKana = hankakuToZenkaku(title);

    // 型番からSKUを生成
    const modelNumber = item['型番'] || '';
    let sku = '';

    if (modelNumber) {
      const parts = modelNumber.split('-');
      if (parts.length >= 2) {
        sku = `${parts[0]}-${parts[1]}-001`;
      }
    }

    // 既に処理済みのSKUはスキップ
    if (sku && processedSkus.has(sku)) continue;
    if (sku) processedSkus.add(sku);

    // 寸法情報
    const width = parseInt(item['サイズW']) || 0;
    const depth = parseInt(item['サイズD']) || 0;
    const height = parseInt(item['サイズH']) || 0;
    const seatHeight = parseInt(item['サイズSH']) || 0;

    // 料金情報
    const price = parseInt(item['レンタル料金1日']) || 0;

    // 在庫情報
    const inventory = parseInt(item['在庫数']) || 0;
    const location = item['在庫場所'] || '';

    // 商品情報
    const color = item['色'] || '';
    const material = item['素材'] || '';
    const notes = item['その他'] || '';

    // 公開ステータス
    const status = item['公開ステータス(公開/非公開)'] === '公開' ? 'active' : 'draft';

    // クレンジングされた商品データを追加
    cleanedProducts.push({
      'Handle': title.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, ''),
      'Title': title,
      'Body HTML': `<p>${notes}</p>`,
      'Vendor': 'レンタル家具',
      'Product Category': 'Home & Garden > Furniture',
      'Type': item['家具小道具小カテゴリー'] || 'レンタル商品',
      'Tags': `レンタル商品,${item['家具小道具小カテゴリー'] || ''}`,
      'Published': status === 'active' ? 'TRUE' : 'FALSE',
      'Option1 Name': 'レンタル日数',
      'Option1 Value': '1日レンタル',
      'Option2 Name': '',
      'Option2 Value': '',
      'Option3 Name': '',
      'Option3 Value': '',
      'Variant SKU': sku,
      'Variant Grams': '0',
      'Variant Inventory Tracker': 'shopify',
      'Variant Inventory Qty': inventory.toString(),
      'Variant Inventory Policy': 'deny',
      'Variant Fulfillment Service': 'manual',
      'Variant Price': price.toString(),
      'Variant Compare At Price': '',
      'Variant Requires Shipping': 'TRUE',
      'Variant Taxable': 'TRUE',
      'Variant Barcode': '',
      'Image Src': '',
      'Image Position': '',
      'Image Alt Text': '',
      'Gift Card': 'FALSE',
      'SEO Title': title,
      'SEO Description': '',
      'Google Shopping / Google Product Category': '',
      'Google Shopping / Gender': '',
      'Google Shopping / Age Group': '',
      'Google Shopping / MPN': '',
      'Google Shopping / AdWords Grouping': '',
      'Google Shopping / AdWords Labels': '',
      'Google Shopping / Condition': '',
      'Google Shopping / Custom Product': '',
      'Google Shopping / Custom Label 0': '',
      'Google Shopping / Custom Label 1': '',
      'Google Shopping / Custom Label 2': '',
      'Google Shopping / Custom Label 3': '',
      'Google Shopping / Custom Label 4': '',
      'Variant Image': '',
      'Variant Weight Unit': 'kg',
      'Variant Tax Code': '',
      'Cost per item': '',
      'Included / Japan': 'TRUE',
      'Status': status,
      // メタフィールド情報
      'Metafield: rental.basic_info [json]': JSON.stringify({
        productCode: sku.split('-')[0] || '',
        detailCode: sku.split('-')[1] || '',
        kana: titleKana,
        dimensions: {
          width,
          depth,
          height
        },
        seatDimensions: {
          width: 0,
          depth: 0,
          height: seatHeight
        },
        material,
        color,
        maker: '',
        campaign: item['キャンペーン'] || '通常商品',
        notes: notes
      }),
      'Metafield: rental.pricing [json]': JSON.stringify({
        basePrice: price,
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30
      }),
      'Metafield: rental.inventory_items [json]': JSON.stringify([
        {
          id: `item-${sku}`,
          sku: sku,
          status: 'available',
          location: location,
          notes: ''
        }
      ]),
      'Metafield: rental.reservation_info [json]': JSON.stringify([
        {
          itemId: `item-${sku}`,
          reservations: []
        }
      ]),
      'Metafield: rental.color [string]': color,
      'Metafield: rental.material [string]': material,
      'Metafield: product.height [number_integer]': height.toString(),
      'Metafield: product.width [number_integer]': width.toString(),
      'Metafield: product.depth [number_integer]': depth.toString(),
      'Metafield: rental.location [string]': location
    });

    // バリエーションを追加（2日〜8日以上）
    const variantTitles = [
      '2日レンタル',
      '3日レンタル',
      '4日レンタル',
      '5日レンタル',
      '6日レンタル',
      '7日レンタル',
      '8日以上レンタル'
    ];

    for (let i = 0; i < variantTitles.length; i++) {
      const variantTitle = variantTitles[i];
      const days = i + 2; // 2日〜8日
      const variantPrice = days <= 7
        ? Math.round(price * (1 + (days - 1) * 0.2))
        : Math.round(price * (1 + 6 * 0.2 + 0.1)); // 8日以上は7日料金+10%

      const variantSku = sku.replace(/-001$/, `-${String(days).padStart(3, '0')}`);

      cleanedProducts.push({
        'Handle': title.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, ''),
        'Title': '',
        'Body HTML': '',
        'Vendor': '',
        'Product Category': '',
        'Type': '',
        'Tags': '',
        'Published': '',
        'Option1 Name': '',
        'Option1 Value': variantTitle,
        'Option2 Name': '',
        'Option2 Value': '',
        'Option3 Name': '',
        'Option3 Value': '',
        'Variant SKU': variantSku,
        'Variant Grams': '0',
        'Variant Inventory Tracker': 'shopify',
        'Variant Inventory Qty': inventory.toString(),
        'Variant Inventory Policy': 'deny',
        'Variant Fulfillment Service': 'manual',
        'Variant Price': variantPrice.toString(),
        'Variant Compare At Price': '',
        'Variant Requires Shipping': 'TRUE',
        'Variant Taxable': 'TRUE',
        'Variant Barcode': '',
        'Image Src': '',
        'Image Position': '',
        'Image Alt Text': '',
        'Gift Card': 'FALSE',
        'SEO Title': '',
        'SEO Description': '',
        'Google Shopping / Google Product Category': '',
        'Google Shopping / Gender': '',
        'Google Shopping / Age Group': '',
        'Google Shopping / MPN': '',
        'Google Shopping / AdWords Grouping': '',
        'Google Shopping / AdWords Labels': '',
        'Google Shopping / Condition': '',
        'Google Shopping / Custom Product': '',
        'Google Shopping / Custom Label 0': '',
        'Google Shopping / Custom Label 1': '',
        'Google Shopping / Custom Label 2': '',
        'Google Shopping / Custom Label 3': '',
        'Google Shopping / Custom Label 4': '',
        'Variant Image': '',
        'Variant Weight Unit': 'kg',
        'Variant Tax Code': '',
        'Cost per item': '',
        'Included / Japan': 'TRUE',
        'Status': ''
      });
    }
  }

  // 商品一覧_202505031329-sofa.csvのデータを処理
  for (const item of sofaItems) {
    // 廃棄済みの商品はスキップ
    if (item['廃業区分'] === '1') continue;

    const productCode = item['商品コード'] || '';
    const detailCode = item['商品詳細コード'] || '';
    const sku = `${productCode}-${detailCode}-001`;

    // 既に処理済みのSKUはスキップ
    if (processedSkus.has(sku)) continue;
    processedSkus.add(sku);

    const title = cleanProductName(item['商品名']);
    const titleKana = hankakuToZenkaku(item['商品名フリガナ'] || title);

    // 寸法情報
    const width = parseInt(item['サイズW']) || 0;
    const depth = parseInt(item['サイズD']) || 0;
    const height = parseInt(item['サイズH']) || 0;
    const diameter = parseInt(item['直径']) || 0;

    // 料金情報
    const price = parseInt(item['レンタル単価']) || 0;

    // 在庫情報
    const inventory = parseInt(item['在庫数']) || 0;
    const location = item['在庫場所'] || '';

    // 商品情報
    const notes = item['特記事項'] || '';
    const status = item['ステータス'] === '1' ? 'active' : 'draft';

    // 廃棄情報
    const isDisposed = item['廃業区分'] === '1';
    const disposalDate = item['廃棄日'] || '';
    const disposalReason = item['備考'] || '';

    // クレンジングされた商品データを追加
    cleanedProducts.push({
      'Handle': title.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, ''),
      'Title': title,
      'Body HTML': `<p>${notes}</p>`,
      'Vendor': 'レンタル家具',
      'Product Category': 'Home & Garden > Furniture',
      'Type': item['商品カテゴリ'] || 'レンタル商品',
      'Tags': `レンタル商品,${item['商品カテゴリ'] || ''}`,
      'Published': status === 'active' ? 'TRUE' : 'FALSE',
      'Option1 Name': 'レンタル日数',
      'Option1 Value': '1日レンタル',
      'Option2 Name': '',
      'Option2 Value': '',
      'Option3 Name': '',
      'Option3 Value': '',
      'Variant SKU': sku,
      'Variant Grams': '0',
      'Variant Inventory Tracker': 'shopify',
      'Variant Inventory Qty': inventory.toString(),
      'Variant Inventory Policy': 'deny',
      'Variant Fulfillment Service': 'manual',
      'Variant Price': price.toString(),
      'Variant Compare At Price': '',
      'Variant Requires Shipping': 'TRUE',
      'Variant Taxable': 'TRUE',
      'Variant Barcode': '',
      'Image Src': '',
      'Image Position': '',
      'Image Alt Text': '',
      'Gift Card': 'FALSE',
      'SEO Title': title,
      'SEO Description': '',
      'Google Shopping / Google Product Category': '',
      'Google Shopping / Gender': '',
      'Google Shopping / Age Group': '',
      'Google Shopping / MPN': '',
      'Google Shopping / AdWords Grouping': '',
      'Google Shopping / AdWords Labels': '',
      'Google Shopping / Condition': '',
      'Google Shopping / Custom Product': '',
      'Google Shopping / Custom Label 0': '',
      'Google Shopping / Custom Label 1': '',
      'Google Shopping / Custom Label 2': '',
      'Google Shopping / Custom Label 3': '',
      'Google Shopping / Custom Label 4': '',
      'Variant Image': '',
      'Variant Weight Unit': 'kg',
      'Variant Tax Code': '',
      'Cost per item': '',
      'Included / Japan': 'TRUE',
      'Status': status,
      // メタフィールド情報
      'Metafield: rental.basic_info [json]': JSON.stringify({
        productCode,
        detailCode,
        kana: titleKana,
        dimensions: {
          width,
          depth,
          height
        },
        seatDimensions: {
          width: 0,
          depth: 0,
          height: 0
        },
        material: '',
        color: '',
        maker: item['メーカー名'] || '',
        campaign: '通常商品',
        notes: notes
      }),
      'Metafield: rental.pricing [json]': JSON.stringify({
        basePrice: price,
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30
      }),
      'Metafield: rental.inventory_items [json]': JSON.stringify([
        {
          id: `item-${sku}`,
          sku: sku,
          status: isDisposed ? 'unavailable' : 'available',
          location: location,
          notes: ''
        }
      ]),
      'Metafield: rental.reservation_info [json]': JSON.stringify([
        {
          itemId: `item-${sku}`,
          reservations: []
        }
      ]),
      'Metafield: rental.location [string]': location,
      'Metafield: product.height [number_integer]': height.toString(),
      'Metafield: product.width [number_integer]': width.toString(),
      'Metafield: product.depth [number_integer]': depth.toString(),
      'Metafield: rental.is_disposed [boolean]': isDisposed.toString(),
      'Metafield: rental.status [string]': isDisposed ? 'unavailable' : 'available'
    });

    // 廃棄情報がある場合は追加
    if (isDisposed) {
      if (disposalDate) {
        cleanedProducts[cleanedProducts.length - 1]['Metafield: rental.disposal_date [date]'] = disposalDate;
      }

      if (disposalReason) {
        cleanedProducts[cleanedProducts.length - 1]['Metafield: rental.disposal_reason [multi_line_text_field]'] = disposalReason;
      }
    }

    // バリエーションを追加（2日〜8日以上）
    const variantTitles = [
      '2日レンタル',
      '3日レンタル',
      '4日レンタル',
      '5日レンタル',
      '6日レンタル',
      '7日レンタル',
      '8日以上レンタル'
    ];

    for (let i = 0; i < variantTitles.length; i++) {
      const variantTitle = variantTitles[i];
      const days = i + 2; // 2日〜8日
      const variantPrice = days <= 7
        ? Math.round(price * (1 + (days - 1) * 0.2))
        : Math.round(price * (1 + 6 * 0.2 + 0.1)); // 8日以上は7日料金+10%

      const variantSku = sku.replace(/-001$/, `-${String(days).padStart(3, '0')}`);

      cleanedProducts.push({
        'Handle': title.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, ''),
        'Title': '',
        'Body HTML': '',
        'Vendor': '',
        'Product Category': '',
        'Type': '',
        'Tags': '',
        'Published': '',
        'Option1 Name': '',
        'Option1 Value': variantTitle,
        'Option2 Name': '',
        'Option2 Value': '',
        'Option3 Name': '',
        'Option3 Value': '',
        'Variant SKU': variantSku,
        'Variant Grams': '0',
        'Variant Inventory Tracker': 'shopify',
        'Variant Inventory Qty': inventory.toString(),
        'Variant Inventory Policy': 'deny',
        'Variant Fulfillment Service': 'manual',
        'Variant Price': variantPrice.toString(),
        'Variant Compare At Price': '',
        'Variant Requires Shipping': 'TRUE',
        'Variant Taxable': 'TRUE',
        'Variant Barcode': '',
        'Image Src': '',
        'Image Position': '',
        'Image Alt Text': '',
        'Gift Card': 'FALSE',
        'SEO Title': '',
        'SEO Description': '',
        'Google Shopping / Google Product Category': '',
        'Google Shopping / Gender': '',
        'Google Shopping / Age Group': '',
        'Google Shopping / MPN': '',
        'Google Shopping / AdWords Grouping': '',
        'Google Shopping / AdWords Labels': '',
        'Google Shopping / Condition': '',
        'Google Shopping / Custom Product': '',
        'Google Shopping / Custom Label 0': '',
        'Google Shopping / Custom Label 1': '',
        'Google Shopping / Custom Label 2': '',
        'Google Shopping / Custom Label 3': '',
        'Google Shopping / Custom Label 4': '',
        'Variant Image': '',
        'Variant Weight Unit': 'kg',
        'Variant Tax Code': '',
        'Cost per item': '',
        'Included / Japan': 'TRUE',
        'Status': ''
      });
    }
  }

  return cleanedProducts;
}

// CSVファイルを書き込む関数
function writeCsvFile(filePath, data) {
  try {
    const csvContent = stringify(data, { header: true });
    fs.writeFileSync(filePath, csvContent);
    console.log(`CSVファイル ${filePath} を作成しました。`);
    return true;
  } catch (error) {
    console.error(`CSVファイル ${filePath} の書き込み中にエラーが発生しました:`, error);
    return false;
  }
}

// メイン処理
function main() {
  console.log('商品データクレンジングを開始します...');

  // CSVファイルを読み込む
  console.log(`${INPUT_RENTAL_ITEMS_PATH} を読み込んでいます...`);
  const rentalItems = readCsvFile(INPUT_RENTAL_ITEMS_PATH);
  console.log(`${rentalItems.length}件のレンタルアイテムを読み込みました。`);

  console.log(`${INPUT_SOFA_PATH} を読み込んでいます...`);
  const sofaItems = readCsvFile(INPUT_SOFA_PATH, 'shift-jis');
  console.log(`${sofaItems.length}件のソファアイテムを読み込みました。`);

  // データをクレンジング
  console.log('データをクレンジングしています...');
  const cleanedProducts = cleanProductData(rentalItems, sofaItems);
  console.log(`${cleanedProducts.length}件のクレンジング済み商品データを生成しました。`);

  // クレンジング済みデータをCSVファイルに書き込む
  console.log(`${OUTPUT_PATH} に書き込んでいます...`);
  writeCsvFile(OUTPUT_PATH, cleanedProducts);

  console.log('商品データクレンジングが完了しました。');
}

// スクリプト実行
main();
