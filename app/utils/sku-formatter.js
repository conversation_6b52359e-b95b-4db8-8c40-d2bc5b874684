/**
 * SKU表示フォーマット機能
 * 
 * SKUを統一されたハイフン付きフォーマットで表示する
 */

/**
 * SKUをハイフン付きフォーマットに変換
 * @param {string} sku - 元のSKU
 * @returns {string} - フォーマット済みSKU
 */
export function formatSkuWithHyphens(sku) {
  if (!sku) return '';

  // 既にハイフン付きの場合はそのまま返す
  if (sku.includes('-')) {
    return sku;
  }

  // 数字のみの場合（例: 10101008 → 101-01-008）
  if (/^\d+$/.test(sku)) {
    if (sku.length >= 8) {
      // 8桁以上の場合: 101-01-008 形式
      const part1 = sku.substring(0, 3);
      const part2 = sku.substring(3, 5);
      const part3 = sku.substring(5, 8);
      const remaining = sku.substring(8);
      
      let formatted = `${part1}-${part2}-${part3}`;
      if (remaining) {
        formatted += `-${remaining}`;
      }
      return formatted;
    } else if (sku.length >= 6) {
      // 6-7桁の場合: 201-07-1 形式
      const part1 = sku.substring(0, 3);
      const part2 = sku.substring(3, 5);
      const part3 = sku.substring(5);
      return `${part1}-${part2}-${part3}`;
    }
  }

  return sku;
}

/**
 * SKUにバリアント情報を追加
 * @param {string} baseSku - 基本SKU
 * @param {string} variantInfo - バリアント情報（例: "1D", "2D"）
 * @returns {string} - 完全なSKU
 */
export function addVariantToSku(baseSku, variantInfo) {
  const formattedBase = formatSkuWithHyphens(baseSku);
  
  if (!variantInfo) {
    return formattedBase;
  }

  // 既にバリアント情報が含まれている場合は追加しない
  if (formattedBase.endsWith(`-${variantInfo}`)) {
    return formattedBase;
  }

  return `${formattedBase}-${variantInfo}`;
}

/**
 * レンタル日数からバリアント情報を生成
 * @param {number} days - レンタル日数
 * @returns {string} - バリアント情報
 */
export function generateVariantFromDays(days) {
  if (days >= 8) {
    return '8D'; // 8日以上は8Dで統一
  }
  return `${days}D`;
}

/**
 * 商品表示用の完全なSKUを生成
 * @param {string} baseSku - 基本SKU
 * @param {number} rentalDays - レンタル日数（オプション）
 * @returns {string} - 表示用SKU
 */
export function generateDisplaySku(baseSku, rentalDays = null) {
  const formattedBase = formatSkuWithHyphens(baseSku);
  
  if (rentalDays) {
    const variant = generateVariantFromDays(rentalDays);
    return addVariantToSku(formattedBase, variant);
  }
  
  return formattedBase;
}

/**
 * 検索用のSKU条件を生成
 * @param {string} searchQuery - 検索クエリ
 * @returns {Array} - Prisma検索条件の配列
 */
export function generateSkuSearchConditions(searchQuery) {
  if (!searchQuery) return [];

  const trimmedQuery = searchQuery.trim();
  const conditions = [];

  // 1. 完全一致
  conditions.push({
    sku: { equals: trimmedQuery, mode: 'insensitive' }
  });

  // 2. 前方一致
  conditions.push({
    sku: { startsWith: trimmedQuery, mode: 'insensitive' }
  });

  // 3. 部分一致
  conditions.push({
    sku: { contains: trimmedQuery, mode: 'insensitive' }
  });

  // 4. ハイフンなし形式の検索（10101008 → 101-01-008）
  if (/^\d+$/.test(trimmedQuery) && trimmedQuery.length >= 6) {
    const formattedSku = formatSkuWithHyphens(trimmedQuery);
    conditions.push({
      sku: { equals: formattedSku, mode: 'insensitive' }
    });
    conditions.push({
      sku: { startsWith: formattedSku, mode: 'insensitive' }
    });
  }

  // 5. ハイフン付き形式をハイフンなしで検索
  if (trimmedQuery.includes('-')) {
    const withoutHyphens = trimmedQuery.replace(/-/g, '');
    conditions.push({
      sku: { contains: withoutHyphens, mode: 'insensitive' }
    });
  }

  return conditions;
}

/**
 * SKUからカテゴリ情報を抽出
 * @param {string} sku - SKU
 * @returns {Object} - カテゴリ情報
 */
export function extractCategoryFromSku(sku) {
  if (!sku) return null;

  const formattedSku = formatSkuWithHyphens(sku);
  const parts = formattedSku.split('-');
  
  if (parts.length >= 3) {
    const categoryCode = parts[0]; // 例: "101"
    const subCategoryCode = parts[1]; // 例: "01"
    const itemCode = parts[2]; // 例: "008"
    
    return {
      categoryCode,
      subCategoryCode,
      itemCode,
      fullCategoryCode: `${categoryCode}-${subCategoryCode}`
    };
  }

  return null;
}

/**
 * カテゴリコードからShopifyタグを生成
 * @param {string} categoryCode - カテゴリコード
 * @param {string} subCategoryCode - サブカテゴリコード
 * @returns {Array} - Shopifyタグの配列
 */
export function generateShopifyTags(categoryCode, subCategoryCode) {
  const tags = [];

  // カテゴリマッピング（実際のマスタデータに基づいて調整）
  const categoryMapping = {
    '101': 'ソファ',
    '102': 'チェア',
    '103': 'テーブル',
    '201': 'ソファ',
    '202': 'チェア',
    '203': 'テーブル'
  };

  const subCategoryMapping = {
    '01': '1シーター',
    '02': '2シーター',
    '03': '3シーター',
    '06': 'ソファ',
    '07': 'アクセサリー'
  };

  if (categoryMapping[categoryCode]) {
    tags.push(categoryMapping[categoryCode]);
  }

  if (subCategoryMapping[subCategoryCode]) {
    tags.push(subCategoryMapping[subCategoryCode]);
  }

  // カテゴリコードもタグとして追加
  tags.push(`CAT-${categoryCode}`);
  tags.push(`SUBCAT-${subCategoryCode}`);

  return tags;
}

/**
 * SKUからShopifyコレクションを決定
 * @param {string} sku - SKU
 * @returns {string} - コレクション名
 */
export function determineShopifyCollection(sku) {
  const categoryInfo = extractCategoryFromSku(sku);
  
  if (!categoryInfo) return 'その他';

  const { categoryCode } = categoryInfo;

  // カテゴリコードに基づいてコレクションを決定
  if (categoryCode.startsWith('1')) {
    return 'ソファコレクション';
  } else if (categoryCode.startsWith('2')) {
    return 'チェアコレクション';
  } else if (categoryCode.startsWith('3')) {
    return 'テーブルコレクション';
  }

  return 'その他';
}

/**
 * SKUからShopify商品タイプを決定
 * @param {string} sku - SKU
 * @returns {string} - 商品タイプ
 */
export function determineShopifyProductType(sku) {
  const categoryInfo = extractCategoryFromSku(sku);
  
  if (!categoryInfo) return 'レンタル家具';

  const { categoryCode, subCategoryCode } = categoryInfo;

  // より具体的な商品タイプを生成
  if (categoryCode === '101' || categoryCode === '201') {
    if (subCategoryCode === '01') return 'ソファ - 1シーター';
    if (subCategoryCode === '02') return 'ソファ - 2シーター';
    if (subCategoryCode === '03') return 'ソファ - 3シーター';
    return 'ソファ';
  }

  return 'レンタル家具';
}
