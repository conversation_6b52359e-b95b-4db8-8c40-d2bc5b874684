import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const graphQLClient = new GraphQLClient(
  `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品作成のミューテーション
const CREATE_PRODUCT_MUTATION = gql`
  mutation productCreate($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// バリアント作成のミューテーション
const CREATE_VARIANT_MUTATION = gql`
  mutation productVariantCreate($input: ProductVariantInput!) {
    productVariantCreate(input: $input) {
      productVariant {
        id
        title
        price
        sku
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 在庫設定のミューテーション
const SET_INVENTORY_MUTATION = gql`
  mutation inventoryActivate($inventoryItemId: ID!, $locationId: ID!, $available: Int!) {
    inventoryActivate(inventoryItemId: $inventoryItemId, locationId: $locationId, available: $available) {
      inventoryLevel {
        id
        available
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 在庫アイテム取得のクエリ
const GET_INVENTORY_ITEM_QUERY = gql`
  query getInventoryItem($variantId: ID!) {
    productVariant(id: $variantId) {
      inventoryItem {
        id
      }
    }
  }
`;

// ロケーション取得のクエリ
const GET_LOCATIONS_QUERY = gql`
  query getLocations {
    locations(first: 10) {
      edges {
        node {
          id
          name
        }
      }
    }
  }
`;

// メタフィールドの設定
async function setJsonMetafield(productId, namespace, key, jsonValue) {
  const mutation = gql`
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    metafields: [
      {
        ownerId: productId,
        namespace,
        key,
        value: JSON.stringify(jsonValue),
        type: 'json',
      }
    ]
  };

  try {
    const result = await graphQLClient.request(mutation, variables);
    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', result.metafieldsSet.userErrors);
      return false;
    }
    return true;
  } catch (error) {
    console.error(`メタフィールド ${namespace}.${key} 設定エラー:`, error);
    return false;
  }
}

// 商品を作成する関数
async function createProduct(productData) {
  try {
    const variables = {
      input: productData
    };

    const result = await graphQLClient.request(CREATE_PRODUCT_MUTATION, variables);

    if (result.productCreate.userErrors.length > 0) {
      console.error('商品作成エラー:', result.productCreate.userErrors);
      return null;
    }

    return result.productCreate.product;
  } catch (error) {
    console.error('商品作成中にエラーが発生しました:', error);
    return null;
  }
}

// バリアントを作成する関数
async function createVariant(productId, variantData) {
  try {
    const variables = {
      input: {
        productId,
        ...variantData
      }
    };

    const result = await graphQLClient.request(CREATE_VARIANT_MUTATION, variables);

    if (result.productVariantCreate.userErrors.length > 0) {
      console.error('バリアント作成エラー:', result.productVariantCreate.userErrors);
      return null;
    }

    return result.productVariantCreate.productVariant;
  } catch (error) {
    console.error('バリアント作成中にエラーが発生しました:', error);
    return null;
  }
}

// 在庫アイテムIDを取得する関数
async function getInventoryItemId(variantId) {
  try {
    const result = await graphQLClient.request(GET_INVENTORY_ITEM_QUERY, { variantId });
    return result.productVariant.inventoryItem.id;
  } catch (error) {
    console.error('在庫アイテムID取得エラー:', error);
    return null;
  }
}

// ロケーションを取得する関数
async function getLocations() {
  try {
    const result = await graphQLClient.request(GET_LOCATIONS_QUERY);
    return result.locations.edges.map(edge => edge.node);
  } catch (error) {
    console.error('ロケーション取得エラー:', error);
    return [];
  }
}

// 在庫を設定する関数
async function setInventory(variantId, available) {
  try {
    // 在庫アイテムIDを取得
    const inventoryItemId = await getInventoryItemId(variantId);
    if (!inventoryItemId) {
      console.error('在庫アイテムIDが取得できませんでした');
      return false;
    }

    // ロケーションを取得
    const locations = await getLocations();
    if (locations.length === 0) {
      console.error('ロケーションが取得できませんでした');
      return false;
    }

    // 最初のロケーションを使用
    const locationId = locations[0].id;

    // 在庫を設定
    const variables = {
      inventoryItemId,
      locationId,
      available
    };

    const result = await graphQLClient.request(SET_INVENTORY_MUTATION, variables);

    if (result.inventoryActivate.userErrors.length > 0) {
      console.error('在庫設定エラー:', result.inventoryActivate.userErrors);
      return false;
    }

    return true;
  } catch (error) {
    console.error('在庫設定中にエラーが発生しました:', error);
    return false;
  }
}

// 現在の日付から指定した日数後の日付を取得
function getDateAfterDays(days) {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return date.toISOString().split('T')[0];
}

// テスト用商品データ
const testProducts = [
  {
    title: "【正しい実装】ベーシックソファ オフホワイト 1シーター",
    descriptionHtml: "ベーシックソファ オフホワイト 1シーター - 正しい在庫管理のテスト用商品",
    vendor: "ヤマナリ",
    productType: "ソファ",
    tags: ["テスト", "ソファ", "オフホワイト", "正しい実装"],
    status: "ACTIVE",
    options: ["レンタル日数"],
    variants: [
      {
        price: "8000",
        sku: "10101007-001",
        options: ["1日レンタル"],
        inventoryManagement: "SHOPIFY"
      }
    ],
    metafields: {
      basic_info: {
        productCode: "10101007",
        detailCode: "001",
        kana: "ベーシックソファオフホワイト1シーターヤマナリ",
        dimensions: {
          width: 87,
          depth: 74,
          height: 76
        },
        seatDimensions: {
          width: 52,
          depth: 54,
          height: 40
        },
        material: "ファブリック",
        color: "オフホワイト",
        maker: "ヤマナリ",
        campaign: "通常商品",
        notes: "2台ともクリーニング済みですが全体的に黄ばみ発生（H30.12.15)"
      },
      pricing: {
        basePrice: 8000,
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30
      },
      inventory_items: [
        {
          id: "item-001",
          sku: "10101007-001-1",
          status: "available",
          location: "NY",
          notes: "背面向かって右側うっすら黒いしみ"
        },
        {
          id: "item-002",
          sku: "10101007-001-2",
          status: "maintenance",
          location: "NY",
          notes: "向かって左アーム手前と正面左側に黄色い輪染み有"
        }
      ],
      reservation_info: [
        {
          itemId: "item-001",
          reservations: [
            {
              id: "reservation-001",
              startDate: getDateAfterDays(5),
              endDate: getDateAfterDays(8),
              status: "confirmed",
              customerName: "山田太郎",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1001",
              orderLineItemId: "gid://shopify/LineItem/1001",
              notes: "特になし"
            },
            {
              id: "reservation-002",
              startDate: getDateAfterDays(15),
              endDate: getDateAfterDays(20),
              status: "pending",
              customerName: "佐藤花子",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1002",
              orderLineItemId: "gid://shopify/LineItem/1002",
              notes: "配送希望"
            }
          ]
        },
        {
          itemId: "item-002",
          reservations: []
        }
      ]
    }
  },
  {
    title: "【正しい実装】カリモクソファ モケットグリーン 1シーター",
    descriptionHtml: "カリモクソファ モケットグリーン 1シーター - 正しい在庫管理のテスト用商品",
    vendor: "カリモク",
    productType: "ソファ",
    tags: ["テスト", "ソファ", "モケットグリーン", "正しい実装"],
    status: "ACTIVE",
    options: ["レンタル日数"],
    variants: [
      {
        price: "7000",
        sku: "10101031-001",
        options: ["1日レンタル"],
        inventoryManagement: "SHOPIFY"
      }
    ],
    metafields: {
      basic_info: {
        productCode: "10101031",
        detailCode: "001",
        kana: "カリモクソファモケットグリーン1シーター",
        dimensions: {
          width: 61,
          depth: 66,
          height: 70
        },
        seatDimensions: {
          width: 52,
          depth: 46,
          height: 34
        },
        material: "モケット",
        color: "グリーン",
        maker: "カリモク",
        campaign: "Japan_+α_200904",
        notes: "2シーター1台あり"
      },
      pricing: {
        basePrice: 7000,
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30
      },
      inventory_items: [
        {
          id: "item-003",
          sku: "10101031-001-1",
          status: "available",
          location: "NY",
          notes: "特になし"
        },
        {
          id: "item-004",
          sku: "10101031-001-2",
          status: "damaged",
          location: "NY",
          notes: "座面に傷あり"
        }
      ],
      reservation_info: [
        {
          itemId: "item-003",
          reservations: [
            {
              id: "reservation-003",
              startDate: getDateAfterDays(3),
              endDate: getDateAfterDays(4),
              status: "confirmed",
              customerName: "田中次郎",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1003",
              orderLineItemId: "gid://shopify/LineItem/1003",
              notes: "短期レンタル"
            },
            {
              id: "reservation-004",
              startDate: getDateAfterDays(7),
              endDate: getDateAfterDays(9),
              status: "confirmed",
              customerName: "高橋三郎",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1004",
              orderLineItemId: "gid://shopify/LineItem/1004",
              notes: "休日を挟んだレンタル"
            }
          ]
        },
        {
          itemId: "item-004",
          reservations: [
            {
              id: "reservation-005",
              startDate: getDateAfterDays(20),
              endDate: getDateAfterDays(30),
              status: "pending",
              customerName: "伊藤四郎",
              customerEmail: "<EMAIL>",
              orderId: "gid://shopify/Order/1005",
              orderLineItemId: "gid://shopify/LineItem/1005",
              notes: "仮予約"
            }
          ]
        }
      ]
    }
  }
];

// 追加バリアントデータ
const additionalVariants = [
  {
    productIndex: 0,
    rentalDays: [
      { title: "2日レンタル", priceMultiplier: 1.2 },
      { title: "3日レンタル", priceMultiplier: 1.4 },
      { title: "4日レンタル", priceMultiplier: 1.6 },
      { title: "5日レンタル", priceMultiplier: 1.8 },
      { title: "6日レンタル", priceMultiplier: 2.0 },
      { title: "7日レンタル", priceMultiplier: 2.2 },
      { title: "8日以上レンタル", priceMultiplier: 2.3 }
    ]
  },
  {
    productIndex: 1,
    rentalDays: [
      { title: "2日レンタル", priceMultiplier: 1.2 },
      { title: "3日レンタル", priceMultiplier: 1.4 },
      { title: "4日レンタル", priceMultiplier: 1.6 },
      { title: "5日レンタル", priceMultiplier: 1.8 },
      { title: "6日レンタル", priceMultiplier: 2.0 },
      { title: "7日レンタル", priceMultiplier: 2.2 },
      { title: "8日以上レンタル", priceMultiplier: 2.3 }
    ]
  }
];

// メイン処理
async function createCorrectInventoryTestProducts() {
  console.log('正しい在庫管理のテスト用商品を作成しています...');

  const createdProducts = [];

  for (const productData of testProducts) {
    console.log(`\n商品を作成中: ${productData.title}`);

    // メタフィールドを一時的に削除
    const metafields = productData.metafields;
    delete productData.metafields;

    // 商品を作成
    const product = await createProduct(productData);

    if (!product) {
      console.error(`商品の作成に失敗しました: ${productData.title}`);
      continue;
    }

    console.log(`商品を作成しました: ${product.title} (ID: ${product.id})`);
    createdProducts.push(product);

    // 追加バリアントの作成
    const productIndex = createdProducts.length - 1;
    const additionalVariantData = additionalVariants.find(v => v.productIndex === productIndex);

    if (additionalVariantData) {
      console.log(`${product.title} の追加バリアントを作成中...`);

      const basePrice = parseInt(testProducts[productIndex].variants[0].price);
      const baseVariant = await createVariant(product.id, testProducts[productIndex].variants[0]);

      if (baseVariant) {
        console.log(`基本バリアントを作成しました: ${baseVariant.title} (${baseVariant.sku})`);

        // 在庫数を設定（2台）
        await setInventory(baseVariant.id, 2);
        console.log(`在庫数を設定しました: 2`);
      }

      for (const rentalDay of additionalVariantData.rentalDays) {
        const price = Math.round(basePrice * rentalDay.priceMultiplier);
        const skuSuffix = rentalDay.title.includes('8日以上') ? '008' : `00${rentalDay.title.charAt(0)}`;
        const sku = `${testProducts[productIndex].variants[0].sku.slice(0, -3)}${skuSuffix}`;

        console.log(`バリアントを作成中: ${rentalDay.title} (${sku})`);

        const variantData = {
          options: [rentalDay.title],
          price: price.toString(),
          sku: sku,
          inventoryManagement: "SHOPIFY"
        };

        const variant = await createVariant(product.id, variantData);

        if (variant) {
          console.log(`バリアントを作成しました: ${variant.title} (${variant.sku})`);

          // 在庫数を設定
          await setInventory(variant.id, 2);
        } else {
          console.error(`バリアントの作成に失敗しました: ${rentalDay.title}`);
        }
      }
    }

    // メタフィールドの設定
    if (metafields) {
      console.log(`${product.title} のメタフィールドを設定中...`);

      // 基本情報
      if (metafields.basic_info) {
        console.log('基本情報を設定中...');
        await setJsonMetafield(product.id, 'rental', 'basic_info', metafields.basic_info);
      }

      // 料金設定
      if (metafields.pricing) {
        console.log('料金設定を設定中...');
        await setJsonMetafield(product.id, 'rental', 'pricing', metafields.pricing);
      }

      // 在庫アイテム情報
      if (metafields.inventory_items) {
        console.log('在庫アイテム情報を設定中...');
        await setJsonMetafield(product.id, 'rental', 'inventory_items', metafields.inventory_items);
      }

      // 予約情報
      if (metafields.reservation_info) {
        console.log('予約情報を設定中...');
        await setJsonMetafield(product.id, 'rental', 'reservation_info', metafields.reservation_info);
      }
    }
  }

  console.log('\n=== 作成完了 ===');
  console.log(`${createdProducts.length}件の商品を作成しました`);

  if (createdProducts.length > 0) {
    console.log('\n作成された商品:');
    createdProducts.forEach(product => {
      console.log(`- ${product.title} (ID: ${product.id})`);
    });

    console.log('\nShopify管理画面で商品を確認: https://admin.shopify.com/store/peaces-test-block/products');
  }
}

// 実行
createCorrectInventoryTestProducts();
