# テスト結果検証レポート

## 検証概要

このレポートは、テスト結果の自動検証結果をまとめたものです。

検証実行日時: 2025/5/19 12:43:33

## 検証結果

- 合計検証数: 8
- 成功: 4
- 失敗: 4
- 成功率: 50%

## 詳細結果

### 検証1: テスト結果の成功/失敗数が期待と異なります。

- 結果: ❌ 失敗
- 詳細:
```json
{
  "expectedSuccess": 4,
  "actualSuccess": 4,
  "expectedFailure": 0,
  "actualFailure": 3
}
```

### 検証2: テスト "商品同期" は期待通りの結果です。

- 結果: ✅ 成功
### 検証3: テスト "在庫カレンダー更新" は期待通りの結果です。

- 結果: ✅ 成功
### 検証4: テスト "予約作成" は期待通りの結果です。

- 結果: ✅ 成功
### 検証5: テスト "注文連携" は期待通りの結果です。

- 結果: ✅ 成功
### 検証6: テスト "商品バリエーション" の期待結果が定義されていません。

- 結果: ❌ 失敗
- 詳細:
```json
{
  "actual": {
    "testName": "商品バリエーション",
    "success": false,
    "timestamp": "2025-05-19T03:43:26.015Z",
    "message": "バリエーション商品のテストに失敗しました"
  }
}
```

### 検証7: テスト "エラーケース" の期待結果が定義されていません。

- 結果: ❌ 失敗
- 詳細:
```json
{
  "actual": {
    "testName": "エラーケース",
    "success": false,
    "timestamp": "2025-05-19T03:43:26.015Z",
    "message": "エラーケースのテストに失敗しました"
  }
}
```

### 検証8: テスト "テストデータのクリーンアップ" の期待結果が定義されていません。

- 結果: ❌ 失敗
- 詳細:
```json
{
  "actual": {
    "testName": "テストデータのクリーンアップ",
    "success": false,
    "timestamp": "2025-05-19T03:43:26.015Z",
    "message": "テストデータのクリーンアップに失敗しました"
  }
}
```

