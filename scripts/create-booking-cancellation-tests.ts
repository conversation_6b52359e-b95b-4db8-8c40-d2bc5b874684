/**
 * キャンセルパターンを含むテスト予約データ作成スクリプト
 * 
 * このスクリプトは、様々なキャンセルパターンを含むテスト予約データを作成します。
 * - 仮予約 → キャンセル（デポジット返金）
 * - 本予約 → キャンセル（7日前、100%返金）
 * - 本予約 → キャンセル（3日前、90%返金）
 * - 本予約 → キャンセル（当日、50%返金）
 * 
 * 使用方法:
 * npx tsx scripts/create-booking-cancellation-tests.ts
 */

import { PrismaClient, BookingStatus, BookingType, PaymentMethod, PaymentStatus } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// ショップドメイン
const SHOP = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';

// 現在の日付
const NOW = new Date();

// 日付ユーティリティ関数
const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

const subtractDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() - days);
  return result;
};

// 商品を取得するクエリ
const GET_PRODUCTS = gql`
  query getProducts($first: Int!) {
    products(first: $first) {
      edges {
        node {
          id
          title
          variants(first: 10) {
            edges {
              node {
                id
                title
                price
                inventoryQuantity
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
              }
            }
          }
        }
      }
    }
  }
`;

// 顧客を取得するクエリ
const GET_CUSTOMERS = gql`
  query getCustomers($first: Int!) {
    customers(first: $first) {
      edges {
        node {
          id
          firstName
          lastName
          email
          phone
        }
      }
    }
  }
`;

/**
 * 商品データを取得する
 */
async function getProducts() {
  console.log('商品データを取得中...');
  
  try {
    const result = await client.request(GET_PRODUCTS, {
      first: 10
    });

    const products = result.products.edges.map((edge: any) => {
      const product = edge.node;
      const variants = product.variants.edges.map((variantEdge: any) => variantEdge.node);
      
      // メタフィールドを解析
      const metafields: any = {};
      product.metafields.edges.forEach((metafieldEdge: any) => {
        const metafield = metafieldEdge.node;
        if (!metafields[metafield.namespace]) {
          metafields[metafield.namespace] = {};
        }
        
        try {
          metafields[metafield.namespace][metafield.key] = JSON.parse(metafield.value);
        } catch (e) {
          metafields[metafield.namespace][metafield.key] = metafield.value;
        }
      });
      
      return {
        ...product,
        variants,
        metafields
      };
    });
    
    if (products.length === 0) {
      throw new Error('商品データが見つかりません。先に商品データを作成してください。');
    }

    console.log(`${products.length}件の商品が見つかりました`);
    return products;
  } catch (error) {
    console.error('商品データの取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 顧客データを取得する
 */
async function getCustomers() {
  console.log('顧客データを取得中...');
  
  try {
    const result = await client.request(GET_CUSTOMERS, {
      first: 10
    });

    const customers = result.customers.edges.map((edge: any) => edge.node);
    
    if (customers.length === 0) {
      throw new Error('顧客データが見つかりません。先に顧客データを作成してください。');
    }

    console.log(`${customers.length}件の顧客が見つかりました`);
    return customers;
  } catch (error) {
    console.error('顧客データの取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * レンタル日数に基づいて適切なバリエーションを取得する
 */
function getVariantForRentalDays(product: any, days: number, isProvisional: boolean = false) {
  if (isProvisional) {
    // 仮予約バリエーションを探す
    return product.variants.find((variant: any) => 
      variant.title.includes('仮予約') || variant.title.includes('provisional')
    );
  }
  
  // 日数に応じたバリエーションを探す
  if (days >= 8) {
    return product.variants.find((variant: any) => 
      variant.title.includes('8日以上') || variant.title.includes('8plus')
    );
  }
  
  return product.variants.find((variant: any) => 
    variant.title.includes(`${days}日`) || variant.title.includes(`${days}day`)
  );
}

/**
 * Prismaに予約データを作成する
 */
async function createPrismaBooking(
  product: any,
  customer: any,
  startDate: Date,
  endDate: Date,
  status: BookingStatus,
  bookingType: BookingType,
  paymentStatus: PaymentStatus,
  refundRate: number = 0, // 返金率（0〜1）
  cancelDate: Date | null = null
) {
  try {
    const customerName = `${customer.firstName} ${customer.lastName}`;
    const customerId = customer.id.replace('gid://shopify/Customer/', '');
    
    // レンタル日数を計算
    const days = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    
    // 基本料金（1日目）
    const basePrice = product.variants[0].price ? parseFloat(product.variants[0].price) : 5000;
    
    // 料金計算
    let totalAmount = Math.min(basePrice, 5000); // 最大5000円に制限
    if (days > 1) {
      const day2to6Count = Math.min(days - 1, 5);
      totalAmount += totalAmount * 0.2 * day2to6Count;
    }
    if (days > 6) {
      const day7PlusCount = days - 6;
      totalAmount += totalAmount * 0.1 * day7PlusCount;
    }
    
    // デポジット金額（10%）
    const depositAmount = Math.round(totalAmount * 0.1);
    
    // キャンセル時のメモ
    let notes = `${customerName}様の${bookingType === BookingType.PROVISIONAL ? '仮予約' : '本予約'}`;
    if (status === BookingStatus.CANCELLED) {
      const refundPercent = Math.round(refundRate * 100);
      notes = `${customerName}様の${bookingType === BookingType.PROVISIONAL ? '仮予約' : '本予約'}（キャンセル、${refundPercent}%返金）`;
      if (cancelDate) {
        notes += ` - キャンセル日: ${cancelDate.toISOString().split('T')[0]}`;
      }
    }
    
    // 予約データを作成
    const booking = await prisma.booking.create({
      data: {
        productId: product.id,
        startDate,
        endDate,
        customerId,
        customerEmail: customer.email,
        customerName,
        customerPhone: customer.phone || '',
        totalAmount: totalAmount.toString(),
        depositAmount: depositAmount.toString(),
        depositPaid: status !== BookingStatus.CANCELLED || bookingType === BookingType.PROVISIONAL,
        bookingId: `BK-${uuidv4().substring(0, 8).toUpperCase()}`,
        shop: SHOP,
        bookingType,
        status,
        paymentStatus,
        paymentMethod: status === BookingStatus.CANCELLED ? null : PaymentMethod.CREDIT_CARD,
        paymentDate: status === BookingStatus.CANCELLED ? null : new Date(),
        notes,
        orderId: null,
        orderName: null,
      }
    });
    
    console.log(`予約を作成しました: ${booking.bookingId} (${status}, ${paymentStatus})`);
    return booking;
  } catch (error) {
    console.error('Prisma予約作成中にエラーが発生しました:', error);
    return null;
  }
}

/**
 * メインの実行関数
 */
async function main() {
  try {
    console.log('キャンセルパターンを含むテスト予約データの作成を開始します...');
    
    // 1. 商品データを取得
    const products = await getProducts();
    
    // 2. 顧客データを取得
    const customers = await getCustomers();
    
    // 3. 既存の予約データを削除
    const deletedBookings = await prisma.booking.deleteMany({
      where: {
        shop: SHOP,
        notes: {
          contains: 'キャンセル'
        }
      }
    });
    console.log(`${deletedBookings.count}件の既存キャンセル予約を削除しました`);
    
    // 4. 予約データを作成
    const bookings = [];
    
    // 4.1 仮予約 → キャンセル（デポジット返金）
    const provisionalCancelStartDate = addDays(NOW, 14);
    const provisionalCancelEndDate = addDays(NOW, 16);
    const provisionalCancelDays = 3;
    
    const provisionalCancelBooking = await createPrismaBooking(
      products[0],
      customers[0],
      provisionalCancelStartDate,
      provisionalCancelEndDate,
      BookingStatus.CANCELLED,
      BookingType.PROVISIONAL,
      PaymentStatus.REFUNDED,
      1.0, // 100%返金
      subtractDays(NOW, 2) // 2日前にキャンセル
    );
    bookings.push(provisionalCancelBooking);
    
    // 4.2 本予約 → キャンセル（7日前、100%返金）
    const confirmedCancel7DaysStartDate = addDays(NOW, 30);
    const confirmedCancel7DaysEndDate = addDays(NOW, 33);
    const confirmedCancel7DaysDays = 4;
    
    const confirmedCancel7DaysBooking = await createPrismaBooking(
      products[1],
      customers[1],
      confirmedCancel7DaysStartDate,
      confirmedCancel7DaysEndDate,
      BookingStatus.CANCELLED,
      BookingType.CONFIRMED,
      PaymentStatus.REFUNDED,
      1.0, // 100%返金
      subtractDays(confirmedCancel7DaysStartDate, 8) // 8日前にキャンセル
    );
    bookings.push(confirmedCancel7DaysBooking);
    
    // 4.3 本予約 → キャンセル（3日前、90%返金）
    const confirmedCancel3DaysStartDate = addDays(NOW, 20);
    const confirmedCancel3DaysEndDate = addDays(NOW, 22);
    const confirmedCancel3DaysDays = 3;
    
    const confirmedCancel3DaysBooking = await createPrismaBooking(
      products[2],
      customers[2],
      confirmedCancel3DaysStartDate,
      confirmedCancel3DaysEndDate,
      BookingStatus.CANCELLED,
      BookingType.CONFIRMED,
      PaymentStatus.REFUNDED,
      0.9, // 90%返金
      subtractDays(confirmedCancel3DaysStartDate, 3) // 3日前にキャンセル
    );
    bookings.push(confirmedCancel3DaysBooking);
    
    // 4.4 本予約 → キャンセル（当日、50%返金）
    const confirmedCancelSameDayStartDate = addDays(NOW, 10);
    const confirmedCancelSameDayEndDate = addDays(NOW, 12);
    const confirmedCancelSameDayDays = 3;
    
    const confirmedCancelSameDayBooking = await createPrismaBooking(
      products[3],
      customers[0],
      confirmedCancelSameDayStartDate,
      confirmedCancelSameDayEndDate,
      BookingStatus.CANCELLED,
      BookingType.CONFIRMED,
      PaymentStatus.REFUNDED,
      0.5, // 50%返金
      confirmedCancelSameDayStartDate // 当日キャンセル
    );
    bookings.push(confirmedCancelSameDayBooking);
    
    console.log('\n=== テスト予約データ作成結果 ===');
    console.log(`作成した予約: ${bookings.filter(b => b !== null).length}件`);
    console.log('=============================\n');
    
    console.log('キャンセルパターンを含むテスト予約データの作成が完了しました');
  } catch (error) {
    console.error('テスト予約データ作成中にエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('予期しないエラーが発生しました:', error);
    process.exit(1);
  });
