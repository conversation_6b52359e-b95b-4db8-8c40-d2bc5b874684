# Shopify顧客参照テスト結果

このドキュメントでは、顧客IDのみを保存し、必要時にShopify APIから顧客情報を取得する方式のテスト結果と実装計画について説明します。

## テスト概要

顧客データの最小化とプライバシー保護の観点から、Prismaデータベースには顧客IDのみを保存し、顧客情報が必要な時にShopify APIを通じて取得する方式をテストしました。

### テスト内容

1. **顧客一覧の取得**: Shopify APIを使用して顧客一覧を取得
2. **顧客IDからの情報取得**: 顧客IDを使って顧客の詳細情報を取得
3. **キャッシュ機能**: 同じ顧客情報の複数回取得におけるキャッシュの効果測定
4. **顧客名での検索**: 顧客名を使った検索機能のテスト
5. **メールアドレスでの検索**: メールアドレスを使った検索機能のテスト

## テスト結果

テストは成功し、以下の結果が確認できました：

### 1. 顧客一覧の取得

```
--- テスト1: 顧客一覧取得 ---
4件の顧客データを取得しました。
```

Shopify APIを使用して顧客一覧を正常に取得できました。

### 2. 顧客IDからの情報取得

```
--- テスト2: 顧客IDから情報取得 ---
テスト対象顧客: テスト 高橋 (ID: 8260724097192)

初回取得（キャッシュなし）:
取得時間: 318.35ms
取得データ: {
  "id": "gid://shopify/Customer/8260724097192",
  "firstName": "テスト",
  "lastName": "高橋",
  "email": "<EMAIL>",
  "phone": null,
  "createdAt": "2025-03-26T04:52:19Z",
  "defaultAddress": {
    "address1": "",
    "city": "",
    "country": "Japan",
    "zip": ""
  }
}...
```

顧客IDを使って顧客の詳細情報を正常に取得できました。初回取得時間は約318msでした。

### 3. キャッシュ機能

```
キャッシュありで複数回取得:
  取得 1: 297.86ms
  取得 2: 0.00ms
  取得 3: 0.00ms
  取得 4: 0.00ms
  取得 5: 0.00ms
平均取得時間: 59.57ms
```

キャッシュ機能が正常に動作し、2回目以降の取得は0.00msと高速でした。これにより、API呼び出しの回数を大幅に削減できることが確認できました。

### 4. 顧客名での検索

```
--- テスト3: 顧客名で検索 ---
検索キーワード: "テスト"
検索時間: 331.45ms
検索結果: 3件
最初の検索結果: {
  "id": "gid://shopify/Customer/8260724097192",
  "firstName": "テスト",
  "lastName": "高橋",
  "email": "<EMAIL>",
  "phone": null
}
```

顧客名「テスト」で検索し、3件の顧客を正常に見つけることができました。検索時間は約331msでした。

### 5. メールアドレスでの検索

```
--- テスト4: メールアドレスで検索 ---
検索キーワード: "<EMAIL>"
検索時間: 324.38ms
検索結果: 1件
最初の検索結果: {
  "id": "gid://shopify/Customer/8260724097192",
  "firstName": "テスト",
  "lastName": "高橋",
  "email": "<EMAIL>",
  "phone": null
}
```

メールアドレスで検索し、1件の顧客を正常に見つけることができました。検索時間は約324msでした。

## 実装計画

テスト結果から、顧客IDのみを保存し、必要時にShopify APIから顧客情報を取得する方式は実行可能であることが確認できました。以下に実装計画を示します：

### 1. 顧客参照サービスの実装

```typescript
// app/services/customer-reference.service.ts

export class CustomerReferenceService {
  private cache: Map<string, any> = new Map();
  private cacheTTL: number = 3600000; // 1時間キャッシュ

  constructor(private shopify: ShopifyService) {}

  // 顧客情報を取得（キャッシュ付き）
  async getCustomerById(customerId: string): Promise<any> {
    // キャッシュチェック
    const cacheKey = `customer_${customerId}`;
    const cachedData = this.cache.get(cacheKey);
    
    if (cachedData && cachedData.timestamp > Date.now() - this.cacheTTL) {
      return cachedData.data;
    }
    
    // Shopify APIから顧客情報を取得
    const customer = await this.shopify.getCustomer(customerId);
    
    // キャッシュに保存
    this.cache.set(cacheKey, {
      data: customer,
      timestamp: Date.now()
    });
    
    return customer;
  }
  
  // 顧客検索
  async searchCustomers(query: string): Promise<any[]> {
    return this.shopify.searchCustomers(query);
  }
}
```

### 2. データモデルの変更

Prismaスキーマを更新し、顧客関連のモデルから不要なフィールドを削除します：

```prisma
// prisma/schema.prisma

model Booking {
  id            String   @id @default(uuid())
  // 他のフィールド...
  
  // 顧客IDのみを保存
  customerId    String?
  
  // 以下のフィールドは削除
  // customerName  String?
  // customerEmail String?
  // customerPhone String?
  // customerAddress String?
}
```

### 3. UI/UXの更新

顧客情報表示コンポーネントを実装します：

```tsx
// app/components/CustomerDisplay.tsx

import { useState, useEffect } from 'react';
import { useCustomerReference } from '../hooks/useCustomerReference';

export function CustomerDisplay({ customerId }) {
  const [customer, setCustomer] = useState(null);
  const [loading, setLoading] = useState(true);
  const customerService = useCustomerReference();
  
  useEffect(() => {
    async function loadCustomer() {
      if (customerId) {
        setLoading(true);
        try {
          const data = await customerService.getCustomerById(customerId);
          setCustomer(data);
        } catch (error) {
          console.error('顧客情報取得エラー:', error);
        } finally {
          setLoading(false);
        }
      }
    }
    
    loadCustomer();
  }, [customerId]);
  
  if (loading) return <div>読み込み中...</div>;
  if (!customer) return <div>顧客情報がありません</div>;
  
  return (
    <div className="customer-info">
      <h3>{customer.firstName} {customer.lastName}</h3>
      <p>メール: {customer.email}</p>
      <p>電話: {customer.phone || '未設定'}</p>
    </div>
  );
}
```

## 期待される効果

1. **セキュリティとプライバシーの強化**
   - 顧客の個人情報がPrismaデータベースに保存されないため、データ漏洩リスクが低減
   - GDPRなどのプライバシー規制への対応が容易に

2. **データの一貫性向上**
   - 常に最新の顧客情報をShopifyから取得するため、データの不一致が発生しない
   - 顧客情報の更新がリアルタイムに反映される

3. **メンテナンス性の向上**
   - データモデルがシンプルになり、保守が容易に
   - 同期の複雑さが軽減される

4. **パフォーマンスの最適化**
   - キャッシュ機構により、API呼び出しの回数を最小限に抑えられる
   - 必要な時にのみ顧客情報を取得するため、リソース使用量が削減される

## 結論

テスト結果から、顧客IDのみを保存し、必要時にShopify APIから顧客情報を取得する方式は技術的に実現可能であり、セキュリティとパフォーマンスの両面でメリットがあることが確認できました。この方式を採用することで、データ保護を強化しつつ、必要な顧客情報へのアクセスを維持できます。
