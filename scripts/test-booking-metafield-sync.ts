/**
 * 予約メタフィールド同期テスト
 *
 * このスクリプトは、予約データがShopifyのメタフィールドに正しく同期されるかをテストします。
 * 実行方法: npx tsx scripts/test-booking-metafield-sync.ts [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { format, addDays } from 'date-fns';
import { ja } from 'date-fns/locale';
import { GraphQLClient } from 'graphql-request';
import { retry, handleError } from '../app/utils/booking/error-handler';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト設定
const config = {
  // テストする商品のShopify ID
  productId: process.argv[2] || '', // コマンドライン引数から取得

  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',

  // テスト用の顧客情報
  customerName: 'テスト顧客',
  customerEmail: '<EMAIL>',
  customerId: '12345678',

  // テスト用の予約期間（日数）
  bookingDays: 3
};

/**
 * 商品情報を取得する関数
 */
async function getProductInfo(productId: string) {
  try {
    // Prismaから商品情報を取得
    const product = await prisma.product.findFirst({
      where: {
        OR: [
          { id: productId },
          { shopifyId: productId }
        ]
      }
    });

    if (!product) {
      console.error(`商品ID ${productId} が見つかりません`);
      return null;
    }

    return product;
  } catch (error) {
    console.error('商品情報取得エラー:', error);
    return null;
  }
}

/**
 * 商品情報を表示する関数
 */
async function displayProductInfo(productId: string) {
  try {
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        bookings: {
          where: {
            status: {
              in: ['PROVISIONAL', 'CONFIRMED']
            }
          },
          orderBy: {
            startDate: 'asc'
          }
        }
      }
    });

    if (!product) {
      console.error(`商品ID ${productId} が見つかりません`);
      return;
    }

    console.log('----- 商品情報 -----');
    console.log(`ID: ${product.id}`);
    console.log(`Shopify ID: ${product.shopifyId}`);
    console.log(`タイトル: ${product.title}`);
    console.log(`SKU: ${product.sku}`);
    console.log(`価格: ${product.price}`);
    console.log(`ステータス: ${product.status}`);

    // 予約情報を表示
    console.log('\n----- 予約情報 -----');
    if (product.bookings.length === 0) {
      console.log('予約はありません');
    } else {
      product.bookings.forEach((booking, index) => {
        console.log(`予約 ${index + 1}:`);
        console.log(`  ID: ${booking.id}`);
        console.log(`  予約ID: ${booking.bookingId}`);
        console.log(`  開始日: ${format(booking.startDate, 'yyyy/MM/dd', { locale: ja })}`);
        console.log(`  終了日: ${format(booking.endDate, 'yyyy/MM/dd', { locale: ja })}`);
        console.log(`  ステータス: ${booking.status}`);
        console.log(`  顧客名: ${booking.customerName}`);
        console.log(`  顧客メール: ${booking.customerEmail}`);
        console.log(`  顧客ID: ${booking.customerId || 'なし'}`);
        console.log('---');
      });
    }
  } catch (error) {
    console.error('商品情報表示エラー:', error);
  }
}

/**
 * テスト用の予約を作成する関数
 */
async function createTestBooking(productId: string) {
  try {
    console.log(`商品ID ${productId} のテスト予約を作成します...`);

    // 商品情報を取得
    const product = await getProductInfo(productId);

    if (!product) {
      return null;
    }

    // 予約IDを生成
    const bookingId = `TEST-BOOK-${Date.now().toString().substring(7)}`;

    // 予約期間を設定
    const startDate = new Date();
    const endDate = addDays(startDate, config.bookingDays);

    // 予約を作成
    const booking = await prisma.booking.create({
      data: {
        bookingId,
        shop: config.shop,
        productId: product.id,
        startDate,
        endDate,
        customerName: config.customerName,
        customerEmail: config.customerEmail,
        customerId: config.customerId,
        status: 'PROVISIONAL',
        bookingType: 'PROVISIONAL',
        notes: 'テスト予約',
        priority: 1,
        totalAmount: product.price,
        depositAmount: product.price * 0.1, // デポジットは10%
      },
    });

    console.log('テスト予約が作成されました');
    console.log(`予約ID: ${booking.id}`);
    console.log(`開始日: ${format(booking.startDate, 'yyyy/MM/dd', { locale: ja })}`);
    console.log(`終了日: ${format(booking.endDate, 'yyyy/MM/dd', { locale: ja })}`);

    return booking;
  } catch (error) {
    console.error('テスト予約作成エラー:', error);
    return null;
  }
}

/**
 * メタフィールド同期をテストする関数
 */
async function testMetafieldSync(productId: string) {
  try {
    console.log(`商品ID ${productId} のメタフィールド同期テストを実行します...`);

    // 商品情報を取得
    const product = await getProductInfo(productId);

    if (!product) {
      return false;
    }

    // 同期前の商品情報を表示
    console.log('\n----- 同期前の商品情報 -----');
    await displayProductInfo(product.id);

    // GraphQLクライアントを初期化
    console.log('GraphQLクライアントを初期化中...');
    const shopifyAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

    if (!shopifyAccessToken) {
      throw new Error('SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません');
    }

    const graphQLClient = new GraphQLClient(
      `https://${config.shop}/admin/api/2025-01/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': shopifyAccessToken,
          'Content-Type': 'application/json',
        },
      }
    );

    // メタフィールドを同期
    console.log('\n----- メタフィールド同期を実行中... -----');

    try {
      // 商品の全予約情報を取得（有効な予約のみ）
      const bookings = await prisma.booking.findMany({
        where: {
          productId: product.id,
          status: {
            in: ['PROVISIONAL', 'CONFIRMED']
          }
        },
        orderBy: {
          startDate: 'asc'
        }
      });

      console.log(`予約情報を取得しました: ${bookings.length}件`);

      // 予約情報をメタフィールド用のフォーマットに変換
      const metafieldData = formatBookingsForMetafield(bookings);

      // Shopify IDを正規化
      const shopifyId = product.shopifyId.startsWith('gid://shopify/Product/')
        ? product.shopifyId
        : `gid://shopify/Product/${product.shopifyId}`;

      console.log('メタフィールド更新を実行します:');
      console.log('- shopifyId:', shopifyId);
      console.log('- namespace:', 'rental');
      console.log('- key:', 'bookings');
      console.log('- metafieldData:', JSON.stringify(metafieldData, null, 2));

      // メタフィールドの設定（リトライ機能付き）
      const result = await retry(
        async () => {
          console.log('GraphQL呼び出しを実行します...');

          const mutation = `
            mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
              metafieldsSet(metafields: $metafields) {
                metafields {
                  id
                  namespace
                  key
                  value
                }
                userErrors {
                  field
                  message
                }
              }
            }
          `;

          const variables = {
            metafields: [
              {
                ownerId: shopifyId,
                namespace: 'rental',
                key: 'bookings',
                value: JSON.stringify(metafieldData),
                type: 'json'
              }
            ]
          };

          const response = await graphQLClient.request(mutation, variables);
          console.log('GraphQL応答を受信しました');
          console.log('GraphQL応答内容:', JSON.stringify(response, null, 2));

          if (response.metafieldsSet.userErrors.length > 0) {
            console.error('メタフィールド更新エラー:', response.metafieldsSet.userErrors);
            throw new Error(`メタフィールドの設定中にエラーが発生しました: ${JSON.stringify(response.metafieldsSet.userErrors)}`);
          }

          return response;
        },
        3, // リトライ回数
        1000, // 1秒後にリトライ
        2 // 指数バックオフ（1秒、2秒、4秒...）
      );

      console.log('予約情報メタフィールドを更新しました');
      console.log('メタフィールド同期が成功しました');
      console.log(`メッセージ: ${JSON.stringify(result)}`);
    } catch (error) {
      console.error('メタフィールド同期エラー:', error);
      return false;
    }

    // 同期後の商品情報を表示
    console.log('\n----- 同期後の商品情報 -----');
    await displayProductInfo(product.id);

    return true;
  } catch (error) {
    console.error('メタフィールド同期テストエラー:', error);
    return false;
  }
}

/**
 * 予約情報をメタフィールド用のフォーマットに変換する関数
 */
function formatBookingsForMetafield(bookings: any[]): any {
  // バリアント別に予約をグループ化
  const bookingsByVariant: Record<string, any[]> = {};

  bookings.forEach(booking => {
    const variantId = booking.variantId || 'default';
    if (!bookingsByVariant[variantId]) {
      bookingsByVariant[variantId] = [];
    }

    bookingsByVariant[variantId].push({
      id: booking.bookingId,
      startDate: format(booking.startDate, 'yyyy-MM-dd'),
      endDate: format(booking.endDate, 'yyyy-MM-dd'),
      status: booking.status,
      type: booking.bookingType,
      customerName: booking.customerName,
      customerEmail: booking.customerEmail
    });
  });

  // バリアント情報を構築
  const variants: Record<string, any> = {};

  Object.entries(bookingsByVariant).forEach(([variantId, variantBookings]) => {
    variants[variantId] = {
      status: variantBookings.some(b => b.status === 'CONFIRMED') ? 'unavailable' : 'available',
      reservations: variantBookings
    };
  });

  // 従来の形式との互換性のために、すべての予約も含める
  const allBookings = bookings.map(booking => ({
    id: booking.bookingId,
    startDate: format(booking.startDate, 'yyyy-MM-dd'),
    endDate: format(booking.endDate, 'yyyy-MM-dd'),
    status: booking.status,
    customerName: booking.customerName,
    customerEmail: booking.customerEmail,
    variantId: booking.variantId
  }));

  // メタフィールド用のデータ構造
  const metafieldData = {
    // 従来の形式との互換性のために残す
    bookings: allBookings,
    // 新しい構造化データ
    status: Object.values(variants).some((v: any) => v.status === 'unavailable') ? 'unavailable' : 'available',
    lastUpdated: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
    variants,
    availability: {
      rentalStatus: 'available',
      startDate: format(new Date(), 'yyyy-MM-dd'),
      maintenanceDates: [],
      blockedDates: []
    }
  };

  return metafieldData;
}

/**
 * テスト予約を削除する関数
 */
async function cleanupTestBooking(bookingId: string) {
  try {
    console.log(`予約ID ${bookingId} を削除します...`);

    await prisma.booking.delete({
      where: { id: bookingId }
    });

    console.log('テスト予約が削除されました');
    return true;
  } catch (error) {
    console.error('テスト予約削除エラー:', error);
    return false;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('予約メタフィールド同期テストを開始します...');

    // コマンドライン引数から商品IDを取得
    const productId = config.productId || process.argv[2];

    if (!productId) {
      console.error('商品IDが指定されていません。コマンドライン引数で商品IDを指定してください。');
      console.error('例: npx tsx scripts/test-booking-metafield-sync.ts 123456789');
      process.exit(1);
    }

    // テスト予約を作成
    const booking = await createTestBooking(productId);

    if (!booking) {
      console.error('テスト予約の作成に失敗しました');
      process.exit(1);
    }

    // メタフィールド同期をテスト
    const testResult = await testMetafieldSync(productId);

    // テスト予約を削除
    const cleanupResult = await cleanupTestBooking(booking.id);

    // テスト結果のサマリー
    console.log('\n===== テスト結果サマリー =====');
    console.log(`テスト予約作成: 成功`);
    console.log(`メタフィールド同期テスト: ${testResult ? '成功' : '失敗'}`);
    console.log(`テスト予約削除: ${cleanupResult ? '成功' : '失敗'}`);

    if (testResult) {
      console.log('\nテストが成功しました！');
    } else {
      console.error('\nテストが失敗しました。');
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:');
    console.error(error);
    if (error instanceof Error) {
      console.error('エラーメッセージ:', error.message);
      console.error('スタックトレース:', error.stack);
    }
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
