/**
 * 在庫カレンダークリーンアップスクリプト
 *
 * 特定の商品の在庫カレンダーをクリーンアップするスクリプト
 * 実行方法: npx tsx scripts/cleanup-inventory-calendar.ts [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * メイン関数
 */
async function main() {
  try {
    // コマンドライン引数から商品IDを取得
    const productId = process.argv[2];
    
    if (!productId) {
      console.error('商品IDを指定してください');
      console.error('使用方法: npx tsx scripts/cleanup-inventory-calendar.ts [商品ID]');
      process.exit(1);
    }
    
    console.log(`商品ID ${productId} の在庫カレンダーをクリーンアップします...`);
    
    // 商品情報を取得
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });
    
    if (!product) {
      console.error(`商品ID ${productId} が見つかりません`);
      process.exit(1);
    }
    
    console.log(`商品「${product.title}」の在庫カレンダーをクリーンアップします...`);
    
    // 在庫カレンダーを削除
    const result = await prisma.inventoryCalendar.deleteMany({
      where: { productId }
    });
    
    console.log(`${result.count}件の在庫カレンダーを削除しました`);
    
    // 仮予約バリエーションの在庫カレンダーも削除
    const provisionalVariant = await prisma.product.findFirst({
      where: {
        shopifyId: `${product.shopifyId}-PROVISIONAL`
      }
    });
    
    if (provisionalVariant) {
      console.log(`仮予約バリエーション「${provisionalVariant.title}」の在庫カレンダーもクリーンアップします...`);
      
      const provisionalResult = await prisma.inventoryCalendar.deleteMany({
        where: { productId: provisionalVariant.id }
      });
      
      console.log(`${provisionalResult.count}件の仮予約バリエーション在庫カレンダーを削除しました`);
    }
    
    console.log('クリーンアップが完了しました');
  } catch (error) {
    console.error('クリーンアップ中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
