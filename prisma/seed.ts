import { PrismaClient, BookingStatus, ReturnTimeSlot } from '@prisma/client';
import { randomUUID } from 'crypto';

const prisma = new PrismaClient();

async function main() {
  // テスト用の商品を作成
  const product = await prisma.product.upsert({
    where: { id: 'test-product-1' },
    update: {},
    create: {
      id: 'test-product-1',
      title: 'テスト商品1',
      description: 'テスト用の商品です',
      price: 1000,
      shop: 'test-shop',
      shopifyId: 'shopify-test-1',
      sku: 'TEST-001',
      status: 'AVAILABLE',
    },
  });

  console.log('Created test product:', product);

  // テスト用の予約を作成
  const booking1 = await prisma.booking.upsert({
    where: { bookingId: 'test-booking-1' },
    update: {},
    create: {
      id: randomUUID(),
      bookingId: 'test-booking-1',
      productId: product.id,
      startDate: new Date('2025-05-20'),
      endDate: new Date('2025-05-25'),
      customerName: 'テスト顧客1',
      customerEmail: '<EMAIL>',
      status: BookingStatus.CONFIRMED,
      shop: 'test-shop',
      returnTimeSlot: ReturnTimeSlot.SAME_DAY_REUSABLE,
      returnNotes: '返却後同日再貸出可能',
    },
  });

  console.log('Created test booking 1:', booking1);

  const booking2 = await prisma.booking.upsert({
    where: { bookingId: 'test-booking-2' },
    update: {},
    create: {
      id: randomUUID(),
      bookingId: 'test-booking-2',
      productId: product.id,
      startDate: new Date('2025-06-01'),
      endDate: new Date('2025-06-05'),
      customerName: 'テスト顧客2',
      customerEmail: '<EMAIL>',
      status: BookingStatus.PROVISIONAL,
      shop: 'test-shop',
      returnTimeSlot: ReturnTimeSlot.STANDARD,
      returnNotes: '通常返却',
    },
  });

  console.log('Created test booking 2:', booking2);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
