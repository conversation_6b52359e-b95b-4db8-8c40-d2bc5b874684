import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  BlockStack,
  DataTable,
  Button,
  Badge,
  Pagination,
  EmptyState,
  TextField,
  ButtonGroup,
  Filters,
  InlineStack,
  Icon,
  Tooltip
} from "@shopify/polaris";
import { SearchIcon, CalendarIcon } from "@shopify/polaris-icons";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { useState } from "react";

/**
 * 予約管理ページ（インデックスページ）
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  // URLからパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get("page") || "1", 10);
  const pageSize = 20; // 表示件数
  const searchQuery = url.searchParams.get("q") || "";
  const statusFilter = url.searchParams.get("status") || "";
  const sortField = url.searchParams.get("sort") || "createdAt";
  const sortOrder = url.searchParams.get("order") || "desc";

  try {
    // 検索条件を構築
    const whereCondition: any = { shop };

    // 検索クエリがある場合
    if (searchQuery) {
      whereCondition.OR = [
        { customerName: { contains: searchQuery, mode: 'insensitive' } },
        { customerEmail: { contains: searchQuery, mode: 'insensitive' } },
        { bookingId: { contains: searchQuery, mode: 'insensitive' } },
      ];
    }

    // ステータスフィルターがある場合
    if (statusFilter) {
      whereCondition.status = statusFilter;
    }

    // 並び順を設定
    const orderBy: any = {};
    orderBy[sortField] = sortOrder;

    // 予約データを取得（商品情報も含める、shopifyOrderIdは除外）
    const bookings = await prisma.booking.findMany({
      where: whereCondition,
      orderBy,
      skip: (page - 1) * pageSize,
      take: pageSize,
      select: {
        id: true,
        bookingId: true,
        productId: true,
        startDate: true,
        endDate: true,
        status: true,
        customerName: true,
        customerEmail: true,
        totalAmount: true,
        createdAt: true,
        orderId: true,
        orderName: true,
        // shopifyOrderIdは除外
        // shopifyOrderNameは除外
        product: {
          select: {
            title: true,
            sku: true,
          },
        },
      },
    });

    // 総予約数を取得
    const totalBookings = await prisma.booking.count({
      where: whereCondition,
    });

    // 予約データをフォーマット
    const formattedBookings = bookings.map(booking => {
      // 商品情報を取得
      const sku = booking.product?.sku || "";
      const productTitle = booking.product?.title || "";

      return {
        id: booking.id,
        bookingId: booking.bookingId || "",
        productId: booking.productId,
        sku,
        productTitle,
        startDate: booking.startDate ? new Date(booking.startDate).toLocaleDateString() : "",
        endDate: booking.endDate ? new Date(booking.endDate).toLocaleDateString() : "",
        status: booking.status,
        customerName: booking.customerName || "",
        customerEmail: booking.customerEmail || "",
        totalAmount: booking.totalAmount ? `¥${booking.totalAmount.toLocaleString()}` : "",
        createdAt: booking.createdAt ? new Date(booking.createdAt).toLocaleString() : "",
      };
    });

    return json({
      bookings: formattedBookings,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalBookings / pageSize),
        totalItems: totalBookings,
      },
      filters: {
        query: searchQuery,
        status: statusFilter,
        sort: sortField,
        order: sortOrder,
      },
    });
  } catch (error) {
    console.error("予約データ取得エラー:", error);
    return json({
      bookings: [],
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
      },
      filters: {
        query: searchQuery,
        status: statusFilter,
        sort: sortField,
        order: sortOrder,
      },
      error: "予約データの取得中にエラーが発生しました"
    });
  }
}

export default function BookingsIndexPage() {
  const { bookings, pagination, filters, error } = useLoaderData<typeof loader>();
  const [searchValue, setSearchValue] = useState(filters.query);

  // ステータスに応じたバッジを生成
  const getStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case "DRAFT":
        return <Badge tone="info">ドラフト</Badge>;
      case "PROVISIONAL":
        return <Badge tone="attention">仮予約</Badge>;
      case "CONFIRMED":
        return <Badge tone="success">確定</Badge>;
      case "CANCELLED":
        return <Badge tone="critical">キャンセル</Badge>;
      case "COMPLETED":
        return <Badge tone="success">完了</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // 検索フォームの送信処理
  const handleSearch = () => {
    const url = new URL(window.location.href);
    url.searchParams.set("q", searchValue);
    url.searchParams.set("page", "1"); // 検索時は1ページ目に戻る

    // 検索クエリが空の場合はパラメータを削除
    if (!searchValue) {
      url.searchParams.delete("q");
    }

    window.location.href = url.toString();
  };

  // ステータスフィルターの変更処理
  const handleStatusChange = (status: string) => {
    const url = new URL(window.location.href);
    url.searchParams.set("status", status);
    url.searchParams.set("page", "1"); // フィルター変更時は1ページ目に戻る
    window.location.href = url.toString();
  };

  // ページネーションの処理
  const handlePageChange = (page: number) => {
    const url = new URL(window.location.href);
    url.searchParams.set("page", page.toString());
    window.location.href = url.toString();
  };

  // データテーブルの行を生成
  const rows = bookings.map(booking => [
    <Link to={`/app/bookings/${booking.id}`}>{booking.bookingId || booking.id.substring(0, 8)}</Link>,
    <Text fontWeight="bold">{booking.sku}</Text>,
    <Text>{booking.productTitle}</Text>,
    booking.startDate,
    booking.endDate,
    booking.totalAmount,
    getStatusBadge(booking.status),
    booking.customerName,
    booking.customerEmail,
    booking.createdAt,
  ]);

  // ステータスフィルターオプション
  const statusOptions = [
    { label: "すべて", value: "" },
    { label: "ドラフト", value: "DRAFT" },
    { label: "仮予約", value: "PROVISIONAL" },
    { label: "確定", value: "CONFIRMED" },
    { label: "キャンセル", value: "CANCELLED" },
    { label: "完了", value: "COMPLETED" },
  ];

  // URLからsuccessパラメータを取得
  const url = new URL(window.location.href);
  const success = url.searchParams.get("success");
  const count = url.searchParams.get("count");

  return (
    <Page
      title="予約一覧"
      subtitle="レンタル予約の管理と確認"
      backAction={{ content: "ホーム", url: "/app" }}
      primaryAction={{
        content: "新規予約",
        url: "/app/bookings/new",
      }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                {error && (
                  <Text tone="critical">{error}</Text>
                )}

                {/* 検索とフィルターセクション */}
                <Box paddingBlockEnd="400">
                  <InlineStack gap="400" align="start" blockAlign="center">
                    <div style={{ flex: 1 }}>
                      <div style={{ display: 'flex' }}>
                        <div style={{ flex: 1 }}>
                          <TextField
                            label="検索"
                            type="search"
                            value={searchValue}
                            placeholder="商品名、SKU、顧客名、メールで検索"
                            prefix={<Icon source={SearchIcon} />}
                            onClearButtonClick={() => setSearchValue("")}
                            onChange={(value) => {
                              console.log('検索値変更:', value);
                              setSearchValue(value);
                            }}
                            autoComplete="off"
                          />
                        </div>
                        <div style={{ marginLeft: '8px', marginTop: '24px' }}>
                          <Button onClick={handleSearch}>検索</Button>
                        </div>
                      </div>
                    </div>
                    <div style={{ minWidth: "200px" }}>
                      <Filters
                        queryValue={filters.status}
                        filters={[
                          {
                            key: "status",
                            label: "ステータス",
                            filter: (
                              <select
                                value={filters.status}
                                onChange={(e) => handleStatusChange(e.target.value)}
                                style={{ width: "100%", padding: "8px", borderRadius: "4px", border: "1px solid #ccc" }}
                              >
                                {statusOptions.map((option) => (
                                  <option key={option.value} value={option.value}>
                                    {option.label}
                                  </option>
                                ))}
                              </select>
                            ),
                            shortcut: true,
                          },
                        ]}
                        onQueryChange={(value) => setSearchValue(value)}
                        onQueryClear={() => setSearchValue("")}
                      />
                    </div>
                    <div>
                      <ButtonGroup segmented>
                        <Tooltip content="カレンダー表示">
                          <Button icon={CalendarIcon} url="/app/bookings/calendar" />
                        </Tooltip>
                      </ButtonGroup>
                    </div>
                  </InlineStack>
                </Box>

                {bookings.length === 0 ? (
                  <EmptyState
                    heading="予約データがありません"
                    image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                  >
                    <p>検索条件に一致する予約データが見つかりません。</p>
                  </EmptyState>
                ) : (
                  <DataTable
                    columnContentTypes={[
                      "text",
                      "text",
                      "text",
                      "text",
                      "text",
                      "text",
                      "text",
                      "text",
                      "text",
                      "text",
                    ]}
                    headings={[
                      "予約ID",
                      "SKU",
                      "商品名",
                      "開始日",
                      "終了日",
                      "金額",
                      "ステータス",
                      "顧客名",
                      "メール",
                      "作成日時",
                    ]}
                    rows={rows}
                    hoverable
                    increasedTableDensity
                  />
                )}

                {pagination.totalPages > 1 && (
                  <Box paddingBlockStart="400">
                    <Pagination
                      hasPrevious={pagination.currentPage > 1}
                      onPrevious={() => handlePageChange(pagination.currentPage - 1)}
                      hasNext={pagination.currentPage < pagination.totalPages}
                      onNext={() => handlePageChange(pagination.currentPage + 1)}
                    />
                    <div style={{ textAlign: "center", marginTop: "10px" }}>
                      <Text as="span">全{pagination.totalItems}件中 {(pagination.currentPage - 1) * 20 + 1}-{Math.min(pagination.currentPage * 20, pagination.totalItems)}件を表示</Text>
                    </div>
                  </Box>
                )}
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
