/**
 * 注文作成再試行API
 * 
 * 予約の注文作成に失敗した場合に、注文作成を再試行するAPI
 */

import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "~/shopify.server";
import { prisma } from "~/db.server";
import { createOrderFromBooking } from "~/utils/booking/order-creator";
import { retry, handleError } from "~/utils/booking/error-handler";

export async function action({ request, params }: ActionFunctionArgs) {
  try {
    // 認証
    const { admin, session } = await authenticate.admin(request);
    const { id } = params;

    if (!id) {
      return json({ success: false, message: "予約IDが指定されていません" }, { status: 400 });
    }

    // フォームデータを取得
    const formData = await request.formData();
    const action = formData.get("action");

    if (action !== "retry_order_creation") {
      return json({ success: false, message: "無効なアクションです" }, { status: 400 });
    }

    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id },
      include: {
        product: true
      }
    });

    if (!booking) {
      return json({ success: false, message: "予約が見つかりません" }, { status: 404 });
    }

    console.log(`予約ID ${booking.id} の注文作成を再試行します...`);

    // 注文作成処理を3回までリトライ
    const orderResult = await retry(
      async () => {
        console.log(`予約ID ${booking.id} の注文を作成します...`);
        return await createOrderFromBooking(prisma, admin, booking.id);
      },
      3, // リトライ回数
      1000, // 1秒後にリトライ
      2 // 指数バックオフ（1秒、2秒、4秒...）
    );

    if (orderResult && orderResult.success) {
      console.log('注文作成結果:', orderResult);
      console.log(`予約ID ${booking.id} の注文を作成しました: 注文ID ${orderResult.orderId}, 注文番号 ${orderResult.orderName}`);

      // 予約データを更新して注文情報を関連付け
      await prisma.booking.update({
        where: { id: booking.id },
        data: {
          orderId: orderResult.orderId,
          orderName: orderResult.orderName,
          paymentStatus: booking.bookingType === 'PROVISIONAL' ? 'PENDING' : 'COMPLETED',
          // 注文作成成功フラグを追加
          metadata: {
            ...(booking.metadata || {}),
            orderCreationSuccess: true,
            orderCreationDate: new Date().toISOString(),
            // エラー情報をクリア
            orderCreationError: false,
            orderCreationErrorMessage: null,
            orderCreationErrorDate: null,
            orderCreationErrorDetails: null
          }
        }
      });

      console.log(`予約ID ${booking.id} に注文情報を関連付けました`);

      return json({
        success: true,
        message: "注文が正常に作成されました",
        orderId: orderResult.orderId,
        orderName: orderResult.orderName
      });
    } else {
      // 注文作成失敗の場合
      console.error(`予約ID ${booking.id} の注文作成に失敗しました:`, orderResult);
      
      // エラー情報を予約データに保存
      await prisma.booking.update({
        where: { id: booking.id },
        data: {
          metadata: {
            ...(booking.metadata || {}),
            orderCreationError: true,
            orderCreationErrorMessage: orderResult.errorMessage || '注文作成に失敗しました',
            orderCreationErrorDate: new Date().toISOString(),
            orderCreationErrorDetails: JSON.stringify(orderResult.error || {})
          }
        }
      });
      
      return json({
        success: false,
        message: orderResult.errorMessage || "注文作成に失敗しました",
        error: orderResult.error
      }, { status: 500 });
    }
  } catch (error) {
    // エラーハンドリング
    const errorInfo = handleError(error, {
      operation: "retry_order_creation",
      params
    }, true);

    return json({
      success: false,
      message: errorInfo.message,
      error: errorInfo
    }, { status: 500 });
  }
}
