/**
 * Shopify Admin API接続テストスクリプト
 */

import { GraphQLClient } from 'graphql-request';
import dotenv from 'dotenv';

dotenv.config();

const config = {
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
  apiSecretKey: process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
};

// ショップ名を正規化（.myshopify.comを除去）
const shopName = config.shop.replace('.myshopify.com', '');

console.log('Shopify設定:');
console.log('Shop:', config.shop);
console.log('API Token:', config.apiSecretKey ? 'あり (長さ: ' + config.apiSecretKey.length + ')' : 'なし');

if (!config.apiSecretKey) {
  console.error('SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません');
  process.exit(1);
}

// Shopify公式推奨のAPI バージョン 2025-01を使用
const shopifyClient = new GraphQLClient(
  `https://${shopName}.myshopify.com/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': config.apiSecretKey,
      'Content-Type': 'application/json',
    },
  }
);

// 簡単なクエリでAPI接続をテスト
const TEST_QUERY = `
  query {
    shop {
      name
      email
      myshopifyDomain
      plan {
        displayName
      }
    }
  }
`;

// 商品一覧を取得するクエリ（Shopify公式ドキュメント準拠）
const GET_PRODUCTS_QUERY = `
  query getProducts($first: Int!, $after: String) {
    products(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          handle
          status
          createdAt
          updatedAt
          productType
          vendor
          tags
          variants(first: 10) {
            edges {
              node {
                id
                title
                sku
                inventoryQuantity
                inventoryPolicy
                price
                compareAtPrice
                availableForSale
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    }
  }
`;

async function testConnection() {
  try {
    console.log('\nShopify Admin APIへの接続をテストしています...');
    const result = await shopifyClient.request(TEST_QUERY);

    console.log('接続成功!');
    console.log('ショップ名:', result.shop.name);
    console.log('ドメイン:', result.shop.myshopifyDomain);
    console.log('プラン:', result.shop.plan.displayName);
    console.log('メール:', result.shop.email);

    return true;
  } catch (error) {
    console.error('接続エラー:', error.message);
    if (error.response) {
      console.error('レスポンス:', error.response.errors);
    }
    return false;
  }
}

async function testProductsQuery() {
  try {
    console.log('\n商品データの取得をテストしています...');
    const result = await shopifyClient.request(GET_PRODUCTS_QUERY, {
      first: 5,
      after: null
    });

    const products = result.products.edges;
    console.log(`商品数: ${products.length}`);

    products.forEach((edge, index) => {
      const product = edge.node;
      console.log(`\n商品 ${index + 1}:`);
      console.log(`  ID: ${product.id}`);
      console.log(`  タイトル: ${product.title}`);
      console.log(`  ハンドル: ${product.handle}`);
      console.log(`  ステータス: ${product.status}`);
      console.log(`  タイプ: ${product.productType}`);
      console.log(`  ベンダー: ${product.vendor}`);
      console.log(`  バリアント数: ${product.variants.edges.length}`);

      if (product.variants.edges.length > 0) {
        const variant = product.variants.edges[0].node;
        console.log(`  最初のバリアント:`);
        console.log(`    ID: ${variant.id}`);
        console.log(`    SKU: ${variant.sku}`);
        console.log(`    価格: ${variant.price}`);
        console.log(`    在庫: ${variant.inventoryQuantity}`);
      }
    });

    return products;
  } catch (error) {
    console.error('商品取得エラー:', error.message);
    if (error.response) {
      console.error('レスポンス:', error.response.errors);
    }
    return [];
  }
}

async function main() {
  const connectionOk = await testConnection();

  if (connectionOk) {
    const products = await testProductsQuery();

    if (products.length > 0) {
      console.log('\n=== 推奨する商品ID ===');
      products.forEach((edge, index) => {
        const product = edge.node;
        const shopifyId = product.id.replace('gid://shopify/Product/', '');
        console.log(`商品 ${index + 1}: ${shopifyId} (${product.title})`);
      });
    } else {
      console.log('\n商品が見つかりませんでした。Shopifyストアに商品を追加してください。');
    }
  }
}

main().catch(console.error);
