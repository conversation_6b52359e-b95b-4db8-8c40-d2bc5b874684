/**
 * 検索機能テストスクリプト
 *
 * /app/bookings の検索機能をテストします：
 * - 商品名検索
 * - SKU検索
 * - 顧客名検索
 * - メール検索
 * - 顧客ID検索
 */

const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

// テスト用データの作成
async function createTestData() {
  console.log('=== テスト用データを作成中... ===');

  const shop = 'peaces-test-block.myshopify.com';

  try {
    // テスト用商品を作成
    const testProducts = [
      {
        title: 'シルバーネックレス',
        sku: 'SLV-001',
        status: 'AVAILABLE',
        shopifyId: 'gid://shopify/Product/8872753823912',
        shop: shop,
        price: 5000,
        basicInfo: {
          productCode: 'SLV-001',
          category: 'jewelry'
        }
      },
      {
        title: 'ゴールドリング',
        sku: 'GLD-002',
        status: 'AVAILABLE',
        shopifyId: 'gid://shopify/Product/8872753823913',
        shop: shop,
        price: 8000,
        basicInfo: {
          productCode: 'GLD-002',
          category: 'jewelry'
        }
      },
      {
        title: 'シルバーブレスレット',
        sku: 'SLV-003',
        status: 'AVAILABLE',
        shopifyId: 'gid://shopify/Product/8872753823914',
        shop: shop,
        price: 6000,
        basicInfo: {
          productCode: 'SLV-003',
          category: 'jewelry'
        }
      }
    ];

    // 既存の商品を削除
    await prisma.product.deleteMany({
      where: {
        shop: shop,
        sku: { in: ['SLV-001', 'GLD-002', 'SLV-003'] }
      }
    });

    // 新しい商品を作成
    const createdProducts = [];
    for (const productData of testProducts) {
      const product = await prisma.product.create({
        data: productData
      });
      createdProducts.push(product);
      console.log(`商品作成: ${product.title} (${product.sku})`);
    }

    // テスト用予約を作成
    const testBookings = [
      {
        productId: createdProducts[0].id,
        customerName: '佐藤 花子',
        customerEmail: '<EMAIL>',
        customerId: 'cust_001',
        bookingId: 'BOOK-001',
        startDate: new Date('2025-06-01'),
        endDate: new Date('2025-06-03'),
        status: 'CONFIRMED',
        shop: shop,
        totalAmount: 5000
      },
      {
        productId: createdProducts[1].id,
        customerName: '田中 太郎',
        customerEmail: '<EMAIL>',
        customerId: 'cust_002',
        bookingId: 'BOOK-002',
        startDate: new Date('2025-06-05'),
        endDate: new Date('2025-06-07'),
        status: 'PROVISIONAL',
        shop: shop,
        totalAmount: 8000
      },
      {
        productId: createdProducts[2].id,
        customerName: '鈴木一郎',
        customerEmail: '<EMAIL>',
        customerId: 'cust_003',
        bookingId: 'BOOK-003',
        startDate: new Date('2025-06-10'),
        endDate: new Date('2025-06-12'),
        status: 'CONFIRMED',
        shop: shop,
        totalAmount: 6000
      }
    ];

    // 既存の予約を削除
    await prisma.booking.deleteMany({
      where: {
        shop: shop,
        bookingId: { in: ['BOOK-001', 'BOOK-002', 'BOOK-003'] }
      }
    });

    // 新しい予約を作成
    for (const bookingData of testBookings) {
      const booking = await prisma.booking.create({
        data: bookingData
      });
      console.log(`予約作成: ${booking.bookingId} - ${booking.customerName}`);
    }

    console.log('テスト用データの作成が完了しました\n');
    return { products: createdProducts, bookings: testBookings };

  } catch (error) {
    console.error('テスト用データ作成エラー:', error);
    throw error;
  }
}

// 検索テスト関数
async function testSearch(query, description) {
  console.log(`\n=== ${description}: "${query}" ===`);

  const shop = 'peaces-test-block.myshopify.com';

  try {
    // 検索条件を構築（app/bookings._index.tsx と同じロジック）
    const trimmedQuery = query.trim().toLowerCase();

    // 基本的な検索条件
    const searchConditions = [
      { customerName: { contains: trimmedQuery, mode: 'insensitive' } },
      { customerEmail: { contains: trimmedQuery, mode: 'insensitive' } },
      { customerId: { contains: trimmedQuery, mode: 'insensitive' } },
      { bookingId: { contains: trimmedQuery, mode: 'insensitive' } }
    ];

    // 商品検索条件
    const productSearchCondition = {
      product: {
        OR: [
          { title: { contains: trimmedQuery, mode: 'insensitive' } },
          { sku: { contains: trimmedQuery, mode: 'insensitive' } }
        ]
      }
    };

    searchConditions.push(productSearchCondition);

    // 最終的な検索条件
    const whereCondition = {
      shop,
      OR: searchConditions
    };

    // 検索実行
    const bookings = await prisma.booking.findMany({
      where: whereCondition,
      include: {
        product: {
          select: {
            title: true,
            sku: true,
            status: true
          }
        }
      },
      take: 10
    });

    console.log(`検索結果: ${bookings.length}件の予約が見つかりました`);

    // 結果を表示
    if (bookings.length > 0) {
      bookings.forEach((booking, index) => {
        console.log(`\n[${index + 1}] 予約情報:`);
        console.log(`  予約ID: ${booking.bookingId || 'なし'}`);
        console.log(`  顧客名: ${booking.customerName || 'なし'}`);
        console.log(`  顧客メール: ${booking.customerEmail || 'なし'}`);
        console.log(`  顧客ID: ${booking.customerId || 'なし'}`);
        console.log(`  商品: ${booking.product?.title || 'なし'} (${booking.product?.sku || 'なし'})`);
        console.log(`  ステータス: ${booking.status}`);
        console.log(`  期間: ${booking.startDate?.toISOString().split('T')[0]} ～ ${booking.endDate?.toISOString().split('T')[0]}`);
      });
    } else {
      console.log('  該当する予約が見つかりませんでした');
    }

    return bookings.length > 0;

  } catch (error) {
    console.error('検索エラー:', error);
    return false;
  }
}

// メイン関数
async function main() {
  console.log('検索機能テストを開始します...\n');

  try {
    // テスト用データを作成
    await createTestData();

    // 検索テストケース
    const testCases = [
      // 商品名検索
      { query: 'シルバー', description: '商品名検索（部分一致）' },
      { query: 'シルバーネックレス', description: '商品名検索（完全一致）' },
      { query: 'ゴールド', description: '商品名検索（部分一致）' },

      // SKU検索
      { query: 'SLV', description: 'SKU検索（部分一致）' },
      { query: 'SLV-001', description: 'SKU検索（完全一致）' },
      { query: 'GLD-002', description: 'SKU検索（完全一致）' },

      // 顧客名検索
      { query: '佐藤', description: '顧客名検索（姓）' },
      { query: '花子', description: '顧客名検索（名）' },
      { query: '佐藤 花子', description: '顧客名検索（フルネーム・スペースあり）' },
      { query: '佐藤花子', description: '顧客名検索（フルネーム・スペースなし）' },
      { query: '鈴木一郎', description: '顧客名検索（スペースなし）' },

      // メール検索
      { query: 'sato.hanako', description: 'メール検索（部分一致）' },
      { query: '<EMAIL>', description: 'メール検索（完全一致）' },
      { query: '@example.com', description: 'メール検索（ドメイン）' },

      // 顧客ID検索
      { query: 'cust_001', description: '顧客ID検索' },
      { query: 'cust', description: '顧客ID検索（部分一致）' },

      // 予約ID検索
      { query: 'BOOK-001', description: '予約ID検索' },
      { query: 'BOOK', description: '予約ID検索（部分一致）' },

      // 存在しない検索
      { query: '存在しない商品', description: '存在しない検索' },
      { query: 'nonexistent', description: '存在しない英語検索' }
    ];

    // 各テストケースを実行
    let successCount = 0;
    let totalCount = testCases.length;

    for (const testCase of testCases) {
      const success = await testSearch(testCase.query, testCase.description);
      if (success) successCount++;
    }

    // テスト結果サマリー
    console.log('\n=== テスト結果サマリー ===');
    console.log(`成功: ${successCount}/${totalCount} テスト`);
    console.log(`失敗: ${totalCount - successCount}/${totalCount} テスト`);

    if (successCount === totalCount) {
      console.log('✅ すべてのテストが成功しました！');
    } else {
      console.log('⚠️  一部のテストが失敗しました');
    }

  } catch (error) {
    console.error('テスト実行エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
main()
  .then(() => {
    console.log('\n検索機能テストが完了しました');
    process.exit(0);
  })
  .catch((error) => {
    console.error('予期しないエラー:', error);
    process.exit(1);
  });
