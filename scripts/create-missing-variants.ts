/**
 * 既存商品の不足バリエーション一括作成スクリプト
 *
 * 既にShopifyに登録されている商品で、バリエーションが不足している商品に
 * 必要なレンタル期間バリエーションを自動作成します
 */

import { authenticate } from '../app/shopify.server';
import { VariantAutoCreatorService } from '../app/services/shopify/variant-auto-creator.service';
import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

interface ProductInfo {
  shopifyId: string;
  title: string;
  basePrice: number;
  existingVariants: number;
}

class MissingVariantCreator {
  private variantCreator: VariantAutoCreatorService;
  private results: Array<{
    productId: string;
    title: string;
    success: boolean;
    createdCount: number;
    errors: string[];
  }> = [];

  constructor() {
    this.variantCreator = VariantAutoCreatorService.getInstance();
  }

  /**
   * 全商品の不足バリエーションを作成
   */
  async createMissingVariantsForAllProducts(): Promise<void> {
    console.log('=== 全商品バリエーション不足チェック開始 ===\n');

    try {
      // セッションを取得（最初に見つかったオフラインセッションを使用）
      const session = await prisma.session.findFirst({
        where: {
          id: {
            startsWith: 'offline_'
          }
        }
      });

      if (!session) {
        console.error('❌ オフラインセッションが見つかりません');
        return;
      }

      console.log(`✅ セッション取得成功: ${session.shop}`);

      // Shopify Admin APIクライアントを作成
      const { admin } = await authenticate.admin({
        session: JSON.parse(session.state)
      });

      // Shopifyから全商品を取得
      const products = await this.getAllProducts(admin);
      console.log(`📦 取得した商品数: ${products.length}\n`);

      // 各商品のバリエーションをチェック・作成
      for (const product of products) {
        await this.processProduct(admin, product);

        // API制限を避けるため少し待機
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      this.displayResults();

    } catch (error) {
      console.error('❌ バリエーション作成処理エラー:', error);
    } finally {
      await prisma.$disconnect();
    }
  }

  /**
   * 特定商品のバリエーションを作成
   */
  async createMissingVariantsForProduct(shopifyProductId: string): Promise<void> {
    console.log(`=== 商品 ${shopifyProductId} のバリエーション作成 ===\n`);

    try {
      // セッションを取得
      const session = await prisma.session.findFirst({
        where: {
          id: {
            startsWith: 'offline_'
          }
        }
      });

      if (!session) {
        console.error('❌ オフラインセッションが見つかりません');
        return;
      }

      // Shopify Admin APIクライアントを作成
      const { admin } = await authenticate.admin({
        session: JSON.parse(session.state)
      });

      // 商品情報を取得
      const product = await this.getProductInfo(admin, shopifyProductId);

      if (!product) {
        console.error('❌ 商品が見つかりません');
        return;
      }

      // バリエーションを作成
      await this.processProduct(admin, product);
      this.displayResults();

    } catch (error) {
      console.error('❌ バリエーション作成エラー:', error);
    } finally {
      await prisma.$disconnect();
    }
  }

  /**
   * Shopifyから全商品を取得
   */
  private async getAllProducts(admin: any): Promise<ProductInfo[]> {
    const products: ProductInfo[] = [];
    let hasNextPage = true;
    let cursor = null;

    while (hasNextPage) {
      const query = `
        query getProducts($first: Int!, $after: String) {
          products(first: $first, after: $after) {
            edges {
              node {
                id
                title
                variants(first: 50) {
                  edges {
                    node {
                      id
                      title
                      price
                    }
                  }
                }
              }
              cursor
            }
            pageInfo {
              hasNextPage
            }
          }
        }
      `;

      const response = await admin.graphql(query, {
        first: 50,
        after: cursor
      });

      const data = await response.json();

      if (data.errors) {
        throw new Error(`GraphQL エラー: ${JSON.stringify(data.errors)}`);
      }

      const edges = data.data.products.edges;

      for (const edge of edges) {
        const product = edge.node;
        const shopifyId = product.id.replace('gid://shopify/Product/', '');
        const variants = product.variants.edges;

        // 基本価格を取得（最初のバリエーションの価格）
        const basePrice = variants.length > 0 ? parseFloat(variants[0].node.price) : 0;

        if (basePrice > 0) {
          products.push({
            shopifyId,
            title: product.title,
            basePrice,
            existingVariants: variants.length
          });
        }
      }

      hasNextPage = data.data.products.pageInfo.hasNextPage;
      cursor = edges.length > 0 ? edges[edges.length - 1].cursor : null;
    }

    return products;
  }

  /**
   * 特定商品の情報を取得
   */
  private async getProductInfo(admin: any, shopifyProductId: string): Promise<ProductInfo | null> {
    try {
      const response = await admin.graphql(`
        query getProduct($id: ID!) {
          product(id: $id) {
            id
            title
            variants(first: 50) {
              edges {
                node {
                  id
                  title
                  price
                }
              }
            }
          }
        }
      `, {
        id: `gid://shopify/Product/${shopifyProductId}`
      });

      const data = await response.json();

      if (data.errors) {
        throw new Error(`GraphQL エラー: ${JSON.stringify(data.errors)}`);
      }

      const product = data.data.product;
      if (!product) {
        return null;
      }

      const variants = product.variants.edges;
      const basePrice = variants.length > 0 ? parseFloat(variants[0].node.price) : 0;

      return {
        shopifyId,
        title: product.title,
        basePrice,
        existingVariants: variants.length
      };

    } catch (error) {
      console.error('商品情報取得エラー:', error);
      return null;
    }
  }

  /**
   * 単一商品を処理
   */
  private async processProduct(admin: any, product: ProductInfo): Promise<void> {
    console.log(`--- ${product.title} ---`);
    console.log(`基本価格: ¥${product.basePrice.toLocaleString()}`);
    console.log(`既存バリエーション数: ${product.existingVariants}`);

    try {
      const result = await this.variantCreator.createMissingVariants(admin, product.shopifyId, {
        basePrice: product.basePrice,
        createDays: [1, 2, 3, 4, 5, 6, 7],
        createProvisionalVariants: false,
        productStatus: 'available', // 既存商品はavailableとして設定
        location: 'NY', // デフォルト在庫場所
        sku: product.shopifyId // 商品IDをSKUとして使用
      });

      if (result.createdVariants.length > 0) {
        console.log(`✅ ${result.createdVariants.length}個のバリエーションを作成しました`);
        result.createdVariants.forEach(variant => {
          console.log(`  - ${variant.title}: ¥${variant.price.toLocaleString()}`);
        });
      } else {
        console.log('✅ すべてのバリエーションが既に存在します');
      }

      if (result.skippedVariants.length > 0) {
        console.log(`⚠️ ${result.skippedVariants.length}個のバリエーションをスキップしました`);
        result.skippedVariants.forEach(variant => {
          console.log(`  - ${variant.days}日: ${variant.reason}`);
        });
      }

      this.results.push({
        productId: product.shopifyId,
        title: product.title,
        success: result.success,
        createdCount: result.createdVariants.length,
        errors: result.errors
      });

    } catch (error) {
      console.error(`❌ エラー: ${error.message}`);
      this.results.push({
        productId: product.shopifyId,
        title: product.title,
        success: false,
        createdCount: 0,
        errors: [error.message]
      });
    }

    console.log('');
  }

  /**
   * 結果を表示
   */
  private displayResults(): void {
    console.log('=== バリエーション作成結果サマリー ===');

    const totalProducts = this.results.length;
    const successProducts = this.results.filter(r => r.success).length;
    const totalCreated = this.results.reduce((sum, r) => sum + r.createdCount, 0);
    const errorProducts = this.results.filter(r => !r.success).length;

    console.log(`処理した商品数: ${totalProducts}`);
    console.log(`成功: ${successProducts}`);
    console.log(`失敗: ${errorProducts}`);
    console.log(`作成したバリエーション総数: ${totalCreated}`);

    if (errorProducts > 0) {
      console.log('\n失敗した商品:');
      this.results.filter(r => !r.success).forEach(result => {
        console.log(`❌ ${result.title}`);
        result.errors.forEach(error => {
          console.log(`  - ${error}`);
        });
      });
    }

    console.log('\n=== バリエーション作成完了 ===');
  }
}

// メイン実行
async function main() {
  const creator = new MissingVariantCreator();

  const args = process.argv.slice(2);
  const command = args[0];
  const productId = args[1];

  switch (command) {
    case 'all':
      await creator.createMissingVariantsForAllProducts();
      break;
    case 'product':
      if (!productId) {
        console.log('使用方法: npm run create-variants product <shopifyProductId>');
        return;
      }
      await creator.createMissingVariantsForProduct(productId);
      break;
    default:
      console.log('使用方法:');
      console.log('  npm run create-variants all           # 全商品をチェック');
      console.log('  npm run create-variants product <id>  # 特定商品をチェック');
      break;
  }
}

if (import.meta.url === new URL(import.meta.url).href) {
  main().catch(console.error);
}

export { MissingVariantCreator };
