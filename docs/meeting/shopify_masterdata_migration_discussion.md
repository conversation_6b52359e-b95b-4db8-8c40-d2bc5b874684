# Shopifyマスタデータ整理・移行に関する協議資料

**日付**: 2025年5月21日
**目的**: 商品マスタデータのShopify移行における整理方針の決定

## 検討の背景

現在、以下の2つのデータソースが存在しており、Shopifyへの移行にあたり統一的なデータ構造の確立が必要です：

1. **サイト商品データ**: rental_items_20250509_153839.csv
2. **データベース商品データ**: 「商品一覧_」で始まる複数のファイル（カテゴリ別）

## 現状の課題

1. **データベース商品データの問題点**:
   - 商品名に状態情報が混在（例: 「●★ﾛﾝｸﾞﾃﾞｽｸ」）
   - 半角カタカナ使用（表示/印刷のための措置）
   - 各種メタデータの不統一

2. **データ構造の不一致**:
   - サイトとデータベースのCSV項目が異なる
   - 共通キーによる紐付けが困難

3. **管理方法の不明確さ**:
   - レンタル状態の記録方法
   - 日数バリエーションの実装方法
   - 在庫ユニット管理の手法

## 議論すべき重要ポイント

## 1. データ構造の標準化と整理

### 1-1. 商品名の標準化
- **現状**: データベースでは半角カタカナと状態記号（「●★」など）を含む商品名
- **提案**: 
  - 商品名からステータス情報を削除し、メタフィールドへ移行
  - 全角カタカナ/漢字へ統一（表示・検索の一貫性向上）
  - 命名規則の策定（[カテゴリ][製品名][バリエーション]など）

### 1-2. CSV構造の統一
- **現状**: サイトとデータベースで項目数・構造が異なる
- **提案**:
  - Shopify推奨フォーマットに基づく統一CSVテンプレートの作成
  - 共通必須項目と拡張項目の明確化
  - 変換マッピングルールの作成（旧項目→新項目）

### 1-3. 商品コード体系の再設計
- **現状**: 「10201113-001」などの現在のコード体系
- **提案**:
  - SKUの命名規則確立（在庫管理のためのユニークID）
  - バリアント管理に適したコード体系への移行
  - 商品・バリアント・在庫ユニットの関係を明確化するコード設計

## 2. バリエーション管理の設計

### 2-1. 日数バリエーションの実装方式
- **予約タイプ実装方式検討.md**に基づく選択肢の提示
  - レンタル日数をバリエーションとする場合の設定方法
  - 予約タイプ（本予約/仮予約）をメタフィールドとする方式
  - 料金計算ロジックの実装方法（フロントエンド vs バックエンド）

### 2-2. 商品の実バリエーション管理
- **現状**: 色・サイズなどの実バリエーションが別レコードとして存在
- **提案**:
  - Shopifyの標準バリアント機能への移行手順
  - 親商品・バリアント関係の構築方法
  - 在庫管理単位とバリアントの関係設計

### 2-3. レンタル状態の管理
- **現状**: 商品名に「●★」などでステータス記録
- **提案**:
  - メタフィールドを使った状態管理システムの構築
  - 在庫ユニットベースでのステータス管理
  - 状態変更時のワークフロー設計（メンテナンス→利用可能など）

## 3. データ移行・同期の方法

### 3-1. 画像データの取得と管理
- サイトからの商品画像スクレイピング方法
  - 許可取得と法的側面の確認
  - 自動取得と手動取得の選択
  - 画像命名規則と整理方法
  - 最適な解像度・サイズの検討

### 3-2. 一括インポート方法
- Shopify CSVインポート機能の制限と対策
- 段階的インポート計画（カテゴリ別、数量制限など）
- エラー処理とログ管理の方法
- インポート後の検証プロセス

### 3-3. データ更新・同期の仕組み
- 継続的なデータ同期の必要性の有無
- 更新頻度と方法の決定
- マスターデータの管理主体（Shopify vs 外部システム）

## 4. 商品分類とメタデータ

### 4-1. カテゴリとコレクション設計
- **現状**: 大カテゴリー/小カテゴリーの階層構造
- **提案**:
  - Shopifyコレクション構造への変換方法
  - 自動コレクション条件の設定
  - ナビゲーション階層の設計

### 4-2. タグ体系の設計
- Shopifyタグの効果的な使用方法
- 検索・フィルタリングを考慮したタグ設計
- 自動タグ付けルールの確立

### 4-3. メタフィールド設計
- 専用メタフィールドセットの定義
  - レンタル料金設定
  - 在庫ユニット管理
  - 予約状態管理
  - 商品仕様データ（サイズ、素材、色など）

## 5. 追加検討事項

### 5-1. 国際化対応
- 多言語対応の必要性（今後のインバウンド需要など）
- 通貨表示・税率設定の方法

### 5-2. 価格設定戦略
- バリエーションによる価格設定方法
- 割引・セール・キャンペーンの実装方法
- 仮予約料金（10%）の処理方法

### 5-3. SEO最適化
- 商品ページのSEO設定
- 構造化データの活用方法
- URLの設計と最適化

### 5-4. バッチ処理の自動化
- 定期的なデータ更新処理の自動化
- インポート/エクスポートのスケジュール設定
- エラーアラートと監視体制

## 推奨アクションプラン

1. **統一データモデルの確立** (優先度: 高)
   - [ ] Shopify向け標準CSV形式の定義
   - [ ] 移行マッピングルールの文書化
   - [ ] メタフィールド設計の確定

2. **段階的移行計画の策定** (優先度: 高)
   - [ ] 優先カテゴリの決定
   - [ ] パイロット移行の実施（少量データでテスト）
   - [ ] 全体移行のスケジュール設定

3. **検証プロセスの確立** (優先度: 中)
   - [ ] データ整合性チェックリストの作成
   - [ ] 移行後のQA手順の明確化
   - [ ] エラー時の対応フロー定義

4. **マスターデータ管理運用ガイドラインの作成** (優先度: 中)
   - [ ] データ更新のワークフロー
   - [ ] 責任者の指定と役割分担
   - [ ] トレーニング資料の準備

## 決定事項

以下の項目について本日の会議で決定したいと考えています：

1. 商品名標準化の方針
2. 日数バリエーション実装方式の選択
3. レンタル状態管理の方法
4. 優先して移行するカテゴリ
5. 画像取得の方法
6. カテゴリ/タグ体系の方針

## 次のステップ

1. 決定事項に基づくデータモデル詳細設計
2. サンプルCSVテンプレートの作成
3. 移行スクリプトの開発
4. パイロット移行の実施

## 添付資料

- 「予約タイプ実装方式検討.md」
- 現行CSV構造分析
- Shopify推奨CSVフォーマット