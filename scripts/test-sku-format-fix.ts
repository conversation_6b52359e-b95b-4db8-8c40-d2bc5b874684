/**
 * SKU形式修正テスト
 */

import { authenticate } from '../app/shopify.server';
import { VariantAutoCreatorService } from '../app/services/shopify/variant-auto-creator.service';
import { GraphQLClient } from 'graphql-request';

async function testSkuFormatFix() {
  console.log('=== SKU形式修正テスト開始 ===');

  try {
    // 環境変数から設定を取得
    const shopifyShop = process.env.SHOPIFY_SHOP;
    const shopifyAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

    if (!shopifyShop || !shopifyAccessToken) {
      console.error('❌ 環境変数が設定されていません');
      return;
    }

    // GraphQLClientを直接作成
    const graphqlClient = new GraphQLClient(
      `https://${shopifyShop}/admin/api/2025-01/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': shopifyAccessToken,
          'Content-Type': 'application/json',
        },
      }
    );

    // adminオブジェクトを作成
    const admin = {
      graphql: async (query: string, variables?: any) => {
        const result = await graphqlClient.request(query, variables);
        return {
          json: () => Promise.resolve({ data: result })
        };
      }
    };

    const variantService = new VariantAutoCreatorService();

    // テスト商品（正しいSKU形式で）
    const testProduct = {
      title: 'テスト商品 SKU形式修正確認',
      handle: 'test-sku-format-fix',
      productType: 'グラス',
      vendor: 'テストベンダー',
      tags: ['テスト', 'レンタル'],
      status: 'ACTIVE',
      variants: [{
        title: 'デフォルト',
        price: '1200',
        sku: '201-01-017',
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY'
      }]
    };

    // 1. 商品作成
    console.log('1. テスト商品を作成中...');
    const createResponse = await admin.graphql(`
      mutation productCreate($input: ProductInput!) {
        productCreate(input: $input) {
          product {
            id
            title
            handle
          }
          userErrors {
            field
            message
          }
        }
      }
    `, {
      input: testProduct
    });

    const createData = await createResponse.json();
    
    if (createData.errors || createData.data.productCreate.userErrors.length > 0) {
      throw new Error(`商品作成エラー: ${JSON.stringify(createData.errors || createData.data.productCreate.userErrors)}`);
    }

    const productId = createData.data.productCreate.product.id;
    const shopifyProductId = productId.split('/').pop();
    console.log(`✅ 商品作成成功: ${shopifyProductId}`);

    // 2. バリエーション自動作成（正しいSKU形式で）
    console.log('\n2. バリエーション自動作成（正しいSKU形式）...');
    
    const result = await variantService.createMissingVariants(admin, shopifyProductId, {
      basePrice: 1200,
      productStatus: 'available',
      location: 'NY',
      sku: '201-01-017', // 正しいSKU形式を指定
      createProvisionalVariants: false
    });

    console.log('\n=== バリエーション作成結果 ===');
    console.log(`作成されたバリエーション数: ${result.createdVariants.length}`);
    console.log(`在庫設定: ${result.inventoryUpdated ? '✅ 成功' : '❌ 失敗'}`);
    console.log(`メタフィールド設定: ${result.metafieldsSet ? '✅ 成功' : '❌ 失敗'}`);
    console.log(`全体の成功: ${result.success ? '✅ 成功' : '❌ 失敗'}`);
    
    if (result.errors.length > 0) {
      console.log('\nエラー:');
      result.errors.forEach(error => console.log(`  - ${error}`));
    }

    // 3. 作成されたバリエーションのSKUを確認
    console.log('\n3. 作成されたバリエーションのSKUを確認中...');
    const variantResponse = await admin.graphql(`
      query getProductVariants($id: ID!) {
        product(id: $id) {
          title
          variants(first: 20) {
            edges {
              node {
                id
                title
                sku
                price
              }
            }
          }
        }
      }
    `, {
      id: productId
    });

    const variantData = await variantResponse.json();
    
    if (variantData.errors) {
      throw new Error(`バリエーション確認エラー: ${JSON.stringify(variantData.errors)}`);
    }

    const product = variantData.data.product;
    console.log(`\n📦 商品: ${product.title}`);
    console.log(`総バリエーション数: ${product.variants.edges.length}`);
    
    let correctSkuCount = 0;
    const expectedSkuPattern = /^201-01-017-\d+D(\+)?$/;
    
    product.variants.edges.forEach((variantEdge: any, index: number) => {
      const variant = variantEdge.node;
      const isCorrectSku = expectedSkuPattern.test(variant.sku);
      
      console.log(`  ${index + 1}. ${variant.title}`);
      console.log(`     SKU: ${variant.sku} ${isCorrectSku ? '✅' : '❌'}`);
      console.log(`     価格: ¥${variant.price}`);
      
      if (isCorrectSku) {
        correctSkuCount++;
      }
    });

    // 4. 結果評価
    console.log('\n=== テスト結果評価 ===');
    const expectedVariants = 8; // 1D, 2D, 3D, 4D, 5D, 6D, 7D, 8D+
    const actualVariants = product.variants.edges.length;
    
    console.log(`バリエーション作成: ${actualVariants}/${expectedVariants} ${actualVariants === expectedVariants ? '✅' : '❌'}`);
    console.log(`正しいSKU形式: ${correctSkuCount}/${actualVariants} ${correctSkuCount === actualVariants ? '✅' : '❌'}`);
    console.log(`メタフィールド設定: ${result.metafieldsSet ? '✅' : '❌'}`);
    console.log(`在庫設定: ${result.inventoryUpdated ? '✅' : '❌'}`);

    if (actualVariants === expectedVariants && correctSkuCount === actualVariants && result.metafieldsSet) {
      console.log('\n🎉 SKU形式修正テスト成功！');
      console.log('✅ バリエーション作成: 完璧');
      console.log('✅ SKU形式: 完璧（201-01-017-1D, 201-01-017-2D...）');
      console.log('✅ メタフィールド設定: 完璧');
      console.log(`${result.inventoryUpdated ? '✅' : '⚠️'} 在庫設定: ${result.inventoryUpdated ? '完璧' : '要確認'}`);
      
      console.log('\n📋 期待されるSKU:');
      console.log('  - 201-01-017-1D (1日レンタル)');
      console.log('  - 201-01-017-2D (2日レンタル)');
      console.log('  - 201-01-017-3D (3日レンタル)');
      console.log('  - 201-01-017-4D (4日レンタル)');
      console.log('  - 201-01-017-5D (5日レンタル)');
      console.log('  - 201-01-017-6D (6日レンタル)');
      console.log('  - 201-01-017-7D (7日レンタル)');
      console.log('  - 201-01-017-8D+ (8日以上レンタル)');
      
      console.log(`\n📋 商品URL: https://peaces-test-block.myshopify.com/admin/products/${shopifyProductId}`);
    } else {
      console.log('\n⚠️ 一部のテストに問題があります');
      if (correctSkuCount < actualVariants) {
        console.log('❌ SKU形式が正しくありません');
      }
    }

    // 5. クリーンアップ
    console.log('\n5. テスト商品を削除中...');
    await admin.graphql(`
      mutation productDelete($input: ProductDeleteInput!) {
        productDelete(input: $input) {
          deletedProductId
          userErrors {
            field
            message
          }
        }
      }
    `, {
      input: {
        id: productId
      }
    });

    console.log('✅ テスト商品削除完了');

  } catch (error) {
    console.error('❌ テストエラー:', error);
  }

  console.log('\n=== SKU形式修正テスト完了 ===');
}

// スクリプト実行
if (import.meta.url === new URL(import.meta.url).href) {
  testSkuFormatFix().catch(console.error);
}

export { testSkuFormatFix };
