/**
 * メタフィールド作成スクリプト
 *
 * このスクリプトは、Shopify GraphQL Admin APIを使用して、
 * 整理したメタフィールド定義を作成します。
 *
 * 使用方法:
 * 1. .env ファイルに SHOPIFY_ADMIN_API_ACCESS_TOKEN と SHOPIFY_SHOP を設定
 * 2. node scripts/create-metafields.js を実行
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

// 環境変数の読み込み
dotenv.config();

// 環境変数名を実際の.envファイルに合わせて変更
const SHOP_URL = process.env.SHOPIFY_SHOP;
const ADMIN_API_ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

// GraphQL APIエンドポイント
const API_URL = `https://${SHOP_URL}/admin/api/2023-10/graphql.json`;

// メタフィールド定義の作成用GraphQLミューテーション
const CREATE_METAFIELD_DEFINITION = `
  mutation CreateMetafieldDefinition($definition: MetafieldDefinitionInput!) {
    metafieldDefinitionCreate(definition: $definition) {
      createdDefinition {
        id
        name
        namespace
        key
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// GraphQL APIを呼び出す関数
async function callGraphQL(query, variables) {
  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ADMIN_API_ACCESS_TOKEN,
      },
      body: JSON.stringify({
        query,
        variables,
      }),
    });

    const result = await response.json();

    if (result.errors) {
      console.error('GraphQL Errors:', result.errors);
      return null;
    }

    return result.data;
  } catch (error) {
    console.error('API Call Error:', error);
    return null;
  }
}

// メタフィールド定義を作成する関数
async function createMetafieldDefinition(definition) {
  console.log(`Creating metafield definition: ${definition.namespace}.${definition.key}`);

  const data = await callGraphQL(CREATE_METAFIELD_DEFINITION, {
    definition: {
      name: definition.name,
      namespace: definition.namespace,
      key: definition.key,
      description: definition.description,
      type: definition.type,
      validations: definition.validations || [],
      ownerType: definition.ownerType,
      visibleToStorefrontApi: definition.visibleToStorefrontApi || false,
    },
  });

  if (data && data.metafieldDefinitionCreate) {
    if (data.metafieldDefinitionCreate.userErrors.length > 0) {
      console.error('Error creating metafield definition:', data.metafieldDefinitionCreate.userErrors);
      return false;
    }

    console.log(`Successfully created metafield definition: ${definition.namespace}.${definition.key}`);
    return true;
  }

  return false;
}

// メタフィールド定義
const metafieldDefinitions = [
  // 使用中のJSONメタフィールド
  {
    namespace: "rental",
    key: "basic_info",
    name: "レンタル商品基本情報",
    description: "商品の基本情報（商品コード、詳細コード、フリガナ、在庫場所、ステータス）",
    type: "json",
    ownerType: "PRODUCT",
    validations: {
      value: {
        type: "object",
        required: ["productCode", "detailCode", "kana", "location", "status"],
        properties: {
          productCode: { type: "string" },
          detailCode: { type: "string" },
          kana: { type: "string" },
          location: { type: "string" },
          status: {
            type: "string",
            enum: ["available", "maintenance", "disposed"],
          },
        },
      },
    },
  },
  {
    namespace: "rental",
    key: "pricing",
    name: "レンタル料金設定",
    description: "レンタル料金の設定（基本料金、日数別料金率など）",
    type: "json",
    ownerType: "PRODUCT",
    validations: {
      value: {
        type: "object",
        required: ["basePrice"],
        properties: {
          basePrice: { type: "number" },
          dayRates: {
            type: "object",
            properties: {
              day2to7: { type: "number" }, // 2-7日目の料金率（例: 0.8 = 80%）
              day8plus: { type: "number" }, // 8日目以降の料金率（例: 0.1 = 10%）
            }
          },
        },
      },
    },
  },

  // メンテナンス情報関連
  {
    namespace: "rental",
    key: "last_maintenance_date",
    name: "最終メンテナンス日",
    description: "商品の最終メンテナンス日",
    type: "date",
    ownerType: "PRODUCT",
  },
  {
    namespace: "rental",
    key: "next_maintenance_date",
    name: "次回メンテナンス日",
    description: "商品の次回メンテナンス予定日",
    type: "date",
    ownerType: "PRODUCT",
  },
  {
    namespace: "rental",
    key: "maintenance_status",
    name: "メンテナンス状態",
    description: "商品のメンテナンス状態",
    type: "single_line_text_field",
    ownerType: "PRODUCT",
  },
  {
    namespace: "rental",
    key: "maintenance_notes",
    name: "メンテナンス備考",
    description: "商品のメンテナンスに関する備考",
    type: "multi_line_text_field",
    ownerType: "PRODUCT",
  },

  // 購入情報関連
  {
    namespace: "rental",
    key: "manufacturer",
    name: "メーカー",
    description: "商品のメーカー名",
    type: "single_line_text_field",
    ownerType: "PRODUCT",
  },
  {
    namespace: "rental",
    key: "purchase_place",
    name: "購入場所",
    description: "商品の購入場所",
    type: "single_line_text_field",
    ownerType: "PRODUCT",
  },
  {
    namespace: "rental",
    key: "purchase_date",
    name: "購入日",
    description: "商品の購入日",
    type: "date",
    ownerType: "PRODUCT",
  },
  {
    namespace: "rental",
    key: "purchase_price",
    name: "購入価格",
    description: "商品の購入価格",
    type: "number_decimal",
    ownerType: "PRODUCT",
  },

  // 廃棄情報関連
  {
    namespace: "rental",
    key: "is_disposed",
    name: "廃棄済み",
    description: "商品が廃棄済みかどうか",
    type: "boolean",
    ownerType: "PRODUCT",
  },
  {
    namespace: "rental",
    key: "disposal_date",
    name: "廃棄日",
    description: "商品の廃棄日",
    type: "date",
    ownerType: "PRODUCT",
  },
  {
    namespace: "rental",
    key: "disposal_reason",
    name: "廃棄理由",
    description: "商品の廃棄理由",
    type: "multi_line_text_field",
    ownerType: "PRODUCT",
  },

  // 注文メタフィールド
  {
    namespace: "rental",
    key: "booking_id",
    name: "予約ID",
    description: "データベースの予約情報との連携用ID",
    type: "single_line_text_field",
    ownerType: "ORDER",
  },
];

// メインの実行関数
async function main() {
  console.log('Starting metafield definition creation...');

  let successCount = 0;
  let errorCount = 0;

  for (const definition of metafieldDefinitions) {
    const success = await createMetafieldDefinition(definition);
    if (success) {
      successCount++;
    } else {
      errorCount++;
    }

    // APIレート制限を避けるための遅延
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log('\nMetafield definition creation completed:');
  console.log(`- Total: ${metafieldDefinitions.length}`);
  console.log(`- Success: ${successCount}`);
  console.log(`- Error: ${errorCount}`);
}

// スクリプトの実行
main().catch(error => {
  console.error('Script execution error:', error);
  process.exit(1);
});
