# Shopify-Prisma連携と管理画面更新のプロンプト

## 背景

Shopify-Prisma連携の設計と実装を進めながら、アプリの管理画面も並行して更新していきたいと考えています。前回のセッションでは、顧客IDのみを保存し必要時にShopify APIから情報を取得する方式のテストを実施し、実行可能性を確認しました。また、Shopify-Prisma連携の設計ドキュメントや実装状況の整理も行いました。

## 目標

1. Shopify-Prisma連携の実装を進める
   - 顧客参照サービスの実装
   - 同期データの最小化実装
   - オンデマンド参照の実装

2. 管理画面の更新
   - 予約一覧画面の改善
   - 顧客管理画面の実装
   - 商品管理画面の改善

## 具体的な作業内容

### Shopify-Prisma連携の実装

1. **顧客参照サービスの実装**
   - キャッシュ機構を含む顧客参照サービスの実装
   - エラーハンドリングの強化
   - 顧客表示コンポーネントの作成

2. **同期データの最小化実装**
   - Prismaスキーマから不要な顧客フィールドの削除
   - 顧客データをIDのみに変更
   - 商品データを基本情報のみに最適化

3. **オンデマンド参照の実装**
   - 必要時のみShopify APIを呼び出す仕組みの実装
   - API呼び出し回数の最適化
   - キャッシュ戦略の実装

### 管理画面の更新

1. **予約一覧画面の改善**
   - 顧客情報の表示方法を改善（オンデマンド参照を使用）
   - 検索機能の強化（顧客名、メールでの検索）
   - フィルタリング機能の追加

2. **顧客管理画面の実装**
   - 顧客一覧画面の実装
   - 顧客詳細画面の実装
   - 顧客検索機能の実装

3. **商品管理画面の改善**
   - 商品一覧の表示改善
   - 在庫状況の視覚化
   - 予約状況との連携表示

## 優先順位

1. 顧客参照サービスの実装（最優先）
2. 予約一覧画面での顧客情報表示の改善
3. 同期データの最小化実装
4. 顧客管理画面の実装
5. オンデマンド参照の最適化
6. 商品管理画面の改善

## 質問事項

1. 顧客参照サービスの実装において、どのようなキャッシュ戦略が最適でしょうか？
2. Prismaスキーマの変更に伴うマイグレーション戦略はどうすべきでしょうか？
3. 管理画面のUIデザインについて、特に考慮すべき点はありますか？
4. API呼び出しの最適化において、バッチ処理と個別処理のバランスをどう取るべきでしょうか？
5. エラーハンドリングとフォールバック表示の最適な実装方法は？

## 参考資料

- 前回作成した「Shopify_Prisma顧客参照テスト結果.md」
- 「Shopify_Prisma同期設計.md」
- 「Shopify_Prisma同期実装状況.md」
- 「Shopify_Prisma同期最適化計画.md」

## その他の考慮事項

- パフォーマンスとユーザーエクスペリエンスのバランス
- セキュリティとプライバシーの確保
- 将来の拡張性と保守性
- エラー状態からの回復メカニズム
- テスト戦略と品質保証
