/**
 * order-creator.tsファイルを分析するスクリプト
 * 
 * このスクリプトは、order-creator.tsファイルを分析して、
 * 重複注文が発生する可能性のある箇所を特定します。
 * 
 * 実行方法: npx tsx scripts/analyze-order-creator.ts
 */

import fs from 'fs';
import path from 'path';

// order-creator.tsファイルのパス
const orderCreatorPath = path.join(process.cwd(), 'app/utils/booking/order-creator.ts');

// ファイルを読み込む関数
function readFile(filePath: string): string {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`ファイルの読み込みに失敗しました: ${filePath}`, error);
    throw error;
  }
}

// 重複注文の可能性がある箇所を分析する関数
function analyzeOrderCreator(fileContent: string) {
  console.log('order-creator.tsファイルを分析中...');
  
  // 分析結果
  const analysis = {
    hasExistingOrderCheck: false,
    hasRetryLogic: false,
    retryImplementation: '',
    orderCreationLogic: '',
    potentialIssues: [] as string[]
  };
  
  // 既存の注文チェックの有無を確認
  if (fileContent.includes('shopifyOrderId') && 
      (fileContent.includes('findUnique') || fileContent.includes('findFirst'))) {
    analysis.hasExistingOrderCheck = true;
  } else {
    analysis.potentialIssues.push('既存の注文をチェックするロジックが見つかりません。同じ予約に対して複数の注文が作成される可能性があります。');
  }
  
  // リトライロジックの有無を確認
  if (fileContent.includes('retry(') || fileContent.includes('function retry')) {
    analysis.hasRetryLogic = true;
    
    // リトライ実装を抽出
    const retryMatch = fileContent.match(/const result = await retry\(([\s\S]*?)\);/);
    if (retryMatch) {
      analysis.retryImplementation = retryMatch[0];
      
      // リトライ内で新しい注文を作成しているかチェック
      if (!analysis.retryImplementation.includes('return') || 
          analysis.retryImplementation.includes('new') || 
          analysis.retryImplementation.includes('create')) {
        analysis.potentialIssues.push('リトライロジック内で新しい注文を作成している可能性があります。これにより重複注文が発生する可能性があります。');
      }
    }
  } else {
    analysis.potentialIssues.push('リトライロジックが見つかりません。エラー発生時の処理が不明確です。');
  }
  
  // 注文作成ロジックを抽出
  const orderCreationMatch = fileContent.match(/async \(\) => \{([\s\S]*?)return \{/);
  if (orderCreationMatch) {
    analysis.orderCreationLogic = orderCreationMatch[0];
    
    // 注文作成前の重複チェックがあるかどうか
    if (!analysis.orderCreationLogic.includes('shopifyOrderId') || 
        !analysis.orderCreationLogic.includes('if (') || 
        !analysis.orderCreationLogic.includes('return')) {
      analysis.potentialIssues.push('注文作成前に重複チェックが行われていない可能性があります。');
    }
    
    // エラーハンドリングの確認
    if (!analysis.orderCreationLogic.includes('try') || 
        !analysis.orderCreationLogic.includes('catch')) {
      analysis.potentialIssues.push('適切なエラーハンドリングが行われていない可能性があります。');
    }
  } else {
    analysis.potentialIssues.push('注文作成ロジックを特定できませんでした。');
  }
  
  // 注文タグの設定を確認
  if (fileContent.includes('tags:') && fileContent.includes('booking-')) {
    // 良い実装
  } else {
    analysis.potentialIssues.push('予約IDに基づいた一意の注文タグが設定されていない可能性があります。これにより、同じ予約に対する注文の追跡が困難になります。');
  }
  
  return analysis;
}

// 改善提案を生成する関数
function generateRecommendations(analysis: any) {
  const recommendations = [];
  
  if (!analysis.hasExistingOrderCheck) {
    recommendations.push(`
    // 注文作成前に既存の注文をチェック
    const existingBooking = await prisma.booking.findUnique({
      where: { id: bookingId },
      select: { shopifyOrderId: true, shopifyOrderName: true }
    });

    // 既に注文が関連付けられている場合は、新しい注文を作成しない
    if (existingBooking && existingBooking.shopifyOrderId) {
      console.log(\`予約ID \${bookingId} には既に注文ID \${existingBooking.shopifyOrderId} が関連付けられています。\`);
      return {
        success: true,
        orderId: existingBooking.shopifyOrderId,
        orderName: existingBooking.shopifyOrderName,
        isDraftOrder: false,
        isExisting: true
      };
    }
    `);
  }
  
  if (analysis.hasRetryLogic && analysis.potentialIssues.some(issue => issue.includes('リトライロジック'))) {
    recommendations.push(`
    // 改善されたリトライロジック
    const result = await retry(
      async () => {
        // 既存のリクエスト処理
      },
      retryCount,
      1000, // 1秒後にリトライ
      2, // 指数バックオフ（1秒、2秒、4秒...）
      async (error, attempt) => {
        // エラーログを記録
        console.error(\`注文作成の試行 \${attempt}/\${retryCount} が失敗しました:\`, error);
        
        // 特定のエラーの場合はリトライしない
        if (error.message && error.message.includes('既に注文が存在します')) {
          return false; // リトライしない
        }
        
        return true; // リトライする
      }
    );
    `);
  }
  
  recommendations.push(`
  // 予約IDに基づいた一意の注文タグを生成
  const orderTag = \`booking-\${booking.bookingId}\`;

  // 注文作成用の入力データを作成
  const orderInput = {
    customerId: customerId,
    lineItems: [
      // ...
    ],
    tags: ['rental', booking.bookingType.toLowerCase(), orderTag],
    // ...
  };
  `);
  
  recommendations.push(`
  // デバッグログの追加
  console.log(\`注文作成を開始します: 予約ID=\${bookingId}, 顧客ID=\${customerId}, 商品ID=\${productId}\`);

  // 注文作成後
  console.log(\`注文作成が完了しました: 注文ID=\${orderId}, 注文番号=\${orderName}, 予約ID=\${bookingId}\`);
  `);
  
  return recommendations;
}

// メイン実行関数
async function analyzeOrderCreatorFile() {
  try {
    console.log(`order-creator.tsファイルを分析します: ${orderCreatorPath}`);
    
    // ファイルが存在するか確認
    if (!fs.existsSync(orderCreatorPath)) {
      throw new Error(`ファイルが見つかりません: ${orderCreatorPath}`);
    }
    
    // ファイルを読み込む
    const fileContent = readFile(orderCreatorPath);
    console.log(`ファイルサイズ: ${fileContent.length} バイト`);
    
    // ファイルを分析
    const analysis = analyzeOrderCreator(fileContent);
    
    // 分析結果を表示
    console.log('\n===== 分析結果 =====');
    console.log(`既存の注文チェック: ${analysis.hasExistingOrderCheck ? '✅ あり' : '❌ なし'}`);
    console.log(`リトライロジック: ${analysis.hasRetryLogic ? '✅ あり' : '❌ なし'}`);
    
    if (analysis.potentialIssues.length > 0) {
      console.log('\n潜在的な問題点:');
      analysis.potentialIssues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    } else {
      console.log('\n潜在的な問題点は見つかりませんでした。');
    }
    
    // 改善提案を生成
    const recommendations = generateRecommendations(analysis);
    
    console.log('\n===== 改善提案 =====');
    recommendations.forEach((recommendation, index) => {
      console.log(`\n改善提案 ${index + 1}:`);
      console.log(recommendation);
    });
    
    return {
      analysis,
      recommendations
    };
  } catch (error) {
    console.error('分析中にエラーが発生しました:', error);
    throw error;
  }
}

// スクリプトを実行
analyzeOrderCreatorFile()
  .then(() => {
    console.log('分析が完了しました');
    process.exit(0);
  })
  .catch((error) => {
    console.error('分析が失敗しました:', error);
    process.exit(1);
  });
