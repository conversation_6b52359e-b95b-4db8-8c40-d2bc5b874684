import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';

const prisma = new PrismaClient();

async function importMasterData() {
  try {
    console.log('マスタデータのインポートを開始します...');

    const shop = 'development.myshopify.com'; // 開発環境用

    // 受注方法マスタのインポート
    console.log('受注方法マスタをインポート中...');

    const orderPaths = [
      { code: '0', name: '店頭', isDefault: false },
      { code: '1', name: '電話', isDefault: false },
      { code: '2', name: 'FAX', isDefault: false },
      { code: '3', name: 'メール', isDefault: false },
      { code: '4', name: 'WEB', isDefault: true } // デフォルト
    ];

    for (const orderPath of orderPaths) {
      await prisma.orderPath.upsert({
        where: {
          shop_code: {
            shop: shop,
            code: orderPath.code
          }
        },
        update: {
          name: orderPath.name,
          isDefault: orderPath.isDefault,
          isActive: true
        },
        create: {
          shop: shop,
          code: orderPath.code,
          name: orderPath.name,
          isDefault: orderPath.isDefault,
          isActive: true
        }
      });
    }

    console.log(`${orderPaths.length}件の受注方法マスタをインポートしました`);

    // ストア担当者マスタのインポート
    console.log('ストア担当者マスタをインポート中...');

    // CSVファイルから担当者データを読み込み
    const csvFilePath = path.join(process.cwd(), 'master-data-csv/other-master-data/担当者マスタ.csv');

    if (!fs.existsSync(csvFilePath)) {
      console.error(`担当者マスタCSVファイルが見つかりません: ${csvFilePath}`);
      return;
    }

    const storeStaff: Array<{ staffCode: string; staffName: string; staffKana: string }> = [];

    // CSVファイルを読み込み
    await new Promise<void>((resolve, reject) => {
      fs.createReadStream(csvFilePath)
        .pipe(csv())
        .on('data', (row) => {
          const staff = {
            staffCode: row['担当者コード'] || '',
            staffName: row['担当者名'] || '',
            staffKana: row['担当者カナ'] || ''
          };

          if (staff.staffCode && staff.staffName) {
            storeStaff.push(staff);
          }
        })
        .on('end', resolve)
        .on('error', reject);
    });

    console.log(`${storeStaff.length}件の担当者データを読み込みました`);

    for (const staff of storeStaff) {
      await prisma.storeStaff.upsert({
        where: {
          shop_staffCode: {
            shop: shop,
            staffCode: staff.staffCode
          }
        },
        update: {
          staffName: staff.staffName,
          isActive: true
        },
        create: {
          shop: shop,
          staffCode: staff.staffCode,
          staffName: staff.staffName,
          isActive: true
        }
      });
    }

    console.log(`${storeStaff.length}件のストア担当者マスタをインポートしました`);

    // インポート結果を確認
    const importedOrderPaths = await prisma.orderPath.findMany({
      where: { shop },
      orderBy: { code: 'asc' }
    });

    const importedStoreStaff = await prisma.storeStaff.findMany({
      where: { shop },
      orderBy: { staffCode: 'asc' }
    });

    console.log('\n=== インポート結果 ===');

    console.log('\n受注方法マスタ:');
    importedOrderPaths.forEach((orderPath, index) => {
      const defaultMark = orderPath.isDefault ? ' (デフォルト)' : '';
      console.log(`${index + 1}. ${orderPath.code}: ${orderPath.name}${defaultMark}`);
    });

    console.log('\nストア担当者マスタ:');
    importedStoreStaff.forEach((staff, index) => {
      console.log(`${index + 1}. ${staff.staffCode}: ${staff.staffName}`);
    });

    console.log('\nマスタデータのインポートが完了しました！');

  } catch (error) {
    console.error('マスタデータのインポートでエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  importMasterData();
}

export { importMasterData };
