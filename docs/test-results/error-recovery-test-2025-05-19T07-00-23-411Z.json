{"timestamp": "2025-05-19T07:00:21.744Z", "duration": 1.667, "totalTests": 3, "successCount": 2, "failureCount": 1, "tests": [{"testName": "API障害からの回復テスト", "success": true, "timestamp": "2025-05-19T07:00:22.068Z", "message": "API障害から正常に回復しました", "error": "", "duration": 324}, {"testName": "ネットワーク切断からの回復テスト", "success": true, "timestamp": "2025-05-19T07:00:22.103Z", "message": "ネットワーク切断から正常に回復しました", "error": "", "duration": 35}, {"testName": "データ不整合の自動修復テスト", "success": false, "timestamp": "2025-05-19T07:00:23.411Z", "message": "データ不整合の修復に失敗しました", "error": "商品ID 123456789 が見つかりません", "duration": 1308}], "environment": {"nodeVersion": "v20.18.1", "os": "darwin arm64"}, "performance": {"averageTestDuration": 555.6666666666666, "slowestTest": {"name": "データ不整合の自動修復テスト", "duration": 1308}, "fastestTest": {"name": "ネットワーク切断からの回復テスト", "duration": 35}}}