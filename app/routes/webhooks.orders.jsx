/**
 * 注文作成・更新時のWebhookハンドラー
 *
 * このファイルは、Shopifyの注文作成・更新イベントのWebhookを処理し、
 * データベースとの同期を行います。
 */
import { json } from "@remix-run/node";
import { prisma } from "~/db.server";
import { DbSyncService } from "~/services/sync/db-sync.service";
import { authenticate } from "~/shopify.server";

/**
 * Webhookリクエストを処理するアクションハンドラー
 */
export async function action({ request }) {
  try {
    console.log("========== 注文Webhook受信開始 ==========");

    // リクエストヘッダーを確認
    const headers = Object.fromEntries(request.headers.entries());
    console.log("Webhookヘッダー:", JSON.stringify(headers, null, 2));

    // リクエストボディを取得（認証前に取得）
    const rawBody = await request.text();
    const payload = JSON.parse(rawBody);
    console.log("Webhookペイロード:", JSON.stringify(payload, null, 2));

    // Shopify認証
    const hmac = headers["x-shopify-hmac-sha256"];
    const shop = headers["x-shopify-shop-domain"];
    const topic = headers["x-shopify-topic"];

    if (!hmac || !shop || !topic) {
      console.error("必要なヘッダーが不足しています");
      return json({ error: "必要なヘッダーが不足しています" }, { status: 401 });
    }

    console.log(`Webhook処理: Shop ${shop}, Topic ${topic}`);

    // セッションを取得（オフラインセッション）
    const sessionId = `offline_${shop}`;
    const session = await prisma.session.findUnique({
      where: { id: sessionId }
    });

    if (!session) {
      console.error(`セッションが見つかりません: ${sessionId}`);
      return json({ error: "セッションが見つかりません" }, { status: 401 });
    }

    console.log("セッション取得成功:", session.shop);

    // 注文情報を取得
    const orderId = payload.id;
    console.log(`Webhook処理: 注文ID ${orderId} (${payload.name || '不明'})`);

    // DbSyncServiceを使用して注文データを同期
    try {
      // 認証情報を取得
      const { admin } = await authenticate.admin(request);
      
      // DbSyncServiceのインスタンスを作成
      const dbSyncService = new DbSyncService(request);
      
      // 注文データを同期
      const syncResult = await dbSyncService.syncOrderFromShopify(orderId);
      
      if (syncResult.success) {
        console.log(`注文同期成功: ${syncResult.message}`);
        
        // 注文ステータスに応じた処理
        await processOrderStatus(payload, dbSyncService);
      } else {
        console.warn(`注文同期警告: ${syncResult.message}`);
      }
    } catch (syncError) {
      console.error("注文同期エラー:", syncError);
      // 同期エラーがあっても処理を続行
    }

    console.log("========== 注文Webhook処理完了 ==========");
    return json({ success: true });
  } catch (error) {
    console.error("========== 注文Webhook処理エラー ==========");
    console.error("エラー詳細:", error);
    console.error("エラーメッセージ:", error.message);
    console.error("エラースタック:", error.stack);
    return json({ error: error.message }, { status: 500 });
  }
}

/**
 * 注文ステータスに応じた処理を行う
 * @param {Object} order - 注文情報
 * @param {DbSyncService} dbSyncService - DbSyncServiceのインスタンス
 */
async function processOrderStatus(order, dbSyncService) {
  try {
    // 注文の支払い状態を確認
    const financialStatus = order.financial_status;
    
    // 注文のラインアイテムを確認
    const lineItems = order.line_items || [];
    
    // 予約IDを抽出
    let bookingId = null;
    
    // 各ラインアイテムのプロパティを確認
    for (const item of lineItems) {
      const properties = item.properties || [];
      
      // プロパティから予約IDを検索
      for (const prop of properties) {
        if (prop.name === '_booking_id') {
          bookingId = prop.value;
          break;
        }
      }
      
      if (bookingId) break;
    }
    
    if (!bookingId) {
      console.log("予約IDが見つかりません。通常の注文として処理します。");
      return;
    }
    
    console.log(`予約ID ${bookingId} の処理を開始します。`);
    
    // 予約情報を取得
    const booking = await prisma.booking.findFirst({
      where: { bookingId }
    });
    
    if (!booking) {
      console.warn(`予約ID ${bookingId} が見つかりません。`);
      return;
    }
    
    // 支払い状態に応じた処理
    if (financialStatus === 'paid') {
      // 支払い完了の場合、予約を確定状態に更新
      await prisma.booking.update({
        where: { id: booking.id },
        data: {
          status: 'CONFIRMED',
          bookingType: 'CONFIRMED',
          paymentStatus: 'PAID',
          updatedAt: new Date()
        }
      });
      
      console.log(`予約ID ${bookingId} を確定状態に更新しました。`);
    } else if (financialStatus === 'refunded' || financialStatus === 'voided') {
      // 返金または無効化の場合、予約をキャンセル状態に更新
      await prisma.booking.update({
        where: { id: booking.id },
        data: {
          status: 'CANCELLED',
          paymentStatus: financialStatus === 'refunded' ? 'REFUNDED' : 'CANCELLED',
          updatedAt: new Date()
        }
      });
      
      console.log(`予約ID ${bookingId} をキャンセル状態に更新しました。`);
    } else {
      console.log(`予約ID ${bookingId} の支払い状態は ${financialStatus} です。処理は行いません。`);
    }
  } catch (error) {
    console.error("注文ステータス処理エラー:", error);
  }
}

/**
 * GETリクエストを処理するローダーハンドラー
 */
export function loader() {
  return json({ message: "Method not allowed" }, { status: 405 });
}
