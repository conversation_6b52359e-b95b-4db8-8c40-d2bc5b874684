# 負荷テストと同時実行テストレポート

## テスト概要

このレポートは、多数の同時予約リクエストの処理、同一商品への複数同時アクセス時の競合解決、高負荷時のパフォーマンスのテスト結果をまとめたものです。

テスト実行日時: 2025/5/19 16:16:01
実行時間: 7.18秒

## テスト環境

- 同時リクエスト数: 3
- Node.js バージョン: v20.18.1
- OS: darwin arm64
- CPUコア数: 10

## テスト結果

- 合計テスト数: 3
- 成功: 1
- 失敗: 2
- 成功率: 33%

## パフォーマンス分析

- スループット: 0.42 リクエスト/秒
- 平均テスト実行時間: 3561.33ミリ秒
- 最も遅いテスト: リクエスト #1 (5994.00ミリ秒)
- 最も速いテスト: リクエスト #0 (1029.00ミリ秒)

## 詳細結果

### リクエスト1

- 結果: ❌ 失敗
- メッセージ: 予約リクエストが失敗しました
- エラー: カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-05-26
- 実行時間: 1029.00ミリ秒
- 詳細:
```json
{
  "requestId": 0,
  "bookingId": ""
}
```

### リクエスト1

- 結果: ✅ 成功
- メッセージ: 予約リクエストが成功しました

- 実行時間: 5994.00ミリ秒
- 詳細:
```json
{
  "requestId": 1,
  "bookingId": "50ef94dd-2336-4516-99d4-79bb395851d6"
}
```

### リクエスト2

- 結果: ❌ 失敗
- メッセージ: 予約リクエストが失敗しました
- エラー: カート追加エラー: 予約の作成に失敗しました: 
Invalid `prisma.inventoryCalendar.upsert()` invocation in
/Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp/app/services/inventory-calendar.service.ts:363:40

  360   throw new Error(`商品 ID ${productId} が見つかりません`);
  361 }
  362 
→ 363 await prisma.inventoryCalendar.upsert(
Unique constraint failed on the fields: (`shop`,`productId`,`date`)
- 実行時間: 3661.00ミリ秒
- 詳細:
```json
{
  "requestId": 2,
  "bookingId": ""
}
```

