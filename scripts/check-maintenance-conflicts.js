/**
 * メンテナンス競合状況チェックスクリプト
 * 
 * 1. 貸出中商品のメンテナンス設定チェック
 * 2. メンテナンス中商品の表示状況チェック
 */

import { PrismaClient } from '@prisma/client';
import { format } from 'date-fns';

const prisma = new PrismaClient();

async function checkMaintenanceConflicts() {
  console.log('🔍 メンテナンス競合状況チェックを開始...\n');

  try {
    // TEST-TABLE-001の状況確認
    console.log('=== TEST-TABLE-001 の状況確認 ===');
    const testTable = await prisma.product.findFirst({
      where: { sku: 'TEST-TABLE-001' }
    });

    if (testTable) {
      console.log(`商品ID: ${testTable.id}`);
      console.log(`商品名: ${testTable.title}`);
      console.log(`SKU: ${testTable.sku}`);
      console.log(`ステータス: ${testTable.status}`);

      // 予約状況を確認
      const bookings = await prisma.booking.findMany({
        where: {
          productId: testTable.id,
          status: { in: ['PROVISIONAL', 'CONFIRMED'] },
          endDate: { gte: new Date() }
        },
        orderBy: { startDate: 'asc' }
      });

      console.log(`\n📅 現在の予約状況: ${bookings.length}件`);
      bookings.forEach((booking, index) => {
        console.log(`  ${index + 1}. ${booking.bookingId}`);
        console.log(`     期間: ${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')}`);
        console.log(`     ステータス: ${booking.status}`);
        console.log(`     顧客: ${booking.customerName}`);
      });

      // メンテナンス状況を確認
      const maintenances = await prisma.maintenance.findMany({
        where: { productId: testTable.id },
        orderBy: { createdAt: 'desc' }
      });

      console.log(`\n🔧 メンテナンス記録: ${maintenances.length}件`);
      maintenances.forEach((maintenance, index) => {
        console.log(`  ${index + 1}. ${maintenance.id}`);
        console.log(`     ステータス: ${maintenance.status}`);
        console.log(`     期間: ${format(maintenance.startDate, 'yyyy-MM-dd')} 〜 ${maintenance.endDate ? format(maintenance.endDate, 'yyyy-MM-dd') : '未設定'}`);
        console.log(`     タイプ: ${maintenance.type}`);
        console.log(`     備考: ${maintenance.notes || 'なし'}`);
      });

      // 在庫カレンダーを確認
      const today = new Date();
      const futureDate = new Date();
      futureDate.setDate(today.getDate() + 14);

      const inventoryCalendar = await prisma.inventoryCalendar.findMany({
        where: {
          productId: testTable.id,
          date: { gte: today, lte: futureDate }
        },
        orderBy: { date: 'asc' }
      });

      console.log(`\n📊 在庫カレンダー（今後14日間）: ${inventoryCalendar.length}件`);
      inventoryCalendar.forEach(entry => {
        const dateStr = format(entry.date, 'yyyy-MM-dd');
        const status = entry.isAvailable ? '✅ 利用可能' : '❌ 利用不可';
        const reason = entry.unavailableReason ? ` (${entry.unavailableReason})` : '';
        console.log(`  ${dateStr}: ${status}${reason}`);
      });

      // 競合チェック
      console.log(`\n⚠️ 競合チェック:`);
      const activeBookings = bookings.filter(b => 
        new Date(b.startDate) <= new Date() && new Date(b.endDate) >= new Date()
      );
      const activeMaintenances = maintenances.filter(m => m.status === 'IN_PROGRESS');

      if (activeBookings.length > 0 && activeMaintenances.length > 0) {
        console.log(`❌ 問題: 貸出中なのにメンテナンス中になっています`);
        console.log(`   貸出中予約: ${activeBookings.length}件`);
        console.log(`   進行中メンテナンス: ${activeMaintenances.length}件`);
      } else if (activeBookings.length > 0) {
        console.log(`📝 貸出中: ${activeBookings.length}件の予約が進行中`);
      } else if (activeMaintenances.length > 0) {
        console.log(`🔧 メンテナンス中: ${activeMaintenances.length}件のメンテナンスが進行中`);
      } else {
        console.log(`✅ 利用可能: 予約もメンテナンスもありません`);
      }
    } else {
      console.log('❌ TEST-TABLE-001 が見つかりません');
    }

    console.log('\n' + '='.repeat(60) + '\n');

    // TEST-SOFA-001の状況確認
    console.log('=== TEST-SOFA-001 の状況確認 ===');
    const testSofa = await prisma.product.findFirst({
      where: { sku: 'TEST-SOFA-001' }
    });

    if (testSofa) {
      console.log(`商品ID: ${testSofa.id}`);
      console.log(`商品名: ${testSofa.title}`);
      console.log(`SKU: ${testSofa.sku}`);
      console.log(`ステータス: ${testSofa.status}`);

      // メンテナンス状況を確認
      const sofaMaintenances = await prisma.maintenance.findMany({
        where: { productId: testSofa.id },
        orderBy: { createdAt: 'desc' }
      });

      console.log(`\n🔧 メンテナンス記録: ${sofaMaintenances.length}件`);
      sofaMaintenances.forEach((maintenance, index) => {
        console.log(`  ${index + 1}. ${maintenance.id}`);
        console.log(`     ステータス: ${maintenance.status}`);
        console.log(`     期間: ${format(maintenance.startDate, 'yyyy-MM-dd')} 〜 ${maintenance.endDate ? format(maintenance.endDate, 'yyyy-MM-dd') : '未設定'}`);
        console.log(`     タイプ: ${maintenance.type}`);
        console.log(`     備考: ${maintenance.notes || 'なし'}`);
      });

      // 商品の基本情報（basic_info）を確認
      if (testSofa.basicInfo) {
        console.log(`\n📋 基本情報:`, testSofa.basicInfo);
      }

      // 表示状況チェック
      const activeMaintenances = sofaMaintenances.filter(m => m.status === 'IN_PROGRESS');
      console.log(`\n🔍 表示状況チェック:`);
      if (activeMaintenances.length > 0) {
        console.log(`⚠️ メンテナンス中商品が表示されない可能性があります`);
        console.log(`   進行中メンテナンス: ${activeMaintenances.length}件`);
        
        // 商品ステータスをチェック
        if (testSofa.status === 'MAINTENANCE' || testSofa.status === 'UNAVAILABLE') {
          console.log(`   商品ステータス: ${testSofa.status} (表示されない原因)`);
        } else {
          console.log(`   商品ステータス: ${testSofa.status} (表示されるはず)`);
        }
      } else {
        console.log(`✅ メンテナンス中ではありません`);
      }
    } else {
      console.log('❌ TEST-SOFA-001 が見つかりません');
    }

    console.log('\n' + '='.repeat(60) + '\n');

    // 全体的な問題チェック
    console.log('=== 全体的な問題チェック ===');
    
    // 貸出中かつメンテナンス中の商品を検索
    const conflictProducts = await prisma.$queryRaw`
      SELECT DISTINCT p.id, p.title, p.sku, p.status
      FROM products p
      INNER JOIN bookings b ON p.id = b."productId"
      INNER JOIN maintenances m ON p.id = m."productId"
      WHERE b.status IN ('PROVISIONAL', 'CONFIRMED')
        AND b."endDate" >= CURRENT_DATE
        AND m.status = 'IN_PROGRESS'
    `;

    console.log(`🚨 貸出中かつメンテナンス中の商品: ${conflictProducts.length}件`);
    conflictProducts.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.title} (${product.sku})`);
      console.log(`     ステータス: ${product.status}`);
    });

    // メンテナンス中で表示されない可能性のある商品
    const hiddenMaintenanceProducts = await prisma.product.findMany({
      where: {
        status: { in: ['MAINTENANCE', 'UNAVAILABLE'] },
        maintenances: {
          some: {
            status: 'IN_PROGRESS'
          }
        }
      },
      include: {
        maintenances: {
          where: { status: 'IN_PROGRESS' }
        }
      }
    });

    console.log(`\n👻 表示されない可能性のあるメンテナンス中商品: ${hiddenMaintenanceProducts.length}件`);
    hiddenMaintenanceProducts.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.title} (${product.sku})`);
      console.log(`     ステータス: ${product.status}`);
      console.log(`     進行中メンテナンス: ${product.maintenances.length}件`);
    });

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
checkMaintenanceConflicts();
