/**
 * 検索ユーティリティ関数
 * 
 * 検索機能に関する共通の関数を提供します。
 * 特に日本語名検索に関する機能を強化しています。
 */

/**
 * 検索クエリを前処理し、検索条件を生成する
 * 
 * @param searchQuery 検索クエリ
 * @param fields 検索対象のフィールド（例: ['customerName', 'customerEmail']）
 * @returns 検索条件オブジェクト
 */
export function buildSearchCondition(searchQuery: string, fields: string[]): any {
  // 検索クエリが空の場合は空のオブジェクトを返す
  if (!searchQuery || searchQuery.trim() === '') {
    return {};
  }

  // 検索クエリをトリミングして小文字に変換
  const trimmedQuery = searchQuery.trim().toLowerCase();

  // スペースを削除したクエリも作成（「佐藤 花子」→「佐藤花子」のような検索に対応）
  const noSpaceQuery = trimmedQuery.replace(/\s+/g, '');

  // 名前の部分を抽出（日本語名対応）
  const nameParts = trimmedQuery.match(/[\p{L}\p{N}]+/gu) || [];

  // 日本語名かどうかを判定
  const isJapaneseName = /[\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Han}]/u.test(trimmedQuery);
  const hasMultipleParts = nameParts.length > 1;

  // 検索条件を構築
  const searchConditions: any[] = [];

  // 基本的な検索条件（各フィールドに対して検索）
  for (const field of fields) {
    searchConditions.push({
      [field]: { contains: trimmedQuery, mode: 'insensitive' }
    });
  }

  // 日本語名または複数単語の検索の場合、追加の検索条件を生成
  if ((isJapaneseName || hasMultipleParts) && fields.some(f => f.toLowerCase().includes('name'))) {
    // 名前フィールドが含まれている場合のみ適用

    // 名前フィールドを特定
    const nameFields = fields.filter(f => f.toLowerCase().includes('name'));

    // 各単語で検索
    for (const part of nameParts) {
      if (part.length > 1) {
        for (const field of nameFields) {
          searchConditions.push({
            [field]: { contains: part, mode: 'insensitive' }
          });
        }
      }
    }

    // スペースなしクエリでも検索
    if (noSpaceQuery.length > 2 && noSpaceQuery !== trimmedQuery) {
      for (const field of nameFields) {
        searchConditions.push({
          [field]: { contains: noSpaceQuery, mode: 'insensitive' }
        });
      }
    }
  }

  return { OR: searchConditions };
}

/**
 * 検索結果をアプリケーション側でさらにフィルタリングする
 * 
 * @param items 検索結果の配列
 * @param searchQuery 検索クエリ
 * @param nameField 名前フィールド（例: 'customerName'）
 * @param otherFields その他の検索対象フィールド（例: ['customerEmail', 'customerId']）
 * @returns フィルタリングされた配列
 */
export function filterItemsByName<T>(
  items: T[],
  searchQuery: string,
  nameField: keyof T,
  otherFields: (keyof T)[] = []
): T[] {
  if (!searchQuery || searchQuery.trim() === '') {
    return items;
  }

  const trimmedQuery = searchQuery.trim().toLowerCase();
  const noSpaceQuery = trimmedQuery.replace(/\s+/g, '');

  // 名前の部分を抽出（日本語名対応）
  const nameParts = trimmedQuery.match(/[\p{L}\p{N}]+/gu) || [];

  // 姓名の順序を入れ替えたバリエーションも生成
  let nameVariations: string[] = [];
  if (nameParts.length === 2) {
    // 「佐藤 花子」→「花子 佐藤」のようなバリエーションを追加
    nameVariations.push(`${nameParts[1]} ${nameParts[0]}`);
    nameVariations.push(`${nameParts[1]}${nameParts[0]}`); // スペースなし
  }

  // 日本語名かどうかを判定
  const isJapaneseName = /[\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Han}]/u.test(trimmedQuery);

  return items.filter(item => {
    // 基本的な検索条件に一致する場合は含める
    const nameValue = String(item[nameField] || '').toLowerCase();
    
    if (nameValue.includes(trimmedQuery)) {
      return true;
    }
    
    // その他のフィールドでも検索
    for (const field of otherFields) {
      const fieldValue = String(item[field] || '').toLowerCase();
      if (fieldValue.includes(trimmedQuery)) {
        return true;
      }
    }

    // 名前フィールドが空の場合はスキップ
    if (!nameValue) {
      return false;
    }

    const nameNoSpace = nameValue.replace(/\s+/g, '');

    // 1. スペースなしクエリが名前（スペースなし）に含まれているか確認
    // 例: 「佐藤花子」が「佐藤 花子」に一致
    const matchesNoSpace = noSpaceQuery.length > 2 && nameNoSpace.includes(noSpaceQuery);

    // 2. 名前の各部分がすべて名前に含まれているか確認
    // 例: 「佐藤」と「花子」の両方が含まれる
    const matchesAllParts = nameParts.length > 1 &&
      nameParts.every(part => part.length > 1 && nameValue.includes(part.toLowerCase()));

    // 3. 姓名の順序を入れ替えたバリエーションが名前に含まれているか確認
    // 例: 「花子 佐藤」や「花子佐藤」が「佐藤 花子」に一致
    const matchesVariation = nameVariations.some(variation => {
      const variationLower = variation.toLowerCase();
      return nameValue.includes(variationLower) ||
             nameNoSpace.includes(variationLower.replace(/\s+/g, ''));
    });

    // 4. 文字単位の比較（より柔軟な一致）
    // 例: 「佐藤花子」の各文字が順番に含まれているか
    let matchesCharByChar = false;
    if (noSpaceQuery.length > 2) {
      // 名前から検索クエリの各文字が順番に含まれているか確認
      let remainingName = nameNoSpace;
      let allCharsFound = true;

      for (const char of noSpaceQuery) {
        const charIndex = remainingName.indexOf(char);
        if (charIndex === -1) {
          allCharsFound = false;
          break;
        }
        // 見つかった文字以降の部分で次の文字を検索
        remainingName = remainingName.substring(charIndex + 1);
      }

      matchesCharByChar = allCharsFound;
    }

    // 5. 日本語名の場合、追加の検索パターンを試す
    let matchesJapaneseNamePatterns = false;
    if (isJapaneseName && noSpaceQuery.length >= 3) {
      // 可能な分割パターンを試す（2文字姓+残り、3文字姓+残り、など）
      for (let i = 1; i < noSpaceQuery.length; i++) {
        const lastName = noSpaceQuery.substring(0, i);
        const firstName = noSpaceQuery.substring(i);
        
        // 姓名の間にスペースを入れたパターン
        const nameWithSpace = `${lastName} ${firstName}`;
        const reversedName = `${firstName} ${lastName}`;
        
        if (nameValue.includes(nameWithSpace) || nameValue.includes(reversedName)) {
          matchesJapaneseNamePatterns = true;
          break;
        }
        
        // スペースなしでも確認
        if (nameNoSpace.includes(lastName + firstName) || 
            nameNoSpace.includes(firstName + lastName)) {
          matchesJapaneseNamePatterns = true;
          break;
        }
      }
    }

    // いずれかの条件に一致する場合はマッチとみなす
    return matchesNoSpace || matchesAllParts || matchesVariation || 
           matchesCharByChar || matchesJapaneseNamePatterns;
  });
}
