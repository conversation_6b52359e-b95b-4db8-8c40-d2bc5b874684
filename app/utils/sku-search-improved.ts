/**
 * 改善されたSKU検索ユーティリティ
 * 
 * 基本SKUから全バリアントを効率的に検索し、
 * レンタル商品のバリアント体系に最適化された検索機能を提供します。
 */

import { formatSkuWithHyphens } from './sku-formatter.js';

/**
 * SKU検索結果の型定義
 */
export interface SkuSearchResult {
  baseSku: string;
  variants: string[];
  searchConditions: any[];
}

/**
 * SKUからバリアント部分を除去して基本SKUを取得
 * @param sku フルSKU（例: "10101031-001-1D"）
 * @returns 基本SKU（例: "10101031-001"）
 */
export function extractBaseSku(sku: string): string {
  // レンタル日数バリアントのパターン
  const variantPattern = /-(1D|2D|3D|4D|5D|6D|7D|8D\+|PROV)$/i;
  
  // バリアント部分を除去
  const baseSku = sku.replace(variantPattern, '');
  
  return baseSku;
}

/**
 * 基本SKUから全てのバリアントSKUを生成
 * @param baseSku 基本SKU（例: "10101031-001"）
 * @returns バリアントSKUの配列
 */
export function generateAllVariantSkus(baseSku: string): string[] {
  const variants = ['1D', '2D', '3D', '4D', '5D', '6D', '7D', '8D+', 'PROV'];
  return variants.map(variant => `${baseSku}-${variant}`);
}

/**
 * 改善されたSKU検索条件を生成
 * @param searchQuery 検索クエリ
 * @returns SKU検索結果
 */
export function buildImprovedSkuSearch(searchQuery: string): SkuSearchResult {
  if (!searchQuery || searchQuery.trim() === '') {
    return {
      baseSku: '',
      variants: [],
      searchConditions: []
    };
  }

  const trimmedQuery = searchQuery.trim();
  const conditions = [];
  let baseSku = '';
  let variants: string[] = [];

  // 1. 入力されたSKUから基本SKUを抽出
  baseSku = extractBaseSku(trimmedQuery);
  
  // 2. SKUフォーマットを正規化（ハイフンなしの場合も対応）
  if (/^\d+$/.test(baseSku)) {
    baseSku = formatSkuWithHyphens(baseSku);
  }

  // 3. 基本SKUが有効な形式の場合、全バリアントを生成
  if (isValidBaseSku(baseSku)) {
    variants = generateAllVariantSkus(baseSku);
    
    // 基本SKUとすべてのバリアントを検索対象に含める
    const allSkus = [baseSku, ...variants];
    
    // IN句での一括検索（最も効率的）
    conditions.push({
      sku: { in: allSkus }
    });
  } else {
    // 有効な基本SKUでない場合は、従来の検索方法を使用
    
    // 完全一致
    conditions.push({
      sku: { equals: trimmedQuery, mode: 'insensitive' }
    });
    
    // 前方一致（バリアントを含む可能性）
    conditions.push({
      sku: { startsWith: trimmedQuery, mode: 'insensitive' }
    });
    
    // 部分一致
    conditions.push({
      sku: { contains: trimmedQuery, mode: 'insensitive' }
    });
  }

  return {
    baseSku,
    variants,
    searchConditions: conditions
  };
}

/**
 * 有効な基本SKU形式かチェック
 * @param sku SKU文字列
 * @returns 有効な基本SKUかどうか
 */
function isValidBaseSku(sku: string): boolean {
  // 商品コード-詳細コード形式（例: 101-01-031-001 または 10101031-001）
  const patterns = [
    /^\d{3}-\d{2}-\d{3}-\d{3}$/,  // 101-01-031-001
    /^\d{8}-\d{3}$/,               // 10101031-001
    /^\d{3}-\d{2}-\d{3}$/,         // 201-07-107
  ];
  
  return patterns.some(pattern => pattern.test(sku));
}

/**
 * 商品とバリアントを統合検索するための条件を生成
 * @param searchQuery 検索クエリ
 * @returns 統合検索条件
 */
export function buildProductVariantSearchConditions(searchQuery: string) {
  const { baseSku, variants, searchConditions } = buildImprovedSkuSearch(searchQuery);
  
  // 商品検索とバリアント検索を組み合わせる
  const productConditions = [];
  
  if (baseSku) {
    // 基本SKUで商品を検索
    productConditions.push({
      OR: [
        { sku: { equals: baseSku, mode: 'insensitive' } },
        { sku: { contains: baseSku, mode: 'insensitive' } }
      ]
    });
  }
  
  // タイトル検索も含める
  productConditions.push({
    title: { contains: searchQuery, mode: 'insensitive' }
  });
  
  return {
    productConditions,
    variantConditions: searchConditions,
    baseSku,
    variants
  };
}

/**
 * バリアント情報を含む検索結果を整形
 * @param products 商品リスト
 * @param includeVariants バリアント情報を含めるか
 * @returns 整形された商品リスト
 */
export async function formatSearchResultsWithVariants(
  products: any[],
  includeVariants: boolean = true
) {
  if (!includeVariants) {
    return products;
  }

  // 各商品にバリアント情報を追加
  return products.map(product => {
    const baseSku = extractBaseSku(product.sku);
    const possibleVariants = generateAllVariantSkus(baseSku);
    
    return {
      ...product,
      baseSku,
      possibleVariants,
      hasMultipleVariants: true
    };
  });
}

/**
 * SKU検索の使用例と説明
 */
export function demonstrateSkuSearch() {
  const examples = [
    {
      query: '10101031-001',
      description: '基本SKUから全バリアントを検索'
    },
    {
      query: '10101031-001-3D',
      description: 'バリアント付きSKUから基本SKUと全バリアントを検索'
    },
    {
      query: '10101031',
      description: '商品コードのみから関連商品を検索'
    }
  ];

  console.log('=== 改善されたSKU検索の例 ===\n');
  
  examples.forEach(({ query, description }) => {
    const result = buildImprovedSkuSearch(query);
    console.log(`検索: "${query}" - ${description}`);
    console.log(`基本SKU: ${result.baseSku}`);
    console.log(`バリアント: ${result.variants.join(', ')}`);
    console.log(`検索条件数: ${result.searchConditions.length}\n`);
  });
}

/**
 * テスト用のエクスポート
 */
export const IMPROVED_SKU_SEARCH_TESTS = {
  extractBaseSku,
  generateAllVariantSkus,
  isValidBaseSku,
  buildImprovedSkuSearch
};