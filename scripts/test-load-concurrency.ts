/**
 * 負荷テストと同時実行テスト
 *
 * このスクリプトは、多数の同時予約リクエストの処理、同一商品への複数同時アクセス時の競合解決、
 * 高負荷時のパフォーマンスを測定します。
 *
 * 実行方法: npx tsx scripts/test-load-concurrency.ts [同時リクエスト数]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';
import { addDays, format, differenceInMilliseconds } from 'date-fns';
import { ja } from 'date-fns/locale';
import { CartService } from '../app/services/cart.service';
import { BookingService } from '../app/services/booking.service';
import * as fs from 'fs';
import * as path from 'path';
import os from 'os';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト結果の型定義
interface TestResult {
  testName: string;
  success: boolean;
  timestamp: string;
  message?: string;
  error?: string;
  details?: Record<string, any>;
  duration?: number; // ミリ秒単位
}

// テスト結果セットの型定義
interface TestResultSet {
  timestamp: string;
  duration: number;
  totalTests: number;
  successCount: number;
  failureCount: number;
  tests: TestResult[];
  environment: {
    concurrentRequests: number;
    nodeVersion: string;
    os: string;
    cpuCores: number;
  };
  performance: {
    averageTestDuration: number;
    slowestTest: {
      name: string;
      duration: number;
    };
    fastestTest: {
      name: string;
      duration: number;
    };
    throughput: number; // リクエスト/秒
    successRate: number; // 成功率（%）
  };
}

// テスト設定
const config = {
  // テストする商品のShopify ID
  productId: process.argv[3] || '123456789', // コマンドライン引数から取得、または既定値を使用

  // 同時リクエスト数
  concurrentRequests: parseInt(process.argv[2] || '10', 10),

  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',

  // テスト用の顧客情報のベース
  customerBase: {
    namePrefix: 'テストユーザー',
    emailDomain: 'example.com',
    phonePrefix: '090-',
    address: '東京都渋谷区'
  },

  // 予約日数（今日から何日後に予約するか）
  startDaysFromNow: 7,
  durationDays: 3,

  // レポート出力設定
  outputDir: path.join(process.cwd(), 'docs/test-results'),

  // ワーカースレッド数（CPUコア数に基づく）
  workerThreads: Math.max(1, Math.min(os.cpus().length - 1, 4)) // 最大4スレッド、最小1スレッド
};

/**
 * 商品情報を取得する関数
 */
async function getProductInfo(shopifyProductId: string) {
  try {
    // Prismaから商品情報を取得
    const product = await prisma.product.findFirst({
      where: { shopifyId: shopifyProductId }
    });

    if (!product) {
      console.error(`商品ID ${shopifyProductId} が見つかりません`);
      return null;
    }

    return product;
  } catch (error) {
    console.error('商品情報取得エラー:', error);
    return null;
  }
}

/**
 * テスト用の顧客情報を生成する関数
 */
function generateCustomerInfo(index: number) {
  return {
    name: `${config.customerBase.namePrefix}${index}`,
    email: `test${index}@${config.customerBase.emailDomain}`,
    phone: `${config.customerBase.phonePrefix}${String(index).padStart(4, '0')}`,
    address: config.customerBase.address
  };
}

/**
 * 単一予約リクエストを実行する関数
 */
async function executeBookingRequest(requestId: number, product: any) {
  const startTime = new Date();
  let success = false;
  let error = '';
  let bookingId = '';

  try {
    // テスト用の日程を設定（リクエストごとに大きくずらす）
    const today = new Date();
    // リクエストごとに30日ずつずらす（重複を避けるため）
    const startDate = addDays(today, config.startDaysFromNow + 200 + (requestId * 30));
    const endDate = addDays(startDate, config.durationDays - 1);

    // 顧客情報を生成
    const customer = generateCustomerInfo(requestId);

    // CartServiceを初期化（モックリクエストを作成）
    const mockRequest = new Request(`https://${config.shop}/admin/api/2024-01/graphql.json`);
    const cartService = new CartService(mockRequest);

    // カートに追加
    const cartResult = await cartService.addToCart({
      productId: product.id,
      variantId: '1', // テスト用の固定値
      shopifyProductId: product.shopifyId,
      startDate: format(startDate, 'yyyy-MM-dd'),
      endDate: format(endDate, 'yyyy-MM-dd'),
      customerEmail: customer.email,
      customerName: customer.name,
      customerPhone: customer.phone,
      customerAddress: customer.address,
      bookingType: 'provisional',
      notes: `負荷テスト予約 #${requestId}`
    });

    if (!cartResult.success) {
      throw new Error(`カート追加エラー: ${cartResult.error}`);
    }

    bookingId = cartResult.bookingId;

    // BookingServiceを初期化
    const bookingService = new BookingService();

    // 注文IDを生成
    const orderId = Math.floor(Math.random() * 10000000000).toString();
    const orderName = `#${Math.floor(Math.random() * 10000)}`;

    // 予約の注文を更新
    const updateResult = await bookingService.updateBookingStatus(cartResult.bookingId, 'CONFIRMED', {
      orderId,
      orderName,
      notes: `負荷テスト注文 #${requestId}`
    });

    if (!updateResult.success) {
      throw new Error(`予約更新エラー: ${updateResult.error}`);
    }

    // 支払い状態を更新
    const paymentResult = await bookingService.updatePaymentStatus(cartResult.bookingId, 'COMPLETED', {
      paymentMethod: 'CREDIT_CARD',
      notes: `負荷テスト支払い #${requestId}`
    });

    if (!paymentResult.success) {
      throw new Error(`支払い状態更新エラー: ${paymentResult.error}`);
    }

    success = true;
  } catch (err) {
    error = err.message || String(err);
  }

  const endTime = new Date();
  const duration = differenceInMilliseconds(endTime, startTime);

  return {
    requestId,
    success,
    error,
    bookingId,
    duration
  };
}



/**
 * 同時予約リクエストをテストする関数（ワーカースレッドを使用せず）
 */
async function runConcurrentRequests(concurrentRequests: number, productId: string) {
  try {
    console.log(`${concurrentRequests}件の同時予約リクエストテストを開始します...`);

    // 開始時刻を記録
    const startTime = new Date();

    // 商品情報を取得
    const product = await getProductInfo(productId);

    if (!product) {
      throw new Error(`商品ID ${productId} が見つかりません`);
    }

    console.log(`${concurrentRequests}件の同時リクエストをシミュレートします`);

    // 完了したリクエスト数
    let completedRequests = 0;

    // 進捗表示用のインターバル
    const progressInterval = setInterval(() => {
      const percent = Math.round((completedRequests / concurrentRequests) * 100);
      process.stdout.write(`\r進捗: ${completedRequests}/${concurrentRequests} (${percent}%)`);
    }, 1000);

    try {
      const promises = [];

      // 同時リクエストを作成
      for (let i = 0; i < concurrentRequests; i++) {
        // 非同期関数を作成
        const requestPromise = async () => {
          try {
            const result = await executeBookingRequest(i, product);
            completedRequests++;
            return result;
          } catch (error) {
            completedRequests++;
            throw error;
          }
        };

        promises.push(requestPromise());
      }

      // すべてのリクエストを実行
      const results = await Promise.allSettled(promises);

      clearInterval(progressInterval);
      process.stdout.write(`\r進捗: ${completedRequests}/${concurrentRequests} (100%)\n`);

      // 結果を変換
      return results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          return {
            requestId: index,
            success: false,
            error: result.reason?.message || String(result.reason),
            bookingId: '',
            duration: 0
          };
        }
      });
    } catch (error) {
      clearInterval(progressInterval);
      throw error;
    }
  } catch (error) {
    console.error('同時予約リクエストテストエラー:', error);
    throw error;
  }
}

/**
 * テスト結果をJSONファイルに保存する関数
 */
function saveTestResults(results: TestResultSet, filePath: string): void {
  try {
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(filePath, JSON.stringify(results, null, 2));
    console.log(`テスト結果JSONファイルを生成しました: ${filePath}`);
  } catch (error) {
    console.error(`テスト結果の保存に失敗しました: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * テスト結果をMarkdownレポートとして生成する関数
 */
function generateMarkdownReport(results: TestResultSet, outputPath: string): string {
  try {
    let markdown = `# 負荷テストと同時実行テストレポート

## テスト概要

このレポートは、多数の同時予約リクエストの処理、同一商品への複数同時アクセス時の競合解決、高負荷時のパフォーマンスのテスト結果をまとめたものです。

テスト実行日時: ${new Date(results.timestamp).toLocaleString('ja-JP')}
実行時間: ${results.duration.toFixed(2)}秒

## テスト環境

- 同時リクエスト数: ${results.environment.concurrentRequests}
- Node.js バージョン: ${results.environment.nodeVersion}
- OS: ${results.environment.os}
- CPUコア数: ${results.environment.cpuCores}

## テスト結果

- 合計テスト数: ${results.totalTests}
- 成功: ${results.successCount}
- 失敗: ${results.failureCount}
- 成功率: ${Math.round((results.successCount / results.totalTests) * 100)}%

## パフォーマンス分析

- スループット: ${results.performance.throughput.toFixed(2)} リクエスト/秒
- 平均テスト実行時間: ${results.performance.averageTestDuration.toFixed(2)}ミリ秒
- 最も遅いテスト: ${results.performance.slowestTest.name} (${results.performance.slowestTest.duration.toFixed(2)}ミリ秒)
- 最も速いテスト: ${results.performance.fastestTest.name} (${results.performance.fastestTest.duration.toFixed(2)}ミリ秒)

## 詳細結果

`;

    // 各テストケースの詳細結果を追加（最初の10件のみ）
    results.tests.slice(0, 10).forEach((result, index) => {
      markdown += `### リクエスト${result.details?.requestId || index + 1}

- 結果: ${result.success ? '✅ 成功' : '❌ 失敗'}
${result.message ? `- メッセージ: ${result.message}` : ''}
${result.error ? `- エラー: ${result.error}` : ''}
- 実行時間: ${result.duration ? `${result.duration.toFixed(2)}ミリ秒` : '不明'}
`;

      if (result.details) {
        markdown += `- 詳細:\n\`\`\`json\n${JSON.stringify(result.details, null, 2)}\n\`\`\`\n\n`;
      }
    });

    if (results.tests.length > 10) {
      markdown += `\n... 他 ${results.tests.length - 10} 件のリクエスト結果は省略されました ...\n`;
    }

    // レポートファイルに書き込み
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(outputPath, markdown);
    console.log(`テスト結果レポートを生成しました: ${outputPath}`);

    return outputPath;
  } catch (error) {
    console.error(`Markdownレポートの生成に失敗しました: ${error instanceof Error ? error.message : String(error)}`);
    return '';
  }
}

/**
 * テスト結果を分析する関数
 */
function analyzeTestResults(results: any[], totalDuration: number): {
  averageTestDuration: number;
  slowestTest: { name: string; duration: number };
  fastestTest: { name: string; duration: number };
  throughput: number;
  successRate: number;
} {
  // 実行時間が記録されているテストのみを対象にする
  const testsWithDuration = results.filter(test => test.duration !== undefined);

  if (testsWithDuration.length === 0) {
    return {
      averageTestDuration: 0,
      slowestTest: { name: '不明', duration: 0 },
      fastestTest: { name: '不明', duration: 0 },
      throughput: 0,
      successRate: 0
    };
  }

  // 平均実行時間を計算
  const totalTestDuration = testsWithDuration.reduce((sum, test) => sum + test.duration, 0);
  const averageTestDuration = totalTestDuration / testsWithDuration.length;

  // 最も遅いテストを特定
  const slowestTest = testsWithDuration.reduce((slowest, test) =>
    test.duration > slowest.duration ? { name: `リクエスト #${test.requestId}`, duration: test.duration } : slowest,
    { name: '', duration: -1 }
  );

  // 最も速いテストを特定
  const fastestTest = testsWithDuration.reduce((fastest, test) =>
    test.duration < fastest.duration || fastest.duration === -1 ? { name: `リクエスト #${test.requestId}`, duration: test.duration } : fastest,
    { name: '', duration: -1 }
  );

  // スループットを計算（リクエスト/秒）
  const throughput = results.length / (totalDuration / 1000);

  // 成功率を計算
  const successCount = results.filter(r => r.success).length;
  const successRate = (successCount / results.length) * 100;

  return {
    averageTestDuration,
    slowestTest,
    fastestTest,
    throughput,
    successRate
  };
}

/**
 * メイン関数
 */
async function main() {

  try {
    console.log('負荷テストと同時実行テストを開始します...');
    console.log(`同時リクエスト数: ${config.concurrentRequests}`);
    console.log(`商品ID: ${config.productId}`);

    // 開始時刻を記録
    const startTime = new Date();

    // 同時予約リクエストをテスト
    const requestResults = await runConcurrentRequests(config.concurrentRequests, config.productId);

    // 終了時刻を記録
    const endTime = new Date();
    const totalDuration = differenceInMilliseconds(endTime, startTime);

    console.log(`\n負荷テストが完了しました。実行時間: ${(totalDuration / 1000).toFixed(2)}秒`);

    // テスト結果を分析
    const performanceAnalysis = analyzeTestResults(requestResults, totalDuration);

    // 成功/失敗の集計
    const successCount = requestResults.filter(r => r.success).length;
    const failureCount = requestResults.length - successCount;

    console.log('\n===== テスト結果サマリー =====');
    console.log(`合計リクエスト数: ${requestResults.length}`);
    console.log(`成功: ${successCount}`);
    console.log(`失敗: ${failureCount}`);
    console.log(`成功率: ${Math.round(performanceAnalysis.successRate)}%`);
    console.log(`スループット: ${performanceAnalysis.throughput.toFixed(2)} リクエスト/秒`);
    console.log(`平均実行時間: ${performanceAnalysis.averageTestDuration.toFixed(2)}ミリ秒`);

    // テスト結果をTestResult形式に変換
    const testResults: TestResult[] = requestResults.map(result => ({
      testName: `予約リクエスト #${result.requestId}`,
      success: result.success,
      timestamp: new Date().toISOString(),
      message: result.success ? '予約リクエストが成功しました' : '予約リクエストが失敗しました',
      error: result.error || undefined,
      duration: result.duration,
      details: {
        requestId: result.requestId,
        bookingId: result.bookingId
      }
    }));

    // テスト結果セットを作成
    const resultSet: TestResultSet = {
      timestamp: startTime.toISOString(),
      duration: totalDuration / 1000, // 秒単位
      totalTests: testResults.length,
      successCount,
      failureCount,
      tests: testResults,
      environment: {
        concurrentRequests: config.concurrentRequests,
        nodeVersion: process.version,
        os: `${process.platform} ${process.arch}`,
        cpuCores: os.cpus().length
      },
      performance: {
        averageTestDuration: performanceAnalysis.averageTestDuration,
        slowestTest: performanceAnalysis.slowestTest,
        fastestTest: performanceAnalysis.fastestTest,
        throughput: performanceAnalysis.throughput,
        successRate: performanceAnalysis.successRate
      }
    };

    // 結果ディレクトリが存在しない場合は作成
    if (!fs.existsSync(config.outputDir)) {
      fs.mkdirSync(config.outputDir, { recursive: true });
    }

    // タイムスタンプを生成
    const timestamp = startTime.toISOString().replace(/[:.]/g, '-');

    // JSONファイルに保存
    const jsonFilePath = path.join(config.outputDir, `load-test-${config.concurrentRequests}req-${timestamp}.json`);
    saveTestResults(resultSet, jsonFilePath);

    // Markdownレポートを生成
    const markdownFilePath = path.join(config.outputDir, `load-test-${config.concurrentRequests}req-${timestamp}.md`);
    generateMarkdownReport(resultSet, markdownFilePath);

    // テスト後のクリーンアップ
    console.log('\n----- テスト後のクリーンアップ -----');

    // 作成した予約を削除
    const bookingIds = requestResults
      .filter(r => r.success && r.bookingId)
      .map(r => r.bookingId);

    if (bookingIds.length > 0) {
      console.log(`${bookingIds.length}件の予約を削除します...`);

      for (const bookingId of bookingIds) {
        try {
          await prisma.booking.delete({ where: { id: bookingId } });
        } catch (error) {
          console.warn(`予約 ${bookingId} の削除に失敗しました:`, error);
        }
      }

      console.log('予約の削除が完了しました');
    }

    if (failureCount === 0) {
      console.log('\nすべてのテストが成功しました！');
    } else {
      console.warn(`\n${failureCount}件のリクエストが失敗しました。詳細はレポートを確認してください。`);
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
