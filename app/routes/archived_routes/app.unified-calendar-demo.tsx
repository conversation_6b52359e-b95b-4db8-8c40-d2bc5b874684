/**
 * 統一カレンダーのデモページ
 * 
 * 拡張された統一カレンダーコンポーネントの使用例を示すページです。
 * 表示モードと選択モードの両方を表示します。
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  BlockStack,
  Select,
  Box,
  Banner,
  InlineStack,
  Button,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { useState, useCallback } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { UnifiedCalendar } from "../components/Calendar/UnifiedCalendar";
import { formatJapaneseDateWithWeekday, type CalendarBooking } from "../utils/calendar/unified-calendar";
import type { BookingStatus, BookingType } from "@prisma/client";

// ローダー関数
export const loader = async ({ request }: LoaderFunctionArgs) => {
  // 認証
  const { admin } = await authenticate.admin(request);

  // 商品データを取得
  const products = await prisma.product.findMany({
    where: {
      isRental: true,
    },
    orderBy: {
      title: "asc",
    },
    take: 100,
  });

  // 予約データを取得
  const bookings = await prisma.booking.findMany({
    where: {
      status: {
        in: ["CONFIRMED", "PROVISIONAL"],
      },
    },
    orderBy: {
      startDate: "asc",
    },
    take: 100,
  });

  // メンテナンス期間データを取得
  const maintenancePeriods = await prisma.maintenancePeriod.findMany({
    orderBy: {
      startDate: "asc",
    },
    take: 100,
  });

  return json({
    products,
    bookings,
    maintenancePeriods,
  });
};

// メインコンポーネント
export default function UnifiedCalendarDemo() {
  const { products, bookings, maintenancePeriods } = useLoaderData<typeof loader>();
  
  // 文字列型の日付を Date オブジェクトに変換し、必要なフィールドのみで CalendarBooking 型の配列を作成
  const calendarBookings: CalendarBooking[] = bookings.map((b) => ({
    id: b.id,
    productId: b.productId,
    startDate: new Date(b.startDate),
    endDate: new Date(b.endDate),
    status: b.status as BookingStatus,
    bookingType: b.bookingType as BookingType,
    priority: b.priority,
    customerEmail: b.customerEmail,
    customerName: b.customerName,
  }));

  // メンテナンス期間データを変換
  const calendarMaintenancePeriods = maintenancePeriods.map((p) => ({
    id: p.id,
    startDate: new Date(p.startDate),
    endDate: new Date(p.endDate),
  }));
  
  // 選択された商品ID
  const [selectedProductId, setSelectedProductId] = useState<string>("");
  
  // 選択された日付範囲
  const [selectedRange, setSelectedRange] = useState<{
    startDate: Date | null;
    endDate: Date | null;
  }>({
    startDate: null,
    endDate: null,
  });
  
  // 商品選択ハンドラー
  const handleProductChange = useCallback((value: string) => {
    setSelectedProductId(value);
    setSelectedRange({ startDate: null, endDate: null });
  }, []);
  
  // 日付範囲選択ハンドラー
  const handleRangeSelect = useCallback((startDate: Date, endDate: Date) => {
    setSelectedRange({ startDate, endDate });
  }, []);
  
  // 選択された商品に関連する予約のみをフィルタリング
  const filteredBookings = selectedProductId
    ? calendarBookings.filter((b) => b.productId === selectedProductId)
    : [];

  // 選択された商品に関連するメンテナンス期間のみをフィルタリング
  const filteredMaintenancePeriods = selectedProductId
    ? calendarMaintenancePeriods.filter((p) => p.id.includes(selectedProductId))
    : [];
  
  return (
    <Page title="統一カレンダーデモ">
      <TitleBar title="統一カレンダーデモ" />
      
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text variant="headingMd" as="h2">
                商品を選択
              </Text>
              
              <Select
                label="商品"
                options={products.map((p) => ({ label: p.title, value: p.id }))}
                value={selectedProductId}
                onChange={handleProductChange}
                placeholder="商品を選択してください"
              />
              
              {!selectedProductId && (
                <Banner tone="info">
                  <Text as="p">商品を選択すると、カレンダーが表示されます。</Text>
                </Banner>
              )}
            </BlockStack>
          </Card>
        </Layout.Section>
        
        {selectedProductId && (
          <>
            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2">
                    表示モード（DisplayCalendar相当）
                  </Text>
                  
                  <UnifiedCalendar
                    bookings={filteredBookings}
                    maintenancePeriods={filteredMaintenancePeriods}
                    settings={{
                      excludeSundays: true,
                      excludeHolidays: true,
                      excludeNewYear: true,
                    }}
                    mode="display"
                    numberOfMonths={2}
                    title="予約状況カレンダー"
                    description="このカレンダーでは、仮予約（黄色）、本予約（緑色）、メンテナンス（灰色）、休日（赤色）の表示を確認できます。"
                    showLegend={true}
                  />
                </BlockStack>
              </Card>
            </Layout.Section>
            
            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2">
                    選択モード（SimpleCalendar相当）
                  </Text>
                  
                  <UnifiedCalendar
                    bookings={filteredBookings}
                    maintenancePeriods={filteredMaintenancePeriods}
                    settings={{
                      excludeSundays: true,
                      excludeHolidays: true,
                      excludeNewYear: true,
                      minRentalDays: 1,
                      maxRentalDays: 30,
                    }}
                    mode="select"
                    minDate={new Date()}
                    maxDate={new Date(new Date().setFullYear(new Date().getFullYear() + 1))}
                    onRangeSelect={handleRangeSelect}
                    showSelectedDates={true}
                    showLegend={true}
                    showValidationErrors={true}
                    title="予約カレンダー"
                    description="このカレンダーでは、日付範囲を選択できます。"
                  />
                </BlockStack>
              </Card>
            </Layout.Section>
            
            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2">
                    選択された日付範囲
                  </Text>
                  
                  {selectedRange.startDate && selectedRange.endDate ? (
                    <BlockStack gap="200">
                      <Text as="p">
                        開始日: {formatJapaneseDateWithWeekday(selectedRange.startDate)}
                      </Text>
                      <Text as="p">
                        終了日: {formatJapaneseDateWithWeekday(selectedRange.endDate)}
                      </Text>
                      <Text as="p">
                        期間: {Math.round((selectedRange.endDate.getTime() - selectedRange.startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1} 日間
                      </Text>
                      <InlineStack gap="200">
                        <Button primary>予約する</Button>
                        <Button onClick={() => setSelectedRange({ startDate: null, endDate: null })}>
                          クリア
                        </Button>
                      </InlineStack>
                    </BlockStack>
                  ) : (
                    <Text as="p">日付が選択されていません</Text>
                  )}
                </BlockStack>
              </Card>
            </Layout.Section>
          </>
        )}
      </Layout>
    </Page>
  );
}
