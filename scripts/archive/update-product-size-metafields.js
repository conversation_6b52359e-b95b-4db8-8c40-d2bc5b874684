import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Shopify GraphQL APIのエンドポイント
const endpoint = `https://${process.env.SHOPIFY_SHOP}/admin/api/2023-10/graphql.json`;

// GraphQLクライアントの初期化
const client = new GraphQLClient(endpoint, {
  headers: {
    'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
    'Content-Type': 'application/json',
  },
});

// 商品を取得するGraphQLクエリ
const GET_PRODUCTS = gql`
  query getProducts($first: Int!, $after: String) {
    products(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          handle
        }
      }
    }
  }
`;

// メタフィールドを設定するGraphQLミューテーション
const SET_METAFIELD = gql`
  mutation metafieldSet($metafield: MetafieldsSetInput!) {
    metafieldsSet(metafields: [$metafield]) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// データベースから商品サイズ情報を取得する関数
// 実際の実装では、Prismaクライアントを使用してデータベースから情報を取得します
async function getProductSizeFromDatabase(shopifyProductId) {
  // この関数は実際の実装では、データベースから商品サイズ情報を取得します
  // ここではダミーデータを返します
  console.log(`データベースから商品ID ${shopifyProductId} のサイズ情報を取得中...`);
  
  // 実際の実装では、以下のようにPrismaクライアントを使用します
  /*
  const product = await prisma.product.findUnique({
    where: {
      shopifyId: shopifyProductId.replace('gid://shopify/Product/', '')
    },
    select: {
      height: true,
      width: true,
      depth: true,
      weight: true
    }
  });
  return product;
  */
  
  // ダミーデータを返します（実際の実装では削除してください）
  return {
    height: Math.floor(Math.random() * 1000) + 100, // 100-1100mm
    width: Math.floor(Math.random() * 1000) + 100,  // 100-1100mm
    depth: Math.floor(Math.random() * 500) + 50,    // 50-550mm
    weight: Math.floor(Math.random() * 5000) + 500  // 500-5500g
  };
}

// 商品のメタフィールドを更新する関数
async function updateProductMetafield(productId, namespace, key, value, valueType) {
  try {
    const result = await client.request(SET_METAFIELD, {
      metafield: {
        ownerId: productId,
        namespace: namespace,
        key: key,
        value: String(value),
        type: valueType
      }
    });
    
    if (result.metafieldsSet.userErrors.length > 0) {
      console.error(`商品 ${productId} の ${key} メタフィールド更新中にエラーが発生しました:`, result.metafieldsSet.userErrors);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error(`商品 ${productId} の ${key} メタフィールド更新中にエラーが発生しました:`, error);
    return false;
  }
}

// すべての商品のサイズメタフィールドを更新する関数
async function updateAllProductSizeMetafields() {
  try {
    let hasNextPage = true;
    let cursor = null;
    let processedCount = 0;
    
    while (hasNextPage) {
      // 商品を取得
      const result = await client.request(GET_PRODUCTS, {
        first: 10, // 一度に10商品を取得
        after: cursor
      });
      
      const { edges, pageInfo } = result.products;
      
      // 各商品のメタフィールドを更新
      for (const { node: product } of edges) {
        console.log(`商品を処理中: ${product.title} (${product.id})`);
        
        // データベースから商品サイズ情報を取得
        const sizeInfo = await getProductSizeFromDatabase(product.id);
        
        if (sizeInfo) {
          // 高さのメタフィールドを更新
          if (sizeInfo.height !== null && sizeInfo.height !== undefined) {
            await updateProductMetafield(product.id, 'product', 'height', sizeInfo.height, 'number_integer');
          }
          
          // 幅のメタフィールドを更新
          if (sizeInfo.width !== null && sizeInfo.width !== undefined) {
            await updateProductMetafield(product.id, 'product', 'width', sizeInfo.width, 'number_integer');
          }
          
          // 奥行きのメタフィールドを更新
          if (sizeInfo.depth !== null && sizeInfo.depth !== undefined) {
            await updateProductMetafield(product.id, 'product', 'depth', sizeInfo.depth, 'number_integer');
          }
          
          // 重量のメタフィールドを更新
          if (sizeInfo.weight !== null && sizeInfo.weight !== undefined) {
            await updateProductMetafield(product.id, 'product', 'weight', sizeInfo.weight, 'number_integer');
          }
          
          processedCount++;
          console.log(`商品 ${product.title} のサイズメタフィールドを更新しました`);
        } else {
          console.log(`商品 ${product.title} のサイズ情報が見つかりませんでした`);
        }
      }
      
      // ページネーション情報を更新
      hasNextPage = pageInfo.hasNextPage;
      cursor = pageInfo.endCursor;
      
      console.log(`${processedCount} 商品を処理しました。次のページがあります: ${hasNextPage}`);
    }
    
    console.log(`合計 ${processedCount} 商品のサイズメタフィールドを更新しました。`);
  } catch (error) {
    console.error('エラーが発生しました:', error);
  }
}

// スクリプトの実行
updateAllProductSizeMetafields();
