/**
 * 期限切れ仮予約作成スクリプト
 *
 * 期限切れの仮予約を作成するスクリプト
 * 実行方法: npx tsx scripts/create-expired-provisional-booking.ts [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';
import { addDays, format, subDays } from 'date-fns';
import { ja } from 'date-fns/locale';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト設定
const config = {
  // テストする商品のShopify ID
  productId: process.argv[2], // コマンドライン引数から取得
  
  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
  
  // テスト用の顧客情報
  customer: {
    email: '<EMAIL>',
    name: 'テストユーザー',
    phone: '090-1234-5678',
    address: '東京都渋谷区'
  }
};

/**
 * メイン関数
 */
async function main() {
  try {
    if (!config.productId) {
      console.error('商品IDを指定してください');
      console.error('使用方法: npx tsx scripts/create-expired-provisional-booking.ts [商品ID]');
      process.exit(1);
    }
    
    console.log(`商品ID ${config.productId} の期限切れ仮予約を作成します...`);
    
    // 商品情報を取得
    const product = await prisma.product.findFirst({
      where: { shopifyId: config.productId }
    });
    
    if (!product) {
      console.error(`商品ID ${config.productId} が見つかりません`);
      process.exit(1);
    }
    
    console.log(`商品「${product.title}」の期限切れ仮予約を作成します...`);
    
    // 期限切れの仮予約を作成
    // 開始日: 7日後
    // 終了日: 10日後
    // 期限切れ日: 今日（開始日の7日前）
    const startDate = addDays(new Date(), 7);
    const endDate = addDays(startDate, 3);
    const expiresAt = new Date(); // 今日が期限切れ日
    
    // 予約番号を生成
    const bookingId = `EXPIRED-${Date.now()}-${uuidv4().substring(0, 8).toUpperCase()}`;
    
    // 予約を作成
    const booking = await prisma.booking.create({
      data: {
        id: uuidv4(),
        bookingId,
        shop: config.shop,
        productId: product.id,
        variantId: '1', // テスト用の固定値
        startDate,
        endDate,
        customerName: config.customer.name,
        customerEmail: config.customer.email,
        customerPhone: config.customer.phone,
        customerAddress: config.customer.address,
        bookingType: 'PROVISIONAL',
        status: 'PROVISIONAL',
        paymentStatus: 'PENDING',
        totalAmount: product.price,
        priority: 1,
        expiresAt,
        notes: 'テスト用期限切れ仮予約',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
    
    console.log('期限切れ仮予約を作成しました:');
    console.log(`予約ID: ${booking.id}`);
    console.log(`予約番号: ${booking.bookingId}`);
    console.log(`開始日: ${format(booking.startDate, 'yyyy/MM/dd', { locale: ja })}`);
    console.log(`終了日: ${format(booking.endDate, 'yyyy/MM/dd', { locale: ja })}`);
    console.log(`期限切れ日: ${format(expiresAt, 'yyyy/MM/dd', { locale: ja })}`);
    
    // 在庫カレンダーを更新
    for (let date = new Date(startDate); date <= endDate; date = addDays(date, 1)) {
      await prisma.inventoryCalendar.create({
        data: {
          id: uuidv4(),
          shop: config.shop,
          productId: product.id,
          shopifyProductId: product.shopifyId,
          date,
          isAvailable: false,
          unavailableReason: 'BOOKED',
          bookingId: booking.id,
          bookingType: 'PROVISIONAL',
          expiresAt,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }
    
    console.log('在庫カレンダーを更新しました');
    console.log('期限切れ仮予約の作成が完了しました');
    
    // 期限切れ仮予約チェックスクリプトを実行するコマンドを表示
    console.log('\n以下のコマンドで期限切れ仮予約チェックを実行できます:');
    console.log('npm run check:provisional-expiration');
  } catch (error) {
    console.error('期限切れ仮予約作成中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
