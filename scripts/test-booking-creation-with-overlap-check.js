/**
 * 重複チェック付き予約作成テストスクリプト
 *
 * このスクリプトは、重複チェックを行いながら予約を作成します。
 * 実行方法: node scripts/test-booking-creation-with-overlap-check.js [商品ID] [開始日] [終了日] [バリアントID]
 */

import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import { v4 as uuidv4 } from 'uuid';
import { addDays, format, parseISO } from 'date-fns';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品情報を取得するクエリ
const GET_PRODUCT = gql`
  query getProduct($id: ID!) {
    product(id: $id) {
      id
      title
      status
      variants(first: 5) {
        edges {
          node {
            id
            title
            sku
            inventoryQuantity
            inventoryPolicy
          }
        }
      }
      metafields(first: 10) {
        edges {
          node {
            namespace
            key
            value
          }
        }
      }
    }
  }
`;

// メタフィールドを更新するミューテーション
const UPDATE_METAFIELD = gql`
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * 商品情報を取得する関数
 * @param {string} productId 商品ID
 * @returns {Promise<object>} 商品情報
 */
async function getProductInfo(productId) {
  try {
    // Shopify IDを正規化
    const shopifyId = productId.startsWith('gid://shopify/Product/')
      ? productId
      : `gid://shopify/Product/${productId}`;

    console.log(`商品情報を取得中... (Shopify ID: ${shopifyId})`);

    // Shopifyから商品情報を取得
    const result = await shopifyClient.request(GET_PRODUCT, {
      id: shopifyId
    });

    if (!result.product) {
      throw new Error(`商品ID ${productId} が見つかりません`);
    }

    const product = result.product;
    console.log(`商品情報を取得しました: ${product.title}`);

    // データベースから商品情報を取得
    const dbProduct = await prisma.product.findFirst({
      where: {
        shopifyId: productId.replace('gid://shopify/Product/', '')
      }
    });

    if (!dbProduct) {
      throw new Error(`商品ID ${productId} がデータベースに存在しません`);
    }

    return { shopifyProduct: product, dbProduct };
  } catch (error) {
    console.error('商品情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 予約の重複をチェックする関数
 * @param {string} productId 商品ID
 * @param {string} variantId バリアントID
 * @param {Date} startDate 開始日
 * @param {Date} endDate 終了日
 * @returns {Promise<object>} 重複チェック結果
 */
async function checkBookingOverlap(productId, variantId, startDate, endDate) {
  try {
    console.log(`商品ID ${productId} の予約重複チェックを実行します...`);
    console.log(`期間: ${format(startDate, 'yyyy-MM-dd')} 〜 ${format(endDate, 'yyyy-MM-dd')}`);
    
    if (variantId) {
      console.log(`バリアントID: ${variantId}`);
    }

    // 重複する予約を検索
    const query = {
      productId,
      AND: [
        {
          startDate: {
            lte: endDate,
          },
          endDate: {
            gte: startDate,
          },
        },
      ],
    };

    // バリアントIDが指定されている場合は、同じバリアントの予約のみをチェック
    if (variantId) {
      query.variantId = variantId;
    }

    const overlappingBookings = await prisma.booking.findMany({
      where: query,
    });

    if (overlappingBookings.length > 0) {
      console.log(`重複する予約が ${overlappingBookings.length} 件見つかりました:`);
      overlappingBookings.forEach((booking, index) => {
        console.log(`${index + 1}. 予約ID: ${booking.bookingId}, 期間: ${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')}`);
      });
      return { hasOverlap: true, overlappingBookings };
    } else {
      console.log('重複する予約はありません');
      return { hasOverlap: false, overlappingBookings: [] };
    }
  } catch (error) {
    console.error('予約重複チェック中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 予約情報をメタフィールド用のフォーマットに変換する関数
 * @param {Array} bookings 予約情報
 * @returns {object} メタフィールド用の予約情報
 */
function formatBookingsForMetafield(bookings) {
  // 予約情報をメタフィールド用のフォーマットに変換
  const formattedBookings = bookings.map(booking => ({
    id: booking.bookingId,
    startDate: format(booking.startDate, 'yyyy-MM-dd'),
    endDate: format(booking.endDate, 'yyyy-MM-dd'),
    status: booking.status,
    customerName: booking.customerName,
    customerEmail: booking.customerEmail
  }));

  // メタフィールド用のデータ構造
  const metafieldData = {
    bookings: formattedBookings,
    availability: {
      rentalStatus: 'available',
      startDate: format(new Date(), 'yyyy-MM-dd'),
      maintenanceDates: [],
      blockedDates: []
    }
  };

  return metafieldData;
}

/**
 * 予約情報をShopifyメタフィールドに更新する関数
 * @param {string} productId 商品ID
 * @param {Array} bookings 予約情報
 * @returns {Promise<object>} 更新結果
 */
async function updateBookingMetafield(productId, bookings) {
  try {
    console.log('予約情報メタフィールドを更新中...');

    // Shopify IDを正規化
    const shopifyId = productId.startsWith('gid://shopify/Product/')
      ? productId
      : `gid://shopify/Product/${productId}`;

    // 予約情報をメタフィールド用のフォーマットに変換
    const metafieldData = formatBookingsForMetafield(bookings);

    // メタフィールドの設定
    const result = await shopifyClient.request(UPDATE_METAFIELD, {
      metafields: [
        {
          ownerId: shopifyId,
          namespace: 'rental',
          key: 'bookings',
          value: JSON.stringify(metafieldData),
          type: 'json'
        }
      ]
    });

    if (result.metafieldsSet.userErrors.length > 0) {
      throw new Error(`メタフィールドの設定中にエラーが発生しました: ${JSON.stringify(result.metafieldsSet.userErrors)}`);
    }

    console.log('予約情報メタフィールドを更新しました');
    return result.metafieldsSet.metafields[0];
  } catch (error) {
    console.error('予約情報メタフィールドの更新中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * 重複チェック付きで予約を作成する関数
 * @param {object} product 商品情報
 * @param {Date} startDate 開始日
 * @param {Date} endDate 終了日
 * @param {string} variantId バリアントID
 * @returns {Promise<object>} 作成結果
 */
async function createBookingWithOverlapCheck(product, startDate, endDate, variantId = null) {
  try {
    console.log(`商品ID ${product.dbProduct.id} の予約を作成中...`);
    console.log(`期間: ${format(startDate, 'yyyy-MM-dd')} 〜 ${format(endDate, 'yyyy-MM-dd')}`);
    
    if (variantId) {
      console.log(`バリアントID: ${variantId}`);
    }

    // 予約の重複をチェック
    const overlapResult = await checkBookingOverlap(product.dbProduct.id, variantId, startDate, endDate);
    
    if (overlapResult.hasOverlap) {
      console.error('重複する予約があるため、予約を作成できません');
      return { success: false, error: '重複する予約があります' };
    }

    // 予約データを作成
    const booking = await prisma.booking.create({
      data: {
        id: uuidv4(),
        bookingId: `TEST-${uuidv4().substring(0, 8).toUpperCase()}`,
        shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
        productId: product.dbProduct.id,
        variantId: variantId || product.shopifyProduct.variants.edges[0]?.node.id || null,
        startDate,
        endDate,
        customerName: '重複チェックテストユーザー',
        customerEmail: '<EMAIL>',
        bookingType: 'PROVISIONAL',
        status: 'PROVISIONAL',
        paymentStatus: 'PENDING',
        totalAmount: 10000,
        depositAmount: 1000,
        priority: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('予約を作成しました:');
    console.log(`予約ID: ${booking.id}`);
    console.log(`予約番号: ${booking.bookingId}`);
    console.log(`期間: ${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')}`);

    // 商品の全予約情報を取得
    const allBookings = await prisma.booking.findMany({
      where: {
        productId: product.dbProduct.id,
        status: {
          in: ['PROVISIONAL', 'CONFIRMED']
        }
      }
    });

    // Shopifyメタフィールドを更新
    try {
      await updateBookingMetafield(`gid://shopify/Product/${product.dbProduct.shopifyId}`, allBookings);
    } catch (metafieldError) {
      console.error('Shopifyメタフィールドの更新中にエラーが発生しました:', metafieldError);
      // メタフィールドの更新に失敗しても、予約の作成自体は成功とする
    }

    return { success: true, booking };
  } catch (error) {
    console.error('予約作成中にエラーが発生しました:', error);
    return { success: false, error };
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    // コマンドライン引数から商品ID、開始日、終了日、バリアントIDを取得
    const productId = process.argv[2];
    const startDateStr = process.argv[3];
    const endDateStr = process.argv[4];
    const variantId = process.argv[5] || null;

    if (!productId || !startDateStr || !endDateStr) {
      console.error('引数が不足しています。使用方法: node scripts/test-booking-creation-with-overlap-check.js [商品ID] [開始日(YYYY-MM-DD)] [終了日(YYYY-MM-DD)] [バリアントID]');
      process.exit(1);
    }

    // 日付文字列をDateオブジェクトに変換
    const startDate = parseISO(startDateStr);
    const endDate = parseISO(endDateStr);

    console.log(`商品ID ${productId} の予約作成テストを実行します...`);
    console.log(`期間: ${format(startDate, 'yyyy-MM-dd')} 〜 ${format(endDate, 'yyyy-MM-dd')}`);
    
    if (variantId) {
      console.log(`バリアントID: ${variantId}`);
    }

    // 商品情報を取得
    const product = await getProductInfo(productId);

    // 重複チェック付きで予約を作成
    const result = await createBookingWithOverlapCheck(product, startDate, endDate, variantId);

    if (result.success) {
      console.log('予約作成テストが成功しました！');
    } else {
      console.error('予約作成テストが失敗しました:', result.error);
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
