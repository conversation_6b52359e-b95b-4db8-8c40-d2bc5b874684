# Shopify向けCSVフォーマット分析と推奨構造

## 現行CSVフォーマットの比較

### 現在のサイトデータフォーマット（rental_items_20250509_153839.csv）

主な項目:
- 家具小道具メニュー
- 家具小道具大カテゴリー
- 家具小道具小カテゴリー
- 名前
- 型番
- サイズW
- サイズD
- サイズH
- サイズSH
- サイズ(その他)
- 色
- 色(その他)
- 素材
- その他
- 在庫数
- レンタル料金1日
- レンタル料金(1泊2日)
- レンタル料金(2泊3日)
- レンタル料金(3泊4日)
- 公開ステータス(公開/非公開)
- 新着ステータス(なし/あり)
- キャンペーン

### 現在のデータベースフォーマット（商品一覧_202505031504家具.csv）

主な項目:
- 商品基本マスタID
- 商品詳細マスタID
- カテゴリマスタID
- 商品カテゴリ
- 商品コード
- 商品詳細コード
- 商品名
- 商品名フリガナ
- 商品詳細名称
- 品番
- 型番
- レンタル単価
- 在庫数
- タグ
- 在庫場所
- サイズW
- サイズD
- サイズH
- 直径
- 特記事項
- レンタル開始日
- レンタル終了日
- メーカー名
- 購入場所
- 購入店舗
- 購入金額
- 購入日
- 商品ステータス
- 備考
- JANコード
- ステータス
- 廃業区分
- 廃棄日
- 備考

## Shopify推奨CSVフォーマット

Shopifyの商品インポートでは以下のフォーマットが推奨されています：

### 基本項目

- Handle（商品ハンドル）: URLスラッグとして使用される一意の識別子
- Title（商品名）: 商品の表示名
- Body HTML（商品説明）: 詳細な商品説明（HTMLタグ可）
- Vendor（ベンダー）: メーカーやブランド名
- Type（タイプ）: 商品タイプ
- Tags（タグ）: カンマ区切りのタグ
- Published（公開状態）: true/false
- Option1 Name（オプション1名）: 最初のバリエーション属性（例：サイズ）
- Option1 Value（オプション1値）: 属性の値（例：S、M、L）
- Option2 Name/Value, Option3 Name/Value: 追加のバリエーション属性
- Variant SKU（バリアントSKU）: 在庫管理用の一意のコード
- Variant Price（バリアント価格）: 販売価格
- Variant Compare At Price（比較価格）: 元値（割引表示用）
- Variant Inventory Tracker（在庫管理）: shopify
- Variant Inventory Qty（在庫数）: 数量
- Variant Inventory Policy（在庫切れ時）: deny/continue
- Variant Fulfillment Service（受注処理）: manual
- Variant Requires Shipping（配送必要）: true/false
- Variant Taxable（課税対象）: true/false
- Variant Weight（重量）: 数値
- Variant Weight Unit（重量単位）: kg/g/lb/oz
- Image Src（画像URL）: 画像のフルURL
- Image Position（画像位置）: 1, 2, 3...
- Image Alt Text（画像代替テキスト）: 画像の説明
- Gift Card（ギフトカード）: true/false
- SEO Title（SEOタイトル）: SEO用タイトル
- SEO Description（SEO説明）: SEO用説明
- Status（ステータス）: active/draft/archived

### メタフィールド列

Shopifyでは、CSVにカスタムメタフィールドも含めることができます：

- Metafield: namespace.key.type（命名規則）
  例: `Metafield: rental.status.single_line_text_field`

## 推奨レンタル商品用Shopify CSVフォーマット

以下に、現在のデータを統合したレンタル商品向けのCSVフォーマットを提案します：

### 基本項目（Shopify標準）

- Handle: [自動生成 or 商品コードをベース]
- Title: [標準化された商品名]
- Body HTML: [商品説明]
- Vendor: [メーカー名]
- Type: [家具小道具カテゴリー]
- Tags: [大カテゴリー, 小カテゴリー, 素材, 色, その他タグ]
- Published: [公開ステータスに基づく]
- Option1 Name: "レンタル日数"
- Option1 Value: "1日" or "2日" or "3日"...
- Variant SKU: [商品コード + 詳細コード + 日数コード]
- Variant Price: [対応するレンタル料金]
- Variant Inventory Qty: [在庫数]
- Image Src: [画像URL]

### カスタムメタフィールド（レンタル特有）

- Metafield: rental.inventory_units.json: [在庫ユニット情報のJSON]
- Metafield: rental.booking_type.single_line_text_field: [予約タイプ]
- Metafield: rental.status.single_line_text_field: [商品状態]
- Metafield: rental.location.single_line_text_field: [在庫場所]
- Metafield: product.width.number_integer: [サイズW]
- Metafield: product.depth.number_integer: [サイズD]
- Metafield: product.height.number_integer: [サイズH]
- Metafield: product.diameter.number_integer: [直径]
- Metafield: rental.color.single_line_text_field: [色]
- Metafield: rental.material.single_line_text_field: [素材]
- Metafield: rental.purchase_date.date: [購入日]
- Metafield: rental.purchase_price.number_decimal: [購入金額]
- Metafield: rental.maintenance_notes.multi_line_text_field: [メンテナンス備考]
- Metafield: rental.is_disposed.boolean: [廃棄区分]
- Metafield: rental.disposal_date.date: [廃棄日]
- Metafield: rental.disposal_reason.multi_line_text_field: [廃棄理由]

## CSV変換マッピングルール（概略）

### サイトデータから新フォーマットへ

- 名前 → Title
- 家具小道具大カテゴリー → Type + Tags
- 家具小道具小カテゴリー → Tags
- 型番 → Variant SKU（一部）
- サイズW/D/H → Metafield: product.width/depth/height
- 色 → Metafield: rental.color + Tags
- 素材 → Metafield: rental.material + Tags
- 在庫数 → Variant Inventory Qty
- レンタル料金1日/2日/3日... → 対応するバリアント価格
- 公開ステータス → Published

### データベースデータから新フォーマットへ

- 商品名（状態情報除去）→ Title
- 商品コード + 商品詳細コード → Variant SKU（一部）
- レンタル単価 → Variant Price（1日バリアント）
- 在庫数 → Variant Inventory Qty
- タグ → Tags
- 在庫場所 → Metafield: rental.location
- サイズW/D/H → Metafield: product.width/depth/height
- 直径 → Metafield: product.diameter
- 特記事項 → Body HTML + Metafield: rental.maintenance_notes
- メーカー名 → Vendor
- 購入場所 → Metafield: rental.purchase_place
- 購入金額 → Metafield: rental.purchase_price
- 購入日 → Metafield: rental.purchase_date
- 商品ステータス → Metafield: rental.status
- 備考 → Metafield: rental.notes
- 廃業区分 → Metafield: rental.is_disposed
- 廃棄日 → Metafield: rental.disposal_date

## データ変換プロセス概要

1. ソースデータの前処理
   - 商品名から状態記号の除去と分離
   - 半角カタカナを全角に変換
   - 要素の正規化（空白除去、形式整形など）

2. マスターデータの統合
   - サイトデータとデータベースデータの紐付け
   - 共通キーによる突合
   - 競合解決ルールの適用

3. Shopify CSV形式への変換
   - 項目マッピング適用
   - メタフィールド構造の生成
   - バリアントの展開（日数ごと）

4. 画像ファイルの整理
   - 画像ファイルのダウンロードとリネーム
   - フォルダ構造の整理
   - 画像参照パスの更新

5. データ検証
   - 必須項目の確認
   - 形式バリデーション
   - 整合性チェック

このプロセスを段階的に実施し、各段階で検証を行うことで、データ移行の品質を確保します。