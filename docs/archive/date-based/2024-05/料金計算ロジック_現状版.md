# レンタル商品料金計算ロジック（現状版）

## 概要

このドキュメントでは、レンタル商品ECシステムにおける現在の料金計算ロジックについて説明します。現在実装されている料金体系、デポジット計算、および今後の拡張可能性について解説します。

## 現在の料金体系

### 基本料金モデル

レンタル商品の料金は以下のシンプルな体系に基づいて計算されています：

1. **1日目**: 基本料金の100%
2. **2〜7日目**: 各日基本料金の20%
3. **8日目以降**: 各日基本料金の10%

この料金体系は、以下の利点があります：
- シンプルで理解しやすい
- 実装が容易
- 長期レンタルを促進する割引構造
- 商品の基本価格を1日目の料金に設定するだけで運用可能

### 計算例

例えば、基本料金が10,000円の商品の場合：

| レンタル日数 | 計算式 | 合計金額 |
|------------|-------|--------|
| 1日 | 10,000円 × 100% | 10,000円 |
| 3日 | 10,000円 + (10,000円 × 20% × 2日) | 14,000円 |
| 7日 | 10,000円 + (10,000円 × 20% × 6日) | 22,000円 |
| 10日 | 10,000円 + (10,000円 × 20% × 6日) + (10,000円 × 10% × 3日) | 25,000円 |

## デポジット計算

現在のデポジット計算は非常にシンプルです：

- **デポジット金額**: 合計レンタル料金の10%

例えば、合計レンタル料金が25,000円の場合、デポジット金額は2,500円となります。

## 実装方法

### 料金計算関数

以下のTypeScript関数を使用して、レンタル料金を計算しています：

```typescript
/**
 * レンタル料金を計算する関数
 * @param basePrice 基本料金（1日目の料金）
 * @param days レンタル日数
 * @returns 合計レンタル料金
 */
function calculateRentalPrice(basePrice: number, days: number): number {
  // 基本料金（1日目）
  let totalPrice = basePrice;

  // 2〜7日目の料金を計算
  if (days > 1) {
    const day2to7Count = Math.min(days - 1, 6); // 最大6日間（2日目〜7日目）
    totalPrice += basePrice * 0.2 * day2to7Count;
  }

  // 8日目以降の料金を計算
  if (days > 7) {
    const day8PlusCount = days - 7;
    totalPrice += basePrice * 0.1 * day8PlusCount;
  }

  // 整数に丸める
  return Math.round(totalPrice);
}

/**
 * デポジット金額を計算する関数
 * @param totalPrice 合計レンタル料金
 * @returns デポジット金額
 */
function calculateDeposit(totalPrice: number): number {
  return Math.round(totalPrice * 0.1);
}
```

### 日数計算関数

レンタル期間の日数を計算する関数：

```typescript
/**
 * 2つの日付間の日数を計算する関数
 * @param startDate 開始日
 * @param endDate 終了日
 * @returns 日数
 */
function calculateDays(startDate: Date, endDate: Date): number {
  // 日数を計算
  const oneDay = 24 * 60 * 60 * 1000; // 1日のミリ秒数
  const diffDays = Math.round(Math.abs((startDate.getTime() - endDate.getTime()) / oneDay));
  
  // 終了日を含める
  return diffDays + 1;
}
```

## 特別な料金設定

現在、特別な料金設定（季節料金、曜日別料金など）は実装されていません。特別な割引が必要な場合は、Shopifyのクーポン機能を使用して対応します。

## キャンセル料金

キャンセル料金は以下のポリシーに基づいて計算されます：

- **当日キャンセル**: 合計料金の100%
- **1〜2日前**: 合計料金の80%
- **3〜4日前**: 合計料金の50%
- **5〜6日前**: 合計料金の30%
- **7日前以上**: 合計料金の10%（デポジット分のみ）

```typescript
/**
 * キャンセル料金を計算する関数
 * @param totalPrice 合計料金
 * @param daysBeforeStart キャンセル時点から開始日までの日数
 * @param depositAmount 支払い済みデポジット金額
 * @returns { cancellationFee: キャンセル料, refundAmount: 返金額 }
 */
function calculateCancellationFee(
  totalPrice: number,
  daysBeforeStart: number,
  depositAmount: number
): { cancellationFee: number; refundAmount: number } {
  let cancellationRate = 0;

  // キャンセルポリシーに基づいて料率を設定
  if (daysBeforeStart <= 0) {
    // 当日: 100%
    cancellationRate = 1.0;
  } else if (daysBeforeStart <= 2) {
    // 1-2日前: 80%
    cancellationRate = 0.8;
  } else if (daysBeforeStart <= 4) {
    // 3-4日前: 50%
    cancellationRate = 0.5;
  } else if (daysBeforeStart <= 6) {
    // 5-6日前: 30%
    cancellationRate = 0.3;
  } else {
    // 7日前以上: 10%（デポジット分のみ）
    cancellationRate = 0.1;
  }

  // キャンセル料金を計算
  const cancellationFee = Math.round(totalPrice * cancellationRate);

  // 返金額を計算（デポジット払い済みの場合）
  const refundAmount =
    depositAmount > cancellationFee ? depositAmount - cancellationFee : 0;

  return {
    cancellationFee,
    refundAmount,
  };
}
```

## カートへの追加と表示

### カートへの追加処理

選択された日付と計算された料金をカートに追加する処理：

```typescript
/**
 * カートに商品を追加する関数
 * @param product 商品情報
 * @param startDate レンタル開始日
 * @param endDate レンタル終了日
 * @param calculatedPrice 計算された料金
 */
async function addToCart(
  product: any,
  startDate: Date,
  endDate: Date,
  calculatedPrice: number
) {
  // 日付をフォーマット
  const formattedStartDate = formatDate(startDate);
  const formattedEndDate = formatDate(endDate);

  // 日数を計算
  const days = calculateDays(startDate, endDate);

  // Line Item Propertiesとして日付情報を保存
  const properties = {
    'レンタル開始日': formattedStartDate,
    'レンタル終了日': formattedEndDate,
    'レンタル日数': days,
    '計算料金': formatPrice(calculatedPrice)
  };

  try {
    // カートに追加
    const response = await fetch('/cart/add.js', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        id: product.variant_id,
        quantity: 1,
        properties: properties
      })
    });

    if (!response.ok) {
      throw new Error('カートへの追加に失敗しました');
    }

    // カートページにリダイレクト
    window.location.href = '/cart';
  } catch (error) {
    console.error('エラー:', error);
    alert('カートへの追加中にエラーが発生しました。もう一度お試しください。');
  }
}
```

## 今後の拡張可能性

現在の料金計算ロジックはシンプルですが、将来的には以下の拡張が考えられます：

1. **季節料金**:
   - 繁忙期（夏季、年末年始など）の割増料金
   - 閑散期の割引料金

2. **曜日別料金**:
   - 週末（金・土・日）の割増料金
   - 平日の割引料金

3. **長期レンタル割引**:
   - 30日以上のレンタルでさらに割引
   - 定期レンタルプランの提供

4. **セット割引**:
   - 複数商品同時レンタルの割引
   - 組み合わせパッケージの料金

5. **顧客ランク別料金**:
   - リピーター割引
   - 会員ランク別の料金設定

ただし、これらの拡張は現時点では優先度が低く、必要に応じて実装を検討します。当面は現在のシンプルな料金体系を維持し、特別な割引が必要な場合はShopifyのクーポン機能を使用して対応します。

## まとめ

このドキュメントでは、レンタル商品ECシステムにおける現在の料金計算ロジックについて説明しました。シンプルな料金体系（1日目は基本料金、2〜7日目は各日20%、8日目以降は各日10%）を採用することで、実装の複雑さを軽減しつつ、柔軟なレンタルシステムを構築しています。

デポジット計算も合計料金の10%という明確なルールで実装されており、キャンセル料金も日数に応じた明確な料率で計算されています。

現在の料金計算ロジックはビジネス要件を満たしており、将来的な拡張も可能な設計となっています。
