import { json, type LoaderFunctionArgs, type ActionFunctionArgs, redirect } from "@remix-run/node";
import { useLoaderData, useSubmit, useNavigate, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  BlockStack,
  Button,
  InlineStack,
  TextField,
  Banner,
  FormLayout,
  Select,
  Divider
} from "@shopify/polaris";
import { useState, useCallback } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";

/**
 * 新規顧客作成ページのローダー
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    return json({
      shop,
    });
  } catch (error) {
    console.error("データ取得エラー:", error);
    return json({
      error: "データの取得中にエラーが発生しました",
    });
  }
}

/**
 * 新規顧客作成ページのアクション
 */
export async function action({ request }: ActionFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  // フォームデータを取得
  const formData = await request.formData();
  const action = formData.get("action") as string;

  try {
    if (action === "create_customer") {
      // 顧客作成処理
      const name = formData.get("name") as string;
      const email = formData.get("email") as string;
      const phone = formData.get("phone") as string;
      const address = formData.get("address") as string;
      const notes = formData.get("notes") as string;

      if (!name) {
        return json({
          success: false,
          error: "顧客名は必須項目です",
        });
      }

      // 顧客を作成
      const customer = await prisma.customer.create({
        data: {
          shop,
          name,
          email,
          phone,
          address,
          notes,
        },
      });

      // 顧客が作成されたら顧客一覧ページにリダイレクト
      if (customer) {
        return redirect(`/app/customers?success=true&name=${encodeURIComponent(name)}`);
      }

      return json({
        success: false,
        error: "顧客の作成に失敗しました",
      });
    }

    return json({
      success: false,
      error: "無効なアクションです",
    });
  } catch (error) {
    console.error("顧客作成エラー:", error);
    return json({
      success: false,
      error: "顧客の作成中にエラーが発生しました",
    });
  }
}

/**
 * 新規顧客作成ページ
 */
export default function CustomerNewPage() {
  const { error } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const navigate = useNavigate();

  // フォーム状態
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [address, setAddress] = useState("");
  const [notes, setNotes] = useState("");
  const [submitError, setSubmitError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 顧客作成ハンドラー
  const handleCreateCustomer = useCallback(() => {
    if (!name) {
      setSubmitError("顧客名を入力してください");
      return;
    }

    setIsSubmitting(true);
    setSubmitError("");

    const formData = new FormData();
    formData.append("action", "create_customer");
    formData.append("name", name);
    formData.append("email", email);
    formData.append("phone", phone);
    formData.append("address", address);
    formData.append("notes", notes);

    submit(formData, { method: "post" });
  }, [name, email, phone, address, notes, submit]);

  return (
    <Page
      title="新規顧客作成"
      subtitle="顧客情報を登録します"
      backAction={{ content: "顧客一覧に戻る", url: "/app/customers" }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                {error && (
                  <Box paddingBlockEnd="400">
                    <Banner tone="critical">
                      <p>{error}</p>
                    </Banner>
                  </Box>
                )}

                {submitError && (
                  <Box paddingBlockEnd="400">
                    <Banner tone="critical">
                      <p>{submitError}</p>
                    </Banner>
                  </Box>
                )}

                <FormLayout>
                  <TextField
                    label="顧客名 *"
                    value={name}
                    onChange={setName}
                    placeholder="例: 山田太郎"
                    autoComplete="off"
                    required
                  />

                  <TextField
                    label="メールアドレス"
                    value={email}
                    onChange={setEmail}
                    placeholder="例: <EMAIL>"
                    autoComplete="off"
                    type="email"
                  />

                  <TextField
                    label="電話番号"
                    value={phone}
                    onChange={setPhone}
                    placeholder="例: 090-1234-5678"
                    autoComplete="off"
                  />

                  <TextField
                    label="住所"
                    value={address}
                    onChange={setAddress}
                    placeholder="例: 東京都渋谷区..."
                    autoComplete="off"
                    multiline={2}
                  />

                  <TextField
                    label="備考"
                    value={notes}
                    onChange={setNotes}
                    placeholder="備考があれば入力してください"
                    autoComplete="off"
                    multiline={3}
                  />
                </FormLayout>

                <Divider />

                <Box paddingBlockStart="400">
                  <InlineStack gap="400" align="end">
                    <Button onClick={() => navigate("/app/customers")}>キャンセル</Button>
                    <Button
                      primary
                      onClick={handleCreateCustomer}
                      loading={isSubmitting}
                    >
                      顧客を作成
                    </Button>
                  </InlineStack>
                </Box>
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
