/**
 * テスト商品セットアップスクリプト
 *
 * このスクリプトでは以下を実行します：
 * 1. 既存のテスト商品を削除
 * 2. 新しい仕様に沿ったテスト商品を追加
 * 3. テスト用の予約データを作成
 */

import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// GraphQLクエリとミューテーション
const GET_PRODUCTS = gql`
  query getProducts($query: String) {
    products(first: 50, query: $query) {
      edges {
        node {
          id
          title
          handle
          variants(first: 10) {
            edges {
              node {
                id
                title
                price
              }
            }
          }
        }
      }
    }
  }
`;

const DELETE_PRODUCT = gql`
  mutation productDelete($input: ProductDeleteInput!) {
    productDelete(input: $input) {
      deletedProductId
      shop {
        id
      }
      userErrors {
        field
        message
      }
    }
  }
`;

const CREATE_PRODUCT = gql`
  mutation productCreate($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * 既存のテスト商品を削除
 */
async function deleteTestProducts() {
  console.log("=== 既存のテスト商品を削除 ===\n");

  try {
    // テスト商品を検索
    const result = await shopifyClient.request(GET_PRODUCTS, {
      query: "title:テスト商品"
    });

    const products = result.products.edges;
    console.log(`${products.length}件のテスト商品が見つかりました`);

    // 各商品を削除
    for (const product of products) {
      const productId = product.node.id;
      const title = product.node.title;

      console.log(`商品を削除: ${title} (${productId})`);

      // Shopify APIで商品を削除
      await shopifyClient.request(DELETE_PRODUCT, {
        input: { id: productId }
      });

      // データベースからも削除
      await prisma.product.deleteMany({
        where: {
          shopifyId: productId.replace('gid://shopify/Product/', '')
        }
      });
    }

    console.log("\n既存のテスト商品の削除が完了しました");
  } catch (error) {
    console.error("テスト商品の削除中にエラーが発生しました:", error);
  }
}

/**
 * 新しいテスト商品を作成
 */
async function createTestProducts() {
  console.log("\n=== 新しいテスト商品を作成 ===\n");

  const testProducts = [
    {
      title: "テスト商品A（家具）",
      productType: "家具",
      basePrice: 10000,
      sku: "TEST-FURNITURE-A",
      description: "テスト用の家具商品です。バリエーション方式のテスト用に使用します。"
    },
    {
      title: "テスト商品B（電化製品）",
      productType: "電化製品",
      basePrice: 5000,
      sku: "TEST-ELECTRONICS-B",
      description: "テスト用の電化製品です。バリエーション方式のテスト用に使用します。"
    },
    {
      title: "テスト商品C（食器）",
      productType: "食器",
      basePrice: 3000,
      sku: "TEST-TABLEWARE-C",
      description: "テスト用の食器セットです。バリエーション方式のテスト用に使用します。"
    }
  ];

  try {
    for (const product of testProducts) {
      console.log(`商品を作成: ${product.title}`);

      // バリエーションの価格を計算
      const prices = calculateVariantPrices(product.basePrice);

      // Shopify APIで商品を作成
      const result = await shopifyClient.request(CREATE_PRODUCT, {
        input: {
          title: product.title,
          productType: product.productType,
          descriptionHtml: product.description,
          vendor: "テスト販売者",
          options: ["レンタル日数"],
          variants: [
            { price: prices[0].toString(), options: ["1日"], sku: `${product.sku}-1D` },
            { price: prices[1].toString(), options: ["2日"], sku: `${product.sku}-2D` },
            { price: prices[2].toString(), options: ["3日"], sku: `${product.sku}-3D` },
            { price: prices[3].toString(), options: ["4日"], sku: `${product.sku}-4D` },
            { price: prices[4].toString(), options: ["5日"], sku: `${product.sku}-5D` },
            { price: prices[5].toString(), options: ["6日"], sku: `${product.sku}-6D` },
            { price: prices[6].toString(), options: ["7日"], sku: `${product.sku}-7D` },
            { price: prices[6].toString(), options: ["8日以上"], sku: `${product.sku}-8D+` }
          ]
        }
      });

      if (result.productCreate.userErrors.length > 0) {
        console.error("商品作成エラー:", result.productCreate.userErrors);
        continue;
      }

      const createdProduct = result.productCreate.product;
      console.log(`商品が作成されました: ${createdProduct.title} (${createdProduct.id})`);

      // データベースにも商品を登録
      await prisma.product.create({
        data: {
          title: product.title,
          sku: product.sku,
          price: product.basePrice,
          description: product.description,
          status: "AVAILABLE",
          shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
          shopifyId: createdProduct.id.replace('gid://shopify/Product/', ''),
          basicInfo: JSON.stringify({
            productCode: product.sku,
            status: 'available',
            location: 'NY'
          })
        }
      });
    }

    console.log("\n新しいテスト商品の作成が完了しました");
  } catch (error) {
    console.error("テスト商品の作成中にエラーが発生しました:", error);
  }
}

/**
 * バリエーションの価格を計算
 * @param {number} basePrice 基本料金
 * @returns {number[]} 各バリエーションの価格
 */
function calculateVariantPrices(basePrice) {
  return [
    basePrice,                    // 1日: 100%
    basePrice * 1.2,              // 2日: 120%
    basePrice * 1.4,              // 3日: 140%
    basePrice * 1.6,              // 4日: 160%
    basePrice * 1.8,              // 5日: 180%
    basePrice * 2.0,              // 6日: 200%
    basePrice * 2.2,              // 7日: 220%
    // 8日以上も7日と同じ価格（追加料金は別途計算）
  ];
}

/**
 * テスト用の予約データを作成
 */
async function createTestBookings() {
  console.log("\n=== テスト用の予約データを作成 ===\n");

  try {
    // データベースから商品を取得
    const products = await prisma.product.findMany({
      where: {
        sku: {
          startsWith: "TEST-"
        }
      }
    });

    if (products.length === 0) {
      console.log("テスト商品が見つかりません");
      return;
    }

    // 既存の予約を削除
    await prisma.booking.deleteMany({
      where: {
        productId: {
          in: products.map(p => p.id)
        }
      }
    });

    console.log("既存の予約を削除しました");

    // 現在の日付
    const now = new Date();

    // テスト用の予約データ
    const bookings = [
      {
        productId: products[0].id,
        startDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 5),
        endDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 7),
        status: "CONFIRMED",
        customerName: "テスト顧客A",
        customerEmail: "<EMAIL>"
      },
      {
        productId: products[0].id,
        startDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 15),
        endDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 20),
        status: "PROVISIONAL",
        customerName: "テスト顧客B",
        customerEmail: "<EMAIL>"
      },
      {
        productId: products[1].id,
        startDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 3),
        endDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 10),
        status: "CONFIRMED",
        customerName: "テスト顧客C",
        customerEmail: "<EMAIL>"
      }
    ];

    // 予約データを作成
    for (const booking of bookings) {
      const createdBooking = await prisma.booking.create({
        data: {
          productId: booking.productId,
          startDate: booking.startDate,
          endDate: booking.endDate,
          status: booking.status,
          customerName: booking.customerName,
          customerEmail: booking.customerEmail,
          bookingId: `BOOK-${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
          totalAmount: "10000",
          depositAmount: "1000",
          shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com'
        }
      });

      console.log(`予約を作成しました: ${createdBooking.bookingId} (${booking.status})`);
    }

    console.log("\nテスト用の予約データの作成が完了しました");
  } catch (error) {
    console.error("予約データの作成中にエラーが発生しました:", error);
  }
}

// メイン処理
async function main() {
  try {
    await deleteTestProducts();
    await createTestProducts();
    await createTestBookings();

    console.log("\n=== テスト環境のセットアップが完了しました ===");
  } catch (error) {
    console.error("テスト環境のセットアップ中にエラーが発生しました:", error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
