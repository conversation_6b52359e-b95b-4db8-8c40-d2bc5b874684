import { json } from "@remix-run/node";
import { useLoaderData, useSubmit, Form, useNavigate } from "@remix-run/react";
import { Page, Layout, Card, Button, Select, Banner, Text, Spinner, DatePicker, Box, Divider, Badge, List, Link } from "@shopify/polaris";
import { useState, useCallback } from "react";
import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { prisma } from "../db.server";
import { authenticate } from "../shopify.server";
import { formatISODate, parseISODate } from "../utils/calendar/calendar-utils";

/**
 * カレンダー移行テストページ
 * メタフィールドからデータベースへの移行が正しく行われたかを確認
 */

// ローダーデータの型
interface LoaderData {
  products: Array<{
    id: string;
    title: string;
    shopifyId: string;
  }>;
  rentalAvailabilities: any[];
  inventoryCalendars: any[];
  bookings: any[];
  productId: string | null;
  availableDates: any | null;
  blackoutDates: any[];
}

// ローダー関数
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  
  // URLパラメータの取得
  const url = new URL(request.url);
  const productId = url.searchParams.get("productId");
  
  // 商品一覧を取得
  const products = await prisma.product.findMany({
    select: {
      id: true,
      title: true,
      shopifyId: true
    },
    take: 100,
    orderBy: {
      title: 'asc'
    }
  });
  
  let rentalAvailabilities: any[] = [];
  let inventoryCalendars: any[] = [];
  let bookings: any[] = [];
  let availableDates = null;
  let blackoutDates: any[] = [];
  
  // 商品が選択されている場合はデータを取得
  if (productId) {
    // レンタル可能期間を取得
    rentalAvailabilities = await prisma.rentalAvailability.findMany({
      where: { productId }
    });
    
    // 在庫カレンダーを取得
    const today = new Date();
    const thirtyDaysLater = new Date(today);
    thirtyDaysLater.setDate(today.getDate() + 30);
    
    inventoryCalendars = await prisma.inventoryCalendar.findMany({
      where: {
        productId,
        date: {
          gte: today,
          lte: thirtyDaysLater
        }
      },
      orderBy: {
        date: 'asc'
      }
    });
    
    // 予約情報を取得
    bookings = await prisma.booking.findMany({
      where: {
        productId,
        status: { in: ['CONFIRMED', 'PROVISIONAL'] }
      }
    });
    
    // 既存のAvailableDatesを取得
    availableDates = await prisma.availableDates.findUnique({
      where: { productId }
    });
    
    // 既存のBlackoutDatesを取得
    blackoutDates = await prisma.blackoutDate.findMany({
      where: { productId }
    });
  }
  
  return json({
    products,
    rentalAvailabilities,
    inventoryCalendars,
    bookings,
    productId,
    availableDates,
    blackoutDates
  });
}

// アクション関数
export async function action({ request }: ActionFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  
  const formData = await request.formData();
  const action = formData.get("action") as string;
  const productId = formData.get("productId") as string;
  
  if (action === "migrate") {
    try {
      // 商品の存在確認
      const product = await prisma.product.findUnique({
        where: { id: productId }
      });
      
      if (!product) {
        return json({ error: `Product not found: ${productId}` }, { status: 404 });
      }
      
      // メタフィールドからデータを取得
      const metafields = await admin.graphql(`
        query {
          product(id: "gid://shopify/Product/${product.shopifyId}") {
            metafields(first: 50) {
              edges {
                node {
                  id
                  namespace
                  key
                  value
                  type
                }
              }
            }
          }
        }
      `);
      
      const metafieldsData = await metafields.json();
      const metafieldEdges = metafieldsData.data.product.metafields.edges;
      
      // レンタル期間メタフィールドを検索
      const rentalDatesMetafield = metafieldEdges.find(
        (edge: any) => edge.node.namespace === "rental" && edge.node.key === "rental_dates"
      );
      
      // 予約メタフィールドを検索
      const bookingsMetafield = metafieldEdges.find(
        (edge: any) => edge.node.namespace === "rental" && edge.node.key === "bookings"
      );
      
      // レンタル期間データを処理
      if (rentalDatesMetafield) {
        const rentalDates = JSON.parse(rentalDatesMetafield.node.value);
        
        // レンタル可能期間を処理
        if (rentalDates.availableDates) {
          // RentalAvailabilityを作成
          await prisma.rentalAvailability.upsert({
            where: {
              productId_startDate_endDate: {
                productId,
                startDate: parseISODate(rentalDates.availableDates.start),
                endDate: parseISODate(rentalDates.availableDates.end)
              }
            },
            update: {
              isAvailable: true,
              notes: "Migrated from metafield"
            },
            create: {
              shop: product.shop,
              productId,
              shopifyProductId: product.shopifyId,
              startDate: parseISODate(rentalDates.availableDates.start),
              endDate: parseISODate(rentalDates.availableDates.end),
              isAvailable: true,
              notes: "Migrated from metafield"
            }
          });
          
          // AvailableDatesを作成
          await prisma.availableDates.upsert({
            where: { productId },
            update: {
              startDate: parseISODate(rentalDates.availableDates.start),
              endDate: parseISODate(rentalDates.availableDates.end)
            },
            create: {
              productId,
              startDate: parseISODate(rentalDates.availableDates.start),
              endDate: parseISODate(rentalDates.availableDates.end)
            }
          });
        }
        
        // ブラックアウト日を処理
        if (rentalDates.blackoutDates && rentalDates.blackoutDates.length > 0) {
          for (const blackout of rentalDates.blackoutDates) {
            // BlackoutDateを作成
            await prisma.blackoutDate.upsert({
              where: {
                productId_startDate_endDate: {
                  productId,
                  startDate: parseISODate(blackout.start),
                  endDate: parseISODate(blackout.end)
                }
              },
              update: {
                reason: blackout.reason || "Migrated from metafield"
              },
              create: {
                productId,
                startDate: parseISODate(blackout.start),
                endDate: parseISODate(blackout.end),
                reason: blackout.reason || "Migrated from metafield"
              }
            });
            
            // InventoryCalendarを更新
            const startDate = parseISODate(blackout.start);
            const endDate = parseISODate(blackout.end);
            
            // 日付範囲内の各日付に対して処理
            for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
              const currentDate = new Date(date);
              
              await prisma.inventoryCalendar.upsert({
                where: {
                  shop_productId_date: {
                    shop: product.shop,
                    productId,
                    date: currentDate
                  }
                },
                update: {
                  isAvailable: false,
                  unavailableReason: "blackout",
                  note: blackout.reason || "Blackout period"
                },
                create: {
                  shop: product.shop,
                  productId,
                  shopifyProductId: product.shopifyId,
                  date: currentDate,
                  isAvailable: false,
                  unavailableReason: "blackout",
                  note: blackout.reason || "Blackout period"
                }
              });
            }
          }
        }
      }
      
      // 予約データを処理
      if (bookingsMetafield) {
        const bookingsData = JSON.parse(bookingsMetafield.node.value);
        
        if (bookingsData.bookings && bookingsData.bookings.length > 0) {
          for (const booking of bookingsData.bookings) {
            // Bookingを作成
            await prisma.booking.upsert({
              where: { bookingId: booking.id },
              update: {
                status: booking.status === 'cancelled' ? 'CANCELLED' : 
                        booking.status === 'completed' ? 'COMPLETED' : 
                        booking.type === 'confirmed' ? 'CONFIRMED' : 'PROVISIONAL',
                bookingType: booking.type === 'confirmed' ? 'CONFIRMED' : 'PROVISIONAL',
                priority: booking.priority || 1,
                customerEmail: booking.customerEmail,
                customerName: booking.customerName
              },
              create: {
                productId,
                startDate: parseISODate(booking.startDate),
                endDate: parseISODate(booking.endDate),
                status: booking.status === 'cancelled' ? 'CANCELLED' : 
                        booking.status === 'completed' ? 'COMPLETED' : 
                        booking.type === 'confirmed' ? 'CONFIRMED' : 'PROVISIONAL',
                bookingType: booking.type === 'confirmed' ? 'CONFIRMED' : 'PROVISIONAL',
                priority: booking.priority || 1,
                customerEmail: booking.customerEmail,
                customerName: booking.customerName,
                bookingId: booking.id,
                shop: product.shop
              }
            });
            
            // 予約がキャンセルされていない場合のみInventoryCalendarを更新
            if (booking.status !== 'cancelled') {
              const startDate = parseISODate(booking.startDate);
              const endDate = parseISODate(booking.endDate);
              
              // 日付範囲内の各日付に対して処理
              for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
                const currentDate = new Date(date);
                
                // 既存のInventoryCalendarを確認
                const existingInventory = await prisma.inventoryCalendar.findUnique({
                  where: {
                    shop_productId_date: {
                      shop: product.shop,
                      productId,
                      date: currentDate
                    }
                  }
                });
                
                // ブラックアウト日でない場合のみ更新
                if (!existingInventory || existingInventory.unavailableReason !== "blackout") {
                  await prisma.inventoryCalendar.upsert({
                    where: {
                      shop_productId_date: {
                        shop: product.shop,
                        productId,
                        date: currentDate
                      }
                    },
                    update: {
                      isAvailable: false,
                      unavailableReason: "booked",
                      bookingId: booking.id,
                      note: `Booked by ${booking.customerName || 'customer'}`
                    },
                    create: {
                      shop: product.shop,
                      productId,
                      shopifyProductId: product.shopifyId,
                      date: currentDate,
                      isAvailable: false,
                      unavailableReason: "booked",
                      bookingId: booking.id,
                      note: `Booked by ${booking.customerName || 'customer'}`
                    }
                  });
                }
              }
            }
          }
        }
      }
      
      return json({ success: true });
    } catch (error) {
      console.error("Migration error:", error);
      return json({ error: (error as Error).message }, { status: 500 });
    }
  }
  
  return json({ error: "Invalid action" }, { status: 400 });
}

// メインコンポーネント
export default function CalendarMigrationTest() {
  const { 
    products, 
    rentalAvailabilities, 
    inventoryCalendars, 
    bookings, 
    productId,
    availableDates,
    blackoutDates
  } = useLoaderData<typeof loader>();
  
  const [selectedProductId, setSelectedProductId] = useState(productId || "");
  const [isLoading, setIsLoading] = useState(false);
  const [migrationResult, setMigrationResult] = useState<{ success?: boolean; error?: string } | null>(null);
  
  const submit = useSubmit();
  const navigate = useNavigate();
  
  // 商品選択時の処理
  const handleProductChange = useCallback((value: string) => {
    setSelectedProductId(value);
    navigate(`/app/calendar-migration-test?productId=${value}`);
  }, [navigate]);
  
  // 移行実行時の処理
  const handleMigrate = useCallback(() => {
    if (!selectedProductId) return;
    
    setIsLoading(true);
    setMigrationResult(null);
    
    const formData = new FormData();
    formData.append("action", "migrate");
    formData.append("productId", selectedProductId);
    
    submit(formData, { method: "post" });
    
    // 移行完了後の処理
    setTimeout(() => {
      setIsLoading(false);
      setMigrationResult({ success: true });
      
      // ページをリロード
      navigate(`/app/calendar-migration-test?productId=${selectedProductId}`);
    }, 2000);
  }, [selectedProductId, submit, navigate]);
  
  // 商品選択オプションの作成
  const productOptions = [
    { label: "商品を選択", value: "" },
    ...products.map(product => ({
      label: product.title || product.id,
      value: product.id
    }))
  ];
  
  return (
    <Page
      title="カレンダー移行テスト"
      subtitle="メタフィールドからデータベースへの移行が正しく行われたかを確認します"
      backAction={{ content: "戻る", url: "/app" }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Card.Section>
              <Select
                label="商品を選択"
                options={productOptions}
                value={selectedProductId}
                onChange={handleProductChange}
              />
            </Card.Section>
            
            {selectedProductId && (
              <Card.Section>
                <Button
                  primary
                  onClick={handleMigrate}
                  loading={isLoading}
                  disabled={isLoading}
                >
                  選択した商品のカレンダーデータを移行
                </Button>
                
                {migrationResult?.success && (
                  <Banner status="success" title="移行成功" onDismiss={() => setMigrationResult(null)}>
                    <p>カレンダーデータの移行が完了しました。</p>
                  </Banner>
                )}
                
                {migrationResult?.error && (
                  <Banner status="critical" title="移行エラー" onDismiss={() => setMigrationResult(null)}>
                    <p>{migrationResult.error}</p>
                  </Banner>
                )}
              </Card.Section>
            )}
          </Card>
        </Layout.Section>
        
        {selectedProductId && (
          <>
            <Layout.Section>
              <Card title="レンタル可能期間">
                <Card.Section>
                  <Text variant="headingMd">RentalAvailability</Text>
                  {rentalAvailabilities.length > 0 ? (
                    <List type="bullet">
                      {rentalAvailabilities.map(ra => (
                        <List.Item key={ra.id}>
                          {formatISODate(ra.startDate)} 〜 {formatISODate(ra.endDate)}
                          {ra.isAvailable ? (
                            <Badge status="success">利用可能</Badge>
                          ) : (
                            <Badge status="critical">利用不可</Badge>
                          )}
                          {ra.notes && <div>備考: {ra.notes}</div>}
                        </List.Item>
                      ))}
                    </List>
                  ) : (
                    <Text>レンタル可能期間が設定されていません</Text>
                  )}
                </Card.Section>
                
                <Card.Section>
                  <Text variant="headingMd">AvailableDates (旧モデル)</Text>
                  {availableDates ? (
                    <div>
                      {formatISODate(availableDates.startDate)} 〜 {formatISODate(availableDates.endDate)}
                    </div>
                  ) : (
                    <Text>利用可能期間が設定されていません</Text>
                  )}
                </Card.Section>
              </Card>
            </Layout.Section>
            
            <Layout.Section>
              <Card title="ブラックアウト日">
                <Card.Section>
                  <Text variant="headingMd">BlackoutDate</Text>
                  {blackoutDates.length > 0 ? (
                    <List type="bullet">
                      {blackoutDates.map(bd => (
                        <List.Item key={bd.id}>
                          {formatISODate(bd.startDate)} 〜 {formatISODate(bd.endDate)}
                          {bd.reason && <div>理由: {bd.reason}</div>}
                        </List.Item>
                      ))}
                    </List>
                  ) : (
                    <Text>ブラックアウト日が設定されていません</Text>
                  )}
                </Card.Section>
              </Card>
            </Layout.Section>
            
            <Layout.Section>
              <Card title="在庫カレンダー">
                <Card.Section>
                  <Text variant="headingMd">InventoryCalendar</Text>
                  {inventoryCalendars.length > 0 ? (
                    <List type="bullet">
                      {inventoryCalendars.map(ic => (
                        <List.Item key={ic.id}>
                          {formatISODate(ic.date)}
                          {ic.isAvailable ? (
                            <Badge status="success">利用可能</Badge>
                          ) : (
                            <Badge status="critical">利用不可 ({ic.unavailableReason})</Badge>
                          )}
                          {ic.note && <div>備考: {ic.note}</div>}
                          {ic.bookingId && <div>予約ID: {ic.bookingId}</div>}
                        </List.Item>
                      ))}
                    </List>
                  ) : (
                    <Text>在庫カレンダーが設定されていません</Text>
                  )}
                </Card.Section>
              </Card>
            </Layout.Section>
            
            <Layout.Section>
              <Card title="予約情報">
                <Card.Section>
                  <Text variant="headingMd">Booking</Text>
                  {bookings.length > 0 ? (
                    <List type="bullet">
                      {bookings.map(booking => (
                        <List.Item key={booking.id}>
                          {formatISODate(booking.startDate)} 〜 {formatISODate(booking.endDate)}
                          <Badge status={
                            booking.status === 'CONFIRMED' ? 'success' :
                            booking.status === 'PROVISIONAL' ? 'attention' :
                            booking.status === 'CANCELLED' ? 'critical' : 'info'
                          }>
                            {booking.status}
                          </Badge>
                          <div>予約ID: {booking.bookingId}</div>
                          {booking.customerName && <div>顧客名: {booking.customerName}</div>}
                          {booking.customerEmail && <div>メール: {booking.customerEmail}</div>}
                        </List.Item>
                      ))}
                    </List>
                  ) : (
                    <Text>予約情報が設定されていません</Text>
                  )}
                </Card.Section>
              </Card>
            </Layout.Section>
          </>
        )}
      </Layout>
    </Page>
  );
}
