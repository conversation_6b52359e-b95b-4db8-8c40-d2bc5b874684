/**
 * テスト実行ユーティリティ
 * 
 * このモジュールは、テストを実行し、結果を収集するための関数を提供します。
 * 複数のテストを連続実行し、結果を集約する機能を含みます。
 */

import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { TestResult, TestResultSet, saveTestResults } from './test-result-validator';
import { generateHtmlReport, generateMarkdownReport } from './test-reporter';

// 色付きログ出力のための定数
export const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
};

// テスト設定の型定義
export interface TestConfig {
  command: string;
  name: string;
  expectedResults?: Record<string, any>;
  timeout?: number;
}

// テスト実行オプションの型定義
export interface TestRunOptions {
  outputDir?: string;
  logFile?: string;
  jsonFile?: string;
  markdownFile?: string;
  htmlFile?: string;
  openInBrowser?: boolean;
  validateResults?: boolean;
}

/**
 * コマンドを実行する関数
 * @param command 実行するコマンド
 * @param options オプション
 * @returns 実行結果
 */
export function executeCommand(command: string, options: { silent?: boolean, timeout?: number } = {}): { success: boolean, output: string } {
  try {
    const output = execSync(command, {
      encoding: 'utf8',
      stdio: options.silent ? 'pipe' : 'inherit',
      timeout: options.timeout || 300000 // デフォルトは5分
    });
    return { success: true, output };
  } catch (error: any) {
    return {
      success: false,
      output: error.stdout || error.message || String(error)
    };
  }
}

/**
 * ログを書き込む関数
 * @param message ログメッセージ
 * @param logFile ログファイルのパス
 */
export function writeLog(message: string, logFile?: string): void {
  console.log(message);
  if (logFile) {
    fs.appendFileSync(logFile, message + '\n');
  }
}

/**
 * テストを実行する関数
 * @param tests テスト設定の配列
 * @param options テスト実行オプション
 * @returns テスト結果セットとレポートファイルのパス
 */
export async function runTests(
  tests: TestConfig[],
  options: TestRunOptions = {}
): Promise<{ results: TestResultSet, reportFiles: { json?: string, markdown?: string, html?: string } }> {
  // 開始時刻
  const startTime = new Date();
  const timestamp = startTime.toISOString().replace(/:/g, '-').replace(/\..+/, '');

  // 出力ディレクトリの設定
  const outputDir = options.outputDir || path.join(process.cwd(), 'app/tests/results');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // ログファイルの設定
  const logFile = options.logFile || path.join(outputDir, `test-run-${timestamp}.log`);
  fs.writeFileSync(logFile, ''); // ログファイルを初期化

  writeLog(`${COLORS.MAGENTA}=== テスト実行 ===${COLORS.RESET}`, logFile);
  writeLog(`開始時刻: ${startTime.toLocaleString()}`, logFile);
  writeLog(`ログファイル: ${logFile}`, logFile);

  // 結果を保存する配列
  const testResults: TestResult[] = [];

  // 各テストを実行
  for (const test of tests) {
    writeLog(`\n${COLORS.CYAN}=== ${test.name} を実行中... ===${COLORS.RESET}\n`, logFile);

    try {
      const startTestTime = Date.now();
      const { success, output } = executeCommand(test.command, {
        silent: true,
        timeout: test.timeout || 300000
      });
      const endTestTime = Date.now();
      const testDuration = (endTestTime - startTestTime) / 1000;

      writeLog(output, logFile);

      if (success) {
        writeLog(`\n${COLORS.GREEN}✅ ${test.name} が正常に完了しました (${testDuration.toFixed(2)}秒)${COLORS.RESET}\n`, logFile);
        testResults.push({
          testName: test.name,
          success: true,
          timestamp: new Date().toISOString(),
          message: `テストが正常に完了しました (${testDuration.toFixed(2)}秒)`,
          details: { duration: testDuration }
        });
      } else {
        writeLog(`\n${COLORS.RED}❌ ${test.name} の実行中にエラーが発生しました (${testDuration.toFixed(2)}秒)${COLORS.RESET}\n`, logFile);
        testResults.push({
          testName: test.name,
          success: false,
          timestamp: new Date().toISOString(),
          error: `テスト実行中にエラーが発生しました`,
          details: { duration: testDuration, output }
        });
      }
    } catch (error: any) {
      writeLog(`\n${COLORS.RED}❌ ${test.name} の実行中に予期しないエラーが発生しました${COLORS.RESET}\n`, logFile);
      writeLog(error.stack || error.message || String(error), logFile);
      testResults.push({
        testName: test.name,
        success: false,
        timestamp: new Date().toISOString(),
        error: error.message || String(error)
      });
    }
  }

  // 終了時刻と実行時間の計算
  const endTime = new Date();
  const duration = (endTime.getTime() - startTime.getTime()) / 1000;

  // 成功/失敗の集計
  const successCount = testResults.filter(r => r.success).length;
  const failureCount = testResults.length - successCount;

  // 合計を表示
  writeLog(`\n${COLORS.CYAN}合計: ${testResults.length} テスト${COLORS.RESET}`, logFile);
  writeLog(`${COLORS.GREEN}成功: ${successCount} テスト${COLORS.RESET}`, logFile);
  writeLog(`${COLORS.RED}失敗: ${failureCount} テスト${COLORS.RESET}`, logFile);

  // 終了時刻を表示
  writeLog(`\n終了時刻: ${endTime.toLocaleString()}`, logFile);
  writeLog(`実行時間: ${duration.toFixed(2)}秒`, logFile);

  // テスト結果セットを作成
  const results: TestResultSet = {
    timestamp: startTime.toISOString(),
    duration,
    totalTests: testResults.length,
    successCount,
    failureCount,
    tests: testResults
  };

  // レポートファイルのパス
  const reportFiles: { json?: string, markdown?: string, html?: string } = {};

  // JSONファイルに保存
  const jsonFilePath = options.jsonFile || path.join(outputDir, `test-results-${timestamp}.json`);
  saveTestResults(results, jsonFilePath);
  reportFiles.json = jsonFilePath;
  writeLog(`\n結果JSONファイル: ${jsonFilePath}`, logFile);

  // Markdownレポートを生成
  if (options.markdownFile !== false) {
    const markdownFilePath = options.markdownFile || path.join(outputDir, `test-results-${timestamp}.md`);
    generateMarkdownReport(results, markdownFilePath);
    reportFiles.markdown = markdownFilePath;
    writeLog(`\nMarkdownレポート: ${markdownFilePath}`, logFile);
  }

  // HTMLレポートを生成
  if (options.htmlFile !== false) {
    const htmlFilePath = options.htmlFile || path.join(outputDir, `test-results-${timestamp}.html`);
    generateHtmlReport(results, htmlFilePath);
    reportFiles.html = htmlFilePath;
    writeLog(`\nHTMLレポート: ${htmlFilePath}`, logFile);

    // ブラウザでレポートを開く
    if (options.openInBrowser) {
      try {
        const platform = os.platform();
        if (platform === 'darwin') {
          // macOS
          execSync(`open "${htmlFilePath}"`);
        } else if (platform === 'win32') {
          // Windows
          execSync(`start "" "${htmlFilePath}"`);
        } else if (platform === 'linux') {
          // Linux
          execSync(`xdg-open "${htmlFilePath}"`);
        } else {
          writeLog(`\nレポートが生成されました: ${htmlFilePath}`, logFile);
          writeLog('ブラウザで手動で開いてください。', logFile);
        }
      } catch (error) {
        writeLog(`\nレポートが生成されましたが、ブラウザでの表示に失敗しました: ${htmlFilePath}`, logFile);
        writeLog('ブラウザで手動で開いてください。', logFile);
      }
    }
  }

  return { results, reportFiles };
}

/**
 * 期待結果ファイルを読み込む関数
 * @param filePath 期待結果ファイルのパス
 * @returns 期待結果オブジェクト
 */
export function loadExpectedResults(filePath: string): Record<string, any> {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    throw new Error(`期待結果の読み込みに失敗しました: ${error instanceof Error ? error.message : String(error)}`);
  }
}
