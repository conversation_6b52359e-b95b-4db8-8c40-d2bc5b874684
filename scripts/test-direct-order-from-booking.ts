/**
 * 予約データからShopify注文を直接作成するテストスクリプト
 *
 * このスクリプトは、既存の予約データからShopify注文を作成し、
 * 予約データとShopify注文が正しく関連付けられているかをテストします。
 * Shopify Admin APIに直接アクセスするため、認証の問題を回避できます。
 *
 * 実行方法: npx tsx scripts/test-direct-order-from-booking.ts [予約ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';
import { createOrderFromBooking } from '../app/utils/booking/order-creator';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * Shopify Admin APIを直接初期化する関数
 */
function initializeDirectShopifyAdmin() {
  try {
    // 環境変数からアクセストークンを取得
    const shopifyAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
    const shop = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';

    if (!shopifyAccessToken) {
      throw new Error('SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません');
    }

    console.log(`ショップ: ${shop}`);
    console.log(`アクセストークン: ${shopifyAccessToken.substring(0, 5)}...`);

    // 直接AdminAPIクライアントを作成
    const admin = {
      graphql: async (query, options) => {
        const url = `https://${shop}/admin/api/2025-01/graphql.json`;
        console.log(`GraphQL URL: ${url}`);

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': shopifyAccessToken
          },
          body: JSON.stringify({
            query,
            variables: options?.variables
          })
        });

        return response;
      },
      rest: {
        resources: {
          // 必要に応じてRESTリソースを追加
        }
      }
    };

    console.log('Shopify Admin APIの直接初期化が完了しました');
    return admin;
  } catch (error) {
    console.error('Shopify Admin API初期化エラー:', error);
    throw error;
  }
}

/**
 * 予約情報を表示する関数
 */
async function displayBookingInfo(bookingId: string) {
  try {
    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        product: true
      }
    });

    if (!booking) {
      console.error(`予約ID ${bookingId} が見つかりません`);
      return null;
    }

    // 予約情報を表示
    console.log('予約情報:');
    console.log('- 予約ID:', booking.id);
    console.log('- 予約番号:', booking.bookingId);
    console.log('- 予約タイプ:', booking.bookingType);
    console.log('- 開始日:', format(booking.startDate, 'yyyy/MM/dd', { locale: ja }));
    console.log('- 終了日:', format(booking.endDate, 'yyyy/MM/dd', { locale: ja }));
    console.log('- 顧客名:', booking.customerName);
    console.log('- 顧客メール:', booking.customerEmail);
    console.log('- 顧客ID:', booking.customerId);
    
    if (booking.product) {
      console.log('- 商品名:', booking.product.title);
      console.log('- 商品ID:', booking.product.id);
      console.log('- Shopify商品ID:', booking.product.shopifyId);
    } else {
      console.log('- 商品情報: 見つかりません');
      console.log('- 商品ID (予約から):', booking.productId || 'なし');
    }
    
    console.log('- 合計金額:', booking.totalAmount);
    console.log('- 注文ID:', booking.orderId || 'なし');
    console.log('- 注文番号:', booking.orderName || 'なし');

    return booking;
  } catch (error) {
    console.error('予約情報の取得中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * メイン処理
 */
async function main() {
  try {
    // コマンドライン引数から予約IDを取得
    const bookingId = process.argv[2];
    
    if (!bookingId) {
      console.error('予約IDを指定してください');
      console.error('使用方法: npx tsx scripts/test-direct-order-from-booking.ts [予約ID]');
      process.exit(1);
    }
    
    console.log(`予約ID ${bookingId} から注文を作成します...`);
    
    // 予約情報を表示
    const booking = await displayBookingInfo(bookingId);
    
    if (!booking) {
      process.exit(1);
    }
    
    // 既に注文が関連付けられているかチェック
    if (booking.orderId) {
      console.log(`予約ID ${bookingId} には既に注文ID ${booking.orderId} が関連付けられています。`);
      console.log('既存の注文を削除してから再実行するか、別の予約IDを指定してください。');
      process.exit(1);
    }
    
    // Shopify Admin APIを直接初期化
    console.log('Shopify Admin APIを直接初期化中...');
    const admin = initializeDirectShopifyAdmin();
    
    // 注文を作成
    console.log('注文作成を開始します...');
    
    const result = await createOrderFromBooking(
      prisma,
      admin,
      bookingId,
      3 // リトライ回数
    );
    
    console.log('注文作成結果:', result);
    
    if (result.success) {
      console.log('注文が正常に作成されました:');
      console.log('- 注文ID:', result.orderId);
      console.log('- 注文番号:', result.orderName);
      console.log('- ドラフト注文:', result.isDraftOrder ? 'はい' : 'いいえ');
      
      // 更新された予約情報を表示
      console.log('\n更新後の予約情報:');
      await displayBookingInfo(bookingId);
    } else {
      console.error('注文の作成に失敗しました:', result.error);
    }
    
    console.log('処理が完了しました');
    process.exit(0);
  } catch (error) {
    console.error('エラーが発生しました:', error);
    process.exit(1);
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
