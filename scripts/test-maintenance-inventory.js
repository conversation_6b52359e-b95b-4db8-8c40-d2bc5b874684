import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testMaintenanceInventory() {
  console.log('🔍 メンテナンス状態と在庫状況の確認を開始...\n');

  try {
    // TEST-TABLE-001 商品の情報を取得
    const product = await prisma.product.findFirst({
      where: {
        sku: 'TEST-TABLE-001'
      }
    });

    if (!product) {
      console.log('❌ TEST-TABLE-001 商品が見つかりません');
      return;
    }

    console.log('📦 商品情報:');
    console.log(`  - ID: ${product.id}`);
    console.log(`  - タイトル: ${product.title}`);
    console.log(`  - SKU: ${product.sku}`);
    console.log(`  - Shopify ID: ${product.shopifyId}`);
    console.log('');

    // メンテナンス記録を取得
    const maintenances = await prisma.maintenance.findMany({
      where: {
        productId: product.id
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log('🔧 メンテナンス記録:');
    if (maintenances.length === 0) {
      console.log('  - メンテナンス記録なし');
    } else {
      maintenances.forEach((maintenance, index) => {
        console.log(`  ${index + 1}. ID: ${maintenance.id}`);
        console.log(`     ステータス: ${maintenance.status}`);
        console.log(`     タイプ: ${maintenance.type}`);
        console.log(`     開始日: ${maintenance.startDate.toISOString().split('T')[0]}`);
        console.log(`     終了日: ${maintenance.endDate ? maintenance.endDate.toISOString().split('T')[0] : '未設定'}`);
        console.log(`     備考: ${maintenance.notes || 'なし'}`);
        console.log('');
      });
    }

    // 在庫カレンダーを取得（今日から7日間）
    const today = new Date();
    const endDate = new Date();
    endDate.setDate(today.getDate() + 7);

    const inventoryCalendar = await prisma.inventoryCalendar.findMany({
      where: {
        productId: product.id,
        date: {
          gte: today,
          lte: endDate
        }
      },
      orderBy: {
        date: 'asc'
      }
    });

    console.log('📅 在庫カレンダー（今日から7日間）:');
    if (inventoryCalendar.length === 0) {
      console.log('  - 在庫カレンダーデータなし');
    } else {
      inventoryCalendar.forEach(entry => {
        const dateStr = entry.date.toISOString().split('T')[0];
        const status = entry.isAvailable ? '✅ 利用可能' : '❌ 利用不可';
        const reason = entry.unavailableReason ? ` (理由: ${entry.unavailableReason})` : '';
        console.log(`  ${dateStr}: ${status}${reason}`);
      });
    }
    console.log('');

    // 進行中のメンテナンスがあるかチェック
    const activeMaintenances = maintenances.filter(m => m.status === 'IN_PROGRESS');

    console.log('⚠️  現在の状況:');
    if (activeMaintenances.length > 0) {
      console.log('  - 進行中のメンテナンスあり');
      console.log('  - 商品は予約不可状態であるべき');
    } else {
      console.log('  - 進行中のメンテナンスなし');
      console.log('  - 商品は予約可能状態であるべき');
    }

    // 在庫状況の整合性チェック
    const unavailableDates = inventoryCalendar.filter(entry => !entry.isAvailable);
    const maintenanceUnavailable = unavailableDates.filter(entry =>
      entry.unavailableReason === 'MAINTENANCE' || entry.unavailableReason === 'maintenance'
    );

    console.log('\n🔍 整合性チェック:');
    if (activeMaintenances.length > 0 && maintenanceUnavailable.length === 0) {
      console.log('  ❌ 問題: 進行中のメンテナンスがあるのに在庫カレンダーが更新されていません');
    } else if (activeMaintenances.length === 0 && maintenanceUnavailable.length > 0) {
      console.log('  ❌ 問題: 進行中のメンテナンスがないのに在庫カレンダーでメンテナンス中になっています');
    } else {
      console.log('  ✅ メンテナンス状態と在庫カレンダーが整合しています');
    }

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
testMaintenanceInventory();
