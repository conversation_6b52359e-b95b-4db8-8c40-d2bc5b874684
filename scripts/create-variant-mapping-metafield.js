/**
 * variant_mappingメタフィールド作成スクリプト
 * 
 * このスクリプトは、商品バリエーション方式の実装に必要なvariant_mappingメタフィールドを作成します。
 * このメタフィールドは、レンタル日数とバリエーションIDのマッピングを管理します。
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数を読み込む
dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// メタフィールド定義作成のGraphQLクエリ
const CREATE_METAFIELD_DEFINITION = gql`
  mutation metafieldDefinitionCreate($definition: MetafieldDefinitionInput!) {
    metafieldDefinitionCreate(definition: $definition) {
      createdDefinition {
        id
        name
        namespace
        key
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 商品とそのバリエーションを取得するGraphQLクエリ
const GET_PRODUCTS_WITH_VARIANTS = gql`
  query getProductsWithVariants($first: Int!, $after: String) {
    products(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          variants(first: 10) {
            edges {
              node {
                id
                title
              }
            }
          }
        }
      }
    }
  }
`;

// メタフィールドを設定するGraphQLクエリ
const SET_METAFIELD = gql`
  mutation metafieldSet($metafield: MetafieldInput!) {
    metafieldSet(metafield: $metafield) {
      metafield {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * variant_mappingメタフィールド定義を作成する
 */
async function createVariantMappingMetafieldDefinition() {
  try {
    console.log('variant_mappingメタフィールド定義を作成中...');

    // メタフィールド定義
    const definition = {
      name: 'バリエーションマッピング',
      namespace: 'rental',
      key: 'variant_mapping',
      description: '日数とバリエーションIDのマッピング',
      type: 'json',
      ownerType: 'PRODUCT',
      visibleToStorefrontApi: true
    };

    // メタフィールド定義を作成
    const result = await client.request(CREATE_METAFIELD_DEFINITION, {
      definition
    });

    if (result.metafieldDefinitionCreate.userErrors.length > 0) {
      console.error(`エラー: ${JSON.stringify(result.metafieldDefinitionCreate.userErrors, null, 2)}`);
      return false;
    }

    console.log('variant_mappingメタフィールド定義を作成しました');
    return true;
  } catch (error) {
    console.error(`メタフィールド定義の作成中にエラーが発生しました: ${error.message}`);
    return false;
  }
}

/**
 * 商品のバリエーションマッピングを設定する
 * @param {string} productId 商品ID
 * @param {Array} variants バリエーションの配列
 */
async function setVariantMapping(productId, variants) {
  try {
    // バリエーションのタイトルからマッピングを作成
    const mapping = {};
    
    for (const variant of variants) {
      const title = variant.title.toLowerCase();
      
      if (title.includes('1日') || title.includes('1day')) {
        mapping['1day'] = variant.id;
      } else if (title.includes('2日') || title.includes('2day')) {
        mapping['2day'] = variant.id;
      } else if (title.includes('3日') || title.includes('3day')) {
        mapping['3day'] = variant.id;
      } else if (title.includes('4日') || title.includes('4day')) {
        mapping['4day'] = variant.id;
      } else if (title.includes('5日') || title.includes('5day')) {
        mapping['5day'] = variant.id;
      } else if (title.includes('6日') || title.includes('6day')) {
        mapping['6day'] = variant.id;
      } else if (title.includes('7日') || title.includes('7day')) {
        mapping['7day'] = variant.id;
      } else if (title.includes('8日以上') || title.includes('8plus') || title.includes('8day')) {
        mapping['8plus'] = variant.id;
      }
    }

    // マッピングが空の場合はスキップ
    if (Object.keys(mapping).length === 0) {
      console.log(`商品 ${productId} にはレンタル日数バリエーションがありません`);
      return;
    }

    // variant_mappingメタフィールドを設定
    const result = await client.request(SET_METAFIELD, {
      metafield: {
        ownerId: productId,
        namespace: 'rental',
        key: 'variant_mapping',
        value: JSON.stringify(mapping),
        type: 'json'
      }
    });

    if (result.metafieldSet.userErrors.length > 0) {
      console.error(`エラー: ${JSON.stringify(result.metafieldSet.userErrors, null, 2)}`);
      return;
    }

    console.log(`商品 ${productId} のvariant_mappingメタフィールドを設定しました`);
  } catch (error) {
    console.error(`variant_mappingメタフィールドの設定中にエラーが発生しました: ${error.message}`);
  }
}

/**
 * 全ての商品のバリエーションマッピングを設定する
 */
async function setAllVariantMappings() {
  let hasNextPage = true;
  let cursor = null;
  let count = 0;

  console.log('全ての商品のバリエーションマッピングを設定します...');

  while (hasNextPage) {
    try {
      // 商品とそのバリエーションを取得
      const result = await client.request(GET_PRODUCTS_WITH_VARIANTS, {
        first: 10,
        after: cursor
      });

      const products = result.products.edges;
      hasNextPage = result.products.pageInfo.hasNextPage;
      cursor = result.products.pageInfo.endCursor;

      // 各商品のバリエーションマッピングを設定
      for (const product of products) {
        const productId = product.node.id;
        const variants = product.node.variants.edges.map(edge => ({
          id: edge.node.id,
          title: edge.node.title
        }));

        await setVariantMapping(productId, variants);
        count++;

        // API制限を回避するための短い待機
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      console.log(`${count}件の商品を処理しました`);
    } catch (error) {
      console.error(`商品の取得中にエラーが発生しました: ${error.message}`);
      hasNextPage = false;
    }
  }

  console.log(`合計${count}件の商品のバリエーションマッピングを設定しました`);
}

// スクリプトの実行
async function main() {
  // variant_mappingメタフィールド定義を作成
  const success = await createVariantMappingMetafieldDefinition();
  
  if (success) {
    // 全ての商品のバリエーションマッピングを設定
    await setAllVariantMappings();
  }
}

main().catch(error => {
  console.error(`スクリプトの実行中にエラーが発生しました: ${error.message}`);
});
