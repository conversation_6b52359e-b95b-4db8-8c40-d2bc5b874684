/**
 * すべての商品の在庫数を1に設定するスクリプト（更新版）
 *
 * このスクリプトは、Shopifyの商品在庫数を1に設定します。
 * メンテナンス中や廃棄予定の商品は除外します。
 * 
 * 2025-01 APIに対応し、正しいロケーションIDを使用します。
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

dotenv.config();

// Shopify API設定
const config = {
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block',
  apiSecretKey: process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
};

// 設定の検証
if (!config.apiSecretKey) {
  console.error('エラー: SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません。');
  process.exit(1);
}

// ショップ名を正規化
const shopName = config.shop.replace('.myshopify.com', '');

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${shopName}.myshopify.com/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': config.apiSecretKey,
      'Content-Type': 'application/json',
    },
  }
);

// 利用可能なロケーションを取得するクエリ
const GET_LOCATIONS = gql`
  query GetLocations {
    locations(first: 10) {
      edges {
        node {
          id
          name
          isActive
        }
      }
    }
  }
`;

// 商品を取得するクエリ
const GET_PRODUCTS = gql`
  query getProducts($first: Int!, $after: String) {
    products(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          status
          variants(first: 20) {
            edges {
              node {
                id
                title
                sku
                inventoryItem {
                  id
                  inventoryLevels(first: 10) {
                    edges {
                      node {
                        id
                        location {
                          id
                          name
                        }
                        quantities(names: ["available"]) {
                          name
                          quantity
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
              }
            }
          }
        }
      }
    }
  }
`;

// 在庫を設定するミューテーション
const SET_INVENTORY = gql`
  mutation inventorySetQuantities($input: InventorySetQuantitiesInput!) {
    inventorySetQuantities(input: $input) {
      inventoryAdjustmentGroup {
        reason
        changes {
          name
          delta
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールドから値を取得する関数
function getMetafieldValue(metafields, namespace, key) {
  const metafield = metafields.find(
    m => m.node.namespace === namespace && m.node.key === key
  );

  if (!metafield) return null;

  try {
    // JSON形式の場合はパースする
    return JSON.parse(metafield.node.value);
  } catch (e) {
    // 通常の文字列の場合はそのまま返す
    return metafield.node.value;
  }
}

// 商品の在庫を更新する関数
async function updateProductInventory(product, locationId) {
  try {
    const metafields = product.metafields.edges;
    const variants = product.variants.edges.map(edge => edge.node);

    // メタフィールドからステータスを取得（customネームスペースを使用）
    const statusMetafield = getMetafieldValue(metafields, 'custom', 'status');

    // メンテナンス中や廃棄予定の商品は除外
    if (statusMetafield === 'maintenance' || statusMetafield === 'damaged' || statusMetafield === 'unavailable') {
      console.log(`商品「${product.title}」はステータスが ${statusMetafield} のため、在庫を更新しません。`);
      return { success: true, skipped: true };
    }

    // 在庫数を1に設定
    const targetInventory = 1;
    let updatedCount = 0;
    let errorCount = 0;

    console.log(`商品「${product.title}」の在庫を${targetInventory}に設定します...`);

    // 各バリアントの在庫を更新
    for (const variant of variants) {
      const inventoryLevels = variant.inventoryItem?.inventoryLevels?.edges || [];
      
      for (const { node: level } of inventoryLevels) {
        const currentQuantity = level.quantities.find(q => q.name === "available")?.quantity || 0;
        
        if (currentQuantity !== targetInventory) {
          try {
            const result = await client.request(SET_INVENTORY, {
              input: {
                reason: "correction",
                name: "available",
                ignoreCompareQuantity: true,
                quantities: [
                  {
                    inventoryItemId: variant.inventoryItem.id,
                    locationId: level.location.id,
                    quantity: targetInventory
                  }
                ]
              }
            });

            if (result.inventorySetQuantities?.userErrors?.length > 0) {
              console.error(`  ❌ エラー (${variant.title}):`, result.inventorySetQuantities.userErrors);
              errorCount++;
            } else {
              console.log(`  ✓ ${variant.title} の在庫を ${currentQuantity} → ${targetInventory} に更新 (${level.location.name})`);
              updatedCount++;
            }
          } catch (error) {
            console.error(`  ❌ 更新エラー (${variant.title}):`, error.message);
            errorCount++;
          }
        } else {
          console.log(`  ✓ ${variant.title} の在庫は既に ${targetInventory} です (${level.location.name})`);
        }
      }
    }

    return { 
      success: errorCount === 0, 
      updatedCount, 
      errorCount,
      skipped: false 
    };
  } catch (error) {
    console.error(`商品「${product.title}」の在庫更新中にエラーが発生しました:`, error);
    return { success: false, errorCount: 1, skipped: false };
  }
}

// すべての商品の在庫を更新する関数
async function updateAllProductsInventory() {
  try {
    console.log('=== 在庫一括設定スクリプト開始 ===\n');

    // 1. 利用可能なロケーションを取得
    console.log('1. ロケーション情報を取得中...');
    const locationsResult = await client.request(GET_LOCATIONS);
    const locations = locationsResult.locations.edges
      .map(edge => edge.node)
      .filter(loc => loc.isActive);
    
    console.log(`✓ ${locations.length}個のアクティブなロケーションを取得:`);
    locations.forEach(loc => {
      console.log(`  - ${loc.name} (ID: ${loc.id})`);
    });
    console.log('');

    // デフォルトロケーションを選択（最初のアクティブなロケーション）
    const defaultLocation = locations[0];
    if (!defaultLocation) {
      console.error('エラー: アクティブなロケーションが見つかりません');
      return;
    }

    // 2. 商品データを取得
    console.log('2. 商品データを取得中...');
    let hasNextPage = true;
    let cursor = null;
    let allProducts = [];

    // ページネーションを使用してすべての商品を取得
    while (hasNextPage) {
      try {
        const result = await client.request(GET_PRODUCTS, {
          first: 50,
          after: cursor
        });

        const products = result.products.edges.map(edge => edge.node);
        allProducts = allProducts.concat(products);

        hasNextPage = result.products.pageInfo.hasNextPage;
        cursor = result.products.pageInfo.endCursor;

        console.log(`  ${products.length}件の商品を取得... (合計: ${allProducts.length}件)`);

        if (hasNextPage) {
          // APIレート制限対策
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } catch (error) {
        console.error('商品データの取得中にエラーが発生しました:', error);
        hasNextPage = false;
      }
    }

    console.log(`✓ 合計${allProducts.length}件の商品を取得しました\n`);

    // 3. 各商品の在庫を更新
    console.log('3. 在庫の更新を開始...\n');
    let successCount = 0;
    let errorCount = 0;
    let skippedCount = 0;
    let totalUpdated = 0;

    for (const product of allProducts) {
      console.log(`\n商品: ${product.title}`);

      try {
        const result = await updateProductInventory(product, defaultLocation.id);

        if (result.skipped) {
          skippedCount++;
        } else if (result.success) {
          successCount++;
          totalUpdated += result.updatedCount;
        } else {
          errorCount++;
        }
      } catch (error) {
        console.error(`商品「${product.title}」の処理中にエラーが発生しました:`, error);
        errorCount++;
      }

      // APIレート制限対策
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // 4. 結果サマリー
    console.log('\n=== 在庫更新結果サマリー ===');
    console.log(`総商品数: ${allProducts.length}`);
    console.log(`更新成功: ${successCount}`);
    console.log(`スキップ: ${skippedCount} (メンテナンス等)`);
    console.log(`エラー: ${errorCount}`);
    console.log(`更新されたバリアント数: ${totalUpdated}`);
    console.log('\n✓ 在庫更新が完了しました');
  } catch (error) {
    console.error('エラーが発生しました:', error);
  }
}

// スクリプト実行
updateAllProductsInventory().catch((error) => {
  console.error('実行エラー:', error);
  process.exit(1);
});