/**
 * エラーハンドリングユーティリティ
 *
 * このモジュールは、エラーハンドリングのための関数を提供します。
 */

/**
 * エラータイプの列挙型
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  API = 'API',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  TIMEOUT = 'TIMEOUT',
  RATE_LIMIT = 'RATE_LIMIT',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN'
}

/**
 * エラー情報のインターフェース
 */
export interface ErrorInfo {
  type: ErrorType;
  message: string;
  code?: string;
  details?: Record<string, any>;
  originalError?: Error;
  timestamp: Date;
  retryable: boolean;
}

/**
 * リトライ可能な関数を実行する
 * @param fn 実行する関数
 * @param retries リトライ回数
 * @param delay 遅延時間（ミリ秒）
 * @param backoff バックオフ係数
 * @param shouldRetry リトライするかどうかを判断する関数
 * @returns 関数の実行結果
 */
export async function retry<T>(
  fn: () => Promise<T>,
  retries: number = 3,
  delay: number = 1000,
  backoff: number = 2,
  shouldRetry?: (error: any, attempt: number) => boolean | Promise<boolean>
): Promise<T> {
  let attempt = 1;

  async function attemptFn(): Promise<T> {
    try {
      return await fn();
    } catch (error) {
      // リトライ回数を超えた場合はエラーをスロー
      if (retries <= 0) {
        throw error;
      }

      // リトライするかどうかを判断
      if (shouldRetry) {
        const shouldRetryResult = await shouldRetry(error, attempt);
        if (!shouldRetryResult) {
          console.log(`リトライをスキップします... エラー:`, error instanceof Error ? error.message : String(error));
          throw error;
        }
      }

      console.log(`リトライします... 試行 ${attempt}/${attempt + retries}, 残り ${retries} 回`);
      console.log(`エラー: ${error instanceof Error ? error.message : String(error)}`);

      // 詳細なエラーログを出力
      if (error instanceof Error && error.stack) {
        console.log(`スタックトレース: ${error.stack}`);
      }

      // 遅延を入れる
      await new Promise(resolve => setTimeout(resolve, delay));

      // リトライ回数を減らし、遅延時間を増やす
      retries--;
      delay *= backoff;
      attempt++;

      // 再帰的に関数を呼び出す
      return attemptFn();
    }
  }

  return attemptFn();
}

/**
 * エラーの種類を判定する
 * @param error エラーオブジェクト
 * @returns エラータイプ
 */
export function classifyError(error: any): ErrorType {
  if (!error) {
    return ErrorType.UNKNOWN;
  }

  // ネットワークエラー
  if (error.name === 'NetworkError' || error.message?.includes('network') || error.message?.includes('connection')) {
    return ErrorType.NETWORK;
  }

  // APIエラー
  if (error.response) {
    const status = error.response.status;

    if (status === 401) {
      return ErrorType.AUTHENTICATION;
    } else if (status === 403) {
      return ErrorType.AUTHORIZATION;
    } else if (status === 404) {
      return ErrorType.NOT_FOUND;
    } else if (status === 409) {
      return ErrorType.CONFLICT;
    } else if (status === 422) {
      return ErrorType.VALIDATION;
    } else if (status === 429) {
      return ErrorType.RATE_LIMIT;
    } else if (status >= 500) {
      return ErrorType.SERVER;
    }

    return ErrorType.API;
  }

  // GraphQLエラー
  if (error.graphQLErrors && error.graphQLErrors.length > 0) {
    const graphQLError = error.graphQLErrors[0];

    if (graphQLError.extensions?.code === 'UNAUTHENTICATED') {
      return ErrorType.AUTHENTICATION;
    } else if (graphQLError.extensions?.code === 'FORBIDDEN') {
      return ErrorType.AUTHORIZATION;
    } else if (graphQLError.extensions?.code === 'NOT_FOUND') {
      return ErrorType.NOT_FOUND;
    } else if (graphQLError.extensions?.code === 'VALIDATION_FAILED') {
      return ErrorType.VALIDATION;
    } else if (graphQLError.extensions?.code === 'THROTTLED') {
      return ErrorType.RATE_LIMIT;
    } else if (graphQLError.extensions?.code === 'INTERNAL_SERVER_ERROR') {
      return ErrorType.SERVER;
    }

    return ErrorType.API;
  }

  // タイムアウトエラー
  if (error.name === 'TimeoutError' || error.message?.includes('timeout')) {
    return ErrorType.TIMEOUT;
  }

  // その他のエラー
  return ErrorType.UNKNOWN;
}

/**
 * エラーがリトライ可能かどうかを判定する
 * @param errorType エラータイプ
 * @returns リトライ可能かどうか
 */
export function isRetryableError(errorType: ErrorType): boolean {
  switch (errorType) {
    case ErrorType.NETWORK:
    case ErrorType.TIMEOUT:
    case ErrorType.RATE_LIMIT:
    case ErrorType.SERVER:
      return true;
    default:
      return false;
  }
}

/**
 * ユーザーフレンドリーなエラーメッセージを生成する
 * @param errorInfo エラー情報
 * @returns ユーザーフレンドリーなエラーメッセージ
 */
export function getUserFriendlyMessage(errorInfo: ErrorInfo): string {
  switch (errorInfo.type) {
    case ErrorType.NETWORK:
      return 'ネットワーク接続に問題があります。インターネット接続を確認してください。';
    case ErrorType.AUTHENTICATION:
      return '認証に失敗しました。再度ログインしてください。';
    case ErrorType.AUTHORIZATION:
      return 'この操作を実行する権限がありません。';
    case ErrorType.NOT_FOUND:
      return '要求されたリソースが見つかりませんでした。';
    case ErrorType.VALIDATION:
      return '入力データが無効です。入力内容を確認してください。';
    case ErrorType.CONFLICT:
      return 'データの競合が発生しました。最新の情報を取得してから再試行してください。';
    case ErrorType.TIMEOUT:
      return 'リクエストがタイムアウトしました。後でもう一度お試しください。';
    case ErrorType.RATE_LIMIT:
      return 'リクエスト制限に達しました。しばらく待ってから再試行してください。';
    case ErrorType.SERVER:
      return 'サーバーエラーが発生しました。しばらく待ってから再試行してください。';
    case ErrorType.UNKNOWN:
    default:
      return `予期しないエラーが発生しました: ${errorInfo.message}`;
  }
}

/**
 * エラー情報を詳細に記録する
 * @param error エラーオブジェクト
 * @param context コンテキスト情報
 * @returns エラー情報
 */
export function logDetailedError(error: any, context: Record<string, any> = {}): ErrorInfo {
  const timestamp = new Date();
  const errorType = classifyError(error);
  const errorMessage = error instanceof Error ? error.message : String(error);
  const errorStack = error instanceof Error ? error.stack : undefined;

  // GraphQLエラーの場合、より詳細な情報を抽出
  let graphqlErrors = null;
  if (error.response && error.response.errors) {
    graphqlErrors = error.response.errors;
  } else if (error.errors) {
    graphqlErrors = error.errors;
  } else if (error.data && error.data.userErrors) {
    graphqlErrors = error.data.userErrors;
  }

  // 詳細情報を構築
  const details: Record<string, any> = {
    ...context,
    graphqlErrors
  };

  // エラー情報を構築
  const errorInfo: ErrorInfo = {
    type: errorType,
    message: errorMessage,
    details,
    originalError: error instanceof Error ? error : undefined,
    timestamp,
    retryable: isRetryableError(errorType)
  };

  // コンソールに詳細情報を出力
  console.error('詳細エラー情報:', JSON.stringify({
    type: errorInfo.type,
    message: errorInfo.message,
    timestamp: errorInfo.timestamp.toISOString(),
    retryable: errorInfo.retryable,
    details: errorInfo.details,
    stack: errorStack
  }, null, 2));

  // エラーの種類に応じた詳細なログ
  if (graphqlErrors) {
    console.error('GraphQLエラーの詳細:');
    if (Array.isArray(graphqlErrors)) {
      graphqlErrors.forEach((err: any, index: number) => {
        console.error(`エラー ${index + 1}:`, err);
        if (err.extensions) {
          console.error('拡張情報:', err.extensions);
        }
      });
    } else {
      console.error('GraphQLエラー:', graphqlErrors);
    }
  }

  // レスポンスの詳細情報
  if (error.response) {
    try {
      console.error('レスポンス情報:');
      console.error('- ステータス:', error.response.status);
      console.error('- ステータステキスト:', error.response.statusText);

      if (error.response.headers) {
        console.error('- ヘッダー:', error.response.headers);
      }

      if (error.response.data) {
        console.error('- データ:', JSON.stringify(error.response.data, null, 2));
      }
    } catch (logError) {
      console.error('レスポンス情報のログ出力中にエラーが発生しました:', logError);
    }
  }

  return errorInfo;
}

/**
 * エラー通知を送信する（モック実装）
 * @param errorInfo エラー情報
 */
export function sendErrorNotification(errorInfo: ErrorInfo): void {
  // 実際の実装では、Slack、メール、またはその他の通知サービスにエラーを送信する
  // ここではモック実装として、コンソールにメッセージを出力するだけ
  console.log(`[通知] エラーが発生しました: ${errorInfo.message}`);
  console.log(`エラータイプ: ${errorInfo.type}`);
  console.log(`タイムスタンプ: ${errorInfo.timestamp.toISOString()}`);
  console.log(`詳細: ${JSON.stringify(errorInfo.details, null, 2)}`);
}

/**
 * エラーをハンドリングする
 * @param error エラーオブジェクト
 * @param context コンテキスト情報
 * @param notify 通知を送信するかどうか
 * @returns エラー情報
 */
export function handleError(error: any, context: Record<string, any> = {}, notify: boolean = false): ErrorInfo {
  // 詳細なエラー情報を記録
  const errorInfo = logDetailedError(error, context);

  // 必要に応じて通知を送信
  if (notify) {
    sendErrorNotification(errorInfo);
  }

  return errorInfo;
}

/**
 * ユーザーフレンドリーなエラーメッセージを表示する
 * @param error エラーオブジェクト
 * @param context コンテキスト情報
 * @returns ユーザーフレンドリーなエラーメッセージ
 */
export function getFriendlyErrorMessage(error: any, context: Record<string, any> = {}): string {
  const errorInfo = handleError(error, context);
  return getUserFriendlyMessage(errorInfo);
}
