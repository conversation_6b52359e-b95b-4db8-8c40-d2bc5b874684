/**
 * バリエーション方式テスト環境セットアップスクリプト
 * 
 * このスクリプトでは、以下の処理を一括で実行します：
 * 1. 既存のテスト商品を削除
 * 2. 新しい仕様に沿ったテスト商品を追加
 * 3. 商品バリエーションを設定
 * 4. テスト用の予約データを作成
 */

import { execSync } from 'child_process';
import dotenv from 'dotenv';

// 環境変数を読み込む
dotenv.config();

/**
 * コマンドを実行して結果を表示
 * @param {string} command 実行するコマンド
 * @param {string} description コマンドの説明
 */
function runCommand(command, description) {
  console.log(`\n=== ${description} ===\n`);
  
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`\n✅ ${description}が完了しました`);
  } catch (error) {
    console.error(`\n❌ ${description}中にエラーが発生しました:`, error);
    process.exit(1);
  }
}

// メイン処理
async function main() {
  console.log("=== バリエーション方式テスト環境のセットアップを開始します ===\n");
  
  // 1. 既存のテスト商品を削除し、新しいテスト商品を作成
  runCommand('node --loader ts-node/esm scripts/test-products-setup.js', 'テスト商品のセットアップ');
  
  // 2. 商品バリエーションを設定
  runCommand('node --loader ts-node/esm scripts/setup-product-variants.js', '商品バリエーションの設定');
  
  // 3. テスト用の予約データを作成
  runCommand('node --loader ts-node/esm scripts/create-test-bookings.js', 'テスト用予約データの作成');
  
  console.log("\n=== バリエーション方式テスト環境のセットアップが完了しました ===");
  console.log("\n以下のテストが可能になりました：");
  console.log("- 日付選択に基づくバリエーション自動選択");
  console.log("- カートへの追加と予約情報の表示");
  console.log("- 仮予約と本予約の管理");
  console.log("- 長期レンタル（8日以上）の処理");
  console.log("- 予約カレンダーでの予約状況表示");
}

main();
