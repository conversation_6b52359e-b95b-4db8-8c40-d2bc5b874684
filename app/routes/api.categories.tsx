/**
 * カテゴリマスタAPI
 * 
 * カテゴリ一覧取得、SKU生成、検証機能を提供
 */

import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "~/shopify.server";
import { CategoryMasterService } from "~/services/category-master.service";

const categoryService = new CategoryMasterService();

export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  
  const url = new URL(request.url);
  const action = url.searchParams.get('action');
  const categoryCode = url.searchParams.get('categoryCode');
  const subCategoryCode = url.searchParams.get('subCategoryCode');
  const sku = url.searchParams.get('sku');
  
  try {
    switch (action) {
      case 'list':
        // カテゴリ一覧取得
        const categories = await categoryService.getActiveCategoriesByShop(shop);
        return json({ success: true, categories });
        
      case 'validate-sku':
        // SKU検証
        if (!sku) {
          return json({ success: false, error: 'SKUが指定されていません' }, { status: 400 });
        }
        
        const validation = await categoryService.validateSKU(shop, sku);
        return json({ success: true, validation });
        
      case 'generate-sku':
        // SKU生成
        if (!categoryCode || !subCategoryCode) {
          return json({ 
            success: false, 
            error: 'カテゴリコードとサブカテゴリコードが必要です' 
          }, { status: 400 });
        }
        
        const newSKU = await categoryService.generateNextSKU(shop, categoryCode, subCategoryCode);
        return json({ success: true, sku: newSKU });
        
      case 'parse-sku':
        // SKU解析
        if (!sku) {
          return json({ success: false, error: 'SKUが指定されていません' }, { status: 400 });
        }
        
        const skuStructure = categoryService.parseSKU(sku);
        if (!skuStructure) {
          return json({ success: false, error: 'SKU形式が正しくありません' }, { status: 400 });
        }
        
        // カテゴリ情報も取得
        const category = await categoryService.getCategoryByCode(shop, skuStructure.categoryCode);
        const subCategory = await categoryService.getSubCategoryByCode(
          shop, 
          skuStructure.categoryCode, 
          skuStructure.subCategoryCode
        );
        
        return json({ 
          success: true, 
          skuStructure,
          category,
          subCategory
        });
        
      default:
        // デフォルトはカテゴリ一覧
        const defaultCategories = await categoryService.getActiveCategoriesByShop(shop);
        return json({ success: true, categories: defaultCategories });
    }
    
  } catch (error) {
    console.error('カテゴリAPI エラー:', error);
    return json({ 
      success: false, 
      error: error instanceof Error ? error.message : '不明なエラーが発生しました' 
    }, { status: 500 });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  
  const formData = await request.formData();
  const action = formData.get('action') as string;
  
  try {
    switch (action) {
      case 'create-category':
        // カテゴリ作成
        const categoryData = {
          shop,
          code: formData.get('code') as string,
          name: formData.get('name') as string,
          parentCode: formData.get('parentCode') as string || undefined,
          level: parseInt(formData.get('level') as string) || 1,
          displayOrder: parseInt(formData.get('displayOrder') as string) || 0,
          description: formData.get('description') as string || undefined
        };
        
        if (!categoryData.code || !categoryData.name) {
          return json({ 
            success: false, 
            error: 'カテゴリコードと名前は必須です' 
          }, { status: 400 });
        }
        
        const newCategory = await categoryService.createCategory(categoryData);
        return json({ success: true, category: newCategory });
        
      case 'create-subcategory':
        // サブカテゴリ作成
        const subCategoryData = {
          shop,
          code: formData.get('code') as string,
          name: formData.get('name') as string,
          parentCategoryCode: formData.get('parentCategoryCode') as string,
          displayOrder: parseInt(formData.get('displayOrder') as string) || 0,
          description: formData.get('description') as string || undefined
        };
        
        if (!subCategoryData.code || !subCategoryData.name || !subCategoryData.parentCategoryCode) {
          return json({ 
            success: false, 
            error: 'サブカテゴリコード、名前、親カテゴリコードは必須です' 
          }, { status: 400 });
        }
        
        const newSubCategory = await categoryService.createSubCategory(subCategoryData);
        return json({ success: true, subCategory: newSubCategory });
        
      case 'update-category':
        // カテゴリ更新
        const updateCategoryCode = formData.get('code') as string;
        const updateData = {
          name: formData.get('name') as string,
          parentCode: formData.get('parentCode') as string || undefined,
          level: parseInt(formData.get('level') as string) || undefined,
          displayOrder: parseInt(formData.get('displayOrder') as string) || undefined,
          isActive: formData.get('isActive') === 'true',
          description: formData.get('description') as string || undefined
        };
        
        if (!updateCategoryCode) {
          return json({ 
            success: false, 
            error: 'カテゴリコードが必要です' 
          }, { status: 400 });
        }
        
        const updatedCategory = await categoryService.updateCategory(shop, updateCategoryCode, updateData);
        return json({ success: true, category: updatedCategory });
        
      case 'update-subcategory':
        // サブカテゴリ更新
        const updateSubCategoryCode = formData.get('code') as string;
        const updateParentCategoryCode = formData.get('parentCategoryCode') as string;
        const updateSubData = {
          name: formData.get('name') as string,
          displayOrder: parseInt(formData.get('displayOrder') as string) || undefined,
          isActive: formData.get('isActive') === 'true',
          description: formData.get('description') as string || undefined
        };
        
        if (!updateSubCategoryCode || !updateParentCategoryCode) {
          return json({ 
            success: false, 
            error: 'サブカテゴリコードと親カテゴリコードが必要です' 
          }, { status: 400 });
        }
        
        const updatedSubCategory = await categoryService.updateSubCategory(
          shop, 
          updateParentCategoryCode, 
          updateSubCategoryCode, 
          updateSubData
        );
        return json({ success: true, subCategory: updatedSubCategory });
        
      default:
        return json({ 
          success: false, 
          error: '不正なアクションです' 
        }, { status: 400 });
    }
    
  } catch (error) {
    console.error('カテゴリAPI アクションエラー:', error);
    return json({ 
      success: false, 
      error: error instanceof Error ? error.message : '不明なエラーが発生しました' 
    }, { status: 500 });
  }
}
