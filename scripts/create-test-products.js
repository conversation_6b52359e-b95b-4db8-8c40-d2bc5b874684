import fetch from 'node-fetch';
import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';

dotenv.config();
const prisma = new PrismaClient();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLリクエストを送信する関数
async function sendGraphQLRequest(query, variables = {}) {
  const url = `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`;

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify({
        query,
        variables
      })
    });

    const data = await response.json();

    if (data.errors) {
      console.error('GraphQLエラー:', JSON.stringify(data.errors, null, 2));
      return null;
    }

    return data.data;
  } catch (error) {
    console.error('リクエストエラー:', error);
    return null;
  }
}

// 商品を作成する関数
async function createProduct(title, variants, metafields = []) {
  const query = `
    mutation productCreate($input: ProductInput!) {
      productCreate(input: $input) {
        product {
          id
          title
          handle
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    input: {
      title,
      variants,
      tags: "テスト,レンタル商品",
      productType: "レンタル商品",
      vendor: "テスト",
      options: ["レンタル期間"],
      metafields
    }
  };

  const result = await sendGraphQLRequest(query, variables);

  if (result && result.productCreate && result.productCreate.product) {
    console.log(`商品 ${title} を作成しました。ID: ${result.productCreate.product.id}`);
    return result.productCreate.product;
  } else {
    console.error(`商品 ${title} の作成に失敗しました。`);
    if (result && result.productCreate && result.productCreate.userErrors) {
      console.error('ユーザーエラー:', JSON.stringify(result.productCreate.userErrors, null, 2));
    }
    console.error('レスポンス全体:', JSON.stringify(result, null, 2));
    return null;
  }
}

// メタフィールドを設定する関数
async function setMetafields(productId, metafields) {
  const query = `
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    metafields: metafields.map(metafield => ({
      ...metafield,
      ownerId: productId
    }))
  };

  const result = await sendGraphQLRequest(query, variables);

  if (result && result.metafieldsSet && result.metafieldsSet.metafields) {
    console.log(`商品 ${productId} のメタフィールドを設定しました。`);
    return true;
  } else {
    console.error(`商品 ${productId} のメタフィールド設定に失敗しました。`);
    return false;
  }
}

// 実際のSKUを使用したテスト商品データ
const REAL_TEST_PRODUCTS = [
  {
    sku: '212-05-023',
    title: '花器　シルバー　穴開きボトル型',
    basePrice: 1500,
    category: '花器',
    color: 'シルバー',
    material: 'メタル',
    size: { width: 14, height: 31, depth: 14 },
    status: 'available',
    location: 'NY'
  },
  {
    sku: '201-07-107',
    title: 'シルバートレー　オーバル　フリルフレーム　持ち手付',
    basePrice: 3000,
    category: 'キッチン',
    color: 'シルバー',
    material: 'シルバー',
    size: { width: 70, height: 0, depth: 50 },
    status: 'available',
    location: 'NY'
  },
  {
    sku: '201-05-797',
    title: 'キャンドルスタンド　シャインシルバー　3灯',
    basePrice: 2000,
    category: 'キャンドル',
    color: 'シルバー',
    material: 'メタル',
    size: { width: 31, height: 35, depth: 0 },
    status: 'available',
    location: 'NY'
  },
  {
    sku: '211-12-954',
    title: 'オブジェ　タイドロープ　ゴールド',
    basePrice: 1000,
    category: 'オブジェ',
    color: 'ゴールド',
    material: 'メタル',
    size: { width: 27, height: 14, depth: 0 },
    status: 'maintenance',
    location: 'NY'
  },
  {
    sku: '206-02-320',
    title: 'ジュエリートレー　ホワイト×クリア　フリル型　持ち手付',
    basePrice: 1200,
    category: 'コスメ',
    color: 'ホワイト',
    material: 'プラスチック',
    size: { width: 20, height: 17, depth: 0 },
    status: 'available',
    location: 'PR'
  }
];

// 価格計算関数
function calculateVariantPrice(basePrice, days) {
  if (days === 1) return basePrice;
  if (days >= 2 && days <= 7) {
    return basePrice + Math.floor(basePrice * 0.2 * (days - 1));
  }
  if (days >= 8) {
    return Math.floor(basePrice * 0.1); // 8日以上は1日あたりの価格
  }
  return basePrice;
}

// バリアント生成関数
function generateVariants(productData) {
  const variants = [];
  const { sku, basePrice } = productData;

  // 1-7日レンタルバリアント
  for (let days = 1; days <= 7; days++) {
    variants.push({
      price: calculateVariantPrice(basePrice, days),
      title: `${days}日レンタル`,
      sku: `${sku}-${days}D`,
      option1: `${days}日レンタル`,
      inventoryQuantity: 1,
      inventoryManagement: 'SHOPIFY'
    });
  }

  // 8日以上レンタルバリアント
  variants.push({
    price: calculateVariantPrice(basePrice, 8),
    title: '8日以上レンタル',
    sku: `${sku}-8D+`,
    option1: '8日以上レンタル',
    inventoryQuantity: 1,
    inventoryManagement: 'SHOPIFY'
  });

  // 仮予約バリアント
  variants.push({
    price: Math.floor(basePrice * 0.1),
    title: '仮予約',
    sku: `${sku}-PROV`,
    option1: '仮予約',
    inventoryQuantity: 1,
    inventoryManagement: 'SHOPIFY'
  });

  return variants;
}

// Prismaに商品を保存
async function saveProductToPrisma(productData, shopifyProduct) {
  const shop = 'ease-next-temp.myshopify.com';

  try {
    const product = await prisma.product.create({
      data: {
        shop,
        sku: productData.sku,
        title: productData.title,
        handle: shopifyProduct.handle,
        shopifyId: shopifyProduct.id,
        price: productData.basePrice,
        status: productData.status === 'maintenance' ? 'MAINTENANCE' : 'AVAILABLE',
        inventory: productData.status === 'available' ? 1 : 0,
        basicInfo: {
          category: productData.category,
          basePrice: productData.basePrice,
          color: productData.color,
          material: productData.material,
          size: productData.size,
          location: productData.location,
          status: productData.status
        }
      }
    });

    console.log(`✅ Prismaに商品保存: ${productData.sku}`);
    return product;
  } catch (error) {
    console.error(`❌ Prisma保存エラー (${productData.sku}):`, error.message);
    return null;
  }
}

// テスト用商品を作成する関数
async function createTestProducts() {
  console.log('🏗️ === 実際のSKUを使用したテスト商品作成 ===');

  for (const productData of REAL_TEST_PRODUCTS) {
    console.log(`\n📦 商品作成中: ${productData.sku} - ${productData.title}`);

    // バリアント生成
    const variants = generateVariants(productData);
    console.log(`  バリアント数: ${variants.length}個`);

    // Shopifyに商品作成
    const shopifyProduct = await createProduct(
      productData.title,
      variants
    );

    if (shopifyProduct) {
      // 基本情報メタフィールド
      const basicInfo = {
        category: productData.category,
        color: productData.color,
        material: productData.material,
        size: productData.size,
        location: productData.location,
        status: productData.status,
        basePrice: productData.basePrice
      };

      // 料金設定メタフィールド
      const pricing = {
        basePrice: productData.basePrice,
        depositRate: 0.1,
        discountRules: {
          day2_7_rate: 0.2,
          day8_plus_rate: 0.1
        },
        provisionalRate: 0.1,
        minimumDays: 1,
        maximumDays: 30
      };

      // 在庫アイテム情報メタフィールド
      const inventoryItems = [
        {
          id: `item-${productData.sku}-1`,
          sku: `${productData.sku}-1`,
          status: productData.status,
          location: productData.location,
          notes: "テスト用商品"
        }
      ];

      // メタフィールドを設定
      await setMetafields(shopifyProduct.id, [
        {
          namespace: "rental",
          key: "basic_info",
          value: JSON.stringify(basicInfo),
          type: "json"
        },
        {
          namespace: "rental",
          key: "pricing",
          value: JSON.stringify(pricing),
          type: "json"
        },
        {
          namespace: "rental",
          key: "inventory_items",
          value: JSON.stringify(inventoryItems),
          type: "json"
        }
      ]);

      // Prismaに商品を保存
      await saveProductToPrisma(productData, shopifyProduct);
    }
  }

  await prisma.$disconnect();
  console.log("\n🎉 テスト商品の作成が完了しました。");
}

// 実行
createTestProducts();
