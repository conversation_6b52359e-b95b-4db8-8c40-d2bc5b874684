/**
 * 実際のフローに近いテストデータを作成するスクリプト
 * 
 * このスクリプトは、Shopify APIを使用して顧客データを作成し、
 * それに紐づく予約データをPrismaデータベースに作成します。
 * 
 * 使用方法:
 * npm run tsx scripts/create-realistic-test-data.ts
 */

import { PrismaClient, ProductStatus, BookingStatus, BookingType, PaymentMethod, PaymentStatus } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// ショップドメイン
const SHOP = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';

// 現在の日付
const NOW = new Date();

// 日付ユーティリティ関数
const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

// 顧客作成のGraphQLミューテーション
const CREATE_CUSTOMER = gql`
  mutation customerCreate($input: CustomerInput!) {
    customerCreate(input: $input) {
      customer {
        id
        firstName
        lastName
        email
        phone
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// テスト顧客データ
const testCustomers = [
  {
    firstName: '山田',
    lastName: '太郎',
    email: `yamada.taro.${uuidv4().substring(0, 8)}@example.com`,
    phone: '090-1234-5678',
    addresses: [
      {
        address1: '東京都渋谷区神宮前1-1-1',
        city: '渋谷区',
        province: '東京都',
        zip: '150-0001',
        country: 'Japan',
        firstName: '山田',
        lastName: '太郎',
      }
    ]
  },
  {
    firstName: '佐藤',
    lastName: '花子',
    email: `sato.hanako.${uuidv4().substring(0, 8)}@example.com`,
    phone: '090-2345-6789',
    addresses: [
      {
        address1: '大阪府大阪市北区梅田2-2-2',
        city: '大阪市北区',
        province: '大阪府',
        zip: '530-0001',
        country: 'Japan',
        firstName: '佐藤',
        lastName: '花子',
      }
    ]
  },
  {
    firstName: '鈴木',
    lastName: '一郎',
    email: `suzuki.ichiro.${uuidv4().substring(0, 8)}@example.com`,
    phone: '090-3456-7890',
    addresses: [
      {
        address1: '福岡県福岡市博多区博多駅前3-3-3',
        city: '福岡市博多区',
        province: '福岡県',
        zip: '812-0011',
        country: 'Japan',
        firstName: '鈴木',
        lastName: '一郎',
      }
    ]
  }
];

/**
 * Shopifyに顧客を作成する
 */
async function createShopifyCustomers() {
  console.log('Shopifyに顧客データを作成中...');
  const createdCustomers = [];

  for (const customer of testCustomers) {
    try {
      console.log(`顧客を作成中: ${customer.firstName} ${customer.lastName} (${customer.email})`);
      
      const response = await client.request(CREATE_CUSTOMER, {
        input: customer
      });

      if (response.customerCreate.userErrors.length > 0) {
        console.error('顧客作成エラー:', response.customerCreate.userErrors);
        continue;
      }

      const createdCustomer = response.customerCreate.customer;
      console.log(`顧客を作成しました: ID=${createdCustomer.id}`);
      createdCustomers.push(createdCustomer);

      // API制限を考慮して少し待機
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error('顧客作成中にエラーが発生しました:', error);
    }
  }

  return createdCustomers;
}

/**
 * Prismaに顧客データを作成する
 */
async function createPrismaCustomers(shopifyCustomers: any[]) {
  console.log('Prismaに顧客データを作成中...');
  const createdCustomers = [];

  for (const customer of shopifyCustomers) {
    try {
      const shopifyId = customer.id.replace('gid://shopify/Customer/', '');
      const name = `${customer.firstName} ${customer.lastName}`;
      
      const prismaCustomer = await prisma.customer.create({
        data: {
          shop: SHOP,
          shopifyId,
          name,
          email: customer.email,
          phone: customer.phone,
          createdAt: NOW,
          updatedAt: NOW,
        }
      });

      console.log(`Prismaに顧客を作成しました: ID=${prismaCustomer.id}`);
      createdCustomers.push(prismaCustomer);
    } catch (error) {
      console.error('Prisma顧客作成中にエラーが発生しました:', error);
    }
  }

  return createdCustomers;
}

/**
 * 商品データを取得する
 */
async function getProducts() {
  console.log('商品データを取得中...');
  
  const products = await prisma.product.findMany({
    where: {
      shop: SHOP,
      status: ProductStatus.AVAILABLE,
    },
    take: 5, // 最大5件取得
  });

  if (products.length === 0) {
    console.log('商品データが見つかりません。テスト商品を作成します...');
    
    // テスト商品を作成
    const testProducts = [
      {
        title: "テスト商品A",
        description: "テスト商品A説明",
        price: 10000,
        sku: "TEST-A-001",
        shopifyId: "gid://shopify/Product/1001",
        shop: SHOP,
        status: ProductStatus.AVAILABLE,
      },
      {
        title: "テスト商品B",
        description: "テスト商品B説明",
        price: 15000,
        sku: "TEST-B-001",
        shopifyId: "gid://shopify/Product/1002",
        shop: SHOP,
        status: ProductStatus.AVAILABLE,
      },
      {
        title: "テスト商品C",
        description: "テスト商品C説明",
        price: 20000,
        sku: "TEST-C-001",
        shopifyId: "gid://shopify/Product/1003",
        shop: SHOP,
        status: ProductStatus.AVAILABLE,
      },
    ];

    const createdProducts = [];
    for (const product of testProducts) {
      const createdProduct = await prisma.product.create({
        data: product
      });
      createdProducts.push(createdProduct);
    }

    return createdProducts;
  }

  return products;
}

/**
 * 予約データを作成する
 */
async function createBookings(products: any[], customers: any[]) {
  console.log('予約データを作成中...');
  const bookings = [];

  // 各顧客に対して予約を作成
  for (let i = 0; i < customers.length; i++) {
    const customer = customers[i];
    const product = products[i % products.length]; // 商品を循環させる
    
    // 確定予約（将来の日付）
    const confirmedBooking = await prisma.booking.create({
      data: {
        productId: product.id,
        startDate: addDays(NOW, 7 + i * 2),
        endDate: addDays(NOW, 10 + i * 2),
        customerId: customer.shopifyId, // Shopify顧客ID
        customerEmail: customer.email,
        customerName: customer.name,
        customerPhone: customer.phone || '',
        totalAmount: product.price,
        depositAmount: product.price * 0.1,
        depositPaid: true,
        bookingId: `BK-${uuidv4().substring(0, 8)}`,
        shop: SHOP,
        bookingType: BookingType.CONFIRMED,
        status: BookingStatus.CONFIRMED,
        paymentStatus: PaymentStatus.COMPLETED,
        paymentMethod: PaymentMethod.CREDIT_CARD,
        paymentDate: NOW,
        notes: `${customer.name}様の確定予約`,
        orderId: `gid://shopify/Order/${1000000 + i}`,
        orderName: `#${1001 + i}`,
      }
    });
    
    bookings.push(confirmedBooking);
    
    // 仮予約（将来の日付）
    const provisionalBooking = await prisma.booking.create({
      data: {
        productId: products[(i + 1) % products.length].id,
        startDate: addDays(NOW, 14 + i * 3),
        endDate: addDays(NOW, 16 + i * 3),
        customerId: customer.shopifyId, // Shopify顧客ID
        customerEmail: customer.email,
        customerName: customer.name,
        customerPhone: customer.phone || '',
        totalAmount: products[(i + 1) % products.length].price,
        depositAmount: products[(i + 1) % products.length].price * 0.1,
        depositPaid: false,
        bookingId: `BK-${uuidv4().substring(0, 8)}`,
        shop: SHOP,
        bookingType: BookingType.PROVISIONAL,
        status: BookingStatus.PROVISIONAL,
        paymentStatus: PaymentStatus.PENDING,
        paymentMethod: null,
        notes: `${customer.name}様の仮予約`,
      }
    });
    
    bookings.push(provisionalBooking);
  }

  return bookings;
}

/**
 * メインの実行関数
 */
async function main() {
  try {
    console.log('実際のフローに近いテストデータの作成を開始します...');
    
    // 1. Shopifyに顧客を作成
    const shopifyCustomers = await createShopifyCustomers();
    
    // 2. Prismaに顧客データを作成
    const prismaCustomers = await createPrismaCustomers(shopifyCustomers);
    
    // 3. 商品データを取得または作成
    const products = await getProducts();
    
    // 4. 予約データを作成
    const bookings = await createBookings(products, prismaCustomers);
    
    console.log('\n=== テストデータ作成結果 ===');
    console.log(`Shopify顧客: ${shopifyCustomers.length}件`);
    console.log(`Prisma顧客: ${prismaCustomers.length}件`);
    console.log(`商品: ${products.length}件`);
    console.log(`予約: ${bookings.length}件`);
    console.log('=============================\n');
    
    console.log('テストデータの作成が完了しました');
  } catch (error) {
    console.error('テストデータ作成中にエラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('予期しないエラーが発生しました:', error);
    process.exit(1);
  });
