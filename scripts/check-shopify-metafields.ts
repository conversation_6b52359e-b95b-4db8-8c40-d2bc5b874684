/**
 * Shopifyメタフィールド確認スクリプト
 *
 * このスクリプトは、Shopifyの商品メタフィールドを確認します。
 * 実行方法: npx tsx scripts/check-shopify-metafields.ts [商品ID]
 */

import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';
import { GraphQLClient } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト設定
const config = {
  // テストする商品のShopify ID
  productId: process.argv[2] || '', // コマンドライン引数から取得

  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com'
};

/**
 * 商品情報を取得する関数
 */
async function getProductInfo(productId: string) {
  try {
    // Prismaから商品情報を取得
    const product = await prisma.product.findFirst({
      where: {
        OR: [
          { id: productId },
          { shopifyId: productId }
        ]
      }
    });

    if (!product) {
      console.error(`商品ID ${productId} が見つかりません`);
      return null;
    }

    return product;
  } catch (error) {
    console.error('商品情報取得エラー:', error);
    return null;
  }
}

/**
 * Shopifyメタフィールドを取得する関数
 */
async function getShopifyMetafields(shopifyProductId: string) {
  try {
    console.log(`Shopify商品ID ${shopifyProductId} のメタフィールドを取得します...`);

    // Shopify IDを正規化
    const shopifyId = shopifyProductId.startsWith('gid://shopify/Product/')
      ? shopifyProductId
      : `gid://shopify/Product/${shopifyProductId}`;

    // GraphQLクライアントを初期化
    console.log('GraphQLクライアントを初期化中...');
    const shopifyAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

    if (!shopifyAccessToken) {
      throw new Error('SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません');
    }

    const graphQLClient = new GraphQLClient(
      `https://${config.shop}/admin/api/2025-01/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': shopifyAccessToken,
          'Content-Type': 'application/json',
        },
      }
    );

    // GraphQLクエリを実行
    const query = `
      query getProductMetafields($productId: ID!) {
        product(id: $productId) {
          id
          title
          metafields(first: 50) {
            edges {
              node {
                id
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    `;

    console.log(`GraphQLクエリを実行中... (商品ID: ${shopifyId})`);
    const result = await graphQLClient.request(query, { productId: shopifyId });

    if (!result.product) {
      console.error('商品が見つかりません');
      return null;
    }

    return result.product;
  } catch (error) {
    console.error('Shopifyメタフィールド取得エラー:', error);
    return null;
  }
}

/**
 * メタフィールドを表示する関数
 */
function displayMetafields(product: any) {
  console.log('----- 商品情報 -----');
  console.log(`ID: ${product.id}`);
  console.log(`タイトル: ${product.title}`);

  console.log('\n----- メタフィールド一覧 -----');
  const metafields = product.metafields.edges;

  if (metafields.length === 0) {
    console.log('メタフィールドはありません');
    return;
  }

  metafields.forEach((edge: any, index: number) => {
    const metafield = edge.node;
    console.log(`メタフィールド ${index + 1}:`);
    console.log(`  ID: ${metafield.id}`);
    console.log(`  名前空間: ${metafield.namespace}`);
    console.log(`  キー: ${metafield.key}`);
    console.log(`  タイプ: ${metafield.type}`);

    // JSONの場合は整形して表示
    if (metafield.type === 'json') {
      try {
        const value = JSON.parse(metafield.value);
        console.log('  値:');
        console.log(JSON.stringify(value, null, 2));
      } catch (error) {
        console.log(`  値: ${metafield.value}`);
      }
    } else {
      console.log(`  値: ${metafield.value}`);
    }

    console.log('---');
  });
}

/**
 * 予約メタフィールドを詳細表示する関数
 */
function displayBookingMetafield(product: any) {
  const metafields = product.metafields.edges;
  const bookingMetafield = metafields.find((edge: any) =>
    edge.node.namespace === 'rental' && edge.node.key === 'bookings'
  );

  if (!bookingMetafield) {
    console.log('\n----- 予約メタフィールド -----');
    console.log('予約メタフィールドはありません');
    return;
  }

  console.log('\n----- 予約メタフィールド詳細 -----');
  const metafield = bookingMetafield.node;

  try {
    const value = JSON.parse(metafield.value);

    // 予約情報を表示
    console.log('予約一覧:');
    if (value.bookings && value.bookings.length > 0) {
      value.bookings.forEach((booking: any, index: number) => {
        console.log(`予約 ${index + 1}:`);
        console.log(`  ID: ${booking.id}`);
        console.log(`  開始日: ${booking.startDate}`);
        console.log(`  終了日: ${booking.endDate}`);
        console.log(`  ステータス: ${booking.status}`);
        console.log(`  顧客名: ${booking.customerName}`);
        console.log(`  顧客メール: ${booking.customerEmail || 'なし'}`);
        console.log(`  バリアントID: ${booking.variantId || 'なし'}`);
        console.log('---');
      });
    } else {
      console.log('予約はありません');
    }

    // バリアント情報を表示
    if (value.variants) {
      console.log('\nバリアント情報:');
      Object.entries(value.variants).forEach(([variantId, variantData]: [string, any]) => {
        console.log(`バリアントID: ${variantId}`);
        console.log(`  ステータス: ${variantData.status}`);

        if (variantData.reservations && variantData.reservations.length > 0) {
          console.log('  予約:');
          variantData.reservations.forEach((reservation: any, index: number) => {
            console.log(`    予約 ${index + 1}:`);
            console.log(`      ID: ${reservation.id}`);
            console.log(`      タイプ: ${reservation.type}`);
            console.log(`      開始日: ${reservation.startDate}`);
            console.log(`      終了日: ${reservation.endDate}`);
            console.log(`      ステータス: ${reservation.status}`);
            console.log('    ---');
          });
        } else {
          console.log('  予約はありません');
        }

        console.log('---');
      });
    }

    // 利用可能状態を表示
    if (value.availability) {
      console.log('\n利用可能状態:');
      console.log(`  レンタル状態: ${value.availability.rentalStatus}`);
      console.log(`  開始日: ${value.availability.startDate}`);

      if (value.availability.maintenanceDates && value.availability.maintenanceDates.length > 0) {
        console.log('  メンテナンス日:');
        value.availability.maintenanceDates.forEach((date: string) => {
          console.log(`    - ${date}`);
        });
      }

      if (value.availability.blockedDates && value.availability.blockedDates.length > 0) {
        console.log('  ブロック日:');
        value.availability.blockedDates.forEach((date: string) => {
          console.log(`    - ${date}`);
        });
      }
    }

    // 全体のステータスを表示
    if (value.status) {
      console.log(`\n全体のステータス: ${value.status}`);
    }

    // 最終更新日時を表示
    if (value.lastUpdated) {
      console.log(`最終更新日時: ${value.lastUpdated}`);
    }
  } catch (error) {
    console.error('予約メタフィールドの解析に失敗しました:', error);
    console.log('生のメタフィールド値:');
    console.log(metafield.value);
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('Shopifyメタフィールド確認を開始します...');

    // コマンドライン引数から商品IDを取得
    const productId = config.productId || process.argv[2];

    if (!productId) {
      console.error('商品IDが指定されていません。コマンドライン引数で商品IDを指定してください。');
      console.error('例: npx tsx scripts/check-shopify-metafields.ts 123456789');
      process.exit(1);
    }

    // 商品情報を取得
    const product = await getProductInfo(productId);

    if (!product) {
      console.error('商品情報の取得に失敗しました');
      process.exit(1);
    }

    console.log(`商品情報を取得しました: ${product.title} (ID: ${product.id}, Shopify ID: ${product.shopifyId})`);

    // Shopifyメタフィールドを取得
    const shopifyProduct = await getShopifyMetafields(product.shopifyId);

    if (!shopifyProduct) {
      console.error('Shopifyメタフィールドの取得に失敗しました');
      process.exit(1);
    }

    // メタフィールドを表示
    displayMetafields(shopifyProduct);

    // 予約メタフィールドを詳細表示
    displayBookingMetafield(shopifyProduct);

    console.log('\nShopifyメタフィールド確認が完了しました');
  } catch (error) {
    console.error('実行中にエラーが発生しました:');
    console.error(error);
    if (error instanceof Error) {
      console.error('エラーメッセージ:', error.message);
      console.error('スタックトレース:', error.stack);
    }
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
