/**
 * 単一商品での在庫設定テスト
 */

import { authenticate } from '../app/shopify.server';
import { VariantAutoCreatorService } from '../app/services/shopify/variant-auto-creator.service';

async function testSingleProductInventory() {
  console.log('=== 単一商品在庫設定テスト開始 ===');

  try {
    const { admin } = await authenticate.admin(new Request('http://localhost'));
    const variantService = new VariantAutoCreatorService();

    // テスト商品データ
    const testProduct = {
      title: 'テスト商品 在庫設定確認',
      handle: 'test-inventory-product',
      productType: 'グラス',
      vendor: 'テストベンダー',
      tags: ['テスト', 'レンタル'],
      status: 'ACTIVE',
      variants: [{
        title: 'デフォルト',
        price: '1000',
        sku: 'TEST-INV-001',
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY'
      }]
    };

    // 1. 商品作成
    console.log('1. テスト商品を作成中...');
    const createResponse = await admin.graphql(`
      mutation productCreate($input: ProductInput!) {
        productCreate(input: $input) {
          product {
            id
            title
            handle
          }
          userErrors {
            field
            message
          }
        }
      }
    `, {
      input: testProduct
    });

    const createData = await createResponse.json();
    
    if (createData.errors || createData.data.productCreate.userErrors.length > 0) {
      throw new Error(`商品作成エラー: ${JSON.stringify(createData.errors || createData.data.productCreate.userErrors)}`);
    }

    const productId = createData.data.productCreate.product.id;
    const shopifyProductId = productId.split('/').pop();
    console.log(`✅ 商品作成成功: ${shopifyProductId}`);

    // 2. バリエーション自動作成（在庫設定含む）
    console.log('2. バリエーション自動作成と在庫設定をテスト中...');
    
    const result = await variantService.createMissingVariants(admin, shopifyProductId, {
      basePrice: 1000,
      productStatus: 'available',
      location: 'NY',
      createProvisionalVariants: false
    });

    console.log('=== バリエーション作成結果 ===');
    console.log(`作成されたバリエーション数: ${result.createdVariants.length}`);
    console.log(`在庫設定: ${result.inventoryUpdated ? '成功' : '失敗'}`);
    
    if (result.errors.length > 0) {
      console.log('エラー:');
      result.errors.forEach(error => console.log(`  - ${error}`));
    }

    // 3. 在庫状況確認
    console.log('3. 在庫状況を確認中...');
    const inventoryResponse = await admin.graphql(`
      query getProductInventory($id: ID!) {
        product(id: $id) {
          title
          variants(first: 20) {
            edges {
              node {
                id
                title
                sku
                inventoryItem {
                  id
                  tracked
                  inventoryLevels(first: 5) {
                    edges {
                      node {
                        id
                        available
                        location {
                          id
                          name
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    `, {
      id: productId
    });

    const inventoryData = await inventoryResponse.json();
    
    if (inventoryData.errors) {
      throw new Error(`在庫確認エラー: ${JSON.stringify(inventoryData.errors)}`);
    }

    const product = inventoryData.data.product;
    console.log(`\n📦 商品: ${product.title}`);
    
    product.variants.edges.forEach((variantEdge: any, index: number) => {
      const variant = variantEdge.node;
      console.log(`\n  ${index + 1}. ${variant.title} (SKU: ${variant.sku})`);
      console.log(`     Tracked: ${variant.inventoryItem.tracked}`);
      
      const inventoryLevels = variant.inventoryItem.inventoryLevels.edges;
      if (inventoryLevels.length === 0) {
        console.log(`     ⚠️ 在庫レベルが設定されていません`);
      } else {
        inventoryLevels.forEach((levelEdge: any) => {
          const level = levelEdge.node;
          console.log(`     📍 ${level.location.name}: ${level.available}個`);
        });
      }
    });

    // 4. クリーンアップ
    console.log('\n4. テスト商品を削除中...');
    await admin.graphql(`
      mutation productDelete($input: ProductDeleteInput!) {
        productDelete(input: $input) {
          deletedProductId
          userErrors {
            field
            message
          }
        }
      }
    `, {
      input: {
        id: productId
      }
    });

    console.log('✅ テスト商品削除完了');

  } catch (error) {
    console.error('❌ テストエラー:', error);
  }

  console.log('\n=== 単一商品在庫設定テスト完了 ===');
}

// スクリプト実行
if (import.meta.url === new URL(import.meta.url).href) {
  testSingleProductInventory().catch(console.error);
}

export { testSingleProductInventory };
