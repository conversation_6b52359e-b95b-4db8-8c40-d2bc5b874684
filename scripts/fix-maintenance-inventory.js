import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixMaintenanceInventory() {
  console.log('🔧 メンテナンス中商品の在庫カレンダーを修正開始...\n');

  try {
    // TEST-TABLE-001 商品の情報を取得
    const product = await prisma.product.findFirst({
      where: {
        sku: 'TEST-TABLE-001'
      }
    });

    if (!product) {
      console.log('❌ TEST-TABLE-001 商品が見つかりません');
      return;
    }

    console.log('📦 商品情報:');
    console.log(`  - ID: ${product.id}`);
    console.log(`  - タイトル: ${product.title}`);
    console.log(`  - SKU: ${product.sku}`);
    console.log('');

    // 進行中のメンテナンスを取得
    const activeMaintenances = await prisma.maintenance.findMany({
      where: {
        productId: product.id,
        status: 'IN_PROGRESS'
      }
    });

    console.log('🔧 進行中のメンテナンス:');
    if (activeMaintenances.length === 0) {
      console.log('  - 進行中のメンテナンスなし');
      return;
    }

    activeMaintenances.forEach((maintenance, index) => {
      console.log(`  ${index + 1}. ID: ${maintenance.id}`);
      console.log(`     ステータス: ${maintenance.status}`);
      console.log(`     タイプ: ${maintenance.type}`);
      console.log(`     開始日: ${maintenance.startDate.toISOString().split('T')[0]}`);
      console.log(`     終了日: ${maintenance.endDate ? maintenance.endDate.toISOString().split('T')[0] : '未設定'}`);
    });
    console.log('');

    // 在庫カレンダーを直接更新
    console.log('🔄 在庫カレンダーを更新中...');

    // メンテナンス期間中の日付を生成（今日から30日間のみ）
    const maintenanceDates = [];
    const today = new Date();
    const maxDate = new Date();
    maxDate.setDate(today.getDate() + 30); // 最大30日間のみ処理

    for (const maintenance of activeMaintenances) {
      const startDate = new Date(Math.max(maintenance.startDate.getTime(), today.getTime()));
      const endDate = maintenance.endDate ?
        new Date(Math.min(maintenance.endDate.getTime(), maxDate.getTime())) :
        maxDate;

      const currentDate = new Date(startDate);
      while (currentDate <= endDate) {
        maintenanceDates.push(new Date(currentDate));
        currentDate.setDate(currentDate.getDate() + 1);
      }
    }

    console.log(`📅 メンテナンス期間: ${maintenanceDates.length}日間`);

    // 在庫カレンダーを更新
    for (const date of maintenanceDates) {
      await prisma.inventoryCalendar.upsert({
        where: {
          shop_productId_date: {
            shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
            productId: product.id,
            date: date
          }
        },
        update: {
          isAvailable: false,
          unavailableReason: 'MAINTENANCE',
          maintenanceId: activeMaintenances[0].id,
          updatedAt: new Date()
        },
        create: {
          shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
          productId: product.id,
          shopifyProductId: product.shopifyId || '0',
          date: date,
          isAvailable: false,
          unavailableReason: 'MAINTENANCE',
          maintenanceId: activeMaintenances[0].id,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }

    console.log('✅ 在庫カレンダー更新完了');
    console.log('');

    // 更新後の在庫カレンダーを確認
    const checkStartDate = new Date();
    const checkEndDate = new Date();
    checkEndDate.setDate(checkStartDate.getDate() + 7);

    const inventoryCalendar = await prisma.inventoryCalendar.findMany({
      where: {
        productId: product.id,
        date: {
          gte: checkStartDate,
          lte: checkEndDate
        }
      },
      orderBy: {
        date: 'asc'
      }
    });

    console.log('📅 更新後の在庫カレンダー（今日から7日間）:');
    if (inventoryCalendar.length === 0) {
      console.log('  - 在庫カレンダーデータなし');
    } else {
      inventoryCalendar.forEach(entry => {
        const dateStr = entry.date.toISOString().split('T')[0];
        const status = entry.isAvailable ? '✅ 利用可能' : '❌ 利用不可';
        const reason = entry.unavailableReason ? ` (理由: ${entry.unavailableReason})` : '';
        const maintenanceId = entry.maintenanceId ? ` [メンテナンスID: ${entry.maintenanceId}]` : '';
        console.log(`  ${dateStr}: ${status}${reason}${maintenanceId}`);
      });
    }

    // 整合性チェック
    const unavailableDates = inventoryCalendar.filter(entry => !entry.isAvailable);
    const maintenanceUnavailable = unavailableDates.filter(entry =>
      entry.unavailableReason === 'MAINTENANCE' || entry.unavailableReason === 'maintenance'
    );

    console.log('\n🔍 修正後の整合性チェック:');
    if (activeMaintenances.length > 0 && maintenanceUnavailable.length === 0) {
      console.log('  ❌ まだ問題があります: 進行中のメンテナンスがあるのに在庫カレンダーが更新されていません');
    } else if (activeMaintenances.length === 0 && maintenanceUnavailable.length > 0) {
      console.log('  ❌ 問題: 進行中のメンテナンスがないのに在庫カレンダーでメンテナンス中になっています');
    } else {
      console.log('  ✅ メンテナンス状態と在庫カレンダーが整合しています');
      console.log(`  📊 メンテナンス中の日数: ${maintenanceUnavailable.length}日`);
    }

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
fixMaintenanceInventory();
