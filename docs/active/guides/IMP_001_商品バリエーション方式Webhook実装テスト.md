# 商品バリエーション方式Webhook実装テスト

## 概要

商品バリエーション方式の実装において、Shopify Webhookを使用したメタフィールド自動設定の実装をテストする方法について説明します。

## 前提条件

1. アプリがShopifyストアにインストールされていること
2. 必要な環境変数が設定されていること
   - `SHOPIFY_API_KEY`
   - `SHOPIFY_API_SECRET`
   - `SHOPIFY_SHOP`
   - `SHOPIFY_ADMIN_API_ACCESS_TOKEN`
3. Prismaデータベースが設定されていること

## テスト手順

### 1. Webhookの登録確認

1. アプリを起動する
   ```bash
   npm run dev:fixed-tunnel
   ```

2. Shopify管理画面でWebhookが正しく登録されているか確認する
   - Shopify管理画面 > 設定 > 通知 > Webhook
   - 以下のWebhookが登録されていることを確認:
     - `products/create`
     - `products/update`

3. 登録されていない場合は、`docs/IMP_002_手動Webhook登録手順.md`に従って手動でWebhookを登録する

### 2. テスト用商品の作成

1. Shopify管理画面で新しい商品を作成する
   - 商品名: `テスト商品 - バリエーションテスト`
   - SKU: `TEST-VAR-001`

2. 以下のバリエーションを追加する
   - `テスト商品 - バリエーションテスト - 1日` (SKU: `TEST-VAR-001-1D`)
   - `テスト商品 - バリエーションテスト - 2日` (SKU: `TEST-VAR-001-2D`)
   - `テスト商品 - バリエーションテスト - 3日` (SKU: `TEST-VAR-001-3D`)
   - `テスト商品 - バリエーションテスト - 4日` (SKU: `TEST-VAR-001-4D`)
   - `テスト商品 - バリエーションテスト - 5日` (SKU: `TEST-VAR-001-5D`)
   - `テスト商品 - バリエーションテスト - 6日` (SKU: `TEST-VAR-001-6D`)
   - `テスト商品 - バリエーションテスト - 7日` (SKU: `TEST-VAR-001-7D`)
   - `テスト商品 - バリエーションテスト - 8日以上` (SKU: `TEST-VAR-001-8P`)

3. 商品を保存する

### 3. Webhookの動作確認

1. アプリのログを確認する
   ```bash
   npm run dev
   ```

2. ログに以下のようなメッセージが表示されていることを確認する
   ```
   Webhook received: products/create for your-store.myshopify.com
   ```

3. Shopify管理画面で商品のメタフィールドを確認する
   - 商品詳細画面 > メタフィールド
   - 以下のメタフィールドが自動的に設定されていることを確認:
     - `rental.variant_mapping`
     - `rental.pricing`
     - `rental.basic_info`
     - `rental.variation_type`

### 4. メタフィールドの内容確認

1. `rental.variant_mapping`メタフィールドの内容を確認する
   - 各日数とバリエーションIDのマッピングが正しく設定されているか確認

   ```json
   {
     "1day": "gid://shopify/ProductVariant/[ID]",
     "2day": "gid://shopify/ProductVariant/[ID]",
     ...
     "8plus": "gid://shopify/ProductVariant/[ID]"
   }
   ```

2. `rental.pricing`メタフィールドの内容を確認する
   - 料金設定が正しく設定されているか確認
   - `variantPrices`フィールドが追加されているか確認

   ```json
   {
     "basePrice": 5000,
     "depositRate": 0.1,
     "discountRules": {
       "day2_6_rate": 0.2,
       "day7_plus_rate": 0.1
     },
     "minimumDays": 1,
     "maximumDays": 30,
     "variantPrices": {
       "1day": 5000,
       "2day": 6000,
       ...
     }
   }
   ```

3. `rental.basic_info`メタフィールドの内容を確認する
   - 商品の基本情報が正しく設定されているか確認

   ```json
   {
     "productCode": "TEST-VAR",
     "detailCode": "001",
     "kana": "テスト商品 - バリエーションテスト",
     "location": "NY",
     "status": "available"
   }
   ```

4. `rental.variation_type`メタフィールドの内容を確認する
   - 値が「レンタル日数」に設定されているか確認

### 5. 商品更新時のテスト

1. 商品情報を更新する（例: 商品名を変更する）
2. 更新後にWebhookが発火し、メタフィールドが更新されることを確認する
3. ログに以下のようなメッセージが表示されていることを確認する
   ```
   Webhook received: products/update for your-store.myshopify.com
   ```

## トラブルシューティング

### Webhookが発火しない場合

1. Shopify管理画面でWebhookの登録状況を確認する
   - 「設定」→「通知」→「Webhook」セクションで確認します
2. アプリの環境変数が正しく設定されているか確認する
   - 特に`SHOPIFY_ADMIN_API_ACCESS_TOKEN`が正しく設定されているか確認する
3. 手動でWebhookを再登録する
   - `docs/IMP_002_手動Webhook登録手順.md`に従って手動でWebhookを登録する

### メタフィールドが設定されない場合

1. バリエーション名が命名規則に従っているか確認する
2. ログでエラーメッセージを確認する
3. `app/routes/webhooks.products.jsx`ファイルのコードを確認する

### バリエーションマッピングが不完全な場合

1. すべてのバリエーション（1日〜8日以上）が正しく命名されているか確認する
2. バリエーション名に日本語とローマ字が混在していないか確認する

## まとめ

Shopify Webhookを活用することで、商品登録後に自動的にメタフィールドを設定する仕組みを構築できます。これにより、商品バリエーション方式の実装が効率化され、手動でのメタフィールド設定が不要になります。

バリエーション命名規則とSKU命名規則を統一することで、自動設定の精度が向上します。また、Prismaデータベースと連携することで、商品情報の一元管理が可能になります。
