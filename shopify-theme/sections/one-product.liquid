{%- if section.settings.product != blank -%}
  {% include 'global-variables' %}
  {%- assign product = all_products[section.settings.product] -%}
  {% include 'product-res-variables' %}
  {%- assign image_size = image_size | default: '470x' -%}
  {%- assign reverse = reverse | default: section.settings.reverse -%}
  {%- assign show_arrows = show_arrows | default: section.settings.show_arrows -%}
  {%- assign show_label_in_stock = show_label_in_stock | default: section.settings.show_label_in_stock -%}
  {%- assign show_label_pre_order = show_label_pre_order | default: section.settings.show_label_pre_order -%}
  {%- assign show_label_out_stock = show_label_out_stock | default: section.settings.show_label_out_stock -%}
  {%- assign show_label_sale = show_label_sale | default: section.settings.show_label_sale -%}
  {%- assign show_label_new = show_label_new | default: section.settings.show_label_new -%}
  {%- assign show_label_hot = show_label_hot | default: section.settings.show_label_hot -%}
  {%- assign show_collections = show_collections | default: section.settings.show_collections -%}
  {%- assign show_title = show_title | default: section.settings.show_title -%}
  {%- assign show_sku = show_sku | default: section.settings.show_sku -%}
  {%- assign show_barcode = show_barcode | default: section.settings.show_barcode -%}
  {%- assign show_availability = show_availability | default: section.settings.show_availability -%}
  {%- assign show_type = show_type | default: section.settings.show_type -%}
  {%- assign show_vendor = show_vendor | default: section.settings.show_vendor -%}
  {%- assign show_price = show_price | default: section.settings.show_price -%}
  {%- assign show_sale_details = show_sale_details | default: section.settings.show_sale_details -%}
  {%- assign show_options = show_options | default: section.settings.show_options -%}
  {%- assign show_custom_options = show_custom_options | default: settings.product_show_custom_options -%}
  {%- assign show_size_guide = show_size_guide | default: section.settings.show_size_guide -%}
  {%- assign show_delivery_return = show_delivery_return | default: section.settings.show_delivery_return -%}
  {%- assign show_quantity = show_quantity | default: section.settings.show_quantity -%}
  {%- assign show_button_add_to_cart = show_button_add_to_cart | default: section.settings.show_button_add_to_cart -%}
  {%- assign show_button_add_to_wishlist = show_button_add_to_wishlist | default: section.settings.show_button_add_to_wishlist -%}
  {%- assign show_button_add_to_compare = show_button_add_to_compare | default: section.settings.show_button_add_to_compare -%}
  {%- assign show_button_dynamic_checkout = show_button_dynamic_checkout | default: section.settings.show_button_dynamic_checkout -%}
  {%- assign show_dynamic_checkout_confirmation = show_dynamic_checkout_confirmation | default: section.settings.show_dynamic_checkout_confirmation -%}
  {%- assign styled_dynamic_checkout = styled_dynamic_checkout | default: section.settings.styled_dynamic_checkout -%}
  {%- assign show_reviews = show_reviews | default: section.settings.show_reviews -%}
  {%- assign hide_reviews_counter = hide_reviews_counter | default: section.settings.hide_reviews_counter -%}
  {%- assign show_countdown = show_countdown | default: section.settings.show_countdown -%}
  {% capture image_size_placeholder %}_{width}x.{% endcapture %}
  {%- if settings.compare_type == 'disable' -%}
    {%- assign show_button_add_to_compare = false -%}
  {%- endif -%}
  {%- if settings.wishlist_type == 'disable' -%}
    {%- assign show_button_add_to_wishlist = false -%}
  {%- endif -%}
  {%- if settings.reviews_type == 'disable' -%}
    {%- assign show_reviews = false -%}
  {%- endif -%}
  {%- if section.settings.layout == '1' -%}
    {%- assign mobile_centered = true -%}
    {%- assign mobile_start = false -%}
  {%- else -%}
    {%- assign mobile_centered = false -%}
    {%- assign mobile_start = true -%}
  {%- endif -%}
  {%- assign product_form_id = 'product-form-' | append: section.id -%}
  <div data-section-id="{{ section.id }}" data-section-type="one-product">
    {% capture container_html %}
      <div class="container{% if section.settings.bg_image != blank %} position-relative py-40 py-md-0{% endif %}">
        <div class="one-product"{% include 'product-get-attributes' %}>
          <div class="row align-items-md-center">
            {%- if section.settings.show_gallery -%}
              <div class="col-12 col-md-6 d-flex justify-content-center{% if reverse %} justify-content-md-end order-md-1{% else %} justify-content-md-start{% endif %}">
                <div class="one-product__image w-100">
                  {%- if section.settings.image != blank -%}
                    {%- if section.settings.product_format_pjpg -%}
                      {%- assign image_format = 'pjpg' -%}
                    {%- else -%}
                      {%- assign image_format = null -%}
                    {%- endif -%}
                    <a href="{{ url }}" class="d-block">
                      {%- capture image_id -%} data-image-id="{{ image.id }}"{%- endcapture -%}
                      {% render 'rimage' with image: section.settings.image size: image_size format: image_format %}
                    </a>
                  {%- else -%}
                    {%- assign img_hover_url = hover_image | img_url: '1x1', format: image_format | replace: '_1x1.', image_size_placeholder -%}
                    <div class="product-image product-image--hover-{{ settings.product_hover_animation_type }} position-relative w-100 js-product-images-navigation js-product-images-hovered-end{% if settings.product_replace_images_hover %} js-product-images-hover{% endif %}"{% if settings.product_replace_images_hover and hover_image != blank %} data-js-product-image-hover="{{ img_hover_url }}" data-js-product-image-hover-id="{{ hover_image.id }}"{% endif %}>
                      {% render 'product-get-images' with url: url image: image image_size: image_size title: title %}
                      {%- if show_arrows and product.images.size > 1 -%}
                        <div class="one-product__image-navigation absolute-stretch d-flex align-items-center z-index-1 pointer-events-none">
                          <div class="product-images-navigation position-absolute left-0 ml-10 pointer-events-all">
                            <span class="d-flex flex-center rounded-circle cursor-pointer" data-js-product-images-navigation="prev"{% if product.images.first.id == image.id %} data-disabled="disabled"{% endif %}><i class="mr-2">{% render 'icon-theme-006' %}</i></span>
                          </div>
                          <div class="product-images-navigation position-absolute right-0 mr-10 pointer-events-all">
                            <span class="d-flex flex-center rounded-circle cursor-pointer" data-js-product-images-navigation="next"{% if product.images.last.id == image.id %} data-disabled="disabled"{% endif %}><i class="ml-3">{% render 'icon-theme-007' %}</i></span>
                          </div>
                        </div>
                      {%- endif -%}
                    </div>
                  {%- endif -%}
                </div>
              </div>
            {%- endif -%}
            <div class="col-12{% if section.settings.show_gallery %} col-md-6 mt-30 mt-md-0{% endif %}">
              <div class="one-product-info{% if section.settings.bg_image != blank %} py-md-100 my-md-20{% endif %}">
                {%- if show_label_in_stock or show_label_pre_order or show_label_out_stock or show_label_sale or show_label_new or show_label_hot -%}
                  <div class="one-product-info__labels d-flex{% if section.settings.layout == '1' %} justify-content-center justify-content-md-start{% endif %} mb-15">
                    {%- if show_label_hot -%}
                      {% include 'product-get-label-hot' %}
                    {%- endif -%}
                    {%- if show_label_new -%}
                      {% include 'product-get-label-new' %}
                    {%- endif -%}
                    {%- if show_label_sale -%}
                      {% include 'product-get-label-sale' %}
                    {%- endif -%}
                    {%- if show_label_in_stock -%}
                      {% include 'product-get-label-in-stock' %}
                    {%- endif -%}
                    {%- if show_label_pre_order -%}
                      {% include 'product-get-label-pre-order' %}
                    {%- endif -%}
                    {%- if show_label_out_stock -%}
                      {% include 'product-get-label-out-stock' %}
                    {%- endif -%}
                  </div>
                {%- endif -%}
                {%- if show_countdown -%}
                  <div class="one-product-info__countdown mt-25 mb-15">
                    {% capture countdown_title %}{{ 'products.product.countdown.title' | t }}{% endcapture %}
                    {% render 'product-get-countdown' with countdown_date: countdown_date, countdown_type: 2, countdown_title: countdown_title, centered: centered mobile_centered: mobile_centered %}
                  </div>
                {%- endif -%}
                {%- if show_collections and product.collections.size > 0 -%}
                  <div class="one-product-info__collections mb-3{% if section.settings.layout == '1' %} text-center text-md-left{% endif %}">
                    {% render 'product-get-collections' with product: product %}
                  </div>
                {%- endif -%}
                <div class="one-product-info__title mb-15{% if section.settings.layout == '1' %} text-center text-md-left{% endif %}">
                  <a href="{{ url }}" class="h3 m-0">{{ title }}</a>
                </div>
                {%- if show_reviews -%}
                  <div class="one-product-info__reviews{% if section.settings.layout == '1' %} text-center text-md-left{% endif %}">
                    {%- if settings.reviews_type == 'default' -%}
                      {% render 'product-get-review' with product: product type: 2 hide_counter: hide_reviews_counter hide_mobile_counter: hide_reviews_mobile_counter %}
                    {%- elsif settings.reviews_type == 'growave' and settings.app_growave_enable == true -%}
                      {% capture the_snippet_review_avg %}{% render 'ssw-widget-avg-rate-listing', product: product %}{% endcapture %}
                      {%- unless the_snippet_review_avg contains 'Liquid error' or product.metafields.ssw['review'].avg == 0 -%}
                        {{ the_snippet_review_avg }}
                      {%- endunless -%}
                    
                    {%- endif -%}
                  </div>
                {%- endif -%}
                {%- if show_sku or show_barcode or show_availability or show_type or show_vendor -%}
                  <div class="one-product-info__details mb-25{% if section.settings.layout == '1' %} text-center text-md-left{% endif %}">
                    {%- if show_sku -%}
                      <div class="one-product-info__sku{% if sku == blank %} d-none-important{% endif %} mb-5">
                        <p class="m-0" data-js-product-sku>{{ 'products.product.sku' | t }}: <span>{{ sku }}</span></p>
                      </div>
                    {%- endif -%}
                    {%- if show_barcode -%}
                      <div class="one-product-info__barcode{% if barcode == blank %} d-none-important{% endif %} mb-5">
                        <p class="m-0" data-js-product-barcode>{{ 'products.product.barcode' | t }}:
                          <span>{{ barcode }}</span></p>
                      </div>
                    {%- endif -%}
                    {%- if show_availability -%}
                      <div class="one-product-info__availability mb-5">
                        {%- if quantity == 1 -%}
                          {%- capture item -%}{{ 'layout.cart.items_count.one' | t }}{%- endcapture -%}
                        {%- else -%}
                          {%- capture item -%}{{ 'layout.cart.items_count.other' | t }}{%- endcapture -%}
                        {%- endif -%}
                        <p class="m-0" data-js-product-availability data-availability="{% if available %}true{% else %}false{% endif %}">{{ 'products.product.availability' | t }}: <span>{% if available %}{{ 'products.product.availability_value_in_stock' | t: count: quantity, item: item }}{% else %}{{ 'products.product.availability_value_out_stock' | t }}{% endif %}</span></p>
                      </div>
                    {%- endif -%}
                    {%- if show_type -%}
                      <div class="one-product-info__type{% if type == blank %} d-none-important{% endif %} mb-5">
                        <p class="m-0" data-js-product-type>{{ 'products.product.type' | t }}: <span>{{ type }}</span></p>
                      </div>
                    {%- endif -%}
                    {%- if show_vendor -%}
                      <div class="one-product-info__vendor{% if vendor == blank %} d-none-important{% endif %} mb-5">
                        <p class="m-0" data-js-product-vendor>{{ 'products.product.vendor' | t }}:
                          <span>{{ vendor }}</span></p>
                      </div>
                    {%- endif -%}
                  </div>
                {%- endif -%}
                {%- if show_price -%}
                  <div class="mb-20">
                    <div class="one-product-info__price{% if section.settings.layout == '1' %} text-center text-md-left{% endif %}">
                      {% include 'product-get-price' %}
                    </div>
                    {%- if show_sale_details -%}
                      <p class="one-product-info__price-sale-details mt-5{% if section.settings.layout == '1' %} text-center text-md-left{% endif %}{% unless compare_at_price > price %} d-none-important{% endunless %}"
                         data-js-product-price-sale-details>
                        {%- if compare_at_price > price -%}
                          {%- assign price_save = compare_at_price | minus: price -%}
                          {%- assign price_save_money = price_save | money -%}
                          {%- assign price_save_procent = price | times: 100 | divided_by: compare_at_price | minus: 100 | times: -1 -%}
                          {{ 'products.product.price_sale_details_html' | t: price: price_save_money, procent: price_save_procent }}
                        {%- endif -%}
                      </p>
                    {%- endif -%}
                  </div>
                {%- endif -%}
                {%- if section.settings.description_content != blank -%}
                  <div class="one-product-info__description rte {% if section.settings.button_layout == '1' %}mb-30{% else %}mb-15{% endif %}{% if section.settings.layout == '1' %} text-center text-md-left{% endif %}">
                    {{ section.settings.description_content }}
                  </div>
                {%- endif -%}
                <div>
                  <div class="d-flex flex-column w-100 m-0">
                    {%- if show_options and show_custom_options -%}
                      {%- unless product.has_only_default_variant -%}
                        <div class="one-product-info__options mb-15">
                          {% include 'product-get-options' with options_type: 2 options_show_title: true mobile_start: mobile_start %}
                        </div>
                      {%- endunless -%}
                    {%- else -%}
                      {%- assign variants_size = product.variants | size -%}
                      {%- if variants_size > 1 -%}
                        {%- assign enable_select_options_button = true -%}
                      {%- endif -%}
                    {%- endif -%}
                    {%- if show_button_add_to_cart or show_options -%}
                      <div class="one-product-info__variants mb-15{% if show_options == false or show_custom_options %} d-none{% endif %}">
                        {% render 'product-get-variants' with product: product current_variant: current_variant show_options: show_options show_custom_options: show_custom_options, product_form_id: product_form_id %}
                      </div>
                    {%- endif -%}
                    {%- if show_size_guide and product.metafields.sizeguide.html != 'hide' or show_delivery_return -%}
                      <div class="one-product-info__details-buttons mb-30 mb-md-15 overflow-hidden">
                        <div class="row{% if section.settings.layout == '1' %} justify-content-center justify-content-md-start{% endif %}">
                          {%- if show_size_guide and product.metafields.sizeguide.html != 'hide' -%}
                            <div class="col-auto">
                              <div class="btn-link h6 d-flex align-items-center mb-10 js-popup-button" data-js-popup-button="size-guide">{% if product_popup_buttons_show_icon %}<i class="mr-8">{% include 'icon-theme-091' %}</i> {% endif %}{{ 'products.product.size_guide' | t }}</div>
                            </div>
                          {%- endif -%}
                          {%- if show_delivery_return -%}
                            <div class="col-auto">
                              <div class="btn-link h6 d-flex align-items-center mb-10 js-popup-button" data-js-popup-button="delivery-return">{% if product_popup_buttons_show_icon %}<i class="mr-8">{% include 'icon-theme-135' %}</i> {% endif %}{{ 'products.product.delivery_return' | t }}</div>
                            </div>
                          {%- endif -%}
                        </div>
                      </div>
                    {%- endif -%}
                    {%- if section.settings.button_layout == '1' -%}
                      {%- if show_quantity -%}
                        <div class="one-product-info__field one-product-info__quantity d-flex flex-column{% if section.settings.layout == '1' %} align-items-center align-items-md-start{% endif %} mb-20">
                          {% render 'product-get-quantity' with quantity_show_title: true quantity_type: 3, product_form_id: product_form_id %}
                        </div>
                      {%- endif -%}
                      {%- if show_button_add_to_cart or show_button_add_to_wishlist or show_button_add_to_compare -%}
                        <div class="one-product-info__buttons d-flex flex-column{% if section.settings.layout == '1' %} align-items-center align-items-md-start{% endif %} mt-5 mt-md-10 overflow-hidden">
                          {%- if show_button_add_to_cart -%}
                            <div class="one-product-info__button-add-to-cart">
                              {% include 'product-get-button-cart', show_button_dynamic_checkout: show_button_dynamic_checkout, show_dynamic_checkout_confirmation: show_dynamic_checkout_confirmation %}
                            </div>
                          {%- endif -%}
                          {%- if show_button_add_to_wishlist or show_button_add_to_compare -%}
                            <div class="one-product-info__buttons-section d-flex">
                              {%- if show_button_add_to_wishlist -%}
                                <div class="one-product-info__button-add-to-wishlist mt-15">
                                  {%- if settings.wishlist_type == 'default' -%}
                                    {% include 'product-get-button-wishlist' %}
                                  {%- elsif settings.wishlist_type == 'growave' and settings.app_growave_enable == true -%}
                                    {% capture the_snippet_fave_icon %}{% render 'ssw-widget-faveicon' with product.id %}{% endcapture %}
                                    {%- unless the_snippet_fave_icon contains 'Liquid error' -%}
                                      {{ the_snippet_fave_icon }}
                                    {%- endunless -%}
                                  {%- endif -%}
                                </div>
                              {%- endif -%}
                              {%- if show_button_add_to_compare -%}
                                <div class="one-product-info__button-add-to-compare mt-15">
                                  {% include 'product-get-button-compare' %}
                                </div>
                              {%- endif -%}
                            </div>
                          {%- endif -%}
                        </div>
                      {%- endif -%}
                    {%- elsif section.settings.button_layout == '2' -%}
                      {%- if show_button_add_to_cart or show_button_add_to_wishlist or show_button_add_to_compare -%}
                        <div class="one-product-info__buttons d-flex flex-column{% if section.settings.layout == '1' %} align-items-center align-items-md-start{% endif %} mt-5 mt-md-10 overflow-hidden">
                          {%- if show_quantity or show_button_add_to_cart -%}
                            <div class="d-flex align-items-end">
                              {%- if show_quantity -%}
                                <div class="one-product-info__field one-product-info__quantity">
                                  {% render 'product-get-quantity' with quantity_show_title: true quantity_type: 3, product_form_id: product_form_id %}
                                </div>
                              {%- endif -%}
                              {%- if show_button_add_to_cart -%}
                                <div class="one-product-info__button-add-to-cart">
                                  {% include 'product-get-button-cart' with button_select_disable: true %}
                                </div>
                              {%- endif -%}
                            </div>
                          {%- endif -%}
                          {%- if show_button_add_to_wishlist or show_button_add_to_compare -%}
                            <div class="one-product-info__buttons-section d-flex">
                              {%- if show_button_add_to_wishlist -%}
                                <div class="one-product-info__button-add-to-wishlist mt-15">
                                  {%- if settings.wishlist_type == 'default' -%}
                                    {% include 'product-get-button-wishlist' %}
                                  {%- elsif settings.wishlist_type == 'growave' and settings.app_growave_enable == true -%}
                                    {% capture the_snippet_fave_icon %}{% render 'ssw-widget-faveicon' with product.id %}{% endcapture %}
                                    {%- unless the_snippet_fave_icon contains 'Liquid error' -%}
                                      {{ the_snippet_fave_icon }}
                                    {%- endunless -%}
                                  {%- endif -%}
                                </div>
                              {%- endif -%}
                              {%- if show_button_add_to_compare -%}
                                <div class="one-product-info__button-add-to-compare mt-15">
                                  {% include 'product-get-button-compare' %}
                                </div>
                              {%- endif -%}
                            </div>
                          {%- endif -%}
                        </div>
                      {%- endif -%}
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endcapture %}
    {%- if section.settings.bg_image != blank -%}
      <div class="one-product__bg position-relative">
        {%- if section.settings.height != 'auto' -%}
            <div class="one-product__bg_spacer d-none d-md-block {% if section.settings.height == 'fullscreen' %} lazyload-fullscreen{% elsif section.settings.height == 'fullscreen_header' %} lazyload-fullscreen-header{% endif %}"></div>
        {%- endif -%}
        <div class="one-product__bg_image absolute-stretch lazyload{% if section.settings.bg_image_mobile != blank %} d-none d-sm-block{% endif %}" 
            data-master="{{ section.settings.bg_image | img_url: '1x1', format: 'pjpg' | replace: '_1x1.', image_size_placeholder }}"
            data-bg="{{ section.settings.bg_image | img_url: '2000x', format: 'pjpg' }}">
        </div>
        {%- if section.settings.bg_image_mobile != blank -%}
          <div class="one-product__bg_image absolute-stretch d-sm-none lazyload"
              data-master="{{ section.settings.bg_image_mobile | img_url: '1x1', format: 'pjpg' | replace: '_1x1.', image_size_placeholder }}"
              data-bg="{{ section.settings.bg_image_mobile | img_url: '2000x', format: 'pjpg' }}">
          </div>
        {%- endif -%}
        <div class="one-product__content{% if section.settings.height != 'auto' %} absolute-md-stretch position-md-relative d-md-flex align-items-md-center{% endif %}">
            {{ container_html }}
        </div>
      </div>
    {%- else -%}
      {{ container_html }}
    {%- endif -%}
  </div>
{%- else -%}
  {% render 'no-blocks' with message: 'homepage.onboarding.no_content_message.one_product' %}
{%- endif -%}


{% schema %}
{
  "name": { "en": "One Product", "ja": "単一商品"},
  "settings": [
    {
      "type": "product",
      "id": "product",
      "label": { "en": "Product", "ja": "商品"}
    },
    {
      "type": "select",
      "id": "layout",
      "label": { "en": "Layout", "ja": "レイアウト"},
      "default": "1",
      "options": [
        {
          "value": "1",
          "label": { "en": "1 (mobile centered)", "ja": "1 - モバイルでは中央"}
        },
        {
          "value": "2",
          "label": "2"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "reverse",
      "label": { "en": "Reverse layout", "ja": "レイアウトの反転"},
      "default": false
    },
    {
      "type": "header",
      "content": { "en": "Gallery", "ja": "ギャラリー"}
    },
    {
      "type": "checkbox",
      "id": "show_gallery",
      "label": { "en": "Show gallery", "ja": "ギャラリー表示"},
      "default": true
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": { "en": "Image", "ja": "画像"}
    },
    {
      "type": "checkbox",
      "id": "show_arrows",
      "label": { "en": "Arrows", "ja": "矢印表示"},
      "default": true
    },
    {
      "type": "header",
      "content": { "en": "Labels", "ja": "ラベル"}
    },
    {
      "type": "checkbox",
      "id": "show_label_in_stock",
      "label": { "en": "Show label 'In stock'", "ja": "在庫ありラベルを表示"},
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_label_pre_order",
      "label": { "en": "Show label 'Pre order'", "ja": "予約プレオーダーラベルを表示"},
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_label_out_stock",
      "label": { "en": "Show label 'Out of stock'", "ja": "在庫なしラベルを表示"},
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_label_sale",
      "label": { "en": "Show label 'Sale'", "ja": "セールラベルを表示"},
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_label_new",
      "label": { "en": "Show label 'New'", "ja": "新商品ラベルを表示"},
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_label_hot",
      "label": { "en": "Show label 'Hot'", "ja": "おすすめラベルを表示"},
      "default": false
    },
    {
      "type": "header",
      "content": { "en": "Elements", "ja": "要素"}
    },
    {
      "type": "checkbox",
      "id": "show_collections",
      "label": { "en": "Show the product's collections", "ja": "商品のコレクションを表示"},
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_title",
      "label": { "en": "Show the product title", "ja": "商品タイトルを表示"},
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_price",
      "label": { "en": "Show the product price", "ja": "商品価格を表示"},
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_sale_details",
      "label": { "en": "Show sale price details", "ja": "セール価格詳細を表示"},
      "default": true
    },
    {
      "type": "textarea",
      "id": "description_content",
      "label": { "en": "Description content", "ja": "商品説明"},
      "info": { "en": "Clear field to remove description", "ja": "表示しない場合は空欄にしてください。"},
      "default": "商品説明" //"Description"
    },
    {
      "type": "checkbox",
      "id": "show_sku",
      "label": { "en": "Show product SKU", "ja": "SKUを表示"},
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_barcode",
      "label": { "en": "Show product barcode", "ja": "商品バーコードを表示"},
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_availability",
      "label": { "en": "Show product stock status", "ja": "商品在庫状況を表示"},
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_type",
      "label": { "en": "Show product type", "ja": "商品タイプを表示"},
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": { "en": "Show product vendor", "ja": "販売元を表示"},
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_countdown",
      "label": { "en": "Show countdown", "ja": "カウントダウンを表示"},
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_options",
      "label": { "en": "Show options (variants)", "ja": "オプション(バリエーション)を表示"},
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_size_guide",
      "label": { "en": "Show button 'Size guide'", "ja": "サイズガイドボタンを表示"},
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_delivery_return",
      "label": { "en": "Show button 'Delivery return'", "ja": "配送と返品ボタンを表示"},
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_quantity",
      "label": { "en": "Show quantity", "ja": "数量を表示"},
      "default": false
    },
    {
      "type": "select",
      "id": "button_layout",
      "label": { "en": "Buttons layout", "ja": "ボタンのレイアウト"},
      "default": "1",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "show_button_add_to_cart",
      "label": { "en": "Show button 'Add to cart'", "ja": "カート追加ボタンを表示"},
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_button_add_to_wishlist",
      "label": { "en": "Show button 'Add to wishlist'", "ja": "お気に入り追加ボタンを表示"},
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_button_add_to_compare",
      "label": { "en": "Show button 'Add to compare'", "ja": "比較リスト追加ボタンを表示"},
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_button_dynamic_checkout",
      "label": { "en": "Show dynamic checkout button", "ja": "動的チェックアウトボタンを表示"},
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_dynamic_checkout_confirmation",
      "label": { "en": "Show dynamic checkout confirmation", "ja": "動的チェックアウトの確認を表示"},
      "default": false
    },
    {
      "type": "checkbox",
      "id": "styled_dynamic_checkout",
      "label": { "en": "Custom style for dynamic checkout button", "ja": "動的チェックアウトアウトボタンのカスタムスタイル"},
      "default": false
    },
    {
      "type": "header",
      "content": { "en": "Background image", "ja": "背景画像"}
    },
    {
      "type": "image_picker",
      "id": "bg_image",
      "label": { "en": "Background image", "ja": "背景画像"}
    },
    {
    "type": "select",
    "id": "height",
    "label": { "en": "Image height", "ja": "画像の高さ"},
    "default": "auto",
    "options": [
    {
    "value": "auto",
    "label": { "en": "Auto adaptation to the image", "ja": "画像の高さに応じる - オート"}
    },
    {
    "value": "fullscreen",
    "label": { "en": "Fullscreen", "ja": "フルスクリーン"}
    },
    {
    "value": "fullscreen_header",
    "label": { "en": "Fullscreen excluding header", "ja": "ヘッダーを除くフルスクリーン"}
    }
    ]
    },
    {
      "type": "image_picker",
      "id": "bg_image_mobile",
      "label": { "en": "Mobile background image", "ja": "モバイルでの背景画像"}
    },
    {
"type": "header",
"content": { "en": "Support", "ja": "ビデオチュートリアルとユーザーマニュアル"}
},
    {
      "type": "paragraph",
      "content": "[MISEル YouTube](https:\/\/www.youtube.com\/channel\/UChCFvSvr3YgbYDcumJ_OqzQ)"
    },
    {
      "type": "paragraph",
      "content": "[単一商品](https:\/\/myoops.gitbook.io\/misell-theme\/sections\/one-product)"
    }
  ],
  "presets": [
      {
          "name": { "en":"One-Product" , "ja": "単一商品"},
          "category": "4) Collections"
      }
  ]
}
{% endschema %}