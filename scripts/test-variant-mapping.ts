import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

// Shopify Admin API設定
const SHOPIFY_SHOP = process.env.SHOPIFY_SHOP;
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

/**
 * variant_mappingメタフィールドの活用テスト
 */

// 1. 商品のバリアント情報を取得してマッピングを生成
async function generateVariantMapping(productId: string) {
  const query = `
    query getProductVariants($id: ID!) {
      product(id: $id) {
        id
        title
        variants(first: 20) {
          edges {
            node {
              id
              title
              sku
              price
            }
          }
        }
      }
    }
  `;

  const response = await fetch(`https://${SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN!
    },
    body: JSON.stringify({ query, variables: { id: `gid://shopify/Product/${productId}` } })
  });

  const result = await response.json();
  const product = result.data?.product;

  if (!product) {
    throw new Error('商品が見つかりません');
  }

  // バリアントマッピングを生成
  const variantMapping = {
    productId: product.id,
    productTitle: product.title,
    variants: product.variants.edges.map((edge: any) => {
      const variant = edge.node;
      const rentalDays = extractRentalDays(variant.title);
      
      return {
        variantId: variant.id,
        title: variant.title,
        sku: variant.sku,
        price: parseFloat(variant.price),
        rentalDays: rentalDays,
        rentalType: getRentalType(rentalDays)
      };
    })
  };

  return variantMapping;
}

// タイトルからレンタル日数を抽出
function extractRentalDays(title: string): number | string {
  if (title.includes('仮予約')) return 'provisional';
  if (title.includes('8日以上')) return '8plus';
  
  const match = title.match(/(\d+)日/);
  return match ? parseInt(match[1]) : 0;
}

// レンタルタイプを取得
function getRentalType(days: number | string): string {
  if (days === 'provisional') return 'PROVISIONAL';
  if (days === '8plus') return 'EXTENDED';
  return 'STANDARD';
}

// 2. 予約に基づいて適切なバリアントを選択
async function selectVariantForBooking(
  productId: string, 
  rentalDays: number,
  isProvisional: boolean = false
) {
  // 実際にはメタフィールドから取得するが、ここではデモ用に生成
  const variantMapping = await generateVariantMapping(productId);
  
  if (isProvisional) {
    // 仮予約の場合
    const provisionalVariant = variantMapping.variants.find(
      v => v.rentalDays === 'provisional'
    );
    return provisionalVariant || null;
  }
  
  // 通常予約の場合
  let targetVariant;
  
  if (rentalDays >= 8) {
    // 8日以上
    targetVariant = variantMapping.variants.find(
      v => v.rentalDays === '8plus'
    );
  } else {
    // 1-7日
    targetVariant = variantMapping.variants.find(
      v => v.rentalDays === rentalDays
    );
  }
  
  return targetVariant || null;
}

// 3. 価格計算
function calculatePrice(variant: any, rentalDays: number): number {
  if (variant.rentalDays === 'provisional') {
    // 仮予約は基本料金の10%
    return variant.price;
  }
  
  if (variant.rentalDays === '8plus' && rentalDays > 8) {
    // 8日以上の場合、7日分 + 追加日数料金
    const basePrice = variant.price * 7; // 基本料金を逆算
    const additionalDays = rentalDays - 7;
    const additionalPrice = variant.price * additionalDays;
    return basePrice + additionalPrice;
  }
  
  return variant.price;
}

// 4. メタフィールドを設定
async function setVariantMappingMetafield(productId: string, mapping: any) {
  const mutation = `
    mutation setMetafields($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          key
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const metafields = [{
    ownerId: `gid://shopify/Product/${productId}`,
    namespace: 'rental',
    key: 'variant_mapping',
    value: JSON.stringify(mapping),
    type: 'json'
  }];

  const response = await fetch(`https://${SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN!
    },
    body: JSON.stringify({ mutation, variables: { metafields } })
  });

  const result = await response.json();
  return result.data?.metafieldsSet;
}

// テスト実行
async function testVariantMapping() {
  console.log('=== variant_mapping活用テスト ===\n');

  try {
    // テスト用商品ID（実際の商品IDに置き換えてください）
    const testProductId = '8981670658216';
    
    console.log('1. バリアントマッピングを生成...');
    const mapping = await generateVariantMapping(testProductId);
    console.log('生成されたマッピング:');
    console.log(JSON.stringify(mapping, null, 2));
    
    console.log('\n2. 予約に基づくバリアント選択テスト...');
    
    // 3日間レンタル
    const variant3days = await selectVariantForBooking(testProductId, 3);
    console.log(`\n3日間レンタル → ${variant3days?.title} (${variant3days?.sku})`);
    console.log(`価格: ¥${variant3days?.price}`);
    
    // 10日間レンタル
    const variant10days = await selectVariantForBooking(testProductId, 10);
    console.log(`\n10日間レンタル → ${variant10days?.title} (${variant10days?.sku})`);
    const price10days = calculatePrice(variant10days, 10);
    console.log(`価格: ¥${price10days} (7日分 + 3日分追加)`);
    
    // 仮予約
    const variantProvisional = await selectVariantForBooking(testProductId, 0, true);
    console.log(`\n仮予約 → ${variantProvisional?.title} (${variantProvisional?.sku})`);
    console.log(`価格: ¥${variantProvisional?.price}`);
    
    console.log('\n3. variant_mappingの活用メリット:');
    console.log('- レンタル日数から自動的に適切なバリアントを選択');
    console.log('- 価格計算の一元管理');
    console.log('- 予約作成時の処理を簡素化');
    console.log('- バリアント情報の可視化');
    
  } catch (error) {
    console.error('エラー:', error);
  }
}

// 実際の使用例
async function createBookingWithVariantMapping(
  productId: string,
  customerId: string,
  startDate: Date,
  endDate: Date,
  isProvisional: boolean = false
) {
  // レンタル日数を計算
  const rentalDays = Math.ceil(
    (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
  );
  
  // 適切なバリアントを選択
  const variant = await selectVariantForBooking(productId, rentalDays, isProvisional);
  
  if (!variant) {
    throw new Error('適切なバリアントが見つかりません');
  }
  
  // 価格を計算
  const totalPrice = calculatePrice(variant, rentalDays);
  
  // 予約データを作成
  const bookingData = {
    productId,
    customerId,
    variantId: variant.variantId,
    sku: variant.sku,
    startDate,
    endDate,
    rentalDays,
    price: totalPrice,
    bookingType: isProvisional ? 'PROVISIONAL' : 'CONFIRMED'
  };
  
  console.log('\n予約データ:');
  console.log(bookingData);
  
  return bookingData;
}

async function main() {
  try {
    await testVariantMapping();
    
    // 実際の使用例
    console.log('\n\n=== 実際の使用例 ===');
    const booking = await createBookingWithVariantMapping(
      '8981670658216',
      'customer-123',
      new Date('2025-06-01'),
      new Date('2025-06-05'),
      false
    );
    
  } catch (error) {
    console.error('エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 実行
if (require.main === module) {
  main();
}

export { generateVariantMapping, selectVariantForBooking, calculatePrice };