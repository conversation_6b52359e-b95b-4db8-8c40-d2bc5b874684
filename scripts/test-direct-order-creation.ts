/**
 * 通常注文作成テストスクリプト
 *
 * このスクリプトは、Shopify Admin APIを使用して通常注文を直接作成します。
 * ドラフト注文を経由せず、直接注文を作成することで、write_ordersスコープが正しく機能しているかをテストします。
 *
 * 実行方法: npx tsx scripts/test-direct-order-creation.ts
 */

import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Shopify GraphQL APIクライアントの初期化
function createShopifyClient() {
  const shop = process.env.SHOPIFY_SHOP;
  const accessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

  if (!shop || !accessToken) {
    throw new Error('SHOPIFY_SHOP または SHOPIFY_ADMIN_API_ACCESS_TOKEN が設定されていません');
  }

  return new GraphQLClient(
    `https://${shop}/admin/api/2025-01/graphql.json`,
    {
      headers: {
        'X-Shopify-Access-Token': accessToken,
        'Content-Type': 'application/json',
      },
    }
  );
}

// 通常注文作成ミューテーション
const CREATE_ORDER = gql`
  mutation orderCreate($order: OrderCreateOrderInput!) {
    orderCreate(order: $order) {
      order {
        id
        name
        totalPrice
        customer {
          id
          email
        }
        lineItems(first: 10) {
          edges {
            node {
              title
              quantity
              originalUnitPrice
              variant {
                id
                title
              }
            }
          }
        }
        metafields(first: 10) {
          edges {
            node {
              namespace
              key
              value
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 通常注文作成テスト
async function testDirectOrderCreation() {
  try {
    console.log('通常注文作成テストを開始します...');

    // Shopify APIクライアントを作成
    const client = createShopifyClient();

    // 顧客IDを取得（環境変数から）
    const customerId = process.env.TEST_CUSTOMER_ID;
    if (!customerId) {
      throw new Error('TEST_CUSTOMER_ID が設定されていません');
    }

    // 商品IDを取得（環境変数から）
    const productId = process.env.TEST_PRODUCT_ID;
    const variantId = process.env.TEST_VARIANT_ID;
    if (!productId || !variantId) {
      throw new Error('TEST_PRODUCT_ID または TEST_VARIANT_ID が設定されていません');
    }

    console.log(`テスト情報: 顧客ID=${customerId}, 商品ID=${productId}, バリアントID=${variantId}`);

    // 顧客IDを正規化
    const normalizedCustomerId = customerId.startsWith('gid://shopify/Customer/')
      ? customerId
      : `gid://shopify/Customer/${customerId}`;

    // 現在の日時を取得
    const now = new Date();
    const startDate = new Date(now);
    startDate.setDate(startDate.getDate() + 1); // 明日から

    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 3); // 3日間

    // 日付をフォーマット
    const formatDate = (date: Date) => {
      return date.toISOString().split('T')[0];
    };

    const rentalPeriod = `${formatDate(startDate)} 〜 ${formatDate(endDate)}`;

    // 通常注文作成用の入力データを作成
    const orderInput = {
      customerId: normalizedCustomerId,
      lineItems: [
        {
          title: `テスト商品 (レンタル: ${rentalPeriod})`,
          quantity: 1,
          variantId: variantId,
          taxable: true,
          requiresShipping: false
        }
      ],
      tags: ['rental', 'test', 'direct-order'],
      note: `テスト予約\n予約期間: ${rentalPeriod}\n備考: 直接注文作成テスト`,
      metafields: [
        {
          namespace: "custom",
          key: "booking_id",
          value: "test-direct-" + Date.now(),
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "booking_number",
          value: "DIRECT-" + Math.floor(Math.random() * 10000),
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "booking_type",
          value: "CONFIRMED",
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "rental_period",
          value: rentalPeriod,
          type: "single_line_text_field"
        }
      ]
    };

    console.log('通常注文作成データ:', JSON.stringify(orderInput, null, 2));

    // 通常注文を作成
    console.log('通常注文を作成中...');
    const orderResponse = await client.request(CREATE_ORDER, {
      order: orderInput
    });

    console.log('通常注文作成応答:', JSON.stringify(orderResponse, null, 2));

    if (orderResponse.orderCreate.userErrors.length > 0) {
      console.error('通常注文作成エラー:', orderResponse.orderCreate.userErrors);
      throw new Error(`通常注文の作成中にエラーが発生しました: ${JSON.stringify(orderResponse.orderCreate.userErrors)}`);
    }

    // 注文IDを取得
    const orderId = orderResponse.orderCreate.order.id;
    const orderName = orderResponse.orderCreate.order.name;

    console.log(`注文が作成されました: 注文ID ${orderId}, 注文番号 ${orderName}`);
    console.log(`Shopify管理画面で確認: https://${process.env.SHOPIFY_SHOP}/admin/orders/${orderName.replace('#', '')}`);

    return {
      success: true,
      orderId,
      orderName
    };
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    throw error;
  }
}

// スクリプトを実行
testDirectOrderCreation()
  .then(() => {
    console.log('テストが完了しました');
    process.exit(0);
  })
  .catch((error) => {
    console.error('テストが失敗しました:', error);
    process.exit(1);
  });
