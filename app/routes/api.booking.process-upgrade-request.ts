/**
 * 仮予約から本予約への変更リクエスト処理API
 * 
 * 仮予約から本予約への変更リクエストを処理するAPIエンドポイント
 * POST /api/booking/process-upgrade-request
 */

import { json, type ActionFunctionArgs } from "@remix-run/node";
import { prisma } from "~/db.server";
import { logger } from "~/utils/logger";
import { sendEmail } from "~/utils/email";
import { formatErrorMessage } from "~/utils/error-utils";
import { format } from "date-fns";
import { ja } from "date-fns/locale";
import { BookingService } from "~/services/booking.service";

/**
 * 仮予約から本予約への変更リクエストを処理するアクション
 */
export async function action({ request }: ActionFunctionArgs) {
  try {
    // リクエストボディを取得
    const body = await request.json();
    const { 
      upgradeRequestId, 
      action, 
      adminNotes,
      adminId
    } = body;

    // 必須パラメータのチェック
    if (!upgradeRequestId) {
      return json({ success: false, error: "変更リクエストIDは必須です" }, { status: 400 });
    }

    if (!action || !['approve', 'reject', 'cancel'].includes(action)) {
      return json({ success: false, error: "アクションは approve, reject, cancel のいずれかである必要があります" }, { status: 400 });
    }

    // 変更リクエスト情報を取得
    const upgradeRequest = await prisma.bookingUpgradeRequest.findUnique({
      where: { id: upgradeRequestId }
    });

    if (!upgradeRequest) {
      return json({ success: false, error: "変更リクエストが見つかりません" }, { status: 404 });
    }

    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: upgradeRequest.bookingId }
    });

    if (!booking) {
      return json({ success: false, error: "予約が見つかりません" }, { status: 404 });
    }

    // 商品情報を取得
    const product = await prisma.product.findUnique({
      where: { id: booking.productId }
    });

    if (!product) {
      return json({ success: false, error: "商品が見つかりません" }, { status: 404 });
    }

    // BookingServiceを初期化
    const bookingService = new BookingService();

    // アクションに応じた処理
    let result;
    let newStatus;
    let statusMessage;

    switch (action) {
      case 'approve':
        // 仮予約を本予約に変更
        result = await bookingService.updateBookingStatus(booking.id, 'CONFIRMED', {
          notes: `仮予約から本予約への変更リクエストが承認されました。${adminNotes || ''}`
        });
        newStatus = 'APPROVED';
        statusMessage = '承認されました';
        break;
      
      case 'reject':
        // 変更リクエストを拒否
        result = { success: true, booking };
        newStatus = 'REJECTED';
        statusMessage = '拒否されました';
        break;
      
      case 'cancel':
        // 変更リクエストをキャンセル
        result = { success: true, booking };
        newStatus = 'CANCELLED';
        statusMessage = 'キャンセルされました';
        break;
    }

    if (!result.success) {
      return json({ 
        success: false, 
        error: result.error || "処理中にエラーが発生しました",
        booking
      }, { status: 500 });
    }

    // 変更リクエスト情報を更新
    const updatedRequest = await prisma.bookingUpgradeRequest.update({
      where: { id: upgradeRequest.id },
      data: {
        status: newStatus,
        adminNotes: adminNotes || null,
        processedAt: new Date(),
        processedBy: adminId || null,
        updatedAt: new Date()
      }
    });

    // リクエスト者にメール通知
    try {
      if (upgradeRequest.requesterEmail) {
        const emailResult = await sendEmail({
          to: upgradeRequest.requesterEmail,
          subject: `【お知らせ】仮予約から本予約への変更リクエストが${statusMessage}（予約番号: ${booking.bookingId}）`,
          text: `
${upgradeRequest.requesterName || "お客様"}

いつもご利用ありがとうございます。

商品「${product.title}」（予約番号: ${booking.bookingId}）の
仮予約から本予約への変更リクエストが${statusMessage}。

■ 予約情報
予約番号: ${booking.bookingId}
商品名: ${product.title}
予約期間: ${format(new Date(booking.startDate), 'yyyy年MM月dd日', { locale: ja })} 〜 ${format(new Date(booking.endDate), 'yyyy年MM月dd日', { locale: ja })}

${action === 'approve' ? '本予約への変更が完了しました。' : ''}
${adminNotes ? `\n備考: ${adminNotes}` : ''}

ご不明な点がございましたら、お気軽にお問い合わせください。

よろしくお願いいたします。
          `,
          html: `
<p>${upgradeRequest.requesterName || "お客様"}</p>

<p>いつもご利用ありがとうございます。</p>

<p>商品「${product.title}」（予約番号: ${booking.bookingId}）の<br>
仮予約から本予約への変更リクエストが${statusMessage}。</p>

<h3>予約情報</h3>
<ul>
  <li>予約番号: ${booking.bookingId}</li>
  <li>商品名: ${product.title}</li>
  <li>予約期間: ${format(new Date(booking.startDate), 'yyyy年MM月dd日', { locale: ja })} 〜 ${format(new Date(booking.endDate), 'yyyy年MM月dd日', { locale: ja })}</li>
</ul>

${action === 'approve' ? '<p>本予約への変更が完了しました。</p>' : ''}
${adminNotes ? `<p>備考: ${adminNotes}</p>` : ''}

<p>ご不明な点がございましたら、お気軽にお問い合わせください。</p>

<p>よろしくお願いいたします。</p>
          `
        });

        logger.info(`リクエスト者への通知メール送信: ${upgradeRequest.requesterEmail}`, emailResult);
      }
    } catch (emailError) {
      logger.error("リクエスト者への通知メール送信エラー:", emailError);
      // メール送信エラーは処理を続行
    }

    // 仮予約している顧客にメール通知（承認または拒否の場合）
    if (action !== 'cancel' && booking.customerEmail) {
      try {
        const emailResult = await sendEmail({
          to: booking.customerEmail,
          subject: `【お知らせ】仮予約から本予約への変更リクエストが${statusMessage}（予約番号: ${booking.bookingId}）`,
          text: `
${booking.customerName || "お客様"}

いつもご利用ありがとうございます。

あなたが仮予約中の商品「${product.title}」（予約番号: ${booking.bookingId}）について、
別のお客様からの本予約リクエストが${statusMessage}。

■ 予約情報
予約番号: ${booking.bookingId}
商品名: ${product.title}
予約期間: ${format(new Date(booking.startDate), 'yyyy年MM月dd日', { locale: ja })} 〜 ${format(new Date(booking.endDate), 'yyyy年MM月dd日', { locale: ja })}

${action === 'approve' ? '仮予約は本予約に変更されました。' : '仮予約は引き続き有効です。'}
${adminNotes ? `\n備考: ${adminNotes}` : ''}

ご不明な点がございましたら、お気軽にお問い合わせください。

よろしくお願いいたします。
          `,
          html: `
<p>${booking.customerName || "お客様"}</p>

<p>いつもご利用ありがとうございます。</p>

<p>あなたが仮予約中の商品「${product.title}」（予約番号: ${booking.bookingId}）について、<br>
別のお客様からの本予約リクエストが${statusMessage}。</p>

<h3>予約情報</h3>
<ul>
  <li>予約番号: ${booking.bookingId}</li>
  <li>商品名: ${product.title}</li>
  <li>予約期間: ${format(new Date(booking.startDate), 'yyyy年MM月dd日', { locale: ja })} 〜 ${format(new Date(booking.endDate), 'yyyy年MM月dd日', { locale: ja })}</li>
</ul>

<p>${action === 'approve' ? '仮予約は本予約に変更されました。' : '仮予約は引き続き有効です。'}</p>
${adminNotes ? `<p>備考: ${adminNotes}</p>` : ''}

<p>ご不明な点がございましたら、お気軽にお問い合わせください。</p>

<p>よろしくお願いいたします。</p>
          `
        });

        logger.info(`仮予約顧客への通知メール送信: ${booking.customerEmail}`, emailResult);
      } catch (emailError) {
        logger.error("仮予約顧客への通知メール送信エラー:", emailError);
        // メール送信エラーは処理を続行
      }
    }

    return json({ 
      success: true, 
      message: `仮予約から本予約への変更リクエストが${statusMessage}`,
      upgradeRequest: updatedRequest,
      booking: result.booking
    });
  } catch (error) {
    logger.error("仮予約から本予約への変更リクエスト処理エラー:", error);
    return json({ 
      success: false, 
      error: formatErrorMessage(error) 
    }, { status: 500 });
  }
}
