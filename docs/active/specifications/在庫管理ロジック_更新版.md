# レンタル商品在庫管理ロジック

## 概要

このドキュメントでは、レンタル商品ECシステムにおける在庫管理ロジックについて詳細に説明します。Shopifyとデータベースを連携させた在庫管理の実装方法、メンテナンス状態の管理、および予約状態の管理について解説します。

## 基本方針

### ユニーク商品の管理

本システムでは、すべての商品はユニークであり（一部バリエーションとしてユニーク）、在庫数は0か1（利用不可または利用可能）で管理します。

- **在庫1（isAvailable = true）**: 商品がレンタル可能な状態
- **在庫0（isAvailable = false）**: 商品がレンタル不可能な状態

### 在庫0となる条件

以下のいずれかの条件に該当する場合、商品の在庫は0（レンタル不可）となります：

1. **予約中（unavailableReason = "reserved"）**: 他の顧客によって予約されている
2. **メンテナンス中（unavailableReason = "maintenance"）**: 点検・修理・クリーニングなどのメンテナンス中
3. **その他の理由（unavailableReason = "other"）**: 展示用、社内利用中、一時的に取り扱い停止など

### 在庫状態の変更タイミング

1. **予約確定時**: 予約期間中の各日付の在庫を0に設定（unavailableReason = "reserved"）
2. **予約キャンセル時**: 該当予約の期間中の各日付の在庫を1に戻す
3. **メンテナンス設定時**: メンテナンス期間中の各日付の在庫を0に設定（unavailableReason = "maintenance"）
4. **メンテナンス完了時**: 該当期間の各日付の在庫を1に戻す（他の予約がない場合）

## 実装方法

### 在庫カレンダーモデル

在庫状態を日付ごとに管理するための`InventoryCalendar`モデルを使用します：

```typescript
// prisma/schema.prisma
model InventoryCalendar {
  id                String          @id @default(cuid())
  shop              String
  shopifyProductId  String          // Shopify商品ID
  date              DateTime        // 対象日
  totalStock        Int             // 総在庫数
  availableStock    Int             // 利用可能在庫数
  reservedStock     Int             // 予約済み在庫数
  maintenanceStock  Int             // メンテナンス中在庫数

  // タイムスタンプ
  createdAt         DateTime        @default(now()) @map("created_at")
  updatedAt         DateTime        @updatedAt @map("updated_at")

  @@unique([shop, shopifyProductId, date])
  @@index([shop, date])
  @@map("inventory_calendar")
}
```

### レンタル可能期間モデル

商品ごとのレンタル可能期間を管理するための`RentalAvailability`モデルを使用します：

```typescript
// prisma/schema.prisma
model RentalAvailability {
  id                String          @id @default(cuid())
  shop              String
  shopifyProductId  String          // Shopify商品ID
  startDate         DateTime        // レンタル可能開始日
  endDate           DateTime        // レンタル可能終了日
  isAvailable       Boolean         @default(true) // 利用可能フラグ
  notes             String?         // 備考

  // タイムスタンプ
  createdAt         DateTime        @default(now()) @map("created_at")
  updatedAt         DateTime        @updatedAt @map("updated_at")

  @@index([shop, shopifyProductId])
  @@index([startDate, endDate])
  @@map("rental_availabilities")
}
```

### メンテナンス状態の管理

メンテナンス状態は以下のように管理します：

1. **メタフィールドによる管理**:
   - `rental.maintenance_status`: メンテナンス状態（良好、点検中、修理中、清掃中、廃棄予定）
   - `rental.last_maintenance_date`: 最終メンテナンス日
   - `rental.next_maintenance_date`: 次回メンテナンス予定日
   - `rental.maintenance_notes`: メンテナンス備考

2. **メンテナンス履歴の管理**:
   - メンテナンス履歴をデータベースで管理
   - メンテナンス種類、担当者、詳細情報を記録

### 在庫チェックロジック

指定された期間に商品が利用可能かどうかをチェックする関数：

```typescript
/**
 * 指定された期間に商品が利用可能かチェックする関数
 * @param productId 商品ID
 * @param startDate 開始日
 * @param endDate 終了日
 * @returns 利用可能かどうか
 */
export async function isDateRangeAvailable(
  productId: string,
  startDate: Date,
  endDate: Date
): Promise<boolean> {
  try {
    // レンタル可能期間をチェック
    const availability = await prisma.rentalAvailability.findFirst({
      where: {
        shopifyProductId: productId,
        startDate: { lte: startDate },
        endDate: { gte: endDate },
        isAvailable: true
      }
    });

    if (!availability) {
      return false; // レンタル可能期間外
    }

    // 日付範囲内の在庫カレンダーを取得
    const inventoryCalendars = await prisma.inventoryCalendar.findMany({
      where: {
        shopifyProductId: productId,
        date: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    // 各日の在庫状況をチェック
    for (const calendar of inventoryCalendars) {
      if (calendar.availableStock <= 0) {
        return false; // 在庫不足
      }
    }

    // 既存の予約をチェック
    const existingBookings = await prisma.booking.findMany({
      where: {
        productId: productId,
        status: { in: ['PROVISIONAL', 'CONFIRMED'] },
        OR: [
          {
            startDate: { lte: endDate },
            endDate: { gte: startDate }
          }
        ]
      }
    });

    if (existingBookings.length > 0) {
      return false; // 既存の予約と重複
    }

    return true; // 利用可能
  } catch (error) {
    console.error('Failed to check date range availability:', error);
    return false;
  }
}
```

### 在庫更新ロジック

予約確定時やキャンセル時に在庫状態を更新する関数：

```typescript
/**
 * 予約確定時に在庫を更新する関数
 * @param booking 予約情報
 */
export async function updateInventoryForBooking(booking: Booking): Promise<void> {
  try {
    const { productId, startDate, endDate, status } = booking;
    
    // 予約期間の日付範囲を取得
    const dateRange = getDateRange(startDate, endDate);
    
    // 予約がキャンセルされた場合
    const isCancel = status === 'CANCELLED';
    
    // 各日の在庫を更新
    for (const date of dateRange) {
      // 在庫カレンダーを取得または作成
      let inventory = await prisma.inventoryCalendar.findFirst({
        where: {
          shopifyProductId: productId,
          date: date
        }
      });
      
      if (!inventory) {
        // 在庫カレンダーが存在しない場合は作成
        inventory = await prisma.inventoryCalendar.create({
          data: {
            shopifyProductId: productId,
            date: date,
            totalStock: 1,
            availableStock: 1,
            reservedStock: 0,
            maintenanceStock: 0
          }
        });
      }
      
      // 在庫を更新
      await prisma.inventoryCalendar.update({
        where: { id: inventory.id },
        data: {
          reservedStock: isCancel 
            ? Math.max(0, inventory.reservedStock - 1) 
            : inventory.reservedStock + 1,
          availableStock: isCancel 
            ? inventory.availableStock + 1 
            : Math.max(0, inventory.availableStock - 1)
        }
      });
    }
    
    // Shopify在庫も更新
    await updateShopifyInventory(productId, !isCancel);
  } catch (error) {
    console.error('Failed to update inventory for booking:', error);
    throw new Error(`Failed to update inventory: ${error.message}`);
  }
}

/**
 * Shopify在庫を更新する関数
 * @param productId 商品ID
 * @param decrease 在庫を減らすかどうか
 */
async function updateShopifyInventory(
  productId: string, 
  decrease: boolean
): Promise<void> {
  try {
    // Shopify Admin APIを使用して在庫を更新
    // ...
  } catch (error) {
    console.error('Failed to update Shopify inventory:', error);
    throw new Error(`Failed to update Shopify inventory: ${error.message}`);
  }
}
```

### メンテナンス状態更新ロジック

メンテナンス状態を更新する関数：

```typescript
/**
 * メンテナンス状態を更新する関数
 * @param productId 商品ID
 * @param status メンテナンス状態
 * @param startDate メンテナンス開始日
 * @param endDate メンテナンス終了日
 * @param notes メンテナンス備考
 */
export async function updateMaintenanceStatus(
  productId: string,
  status: string,
  startDate?: Date,
  endDate?: Date,
  notes?: string
): Promise<void> {
  try {
    // メンテナンス情報を更新
    await prisma.product.update({
      where: { id: productId },
      data: {
        maintenanceInfo: {
          status,
          startDate: startDate?.toISOString(),
          endDate: endDate?.toISOString(),
          notes
        }
      }
    });
    
    // メンテナンス履歴を記録
    await prisma.maintenanceHistory.create({
      data: {
        productId,
        status,
        startDate,
        endDate,
        notes
      }
    });
    
    // メンテナンス期間中の在庫を更新
    if (startDate && endDate) {
      const dateRange = getDateRange(startDate, endDate);
      
      for (const date of dateRange) {
        // 在庫カレンダーを取得または作成
        let inventory = await prisma.inventoryCalendar.findFirst({
          where: {
            shopifyProductId: productId,
            date: date
          }
        });
        
        if (!inventory) {
          // 在庫カレンダーが存在しない場合は作成
          inventory = await prisma.inventoryCalendar.create({
            data: {
              shopifyProductId: productId,
              date: date,
              totalStock: 1,
              availableStock: 1,
              reservedStock: 0,
              maintenanceStock: 0
            }
          });
        }
        
        // メンテナンス状態に応じて在庫を更新
        const isMaintenance = status !== '良好';
        
        await prisma.inventoryCalendar.update({
          where: { id: inventory.id },
          data: {
            maintenanceStock: isMaintenance 
              ? 1 
              : 0,
            availableStock: isMaintenance 
              ? 0 
              : (inventory.reservedStock > 0 ? 0 : 1)
          }
        });
      }
    }
    
    // Shopify在庫も更新
    await updateShopifyInventory(productId, status !== '良好');
  } catch (error) {
    console.error('Failed to update maintenance status:', error);
    throw new Error(`Failed to update maintenance status: ${error.message}`);
  }
}
```

## 在庫状態の視覚化

### カレンダーUIでの表示

在庫状態をカレンダーUIで視覚化する例：

```typescript
// 在庫状態に応じた色分け
const getDateCellColor = (date: Date, inventoryStatus: InventoryStatus) => {
  if (!inventoryStatus) {
    return 'gray'; // データなし
  }
  
  if (inventoryStatus.maintenanceStock > 0) {
    return 'orange'; // メンテナンス中
  }
  
  if (inventoryStatus.reservedStock > 0) {
    return 'red'; // 予約済み
  }
  
  if (inventoryStatus.availableStock > 0) {
    return 'green'; // 利用可能
  }
  
  return 'gray'; // その他
};

// ホバー時の詳細情報表示
const getDateTooltip = (date: Date, inventoryStatus: InventoryStatus) => {
  if (!inventoryStatus) {
    return '情報なし';
  }
  
  let tooltip = `${formatDate(date)}\n`;
  
  if (inventoryStatus.maintenanceStock > 0) {
    tooltip += 'メンテナンス中\n';
  }
  
  if (inventoryStatus.reservedStock > 0) {
    tooltip += `予約数: ${inventoryStatus.reservedStock}\n`;
  }
  
  tooltip += `利用可能数: ${inventoryStatus.availableStock}`;
  
  return tooltip;
};
```

## メンテナンス履歴の管理

### メンテナンス履歴モデル

メンテナンス履歴を管理するための`MaintenanceHistory`モデルを追加します：

```typescript
// prisma/schema.prisma
model MaintenanceHistory {
  id                String          @id @default(cuid())
  shop              String
  productId         String
  status            String          // メンテナンス状態
  startDate         DateTime?       // 開始日
  endDate           DateTime?       // 終了日
  notes             String?         // 備考
  staffId           String?         // 担当者ID
  
  // リレーション
  product           Product         @relation(fields: [productId], references: [id])
  staff             Staff?          @relation(fields: [staffId], references: [id])
  
  // タイムスタンプ
  createdAt         DateTime        @default(now()) @map("created_at")
  updatedAt         DateTime        @updatedAt @map("updated_at")
  
  @@index([productId])
  @@index([staffId])
  @@map("maintenance_history")
}
```

### メンテナンス予定モデル

メンテナンス予定を管理するための`MaintenanceSchedule`モデルを追加します：

```typescript
// prisma/schema.prisma
model MaintenanceSchedule {
  id                String          @id @default(cuid())
  shop              String
  productId         String
  type              String          // メンテナンスタイプ（定期点検、修理、クリーニングなど）
  scheduledDate     DateTime        // 予定日
  status            String          // 状態（予定、完了、キャンセルなど）
  notes             String?         // 備考
  staffId           String?         // 担当者ID
  
  // リレーション
  product           Product         @relation(fields: [productId], references: [id])
  staff             Staff?          @relation(fields: [staffId], references: [id])
  
  // タイムスタンプ
  createdAt         DateTime        @default(now()) @map("created_at")
  updatedAt         DateTime        @updatedAt @map("updated_at")
  
  @@index([productId])
  @@index([staffId])
  @@index([scheduledDate])
  @@map("maintenance_schedules")
}
```

## バリエーション管理

### バリエーショングループの管理

バリエーションをユニーク商品として管理するための設定：

1. **メタフィールドによる管理**:
   - `rental.variation_group`: バリエーショングループID
   - `rental.variation_type`: バリエーションタイプ（色、サイズなど）

2. **バリエーション間の関連付け**:
   - 同じバリエーショングループIDを持つ商品は同じ商品の異なるバリエーション
   - バリエーションタイプによって区別（例：「色：ブラック」「色：ホワイト」）

### バリエーション検索ロジック

バリエーションを検索する関数：

```typescript
/**
 * 同じバリエーショングループの商品を検索する関数
 * @param productId 商品ID
 * @returns 同じグループの商品リスト
 */
export async function findVariationProducts(
  productId: string
): Promise<Product[]> {
  try {
    // 対象商品のバリエーショングループIDを取得
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: {
        metadata: true
      }
    });
    
    if (!product || !product.metadata || !product.metadata.basicInfo) {
      return [];
    }
    
    const variationGroup = product.metadata.basicInfo.variationGroup;
    
    if (!variationGroup) {
      return [];
    }
    
    // 同じバリエーショングループの商品を検索
    const variations = await prisma.product.findMany({
      where: {
        metadata: {
          path: ['basicInfo', 'variationGroup'],
          equals: variationGroup
        },
        id: { not: productId } // 自分自身を除外
      }
    });
    
    return variations;
  } catch (error) {
    console.error('Failed to find variation products:', error);
    return [];
  }
}
```

## まとめ

このドキュメントでは、レンタル商品ECシステムにおける在庫管理ロジックについて詳細に説明しました。ユニーク商品の管理、在庫状態の変更タイミング、メンテナンス状態の管理、およびバリエーション管理について解説しました。

在庫管理は、レンタル商品ECシステムの中核となる機能であり、正確な在庫状態の管理と視覚化が重要です。本システムでは、Shopifyとデータベースを連携させることで、柔軟かつ堅牢な在庫管理を実現しています。

今後の拡張として、メンテナンス履歴の詳細管理、メンテナンス予定の管理、およびバリエーション管理の強化を計画しています。これにより、より効率的な在庫管理と運用が可能になります。
