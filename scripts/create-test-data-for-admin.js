/**
 * テスト用データ作成スクリプト
 *
 * このスクリプトは、バックエンド管理画面のテスト用に、以下のデータを作成します：
 * 1. 商品データ（バリエーション、カテゴリ、在庫場所、ステータスなど）
 * 2. 顧客データ
 * 3. 予約データ
 */

import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

dotenv.config();
const prisma = new PrismaClient();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品作成のGraphQLミューテーション
const CREATE_PRODUCT = gql`
  mutation productCreate($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールド設定のGraphQLミューテーション
const SET_METAFIELDS = gql`
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 商品バリアント作成のGraphQLミューテーション
const CREATE_VARIANT = gql`
  mutation productVariantCreate($input: ProductVariantInput!) {
    productVariantCreate(input: $input) {
      productVariant {
        id
        title
        sku
        price
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 顧客作成のGraphQLミューテーション
const CREATE_CUSTOMER = gql`
  mutation customerCreate($input: CustomerInput!) {
    customerCreate(input: $input) {
      customer {
        id
        firstName
        lastName
        email
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// テスト商品データ
const testProducts = [
  {
    title: 'テスト椅子 ベーシック',
    description: 'テスト用の基本的な椅子です。',
    productType: 'チェア',
    vendor: 'テストベンダー',
    tags: ['テスト', 'チェア', 'ベーシック'],
    variants: [
      { title: '1日レンタル', price: '1000', sku: 'CHAIR-BASIC-1D', inventoryQuantity: 1 },
      { title: '2日レンタル', price: '1800', sku: 'CHAIR-BASIC-2D', inventoryQuantity: 1 },
      { title: '3日レンタル', price: '2400', sku: 'CHAIR-BASIC-3D', inventoryQuantity: 1 },
      { title: '4日レンタル', price: '2800', sku: 'CHAIR-BASIC-4D', inventoryQuantity: 1 },
      { title: '5日レンタル', price: '3000', sku: 'CHAIR-BASIC-5D', inventoryQuantity: 1 },
      { title: '6日レンタル', price: '3200', sku: 'CHAIR-BASIC-6D', inventoryQuantity: 1 },
      { title: '7日レンタル', price: '3400', sku: 'CHAIR-BASIC-7D', inventoryQuantity: 1 },
      { title: '8日以上レンタル', price: '3600', sku: 'CHAIR-BASIC-8D', inventoryQuantity: 1 }
    ],
    metafields: {
      basic_info: {
        product_code: 'CHAIR-BASIC',
        detail_code: '001',
        status: 'available',
        inventory_location: 'NY'
      },
      pricing: {
        base_price: 1000,
        discount_rules: {
          '2-7': 0.8,
          '8+': 0.9
        }
      }
    }
  },
  {
    title: 'テスト椅子 カラーバリエーション',
    description: 'テスト用のカラーバリエーションがある椅子です。',
    productType: 'チェア',
    vendor: 'テストベンダー',
    tags: ['テスト', 'チェア', 'カラーバリエーション'],
    variants: [
      { title: 'レッド - 1日レンタル', price: '1200', sku: 'CHAIR-COLOR-RED-1D', inventoryQuantity: 1 },
      { title: 'レッド - 2日レンタル', price: '2160', sku: 'CHAIR-COLOR-RED-2D', inventoryQuantity: 1 },
      { title: 'ブルー - 1日レンタル', price: '1200', sku: 'CHAIR-COLOR-BLUE-1D', inventoryQuantity: 1 },
      { title: 'ブルー - 2日レンタル', price: '2160', sku: 'CHAIR-COLOR-BLUE-2D', inventoryQuantity: 1 },
      { title: 'グリーン - 1日レンタル', price: '1200', sku: 'CHAIR-COLOR-GREEN-1D', inventoryQuantity: 1 },
      { title: 'グリーン - 2日レンタル', price: '2160', sku: 'CHAIR-COLOR-GREEN-2D', inventoryQuantity: 1 }
    ],
    metafields: {
      basic_info: {
        product_code: 'CHAIR-COLOR',
        detail_code: '002',
        status: 'available',
        inventory_location: 'NY'
      },
      pricing: {
        base_price: 1200,
        discount_rules: {
          '2-7': 0.8,
          '8+': 0.9
        }
      }
    }
  },
  {
    title: 'テスト椅子 メンテナンス中',
    description: 'テスト用のメンテナンス中の椅子です。',
    productType: 'チェア',
    vendor: 'テストベンダー',
    tags: ['テスト', 'チェア', 'メンテナンス'],
    variants: [
      { title: '1日レンタル', price: '1000', sku: 'CHAIR-MAINTENANCE-1D', inventoryQuantity: 0 }
    ],
    metafields: {
      basic_info: {
        product_code: 'CHAIR-MAINTENANCE',
        detail_code: '003',
        status: 'maintenance',
        inventory_location: 'NY'
      }
    }
  },
  {
    title: 'テスト椅子 廃棄予定',
    description: 'テスト用の廃棄予定の椅子です。',
    productType: 'チェア',
    vendor: 'テストベンダー',
    tags: ['テスト', 'チェア', '廃棄'],
    variants: [
      { title: '1日レンタル', price: '1000', sku: 'CHAIR-DISPOSAL-1D', inventoryQuantity: 0 }
    ],
    metafields: {
      basic_info: {
        product_code: 'CHAIR-DISPOSAL',
        detail_code: '004',
        status: 'unavailable',
        inventory_location: 'NY'
      }
    }
  },
  {
    title: 'テストテーブル PR在庫',
    description: 'テスト用のPR在庫のテーブルです。',
    productType: 'テーブル',
    vendor: 'テストベンダー',
    tags: ['テスト', 'テーブル', 'PR'],
    variants: [
      { title: '1日レンタル', price: '2000', sku: 'TABLE-PR-1D', inventoryQuantity: 1 },
      { title: '2日レンタル', price: '3600', sku: 'TABLE-PR-2D', inventoryQuantity: 1 }
    ],
    metafields: {
      basic_info: {
        product_code: 'TABLE-PR',
        detail_code: '005',
        status: 'available',
        inventory_location: 'PR'
      },
      pricing: {
        base_price: 2000,
        discount_rules: {
          '2-7': 0.8,
          '8+': 0.9
        }
      }
    }
  }
];

// テスト顧客データ
const testCustomers = [
  {
    lastName: '山田',
    firstName: '太郎',
    email: '<EMAIL>',
    phone: '090-1234-5678',
    addresses: [
      {
        address1: '東京都渋谷区渋谷1-1-1',
        city: '渋谷区',
        province: '東京都',
        zip: '150-0002',
        country: 'JP',
        lastName: '山田',
        firstName: '太郎'
      }
    ]
  },
  {
    lastName: '佐藤',
    firstName: '花子',
    email: '<EMAIL>',
    phone: '090-8765-4321',
    addresses: [
      {
        address1: '大阪府大阪市北区梅田2-2-2',
        city: '大阪市北区',
        province: '大阪府',
        zip: '530-0001',
        country: 'JP',
        lastName: '佐藤',
        firstName: '花子'
      }
    ]
  },
  {
    lastName: '鈴木',
    firstName: '一郎',
    email: '<EMAIL>',
    phone: '090-2345-6789',
    addresses: [
      {
        address1: '愛知県名古屋市中区栄3-3-3',
        city: '名古屋市中区',
        province: '愛知県',
        zip: '460-0008',
        country: 'JP',
        lastName: '鈴木',
        firstName: '一郎'
      }
    ]
  }
];

// 商品を作成する関数
async function createProduct(productData) {
  try {
    console.log(`商品「${productData.title}」を作成中...`);

    // 基本的な商品情報を作成
    const result = await client.request(CREATE_PRODUCT, {
      input: {
        title: productData.title,
        descriptionHtml: productData.description,
        productType: productData.productType,
        vendor: productData.vendor,
        tags: productData.tags,
        options: ['レンタル期間'],
        variants: [
          {
            price: productData.variants[0].price,
            sku: productData.variants[0].sku,
            inventoryManagement: 'SHOPIFY',
            inventoryPolicy: 'DENY',
            options: [productData.variants[0].title]
          }
        ]
      }
    });

    if (result.productCreate.userErrors.length > 0) {
      console.error(`商品「${productData.title}」の作成中にエラーが発生しました:`, result.productCreate.userErrors);
      return null;
    }

    const productId = result.productCreate.product.id;
    console.log(`商品「${productData.title}」を作成しました。ID: ${productId}`);

    // メタフィールドを設定
    if (productData.metafields) {
      const metafields = [];

      for (const namespace in productData.metafields) {
        const value = JSON.stringify(productData.metafields[namespace]);

        metafields.push({
          ownerId: productId,
          namespace,
          key: namespace,
          value,
          type: 'json'
        });
      }

      const metafieldsResult = await client.request(SET_METAFIELDS, {
        metafields
      });

      if (metafieldsResult.metafieldsSet.userErrors.length > 0) {
        console.error(`商品「${productData.title}」のメタフィールド設定中にエラーが発生しました:`, metafieldsResult.metafieldsSet.userErrors);
      } else {
        console.log(`商品「${productData.title}」のメタフィールドを設定しました。`);
      }
    }

    // 追加のバリアントを作成
    for (let i = 1; i < productData.variants.length; i++) {
      const variant = productData.variants[i];

      const variantResult = await client.request(CREATE_VARIANT, {
        input: {
          productId,
          price: variant.price,
          sku: variant.sku,
          inventoryManagement: 'SHOPIFY',
          inventoryPolicy: 'DENY',
          options: [variant.title]
        }
      });

      if (variantResult.productVariantCreate.userErrors.length > 0) {
        console.error(`バリアント「${variant.title}」の作成中にエラーが発生しました:`, variantResult.productVariantCreate.userErrors);
      } else {
        console.log(`バリアント「${variant.title}」を作成しました。`);
      }

      // APIレート制限対策
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    return productId;
  } catch (error) {
    console.error(`商品「${productData.title}」の作成中にエラーが発生しました:`, error);
    return null;
  }
}

// 顧客を作成する関数
async function createCustomer(customerData) {
  try {
    console.log(`顧客「${customerData.lastName} ${customerData.firstName}」を作成中...`);

    const result = await client.request(CREATE_CUSTOMER, {
      input: customerData
    });

    if (result.customerCreate.userErrors.length > 0) {
      console.error(`顧客「${customerData.lastName} ${customerData.firstName}」の作成中にエラーが発生しました:`, result.customerCreate.userErrors);
      return null;
    }

    const customerId = result.customerCreate.customer.id;
    console.log(`顧客「${customerData.lastName} ${customerData.firstName}」を作成しました。ID: ${customerId}`);

    return customerId;
  } catch (error) {
    console.error(`顧客「${customerData.lastName} ${customerData.firstName}」の作成中にエラーが発生しました:`, error);
    return null;
  }
}

// メイン処理
async function main() {
  console.log('テストデータ作成を開始します...');

  // 商品データを作成
  console.log('商品データを作成しています...');
  const productIds = [];

  for (const product of testProducts) {
    const productId = await createProduct(product);
    if (productId) {
      productIds.push(productId);
    }

    // APIレート制限対策
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // 顧客データはすでに作成済みのためスキップ
  console.log('顧客データはすでに作成済みのためスキップします。');
  const customerIds = [
    'gid://shopify/Customer/8420391289000',
    'gid://shopify/Customer/8420391321768',
    'gid://shopify/Customer/8420391387304'
  ];

  console.log('テストデータ作成が完了しました。');
  console.log(`作成した商品: ${productIds.length}件`);
  console.log(`作成した顧客: ${customerIds.length}件`);
}

// スクリプト実行
main().catch(error => {
  console.error('スクリプト実行中にエラーが発生しました:', error);
  process.exit(1);
}).finally(async () => {
  await prisma.$disconnect();
});
