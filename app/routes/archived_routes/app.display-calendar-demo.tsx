import React, { useState } from 'react';
import { Page, BlockStack } from '@shopify/polaris';
import { DisplayCalendar } from '../components/Calendar/DisplayCalendar';

// 日付に日数を追加する関数
function addDays(date: Date, days: number): Date {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

export default function DisplayCalendarDemo() {
  // 現在の日付
  const today = new Date();
  
  // サンプル予約データ
  const [bookings] = useState([
    {
      id: '1',
      startDate: addDays(today, 3),
      endDate: addDays(today, 5),
      status: 'PROVISIONAL' as const
    },
    {
      id: '2',
      startDate: addDays(today, 10),
      endDate: addDays(today, 12),
      status: 'CONFIRMED' as const
    },
    {
      id: '3',
      startDate: addDays(today, 20),
      endDate: addDays(today, 22),
      status: 'CONFIRMED' as const
    }
  ]);
  
  // サンプルメンテナンス期間
  const [maintenancePeriods] = useState([
    {
      id: '1',
      startDate: addDays(today, 15),
      endDate: addDays(today, 18)
    }
  ]);
  
  return (
    <Page title="表示専用カレンダーデモ">
      <BlockStack gap="4">
        <DisplayCalendar
          bookings={bookings}
          maintenancePeriods={maintenancePeriods}
          title="商品の予約状況"
          description="このカレンダーでは、商品の予約状況を確認できます。仮予約（黄色）、本予約（緑色）、メンテナンス（灰色）、休業日（赤色）が表示されます。"
          showLegend={true}
          numberOfMonths={3}
        />
      </BlockStack>
    </Page>
  );
}
