{"roo-code.customPrompts": [{"name": "🛒 Remix Loader (Admin API)", "description": "選択したGraphQLクエリを使い、認証済みのRemix Loader関数を生成", "prompt": "あなたはShopify Remixアプリ開発のエキスパートです。以下のShopify Admin GraphQLクエリ ({selectedText}) を使用して、認証チェック (`admin.authenticate.admin(request)`) を行い、データを取得するRemixの `loader` 関数 (TypeScript) を生成してください。エラーハンドリング（認証エラー、APIエラー）も含めてください。取得したデータは適切に型付けし、`json` ヘルパーで返却してください。\n```typescript\n// Example structure:\nimport { LoaderFunctionArgs, json } from \"@remix-run/node\";\nimport { admin } from \"../shopify.server\"; // Adjust path if needed\n\nexport const loader = async ({ request }: LoaderFunctionArgs) => {\n  const { admin: adminClient, session } = await admin.authenticate.admin(request);\n\n  const query = `{selectedText}`;\n\n  try {\n    const response = await adminClient.graphql(query);\n    const data = await response.json();\n\n    if (data.errors || !data.data) {\n      console.error(\"GraphQL Errors:\", data.errors);\n      throw new Response(\"Failed to fetch data from Shopify\", { status: 500 });\n    }\n\n    // TODO: Add specific type based on the query\n    return json({ data: data.data });\n\n  } catch (error) {\n    console.error(\"Loader Error:\", error);\n    if (error instanceof Response) {\n        throw error;\n    }\n    // Handle other potential errors (e.g., network issues)\n    throw new Response(\"Internal Server Error\", { status: 500 });\n  }\n};\n```\n上記を参考に、選択されたクエリに合わせた完全な `loader` 関数を生成してください。"}, {"name": "🛠️ Remix Action (Admin API Mutation)", "description": "選択したGraphQLミューテーションを使い、認証済みのRemix Action関数を生成", "prompt": "あなたはShopify Remixアプリ開発のエキスパートです。以下のShopify Admin GraphQLミューテーション ({selectedText}) を使用して、認証チェック (`admin.authenticate.admin(request)`) を行い、データを更新/作成するRemixの `action` 関数 (TypeScript) を生成してください。フォームデータ (`request.formData()`) の受け取りとパース、ミューテーション変数へのマッピング、エラーハンドリング（認証、API、バリデーション）、成功時のリダイレクトまたはJSONレスポンスを含めてください。\n```typescript\n// Example structure:\nimport { ActionFunctionArgs, json, redirect } from \"@remix-run/node\";\nimport { admin } from \"../shopify.server\"; // Adjust path if needed\n// import { ZodSchema } from 'zod'; // Optional: For validation\n\nexport const action = async ({ request }: ActionFunctionArgs) => {\n  const { admin: adminClient, session } = await admin.authenticate.admin(request);\n  const formData = await request.formData();\n\n  // TODO: Parse and validate formData\n  // const parsedData = YourZodSchema.parse(Object.fromEntries(formData));\n  const variables = { /* map formData to variables */ };\n\n  const mutation = `{selectedText}`;\n\n  try {\n    const response = await adminClient.graphql(mutation, { variables });\n    const data = await response.json();\n\n    if (data.errors || !data.data /* Check specific mutation result */) {\n      console.error(\"GraphQL Errors:\", data.errors);\n      return json({ errors: data.errors || [{ message: \"Mutation failed\" }] }, { status: 400 });\n    }\n\n    // TODO: Adjust redirect path or return JSON\n    return redirect(\"/app\"); // Or return json({ success: true });\n\n  } catch (error) {\n    console.error(\"Action Error:\", error);\n    // Handle validation errors, API errors etc.\n    if (error instanceof Response) throw error;\n    if (error.name === 'ZodError') return json({ errors: error.errors }, { status: 400 });\n    return json({ errors: [{ message: \"Internal Server Error\" }] }, { status: 500 });\n  }\n};\n```\n上記を参考に、選択されたミューテーションと想定されるフォームデータに合わせた完全な `action` 関数を生成してください。"}, {"name": "🎣 Webhook Handler 生成", "description": "指定したShopify Webhookトピックの基本的なハンドラ関数を生成", "prompt": "あなたはShopify Remixアプリ開発のエキスパートです。Shopify Webhook を処理するための基本的なハンドラ関数 (TypeScript) を生成してください。対象のWebhookトピックは `{selectedText}` です。`shopify.server.ts` の `registerWebhooks` で登録することを想定し、`context.admin.graphql` を使用して関連データを取得または更新する基本的な構造と、エラーハンドリングを含めてください。\n```typescript\n// Example for webhook handler function (to be registered in shopify.server.ts)\nimport { DeliveryMethod } from \"@shopify/shopify-api\";\nimport type { WebhookHandler } from \"@shopify/shopify-app-remix\";\n\nconst {selectedText}_HANDLER: WebhookHandler = {\n  deliveryMethod: DeliveryMethod.Http,\n  callbackUrl: \"/webhooks\", // Ensure this matches your setup\n  callback: async (topic, shop, body, webhookId, context) => {\n    console.log(`Received webhook: ${topic} for shop ${shop}`);\n    const payload = JSON.parse(body);\n\n    try {\n      // TODO: Process the payload and potentially interact with Shopify API\n      // Example: Fetch related data\n      /*\n      const response = await context.admin.graphql(\n        `#graphql\n        query shopInfo {\n          shop {\n            name\n          }\n        }`\n      );\n      const shopData = await response.json();\n      console.log('Shop Data:', shopData.data.shop.name);\n      */\n      console.log('Webhook processed successfully:', webhookId);\n\n    } catch (error) {\n      console.error(`Error processing ${topic} webhook for ${shop}:`, error);\n      // Optionally, re-throw to signal failure if necessary\n    }\n  },\n};\n\nexport default {selectedText}_HANDLER;\n```\n上記を参考に、`{selectedText}` トピック用の完全なハンドラ関数ファイルを生成してください。"}, {"name": "🎨 Polaris コンポーネント実装", "description": "要件に基づいて適切なPolarisコンポーネントを使ったReactコードを生成", "prompt": "あなたはReactとShopify Polaris Design Systemのエキスパートです。以下の要件を満たすReactコンポーネント (TypeScript, Function Component) を、Shopify Polarisコンポーネント（`@shopify/polaris`）を適切に使用して実装してください。状態管理が必要な場合は `useState` を使用し、基本的なアクセシビリティも考慮してください。\n\n要件:\n{selectedText}\n\n考慮すべきPolarisコンポーネントの例: `Page`, `Layout`, `Card`, `Form`, `TextField`, `Button`, `DataTable`, `ResourceList`, `Modal`, `Toast` など。"}, {"name": "🌉 App Bridge スニペット生成", "description": "指定したApp Bridge機能を使うためのReactコードスニペット生成", "prompt": "あなたはShopify App Bridgeのエキスパートです。Reactコンポーネント内でShopify App Bridgeの `{selectedText}` 機能を使用するための、基本的なコードスニペット (TypeScript) を生成してください。`@shopify/app-bridge-react` の `useAppBridge` フックを使用し、必要なアクションを呼び出す例を示してください。\n\n例 (`{selectedText}` が `Toast` の場合): \n```typescript\nimport { useAppBridge } from \"@shopify/app-bridge-react\";\nimport { Button } from \"@shopify/polaris\";\nimport { Toast } from '@shopify/app-bridge/actions';\n\nfunction MyComponent() {\n  const app = useAppBridge();\n\n  const showToast = () => {\n    const toastNotice = Toast.create(app, {\n      message: 'Action completed successfully!',\n      duration: 5000,\n    });\n    toastNotice.dispatch(Toast.Action.SHOW);\n  };\n\n  return <Button onClick={showToast}>Show Toast</Button>;\n}\n```\n上記を参考に、`{selectedText}` に対応するApp Bridgeアクションのコードスニペットを生成してください。"}, {"name": "✅ Remix Loader/Action テスト生成 (Vitest)", "description": "選択したRemix Loader/Action関数の基本的なVitestテストコードを生成", "prompt": "あなたはRemixとVitestによるテストのエキスパートです。以下のRemixの `loader` または `action` 関数 ({selectedText}) に対する基本的な単体テストコード (TypeScript, Vitest) を生成してください。`@shopify/shopify-app-remix/testing` のヘルパー（`authenticateAdmin` など）や `vi.mock` を使用して、Shopify認証とAPIコールをモック化し、正常系と基本的なエラー系のテストケースを含めてください。\n```typescript\n// Example structure (adjust imports and mocks as needed)\nimport { describe, it, expect, vi } from 'vitest';\nimport { loader, action } from './your-route-file'; // Adjust path\nimport { authenticateAdmin } from '@shopify/shopify-app-remix/testing';\n// import { rest } from 'msw'; // If using MSW for API mocking\n// import { server } from '~/mocks/server'; // If using MSW\n\nvi.mock('../shopify.server', () => ({ // Mock shopify.server module\n  admin: {\n    authenticate: {\n      admin: vi.fn(),\n    },\n    graphql: vi.fn(), // Mock graphql client if needed\n  },\n}));\n\ndescribe('Your Route Loader/Action', () => {\n  // --- Loader Tests ---\n  describe('loader', () => {\n    it('should return data on success', async () => {\n      // Arrange\n      const mockRequest = new Request('https://test.myshopify.io/app/your-route');\n      const { admin: mockAdmin } = await authenticateAdmin(mockRequest); // Use testing helper\n      (vi.mocked(mockAdmin.graphql) as any).mockResolvedValueOnce({\n        json: async () => ({ data: { /* Mock your GraphQL response */ } }),\n      });\n\n      // Act\n      const response = await loader({ request: mockRequest, context: {}, params: {} });\n      const data = await response.json();\n\n      // Assert\n      expect(response.status).toBe(200);\n      expect(data).toEqual({ data: { /* Expected data structure */ } });\n      expect(mockAdmin.graphql).toHaveBeenCalledWith(/* Expected GraphQL query */);\n    });\n\n    it('should throw 500 on API error', async () => {\n      // Arrange\n      const mockRequest = new Request('https://test.myshopify.io/app/your-route');\n      const { admin: mockAdmin } = await authenticateAdmin(mockRequest);\n      (vi.mocked(mockAdmin.graphql) as any).mockResolvedValueOnce({\n        json: async () => ({ errors: [{ message: 'API Error' }] }),\n      });\n\n      // Act & Assert\n      await expect(loader({ request: mockRequest, context: {}, params: {} })).rejects.toThrow(Response);\n      // Optionally check status code if possible\n    });\n  });\n\n  // --- Action Tests ---\n  describe('action', () => {\n    it('should redirect on successful mutation', async () => {\n      // Arrange\n      const formData = new FormData();\n      formData.append('field1', 'value1');\n      const mockRequest = new Request('https://test.myshopify.io/app/your-route', {\n        method: 'POST',\n        body: formData,\n      });\n      const { admin: mockAdmin } = await authenticateAdmin(mockRequest);\n       (vi.mocked(mockAdmin.graphql) as any).mockResolvedValueOnce({\n         json: async () => ({ data: { /* Mock your mutation response */ } }),\n       });\n\n      // Act\n      const response = await action({ request: mockRequest, context: {}, params: {} });\n\n      // Assert\n      expect(response.status).toBe(302); // Assuming redirect\n      expect(response.headers.get('Location')).toBe('/app'); // Adjust path\n      expect(mockAdmin.graphql).toHaveBeenCalledWith(/* Expected mutation query */, { variables: { /* Expected variables */ } });\n    });\n\n     it('should return 400 on validation/API error', async () => {\n       // Arrange\n       const formData = new FormData(); // Potentially invalid data\n       const mockRequest = new Request('https://test.myshopify.io/app/your-route', {\n         method: 'POST',\n         body: formData,\n       });\n       const { admin: mockAdmin } = await authenticateAdmin(mockRequest);\n       (vi.mocked(mockAdmin.graphql) as any).mockResolvedValueOnce({\n         json: async () => ({ errors: [{ message: 'Invalid input' }] }),\n       });\n\n       // Act\n       const response = await action({ request: mockRequest, context: {}, params: {} });\n       const data = await response.json();\n\n       // Assert\n       expect(response.status).toBe(400);\n       expect(data.errors).toBeDefined();\n     });\n  });\n});\n```\n上記を参考に、選択された `{selectedText}` に対応するテストコードを生成してください。"}, {"name": "⚙️ TS 型定義生成 (from JSON/GraphQL)", "description": "選択したJSONデータやGraphQLスキーマから基本的なTypeScript型定義を生成", "prompt": "あなたはTypeScriptのエキスパートです。以下のJSONデータまたはGraphQLスキーマ ({selectedText}) を解析し、対応する基本的なTypeScriptのインターフェースまたは型エイリアスを生成してください。ネストされた構造も可能な限り正確に型付けしてください。\n```typescript\n// Generated Type Definition(s)\n\n{generated_code}\n```"}, {"name": "✨ Shopify/Remix コードリファクタリング", "description": "選択したShopify/Remixコードをベストプラクティスに沿って改善", "prompt": "あなたはShopify Remixアプリ開発のベストプラクティスに精通したシニアエンジニアです。以下のコード ({selectedText}) をレビューし、可読性、保守性、パフォーマンス、エラーハンドリング、Shopify/Remixの慣習（例: 適切なloader/actionの使用、Polarisの活用、認証処理）の観点から改善点を指摘し、リファクタリング後のコードを提案してください。"}]}