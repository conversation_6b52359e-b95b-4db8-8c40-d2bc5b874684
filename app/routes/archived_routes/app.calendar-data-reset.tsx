import { json } from "@remix-run/node";
import { useLoaderData, useSubmit, Form } from "@remix-run/react";
import {
  Page,
  BlockStack,
  Card,
  Button,
  Banner,
  Text,
  Box,
  InlineStack,
  Divider
} from "@shopify/polaris";
import { useState } from "react";
import type { ActionFunctionArgs } from "@remix-run/node";
import { clearCalendarData } from "~/scripts/clear-calendar-data";
import { createCalendarTestData } from "~/scripts/create-calendar-test-data";

/**
 * カレンダーデータリセットページ
 * 既存のカレンダーデータを削除し、新しいテストデータを作成する
 */

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const action = formData.get("action");

  try {
    if (action === "clear") {
      // カレンダーデータを削除
      const result = await clearCalendarData();
      return json({ success: true, action: "clear", result });
    } else if (action === "create") {
      // テストデータを作成
      const result = await createCalendarTestData();
      return json({ success: true, action: "create", result });
    } else if (action === "reset") {
      // 削除してから作成
      const clearResult = await clearCalendarData();
      const createResult = await createCalendarTestData();
      return json({ 
        success: true, 
        action: "reset", 
        clearResult,
        createResult
      });
    }

    return json({ success: false, error: "無効なアクション" });
  } catch (error) {
    console.error("エラー:", error);
    return json({ success: false, error: String(error) });
  }
}

export default function CalendarDataReset() {
  const actionData = useLoaderData<typeof action>();
  const [isLoading, setIsLoading] = useState(false);
  const submit = useSubmit();

  const handleClear = () => {
    if (confirm("すべてのカレンダーデータを削除します。よろしいですか？")) {
      setIsLoading(true);
      const formData = new FormData();
      formData.append("action", "clear");
      submit(formData, { method: "post" });
    }
  };

  const handleCreate = () => {
    if (confirm("新しいテストデータを作成します。よろしいですか？")) {
      setIsLoading(true);
      const formData = new FormData();
      formData.append("action", "create");
      submit(formData, { method: "post" });
    }
  };

  const handleReset = () => {
    if (confirm("すべてのカレンダーデータを削除して新しいテストデータを作成します。よろしいですか？")) {
      setIsLoading(true);
      const formData = new FormData();
      formData.append("action", "reset");
      submit(formData, { method: "post" });
    }
  };

  return (
    <Page title="カレンダーデータリセット">
      <BlockStack gap="4">
        <Card>
          <BlockStack gap="4">
            <Text variant="headingMd" as="h2">カレンダーデータの管理</Text>
            <Text>このページでは、カレンダー関連のデータを管理できます。データを削除したり、テストデータを作成したりすることができます。</Text>
            
            <Divider />
            
            <BlockStack gap="4">
              <InlineStack gap="4">
                <Button
                  destructive
                  onClick={handleClear}
                  loading={isLoading && !actionData}
                  disabled={isLoading && !actionData}
                >
                  すべてのカレンダーデータを削除
                </Button>
                
                <Button
                  primary
                  onClick={handleCreate}
                  loading={isLoading && !actionData}
                  disabled={isLoading && !actionData}
                >
                  テストデータを作成
                </Button>
                
                <Button
                  onClick={handleReset}
                  loading={isLoading && !actionData}
                  disabled={isLoading && !actionData}
                >
                  削除してテストデータを作成
                </Button>
              </InlineStack>
              
              <Text variant="bodySm" as="p" color="subdued">
                注意: データの削除は元に戻せません。必要に応じてバックアップを取ってください。
              </Text>
            </BlockStack>
          </BlockStack>
        </Card>

        {actionData?.success && actionData.action === "clear" && (
          <Card>
            <BlockStack gap="4">
              <Banner status="success" title="データ削除完了">
                <p>カレンダーデータの削除が完了しました。</p>
              </Banner>
              
              <Box paddingBlockStart="4">
                <BlockStack gap="2">
                  <Text variant="headingMd" as="h3">削除結果</Text>
                  <Text variant="bodyMd" as="p">InventoryCalendar: {actionData.result.deletedCounts.inventoryCalendars}件</Text>
                  <Text variant="bodyMd" as="p">Booking: {actionData.result.deletedCounts.bookings}件</Text>
                  <Text variant="bodyMd" as="p">BlackoutDate: {actionData.result.deletedCounts.blackoutDates}件</Text>
                  <Text variant="bodyMd" as="p">AvailableDates: {actionData.result.deletedCounts.availableDates}件</Text>
                  <Text variant="bodyMd" as="p">RentalAvailability: {actionData.result.deletedCounts.rentalAvailabilities}件</Text>
                </BlockStack>
              </Box>
            </BlockStack>
          </Card>
        )}

        {actionData?.success && actionData.action === "create" && (
          <Card>
            <BlockStack gap="4">
              <Banner status="success" title="テストデータ作成完了">
                <p>テストデータの作成が完了しました。</p>
              </Banner>
              
              <Box paddingBlockStart="4">
                <BlockStack gap="2">
                  <Text variant="headingMd" as="h3">作成結果</Text>
                  {actionData.result.results.map((result, index) => (
                    <Card key={index}>
                      <BlockStack gap="2">
                        <Text variant="headingMd" as="h4">{result.title} ({result.productId})</Text>
                        {result.success ? (
                          <BlockStack gap="2">
                            {result.actions.map((action, i) => (
                              <Text key={i} variant="bodyMd" as="p">• {action}</Text>
                            ))}
                          </BlockStack>
                        ) : (
                          <Banner status="critical">
                            <p>エラー: {result.error}</p>
                          </Banner>
                        )}
                      </BlockStack>
                    </Card>
                  ))}
                </BlockStack>
              </Box>
            </BlockStack>
          </Card>
        )}

        {actionData?.success && actionData.action === "reset" && (
          <Card>
            <BlockStack gap="4">
              <Banner status="success" title="データリセット完了">
                <p>カレンダーデータの削除とテストデータの作成が完了しました。</p>
              </Banner>
              
              <Box paddingBlockStart="4">
                <BlockStack gap="2">
                  <Text variant="headingMd" as="h3">削除結果</Text>
                  <Text variant="bodyMd" as="p">InventoryCalendar: {actionData.clearResult.deletedCounts.inventoryCalendars}件</Text>
                  <Text variant="bodyMd" as="p">Booking: {actionData.clearResult.deletedCounts.bookings}件</Text>
                  <Text variant="bodyMd" as="p">BlackoutDate: {actionData.clearResult.deletedCounts.blackoutDates}件</Text>
                  <Text variant="bodyMd" as="p">AvailableDates: {actionData.clearResult.deletedCounts.availableDates}件</Text>
                  <Text variant="bodyMd" as="p">RentalAvailability: {actionData.clearResult.deletedCounts.rentalAvailabilities}件</Text>
                </BlockStack>
              </Box>
              
              <Divider />
              
              <Box paddingBlockStart="4">
                <BlockStack gap="2">
                  <Text variant="headingMd" as="h3">テストデータ作成結果</Text>
                  {actionData.createResult.results.map((result, index) => (
                    <Card key={index}>
                      <BlockStack gap="2">
                        <Text variant="headingMd" as="h4">{result.title} ({result.productId})</Text>
                        {result.success ? (
                          <BlockStack gap="2">
                            {result.actions.map((action, i) => (
                              <Text key={i} variant="bodyMd" as="p">• {action}</Text>
                            ))}
                          </BlockStack>
                        ) : (
                          <Banner status="critical">
                            <p>エラー: {result.error}</p>
                          </Banner>
                        )}
                      </BlockStack>
                    </Card>
                  ))}
                </BlockStack>
              </Box>
            </BlockStack>
          </Card>
        )}

        {actionData?.success === false && (
          <Card>
            <Banner status="critical" title="エラー">
              <p>{actionData.error}</p>
            </Banner>
          </Card>
        )}
      </BlockStack>
    </Page>
  );
}
