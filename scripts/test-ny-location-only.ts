/**
 * NYロケーションのみのテスト
 */

import { authenticate } from '../app/shopify.server';
import { VariantAutoCreatorService } from '../app/services/shopify/variant-auto-creator.service';
import { GraphQLClient } from 'graphql-request';

async function testNYLocationOnly() {
  console.log('=== NYロケーション商品のみテスト開始 ===');

  try {
    // 環境変数から設定を取得
    const shopifyShop = process.env.SHOPIFY_SHOP;
    const shopifyAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

    if (!shopifyShop || !shopifyAccessToken) {
      console.error('❌ 環境変数が設定されていません');
      return;
    }

    // GraphQLClientを直接作成
    const graphqlClient = new GraphQLClient(
      `https://${shopifyShop}/admin/api/2025-01/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': shopifyAccessToken,
          'Content-Type': 'application/json',
        },
      }
    );

    // adminオブジェクトを作成
    const admin = {
      graphql: async (query: string, variables?: any) => {
        const result = await graphqlClient.request(query, variables);
        return {
          json: () => Promise.resolve({ data: result })
        };
      }
    };

    const variantService = new VariantAutoCreatorService();

    // NYロケーションのテスト商品
    const testProduct = {
      title: 'テスト商品 NYロケーション確認',
      handle: 'test-ny-location-only',
      productType: 'グラス',
      vendor: 'テストベンダー',
      tags: ['テスト', 'レンタル', 'NY'],
      status: 'ACTIVE',
      variants: [{
        title: 'デフォルト',
        price: '1000',
        sku: 'TEST-NY-001',
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY'
      }]
    };

    // 1. 商品作成
    console.log('1. NYロケーション商品を作成中...');
    const createResponse = await admin.graphql(`
      mutation productCreate($input: ProductInput!) {
        productCreate(input: $input) {
          product {
            id
            title
            handle
          }
          userErrors {
            field
            message
          }
        }
      }
    `, {
      input: testProduct
    });

    const createData = await createResponse.json();
    
    if (createData.errors || createData.data.productCreate.userErrors.length > 0) {
      throw new Error(`商品作成エラー: ${JSON.stringify(createData.errors || createData.data.productCreate.userErrors)}`);
    }

    const productId = createData.data.productCreate.product.id;
    const shopifyProductId = productId.split('/').pop();
    console.log(`✅ 商品作成成功: ${shopifyProductId}`);

    // 2. バリエーション自動作成（NYロケーション指定）
    console.log('\n2. バリエーション自動作成（NYロケーション指定）...');
    
    const result = await variantService.createMissingVariants(admin, shopifyProductId, {
      basePrice: 1000,
      productStatus: 'available',
      location: 'NY', // 明示的にNYを指定
      createProvisionalVariants: false
    });

    console.log('\n=== バリエーション作成結果 ===');
    console.log(`作成されたバリエーション数: ${result.createdVariants.length}`);
    console.log(`在庫設定: ${result.inventoryUpdated ? '✅ 成功' : '❌ 失敗'}`);
    console.log(`メタフィールド設定: ${result.metafieldsSet ? '✅ 成功' : '❌ 失敗'}`);
    console.log(`全体の成功: ${result.success ? '✅ 成功' : '❌ 失敗'}`);
    
    if (result.errors.length > 0) {
      console.log('\nエラー:');
      result.errors.forEach(error => console.log(`  - ${error}`));
    }

    // 3. メタフィールド確認
    console.log('\n3. メタフィールドを確認中...');
    const metafieldResponse = await admin.graphql(`
      query getProductMetafields($id: ID!) {
        product(id: $id) {
          id
          title
          metafields(first: 10) {
            edges {
              node {
                id
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    `, {
      id: productId
    });

    const metafieldData = await metafieldResponse.json();
    
    if (metafieldData.errors) {
      console.error('メタフィールド確認エラー:', metafieldData.errors);
    } else {
      const metafields = metafieldData.data.product.metafields.edges;
      console.log(`📋 メタフィールド数: ${metafields.length}`);
      
      metafields.forEach((metafieldEdge: any) => {
        const metafield = metafieldEdge.node;
        console.log(`  ${metafield.namespace}.${metafield.key}: ${metafield.value}`);
        
        // ロケーション情報を特に確認
        if (metafield.key === 'location') {
          console.log(`🎯 ロケーション設定: ${metafield.value}`);
        }
        if (metafield.key === 'basic_info') {
          try {
            const basicInfo = JSON.parse(metafield.value);
            console.log(`🎯 basic_info.location: ${basicInfo.location}`);
          } catch (e) {
            console.log(`⚠️ basic_info解析エラー: ${metafield.value}`);
          }
        }
      });
    }

    // 4. 結果評価
    console.log('\n=== テスト結果評価 ===');
    const expectedVariants = 8; // 1D, 2D, 3D, 4D, 5D, 6D, 7D, 8D+
    const actualVariants = result.createdVariants.length;
    
    console.log(`バリエーション作成: ${actualVariants}/${expectedVariants} ${actualVariants === expectedVariants ? '✅' : '❌'}`);
    console.log(`メタフィールド設定: ${result.metafieldsSet ? '✅' : '❌'}`);
    console.log(`在庫設定: ${result.inventoryUpdated ? '✅' : '❌'}`);

    if (actualVariants === expectedVariants && result.metafieldsSet) {
      console.log('\n🎉 NYロケーション設定テスト成功！');
      console.log('✅ バリエーション作成: 完璧');
      console.log('✅ メタフィールド設定: 完璧');
      console.log(`${result.inventoryUpdated ? '✅' : '⚠️'} 在庫設定: ${result.inventoryUpdated ? '完璧' : '要確認'}`);
      
      console.log('\n📋 次のステップ:');
      console.log('1. Shopify管理画面でロケーションがNYになっているか確認');
      console.log('2. 各バリエーションの在庫が1個に設定されているか確認');
      console.log(`3. 商品URL: https://peaces-test-block.myshopify.com/admin/products/${shopifyProductId}`);
    } else {
      console.log('\n⚠️ 一部のテストに問題があります');
    }

    // 5. クリーンアップ
    console.log('\n5. テスト商品を削除中...');
    await admin.graphql(`
      mutation productDelete($input: ProductDeleteInput!) {
        productDelete(input: $input) {
          deletedProductId
          userErrors {
            field
            message
          }
        }
      }
    `, {
      input: {
        id: productId
      }
    });

    console.log('✅ テスト商品削除完了');

  } catch (error) {
    console.error('❌ テストエラー:', error);
  }

  console.log('\n=== NYロケーション商品のみテスト完了 ===');
}

// スクリプト実行
if (import.meta.url === new URL(import.meta.url).href) {
  testNYLocationOnly().catch(console.error);
}

export { testNYLocationOnly };
