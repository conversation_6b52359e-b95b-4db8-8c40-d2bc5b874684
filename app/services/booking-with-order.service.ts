/**
 * 予約と注文の連携サービス
 * 
 * 予約情報をShopify注文として作成するための関数を提供します。
 */

import { prisma } from "~/db.server";
import { v4 as uuidv4 } from "uuid";
import { calculateRentalPrice, calculateRentalDays } from "~/utils/pricing";
import { logger } from "~/utils/logger";
import { createOrderFromBooking } from "~/utils/booking/order-creator";
import { AdminApiContext } from "@shopify/shopify-app-remix/server";
import { format } from "date-fns";

/**
 * ShopifyのGIDを作成する
 */
function createShopifyGid(type: string, id: string): string {
  return `gid://shopify/${type}/${id}`;
}

/**
 * 予約と注文を同時に作成する
 * 
 * @param params 予約作成パラメータ
 * @returns 作成された予約と注文の情報
 */
export async function createBookingWithDraftOrder(params: {
  shop: string;
  productId: string;
  variantId: string;
  startDate: string;
  endDate: string;
  customerEmail: string;
  customerName: string;
  customerId?: string;
  notes?: string;
  basePrice?: number;
  bookingType?: "PROVISIONAL" | "CONFIRMED";
  admin: AdminApiContext;
}) {
  const {
    shop,
    productId,
    variantId,
    startDate,
    endDate,
    customerEmail,
    customerName,
    customerId,
    notes = "",
    basePrice,
    bookingType = "PROVISIONAL",
    admin
  } = params;

  try {
    logger.info("予約と注文の作成を開始:", { productId, startDate, endDate });

    // デフォルトの商品ID（商品IDが指定されていない場合に使用）
    const defaultProductId = "cm9qrryuu0000jvcj3nvwf919"; // テスト用のデフォルト商品ID

    // 1. 日付の検証
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
      return {
        success: false,
        error: "無効な日付形式です"
      };
    }

    if (startDateObj > endDateObj) {
      return {
        success: false,
        error: "開始日は終了日より前である必要があります"
      };
    }

    // 2. 商品情報を取得
    let product = null;
    try {
      if (productId) {
        product = await prisma.product.findUnique({
          where: { id: productId }
        });
      }
    } catch (error) {
      logger.warn(`商品情報の取得に失敗しました (ID: ${productId})`, error);
    }

    // 3. レンタル料金を計算
    const rentalDays = calculateRentalDays(startDateObj, endDateObj);
    let totalPrice = 0;

    if (basePrice) {
      // 基本料金が指定されている場合はそれを使用
      totalPrice = basePrice;
    } else if (product?.price) {
      // 商品の価格から計算
      totalPrice = calculateRentalPrice(product.price, rentalDays);
    } else {
      // デフォルト価格（テスト用）
      totalPrice = calculateRentalPrice(5000, rentalDays);
    }

    // 仮予約の場合はデポジット金額（10%）を設定
    const depositAmount = totalPrice * 0.1;

    // 4. 予約IDを生成
    const bookingId = `BOOK-${Date.now().toString().substring(6)}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;

    // 5. データベースに予約情報を保存
    const booking = await prisma.booking.create({
      data: {
        bookingId,
        shop,
        productId: productId || defaultProductId,
        variantId,
        startDate: startDateObj,
        endDate: endDateObj,
        status: bookingType,
        bookingType,
        customerEmail,
        customerName,
        customerId: customerId || null,
        totalAmount: totalPrice,
        depositAmount,
        notes,
        metadata: { notes },
        priority: 1
      },
      include: {
        product: true
      }
    });

    logger.info(`予約情報をデータベースに保存しました: ${booking.id}`);

    // 6. Shopify注文を作成
    try {
      logger.info("Shopify注文の作成を開始...");
      
      const orderResult = await createOrderFromBooking(
        prisma,
        admin,
        booking.id
      );

      if (orderResult.success) {
        logger.info(`Shopify注文の作成に成功しました: ${orderResult.orderId}`);
        
        // 予約情報を更新（注文IDを保存）
        await prisma.booking.update({
          where: { id: booking.id },
          data: {
            shopifyOrderId: orderResult.orderId,
            shopifyOrderName: orderResult.orderName
          }
        });

        return {
          success: true,
          booking: {
            ...booking,
            shopifyOrderId: orderResult.orderId,
            shopifyOrderName: orderResult.orderName
          },
          draftOrder: {
            id: orderResult.orderId,
            name: orderResult.orderName,
            totalPrice: totalPrice.toString(),
            isDraftOrder: orderResult.isDraftOrder || false
          },
          totalPrice
        };
      } else {
        logger.error("Shopify注文の作成に失敗しました:", orderResult.error);
        
        return {
          success: true, // 予約自体は成功
          booking,
          error: "Shopify注文の作成に失敗しましたが、予約は保存されました",
          details: orderResult.error,
          totalPrice
        };
      }
    } catch (orderError) {
      logger.error("Shopify注文作成エラー:", orderError);
      
      return {
        success: true, // 予約自体は成功
        booking,
        error: "Shopify注文の作成中にエラーが発生しましたが、予約は保存されました",
        details: orderError instanceof Error ? orderError.message : String(orderError),
        totalPrice
      };
    }
  } catch (error) {
    logger.error("予約作成エラー:", error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : "予期せぬエラーが発生しました",
      details: error
    };
  }
}
