# 商品カテゴリ管理システム

商品カテゴリとSKUの連動管理システムの実装手順とスクリプト使用方法

## 概要

このシステムは以下の機能を提供します：

1. **SKU構造の標準化**: `[カテゴリコード]-[サブカテゴリ]-[連番]` 形式
2. **カテゴリマスタ管理**: 階層化されたカテゴリ構造
3. **自動SKU生成**: カテゴリに基づく自動連番生成
4. **データ整合性検証**: SKUとカテゴリの連動チェック

## ファイル構成

```
scripts/category-management/
├── README.md                           # このファイル
├── 01_analyze_excel_categories.js      # カテゴリ構造分析
├── 02_create_unified_master.js         # 統合マスタ作成
└── 03_import_master_data.js            # データベースインポート

app/services/
└── category-master.service.ts          # カテゴリ管理サービス

app/routes/
└── api.categories.tsx                  # カテゴリAPI

docs/active/specifications/
└── 商品カテゴリ管理システム設計.md      # 設計書
```

## 実行手順

### 1. 前提条件

- Node.js v20.18.1以上
- Prismaクライアントが設定済み
- 商品データCSVファイルが存在すること

### 2. データベースマイグレーション

```bash
# Prismaスキーマを更新後、マイグレーションを実行
npx prisma migrate dev --name add_category_masters
```

### 3. カテゴリ構造分析

```bash
# 商品データからカテゴリ構造を分析
node scripts/category-management/01_analyze_excel_categories.js
```

**出力ファイル:**
- `master-data-csv/generated/category_analysis.csv`
- `master-data-csv/generated/subcategory_analysis.csv`
- `master-data-csv/generated/category_analysis_report.md`

### 4. 統合マスタ作成

```bash
# 分析結果から統合カテゴリマスタを作成
node scripts/category-management/02_create_unified_master.js
```

**出力ファイル:**
- `master-data-csv/generated/unified_category_master.csv`
- `master-data-csv/generated/unified_subcategory_master.csv`
- `master-data-csv/generated/unified_master_statistics.md`

### 5. データベースインポート

```bash
# 統合マスタをデータベースにインポート
node scripts/category-management/03_import_master_data.js [shop]

# 例: 特定のショップを指定
node scripts/category-management/03_import_master_data.js ease-next-temp.myshopify.com
```

**出力ファイル:**
- `master-data-csv/generated/import_results.md`

## API使用方法

### カテゴリ一覧取得

```javascript
// GET /api/categories?action=list
const response = await fetch('/api/categories?action=list');
const data = await response.json();
console.log(data.categories);
```

### SKU検証

```javascript
// GET /api/categories?action=validate-sku&sku=212-05-023
const response = await fetch('/api/categories?action=validate-sku&sku=212-05-023');
const data = await response.json();
console.log(data.validation);
```

### SKU生成

```javascript
// GET /api/categories?action=generate-sku&categoryCode=212&subCategoryCode=05
const response = await fetch('/api/categories?action=generate-sku&categoryCode=212&subCategoryCode=05');
const data = await response.json();
console.log(data.sku); // 例: "212-05-024"
```

### SKU解析

```javascript
// GET /api/categories?action=parse-sku&sku=212-05-023
const response = await fetch('/api/categories?action=parse-sku&sku=212-05-023');
const data = await response.json();
console.log(data.skuStructure);
console.log(data.category);
console.log(data.subCategory);
```

## サービスクラス使用例

```typescript
import { CategoryMasterService } from '~/services/category-master.service';

const categoryService = new CategoryMasterService();

// カテゴリ一覧取得
const categories = await categoryService.getActiveCategoriesByShop(shop);

// SKU生成
const newSKU = await categoryService.generateNextSKU(shop, '212', '05');

// SKU検証
const validation = await categoryService.validateSKU(shop, '212-05-023');
```

## データ構造

### CategoryMaster

```typescript
interface CategoryMasterData {
  id: string;
  shop: string;
  code: string;           // 例: "212"
  name: string;           // 例: "花器"
  parentCode?: string;
  level: number;          // 1=大カテゴリ
  displayOrder: number;
  isActive: boolean;
  description?: string;
  subCategories?: SubCategoryMasterData[];
}
```

### SubCategoryMaster

```typescript
interface SubCategoryMasterData {
  id: string;
  shop: string;
  code: string;                // 例: "05"
  name: string;                // 例: "ゴールド・シルバー"
  parentCategoryCode: string;  // 例: "212"
  isActive: boolean;
  displayOrder: number;
  description?: string;
}
```

### SKU構造

```typescript
interface SKUStructure {
  categoryCode: string;     // 例: "212"
  subCategoryCode: string;  // 例: "05"
  serialNumber: string;     // 例: "023"
}
```

## トラブルシューティング

### よくあるエラー

1. **ファイルが見つからない**
   ```
   Error: ファイルが見つかりません: master-data-csv/rental_items_20250509_153839.csv
   ```
   → 商品データCSVファイルが正しい場所にあることを確認

2. **データベース接続エラー**
   ```
   Error: データベース接続エラー
   ```
   → DATABASE_URL環境変数が正しく設定されていることを確認

3. **SKU形式エラー**
   ```
   Error: SKU形式が正しくありません
   ```
   → SKUが `XXX-XX-XXX` 形式になっていることを確認

### ログ確認

各スクリプトは詳細なログを出力します：

```bash
# 分析ログ
cat master-data-csv/generated/category_analysis_report.md

# インポートログ
cat master-data-csv/generated/import_results.md
```

## 今後の拡張

1. **カテゴリ管理画面**: 管理者向けのWebUI
2. **バッチ更新機能**: 既存商品のカテゴリ情報一括更新
3. **カテゴリ階層表示**: ツリー形式でのカテゴリ表示
4. **SKU重複チェック**: 定期的な整合性チェック

## 関連ドキュメント

- [商品カテゴリ管理システム設計](../../docs/active/specifications/商品カテゴリ管理システム設計.md)
- [Prismaスキーマ](../../prisma/schema.prisma)
- [商品管理API](../../app/routes/api.categories.tsx)
