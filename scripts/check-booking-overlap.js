/**
 * 予約の重複チェックスクリプト
 *
 * このスクリプトは、予約の重複をチェックし、重複がある場合はエラーを返します。
 * 実行方法: node scripts/check-booking-overlap.js [商品ID] [開始日] [終了日]
 */

import { PrismaClient } from '@prisma/client';
import { format, parseISO } from 'date-fns';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * 予約の重複をチェックする関数
 * @param {string} productId 商品ID
 * @param {Date} startDate 開始日
 * @param {Date} endDate 終了日
 * @returns {Promise<object>} 重複チェック結果
 */
async function checkBookingOverlap(productId, startDate, endDate) {
  try {
    console.log(`商品ID ${productId} の予約重複チェックを実行します...`);
    console.log(`期間: ${format(startDate, 'yyyy-MM-dd')} 〜 ${format(endDate, 'yyyy-MM-dd')}`);

    // データベースから全ての予約を取得
    // 注意: productIdはデータベース上のUUIDであり、Shopify商品IDではない
    // まず、データベース上の商品情報を取得
    const dbProduct = await prisma.product.findFirst({
      where: {
        shopifyId: productId.toString().replace('gid://shopify/Product/', '')
      }
    });

    if (!dbProduct) {
      console.log(`商品ID ${productId} がデータベースに存在しません`);
      return { hasOverlap: false, overlappingBookings: [] };
    }

    console.log(`データベース上の商品ID: ${dbProduct.id}`);

    // データベース上の商品IDを使用して予約を検索
    const allBookings = await prisma.booking.findMany({
      where: {
        productId: dbProduct.id,
      },
    });

    console.log(`商品ID ${productId} の全予約数: ${allBookings.length}`);

    // 手動で重複をチェック
    const overlappingBookings = allBookings.filter(booking => {
      const bookingStartDate = booking.startDate;
      const bookingEndDate = booking.endDate;

      // 予約期間が重複するかどうかをチェック
      const isOverlapping =
        (bookingStartDate <= endDate && bookingEndDate >= startDate);

      if (isOverlapping) {
        console.log(`重複する予約: ${booking.bookingId}, 期間: ${format(bookingStartDate, 'yyyy-MM-dd')} 〜 ${format(bookingEndDate, 'yyyy-MM-dd')}`);
      }

      return isOverlapping;
    });

    if (overlappingBookings.length > 0) {
      console.log(`重複する予約が ${overlappingBookings.length} 件見つかりました:`);
      overlappingBookings.forEach((booking, index) => {
        console.log(`${index + 1}. 予約ID: ${booking.bookingId}, 期間: ${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')}`);
      });
      return { hasOverlap: true, overlappingBookings };
    } else {
      console.log('重複する予約はありません');
      return { hasOverlap: false, overlappingBookings: [] };
    }
  } catch (error) {
    console.error('予約重複チェック中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    // コマンドライン引数から商品IDと日程を取得
    const productId = process.argv[2];
    const startDateStr = process.argv[3];
    const endDateStr = process.argv[4];

    if (!productId || !startDateStr || !endDateStr) {
      console.error('引数が不足しています。使用方法: node scripts/check-booking-overlap.js [商品ID] [開始日(YYYY-MM-DD)] [終了日(YYYY-MM-DD)]');
      process.exit(1);
    }

    // 日付文字列をDateオブジェクトに変換
    const startDate = parseISO(startDateStr);
    const endDate = parseISO(endDateStr);

    // 予約の重複をチェック
    const result = await checkBookingOverlap(productId, startDate, endDate);

    if (result.hasOverlap) {
      console.log('この期間は予約できません（重複する予約があります）');
      process.exit(1);
    } else {
      console.log('この期間は予約可能です');
    }
  } catch (error) {
    console.error('エラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
