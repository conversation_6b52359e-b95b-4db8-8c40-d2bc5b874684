<!-- snippets/product-page-get-main.liquid -->
{%- assign product_form_id = 'product-form-' | append: section.id -%}
{%- if template_layout == '3' or template_layout == '4' or settings.product_info_enable_sticky_gallery -%}
    {%- assign enable_sticky_gallery = true -%}
{%- endif -%}
{%- assign gallery_size = section.settings.gallery_size | default: '6' | abs -%}
{%- if template_layout == '1' -%}
    <single-product class="product-page-main d-block"{% include 'product-get-attributes' %} data-js-product-clone-id="footbar">
        <div class="row">
            <div class="col-12 col-md-{{ gallery_size }}{% if reverse %} order-md-1{% endif %}{% if enable_sticky_gallery %} index-10 sticky-sidebar js-sticky-sidebar{% endif %}">
                {%- if enable_sticky_gallery -%}
                    <div data-js-sticky-sidebar-body{% if is_quick_view %} data-js-disable-offsets{% endif %}>
                {%- endif -%}
                        {% include 'product-page-get-gallery' %}
                {%- if enable_sticky_gallery -%}
                    </div>
                {%- endif -%}
            </div>
            <div class="col-12 col-md-{{ 12 | minus: gallery_size }}">
                {% include 'product-page-get-info' with product_form_id: product_form_id %}
            </div>
        </div>
    </single-product>
{%- elsif template_layout == '2' -%}
    <single-product class="product-page-main d-block"{% include 'product-get-attributes' %} data-js-product-clone-id="footbar">
        <div class="row">
            <div class="col-12 col-md-{{ gallery_size }}{% if enable_sticky_gallery %} index-10 sticky-sidebar js-sticky-sidebar{% endif %}">
                {%- if enable_sticky_gallery -%}
                    <div data-js-sticky-sidebar-body{% if is_quick_view %} data-js-disable-offsets{% endif %}>
                {%- endif -%}
                        {% include 'product-page-get-gallery' %}
                {%- if enable_sticky_gallery -%}
                    </div>
                {%- endif -%}
            </div>
            <div class="col-12 col-md-{{ 12 | minus: gallery_size }}">
                {% include 'product-page-get-info' with product_form_id: product_form_id %}
                {%- if section.settings.show_tabs -%}
                    <div class="product-page__tabs mt-30 mb-40">
                        {% include 'product-page-get-tabs' %}
                    </div>
                {%- endif -%}
            </div>
        </div>
    </single-product>
{%- elsif template_layout == '3' -%}
    <single-product class="product-page-main d-block"{% include 'product-get-attributes' %} data-js-product-clone-id="footbar">
        <div class="row">
            <div class="col-12 col-md-{{ gallery_size }}{% if enable_sticky_gallery %} index-10 sticky-sidebar js-sticky-sidebar{% endif %}">
                {%- if enable_sticky_gallery -%}
                    <div data-js-sticky-sidebar-body{% if is_quick_view %} data-js-disable-offsets{% endif %}>
                {%- endif -%}
                        {% include 'product-page-get-gallery' %}
                {%- if enable_sticky_gallery -%}
                    </div>
                {%- endif -%}
            </div>
            <div class="col-12 col-md-{{ 12 | minus: gallery_size }}{% if enable_sticky_gallery %} sticky-sidebar js-sticky-sidebar{% endif %}">
                {%- if enable_sticky_gallery -%}
                    <div data-js-sticky-sidebar-body{% if is_quick_view %} data-js-disable-offsets{% endif %}>
                {%- endif -%}
                        {% include 'product-page-get-info' with product_form_id: product_form_id %}
                        {%- if section.settings.show_tabs -%}
                            <div class="product-page__tabs mt-30 pb-40">
                                {% include 'product-page-get-tabs' %}
                            </div>
                        {%- endif -%}
                {%- if enable_sticky_gallery -%}
                    </div>
                {%- endif -%}
            </div>
        </div>
    </single-product>
{%- elsif template_layout == '4' -%}
    <single-product class="product-page-main d-block"{% include 'product-get-attributes' %} data-js-product-clone-id="footbar">
        <div class="row">
            <div class="col-12 col-md-{{ gallery_size }}{% if enable_sticky_gallery %} index-10 sticky-sidebar js-sticky-sidebar{% endif %}">
                {%- if enable_sticky_gallery -%}
                    <div data-js-sticky-sidebar-body{% if is_quick_view %} data-js-disable-offsets{% endif %}>
                {%- endif -%}
                        {% include 'product-page-get-gallery' %}
                {%- if enable_sticky_gallery -%}
                    </div>
                {%- endif -%}
            </div>
            <div class="col-12 col-md-{{ 12 | minus: gallery_size }}{% if enable_sticky_gallery %} sticky-sidebar js-sticky-sidebar{% endif %}">
                {%- if enable_sticky_gallery -%}
                    <div data-js-sticky-sidebar-body{% if is_quick_view %} data-js-disable-offsets{% endif %}>
                {%- endif -%}
                        {% include 'product-page-get-info' with product_form_id: product_form_id %}
                        {%- if section.settings.show_tabs -%}
                            <div class="product-page__tabs mt-30 pb-40">
                                {% include 'product-page-get-tabs' %}
                            </div>
                        {%- endif -%}
                {%- if enable_sticky_gallery -%}
                    </div>
                {%- endif -%}
            </div>
        </div>
    </single-product>
{%- elsif template_layout == '5' -%}
    <single-product class="product-page-main d-block"{% include 'product-get-attributes' %} data-js-product-clone-id="footbar">
        {% include 'product-page-get-gallery' %}
        {% include 'product-page-get-info' with product_form_id: product_form_id %}
    </single-product>
{%- endif -%}
{%- if enable_sticky_gallery -%}
    <script>
        theme.AssetsLoader.require('scripts', 'sticky_sidebar');
    </script>
{%- endif -%}