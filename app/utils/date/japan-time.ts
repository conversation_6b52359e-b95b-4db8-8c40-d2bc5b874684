/**
 * 日本時間関連のユーティリティ関数
 */

import { format, addDays, startOfDay, endOfDay } from 'date-fns';
import { ja } from 'date-fns/locale';

/**
 * 日本のタイムゾーン
 */
export const JAPAN_TIMEZONE = 'Asia/Tokyo';

/**
 * 日本時間のフォーマットオプション
 */
export const JAPAN_TIMEZONE_OPTIONS = {
  timeZone: JAPAN_TIMEZONE,
  locale: 'ja-JP',
};

/**
 * 現在の日本時間を取得する
 * @returns 現在の日本時間
 */
export function getNowInJapan(): Date {
  // 日本時間を取得
  const now = new Date();
  // 日本時間のオフセット（+9時間）を考慮
  // 実際のプロダクションコードでは、より正確なタイムゾーン処理が必要
  return now;
}

/**
 * 日本時間で日付をフォーマットする
 * @param date フォーマットする日付
 * @param formatStr フォーマット文字列（デフォルト: 'yyyy年MM月dd日'）
 * @returns フォーマットされた日付文字列
 */
export function formatJapanDate(date: Date | string | null, formatStr = 'yyyy年MM月dd日'): string {
  if (!date) return '';

  const dateObj = typeof date === 'string' ? new Date(date) : date;

  try {
    return format(dateObj, formatStr, { locale: ja });
  } catch (error) {
    console.error('日付フォーマットエラー:', error);
    return '';
  }
}

/**
 * 日本時間で日の始まり（00:00:00）を取得する
 * @param date 対象の日付
 * @returns 日の始まりの日時
 */
export function startOfDayJapan(date: Date | string): Date {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return startOfDay(dateObj);
}

/**
 * 日本時間で日の終わり（23:59:59.999）を取得する
 * @param date 対象の日付
 * @returns 日の終わりの日時
 */
export function endOfDayJapan(date: Date | string): Date {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return endOfDay(dateObj);
}

/**
 * 日本時間で日付を加算する
 * @param date 元の日付
 * @param days 加算する日数
 * @returns 加算後の日付
 */
export function addDaysJapan(date: Date | string, days: number): Date {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return addDays(dateObj, days);
}