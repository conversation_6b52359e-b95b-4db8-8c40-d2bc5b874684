import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { authenticate } from "../shopify.server";
import {
  Page,
  Layout,
  Card,
  Text,
  BlockStack,
} from "@shopify/polaris";
import type { LoaderFunctionArgs } from "@remix-run/node";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin } = await authenticate.admin(request);

  // GraphQLクエリを実行して商品を取得
  const response = await admin.graphql(
    `#graphql
      query {
        products(first: 3) {
          edges {
            node {
              id
              title
              handle
              createdAt
              updatedAt
            }
          }
        }
      }`
  );

  const json = await response.json();

  return {
    products: json.data.products.edges,
  };
};

export default function ProductsTest() {
  const { products } = useLoaderData<typeof loader>();

  return (
    <Page title="商品読み取りテスト">
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="h2" variant="headingMd">
                最初の3件の商品情報
              </Text>
              {products.map(({ node }) => (
                <BlockStack key={node.id} gap="200">
                  <Text as="h3" variant="headingSm">
                    {node.title}
                  </Text>
                  <Text as="p" variant="bodyMd">
                    ID: {node.id}
                  </Text>
                  <Text as="p" variant="bodyMd">
                    Handle: {node.handle}
                  </Text>
                  <Text as="p" variant="bodyMd">
                    作成日: {new Date(node.createdAt).toLocaleString('ja-JP')}
                  </Text>
                  <Text as="p" variant="bodyMd">
                    更新日: {new Date(node.updatedAt).toLocaleString('ja-JP')}
                  </Text>
                </BlockStack>
              ))}
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
} 