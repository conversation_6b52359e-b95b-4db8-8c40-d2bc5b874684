import dotenv from 'dotenv';
import fs from 'fs';
import { GraphQLClient, gql } from 'graphql-request';

// csv-parse/syncがない場合の代替処理
function parseCsv(content) {
  const lines = content.split('\n');
  const headers = lines[0].split(',').map(h => h.trim());

  const records = [];
  for (let i = 1; i < lines.length; i++) {
    if (!lines[i].trim()) continue;

    const values = lines[i].split(',').map(v => v.trim());
    const record = {};

    headers.forEach((header, index) => {
      record[header] = values[index] || '';
    });

    records.push(record);
  }

  return records;
}

// 環境変数の読み込み
dotenv.config();

// Shopify API設定
const SHOP = process.env.SHOPIFY_SHOP;
const ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const API_VERSION = '2024-01';

// GraphQLクライアントの設定
const endpoint = `https://${SHOP}/admin/api/${API_VERSION}/graphql.json`;
const graphQLClient = new GraphQLClient(endpoint, {
  headers: {
    'X-Shopify-Access-Token': ACCESS_TOKEN,
    'Content-Type': 'application/json',
  },
});

// メタフィールド定義のマッピング
const metafieldDefinitions = {
  height: {
    namespace: 'custom',
    key: 'height',
    type: 'number_decimal',
    name: '高さ',
    description: '商品の高さ（cm）',
  },
  width: {
    namespace: 'custom',
    key: 'width',
    type: 'number_decimal',
    name: '幅',
    description: '商品の幅（cm）',
  },
  depth: {
    namespace: 'custom',
    key: 'depth',
    type: 'number_decimal',
    name: '奥行き',
    description: '商品の奥行き（cm）',
  },
  weight: {
    namespace: 'custom',
    key: 'weight',
    type: 'number_decimal',
    name: '重量',
    description: '商品の重量（kg）',
  },
  material: {
    namespace: 'custom',
    key: 'material',
    type: 'single_line_text_field',
    name: '素材',
    description: '商品の素材',
  },
  color: {
    namespace: 'custom',
    key: 'color',
    type: 'single_line_text_field',
    name: '色',
    description: '商品の色',
  },
  location: {
    namespace: 'custom',
    key: 'location',
    type: 'single_line_text_field',
    name: '在庫場所',
    description: '在庫の保管場所（NY/PR）',
  },
  status: {
    namespace: 'rental',
    key: 'status',
    type: 'single_line_text_field',
    name: 'レンタル状態',
    description: '商品のレンタル状態（available/maintenance/damaged/unavailable）',
  },
  category: {
    namespace: 'custom',
    key: 'category',
    type: 'single_line_text_field',
    name: 'カテゴリ',
    description: '商品のカテゴリ',
  },
  subcategory: {
    namespace: 'custom',
    key: 'subcategory',
    type: 'single_line_text_field',
    name: 'サブカテゴリ',
    description: '商品のサブカテゴリ',
  },
  name: {
    namespace: 'custom',
    key: 'product_name',
    type: 'single_line_text_field',
    name: '商品名',
    description: '商品の正式名称',
  },
  price_1day: {
    namespace: 'custom',
    key: 'price_1day',
    type: 'number_integer',
    name: '1日レンタル料金',
    description: '1日あたりのレンタル料金',
  },
  price_2day: {
    namespace: 'custom',
    key: 'price_2day',
    type: 'number_integer',
    name: '2日レンタル料金',
    description: '2日間のレンタル料金',
  },
  price_3day: {
    namespace: 'custom',
    key: 'price_3day',
    type: 'number_integer',
    name: '3日レンタル料金',
    description: '3日間のレンタル料金',
  },
  price_4day: {
    namespace: 'custom',
    key: 'price_4day',
    type: 'number_integer',
    name: '4日レンタル料金',
    description: '4日間のレンタル料金',
  },
  stock: {
    namespace: 'custom',
    key: 'stock',
    type: 'number_integer',
    name: '在庫数',
    description: '商品の在庫数',
  },
  campaign: {
    namespace: 'custom',
    key: 'campaign',
    type: 'single_line_text_field',
    name: 'キャンペーン',
    description: '適用されるキャンペーン',
  },
  maker: {
    namespace: 'custom',
    key: 'maker',
    type: 'single_line_text_field',
    name: 'メーカー',
    description: '商品のメーカーまたは購入先',
  },
  purchase_date: {
    namespace: 'custom',
    key: 'purchase_date',
    type: 'single_line_text_field',
    name: '購入日',
    description: '商品の購入日（YYYYMMDD形式）',
  },
  maintenance_notes: {
    namespace: 'custom',
    key: 'maintenance_notes',
    type: 'multi_line_text_field',
    name: 'メンテナンス備考',
    description: '商品のメンテナンスに関する備考',
  },
  product_code: {
    namespace: 'custom',
    key: 'product_code',
    type: 'single_line_text_field',
    name: '商品コード',
    description: '商品の基本コード',
  },
  detail_code: {
    namespace: 'custom',
    key: 'detail_code',
    type: 'single_line_text_field',
    name: '商品詳細コード',
    description: '商品の詳細コード',
  },
  kana: {
    namespace: 'custom',
    key: 'kana',
    type: 'single_line_text_field',
    name: '商品名フリガナ',
    description: '商品名のフリガナ表記',
  },
  special_notes: {
    namespace: 'custom',
    key: 'special_notes',
    type: 'multi_line_text_field',
    name: '特記事項',
    description: '商品に関する特記事項',
  },
  // レンタル商品基本情報（JSON形式）
  rental_basic_info: {
    namespace: 'rental',
    key: 'basic_info',
    type: 'json',
    name: 'レンタル商品基本情報',
    description: 'レンタル商品の基本情報（JSON形式）',
  },
  // レンタル料金設定（JSON形式）
  rental_pricing: {
    namespace: 'rental',
    key: 'pricing',
    type: 'json',
    name: 'レンタル料金設定',
    description: 'レンタル料金の設定情報（JSON形式）',
  },
  // バリエーションマッピング（JSON形式）
  variant_mapping: {
    namespace: 'rental',
    key: 'variant_mapping',
    type: 'json',
    name: 'バリエーションマッピング',
    description: 'バリエーションのマッピング情報（JSON形式）',
  },
  // 在庫情報（JSON形式）
  inventory_info: {
    namespace: 'rental',
    key: 'inventory_info',
    type: 'json',
    name: '在庫情報',
    description: '在庫の詳細情報（JSON形式）',
  },
  // 予約情報（JSON形式）
  reservation_info: {
    namespace: 'rental',
    key: 'reservation_info',
    type: 'json',
    name: '予約情報',
    description: '予約の詳細情報（JSON形式）',
  }
};

// CSVファイルの読み込み
async function readCSV(filePath) {
  try {
    const content = fs.readFileSync(filePath, { encoding: 'utf-8' });
    // 自作のCSVパーサーを使用
    const records = parseCsv(content);
    return records;
  } catch (error) {
    console.error('CSVファイルの読み込みエラー:', error);
    throw error;
  }
}

// 商品IDの取得（SKUから）
async function getProductIdFromSku(sku) {
  const query = gql`
    query getProductBySku($query: String!) {
      products(first: 1, query: $query) {
        edges {
          node {
            id
            title
          }
        }
      }
    }
  `;

  try {
    const data = await graphQLClient.request(query, { query: `sku:${sku}` });
    if (data.products.edges.length > 0) {
      return {
        id: data.products.edges[0].node.id,
        title: data.products.edges[0].node.title,
      };
    }
    return null;
  } catch (error) {
    console.error(`SKU ${sku} の商品ID取得エラー:`, error);
    return null;
  }
}

// メタフィールドの設定
async function setMetafield(productId, namespace, key, value, type) {
  const mutation = gql`
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  // JSON型の場合は特別な処理
  let processedValue = value;
  if (type === 'json') {
    try {
      // 文字列がすでにJSONオブジェクトの場合は解析
      if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
        // バックスラッシュでエスケープされたJSONを正しく解析
        const cleanedValue = value.replace(/\\"/g, '"');
        const jsonObj = JSON.parse(cleanedValue);
        processedValue = JSON.stringify(jsonObj);
      } else {
        // そのまま文字列として使用
        processedValue = String(value);
      }
    } catch (error) {
      console.error('JSON解析エラー:', error, value);
      return false;
    }
  } else {
    // 通常の型の場合は文字列に変換
    processedValue = String(value);
  }

  const variables = {
    metafields: [
      {
        ownerId: productId,
        namespace,
        key,
        value: processedValue,
        type,
      }
    ]
  };

  try {
    const result = await graphQLClient.request(mutation, variables);
    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定エラー:', result.metafieldsSet.userErrors);
      return false;
    }
    return true;
  } catch (error) {
    console.error(`メタフィールド ${namespace}.${key} 設定エラー:`, error);
    return false;
  }
}

// メイン処理
async function importMetafieldsFromCSV(csvFilePath) {
  try {
    // CSVファイルの読み込み
    const products = await readCSV(csvFilePath);
    console.log(`${products.length}件の商品データを読み込みました`);

    let successCount = 0;
    let errorCount = 0;

    // 各商品のメタフィールドを設定
    for (const product of products) {
      const sku = product.sku || product.SKU;
      if (!sku) {
        console.warn('SKUが見つかりません:', product);
        errorCount++;
        continue;
      }

      // SKUから商品IDを取得
      const productInfo = await getProductIdFromSku(sku);
      if (!productInfo) {
        console.warn(`SKU ${sku} の商品が見つかりません`);
        errorCount++;
        continue;
      }

      console.log(`処理中: ${productInfo.title} (${sku})`);

      // 各メタフィールドを設定
      let productSuccess = true;
      for (const [field, definition] of Object.entries(metafieldDefinitions)) {
        if (product[field] && product[field].trim() !== '') {
          const success = await setMetafield(
            productInfo.id,
            definition.namespace,
            definition.key,
            product[field],
            definition.type
          );

          if (!success) {
            productSuccess = false;
            console.error(`${productInfo.title} の ${field} 設定に失敗しました`);
          }
        }
      }

      if (productSuccess) {
        successCount++;
        console.log(`${productInfo.title} のメタフィールドを設定しました`);
      } else {
        errorCount++;
      }
    }

    console.log('=== 処理完了 ===');
    console.log(`成功: ${successCount}件`);
    console.log(`失敗: ${errorCount}件`);
  } catch (error) {
    console.error('処理中にエラーが発生しました:', error);
  }
}

// コマンドライン引数からCSVファイルパスを取得
const csvFilePath = process.argv[2];
if (!csvFilePath) {
  console.error('使用方法: node import-metafields-from-csv.js <CSVファイルパス>');
  process.exit(1);
}

// 実行
importMetafieldsFromCSV(csvFilePath);
