/**
 * 在庫カレンダーサービス
 *
 * 日付ごとの在庫状態を管理するサービス
 */

import { PrismaClient } from '@prisma/client';
import { addDays, format, isAfter, isBefore, isEqual, parseISO } from 'date-fns';

const prisma = new PrismaClient();

/**
 * 在庫カレンダーサービス
 */
export class InventoryCalendarService {
  /**
   * 商品の在庫カレンダーを更新
   * @param productId 商品ID
   * @param startDate 開始日
   * @param endDate 終了日
   */
  async updateInventoryCalendar(productId: string, startDate?: Date, endDate?: Date): Promise<void> {
    try {
      // 商品情報を取得
      const product = await prisma.product.findUnique({
        where: { id: productId }
      });

      if (!product) {
        throw new Error(`商品 ID ${productId} が見つかりません`);
      }

      // 日付範囲の設定
      const today = new Date();
      const calendarStartDate = startDate || today;

      // デフォルトは3ヶ月先まで
      const defaultEndDate = new Date(today);
      defaultEndDate.setMonth(today.getMonth() + 3);

      const calendarEndDate = endDate || defaultEndDate;

      // 予約情報を取得
      const bookings = await prisma.booking.findMany({
        where: {
          productId,
          status: {
            in: ['PROVISIONAL', 'CONFIRMED']
          },
          endDate: {
            gte: calendarStartDate
          }
        }
      });

      // メンテナンス情報を取得
      const maintenances = await prisma.maintenance.findMany({
        where: {
          productId,
          endDate: {
            gte: calendarStartDate
          }
        }
      });

      // 日付ごとに在庫状態を計算
      for (let date = new Date(calendarStartDate);
           isBefore(date, calendarEndDate) || isEqual(date, calendarEndDate);
           date = addDays(date, 1)) {

        const currentDate = new Date(date);
        const dateStr = format(currentDate, 'yyyy-MM-dd');

        // 予約状況をチェック
        const bookingsOnDate = bookings.filter(booking => {
          const bookingStartDate = new Date(booking.startDate);
          const bookingEndDate = new Date(booking.endDate);
          return (
            (isEqual(currentDate, bookingStartDate) || isAfter(currentDate, bookingStartDate)) &&
            (isEqual(currentDate, bookingEndDate) || isBefore(currentDate, bookingEndDate))
          );
        });

        // メンテナンス状況をチェック
        const maintenancesOnDate = maintenances.filter(maintenance => {
          const maintenanceStartDate = new Date(maintenance.startDate);
          const maintenanceEndDate = new Date(maintenance.endDate);
          return (
            (isEqual(currentDate, maintenanceStartDate) || isAfter(currentDate, maintenanceStartDate)) &&
            (isEqual(currentDate, maintenanceEndDate) || isBefore(currentDate, maintenanceEndDate))
          );
        });

        // 在庫状態を決定
        const isBooked = bookingsOnDate.length > 0;
        const isInMaintenance = maintenancesOnDate.length > 0;
        const isAvailable = !isBooked && !isInMaintenance;

        // 在庫カレンダーを更新
        await prisma.inventoryCalendar.upsert({
          where: {
            shop_productId_date: {
              shop: process.env.SHOPIFY_SHOP || '',
              productId,
              date: currentDate
            }
          },
          update: {
            isAvailable,
            unavailableReason: isBooked ? 'BOOKED' : (isInMaintenance ? 'MAINTENANCE' : null),
            maintenanceId: isInMaintenance ? maintenancesOnDate[0]?.id : null,
            bookingId: isBooked ? bookingsOnDate[0]?.id : null,
            updatedAt: new Date()
          },
          create: {
            shop: process.env.SHOPIFY_SHOP || '',
            productId,
            shopifyProductId: product.shopifyId || '0', // 必須フィールド
            date: currentDate,
            isAvailable,
            unavailableReason: isBooked ? 'BOOKED' : (isInMaintenance ? 'MAINTENANCE' : null),
            maintenanceId: isInMaintenance ? maintenancesOnDate[0]?.id : null,
            bookingId: isBooked ? bookingsOnDate[0]?.id : null,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });
      }
    } catch (error) {
      console.error(`在庫カレンダー更新エラー (${productId}):`, error);
      throw error;
    }
  }

  /**
   * 日付範囲の在庫状態を確認
   * @param productId 商品ID
   * @param startDate 開始日
   * @param endDate 終了日
   * @param bookingType 予約タイプ（PROVISIONAL/CONFIRMED）
   * @param currentBookingId 現在の予約ID（更新時に自分自身を除外するため）
   * @returns 在庫が利用可能かどうか
   */
  async checkAvailability(
    productId: string,
    startDate: Date,
    endDate: Date,
    bookingType?: string,
    currentBookingId?: string
  ): Promise<{
    available: boolean;
    unavailableDates: string[];
    reasons: { [date: string]: string };
    bookingIds?: string[];
  }> {
    try {
      // 商品情報を取得
      const product = await prisma.product.findUnique({
        where: { id: productId }
      });

      if (!product) {
        throw new Error(`商品 ID ${productId} が見つかりません`);
      }

      // 在庫カレンダーを取得
      const inventoryCalendar = await prisma.inventoryCalendar.findMany({
        where: {
          productId,
          date: {
            gte: startDate,
            lte: endDate
          },
          ...(currentBookingId ? { bookingId: { not: currentBookingId } } : {})
        }
      });

      // 在庫カレンダーがない日付は利用可能とみなす
      const unavailableDates: string[] = [];
      const reasons: { [date: string]: string } = {};
      const bookingIds: string[] = [];

      // 日付ごとに在庫状態を確認
      for (let date = new Date(startDate);
           isBefore(date, endDate) || isEqual(date, endDate);
           date = addDays(date, 1)) {

        const currentDate = new Date(date);
        const dateStr = format(currentDate, 'yyyy-MM-dd');

        // 在庫カレンダーから該当日の情報を取得
        const calendarEntry = inventoryCalendar.find(entry =>
          isEqual(new Date(entry.date), currentDate)
        );

        // 在庫カレンダーがない場合は利用可能とみなす
        if (!calendarEntry) {
          continue;
        }

        // 利用不可の場合
        if (!calendarEntry.isAvailable) {
          // 仮予約と本予約の競合チェック
          // 本予約の場合は、仮予約・本予約どちらも予約不可
          // 仮予約の場合は、本予約があれば予約不可、仮予約があれば予約不可
          const entryBookingType = calendarEntry.bookingType || 'CONFIRMED';

          // 予約タイプに基づいて利用可能かどうかを判断
          let isUnavailable = true;

          // 常に予約不可（仮予約と本予約は同じ商品の同じ日程を予約できない）
          if (isUnavailable) {
            unavailableDates.push(dateStr);

            // 予約タイプに応じた理由を設定
            let reason = calendarEntry.unavailableReason || '不明';
            if (reason === 'BOOKED') {
              reason = entryBookingType === 'PROVISIONAL' ? '仮予約済み' : '本予約済み';
            }

            reasons[dateStr] = reason;

            // 予約IDを追加
            if (calendarEntry.bookingId && !bookingIds.includes(calendarEntry.bookingId)) {
              bookingIds.push(calendarEntry.bookingId);
            }
          }
        }
      }

      return {
        available: unavailableDates.length === 0,
        unavailableDates,
        reasons,
        bookingIds
      };
    } catch (error) {
      console.error(`在庫確認エラー (${productId}):`, error);
      throw error;
    }
  }

  /**
   * 予約作成・更新時に在庫カレンダーを更新
   * @param booking 予約情報
   * @returns 更新結果
   */
  async updateCalendarForBooking(booking: any): Promise<{ success: boolean; error?: string }> {
    try {
      const { productId, startDate, endDate, id: bookingId, status, bookingType } = booking;

      // キャンセルされた予約の場合は在庫を解放
      if (status === 'CANCELLED' || status === 'COMPLETED') {
        await this.releaseInventory(productId, startDate, endDate, bookingId);
        return { success: true };
      } else {
        // 予約前に在庫の利用可能性をチェック（予約タイプと予約IDを考慮）
        const availabilityResult = await this.checkAvailability(
          productId,
          new Date(startDate),
          new Date(endDate),
          bookingType,
          bookingId
        );

        // 既に予約されている場合はエラーを返す
        if (!availabilityResult.available) {
          // 予約タイプに応じたエラーメッセージを生成
          const bookingTypeText = bookingType === 'PROVISIONAL' ? '仮予約' : '本予約';
          const conflictingBookingIds = availabilityResult.bookingIds || [];

          // 競合する予約の詳細情報を取得
          let conflictDetails = '';
          if (conflictingBookingIds.length > 0) {
            const conflictingBookings = await prisma.booking.findMany({
              where: {
                id: { in: conflictingBookingIds }
              },
              select: {
                id: true,
                bookingId: true,
                bookingType: true,
                startDate: true,
                endDate: true
              }
            });

            if (conflictingBookings.length > 0) {
              conflictDetails = `\n競合する予約: ${conflictingBookings.map(b =>
                `${b.bookingId} (${b.bookingType === 'PROVISIONAL' ? '仮予約' : '本予約'}: ${format(b.startDate, 'yyyy/MM/dd')}〜${format(b.endDate, 'yyyy/MM/dd')})`
              ).join(', ')}`;
            }
          }

          return {
            success: false,
            error: `選択された日程は既に予約されています: ${availabilityResult.unavailableDates.join(', ')}${conflictDetails}`
          };
        }

        // 予約中の在庫を確保
        await this.reserveInventory(productId, startDate, endDate, bookingId, bookingType);
        return { success: true };
      }
    } catch (error) {
      console.error(`予約の在庫カレンダー更新エラー:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 在庫を予約状態に更新
   * @param productId 商品ID
   * @param startDate 開始日
   * @param endDate 終了日
   * @param bookingId 予約ID
   * @param bookingType 予約タイプ（PROVISIONAL/CONFIRMED）
   */
  private async reserveInventory(
    productId: string,
    startDate: Date,
    endDate: Date,
    bookingId: string,
    bookingType?: string
  ): Promise<void> {
    try {
      // 予約情報を取得して予約タイプを確認
      let bookingTypeValue = bookingType;

      if (!bookingTypeValue) {
        const booking = await prisma.booking.findUnique({
          where: { id: bookingId },
          select: { bookingType: true }
        });

        if (booking) {
          bookingTypeValue = booking.bookingType;
        }
      }

      // 仮予約の場合は有効期限を設定（開始日の7日前）
      let expiresAt: Date | null = null;
      if (bookingTypeValue === 'PROVISIONAL') {
        expiresAt = addDays(new Date(startDate), -7);
      }

      // 商品情報を取得
      const product = await prisma.product.findUnique({
        where: { id: productId }
      });

      if (!product) {
        throw new Error(`商品 ID ${productId} が見つかりません`);
      }

      // 日付範囲の在庫カレンダーを更新
      for (let date = new Date(startDate);
           isBefore(date, endDate) || isEqual(date, endDate);
           date = addDays(date, 1)) {

        const currentDate = new Date(date);

        // リトライロジック
        let retryCount = 0;
        const maxRetries = 3;
        let success = false;
        let lastError = null;

        while (!success && retryCount < maxRetries) {
          try {
            // 競合を避けるために最新の在庫状態を確認
            const existingInventory = await prisma.inventoryCalendar.findUnique({
              where: {
                shop_productId_date: {
                  shop: process.env.SHOPIFY_SHOP || '',
                  productId,
                  date: currentDate
                }
              }
            });

            // 既に予約されている場合はエラー（同じ予約IDの場合は除く）
            if (existingInventory &&
                !existingInventory.isAvailable &&
                existingInventory.bookingId &&
                existingInventory.bookingId !== bookingId) {
              throw new Error(`日付 ${currentDate.toISOString().split('T')[0]} は既に予約されています`);
            }

            // 在庫カレンダーを更新
            await prisma.inventoryCalendar.upsert({
              where: {
                shop_productId_date: {
                  shop: process.env.SHOPIFY_SHOP || '',
                  productId,
                  date: currentDate
                }
              },
              update: {
                isAvailable: false,
                unavailableReason: 'BOOKED',
                bookingId,
                bookingType: bookingTypeValue,
                expiresAt,
                updatedAt: new Date()
              },
              create: {
                shop: process.env.SHOPIFY_SHOP || '',
                productId,
                shopifyProductId: product.shopifyId || '0', // 必須フィールド
                date: currentDate,
                isAvailable: false,
                unavailableReason: 'BOOKED',
                bookingId,
                bookingType: bookingTypeValue,
                expiresAt,
                createdAt: new Date(),
                updatedAt: new Date()
              }
            });

            success = true;
          } catch (error) {
            lastError = error;
            retryCount++;

            // 一意性制約違反の場合は少し待ってリトライ
            if (error.code === 'P2002') {
              console.log(`在庫カレンダー更新の競合が発生しました。リトライ ${retryCount}/${maxRetries}`);
              // ランダムな待機時間（50〜200ms）
              await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 150));
            } else {
              // その他のエラーはそのまま投げる
              throw error;
            }
          }
        }

        // 最大リトライ回数を超えた場合
        if (!success) {
          console.error(`在庫カレンダー更新の最大リトライ回数を超えました: ${maxRetries}回`);
          throw lastError;
        }
      }
    } catch (error) {
      console.error(`在庫予約エラー (${productId}, ${bookingId}):`, error);
      throw error;
    }
  }

  /**
   * 在庫を解放
   */
  private async releaseInventory(
    productId: string,
    startDate: Date,
    endDate: Date,
    bookingId: string
  ): Promise<void> {
    // 該当予約の在庫カレンダーを取得
    const inventoryCalendar = await prisma.inventoryCalendar.findMany({
      where: {
        productId,
        bookingId,
        date: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    // 在庫カレンダーを更新
    for (const entry of inventoryCalendar) {
      // 他の予約やメンテナンスがないか確認
      const otherBookings = await prisma.booking.findMany({
        where: {
          productId,
          id: { not: bookingId },
          status: { in: ['PROVISIONAL', 'CONFIRMED'] },
          startDate: { lte: entry.date },
          endDate: { gte: entry.date }
        }
      });

      const maintenances = await prisma.maintenance.findMany({
        where: {
          productId,
          startDate: { lte: entry.date },
          endDate: { gte: entry.date }
        }
      });

      // 他の予約やメンテナンスがない場合は在庫を解放
      if (otherBookings.length === 0 && maintenances.length === 0) {
        await prisma.inventoryCalendar.update({
          where: { id: entry.id },
          data: {
            isAvailable: true,
            unavailableReason: null,
            bookingId: null,
            updatedAt: new Date()
          }
        });
      }
      // 他のメンテナンスがある場合
      else if (maintenances.length > 0) {
        await prisma.inventoryCalendar.update({
          where: { id: entry.id },
          data: {
            isAvailable: false,
            unavailableReason: 'MAINTENANCE',
            bookingId: null,
            maintenanceId: maintenances[0].id,
            updatedAt: new Date()
          }
        });
      }
      // 他の予約がある場合
      else if (otherBookings.length > 0) {
        await prisma.inventoryCalendar.update({
          where: { id: entry.id },
          data: {
            isAvailable: false,
            unavailableReason: 'BOOKED',
            bookingId: otherBookings[0].id,
            updatedAt: new Date()
          }
        });
      }
    }
  }
}
