/**
 * CSVから商品データを読み込み、メタフィールドを設定するスクリプト
 *
 * このスクリプトは、CSVファイルから商品データを読み込み、
 * 各商品のメタフィールドを設定します。廃棄情報も適切に処理します。
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';
import fs from 'fs';
import { parse } from 'csv-parse/sync';
import path from 'path';

dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品検索のGraphQLクエリ
const SEARCH_PRODUCTS_QUERY = gql`
  query searchProducts($query: String!, $first: Int!) {
    products(query: $query, first: $first) {
      edges {
        node {
          id
          title
          handle
          variants(first: 10) {
            edges {
              node {
                id
                title
                sku
                price
              }
            }
          }
        }
      }
    }
  }
`;

// メタフィールド設定のGraphQLミューテーション
const SET_METAFIELDS = gql`
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// CSVファイルを読み込む関数
function readCsvFile(filePath) {
  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const records = parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true
    });
    return records;
  } catch (error) {
    console.error('CSVファイルの読み込み中にエラーが発生しました:', error);
    return [];
  }
}

// 商品を検索する関数
async function searchProducts(query, limit = 10) {
  try {
    const result = await client.request(SEARCH_PRODUCTS_QUERY, {
      query,
      first: limit
    });
    return result.products.edges.map(edge => edge.node);
  } catch (error) {
    console.error('商品検索中にエラーが発生しました:', error);
    return [];
  }
}

// メタフィールドを設定する関数
async function setMetafields(ownerId, metafields) {
  try {
    // 空の値を持つメタフィールドを除外
    const validMetafields = metafields.filter(metafield => {
      if (metafield.type === 'date' && !metafield.value) return false;
      if (metafield.type === 'multi_line_text_field' && !metafield.value) return false;
      return true;
    });

    const metafieldsInput = validMetafields.map(metafield => {
      const input = {
        ownerId,
        namespace: metafield.namespace,
        key: metafield.key
      };

      if (metafield.type === 'json') {
        input.value = JSON.stringify(metafield.value);
        input.type = 'json';
      } else if (metafield.type === 'boolean') {
        input.value = metafield.value.toString();
        input.type = 'boolean';
      } else if (metafield.type === 'number_integer' || metafield.type === 'number_decimal') {
        input.value = metafield.value.toString();
        input.type = metafield.type;
      } else if (metafield.type === 'date') {
        input.value = metafield.value;
        input.type = 'date';
      } else {
        input.value = metafield.value;
        input.type = metafield.type || 'single_line_text_field';
      }

      return input;
    });

    if (metafieldsInput.length === 0) {
      console.log('設定するメタフィールドがありません。');
      return true;
    }

    const result = await client.request(SET_METAFIELDS, {
      metafields: metafieldsInput
    });

    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定中にエラーが発生しました:', result.metafieldsSet.userErrors);
      return false;
    }

    console.log(`${metafieldsInput.length}件のメタフィールドを設定しました。`);
    return true;
  } catch (error) {
    console.error('メタフィールド設定中にエラーが発生しました:', error);
    return false;
  }
}

// CSVデータから商品メタフィールドを生成する関数
function generateMetafields(csvData) {
  // SKUから商品コードと詳細コードを抽出
  const skuMatch = csvData.SKU?.match(/^(\d+)-(\d+)/);
  const productCode = skuMatch ? skuMatch[1] : '';
  const detailCode = skuMatch ? skuMatch[2] : '';

  // 廃棄情報を確認
  const isDisposed = csvData.廃棄済み === 'TRUE' || csvData.廃棄済み === 'true' || csvData.廃棄済み === '1';
  
  // 寸法情報を取得
  const width = parseInt(csvData.幅 || '0');
  const depth = parseInt(csvData.奥行き || '0');
  const height = parseInt(csvData.高さ || '0');

  // 基本情報メタフィールド
  const basicInfo = {
    productCode,
    detailCode,
    kana: csvData.カナ || '',
    dimensions: {
      width,
      depth,
      height
    },
    seatDimensions: {
      width: parseInt(csvData.座面幅 || '0'),
      depth: parseInt(csvData.座面奥行き || '0'),
      height: parseInt(csvData.座面高さ || '0')
    },
    material: csvData.素材 || '',
    color: csvData.色 || '',
    maker: csvData.メーカー || '',
    campaign: csvData.キャンペーン || '通常商品',
    notes: csvData.備考 || ''
  };

  // 料金設定メタフィールド
  const pricing = {
    basePrice: parseInt(csvData.基本料金 || '0'),
    depositRate: parseFloat(csvData.デポジット率 || '0.1'),
    discountRules: {
      day2_6_rate: parseFloat(csvData.割引率_2_6日 || '0.2'),
      day7_plus_rate: parseFloat(csvData.割引率_7日以上 || '0.1')
    },
    minimumDays: parseInt(csvData.最小日数 || '1'),
    maximumDays: parseInt(csvData.最大日数 || '30')
  };

  // 在庫アイテム情報メタフィールド
  const inventoryItems = [
    {
      id: `item-${productCode}-${detailCode}-1`,
      sku: `${productCode}-${detailCode}-1`,
      status: isDisposed ? 'unavailable' : (csvData.状態 || 'available'),
      location: csvData.在庫場所 || 'NY',
      notes: csvData.在庫備考 || ''
    }
  ];

  // 複数台ある場合は追加
  if (csvData.台数 && parseInt(csvData.台数) > 1) {
    for (let i = 2; i <= parseInt(csvData.台数); i++) {
      inventoryItems.push({
        id: `item-${productCode}-${detailCode}-${i}`,
        sku: `${productCode}-${detailCode}-${i}`,
        status: isDisposed ? 'unavailable' : (csvData[`状態_${i}台目`] || 'available'),
        location: csvData[`在庫場所_${i}台目`] || csvData.在庫場所 || 'NY',
        notes: csvData[`在庫備考_${i}台目`] || ''
      });
    }
  }

  // 予約情報メタフィールド
  const reservationInfo = inventoryItems.map(item => ({
    itemId: item.id,
    reservations: []
  }));

  // メタフィールドのリスト
  const metafields = [
    {
      namespace: 'rental',
      key: 'basic_info',
      type: 'json',
      value: basicInfo
    },
    {
      namespace: 'rental',
      key: 'pricing',
      type: 'json',
      value: pricing
    },
    {
      namespace: 'rental',
      key: 'inventory_items',
      type: 'json',
      value: inventoryItems
    },
    {
      namespace: 'rental',
      key: 'reservation_info',
      type: 'json',
      value: reservationInfo
    },
    {
      namespace: 'rental',
      key: 'variation_type',
      type: 'single_line_text_field',
      value: csvData.バリエーションタイプ || 'color'
    },
    {
      namespace: 'rental',
      key: 'variant_mapping',
      type: 'json',
      value: {
        group: `group-${productCode}`,
        variants: [
          {
            id: '', // 後で設定
            type: csvData.バリエーションタイプ || 'color',
            value: csvData.色 || ''
          }
        ]
      }
    },
    {
      namespace: 'rental',
      key: 'color',
      type: 'single_line_text_field',
      value: csvData.色 || ''
    },
    {
      namespace: 'rental',
      key: 'material',
      type: 'single_line_text_field',
      value: csvData.素材 || ''
    },
    {
      namespace: 'product',
      key: 'height',
      type: 'number_integer',
      value: height
    },
    {
      namespace: 'product',
      key: 'width',
      type: 'number_integer',
      value: width
    },
    {
      namespace: 'product',
      key: 'depth',
      type: 'number_integer',
      value: depth
    },
    {
      namespace: 'rental',
      key: 'location',
      type: 'single_line_text_field',
      value: csvData.在庫場所 || 'NY'
    },
    {
      namespace: 'rental',
      key: 'booking_notes',
      type: 'multi_line_text_field',
      value: csvData.予約備考 || ''
    },
    {
      namespace: 'rental',
      key: 'purchase_price',
      type: 'number_decimal',
      value: parseFloat(csvData.購入価格 || '0')
    },
    {
      namespace: 'rental',
      key: 'purchase_place',
      type: 'single_line_text_field',
      value: csvData.購入場所 || ''
    },
    {
      namespace: 'rental',
      key: 'purchase_date',
      type: 'date',
      value: csvData.購入日 || ''
    },
    {
      namespace: 'rental',
      key: 'manufacturer',
      type: 'single_line_text_field',
      value: csvData.メーカー || ''
    },
    {
      namespace: 'rental',
      key: 'designer',
      type: 'single_line_text_field',
      value: csvData.デザイナー || ''
    },
    {
      namespace: 'rental',
      key: 'maintenance_notes',
      type: 'multi_line_text_field',
      value: csvData.メンテナンス備考 || ''
    },
    {
      namespace: 'rental',
      key: 'is_disposed',
      type: 'boolean',
      value: isDisposed
    },
    {
      namespace: 'rental',
      key: 'status',
      type: 'single_line_text_field',
      value: isDisposed ? 'unavailable' : (csvData.状態 || 'available')
    }
  ];

  // 廃棄情報がある場合のみ追加
  if (isDisposed) {
    if (csvData.廃棄日) {
      metafields.push({
        namespace: 'rental',
        key: 'disposal_date',
        type: 'date',
        value: csvData.廃棄日
      });
    }
    
    if (csvData.廃棄理由) {
      metafields.push({
        namespace: 'rental',
        key: 'disposal_reason',
        type: 'multi_line_text_field',
        value: csvData.廃棄理由
      });
    }
  }

  return metafields;
}

// メインの実行関数
async function importProductsFromCsv(csvFilePath) {
  console.log('CSVからの商品インポートを開始します...');

  // CSVファイルを読み込む
  const csvData = readCsvFile(csvFilePath);
  if (csvData.length === 0) {
    console.error('CSVデータが空です。');
    return;
  }

  console.log(`${csvData.length}件の商品データを読み込みました。`);

  // 各商品を処理
  for (let i = 0; i < csvData.length; i++) {
    const item = csvData[i];
    console.log(`\n商品 ${i + 1}/${csvData.length} を処理中: ${item.Title || item.商品名}`);

    // SKUで商品を検索
    const sku = item.SKU || item.sku;
    if (!sku) {
      console.error('SKUが指定されていません。スキップします。');
      continue;
    }

    const products = await searchProducts(`sku:${sku}`, 1);
    if (products.length === 0) {
      console.error(`SKU ${sku} の商品が見つかりませんでした。スキップします。`);
      continue;
    }

    const product = products[0];
    console.log(`商品 ${product.title} が見つかりました。ID: ${product.id}`);

    // メタフィールドを生成
    const metafields = generateMetafields(item);

    // variant_mappingのIDを設定
    const variantMappingIndex = metafields.findIndex(m => m.namespace === 'rental' && m.key === 'variant_mapping');
    if (variantMappingIndex !== -1) {
      metafields[variantMappingIndex].value.variants[0].id = product.id;
    }

    // メタフィールドを設定
    await setMetafields(product.id, metafields);

    console.log(`商品 ${product.title} のメタフィールドを設定しました。`);
  }

  console.log('\nCSVからの商品インポートが完了しました。');
}

// コマンドライン引数からCSVファイルパスを取得
const csvFilePath = process.argv[2];
if (!csvFilePath) {
  console.error('CSVファイルパスを指定してください。例: node import-products-from-csv.js path/to/products.csv');
  process.exit(1);
}

// スクリプト実行
importProductsFromCsv(csvFilePath).catch(error => {
  console.error('スクリプト実行中にエラーが発生しました:', error);
  process.exit(1);
});
