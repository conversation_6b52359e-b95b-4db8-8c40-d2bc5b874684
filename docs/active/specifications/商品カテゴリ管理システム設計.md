# 商品カテゴリ管理システム設計

## 概要

商品カテゴリとSKUの連動管理システムを設計し、アプリでの効率的なカテゴリ管理を実現する。

## 現状分析

### 既存ファイル
- `商品カテゴリマスタ.csv`: 基本カテゴリコード（18件）
- `タグマスタ.csv`: 同内容（重複）
- `【重要】商品カテゴリ一覧.xlsx`: 詳細階層構造
- `rental_items_20250509_153839.csv`: 実商品データ（7702件）

### SKU構造パターン
```
[カテゴリコード]-[サブカテゴリ]-[連番]
例: 212-05-023 (花器-サブ05-連番023)
```

### アプリでの現在の使用状況
- `app/bookings/aggregate`: カテゴリフィルタリング機能
- 商品の `basicInfo.category` に格納
- 動的カテゴリ一覧抽出

## 設計方針

### 1. 統合マスタデータ構造

```typescript
interface CategoryMaster {
  code: string;           // カテゴリコード（例: "212"）
  name: string;           // カテゴリ名（例: "花器"）
  parentCode?: string;    // 親カテゴリコード
  level: number;          // 階層レベル（1=大カテゴリ, 2=中カテゴリ, 3=小カテゴリ）
  displayOrder: number;   // 表示順序
  isActive: boolean;      // 有効フラグ
  description?: string;   // 説明
  subCategories?: SubCategory[];
}

interface SubCategory {
  code: string;           // サブカテゴリコード（例: "05"）
  name: string;           // サブカテゴリ名
  parentCategoryCode: string;
  isActive: boolean;
}
```

### 2. SKU管理ルール

#### SKU生成ルール
```typescript
// SKU形式: [カテゴリコード]-[サブカテゴリ]-[連番]
// 例: 212-05-023

interface SKUStructure {
  categoryCode: string;    // 3桁カテゴリコード
  subCategoryCode: string; // 2桁サブカテゴリコード
  serialNumber: string;    // 3桁連番
}

function generateSKU(categoryCode: string, subCategoryCode: string): string {
  const nextSerial = getNextSerialNumber(categoryCode, subCategoryCode);
  return `${categoryCode}-${subCategoryCode}-${nextSerial.padStart(3, '0')}`;
}
```

#### カテゴリ検証ルール
```typescript
function validateSKU(sku: string): boolean {
  const pattern = /^(\d{3})-(\d{2})-(\d{3})$/;
  const match = sku.match(pattern);
  
  if (!match) return false;
  
  const [, categoryCode, subCategoryCode] = match;
  
  // カテゴリコードの存在確認
  const categoryExists = categoryMaster.some(cat => cat.code === categoryCode);
  
  // サブカテゴリコードの存在確認
  const subCategoryExists = subCategoryMaster.some(
    sub => sub.code === subCategoryCode && sub.parentCategoryCode === categoryCode
  );
  
  return categoryExists && subCategoryExists;
}
```

### 3. データベース設計

#### 新テーブル: CategoryMaster
```sql
CREATE TABLE category_masters (
  id VARCHAR PRIMARY KEY,
  code VARCHAR(10) NOT NULL UNIQUE,
  name VARCHAR(100) NOT NULL,
  parent_code VARCHAR(10),
  level INTEGER NOT NULL,
  display_order INTEGER NOT NULL,
  is_active BOOLEAN DEFAULT true,
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_category_masters_code ON category_masters(code);
CREATE INDEX idx_category_masters_parent ON category_masters(parent_code);
```

#### 新テーブル: SubCategoryMaster
```sql
CREATE TABLE sub_category_masters (
  id VARCHAR PRIMARY KEY,
  code VARCHAR(10) NOT NULL,
  name VARCHAR(100) NOT NULL,
  parent_category_code VARCHAR(10) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  display_order INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(code, parent_category_code),
  FOREIGN KEY (parent_category_code) REFERENCES category_masters(code)
);
```

### 4. アプリ連動機能

#### カテゴリ管理API
```typescript
// app/routes/api.categories.tsx
export async function loader() {
  const categories = await prisma.categoryMaster.findMany({
    where: { isActive: true },
    include: { subCategories: true },
    orderBy: { displayOrder: 'asc' }
  });
  
  return json({ categories });
}
```

#### SKU自動生成機能
```typescript
// app/services/sku-generator.service.ts
export class SKUGeneratorService {
  async generateSKU(categoryCode: string, subCategoryCode: string): Promise<string> {
    // 既存SKUから次の連番を取得
    const lastProduct = await prisma.product.findFirst({
      where: {
        sku: { startsWith: `${categoryCode}-${subCategoryCode}-` }
      },
      orderBy: { sku: 'desc' }
    });
    
    let nextSerial = 1;
    if (lastProduct) {
      const match = lastProduct.sku.match(/(\d{3})$/);
      if (match) {
        nextSerial = parseInt(match[1]) + 1;
      }
    }
    
    return `${categoryCode}-${subCategoryCode}-${nextSerial.toString().padStart(3, '0')}`;
  }
}
```

## 実装計画

### フェーズ1: マスタデータ整備
1. Excelファイルからカテゴリ階層構造を抽出
2. 統合カテゴリマスタCSVを作成
3. サブカテゴリマスタCSVを作成

### フェーズ2: データベース実装
1. Prismaスキーマにテーブル追加
2. マイグレーション実行
3. マスタデータインポート機能作成

### フェーズ3: アプリ機能実装
1. カテゴリ管理画面作成
2. SKU自動生成機能実装
3. 既存商品のカテゴリ情報更新

### フェーズ4: 検証・最適化
1. SKU重複チェック機能
2. カテゴリ整合性チェック
3. パフォーマンス最適化

## 期待効果

1. **SKU管理の自動化**: カテゴリに基づく自動SKU生成
2. **データ整合性向上**: カテゴリとSKUの連動検証
3. **検索性能向上**: 構造化されたカテゴリによる効率的フィルタリング
4. **運用効率化**: 統一されたカテゴリ管理ルール
