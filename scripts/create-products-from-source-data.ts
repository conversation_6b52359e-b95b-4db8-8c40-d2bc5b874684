import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();

// Shopify API設定
const config = {
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block',
  apiSecretKey: process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
};

// 設定の検証
if (!config.apiSecretKey) {
  console.error('エラー: SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません。');
  process.exit(1);
}

// ショップ名を正規化
const shopName = config.shop.replace('.myshopify.com', '');

// Shopify GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${shopName}.myshopify.com/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': config.apiSecretKey,
      'Content-Type': 'application/json',
    },
  }
);

interface ProductData {
  productCode: string;
  detailCode: string;
  categoryId: number;
  categoryName: string;
  sku: string;
  itemNumber: number;
  title: string;
  kana: string;
  description: string;
  remarks: string;
  price: number;
  quantity: number;
  location: string;
  width: number;
  depth: number;
  height: number;
  weight: number;
  purchaseDate: string;
  purchasePrice: number;
  status: number;
  maintenanceNotes: string;
}

// 元データ（ユーザーから提供された情報）
const sourceData: ProductData[] = [
  {
    productCode: "10101007",
    detailCode: "1",
    categoryId: 3,
    categoryName: "ソファ",
    sku: "10101007",
    itemNumber: 1,
    title: "85_●ﾍﾞｰｼｯｸｿﾌｧ ｵﾌﾎﾜｲﾄ 1ｼｰﾀｰ",
    kana: "ﾍﾞｰｼｯｸｿﾌｧｵﾌﾎﾜｲﾄ1ｼｰﾀｰﾔﾏﾅﾘ",
    description: "１　背面向かって右側うっすら黒いしみ",
    remarks: "",
    price: 8000,
    quantity: 1,
    location: "NY",
    width: 87,
    depth: 74,
    height: 76,
    weight: 0,
    purchaseDate: "19700101",
    purchasePrice: 0,
    status: 1,
    maintenanceNotes: "●2台ともクリーニング済みですが全体的に黄ばみ発生（H30.12.15)／[002]向かって左アーム手前と正面左側に黄色い輪染み有／[001]背面向かって右側うっすら黒いしみあり(写真には写りません)　SW52/SD54/SH40"
  },
  {
    productCode: "10101007",
    detailCode: "2", 
    categoryId: 3,
    categoryName: "ソファ",
    sku: "10101007",
    itemNumber: 2,
    title: "85_●ﾍﾞｰｼｯｸｿﾌｧ ｵﾌﾎﾜｲﾄ 1ｼｰﾀｰ",
    kana: "ﾍﾞｰｼｯｸｿﾌｧｵﾌﾎﾜｲﾄ1ｼｰﾀｰﾔﾏﾅﾘ",
    description: "２　向かって左アーム手前と正面左側に黄色い輪染み有",
    remarks: "",
    price: 8000,
    quantity: 1,
    location: "NY",
    width: 87,
    depth: 74,
    height: 76,
    weight: 0,
    purchaseDate: "19700101",
    purchasePrice: 0,
    status: 1,
    maintenanceNotes: "●2台ともクリーニング済みですが全体的に黄ばみ発生（H30.12.15)／[002]向かって左アーム手前と正面左側に黄色い輪染み有／[001]背面向かって右側うっすら黒いしみあり(写真には写りません)　SW52/SD54/SH40"
  }
];

// GraphQLクエリ定義
const CREATE_PRODUCT = gql`
  mutation productCreate($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
        status
        variants(first: 20) {
          nodes {
            id
            title
            sku
            price
            inventoryItem {
              id
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

const UPDATE_VARIANT = gql`
  mutation productVariantUpdate($input: ProductVariantInput!) {
    productVariantUpdate(input: $input) {
      productVariant {
        id
        title
        price
        sku
        inventoryItem {
          id
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

const SET_METAFIELDS = gql`
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
      }
      userErrors {
        field
        message
      }
    }
  }
`;

async function createCompleteProduct(data: ProductData) {
  console.log(`🛍️ 商品作成開始: ${data.title} [${data.detailCode}]`);

  // 1. Shopify商品を作成（正しいGraphQL形式）
  const productInput = {
    title: `${data.title} [${data.detailCode}]`,
    handle: `${data.productCode}-detail-${data.detailCode}`,
    descriptionHtml: `
      <h3>${data.title}</h3>
      <p><strong>商品番号:</strong> ${data.productCode}-${data.detailCode}</p>
      <p><strong>状態:</strong> ${data.description}</p>
      <p><strong>サイズ:</strong> 幅${data.width}cm × 奥行${data.depth}cm × 高さ${data.height}cm</p>
      <p><strong>カテゴリ:</strong> ${data.categoryName}</p>
      <p><strong>保管場所:</strong> ${data.location}</p>
      ${data.maintenanceNotes ? `<p><strong>メンテナンス備考:</strong> ${data.maintenanceNotes}</p>` : ''}
    `,
    vendor: 'IZIZ RENTAL',
    productType: data.categoryName,
    tags: [`${data.categoryName}`, `商品番号:${data.productCode}`, `詳細:${data.detailCode}`, `場所:${data.location}`],
    status: 'ACTIVE'
  };

  try {
    const productResult = await client.request(CREATE_PRODUCT, { input: productInput });
    
    if (productResult.productCreate.userErrors.length > 0) {
      console.error('❌ 商品作成エラー:', productResult.productCreate.userErrors);
      return null;
    }

    const shopifyProduct = productResult.productCreate.product;
    console.log(`✅ Shopify商品作成完了: ${shopifyProduct.id}`);

    // 2. デフォルトバリアントを更新（レンタル用に設定）
    const defaultVariantId = shopifyProduct.variants.nodes[0].id;
    const variantUpdateResult = await client.request(UPDATE_VARIANT, {
      input: {
        id: defaultVariantId,
        sku: `${data.productCode}-${data.detailCode}`,
        price: data.price.toString(),
        title: 'レンタル商品'
      }
    });

    if (variantUpdateResult.productVariantUpdate.userErrors.length > 0) {
      console.error('❌ バリアント更新エラー:', variantUpdateResult.productVariantUpdate.userErrors);
      return null;
    }

    console.log(`✅ デフォルトバリアント更新完了: SKU=${data.productCode}-${data.detailCode}`);

    // 4. メタフィールドを設定
    const metafields = [
      {
        ownerId: shopifyProduct.id,
        namespace: 'product',
        key: 'depth',
        value: data.depth.toString(),
        type: 'number_integer'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'product',
        key: 'height', 
        value: data.height.toString(),
        type: 'number_integer'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'product',
        key: 'width',
        value: data.width.toString(),
        type: 'number_integer'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'basic_info',
        value: JSON.stringify({
          productCode: data.productCode,
          detailCode: data.detailCode,
          kana: data.kana,
          location: data.location,
          status: 'available'
        }),
        type: 'json'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'pricing',
        value: JSON.stringify({
          basePrice: data.price,
          depositRate: 0.1,
          minimumDays: 1,
          maximumDays: 30,
          dailyRate: data.price
        }),
        type: 'json'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'inventory_items',
        value: JSON.stringify([
          {
            itemId: `${data.productCode}-${data.detailCode}`,
            condition: data.description,
            lastInspectionDate: new Date().toISOString().split('T')[0],
            maintenanceNotes: data.maintenanceNotes,
            status: 'available'
          }
        ]),
        type: 'json'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'reservation_info',
        value: JSON.stringify({
          reservations: [],
          maintenanceSchedule: [],
          availabilityRules: {
            minimumNotice: 1,
            maximumAdvance: 180,
            blackoutDates: []
          }
        }),
        type: 'json'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'booking_notes',
        value: data.description ? `特記事項: ${data.description}` : '特別な指示はありません。通常通りの予約が可能です。',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'booking_type',
        value: 'confirmed',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'color',
        value: 'オフホワイト',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'location',
        value: data.location,
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'maintenance_notes',
        value: data.maintenanceNotes,
        type: 'multi_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'manufacturer',
        value: '',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'material',
        value: '',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'purchase_date',
        value: data.purchaseDate !== '19700101' ? data.purchaseDate : '',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'purchase_place',
        value: '',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'purchase_price',
        value: data.purchasePrice.toString(),
        type: 'number_decimal'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'status',
        value: 'available',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'variation_type',
        value: 'rental_period',
        type: 'single_line_text_field'
      }
    ];

    // メタフィールドを一括設定
    const metafieldResult = await client.request(SET_METAFIELDS, { metafields });
    
    if (metafieldResult.metafieldsSet.userErrors.length > 0) {
      console.error('⚠️ メタフィールド設定エラー:', metafieldResult.metafieldsSet.userErrors);
    } else {
      console.log(`✅ メタフィールド設定完了: ${metafieldResult.metafieldsSet.metafields.length}件`);
    }

    // 4. Prismaに商品データを登録
    try {
      const productId = shopifyProduct.id.replace('gid://shopify/Product/', '');
      
      const prismaProduct = await prisma.product.create({
        data: {
          shopifyProductId: productId,
          title: shopifyProduct.title,
          handle: shopifyProduct.handle,
          sku: `${data.productCode}-${data.detailCode}`,
          shop: `${shopName}.myshopify.com`,
          basicInfo: {
            productCode: data.productCode,
            detailCode: data.detailCode,
            kana: data.kana,
            location: data.location,
            status: 'available'
          },
          pricing: {
            basePrice: data.price,
            depositRate: 0.1,
            discountRules: {
              day2_6_rate: 0.2,
              day7_plus_rate: 0.1
            },
            minimumDays: 1,
            maximumDays: 30
          },
          dimensions: {
            width: data.width,
            depth: data.depth,  
            height: data.height,
            weight: data.weight || 0
          },
          metadata: {
            category: data.categoryName,
            description: data.description,
            maintenanceNotes: data.maintenanceNotes,
            purchaseDate: data.purchaseDate,
            purchasePrice: data.purchasePrice
          },
          syncStatus: 'synced',
          lastSyncedAt: new Date()
        }
      });

      console.log(`✅ Prisma商品登録完了: ${prismaProduct.id}`);

      // 5. バリアント情報をPrismaに登録
      for (const [index, variant] of shopifyProduct.variants.nodes.entries()) {
        const variantData = variants[index];
        if (variantData) {
          await prisma.productVariant.create({
            data: {
              shopifyVariantId: variant.id.replace('gid://shopify/ProductVariant/', ''),
              productId: prismaProduct.id,
              title: variant.title,
              sku: variantData.sku,
              price: variantData.price,
              inventoryManagement: 'shopify',
              inventoryPolicy: 'deny',
              position: index + 1
            }
          });
        }
      }

      console.log(`✅ バリアント登録完了: ${shopifyProduct.variants.nodes.length}件`);

      return {
        shopifyProduct,
        prismaProduct,
        metafieldsCount: metafields.length
      };

    } catch (error) {
      console.error('❌ Prisma登録エラー:', error);
      return {
        shopifyProduct,
        prismaProduct: null,
        metafieldsCount: metafields.length
      };
    }

  } catch (error) {
    console.error('❌ 商品作成エラー:', error);
    return null;
  }
}

async function main() {
  try {
    console.log('🚀 元データから完全商品登録を開始...');
    console.log(`📦 登録対象: ${sourceData.length}件の商品（2脚のソファ）`);

    for (const data of sourceData) {
      const result = await createCompleteProduct(data);
      
      if (result) {
        console.log(`✅ 商品登録完了: ${data.title} [${data.detailCode}]`);
        console.log(`   - Shopify ID: ${result.shopifyProduct.id}`);
        console.log(`   - メタフィールド: ${result.metafieldsCount}件`);
        console.log(`   - バリアント: ${result.shopifyProduct.variants.nodes.length}件`);
        if (result.prismaProduct) {
          console.log(`   - Prisma ID: ${result.prismaProduct.id}`);
        }
      } else {
        console.error(`❌ 商品登録失敗: ${data.title} [${data.detailCode}]`);
      }

      // API制限回避のため少し待機
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('🎉 全ての商品登録が完了しました！');
    console.log('📊 結果:');
    console.log('   - ソファ商品 [1]: 背面右側に黒いしみ');
    console.log('   - ソファ商品 [2]: 左アーム前と正面左側に黄色い輪染み');
    console.log('   - 各商品に9つのレンタル期間バリアント');
    console.log('   - 完全なメタフィールド設定（inventory_items, reservation_info含む）');

  } catch (error) {
    console.error('❌ エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// メイン実行
main().catch(console.error);

export { createCompleteProduct, sourceData };