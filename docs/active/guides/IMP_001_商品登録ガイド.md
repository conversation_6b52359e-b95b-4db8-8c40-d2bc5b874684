# 商品登録ガイド

## 概要

このガイドでは、CSVデータから商品をクレンジングし、Shopifyに登録する方法について説明します。

## 前提条件

- Node.js v20.18.1以上がインストールされていること
- 必要なパッケージがインストールされていること（graphql-request, graphql, csv-parse）
- Shopify Admin APIのアクセストークンが設定されていること

## 1. 環境設定

### Node.jsのバージョン確認

```bash
node -v
```

Node.jsのバージョンが20.18.1未満の場合は、以下のいずれかの方法でアップグレードしてください。

#### nvmを使用する場合

```bash
nvm install 20.18.1
nvm use 20.18.1
```

#### Homebrewを使用する場合

```bash
brew install node@20
export PATH="/usr/local/opt/node@20/bin:$PATH"
```

### 必要なパッケージのインストール

```bash
npm install graphql-request graphql csv-parse
```

## 2. データクレンジング

### 2.1 CSVデータの準備

以下のCSVファイルを用意します。

- `rental_items_20250509_153839.csv`：Webサイト用の商品データ
- `商品一覧_202505031329-sofa.csv`：データベース用の商品データ

これらのファイルは `/Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp/master-data-csv/` に配置されています。

### 2.2 データクレンジングスクリプトの実行

以下のコマンドを実行して、CSVデータをクレンジングします。

```bash
cd /Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp
export PATH="/usr/local/opt/node@20/bin:$PATH"
node scripts/clean-product-data.js
```

このスクリプトは以下の処理を行います。

1. CSVファイルからデータを読み込む
2. 商品名から不要なプレフィックスやサフィックスを削除する
3. 半角カタカナを全角カタカナに変換する
4. SKUを正規化する
5. 料金計算を行う（1日料金をベースに2日以降の料金を計算）
6. メタフィールドデータを生成する
7. クレンジングされたデータをCSVファイルに出力する

出力されたCSVファイルは `/Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp/master-data-csv/cleaned_products.csv` に保存されます。

## 3. Shopifyへの商品登録

### 3.1 環境変数の設定

`.env`ファイルに以下の環境変数を設定します。

```
SHOPIFY_SHOP=your-store.myshopify.com
SHOPIFY_ADMIN_API_ACCESS_TOKEN=your-admin-api-access-token
```

### 3.2 商品登録スクリプトの実行

以下のコマンドを実行して、クレンジングされたデータをShopifyに登録します。

```bash
cd /Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp
export PATH="/usr/local/opt/node@20/bin:$PATH"
node scripts/import-products-to-shopify.js
```

このスクリプトは以下の処理を行います。

1. クレンジングされたCSVファイルからデータを読み込む
2. 商品データをグループ化する（バリアントごとにグループ化）
3. Shopify Admin APIを使用して商品を作成する
4. 各商品のバリアントを作成する
5. メタフィールドを設定する

## 4. 注意事項

### 4.1 商品名の表記

商品名は以下のルールに従ってクレンジングされます。

- 【】内のテキストを削除
- 数字_や《》内のテキストを削除
- ●や◇販売価格￥\d+◇を削除
- 先頭と末尾の空白を削除

### 4.2 カタカナの表記

商品名のフリガナは、半角カタカナから全角カタカナに変換されます。例えば：

- `ﾍﾞｰｼｯｸｿﾌｧｵﾌﾎﾜｲﾄ1ｼｰﾀｰ` → `ベーシックソファオフホワイト1シーター`

### 4.3 SKUの形式

SKUは以下の形式に正規化されます。

```
商品コード-詳細コード-バリアント番号
```

例：
- 1日レンタル：`10101007-001-001`
- 2日レンタル：`10101007-001-002`
- 3日レンタル：`10101007-001-003`

### 4.4 料金計算

料金は以下のルールに従って計算されます。

- 1日料金：基本料金
- 2〜7日料金：基本料金 + (日数 - 1) × 基本料金 × 0.2
- 8日以上料金：基本料金 + 6 × 基本料金 × 0.2 + 基本料金 × 0.1

例えば、基本料金が8,000円の場合：
- 1日料金：8,000円
- 2日料金：9,600円（8,000円 + 1 × 8,000円 × 0.2）
- 3日料金：11,200円（8,000円 + 2 × 8,000円 × 0.2）
- 8日以上料金：18,400円（8,000円 + 6 × 8,000円 × 0.2 + 8,000円 × 0.1）

### 4.5 廃棄済み商品の処理

廃棄済みの商品（`廃業区分` が `1` の商品）は、以下のように処理されます。

- `rental.is_disposed` メタフィールドが `true` に設定される
- `rental.status` メタフィールドが `unavailable` に設定される
- 廃棄日（`廃棄日`）が指定されている場合は `rental.disposal_date` メタフィールドが設定される
- 廃棄理由（`備考`）が指定されている場合は `rental.disposal_reason` メタフィールドが設定される

### 4.6 メタフィールド

以下のメタフィールドが設定されます。

- `rental.basic_info`：商品の基本情報（商品コード、詳細コード、カナ、寸法情報など）
- `rental.pricing`：料金情報（基本料金、デポジット率、割引ルールなど）
- `rental.inventory_items`：在庫アイテム情報（ID、SKU、状態、場所など）
- `rental.reservation_info`：予約情報（在庫アイテムごとの予約情報）
- `rental.color`：色
- `rental.material`：素材
- `product.height`：高さ
- `product.width`：幅
- `product.depth`：奥行き
- `rental.location`：在庫場所
- `rental.is_disposed`：廃棄済みかどうか
- `rental.status`：レンタル状態
- `rental.disposal_date`：廃棄日（廃棄済みの場合のみ）
- `rental.disposal_reason`：廃棄理由（廃棄済みの場合のみ）

## 5. トラブルシューティング

### 5.1 Node.jsのバージョンエラー

以下のようなエラーが発生した場合は、Node.jsのバージョンが古い可能性があります。

```
npm error code EBADENGINE
npm error engine Unsupported engine
npm error engine Not compatible with your version of node/npm: undici@7.9.0
npm error notsup Not compatible with your version of node/npm: undici@7.9.0
npm error notsup Required: {"node":">=20.18.1"}
npm error notsup Actual:   {"npm":"10.8.2","node":"v20.17.0"}
```

Node.jsをv20.18.1以上にアップグレードしてください。

### 5.2 APIレート制限

Shopify Admin APIにはレート制限があります。多数の商品を登録する場合は、スクリプトの `BATCH_SIZE` を小さくして、一度に処理する商品数を減らすことを検討してください。

### 5.3 エラーログの確認

スクリプト実行中にエラーが発生した場合は、コンソールに出力されるエラーメッセージを確認してください。エラーメッセージには、問題の原因と解決策が含まれている場合があります。

## 6. 在庫設定の自動化

商品登録後、在庫設定を自動化するスクリプトを使用して、商品の在庫状態を一括で設定することができます。

### 6.1 在庫設定の基本ルール

在庫設定は以下のルールに従って自動化されます：

1. **在庫の場所**：メタフィールド `rental.location` または `rental.inventory_items[0].location` の値に基づいて、「PR」または「NY」に設定されます。
2. **基本在庫数**：通常の商品は在庫数1で設定されます。
3. **バリアント**：バリアントがある場合は、各バリアントごとに在庫数1で設定されます。
4. **商品状態**：商品状態が「廃棄済み」または「メンテナンス中」の場合は、在庫数0で設定されます。

### 6.2 在庫設定スクリプトの実行

以下のコマンドを実行して、在庫設定を自動化します：

```bash
cd /Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp
export PATH="/usr/local/opt/node@20/bin:$PATH"
node scripts/update-inventory-settings.js
```

このスクリプトは以下の処理を行います：

1. Shopifyの在庫ロケーションを取得する
2. すべての商品とそのバリアントを取得する
3. 各商品の状態と在庫場所を確認する
4. 商品状態に基づいて適切な在庫数を設定する
5. 在庫場所（PR/NY）に基づいて適切なロケーションに在庫を設定する

### 6.3 在庫設定の確認

在庫設定が完了したら、Shopify管理画面で在庫状態を確認してください。以下の点を確認します：

1. 各商品の在庫数が正しく設定されているか
2. 各商品の在庫場所が正しく設定されているか
3. 廃棄済みまたはメンテナンス中の商品の在庫数が0になっているか

## 7. まとめ

このガイドでは、CSVデータから商品をクレンジングし、Shopifyに登録する方法、および在庫設定を自動化する方法について説明しました。スクリプトを使用することで、大量の商品を効率的に登録し、適切な在庫設定を行うことができます。

商品データに問題がある場合は、データクレンジングスクリプトを修正して、問題を解決することができます。また、商品登録スクリプトや在庫設定スクリプトを修正して、Shopifyへの登録方法や在庫設定方法をカスタマイズすることもできます。
