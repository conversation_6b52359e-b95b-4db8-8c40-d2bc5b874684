# SKU体系とバリアント対応の現状分析

**作成日**: 2025年5月25日  
**目的**: 現在のSKU体系とバリアント対応の確認

## 📊 現在のSKU体系

### **SKUフォーマット**
```
基本形式: {商品コード}-{詳細コード}-{バリアント}
例: 10101031-001-1D

構成要素:
- 商品コード: 10101031（型番）
- 詳細コード: 001（個体番号）
- バリアント: 1D（1日レンタル）
```

### **バリアントサフィックス**
- `1D` - 1日レンタル
- `2D` - 2日レンタル
- `3D` - 3日レンタル
- `4D` - 4日レンタル
- `5D` - 5日レンタル
- `6D` - 6日レンタル
- `7D` - 7日レンタル
- `8D+` - 8日以上レンタル
- `PROV` - 仮予約

## 🔍 現在の実装状況

### **1. SKUフォーマッター（sku-formatter.js）**
- ✅ バリアント対応済み
- ✅ レンタル日数からバリアント生成
- ✅ ハイフン付きフォーマット対応

```javascript
// 例
formatSkuWithHyphens("10101031") 
// → "101-01-031"

addVariantToSku("10101031-001", "1D") 
// → "10101031-001-1D"

generateVariantFromDays(3) 
// → "3D"
generateVariantFromDays(10) 
// → "8D"（8日以上は8Dで統一）
```

### **2. 商品登録スクリプト**
各スクリプトでバリアント付きSKUを生成：

```javascript
// create-product-with-group-management.ts
const variantInput = {
  sku: `${data.productCode}-${data.detailCode}-${config.suffix}`,
  // 例: "10101031-001-1D"
};
```

## 🚨 確認された問題点

### **1. CSVデータのSKU**
元のCSVデータでは商品コードのみ：
```csv
商品コード: 10101031
詳細コード: 001
```

### **2. バリアント考慮の必要性**
- 各商品は9つのバリアント（1D〜8D+、PROV）を持つ
- SKU検索時にバリアントを考慮する必要がある

## 📝 更新が必要なスクリプト

### **1. 商品検索関連**
- `sku-search.ts` - バリアントを考慮した検索
- `sku-search-client.ts` - クライアント側の検索

### **2. 商品同期関連**
- `normalize-product-skus.js` - SKUの正規化
- `sync-products.js` - 商品同期時のSKU処理

### **3. テストスクリプト**
- 各種テストスクリプトでバリアント対応確認

## 🛠️ 推奨される改善

### **1. SKU検索の改善**
```javascript
// 現在: 完全一致検索
sku: "10101031-001-1D"

// 改善案: 基本SKUで全バリアントを検索
baseSku: "10101031-001"
// → 10101031-001-1D, 10101031-001-2D, ... を全て取得
```

### **2. 商品マスタとバリアントの関係**
```javascript
// Productテーブル
{
  sku: "10101031-001", // 基本SKU（バリアントなし）
  shopifyProductId: "xxx"
}

// ProductVariantテーブル
{
  productId: "xxx",
  sku: "10101031-001-1D", // バリアント付きSKU
  title: "1日レンタル"
}
```

### **3. SKU生成ユーティリティの活用**
```javascript
import { 
  formatSkuWithHyphens, 
  addVariantToSku,
  generateVariantFromDays 
} from './utils/sku-formatter';

// 統一的なSKU生成
function generateProductSku(productCode, detailCode, rentalDays = null) {
  const baseSku = `${productCode}-${detailCode}`;
  
  if (rentalDays !== null) {
    const variant = generateVariantFromDays(rentalDays);
    return addVariantToSku(baseSku, variant);
  }
  
  return baseSku;
}
```

## 💡 結論

### **現状**
- SKUフォーマッターは既にバリアント対応済み
- 商品登録スクリプトも適切にバリアント付きSKUを生成

### **改善点**
1. **商品検索**: 基本SKUでの検索機能追加
2. **データ構造**: ProductとProductVariantの関係を明確化
3. **一貫性**: 全てのスクリプトでsku-formatter.jsを使用

### **アクションアイテム**
1. ✅ SKU生成は問題なし（既にバリアント対応）
2. ⚠️ SKU検索をバリアント考慮型に改善
3. ⚠️ 商品同期時のSKU処理を確認

基本的にSKU生成スクリプトの更新は不要ですが、検索機能の改善が推奨されます。