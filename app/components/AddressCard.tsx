import React, { useState } from 'react';
import {
  Card,
  Text,
  Button,
  ButtonGroup,
  Badge,
  Divider
} from '@shopify/polaris';
import type { EnrichedAddress } from '../services/customer-address.service';

interface AddressCardProps {
  address: EnrichedAddress;
  index: number;
  showCorporateFields?: boolean;
  showMemoField?: boolean;
  onEdit?: (address: EnrichedAddress) => void;
  onDelete?: (addressId: string) => void;
  onAddExtension?: (address: EnrichedAddress) => void;
}

export function AddressCard({
  address,
  index,
  showCorporateFields = false,
  showMemoField = true,
  onEdit,
  onDelete,
  onAddExtension
}: AddressCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const hasExtension = address.extension && Object.keys(address.extension).length > 0;
  const displayName = address.displayName || `住所 ${index}`;

  const formatDate = (date: Date | string | undefined) => {
    if (!date) return '';
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleDateString('ja-JP');
  };

  const getOrderPathName = (orderPathCode?: string) => {
    const orderPaths: Record<string, string> = {
      '0': '店頭',
      '1': '電話', 
      '2': 'FAX',
      '3': 'メール',
      '4': 'WEB'
    };
    return orderPaths[orderPathCode || ''] || 'WEB';
  };

  return (
    <Card>
      <div style={{ padding: '16px' }}>
        {/* ヘッダー */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Text as="h4" variant="headingMd">{displayName}</Text>
            {address.isShopifyAddress && (
              <Badge tone="info">Shopify</Badge>
            )}
            {hasExtension && (
              <Badge tone="success">拡張情報あり</Badge>
            )}
          </div>
          
          <ButtonGroup>
            {hasExtension && onEdit && (
              <Button size="slim" onClick={() => onEdit(address)}>
                編集
              </Button>
            )}
            {!hasExtension && onAddExtension && (
              <Button size="slim" variant="plain" onClick={() => onAddExtension(address)}>
                詳細情報を追加
              </Button>
            )}
            {onDelete && (
              <Button 
                size="slim" 
                variant="plain" 
                tone="critical"
                onClick={() => onDelete(address.id || '')}
              >
                削除
              </Button>
            )}
          </ButtonGroup>
        </div>

        {/* 基本住所情報（Shopify標準） */}
        <div style={{ marginBottom: '12px' }}>
          <Text as="h5" variant="headingSm">基本住所情報</Text>
          <div style={{ marginTop: '8px' }}>
            {address.company && (
              <Text as="p" variant="bodyMd" fontWeight="bold">
                {address.company}
              </Text>
            )}
            {(address.first_name || address.last_name) && (
              <Text as="p" variant="bodyMd">
                {address.last_name} {address.first_name}
              </Text>
            )}
            {address.zip && address.province && address.city && address.address1 && (
              <>
                <Text as="p" variant="bodyMd">
                  〒{address.zip}
                </Text>
                <Text as="p" variant="bodyMd">
                  {address.province} {address.city} {address.address1}
                </Text>
              </>
            )}
            {address.address2 && (
              <Text as="p" variant="bodyMd">
                {address.address2}
              </Text>
            )}
            {address.phone && (
              <Text as="p" variant="bodyMd">
                📞 {address.phone}
              </Text>
            )}
          </div>
        </div>

        {/* 拡張情報の表示 */}
        {hasExtension && (
          <>
            <Divider />
            <div style={{ marginTop: '12px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text as="h5" variant="headingSm">詳細情報</Text>
                <Button 
                  size="slim" 
                  variant="plain"
                  onClick={() => setIsExpanded(!isExpanded)}
                >
                  {isExpanded ? '折りたたむ' : '展開する'}
                </Button>
              </div>

              {/* 常に表示する重要な情報 */}
              <div style={{ marginTop: '8px' }}>
                {address.extension?.fax && (
                  <Text as="p" variant="bodyMd">
                    📠 FAX: {address.extension.fax}
                  </Text>
                )}
                {address.extension?.storeStaff && (
                  <Text as="p" variant="bodyMd">
                    👤 担当者: {address.extension.storeStaff}
                  </Text>
                )}
                {address.extension?.orderPath && (
                  <Text as="p" variant="bodyMd">
                    📋 受注方法: {getOrderPathName(address.extension.orderPath)}
                  </Text>
                )}
              </div>

              {/* 展開時の詳細情報 */}
              {isExpanded && (
                <div style={{ marginTop: '12px', padding: '12px', backgroundColor: '#f6f6f7', borderRadius: '4px' }}>
                  {/* 法人情報（法人フラグONの場合のみ） */}
                  {showCorporateFields && (
                    <div style={{ marginBottom: '12px' }}>
                      <Text as="h6" variant="headingXs">組織情報</Text>
                      <div style={{ marginTop: '4px' }}>
                        {address.extension?.department && (
                          <Text as="p" variant="bodySm">
                            部署: {address.extension.department}
                          </Text>
                        )}
                        {address.extension?.position && (
                          <Text as="p" variant="bodySm">
                            役職: {address.extension.position}
                          </Text>
                        )}
                        {address.extension?.addressName && (
                          <Text as="p" variant="bodySm">
                            住所名称: {address.extension.addressName}
                          </Text>
                        )}
                      </div>
                    </div>
                  )}

                  {/* 配送情報 */}
                  <div style={{ marginBottom: '12px' }}>
                    <Text as="h6" variant="headingXs">配送情報</Text>
                    <div style={{ marginTop: '4px' }}>
                      {address.extension?.deliveryTimeSlot && (
                        <Text as="p" variant="bodySm">
                          配送時間帯: {address.extension.deliveryTimeSlot}
                        </Text>
                      )}
                      {address.extension?.deliveryNotes && (
                        <div style={{ marginTop: '4px' }}>
                          <Text as="p" variant="bodySm" color="subdued">配送備考:</Text>
                          <Text as="p" variant="bodySm">
                            {address.extension.deliveryNotes}
                          </Text>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 使用履歴 */}
                  {address.extension?.lastUsedAt && (
                    <div>
                      <Text as="h6" variant="headingXs">使用履歴</Text>
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm">
                          最終使用: {formatDate(address.extension.lastUsedAt)}
                        </Text>
                        {address.extension?.usageCount && (
                          <Text as="p" variant="bodySm">
                            使用回数: {address.extension.usageCount}回
                          </Text>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </>
        )}

        {/* 配送メモ（常に表示） */}
        {showMemoField && address.extension?.deliveryMemo && (
          <>
            <Divider />
            <div style={{ marginTop: '12px' }}>
              <Text as="h5" variant="headingSm">配送メモ</Text>
              <div style={{ marginTop: '8px', padding: '8px', backgroundColor: '#fff4e6', borderRadius: '4px', border: '1px solid #ffd580' }}>
                <Text as="p" variant="bodyMd">
                  {address.extension.deliveryMemo}
                </Text>
              </div>
            </div>
          </>
        )}
      </div>
    </Card>
  );
}
