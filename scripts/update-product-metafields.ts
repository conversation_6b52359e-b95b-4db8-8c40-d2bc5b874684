/**
 * 商品メタフィールド更新スクリプト
 *
 * このスクリプトは、指定された商品のメタフィールドを最新の構造に更新します。
 * 特に、予約情報のメタフィールドを新しいバリアント対応の構造に変換します。
 *
 * 使用方法:
 * npx tsx scripts/update-product-metafields.ts --id [商品ID] [--force]
 * 
 * オプション:
 * --id: 更新する商品のID（必須）
 * --force: 強制的に更新する（オプション）
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { format } from 'date-fns';
import { authenticate } from '../app/shopify.server';
import { updateBookingMetafield } from '../app/utils/booking/metafield-updater';
import { parseArgs } from 'node:util';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

/**
 * メイン処理
 */
async function main() {
  try {
    // コマンドライン引数を解析
    const args = parseArgs({
      options: {
        id: { type: 'string' },
        force: { type: 'boolean', default: false }
      },
      strict: false
    });

    const productId = args.values.id;
    const forceUpdate = args.values.force || false;

    if (!productId) {
      console.error('商品IDを指定してください');
      console.error('使用方法: npx tsx scripts/update-product-metafields.ts --id [商品ID] [--force]');
      process.exit(1);
    }

    console.log(`商品ID ${productId} のメタフィールドを更新します...`);
    console.log(`強制更新: ${forceUpdate ? 'はい' : 'いいえ'}`);

    // 商品情報を取得
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!product) {
      console.error(`商品ID ${productId} が見つかりません`);
      process.exit(1);
    }

    console.log('商品情報:', {
      id: product.id,
      title: product.title,
      shopifyId: product.shopifyId,
      sku: product.sku
    });

    // Shopify Admin APIを初期化
    console.log('Shopify Admin APIを初期化中...');
    
    // リクエストオブジェクトを作成（ダミー）
    const headers = new Headers();
    headers.append('Content-Type', 'application/json');
    
    const request = new Request('https://example.com', {
      method: 'GET',
      headers: headers
    });
    
    // authenticate.adminを呼び出す
    const { admin } = await authenticate.admin(request);
    
    console.log('Shopify Admin APIの初期化が完了しました');

    // メタフィールドを更新
    console.log('メタフィールドの更新を開始します...');
    
    const result = await updateBookingMetafield(
      prisma,
      admin,
      productId,
      forceUpdate,
      3 // リトライ回数
    );

    console.log('メタフィールド更新結果:', result);

    if (result.success) {
      if (result.updated) {
        console.log('メタフィールドが正常に更新されました');
      } else {
        console.log('メタフィールドに変更がないため、更新はスキップされました');
      }
    } else {
      console.error('メタフィールドの更新に失敗しました:', result.error);
    }

    console.log('処理が完了しました');
    process.exit(0);
  } catch (error) {
    console.error('エラーが発生しました:', error);
    process.exit(1);
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
