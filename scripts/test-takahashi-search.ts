/**
 * 高橋検索テストスクリプト
 * 
 * このスクリプトは、「高橋」という名前で顧客検索を行い、
 * 検索結果が正しく返されるかをテストします。
 * 
 * 使用方法:
 * npx tsx scripts/test-takahashi-search.ts
 */

import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// 顧客検索用GraphQLクエリ
const SEARCH_CUSTOMERS_QUERY = gql`
  query searchCustomers($query: String!, $first: Int!) {
    customers(first: $first, query: $query) {
      edges {
        node {
          id
          firstName
          lastName
          email
          phone
        }
      }
    }
  }
`;

// 顧客検索関数
async function searchCustomers(query: string, limit = 10) {
  console.log(`\n=== 顧客検索テスト: "${query}" ===`);
  
  try {
    // APIリクエスト
    const response = await client.request(SEARCH_CUSTOMERS_QUERY, {
      query,
      first: limit
    });
    
    const customers = response.customers.edges;
    
    console.log(`${customers.length}件の顧客が見つかりました`);
    
    if (customers.length > 0) {
      customers.forEach((edge: any, index: number) => {
        const customer = edge.node;
        console.log(`\n[${index + 1}] 顧客情報:`);
        console.log(`ID: ${customer.id}`);
        console.log(`数値ID: ${customer.id.replace('gid://shopify/Customer/', '')}`);
        console.log(`名前: ${customer.firstName} ${customer.lastName}`);
        console.log(`メール: ${customer.email}`);
      });
    }
    
    return customers;
  } catch (error: any) {
    console.error('エラー:', error.message);
    if (error.response?.errors) {
      console.error('GraphQLエラー:', JSON.stringify(error.response.errors, null, 2));
    }
    return [];
  }
}

// メイン関数
async function main() {
  console.log('高橋検索テストを開始します...');
  console.log(`ショップ: ${process.env.SHOPIFY_SHOP}`);
  
  // テストケース
  const testQueries = [
    // 基本的な検索
    '高橋',
    '高橋 健太',
    '高橋健太',
    '健太 高橋',
    '健太高橋',
    
    // 部分一致検索
    '高',
    '橋',
    '健太',
    
    // メールアドレス検索
    'takahashi.kenta',
    
    // Shopify公式ドキュメントに基づく検索構文
    'first_name:健太',
    'last_name:高橋',
    'email:takahashi',
  ];
  
  // 各テストケースを実行
  for (const query of testQueries) {
    await searchCustomers(query);
  }
  
  console.log('\n高橋検索テストが完了しました');
}

// スクリプトを実行
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('予期しないエラーが発生しました:', error);
    process.exit(1);
  });
