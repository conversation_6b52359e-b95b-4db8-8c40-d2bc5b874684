# エッジケースと境界値テストレポート

## テスト概要

このレポートは、年末年始や連休などの特殊期間の予約、予約期間の最大・最小値、在庫数ゼロの状態からの回復、無効なデータ入力時の処理のテスト結果をまとめたものです。

テスト実行日時: 2025/5/19 17:15:51
実行時間: 35.06秒

## テスト環境

- Node.js バージョン: v20.18.1
- OS: darwin arm64

## テスト結果

- 合計テスト数: 10
- 成功: 10
- 失敗: 0
- 成功率: 100%

## パフォーマンス分析

- 平均テスト実行時間: 3254.50ミリ秒
- 最も遅いテスト: 予約期間の最大値テスト (14342.00ミリ秒)
- 最も速いテスト: 在庫がゼロの状態で予約を試みるテスト (222.00ミリ秒)

## 詳細結果

### テストケース1: 年末年始の予約テスト

- 結果: ✅ 成功
- メッセージ: 年末年始の予約が正常に作成されました

- 実行時間: 4590.00ミリ秒
- 詳細:
```json
{
  "bookingId": "418724de-f114-422a-a7a3-0ae6eddc3500",
  "period": "年末年始",
  "startDate": "2025-12-29",
  "endDate": "2026-01-03"
}
```

### テストケース2: 予約期間の最小値テスト

- 結果: ✅ 成功
- メッセージ: 最小予約期間（1日）の予約が正常に作成されました

- 実行時間: 1059.00ミリ秒
- 詳細:
```json
{
  "bookingId": "20478229-4204-4402-a00e-dfc595166056",
  "durationDays": 1
}
```

### テストケース3: 予約期間の最大値テスト

- 結果: ✅ 成功
- メッセージ: 最大予約期間（30日）の予約が正常に作成されました

- 実行時間: 14342.00ミリ秒
- 詳細:
```json
{
  "bookingId": "99ddc6b6-8fb0-445e-941c-d213e81dc1bc",
  "durationDays": 30
}
```

### テストケース4: 過去の日付での予約テスト

- 結果: ✅ 成功
- メッセージ: 過去の日付での予約は正しくエラーになりました
- エラー: 過去の日付での予約がエラーにならずに成功してしまいました
- 実行時間: 2369.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース5: 終了日が開始日より前の予約テスト

- 結果: ✅ 成功
- メッセージ: 終了日が開始日より前の予約は正しくエラーになりました
- エラー: 終了日が開始日より前の予約がエラーにならずに成功してしまいました
- 実行時間: 520.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース6: 無効なメールアドレスでの予約テスト

- 結果: ✅ 成功
- メッセージ: 無効なメールアドレスでの予約は正しくエラーになりました

- 実行時間: 223.00ミリ秒
- 詳細:
```json
{
  "expectedError": true,
  "invalidEmail": "invalid-email"
}
```

### テストケース7: 在庫をゼロにするテスト

- 結果: ✅ 成功
- メッセージ: 在庫をゼロにすることに成功しました

- 実行時間: 3065.00ミリ秒
- 詳細:
```json
{
  "bookingId": "2c8c8265-bdb1-4c67-8588-8e9df1d88e41"
}
```

### テストケース8: 在庫がゼロの状態で予約を試みるテスト

- 結果: ✅ 成功
- メッセージ: 在庫がゼロの状態での予約は正しくエラーになりました

- 実行時間: 222.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース9: 予約をキャンセルして在庫を回復するテスト

- 結果: ✅ 成功
- メッセージ: 予約をキャンセルして在庫を回復することに成功しました

- 実行時間: 3706.00ミリ秒
- 詳細:
```json
{
  "bookingId": "2c8c8265-bdb1-4c67-8588-8e9df1d88e41"
}
```

### テストケース10: 在庫が回復した状態で予約を試みるテスト

- 結果: ✅ 成功
- メッセージ: 在庫が回復した状態での予約が正常に作成されました

- 実行時間: 2449.00ミリ秒
- 詳細:
```json
{
  "bookingId": "3f4815bd-810f-4417-a983-dcb5b945c8ad"
}
```

