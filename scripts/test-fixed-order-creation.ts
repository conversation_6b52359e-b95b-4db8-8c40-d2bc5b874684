/**
 * 修正された注文作成ロジックをテストするスクリプト
 * 
 * このスクリプトは、修正された注文作成ロジックをテストします。
 * 
 * 実行方法: npx tsx scripts/test-fixed-order-creation.ts
 */

import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';
import { createOrderFromBooking } from '../app/utils/booking/order-creator';
import { GraphQLClient } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの初期化
const createGraphQLClient = (accessToken: string) => {
  return new GraphQLClient(
    `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
    {
      headers: {
        'X-Shopify-Access-Token': accessToken,
        'Content-Type': 'application/json',
      },
    }
  );
};

// AdminApiContextをモック
const createMockAdminApi = (accessToken: string) => {
  return {
    graphql: async (query: string, options?: any) => {
      const client = createGraphQLClient(accessToken);
      const variables = options?.variables || {};
      
      console.log('GraphQLリクエスト:', { 
        query: query.substring(0, 100) + '...', 
        variables: JSON.stringify(variables).substring(0, 200) + '...' 
      });
      
      try {
        const response = await client.request(query, variables);
        return {
          json: async () => response
        };
      } catch (error) {
        console.error('GraphQLリクエストエラー:', error);
        throw error;
      }
    }
  };
};

// テスト用の予約を作成する関数
async function createTestBooking() {
  try {
    console.log('テスト用の予約を作成します...');
    
    // 利用可能な商品を取得
    const product = await prisma.product.findFirst({
      where: {
        shop: process.env.SHOPIFY_SHOP,
        status: 'AVAILABLE'
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    if (!product) {
      throw new Error('利用可能な商品が見つかりません。商品を登録してください。');
    }
    
    console.log(`商品情報: ID=${product.id}, タイトル=${product.title}, SKU=${product.sku}`);
    
    // 予約IDを生成
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    const bookingId = `BOOK-${timestamp}-${random}`;
    
    // 予約期間を設定（明日から3日間）
    const now = new Date();
    const startDate = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 明日
    const endDate = new Date(startDate.getTime() + 3 * 24 * 60 * 60 * 1000); // 3日後
    
    // 予約データを作成
    const bookingData = {
      bookingId,
      productId: product.id,
      startDate,
      endDate,
      customerName: 'テスト顧客',
      customerEmail: '<EMAIL>',
      customerId: process.env.TEST_CUSTOMER_ID || '8420600185000', // 環境変数から、または既定値
      totalAmount: 1500,
      depositAmount: 150,
      bookingType: 'CONFIRMED',
      status: 'CONFIRMED',
      shop: process.env.SHOPIFY_SHOP,
      variantId: product.shopifyId, // 商品IDをバリアントIDとして使用（テスト用）
      notes: 'テスト用の予約データ - 修正後の注文作成テスト用'
    };
    
    console.log('作成する予約データ:', bookingData);
    
    // 予約を作成
    const booking = await prisma.booking.create({
      data: bookingData
    });
    
    console.log('予約が作成されました:');
    console.log(`- 予約ID: ${booking.id}`);
    console.log(`- 予約番号: ${booking.bookingId}`);
    console.log(`- 商品ID: ${booking.productId}`);
    console.log(`- バリアントID: ${booking.variantId}`);
    
    return booking;
  } catch (error) {
    console.error('予約の作成に失敗しました:', error);
    throw error;
  }
}

// 注文作成をテストする関数
async function testOrderCreation(bookingId: string) {
  try {
    console.log(`\n===== 注文作成テスト =====`);
    console.log(`予約ID: ${bookingId}`);
    
    // セッションを取得
    const session = await prisma.session.findUnique({
      where: {
        id: `offline_${process.env.SHOPIFY_SHOP}`
      }
    });
    
    if (!session) {
      throw new Error('セッションが見つかりません。アプリケーションが正しく認証されていることを確認してください。');
    }
    
    console.log(`セッション情報: Shop=${session.shop}, アクセストークン=${session.accessToken.substring(0, 10)}...`);
    
    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: { product: true }
    });
    
    if (!booking) {
      throw new Error(`予約ID ${bookingId} が見つかりません`);
    }
    
    console.log('予約情報:', {
      id: booking.id,
      bookingId: booking.bookingId,
      productTitle: booking.product.title,
      variantId: booking.variantId
    });
    
    // モックAdminApiを作成
    const mockAdminApi = createMockAdminApi(session.accessToken);
    
    // 注文作成を実行
    console.log('注文作成を実行します...');
    const startTime = Date.now();
    
    const result = await createOrderFromBooking(
      prisma,
      mockAdminApi as any,
      bookingId,
      3 // リトライ回数
    );
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`注文作成結果 (${duration}ms):`);
    console.log(JSON.stringify(result, null, 2));
    
    // 更新された予約情報を取得
    const updatedBooking = await prisma.booking.findUnique({
      where: { id: bookingId },
      select: {
        id: true,
        bookingId: true,
        shopifyOrderId: true,
        shopifyOrderName: true
      }
    });
    
    console.log('更新後の予約情報:', updatedBooking);
    
    return {
      result,
      updatedBooking,
      duration
    };
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    throw error;
  }
}

// 同じ予約IDで複数回注文作成を試みる関数
async function testMultipleOrderCreation(bookingId: string, iterations: number = 3) {
  const results = [];
  
  for (let i = 1; i <= iterations; i++) {
    console.log(`\n===== 注文作成テスト #${i} =====`);
    
    try {
      const result = await testOrderCreation(bookingId);
      results.push({
        iteration: i,
        success: true,
        ...result
      });
    } catch (error) {
      console.error(`テスト #${i} が失敗しました:`, error);
      results.push({
        iteration: i,
        success: false,
        error: error.message
      });
    }
    
    // 各テストの間に少し待機
    if (i < iterations) {
      console.log(`次のテストまで3秒待機中...`);
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  return results;
}

// メイン実行関数
async function main() {
  try {
    console.log('修正された注文作成ロジックのテストを開始します...');
    
    // テスト用の予約を作成
    const booking = await createTestBooking();
    
    // 同じ予約IDで複数回注文作成を試みる
    const results = await testMultipleOrderCreation(booking.id);
    
    // 結果のサマリーを表示
    console.log('\n===== テスト結果サマリー =====');
    console.table(results.map(r => ({
      iteration: r.iteration,
      success: r.success,
      orderId: r.result?.result?.orderId || null,
      isExisting: r.result?.result?.isExisting || false,
      duration: r.duration || 0
    })));
    
    // 重複注文が発生したかどうかを分析
    const successfulResults = results.filter(r => r.success);
    const uniqueOrderIds = new Set(successfulResults.map(r => r.result?.result?.orderId).filter(Boolean));
    
    console.log('\n===== 分析結果 =====');
    console.log(`成功したテスト数: ${successfulResults.length}`);
    console.log(`生成された一意の注文ID数: ${uniqueOrderIds.size}`);
    
    if (uniqueOrderIds.size > 1) {
      console.log(`\n⚠️ 重複注文が検出されました: 同じ予約に対して ${uniqueOrderIds.size} 件の注文が作成されました`);
    } else if (uniqueOrderIds.size === 1) {
      console.log('\n✅ 重複注文は検出されませんでした: 正しく1件の注文のみが作成されました');
    } else {
      console.log('\n❓ 注文が作成されませんでした: エラーが発生した可能性があります');
    }
    
    return results;
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    throw error;
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// スクリプトを実行
console.log('テストスクリプトを開始します...');
main()
  .then((results) => {
    console.log('テストが完了しました');
    process.exit(0);
  })
  .catch((error) => {
    console.error('テストが失敗しました:', error);
    console.error('エラーの詳細:', error.stack);
    process.exit(1);
  });
