# エッジケースと境界値テストレポート

## テスト概要

このレポートは、年末年始や連休などの特殊期間の予約、予約期間の最大・最小値、在庫数ゼロの状態からの回復、無効なデータ入力時の処理のテスト結果をまとめたものです。

テスト実行日時: 2025/5/19 16:11:06
実行時間: 4.25秒

## テスト環境

- Node.js バージョン: v20.18.1
- OS: darwin arm64

## テスト結果

- 合計テスト数: 10
- 成功: 4
- 失敗: 6
- 成功率: 40%

## パフォーマンス分析

- 平均テスト実行時間: 350.10ミリ秒
- 最も遅いテスト: 年末年始の予約テスト (1053.00ミリ秒)
- 最も速いテスト: 予約をキャンセルして在庫を回復するテスト (0.00ミリ秒)

## 詳細結果

### テストケース1: 年末年始の予約テスト

- 結果: ❌ 失敗
- メッセージ: 年末年始の予約に失敗しました
- エラー: カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-12-29, 2025-12-30, 2025-12-31, 2026-01-01, 2026-01-02, 2026-01-03
- 実行時間: 1053.00ミリ秒
- 詳細:
```json
{
  "bookingId": "",
  "period": "年末年始",
  "startDate": "2025-12-29",
  "endDate": "2026-01-03"
}
```

### テストケース2: 予約期間の最小値テスト

- 結果: ❌ 失敗
- メッセージ: 最小予約期間の予約に失敗しました
- エラー: カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-05-26
- 実行時間: 236.00ミリ秒
- 詳細:
```json
{
  "bookingId": "",
  "durationDays": 1
}
```

### テストケース3: 予約期間の最大値テスト

- 結果: ❌ 失敗
- メッセージ: 最大予約期間の予約に失敗しました
- エラー: カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-02, 2025-06-03, 2025-06-04, 2025-06-05, 2025-06-06, 2025-06-07, 2025-06-08, 2025-06-09, 2025-06-10, 2025-06-11, 2025-06-12, 2025-06-13, 2025-06-14, 2025-06-15, 2025-06-16, 2025-06-17, 2025-06-18, 2025-06-19, 2025-06-20, 2025-06-21, 2025-06-22, 2025-06-23, 2025-06-24, 2025-06-25, 2025-06-26, 2025-06-27, 2025-06-28, 2025-06-29, 2025-06-30, 2025-07-01
- 実行時間: 318.00ミリ秒
- 詳細:
```json
{
  "bookingId": "",
  "durationDays": 30
}
```

### テストケース4: 過去の日付での予約テスト

- 結果: ✅ 成功
- メッセージ: 過去の日付での予約は正しくエラーになりました

- 実行時間: 234.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース5: 終了日が開始日より前の予約テスト

- 結果: ✅ 成功
- メッセージ: 終了日が開始日より前の予約は正しくエラーになりました
- エラー: 終了日が開始日より前の予約がエラーにならずに成功してしまいました
- 実行時間: 651.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース6: 無効なメールアドレスでの予約テスト

- 結果: ✅ 成功
- メッセージ: 無効なメールアドレスでの予約は正しくエラーになりました

- 実行時間: 233.00ミリ秒
- 詳細:
```json
{
  "expectedError": true,
  "invalidEmail": "invalid-email"
}
```

### テストケース7: 在庫をゼロにするテスト

- 結果: ❌ 失敗
- メッセージ: 在庫をゼロにすることに失敗しました
- エラー: カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-08, 2025-06-09, 2025-06-10, 2025-06-11
- 実行時間: 281.00ミリ秒
- 詳細:
```json
{
  "bookingId": ""
}
```

### テストケース8: 在庫がゼロの状態で予約を試みるテスト

- 結果: ✅ 成功
- メッセージ: 在庫がゼロの状態での予約は正しくエラーになりました

- 実行時間: 247.00ミリ秒
- 詳細:
```json
{
  "expectedError": true
}
```

### テストケース9: 予約をキャンセルして在庫を回復するテスト

- 結果: ❌ 失敗
- メッセージ: 予約をキャンセルして在庫を回復することに失敗しました
- エラー: テスト1で作成した予約IDがありません
- 実行時間: 不明
- 詳細:
```json
{
  "bookingId": ""
}
```

### テストケース10: 在庫が回復した状態で予約を試みるテスト

- 結果: ❌ 失敗
- メッセージ: 在庫が回復した状態での予約に失敗しました
- エラー: カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-06-08, 2025-06-09, 2025-06-10, 2025-06-11
- 実行時間: 248.00ミリ秒
- 詳細:
```json
{
  "bookingId": ""
}
```

