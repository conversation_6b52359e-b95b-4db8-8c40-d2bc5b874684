/**
 * 商品バリエーション設定スクリプト（仮予約オプション付き）
 *
 * このスクリプトでは、Shopify APIを使用して商品のバリエーションを設定します。
 * 各商品に9種類のバリエーション（1日、2日、3日、4日、5日、6日、7日、8日以上、仮予約）を設定します。
 * 仮予約は基本料金の10%の価格で設定されます。
 */

import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数を読み込む
dotenv.config();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// GraphQLクエリとミューテーション
const GET_PRODUCTS = gql`
  query getProducts($query: String) {
    products(first: 50, query: $query) {
      edges {
        node {
          id
          title
          handle
          variants(first: 10) {
            edges {
              node {
                id
                title
                price
              }
            }
          }
        }
      }
    }
  }
`;

const UPDATE_PRODUCT = gql`
  mutation productUpdate($input: ProductInput!) {
    productUpdate(input: $input) {
      product {
        id
        title
        handle
        variants(first: 10) {
          edges {
            node {
              id
              title
              price
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * 商品のバリエーションを設定
 */
async function setupProductVariants() {
  console.log("=== 商品バリエーションの設定（仮予約オプション付き） ===\n");

  try {
    // 商品を検索（検索クエリを指定しない場合は全商品が対象）
    const searchQuery = process.argv[2] || "";
    console.log(`検索クエリ: ${searchQuery || "すべての商品"}`);
    
    const result = await shopifyClient.request(GET_PRODUCTS, {
      query: searchQuery
    });

    const products = result.products.edges;
    console.log(`${products.length}件の商品が見つかりました`);

    // 各商品のバリエーションを設定
    for (const product of products) {
      const productId = product.node.id;
      const title = product.node.title;
      const basePrice = parseFloat(product.node.variants.edges[0].node.price);

      console.log(`\n商品のバリエーションを設定: ${title} (${productId})`);
      console.log(`基本価格: ${basePrice}`);

      // バリエーションの価格を計算
      const prices = calculateVariantPrices(basePrice);
      const provisionalPrice = Math.round(basePrice * 0.1); // 仮予約は基本料金の10%

      // 商品を更新
      const updateResult = await shopifyClient.request(UPDATE_PRODUCT, {
        input: {
          id: productId,
          options: ["レンタル日数"],
          variants: [
            { price: prices[0].toString(), options: ["1日"] },
            { price: prices[1].toString(), options: ["2日"] },
            { price: prices[2].toString(), options: ["3日"] },
            { price: prices[3].toString(), options: ["4日"] },
            { price: prices[4].toString(), options: ["5日"] },
            { price: prices[5].toString(), options: ["6日"] },
            { price: prices[6].toString(), options: ["7日"] },
            { price: prices[7].toString(), options: ["8日以上"] },
            { price: provisionalPrice.toString(), options: ["仮予約"] }
          ]
        }
      });

      if (updateResult.productUpdate.userErrors.length > 0) {
        console.error("商品更新エラー:", updateResult.productUpdate.userErrors);
        continue;
      }

      console.log(`商品のバリエーションが設定されました: ${title}`);
      console.log("バリエーション価格:");
      prices.forEach((price, index) => {
        const days = index + 1;
        const label = days <= 7 ? `${days}日` : "8日以上";
        console.log(`- ${label}: ${price}`);
      });
      console.log(`- 仮予約: ${provisionalPrice} (基本料金の10%)`);
    }

    console.log("\n商品バリエーションの設定が完了しました");
  } catch (error) {
    console.error("商品バリエーションの設定中にエラーが発生しました:", error);
  }
}

/**
 * バリエーションの価格を計算
 * @param {number} basePrice 基本料金
 * @returns {number[]} 各バリエーションの価格
 */
function calculateVariantPrices(basePrice) {
  // 料金計算ロジック（ドキュメントに基づく）
  // 1日目: 基本料金の100%
  // 2〜7日目: 基本料金の20%
  // 8日目以降: 基本料金の10%
  
  // 1日の場合
  const price1day = basePrice;
  
  // 2日の場合: 基本料金 + (基本料金 * 20%)
  const price2day = basePrice + (basePrice * 0.2);
  
  // 3日の場合: 基本料金 + (基本料金 * 20% * 2)
  const price3day = basePrice + (basePrice * 0.2 * 2);
  
  // 4日の場合: 基本料金 + (基本料金 * 20% * 3)
  const price4day = basePrice + (basePrice * 0.2 * 3);
  
  // 5日の場合: 基本料金 + (基本料金 * 20% * 4)
  const price5day = basePrice + (basePrice * 0.2 * 4);
  
  // 6日の場合: 基本料金 + (基本料金 * 20% * 5)
  const price6day = basePrice + (basePrice * 0.2 * 5);
  
  // 7日の場合: 基本料金 + (基本料金 * 20% * 6)
  const price7day = basePrice + (basePrice * 0.2 * 6);
  
  // 8日以上の場合: 基本料金 + (基本料金 * 20% * 6) + (基本料金 * 10%)
  // 注: 実際には8日目以降の日数に応じて追加料金が発生しますが、
  // バリエーションとしては8日目の料金を設定しておきます
  const price8dayPlus = basePrice + (basePrice * 0.2 * 6) + (basePrice * 0.1);
  
  // 整数に丸める
  return [
    Math.round(price1day),
    Math.round(price2day),
    Math.round(price3day),
    Math.round(price4day),
    Math.round(price5day),
    Math.round(price6day),
    Math.round(price7day),
    Math.round(price8dayPlus)
  ];
}

// メイン処理
async function main() {
  try {
    await setupProductVariants();
  } catch (error) {
    console.error("エラーが発生しました:", error);
  }
}

main();
