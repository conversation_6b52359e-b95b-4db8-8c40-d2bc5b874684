import React, { useState } from 'react';
import { Page, Layout, Card, DatePicker, But<PERSON>, Text, InlineStack, BlockStack, TextField, Banner, Spinner } from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { differenceInDays, format, addDays } from 'date-fns';
import { ja } from 'date-fns/locale';

// 商品データのインターフェース
interface Product {
  id: string;
  title: string;
  handle: string;
  status: string;
  location: string;
  price: string;
  image?: string;
  sku?: string;
}

export default function BookingPage() {
  // 状態管理
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(addDays(new Date(), 1));
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [{ month, year }, setDate] = useState({ month: new Date().getMonth(), year: new Date().getFullYear() });

  // レンタル日数を計算
  const rentalDays = differenceInDays(endDate, startDate);

  // 料金計算（フロントエンドでの簡易計算）
  const calculatePrice = () => {
    if (!selectedProduct) return 0;

    const basePrice = parseFloat(selectedProduct.price);
    let totalPrice = basePrice; // 1日目は基本料金

    // 2日目以降は割引を適用
    if (rentalDays > 1) {
      // 2～6日目: 基本料金から80%割引（1,000円/日）
      const day2to6 = Math.min(rentalDays - 1, 5);
      if (day2to6 > 0) {
        totalPrice += basePrice * 0.2 * day2to6; // 80%割引後の料金
      }

      // 7日目以降: 基本料金から90%割引（500円/日）
      const day7plus = Math.max(rentalDays - 6, 0);
      if (day7plus > 0) {
        totalPrice += basePrice * 0.1 * day7plus; // 90%割引後の料金
      }
    }

    return Math.round(totalPrice);
  };

  // デポジット計算（10%固定）
  const calculateDeposit = () => {
    if (!selectedProduct) return 0;
    return Math.round(parseFloat(selectedProduct.price) * 0.1);
  };

  // 商品を検索する関数
  const searchProducts = async () => {
    if (!searchTerm.trim()) return;

    setSearchLoading(true);
    setError(null);

    try {
      // 実際の実装では、ここでAPIを呼び出して商品を検索します
      // テスト用にダミーデータを返します
      setTimeout(() => {
        // テスト商品を検索結果として返す
        if (searchTerm.toLowerCase().includes('テスト') || searchTerm.toLowerCase().includes('test')) {
          setProducts([
            {
              id: 'gid://shopify/Product/8872753823912',
              title: 'テスト商品',
              handle: 'テスト商品',
              status: 'available',
              location: 'NY',
              price: '5000',
              sku: 'TEST-001'
            }
          ]);
        } else {
          setProducts([]);
        }
        setSearchLoading(false);
      }, 500);
    } catch (err) {
      setError(err instanceof Error ? err.message : '商品の検索中にエラーが発生しました');
      setProducts([]);
      setSearchLoading(false);
    }
  };

  // カートに追加する関数
  const addToCart = async () => {
    if (!selectedProduct) {
      setError('商品を選択してください');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('予約データ送信開始');

      // 送信データを作成
      const requestData = {
        productId: selectedProduct.id,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        customerEmail: '', // ユーザーのメールアドレスがあれば設定
      };

      console.log('送信データ:', requestData);

      // APIを呼び出して予約データを保存
      const response = await fetch('/api.bookings.create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log('レスポンス受信:', response.status);

      const result = await response.json();
      console.log('レスポンスデータ:', result);

      if (result.error) {
        throw new Error(result.error);
      }

      setSuccess('予約が完了しました。予約ID: ' + result.booking.bookingId);
      setLoading(false);
    } catch (err) {
      console.error('予約エラー:', err);
      setError(err instanceof Error ? err.message : 'カートへの追加中にエラーが発生しました');
      setLoading(false);
    }
  };

  // 日付選択のハンドラー
  const handleStartDateChange = (dates) => {
    const newStartDate = new Date(dates.start);
    setStartDate(newStartDate);

    // 開始日が終了日より後の場合、終了日を開始日の翌日に設定
    if (newStartDate >= endDate) {
      setEndDate(addDays(newStartDate, 1));
    }
  };

  const handleEndDateChange = (dates) => {
    setEndDate(new Date(dates.start));
  };

  const handleMonthChange = (month, year) => {
    setDate({ month, year });
  };

  return (
    <Page title="レンタル商品予約">
      <TitleBar title="レンタル商品予約" />
      <Layout>
        <Layout.Section>
          <Card>
            <div className="p-4">
              <BlockStack gap="400">
                <Text variant="headingLg" as="h1">レンタル商品予約</Text>

                {error && (
                  <Banner tone="critical">
                    <Text as="p">{error}</Text>
                  </Banner>
                )}

                {success && (
                  <Banner tone="success">
                    <Text as="p">{success}</Text>
                  </Banner>
                )}

                <BlockStack gap="800">
                  {/* 商品検索セクション */}
                  <Card>
                    <div className="p-4">
                      <BlockStack gap="400">
                        <Text variant="headingMd" as="h2">1. 商品を選択</Text>

                        <InlineStack gap="200" align="start">
                          <div style={{ flexGrow: 1 }}>
                            <TextField
                              label="商品名で検索"
                              value={searchTerm}
                              onChange={setSearchTerm}
                              placeholder="テスト商品"
                              autoComplete="off"
                              helpText="テスト用に「テスト商品」と入力してください"
                            />
                          </div>
                          <div style={{ marginTop: '2rem' }}>
                            <Button onClick={searchProducts} disabled={searchLoading}>
                              {searchLoading ? <Spinner size="small" /> : '検索'}
                            </Button>
                          </div>
                        </InlineStack>

                        {products.length > 0 && (
                          <BlockStack gap="400">
                            <Text variant="headingMd" as="h3">検索結果</Text>
                            {products.map((product) => (
                              <Card key={product.id} padding="400">
                                <BlockStack gap="200">
                                  <Text variant="headingMd" as="h4">{product.title}</Text>
                                  <Text as="p">SKU: {product.sku || 'なし'}</Text>
                                  <Text as="p">場所: {product.location}</Text>
                                  <Text as="p">状態: {product.status === 'available' ? '利用可能' : '利用不可'}</Text>
                                  <Text as="p">価格: ¥{product.price}</Text>
                                  <Button
                                    onClick={() => setSelectedProduct(product)}
                                    disabled={product.status !== 'available'}
                                  >
                                    選択
                                  </Button>
                                </BlockStack>
                              </Card>
                            ))}
                          </BlockStack>
                        )}

                        {products.length === 0 && searchTerm && !searchLoading && (
                          <Text as="p" tone="subdued">商品が見つかりませんでした</Text>
                        )}
                      </BlockStack>
                    </div>
                  </Card>

                  {/* 日付選択セクション */}
                  <Card>
                    <div className="p-4">
                      <BlockStack gap="400">
                        <Text variant="headingMd" as="h2">2. レンタル期間を選択</Text>

                        <InlineStack gap="800" blockAlign="start">
                          <BlockStack gap="200">
                            <Text variant="headingMd" as="h3">貸出開始日</Text>
                            <DatePicker
                              month={month}
                              year={year}
                              onChange={handleStartDateChange}
                              onMonthChange={handleMonthChange}
                              selected={{
                                start: startDate,
                                end: startDate
                              }}
                              disableDatesBefore={new Date()}
                            />
                            <Text as="p">
                              選択日: {format(startDate, 'yyyy年MM月dd日(EEE)', { locale: ja })}
                            </Text>
                          </BlockStack>

                          <BlockStack gap="200">
                            <Text variant="headingMd" as="h3">返却予定日</Text>
                            <DatePicker
                              month={month}
                              year={year}
                              onChange={handleEndDateChange}
                              onMonthChange={handleMonthChange}
                              selected={{
                                start: endDate,
                                end: endDate
                              }}
                              disableDatesBefore={addDays(startDate, 1)}
                            />
                            <Text as="p">
                              選択日: {format(endDate, 'yyyy年MM月dd日(EEE)', { locale: ja })}
                            </Text>
                          </BlockStack>
                        </InlineStack>

                        <Text as="p">レンタル日数: {rentalDays}日間</Text>
                      </BlockStack>
                    </div>
                  </Card>

                  {/* 予約内容確認セクション */}
                  {selectedProduct && (
                    <Card>
                      <div className="p-4">
                        <BlockStack gap="400">
                          <Text variant="headingMd" as="h2">3. 予約内容の確認</Text>

                          <BlockStack gap="200">
                            <Text variant="headingMd" as="h3">{selectedProduct.title}</Text>
                            <Text as="p">SKU: {selectedProduct.sku || 'なし'}</Text>
                            <Text as="p">場所: {selectedProduct.location}</Text>
                            <Text as="p">状態: {selectedProduct.status === 'available' ? '利用可能' : '利用不可'}</Text>
                            <Text as="p">
                              貸出期間: {format(startDate, 'yyyy/MM/dd', { locale: ja })} - {format(endDate, 'yyyy/MM/dd', { locale: ja })}
                            </Text>
                            <Text as="p">レンタル日数: {rentalDays}日間</Text>
                          </BlockStack>

                          <BlockStack gap="200">
                            <Text as="p">基本料金: ¥{selectedProduct.price}</Text>
                            <Text as="p">デポジット (10%): ¥{calculateDeposit().toLocaleString()}</Text>
                            <Text variant="headingMd" as="h3">合計金額: ¥{calculatePrice().toLocaleString()}</Text>
                          </BlockStack>

                          <Button
                            primary
                            onClick={addToCart}
                            disabled={loading}
                          >
                            {loading ? <Spinner size="small" /> : 'カートに追加'}
                          </Button>
                        </BlockStack>
                      </div>
                    </Card>
                  )}
                </BlockStack>
              </BlockStack>
            </div>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
