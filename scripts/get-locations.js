/**
 * Shopifyの在庫ロケーション情報を取得するスクリプト
 */

const { GraphQLClient, gql } = require('graphql-request');
const dotenv = require('dotenv');

dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// ロケーション情報を取得するクエリ
const QUERY = gql`
  {
    locations {
      edges {
        node {
          id
          name
        }
      }
    }
  }
`;

// メイン処理
async function main() {
  try {
    const data = await client.request(QUERY);
    console.log(JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('エラーが発生しました:', error);
  }
}

// スクリプト実行
main();
