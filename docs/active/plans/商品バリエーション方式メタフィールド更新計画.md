# 商品バリエーション方式メタフィールド更新計画

## 概要

商品バリエーション方式の実装に伴い、在庫ユニットレベルでの状態管理を導入するためのメタフィールド更新計画を定義します。このドキュメントでは、新規追加するメタフィールド、更新が必要なメタフィールド、そして維持すべきメタフィールドを整理し、移行手順について説明します。

## 現在のメタフィールド構造

現在、以下のメタフィールドが使用されています：

| 定義名 | タイプ | 使用元 | 名前空間 |
|-------|-------|--------|----------|
| バリエーションタイプ | 1行テキスト | 3 件の商品 | （なし） |
| バリエーションマッピング | JSON | 3 件の商品 | （なし） |
| 色 | 1行テキスト | 0 件の商品 | （なし） |
| 素材 | 1行テキスト | 0 件の商品 | （なし） |
| 高さ | 整数 | 0 件の商品 | （なし） |
| 幅 | 整数 | 0 件の商品 | （なし） |
| 奥行き | 整数 | 0 件の商品 | （なし） |
| 在庫場所 | 1行テキスト | 20 件の商品 | （なし） |
| 予約備考 | 複数行テキスト | 0 件の商品 | （なし） |
| 購入価格 | 小数 | 0 件の商品 | （なし） |
| 購入場所 | 1行テキスト | 0 件の商品 | （なし） |
| 購入日 | 日付 | 0 件の商品 | （なし） |
| メーカー | 1行テキスト | 0 件の商品 | （なし） |
| デザイナー | 1行テキスト | 0 件の商品 | （なし） |
| メンテナンス備考 | 複数行テキスト | 0 件の商品 | （なし） |
| 廃棄済み | True / False | 0 件の商品 | （なし） |
| 廃棄日 | 日付 | 0 件の商品 | （なし） |
| 廃棄理由 | 複数行テキスト | 0 件の商品 | （なし） |
| レンタル状態 | 1行テキスト | 20 件の商品 | （なし） |
| レンタル予約情報 | JSON | 0 件の商品 | （なし） |
| レンタル在庫アイテム | JSON | 0 件の商品 | （なし） |
| レンタル料金設定 | JSON | 20 件の商品 | （なし） |
| レンタル商品基本情報 | JSON | 20 件の商品 | （なし） |

## 更新計画

### 1. 新規追加するメタフィールド

在庫ユニット管理と商品シリーズ管理のために、以下のメタフィールドを新規に追加します：

| 定義名 | 名前空間 | キー | タイプ | 説明 |
|-------|---------|-----|-------|------|
| 在庫ユニット | rental | inventory_units | JSON | 商品の物理的な在庫ユニットとその状態を管理 |
| シリーズコード | rental | series_code | 1行テキスト | 商品シリーズを識別するためのコード |
| マスター商品フラグ | rental | is_master | True / False | 商品グループのマスター商品かどうか |

#### 在庫ユニットのJSONスキーマ

```json
{
  "units": [
    {
      "unit_id": "TABLE-001-01",
      "status": "available",
      "location": "Tokyo",
      "last_maintenance_date": "2024-05-01",
      "notes": "新品同様の状態"
    },
    {
      "unit_id": "TABLE-001-02",
      "status": "maintenance",
      "location": "Tokyo",
      "last_maintenance_date": "2024-04-15",
      "notes": "脚部の修理中"
    }
  ]
}
```

### 2. 名前空間を追加するメタフィールド

現在名前空間がないメタフィールドに、標準化のために名前空間を追加します。以下のメタフィールドは、そのまま保持しながら名前空間を追加します：

| 現在の定義名 | 新しい名前空間 | 新しいキー | タイプ | 変更内容 |
|------------|--------------|---------|-------|---------|
| バリエーションタイプ | rental | variation_type | 1行テキスト | 名前空間を追加して標準化 |
| バリエーションマッピング | rental | variant_mapping | JSON | 名前空間を追加して標準化 |
| レンタル状態 | rental | status | 1行テキスト | 名前空間を追加して標準化 |
| 色 | rental | color | 1行テキスト | 名前空間を追加して標準化 |
| 素材 | rental | material | 1行テキスト | 名前空間を追加して標準化 |
| 在庫場所 | rental | location | 1行テキスト | 名前空間を追加して標準化 |
| 予約備考 | rental | booking_notes | 複数行テキスト | 名前空間を追加して標準化 |
| 購入価格 | rental | purchase_price | 小数 | 名前空間を追加して標準化 |
| 購入場所 | rental | purchase_place | 1行テキスト | 名前空間を追加して標準化 |
| 購入日 | rental | purchase_date | 日付 | 名前空間を追加して標準化 |
| メーカー | rental | manufacturer | 1行テキスト | 名前空間を追加して標準化 |
| デザイナー | rental | designer | 1行テキスト | 名前空間を追加して標準化 |
| メンテナンス備考 | rental | maintenance_notes | 複数行テキスト | 名前空間を追加して標準化 |
| 廃棄済み | rental | is_disposed | True / False | 名前空間を追加して標準化 |
| 廃棄日 | rental | disposal_date | 日付 | 名前空間を追加して標準化 |
| 廃棄理由 | rental | disposal_reason | 複数行テキスト | 名前空間を追加して標準化 |

寸法関連メタフィールドは、製品仕様に関連するため別の名前空間を使用します：

| 現在の定義名 | 新しい名前空間 | 新しいキー | タイプ | 変更内容 |
|------------|--------------|---------|-------|---------|
| 高さ | product | height | 整数 | 名前空間を追加して標準化 |
| 幅 | product | width | 整数 | 名前空間を追加して標準化 |
| 奥行き | product | depth | 整数 | 名前空間を追加して標準化 |

### 3. 評価が必要なメタフィールド

以下のメタフィールドは、新しい在庫ユニット管理への移行に伴い、役割や内容を評価する必要があります：

| 定義名 | タイプ | 評価内容 |
|-------|-------|----------|
| レンタル予約情報 | JSON | 在庫ユニットごとの予約管理へ移行するか評価 |
| レンタル在庫アイテム | JSON | 新しい `inventory_units` との重複を確認し統合または廃止を検討 |

### 4. 維持するメタフィールド

以下のメタフィールドは、すでに名前空間が適切に設定されているか、または現在の役割が継続して必要であるため維持します：

| 定義名 | 名前空間 | キー | タイプ |
|-------|---------|-----|-------|
| レンタル料金設定 | （現在の名前空間） | （現在のキー） | JSON |
| レンタル商品基本情報 | （現在の名前空間） | （現在のキー） | JSON |

## 移行戦略

### 1. 準備フェーズ

1. **バックアップの作成**:
   - すべての商品データとメタフィールドのバックアップを作成
   - ```bash
     node scripts/backup-product-data.js
     ```

2. **新しいメタフィールド定義の作成**:
   - 新規メタフィールド定義をShopifyに登録
   - ```bash
     node scripts/create-metafield-definitions.js
     ```

### 2. データ移行フェーズ

1. **在庫ユニットデータの生成**:
   - 既存の「レンタル状態」と「在庫場所」の情報から在庫ユニットデータを生成
   - ```bash
     node scripts/generate-inventory-units.js
     ```

2. **名前空間の追加**:
   - 既存のメタフィールドに名前空間を追加
   - ```bash
     node scripts/update-metafield-namespaces.js
     ```

3. **シリーズコードの設定**:
   - 関連する商品グループにシリーズコードを設定
   - ```bash
     node scripts/set-series-codes.js
     ```

### 3. 検証フェーズ

1. **データ整合性の検証**:
   - 移行後のデータが正しく設定されているか確認
   - ```bash
     node scripts/verify-metafield-migration.js
     ```

2. **アプリケーションの動作確認**:
   - 新しいメタフィールド構造でアプリケーションが正常に動作するか確認
   - テスト環境での動作確認を実施

### 4. 最終フェーズ

1. **旧データのクリーンアップ**:
   - 不要になったメタフィールドの削除（オプション）
   - ```bash
     node scripts/cleanup-old-metafields.js
     ```

2. **ドキュメントの更新**:
   - 新しいメタフィールド構造に関するドキュメントの更新
   - 管理者向けのガイドの作成

## 実装コード例

### 1. メタフィールド定義の作成

```javascript
// scripts/create-metafield-definitions.js

const { Shopify } = require('@shopify/shopify-api');

async function createMetafieldDefinitions() {
  const client = new Shopify.Clients.Rest(shop, accessToken);
  
  // 在庫ユニット用のメタフィールド定義
  await client.post({
    path: 'metafield_definitions',
    data: {
      metafield_definition: {
        name: "在庫ユニット",
        namespace: "rental",
        key: "inventory_units",
        type: "json",
        description: "商品の物理的な在庫ユニットとその状態",
        ownerType: "PRODUCT"
      },
    },
  });
  
  // シリーズコード用のメタフィールド定義
  await client.post({
    path: 'metafield_definitions',
    data: {
      metafield_definition: {
        name: "シリーズコード",
        namespace: "rental",
        key: "series_code",
        type: "single_line_text_field",
        description: "商品シリーズを識別するためのコード",
        ownerType: "PRODUCT"
      },
    },
  });
  
  // マスター商品フラグ用のメタフィールド定義
  await client.post({
    path: 'metafield_definitions',
    data: {
      metafield_definition: {
        name: "マスター商品",
        namespace: "rental",
        key: "is_master",
        type: "boolean",
        description: "商品グループのマスター商品かどうか",
        ownerType: "PRODUCT"
      },
    },
  });
  
  console.log('メタフィールド定義を作成しました');
}

createMetafieldDefinitions().catch(console.error);
```

### 2. 在庫ユニットデータの生成

```javascript
// scripts/generate-inventory-units.js

const { Shopify } = require('@shopify/shopify-api');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function generateInventoryUnits() {
  const client = new Shopify.Clients.Rest(shop, accessToken);
  
  // すべての商品を取得
  const response = await client.get({
    path: 'products',
    query: { limit: 250 }
  });
  
  const products = response.body.products;
  
  for (const product of products) {
    // 商品のメタフィールドを取得
    const metafieldsResponse = await client.get({
      path: `products/${product.id}/metafields`
    });
    
    const metafields = metafieldsResponse.body.metafields;
    
    // レンタル状態を取得
    const statusField = metafields.find(m => m.key === 'レンタル状態');
    const status = statusField ? statusField.value : 'available';
    
    // 在庫場所を取得
    const locationField = metafields.find(m => m.key === '在庫場所');
    const location = locationField ? locationField.value : 'default';
    
    // 商品コードを生成
    const handle = product.handle;
    const productCode = handle.toUpperCase().replace(/-/g, '_');
    
    // 在庫ユニットデータを作成
    const inventoryUnits = [{
      unit_id: `${productCode}-01`,
      status,
      location,
      last_maintenance_date: new Date().toISOString().split('T')[0],
      notes: ""
    }];
    
    // 在庫ユニットメタフィールドを設定
    await client.post({
      path: `products/${product.id}/metafields`,
      data: {
        metafield: {
          namespace: 'rental',
          key: 'inventory_units',
          value: JSON.stringify({ units: inventoryUnits }),
          type: 'json'
        }
      }
    });
    
    console.log(`商品 ${product.title} (${product.id}) の在庫ユニットデータを生成しました`);
  }
  
  console.log(`${products.length}件の商品の在庫ユニットデータを生成しました`);
}

generateInventoryUnits().catch(console.error);
```

### 3. 名前空間の更新

```javascript
// scripts/update-metafield-namespaces.js

const { Shopify } = require('@shopify/shopify-api');

async function updateMetafieldNamespaces() {
  const client = new Shopify.Clients.Rest(shop, accessToken);
  
  // すべての商品を取得
  const response = await client.get({
    path: 'products',
    query: { limit: 250 }
  });
  
  const products = response.body.products;
  
  // 名前空間を更新するメタフィールドの定義
  const metafieldMappings = [
    { oldKey: 'レンタル状態', newNamespace: 'rental', newKey: 'status' },
    { oldKey: '在庫場所', newNamespace: 'rental', newKey: 'location' },
    { oldKey: 'バリエーションタイプ', newNamespace: 'rental', newKey: 'variation_type' },
    { oldKey: 'バリエーションマッピング', newNamespace: 'rental', newKey: 'variant_mapping' },
    // 他のメタフィールドも同様に定義
  ];
  
  for (const product of products) {
    // 商品のメタフィールドを取得
    const metafieldsResponse = await client.get({
      path: `products/${product.id}/metafields`
    });
    
    const metafields = metafieldsResponse.body.metafields;
    
    // 各メタフィールドの名前空間を更新
    for (const mapping of metafieldMappings) {
      const metafield = metafields.find(m => m.key === mapping.oldKey);
      
      if (metafield) {
        // 新しいメタフィールドを作成
        await client.post({
          path: `products/${product.id}/metafields`,
          data: {
            metafield: {
              namespace: mapping.newNamespace,
              key: mapping.newKey,
              value: metafield.value,
              type: metafield.type
            }
          }
        });
        
        // 古いメタフィールドは一旦残しておく（検証後に削除）
        
        console.log(`商品 ${product.title} (${product.id}) の ${mapping.oldKey} メタフィールドを更新しました`);
      }
    }
  }
  
  console.log(`${products.length}件の商品のメタフィールド名前空間を更新しました`);
}

updateMetafieldNamespaces().catch(console.error);
```

## 検証手順

1. **テスト商品での検証**:
   - 少数の商品に対して更新スクリプトを実行
   - メタフィールドが正しく設定されているか確認
   - アプリケーションが新しいメタフィールド構造で正常に動作するか確認

2. **既存のデータの整合性確認**:
   - 以下のチェックリストに従って検証
     - [ ] 在庫ユニットデータが正しく生成されている
     - [ ] 名前空間が正しく設定されている
     - [ ] 元のデータとの一貫性がある
     - [ ] アプリケーションが新しいデータ構造を正しく読み込める

3. **ロールバック計画**:
   - 問題が発生した場合のロールバック手順を準備
   - バックアップからの復元手順を確認

## リスクと対策

1. **データロスのリスク**:
   - 対策: すべての操作前にバックアップを作成し、段階的に実装する

2. **APIレート制限**:
   - 対策: バッチ処理と再試行ロジックを実装し、APIコール数を最適化

3. **アプリケーション互換性**:
   - 対策: テスト環境で事前に互換性をテストし、必要に応じてアプリケーションコードを更新

4. **運用への影響**:
   - 対策: メンテナンス時間帯に実施し、管理者向けのガイドを事前に準備

## タイムライン

1. **準備フェーズ**: 1週間
   - メタフィールド定義の作成
   - バックアップスクリプトの実装
   - テスト環境のセットアップ

2. **テスト実装**: 1週間
   - テスト商品でのメタフィールド更新
   - アプリケーション互換性のテスト
   - 問題の修正

3. **本番実装**: 1〜2日
   - メンテナンス時間帯に実施
   - 段階的なデータ移行
   - 検証と確認

4. **フォローアップ**: 1週間
   - 運用監視
   - 必要に応じて調整
   - 古いメタフィールドのクリーンアップ（オプション）

## 結論

商品バリエーション方式と在庫ユニット管理を導入するための本メタフィールド更新計画は、既存のデータ構造を尊重しつつ、新しい機能をサポートするための変更を提案しています。段階的な実装と検証により、リスクを最小限に抑えながら移行を進めることが可能です。

この更新により、以下のメリットが期待できます：

1. 物理的な在庫ユニットごとの状態管理が可能になる
2. 商品バリエーションと在庫管理の分離により、より柔軟な管理が可能になる
3. 標準化された名前空間により、メタフィールド管理が容易になる

移行後も、運用状況を注意深く監視し、必要に応じて調整を行うことが重要です。