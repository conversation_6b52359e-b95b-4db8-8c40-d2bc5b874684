{"timestamp": "2025-05-19T07:16:01.040Z", "duration": 7.183, "totalTests": 3, "successCount": 1, "failureCount": 2, "tests": [{"testName": "予約リクエスト #0", "success": false, "timestamp": "2025-05-19T07:16:08.224Z", "message": "予約リクエストが失敗しました", "error": "カート追加エラー: 選択された日程では予約できません。以下の日付が既に予約されています: 2025-05-26", "duration": 1029, "details": {"requestId": 0, "bookingId": ""}}, {"testName": "予約リクエスト #1", "success": true, "timestamp": "2025-05-19T07:16:08.224Z", "message": "予約リクエストが成功しました", "duration": 5994, "details": {"requestId": 1, "bookingId": "50ef94dd-2336-4516-99d4-79bb395851d6"}}, {"testName": "予約リクエスト #2", "success": false, "timestamp": "2025-05-19T07:16:08.224Z", "message": "予約リクエストが失敗しました", "error": "カート追加エラー: 予約の作成に失敗しました: \nInvalid `prisma.inventoryCalendar.upsert()` invocation in\n/Volumes/2TB-Speed/Users/<USER>/git/peaces-app-2-claude/ease-next-temp/app/services/inventory-calendar.service.ts:363:40\n\n  360   throw new Error(`商品 ID ${productId} が見つかりません`);\n  361 }\n  362 \n→ 363 await prisma.inventoryCalendar.upsert(\nUnique constraint failed on the fields: (`shop`,`productId`,`date`)", "duration": 3661, "details": {"requestId": 2, "bookingId": ""}}], "environment": {"concurrentRequests": 3, "nodeVersion": "v20.18.1", "os": "darwin arm64", "cpuCores": 10}, "performance": {"averageTestDuration": 3561.3333333333335, "slowestTest": {"name": "リクエスト #1", "duration": 5994}, "fastestTest": {"name": "リクエスト #0", "duration": 1029}, "throughput": 0.41765279131282196, "successRate": 33.33333333333333}}