/**
 * 改良された検索機能テストスクリプト
 * 
 * pg_trgm拡張機能を使用して日本語名前のスペース問題を解決
 */

const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

// 改良された検索関数
function buildImprovedSearchCondition(query, shop) {
  const trimmedQuery = query.trim();
  
  // 基本的な検索条件
  const basicConditions = [
    { customerEmail: { contains: trimmedQuery, mode: 'insensitive' } },
    { customerId: { contains: trimmedQuery, mode: 'insensitive' } },
    { bookingId: { contains: trimmedQuery, mode: 'insensitive' } }
  ];

  // 商品検索条件
  const productSearchCondition = {
    product: {
      OR: [
        { title: { contains: trimmedQuery, mode: 'insensitive' } },
        { sku: { contains: trimmedQuery, mode: 'insensitive' } }
      ]
    }
  };

  // 日本語名前検索の改良版
  const nameSearchConditions = [];
  
  // 1. 元のクエリでの検索
  nameSearchConditions.push({
    customerName: { contains: trimmedQuery, mode: 'insensitive' }
  });

  // 2. スペースを削除したクエリでの検索
  const queryWithoutSpaces = trimmedQuery.replace(/\s+/g, '');
  if (queryWithoutSpaces !== trimmedQuery) {
    nameSearchConditions.push({
      customerName: { contains: queryWithoutSpaces, mode: 'insensitive' }
    });
  }

  // 3. スペースを追加したクエリでの検索（日本語の場合）
  if (/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(trimmedQuery)) {
    // 日本語文字が含まれている場合
    if (!trimmedQuery.includes(' ') && trimmedQuery.length >= 2) {
      // スペースがない場合、姓名の境界を推測してスペースを追加
      for (let i = 1; i < trimmedQuery.length; i++) {
        const withSpace = trimmedQuery.slice(0, i) + ' ' + trimmedQuery.slice(i);
        nameSearchConditions.push({
          customerName: { contains: withSpace, mode: 'insensitive' }
        });
      }
    }
  }

  // 4. 部分検索（姓または名のみ）
  if (trimmedQuery.length >= 2) {
    // 2文字以上の場合、部分検索も追加
    nameSearchConditions.push({
      customerName: { startsWith: trimmedQuery, mode: 'insensitive' }
    });
    nameSearchConditions.push({
      customerName: { endsWith: trimmedQuery, mode: 'insensitive' }
    });
  }

  // すべての条件を結合
  const allConditions = [
    ...basicConditions,
    productSearchCondition,
    { OR: nameSearchConditions }
  ];

  return {
    shop,
    OR: allConditions
  };
}

// 改良された検索テスト関数
async function testImprovedSearch(query, description) {
  console.log(`\n=== ${description}: "${query}" ===`);
  
  const shop = 'peaces-test-block.myshopify.com';
  
  try {
    const whereCondition = buildImprovedSearchCondition(query, shop);
    
    console.log('検索条件:', JSON.stringify(whereCondition, null, 2));

    // 検索実行
    const bookings = await prisma.booking.findMany({
      where: whereCondition,
      include: {
        product: {
          select: {
            title: true,
            sku: true,
            status: true
          }
        }
      },
      take: 10
    });

    console.log(`検索結果: ${bookings.length}件の予約が見つかりました`);

    // 結果を表示
    if (bookings.length > 0) {
      bookings.forEach((booking, index) => {
        console.log(`\n[${index + 1}] 予約情報:`);
        console.log(`  予約ID: ${booking.bookingId || 'なし'}`);
        console.log(`  顧客名: ${booking.customerName || 'なし'}`);
        console.log(`  顧客メール: ${booking.customerEmail || 'なし'}`);
        console.log(`  顧客ID: ${booking.customerId || 'なし'}`);
        console.log(`  商品: ${booking.product?.title || 'なし'} (${booking.product?.sku || 'なし'})`);
        console.log(`  ステータス: ${booking.status}`);
        console.log(`  期間: ${booking.startDate?.toISOString().split('T')[0]} ～ ${booking.endDate?.toISOString().split('T')[0]}`);
      });
    } else {
      console.log('  該当する予約が見つかりませんでした');
    }

    return bookings.length > 0;

  } catch (error) {
    console.error('検索エラー:', error);
    return false;
  }
}

// trigram類似度検索のテスト
async function testTrigramSearch(query, description) {
  console.log(`\n=== ${description} (trigram): "${query}" ===`);
  
  try {
    // trigram類似度を使用した検索
    const result = await prisma.$queryRaw`
      SELECT 
        "customerName",
        similarity("customerName", ${query}) as similarity_score
      FROM bookings 
      WHERE "customerName" IS NOT NULL
        AND similarity("customerName", ${query}) > 0.3
      ORDER BY similarity_score DESC
      LIMIT 5;
    `;

    console.log(`trigram検索結果: ${result.length}件`);
    
    if (result.length > 0) {
      result.forEach((row, index) => {
        console.log(`[${index + 1}] ${row.customerName} (類似度: ${row.similarity_score.toFixed(3)})`);
      });
    } else {
      console.log('  該当する顧客名が見つかりませんでした');
    }

    return result.length > 0;

  } catch (error) {
    console.error('trigram検索エラー:', error);
    return false;
  }
}

// メイン関数
async function main() {
  console.log('改良された検索機能テストを開始します...\n');
  
  try {
    // 改良された検索テストケース
    const testCases = [
      // 日本語名前検索の改良テスト
      { query: '佐藤花子', description: '日本語名前検索（スペースなし）' },
      { query: '佐藤 花子', description: '日本語名前検索（スペースあり）' },
      { query: '鈴木一郎', description: '日本語名前検索（スペースなし・既存）' },
      { query: '田中太郎', description: '日本語名前検索（スペースなし・推測）' },
      { query: '佐藤', description: '姓のみ検索' },
      { query: '花子', description: '名のみ検索' },
      
      // 商品検索
      { query: 'シルバー', description: '商品名検索' },
      { query: 'SLV', description: 'SKU検索' },
      
      // その他の検索
      { query: 'sato.hanako', description: 'メール検索' },
      { query: 'cust_001', description: '顧客ID検索' }
    ];

    // trigram検索テストケース
    const trigramTestCases = [
      { query: '佐藤花子', description: '日本語名前のtrigram検索' },
      { query: 'さとうはなこ', description: 'ひらがな名前のtrigram検索' },
      { query: 'Sato Hanako', description: 'ローマ字名前のtrigram検索' }
    ];

    let successCount = 0;
    let totalCount = testCases.length + trigramTestCases.length;

    // 改良された検索テストを実行
    console.log('=== 改良された検索テスト ===');
    for (const testCase of testCases) {
      const success = await testImprovedSearch(testCase.query, testCase.description);
      if (success) successCount++;
    }

    // trigram検索テストを実行
    console.log('\n=== trigram類似度検索テスト ===');
    for (const testCase of trigramTestCases) {
      const success = await testTrigramSearch(testCase.query, testCase.description);
      if (success) successCount++;
    }

    // テスト結果サマリー
    console.log('\n=== テスト結果サマリー ===');
    console.log(`成功: ${successCount}/${totalCount} テスト`);
    console.log(`失敗: ${totalCount - successCount}/${totalCount} テスト`);
    
    if (successCount >= totalCount * 0.8) {
      console.log('✅ 検索機能が大幅に改善されました！');
    } else {
      console.log('⚠️  さらなる改善が必要です');
    }

  } catch (error) {
    console.error('テスト実行エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
main()
  .then(() => {
    console.log('\n改良された検索機能テストが完了しました');
    process.exit(0);
  })
  .catch((error) => {
    console.error('予期しないエラー:', error);
    process.exit(1);
  });
