/**
 * テスト結果検証ユーティリティ
 * 
 * このモジュールは、テスト結果を検証するための関数を提供します。
 * テスト結果の自動検証、期待値との比較、検証レポートの生成などの機能を含みます。
 */

import * as fs from 'fs';
import * as path from 'path';

// テスト結果の型定義
export interface TestResult {
  testName: string;
  success: boolean;
  timestamp: string;
  message?: string;
  error?: string;
  details?: Record<string, any>;
  expected?: any;
  actual?: any;
}

// テスト結果セットの型定義
export interface TestResultSet {
  timestamp: string;
  duration: number;
  totalTests: number;
  successCount: number;
  failureCount: number;
  tests: TestResult[];
}

// 検証結果の型定義
export interface ValidationResult {
  valid: boolean;
  message: string;
  details?: Record<string, any>;
}

/**
 * テスト結果を検証する関数
 * @param result テスト結果
 * @param expected 期待される結果
 * @returns 検証結果
 */
export function validateTestResult(result: TestResult, expected: any): ValidationResult {
  // 基本的な成功/失敗の検証
  if (result.success !== expected.success) {
    return {
      valid: false,
      message: `テスト "${result.testName}" の結果が期待と異なります。期待: ${expected.success ? '成功' : '失敗'}, 実際: ${result.success ? '成功' : '失敗'}`,
      details: {
        expected: expected.success,
        actual: result.success
      }
    };
  }

  // 詳細な検証（expected.detailsが指定されている場合）
  if (expected.details && result.details) {
    for (const key in expected.details) {
      if (result.details[key] !== expected.details[key]) {
        return {
          valid: false,
          message: `テスト "${result.testName}" の詳細結果が期待と異なります。キー: ${key}, 期待: ${expected.details[key]}, 実際: ${result.details[key]}`,
          details: {
            key,
            expected: expected.details[key],
            actual: result.details[key]
          }
        };
      }
    }
  }

  // 期待値と実際の値の比較（expected.valueとresult.actualが指定されている場合）
  if (expected.value !== undefined && result.actual !== undefined) {
    if (JSON.stringify(result.actual) !== JSON.stringify(expected.value)) {
      return {
        valid: false,
        message: `テスト "${result.testName}" の値が期待と異なります。`,
        details: {
          expected: expected.value,
          actual: result.actual
        }
      };
    }
  }

  return {
    valid: true,
    message: `テスト "${result.testName}" は期待通りの結果です。`
  };
}

/**
 * テスト結果セットを検証する関数
 * @param results テスト結果セット
 * @param expectedResults 期待される結果セット
 * @returns 検証結果の配列
 */
export function validateTestResults(results: TestResultSet, expectedResults: Record<string, any>): ValidationResult[] {
  const validationResults: ValidationResult[] = [];

  // 全体の成功/失敗数の検証
  if (results.successCount !== expectedResults.successCount || results.failureCount !== expectedResults.failureCount) {
    validationResults.push({
      valid: false,
      message: `テスト結果の成功/失敗数が期待と異なります。`,
      details: {
        expectedSuccess: expectedResults.successCount,
        actualSuccess: results.successCount,
        expectedFailure: expectedResults.failureCount,
        actualFailure: results.failureCount
      }
    });
  }

  // 個々のテスト結果の検証
  for (const result of results.tests) {
    const expectedResult = expectedResults.tests.find(e => e.testName === result.testName);
    if (expectedResult) {
      validationResults.push(validateTestResult(result, expectedResult));
    } else {
      validationResults.push({
        valid: false,
        message: `テスト "${result.testName}" の期待結果が定義されていません。`,
        details: {
          actual: result
        }
      });
    }
  }

  return validationResults;
}

/**
 * JSONファイルからテスト結果を読み込む関数
 * @param filePath JSONファイルのパス
 * @returns テスト結果セット
 */
export function loadTestResults(filePath: string): TestResultSet {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data) as TestResultSet;
  } catch (error) {
    throw new Error(`テスト結果の読み込みに失敗しました: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * テスト結果をJSONファイルに保存する関数
 * @param results テスト結果セット
 * @param filePath 保存先のファイルパス
 */
export function saveTestResults(results: TestResultSet, filePath: string): void {
  try {
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(filePath, JSON.stringify(results, null, 2));
  } catch (error) {
    throw new Error(`テスト結果の保存に失敗しました: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 検証結果をMarkdownレポートとして生成する関数
 * @param validationResults 検証結果の配列
 * @param outputPath 出力先のファイルパス
 * @returns 生成されたファイルのパス
 */
export function generateValidationReport(validationResults: ValidationResult[], outputPath: string): string {
  try {
    const validCount = validationResults.filter(r => r.valid).length;
    const invalidCount = validationResults.length - validCount;
    
    let markdown = `# テスト結果検証レポート

## 検証概要

このレポートは、テスト結果の自動検証結果をまとめたものです。

検証実行日時: ${new Date().toLocaleString('ja-JP')}

## 検証結果

- 合計検証数: ${validationResults.length}
- 成功: ${validCount}
- 失敗: ${invalidCount}
- 成功率: ${Math.round((validCount / validationResults.length) * 100)}%

## 詳細結果

`;

    // 各検証結果の詳細を追加
    validationResults.forEach((result, index) => {
      markdown += `### 検証${index + 1}: ${result.message}

- 結果: ${result.valid ? '✅ 成功' : '❌ 失敗'}
`;

      if (result.details) {
        markdown += `- 詳細:\n\`\`\`json\n${JSON.stringify(result.details, null, 2)}\n\`\`\`\n\n`;
      }
    });

    // レポートファイルに書き込み
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(outputPath, markdown);
    
    return outputPath;
  } catch (error) {
    throw new Error(`検証レポートの生成に失敗しました: ${error instanceof Error ? error.message : String(error)}`);
  }
}
