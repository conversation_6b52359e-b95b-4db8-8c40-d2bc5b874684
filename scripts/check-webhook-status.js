/**
 * Webhook設定状況チェックスクリプト
 */

import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';
import { PrismaClient } from '@prisma/client';

dotenv.config();

const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// Webhook一覧を取得するクエリ
const GET_WEBHOOKS = gql`
  query getWebhooks($first: Int!) {
    webhookSubscriptions(first: $first) {
      edges {
        node {
          id
          topic
          callbackUrl
          format
          createdAt
          updatedAt
        }
      }
    }
  }
`;

// 商品一覧を取得するクエリ
const GET_PRODUCTS = gql`
  query getProducts($first: Int!, $after: String) {
    products(first: $first, after: $after) {
      edges {
        node {
          id
          title
          status
          createdAt
          updatedAt
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`;

async function checkWebhookStatus() {
  console.log('🔍 Webhook設定状況チェックを開始...\n');

  try {
    // 1. Shopify Webhook設定を確認
    console.log('=== Shopify Webhook設定 ===');
    const webhookResult = await shopifyClient.request(GET_WEBHOOKS, {
      first: 20
    });

    const webhooks = webhookResult.webhookSubscriptions.edges.map(edge => edge.node);
    console.log(`設定されているWebhook: ${webhooks.length}件\n`);

    webhooks.forEach((webhook, index) => {
      console.log(`${index + 1}. ${webhook.topic}`);
      console.log(`   URL: ${webhook.callbackUrl}`);
      console.log(`   形式: ${webhook.format}`);
      console.log(`   作成日: ${webhook.createdAt}`);
      console.log(`   更新日: ${webhook.updatedAt}`);
      console.log('');
    });

    // 必要なWebhookが設定されているかチェック
    const requiredWebhooks = [
      'PRODUCTS_CREATE',
      'PRODUCTS_UPDATE'
    ];

    console.log('=== 必要なWebhook設定チェック ===');
    requiredWebhooks.forEach(topic => {
      const webhook = webhooks.find(w => w.topic === topic);
      if (webhook) {
        console.log(`✅ ${topic}: 設定済み`);
        console.log(`   URL: ${webhook.callbackUrl}`);
      } else {
        console.log(`❌ ${topic}: 未設定`);
      }
    });

    console.log('\n=== Shopify商品とデータベース同期状況 ===');

    // 2. Shopify商品を取得
    let allShopifyProducts = [];
    let hasNextPage = true;
    let cursor = null;

    while (hasNextPage && allShopifyProducts.length < 50) { // 最大50件まで
      const result = await shopifyClient.request(GET_PRODUCTS, {
        first: 20,
        after: cursor
      });

      const products = result.products.edges.map(edge => edge.node);
      allShopifyProducts = allShopifyProducts.concat(products);

      hasNextPage = result.products.pageInfo.hasNextPage;
      cursor = result.products.pageInfo.endCursor;
    }

    console.log(`Shopify商品数: ${allShopifyProducts.length}件`);

    // 3. データベース商品を取得
    const dbProducts = await prisma.product.findMany({
      select: {
        id: true,
        shopifyId: true,
        title: true,
        createdAt: true,
        updatedAt: true
      }
    });

    console.log(`データベース商品数: ${dbProducts.length}件\n`);

    // 4. 同期状況をチェック
    console.log('=== 同期状況詳細 ===');

    const syncedProducts = [];
    const missingProducts = [];

    allShopifyProducts.forEach(shopifyProduct => {
      const shopifyId = shopifyProduct.id.replace('gid://shopify/Product/', '');
      const dbProduct = dbProducts.find(p => p.shopifyId === shopifyId);

      if (dbProduct) {
        syncedProducts.push({
          shopifyId,
          title: shopifyProduct.title,
          shopifyCreated: shopifyProduct.createdAt,
          dbCreated: dbProduct.createdAt,
          dbUpdated: dbProduct.updatedAt
        });
      } else {
        missingProducts.push({
          shopifyId,
          title: shopifyProduct.title,
          status: shopifyProduct.status,
          createdAt: shopifyProduct.createdAt,
          updatedAt: shopifyProduct.updatedAt
        });
      }
    });

    console.log(`✅ 同期済み商品: ${syncedProducts.length}件`);
    console.log(`❌ 未同期商品: ${missingProducts.length}件\n`);

    if (missingProducts.length > 0) {
      console.log('=== 未同期商品一覧 ===');
      missingProducts.forEach((product, index) => {
        console.log(`${index + 1}. ${product.title}`);
        console.log(`   Shopify ID: ${product.shopifyId}`);
        console.log(`   ステータス: ${product.status}`);
        console.log(`   作成日: ${product.createdAt}`);
        console.log(`   更新日: ${product.updatedAt}`);
        console.log('');
      });

      // 未同期商品の作成日を分析
      console.log('=== 未同期商品の作成日分析 ===');
      const today = new Date();
      const oneWeekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

      const recentMissing = missingProducts.filter(p => new Date(p.createdAt) > oneWeekAgo);
      const weekToMonthMissing = missingProducts.filter(p => {
        const created = new Date(p.createdAt);
        return created <= oneWeekAgo && created > oneMonthAgo;
      });
      const oldMissing = missingProducts.filter(p => new Date(p.createdAt) <= oneMonthAgo);

      console.log(`過去1週間以内: ${recentMissing.length}件`);
      console.log(`1週間〜1ヶ月前: ${weekToMonthMissing.length}件`);
      console.log(`1ヶ月以上前: ${oldMissing.length}件`);

      if (recentMissing.length > 0) {
        console.log('\n⚠️ 最近作成された商品が同期されていません。Webhookに問題がある可能性があります。');
      }
    }

    // 5. データベースにあってShopifyにない商品をチェック
    const orphanedProducts = dbProducts.filter(dbProduct => {
      return !allShopifyProducts.some(shopifyProduct => {
        const shopifyId = shopifyProduct.id.replace('gid://shopify/Product/', '');
        return shopifyId === dbProduct.shopifyId;
      });
    });

    if (orphanedProducts.length > 0) {
      console.log(`\n🗑️ データベースにのみ存在する商品: ${orphanedProducts.length}件`);
      orphanedProducts.forEach((product, index) => {
        console.log(`${index + 1}. ${product.title} (Shopify ID: ${product.shopifyId})`);
      });
    }

    // 6. 推奨アクション
    console.log('\n=== 推奨アクション ===');
    
    if (missingProducts.length > 0) {
      console.log('1. 未同期商品の手動同期を実行');
      console.log('   → node scripts/sync-shopify-products.js');
    }

    const missingCreateWebhook = !webhooks.find(w => w.topic === 'PRODUCTS_CREATE');
    const missingUpdateWebhook = !webhooks.find(w => w.topic === 'PRODUCTS_UPDATE');

    if (missingCreateWebhook || missingUpdateWebhook) {
      console.log('2. 不足しているWebhookを設定');
      if (missingCreateWebhook) {
        console.log('   → PRODUCTS_CREATE Webhookを設定');
      }
      if (missingUpdateWebhook) {
        console.log('   → PRODUCTS_UPDATE Webhookを設定');
      }
    }

    if (recentMissing.length > 0) {
      console.log('3. Webhookエンドポイントの動作確認');
      console.log('   → /webhooks/products/create');
      console.log('   → /webhooks/products/update');
    }

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
checkWebhookStatus();
