/**
 * 注文作成テストスクリプト（直接APIを使用）
 *
 * このスクリプトは、Shopify Admin APIを直接使用して注文を作成します。
 *
 * 実行方法: npx tsx scripts/test-order-creation-direct.ts
 */

import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Shopify GraphQL APIクライアントの初期化
function createShopifyClient() {
  const shop = process.env.SHOPIFY_SHOP;
  const accessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

  if (!shop || !accessToken) {
    throw new Error('SHOPIFY_SHOP または SHOPIFY_ADMIN_API_ACCESS_TOKEN が設定されていません');
  }

  return new GraphQLClient(
    `https://${shop}/admin/api/2025-01/graphql.json`,
    {
      headers: {
        'X-Shopify-Access-Token': accessToken,
        'Content-Type': 'application/json',
      },
    }
  );
}

// ドラフト注文作成ミューテーション
const CREATE_DRAFT_ORDER = gql`
  mutation draftOrderCreate($input: DraftOrderInput!) {
    draftOrderCreate(input: $input) {
      draftOrder {
        id
        name
        totalPrice
        customer {
          id
          email
        }
        lineItems(first: 10) {
          edges {
            node {
              title
              quantity
              originalUnitPrice
              variant {
                id
                title
              }
            }
          }
        }
        metafields(first: 10) {
          edges {
            node {
              namespace
              key
              value
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// ドラフト注文完了ミューテーション
const COMPLETE_DRAFT_ORDER = gql`
  mutation draftOrderComplete($id: ID!, $paymentPending: Boolean) {
    draftOrderComplete(id: $id, paymentPending: $paymentPending) {
      draftOrder {
        id
        name
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 注文作成テスト
async function testOrderCreation() {
  try {
    console.log('注文作成テストを開始します...');

    // Shopify APIクライアントを作成
    const client = createShopifyClient();

    // 顧客IDを取得（環境変数から）
    const customerId = process.env.TEST_CUSTOMER_ID;
    if (!customerId) {
      throw new Error('TEST_CUSTOMER_ID が設定されていません');
    }

    // 商品IDを取得（環境変数から）
    const productId = process.env.TEST_PRODUCT_ID;
    const variantId = process.env.TEST_VARIANT_ID;
    if (!productId || !variantId) {
      throw new Error('TEST_PRODUCT_ID または TEST_VARIANT_ID が設定されていません');
    }

    console.log(`テスト情報: 顧客ID=${customerId}, 商品ID=${productId}, バリアントID=${variantId}`);

    // 顧客IDを正規化
    const normalizedCustomerId = customerId.startsWith('gid://shopify/Customer/')
      ? customerId
      : `gid://shopify/Customer/${customerId}`;

    // 現在の日時を取得
    const now = new Date();
    const startDate = new Date(now);
    startDate.setDate(startDate.getDate() + 1); // 明日から

    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 3); // 3日間

    // 日付をフォーマット
    const formatDate = (date: Date) => {
      return date.toISOString().split('T')[0];
    };

    const rentalPeriod = `${formatDate(startDate)} 〜 ${formatDate(endDate)}`;

    // ドラフト注文作成用の入力データを作成
    const draftOrderInput = {
      customerId: normalizedCustomerId,
      lineItems: [
        {
          title: `テスト商品 (レンタル: ${rentalPeriod})`,
          quantity: 1,
          taxable: true,
          requiresShipping: false,
          variantId: variantId,
          originalUnitPrice: 5000
        }
      ],
      tags: ['rental', 'test', 'provisional'],
      note: `テスト予約\n予約期間: ${rentalPeriod}\n備考: テスト注文です`,
      metafields: [
        {
          namespace: "custom",
          key: "booking_id",
          value: "test-booking-" + Date.now(),
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "booking_number",
          value: "TEST-" + Math.floor(Math.random() * 10000),
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "booking_type",
          value: "PROVISIONAL",
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "rental_period",
          value: rentalPeriod,
          type: "single_line_text_field"
        }
      ]
    };

    console.log('ドラフト注文作成データ:', JSON.stringify(draftOrderInput, null, 2));

    // ドラフト注文を作成
    console.log('ドラフト注文を作成中...');
    const draftResponse = await client.request(CREATE_DRAFT_ORDER, {
      input: draftOrderInput
    });

    console.log('ドラフト注文作成応答:', JSON.stringify(draftResponse, null, 2));

    if (draftResponse.draftOrderCreate.userErrors.length > 0) {
      console.error('ドラフト注文作成エラー:', draftResponse.draftOrderCreate.userErrors);
      throw new Error(`ドラフト注文の作成中にエラーが発生しました: ${JSON.stringify(draftResponse.draftOrderCreate.userErrors)}`);
    }

    // 作成されたドラフト注文を完了状態にする
    const draftOrderId = draftResponse.draftOrderCreate.draftOrder.id;
    console.log(`ドラフト注文ID: ${draftOrderId} を完了状態にします`);

    const completeResponse = await client.request(COMPLETE_DRAFT_ORDER, {
      id: draftOrderId,
      paymentPending: true // 仮予約の場合は支払い保留
    });

    console.log('ドラフト注文完了応答:', JSON.stringify(completeResponse, null, 2));

    if (completeResponse.draftOrderComplete.userErrors.length > 0) {
      console.error('ドラフト注文完了エラー:', completeResponse.draftOrderComplete.userErrors);
      throw new Error(`ドラフト注文の完了中にエラーが発生しました: ${JSON.stringify(completeResponse.draftOrderComplete.userErrors)}`);
    }

    // ドラフト注文IDを取得
    const completedDraftOrderId = completeResponse.draftOrderComplete.draftOrder.id;
    const draftOrderName = completeResponse.draftOrderComplete.draftOrder.name;

    console.log(`ドラフト注文が完了しました: ID ${completedDraftOrderId}, 番号 ${draftOrderName}`);
    console.log(`Shopify管理画面で確認: https://${process.env.SHOPIFY_SHOP}/admin/draft_orders/${draftOrderName.replace('#D', '')}`);

    return {
      success: true,
      draftOrderId: completedDraftOrderId,
      draftOrderName
    };
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    throw error;
  }
}

// スクリプトを実行
testOrderCreation()
  .then(() => {
    console.log('テストが完了しました');
    process.exit(0);
  })
  .catch((error) => {
    console.error('テストが失敗しました:', error);
    process.exit(1);
  });
