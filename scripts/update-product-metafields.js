/**
 * 商品メタフィールド更新スクリプト
 *
 * このスクリプトは、指定した商品のすべてのメタフィールドを設定します。
 */

import dotenv from 'dotenv';
import { GraphQLClient, gql } from 'graphql-request';

dotenv.config();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品取得のGraphQLクエリ
const GET_PRODUCT = gql`
  query getProduct($id: ID!) {
    product(id: $id) {
      id
      title
      handle
      variants(first: 10) {
        edges {
          node {
            id
            title
            sku
            price
          }
        }
      }
      metafields(first: 50) {
        edges {
          node {
            id
            namespace
            key
            value
            type
          }
        }
      }
    }
  }
`;

// メタフィールド設定のGraphQLミューテーション
const SET_METAFIELDS = gql`
  mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// 商品を取得する関数
async function getProduct(id) {
  try {
    const result = await client.request(GET_PRODUCT, { id });
    return result.product;
  } catch (error) {
    console.error('商品取得中にエラーが発生しました:', error);
    return null;
  }
}

// メタフィールドを設定する関数
async function setMetafields(ownerId, metafields) {
  try {
    const metafieldsInput = metafields.map(metafield => {
      const input = {
        ownerId,
        namespace: metafield.namespace,
        key: metafield.key
      };

      if (metafield.type === 'json') {
        input.value = JSON.stringify(metafield.value);
        input.type = 'json';
      } else if (metafield.type === 'boolean') {
        input.value = metafield.value.toString();
        input.type = 'boolean';
      } else if (metafield.type === 'number_integer' || metafield.type === 'number_decimal') {
        input.value = metafield.value.toString();
        input.type = metafield.type;
      } else if (metafield.type === 'date') {
        input.value = metafield.value;
        input.type = 'date';
      } else {
        input.value = metafield.value;
        input.type = metafield.type || 'single_line_text_field';
      }

      return input;
    });

    const result = await client.request(SET_METAFIELDS, {
      metafields: metafieldsInput
    });

    if (result.metafieldsSet.userErrors.length > 0) {
      console.error('メタフィールド設定中にエラーが発生しました:', result.metafieldsSet.userErrors);
      return false;
    }

    console.log(`${metafieldsInput.length}件のメタフィールドを設定しました。`);
    return true;
  } catch (error) {
    console.error('メタフィールド設定中にエラーが発生しました:', error);
    return false;
  }
}

// メインの実行関数
async function updateProductMetafields(productId) {
  console.log('商品メタフィールドの更新を開始します...');

  // 商品を取得
  const product = await getProduct(productId);
  if (!product) {
    console.error('商品が見つかりませんでした。');
    return;
  }

  console.log(`商品 ${product.title} のメタフィールドを更新します。`);

  // 既存のメタフィールドを確認
  const existingMetafields = product.metafields.edges.map(edge => edge.node);
  console.log(`既存のメタフィールド: ${existingMetafields.length}件`);

  // 基本バリアントを取得
  const baseVariant = product.variants.edges.find(edge =>
    edge.node.title.includes('1日') || edge.node.title === 'Default Title'
  )?.node;

  if (!baseVariant) {
    console.error('基本バリアントが見つかりませんでした。');
    return;
  }

  // SKUから商品コードと詳細コードを抽出
  const skuMatch = baseVariant.sku.match(/^(\d+)-(\d+)/);
  const productCode = skuMatch ? skuMatch[1] : '';
  const detailCode = skuMatch ? skuMatch[2] : '';

  // 設定するメタフィールド
  const metafields = [
    // 既存のJSON型メタフィールド
    {
      namespace: 'rental',
      key: 'basic_info',
      type: 'json',
      value: {
        productCode,
        detailCode,
        kana: 'ベーシックソファオフホワイト1シーター',
        dimensions: {
          width: 87,
          depth: 74,
          height: 76
        },
        seatDimensions: {
          width: 52,
          depth: 54,
          height: 40
        },
        material: 'ファブリック',
        color: 'オフホワイト',
        maker: 'ヤマナリ',
        campaign: '通常商品',
        notes: 'クリーニング済みですが全体的に黄ばみ発生（H30.12.15)'
      }
    },
    {
      namespace: 'rental',
      key: 'pricing',
      type: 'json',
      value: {
        basePrice: parseInt(baseVariant.price),
        depositRate: 0.1,
        discountRules: {
          day2_6_rate: 0.2,
          day7_plus_rate: 0.1
        },
        minimumDays: 1,
        maximumDays: 30
      }
    },
    {
      namespace: 'rental',
      key: 'inventory_items',
      type: 'json',
      value: [
        {
          id: `item-${productCode}-${detailCode}-1`,
          sku: `${productCode}-${detailCode}-1`,
          status: 'available',
          location: 'NY',
          notes: '背面向かって右側うっすら黒いしみ'
        },
        {
          id: `item-${productCode}-${detailCode}-2`,
          sku: `${productCode}-${detailCode}-2`,
          status: 'maintenance',
          location: 'NY',
          notes: '向かって左アーム手前と正面左側に黄色い輪染み有'
        }
      ]
    },
    {
      namespace: 'rental',
      key: 'reservation_info',
      type: 'json',
      value: [
        {
          itemId: `item-${productCode}-${detailCode}-1`,
          reservations: [
            {
              id: 'reservation-001',
              startDate: '2025-05-22',
              endDate: '2025-05-25',
              status: 'confirmed',
              customerName: '山田太郎',
              customerEmail: '<EMAIL>',
              orderId: 'gid://shopify/Order/1001',
              orderLineItemId: 'gid://shopify/LineItem/1001',
              notes: '特になし'
            }
          ]
        },
        {
          itemId: `item-${productCode}-${detailCode}-2`,
          reservations: []
        }
      ]
    },
    // その他のメタフィールド
    {
      namespace: 'rental',
      key: 'variation_type',
      type: 'single_line_text_field',
      value: 'color'
    },
    {
      namespace: 'rental',
      key: 'variant_mapping',
      type: 'json',
      value: {
        group: `group-${productCode}`,
        variants: [
          {
            id: product.id,
            type: 'color',
            value: 'オフホワイト'
          }
        ]
      }
    },
    {
      namespace: 'rental',
      key: 'color',
      type: 'single_line_text_field',
      value: 'オフホワイト'
    },
    {
      namespace: 'rental',
      key: 'material',
      type: 'single_line_text_field',
      value: 'ファブリック'
    },
    {
      namespace: 'product',
      key: 'height',
      type: 'number_integer',
      value: 76
    },
    {
      namespace: 'product',
      key: 'width',
      type: 'number_integer',
      value: 87
    },
    {
      namespace: 'product',
      key: 'depth',
      type: 'number_integer',
      value: 74
    },
    {
      namespace: 'rental',
      key: 'location',
      type: 'single_line_text_field',
      value: 'NY'
    },
    {
      namespace: 'rental',
      key: 'booking_notes',
      type: 'multi_line_text_field',
      value: '特になし'
    },
    {
      namespace: 'rental',
      key: 'purchase_price',
      type: 'number_decimal',
      value: 50000
    },
    {
      namespace: 'rental',
      key: 'purchase_place',
      type: 'single_line_text_field',
      value: 'ヤマナリ本店'
    },
    {
      namespace: 'rental',
      key: 'purchase_date',
      type: 'date',
      value: '2023-05-01'
    },
    {
      namespace: 'rental',
      key: 'manufacturer',
      type: 'single_line_text_field',
      value: 'ヤマナリ'
    },
    {
      namespace: 'rental',
      key: 'designer',
      type: 'single_line_text_field',
      value: '山田デザイン'
    },
    {
      namespace: 'rental',
      key: 'maintenance_notes',
      type: 'multi_line_text_field',
      value: 'クリーニング済み（2023年12月）'
    },
    {
      namespace: 'rental',
      key: 'is_disposed',
      type: 'boolean',
      value: false
    },
    // 廃棄日は空の場合は設定しない
    // {
    //   namespace: 'rental',
    //   key: 'disposal_date',
    //   type: 'date',
    //   value: ''
    // },
    // 廃棄理由は空の場合は設定しない
    // {
    //   namespace: 'rental',
    //   key: 'disposal_reason',
    //   type: 'multi_line_text_field',
    //   value: ''
    // },
    {
      namespace: 'rental',
      key: 'status',
      type: 'single_line_text_field',
      value: 'available'
    }
  ];

  // メタフィールドを設定
  await setMetafields(product.id, metafields);

  console.log('商品メタフィールドの更新が完了しました。');
}

// コマンドライン引数から商品IDを取得
const productId = process.argv[2];
if (!productId) {
  console.error('商品IDを指定してください。例: node update-product-metafields.js gid://shopify/Product/1234567890');
  process.exit(1);
}

// スクリプト実行
updateProductMetafields(productId).catch(error => {
  console.error('スクリプト実行中にエラーが発生しました:', error);
  process.exit(1);
});
