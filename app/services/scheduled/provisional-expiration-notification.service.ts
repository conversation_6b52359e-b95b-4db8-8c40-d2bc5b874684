/**
 * 仮予約期限切れ通知サービス
 *
 * 期限切れ前の仮予約に通知を送信するサービス
 */

import { PrismaClient } from '@prisma/client';
import { format, addDays, differenceInDays } from 'date-fns';
import { ja } from 'date-fns/locale';
// 相対パスに修正
import { sendEmail } from '../../utils/email';
import { logger } from '../../utils/logger';

const prisma = new PrismaClient();

/**
 * 仮予約期限切れ通知サービス
 */
export class ProvisionalExpirationNotificationService {
  /**
   * 期限切れ前の仮予約に通知を送信
   * @param daysBeforeExpiration 期限切れ何日前に通知するか
   */
  async sendExpirationNotifications(daysBeforeExpiration: number = 3): Promise<{
    success: boolean;
    processedCount: number;
    errors: string[];
  }> {
    const errors: string[] = [];
    let processedCount = 0;

    try {
      logger.info(`期限切れ${daysBeforeExpiration}日前の仮予約を検索中...`);
      const now = new Date();

      // 期限切れ日が指定日数後の仮予約を検索
      const expirationDate = addDays(now, daysBeforeExpiration);
      const startOfDay = new Date(expirationDate.setHours(0, 0, 0, 0));
      const endOfDay = new Date(expirationDate.setHours(23, 59, 59, 999));

      // 仮予約を検索
      const provisionalBookings = await prisma.booking.findMany({
        where: {
          bookingType: 'PROVISIONAL',
          status: 'PROVISIONAL',
          expiresAt: {
            gte: startOfDay,
            lte: endOfDay
          }
        },
        include: {
          product: true
        }
      });

      if (provisionalBookings.length === 0) {
        logger.info(`期限切れ${daysBeforeExpiration}日前の仮予約はありません`);
        return { success: true, processedCount: 0, errors: [] };
      }

      logger.info(`${provisionalBookings.length}件の期限切れ${daysBeforeExpiration}日前の仮予約が見つかりました`);

      // 各予約に通知を送信
      for (const booking of provisionalBookings) {
        try {
          // 顧客にメール通知
          if (booking.customerEmail) {
            const emailResult = await sendEmail({
              to: booking.customerEmail,
              subject: `【重要】仮予約の期限が近づいています（予約番号: ${booking.bookingId}）`,
              text: `
${booking.customerName || "お客様"}

いつもご利用ありがとうございます。

あなたの仮予約（予約番号: ${booking.bookingId}）の期限が近づいています。
仮予約は${format(booking.expiresAt || new Date(booking.startDate), 'yyyy年MM月dd日', { locale: ja })}に期限切れとなります。

■ 予約情報
予約番号: ${booking.bookingId}
商品名: ${booking.product.title}
予約期間: ${format(booking.startDate, 'yyyy年MM月dd日', { locale: ja })} 〜 ${format(booking.endDate, 'yyyy年MM月dd日', { locale: ja })}
期限日: ${format(booking.expiresAt || new Date(booking.startDate), 'yyyy年MM月dd日', { locale: ja })}

本予約への変更をご希望の場合は、期限日までにご連絡ください。
期限日を過ぎると、仮予約は自動的にキャンセルされます。

ご不明な点がございましたら、お気軽にお問い合わせください。

よろしくお願いいたします。
              `,
              html: `
<p>${booking.customerName || "お客様"}</p>

<p>いつもご利用ありがとうございます。</p>

<p>あなたの仮予約（予約番号: ${booking.bookingId}）の期限が近づいています。<br>
仮予約は<strong>${format(booking.expiresAt || new Date(booking.startDate), 'yyyy年MM月dd日', { locale: ja })}</strong>に期限切れとなります。</p>

<h3>予約情報</h3>
<ul>
  <li>予約番号: ${booking.bookingId}</li>
  <li>商品名: ${booking.product.title}</li>
  <li>予約期間: ${format(booking.startDate, 'yyyy年MM月dd日', { locale: ja })} 〜 ${format(booking.endDate, 'yyyy年MM月dd日', { locale: ja })}</li>
  <li>期限日: ${format(booking.expiresAt || new Date(booking.startDate), 'yyyy年MM月dd日', { locale: ja })}</li>
</ul>

<p>本予約への変更をご希望の場合は、期限日までにご連絡ください。<br>
期限日を過ぎると、仮予約は自動的にキャンセルされます。</p>

<p>ご不明な点がございましたら、お気軽にお問い合わせください。</p>

<p>よろしくお願いいたします。</p>
              `
            });

            logger.info(`顧客への通知メール送信: ${booking.customerEmail}`, emailResult);
            processedCount++;
          } else {
            logger.warn(`予約ID ${booking.id} に顧客メールアドレスがありません`);
            errors.push(`予約ID ${booking.id} に顧客メールアドレスがありません`);
          }
        } catch (error) {
          logger.error(`予約ID ${booking.id} の通知送信中にエラーが発生しました:`, error);
          errors.push(`予約ID ${booking.id} の通知送信中にエラーが発生しました: ${error.message}`);
        }
      }

      // 管理者にまとめて通知
      try {
        const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";

        const emailResult = await sendEmail({
          to: adminEmail,
          subject: `【通知】${provisionalBookings.length}件の仮予約が期限切れ間近です`,
          text: `
${provisionalBookings.length}件の仮予約が${daysBeforeExpiration}日後に期限切れとなります。

■ 期限切れ間近の仮予約一覧
${provisionalBookings.map(booking => `
予約番号: ${booking.bookingId}
商品名: ${booking.product.title}
顧客名: ${booking.customerName || "未設定"}
予約期間: ${format(booking.startDate, 'yyyy/MM/dd', { locale: ja })} 〜 ${format(booking.endDate, 'yyyy/MM/dd', { locale: ja })}
期限日: ${format(booking.expiresAt || new Date(booking.startDate), 'yyyy/MM/dd', { locale: ja })}
`).join('\n')}

管理画面から対応をお願いします。
          `,
          html: `
<h2>${provisionalBookings.length}件の仮予約が${daysBeforeExpiration}日後に期限切れとなります。</h2>

<h3>期限切れ間近の仮予約一覧</h3>
${provisionalBookings.map(booking => `
<div style="margin-bottom: 1rem; padding: 0.5rem; border: 1px solid #eee; border-radius: 4px;">
  <p><strong>予約番号:</strong> ${booking.bookingId}</p>
  <p><strong>商品名:</strong> ${booking.product.title}</p>
  <p><strong>顧客名:</strong> ${booking.customerName || "未設定"}</p>
  <p><strong>予約期間:</strong> ${format(booking.startDate, 'yyyy/MM/dd', { locale: ja })} 〜 ${format(booking.endDate, 'yyyy/MM/dd', { locale: ja })}</p>
  <p><strong>期限日:</strong> ${format(booking.expiresAt || new Date(booking.startDate), 'yyyy/MM/dd', { locale: ja })}</p>
</div>
`).join('')}

<p>管理画面から対応をお願いします。</p>
          `
        });

        logger.info(`管理者への通知メール送信: ${adminEmail}`, emailResult);
      } catch (error) {
        logger.error('管理者への通知メール送信エラー:', error);
        errors.push(`管理者への通知メール送信エラー: ${error.message}`);
      }

      logger.info(`期限切れ通知処理が完了しました: ${processedCount}件処理, ${errors.length}件エラー`);
      return { success: true, processedCount, errors };
    } catch (error) {
      logger.error('期限切れ通知処理中にエラーが発生しました:', error);
      errors.push(`期限切れ通知処理中にエラーが発生しました: ${error.message}`);
      return { success: false, processedCount, errors };
    }
  }
}
