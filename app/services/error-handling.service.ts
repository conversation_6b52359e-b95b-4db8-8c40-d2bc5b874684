/**
 * エラーハンドリングサービス
 * 
 * このサービスは、アプリケーション全体でのエラーハンドリングを統一し、
 * ユーザーフレンドリーなエラーメッセージを提供するための機能を提供します。
 */

// エラーコードの定義
export enum ErrorCode {
  // 一般的なエラー
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  
  // API関連のエラー
  API_ERROR = 'API_ERROR',
  API_RATE_LIMIT = 'API_RATE_LIMIT',
  API_AUTHENTICATION_ERROR = 'API_AUTHENTICATION_ERROR',
  API_PERMISSION_ERROR = 'API_PERMISSION_ERROR',
  
  // データベース関連のエラー
  DATABASE_ERROR = 'DATABASE_ERROR',
  DATABASE_CONNECTION_ERROR = 'DATABASE_CONNECTION_ERROR',
  DATABASE_QUERY_ERROR = 'DATABASE_QUERY_ERROR',
  DATABASE_CONSTRAINT_ERROR = 'DATABASE_CONSTRAINT_ERROR',
  
  // 予約関連のエラー
  BOOKING_ERROR = 'BOOKING_ERROR',
  BOOKING_CONFLICT = 'BOOKING_CONFLICT',
  BOOKING_INVALID_DATE = 'BOOKING_INVALID_DATE',
  BOOKING_PAST_DATE = 'BOOKING_PAST_DATE',
  BOOKING_TOO_LONG = 'BOOKING_TOO_LONG',
  BOOKING_TOO_SOON = 'BOOKING_TOO_SOON',
  
  // 商品関連のエラー
  PRODUCT_ERROR = 'PRODUCT_ERROR',
  PRODUCT_NOT_FOUND = 'PRODUCT_NOT_FOUND',
  PRODUCT_OUT_OF_STOCK = 'PRODUCT_OUT_OF_STOCK',
  PRODUCT_UNAVAILABLE = 'PRODUCT_UNAVAILABLE',
  
  // 注文関連のエラー
  ORDER_ERROR = 'ORDER_ERROR',
  ORDER_NOT_FOUND = 'ORDER_NOT_FOUND',
  ORDER_ALREADY_PROCESSED = 'ORDER_ALREADY_PROCESSED',
  ORDER_PAYMENT_FAILED = 'ORDER_PAYMENT_FAILED'
}

// エラーの重大度
export enum ErrorSeverity {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL'
}

// エラー情報のインターフェース
export interface ErrorInfo {
  code: ErrorCode;
  message: string;
  severity: ErrorSeverity;
  details?: any;
  originalError?: Error;
  timestamp: Date;
}

// アプリケーションエラークラス
export class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly severity: ErrorSeverity;
  public readonly details?: any;
  public readonly originalError?: Error;
  public readonly timestamp: Date;

  constructor(info: Omit<ErrorInfo, 'timestamp'>) {
    super(info.message);
    this.name = 'AppError';
    this.code = info.code;
    this.severity = info.severity;
    this.details = info.details;
    this.originalError = info.originalError;
    this.timestamp = new Date();
    
    // スタックトレースを正しく設定
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }
}

export class ErrorHandlingService {
  private static instance: ErrorHandlingService;
  private errorListeners: ((error: ErrorInfo) => void)[] = [];
  private errorHistory: ErrorInfo[] = [];
  private maxHistorySize = 100;

  private constructor() {}

  /**
   * シングルトンインスタンスを取得
   */
  public static getInstance(): ErrorHandlingService {
    if (!ErrorHandlingService.instance) {
      ErrorHandlingService.instance = new ErrorHandlingService();
    }
    return ErrorHandlingService.instance;
  }

  /**
   * エラーを処理する
   */
  public handleError(error: Error | AppError | any, defaultCode = ErrorCode.UNKNOWN_ERROR): ErrorInfo {
    let errorInfo: ErrorInfo;
    
    if (error instanceof AppError) {
      // AppErrorの場合はそのまま使用
      errorInfo = {
        code: error.code,
        message: error.message,
        severity: error.severity,
        details: error.details,
        originalError: error.originalError,
        timestamp: error.timestamp
      };
    } else {
      // その他のエラーをAppErrorに変換
      const code = this.detectErrorCode(error) || defaultCode;
      const severity = this.getSeverityForCode(code);
      const message = this.getUserFriendlyMessage(code, error);
      
      errorInfo = {
        code,
        message,
        severity,
        details: error.details || {},
        originalError: error,
        timestamp: new Date()
      };
    }
    
    // エラー履歴に追加
    this.addToErrorHistory(errorInfo);
    
    // エラーリスナーに通知
    this.notifyErrorListeners(errorInfo);
    
    // エラーをログに記録
    this.logError(errorInfo);
    
    return errorInfo;
  }

  /**
   * エラーコードを検出する
   */
  private detectErrorCode(error: any): ErrorCode | null {
    // Prismaエラーの検出
    if (error.name === 'PrismaClientKnownRequestError') {
      if (error.code === 'P2002') {
        return ErrorCode.DATABASE_CONSTRAINT_ERROR;
      } else if (error.code === 'P2025') {
        return ErrorCode.PRODUCT_NOT_FOUND;
      } else {
        return ErrorCode.DATABASE_ERROR;
      }
    }
    
    // ネットワークエラーの検出
    if (error.name === 'FetchError' || error.message?.includes('network')) {
      return ErrorCode.NETWORK_ERROR;
    }
    
    // タイムアウトエラーの検出
    if (error.name === 'AbortError' || error.message?.includes('timeout')) {
      return ErrorCode.TIMEOUT_ERROR;
    }
    
    // API関連エラーの検出
    if (error.message?.includes('API')) {
      if (error.message.includes('rate limit')) {
        return ErrorCode.API_RATE_LIMIT;
      } else if (error.message.includes('authentication') || error.message.includes('401')) {
        return ErrorCode.API_AUTHENTICATION_ERROR;
      } else if (error.message.includes('permission') || error.message.includes('403')) {
        return ErrorCode.API_PERMISSION_ERROR;
      } else {
        return ErrorCode.API_ERROR;
      }
    }
    
    // 予約関連エラーの検出
    if (error.code?.startsWith('BOOKING_')) {
      return error.code as ErrorCode;
    }
    
    return null;
  }

  /**
   * エラーコードに基づいて重大度を取得
   */
  private getSeverityForCode(code: ErrorCode): ErrorSeverity {
    switch (code) {
      case ErrorCode.UNKNOWN_ERROR:
      case ErrorCode.API_ERROR:
      case ErrorCode.DATABASE_ERROR:
      case ErrorCode.DATABASE_CONNECTION_ERROR:
        return ErrorSeverity.CRITICAL;
        
      case ErrorCode.NETWORK_ERROR:
      case ErrorCode.TIMEOUT_ERROR:
      case ErrorCode.API_RATE_LIMIT:
      case ErrorCode.API_AUTHENTICATION_ERROR:
      case ErrorCode.API_PERMISSION_ERROR:
      case ErrorCode.DATABASE_QUERY_ERROR:
      case ErrorCode.ORDER_PAYMENT_FAILED:
        return ErrorSeverity.ERROR;
        
      case ErrorCode.VALIDATION_ERROR:
      case ErrorCode.BOOKING_CONFLICT:
      case ErrorCode.BOOKING_INVALID_DATE:
      case ErrorCode.BOOKING_PAST_DATE:
      case ErrorCode.BOOKING_TOO_LONG:
      case ErrorCode.PRODUCT_NOT_FOUND:
      case ErrorCode.PRODUCT_OUT_OF_STOCK:
      case ErrorCode.ORDER_NOT_FOUND:
        return ErrorSeverity.WARNING;
        
      case ErrorCode.BOOKING_TOO_SOON:
      case ErrorCode.PRODUCT_UNAVAILABLE:
        return ErrorSeverity.INFO;
        
      default:
        return ErrorSeverity.ERROR;
    }
  }

  /**
   * ユーザーフレンドリーなエラーメッセージを取得
   */
  public getUserFriendlyMessage(code: ErrorCode, error?: any): string {
    switch (code) {
      // 一般的なエラー
      case ErrorCode.UNKNOWN_ERROR:
        return '予期せぬエラーが発生しました。しばらく経ってからもう一度お試しください。';
      case ErrorCode.VALIDATION_ERROR:
        return '入力内容に誤りがあります。ご確認ください。';
      case ErrorCode.NETWORK_ERROR:
        return 'ネットワーク接続に問題があります。インターネット接続をご確認ください。';
      case ErrorCode.TIMEOUT_ERROR:
        return 'リクエストがタイムアウトしました。しばらく経ってからもう一度お試しください。';
      
      // API関連のエラー
      case ErrorCode.API_ERROR:
        return 'APIエラーが発生しました。しばらく経ってからもう一度お試しください。';
      case ErrorCode.API_RATE_LIMIT:
        return 'APIリクエストの制限に達しました。しばらく経ってからもう一度お試しください。';
      case ErrorCode.API_AUTHENTICATION_ERROR:
        return '認証エラーが発生しました。ログインし直してください。';
      case ErrorCode.API_PERMISSION_ERROR:
        return '権限エラーが発生しました。この操作を行う権限がありません。';
      
      // データベース関連のエラー
      case ErrorCode.DATABASE_ERROR:
        return 'データベースエラーが発生しました。しばらく経ってからもう一度お試しください。';
      case ErrorCode.DATABASE_CONNECTION_ERROR:
        return 'データベース接続エラーが発生しました。しばらく経ってからもう一度お試しください。';
      case ErrorCode.DATABASE_QUERY_ERROR:
        return 'データベースクエリエラーが発生しました。しばらく経ってからもう一度お試しください。';
      case ErrorCode.DATABASE_CONSTRAINT_ERROR:
        return '一意性制約エラーが発生しました。同じデータが既に存在します。';
      
      // 予約関連のエラー
      case ErrorCode.BOOKING_ERROR:
        return '予約処理中にエラーが発生しました。もう一度お試しください。';
      case ErrorCode.BOOKING_CONFLICT:
        return '選択した日程は既に予約されています。別の日程をお選びください。';
      case ErrorCode.BOOKING_INVALID_DATE:
        return '無効な日付が選択されています。日付を確認してください。';
      case ErrorCode.BOOKING_PAST_DATE:
        return '過去の日付は選択できません。未来の日付を選択してください。';
      case ErrorCode.BOOKING_TOO_LONG:
        return '予約期間が長すぎます。より短い期間を選択してください。';
      case ErrorCode.BOOKING_TOO_SOON:
        return 'ご予約は2日前までにお願いします。別の日程をお選びください。';
      
      // 商品関連のエラー
      case ErrorCode.PRODUCT_ERROR:
        return '商品処理中にエラーが発生しました。もう一度お試しください。';
      case ErrorCode.PRODUCT_NOT_FOUND:
        return '指定された商品が見つかりません。商品が削除されたか、URLが間違っている可能性があります。';
      case ErrorCode.PRODUCT_OUT_OF_STOCK:
        return '商品の在庫がありません。別の商品をお選びください。';
      case ErrorCode.PRODUCT_UNAVAILABLE:
        return 'この商品は現在ご利用いただけません。別の商品をお選びください。';
      
      // 注文関連のエラー
      case ErrorCode.ORDER_ERROR:
        return '注文処理中にエラーが発生しました。もう一度お試しください。';
      case ErrorCode.ORDER_NOT_FOUND:
        return '指定された注文が見つかりません。注文番号をご確認ください。';
      case ErrorCode.ORDER_ALREADY_PROCESSED:
        return 'この注文は既に処理されています。';
      case ErrorCode.ORDER_PAYMENT_FAILED:
        return '決済処理に失敗しました。別の支払い方法をお試しいただくか、カード情報をご確認ください。';
      
      default:
        return error?.message || 'エラーが発生しました。もう一度お試しください。';
    }
  }

  /**
   * エラーリスナーを追加
   */
  public addErrorListener(listener: (error: ErrorInfo) => void): void {
    this.errorListeners.push(listener);
  }

  /**
   * エラーリスナーを削除
   */
  public removeErrorListener(listener: (error: ErrorInfo) => void): void {
    const index = this.errorListeners.indexOf(listener);
    if (index !== -1) {
      this.errorListeners.splice(index, 1);
    }
  }

  /**
   * エラーリスナーに通知
   */
  private notifyErrorListeners(error: ErrorInfo): void {
    for (const listener of this.errorListeners) {
      try {
        listener(error);
      } catch (e) {
        console.error('エラーリスナーでエラーが発生しました:', e);
      }
    }
  }

  /**
   * エラー履歴に追加
   */
  private addToErrorHistory(error: ErrorInfo): void {
    this.errorHistory.unshift(error);
    
    // 履歴サイズを制限
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * エラー履歴を取得
   */
  public getErrorHistory(): ErrorInfo[] {
    return [...this.errorHistory];
  }

  /**
   * エラーをログに記録
   */
  private logError(error: ErrorInfo): void {
    const timestamp = error.timestamp.toISOString();
    const severity = error.severity;
    const code = error.code;
    const message = error.message;
    const details = error.details ? JSON.stringify(error.details) : '';
    const originalError = error.originalError?.stack || '';
    
    // コンソールにログ出力
    console.error(`[${timestamp}] [${severity}] [${code}] ${message}`);
    if (details) {
      console.error(`詳細: ${details}`);
    }
    if (originalError) {
      console.error(`元のエラー: ${originalError}`);
    }
    
    // TODO: 本番環境では外部ログサービスに送信するなどの処理を追加
  }
}
