// scripts/delete-all-bookings.js
// 全ての予約データを削除するスクリプト

import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function deleteAllBookings() {
  try {
    // すべての予約を削除
    const { count } = await prisma.booking.deleteMany({});

    console.log(`${count}件の予約が削除されました`);
  } catch (error) {
    console.error('予約削除エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
deleteAllBookings();
