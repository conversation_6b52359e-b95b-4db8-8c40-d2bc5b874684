import { extension } from '@shopify/ui-extensions/checkout';

// アプリブロックのエントリーポイント
export default extension('product.block.render', (root, { shop, i18n }) => {
  // 商品予約状況表示ブロックを作成
  const container = root.createComponent('BlockStack', { spacing: 'base' });

  // タイトル
  const title = root.createComponent('Heading', {}, '予約状況カレンダー');
  container.appendChild(title);

  // 説明文
  const description = root.createComponent('Text', {}, 'この商品の予約状況を確認できます。');
  container.appendChild(description);

  // 予約状況表示ボックス
  const box = root.createComponent('Box', {
    border: 'base',
    borderRadius: 'base',
    padding: 'base',
    background: 'surface-subdued'
  });

  // 予約状況リスト
  const bookingsList = root.createComponent('BlockStack', { spacing: 'tight' });

  // 予約状況タイトル
  const bookingsTitle = root.createComponent('Text', {}, '予約状況表示（サンプル）');
  bookingsList.appendChild(bookingsTitle);

  // 本予約サンプル
  const confirmedBooking = root.createComponent('Box', {
    padding: 'base',
    background: 'success-subdued',
    borderRadius: 'base'
  });
  confirmedBooking.appendChild(
    root.createComponent('Text', {}, '本予約: 2023/12/10 〜 2023/12/12')
  );
  bookingsList.appendChild(confirmedBooking);

  // 仮予約サンプル
  const provisionalBooking = root.createComponent('Box', {
    padding: 'base',
    background: 'warning-subdued',
    borderRadius: 'base'
  });
  provisionalBooking.appendChild(
    root.createComponent('Text', {}, '仮予約: 2023/12/20 〜 2023/12/22')
  );
  bookingsList.appendChild(provisionalBooking);

  // メンテナンスサンプル
  const maintenance = root.createComponent('Box', {
    padding: 'base',
    background: 'neutral-subdued',
    borderRadius: 'base'
  });
  maintenance.appendChild(
    root.createComponent('Text', {}, 'メンテナンス: 2023/12/25 〜 2023/12/27')
  );
  bookingsList.appendChild(maintenance);

  // ボックスに予約状況リストを追加
  box.appendChild(bookingsList);

  // コンテナにボックスを追加
  container.appendChild(box);

  // 注意書き
  const note = root.createComponent('Text', { size: 'small' }, '※ 予約状況は随時更新されます。最新の情報は予約時に確認してください。');
  container.appendChild(note);

  // 予約ボタン
  const buttonContainer = root.createComponent('InlineStack', { spacing: 'tight', alignment: 'center' });
  const button = root.createComponent('Button', {
    kind: 'primary',
    onPress: () => {
      console.log('予約ボタンがクリックされました');
    }
  }, 'この商品を予約する');
  buttonContainer.appendChild(button);
  container.appendChild(buttonContainer);

  // ルートに追加
  root.appendChild(container);
});
