/**
 * テスト結果レポート生成ユーティリティ
 * 
 * このモジュールは、テスト結果のレポートを生成するための関数を提供します。
 * Markdown形式とHTML形式のレポート生成をサポートします。
 */

import * as fs from 'fs';
import * as path from 'path';
import { TestResultSet, ValidationResult } from './test-result-validator';

// HTML テンプレート
const HTML_TEMPLATE = `<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>テスト結果レポート</title>
  <style>
    body {
      font-family: 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #2c3e50;
    }
    .summary {
      background-color: #f8f9fa;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .summary-item {
      margin: 5px 15px;
    }
    .test-list {
      margin-top: 20px;
    }
    .test-item {
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 15px;
      position: relative;
    }
    .test-name {
      font-weight: bold;
      margin-bottom: 10px;
      padding-right: 80px;
    }
    .test-status {
      position: absolute;
      top: 15px;
      right: 15px;
      padding: 3px 10px;
      border-radius: 3px;
      font-size: 14px;
      font-weight: bold;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    .failure {
      background-color: #f8d7da;
      color: #721c24;
    }
    .test-details {
      background-color: #f8f9fa;
      border-radius: 3px;
      padding: 10px;
      margin-top: 10px;
      font-family: monospace;
      white-space: pre-wrap;
      overflow-x: auto;
    }
    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 14px;
      color: #6c757d;
    }
    .validation-section {
      margin-top: 30px;
      border-top: 1px solid #ddd;
      padding-top: 20px;
    }
    .validation-item {
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 15px;
    }
    .validation-message {
      font-weight: bold;
      margin-bottom: 10px;
    }
    .validation-details {
      background-color: #f8f9fa;
      border-radius: 3px;
      padding: 10px;
      margin-top: 10px;
      font-family: monospace;
      white-space: pre-wrap;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>テスト結果レポート</h1>
  
  <div class="summary">
    <div class="summary-item">
      <strong>実行日時:</strong> {{timestamp}}
    </div>
    <div class="summary-item">
      <strong>実行時間:</strong> {{duration}}秒
    </div>
    <div class="summary-item">
      <strong>合計テスト数:</strong> {{totalTests}}
    </div>
    <div class="summary-item">
      <strong>成功:</strong> {{successCount}}
    </div>
    <div class="summary-item">
      <strong>失敗:</strong> {{failureCount}}
    </div>
  </div>
  
  <h2>テスト結果詳細</h2>
  <div class="test-list">
    {{testResults}}
  </div>
  
  {{validationSection}}
  
  <div class="footer">
    <p>レポート生成日時: {{generatedAt}}</p>
  </div>
</body>
</html>`;

// テスト項目のHTMLテンプレート
const TEST_ITEM_TEMPLATE = `<div class="test-item {{status}}">
  <div class="test-name">
    {{name}}
    <span class="test-status {{status}}">{{statusText}}</span>
  </div>
  {{details}}
</div>`;

// 検証セクションのHTMLテンプレート
const VALIDATION_SECTION_TEMPLATE = `<div class="validation-section">
  <h2>検証結果</h2>
  <div class="summary">
    <div class="summary-item">
      <strong>合計検証数:</strong> {{totalValidations}}
    </div>
    <div class="summary-item">
      <strong>成功:</strong> {{validCount}}
    </div>
    <div class="summary-item">
      <strong>失敗:</strong> {{invalidCount}}
    </div>
  </div>
  <div class="test-list">
    {{validationResults}}
  </div>
</div>`;

// 検証項目のHTMLテンプレート
const VALIDATION_ITEM_TEMPLATE = `<div class="validation-item {{status}}">
  <div class="validation-message">
    {{message}}
    <span class="test-status {{status}}">{{statusText}}</span>
  </div>
  {{details}}
</div>`;

/**
 * テスト結果をMarkdownレポートとして生成する関数
 * @param results テスト結果セット
 * @param outputPath 出力先のファイルパス
 * @param validationResults オプションの検証結果
 * @returns 生成されたファイルのパス
 */
export function generateMarkdownReport(
  results: TestResultSet,
  outputPath: string,
  validationResults?: ValidationResult[]
): string {
  try {
    let markdown = `# テスト結果レポート

## テスト概要

このレポートは、テスト実行の結果をまとめたものです。

テスト実行日時: ${new Date(results.timestamp).toLocaleString('ja-JP')}
実行時間: ${results.duration.toFixed(2)}秒

## テスト結果

- 合計テスト数: ${results.totalTests}
- 成功: ${results.successCount}
- 失敗: ${results.failureCount}
- 成功率: ${Math.round((results.successCount / results.totalTests) * 100)}%

## 詳細結果

`;

    // 各テストケースの詳細結果を追加
    results.tests.forEach((result, index) => {
      markdown += `### テストケース${index + 1}: ${result.testName}

- 結果: ${result.success ? '✅ 成功' : '❌ 失敗'}
${result.message ? `- メッセージ: ${result.message}` : ''}
${result.error ? `- エラー: ${result.error}` : ''}
`;

      if (result.details) {
        markdown += `- 詳細:\n\`\`\`json\n${JSON.stringify(result.details, null, 2)}\n\`\`\`\n\n`;
      }

      if (result.expected !== undefined && result.actual !== undefined) {
        markdown += `- 期待値: \`${JSON.stringify(result.expected)}\`\n`;
        markdown += `- 実際値: \`${JSON.stringify(result.actual)}\`\n\n`;
      }
    });

    // 検証結果がある場合は追加
    if (validationResults && validationResults.length > 0) {
      const validCount = validationResults.filter(r => r.valid).length;
      const invalidCount = validationResults.length - validCount;

      markdown += `## 検証結果

- 合計検証数: ${validationResults.length}
- 成功: ${validCount}
- 失敗: ${invalidCount}
- 成功率: ${Math.round((validCount / validationResults.length) * 100)}%

### 詳細検証結果

`;

      validationResults.forEach((result, index) => {
        markdown += `#### 検証${index + 1}: ${result.message}

- 結果: ${result.valid ? '✅ 成功' : '❌ 失敗'}
`;

        if (result.details) {
          markdown += `- 詳細:\n\`\`\`json\n${JSON.stringify(result.details, null, 2)}\n\`\`\`\n\n`;
        }
      });
    }

    // レポートファイルに書き込み
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(outputPath, markdown);
    
    return outputPath;
  } catch (error) {
    throw new Error(`Markdownレポートの生成に失敗しました: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * テスト結果をHTMLレポートとして生成する関数
 * @param results テスト結果セット
 * @param outputPath 出力先のファイルパス
 * @param validationResults オプションの検証結果
 * @returns 生成されたファイルのパス
 */
export function generateHtmlReport(
  results: TestResultSet,
  outputPath: string,
  validationResults?: ValidationResult[]
): string {
  try {
    // テスト結果のHTMLを生成
    let testResultsHtml = '';
    for (const test of results.tests) {
      const status = test.success ? 'success' : 'failure';
      const statusText = test.success ? '成功' : '失敗';

      let details = '';
      if (test.message) {
        details += `<div class="test-details">メッセージ: ${test.message}</div>`;
      }
      if (test.error) {
        details += `<div class="test-details">エラー: ${test.error}</div>`;
      }
      if (test.details) {
        details += `<div class="test-details">詳細: <pre>${JSON.stringify(test.details, null, 2)}</pre></div>`;
      }
      if (test.expected !== undefined && test.actual !== undefined) {
        details += `<div class="test-details">期待値: ${JSON.stringify(test.expected)}</div>`;
        details += `<div class="test-details">実際値: ${JSON.stringify(test.actual)}</div>`;
      }

      testResultsHtml += TEST_ITEM_TEMPLATE
        .replace(/{{status}}/g, status)
        .replace(/{{statusText}}/g, statusText)
        .replace(/{{name}}/g, test.testName)
        .replace(/{{details}}/g, details);
    }

    // 検証結果のHTMLを生成
    let validationSectionHtml = '';
    if (validationResults && validationResults.length > 0) {
      const validCount = validationResults.filter(r => r.valid).length;
      const invalidCount = validationResults.length - validCount;

      let validationResultsHtml = '';
      for (const validation of validationResults) {
        const status = validation.valid ? 'success' : 'failure';
        const statusText = validation.valid ? '成功' : '失敗';

        let details = '';
        if (validation.details) {
          details = `<div class="validation-details"><pre>${JSON.stringify(validation.details, null, 2)}</pre></div>`;
        }

        validationResultsHtml += VALIDATION_ITEM_TEMPLATE
          .replace(/{{status}}/g, status)
          .replace(/{{statusText}}/g, statusText)
          .replace(/{{message}}/g, validation.message)
          .replace(/{{details}}/g, details);
      }

      validationSectionHtml = VALIDATION_SECTION_TEMPLATE
        .replace(/{{totalValidations}}/g, validationResults.length.toString())
        .replace(/{{validCount}}/g, validCount.toString())
        .replace(/{{invalidCount}}/g, invalidCount.toString())
        .replace(/{{validationResults}}/g, validationResultsHtml);
    }

    // HTMLテンプレートに値を埋め込む
    const html = HTML_TEMPLATE
      .replace(/{{timestamp}}/g, new Date(results.timestamp).toLocaleString())
      .replace(/{{duration}}/g, results.duration.toFixed(2))
      .replace(/{{totalTests}}/g, results.totalTests.toString())
      .replace(/{{successCount}}/g, results.successCount.toString())
      .replace(/{{failureCount}}/g, results.failureCount.toString())
      .replace(/{{testResults}}/g, testResultsHtml)
      .replace(/{{validationSection}}/g, validationSectionHtml)
      .replace(/{{generatedAt}}/g, new Date().toLocaleString());

    // HTMLファイルを保存
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(outputPath, html);

    return outputPath;
  } catch (error) {
    throw new Error(`HTMLレポートの生成に失敗しました: ${error instanceof Error ? error.message : String(error)}`);
  }
}
