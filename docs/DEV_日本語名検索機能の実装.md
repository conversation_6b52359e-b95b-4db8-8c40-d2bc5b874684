# 日本語名検索機能の実装

## 概要

日本語の顧客名検索において、スペースの有無や姓名の順序に関わらず検索できるように、共通の検索ユーティリティ関数を実装しました。

例えば、以下のパターンで同じ検索結果が得られるようになります：
- 「鈴木一郎」（スペースなし）
- 「鈴木 一郎」（スペースあり）
- 「一郎 鈴木」（姓名の順序が逆）
- 「一郎鈴木」（姓名の順序が逆、スペースなし）

## 実装内容

### 1. 共通ユーティリティ関数の作成

`app/utils/search-utils.ts` に以下の共通関数を実装しました：

#### 1.1 `buildSearchCondition` 関数

データベース検索用の条件を生成する関数です。

```typescript
export function buildSearchCondition(searchQuery: string, fields: string[]): any {
  // 検索クエリが空の場合は空のオブジェクトを返す
  if (!searchQuery || searchQuery.trim() === '') {
    return {};
  }

  // 検索クエリをトリミングして小文字に変換
  const trimmedQuery = searchQuery.trim().toLowerCase();

  // スペースを削除したクエリも作成（「佐藤 花子」→「佐藤花子」のような検索に対応）
  const noSpaceQuery = trimmedQuery.replace(/\s+/g, '');

  // 名前の部分を抽出（日本語名対応）
  const nameParts = trimmedQuery.match(/[\p{L}\p{N}]+/gu) || [];

  // 日本語名かどうかを判定
  const isJapaneseName = /[\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Han}]/u.test(trimmedQuery);
  const hasMultipleParts = nameParts.length > 1;

  // 検索条件を構築
  const searchConditions: any[] = [];

  // 基本的な検索条件（各フィールドに対して検索）
  for (const field of fields) {
    searchConditions.push({
      [field]: { contains: trimmedQuery, mode: 'insensitive' }
    });
  }

  // 日本語名または複数単語の検索の場合、追加の検索条件を生成
  if ((isJapaneseName || hasMultipleParts) && fields.some(f => f.toLowerCase().includes('name'))) {
    // 名前フィールドが含まれている場合のみ適用

    // 名前フィールドを特定
    const nameFields = fields.filter(f => f.toLowerCase().includes('name'));

    // 各単語で検索
    for (const part of nameParts) {
      if (part.length > 1) {
        for (const field of nameFields) {
          searchConditions.push({
            [field]: { contains: part, mode: 'insensitive' }
          });
        }
      }
    }

    // スペースなしクエリでも検索
    if (noSpaceQuery.length > 2 && noSpaceQuery !== trimmedQuery) {
      for (const field of nameFields) {
        searchConditions.push({
          [field]: { contains: noSpaceQuery, mode: 'insensitive' }
        });
      }
    }
  }

  return { OR: searchConditions };
}
```

#### 1.2 `filterItemsByName` 関数

アプリケーション側でのフィルタリングを行う関数です。

```typescript
export function filterItemsByName<T>(
  items: T[],
  searchQuery: string,
  nameField: keyof T,
  otherFields: (keyof T)[] = []
): T[] {
  if (!searchQuery || searchQuery.trim() === '') {
    return items;
  }

  const trimmedQuery = searchQuery.trim().toLowerCase();
  const noSpaceQuery = trimmedQuery.replace(/\s+/g, '');

  // 名前の部分を抽出（日本語名対応）
  const nameParts = trimmedQuery.match(/[\p{L}\p{N}]+/gu) || [];

  // 姓名の順序を入れ替えたバリエーションも生成
  let nameVariations: string[] = [];
  if (nameParts.length === 2) {
    // 「佐藤 花子」→「花子 佐藤」のようなバリエーションを追加
    nameVariations.push(`${nameParts[1]} ${nameParts[0]}`);
    nameVariations.push(`${nameParts[1]}${nameParts[0]}`); // スペースなし
  }

  // 日本語名かどうかを判定
  const isJapaneseName = /[\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Han}]/u.test(trimmedQuery);

  return items.filter(item => {
    // 基本的な検索条件に一致する場合は含める
    const nameValue = String(item[nameField] || '').toLowerCase();
    
    if (nameValue.includes(trimmedQuery)) {
      return true;
    }
    
    // その他のフィールドでも検索
    for (const field of otherFields) {
      const fieldValue = String(item[field] || '').toLowerCase();
      if (fieldValue.includes(trimmedQuery)) {
        return true;
      }
    }

    // 名前フィールドが空の場合はスキップ
    if (!nameValue) {
      return false;
    }

    const nameNoSpace = nameValue.replace(/\s+/g, '');

    // 1. スペースなしクエリが名前（スペースなし）に含まれているか確認
    const matchesNoSpace = noSpaceQuery.length > 2 && nameNoSpace.includes(noSpaceQuery);

    // 2. 名前の各部分がすべて名前に含まれているか確認
    const matchesAllParts = nameParts.length > 1 &&
      nameParts.every(part => part.length > 1 && nameValue.includes(part.toLowerCase()));

    // 3. 姓名の順序を入れ替えたバリエーションが名前に含まれているか確認
    const matchesVariation = nameVariations.some(variation => {
      const variationLower = variation.toLowerCase();
      return nameValue.includes(variationLower) ||
             nameNoSpace.includes(variationLower.replace(/\s+/g, ''));
    });

    // 4. 文字単位の比較（より柔軟な一致）
    let matchesCharByChar = false;
    if (noSpaceQuery.length > 2) {
      let remainingName = nameNoSpace;
      let allCharsFound = true;

      for (const char of noSpaceQuery) {
        const charIndex = remainingName.indexOf(char);
        if (charIndex === -1) {
          allCharsFound = false;
          break;
        }
        remainingName = remainingName.substring(charIndex + 1);
      }

      matchesCharByChar = allCharsFound;
    }

    // 5. 日本語名の場合、追加の検索パターンを試す
    let matchesJapaneseNamePatterns = false;
    if (isJapaneseName && noSpaceQuery.length >= 3) {
      // 可能な分割パターンを試す（2文字姓+残り、3文字姓+残り、など）
      for (let i = 1; i < noSpaceQuery.length; i++) {
        const lastName = noSpaceQuery.substring(0, i);
        const firstName = noSpaceQuery.substring(i);
        
        // 姓名の間にスペースを入れたパターン
        const nameWithSpace = `${lastName} ${firstName}`;
        const reversedName = `${firstName} ${lastName}`;
        
        if (nameValue.includes(nameWithSpace) || nameValue.includes(reversedName)) {
          matchesJapaneseNamePatterns = true;
          break;
        }
        
        // スペースなしでも確認
        if (nameNoSpace.includes(lastName + firstName) || 
            nameNoSpace.includes(firstName + lastName)) {
          matchesJapaneseNamePatterns = true;
          break;
        }
      }
    }

    // いずれかの条件に一致する場合はマッチとみなす
    return matchesNoSpace || matchesAllParts || matchesVariation || 
           matchesCharByChar || matchesJapaneseNamePatterns;
  });
}
```

### 2. 各ページでの使用方法

#### 2.1 予約一覧ページ（app/routes/app.bookings._index.tsx）

```typescript
// インポート
import { buildSearchCondition, filterItemsByName } from "~/utils/search-utils";

// データベース検索条件の生成
if (searchQuery) {
  const searchFields = ['customerName', 'customerEmail', 'customerId', 'bookingId'];
  const searchCondition = buildSearchCondition(searchQuery, searchFields);
  
  // 商品検索条件を追加
  if (searchCondition.OR && searchCondition.OR.length > 0) {
    const trimmedQuery = searchQuery.trim().toLowerCase();
    const productSearchCondition = {
      product: {
        OR: [
          { title: { contains: trimmedQuery, mode: 'insensitive' } },
          { sku: { contains: trimmedQuery, mode: 'insensitive' } }
        ]
      }
    };
    
    searchCondition.OR.push(productSearchCondition);
  }
  
  // 最終的な検索条件を設定
  whereCondition = { ...whereCondition, ...searchCondition };
}

// アプリケーション側のフィルタリング
if (searchQuery) {
  filteredBookings = filterItemsByName(
    bookings,
    searchQuery,
    'customerName',
    ['customerEmail', 'customerId', 'bookingId']
  );
}
```

#### 2.2 顧客一覧ページ（app/routes/app.customers._index.tsx）

```typescript
// インポート
import { buildSearchCondition } from "~/utils/search-utils";

// データベース検索条件の生成
if (searchQuery) {
  const searchFields = ['name', 'email', 'phone'];
  const searchCondition = buildSearchCondition(searchQuery, searchFields);
  
  // 最終的な検索条件を設定
  whereCondition = { ...whereCondition, ...searchCondition };
}
```

#### 2.3 注文一覧ページ（app/routes/app.orders._index.tsx）

```typescript
// インポート
import { buildSearchCondition } from "~/utils/search-utils";

// データベース検索条件の生成
if (searchQuery) {
  const searchFields = ['customerName', 'customerEmail', 'orderNumber'];
  const searchCondition = buildSearchCondition(searchQuery, searchFields);
  
  // 最終的な検索条件を設定
  whereCondition = { ...whereCondition, ...searchCondition };
}
```

## 検索アルゴリズムの説明

日本語名検索では、以下の5つの方法を組み合わせて検索を行います：

1. **スペースなし検索**: 「佐藤 花子」→「佐藤花子」のように、スペースを削除して検索
2. **部分一致検索**: 「佐藤」と「花子」の両方が含まれているかを確認
3. **姓名順序入れ替え検索**: 「佐藤 花子」→「花子 佐藤」のように、姓名の順序を入れ替えて検索
4. **文字単位検索**: 「佐藤花子」の各文字が順番に含まれているかを確認
5. **日本語名パターン検索**: 「佐藤花子」を「佐藤」と「花子」、「佐」と「藤花子」など、様々な分割パターンで検索

これらの方法を組み合わせることで、スペースの有無や姓名の順序に関わらず検索できるようになります。

## 今後の改善点

1. **パフォーマンスの最適化**: 大量のデータがある場合、検索のパフォーマンスを最適化する
2. **検索精度の向上**: より高度な自然言語処理技術を導入して検索精度を向上させる
3. **テストの追加**: 様々なパターンでのテストを追加して機能の正確性を確保する
