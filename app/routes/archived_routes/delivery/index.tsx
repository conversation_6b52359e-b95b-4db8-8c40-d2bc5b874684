import React, { useState } from 'react';
import { LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate } from '@remix-run/react';
import { authenticate } from '../shopify.server';
import { 
  Page, 
  LegacyCard, 
  ResourceList, 
  ResourceItem, 
  Filters, 
  Badge, 
  EmptyState, 
  Text,
  Pagination
} from '@shopify/polaris';
import { prisma } from '../db.server';

// 配送ステータスに応じたバッジ設定
const deliveryStatusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  SCHEDULED: { tone: 'info', label: '予定' },
  IN_TRANSIT: { tone: 'attention', label: '配送中' },
  DELIVERED: { tone: 'success', label: '配送完了' },
  RETURNED: { tone: 'success', label: '返却完了' },
  DELAYED: { tone: 'warning', label: '遅延' },
  CANCELLED: { tone: 'critical', label: 'キャンセル' }
};

// 配送タイプに応じたラベル
const deliveryTypeLabels: Record<string, string> = {
  DELIVERY: '配送',
  PICKUP: '集荷',
  RETURN: '返却'
};

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);
  
  // クエリパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  const limit = parseInt(url.searchParams.get('limit') || '50', 10);
  const status = url.searchParams.get('status') || undefined;
  const type = url.searchParams.get('type') || undefined;
  const query = url.searchParams.get('query') || undefined;
  const dateFrom = url.searchParams.get('dateFrom') || undefined;
  const dateTo = url.searchParams.get('dateTo') || undefined;
  
  // 検索条件を構築
  const where: any = {};
  
  if (status) {
    where.returnStatus = status;
  }
  
  if (type) {
    where.deliveryType = type;
  }
  
  if (dateFrom) {
    where.deliveryDate = { gte: new Date(dateFrom) };
  }
  
  if (dateTo) {
    if (where.deliveryDate) {
      where.deliveryDate.lte = new Date(dateTo);
    } else {
      where.deliveryDate = { lte: new Date(dateTo) };
    }
  }
  
  if (query) {
    where.OR = [
      { notes: { contains: query, mode: 'insensitive' } },
      { address: { contains: query, mode: 'insensitive' } },
      { 
        booking: { 
          OR: [
            { customerName: { contains: query, mode: 'insensitive' } },
            { customerEmail: { contains: query, mode: 'insensitive' } }
          ]
        }
      },
      {
        staff: {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { code: { contains: query } }
          ]
        }
      }
    ];
  }
  
  // 配送データを取得
  const deliverySchedules = await prisma.deliverySchedule.findMany({
    where,
    include: {
      booking: {
        include: {
          product: true
        }
      },
      staff: true,
      carrier: true
    },
    orderBy: [
      { deliveryDate: 'asc' }
    ],
    skip: (page - 1) * limit,
    take: limit
  });
  
  // 総件数を取得
  const total = await prisma.deliverySchedule.count({ where });
  
  return json({
    deliverySchedules: deliverySchedules.map(schedule => ({
      id: schedule.id,
      bookingId: schedule.bookingId,
      customerName: schedule.booking.customerName || '',
      customerEmail: schedule.booking.customerEmail || '',
      productTitle: schedule.booking.product.title,
      productSku: schedule.booking.product.sku,
      deliveryDate: schedule.deliveryDate.toISOString(),
      deliveryType: schedule.deliveryType,
      timeFrom: schedule.timeFrom,
      timeTo: schedule.timeTo,
      destination: schedule.destination || '',
      address: schedule.address || '',
      staffName: schedule.staff?.name || '',
      carrierName: schedule.carrier?.name || '',
      returnStatus: schedule.returnStatus || '',
      actualReturnDate: schedule.actualReturnDate?.toISOString() || null,
      notes: schedule.notes || '',
      pickingStatus: schedule.pickingStatus || '',
      pickingLocation: schedule.pickingLocation || '',
      pickingStaffName: schedule.staff?.name || ''
    })),
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}

export default function DeliveryIndex() {
  const { deliverySchedules, pagination } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  
  // 配送詳細ページへ移動
  const handleDeliveryClick = (deliveryId: string) => {
    navigate(`/delivery/${deliveryId}`);
  };
  
  // ステータスフィルターを適用
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    const params = new URLSearchParams(window.location.search);
    
    if (value) {
      params.set('status', value);
    } else {
      params.delete('status');
    }
    
    navigate(`/delivery?${params.toString()}`);
  };
  
  // タイプフィルターを適用
  const handleTypeFilterChange = (value: string) => {
    setTypeFilter(value);
    const params = new URLSearchParams(window.location.search);
    
    if (value) {
      params.set('type', value);
    } else {
      params.delete('type');
    }
    
    navigate(`/delivery?${params.toString()}`);
  };
  
  // 検索クエリを適用
  const handleSearchSubmit = () => {
    const params = new URLSearchParams(window.location.search);
    
    if (searchQuery) {
      params.set('query', searchQuery);
    } else {
      params.delete('query');
    }
    
    navigate(`/delivery?${params.toString()}`);
  };
  
  // ページネーション処理
  const handlePaginationChange = (newPage: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set('page', newPage.toString());
    navigate(`/delivery?${params.toString()}`);
  };
  
  // 日付をフォーマット
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };
  
  // フィルターオプションを生成
  const filters = [
    {
      key: 'status',
      label: 'ステータス',
      filter: (
        <select
          value={statusFilter}
          onChange={(e) => handleStatusFilterChange(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="">すべて</option>
          <option value="SCHEDULED">予定</option>
          <option value="IN_TRANSIT">配送中</option>
          <option value="DELIVERED">配送完了</option>
          <option value="RETURNED">返却完了</option>
          <option value="DELAYED">遅延</option>
          <option value="CANCELLED">キャンセル</option>
        </select>
      ),
      shortcut: true,
    },
    {
      key: 'type',
      label: '配送タイプ',
      filter: (
        <select
          value={typeFilter}
          onChange={(e) => handleTypeFilterChange(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="">すべて</option>
          <option value="DELIVERY">配送</option>
          <option value="PICKUP">集荷</option>
          <option value="RETURN">返却</option>
        </select>
      ),
      shortcut: true,
    }
  ];
  
  return (
    <Page
      title="配送管理"
      primaryAction={{
        content: "新規配送",
        url: "/delivery/new"
      }}
    >
      <LegacyCard>
        <ResourceList
          resourceName={{ singular: '配送', plural: '配送' }}
          items={deliverySchedules}
          renderItem={(delivery) => {
            const { 
              id, 
              customerName, 
              productTitle, 
              deliveryDate, 
              deliveryType, 
              timeFrom, 
              timeTo, 
              destination, 
              address, 
              staffName, 
              carrierName, 
              returnStatus, 
              actualReturnDate
            } = delivery;
            
            const status = returnStatus || 'SCHEDULED';
            const badge = deliveryStatusBadgeMap[status] || { tone: 'new', label: status };
            const typeLabel = deliveryTypeLabels[deliveryType] || deliveryType;
            
            const shortcutActions = [
              {
                content: '詳細',
                url: `/delivery/${id}`,
              },
              {
                content: '編集',
                url: `/delivery/edit/${id}`,
              }
            ];
            
            if (status === 'SCHEDULED' || status === 'IN_TRANSIT') {
              shortcutActions.push({
                content: '完了',
                url: `/delivery/complete/${id}`,
              });
            }
            
            return (
              <ResourceItem
                id={id}
                onClick={() => handleDeliveryClick(id)}
                shortcutActions={shortcutActions}
                persistActions
              >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <Text as="h3" variant="bodyMd" fontWeight="bold">
                      {customerName || '顧客名なし'}
                    </Text>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodyMd">{productTitle}</Text>
                    </div>
                    <div style={{ marginTop: '4px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        {typeLabel}: {formatDate(deliveryDate)} {timeFrom}〜{timeTo}
                      </Text>
                    </div>
                    {destination && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          配送先: {destination}
                        </Text>
                      </div>
                    )}
                    {address && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          住所: {address}
                        </Text>
                      </div>
                    )}
                    {staffName && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          担当者: {staffName}
                        </Text>
                      </div>
                    )}
                    {carrierName && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          配送業者: {carrierName}
                        </Text>
                      </div>
                    )}
                    {actualReturnDate && (
                      <div style={{ marginTop: '4px' }}>
                        <Text as="p" variant="bodySm" tone="subdued">
                          実際の返却日: {formatDate(actualReturnDate)}
                        </Text>
                      </div>
                    )}
                  </div>
                  <div>
                    <Badge tone={badge.tone}>{badge.label}</Badge>
                  </div>
                </div>
              </ResourceItem>
            );
          }}
          filterControl={
            <Filters
              queryValue={searchQuery}
              filters={filters}
              onQueryChange={setSearchQuery}
              onQueryClear={() => setSearchQuery('')}
              onClearAll={() => {
                setSearchQuery('');
                setStatusFilter('');
                setTypeFilter('');
                navigate('/delivery');
              }}
              onQueryBlur={handleSearchSubmit}
              hideQueryField={false}
            />
          }
          emptyState={
            <EmptyState
              heading="配送情報がありません"
              image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
            >
              <p>配送情報を登録して、商品の配送状況を管理しましょう。</p>
            </EmptyState>
          }
        />
        {pagination.totalPages > 1 && (
          <div style={{ padding: '16px', display: 'flex', justifyContent: 'center' }}>
            <Pagination
              hasPrevious={pagination.page > 1}
              onPrevious={() => handlePaginationChange(pagination.page - 1)}
              hasNext={pagination.page < pagination.totalPages}
              onNext={() => handlePaginationChange(pagination.page + 1)}
            />
          </div>
        )}
      </LegacyCard>
      <div style={{ marginTop: '16px', textAlign: 'right' }}>
        <Text as="p" variant="bodySm" tone="subdued">
          合計 {pagination.total} 件の配送 ( {pagination.page} / {pagination.totalPages} ページ )
        </Text>
      </div>
    </Page>
  );
}
