import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function addTestOrderData() {
  try {
    console.log("=== テスト用注文データを追加 ===\n");

    // 既存の予約データを取得
    const bookings = await prisma.booking.findMany({
      orderBy: {
        createdAt: 'asc'
      }
    });

    if (bookings.length === 0) {
      console.log("予約データが見つかりません。");
      return;
    }

    console.log(`${bookings.length}件の予約に注文情報を追加します...\n`);

    // 各予約に注文情報を追加
    for (let i = 0; i < bookings.length; i++) {
      const booking = bookings[i];
      const orderNumber = 1001 + i;
      const shopifyOrderId = `gid://shopify/Order/${5000000000000 + i}`;
      const shopifyOrderName = `#${orderNumber}`;

      await prisma.booking.update({
        where: { id: booking.id },
        data: {
          shopifyOrderId,
          shopifyOrderName,
          orderId: shopifyOrderId,
          orderName: shopifyOrderName
        }
      });

      console.log(`予約ID: ${booking.bookingId} → 注文番号: ${shopifyOrderName}`);
    }

    console.log("\n=== 注文データ追加完了 ===");

    // 確認
    const updatedBookings = await prisma.booking.findMany({
      select: {
        bookingId: true,
        customerName: true,
        shopifyOrderId: true,
        shopifyOrderName: true
      }
    });

    console.log("\n=== 更新後の注文情報 ===");
    for (const booking of updatedBookings) {
      console.log(`予約ID: ${booking.bookingId}`);
      console.log(`  顧客: ${booking.customerName}`);
      console.log(`  注文ID: ${booking.shopifyOrderId}`);
      console.log(`  注文番号: ${booking.shopifyOrderName}`);
      console.log('---');
    }

  } catch (error) {
    console.error("エラーが発生しました:", error);
  } finally {
    await prisma.$disconnect();
  }
}

addTestOrderData();
