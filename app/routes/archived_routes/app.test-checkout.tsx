/**
 * テスト用決済フォーム
 *
 * このページは開発・テスト用の簡易決済フォームを提供します。
 * ドラフトオーダーの作成、完了、注文の確認の機能を含みます。
 *
 * 主な機能:
 * - 商品選択と日程指定によるドラフトオーダー作成
 * - 予約データの管理（作成・編集・削除）
 * - 在庫管理のテスト（同じ日程での重複予約防止）
 */

import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, useSubmit, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  BlockStack,
  Text,
  Select,
  TextField,
  Button,
  Banner,
  InlineStack,
  Box,
  Divider,
  RadioButton,
  Spinner,
  DataTable,
  Link,
  Modal,
  ButtonGroup
} from "@shopify/polaris";
import { useState, useCallback, useEffect } from "react";
import { v4 as uuidv4 } from 'uuid';
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { BOOKING_STATUS, BOOKING_TYPE, CUSTOM_ATTRIBUTES, ORDER_NOTE_FORMAT } from "~/constants/booking";
import { calculateRentalDays, checkBookingOverlap, validateRentalDates } from "../utils/booking/booking-validation";
import { calculateRentalPrice } from "../utils/booking/price-calculation";

/**
 * ローダー関数
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // 商品データを取得
    const dbProducts = await prisma.product.findMany({
      where: { shop },
      select: {
        id: true,
        title: true,
        sku: true,
        status: true,
        price: true,
        shopifyId: true
      },
      orderBy: { title: 'asc' },
    });

    // GraphQLで商品情報とバリアントを取得
    const productsWithVariants = await Promise.all(dbProducts.map(async (product) => {
      try {
        // GraphQLで商品のバリアント情報を取得
        const productResponse = await admin.graphql(`
          query getProduct($id: ID!) {
            product(id: $id) {
              id
              title
              variants(first: 5) {
                edges {
                  node {
                    id
                    title
                    sku
                    price
                  }
                }
              }
            }
          }
        `, {
          variables: {
            id: `gid://shopify/Product/${product.shopifyId}`
          }
        });

        const productData = await productResponse.json();

        if (productData.data?.product?.variants?.edges?.length > 0) {
          // 実際のバリアントデータを使用
          return {
            ...product,
            variants: productData.data.product.variants.edges.map(edge => ({
              id: edge.node.id, // 完全なGID形式を保持
              title: edge.node.title,
              sku: edge.node.sku || product.sku,
              price: edge.node.price
            }))
          };
        }
      } catch (error) {
        console.error(`商品ID ${product.id} のバリアント取得エラー:`, error);
      }

      // バリアント取得に失敗した場合は、実際のShopify商品を検索して取得
      try {
        // 商品名で検索
        const searchResponse = await admin.graphql(`
          query searchProducts($query: String!) {
            products(first: 1, query: $query) {
              edges {
                node {
                  id
                  title
                  variants(first: 1) {
                    edges {
                      node {
                        id
                        title
                        sku
                        price
                      }
                    }
                  }
                }
              }
            }
          }
        `, {
          variables: {
            query: product.title
          }
        });

        const searchData = await searchResponse.json();

        if (searchData.data?.products?.edges?.length > 0 &&
            searchData.data.products.edges[0].node.variants.edges.length > 0) {
          const foundProduct = searchData.data.products.edges[0].node;
          const foundVariant = foundProduct.variants.edges[0].node;

          console.log(`商品「${product.title}」の実際のバリアントを検索で発見:`, foundVariant.id);

          return {
            ...product,
            variants: [
              {
                id: foundVariant.id, // 実際のShopifyバリアントID
                title: foundVariant.title,
                sku: foundVariant.sku || product.sku,
                price: foundVariant.price
              }
            ]
          };
        }
      } catch (searchError) {
        console.error(`商品「${product.title}」の検索エラー:`, searchError);
      }

      // 検索でも見つからない場合は、すべての商品を取得して最初の有効なバリアントを使用
      console.log(`商品「${product.title}」のバリアントが見つからないため、ストア内の有効な商品を検索します`);

      try {
        // ストア内のすべての商品を取得（最大5件）
        const allProductsResponse = await admin.graphql(`
          query getAllProducts {
            products(first: 5) {
              edges {
                node {
                  id
                  title
                  variants(first: 1) {
                    edges {
                      node {
                        id
                        title
                        sku
                        price
                      }
                    }
                  }
                }
              }
            }
          }
        `);

        const allProductsData = await allProductsResponse.json();

        // 有効なバリアントを持つ商品を探す
        if (allProductsData.data?.products?.edges) {
          for (const edge of allProductsData.data.products.edges) {
            if (edge.node.variants.edges.length > 0) {
              const validProduct = edge.node;
              const validVariant = validProduct.variants.edges[0].node;

              console.log(`有効な商品を見つけました: ${validProduct.title} - ${validVariant.title} (${validVariant.id})`);

              return {
                ...product,
                variants: [
                  {
                    id: validVariant.id,
                    title: `${validProduct.title} - ${validVariant.title}`,
                    sku: validVariant.sku || product.sku,
                    price: validVariant.price
                  }
                ]
              };
            }
          }
        }
      } catch (allProductsError) {
        console.error("ストア内の商品取得エラー:", allProductsError);
      }

      // すべての方法が失敗した場合は、ダミーデータを使用
      console.log(`商品「${product.title}」のバリアントが見つからず、ダミーデータを使用します`);
      return {
        ...product,
        variants: [
          {
            id: "gid://shopify/ProductVariant/1", // 無効なIDを使用して明示的にエラーを発生させる
            title: "バリアントが見つかりません",
            sku: product.sku,
            price: product.price?.toString() || "1000"
          }
        ]
      };
    }));

    const products = productsWithVariants;

    // 予約データを取得（最新10件）- 有効な予約のみを表示
    const bookings = await prisma.booking.findMany({
      where: {
        shop,
        status: { in: ['PROVISIONAL', 'CONFIRMED'] }
      },
      select: {
        id: true,
        bookingId: true,
        productId: true,
        orderId: true,
        orderName: true,
        startDate: true,
        endDate: true,
        status: true,
        bookingType: true,
        customerEmail: true,
        customerName: true,
        product: {
          select: {
            id: true,
            title: true,
            sku: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
    });

    // Date型を文字列に変換
    const serializedBookings = bookings.map(b => ({
      ...b,
      startDate: b.startDate.toISOString(),
      endDate: b.endDate.toISOString(),
    }));

    return json({
      products,
      bookings: serializedBookings,
      shop,
    });
  } catch (error) {
    console.error("データ取得エラー:", error);
    return json({
      products: [],
      bookings: [],
      shop: session.shop,
      error: "データの取得中にエラーが発生しました",
    });
  }
}

/**
 * アクション関数
 */
export async function action({ request }: ActionFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    const formData = await request.formData();
    const intent = formData.get("intent") as string;

    if (intent === "create-draft-order") {
      const productId = formData.get("productId") as string;
      const variantId = formData.get("variantId") as string;
      const startDate = formData.get("startDate") as string;
      const endDate = formData.get("endDate") as string;
      const customerEmail = formData.get("customerEmail") as string;
      const customerName = formData.get("customerName") as string;
      const totalPrice = formData.get("totalPrice") as string;

      // 必須項目の検証
      if (!productId || !variantId || !startDate || !endDate || !customerEmail) {
        return json({
          success: false,
          error: "必須項目が入力されていません",
        });
      }

      // 商品情報の取得
      const product = await prisma.product.findFirst({
        where: { id: productId },
        select: { title: true },
      });

      if (!product) {
        console.error(`商品ID ${productId} がデータベースに存在しません`);
        return json({
          success: false,
          error: "商品情報の取得に失敗しました",
          details: "指定された商品IDがデータベースに存在しません"
        });
      }

      // 在庫管理のテスト - 予約日程の重複チェック
      // 統一された予約重複チェック関数を使用
      const hasOverlap = await checkBookingOverlap(
        prisma,
        shop,
        productId,
        variantId,
        startDate,
        endDate
      );

      if (hasOverlap) {
        return json({
          success: false,
          error: "選択された日程はすでに予約されています",
          details: "同じ商品・バリアントで同じ日程の予約が既に存在します"
        });
      }

      // 日程の妥当性チェック
      const dateValidation = validateRentalDates(new Date(startDate), new Date(endDate));
      if (!dateValidation.valid) {
        return json({
          success: false,
          error: dateValidation.message || "無効な日程が指定されています",
        });
      }

      // 予約ID生成
      const generatedBookingId = `BOOK-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

      // レンタル日数の計算
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);
      const rentalDays = Math.round((endDateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24)) + 1;

      // ドラフトオーダー作成
      // バリアントIDがすでにGID形式かどうかを確認
      const variantGid = variantId.startsWith('gid://shopify/ProductVariant/')
        ? variantId
        : `gid://shopify/ProductVariant/${variantId}`;

      console.log("使用するバリアントID:", variantGid);

      // 料金計算
      const calculatedPrice = totalPrice || "10000"; // デフォルト値を設定

      const draftOrderResponse = await admin.graphql(`
        mutation draftOrderCreate($input: DraftOrderInput!) {
          draftOrderCreate(input: $input) {
            draftOrder {
              id
              name
              totalPrice
              subtotalPrice
              customer {
                id
                email
              }
              lineItems(first: 5) {
                edges {
                  node {
                    title
                    quantity
                    originalUnitPrice
                    customAttributes {
                      key
                      value
                    }
                  }
                }
              }
              customAttributes {
                key
                value
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `, {
        variables: {
          input: {
            lineItems: [
              {
                variantId: variantGid,
                quantity: 1,
                originalUnitPrice: calculatedPrice, // 明示的に金額を設定
                customAttributes: [
                  { key: "レンタル開始日", value: startDate },
                  { key: "レンタル終了日", value: endDate },
                  { key: "レンタル日数", value: rentalDays.toString() }
                ]
              }
            ],
            customAttributes: [
              { key: "レンタルタイプ", value: "通常レンタル" },
              { key: "予約ID", value: generatedBookingId }
            ],
            email: customerEmail,
            metafields: [
              {
                namespace: "custom",
                key: "note",
                value: `レンタル予約: ${startDate}～${endDate}`,
                type: "single_line_text_field"
              }
            ],
            shippingLine: {
              title: "レンタル配送",
              price: "0.00"
            }
          }
        }
      });

      const draftOrderData = await draftOrderResponse.json();

      if (draftOrderData.errors || draftOrderData.data.draftOrderCreate.userErrors.length > 0) {
        console.error("ドラフトオーダー作成エラー:", draftOrderData.errors || draftOrderData.data.draftOrderCreate.userErrors);
        return json({
          success: false,
          error: "ドラフトオーダーの作成に失敗しました",
          details: draftOrderData.errors || draftOrderData.data.draftOrderCreate.userErrors
        });
      }

      const draftOrder = draftOrderData.data.draftOrderCreate.draftOrder;
      const draftOrderId = draftOrder.id.replace('gid://shopify/DraftOrder/', '');

      // 予約データをデータベースに保存
      const booking = await prisma.booking.create({
        data: {
          id: uuidv4(),
          bookingId: generatedBookingId,
          shop,
          productId,
          variantId,
          orderId: draftOrderId,
          status: 'PROVISIONAL',
          bookingType: 'PROVISIONAL',
          startDate: new Date(startDate),
          endDate: new Date(endDate),
          customerEmail,
          customerName,
          totalAmount: parseFloat(totalPrice || draftOrder.totalPrice),
          createdAt: new Date(),
          updatedAt: new Date(),
          transactionId: uuidv4(),
          priority: 1
        }
      });

      return json({
        success: true,
        draftOrder: {
          id: draftOrderId,
          name: draftOrder.name,
          totalPrice: draftOrder.totalPrice
        },
        booking: {
          id: booking.id,
          bookingId: booking.bookingId,
          status: booking.status
        }
      });
    } else if (intent === "update-booking") {
      // 予約データの更新処理
      const bookingId = formData.get("bookingId") as string;
      const startDate = formData.get("startDate") as string;
      const endDate = formData.get("endDate") as string;
      const customerEmail = formData.get("customerEmail") as string;
      const customerName = formData.get("customerName") as string;

      // 必須項目の検証
      if (!bookingId || !startDate || !endDate || !customerEmail) {
        return json({
          success: false,
          error: "必須項目が入力されていません",
        });
      }

      // 現在の予約情報を取得
      const currentBooking = await prisma.booking.findUnique({
        where: { id: bookingId },
        select: {
          productId: true,
          variantId: true,
          orderId: true,
          status: true,
          bookingId: true
        }
      });

      if (!currentBooking) {
        return json({
          success: false,
          error: "予約情報が見つかりません",
        });
      }

      // 仮予約状態（PROVISIONAL）の場合のみShopifyのドラフトオーダーも更新
      const shouldUpdateDraftOrder = currentBooking.status === 'PROVISIONAL' && currentBooking.orderId;

      // 在庫管理のテスト - 予約日程の重複チェック（自分自身を除外）
      // 統一された予約重複チェック関数を使用
      const hasOverlap = await checkBookingOverlap(
        prisma,
        shop,
        currentBooking.productId,
        currentBooking.variantId,
        startDate,
        endDate,
        bookingId // 自分自身を除外
      );

      if (hasOverlap) {
        return json({
          success: false,
          error: "選択された日程はすでに予約されています",
          details: "同じ商品・バリアントで同じ日程の予約が既に存在します"
        });
      }

      // 日程の妥当性チェック
      const dateValidation = validateRentalDates(new Date(startDate), new Date(endDate));
      if (!dateValidation.valid) {
        return json({
          success: false,
          error: dateValidation.message || "無効な日程が指定されています",
        });
      }

      // Shopifyのドラフトオーダーを更新（仮予約状態の場合のみ）
      let draftOrderUpdateResult = null;
      if (shouldUpdateDraftOrder) {
        // レンタル日数の計算
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);
        const rentalDays = Math.round((endDateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24)) + 1;

        // まず、ドラフトオーダーの詳細を取得して、ラインアイテムIDを取得
        const draftOrderGid = `gid://shopify/DraftOrder/${currentBooking.orderId}`;

        // ドラフトオーダーの詳細を取得
        const draftOrderResponse = await admin.graphql(`
          query getDraftOrder($id: ID!) {
            draftOrder(id: $id) {
              id
              name
              lineItems(first: 5) {
                edges {
                  node {
                    id
                    title
                    variant {
                      id
                    }
                    customAttributes {
                      key
                      value
                    }
                  }
                }
              }
            }
          }
        `, {
          variables: {
            id: draftOrderGid
          }
        });

        const draftOrderData = await draftOrderResponse.json();

        if (draftOrderData.errors) {
          console.error("ドラフトオーダー取得エラー:", draftOrderData.errors);
          return json({
            success: false,
            error: "ドラフトオーダーの取得に失敗しました",
            details: draftOrderData.errors
          });
        }

        // ラインアイテムIDを取得
        const lineItems = draftOrderData.data.draftOrder.lineItems.edges;
        if (lineItems.length === 0) {
          console.error("ドラフトオーダーにラインアイテムがありません");
          return json({
            success: false,
            error: "ドラフトオーダーにラインアイテムがありません"
          });
        }

        const lineItemId = lineItems[0].node.id;

        // ドラフトオーダーの更新
        const updateResponse = await admin.graphql(`
          mutation draftOrderUpdate($id: ID!, $input: DraftOrderInput!) {
            draftOrderUpdate(id: $id, input: $input) {
              draftOrder {
                id
                name
                metafields(first: 5) {
                  edges {
                    node {
                      namespace
                      key
                      value
                    }
                  }
                }
                customAttributes {
                  key
                  value
                }
                lineItems(first: 5) {
                  edges {
                    node {
                      title
                      customAttributes {
                        key
                        value
                      }
                    }
                  }
                }
              }
              userErrors {
                field
                message
              }
            }
          }
        `, {
          variables: {
            id: draftOrderGid,
            input: {
              customAttributes: [
                { key: "レンタルタイプ", value: "通常レンタル" },
                { key: "予約ID", value: currentBooking.bookingId }
              ],
              email: customerEmail,
              metafields: [
                {
                  namespace: "custom",
                  key: "note",
                  value: `レンタル予約: ${startDate}～${endDate}`,
                  type: "single_line_text_field"
                }
              ]
            }
          }
        });

        const updateData = await updateResponse.json();

        if (updateData.errors || updateData.data.draftOrderUpdate.userErrors.length > 0) {
          console.error("ドラフトオーダー更新エラー:", updateData.errors || updateData.data.draftOrderUpdate.userErrors);
          return json({
            success: false,
            error: "ドラフトオーダーの更新に失敗しました",
            details: updateData.errors || updateData.data.draftOrderUpdate.userErrors
          });
        }

        // ドラフトオーダーの更新（ラインアイテムを含む）
        const lineItemUpdateResponse = await admin.graphql(`
          mutation draftOrderUpdate($id: ID!, $input: DraftOrderInput!) {
            draftOrderUpdate(id: $id, input: $input) {
              draftOrder {
                id
                name
                lineItems(first: 5) {
                  edges {
                    node {
                      id
                      customAttributes {
                        key
                        value
                      }
                    }
                  }
                }
              }
              userErrors {
                field
                message
              }
            }
          }
        `, {
          variables: {
            id: draftOrderGid,
            input: {
              lineItems: [{
                variantId: lineItems[0].node.variant.id,
                quantity: 1,
                customAttributes: [
                  { key: "レンタル開始日", value: startDate },
                  { key: "レンタル終了日", value: endDate },
                  { key: "レンタル日数", value: rentalDays.toString() }
                ]
              }]
            }
          }
        });

        const lineItemUpdateData = await lineItemUpdateResponse.json();

        if (lineItemUpdateData.errors || lineItemUpdateData.data.draftOrderUpdate.userErrors.length > 0) {
          console.error("ラインアイテム更新エラー:", lineItemUpdateData.errors || lineItemUpdateData.data.draftOrderUpdate.userErrors);
          return json({
            success: false,
            error: "ラインアイテムの更新に失敗しました",
            details: lineItemUpdateData.errors || lineItemUpdateData.data.draftOrderUpdate.userErrors
          });
        }

        // 更新成功
        draftOrderUpdateResult = updateData.data.draftOrderUpdate.draftOrder;
      }

      // 予約データを更新
      const updatedBooking = await prisma.booking.update({
        where: { id: bookingId },
        data: {
          startDate: new Date(startDate),
          endDate: new Date(endDate),
          customerEmail,
          customerName,
          updatedAt: new Date()
        }
      });

      return json({
        success: true,
        updatedBooking: {
          id: updatedBooking.id,
          bookingId: updatedBooking.bookingId,
          startDate: updatedBooking.startDate.toISOString(),
          endDate: updatedBooking.endDate.toISOString(),
          customerEmail: updatedBooking.customerEmail,
          customerName: updatedBooking.customerName,
          status: updatedBooking.status
        },
        draftOrderUpdated: !!draftOrderUpdateResult,
        draftOrderDetails: draftOrderUpdateResult ? {
          id: draftOrderUpdateResult.id.replace('gid://shopify/DraftOrder/', ''),
          name: draftOrderUpdateResult.name
        } : null
      });
    } else if (intent === "delete-booking") {
      // 予約データの削除処理
      const bookingId = formData.get("bookingId") as string;

      if (!bookingId) {
        return json({
          success: false,
          error: "予約IDが指定されていません",
        });
      }

      // 予約データを削除
      await prisma.booking.delete({
        where: { id: bookingId }
      });

      return json({
        success: true,
        message: "予約データを削除しました"
      });
    } else if (intent === "complete-draft-order") {
      const draftOrderId = formData.get("draftOrderId") as string;
      const paymentPending = formData.get("paymentPending") === "true";

      if (!draftOrderId) {
        return json({
          success: false,
          error: "ドラフトオーダーIDが指定されていません",
        });
      }

      // ドラフトオーダー完了
      const draftOrderGid = `gid://shopify/DraftOrder/${draftOrderId}`;
      const completeResponse = await admin.graphql(`
        mutation draftOrderComplete($id: ID!, $paymentPending: Boolean) {
          draftOrderComplete(id: $id, paymentPending: $paymentPending) {
            draftOrder {
              id
              name
              status
              order {
                id
                name
                confirmed
                totalPrice
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `, {
        variables: {
          id: draftOrderGid,
          paymentPending
        }
      });

      const completeData = await completeResponse.json();

      if (completeData.errors || completeData.data.draftOrderComplete.userErrors.length > 0) {
        console.error("ドラフトオーダー完了エラー:", completeData.errors || completeData.data.draftOrderComplete.userErrors);
        return json({
          success: false,
          error: "ドラフトオーダーの完了に失敗しました",
          details: completeData.errors || completeData.data.draftOrderComplete.userErrors
        });
      }

      const completedOrder = completeData.data.draftOrderComplete.draftOrder;
      const orderId = completedOrder.order ? completedOrder.order.id.replace('gid://shopify/Order/', '') : null;
      const orderName = completedOrder.order ? completedOrder.order.name : null;

      // 予約データを更新
      if (orderId) {
        await prisma.booking.updateMany({
          where: { orderId: draftOrderId },
          data: {
            status: 'CONFIRMED',
            bookingType: 'CONFIRMED',
            orderId,
            orderName,
            updatedAt: new Date()
          }
        });
      }

      return json({
        success: true,
        completedOrder: {
          id: completedOrder.id.replace('gid://shopify/DraftOrder/', ''),
          name: completedOrder.name,
          status: completedOrder.status,
          order: completedOrder.order ? {
            id: orderId,
            name: orderName,
            confirmed: completedOrder.order.confirmed,
            totalPrice: completedOrder.order.totalPrice
          } : null
        }
      });
    }

    return json({
      success: false,
      error: "不明な操作が指定されました",
    });
  } catch (error) {
    console.error("アクションエラー:", error);
    return json({
      success: false,
      error: "処理中にエラーが発生しました: " + error.message,
    });
  }
}

// 料金計算関数はutils/booking/price-calculation.tsに移動しました

/**
 * テスト用決済フォームコンポーネント
 */
export default function TestCheckoutPage() {
  const { products, bookings, error } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();
  const { shop } = useLoaderData<typeof loader>();

  // 状態管理
  const [selectedProductId, setSelectedProductId] = useState("");
  const [selectedVariantId, setSelectedVariantId] = useState("");
  const [startDate, setStartDate] = useState(new Date().toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState(new Date(new Date().setDate(new Date().getDate() + 2)).toISOString().split('T')[0]);
  const [customerEmail, setCustomerEmail] = useState("<EMAIL>");
  const [customerName, setCustomerName] = useState("テストユーザー");
  const [totalPrice, setTotalPrice] = useState("0");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedDraftOrderId, setSelectedDraftOrderId] = useState("");
  const [paymentPending, setPaymentPending] = useState(false);

  // 編集・削除用の状態
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<any>(null);
  const [editStartDate, setEditStartDate] = useState("");
  const [editEndDate, setEditEndDate] = useState("");
  const [editCustomerEmail, setEditCustomerEmail] = useState("");
  const [editCustomerName, setEditCustomerName] = useState("");

  // 商品選択ハンドラー
  const handleProductChange = useCallback((value: string) => {
    setSelectedProductId(value);
    setSelectedVariantId("");

    // 商品が選択されたら、最初のバリアントを自動選択
    const product = products.find(p => p.id === value);
    if (product && product.variants && product.variants.length > 0) {
      setSelectedVariantId(product.variants[0].id);

      // レンタル日数の計算
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);
      const rentalDays = Math.round((endDateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24)) + 1;

      // 料金計算
      const basePrice = parseFloat(product.variants[0].price);
      // 正しいパラメータで料金計算関数を呼び出す
      const calculatedPrice = calculateRentalPrice(new Date(startDate), new Date(endDate), basePrice);
      console.log("計算された料金:", calculatedPrice, "基本料金:", basePrice);
      setTotalPrice(calculatedPrice.toString());
    }
  }, [products, startDate, endDate]);

  // バリアント選択ハンドラー
  const handleVariantChange = useCallback((value: string) => {
    setSelectedVariantId(value);

    // バリアントが選択されたら、料金を再計算
    const product = products.find(p => p.id === selectedProductId);
    if (product) {
      const variant = product.variants.find(v => v.id === value);
      if (variant) {
        // レンタル日数の計算
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);
        const rentalDays = Math.round((endDateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24)) + 1;

        // 料金計算
        const basePrice = parseFloat(variant.price);
        // 正しいパラメータで料金計算関数を呼び出す
        const calculatedPrice = calculateRentalPrice(new Date(startDate), new Date(endDate), basePrice);
        console.log("バリアント変更時の計算料金:", calculatedPrice, "基本料金:", basePrice);
        setTotalPrice(calculatedPrice.toString());
      }
    }
  }, [products, selectedProductId, startDate, endDate]);

  // 日付変更ハンドラー
  const handleDateChange = useCallback((field: 'start' | 'end', value: string) => {
    if (field === 'start') {
      setStartDate(value);
    } else {
      setEndDate(value);
    }

    // 日付が変更されたら、料金を再計算
    const product = products.find(p => p.id === selectedProductId);
    if (product && selectedVariantId) {
      const variant = product.variants.find(v => v.id === selectedVariantId);
      if (variant) {
        // レンタル日数の計算
        const startDateObj = new Date(field === 'start' ? value : startDate);
        const endDateObj = new Date(field === 'end' ? value : endDate);
        const rentalDays = Math.round((endDateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24)) + 1;

        // 料金計算
        const basePrice = parseFloat(variant.price);
        // 正しいパラメータで料金計算関数を呼び出す
        const calculatedPrice = calculateRentalPrice(
          new Date(field === 'start' ? value : startDate),
          new Date(field === 'end' ? value : endDate),
          basePrice
        );
        console.log("日付変更時の計算料金:", calculatedPrice, "基本料金:", basePrice);
        setTotalPrice(calculatedPrice.toString());
      }
    }
  }, [products, selectedProductId, selectedVariantId, startDate, endDate]);

  // ドラフトオーダー作成ハンドラー
  const handleCreateDraftOrder = useCallback(() => {
    if (!selectedProductId || !selectedVariantId || !customerEmail) {
      alert("商品、バリアント、メールアドレスは必須です");
      return;
    }

    setIsSubmitting(true);

    const formData = new FormData();
    formData.append("intent", "create-draft-order");
    formData.append("productId", selectedProductId);
    formData.append("variantId", selectedVariantId);
    formData.append("startDate", startDate);
    formData.append("endDate", endDate);
    formData.append("customerEmail", customerEmail);
    formData.append("customerName", customerName);
    formData.append("totalPrice", totalPrice);

    submit(formData, { method: "post" });
  }, [
    selectedProductId,
    selectedVariantId,
    startDate,
    endDate,
    customerEmail,
    customerName,
    totalPrice,
    submit
  ]);

  // アクションデータが変更されたときに送信中状態をリセット
  useEffect(() => {
    if (actionData) {
      setIsSubmitting(false);
    }
  }, [actionData]);

  // ドラフトオーダー完了ハンドラー
  const handleCompleteDraftOrder = useCallback(() => {
    if (!selectedDraftOrderId) {
      alert("ドラフトオーダーIDを選択してください");
      return;
    }

    setIsSubmitting(true);

    const formData = new FormData();
    formData.append("intent", "complete-draft-order");
    formData.append("draftOrderId", selectedDraftOrderId);
    formData.append("paymentPending", paymentPending.toString());

    submit(formData, { method: "post" });
  }, [selectedDraftOrderId, paymentPending, submit]);

  // 予約編集モーダルを開く
  const handleEditBooking = useCallback((booking: any) => {
    setSelectedBooking(booking);
    setEditStartDate(new Date(booking.startDate).toISOString().split('T')[0]);
    setEditEndDate(new Date(booking.endDate).toISOString().split('T')[0]);
    setEditCustomerEmail(booking.customerEmail);
    setEditCustomerName(booking.customerName || "");
    setEditModalOpen(true);
  }, []);

  // 予約削除モーダルを開く
  const handleDeleteBooking = useCallback((booking: any) => {
    setSelectedBooking(booking);
    setDeleteModalOpen(true);
  }, []);

  // 予約編集を保存
  const handleSaveEdit = useCallback(() => {
    if (!selectedBooking || !editCustomerEmail) {
      alert("必須項目を入力してください");
      return;
    }

    setIsSubmitting(true);

    const formData = new FormData();
    formData.append("intent", "update-booking");
    formData.append("bookingId", selectedBooking.id);
    formData.append("productId", selectedBooking.productId);
    formData.append("variantId", selectedBooking.variantId || "");
    formData.append("startDate", editStartDate);
    formData.append("endDate", editEndDate);
    formData.append("customerEmail", editCustomerEmail);
    formData.append("customerName", editCustomerName);

    submit(formData, { method: "post" });
    setEditModalOpen(false);
  }, [selectedBooking, editStartDate, editEndDate, editCustomerEmail, editCustomerName, submit]);

  // 予約削除を実行
  const handleConfirmDelete = useCallback(() => {
    if (!selectedBooking) return;

    setIsSubmitting(true);

    const formData = new FormData();
    formData.append("intent", "delete-booking");
    formData.append("bookingId", selectedBooking.id);

    submit(formData, { method: "post" });
    setDeleteModalOpen(false);
  }, [selectedBooking, submit]);

  // 商品選択オプション
  const productOptions = [
    { label: "商品を選択", value: "" },
    ...products.map(product => ({
      label: `${product.title} (${product.sku || "SKUなし"})`,
      value: product.id
    }))
  ];

  // バリアント選択オプション
  const variantOptions = [
    { label: "バリアントを選択", value: "" },
    ...(selectedProductId
      ? (products.find(p => p.id === selectedProductId)?.variants || []).map(variant => ({
          label: `${variant.title} - ¥${variant.price} (${variant.sku || "SKUなし"})`,
          value: variant.id
        }))
      : [])
  ];

  // ドラフトオーダー選択オプション
  const draftOrderOptions = [
    { label: "ドラフトオーダーを選択", value: "" },
    ...(bookings || [])
      .filter(b => b.orderId && b.status === 'PROVISIONAL') // 仮予約状態のみを表示
      .map(booking => ({
        label: `${booking.orderName || booking.orderId} - ${booking.customerEmail}`,
        value: booking.orderId || ""
      }))
  ];

  // 予約データテーブル
  const bookingRows = (bookings || []).map(booking => [
    booking.orderName || booking.orderId?.substring(0, 8) || "-",
    booking.product?.title || "不明",
    new Date(booking.startDate).toLocaleDateString(),
    new Date(booking.endDate).toLocaleDateString(),
    booking.customerEmail,
    booking.status,
    <ButtonGroup>
      <Button size="slim" onClick={() => handleEditBooking(booking)}>編集</Button>
      <Button size="slim" tone="critical" onClick={() => handleDeleteBooking(booking)}>削除</Button>
    </ButtonGroup>
  ]);

  return (
    <Page
      title="テスト用決済フォーム"
      subtitle="開発・テスト用の簡易決済フォーム"
      backAction={{ content: "戻る", url: "/app" }}
    >
      <Layout>
        {/* エラーメッセージ */}
        {(error || actionData?.error) && (
          <Layout.Section>
            <Banner
              title="エラー"
              tone="critical"
            >
              <p>{error || actionData?.error}</p>
              {actionData?.details && (
                <pre>{JSON.stringify(actionData.details, null, 2)}</pre>
              )}
            </Banner>
          </Layout.Section>
        )}

        {/* 成功メッセージ - ドラフトオーダー作成 */}
        {actionData?.success && actionData.draftOrder && (
          <Layout.Section>
            <Banner
              title="ドラフトオーダーが作成されました"
              tone="success"
            >
              <BlockStack gap="200">
                <Text>ドラフトオーダー: {actionData.draftOrder.name}</Text>
                <Text>ID: {actionData.draftOrder.id}</Text>
                <Text>合計金額: ¥{actionData.draftOrder.totalPrice}</Text>
                {actionData.booking && (
                  <Text>予約ID: {actionData.booking.bookingId}</Text>
                )}
                <Link url={`https://admin.shopify.com/store/${shop.split('.')[0]}/draft_orders/${actionData.draftOrder.id}`} external>
                  管理画面で表示
                </Link>
              </BlockStack>
            </Banner>
          </Layout.Section>
        )}

        {/* 成功メッセージ - ドラフトオーダー完了 */}
        {actionData?.success && actionData.completedOrder && (
          <Layout.Section>
            <Banner
              title="ドラフトオーダーが完了しました"
              tone="success"
            >
              <BlockStack gap="200">
                <Text>ドラフトオーダー: {actionData.completedOrder.name}</Text>
                <Text>ステータス: {actionData.completedOrder.status}</Text>
                {actionData.completedOrder.order && (
                  <>
                    <Text>注文番号: {actionData.completedOrder.order.name}</Text>
                    <Text>確定状態: {actionData.completedOrder.order.confirmed ? "確定済み" : "未確定"}</Text>
                    <Text>合計金額: ¥{actionData.completedOrder.order.totalPrice}</Text>
                    <Link url={`https://admin.shopify.com/store/${shop.split('.')[0]}/orders/${actionData.completedOrder.order.id}`} external>
                      管理画面で表示
                    </Link>
                  </>
                )}
              </BlockStack>
            </Banner>
          </Layout.Section>
        )}

        {/* 成功メッセージ - 予約編集 */}
        {actionData?.success && actionData.updatedBooking && (
          <Layout.Section>
            <Banner
              title="予約が更新されました"
              tone="success"
            >
              <BlockStack gap="200">
                <Text>予約ID: {actionData.updatedBooking.bookingId}</Text>
                <Text>期間: {new Date(actionData.updatedBooking.startDate).toLocaleDateString()} 〜 {new Date(actionData.updatedBooking.endDate).toLocaleDateString()}</Text>
                <Text>顧客: {actionData.updatedBooking.customerEmail}</Text>
                <Text>ステータス: {actionData.updatedBooking.status}</Text>
              </BlockStack>
            </Banner>
          </Layout.Section>
        )}

        {/* 成功メッセージ - 予約削除 */}
        {actionData?.success && actionData.message && (
          <Layout.Section>
            <Banner
              title={actionData.message}
              tone="success"
            />
          </Layout.Section>
        )}

        <Layout.Section>
          <BlockStack gap="400">
            <Card>
              <BlockStack gap="500">
                <Text variant="headingMd" as="h2">ドラフトオーダー作成</Text>

                <Select
                  label="商品"
                  options={productOptions}
                  value={selectedProductId}
                  onChange={handleProductChange}
                  helpText="予約する商品を選択してください"
                  requiredIndicator
                />

                <Select
                  label="バリアント"
                  options={variantOptions}
                  value={selectedVariantId}
                  onChange={handleVariantChange}
                  helpText="予約するバリアントを選択してください"
                  requiredIndicator
                  disabled={!selectedProductId}
                />

                <InlineStack gap="300" align="space-between">
                  <TextField
                    label="開始日"
                    type="date"
                    value={startDate}
                    onChange={value => handleDateChange('start', value)}
                    helpText="レンタル開始日"
                    requiredIndicator
                  />
                  <TextField
                    label="終了日"
                    type="date"
                    value={endDate}
                    onChange={value => handleDateChange('end', value)}
                    helpText="レンタル終了日"
                    requiredIndicator
                  />
                </InlineStack>

                <TextField
                  label="メールアドレス"
                  type="email"
                  value={customerEmail}
                  onChange={setCustomerEmail}
                  helpText="顧客のメールアドレスを入力してください"
                  requiredIndicator
                  autoComplete="email"
                />

                <TextField
                  label="顧客名"
                  value={customerName}
                  onChange={setCustomerName}
                  helpText="顧客の名前を入力してください"
                  autoComplete="name"
                />

                <TextField
                  label="合計金額"
                  type="number"
                  value={totalPrice}
                  onChange={setTotalPrice}
                  prefix="¥"
                  helpText="レンタルの合計金額"
                  requiredIndicator
                />

                <Box>
                  <Button
                    primary
                    onClick={handleCreateDraftOrder}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <InlineStack gap="200" align="center">
                        <Spinner size="small" />
                        <span>処理中...</span>
                      </InlineStack>
                    ) : (
                      "ドラフトオーダーを作成"
                    )}
                  </Button>
                </Box>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="500">
                <Text variant="headingMd" as="h2">ドラフトオーダー完了</Text>

                <Select
                  label="ドラフトオーダー"
                  options={draftOrderOptions}
                  value={selectedDraftOrderId}
                  onChange={setSelectedDraftOrderId}
                  helpText="完了するドラフトオーダーを選択してください"
                  requiredIndicator
                />

                <Box>
                  <Text variant="headingMd" as="h3">支払い状態</Text>
                  <BlockStack gap="200">
                    <RadioButton
                      label="支払い済み"
                      checked={!paymentPending}
                      id="payment-completed"
                      name="paymentStatus"
                      onChange={() => setPaymentPending(false)}
                    />
                    <RadioButton
                      label="支払い待ち"
                      checked={paymentPending}
                      id="payment-pending"
                      name="paymentStatus"
                      onChange={() => setPaymentPending(true)}
                    />
                  </BlockStack>
                </Box>

                <Box>
                  <Button
                    primary
                    onClick={handleCompleteDraftOrder}
                    disabled={isSubmitting || !selectedDraftOrderId}
                  >
                    {isSubmitting ? (
                      <InlineStack gap="200" align="center">
                        <Spinner size="small" />
                        <span>処理中...</span>
                      </InlineStack>
                    ) : (
                      "ドラフトオーダーを完了"
                    )}
                  </Button>
                </Box>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text variant="headingMd" as="h2">予約・注文データ</Text>

              {bookings.length === 0 ? (
                <Text>予約データがありません</Text>
              ) : (
                <DataTable
                  columnContentTypes={["text", "text", "text", "text", "text", "text", "text"]}
                  headings={["注文番号", "商品", "開始日", "終了日", "顧客", "ステータス", "操作"]}
                  rows={bookingRows}
                />
              )}
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>

      {/* 予約編集モーダル */}
      <Modal
        open={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title="予約を編集"
        primaryAction={{
          content: "保存",
          onAction: handleSaveEdit,
          loading: isSubmitting
        }}
        secondaryActions={[
          {
            content: "キャンセル",
            onAction: () => setEditModalOpen(false)
          }
        ]}
      >
        <Modal.Section>
          <BlockStack gap="400">
            {selectedBooking && (
              <>
                <Text>予約ID: {selectedBooking.bookingId}</Text>
                <Text>商品: {selectedBooking.product?.title || "不明"}</Text>

                <InlineStack gap="300" align="space-between">
                  <TextField
                    label="開始日"
                    type="date"
                    value={editStartDate}
                    onChange={setEditStartDate}
                    helpText="レンタル開始日"
                    requiredIndicator
                  />
                  <TextField
                    label="終了日"
                    type="date"
                    value={editEndDate}
                    onChange={setEditEndDate}
                    helpText="レンタル終了日"
                    requiredIndicator
                  />
                </InlineStack>

                <TextField
                  label="メールアドレス"
                  type="email"
                  value={editCustomerEmail}
                  onChange={setEditCustomerEmail}
                  helpText="顧客のメールアドレスを入力してください"
                  requiredIndicator
                  autoComplete="email"
                />

                <TextField
                  label="顧客名"
                  value={editCustomerName}
                  onChange={setEditCustomerName}
                  helpText="顧客の名前を入力してください"
                  autoComplete="name"
                />
              </>
            )}
          </BlockStack>
        </Modal.Section>
      </Modal>

      {/* 予約削除確認モーダル */}
      <Modal
        open={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="予約を削除"
        primaryAction={{
          content: "削除",
          onAction: handleConfirmDelete,
          destructive: true,
          loading: isSubmitting
        }}
        secondaryActions={[
          {
            content: "キャンセル",
            onAction: () => setDeleteModalOpen(false)
          }
        ]}
      >
        <Modal.Section>
          {selectedBooking && (
            <BlockStack gap="400">
              <Text>以下の予約を削除しますか？</Text>
              <Text>予約ID: {selectedBooking.bookingId}</Text>
              <Text>商品: {selectedBooking.product?.title || "不明"}</Text>
              <Text>期間: {new Date(selectedBooking.startDate).toLocaleDateString()} 〜 {new Date(selectedBooking.endDate).toLocaleDateString()}</Text>
              <Text>顧客: {selectedBooking.customerEmail}</Text>
              <Banner tone="critical">
                この操作は取り消せません。削除すると、この予約データは完全に削除されます。
              </Banner>
            </BlockStack>
          )}
        </Modal.Section>
      </Modal>
    </Page>
  );
}
