import { json, type LoaderFunctionArgs } from '@remix-run/node';
import { useLoaderData, useNavigate, useSearchParams } from '@remix-run/react';
import {
  Page,
  Card,
  Text,
  Button,
  TextField,
  Layout,
  Checkbox,
  Modal,
  FormLayout,
  Select,
  DataTable,
  ButtonGroup,
  Box,
  InlineStack,
  BlockStack
} from '@shopify/polaris';
import { useState, useCallback } from 'react';
import { authenticate } from '../shopify.server';
import { prisma } from '../db.server';

// ピッキングステータスに応じたバッジ設定
const pickingStatusBadgeMap: Record<string, { tone: 'info' | 'success' | 'attention' | 'warning' | 'critical' | 'new'; label: string }> = {
  pending: { tone: 'info', label: 'ピッキング待ち' },
  picked: { tone: 'success', label: 'ピッキング完了' },
  shipped: { tone: 'attention', label: '発送済み' },
  cancelled: { tone: 'critical', label: 'キャンセル' }
};

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticate.admin(request);

  // クエリパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  const limit = parseInt(url.searchParams.get('limit') || '50', 10);
  const deliveryDate = url.searchParams.get('deliveryDate') || undefined;
  const pickingStatus = url.searchParams.get('pickingStatus') || undefined;
  const query = url.searchParams.get('query') || undefined;

  // 検索条件を構築
  const where: any = {
    deliveryType: 'delivery' // 配送のみ（集荷はピッキング不要）
  };

  if (deliveryDate) {
    const date = new Date(deliveryDate);
    const nextDay = new Date(date);
    nextDay.setDate(nextDay.getDate() + 1);

    where.deliveryDate = {
      gte: date,
      lt: nextDay
    };
  }

  if (pickingStatus) {
    where.pickingStatus = pickingStatus;
  }

  if (query) {
    where.OR = [
      { orderId: { contains: query } },
      {
        booking: {
          OR: [
            { bookingId: { contains: query } },
            { customerName: { contains: query, mode: 'insensitive' } },
            { customerCompany: { contains: query, mode: 'insensitive' } },
            {
              product: {
                title: { contains: query, mode: 'insensitive' }
              }
            },
            {
              product: {
                sku: { contains: query }
              }
            }
          ]
        }
      }
    ];
  }

  // ピッキング対象の配送スケジュールを取得
  const schedules = await prisma.deliverySchedule.findMany({
    where,
    include: {
      booking: {
        include: {
          product: true
        }
      },
      staff: true,
      carrier: true,
      pickingStaff: true
    },
    orderBy: [
      { deliveryDate: 'asc' },
      { timeFrom: 'asc' }
    ],
    skip: (page - 1) * limit,
    take: limit
  });

  // 総件数を取得
  const total = await prisma.deliverySchedule.count({ where });

  // スタッフ一覧を取得（ピッキング担当者選択用）
  const staff = await prisma.staff.findMany({
    where: { isActive: true },
    orderBy: { name: 'asc' }
  });

  return json({
    schedules: schedules.map(schedule => ({
      id: schedule.id,
      bookingId: schedule.booking?.bookingId || '',
      productTitle: schedule.booking?.product?.title || '',
      productSku: schedule.booking?.product?.sku || '',
      customerName: schedule.booking?.customerName || '',
      customerCompany: schedule.booking?.customerCompany || '',
      deliveryDate: schedule.deliveryDate.toISOString(),
      timeFrom: schedule.timeFrom || '',
      timeTo: schedule.timeTo || '',
      pickingStatus: schedule.pickingStatus || 'pending',
      pickingStaffName: schedule.pickingStaff?.name || '',
      carrierName: schedule.carrier?.name || '',
      orderId: schedule.orderId || '',
      notes: schedule.notes || '',
      // 商品情報
      productInfo: {
        height: schedule.booking?.product?.height || 0,
        width: schedule.booking?.product?.width || 0,
        depth: schedule.booking?.product?.depth || 0,
        weight: schedule.booking?.product?.weight || 0,
        location: schedule.pickingLocation || ''
      }
    })),
    staff: staff.map(s => ({
      value: s.id,
      label: s.name
    })),
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}

export default function DeliveryPicking() {
  const { schedules, staff, pagination } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // フィルター状態
  const [deliveryDate, setDeliveryDate] = useState(searchParams.get('deliveryDate') || '');
  const [pickingStatus, setPickingStatus] = useState(searchParams.get('pickingStatus') || '');
  const [searchQuery, setSearchQuery] = useState(searchParams.get('query') || '');

  // ピッキング操作状態
  const [selectedSchedules, setSelectedSchedules] = useState<string[]>([]);
  const [showPickingModal, setShowPickingModal] = useState(false);
  const [pickingStaffId, setPickingStaffId] = useState('');

  // 検索実行
  const handleSearch = useCallback(() => {
    const params = new URLSearchParams();

    if (deliveryDate) params.set('deliveryDate', deliveryDate);
    if (pickingStatus) params.set('pickingStatus', pickingStatus);
    if (searchQuery) params.set('query', searchQuery);

    navigate(`/app/delivery/picking?${params.toString()}`);
  }, [deliveryDate, pickingStatus, searchQuery, navigate]);

  // フィルタークリア
  const handleClearFilters = useCallback(() => {
    setDeliveryDate('');
    setPickingStatus('');
    setSearchQuery('');
    navigate('/app/delivery/picking');
  }, [navigate]);

  // ピッキング台帳出力
  const handlePickingListOutput = useCallback(() => {
    // TODO: ピッキング台帳PDF出力機能を実装
    console.log('ピッキング台帳出力');
  }, []);

  // ピッキング完了処理
  const handlePickingComplete = useCallback(async () => {
    if (selectedSchedules.length === 0) return;

    try {
      // TODO: ピッキング完了APIを実装
      console.log('ピッキング完了:', {
        scheduleIds: selectedSchedules,
        staffId: pickingStaffId
      });
      setShowPickingModal(false);
      setPickingStaffId('');
      setSelectedSchedules([]);
      // ページをリロード
      window.location.reload();
    } catch (error) {
      console.error('ピッキング完了エラー:', error);
    }
  }, [selectedSchedules, pickingStaffId]);

  // 今日の日付をデフォルトに設定
  const today = new Date().toISOString().split('T')[0];

  // チェックボックス選択処理
  const handleScheduleSelection = useCallback((scheduleId: string, checked: boolean) => {
    setSelectedSchedules(prev => {
      if (checked) {
        return [...prev, scheduleId];
      } else {
        return prev.filter(id => id !== scheduleId);
      }
    });
  }, []);

  // データテーブル用のデータ準備
  const tableRows = schedules.map(schedule => {
    const booking = schedule.booking;
    const product = booking?.product;

    return [
      <Checkbox
        checked={selectedSchedules.includes(schedule.id)}
        onChange={(checked) => handleScheduleSelection(schedule.id, checked)}
      />,
      booking?.bookingId || schedule.orderId || '-',
      '0000050007', // 顧客No（仮）
      booking?.customerName || '-',
      booking?.customerCompany || '-',
      booking?.customerName || '-', // 部署名（仮で顧客名を使用）
      product?.title || '-',
      product?.sku || '-'
    ];
  });

  return (
    <Page title="【新】ピッキング登録 - 更新済み">
      <Layout>
        <Layout.Section>
          <Card>
            <Box padding="400" background="bg-surface-info">
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd" tone="subdued">対象データ検索</Text>
                <InlineStack gap="400" align="start">
                  <Box minWidth="200px">
                    <TextField
                      label="貸出開始日"
                      type="date"
                      value={deliveryDate}
                      onChange={setDeliveryDate}
                      placeholder={today}
                    />
                  </Box>
                  <Box minWidth="200px">
                    <TextField
                      label="伝票No."
                      value={searchQuery}
                      onChange={setSearchQuery}
                      placeholder="バーコード読み取り"
                    />
                  </Box>
                  <Box paddingBlockStart="600">
                    <ButtonGroup>
                      <Button onClick={handleSearch}>検索</Button>
                      <Button onClick={handleClearFilters}>クリア</Button>
                    </ButtonGroup>
                  </Box>
                </InlineStack>
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <Box padding="400">
              <DataTable
                columnContentTypes={[
                  'text', // チェックボックス
                  'text', // 伝票No
                  'text', // 顧客No
                  'text', // 顧客名
                  'text', // 会社名
                  'text', // 部署名
                  'text', // 商品名
                  'text'  // SKU
                ]}
                headings={[
                  '',
                  '伝票No.',
                  '顧客No.',
                  '顧客名',
                  '会社名',
                  '部署名',
                  '商品名',
                  'SKU'
                ]}
                rows={tableRows}
                truncate
              />
            </Box>

            <Box padding="400" borderBlockStartWidth="025" borderColor="border">
              <InlineStack gap="400" align="end">
                <Button
                  onClick={handlePickingListOutput}
                  variant="primary"
                >
                  ピッキング台帳出力
                </Button>
                <Button
                  onClick={() => setShowPickingModal(true)}
                  disabled={selectedSchedules.length === 0}
                >
                  ピッキング完了
                </Button>
              </InlineStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>

      {/* ピッキング完了モーダル */}
      <Modal
        open={showPickingModal}
        onClose={() => setShowPickingModal(false)}
        title="ピッキング完了"
        primaryAction={{
          content: 'ピッキング完了',
          onAction: handlePickingComplete,
          disabled: !pickingStaffId
        }}
        secondaryActions={[
          {
            content: 'キャンセル',
            onAction: () => setShowPickingModal(false)
          }
        ]}
      >
        <Modal.Section>
          <FormLayout>
            <Text as="p" variant="bodyMd">
              選択した{selectedSchedules.length}件のピッキングを完了します。
            </Text>
            <Select
              label="ピッキング担当者"
              options={[
                { label: 'ピッキング担当者を選択', value: '' },
                ...staff
              ]}
              value={pickingStaffId}
              onChange={setPickingStaffId}
            />
          </FormLayout>
        </Modal.Section>
      </Modal>
    </Page>
  );
}
