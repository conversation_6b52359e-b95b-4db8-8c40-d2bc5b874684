/**
 * 商品価格修正スクリプト
 */

import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

// Shopify GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      'Content-Type': 'application/json',
    },
  }
);

// 商品詳細を取得するクエリ
const GET_PRODUCT_DETAILS = gql`
  query getProduct($id: ID!) {
    product(id: $id) {
      id
      title
      variants(first: 10) {
        edges {
          node {
            id
            title
            sku
            price
          }
        }
      }
    }
  }
`;

async function fixProductPricing() {
  console.log('🔧 商品価格修正を開始...\n');

  try {
    // 問題の商品を取得
    const product = await prisma.product.findFirst({
      where: { sku: '201-07-107-1D' }
    });

    if (!product) {
      console.log('❌ 商品が見つかりません');
      return;
    }

    console.log('=== 修正前の商品情報 ===');
    console.log(`ID: ${product.id}`);
    console.log(`タイトル: ${product.title}`);
    console.log(`SKU: ${product.sku}`);
    console.log(`現在の価格: ¥${(product.price / 100).toLocaleString()}`);
    console.log(`現在の価格（セント）: ${product.price}`);
    console.log('');

    // Shopifyから正しい価格を取得
    const shopifyId = `gid://shopify/Product/${product.shopifyId}`;
    const shopifyResult = await shopifyClient.request(GET_PRODUCT_DETAILS, {
      id: shopifyId
    });

    if (!shopifyResult.product) {
      console.log('❌ Shopify商品が見つかりません');
      return;
    }

    console.log('=== Shopifyの価格情報 ===');
    shopifyResult.product.variants.edges.forEach((edge, index) => {
      const variant = edge.node;
      console.log(`${index + 1}. ${variant.title} (${variant.sku})`);
      console.log(`   価格: ¥${parseFloat(variant.price).toLocaleString()}`);
    });
    console.log('');

    // 1日レンタルの価格を取得
    const oneDayVariant = shopifyResult.product.variants.edges.find(edge => 
      edge.node.sku && edge.node.sku.includes('-1D')
    );

    if (!oneDayVariant) {
      console.log('❌ 1日レンタルバリアントが見つかりません');
      return;
    }

    const correctPrice = Math.round(parseFloat(oneDayVariant.node.price) * 100); // セント単位に変換
    console.log(`=== 正しい価格 ===`);
    console.log(`Shopify 1日価格: ¥${parseFloat(oneDayVariant.node.price).toLocaleString()}`);
    console.log(`修正後価格（セント）: ${correctPrice}`);
    console.log('');

    // 価格を修正
    console.log('🔄 価格を修正中...');
    const updatedProduct = await prisma.product.update({
      where: { id: product.id },
      data: {
        price: correctPrice,
        updatedAt: new Date()
      }
    });

    console.log('✅ 価格修正完了');
    console.log(`修正後価格: ¥${(updatedProduct.price / 100).toLocaleString()}`);
    console.log('');

    // 3日間レンタルの価格計算をテスト
    console.log('=== 3日間レンタル価格計算テスト ===');
    const days = 3;
    const basePrice = correctPrice;
    
    // 2-7日: 基本価格 + (基本価格 × 0.2 × (日数 - 1))
    const totalPrice = basePrice + (basePrice * 0.2 * (days - 1));
    
    console.log(`基本価格（1日）: ¥${(basePrice / 100).toLocaleString()}`);
    console.log(`${days}日間の計算:`);
    console.log(`  基本価格: ¥${(basePrice / 100).toLocaleString()}`);
    console.log(`  追加料金: ¥${(basePrice * 0.2 * (days - 1) / 100).toLocaleString()} (¥${(basePrice / 100).toLocaleString()} × 0.2 × ${days - 1})`);
    console.log(`  合計: ¥${(totalPrice / 100).toLocaleString()}`);
    console.log('');

    // 他の商品も確認・修正
    console.log('=== 他の商品の価格確認 ===');
    const allProducts = await prisma.product.findMany({
      where: {
        price: { lt: 10000 } // 100円未満の商品（明らかに間違い）
      },
      select: {
        id: true,
        title: true,
        sku: true,
        price: true,
        shopifyId: true
      }
    });

    if (allProducts.length > 0) {
      console.log(`価格が異常に安い商品: ${allProducts.length}件`);
      for (const prod of allProducts) {
        console.log(`- ${prod.title} (${prod.sku}): ¥${(prod.price / 100).toLocaleString()}`);
        
        // Shopifyから正しい価格を取得して修正
        try {
          const shopifyProdId = `gid://shopify/Product/${prod.shopifyId}`;
          const shopifyProdResult = await shopifyClient.request(GET_PRODUCT_DETAILS, {
            id: shopifyProdId
          });

          if (shopifyProdResult.product && shopifyProdResult.product.variants.edges.length > 0) {
            const firstVariant = shopifyProdResult.product.variants.edges[0].node;
            const correctProdPrice = Math.round(parseFloat(firstVariant.price) * 100);
            
            if (correctProdPrice > prod.price) {
              await prisma.product.update({
                where: { id: prod.id },
                data: { price: correctProdPrice }
              });
              console.log(`  → 修正: ¥${(correctProdPrice / 100).toLocaleString()}`);
            }
          }
        } catch (error) {
          console.log(`  → エラー: ${error.message}`);
        }
      }
    } else {
      console.log('価格が異常に安い商品はありません');
    }

    console.log('\n✅ 価格修正処理完了');

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
fixProductPricing();
