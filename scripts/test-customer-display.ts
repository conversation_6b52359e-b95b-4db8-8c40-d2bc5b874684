/**
 * ShopifyCustomerDisplayコンポーネントのテストスクリプト
 *
 * このスクリプトは、ShopifyCustomerDisplayコンポーネントが
 * セッション情報を正しく取得できるかどうかをテストします。
 *
 * 実行方法: npx tsx scripts/test-customer-display.ts
 */

import dotenv from 'dotenv';
import { prisma } from '../app/db.server';
import { CustomerReferenceService } from '../app/services/customer-reference.service';

// 環境変数の読み込み
dotenv.config();

// テスト用の顧客ID
const TEST_CUSTOMER_IDS = [
  '8420600119464', // 佐藤花子さん
  '8420600185000'  // 別の顧客ID
];

/**
 * 顧客情報を取得するテスト関数
 */
async function testCustomerInfo() {
  console.log('ShopifyCustomerDisplayコンポーネントのテストを開始します...');

  try {
    // オフラインセッションを取得
    const shopName = process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com';
    const offlineSessionId = `offline_${shopName}`;

    // データベースからセッションを取得
    const session = await prisma.session.findUnique({
      where: { id: offlineSessionId }
    });

    if (!session) {
      console.log('セッションが見つかりませんでした。');
      console.log('アプリがShopifyストアにインストールされていることを確認してください。');
      return false;
    }

    console.log(`セッションが見つかりました: ${session.shop}`);

    // 顧客参照サービスを初期化
    const customerService = new CustomerReferenceService(session);

    // テスト用の顧客情報を取得
    for (const customerId of TEST_CUSTOMER_IDS) {
      console.log(`\n顧客ID ${customerId} の情報を取得中...`);

      try {
        const customerInfo = await customerService.getCustomerById(customerId);

        if (customerInfo) {
          console.log('顧客情報が取得できました:');
          console.log(`- 名前: ${customerInfo.name}`);
          console.log(`- メール: ${customerInfo.email}`);
          console.log(`- 電話: ${customerInfo.phone || 'なし'}`);
          console.log(`- 住所: ${customerInfo.address || 'なし'}`);
        } else {
          console.log(`顧客ID ${customerId} の情報が見つかりませんでした。`);
        }
      } catch (customerError) {
        console.error(`顧客ID ${customerId} の情報取得中にエラーが発生しました:`, customerError);
      }
    }

    // ShopifyCustomerDisplayコンポーネントのシミュレーション
    console.log('\nShopifyCustomerDisplayコンポーネントのシミュレーション:');
    console.log('1. セッション情報あり + 顧客IDあり → 顧客情報を表示');
    console.log('2. セッション情報あり + 顧客IDなし → フォールバック表示');
    console.log('3. セッション情報なし + 顧客IDあり → フォールバック表示');

    return true;
  } catch (error) {
    console.error('テスト中にエラーが発生しました:', error);
    if (error instanceof Error) {
      console.error('エラーメッセージ:', error.message);
      console.error('スタックトレース:', error.stack);
    }
    return false;
  }
}

/**
 * 修正内容の検証方法を表示する関数
 */
function showVerificationSteps() {
  console.log('\n----- 修正内容の検証方法 -----');
  console.log('1. アプリケーションを起動: npm run dev');
  console.log('2. ブラウザでアプリケーションにアクセス');
  console.log('3. 予約一覧画面を開く');
  console.log('4. ブラウザのコンソールで以下を実行:');
  console.log('   console.log(window.ENV.shopifySession)');
  console.log('5. セッション情報が表示されることを確認');
  console.log('6. 顧客情報が正しく表示されていることを確認');
  console.log('7. コンソールにエラーが表示されていないことを確認');

  console.log('\n----- 修正内容の説明 -----');
  console.log('1. root.tsx: Shopifyセッション情報をクライアントサイドに渡すように修正');
  console.log('   - ローダー関数を非同期関数に変更し、Shopify認証情報を取得');
  console.log('   - 取得したセッション情報をENV.shopifySessionに追加');

  console.log('2. app.tsx: セッション情報をwindow.ENVに設定');
  console.log('   - ローダー関数でShopifyセッション情報を取得');
  console.log('   - App関数内でwindow.ENVにセッション情報を設定');

  console.log('3. ShopifyCustomerDisplay.tsx: エラーハンドリングの改善');
  console.log('   - セッション情報がない場合にエラーをスローせず、フォールバック表示を使用');

  console.log('4. global.d.ts: 型定義の更新');
  console.log('   - window.ENVの型定義にshopifySessionプロパティを追加');
}

// メイン処理
async function main() {
  const testResult = await testCustomerInfo();

  if (testResult) {
    console.log('\nテストが成功しました！');
    showVerificationSteps();
  } else {
    console.log('\nテストが失敗しました。');
  }
}

// スクリプトを実行
main();
