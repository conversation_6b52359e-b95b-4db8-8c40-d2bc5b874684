/**
 * 改善された商品検索API
 * 基本SKUから全バリアントを検索し、商品グループとして返す
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { 
  buildImprovedSkuSearch, 
  extractBaseSku,
  formatSearchResultsWithVariants 
} from "../utils/sku-search-improved";

export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  const url = new URL(request.url);
  const searchQuery = url.searchParams.get("q") || "";
  const includeVariants = url.searchParams.get("includeVariants") !== "false";

  try {
    if (!searchQuery) {
      return json({ 
        products: [], 
        message: "検索クエリが必要です",
        searchInfo: null 
      });
    }

    console.log(`改善版商品検索API: "${searchQuery}"`);

    // 改善されたSKU検索条件を生成
    const { baseSku, variants, searchConditions } = buildImprovedSkuSearch(searchQuery);
    
    // 検索条件を構築
    const whereConditions = {
      shop,
      OR: [
        // タイトル検索
        { title: { contains: searchQuery.trim(), mode: 'insensitive' as const } },
        // SKU検索（改善版）
        ...searchConditions.map(condition => condition)
      ]
    };

    // 商品を検索
    const products = await prisma.product.findMany({
      where: whereConditions,
      select: {
        id: true,
        title: true,
        sku: true,
        status: true,
        shopifyId: true,
        basicInfo: true,
        pricing: true,
        metadata: true,
      },
      take: 50 // バリアント分を考慮して増やす
    });

    console.log(`検索結果: ${products.length}件の商品が見つかりました`);

    // 基本SKUでグループ化
    const productGroups = new Map<string, any[]>();
    
    products.forEach(product => {
      const productBaseSku = extractBaseSku(product.sku);
      
      if (!productGroups.has(productBaseSku)) {
        productGroups.set(productBaseSku, []);
      }
      
      productGroups.get(productBaseSku)!.push(product);
    });

    // グループ化された結果を整形
    const groupedResults = Array.from(productGroups.entries()).map(([baseSku, products]) => {
      // 商品グループ名を取得（最初の商品のタイトルから）
      const groupName = products[0]?.title?.replace(/\s*\[.*?\]\s*$/, '').trim() || baseSku;
      
      return {
        baseSku,
        groupName,
        productCount: products.length,
        products: products.map(p => ({
          ...p,
          isVariant: p.sku !== baseSku
        }))
      };
    });

    // 各商品グループの予約状況を取得
    const resultsWithBookings = await Promise.all(
      groupedResults.map(async (group) => {
        // グループ内の全商品IDを収集
        const productIds = group.products.map(p => p.id);
        
        // 予約情報を一括取得
        const bookings = await prisma.booking.findMany({
          where: {
            shop,
            productId: { in: productIds },
            status: { in: ['CONFIRMED', 'IN_PROGRESS'] }
          },
          select: {
            id: true,
            productId: true,
            startDate: true,
            endDate: true,
            status: true,
            bookingType: true,
            customerName: true,
            bookingId: true,
          },
          orderBy: {
            startDate: 'asc'
          }
        });

        // 商品ごとに予約を振り分け
        const productsWithBookings = group.products.map(product => {
          const productBookings = bookings.filter(b => b.productId === product.id);
          
          return {
            ...product,
            bookingCount: productBookings.length,
            bookings: productBookings.map(booking => ({
              id: booking.id,
              bookingId: booking.bookingId || "",
              startDate: formatDate(booking.startDate),
              endDate: formatDate(booking.endDate),
              status: booking.status,
              bookingType: booking.bookingType,
              customerName: booking.customerName || "名前なし"
            }))
          };
        });

        return {
          ...group,
          products: productsWithBookings,
          totalBookings: bookings.length
        };
      })
    );

    // 検索情報を返す
    const searchInfo = {
      query: searchQuery,
      baseSku: baseSku || null,
      variants: variants || [],
      groupCount: resultsWithBookings.length,
      totalProducts: products.length
    };

    return json({ 
      products: resultsWithBookings,
      message: resultsWithBookings.length > 0 
        ? `${resultsWithBookings.length}件の商品グループが見つかりました` 
        : `「${searchQuery}」に一致する商品が見つかりませんでした`,
      searchInfo
    });

  } catch (error) {
    console.error("商品検索エラー:", error);
    return json({ 
      products: [], 
      message: "検索中にエラーが発生しました",
      searchInfo: null,
      error: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

// 日付フォーマット関数
function formatDate(date: Date | null | undefined): string {
  if (!date) return "";
  const d = new Date(date);
  return `${d.getFullYear()}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getDate().toString().padStart(2, '0')}`;
}

/**
 * レスポンスの型定義
 */
export interface ImprovedSearchResponse {
  products: ProductGroup[];
  message: string;
  searchInfo: SearchInfo | null;
  error?: string;
}

export interface ProductGroup {
  baseSku: string;
  groupName: string;
  productCount: number;
  totalBookings: number;
  products: ProductWithBookings[];
}

export interface ProductWithBookings {
  id: string;
  title: string;
  sku: string;
  status: string;
  shopifyId: string;
  basicInfo: any;
  pricing: any;
  metadata: any;
  isVariant: boolean;
  bookingCount: number;
  bookings: BookingInfo[];
}

export interface BookingInfo {
  id: string;
  bookingId: string;
  startDate: string;
  endDate: string;
  status: string;
  bookingType: string;
  customerName: string;
}

export interface SearchInfo {
  query: string;
  baseSku: string | null;
  variants: string[];
  groupCount: number;
  totalProducts: number;
}