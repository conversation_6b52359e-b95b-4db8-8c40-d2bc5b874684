import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { PrismaClient } from "@prisma/client";
import {
  Page,
  Layout,
  Card,
  Banner,
  Text,
  List,
  BlockStack,
  Box,
  InlineStack
} from "@shopify/polaris";
import { metafieldService } from "../services/shopify/metafield.service";
import { parseRentalDatesMetafield, parseBookingsMetafield } from "../utils/metafield/metafield-parser";
import { getDatesInRange } from "../utils/calendar/calendar-utils";

const prisma = new PrismaClient();

function parseISODate(dateString) {
  if (!dateString) return new Date();
  return new Date(dateString);
}

async function migrateCalendarData() {
  console.log("カレンダーデータの移行を開始します...");

  // すべての商品を取得
  const products = await prisma.product.findMany();
  console.log(`${products.length}件の商品を処理します`);

  let success = 0;
  let failed = 0;
  let results = [];

  for (const product of products) {
    try {
      console.log(`商品処理中: ${product.id} (${product.title || product.shopifyId})`);

      // メタフィールドからレンタル期間データを取得
      const metafields = await metafieldService.getProductMetafields(product.shopifyId);
      const rentalDates = parseRentalDatesMetafield(metafields);
      const bookingsData = parseBookingsMetafield(metafields);

      // 既存のAvailableDatesを確認
      const existingAvailableDates = await prisma.availableDates.findUnique({
        where: { productId: product.id }
      });

      // 既存のBlackoutDatesを確認
      const existingBlackoutDates = await prisma.blackoutDate.findMany({
        where: { productId: product.id }
      });

      let productResult = {
        productId: product.id,
        title: product.title || product.shopifyId,
        actions: []
      };

      // レンタル可能期間の移行
      if (rentalDates?.availableDates) {
        console.log(`レンタル可能期間を移行: ${rentalDates.availableDates.start} - ${rentalDates.availableDates.end}`);

        // 既存のRentalAvailabilityを確認
        const existingRentalAvailability = await prisma.rentalAvailability.findFirst({
          where: {
            productId: product.id,
            startDate: parseISODate(rentalDates.availableDates.start),
            endDate: parseISODate(rentalDates.availableDates.end)
          }
        });

        if (!existingRentalAvailability) {
          // RentalAvailabilityを作成
          await prisma.rentalAvailability.create({
            data: {
              shop: product.shop,
              productId: product.id,
              shopifyProductId: product.shopifyId,
              startDate: parseISODate(rentalDates.availableDates.start),
              endDate: parseISODate(rentalDates.availableDates.end),
              isAvailable: true,
              notes: "Migrated from metafield"
            }
          });

          productResult.actions.push("RentalAvailabilityを作成しました");
          console.log(`RentalAvailabilityを作成しました`);
        } else {
          productResult.actions.push("既存のRentalAvailabilityが見つかりました。スキップします。");
          console.log(`既存のRentalAvailabilityが見つかりました。スキップします。`);
        }

        // 既存のAvailableDatesがなければ作成
        if (!existingAvailableDates) {
          await prisma.availableDates.create({
            data: {
              productId: product.id,
              startDate: parseISODate(rentalDates.availableDates.start),
              endDate: parseISODate(rentalDates.availableDates.end)
            }
          });

          productResult.actions.push("AvailableDatesを作成しました");
          console.log(`AvailableDatesを作成しました`);
        }
      }

      // ブラックアウト日の移行
      if (rentalDates?.blackoutDates && rentalDates.blackoutDates.length > 0) {
        console.log(`ブラックアウト日を移行: ${rentalDates.blackoutDates.length}件`);
        productResult.actions.push(`ブラックアウト日を移行: ${rentalDates.blackoutDates.length}件`);

        for (const blackout of rentalDates.blackoutDates) {
          const startDate = parseISODate(blackout.start);
          const endDate = parseISODate(blackout.end);

          // 既存のBlackoutDateを確認
          const existingBlackout = existingBlackoutDates.find(bd =>
            bd.startDate.toISOString() === startDate.toISOString() &&
            bd.endDate.toISOString() === endDate.toISOString()
          );

          if (!existingBlackout) {
            // BlackoutDateを作成
            await prisma.blackoutDate.create({
              data: {
                productId: product.id,
                startDate,
                endDate,
                reason: blackout.reason || "Migrated from metafield"
              }
            });

            productResult.actions.push(`BlackoutDateを作成しました: ${blackout.start} - ${blackout.end}`);
            console.log(`BlackoutDateを作成しました: ${blackout.start} - ${blackout.end}`);
          }

          // 期間内の各日付に対してInventoryCalendarレコードを作成
          const dateRange = {
            start: startDate,
            end: endDate
          };

          const dates = getDatesInRange(dateRange);

          for (const date of dates) {
            // 既存のInventoryCalendarを確認
            const existingInventory = await prisma.inventoryCalendar.findUnique({
              where: {
                shop_productId_date: {
                  shop: product.shop,
                  productId: product.id,
                  date
                }
              }
            });

            if (!existingInventory) {
              // InventoryCalendarを作成
              await prisma.inventoryCalendar.create({
                data: {
                  shop: product.shop,
                  productId: product.id,
                  shopifyProductId: product.shopifyId,
                  date,
                  isAvailable: false,
                  unavailableReason: "blackout",
                  note: blackout.reason || "Blackout period"
                }
              });
            } else {
              // 既存のInventoryCalendarを更新
              await prisma.inventoryCalendar.update({
                where: {
                  id: existingInventory.id
                },
                data: {
                  isAvailable: false,
                  unavailableReason: "blackout",
                  note: blackout.reason || "Blackout period"
                }
              });
            }
          }

          productResult.actions.push(`InventoryCalendarを更新しました: ${dates.length}日`);
          console.log(`InventoryCalendarを更新しました: ${dates.length}日`);
        }
      }

      // 予約データの移行
      if (bookingsData?.bookings && bookingsData.bookings.length > 0) {
        console.log(`予約データを移行: ${bookingsData.bookings.length}件`);
        productResult.actions.push(`予約データを移行: ${bookingsData.bookings.length}件`);

        for (const booking of bookingsData.bookings) {
          // 予約期間
          const startDate = parseISODate(booking.startDate);
          const endDate = parseISODate(booking.endDate);

          // 既存のBookingを確認
          const existingBooking = await prisma.booking.findFirst({
            where: {
              productId: product.id,
              startDate,
              endDate,
              bookingId: booking.id
            }
          });

          if (!existingBooking) {
            // Bookingを作成
            await prisma.booking.create({
              data: {
                productId: product.id,
                startDate,
                endDate,
                status: booking.status === 'cancelled' ? 'CANCELLED' :
                        booking.status === 'completed' ? 'COMPLETED' :
                        booking.type === 'confirmed' ? 'CONFIRMED' : 'PROVISIONAL',
                bookingType: booking.type === 'confirmed' ? 'CONFIRMED' : 'PROVISIONAL',
                priority: booking.priority || 1,
                customerEmail: booking.customerEmail,
                customerName: booking.customerName,
                bookingId: booking.id,
                shop: product.shop
              }
            });

            productResult.actions.push(`Bookingを作成しました: ${booking.id}`);
            console.log(`Bookingを作成しました: ${booking.id}`);
          }

          // 期間内の各日付に対してInventoryCalendarレコードを作成
          const dateRange = {
            start: startDate,
            end: endDate
          };

          const dates = getDatesInRange(dateRange);

          // 予約がキャンセルされていない場合のみInventoryCalendarを更新
          if (booking.status !== 'cancelled') {
            for (const date of dates) {
              // 既存のInventoryCalendarを確認
              const existingInventory = await prisma.inventoryCalendar.findUnique({
                where: {
                  shop_productId_date: {
                    shop: product.shop,
                    productId: product.id,
                    date
                  }
                }
              });

              if (!existingInventory) {
                // InventoryCalendarを作成
                await prisma.inventoryCalendar.create({
                  data: {
                    shop: product.shop,
                    productId: product.id,
                    shopifyProductId: product.shopifyId,
                    date,
                    isAvailable: false,
                    unavailableReason: "booked",
                    bookingId: booking.id,
                    note: `Booked by ${booking.customerName || 'customer'}`
                  }
                });
              } else if (existingInventory.unavailableReason !== "blackout") {
                // ブラックアウト日でない場合のみ更新
                await prisma.inventoryCalendar.update({
                  where: {
                    id: existingInventory.id
                  },
                  data: {
                    isAvailable: false,
                    unavailableReason: "booked",
                    bookingId: booking.id,
                    note: `Booked by ${booking.customerName || 'customer'}`
                  }
                });
              }
            }

            productResult.actions.push(`InventoryCalendarを更新しました: ${dates.length}日`);
            console.log(`InventoryCalendarを更新しました: ${dates.length}日`);
          }
        }
      }

      success++;
      productResult.success = true;
      results.push(productResult);
      console.log(`商品 ${product.id} のカレンダーデータを移行しました`);
    } catch (error) {
      failed++;
      results.push({
        productId: product.id,
        title: product.title || product.shopifyId,
        success: false,
        error: String(error)
      });
      console.error(`商品 ${product.id} の移行中にエラーが発生しました:`, error);
    }
  }

  console.log(`移行完了: 成功=${success}, 失敗=${failed}`);

  return {
    success,
    failed,
    results
  };
}

export async function loader() {
  try {
    const result = await migrateCalendarData();
    return json({ success: true, result });
  } catch (error) {
    console.error("Migration error:", error);
    return json({ success: false, error: String(error) });
  } finally {
    await prisma.$disconnect();
  }
}

export default function MigrateCalendar() {
  const data = useLoaderData<typeof loader>();

  return (
    <Page title="カレンダーデータ移行">
      <BlockStack gap="4">
        {data.success ? (
          <>
            <Card>
              <BlockStack gap="4">
                <Banner status="success" title="移行完了">
                  <p>移行が正常に完了しました。</p>
                </Banner>
                <Box paddingBlockStart="4">
                  <BlockStack gap="2">
                    <Text variant="headingMd" as="h2">移行結果サマリー</Text>
                    <Text variant="bodyMd" as="p">成功: {data.result.success}件</Text>
                    <Text variant="bodyMd" as="p">失敗: {data.result.failed}件</Text>
                  </BlockStack>
                </Box>
              </BlockStack>
            </Card>

            <Box paddingBlockStart="4">
              <Text variant="headingLg" as="h2">詳細結果</Text>
            </Box>

            {data.result.results.map((result, index) => (
              <Card key={index}>
                <BlockStack gap="4">
                  <Text variant="headingMd" as="h3">{result.title} ({result.productId})</Text>
                  {result.success ? (
                    <List type="bullet">
                      {result.actions.map((action, i) => (
                        <List.Item key={i}>{action}</List.Item>
                      ))}
                    </List>
                  ) : (
                    <Banner status="critical">
                      <p>エラー: {result.error}</p>
                    </Banner>
                  )}
                </BlockStack>
              </Card>
            ))}
          </>
        ) : (
          <Card>
            <Banner status="critical" title="エラー">
              <p>移行中にエラーが発生しました: {data.error}</p>
            </Banner>
          </Card>
        )}
      </BlockStack>
    </Page>
  );
}
