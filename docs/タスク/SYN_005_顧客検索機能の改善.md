# 顧客検索機能の改善

**カテゴリ**: Shopify-Prisma同期（SYN）  
**優先度**: 高  
**ステータス**: 未着手  
**担当者**: 未割り当て  
**作成日**: 2025-05-26  
**更新日**: 2025-05-26  

## 概要

現在の顧客検索機能には、以下の問題があります：

1. 「佐藤花子」のようなスペースなしの検索クエリで検索できない
2. 顧客IDでの検索が完全に機能していない場合がある
3. 検索結果の表示順が最適化されていない

これらの問題を解決し、より使いやすい顧客検索機能を実装します。

## 詳細

### 1. スペースなし検索対応

現在、「佐藤 花子」（スペースあり）では検索できますが、「佐藤花子」（スペースなし）では検索できません。これは、Shopifyに保存されている顧客名が「花子 佐藤」のようにスペースを含む形式であるためです。

以下の方法で改善します：

- Shopify Admin APIの検索機能を最大限に活用
- 検索クエリの前処理（スペースの追加/削除、姓名の順序入れ替えなど）
- 検索結果のアプリケーション側でのフィルタリング強化

### 2. 顧客IDでの検索機能の完全対応

顧客IDでの検索が一部のケースで機能していません。以下の方法で改善します：

- 顧客IDの形式チェックと正規化
- 複数の検索方法の組み合わせ（直接ID検索と顧客検索APIの併用）
- エラーハンドリングの強化

### 3. 検索結果の表示順の最適化

現在の検索結果の表示順が最適化されていません。以下の方法で改善します：

- 検索クエリとの関連性に基づく表示順の決定
- 最近の予約を優先表示
- 重複結果の除去と統合

## テスト方法

1. **スペースなし検索テスト**
   - 「佐藤花子」で検索し、「佐藤 花子」の顧客が表示されることを確認
   - 「花子佐藤」で検索し、「花子 佐藤」の顧客が表示されることを確認

2. **顧客ID検索テスト**
   - 顧客ID（例：8418608414888）で検索し、該当顧客が表示されることを確認
   - 存在しない顧客IDで検索し、適切なメッセージが表示されることを確認

3. **表示順テスト**
   - 複数の検索結果がある場合、関連性の高い順に表示されることを確認
   - 最近の予約がある顧客が優先表示されることを確認

## 実装方針

1. **顧客参照サービスの改善**
   - `searchCustomers`メソッドの改善
   - 検索クエリの前処理機能の強化
   - 検索結果のマージと重複除去機能の改善

2. **予約一覧画面の検索機能改善**
   - 検索フォームの改善
   - 検索結果表示の改善
   - 検索結果のソート機能の追加

3. **テストスクリプトの作成**
   - 様々な検索パターンをテストするスクリプトの作成
   - 検索結果の検証機能の追加

## 参考情報

- Shopify Admin API ドキュメント: https://shopify.dev/docs/admin-api/rest/reference/customers/customer
- GraphQL API ドキュメント: https://shopify.dev/docs/admin-api/graphql/reference/customers/customer

## 関連タスク

- SYN_001: Shopify-Prisma同期設計
- SYN_002: 顧客参照サービスの実装
- SYN_003: 同期データの最小化実装
- SYN_004: オンデマンド参照の実装

## 進捗状況

- 2025-05-26: タスク作成
- 2025-05-26: 問題の特定と分析
- 2025-05-26: テストスクリプトの作成と実行
