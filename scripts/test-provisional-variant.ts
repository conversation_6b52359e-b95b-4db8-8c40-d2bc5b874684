/**
 * 仮予約バリエーションテスト
 *
 * このスクリプトは、仮予約バリエーション（10%料金）の作成と動作をテストします。
 * 実行方法: npx tsx scripts/test-provisional-variant.ts [商品ID]
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';
import { addDays, format } from 'date-fns';
import { ja } from 'date-fns/locale';
import { CartService } from '../app/services/cart.service';
import { BookingService } from '../app/services/booking.service';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト設定
const config = {
  // テストする商品のShopify ID
  productId: process.argv[2] || '123456789', // コマンドライン引数から取得、または既定値を使用

  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',

  // テスト用の顧客情報
  customer: {
    email: '<EMAIL>',
    name: 'テストユーザー',
    phone: '090-1234-5678',
    address: '東京都渋谷区'
  },

  // 予約日数（今日から何日後に予約するか）
  startDaysFromNow: 7,
  durationDays: 3
};

/**
 * 商品情報を取得する関数
 */
async function getProductInfo(productIdOrShopifyId: string) {
  try {
    // まずはIDとして検索
    let product = await prisma.product.findUnique({
      where: { id: productIdOrShopifyId }
    });

    // 見つからなければShopify IDとして検索
    if (!product) {
      product = await prisma.product.findFirst({
        where: { shopifyId: productIdOrShopifyId }
      });
    }

    if (!product) {
      console.error(`商品ID/ShopifyID ${productIdOrShopifyId} が見つかりません`);
      return null;
    }

    return product;
  } catch (error) {
    console.error('商品情報取得エラー:', error);
    return null;
  }
}

/**
 * 仮予約バリエーションを作成する関数
 */
async function createProvisionalVariant(product: any) {
  try {
    console.log(`\n商品「${product.title}」の仮予約バリエーションを作成中...`);

    // 基本料金の10%を計算
    const basePrice = product.price || 10000;
    const provisionalPrice = Math.round(basePrice * 0.1);

    console.log(`基本料金: ${basePrice}円`);
    console.log(`仮予約料金（10%）: ${provisionalPrice}円`);

    // 仮予約バリエーションのSKUを作成
    const baseSku = product.sku || 'TEST-001';
    const provisionalSku = `${baseSku}-PROVISIONAL`;

    // 既存の仮予約バリエーションを確認
    const existingVariant = await prisma.product.findFirst({
      where: {
        shop: config.shop,
        sku: provisionalSku
      }
    });

    if (existingVariant) {
      console.log('既存の仮予約バリエーションが見つかりました:');
      console.log(`商品ID: ${existingVariant.id}`);
      console.log(`ShopifyID: ${existingVariant.shopifyId}`);
      console.log(`商品名: ${existingVariant.title}`);
      console.log(`SKU: ${existingVariant.sku}`);
      console.log(`価格: ${existingVariant.price}円`);

      return existingVariant;
    }

    // 仮予約バリエーションを作成
    const provisionalVariant = await prisma.product.create({
      data: {
        id: uuidv4(),
        shop: config.shop,
        shopifyId: `${product.shopifyId}-PROVISIONAL`,
        title: `${product.title}（仮予約）`,
        sku: provisionalSku,
        price: provisionalPrice,
        status: product.status,
        locationId: product.locationId,
        basicInfo: product.basicInfo,
        pricing: product.pricing,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('仮予約バリエーションを作成しました:');
    console.log(`商品ID: ${provisionalVariant.id}`);
    console.log(`ShopifyID: ${provisionalVariant.shopifyId}`);
    console.log(`商品名: ${provisionalVariant.title}`);
    console.log(`SKU: ${provisionalVariant.sku}`);
    console.log(`価格: ${provisionalVariant.price}円`);

    return provisionalVariant;
  } catch (error) {
    console.error('仮予約バリエーション作成エラー:', error);
    return null;
  }
}

/**
 * 仮予約をテストする関数
 */
async function testProvisionalBooking(product: any, provisionalVariant: any) {
  try {
    console.log(`\n仮予約テストを実行中...`);

    // テスト用の日程を設定
    const today = new Date();
    const startDate = addDays(today, config.startDaysFromNow);
    const endDate = addDays(startDate, config.durationDays - 1);

    console.log(`予約期間: ${format(startDate, 'yyyy/MM/dd')} 〜 ${format(endDate, 'yyyy/MM/dd')}`);

    // モックリクエストを作成
    const mockRequest = new Request(`https://${config.shop}/admin/api/2024-01/graphql.json`);

    // CartServiceを初期化
    const cartService = new CartService(mockRequest);

    // 仮予約をカートに追加
    console.log('\n----- 仮予約をカートに追加 -----');

    const cartResult = await cartService.addToCart({
      productId: provisionalVariant.id,
      variantId: '1', // テスト用の固定値
      shopifyProductId: provisionalVariant.shopifyId,
      startDate: format(startDate, 'yyyy-MM-dd'),
      endDate: format(endDate, 'yyyy-MM-dd'),
      customerEmail: config.customer.email,
      customerName: config.customer.name,
      customerPhone: config.customer.phone,
      customerAddress: config.customer.address,
      bookingType: 'provisional',
      notes: 'テスト仮予約'
    });

    if (!cartResult.success) {
      console.error('仮予約のカート追加に失敗しました:', cartResult.error);
      return false;
    }

    console.log('仮予約をカートに追加しました:');
    console.log(`予約ID: ${cartResult.bookingId}`);
    console.log(`カートURL: ${cartResult.cartUrl}`);

    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: cartResult.bookingId }
    });

    if (!booking) {
      console.error('予約情報が見つかりません');
      return false;
    }

    console.log('\n予約情報:');
    console.log(`予約ID: ${booking.id}`);
    console.log(`予約番号: ${booking.bookingId}`);
    console.log(`商品ID: ${booking.productId}`);
    console.log(`予約タイプ: ${booking.bookingType}`);
    console.log(`ステータス: ${booking.status}`);
    console.log(`支払い状態: ${booking.paymentStatus}`);
    console.log(`金額: ${booking.totalAmount}円`);

    // 在庫カレンダー情報を取得
    const inventoryCalendar = await prisma.inventoryCalendar.findMany({
      where: {
        productId: provisionalVariant.id,
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      orderBy: { date: 'asc' }
    });

    console.log('\n在庫カレンダー情報:');
    if (inventoryCalendar.length === 0) {
      console.log('在庫カレンダー情報がありません');
    } else {
      for (const entry of inventoryCalendar) {
        const dateStr = format(new Date(entry.date), 'yyyy/MM/dd(E)', { locale: ja });
        const status = entry.isAvailable ? '○ 利用可能' : '× 利用不可';
        const reason = entry.unavailableReason ? `(${entry.unavailableReason})` : '';
        const bookingInfo = entry.bookingId ? `予約ID: ${entry.bookingId}` : '';
        console.log(`${dateStr}: ${status} ${reason} ${bookingInfo}`);
      }
    }

    // 仮予約から本予約への変更をテスト
    console.log('\n----- 仮予約から本予約への変更 -----');

    // BookingServiceを初期化
    const bookingService = new BookingService();

    // 仮予約を本予約に変更
    const updateResult = await bookingService.updateBookingStatus(booking.id, 'CONFIRMED', {
      notes: 'テスト仮予約から本予約への変更'
    });

    if (!updateResult.success) {
      console.error('仮予約から本予約への変更に失敗しました:', updateResult.error);
      return false;
    }

    console.log('仮予約を本予約に変更しました:');
    console.log(`予約ID: ${updateResult.booking.id}`);
    console.log(`予約タイプ: ${updateResult.booking.bookingType}`);
    console.log(`ステータス: ${updateResult.booking.status}`);

    // 更新後の在庫カレンダー情報を取得
    const updatedInventoryCalendar = await prisma.inventoryCalendar.findMany({
      where: {
        productId: provisionalVariant.id,
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      orderBy: { date: 'asc' }
    });

    console.log('\n更新後の在庫カレンダー情報:');
    if (updatedInventoryCalendar.length === 0) {
      console.log('在庫カレンダー情報がありません');
    } else {
      for (const entry of updatedInventoryCalendar) {
        const dateStr = format(new Date(entry.date), 'yyyy/MM/dd(E)', { locale: ja });
        const status = entry.isAvailable ? '○ 利用可能' : '× 利用不可';
        const reason = entry.unavailableReason ? `(${entry.unavailableReason})` : '';
        const bookingInfo = entry.bookingId ? `予約ID: ${entry.bookingId}` : '';
        console.log(`${dateStr}: ${status} ${reason} ${bookingInfo}`);
      }
    }

    // テスト後のクリーンアップ
    console.log('\n----- テスト後のクリーンアップ -----');

    // 作成した予約を削除
    await prisma.booking.delete({
      where: { id: booking.id }
    });
    console.log(`予約 ${booking.id} を削除しました`);

    // 在庫カレンダーをクリーンアップ
    await prisma.inventoryCalendar.deleteMany({
      where: {
        productId: provisionalVariant.id,
        date: {
          gte: startDate,
          lte: endDate
        }
      }
    });
    console.log('在庫カレンダーをクリーンアップしました');

    return true;
  } catch (error) {
    console.error('仮予約テストエラー:', error);
    return false;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('仮予約バリエーションテストを開始します...');

    // コマンドライン引数から商品IDを取得
    const shopifyProductId = config.productId;

    // 商品情報を取得
    const product = await getProductInfo(shopifyProductId);

    if (!product) {
      console.error('テストを中止します');
      process.exit(1);
    }

    // 仮予約バリエーションを作成
    const provisionalVariant = await createProvisionalVariant(product);

    if (!provisionalVariant) {
      console.error('仮予約バリエーションの作成に失敗しました');
      process.exit(1);
    }

    // 仮予約をテスト
    const testResult = await testProvisionalBooking(product, provisionalVariant);

    // テスト結果のサマリー
    console.log('\n===== テスト結果サマリー =====');
    console.log(`仮予約バリエーションテスト: ${testResult ? '成功' : '失敗'}`);

    if (testResult) {
      console.log('\nテストが成功しました！');
    } else {
      console.error('\nテストが失敗しました。');
      process.exit(1);
    }
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
