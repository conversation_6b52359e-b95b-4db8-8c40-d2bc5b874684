/**
 * レンタル商品カート管理用JavaScript
 *
 * このスクリプトは、レンタル商品のカート機能を管理します。
 * ローカルストレージを使用してカート情報を保存し、
 * カートページでの表示や操作、チェックアウト処理を行います。
 */

class RentalCart {
  constructor() {
    // 設定を読み込んでから初期化
    this.loadConfig().then(() => {
      // カート情報の初期化
      this.cart = this.getCart();

      // カートアイコンの更新
      this.updateCartIcon();

      // イベントリスナーの設定
      this.setupEventListeners();

      console.log("RentalCart initialized with config:", this.config);
    }).catch(error => {
      console.error("Failed to load config:", error);
      // フォールバック設定を使用して初期化
      this.initWithFallbackConfig();
    });
  }

  /**
   * フォールバック設定で初期化
   */
  initWithFallbackConfig() {
    // フォールバック設定
    this.config = {
      appUrl: 'https://app.shopify-app-test.xyz',
      environment: 'development'
    };

    // カート情報の初期化
    this.cart = this.getCart();

    // カートアイコンの更新
    this.updateCartIcon();

    // イベントリスナーの設定
    this.setupEventListeners();

    console.log("RentalCart initialized with fallback config:", this.config);
  }

  /**
   * 設定を読み込む
   */
  async loadConfig() {
    try {
      // テーマから環境設定を取得
      if (window.shopConfig) {
        this.config = window.shopConfig;
        console.log("Config loaded from theme:", this.config);
        return;
      }

      // テーマから設定が取得できない場合はフォールバック設定を使用
      this.config = {
        appUrl: 'https://app.shopify-app-test.xyz',
        environment: 'development'
      };
      console.log("Using fallback config:", this.config);
    } catch (error) {
      console.error("Error loading config:", error);
      // フォールバック設定
      this.config = {
        appUrl: 'https://app.shopify-app-test.xyz',
        environment: 'development'
      };
      console.log("Using fallback config:", this.config);
    }
  }

  /**
   * ローカルストレージからカート情報を取得
   */
  getCart() {
    const cartData = localStorage.getItem('rentalCart');
    return cartData ? JSON.parse(cartData) : {
      items: [],
      startDate: '',
      endDate: '',
      bookingType: 'CONFIRMED'
    };
  }

  /**
   * カート情報をローカルストレージに保存
   */
  saveCart() {
    localStorage.setItem('rentalCart', JSON.stringify(this.cart));
    this.updateCartIcon();
  }

  /**
   * カートアイコンの表示を更新
   */
  updateCartIcon() {
    const itemCount = this.getTotalItemCount();

    // カートアイコンの商品数表示を更新
    const cartCountElements = document.querySelectorAll('.cart-count-bubble, .site-header__cart-count');
    cartCountElements.forEach(element => {
      if (element) {
        // 内部のspan要素を探す
        const span = element.querySelector('span');
        if (span) {
          span.textContent = itemCount.toString();
        } else {
          element.textContent = itemCount.toString();
        }

        // 表示/非表示の切り替え
        element.style.display = itemCount > 0 ? 'block' : 'none';
      }
    });
  }

  /**
   * カート内の商品の合計数を取得
   */
  getTotalItemCount() {
    return this.cart.items.reduce((total, item) => total + (item.quantity || 1), 0);
  }

  /**
   * カート内の商品の合計金額を取得
   */
  getTotalPrice() {
    return this.cart.items.reduce((total, item) => total + (item.price || 0), 0);
  }

  /**
   * 商品をカートに追加
   */
  addItem(item) {
    // 必須項目のチェック
    if (!item.variantId || !item.productId || !item.title) {
      console.error('商品情報が不足しています');
      return false;
    }

    // 日付情報の設定（最初の商品の日付を使用）
    if (!this.cart.startDate || !this.cart.endDate) {
      this.cart.startDate = item.startDate;
      this.cart.endDate = item.endDate;
      this.cart.bookingType = item.bookingType || 'CONFIRMED';
    }

    // 既存の商品かどうかをチェック
    const existingItemIndex = this.cart.items.findIndex(i => i.variantId === item.variantId);

    if (existingItemIndex >= 0) {
      // 既存の商品の場合は数量を増やす
      this.cart.items[existingItemIndex].quantity = (this.cart.items[existingItemIndex].quantity || 1) + (item.quantity || 1);
      this.cart.items[existingItemIndex].price += item.price;
    } else {
      // 新しい商品の場合はカートに追加
      // 数量が指定されていない場合は1を設定
      if (!item.quantity) {
        item.quantity = 1;
      }
      this.cart.items.push(item);
    }

    // カートを保存
    this.saveCart();
    return true;
  }

  /**
   * カートから商品を削除
   */
  removeItem(index) {
    if (index >= 0 && index < this.cart.items.length) {
      this.cart.items.splice(index, 1);

      // カートが空になった場合は日付情報もクリア
      if (this.cart.items.length === 0) {
        this.cart.startDate = '';
        this.cart.endDate = '';
      }

      this.saveCart();
      return true;
    }
    return false;
  }

  /**
   * 商品の数量を更新
   */
  updateItemQuantity(index, quantity) {
    if (index >= 0 && index < this.cart.items.length && quantity > 0) {
      const item = this.cart.items[index];
      const oldQuantity = item.quantity || 1;
      const pricePerUnit = item.price / oldQuantity;

      item.quantity = quantity;
      item.price = pricePerUnit * quantity;

      this.saveCart();
      return true;
    }
    return false;
  }

  /**
   * カートをクリア
   */
  clearCart() {
    this.cart = {
      items: [],
      startDate: '',
      endDate: '',
      bookingType: 'CONFIRMED'
    };
    this.saveCart();
  }

  /**
   * ドラフトオーダーを作成（チェックアウト）
   */
  async createDraftOrder() {
    try {
      // カートが空の場合はエラー
      if (!this.cart.items || this.cart.items.length === 0) {
        throw new Error('カートに商品がありません');
      }

      // 日付情報が不足している場合はエラー
      if (!this.cart.startDate || !this.cart.endDate) {
        throw new Error('レンタル期間が設定されていません');
      }

      // APIリクエスト
      const response = await fetch(`${this.config.appUrl}/api/create-draft-order-from-cart`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          items: this.cart.items,
          startDate: this.cart.startDate,
          endDate: this.cart.endDate,
          bookingType: this.cart.bookingType || 'CONFIRMED'
        })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'ドラフトオーダーの作成に失敗しました');
      }

      // 成功した場合、カートをクリアして請求書ページにリダイレクト
      this.clearCart();

      return {
        success: true,
        draftOrder: data.draftOrder
      };
    } catch (error) {
      console.error('ドラフトオーダー作成エラー:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * イベントリスナーの設定
   */
  setupEventListeners() {
    // カートページでのみ実行
    if (window.location.pathname.includes('/pages/rental-cart')) {
      document.addEventListener('DOMContentLoaded', () => {
        // 削除ボタンのイベントリスナー
        document.querySelectorAll('[data-remove-item]').forEach(button => {
          button.addEventListener('click', (event) => {
            const index = parseInt(event.currentTarget.getAttribute('data-remove-item'));
            this.removeItem(index);
            window.location.reload();
          });
        });

        // 数量変更のイベントリスナー
        document.querySelectorAll('[data-quantity-input]').forEach(input => {
          input.addEventListener('change', (event) => {
            const index = parseInt(event.currentTarget.getAttribute('data-quantity-input'));
            const quantity = parseInt(event.currentTarget.value);
            if (quantity > 0) {
              this.updateItemQuantity(index, quantity);
              window.location.reload();
            }
          });
        });

        // チェックアウトボタンのイベントリスナー
        const checkoutButton = document.getElementById('checkout-button');
        if (checkoutButton) {
          checkoutButton.addEventListener('click', async () => {
            // ローディング表示
            document.getElementById('loading-indicator').style.display = 'block';
            document.getElementById('error-message').style.display = 'none';

            const result = await this.createDraftOrder();

            if (result.success) {
              // 請求書URLがある場合はそちらにリダイレクト
              if (result.draftOrder && result.draftOrder.invoiceUrl) {
                window.location.href = result.draftOrder.invoiceUrl;
              } else {
                // 請求書URLがない場合はカートページにリダイレクト
                window.location.href = '/cart';
              }
            } else {
              // エラー表示
              document.getElementById('error-message').textContent = result.error;
              document.getElementById('error-message').style.display = 'block';
              document.getElementById('loading-indicator').style.display = 'none';
            }
          });
        }
      });
    }
  }
}

// グローバルオブジェクトとして初期化
window.rentalCart = new RentalCart();
