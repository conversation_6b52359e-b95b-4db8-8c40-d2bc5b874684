/**
 * 顧客IDマイグレーションスクリプト
 * 
 * このスクリプトは、既存の予約データに顧客IDを設定します。
 * 顧客名とメールアドレスを使用して、Shopify APIから顧客IDを取得します。
 */

import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

// 顧客検索のGraphQLクエリ
const SEARCH_CUSTOMERS = gql`
  query searchCustomers($query: String!, $first: Int!) {
    customers(first: $first, query: $query) {
      edges {
        node {
          id
          firstName
          lastName
          email
          phone
        }
      }
    }
  }
`;

/**
 * メインの実行関数
 */
async function main() {
  console.log('顧客IDマイグレーションを開始します...');

  try {
    // 顧客IDが設定されていない予約を取得
    const bookings = await prisma.booking.findMany({
      where: {
        customerId: null,
        OR: [
          { customerName: { not: null } },
          { customerEmail: { not: null } }
        ]
      },
      select: {
        id: true,
        customerName: true,
        customerEmail: true,
        shop: true
      }
    });

    console.log(`${bookings.length}件の予約データを処理します...`);

    // 結果を記録するオブジェクト
    const results = {
      total: bookings.length,
      updated: 0,
      skipped: 0,
      errors: 0
    };

    // 各予約を処理
    for (const booking of bookings) {
      try {
        // 検索クエリを構築
        let searchQuery = '';
        if (booking.customerEmail) {
          searchQuery = `email:${booking.customerEmail}`;
        } else if (booking.customerName) {
          searchQuery = `name:${booking.customerName}`;
        } else {
          console.log(`予約ID ${booking.id}: 顧客名とメールアドレスがないためスキップします`);
          results.skipped++;
          continue;
        }

        // Shopify APIで顧客を検索
        const response = await client.request(SEARCH_CUSTOMERS, {
          query: searchQuery,
          first: 1
        });

        // 検索結果を確認
        const customers = response.customers.edges;
        if (customers.length === 0) {
          console.log(`予約ID ${booking.id}: 顧客が見つかりませんでした (検索: ${searchQuery})`);
          results.skipped++;
          continue;
        }

        // 顧客IDを取得
        const customerId = customers[0].node.id.replace('gid://shopify/Customer/', '');
        
        // 予約データを更新
        await prisma.booking.update({
          where: { id: booking.id },
          data: { customerId }
        });

        console.log(`予約ID ${booking.id}: 顧客ID ${customerId} を設定しました`);
        results.updated++;

        // API制限を考慮して少し待機
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`予約ID ${booking.id} の処理中にエラーが発生しました:`, error);
        results.errors++;
      }
    }

    // 結果を表示
    console.log('\n=== マイグレーション結果 ===');
    console.log(`合計: ${results.total}件`);
    console.log(`更新: ${results.updated}件`);
    console.log(`スキップ: ${results.skipped}件`);
    console.log(`エラー: ${results.errors}件`);
    console.log('===========================\n');

    console.log('顧客IDマイグレーションが完了しました');
  } catch (error) {
    console.error('マイグレーション中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('予期しないエラーが発生しました:', error);
    process.exit(1);
  });
