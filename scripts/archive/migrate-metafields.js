/**
 * メタフィールド移行スクリプト
 *
 * このスクリプトは、既存のJSONメタフィールドから
 * 新しい単純なフィールドにデータを移行します。
 *
 * 使用方法:
 * 1. .env ファイルに SHOPIFY_ADMIN_API_ACCESS_TOKEN と SHOPIFY_SHOP を設定
 * 2. node scripts/migrate-metafields.js を実行
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

// 環境変数の読み込み
dotenv.config();

// 環境変数名を実際の.envファイルに合わせて変更
const SHOP_URL = process.env.SHOPIFY_SHOP;
const ADMIN_API_ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;

// GraphQL APIエンドポイント
const API_URL = `https://${SHOP_URL}/admin/api/2023-10/graphql.json`;

// 商品の取得用GraphQLクエリ
const GET_PRODUCTS_WITH_METAFIELDS = `
  query GetProductsWithMetafields($cursor: String) {
    products(first: 10, after: $cursor) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          title
          metafields(first: 20) {
            edges {
              node {
                id
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    }
  }
`;

// メタフィールド作成用GraphQLミューテーション
const CREATE_METAFIELD = `
  mutation CreateMetafield($metafield: MetafieldsSetInput!) {
    metafieldsSet(metafields: [$metafield]) {
      metafields {
        id
        namespace
        key
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// GraphQL APIを呼び出す関数
async function callGraphQL(query, variables) {
  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ADMIN_API_ACCESS_TOKEN,
      },
      body: JSON.stringify({
        query,
        variables,
      }),
    });

    const result = await response.json();

    if (result.errors) {
      console.error('GraphQL Errors:', result.errors);
      return null;
    }

    return result.data;
  } catch (error) {
    console.error('API Call Error:', error);
    return null;
  }
}

// メタフィールドを作成する関数
async function createMetafield(ownerId, namespace, key, value, type) {
  console.log(`Creating metafield: ${namespace}.${key} for ${ownerId}`);

  const data = await callGraphQL(CREATE_METAFIELD, {
    metafield: {
      ownerId,
      namespace,
      key,
      value: String(value),
      type,
    },
  });

  if (data && data.metafieldsSet) {
    if (data.metafieldsSet.userErrors.length > 0) {
      console.error('Error creating metafield:', data.metafieldsSet.userErrors);
      return false;
    }

    console.log(`Successfully created metafield: ${namespace}.${key}`);
    return true;
  }

  return false;
}

// 全商品を取得する関数
async function getAllProducts() {
  let hasNextPage = true;
  let cursor = null;
  let allProducts = [];

  while (hasNextPage) {
    const data = await callGraphQL(GET_PRODUCTS_WITH_METAFIELDS, { cursor });

    if (!data || !data.products) {
      break;
    }

    const products = data.products.edges.map(edge => edge.node);
    allProducts = [...allProducts, ...products];

    hasNextPage = data.products.pageInfo.hasNextPage;
    cursor = data.products.pageInfo.endCursor;
  }

  return allProducts;
}

// メタフィールドを移行する関数
async function migrateMetafields(product) {
  console.log(`\nMigrating metafields for product: ${product.title}`);

  const metafields = product.metafields.edges.map(edge => edge.node);
  let migratedCount = 0;

  // メンテナンス情報の移行
  const maintenanceInfo = metafields.find(m => m.namespace === 'rental' && m.key === 'maintenance_info');
  if (maintenanceInfo && maintenanceInfo.type === 'json') {
    try {
      const data = JSON.parse(maintenanceInfo.value);

      // 最終メンテナンス日
      if (data.lastMaintenanceDate) {
        const success = await createMetafield(
          product.id,
          'rental',
          'last_maintenance_date',
          data.lastMaintenanceDate,
          'date'
        );
        if (success) migratedCount++;
      }

      // 次回メンテナンス日
      if (data.nextMaintenanceDate) {
        const success = await createMetafield(
          product.id,
          'rental',
          'next_maintenance_date',
          data.nextMaintenanceDate,
          'date'
        );
        if (success) migratedCount++;
      }

      // メンテナンス状態
      if (data.maintenanceStatus) {
        const success = await createMetafield(
          product.id,
          'rental',
          'maintenance_status',
          data.maintenanceStatus,
          'single_line_text_field'
        );
        if (success) migratedCount++;
      }

      // メンテナンス備考
      if (data.maintenanceNotes) {
        const success = await createMetafield(
          product.id,
          'rental',
          'maintenance_notes',
          data.maintenanceNotes,
          'multi_line_text_field'
        );
        if (success) migratedCount++;
      }
    } catch (error) {
      console.error(`Error parsing maintenance_info for ${product.title}:`, error);
    }
  }

  // 購入情報の移行
  const purchaseInfo = metafields.find(m => m.namespace === 'rental' && m.key === 'purchase_info');
  if (purchaseInfo && purchaseInfo.type === 'json') {
    try {
      const data = JSON.parse(purchaseInfo.value);

      // メーカー
      if (data.manufacturer) {
        const success = await createMetafield(
          product.id,
          'rental',
          'manufacturer',
          data.manufacturer,
          'single_line_text_field'
        );
        if (success) migratedCount++;
      }

      // 購入場所
      if (data.purchasePlace) {
        const success = await createMetafield(
          product.id,
          'rental',
          'purchase_place',
          data.purchasePlace,
          'single_line_text_field'
        );
        if (success) migratedCount++;
      }

      // 購入日
      if (data.purchaseDate) {
        const success = await createMetafield(
          product.id,
          'rental',
          'purchase_date',
          data.purchaseDate,
          'date'
        );
        if (success) migratedCount++;
      }

      // 購入価格
      if (data.purchasePrice) {
        const success = await createMetafield(
          product.id,
          'rental',
          'purchase_price',
          data.purchasePrice,
          'number_decimal'
        );
        if (success) migratedCount++;
      }
    } catch (error) {
      console.error(`Error parsing purchase_info for ${product.title}:`, error);
    }
  }

  // 廃棄情報の移行
  const disposalInfo = metafields.find(m => m.namespace === 'rental' && m.key === 'disposal_info');
  if (disposalInfo && disposalInfo.type === 'json') {
    try {
      const data = JSON.parse(disposalInfo.value);

      // 廃棄済みフラグ
      if (data.isDisposed !== undefined) {
        const success = await createMetafield(
          product.id,
          'rental',
          'is_disposed',
          data.isDisposed,
          'boolean'
        );
        if (success) migratedCount++;
      }

      // 廃棄日
      if (data.disposalDate) {
        const success = await createMetafield(
          product.id,
          'rental',
          'disposal_date',
          data.disposalDate,
          'date'
        );
        if (success) migratedCount++;
      }

      // 廃棄理由
      if (data.disposalReason) {
        const success = await createMetafield(
          product.id,
          'rental',
          'disposal_reason',
          data.disposalReason,
          'multi_line_text_field'
        );
        if (success) migratedCount++;
      }
    } catch (error) {
      console.error(`Error parsing disposal_info for ${product.title}:`, error);
    }
  }

  return migratedCount;
}

// メインの実行関数
async function main() {
  console.log('Starting metafield migration...');

  // 全商品を取得
  const products = await getAllProducts();
  console.log(`Found ${products.length} products`);

  let totalMigrated = 0;

  // 各商品のメタフィールドを移行
  for (const product of products) {
    const migratedCount = await migrateMetafields(product);
    totalMigrated += migratedCount;

    // APIレート制限を避けるための遅延
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\nMetafield migration completed:');
  console.log(`- Total products processed: ${products.length}`);
  console.log(`- Total metafields migrated: ${totalMigrated}`);
}

// スクリプトの実行
main().catch(error => {
  console.error('Script execution error:', error);
  process.exit(1);
});
