# フロントエンド予約・注文テストシステム実装計画

## メタデータ
- **タスクID**: FE_001
- **作成日**: 2025-05-22
- **優先度**: 最高
- **ステータス**: 🚀 開始準備中
- **予定期間**: 4-6日

## 概要

フロントエンドでの予約・注文フローをテストし、Prismaとの同期を確認するシステムを実装します。また、既存のアプリブロックを改良した新しいアプリブロックを作成し、段階的に置き換えを行います。

## 目的

1. **フロントエンドテストの実現**: 実際のユーザーフローでの予約・注文テスト
2. **Prisma同期確認**: Shopify注文からPrismaデータベースへの同期確認
3. **新アプリブロックの実装**: 改良された予約フロー
4. **既存システムの置き換え**: 段階的な移行とリスク軽減

## 実装計画

### フェーズ1: フロントエンドテスト環境の構築（1-2日）

#### 1.1 既存テーマブロックでの予約フローテスト
- **目的**: 現在のフロントエンド機能の動作確認
- **実装内容**:
  - 既存のdate-picker-theme-blockの動作確認
  - 商品ページでの日付選択テスト
  - カート追加からチェックアウトまでのフローテスト
  - 注文完了後のデータ確認

#### 1.2 Shopify注文作成からPrisma同期確認テスト
- **目的**: 注文ステータス同期システムの動作確認
- **実装内容**:
  - フロントエンドからの注文作成
  - Webhookによる予約データ作成確認
  - Prismaデータベースでの同期状況確認
  - 注文ステータス変更時の同期確認

#### 1.3 リアルタイム在庫状況確認テスト
- **目的**: 在庫カレンダーの正確性確認
- **実装内容**:
  - 予約作成時の在庫カレンダー更新確認
  - 複数商品での在庫状況表示確認
  - 予約キャンセル時の在庫復元確認

### フェーズ2: 新しいアプリブロックの作成（2-3日）

#### 2.1 新アプリブロック設計
- **名前**: `rental-booking-block-v2`
- **機能**:
  - 改良された予約フロー
  - リアルタイム在庫確認
  - エラーハンドリングの強化
  - レスポンシブデザイン対応

#### 2.2 改良された予約フロー実装
- **実装内容**:
  - 統一カレンダーコンポーネントの活用
  - 日付選択時のリアルタイム料金計算
  - 在庫状況の視覚的表示
  - 予約タイプ選択（仮予約/本予約）

#### 2.3 リアルタイム在庫確認機能
- **実装内容**:
  - APIエンドポイントの作成
  - 在庫状況のリアルタイム取得
  - 予約済み日付の表示
  - メンテナンス期間の表示

#### 2.4 Shopify注文との完全連携
- **実装内容**:
  - 注文プロパティの最適化
  - 予約IDの確実な連携
  - メタフィールドとの同期
  - エラー時の適切な処理

### フェーズ3: 既存アプリブロックの置き換え（1日）

#### 3.1 新アプリブロックのテストと検証
- **実装内容**:
  - 機能テストの実行
  - パフォーマンステスト
  - ユーザビリティテスト
  - クロスブラウザテスト

#### 3.2 既存アプリブロックの非表示化または削除
- **実装内容**:
  - 段階的な移行計画
  - 既存ブロックの無効化
  - 新ブロックの有効化
  - 移行後の動作確認

#### 3.3 本番環境での動作確認
- **実装内容**:
  - 本番環境でのテスト
  - パフォーマンス監視
  - エラー監視の設定
  - ユーザーフィードバックの収集

## 技術仕様

### 新アプリブロック技術スタック
- **フレームワーク**: Shopify CLI 3.x
- **言語**: JavaScript/TypeScript
- **UI**: Shopify Polaris
- **API**: Shopify Admin API 2025-01
- **データベース**: Prisma + Neon PostgreSQL

### APIエンドポイント設計

#### 在庫確認API
```
GET /api/inventory/check
Parameters:
- productId: string
- startDate: string (YYYY-MM-DD)
- endDate: string (YYYY-MM-DD)
- variantId?: string

Response:
{
  available: boolean,
  conflicts: Array<{
    date: string,
    type: 'booking' | 'maintenance',
    details: object
  }>,
  pricing: {
    totalPrice: number,
    dailyRate: number,
    discountRate: number
  }
}
```

#### 予約作成API
```
POST /api/bookings/create-from-frontend
Body:
{
  productId: string,
  variantId?: string,
  startDate: string,
  endDate: string,
  bookingType: 'PROVISIONAL' | 'CONFIRMED',
  customerInfo: {
    email: string,
    name: string,
    phone?: string
  },
  notes?: string
}

Response:
{
  success: boolean,
  bookingId?: string,
  cartUrl?: string,
  error?: string
}
```

## テスト計画

### 1. 機能テスト
- **日付選択機能**: カレンダーでの日付選択
- **在庫確認機能**: リアルタイム在庫状況表示
- **料金計算機能**: 正確な料金計算
- **予約作成機能**: 予約データの作成
- **注文連携機能**: Shopify注文との連携

### 2. 統合テスト
- **フロントエンド→バックエンド**: API呼び出し
- **バックエンド→データベース**: データ保存
- **Webhook→同期**: 注文同期処理
- **エラーハンドリング**: 異常系の処理

### 3. E2Eテスト
- **予約フロー全体**: 商品選択から注文完了まで
- **同期確認**: 注文からPrisma同期まで
- **キャンセルフロー**: 予約キャンセルから在庫復元まで

## 成功指標

### 機能的指標
- [ ] フロントエンドでの予約作成成功率: 95%以上
- [ ] Shopify注文からPrisma同期成功率: 100%
- [ ] 在庫状況表示の正確性: 100%
- [ ] 新アプリブロックの機能完全性: 100%

### パフォーマンス指標
- [ ] 在庫確認API応答時間: 500ms以下
- [ ] 予約作成処理時間: 2秒以下
- [ ] ページ読み込み時間: 3秒以下
- [ ] エラー率: 1%以下

### ユーザビリティ指標
- [ ] 日付選択の直感性: 良好
- [ ] エラーメッセージの分かりやすさ: 良好
- [ ] レスポンシブデザイン対応: 完全対応
- [ ] アクセシビリティ: WCAG 2.1 AA準拠

## リスク管理

### 技術的リスク
- **既存システムとの競合**: 段階的移行で対応
- **パフォーマンス問題**: 事前テストで対応
- **ブラウザ互換性**: クロスブラウザテストで対応

### 運用リスク
- **ユーザー混乱**: 明確な移行計画で対応
- **データ不整合**: 同期確認テストで対応
- **ダウンタイム**: 段階的移行で最小化

## 実装ファイル一覧

### 新アプリブロック
- `extensions/rental-booking-block-v2/`
  - `shopify.extension.toml`
  - `src/index.js`
  - `src/components/DatePicker.js`
  - `src/components/InventoryChecker.js`
  - `src/components/PriceCalculator.js`
  - `src/styles/main.css`

### APIエンドポイント
- `app/routes/api.inventory.check.tsx`
- `app/routes/api.bookings.create-from-frontend.tsx`
- `app/routes/api.frontend.test.tsx`

### テストスクリプト
- `scripts/test-frontend-booking-flow.ts`
- `scripts/test-prisma-sync.ts`
- `scripts/test-app-block-migration.ts`

## 次のステップ

1. **フェーズ1開始**: 既存テーマブロックでのテスト実行
2. **環境確認**: 開発環境とテスト環境の準備
3. **テストデータ準備**: テスト用商品とデータの準備
4. **実装開始**: 新アプリブロックの開発開始

このタスクの完了により、フロントエンドでの予約・注文フローが完全にテスト可能になり、新しいアプリブロックによる改良された予約体験が提供されます。
