/**
 * 予約注文作成ユーティリティ
 *
 * このモジュールは、予約情報をShopify注文として作成するための関数を提供します。
 */

import { Booking, PrismaClient, Product } from '@prisma/client';
import { format } from 'date-fns';
import { ErrorType, ErrorInfo, handleError, retry, getFriendlyErrorMessage } from './error-handler';
import { AdminApiContext } from '@shopify/shopify-app-remix/server';

// 通常注文作成ミューテーション
const CREATE_ORDER = `
  mutation orderCreate($order: OrderCreateOrderInput!) {
    orderCreate(order: $order) {
      order {
        id
        name
        totalPrice
        customer {
          id
          email
        }
        lineItems(first: 10) {
          edges {
            node {
              title
              quantity
              originalUnitPrice
              variant {
                id
                title
              }
            }
          }
        }
        metafields(first: 10) {
          edges {
            node {
              namespace
              key
              value
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// ドラフト注文作成ミューテーション（バックアップ用）
const CREATE_DRAFT_ORDER = `
  mutation draftOrderCreate($input: DraftOrderInput!) {
    draftOrderCreate(input: $input) {
      draftOrder {
        id
        name
        totalPrice
        customer {
          id
          email
        }
        lineItems(first: 10) {
          edges {
            node {
              title
              quantity
              originalUnitPrice
              variant {
                id
                title
              }
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// ドラフト注文完了ミューテーション（バックアップ用）
const COMPLETE_DRAFT_ORDER = `
  mutation draftOrderComplete($id: ID!, $paymentPending: Boolean) {
    draftOrderComplete(id: $id, paymentPending: $paymentPending) {
      draftOrder {
        id
        name
        # 注意: orderフィールドにアクセスするには read_orders スコープが必要
        # 権限の問題で取得できない場合があるため、コメントアウト
        # order {
        #   id
        #   name
        #   totalPrice
        # }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

/**
 * 注文作成結果の型定義
 */
export interface OrderCreationResult {
  success: boolean;
  orderId?: string;
  orderName?: string;
  isDraftOrder?: boolean;
  isExisting?: boolean;
  error?: ErrorInfo;
  errorMessage?: string;
}

/**
 * 予約情報をShopify注文として作成する関数
 * @param prisma Prismaクライアント
 * @param admin Shopify管理APIコンテキスト
 * @param bookingId 予約ID
 * @param retryCount リトライ回数
 * @returns 作成された注文情報
 */
export async function createOrderFromBooking(
  prisma: PrismaClient,
  admin: AdminApiContext,
  bookingId: string,
  retryCount: number = 3
): Promise<OrderCreationResult> {
  try {
    console.log('予約から注文を作成中...');

    // 予約情報を取得
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        product: true
      }
    });

    if (!booking) {
      throw new Error(`予約ID ${bookingId} が見つかりません`);
    }

    // 既に注文が関連付けられているかチェック
    if (booking.orderId) {
      console.log(`予約ID ${bookingId} には既に注文ID ${booking.orderId} が関連付けられています。`);
      return {
        success: true,
        orderId: booking.orderId,
        orderName: booking.orderName || '不明',
        isDraftOrder: false,
        isExisting: true
      };
    }

    console.log(`予約情報を取得しました: ${booking.bookingId}`);

    // 顧客IDを正規化
    const customerId = booking.customerId.startsWith('gid://shopify/Customer/')
      ? booking.customerId
      : `gid://shopify/Customer/${booking.customerId}`;

    // 商品IDを正規化
    const productId = booking.product.shopifyId.startsWith('gid://shopify/Product/')
      ? booking.product.shopifyId
      : `gid://shopify/Product/${booking.product.shopifyId}`;

    // バリアントIDを正規化
    const variantId = booking.variantId
      ? (booking.variantId.startsWith('gid://shopify/ProductVariant/')
          ? booking.variantId
          : `gid://shopify/ProductVariant/${booking.variantId}`)
      : null;

    console.log(`正規化されたID: 顧客=${customerId}, 商品=${productId}, バリアント=${variantId}`);

    // 予約期間を計算（日本時間で表示）
    const startDate = format(booking.startDate, 'yyyy-MM-dd', { timeZone: 'Asia/Tokyo' });
    const endDate = format(booking.endDate, 'yyyy-MM-dd', { timeZone: 'Asia/Tokyo' });
    const rentalPeriod = `${startDate} 〜 ${endDate}`;

    console.log(`予約期間（日本時間）: ${rentalPeriod}`);

    // 注文作成用の入力データを作成
    const orderInput = {
      customerId: customerId,
      lineItems: [
        {
          // variantIdまたはtitle+priceSetのいずれかが必要
          title: `${booking.product.title} (レンタル: ${rentalPeriod})`,
          quantity: 1,
          taxable: true,
          requiresShipping: false,
          // 正規化されたバリアントIDを使用
          variantId: variantId,
          // 価格設定
          priceSet: {
            shopMoney: {
              amount: booking.bookingType === 'PROVISIONAL'
                ? booking.depositAmount.toString() // 仮予約の場合はデポジット金額
                : booking.totalAmount.toString(), // 確定予約の場合は全額
              currencyCode: "JPY"
            }
          }
        }
      ],
      tags: ['rental', booking.bookingType.toLowerCase(), `booking-${booking.bookingId}`],
      note: `予約ID: ${booking.bookingId}\n予約期間: ${rentalPeriod}\n備考: ${booking.notes || 'なし'}`
    };

    console.log('注文作成データ:', JSON.stringify(orderInput, null, 2));

    // 注文を作成（リトライ機能付き）
    const result = await retry(
      async () => {
        console.log('GraphQL呼び出しを実行します (注文作成)...');
        console.log(`注文作成を開始します: 予約ID=${bookingId}, 顧客ID=${customerId}, 商品ID=${productId}, バリアントID=${variantId}`);
        console.log('注文作成データ:', JSON.stringify(orderInput, null, 2));

        try {
          // 通常注文の作成を試みる
          console.log('通常注文作成を実行します...');

          try {
            // 通常注文作成ミューテーションを実行
            console.log('通常注文作成ミューテーション:', CREATE_ORDER);
            console.log('通常注文作成変数:', JSON.stringify({ order: orderInput }, null, 2));

            const orderResponse = await admin.graphql(
              CREATE_ORDER,
              {
                variables: {
                  order: orderInput
                }
              }
            );

            const orderResult = await orderResponse.json();
            console.log('通常注文応答:', JSON.stringify(orderResult, null, 2));

            // GraphQLエラーをチェック
            if (orderResult.errors) {
              console.error('GraphQLエラー:', orderResult.errors);
              throw new Error(`GraphQLエラー: ${JSON.stringify(orderResult.errors)}`);
            }

            if (orderResult.data?.orderCreate?.userErrors?.length > 0) {
              console.error('通常注文作成エラー:', orderResult.data.orderCreate.userErrors);
              throw new Error(`通常注文の作成中にエラーが発生しました: ${JSON.stringify(orderResult.data.orderCreate.userErrors)}`);
            }

            // 注文IDを取得
            const orderId = orderResult.data.orderCreate.order.id;
            const orderName = orderResult.data.orderCreate.order.name;

            // 予約情報を更新（注文IDを保存）
            await prisma.booking.update({
              where: { id: booking.id },
              data: {
                orderId: orderId,
                orderName: orderName
              }
            });

            console.log(`予約情報を更新しました: 注文ID ${orderId}, 注文番号 ${orderName}`);
            console.log(`注文作成が完了しました: 注文ID=${orderId}, 注文番号=${orderName}, 予約ID=${bookingId}`);

            return {
              success: true,
              orderId,
              orderName,
              isDraftOrder: false
            };
          } catch (directOrderError) {
            // 通常注文作成に失敗した場合のエラー詳細を記録
            console.error('通常注文作成に失敗しました:', directOrderError);
            if (directOrderError instanceof Error) {
              console.error('エラーメッセージ:', directOrderError.message);
              console.error('スタックトレース:', directOrderError.stack);
            }

            // エラーの種類に応じた処理
            if (directOrderError.message && directOrderError.message.includes('access scope')) {
              console.error('APIアクセススコープが不足しています。必要なスコープ: write_orders');
              throw new Error(`APIアクセススコープが不足しています: ${directOrderError.message}`);
            }

            // ドラフト注文作成を試みる
            console.log('ドラフト注文作成を実行します...');

            // ドラフト注文用の入力データを作成
            const draftOrderInput = {
              ...orderInput,
              lineItems: orderInput.lineItems.map(item => ({
                ...item,
                // ドラフト注文ではpriceSetではなくoriginalUnitPriceWithCurrencyを使用
                originalUnitPriceWithCurrency: {
                  amount: item.priceSet.shopMoney.amount,
                  currencyCode: item.priceSet.shopMoney.currencyCode
                },
                priceSet: undefined
              })),
              // メタフィールドを追加して予約IDを関連付ける
              metafields: [
                {
                  namespace: "custom",
                  key: "booking_id",
                  value: booking.id,
                  type: "single_line_text_field"
                },
                {
                  namespace: "custom",
                  key: "booking_number",
                  value: booking.bookingId,
                  type: "single_line_text_field"
                },
                {
                  namespace: "custom",
                  key: "booking_type",
                  value: booking.bookingType,
                  type: "single_line_text_field"
                },
                {
                  namespace: "custom",
                  key: "rental_period",
                  value: rentalPeriod,
                  type: "single_line_text_field"
                }
              ]
            };

            console.log('ドラフト注文作成ミューテーション:', CREATE_DRAFT_ORDER);
            console.log('ドラフト注文作成変数:', JSON.stringify({ input: draftOrderInput }, null, 2));

            const draftResponse = await admin.graphql(
              CREATE_DRAFT_ORDER,
              {
                variables: {
                  input: draftOrderInput
                }
              }
            );

            const draftResult = await draftResponse.json();
            console.log('ドラフト注文応答:', JSON.stringify(draftResult, null, 2));

            // GraphQLエラーをチェック
            if (draftResult.errors) {
              console.error('GraphQLエラー:', draftResult.errors);
              throw new Error(`GraphQLエラー: ${JSON.stringify(draftResult.errors)}`);
            }

            if (draftResult.data?.draftOrderCreate?.userErrors?.length > 0) {
              console.error('ドラフト注文作成エラー:', draftResult.data.draftOrderCreate.userErrors);
              throw new Error(`ドラフト注文の作成中にエラーが発生しました: ${JSON.stringify(draftResult.data.draftOrderCreate.userErrors)}`);
            }

            // 作成されたドラフト注文を完了状態にする
            const draftOrderId = draftResult.data.draftOrderCreate.draftOrder.id;
            console.log(`ドラフト注文ID: ${draftOrderId} を完了状態にします`);

            console.log('ドラフト注文完了ミューテーション:', COMPLETE_DRAFT_ORDER);
            console.log('ドラフト注文完了変数:', JSON.stringify({
              id: draftOrderId,
              paymentPending: booking.bookingType === 'PROVISIONAL'
            }, null, 2));

            const completeResponse = await admin.graphql(
              COMPLETE_DRAFT_ORDER,
              {
                variables: {
                  id: draftOrderId,
                  paymentPending: booking.bookingType === 'PROVISIONAL' // 仮予約の場合は支払い保留
                }
              }
            );

            const completeResult = await completeResponse.json();
            console.log('ドラフト注文完了応答:', JSON.stringify(completeResult, null, 2));

            // GraphQLエラーをチェック
            if (completeResult.errors) {
              console.error('GraphQLエラー:', completeResult.errors);
              throw new Error(`GraphQLエラー: ${JSON.stringify(completeResult.errors)}`);
            }

            if (completeResult.data?.draftOrderComplete?.userErrors?.length > 0) {
              console.error('ドラフト注文完了エラー:', completeResult.data.draftOrderComplete.userErrors);
              throw new Error(`ドラフト注文の完了中にエラーが発生しました: ${JSON.stringify(completeResult.data.draftOrderComplete.userErrors)}`);
            }

            // ドラフト注文名を取得
            const completedDraftOrderId = completeResult.data.draftOrderComplete.draftOrder.id;
            const draftOrderName = completeResult.data.draftOrderComplete.draftOrder.name;

            // ドラフト注文名から注文名を推測（#D123 → #123）
            const orderName = draftOrderName.replace('#D', '#');

            // 注文IDを構築（実際の注文IDは取得できないため、ドラフト注文IDから推測）
            const orderId = completedDraftOrderId.replace('DraftOrder', 'Order');

            // 予約情報を更新（注文IDを保存）
            await prisma.booking.update({
              where: { id: booking.id },
              data: {
                orderId: orderId,
                orderName: orderName
              }
            });

            console.log(`予約情報を更新しました: 注文ID ${orderId}, 注文番号 ${orderName}`);
            console.log(`ドラフト注文作成が完了しました: 注文ID=${orderId}, 注文番号=${orderName}, 予約ID=${bookingId}`);

            return {
              success: true,
              orderId,
              orderName,
              isDraftOrder: true
            };
          }
        } catch (graphqlError) {
          console.error('GraphQL呼び出しエラー:', graphqlError);
          if (graphqlError instanceof Error) {
            console.error('エラーメッセージ:', graphqlError.message);
            console.error('スタックトレース:', graphqlError.stack);
          }
          throw graphqlError;
        }
      },
      retryCount,
      1000, // 1秒後にリトライ
      2, // 指数バックオフ（1秒、2秒、4秒...）
      async (error, attempt) => {
        // エラーログを記録
        console.error(`注文作成の試行 ${attempt}/${retryCount} が失敗しました:`, error);
        if (error instanceof Error) {
          console.error('エラーメッセージ:', error.message);
          console.error('スタックトレース:', error.stack);
        }

        // 特定のエラーの場合はリトライしない
        if (error.message) {
          // バリアントIDの形式エラー
          if (error.message.includes('Invalid global id')) {
            console.error('バリアントIDの形式が正しくありません。リトライをスキップします。');
            return false;
          }

          // 既に注文が存在する場合
          if (error.message.includes('既に注文が存在します') ||
              error.message.includes('already has an order')) {
            console.error('既に注文が存在します。リトライをスキップします。');
            return false;
          }

          // APIアクセススコープの問題
          if (error.message.includes('access scope')) {
            console.error('APIアクセススコープが不足しています。リトライをスキップします。');
            return false;
          }

          // 認証エラー
          if (error.message.includes('Unauthorized') || error.message.includes('401')) {
            console.error('認証エラーが発生しました。リトライをスキップします。');
            return false;
          }
        }

        // その他のエラーはリトライする
        return true;
      }
    );

    console.log('注文作成が完了しました');
    return result;
  } catch (error) {
    // 詳細なエラー情報を記録
    const errorInfo = handleError(error, {
      operation: 'createOrderFromBooking',
      bookingId,
      retryCount
    }, true); // エラー通知を送信

    // ユーザーフレンドリーなエラーメッセージを生成
    const errorMessage = getFriendlyErrorMessage(error, {
      operation: 'createOrderFromBooking',
      bookingId,
      retryCount
    });

    // エラー情報を含む結果を返す
    return {
      success: false,
      error: errorInfo,
      errorMessage
    };
  }
}
