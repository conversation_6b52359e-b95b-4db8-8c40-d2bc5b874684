/**
 * 商品同期状況確認スクリプト
 *
 * このスクリプトは、Shopifyの商品データとPrismaデータベースの商品データの同期状況を確認します。
 * 特定の商品名を検索して、Shopifyとデータベースの両方に存在するか確認します。
 */

import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// GraphQL APIクライアントの設定
const shopifyClient = new GraphQLClient(
  `https://${process.env.SHOPIFY_SHOP}/admin/api/2024-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN || '',
      'Content-Type': 'application/json',
    },
  }
);

/**
 * 商品名で検索する関数
 * @param searchTerm 検索キーワード
 */
async function searchProducts(searchTerm: string) {
  try {
    console.log(`Shopifyで「${searchTerm}」を検索中...`);

    // 商品検索のGraphQLクエリ
    const SEARCH_PRODUCTS = gql`
      query searchProducts($query: String!, $first: Int!) {
        products(first: $first, query: $query) {
          edges {
            node {
              id
              title
              handle
              status
              variants(first: 1) {
                edges {
                  node {
                    id
                    sku
                    price
                  }
                }
              }
            }
          }
        }
      }
    `;

    // Shopify GraphQL APIを使用して商品を検索
    const response = await shopifyClient.request(SEARCH_PRODUCTS, {
      query: searchTerm,
      first: 10
    });

    // 検索結果を表示
    const products = response.products.edges;
    console.log(`Shopifyで ${products.length} 件の商品が見つかりました`);

    if (products.length > 0) {
      console.log('\n=== Shopify商品一覧 ===');
      products.forEach((edge: any, index: number) => {
        const product = edge.node;
        const variant = product.variants.edges[0]?.node;
        console.log(`${index + 1}. ${product.title}`);
        console.log(`   ID: ${product.id}`);
        console.log(`   ステータス: ${product.status}`);
        console.log(`   SKU: ${variant?.sku || 'なし'}`);
        console.log(`   価格: ${variant?.price || 'なし'}`);
        console.log('---');
      });
    }

    // Prismaデータベースで同じ商品を検索
    console.log(`\nデータベースで「${searchTerm}」を検索中...`);
    const dbProducts = await prisma.product.findMany({
      where: {
        title: {
          contains: searchTerm,
          mode: 'insensitive',
        },
      },
    });

    console.log(`データベースで ${dbProducts.length} 件の商品が見つかりました`);

    if (dbProducts.length > 0) {
      console.log('\n=== データベース商品一覧 ===');
      dbProducts.forEach((product, index) => {
        console.log(`${index + 1}. ${product.title}`);
        console.log(`   ID: ${product.id}`);
        console.log(`   Shopify ID: ${product.shopifyId}`);
        console.log(`   ステータス: ${product.status}`);
        console.log(`   SKU: ${product.sku}`);
        console.log(`   価格: ${product.price}`);
        console.log('---');
      });
    }

    // 同期状況の確認
    console.log('\n=== 同期状況 ===');
    if (products.length === 0 && dbProducts.length === 0) {
      console.log('商品が見つかりませんでした。');
    } else if (products.length > 0 && dbProducts.length === 0) {
      console.log('Shopifyには商品が存在しますが、データベースには同期されていません。');
      console.log('商品同期を実行する必要があります。');
    } else if (products.length === 0 && dbProducts.length > 0) {
      console.log('データベースには商品が存在しますが、Shopifyには存在しません。');
      console.log('データベースのクリーンアップが必要かもしれません。');
    } else {
      console.log('Shopifyとデータベースの両方に商品が存在します。');

      // ShopifyとDBの商品IDを比較
      const shopifyIds = products.map((edge: any) => edge.node.id.replace('gid://shopify/Product/', ''));
      const dbShopifyIds = dbProducts.map(product => product.shopifyId);

      const inShopifyNotInDb = shopifyIds.filter(id => !dbShopifyIds.includes(id));
      const inDbNotInShopify = dbShopifyIds.filter(id => !shopifyIds.includes(id));

      if (inShopifyNotInDb.length > 0) {
        console.log('\n以下のShopify商品はデータベースに同期されていません:');
        inShopifyNotInDb.forEach(id => {
          const product = products.find((edge: any) => edge.node.id.replace('gid://shopify/Product/', '') === id);
          console.log(`- ${product.node.title} (ID: ${id})`);
        });
      }

      if (inDbNotInShopify.length > 0) {
        console.log('\n以下のデータベース商品はShopifyに存在しません:');
        inDbNotInShopify.forEach(id => {
          const product = dbProducts.find(p => p.shopifyId === id);
          console.log(`- ${product?.title} (Shopify ID: ${id})`);
        });
      }
    }

  } catch (error) {
    console.error('エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// コマンドライン引数から検索キーワードを取得
const searchTerm = process.argv[2] || '';

// 商品検索を実行
searchProducts(searchTerm)
  .then(() => console.log('検索完了'))
  .catch(error => console.error('検索エラー:', error));
