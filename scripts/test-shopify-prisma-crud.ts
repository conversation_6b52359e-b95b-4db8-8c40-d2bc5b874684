/**
 * Shopify-Prisma CRUD連携テストスクリプト
 * 
 * 商品・顧客・注文・予約のCRUD操作がShopifyとPrismaで正しく連携されているかテストします
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

interface TestResult {
  operation: string;
  entity: string;
  success: boolean;
  message: string;
  details?: any;
}

class ShopifyPrismaCrudTester {
  private results: TestResult[] = [];

  /**
   * 商品CRUDテスト
   */
  async testProductCrud(): Promise<void> {
    console.log('=== 商品CRUD連携テスト ===');

    // 1. 商品作成テスト
    await this.testProductCreate();
    
    // 2. 商品更新テスト
    await this.testProductUpdate();
    
    // 3. 商品削除テスト
    await this.testProductDelete();
  }

  /**
   * 商品作成テスト
   */
  private async testProductCreate(): Promise<void> {
    try {
      console.log('商品作成テスト開始...');
      
      // Shopifyで商品作成をシミュレート
      const testProductId = `test-${Date.now()}`;
      
      // Webhookが正常に動作するかテスト
      const response = await fetch(`${process.env.SHOPIFY_APP_URL}/webhooks/products/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Topic': 'products/create',
          'X-Shopify-Shop-Domain': process.env.SHOPIFY_SHOP || 'test.myshopify.com',
          'X-Shopify-Hmac-Sha256': 'test-hmac'
        },
        body: JSON.stringify({
          id: testProductId,
          title: 'テスト商品',
          variants: [{ id: 1, price: '1000', sku: 'TEST-001' }]
        })
      });

      if (response.ok) {
        // Prismaで商品が作成されているか確認
        const product = await prisma.product.findFirst({
          where: { shopifyId: testProductId }
        });

        this.addResult('CREATE', 'Product', !!product, 
          product ? '商品作成成功' : '商品作成失敗', { productId: testProductId });
      } else {
        this.addResult('CREATE', 'Product', false, 'Webhook呼び出し失敗');
      }
    } catch (error) {
      this.addResult('CREATE', 'Product', false, `エラー: ${error.message}`);
    }
  }

  /**
   * 商品更新テスト
   */
  private async testProductUpdate(): Promise<void> {
    try {
      console.log('商品更新テスト開始...');
      
      // 既存商品を取得
      const existingProduct = await prisma.product.findFirst();
      
      if (!existingProduct) {
        this.addResult('UPDATE', 'Product', false, 'テスト用商品が見つかりません');
        return;
      }

      // Shopifyで商品更新をシミュレート
      const response = await fetch(`${process.env.SHOPIFY_APP_URL}/webhooks/products/update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Topic': 'products/update',
          'X-Shopify-Shop-Domain': process.env.SHOPIFY_SHOP || 'test.myshopify.com',
          'X-Shopify-Hmac-Sha256': 'test-hmac'
        },
        body: JSON.stringify({
          id: existingProduct.shopifyId,
          title: '更新されたテスト商品',
          variants: [{ id: 1, price: '1500', sku: existingProduct.sku }]
        })
      });

      if (response.ok) {
        // Prismaで商品が更新されているか確認
        const updatedProduct = await prisma.product.findUnique({
          where: { id: existingProduct.id }
        });

        const isUpdated = updatedProduct && updatedProduct.title.includes('更新された');
        this.addResult('UPDATE', 'Product', isUpdated, 
          isUpdated ? '商品更新成功' : '商品更新失敗', { productId: existingProduct.id });
      } else {
        this.addResult('UPDATE', 'Product', false, 'Webhook呼び出し失敗');
      }
    } catch (error) {
      this.addResult('UPDATE', 'Product', false, `エラー: ${error.message}`);
    }
  }

  /**
   * 商品削除テスト
   */
  private async testProductDelete(): Promise<void> {
    try {
      console.log('商品削除テスト開始...');
      
      // テスト用商品を作成
      const testProduct = await prisma.product.create({
        data: {
          shopifyId: `delete-test-${Date.now()}`,
          title: '削除テスト商品',
          sku: 'DELETE-TEST',
          price: 1000,
          shop: process.env.SHOPIFY_SHOP || 'test.myshopify.com',
          status: 'AVAILABLE'
        }
      });

      // Shopifyで商品削除をシミュレート
      const response = await fetch(`${process.env.SHOPIFY_APP_URL}/webhooks/products/delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Topic': 'products/delete',
          'X-Shopify-Shop-Domain': process.env.SHOPIFY_SHOP || 'test.myshopify.com',
          'X-Shopify-Hmac-Sha256': 'test-hmac'
        },
        body: JSON.stringify({
          id: testProduct.shopifyId
        })
      });

      if (response.ok) {
        // Prismaで商品が削除されているか確認
        const deletedProduct = await prisma.product.findUnique({
          where: { id: testProduct.id }
        });

        const isDeleted = !deletedProduct || deletedProduct.status === 'UNAVAILABLE';
        this.addResult('DELETE', 'Product', isDeleted, 
          isDeleted ? '商品削除成功' : '商品削除失敗', { productId: testProduct.id });
      } else {
        this.addResult('DELETE', 'Product', false, 'Webhook呼び出し失敗');
      }
    } catch (error) {
      this.addResult('DELETE', 'Product', false, `エラー: ${error.message}`);
    }
  }

  /**
   * 顧客CRUDテスト
   */
  async testCustomerCrud(): Promise<void> {
    console.log('=== 顧客CRUD連携テスト ===');
    // TODO: 顧客のCRUDテストを実装
    this.addResult('CRUD', 'Customer', false, '未実装');
  }

  /**
   * 注文CRUDテスト
   */
  async testOrderCrud(): Promise<void> {
    console.log('=== 注文CRUD連携テスト ===');
    // TODO: 注文のCRUDテストを実装
    this.addResult('CRUD', 'Order', false, '未実装');
  }

  /**
   * 予約CRUDテスト
   */
  async testBookingCrud(): Promise<void> {
    console.log('=== 予約CRUD連携テスト ===');
    // TODO: 予約のCRUDテストを実装
    this.addResult('CRUD', 'Booking', false, '未実装');
  }

  /**
   * テスト結果を追加
   */
  private addResult(operation: string, entity: string, success: boolean, message: string, details?: any): void {
    this.results.push({ operation, entity, success, message, details });
    console.log(`${success ? '✅' : '❌'} ${entity} ${operation}: ${message}`);
  }

  /**
   * テスト結果を表示
   */
  displayResults(): void {
    console.log('\n=== テスト結果サマリー ===');
    
    const successCount = this.results.filter(r => r.success).length;
    const totalCount = this.results.length;
    
    console.log(`成功: ${successCount}/${totalCount}`);
    console.log(`失敗: ${totalCount - successCount}/${totalCount}`);
    
    console.log('\n=== 詳細結果 ===');
    this.results.forEach(result => {
      console.log(`${result.success ? '✅' : '❌'} ${result.entity} ${result.operation}: ${result.message}`);
      if (result.details) {
        console.log(`   詳細: ${JSON.stringify(result.details)}`);
      }
    });
  }

  /**
   * すべてのテストを実行
   */
  async runAllTests(): Promise<void> {
    try {
      await this.testProductCrud();
      await this.testCustomerCrud();
      await this.testOrderCrud();
      await this.testBookingCrud();
      
      this.displayResults();
    } catch (error) {
      console.error('テスト実行エラー:', error);
    } finally {
      await prisma.$disconnect();
    }
  }
}

// メイン実行
async function main() {
  const tester = new ShopifyPrismaCrudTester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}

export { ShopifyPrismaCrudTester };
