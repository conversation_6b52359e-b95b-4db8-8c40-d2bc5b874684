/**
 * SKU検索ユーティリティテストスクリプト
 */

import { 
  buildSkuSearchConditions, 
  buildProductSearchConditions, 
  explainSkuSearch,
  SKU_SEARCH_TEST_CASES 
} from '../app/utils/sku-search.ts';

console.log('🧪 SKU検索ユーティリティテストを開始...\n');

// テストケースを実行
console.log('=== テストケース実行 ===');
SKU_SEARCH_TEST_CASES.forEach((testCase, index) => {
  console.log(`${index + 1}. ${testCase.description}: "${testCase.input}"`);
  
  const explanations = explainSkuSearch(testCase.input);
  explanations.forEach(explanation => {
    console.log(`   ${explanation}`);
  });
  
  console.log(`   期待されるマッチ: ${testCase.expectedMatches.join(', ')}`);
  console.log('');
});

// 実際の検索条件を確認
console.log('=== 実際の検索条件生成テスト ===');

const testQueries = [
  '201-07-107',
  '20107107', 
  '201-07-107-1D',
  '201',
  '107'
];

testQueries.forEach(query => {
  console.log(`検索クエリ: "${query}"`);
  
  const skuConditions = buildSkuSearchConditions(query);
  console.log(`SKU検索条件: ${skuConditions.length}件`);
  skuConditions.forEach((condition, index) => {
    console.log(`  ${index + 1}. ${JSON.stringify(condition, null, 2)}`);
  });
  
  const productConditions = buildProductSearchConditions(query);
  console.log(`商品検索条件: ${productConditions.length}件`);
  console.log('');
});

console.log('✅ SKU検索ユーティリティテスト完了');
