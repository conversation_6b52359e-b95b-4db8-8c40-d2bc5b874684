#!/usr/bin/env node

/**
 * テスト用商品登録スクリプト
 *
 * 様々なテストシナリオに対応できる商品を登録
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { PrismaClient } from '@prisma/client';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const prisma = new PrismaClient();

// テスト用商品データ
const TEST_PRODUCTS = [
  {
    sku: '212-05-023',
    title: '花器　シルバー　穴開きボトル型',
    handle: '花器-シルバー-穴開きボトル型-test',
    category: '花器',
    tags: ['PROP', 'ニュアンスオブジェ', 'ゴールド・シルバー', 'シルバー'],
    metafields: {
      color: 'シルバー',
      location: 'NY',
      status: 'available',
      booking_type: 'confirmed',
      booking_notes: 'テスト用商品です。',
      width: 14,
      height: 31,
      depth: 14,
      material: 'メタル',
      manufacturer: 'テストメーカー',
      purchase_date: '2025-01-01',
      purchase_price: 5000
    },
    basePrice: 1500
  },
  {
    sku: '201-07-107',
    title: 'シルバートレー　オーバル　フリルフレーム　持ち手付',
    handle: 'シルバートレー-オーバル-フリルフレーム-持ち手付-test',
    category: 'キッチン',
    tags: ['PROP', 'Silver ware', 'キッチン', '銀食器'],
    metafields: {
      color: 'シルバー',
      location: 'NY',
      status: 'available',
      booking_type: 'confirmed',
      booking_notes: 'テスト用商品です。',
      width: 30,
      height: 5,
      depth: 20,
      material: 'シルバー',
      manufacturer: 'テストメーカー',
      purchase_date: '2025-01-01',
      purchase_price: 8000
    },
    basePrice: 3000
  },
  {
    sku: '201-05-797',
    title: 'キャンドルスタンド　シャインシルバー　3灯',
    handle: 'キャンドルスタンド-シャインシルバー-3灯-test',
    category: 'キャンドル',
    tags: ['PROP', 'キャンドル', 'キャンドルスタンド'],
    metafields: {
      color: 'シルバー',
      location: 'NY',
      status: 'available',
      booking_type: 'confirmed',
      booking_notes: 'テスト用商品です。',
      width: 25,
      height: 20,
      depth: 25,
      material: 'メタル',
      manufacturer: 'テストメーカー',
      purchase_date: '2025-01-01',
      purchase_price: 6000
    },
    basePrice: 2000
  },
  {
    sku: '211-12-954',
    title: 'オブジェ　タイドロープ　ゴールド',
    handle: 'オブジェ-タイドロープ-ゴールド-test',
    category: 'オブジェ',
    tags: ['PROP', 'その他オブジェ', 'ニュアンスオブジェ'],
    metafields: {
      color: 'ゴールド',
      location: 'NY',
      status: 'maintenance', // メンテナンス状態のテスト用
      booking_type: 'confirmed',
      booking_notes: 'メンテナンス中のテスト商品です。',
      width: 15,
      height: 10,
      depth: 15,
      material: 'メタル',
      manufacturer: 'テストメーカー',
      purchase_date: '2025-01-01',
      purchase_price: 4000
    },
    basePrice: 1000
  },
  {
    sku: '206-02-320',
    title: 'ジュエリートレー　ホワイト×クリア　フリル型　持ち手付',
    handle: 'ジュエリートレー-ホワイト-クリア-フリル型-持ち手付-test',
    category: 'コスメ',
    tags: ['PROP', 'コスメ', 'コスメボックス・トレー'],
    metafields: {
      color: 'ホワイト',
      location: 'PR', // PR拠点のテスト用
      status: 'available',
      booking_type: 'confirmed',
      booking_notes: 'テスト用商品です。',
      width: 20,
      height: 8,
      depth: 15,
      material: 'プラスチック',
      manufacturer: 'テストメーカー',
      purchase_date: '2025-01-01',
      purchase_price: 3000
    },
    basePrice: 1200
  }
];

/**
 * 商品をPrismaデータベースに登録
 */
async function registerProductInPrisma(shop, productData) {
  const { sku, title, handle, category, tags, metafields, basePrice } = productData;

  // basicInfo JSONを構築
  const basicInfo = {
    category: category,
    tags: tags,
    basePrice: basePrice,
    description: `テスト用商品: ${title}`
  };

  // 商品を作成
  const product = await prisma.product.create({
    data: {
      shop,
      sku,
      title,
      handle,
      status: metafields.status === 'maintenance' ? 'MAINTENANCE' : 'AVAILABLE',
      inventory: metafields.status === 'available' ? 1 : 0, // availableなら在庫1、そうでなければ0
      price: basePrice, // price フィールドを追加
      basicInfo,
      // メタフィールドは別途管理する場合は、ここでは基本情報のみ
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });

  console.log(`✅ 商品登録完了: ${sku} - ${title}`);
  return product;
}

/**
 * レンタル期間バリアントの価格計算
 */
function calculateVariantPrice(basePrice, days) {
  if (days === 1) return basePrice;
  if (days >= 2 && days <= 7) return Math.floor(basePrice * days * 0.2) + basePrice;
  if (days >= 8) return Math.floor(basePrice * 0.1); // 8日以上は1日あたりの価格
  return basePrice;
}

/**
 * 仮予約価格の計算
 */
function calculateProvisionalPrice(basePrice) {
  return Math.floor(basePrice * 0.1); // 10%
}

/**
 * 商品バリアントを作成（Shopify用データ構造）
 */
function createProductVariants(productData) {
  const { sku, title, basePrice } = productData;
  const variants = [];

  // 1-7日レンタルバリアント
  for (let days = 1; days <= 7; days++) {
    variants.push({
      sku: `${sku}-${days}D`,
      title: `${days}日レンタル`,
      price: calculateVariantPrice(basePrice, days),
      inventory: 1,
      option1: `${days}日レンタル`
    });
  }

  // 8日以上レンタルバリアント
  variants.push({
    sku: `${sku}-8D+`,
    title: '8日以上レンタル',
    price: calculateVariantPrice(basePrice, 8),
    inventory: 1,
    option1: '8日以上レンタル'
  });

  // 仮予約バリアント
  variants.push({
    sku: `${sku}-PROV`,
    title: '仮予約',
    price: calculateProvisionalPrice(basePrice),
    inventory: 1,
    option1: '仮予約'
  });

  return variants;
}

/**
 * テスト用商品を一括登録
 */
async function registerTestProducts() {
  const shop = 'ease-next-temp.myshopify.com';

  console.log('=== テスト用商品登録開始 ===');
  console.log(`対象ショップ: ${shop}`);
  console.log(`登録予定商品数: ${TEST_PRODUCTS.length}件`);

  try {
    await prisma.$connect();
    console.log('✅ データベース接続成功');

    // 既存のテスト商品を削除
    console.log('\n既存のテスト商品を削除中...');
    const testSKUs = TEST_PRODUCTS.map(p => p.sku);

    // 関連する予約データを削除
    await prisma.booking.deleteMany({
      where: {
        shop,
        product: {
          sku: { in: testSKUs }
        }
      }
    });

    // 商品を削除
    const deletedCount = await prisma.product.deleteMany({
      where: {
        shop,
        sku: { in: testSKUs }
      }
    });

    console.log(`🗑️ 既存商品削除完了: ${deletedCount.count}件`);

    // 新しい商品を登録
    console.log('\n新しいテスト商品を登録中...');
    const registeredProducts = [];

    for (const productData of TEST_PRODUCTS) {
      const product = await registerProductInPrisma(shop, productData);
      registeredProducts.push(product);

      // バリアント情報をログ出力
      const variants = createProductVariants(productData);
      console.log(`  バリアント数: ${variants.length}個`);
      variants.forEach(variant => {
        console.log(`    - ${variant.sku}: ¥${variant.price} (${variant.title})`);
      });
    }

    console.log(`\n✅ 商品登録完了: ${registeredProducts.length}件`);

    // 登録結果をCSVファイルに出力
    const outputPath = path.join(__dirname, '../../master-data-csv/generated/test_products_registered.csv');
    const csvLines = ['SKU,タイトル,カテゴリ,ベース価格,ステータス,在庫場所'];

    TEST_PRODUCTS.forEach(product => {
      csvLines.push(
        `${product.sku},"${product.title}",${product.category},${product.basePrice},${product.metafields.status},${product.metafields.location}`
      );
    });

    fs.writeFileSync(outputPath, csvLines.join('\n'), 'utf-8');
    console.log(`📄 登録結果をCSVに出力: ${outputPath}`);

    return registeredProducts;

  } catch (error) {
    console.error('❌ エラー:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * メイン処理
 */
async function main() {
  try {
    const products = await registerTestProducts();

    console.log('\n=== 登録完了 ===');
    console.log('次のステップ:');
    console.log('1. Shopifyに商品とバリアントを同期');
    console.log('2. テスト用予約データの作成');
    console.log('3. 価格計算テストの実行');
    console.log('4. 予約システムの動作確認');

  } catch (error) {
    console.error('テスト商品登録に失敗しました:', error);
    process.exit(1);
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
