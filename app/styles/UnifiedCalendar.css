/* UnifiedCalendar用のCSS */

.container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 0.5rem;
  box-sizing: border-box;
}

/* 複数月表示のスタイル */
.multiMonth {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.calendarMonth {
  flex: 1;
  min-width: 280px;
  max-width: 400px;
}

/* モバイル対応のスタイル */
@media screen and (max-width: 767px) {
  .container {
    padding: 0.25rem;
  }

  .multiMonth {
    gap: 0.5rem;
  }

  .calendarMonth {
    min-width: 100%;
    max-width: 100%;
  }
}

/* 表示モードのスタイル */
.displayModeCell {
  pointer-events: none;
}

.title {
  text-align: left;
}

/* 月名と年の表示スタイル */
.monthTitle {
  font-size: 1rem;
}

/* 選択された日付範囲の表示 */
.selectedDates {
  margin-top: 1rem;
  padding: 0.5rem;
  background-color: #f9fafb;
  border-radius: 4px;
  text-align: center;
}

/* カレンダー凡例のスタイル */
.legend {
  margin-top: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.legendItem {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
}

.legendColor {
  width: 1rem;
  height: 1rem;
  border-radius: 2px;
}

/* エラーメッセージのスタイル */
.errorMessage {
  margin-top: 1rem;
  color: #d82c0d;
}

/* ローディングスピナーのスタイル */
.loading {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
}

/* グローバルスタイルのオーバーライド（:global を使用） */
:global(.Polaris-DatePicker__Month) {
  width: 100%;
}

:global(.Polaris-DatePicker__Title) {
  display: flex;
  flex-direction: row-reverse;
  justify-content: center;
  gap: 0.5rem;
  text-align: center;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

:global(.Polaris-DatePicker__Weekday) {
  text-align: center;
  font-weight: 500;
  padding: 0.5rem 0;
  font-size: 0.9rem;
}

:global(.Polaris-DatePicker) {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

:global(.Polaris-DatePicker__Header) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 1rem;
}

:global(.Polaris-DatePicker__Day) {
  position: relative;
  height: 36px;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
  cursor: pointer;
  /* タッチ操作のためのスタイル */
  touch-action: manipulation;
}

:global(.Polaris-DatePicker__Day span) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
}

/* モバイル向けの日付セルサイズ調整 */
@media screen and (max-width: 767px) {
  :global(.Polaris-DatePicker__Day) {
    height: 44px; /* タッチ操作に適したサイズに拡大 */
    min-width: 44px; /* タッチ操作に適したサイズに拡大 */
  }
}

/* 選択中の日付の背景色 */
:global(.Polaris-DatePicker__Day--selected),
:global(.Polaris-DatePicker__Day--selected:hover),
:global(.Polaris-DatePicker__Day--selected:focus),
:global(.Polaris-DatePicker__Day--selected:active) {
  background-color: #202223;
  color: white;
  z-index: 10;
}

/* 選択中の日付のテキスト色を白にする */
:global(.Polaris-DatePicker__Day--selected span),
:global(.Polaris-DatePicker__Day--selected:hover span),
:global(.Polaris-DatePicker__Day--selected:focus span),
:global(.Polaris-DatePicker__Day--selected:active span) {
  color: white;
}

/* 選択範囲内の日付の背景色 */
:global(.Polaris-DatePicker__Day--inRange) {
  background-color: rgba(32, 34, 35, 0.1);
  z-index: 1;
}

/* 無効な日付のスタイル */
:global(.Polaris-DatePicker__Day--disabled) {
  color: #8c9196;
  cursor: not-allowed;
}

/* ホバー状態とフォーカス状態の改善 */
:global(.Polaris-DatePicker__Day:hover) {
  background-color: #f9fafb;
  box-shadow: 0 0 0 2px rgba(0, 128, 96, 0.3);
}

:global(.Polaris-DatePicker__Day:focus) {
  outline: none;
  box-shadow: 0 0 0 2px #458fff;
  z-index: 20;
}

:global(.Polaris-DatePicker__Day:focus-visible) {
  outline: none;
  box-shadow: 0 0 0 2px #458fff, 0 0 0 4px rgba(69, 143, 255, 0.3);
  z-index: 20;
}

/* 選択中の日付のホバー状態とフォーカス状態 */
:global(.Polaris-DatePicker__Day--selected:hover) {
  background-color: #202223;
}

:global(.Polaris-DatePicker__Day--selected:focus),
:global(.Polaris-DatePicker__Day--selected:focus-visible) {
  background-color: #202223;
  box-shadow: 0 0 0 2px #458fff, 0 0 0 4px rgba(69, 143, 255, 0.3);
  z-index: 20;
}

/* 選択中の日付の::afterの色を白にする */
:global(.Polaris-DatePicker__Day--selected span::after) {
  color: white;
}

/* 仮予約のスタイル */
:global(.Polaris-DatePicker__Day[data-provisional="true"]) {
  background-color: rgba(255, 184, 0, 0.25) !important;
}

:global(.Polaris-DatePicker__Day[data-provisional="true"] span) {
  color: #8a5c00; /* コントラスト比を向上 */
  position: relative;
  font-weight: 500;
}

/* 仮予約に「仮」を追加 */
:global(.Polaris-DatePicker__Day[data-provisional="true"] span::after) {
  content: "仮";
  font-size: 0.7em;
  position: absolute;
  bottom: -11px;
  left: 50%;
  transform: translateX(-50%);
  color: #8a5c00; /* コントラスト比を向上 */
  font-weight: bold;
}

/* 仮予約のホバー時 */
:global(.Polaris-DatePicker__Day[data-provisional="true"]:hover) {
  background-color: rgba(255, 184, 0, 0.35) !important;
}

/* 本予約のスタイル */
:global(.Polaris-DatePicker__Day[data-confirmed="true"]) {
  background-color: rgba(0, 128, 96, 0.25) !important;
}

:global(.Polaris-DatePicker__Day[data-confirmed="true"] span) {
  color: #003d2d; /* コントラスト比を向上 */
  position: relative;
  font-weight: 500;
}

/* 本予約に「予」を追加 */
:global(.Polaris-DatePicker__Day[data-confirmed="true"] span::after) {
  content: "予";
  font-size: 0.7em;
  position: absolute;
  bottom: -11px;
  left: 50%;
  transform: translateX(-50%);
  color: #003d2d; /* コントラスト比を向上 */
  font-weight: bold;
}

/* 本予約のホバー時 */
:global(.Polaris-DatePicker__Day[data-confirmed="true"]:hover) {
  background-color: rgba(0, 128, 96, 0.35) !important;
}

/* メンテナンスのスタイル */
:global(.Polaris-DatePicker__Day[data-maintenance="true"]) {
  background-color: rgba(171, 171, 171, 0.55) !important;
}

:global(.Polaris-DatePicker__Day[data-maintenance="true"] span) {
  color: #3e4046; /* コントラスト比を向上 */
  position: relative;
  font-weight: 500;
}

/* メンテナンスに「メ」を追加 */
:global(.Polaris-DatePicker__Day[data-maintenance="true"] span::after) {
  content: "メ";
  font-size: 0.7em;
  position: absolute;
  top: 2px;
  right: 2px;
  color: #3e4046; /* コントラスト比を向上 */
  font-weight: bold;
  z-index: 10;
}

/* メンテナンスのホバー時 */
:global(.Polaris-DatePicker__Day[data-maintenance="true"]:hover) {
  background-color: rgba(171, 171, 171, 0.65) !important;
}

/* 日付セルのスタイル */
.provisionalCell {
  color: #8a5c00; /* コントラスト比を向上 */
  position: relative;
  font-weight: 500;
}

.confirmedCell {
  color: #003d2d; /* コントラスト比を向上 */
  position: relative;
  font-weight: 500;
}

.maintenanceCell {
  color: #3e4046; /* コントラスト比を向上 */
  position: relative;
  font-weight: 500;
}

.sundayCell {
  color: #b42318; /* コントラスト比を向上 */
  position: relative;
  font-weight: 500;
}

.holidayCell {
  color: #b42318; /* コントラスト比を向上 */
  position: relative;
  font-weight: 500;
}

.newyearCell {
  color: #b42318; /* コントラスト比を向上 */
  position: relative;
  font-weight: 500;
}

.selectableCell {
  color: #202223;
  position: relative;
}

/* 日付マーカーのスタイル */
.dateMarker {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.sundayMarker {
  background-color: #d82c0d;
}

.holidayMarker {
  background-color: #d82c0d;
}

.newyearMarker {
  background-color: #d82c0d;
}

.provisionalMarker {
  background-color: #ffb800;
}

.confirmedMarker {
  background-color: #008060;
}

.maintenanceMarker {
  background-color: #637381;
}
