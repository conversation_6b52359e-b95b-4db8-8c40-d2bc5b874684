{% comment %}
  商品詳細ページに表示するレンタル予約フォーム

  使用方法:
  {% render 'product-rental-form', product: product %}
{% endcomment %}

<div class="product-rental-form">
  <h3>レンタル予約</h3>

  <div class="rental-dates">
    <div class="date-field">
      <label for="rental-start-date">開始日</label>
      <input type="date" id="rental-start-date" name="rental-start-date">
    </div>

    <div class="date-field">
      <label for="rental-end-date">終了日</label>
      <input type="date" id="rental-end-date" name="rental-end-date">
    </div>
  </div>

  <div class="rental-info">
    <div class="rental-days">
      <span>レンタル日数:</span>
      <span id="rental-days-count">0</span>日
    </div>

    <div class="rental-price">
      <span>料金:</span>
      <span id="rental-price-amount">¥0</span>
    </div>

    <div class="deposit-price">
      <span>デポジット (10%):</span>
      <span id="deposit-amount">¥0</span>
    </div>
  </div>

  <div id="error-message" class="error-message" style="display: none;"></div>

  <div id="loading-indicator" class="loading-indicator" style="display: none;">
    <span class="loading-spinner"></span>
    <span>処理中...</span>
  </div>

  <button id="add-to-cart-rental" class="button button--primary button--full-width">
    カートに追加
  </button>

  <div class="rental-policy">
    <h4>レンタルポリシー</h4>
    <ul>
      <li>最低レンタル期間は1日です</li>
      <li>日曜日・祝日・年末年始（12/29〜1/3）は休業日です</li>
      <li>料金は1日目が基本料金の100%、2〜6日目が20%、7日目以降が10%です</li>
      <li>デポジットとして基本料金の10%をお支払いいただきます</li>
    </ul>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 商品情報の取得
    const productData = {
      productId: '{{ product.id }}',
      variantId: '{{ product.selected_or_first_available_variant.id }}',
      basePrice: {{ product.price | divided_by: 100.0 }},
      productTitle: '{{ product.title | escape }}',
      variantTitle: '{{ product.selected_or_first_available_variant.title | escape }}',
      productImage: '{{ product.featured_image | img_url: '100x100' }}'
    };

    // ProductRentalクラスのインスタンス化
    window.productRental = new ProductRental(productData);
  });
</script>

<style>
  .product-rental-form {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .rental-dates {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
  }

  .date-field {
    flex: 1;
    min-width: 150px;
  }

  .date-field label {
    display: block;
    margin-bottom: 5px;
  }

  .date-field input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }

  .rental-info {
    margin-bottom: 15px;
  }

  .rental-days, .rental-price, .deposit-price {
    margin-bottom: 5px;
  }

  .error-message {
    margin: 10px 0;
    padding: 10px;
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
  }

  .loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
  }

  .loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-top-color: #333;
    border-radius: 50%;
    animation: spin 1s infinite linear;
    margin-right: 10px;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  .rental-policy {
    margin-top: 20px;
    font-size: 0.9em;
  }

  .rental-policy h4 {
    margin-bottom: 10px;
  }

  .rental-policy ul {
    padding-left: 20px;
  }

  .rental-policy li {
    margin-bottom: 5px;
  }
</style>
