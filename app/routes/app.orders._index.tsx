import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  BlockStack,
  DataTable,
  Button,
  Badge,
  Pagination,
  EmptyState,
  TextField,
  ButtonGroup,
  Filters,
  InlineStack,
  Icon,
  Tooltip,
  Banner
} from "@shopify/polaris";
import { SearchIcon, CalendarIcon } from "@shopify/polaris-icons";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { useState, useEffect } from "react";
import { buildSearchCondition, filterItemsByName } from "../utils/search-utils";

/**
 * 注文管理ページ（インデックスページ）
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  // URLからパラメータを取得
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get("page") || "1", 10);
  const pageSize = 20; // 表示件数
  const searchQuery = url.searchParams.get("q") || "";
  const statusFilter = url.searchParams.get("status") || "";
  const sortField = url.searchParams.get("sort") || "createdAt";
  const sortOrder = url.searchParams.get("order") || "desc";
  const success = url.searchParams.get("success") === "true";
  const count = url.searchParams.get("count") || "0";

  try {
    // 検索条件を構築
    let whereCondition: any = { shop };

    // 検索クエリがある場合
    if (searchQuery) {
      // 共通ユーティリティ関数を使用して検索条件を構築
      const searchFields = ['customerName', 'customerEmail', 'orderNumber'];
      const searchCondition = buildSearchCondition(searchQuery, searchFields);

      // 最終的な検索条件を設定
      whereCondition = { ...whereCondition, ...searchCondition };

      // 検索条件をログに出力（デバッグ用）
      console.log('注文検索条件:', JSON.stringify(whereCondition, null, 2));
    }

    // ステータスフィルターがある場合
    if (statusFilter) {
      whereCondition.paymentStatus = statusFilter;
    }

    // 並び順を設定
    const orderBy: any = {};
    orderBy[sortField] = sortOrder;

    // 注文データを取得
    const orders = await prisma.order.findMany({
      where: whereCondition,
      orderBy,
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    // 総注文数を取得
    const totalOrders = await prisma.order.count({
      where: whereCondition,
    });

    // 注文データをフォーマット
    const formattedOrders = orders.map(order => {
      return {
        id: order.id,
        shopifyId: order.shopifyId,
        orderNumber: order.orderNumber,
        customerName: order.customerName || "",
        customerEmail: order.customerEmail || "",
        startDate: order.startDate ? new Date(order.startDate).toLocaleDateString() : "",
        endDate: order.endDate ? new Date(order.endDate).toLocaleDateString() : "",
        totalAmount: order.totalAmount ? `¥${order.totalAmount.toLocaleString()}` : "",
        paymentStatus: order.paymentStatus || "",
        createdAt: order.createdAt ? new Date(order.createdAt).toLocaleString() : "",
      };
    });

    return json({
      orders: formattedOrders,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalOrders / pageSize),
        totalItems: totalOrders,
      },
      filters: {
        query: searchQuery,
        status: statusFilter,
        sort: sortField,
        order: sortOrder,
      },
      success,
      count: parseInt(count),
    });
  } catch (error) {
    console.error("注文データ取得エラー:", error);
    return json({
      orders: [],
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
      },
      filters: {
        query: searchQuery,
        status: statusFilter,
        sort: sortField,
        order: sortOrder,
      },
      error: "注文データの取得中にエラーが発生しました",
      success: false,
      count: 0,
    });
  }
}

/**
 * 注文管理ページ
 */
export default function OrdersIndexPage() {
  const { orders, pagination, filters, error, success, count } = useLoaderData<typeof loader>();
  const [searchValue, setSearchValue] = useState(filters.query);

  // 検索フォームの送信処理
  const handleSearch = (query?: string) => {
    const url = new URL(window.location.href);
    const searchTerm = query !== undefined ? query : searchValue;

    if (searchTerm && searchTerm !== "") {
      url.searchParams.set("q", searchTerm);
    } else {
      url.searchParams.delete("q");
    }

    url.searchParams.set("page", "1"); // 検索時は1ページ目に戻る
    window.location.href = url.toString();
  };

  // ステータスフィルターの変更処理
  const handleStatusChange = (status: string) => {
    const url = new URL(window.location.href);

    if (status && status !== "") {
      url.searchParams.set("status", status);
    } else {
      url.searchParams.delete("status");
    }

    url.searchParams.set("page", "1"); // フィルター変更時は1ページ目に戻る
    window.location.href = url.toString();
  };

  // ページネーションの処理
  const handlePageChange = (page: number) => {
    const url = new URL(window.location.href);
    url.searchParams.set("page", page.toString());
    window.location.href = url.toString();
  };

  // 並び替えの変更処理
  const handleSortChange = (field: string) => {
    const url = new URL(window.location.href);
    const currentSort = url.searchParams.get("sort") || "createdAt";
    const currentOrder = url.searchParams.get("order") || "desc";

    // 同じフィールドをクリックした場合は並び順を反転
    if (field === currentSort) {
      url.searchParams.set("order", currentOrder === "asc" ? "desc" : "asc");
    } else {
      url.searchParams.set("sort", field);
      url.searchParams.set("order", "asc"); // 新しいフィールドの場合は昇順から
    }

    window.location.href = url.toString();
  };

  // 支払いステータスに応じたバッジを生成
  const getStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case "PAID":
        return <Badge tone="success">支払い済み</Badge>;
      case "PENDING":
        return <Badge tone="attention">未払い</Badge>;
      case "REFUNDED":
        return <Badge tone="warning">返金済み</Badge>;
      case "PARTIALLY_REFUNDED":
        return <Badge tone="warning">一部返金</Badge>;
      case "VOIDED":
        return <Badge tone="critical">無効</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // データテーブルの行を生成
  const rows = orders.map(order => [
    <Link key={`link-${order.id}`} to={`https://${order.shop}/admin/orders/${order.shopifyId}`} target="_blank">{order.orderNumber}</Link>,
    order.customerName,
    order.customerEmail,
    order.startDate,
    order.endDate,
    order.totalAmount,
    getStatusBadge(order.paymentStatus),
    order.createdAt,
  ]);

  return (
    <Page
      title="注文一覧"
      subtitle="Shopify注文の管理と確認"
      backAction={{ content: "ホーム", url: "/app" }}
      primaryAction={{
        content: "同期設定",
        url: "/app/orders/sync",
      }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                {error && (
                  <Text as="p" tone="critical">{error}</Text>
                )}

                {success && (
                  <Banner tone="success" onDismiss={() => {
                    const url = new URL(window.location.href);
                    url.searchParams.delete("success");
                    url.searchParams.delete("count");
                    window.history.replaceState({}, "", url.toString());
                  }}>
                    <p>{count}件の注文が正常に同期されました。</p>
                  </Banner>
                )}

                {/* 検索・フィルターセクション */}
                <Box paddingBlockEnd="400">
                  <InlineStack gap="400" align="start" blockAlign="center">
                    <div style={{ flex: 1 }}>
                      <div style={{ display: 'flex' }}>
                        <div style={{ flex: 1 }}>
                          <TextField
                            label="検索"
                            type="search"
                            value={searchValue}
                            placeholder="注文番号、顧客名、メールで検索"
                            prefix={<Icon source={SearchIcon} />}
                            onClearButtonClick={() => {
                              setSearchValue("");
                              handleSearch("");
                            }}
                            onChange={(value) => {
                              setSearchValue(value);
                            }}
                            onKeyPress={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                handleSearch(searchValue);
                              }
                            }}
                            autoComplete="off"
                          />
                        </div>
                        <div style={{ marginLeft: '8px', marginTop: '24px' }}>
                          <Button onClick={() => handleSearch(searchValue)}>検索</Button>
                        </div>
                      </div>
                    </div>
                    <div>
                      <Filters
                        queryValue={filters.status}
                        filters={[
                          {
                            key: 'status',
                            label: 'ステータス',
                            filter: (
                              <ButtonGroup segmented>
                                <Button
                                  pressed={filters.status === ""}
                                  onClick={() => handleStatusChange("")}
                                >
                                  すべて
                                </Button>
                                <Button
                                  pressed={filters.status === "PAID"}
                                  onClick={() => handleStatusChange("PAID")}
                                >
                                  支払い済み
                                </Button>
                                <Button
                                  pressed={filters.status === "PENDING"}
                                  onClick={() => handleStatusChange("PENDING")}
                                >
                                  未払い
                                </Button>
                              </ButtonGroup>
                            ),
                            shortcut: true,
                          },
                        ]}
                        onQueryChange={(value) => handleStatusChange(value)}
                        onQueryClear={() => handleStatusChange("")}
                      />
                    </div>
                  </InlineStack>
                </Box>

                {orders.length === 0 ? (
                  <EmptyState
                    heading="注文データがありません"
                    image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                  >
                    <p>検索条件に一致する注文データが見つかりません。</p>
                  </EmptyState>
                ) : (
                  <DataTable
                    columnContentTypes={[
                      "text",
                      "text",
                      "text",
                      "text",
                      "text",
                      "text",
                      "text",
                      "text",
                    ]}
                    headings={[
                      <Button plain monochrome onClick={() => handleSortChange("orderNumber")}>注文番号</Button>,
                      <Button plain monochrome onClick={() => handleSortChange("customerName")}>顧客名</Button>,
                      <Button plain monochrome onClick={() => handleSortChange("customerEmail")}>メールアドレス</Button>,
                      <Button plain monochrome onClick={() => handleSortChange("startDate")}>開始日</Button>,
                      <Button plain monochrome onClick={() => handleSortChange("endDate")}>終了日</Button>,
                      <Button plain monochrome onClick={() => handleSortChange("totalAmount")}>金額</Button>,
                      <Button plain monochrome onClick={() => handleSortChange("paymentStatus")}>支払い状況</Button>,
                      <Button plain monochrome onClick={() => handleSortChange("createdAt")}>作成日時</Button>,
                    ]}
                    rows={rows}
                    hoverable
                    increasedTableDensity
                  />
                )}

                {pagination.totalPages > 1 && (
                  <Box paddingBlockStart="400">
                    <Pagination
                      hasPrevious={pagination.currentPage > 1}
                      onPrevious={() => handlePageChange(pagination.currentPage - 1)}
                      hasNext={pagination.currentPage < pagination.totalPages}
                      onNext={() => handlePageChange(pagination.currentPage + 1)}
                    />
                    <div style={{ textAlign: "center", marginTop: "10px" }}>
                      <Text as="span">全{pagination.totalItems}件中 {(pagination.currentPage - 1) * 20 + 1}-{Math.min(pagination.currentPage * 20, pagination.totalItems)}件を表示</Text>
                    </div>
                  </Box>
                )}
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
