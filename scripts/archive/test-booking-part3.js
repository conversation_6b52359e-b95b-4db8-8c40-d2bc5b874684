/**
 * 予約システムテスト - パート3: 高度な予約テスト
 * 
 * このスクリプトでは以下を行います：
 * 1. 複数商品の同じ日程での予約
 * 2. 連続した予約
 * 3. 長期間の予約
 * 4. 予約のキャンセル
 * 5. 仮予約から確定予約への変更
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// テスト結果を保存する配列
const testResults = [];

/**
 * テスト結果を記録する関数
 */
function logTestResult(testName, success, details = {}) {
  const result = {
    testName,
    success,
    timestamp: new Date().toISOString(),
    ...details
  };
  
  testResults.push(result);
  
  // コンソールにも出力
  console.log(`[${success ? 'SUCCESS' : 'FAILURE'}] ${testName}`);
  if (details.message) {
    console.log(`  ${details.message}`);
  }
  if (details.error) {
    console.error(`  Error: ${details.error}`);
  }
}

/**
 * テスト用商品を取得する関数
 */
async function getTestProducts() {
  console.log("テスト用商品を取得しています...");
  
  try {
    const products = await prisma.product.findMany({
      where: {
        sku: {
          startsWith: 'TEST-'
        }
      }
    });
    
    console.log(`${products.length}件のテスト用商品が見つかりました`);
    
    if (products.length === 0) {
      console.log("テスト用商品が見つかりませんでした。先にパート1のテストを実行してください。");
    } else {
      products.forEach((product, index) => {
        console.log(`商品 ${index + 1}: ${product.title} (ID: ${product.id}, SKU: ${product.sku})`);
      });
    }
    
    return products;
  } catch (error) {
    console.error("テスト用商品の取得中にエラーが発生しました:", error);
    return [];
  }
}

/**
 * 予約を作成する関数
 */
async function createBooking(productId, startDate, endDate, customerEmail, status = "CONFIRMED") {
  try {
    const bookingId = `BOOK-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
    
    const booking = await prisma.booking.create({
      data: {
        productId,
        shopifyProductId: productId, // 同じIDを使用
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        status,
        bookingType: status === "CONFIRMED" ? "CONFIRMED" : "PROVISIONAL",
        customerEmail,
        customerName: "テストユーザー",
        shop: "peaces-test-block.myshopify.com",
        bookingId
      }
    });
    
    // 在庫カレンダーも更新
    const start = new Date(startDate);
    const end = new Date(endDate);
    const dateRange = [];
    
    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      dateRange.push(new Date(date));
    }
    
    for (const date of dateRange) {
      await prisma.inventoryCalendar.upsert({
        where: {
          shop_productId_date: {
            shop: "peaces-test-block.myshopify.com",
            productId,
            date
          }
        },
        update: {
          isAvailable: false,
          unavailableReason: "reserved",
          bookingId: booking.id
        },
        create: {
          shop: "peaces-test-block.myshopify.com",
          productId,
          shopifyProductId: productId,
          date,
          isAvailable: false,
          unavailableReason: "reserved",
          bookingId: booking.id
        }
      });
    }
    
    return booking;
  } catch (error) {
    console.error("予約作成エラー:", error);
    throw error;
  }
}

/**
 * テストケース1: 複数商品の同じ日程での予約
 */
async function testMultipleProductsSameDate(products) {
  console.log("\nテストケース1: 複数商品の同じ日程での予約をテストしています...");
  
  try {
    const startDate = "2025-07-01";
    const endDate = "2025-07-03";
    const customerEmail = "<EMAIL>";
    
    console.log(`予約期間: ${startDate} 〜 ${endDate}`);
    console.log(`顧客メール: ${customerEmail}`);
    
    const bookings = [];
    
    for (const product of products) {
      console.log(`商品「${product.title}」の予約を作成しています...`);
      
      const booking = await createBooking(
        product.id,
        startDate,
        endDate,
        customerEmail
      );
      
      console.log(`予約が作成されました (ID: ${booking.id}, 予約ID: ${booking.bookingId})`);
      bookings.push(booking);
    }
    
    logTestResult("複数商品の同じ日程での予約", bookings.length === products.length, {
      message: `${bookings.length}件の商品予約が作成されました`,
      bookingIds: bookings.map(b => b.id)
    });
    
    return bookings;
  } catch (error) {
    console.error("複数商品の同じ日程での予約テストエラー:", error);
    
    logTestResult("複数商品の同じ日程での予約", false, {
      error: error.message
    });
    
    return [];
  }
}

/**
 * テストケース2: 連続した予約
 */
async function testConsecutiveBookings(product) {
  console.log(`\nテストケース2: 連続した予約をテストしています...`);
  
  try {
    const startDate1 = "2025-08-01";
    const endDate1 = "2025-08-03";
    const startDate2 = "2025-08-04";
    const endDate2 = "2025-08-06";
    const customerEmail = "<EMAIL>";
    
    console.log(`商品: ${product.title}`);
    console.log(`予約1: ${startDate1} 〜 ${endDate1}`);
    console.log(`予約2: ${startDate2} 〜 ${endDate2}`);
    
    const booking1 = await createBooking(
      product.id,
      startDate1,
      endDate1,
      customerEmail
    );
    
    const booking2 = await createBooking(
      product.id,
      startDate2,
      endDate2,
      customerEmail
    );
    
    console.log(`予約1が作成されました (ID: ${booking1.id}, 予約ID: ${booking1.bookingId})`);
    console.log(`予約2が作成されました (ID: ${booking2.id}, 予約ID: ${booking2.bookingId})`);
    
    logTestResult("連続した予約", true, {
      message: `連続した2つの予約が作成されました`,
      bookingIds: [booking1.id, booking2.id]
    });
    
    return [booking1, booking2];
  } catch (error) {
    console.error("連続した予約テストエラー:", error);
    
    logTestResult("連続した予約", false, {
      error: error.message
    });
    
    return [];
  }
}

/**
 * テストケース3: 長期間の予約
 */
async function testLongTermBooking(product) {
  console.log(`\nテストケース3: 長期間の予約をテストしています...`);
  
  try {
    const startDate = "2025-09-01";
    const endDate = "2025-09-14"; // 14日間
    const customerEmail = "<EMAIL>";
    
    console.log(`商品: ${product.title}`);
    console.log(`予約期間: ${startDate} 〜 ${endDate} (14日間)`);
    
    const booking = await createBooking(
      product.id,
      startDate,
      endDate,
      customerEmail
    );
    
    console.log(`予約が作成されました (ID: ${booking.id}, 予約ID: ${booking.bookingId})`);
    
    // 予約日数を計算
    const start = new Date(startDate);
    const end = new Date(endDate);
    const days = Math.round((end - start) / (1000 * 60 * 60 * 24)) + 1;
    
    logTestResult("長期間の予約", days === 14, {
      message: `${days}日間の予約が作成されました`,
      bookingId: booking.id
    });
    
    return booking;
  } catch (error) {
    console.error("長期間の予約テストエラー:", error);
    
    logTestResult("長期間の予約", false, {
      error: error.message
    });
    
    return null;
  }
}

/**
 * テストケース4: 予約のキャンセル
 */
async function testBookingCancellation(product) {
  console.log(`\nテストケース4: 予約のキャンセルをテストしています...`);
  
  try {
    const startDate = "2025-10-01";
    const endDate = "2025-10-03";
    const customerEmail = "<EMAIL>";
    
    console.log(`商品: ${product.title}`);
    console.log(`予約期間: ${startDate} 〜 ${endDate}`);
    
    // 予約を作成
    const booking = await createBooking(
      product.id,
      startDate,
      endDate,
      customerEmail
    );
    
    console.log(`予約が作成されました (ID: ${booking.id}, 予約ID: ${booking.bookingId})`);
    
    // 予約をキャンセル
    const cancelledBooking = await prisma.booking.update({
      where: { id: booking.id },
      data: { status: "CANCELLED" }
    });
    
    console.log(`予約がキャンセルされました (ID: ${cancelledBooking.id}, ステータス: ${cancelledBooking.status})`);
    
    // キャンセルされた予約の在庫カレンダーを更新
    await prisma.inventoryCalendar.updateMany({
      where: { bookingId: booking.id },
      data: {
        isAvailable: true,
        unavailableReason: null,
        bookingId: null
      }
    });
    
    console.log("在庫カレンダーが更新されました");
    
    // 在庫カレンダーが正しく更新されたか確認
    const inventoryCalendars = await prisma.inventoryCalendar.findMany({
      where: {
        productId: product.id,
        date: {
          gte: new Date(startDate),
          lte: new Date(endDate)
        }
      }
    });
    
    const allAvailable = inventoryCalendars.every(calendar => calendar.isAvailable);
    
    logTestResult("予約のキャンセル", cancelledBooking.status === "CANCELLED" && allAvailable, {
      message: `予約がキャンセルされ、在庫カレンダーが更新されました`,
      bookingId: cancelledBooking.id
    });
    
    return cancelledBooking;
  } catch (error) {
    console.error("予約のキャンセルテストエラー:", error);
    
    logTestResult("予約のキャンセル", false, {
      error: error.message
    });
    
    return null;
  }
}

/**
 * テストケース5: 仮予約から確定予約への変更
 */
async function testProvisionalToConfirmed(product) {
  console.log(`\nテストケース5: 仮予約から確定予約への変更をテストしています...`);
  
  try {
    const startDate = "2025-11-01";
    const endDate = "2025-11-03";
    const customerEmail = "<EMAIL>";
    
    console.log(`商品: ${product.title}`);
    console.log(`予約期間: ${startDate} 〜 ${endDate}`);
    
    // 仮予約を作成
    const provisionalBooking = await createBooking(
      product.id,
      startDate,
      endDate,
      customerEmail,
      "PROVISIONAL"
    );
    
    console.log(`仮予約が作成されました (ID: ${provisionalBooking.id}, ステータス: ${provisionalBooking.status})`);
    
    // 仮予約を確定予約に変更
    const confirmedBooking = await prisma.booking.update({
      where: { id: provisionalBooking.id },
      data: { 
        status: "CONFIRMED",
        bookingType: "CONFIRMED"
      }
    });
    
    console.log(`予約が確定されました (ID: ${confirmedBooking.id}, ステータス: ${confirmedBooking.status})`);
    
    logTestResult("仮予約から確定予約への変更", confirmedBooking.status === "CONFIRMED", {
      message: `仮予約が確定予約に変更されました`,
      bookingId: confirmedBooking.id
    });
    
    return confirmedBooking;
  } catch (error) {
    console.error("仮予約から確定予約への変更テストエラー:", error);
    
    logTestResult("仮予約から確定予約への変更", false, {
      error: error.message
    });
    
    return null;
  }
}

/**
 * メイン関数
 */
async function runTests() {
  console.log("=== 予約システムテスト - パート3: 高度な予約テスト ===\n");
  
  try {
    // テスト用商品を取得
    const products = await getTestProducts();
    
    if (products.length < 3) {
      console.error("テスト用商品が不足しています。少なくとも3つの商品が必要です。テストを中止します。");
      return;
    }
    
    // テストケース1: 複数商品の同じ日程での予約
    const multipleBookings = await testMultipleProductsSameDate(products);
    
    // テストケース2: 連続した予約
    const consecutiveBookings = await testConsecutiveBookings(products[0]);
    
    // テストケース3: 長期間の予約
    const longTermBooking = await testLongTermBooking(products[1]);
    
    // テストケース4: 予約のキャンセル
    const cancelledBooking = await testBookingCancellation(products[2]);
    
    // テストケース5: 仮予約から確定予約への変更
    const confirmedBooking = await testProvisionalToConfirmed(products[0]);
    
    // テスト結果のサマリーを表示
    console.log("\n=== テスト結果サマリー ===");
    const successCount = testResults.filter(r => r.success).length;
    const failureCount = testResults.length - successCount;
    
    console.log(`実行したテスト数: ${testResults.length}`);
    console.log(`成功: ${successCount}`);
    console.log(`失敗: ${failureCount}`);
    console.log("============================\n");
    
    // 詳細なテスト結果を表示
    console.log("詳細なテスト結果:");
    testResults.forEach((result, index) => {
      console.log(`${index + 1}. [${result.success ? 'SUCCESS' : 'FAILURE'}] ${result.testName}`);
      if (result.message) {
        console.log(`   ${result.message}`);
      }
    });
    
  } catch (error) {
    console.error("テスト実行中にエラーが発生しました:", error);
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}

// テストを実行
runTests();
