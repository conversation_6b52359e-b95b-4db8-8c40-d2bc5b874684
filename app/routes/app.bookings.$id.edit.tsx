import { json, type LoaderFunctionArgs, ActionFunctionArgs, redirect } from "@remix-run/node";
import { useLoaderData, useSubmit, useNavigate, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  BlockStack,
  Button,
  InlineStack,
  TextField,
  Select,
  Banner,
  Divider,
  LegacyStack
} from "@shopify/polaris";
import { useState, useCallback } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { parseISO, format, addDays } from "date-fns";
import { ShopifyCustomerDisplay } from "../components/ShopifyCustomerDisplay";

/**
 * 予約編集ページのローダー
 */
export async function loader({ request, params }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  const { id } = params;

  if (!id) {
    return redirect("/app/bookings");
  }

  try {
    // 予約データを取得
    const booking = await prisma.booking.findFirst({
      where: {
        id,
        shop,
      },
    });

    if (!booking) {
      return json({
        booking: null,
        error: "予約データが見つかりません",
      });
    }

    // 商品情報を取得
    let productInfo = null;
    if (booking.productId) {
      const product = await prisma.product.findFirst({
        where: {
          id: booking.productId,
          shop,
        },
      });

      if (product) {
        productInfo = {
          id: product.id,
          title: product.title,
          sku: product.sku,
        };
      }
    }

    return json({
      booking: {
        ...booking,
        startDate: booking.startDate ? new Date(booking.startDate).toISOString() : null,
        endDate: booking.endDate ? new Date(booking.endDate).toISOString() : null,
        createdAt: booking.createdAt ? new Date(booking.createdAt).toISOString() : null,
        updatedAt: booking.updatedAt ? new Date(booking.updatedAt).toISOString() : null,
      },
      productInfo,
      shopifyShop: process.env.SHOPIFY_SHOP || shop,
      error: null,
    });
  } catch (error) {
    console.error("予約データ取得エラー:", error);
    return json({
      booking: null,
      productInfo: null,
      shopifyShop: process.env.SHOPIFY_SHOP || shop,
      error: "予約データの取得中にエラーが発生しました",
    });
  }
}

/**
 * 予約編集ページのアクション
 */
export async function action({ request, params }: ActionFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  const { id } = params;

  if (!id) {
    return redirect("/app/bookings");
  }

  const formData = await request.formData();

  try {
    // 予約データを取得
    const booking = await prisma.booking.findFirst({
      where: {
        id,
        shop,
      },
    });

    if (!booking) {
      return json({
        success: false,
        error: "予約データが見つかりません",
      });
    }

    // フォームデータを取得
    const startDate = formData.get("startDate") as string;
    const endDate = formData.get("endDate") as string;
    const customerName = formData.get("customerName") as string;
    const customerEmail = formData.get("customerEmail") as string;
    const customerPhone = formData.get("customerPhone") as string;
    const totalAmount = formData.get("totalAmount") as string;
    const depositAmount = formData.get("depositAmount") as string;
    const notes = formData.get("notes") as string;

    // 必須項目のバリデーション
    if (!startDate || !endDate || !customerName) {
      return json({
        success: false,
        error: "必須項目が入力されていません",
      });
    }

    // 予約データを更新
    await prisma.booking.update({
      where: {
        id,
      },
      data: {
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        customerName,
        customerEmail: customerEmail || null,
        customerPhone: customerPhone || null,
        totalAmount: totalAmount ? parseFloat(totalAmount) : null,
        depositAmount: depositAmount ? parseFloat(depositAmount) : null,
        notes: notes || null,
        updatedAt: new Date(),
      },
    });

    return redirect(`/app/bookings/${id}`);
  } catch (error) {
    console.error("予約データ更新エラー:", error);
    return json({
      success: false,
      error: "予約データの更新中にエラーが発生しました",
    });
  }
}

/**
 * 予約編集ページ
 */
export default function BookingEditPage() {
  const { booking: initialBooking, productInfo, shopifyShop, error } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const navigate = useNavigate();

  // 編集可能な予約データの状態
  const [booking, setBooking] = useState(initialBooking);

  // 日付選択の状態
  const [{ month, year }, setDate] = useState(() => {
    const date = booking?.startDate ? new Date(booking.startDate) : new Date();
    return {
      month: date.getMonth(),
      year: date.getFullYear(),
    };
  });

  // 選択された日付範囲
  const [selectedDates, setSelectedDates] = useState({
    start: booking?.startDate ? new Date(booking.startDate) : new Date(),
    end: booking?.endDate ? new Date(booking.endDate) : addDays(new Date(), 1),
  });

  // フィールド変更ハンドラー
  const handleFieldChange = (field: string, value: any) => {
    setBooking(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 日付選択ハンドラー
  const handleDateChange = useCallback(
    (range: { start: Date; end: Date }) => {
      setSelectedDates(range);
    },
    []
  );

  // 月変更ハンドラー
  const handleMonthChange = useCallback(
    (month: number, year: number) => {
      setDate({ month, year });
    },
    []
  );

  // キャンセルハンドラー
  const handleCancel = useCallback(() => {
    navigate(`/app/bookings/${booking.id}`);
  }, [navigate, booking]);

  // フォーム送信ハンドラー
  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const form = event.currentTarget;
    submit(form, { method: 'post' });
  };

  if (error || !booking) {
    return (
      <Page
        title="予約編集"
        backAction={{ content: "予約一覧に戻る", url: "/app/bookings" }}
      >
        <Layout>
          <Layout.Section>
            <Card>
              <Box padding="400">
                <Banner tone="critical">
                  <p>{error || "予約データが見つかりません"}</p>
                </Banner>
              </Box>
            </Card>
          </Layout.Section>
        </Layout>
      </Page>
    );
  }

  return (
    <Page
      title="予約編集"
      backAction={{ content: "詳細に戻る", url: `/app/bookings/${booking.id}` }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Box padding="400">
              <Form method="post" onSubmit={handleSubmit}>
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2">
                    予約情報の編集
                  </Text>

                  <Divider />

                  <Text variant="headingMd" as="h3">
                    商品情報
                  </Text>
                  <Text as="p">
                    {productInfo?.title || "不明な商品"} ({productInfo?.sku || "SKU不明"})
                  </Text>

                  <Divider />

                  <Text variant="headingMd" as="h3">
                    予約期間
                  </Text>

                  <InlineStack gap="400">
                    <TextField
                      label="開始日"
                      name="startDate"
                      type="date"
                      value={format(selectedDates.start, "yyyy-MM-dd")}
                      onChange={(value) => {
                        try {
                          const date = parseISO(value);
                          setSelectedDates({
                            start: date,
                            end: selectedDates.end < date ? date : selectedDates.end
                          });
                        } catch (e) {
                          console.error("日付の解析エラー:", e);
                        }
                      }}
                      autoComplete="off"
                      required
                    />
                    <TextField
                      label="終了日"
                      name="endDate"
                      type="date"
                      value={format(selectedDates.end, "yyyy-MM-dd")}
                      onChange={(value) => {
                        try {
                          const date = parseISO(value);
                          setSelectedDates({
                            start: selectedDates.start,
                            end: date < selectedDates.start ? selectedDates.start : date
                          });
                        } catch (e) {
                          console.error("日付の解析エラー:", e);
                        }
                      }}
                      autoComplete="off"
                      required
                    />
                  </InlineStack>

                  <Divider />

                  <Text variant="headingMd" as="h3">
                    顧客情報
                  </Text>
                  {booking.customerId ? (
                    <ShopifyCustomerDisplay
                      customerId={booking.customerId}
                      fallbackName={booking.customerName}
                      fallbackEmail={booking.customerEmail}
                      fallbackPhone={booking.customerPhone}
                      shopifyShop={shopifyShop}
                      showDetails={true}
                      linkToShopify={true}
                    />
                  ) : (
                    <Text>顧客IDが設定されていません。顧客情報を直接入力してください。</Text>
                  )}
                  <TextField
                    label="顧客名"
                    name="customerName"
                    value={booking.customerName || ""}
                    onChange={(value) => handleFieldChange("customerName", value)}
                    autoComplete="off"
                    required
                  />
                  <TextField
                    label="メールアドレス"
                    name="customerEmail"
                    type="email"
                    value={booking.customerEmail || ""}
                    onChange={(value) => handleFieldChange("customerEmail", value)}
                    autoComplete="off"
                  />
                  <TextField
                    label="電話番号"
                    name="customerPhone"
                    type="tel"
                    value={booking.customerPhone || ""}
                    onChange={(value) => handleFieldChange("customerPhone", value)}
                    autoComplete="off"
                  />

                  <Divider />

                  <Text variant="headingMd" as="h3">
                    金額情報
                  </Text>
                  <InlineStack gap="400">
                    <TextField
                      label="合計金額"
                      name="totalAmount"
                      type="number"
                      value={booking.totalAmount?.toString() || ""}
                      onChange={(value) => handleFieldChange("totalAmount", value ? parseFloat(value) : null)}
                      suffix="円"
                      autoComplete="off"
                    />
                    <TextField
                      label="デポジット"
                      name="depositAmount"
                      type="number"
                      value={booking.depositAmount?.toString() || ""}
                      onChange={(value) => handleFieldChange("depositAmount", value ? parseFloat(value) : null)}
                      suffix="円"
                      autoComplete="off"
                    />
                  </InlineStack>

                  <Divider />

                  <TextField
                    label="備考"
                    name="notes"
                    value={booking.notes || ""}
                    onChange={(value) => handleFieldChange("notes", value)}
                    multiline={3}
                    autoComplete="off"
                  />

                  <InlineStack gap="400" align="end">
                    <Button onClick={handleCancel}>キャンセル</Button>
                    <Button primary submit>
                      保存
                    </Button>
                  </InlineStack>
                </BlockStack>
              </Form>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
