/* RentalCalendar.css */

.rental-calendar {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  max-width: 100%;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* ヘッダー */
.rental-calendar__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 8px;
}

.rental-calendar__nav-button {
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;
}

.rental-calendar__nav-button:hover {
  background-color: #f6f6f6;
  border-color: #ccc;
}

.rental-calendar__nav-button:active {
  transform: translateY(1px);
}

/* 月表示コンテナ */
.rental-calendar__months {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.rental-calendar__month {
  flex: 1;
  min-width: 280px;
}

@media (max-width: 768px) {
  .rental-calendar__months {
    flex-direction: column;
  }
}

.rental-calendar__month-title {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #202223;
}

/* テーブル */
.rental-calendar__table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.rental-calendar__table th {
  text-align: center;
  padding: 8px 0;
  font-weight: 500;
  font-size: 14px;
  color: #637381;
  border-bottom: 1px solid #e1e3e5;
}

.rental-calendar__weekday--sunday {
  color: #bf0711 !important;
}

.rental-calendar__table td {
  text-align: center;
  padding: 2px;
  height: 40px;
  position: relative;
}

.rental-calendar__cell--other-month {
  opacity: 0.3;
}

/* 日付ボタン */
.rental-calendar__day {
  width: 36px;
  height: 36px;
  border: 1px solid transparent;
  border-radius: 4px;
  background: none;
  cursor: pointer;
  font-size: 14px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  padding: 0;
  margin: 0 auto;
}

.rental-calendar__day:hover:not(:disabled) {
  background-color: #f1f2f4;
  border-color: #c4cdd5;
}

.rental-calendar__day:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.rental-calendar__day-number {
  font-size: 14px;
  line-height: 1;
}

/* 日付の状態 */
.rental-calendar__day--today {
  font-weight: bold;
  border-color: #000;
  background-color: #f4f6f8;
}

.rental-calendar__day--holiday {
  color: #bf0711;
  background-color: rgba(253, 201, 201, 0.2);
}

.rental-calendar__day--booked {
  pointer-events: none;
}

.rental-calendar__day--confirmed {
  background-color: rgba(0, 128, 96, 0.2);
  color: #004c3f;
}

.rental-calendar__day--provisional {
  background-color: rgba(255, 184, 0, 0.2);
  color: #8a6116;
}

.rental-calendar__day--maintenance {
  background-color: rgba(171, 171, 171, 0.3);
  color: #637381;
  pointer-events: none;
}

.rental-calendar__day--selected {
  background-color: #008060;
  color: white;
  border-color: #008060;
}

.rental-calendar__day--selected:hover {
  background-color: #006e52;
}

.rental-calendar__day--in-range {
  background-color: rgba(0, 128, 96, 0.1);
  border-color: rgba(0, 128, 96, 0.3);
}

.rental-calendar__day--disabled {
  background-color: #f6f6f6;
  color: #c4cdd5;
}

/* マーカー */
.rental-calendar__marker {
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  font-weight: 600;
  line-height: 1;
}

.rental-calendar__day--confirmed .rental-calendar__marker {
  color: #004c3f;
}

.rental-calendar__day--provisional .rental-calendar__marker {
  color: #8a6116;
}

.rental-calendar__day--maintenance .rental-calendar__marker {
  color: #637381;
}

.rental-calendar__day--holiday .rental-calendar__marker {
  color: #bf0711;
}

/* 凡例 */
.rental-calendar__legend {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 12px;
  background-color: #f9fafb;
  border-radius: 4px;
  font-size: 14px;
}

.rental-calendar__legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rental-calendar__legend-marker {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.rental-calendar__legend-marker--confirmed {
  background-color: rgba(0, 128, 96, 0.2);
  color: #004c3f;
}

.rental-calendar__legend-marker--provisional {
  background-color: rgba(255, 184, 0, 0.2);
  color: #8a6116;
}

.rental-calendar__legend-marker--maintenance {
  background-color: rgba(171, 171, 171, 0.3);
  color: #637381;
}

.rental-calendar__legend-marker--holiday {
  background-color: rgba(253, 201, 201, 0.2);
  color: #bf0711;
}

/* アニメーション */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.rental-calendar__month {
  animation: fadeIn 0.3s ease-out;
}

/* アクセシビリティ */
.rental-calendar__day:focus {
  outline: 2px solid #008060;
  outline-offset: 2px;
}

/* 高コントラストモード対応 */
@media (prefers-contrast: high) {
  .rental-calendar__day--holiday {
    background-color: #ffcccc;
  }
  
  .rental-calendar__day--confirmed {
    background-color: #ccffcc;
  }
  
  .rental-calendar__day--provisional {
    background-color: #ffeecc;
  }
  
  .rental-calendar__day--maintenance {
    background-color: #e0e0e0;
  }
}

/* ダークモード対応 */
@media (prefers-color-scheme: dark) {
  .rental-calendar {
    background: #202223;
    color: #e3e5e7;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  
  .rental-calendar__nav-button {
    border-color: #404244;
    color: #e3e5e7;
  }
  
  .rental-calendar__nav-button:hover {
    background-color: #303234;
    border-color: #505254;
  }
  
  .rental-calendar__month-title {
    color: #e3e5e7;
  }
  
  .rental-calendar__table th {
    color: #8c9196;
    border-bottom-color: #404244;
  }
  
  .rental-calendar__day {
    color: #e3e5e7;
  }
  
  .rental-calendar__day:hover:not(:disabled) {
    background-color: #303234;
    border-color: #505254;
  }
  
  .rental-calendar__day--today {
    background-color: #303234;
    border-color: #e3e5e7;
  }
  
  .rental-calendar__legend {
    background-color: #303234;
  }
}