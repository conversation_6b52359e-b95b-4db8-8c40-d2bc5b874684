/**
 * Shopify同期テストスクリプト
 * 
 * このスクリプトは、Shopifyとの同期が正しく行われているかをテストします。
 * 
 * 実行方法: npx tsx scripts/test-shopify-sync.ts
 */

import dotenv from 'dotenv';
import { prisma } from '../app/db.server';
import { GraphQLClient, gql } from 'graphql-request';

// 環境変数の読み込み
dotenv.config();

// Shopify GraphQL APIクライアントの初期化
function createShopifyClient() {
  const shop = process.env.SHOPIFY_SHOP;
  const accessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
  
  if (!shop || !accessToken) {
    throw new Error('SHOPIFY_SHOP または SHOPIFY_ADMIN_API_ACCESS_TOKEN が設定されていません');
  }
  
  return new GraphQLClient(
    `https://${shop}/admin/api/2024-01/graphql.json`,
    {
      headers: {
        'X-Shopify-Access-Token': accessToken,
        'Content-Type': 'application/json',
      },
    }
  );
}

/**
 * Shopifyから注文を取得
 */
async function getShopifyOrders() {
  const client = createShopifyClient();
  
  const GET_ORDERS_QUERY = gql`
    query getOrders($first: Int!) {
      orders(first: $first, sortKey: CREATED_AT, reverse: true) {
        edges {
          node {
            id
            name
            createdAt
            displayFinancialStatus
            customer {
              id
              displayName
              email
            }
            lineItems(first: 10) {
              edges {
                node {
                  title
                  quantity
                  variant {
                    id
                    title
                    product {
                      id
                      title
                    }
                  }
                }
              }
            }
            metafields(first: 10) {
              edges {
                node {
                  namespace
                  key
                  value
                }
              }
            }
          }
        }
      }
    }
  `;
  
  try {
    const response = await client.request(GET_ORDERS_QUERY, {
      first: 10
    });
    
    return response.orders.edges.map((edge: any) => edge.node);
  } catch (error) {
    console.error('Shopify注文取得エラー:', error);
    throw error;
  }
}

/**
 * Prismaから予約を取得
 */
async function getPrismaBookings() {
  try {
    const bookings = await prisma.booking.findMany({
      where: {
        shopifyOrderId: {
          not: null
        }
      },
      orderBy: {
        created_at: 'desc'
      },
      take: 10,
      include: {
        product: true
      }
    });
    
    return bookings;
  } catch (error) {
    console.error('Prisma予約取得エラー:', error);
    throw error;
  }
}

/**
 * Shopify注文とPrisma予約の同期状態を確認
 */
async function checkSyncStatus() {
  console.log('Shopify同期状態のチェックを開始します...');
  
  try {
    // Shopify注文を取得
    console.log('\n=== Shopify注文の取得 ===');
    const shopifyOrders = await getShopifyOrders();
    console.log(`${shopifyOrders.length}件の注文が見つかりました`);
    
    // Prisma予約を取得
    console.log('\n=== Prisma予約の取得 ===');
    const prismaBookings = await getPrismaBookings();
    console.log(`${prismaBookings.length}件の予約が見つかりました`);
    
    // 同期状態を確認
    console.log('\n=== 同期状態の確認 ===');
    
    // Shopify注文IDを持つPrisma予約の数
    const bookingsWithOrderId = prismaBookings.filter(booking => booking.shopifyOrderId);
    console.log(`Shopify注文IDを持つPrisma予約: ${bookingsWithOrderId.length}件`);
    
    // Prisma予約IDを持つShopify注文の数（メタフィールドで確認）
    const ordersWithBookingId = shopifyOrders.filter(order => {
      const metafields = order.metafields?.edges || [];
      return metafields.some((edge: any) => 
        edge.node.namespace === 'custom' && 
        edge.node.key === 'booking_id' && 
        edge.node.value
      );
    });
    console.log(`Prisma予約IDを持つShopify注文: ${ordersWithBookingId.length}件`);
    
    // 詳細な同期状態を表示
    console.log('\n=== 詳細な同期状態 ===');
    
    // Prisma予約の詳細を表示
    console.log('\nPrisma予約の詳細:');
    for (const booking of prismaBookings.slice(0, 5)) {
      console.log(`- 予約ID: ${booking.id}`);
      console.log(`  予約番号: ${booking.bookingId}`);
      console.log(`  商品: ${booking.product?.title || '不明'}`);
      console.log(`  Shopify注文ID: ${booking.shopifyOrderId || '未設定'}`);
      console.log(`  Shopify注文名: ${booking.shopifyOrderName || '未設定'}`);
      console.log(`  ステータス: ${booking.status}`);
      console.log('');
    }
    
    // Shopify注文の詳細を表示
    console.log('\nShopify注文の詳細:');
    for (const order of shopifyOrders.slice(0, 5)) {
      const bookingIdMetafield = (order.metafields?.edges || []).find((edge: any) => 
        edge.node.namespace === 'custom' && 
        edge.node.key === 'booking_id'
      );
      
      const bookingId = bookingIdMetafield ? bookingIdMetafield.node.value : '未設定';
      
      console.log(`- 注文ID: ${order.id.replace('gid://shopify/Order/', '')}`);
      console.log(`  注文番号: ${order.name}`);
      console.log(`  作成日時: ${new Date(order.createdAt).toLocaleString()}`);
      console.log(`  顧客: ${order.customer?.displayName || '不明'}`);
      console.log(`  予約ID: ${bookingId}`);
      console.log(`  商品: ${order.lineItems.edges.map((edge: any) => edge.node.title).join(', ')}`);
      console.log('');
    }
    
    // 同期の問題を特定
    console.log('\n=== 同期の問題 ===');
    
    // Shopify注文IDを持つがShopifyに存在しない予約
    const orphanedBookings = bookingsWithOrderId.filter(booking => {
      const shopifyOrderId = booking.shopifyOrderId?.replace('gid://shopify/Order/', '');
      return !shopifyOrders.some(order => order.id.includes(shopifyOrderId));
    });
    
    if (orphanedBookings.length > 0) {
      console.log(`Shopifyに存在しない注文IDを持つ予約: ${orphanedBookings.length}件`);
      for (const booking of orphanedBookings.slice(0, 3)) {
        console.log(`- 予約ID: ${booking.id}, Shopify注文ID: ${booking.shopifyOrderId}`);
      }
    } else {
      console.log('Shopifyに存在しない注文IDを持つ予約はありません');
    }
    
    // 予約IDを持つがPrismaに存在しない注文
    const orphanedOrders = ordersWithBookingId.filter(order => {
      const bookingIdMetafield = (order.metafields?.edges || []).find((edge: any) => 
        edge.node.namespace === 'custom' && 
        edge.node.key === 'booking_id'
      );
      
      const bookingId = bookingIdMetafield ? bookingIdMetafield.node.value : null;
      
      return bookingId && !prismaBookings.some(booking => booking.id === bookingId);
    });
    
    if (orphanedOrders.length > 0) {
      console.log(`Prismaに存在しない予約IDを持つ注文: ${orphanedOrders.length}件`);
      for (const order of orphanedOrders.slice(0, 3)) {
        const bookingIdMetafield = (order.metafields?.edges || []).find((edge: any) => 
          edge.node.namespace === 'custom' && 
          edge.node.key === 'booking_id'
        );
        
        const bookingId = bookingIdMetafield ? bookingIdMetafield.node.value : null;
        
        console.log(`- 注文ID: ${order.id.replace('gid://shopify/Order/', '')}, 予約ID: ${bookingId}`);
      }
    } else {
      console.log('Prismaに存在しない予約IDを持つ注文はありません');
    }
    
    return {
      shopifyOrders,
      prismaBookings,
      bookingsWithOrderId,
      ordersWithBookingId,
      orphanedBookings,
      orphanedOrders
    };
  } catch (error) {
    console.error('同期状態のチェック中にエラーが発生しました:', error);
    throw error;
  }
}

/**
 * メイン処理
 */
async function main() {
  try {
    await checkSyncStatus();
  } catch (error) {
    console.error('テスト実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
