/**
 * 商品検索結果と予約状況を表示するコンポーネント
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Text,
  Card,
  BlockStack,
  Badge,
  InlineStack,
  Spinner,
  Banner
} from '@shopify/polaris';
import { formatSkuWithHyphens } from '../utils/sku-formatter.js';

// 商品データの型定義
interface Product {
  id: string;
  title: string;
  sku: string;
  status: string;
  shopifyId: string;
  basicInfo: any;
  bookings: Booking[];
  bookingCount: number;
}

// 予約データの型定義
interface Booking {
  id: string;
  bookingId: string;
  startDate: string;
  endDate: string;
  rentalDays: number;
  status: string;
  bookingType: string;
  customerName: string;
  customerEmail: string;
  totalAmount: string;
  createdAt: string;
}

// APIレスポンスの型定義
interface SearchResponse {
  products: Product[];
  message: string;
  searchQuery?: string;
  error?: string;
}

// コンポーネントのProps
interface ProductSearchResultsProps {
  searchQuery: string;
}

export function ProductSearchResults({ searchQuery }: ProductSearchResultsProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string>("");

  // 商品ステータスのバッジを生成
  const getProductStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case "AVAILABLE":
        return <Badge tone="success">利用可能</Badge>;
      case "MAINTENANCE":
        return <Badge tone="warning">メンテナンス中</Badge>;
      case "DAMAGED":
        return <Badge tone="critical">破損</Badge>;
      case "UNAVAILABLE":
        return <Badge tone="critical">利用不可</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // 予約ステータスのバッジを生成
  const getStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case "DRAFT":
        return <Badge tone="info">ドラフト</Badge>;
      case "PROVISIONAL":
        return <Badge tone="attention">仮予約</Badge>;
      case "CONFIRMED":
        return <Badge tone="success">確定</Badge>;
      case "CANCELLED":
        return <Badge tone="critical">キャンセル</Badge>;
      case "COMPLETED":
        return <Badge tone="success">完了</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // 商品検索を実行
  const searchProducts = async (query: string) => {
    if (!query || query.trim() === "") {
      setProducts([]);
      setMessage("");
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/products/search?q=${encodeURIComponent(query)}`);
      const data: SearchResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "商品検索に失敗しました");
      }

      setProducts(data.products);
      setMessage(data.message);
      console.log(`商品検索結果: ${data.products.length}件`, data.products);

    } catch (err) {
      console.error("商品検索エラー:", err);
      setError(err instanceof Error ? err.message : "商品検索中にエラーが発生しました");
      setProducts([]);
      setMessage("");
    } finally {
      setLoading(false);
    }
  };

  // 検索クエリが変更されたときに検索を実行
  useEffect(() => {
    // 一時的に無効化 - 予約一覧ページでは商品検索は不要
    console.log('ProductSearchResults: 商品検索を無効化しました');
    return;

    const timeoutId = setTimeout(() => {
      searchProducts(searchQuery);
    }, 500); // 500ms のデバウンス

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // 検索クエリがない場合は何も表示しない
  if (!searchQuery || searchQuery.trim() === "") {
    return null;
  }

  // ローディング中
  if (loading) {
    return (
      <Box paddingBlockEnd="400">
        <Card>
          <Box padding="400">
            <InlineStack align="center" gap="200">
              <Spinner size="small" />
              <Text as="p">商品を検索中...</Text>
            </InlineStack>
          </Box>
        </Card>
      </Box>
    );
  }

  // エラーが発生した場合
  if (error) {
    return (
      <Box paddingBlockEnd="400">
        <Banner tone="critical">
          <Text as="p">{error}</Text>
        </Banner>
      </Box>
    );
  }

  // 商品が見つからない場合
  if (products.length === 0 && message) {
    return (
      <Box paddingBlockEnd="400">
        <Banner tone="info">
          <Text as="p">{message}</Text>
        </Banner>
      </Box>
    );
  }

  // 商品が見つかった場合
  if (products.length > 0) {
    return (
      <Box paddingBlockEnd="400">
        <Card>
          <Box padding="400">
            <BlockStack gap="400">
              <Text as="h3" variant="headingMd">
                {message} - 予約状況
              </Text>
              {products.map((product) => (
                <Box key={product.id} background="bg-surface-secondary" padding="300" borderRadius="100">
                  <BlockStack gap="300">
                    <InlineStack align="space-between">
                      <BlockStack gap="100">
                        <Text variant="headingSm" as="h4">{product.title}</Text>
                        <Text as="p" tone="subdued">SKU: {formatSkuWithHyphens(product.sku)}</Text>
                      </BlockStack>
                      {getProductStatusBadge(product.status)}
                    </InlineStack>

                    <Text as="p">
                      <strong>現在の予約件数: {product.bookingCount}件</strong>
                    </Text>

                    {product.bookings.length > 0 ? (
                      <BlockStack gap="200">
                        <Text variant="headingXs" as="h5">予約一覧:</Text>
                        {product.bookings.map((booking) => (
                          <Box key={booking.id} background="bg-surface" padding="200" borderRadius="100">
                            <InlineStack align="space-between" wrap={false}>
                              <BlockStack gap="100">
                                <Text as="p">
                                  <strong>期間:</strong> {booking.startDate} ～ {booking.endDate} ({booking.rentalDays}日間)
                                </Text>
                                <Text as="p">
                                  <strong>顧客:</strong> {booking.customerName || '未設定'}
                                </Text>
                                {booking.customerEmail && (
                                  <Text as="p" tone="subdued">
                                    {booking.customerEmail}
                                  </Text>
                                )}
                                <Text as="p" tone="subdued">
                                  予約ID: {booking.bookingId}
                                </Text>
                              </BlockStack>
                              <BlockStack gap="100" align="end">
                                {getStatusBadge(booking.status)}
                                {booking.totalAmount && (
                                  <Text as="p" variant="bodySm">
                                    {booking.totalAmount}
                                  </Text>
                                )}
                              </BlockStack>
                            </InlineStack>
                          </Box>
                        ))}
                      </BlockStack>
                    ) : (
                      <Box background="bg-surface" padding="200" borderRadius="100">
                        <Text as="p" tone="subdued" alignment="center">
                          この商品には現在予約がありません
                        </Text>
                      </Box>
                    )}
                  </BlockStack>
                </Box>
              ))}
            </BlockStack>
          </Box>
        </Card>
      </Box>
    );
  }

  return null;
}
