{"name": "@shopify/dev-mcp", "version": "1.0.2", "main": "dist/index.js", "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "test": "vitest run", "test:watch": "vitest", "inspector": "npm run build && npm exec @modelcontextprotocol/inspector dist/index.js"}, "author": "", "license": "ISC", "description": "A command line tool for setting up Shopify Dev MCP server", "dependencies": {"@modelcontextprotocol/sdk": "^1.6.1", "zod": "^3.24.2"}, "devDependencies": {"@modelcontextprotocol/inspector": "^0.5.1", "@types/node": "^22.13.10", "@vitest/coverage-v8": "^3.0.9", "memfs": "^4.17.0", "typescript": "^5.8.2", "vitest": "^3.0.9"}, "type": "module", "bin": {"shopify-dev-mcp": "dist/index.js"}, "files": ["dist/**/*.js", "!dist/**/*.test.*", "data/**/*.json.gz", "LICENSE", "README.md", "package.json"], "keywords": ["mcp", "modelcontextprotocol", "shopify"]}