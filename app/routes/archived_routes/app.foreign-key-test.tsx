/**
 * 外部キー制約エラー修正テストページ
 *
 * 外部キー制約エラーの修正を確認するためのテストページです。
 * 様々な商品IDパターンでの予約作成をテストします。
 */

import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, useSubmit, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  BlockStack,
  Text,
  TextField,
  Button,
  Banner,
  InlineStack,
  Box,
  Divider,
  Frame,
  Select,
  Spinner,
  Icon,
  List,
  Link,
  Badge
} from "@shopify/polaris";
// アイコンのインポートを削除
import { useState, useCallback, useEffect, useRef } from "react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";
import { createBookingWithDraftOrder } from "../services/booking.service";
import { addToCart } from "../services/cart.service";
import { v4 as uuidv4 } from 'uuid';

/**
 * ローダー関数
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // 商品データを取得
    const products = await prisma.product.findMany({
      where: { shop },
      select: {
        id: true,
        title: true,
        sku: true,
        status: true,
        price: true,
        shopifyId: true
      },
      orderBy: { title: 'asc' },
    });

    // Shopifyから商品情報を取得
    const shopifyProductsResponse = await admin.graphql(`
      query getProducts {
        products(first: 10) {
          edges {
            node {
              id
              title
              handle
              status
              variants(first: 1) {
                edges {
                  node {
                    id
                    title
                    sku
                    price
                  }
                }
              }
            }
          }
        }
      }
    `);

    const shopifyProductsData = await shopifyProductsResponse.json();
    const shopifyProducts = shopifyProductsData.data?.products?.edges?.map(edge => ({
      id: edge.node.id,
      title: edge.node.title,
      handle: edge.node.handle,
      status: edge.node.status,
      variant: edge.node.variants.edges[0]?.node || null
    })) || [];

    return json({
      products,
      shopifyProducts,
      shop
    });
  } catch (error) {
    console.error("データ取得エラー:", error);
    return json({
      products: [],
      shopifyProducts: [],
      shop: session.shop,
      error: "データの取得中にエラーが発生しました"
    });
  }
}

/**
 * アクション関数
 */
export async function action({ request }: ActionFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    const formData = await request.formData();
    const intent = formData.get("intent") as string;

    if (intent === "test-booking-service") {
      const testCase = formData.get("testCase") as string;
      const variantId = formData.get("variantId") as string || "1";
      const startDate = formData.get("startDate") as string;
      const endDate = formData.get("endDate") as string;

      let productId = "";

      // テストケースに応じて商品IDを設定
      switch (testCase) {
        case "valid-product-id":
          // 有効な商品ID（フォームから取得）
          productId = formData.get("productId") as string;
          break;
        case "invalid-product-id":
          // 無効な商品ID（存在しないID）
          productId = uuidv4();
          break;
        case "empty-product-id":
          // 空の商品ID
          productId = "";
          break;
        default:
          return json({
            success: false,
            error: "無効なテストケースです"
          });
      }

      // 予約サービスを使用して予約を作成
      const result = await createBookingWithDraftOrder({
        shop,
        productId,
        variantId,
        startDate,
        endDate,
        customerEmail: "<EMAIL>",
        customerName: "テストユーザー",
        notes: `テストケース: ${testCase}`,
        bookingType: "PROVISIONAL",
        admin
      });

      return json({
        success: result.success,
        testCase,
        productId,
        result
      });
    }

    if (intent === "test-cart-service") {
      const testCase = formData.get("testCase") as string;
      const variantId = formData.get("variantId") as string || "1";
      const startDate = formData.get("startDate") as string;
      const endDate = formData.get("endDate") as string;

      let productId = "";

      // テストケースに応じて商品IDを設定
      switch (testCase) {
        case "valid-product-id":
          // 有効な商品ID（フォームから取得）
          productId = formData.get("productId") as string;
          break;
        case "invalid-product-id":
          // 無効な商品ID（存在しないID）
          productId = uuidv4();
          break;
        case "empty-product-id":
          // 空の商品ID
          productId = "";
          break;
        default:
          return json({
            success: false,
            error: "無効なテストケースです"
          });
      }

      // カートサービスを使用してカートに追加
      const result = await addToCart({
        shop,
        productId,
        variantId,
        startDate,
        endDate,
        customerEmail: "<EMAIL>",
        customerName: "テストユーザー",
        notes: `テストケース: ${testCase}`,
        admin
      });

      return json({
        success: result.success,
        testCase,
        productId,
        result
      });
    }

    return json({
      success: false,
      error: "不明な操作が指定されました"
    });
  } catch (error) {
    console.error("アクションエラー:", error);
    return json({
      success: false,
      error: "処理中にエラーが発生しました: " + (error instanceof Error ? error.message : String(error))
    });
  }
}

export default function ForeignKeyTest() {
  const { products, shopifyProducts, shop, error } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();

  const [isRunning, setIsRunning] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [errorMessage, setErrorMessage] = useState(error || "");
  const [successMessage, setSuccessMessage] = useState("");
  const [testResults, setTestResults] = useState<{
    bookingService: {
      validProductId: { success: boolean; message: string; data?: any };
      invalidProductId: { success: boolean; message: string; data?: any };
      emptyProductId: { success: boolean; message: string; data?: any };
    };
    cartService: {
      validProductId: { success: boolean; message: string; data?: any };
      invalidProductId: { success: boolean; message: string; data?: any };
      emptyProductId: { success: boolean; message: string; data?: any };
    };
  }>({
    bookingService: {
      validProductId: { success: false, message: "未実行" },
      invalidProductId: { success: false, message: "未実行" },
      emptyProductId: { success: false, message: "未実行" }
    },
    cartService: {
      validProductId: { success: false, message: "未実行" },
      invalidProductId: { success: false, message: "未実行" },
      emptyProductId: { success: false, message: "未実行" }
    }
  });

  const [testConfig, setTestConfig] = useState({
    productId: products[0]?.id || "",
    variantId: "",
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  });

  const logsEndRef = useRef<HTMLDivElement>(null);

  // ログをスクロールして最新の内容を表示
  useEffect(() => {
    if (logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [logs]);

  // アクションデータの処理
  useEffect(() => {
    if (actionData) {
      setIsRunning(false);

      if (actionData.success !== undefined) {
        const { testCase, productId, result } = actionData;

        // テスト結果を更新
        if (actionData.intent === "test-booking-service") {
          setTestResults(prev => ({
            ...prev,
            bookingService: {
              ...prev.bookingService,
              [testCase]: {
                success: actionData.success,
                message: actionData.success ? "成功" : actionData.error || "失敗",
                data: result
              }
            }
          }));
        } else if (actionData.intent === "test-cart-service") {
          setTestResults(prev => ({
            ...prev,
            cartService: {
              ...prev.cartService,
              [testCase]: {
                success: actionData.success,
                message: actionData.success ? "成功" : actionData.error || "失敗",
                data: result
              }
            }
          }));
        }

        // ログに追加
        addLog(`テスト結果 (${testCase}): ${actionData.success ? "成功" : "失敗"}`);
        addLog(`商品ID: ${productId}`);
        addLog(`詳細: ${JSON.stringify(result, null, 2)}`);
      }

      if (actionData.error) {
        setErrorMessage(actionData.error);
        addLog(`エラー: ${actionData.error}`);
      }
    }
  }, [actionData]);

  // 商品選択時の処理
  const handleProductChange = useCallback((value: string) => {
    setTestConfig(prev => ({
      ...prev,
      productId: value
    }));

    // 選択された商品のShopify IDを取得
    const selectedProduct = products.find(p => p.id === value);
    if (selectedProduct && selectedProduct.shopifyId) {
      // 対応するShopify商品を検索
      const shopifyProduct = shopifyProducts.find(p =>
        p.id === `gid://shopify/Product/${selectedProduct.shopifyId}`
      );

      if (shopifyProduct && shopifyProduct.variant) {
        setTestConfig(prev => ({
          ...prev,
          variantId: shopifyProduct.variant.id.replace("gid://shopify/ProductVariant/", "")
        }));
      }
    }
  }, [products, shopifyProducts]);

  // ログの追加
  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  }, []);

  // テストの実行
  const runTests = useCallback(async () => {
    setIsRunning(true);
    setLogs([]);
    setErrorMessage("");
    setSuccessMessage("");
    setTestResults({
      bookingService: {
        validProductId: { success: false, message: "実行中..." },
        invalidProductId: { success: false, message: "実行中..." },
        emptyProductId: { success: false, message: "実行中..." }
      },
      cartService: {
        validProductId: { success: false, message: "実行中..." },
        invalidProductId: { success: false, message: "実行中..." },
        emptyProductId: { success: false, message: "実行中..." }
      }
    });

    addLog("テストを開始します...");

    // 予約サービスのテスト
    addLog("予約サービスのテスト開始");

    // テストケース1: 有効な商品ID
    addLog("テストケース1: 有効な商品ID");
    const formData1 = new FormData();
    formData1.append("intent", "test-booking-service");
    formData1.append("testCase", "valid-product-id");
    formData1.append("productId", testConfig.productId);
    formData1.append("variantId", testConfig.variantId);
    formData1.append("startDate", testConfig.startDate);
    formData1.append("endDate", testConfig.endDate);

    submit(formData1, { method: "post" });

    // 少し待機
    await new Promise(resolve => setTimeout(resolve, 2000));

    // テストケース2: 無効な商品ID
    addLog("テストケース2: 無効な商品ID");
    const formData2 = new FormData();
    formData2.append("intent", "test-booking-service");
    formData2.append("testCase", "invalid-product-id");
    formData2.append("variantId", testConfig.variantId);
    formData2.append("startDate", testConfig.startDate);
    formData2.append("endDate", testConfig.endDate);

    submit(formData2, { method: "post" });

    // 少し待機
    await new Promise(resolve => setTimeout(resolve, 2000));

    // テストケース3: 空の商品ID
    addLog("テストケース3: 空の商品ID");
    const formData3 = new FormData();
    formData3.append("intent", "test-booking-service");
    formData3.append("testCase", "empty-product-id");
    formData3.append("variantId", testConfig.variantId);
    formData3.append("startDate", testConfig.startDate);
    formData3.append("endDate", testConfig.endDate);

    submit(formData3, { method: "post" });

    // 少し待機
    await new Promise(resolve => setTimeout(resolve, 2000));

    // カートサービスのテスト
    addLog("カートサービスのテスト開始");

    // テストケース4: 有効な商品ID
    addLog("テストケース4: 有効な商品ID");
    const formData4 = new FormData();
    formData4.append("intent", "test-cart-service");
    formData4.append("testCase", "valid-product-id");
    formData4.append("productId", testConfig.productId);
    formData4.append("variantId", testConfig.variantId);
    formData4.append("startDate", testConfig.startDate);
    formData4.append("endDate", testConfig.endDate);

    submit(formData4, { method: "post" });

    // 少し待機
    await new Promise(resolve => setTimeout(resolve, 2000));

    // テストケース5: 無効な商品ID
    addLog("テストケース5: 無効な商品ID");
    const formData5 = new FormData();
    formData5.append("intent", "test-cart-service");
    formData5.append("testCase", "invalid-product-id");
    formData5.append("variantId", testConfig.variantId);
    formData5.append("startDate", testConfig.startDate);
    formData5.append("endDate", testConfig.endDate);

    submit(formData5, { method: "post" });

    // 少し待機
    await new Promise(resolve => setTimeout(resolve, 2000));

    // テストケース6: 空の商品ID
    addLog("テストケース6: 空の商品ID");
    const formData6 = new FormData();
    formData6.append("intent", "test-cart-service");
    formData6.append("testCase", "empty-product-id");
    formData6.append("variantId", testConfig.variantId);
    formData6.append("startDate", testConfig.startDate);
    formData6.append("endDate", testConfig.endDate);

    submit(formData6, { method: "post" });

    // テスト完了
    addLog("すべてのテストが完了しました");
    setSuccessMessage("テストが完了しました");
  }, [testConfig, submit, addLog]);

  // テストのリセット
  const resetTests = useCallback(() => {
    setIsRunning(false);
    setLogs([]);
    setErrorMessage("");
    setSuccessMessage("");
    setTestResults({
      bookingService: {
        validProductId: { success: false, message: "未実行" },
        invalidProductId: { success: false, message: "未実行" },
        emptyProductId: { success: false, message: "未実行" }
      },
      cartService: {
        validProductId: { success: false, message: "未実行" },
        invalidProductId: { success: false, message: "未実行" },
        emptyProductId: { success: false, message: "未実行" }
      }
    });
  }, []);

  return (
    <Frame>
      <Page
        title="外部キー制約エラー修正テスト"
        backAction={{ content: "戻る", url: "/app" }}
        primaryAction={{
          content: isRunning ? "テスト実行中..." : "テスト実行",
          // アイコンを削除
          onAction: runTests,
          disabled: isRunning
        }}
        secondaryActions={[
          {
            content: "リセット",
            // アイコンを削除
            onAction: resetTests,
            disabled: isRunning
          }
        ]}
      >
        {errorMessage && (
          <Banner status="critical" onDismiss={() => setErrorMessage("")}>
            <p>{errorMessage}</p>
          </Banner>
        )}

        {successMessage && (
          <Banner status="success" onDismiss={() => setSuccessMessage("")}>
            <p>{successMessage}</p>
          </Banner>
        )}

        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">テスト設定</Text>

                <Select
                  label="商品"
                  options={products.map(p => ({ label: p.title || p.id, value: p.id }))}
                  value={testConfig.productId}
                  onChange={handleProductChange}
                  disabled={isRunning}
                />

                <TextField
                  label="バリアントID"
                  value={testConfig.variantId}
                  onChange={(value) => setTestConfig({...testConfig, variantId: value})}
                  disabled={isRunning}
                />

                <InlineStack gap="200">
                  <TextField
                    label="開始日"
                    type="date"
                    value={testConfig.startDate}
                    onChange={(value) => setTestConfig({...testConfig, startDate: value})}
                    disabled={isRunning}
                  />

                  <TextField
                    label="終了日"
                    type="date"
                    value={testConfig.endDate}
                    onChange={(value) => setTestConfig({...testConfig, endDate: value})}
                    disabled={isRunning}
                  />
                </InlineStack>

                <InlineStack gap="200">
                  <Button
                    primary
                    onClick={runTests}
                    disabled={isRunning}
                  >
                    テスト実行
                  </Button>

                  <Button
                    onClick={resetTests}
                    disabled={isRunning}
                  >
                    リセット
                  </Button>
                </InlineStack>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">テスト結果</Text>

                <BlockStack gap="400">
                  <Text as="h3" variant="headingSm">予約サービス (createBookingWithDraftOrder)</Text>

                  <BlockStack gap="200">
                    <InlineStack gap="200" align="center">
                      <Badge status={testResults.bookingService.validProductId.success ? "success" : testResults.bookingService.validProductId.message === "未実行" ? "new" : "critical"}>
                        {testResults.bookingService.validProductId.success ? "成功" : testResults.bookingService.validProductId.message === "未実行" ? "未実行" : "失敗"}
                      </Badge>
                      <Text variant="bodyMd">テストケース1: 有効な商品ID</Text>
                      {testResults.bookingService.validProductId.message === "実行中..." && <Spinner size="small" />}
                    </InlineStack>

                    <InlineStack gap="200" align="center">
                      <Badge status={testResults.bookingService.invalidProductId.success ? "success" : testResults.bookingService.invalidProductId.message === "未実行" ? "new" : "critical"}>
                        {testResults.bookingService.invalidProductId.success ? "成功" : testResults.bookingService.invalidProductId.message === "未実行" ? "未実行" : "失敗"}
                      </Badge>
                      <Text variant="bodyMd">テストケース2: 無効な商品ID</Text>
                      {testResults.bookingService.invalidProductId.message === "実行中..." && <Spinner size="small" />}
                    </InlineStack>

                    <InlineStack gap="200" align="center">
                      <Badge status={testResults.bookingService.emptyProductId.success ? "success" : testResults.bookingService.emptyProductId.message === "未実行" ? "new" : "critical"}>
                        {testResults.bookingService.emptyProductId.success ? "成功" : testResults.bookingService.emptyProductId.message === "未実行" ? "未実行" : "失敗"}
                      </Badge>
                      <Text variant="bodyMd">テストケース3: 空の商品ID</Text>
                      {testResults.bookingService.emptyProductId.message === "実行中..." && <Spinner size="small" />}
                    </InlineStack>
                  </BlockStack>

                  <Text as="h3" variant="headingSm">カートサービス (addToCart)</Text>

                  <BlockStack gap="200">
                    <InlineStack gap="200" align="center">
                      <Badge status={testResults.cartService.validProductId.success ? "success" : testResults.cartService.validProductId.message === "未実行" ? "new" : "critical"}>
                        {testResults.cartService.validProductId.success ? "成功" : testResults.cartService.validProductId.message === "未実行" ? "未実行" : "失敗"}
                      </Badge>
                      <Text variant="bodyMd">テストケース4: 有効な商品ID</Text>
                      {testResults.cartService.validProductId.message === "実行中..." && <Spinner size="small" />}
                    </InlineStack>

                    <InlineStack gap="200" align="center">
                      <Badge status={testResults.cartService.invalidProductId.success ? "success" : testResults.cartService.invalidProductId.message === "未実行" ? "new" : "critical"}>
                        {testResults.cartService.invalidProductId.success ? "成功" : testResults.cartService.invalidProductId.message === "未実行" ? "未実行" : "失敗"}
                      </Badge>
                      <Text variant="bodyMd">テストケース5: 無効な商品ID</Text>
                      {testResults.cartService.invalidProductId.message === "実行中..." && <Spinner size="small" />}
                    </InlineStack>

                    <InlineStack gap="200" align="center">
                      <Badge status={testResults.cartService.emptyProductId.success ? "success" : testResults.cartService.emptyProductId.message === "未実行" ? "new" : "critical"}>
                        {testResults.cartService.emptyProductId.success ? "成功" : testResults.cartService.emptyProductId.message === "未実行" ? "未実行" : "失敗"}
                      </Badge>
                      <Text variant="bodyMd">テストケース6: 空の商品ID</Text>
                      {testResults.cartService.emptyProductId.message === "実行中..." && <Spinner size="small" />}
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">ログ</Text>

                <Box
                  padding="400"
                  background="bg-surface-secondary"
                  borderRadius="200"
                  overflowY="scroll"
                  maxHeight="400px"
                >
                  {logs.length > 0 ? (
                    <BlockStack gap="200">
                      {logs.map((log, index) => (
                        <Text key={index} variant="bodyMd" as="pre" fontFamily="monospace">
                          {log}
                        </Text>
                      ))}
                      <div ref={logsEndRef} />
                    </BlockStack>
                  ) : (
                    <Text variant="bodyMd" color="text-subdued">テストを実行するとログがここに表示されます</Text>
                  )}
                </Box>
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section secondary>
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">テスト内容</Text>

                <Text variant="bodyMd">このテストでは、外部キー制約エラーの修正を確認するために、以下のテストケースを実行します：</Text>

                <List type="bullet">
                  <List.Item>有効な商品ID（データベースに存在するID）</List.Item>
                  <List.Item>無効な商品ID（データベースに存在しないID）</List.Item>
                  <List.Item>空の商品ID（nullまたは空文字列）</List.Item>
                </List>

                <Text variant="bodyMd">各テストケースで以下のサービスをテストします：</Text>

                <List type="bullet">
                  <List.Item>予約サービス（createBookingWithDraftOrder）</List.Item>
                  <List.Item>カートサービス（addToCart）</List.Item>
                </List>

                <Text variant="bodyMd">修正が正しく行われていれば、すべてのテストケースが成功するはずです。</Text>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>
      </Page>
    </Frame>
  );
}
