import { PrismaClient } from '@prisma/client';
import { GraphQLClient, gql } from 'graphql-request';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();

// Shopify API設定
const config = {
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block',
  apiSecretKey: process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
};

// 設定の検証
if (!config.apiSecretKey) {
  console.error('エラー: SHOPIFY_ADMIN_API_ACCESS_TOKENが設定されていません。');
  process.exit(1);
}

// ショップ名を正規化
const shopName = config.shop.replace('.myshopify.com', '');

// Shopify GraphQL APIクライアントの設定
const client = new GraphQLClient(
  `https://${shopName}.myshopify.com/admin/api/2025-01/graphql.json`,
  {
    headers: {
      'X-Shopify-Access-Token': config.apiSecretKey,
      'Content-Type': 'application/json',
    },
  }
);

interface ProductData {
  productCode: string;
  detailCode: string;
  categoryId: number;
  categoryName: string;
  sku: string;
  itemNumber: number;
  title: string;
  kana: string;
  description: string;
  remarks: string;
  price: number;
  quantity: number;
  location: string;
  width: number;
  depth: number;
  height: number;
  weight: number;
  purchaseDate: string;
  purchasePrice: number;
  status: number;
  maintenanceNotes: string;
  // グループ管理用の追加フィールド
  groupCode?: string; // 同じ形の商品をグループ化するコード
  color?: string; // カラー情報
  material?: string; // 素材情報
}

// 商品作成のMutation（バリアントなし）
const CREATE_PRODUCT_MUTATION = gql`
  mutation CreateProduct($input: ProductInput!) {
    productCreate(input: $input) {
      product {
        id
        title
        handle
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// バリアント作成のMutation
const CREATE_VARIANT_MUTATION = gql`
  mutation CreateProductVariant($productId: ID!, $input: ProductVariantInput!) {
    productVariantCreate(productId: $productId, input: $input) {
      productVariant {
        id
        title
        sku
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// メタフィールド設定のMutation
const SET_METAFIELDS_MUTATION = gql`
  mutation SetMetafields($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// バリアント情報を取得するQuery
const GET_PRODUCT_VARIANTS = gql`
  query GetProductVariants($id: ID!) {
    product(id: $id) {
      variants(first: 20) {
        nodes {
          id
          title
          sku
        }
      }
    }
  }
`;

async function createCompleteProduct(data: ProductData) {
  console.log(`🛍️ 商品作成開始: ${data.title} (${data.detailCode})`);

  try {
    // 1. 商品を作成（バリアントなし）
    const productInput = {
      title: `${data.title} [${data.detailCode}]`,
      handle: `${data.productCode}-${data.detailCode}`,
      descriptionHtml: `
        <h3>${data.title}</h3>
        <p><strong>商品番号:</strong> ${data.productCode}-${data.detailCode}</p>
        <p><strong>状態:</strong> ${data.description}</p>
        <p><strong>サイズ:</strong> 幅${data.width}cm × 奥行${data.depth}cm × 高さ${data.height}cm</p>
        <p><strong>カテゴリ:</strong> ${data.categoryName}</p>
        <p><strong>保管場所:</strong> ${data.location}</p>
        ${data.color ? `<p><strong>カラー:</strong> ${data.color}</p>` : ''}
        ${data.material ? `<p><strong>素材:</strong> ${data.material}</p>` : ''}
        ${data.maintenanceNotes ? `<p><strong>メンテナンス備考:</strong> ${data.maintenanceNotes}</p>` : ''}
      `,
      vendor: 'IZIZ RENTAL',
      productType: data.categoryName,
      tags: [
        `${data.categoryName}`,
        `商品番号:${data.productCode}`,
        `詳細:${data.detailCode}`,
        `場所:${data.location}`,
        data.groupCode ? `グループ:${data.groupCode}` : '',
        data.color ? `カラー:${data.color}` : '',
      ].filter(Boolean),
      status: 'ACTIVE'
    };

    const productResult = await client.request(CREATE_PRODUCT_MUTATION, { input: productInput });
    
    if (productResult.productCreate.userErrors.length > 0) {
      console.error('❌ 商品作成エラー:', productResult.productCreate.userErrors);
      return null;
    }

    const shopifyProduct = productResult.productCreate.product;
    console.log(`✅ Shopify商品作成完了: ${shopifyProduct.id}`);

    // 2. バリアントを個別に作成
    const variantConfigs = [
      { days: 1, multiplier: 1.0, title: '1日レンタル', suffix: '1D' },
      { days: 2, multiplier: 1.2, title: '2日レンタル', suffix: '2D' },
      { days: 3, multiplier: 1.4, title: '3日レンタル', suffix: '3D' },
      { days: 4, multiplier: 1.6, title: '4日レンタル', suffix: '4D' },
      { days: 5, multiplier: 1.8, title: '5日レンタル', suffix: '5D' },
      { days: 6, multiplier: 2.0, title: '6日レンタル', suffix: '6D' },
      { days: 7, multiplier: 2.1, title: '7日レンタル', suffix: '7D' },
      { days: 8, multiplier: 0.1, title: '8日以上レンタル', suffix: '8D+' },
      { days: 0, multiplier: 0.1, title: '仮予約', suffix: 'PROV' }
    ];

    const createdVariants = [];
    
    for (const config of variantConfigs) {
      const variantInput = {
        options: [config.title],
        price: (data.price * config.multiplier).toString(),
        sku: `${data.productCode}-${data.detailCode}-${config.suffix}`,
        inventoryManagement: 'SHOPIFY',
        inventoryPolicy: 'DENY',
        weight: data.weight || 0,
        weightUnit: 'KILOGRAMS'
      };

      try {
        const variantResult = await client.request(CREATE_VARIANT_MUTATION, {
          productId: shopifyProduct.id,
          input: variantInput
        });

        if (variantResult.productVariantCreate.userErrors.length > 0) {
          console.error(`⚠️ バリアント作成エラー (${config.title}):`, variantResult.productVariantCreate.userErrors);
        } else {
          createdVariants.push(variantResult.productVariantCreate.productVariant);
          console.log(`✅ バリアント作成完了: ${config.title}`);
        }
      } catch (error) {
        console.error(`❌ バリアント作成失敗 (${config.title}):`, error);
      }
    }

    // 3. メタフィールドを設定（グループ管理情報を含む）
    const metafields = [
      {
        ownerId: shopifyProduct.id,
        namespace: 'product',
        key: 'depth',
        value: data.depth.toString(),
        type: 'number_integer'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'product',
        key: 'height', 
        value: data.height.toString(),
        type: 'number_integer'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'product',
        key: 'width',
        value: data.width.toString(),
        type: 'number_integer'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'basic_info',
        value: JSON.stringify({
          productCode: data.productCode,
          detailCode: data.detailCode,
          kana: data.kana,
          location: data.location,
          status: 'available',
          groupCode: data.groupCode || data.productCode, // グループコード
          color: data.color || '',
          material: data.material || ''
        }),
        type: 'json'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'pricing',
        value: JSON.stringify({
          basePrice: data.price,
          depositRate: 0.1,
          discountRules: {
            day2_6_rate: 0.2,
            day7_plus_rate: 0.1
          },
          minimumDays: 1,
          maximumDays: 30,
          variantPrices: {
            "1day": data.price,
            "2day": Math.round(data.price * 1.2),
            "3day": Math.round(data.price * 1.4),
            "4day": Math.round(data.price * 1.6),
            "5day": Math.round(data.price * 1.8),
            "6day": Math.round(data.price * 2.0),
            "7day": Math.round(data.price * 2.1),
            "8plus": Math.round(data.price * 0.1)
          }
        }),
        type: 'json'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'group_info',
        value: JSON.stringify({
          groupCode: data.groupCode || data.productCode,
          groupName: data.title.replace(/\[.*\]/, '').trim(),
          variations: [
            {
              detailCode: data.detailCode,
              color: data.color || '',
              material: data.material || '',
              description: data.description
            }
          ]
        }),
        type: 'json'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'inventory_items',
        value: JSON.stringify([
          {
            itemId: `${data.productCode}-${data.detailCode}`,
            condition: data.description,
            lastInspectionDate: new Date().toISOString().split('T')[0],
            maintenanceNotes: data.maintenanceNotes,
            status: 'available'
          }
        ]),
        type: 'json'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'reservation_info',
        value: JSON.stringify({
          reservations: [],
          maintenanceSchedule: [],
          availabilityRules: {
            minimumNotice: 1,
            maximumAdvance: 180,
            blackoutDates: []
          }
        }),
        type: 'json'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'booking_notes',
        value: data.description ? `特記事項: ${data.description}` : '特別な指示はありません。通常通りの予約が可能です。',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'booking_type',
        value: 'confirmed',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'color',
        value: data.color || '',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'location',
        value: data.location,
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'maintenance_notes',
        value: data.maintenanceNotes,
        type: 'multi_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'manufacturer',
        value: '',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'material',
        value: data.material || '',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'purchase_date',
        value: data.purchaseDate !== '19700101' ? data.purchaseDate : '',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'purchase_place',
        value: '',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'purchase_price',
        value: data.purchasePrice.toString(),
        type: 'number_decimal'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'status',
        value: 'available',
        type: 'single_line_text_field'
      },
      {
        ownerId: shopifyProduct.id,
        namespace: 'rental',
        key: 'variation_type',
        value: 'rental_period', // 正しい値に修正
        type: 'single_line_text_field'
      }
    ];

    // メタフィールドを一括設定
    const metafieldResult = await client.request(SET_METAFIELDS_MUTATION, { metafields });
    
    if (metafieldResult.metafieldsSet.userErrors.length > 0) {
      console.error('⚠️ メタフィールド設定エラー:', metafieldResult.metafieldsSet.userErrors);
    } else {
      console.log(`✅ メタフィールド設定完了: ${metafieldResult.metafieldsSet.metafields.length}件`);
    }

    // 4. バリアント情報を再取得
    const variantsResult = await client.request(GET_PRODUCT_VARIANTS, { id: shopifyProduct.id });
    const finalVariants = variantsResult.product.variants.nodes;

    // 5. Prismaに商品データを登録
    try {
      const productId = shopifyProduct.id.replace('gid://shopify/Product/', '');
      
      const prismaProduct = await prisma.product.create({
        data: {
          shopifyProductId: productId,
          title: shopifyProduct.title,
          handle: shopifyProduct.handle,
          sku: `${data.productCode}-${data.detailCode}`,
          shop: `${shopName}.myshopify.com`,
          basicInfo: {
            productCode: data.productCode,
            detailCode: data.detailCode,
            kana: data.kana,
            location: data.location,
            status: 'available',
            groupCode: data.groupCode || data.productCode,
            color: data.color || '',
            material: data.material || ''
          },
          pricing: {
            basePrice: data.price,
            depositRate: 0.1,
            discountRules: {
              day2_6_rate: 0.2,
              day7_plus_rate: 0.1
            },
            minimumDays: 1,
            maximumDays: 30
          },
          dimensions: {
            width: data.width,
            depth: data.depth,  
            height: data.height,
            weight: data.weight || 0
          },
          metadata: {
            category: data.categoryName,
            description: data.description,
            maintenanceNotes: data.maintenanceNotes,
            purchaseDate: data.purchaseDate,
            purchasePrice: data.purchasePrice,
            groupCode: data.groupCode || data.productCode
          },
          syncStatus: 'synced',
          lastSyncedAt: new Date()
        }
      });

      console.log(`✅ Prisma商品登録完了: ${prismaProduct.id}`);

      // 6. バリアント情報をPrismaに登録
      for (const variant of finalVariants) {
        const variantConfig = variantConfigs.find(c => variant.title === c.title);
        if (variantConfig) {
          await prisma.productVariant.create({
            data: {
              shopifyVariantId: variant.id.replace('gid://shopify/ProductVariant/', ''),
              productId: prismaProduct.id,
              title: variant.title,
              sku: variant.sku,
              price: data.price * variantConfig.multiplier,
              inventoryManagement: 'shopify',
              inventoryPolicy: 'deny',
              position: finalVariants.indexOf(variant) + 1
            }
          });
        }
      }

      console.log(`✅ バリアント登録完了: ${finalVariants.length}件`);

      return {
        shopifyProduct,
        prismaProduct,
        metafieldsCount: metafields.length,
        variantsCount: finalVariants.length
      };

    } catch (error) {
      console.error('❌ Prisma登録エラー:', error);
      return {
        shopifyProduct,
        prismaProduct: null,
        metafieldsCount: metafields.length,
        variantsCount: createdVariants.length
      };
    }

  } catch (error) {
    console.error('❌ 商品作成エラー:', error);
    return null;
  }
}

// テストデータ（グループ管理対応）
const testData: ProductData[] = [
  {
    productCode: "10101007",
    detailCode: "1",
    categoryId: 3,
    categoryName: "ソファ",
    sku: "10101007",
    itemNumber: 1,
    title: "85_●ﾍﾞｰｼｯｸｿﾌｧ ｵﾌﾎﾜｲﾄ 1ｼｰﾀｰ",
    kana: "ﾍﾞｰｼｯｸｿﾌｧｵﾌﾎﾜｲﾄ1ｼｰﾀｰﾔﾏﾅﾘ",
    description: "１　背面向かって右側うっすら黒いしみ",
    remarks: "",
    price: 8000,
    quantity: 1,
    location: "NY",
    width: 87,
    depth: 74,
    height: 76,
    weight: 0,
    purchaseDate: "19700101",
    purchasePrice: 0,
    status: 1,
    maintenanceNotes: "●2台ともクリーニング済みですが全体的に黄ばみ発生（H30.12.15)／[002]向かって左アーム手前と正面左側に黄色い輪染み有／[001]背面向かって右側うっすら黒いしみあり(写真には写りません)　SW52/SD54/SH40",
    groupCode: "BASIC-SOFA-1S", // グループコード
    color: "オフホワイト"
  },
  {
    productCode: "10101007",
    detailCode: "2", 
    categoryId: 3,
    categoryName: "ソファ",
    sku: "10101007",
    itemNumber: 2,
    title: "85_●ﾍﾞｰｼｯｸｿﾌｧ ｵﾌﾎﾜｲﾄ 1ｼｰﾀｰ",
    kana: "ﾍﾞｰｼｯｸｿﾌｧｵﾌﾎﾜｲﾄ1ｼｰﾀｰﾔﾏﾅﾘ",
    description: "２　向かって左アーム手前と正面左側に黄色い輪染み有",
    remarks: "",
    price: 8000,
    quantity: 1,
    location: "NY",
    width: 87,
    depth: 74,
    height: 76,
    weight: 0,
    purchaseDate: "19700101",
    purchasePrice: 0,
    status: 1,
    maintenanceNotes: "●2台ともクリーニング済みですが全体的に黄ばみ発生（H30.12.15)／[002]向かって左アーム手前と正面左側に黄色い輪染み有／[001]背面向かって右側うっすら黒いしみあり(写真には写りません)　SW52/SD54/SH40",
    groupCode: "BASIC-SOFA-1S", // 同じグループコード
    color: "オフホワイト"
  }
];

async function main() {
  try {
    console.log('🚀 グループ管理対応商品登録を開始...');
    console.log(`📦 登録対象: ${testData.length}件の商品`);

    for (const data of testData) {
      const result = await createCompleteProduct(data);
      
      if (result) {
        console.log(`✅ 商品登録完了: ${data.title} [${data.detailCode}]`);
        console.log(`   - Shopify ID: ${result.shopifyProduct.id}`);
        console.log(`   - メタフィールド: ${result.metafieldsCount}件`);
        console.log(`   - バリアント: ${result.variantsCount}件`);
        if (result.prismaProduct) {
          console.log(`   - Prisma ID: ${result.prismaProduct.id}`);
        }
      } else {
        console.error(`❌ 商品登録失敗: ${data.title} [${data.detailCode}]`);
      }

      // API制限回避のため少し待機
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    console.log('🎉 全ての商品登録が完了しました！');

  } catch (error) {
    console.error('❌ エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// メイン実行
if (require.main === module) {
  main();
}

export { createCompleteProduct };