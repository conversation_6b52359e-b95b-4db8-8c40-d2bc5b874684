/**
 * 祝日判定ロジックテストスクリプト
 *
 * このスクリプトは以下の機能をテストします：
 * 1. 日曜日の判定
 * 2. 祝日の判定
 * 3. 年末年始の判定
 * 4. 休業日（日曜日・祝日・年末年始）の判定
 */

// ESモジュール形式で実行

// 日本の祝日（2025年）
const JAPANESE_HOLIDAYS_2025 = [
  '2025-01-01', // 元日
  '2025-01-13', // 成人の日
  '2025-02-11', // 建国記念の日
  '2025-02-23', // 天皇誕生日
  '2025-02-24', // 振替休日
  '2025-03-20', // 春分の日
  '2025-04-29', // 昭和の日
  '2025-05-03', // 憲法記念日
  '2025-05-04', // みどりの日
  '2025-05-05', // こどもの日
  '2025-05-06', // 振替休日
  '2025-07-21', // 海の日
  '2025-08-11', // 山の日
  '2025-09-15', // 敬老の日
  '2025-09-23', // 秋分の日
  '2025-10-13', // スポーツの日
  '2025-11-03', // 文化の日
  '2025-11-23', // 勤労感謝の日
  '2025-11-24', // 振替休日
  '2025-12-23', // 天皇誕生日
];

// テストケース
const testCases = [
  // 1. 日曜日判定テスト
  {
    name: '日曜日判定テスト',
    dates: [
      { date: '2025-01-05', expected: true, description: '2025-01-05は日曜日' },
      { date: '2025-01-12', expected: true, description: '2025-01-12は日曜日' },
      { date: '2025-01-19', expected: true, description: '2025-01-19は日曜日' },
      { date: '2025-01-26', expected: true, description: '2025-01-26は日曜日' },
      { date: '2025-01-06', expected: false, description: '2025-01-06は月曜日' },
      { date: '2025-01-07', expected: false, description: '2025-01-07は火曜日' },
      { date: '2025-01-08', expected: false, description: '2025-01-08は水曜日' },
      { date: '2025-01-09', expected: false, description: '2025-01-09は木曜日' },
      { date: '2025-01-10', expected: false, description: '2025-01-10は金曜日' },
      { date: '2025-01-11', expected: false, description: '2025-01-11は土曜日' },
    ],
    testFunction: isSunday
  },

  // 2. 祝日判定テスト
  {
    name: '祝日判定テスト',
    dates: [
      { date: '2025-01-01', expected: true, description: '2025-01-01は元日' },
      { date: '2025-05-05', expected: true, description: '2025-05-05はこどもの日' },
      { date: '2025-11-03', expected: true, description: '2025-11-03は文化の日' },
      { date: '2025-01-02', expected: false, description: '2025-01-02は祝日ではない' },
      { date: '2025-05-01', expected: false, description: '2025-05-01は祝日ではない' },
      { date: '2025-11-04', expected: false, description: '2025-11-04は祝日ではない' },
    ],
    testFunction: isJapaneseHoliday
  },

  // 3. 年末年始判定テスト
  {
    name: '年末年始判定テスト',
    dates: [
      { date: '2024-12-29', expected: true, description: '2024-12-29は年末年始' },
      { date: '2024-12-30', expected: true, description: '2024-12-30は年末年始' },
      { date: '2024-12-31', expected: true, description: '2024-12-31は年末年始' },
      { date: '2025-01-01', expected: true, description: '2025-01-01は年末年始' },
      { date: '2025-01-02', expected: true, description: '2025-01-02は年末年始' },
      { date: '2025-01-03', expected: true, description: '2025-01-03は年末年始' },
      { date: '2024-12-28', expected: false, description: '2024-12-28は年末年始ではない' },
      { date: '2025-01-04', expected: false, description: '2025-01-04は年末年始ではない' },
    ],
    testFunction: isNewYearHoliday
  },

  // 4. 休業日判定テスト
  {
    name: '休業日判定テスト',
    dates: [
      { date: '2025-01-01', expected: true, description: '2025-01-01は元日（祝日かつ年末年始）' },
      { date: '2025-01-05', expected: true, description: '2025-01-05は日曜日' },
      { date: '2025-05-05', expected: true, description: '2025-05-05はこどもの日（祝日）' },
      { date: '2025-12-29', expected: true, description: '2025-12-29は年末年始' },
      { date: '2025-01-07', expected: false, description: '2025-01-07は休業日ではない' },
      { date: '2025-05-02', expected: false, description: '2025-05-02は休業日ではない' },
    ],
    testFunction: isClosedDay
  }
];

// 日付が日曜日かどうかを判定
function isSunday(date) {
  return new Date(date).getDay() === 0;
}

// 日付が祝日かどうかを判定
function isJapaneseHoliday(date) {
  const dateStr = new Date(date).toISOString().split('T')[0];
  return JAPANESE_HOLIDAYS_2025.includes(dateStr);
}

// 日付が年末年始かどうかを判定
function isNewYearHoliday(date) {
  const d = new Date(date);
  const month = d.getMonth() + 1; // 0-indexed to 1-indexed
  const day = d.getDate();

  // 12月29日〜12月31日
  if (month === 12 && day >= 29 && day <= 31) {
    return true;
  }

  // 1月1日〜1月3日
  if (month === 1 && day >= 1 && day <= 3) {
    return true;
  }

  return false;
}

// 休業日かどうかを判定（日曜日・祝日・年末年始）
function isClosedDay(date) {
  return isSunday(date) || isJapaneseHoliday(date) || isNewYearHoliday(date);
}

// 営業日数を計算
function calculateBusinessDays(startDate, endDate) {
  let businessDays = 0;
  const start = new Date(startDate);
  const end = new Date(endDate);
  const current = new Date(start);

  while (current <= end) {
    // 日曜日・祝日・年末年始以外の日をカウント
    if (!isClosedDay(current)) {
      businessDays++;
    }
    current.setDate(current.getDate() + 1);
  }

  return businessDays;
}

// テスト実行関数
function runTests() {
  console.log('===== 祝日判定ロジックテスト開始 =====');

  let totalTests = 0;
  let passedTests = 0;

  for (const testCase of testCases) {
    console.log(`\n----- ${testCase.name} -----`);

    for (const dateTest of testCase.dates) {
      totalTests++;
      const result = testCase.testFunction(dateTest.date);
      const passed = result === dateTest.expected;

      console.log(`${passed ? '✅' : '❌'} ${dateTest.description}: ${result}`);

      if (passed) {
        passedTests++;
      } else {
        console.log(`  期待値: ${dateTest.expected}, 実際値: ${result}`);
      }
    }
  }

  // 祝日を挟んだ期間の営業日数計算テスト
  console.log('\n----- 祝日を挟んだ期間の営業日数計算テスト -----');

  const businessDaysTests = [
    {
      startDate: '2025-04-28', // 月曜日
      endDate: '2025-05-07',   // 水曜日
      expectedDays: 5,         // 4/28, 4/30, 5/1, 5/2, 5/7 (日曜日と祝日を除く)
      description: 'ゴールデンウィーク期間'
    },
    {
      startDate: '2025-11-01', // 土曜日
      endDate: '2025-11-05',   // 水曜日
      expectedDays: 3,         // 11/1, 11/4, 11/5 (日曜日と文化の日を除く)
      description: '文化の日を含む期間'
    },
    {
      startDate: '2024-12-27', // 金曜日
      endDate: '2025-01-06',   // 月曜日
      expectedDays: 4,         // 12/27, 12/28, 1/4, 1/6 (日曜日と年末年始を除く)
      description: '年末年始を含む期間'
    },
    {
      startDate: '2025-09-13', // 土曜日
      endDate: '2025-09-16',   // 火曜日
      expectedDays: 2,         // 9/13, 9/16 (日曜日と敬老の日を除く)
      description: '敬老の日を含む期間'
    }
  ];

  for (const test of businessDaysTests) {
    totalTests++;
    const result = calculateBusinessDays(test.startDate, test.endDate);
    const passed = result === test.expectedDays;

    console.log(`${passed ? '✅' : '❌'} ${test.description}: ${test.startDate} 〜 ${test.endDate}`);
    console.log(`  営業日数: ${result}日`);

    if (passed) {
      passedTests++;
    } else {
      console.log(`  期待値: ${test.expectedDays}日, 実際値: ${result}日`);
    }
  }

  // 総合結果
  console.log('\n===== テスト結果 =====');
  console.log(`合計: ${totalTests}件`);
  console.log(`成功: ${passedTests}件`);
  console.log(`失敗: ${totalTests - passedTests}件`);

  return passedTests === totalTests;
}

// テスト実行
const success = runTests();
if (success) {
  console.log('\n✅ 全てのテストが成功しました！');
} else {
  console.log('\n❌ 一部のテストが失敗しました。');
}
