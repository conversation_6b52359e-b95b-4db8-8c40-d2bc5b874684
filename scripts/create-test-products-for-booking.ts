/**
 * 予約テスト用商品作成スクリプト
 *
 * このスクリプトは、予約テスト用の商品を2つデータベースに作成します。
 * 実行方法: npx tsx scripts/create-test-products-for-booking.ts
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';

// 環境変数の読み込み
dotenv.config();

// Prismaクライアントの初期化
const prisma = new PrismaClient();

// テスト設定
const config = {
  // テスト用のショップ名
  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',

  // 実際のSKUを使用したテスト用商品情報（CSVの全項目を含む）
  products: [
    {
      shopifyId: `212-05-023-${Math.floor(Math.random() * 1000)}`,
      title: '花器　シルバー　穴開きボトル型',
      sku: '212-05-023',
      price: 1500,
      status: 'AVAILABLE',
      locationId: 'NY',
      basicInfo: JSON.stringify({
        // CSVの全項目をマッピング
        menu: 'PROP',
        majorCategory: 'ニュアンスオブジェ;花器',
        minorCategory: 'その他オブジェ;ゴールド・シルバー',
        name: '花器　シルバー　穴開きボトル型',
        modelNumber: '212-05-023',
        sizeW: 14,
        sizeD: 0,
        sizeH: 31,
        sizeSH: 0,
        sizeOther: '',
        color: 'シルバー',
        colorOther: '',
        material: '',
        other: '',
        stockQuantity: 1,
        rentalFee1Day: 1500,
        rentalFee1Night2Days: 1800,
        rentalFee2Nights3Days: 2100,
        rentalFee3Nights4Days: 2400,
        publicStatus: '公開',
        newStatus: 'あり',
        campaign: '2025 NEW ITEMS',
        // 内部管理用
        location: 'NY',
        status: 'available',
        category: '花器',
        subCategory: 'ゴールド・シルバー'
      }),
      pricing: JSON.stringify({
        basePrice: 1500,
        depositRate: 0.1,
        discountRules: {
          day2_7_rate: 0.2,
          day8_plus_rate: 0.1
        },
        provisionalRate: 0.1,
        minimumDays: 1,
        maximumDays: 30
      })
    },
    {
      shopifyId: `201-07-107-${Math.floor(Math.random() * 1000)}`,
      title: 'シルバートレー　オーバル　フリルフレーム　持ち手付',
      sku: '201-07-107',
      price: 3000,
      status: 'AVAILABLE',
      locationId: 'NY',
      basicInfo: JSON.stringify({
        // CSVの全項目をマッピング
        menu: 'PROP',
        majorCategory: 'キッチン;コスメ',
        minorCategory: 'コスメボックス・トレー;銀食器',
        name: 'シルバートレー　オーバル　フリルフレーム　持ち手付',
        modelNumber: '201-07-107',
        sizeW: 70,
        sizeD: 50,
        sizeH: 0,
        sizeSH: 0,
        sizeOther: '',
        color: 'シルバー',
        colorOther: '',
        material: '',
        other: '',
        stockQuantity: 1,
        rentalFee1Day: 3000,
        rentalFee1Night2Days: 3600,
        rentalFee2Nights3Days: 4200,
        rentalFee3Nights4Days: 4800,
        publicStatus: '公開',
        newStatus: 'あり',
        campaign: 'Silver ware;2025 NEW ITEMS',
        // 内部管理用
        location: 'NY',
        status: 'available',
        category: 'キッチン',
        subCategory: '銀食器'
      }),
      pricing: JSON.stringify({
        basePrice: 3000,
        depositRate: 0.1,
        discountRules: {
          day2_7_rate: 0.2,
          day8_plus_rate: 0.1
        },
        provisionalRate: 0.1,
        minimumDays: 1,
        maximumDays: 30
      })
    },
    {
      shopifyId: `201-05-797-${Math.floor(Math.random() * 1000)}`,
      title: 'キャンドルスタンド　シャインシルバー　3灯',
      sku: '201-05-797',
      price: 2000,
      status: 'AVAILABLE',
      locationId: 'NY',
      basicInfo: JSON.stringify({
        // CSVの全項目をマッピング
        menu: 'PROP',
        majorCategory: 'キャンドル',
        minorCategory: 'キャンドルスタンド',
        name: 'キャンドルスタンド　シャインシルバー　3灯',
        modelNumber: '201-05-797',
        sizeW: 31,
        sizeD: 0,
        sizeH: 35,
        sizeSH: 0,
        sizeOther: '',
        color: 'シルバー',
        colorOther: '',
        material: '',
        other: '',
        stockQuantity: 2,
        rentalFee1Day: 2000,
        rentalFee1Night2Days: 2400,
        rentalFee2Nights3Days: 2800,
        rentalFee3Nights4Days: 3200,
        publicStatus: '公開',
        newStatus: 'あり',
        campaign: '2025 NEW ITEMS',
        // 内部管理用
        location: 'NY',
        status: 'available',
        category: 'キャンドル',
        subCategory: 'キャンドルスタンド'
      }),
      pricing: JSON.stringify({
        basePrice: 2000,
        depositRate: 0.1,
        discountRules: {
          day2_7_rate: 0.2,
          day8_plus_rate: 0.1
        },
        provisionalRate: 0.1,
        minimumDays: 1,
        maximumDays: 30
      })
    },
    {
      shopifyId: `211-12-954-${Math.floor(Math.random() * 1000)}`,
      title: 'オブジェ　タイドロープ　ゴールド',
      sku: '211-12-954',
      price: 1000,
      status: 'MAINTENANCE',
      locationId: 'NY',
      basicInfo: JSON.stringify({
        // CSVの全項目をマッピング
        menu: 'PROP',
        majorCategory: 'ニュアンスオブジェ',
        minorCategory: 'その他オブジェ',
        name: 'オブジェ　タイドロープ　ゴールド',
        modelNumber: '211-12-954',
        sizeW: 27,
        sizeD: 0,
        sizeH: 14,
        sizeSH: 0,
        sizeOther: '',
        color: 'ゴールド',
        colorOther: '',
        material: '',
        other: '',
        stockQuantity: 1,
        rentalFee1Day: 1000,
        rentalFee1Night2Days: 1200,
        rentalFee2Nights3Days: 1400,
        rentalFee3Nights4Days: 1600,
        publicStatus: '公開',
        newStatus: 'あり',
        campaign: '2025 NEW ITEMS',
        // 内部管理用
        location: 'NY',
        status: 'maintenance',
        category: 'オブジェ',
        subCategory: 'その他オブジェ'
      }),
      pricing: JSON.stringify({
        basePrice: 1000,
        depositRate: 0.1,
        discountRules: {
          day2_7_rate: 0.2,
          day8_plus_rate: 0.1
        },
        provisionalRate: 0.1,
        minimumDays: 1,
        maximumDays: 30
      })
    },
    {
      shopifyId: `206-02-320-${Math.floor(Math.random() * 1000)}`,
      title: 'ジュエリートレー　ホワイト×クリア　フリル型　持ち手付',
      sku: '206-02-320',
      price: 1200,
      status: 'AVAILABLE',
      locationId: 'PR',
      basicInfo: JSON.stringify({
        // CSVの全項目をマッピング
        menu: 'PROP',
        majorCategory: 'コスメ',
        minorCategory: 'コスメボックス・トレー',
        name: 'ジュエリートレー　ホワイト×クリア　フリル型　持ち手付',
        modelNumber: '206-02-320',
        sizeW: 20,
        sizeD: 0,
        sizeH: 17,
        sizeSH: 0,
        sizeOther: '',
        color: 'ホワイト;クリア',
        colorOther: '',
        material: '',
        other: '',
        stockQuantity: 1,
        rentalFee1Day: 1200,
        rentalFee1Night2Days: 1440,
        rentalFee2Nights3Days: 1680,
        rentalFee3Nights4Days: 1920,
        publicStatus: '公開',
        newStatus: 'あり',
        campaign: '2025 NEW ITEMS',
        // 内部管理用
        location: 'PR',
        status: 'available',
        category: 'コスメ',
        subCategory: 'コスメボックス・トレー'
      }),
      pricing: JSON.stringify({
        basePrice: 1200,
        depositRate: 0.1,
        discountRules: {
          day2_7_rate: 0.2,
          day8_plus_rate: 0.1
        },
        provisionalRate: 0.1,
        minimumDays: 1,
        maximumDays: 30
      })
    }
  ]
};

/**
 * カテゴリマスタからカテゴリ情報を取得
 */
async function getCategoryInfo(majorCategory: string, minorCategory: string): Promise<any> {
  try {
    const shop = config.shop;

    // CSVのカテゴリ名からカテゴリマスタのコードにマッピング
    const categoryMapping: { [key: string]: string } = {
      'ニュアンスオブジェ': '201',
      '花器': '212',
      'キッチン': '201',
      'コスメ': '206',
      'キャンドル': '201',
      'オブジェ': '211',
      'ガーデン・アウトドア': '104'
    };

    const subCategoryMapping: { [key: string]: string } = {
      'その他オブジェ': '06',
      'ゴールド・シルバー': '01',
      'コスメボックス・トレー': '08',
      '銀食器': '07',
      'キャンドルスタンド': '05',
      'BBQグッズ': '10'
    };

    // メジャーカテゴリから最初のカテゴリを取得
    const firstMajorCategory = majorCategory.split(';')[0];
    const categoryCode = categoryMapping[firstMajorCategory] || '201'; // デフォルトはニュアンスオブジェ

    // マイナーカテゴリから最初のサブカテゴリを取得
    const firstMinorCategory = minorCategory.split(';')[0];
    const subCategoryCode = subCategoryMapping[firstMinorCategory] || '06'; // デフォルトはその他オブジェ

    // カテゴリマスタから実際のデータを取得
    const category = await prisma.categoryMaster.findUnique({
      where: {
        shop_code: {
          shop,
          code: categoryCode
        }
      }
    });

    const subCategory = await prisma.subCategoryMaster.findUnique({
      where: {
        shop_code_parentCategoryCode: {
          shop,
          code: subCategoryCode,
          parentCategoryCode: categoryCode
        }
      }
    });

    return {
      categoryCode: category?.code || categoryCode,
      categoryName: category?.name || firstMajorCategory,
      subCategoryCode: subCategory?.code || subCategoryCode,
      subCategoryName: subCategory?.name || firstMinorCategory
    };

  } catch (error) {
    console.warn(`カテゴリ情報取得エラー: ${error.message}`);
    // エラーの場合はデフォルト値を返す
    return {
      categoryCode: '201',
      categoryName: 'ニュアンスオブジェ',
      subCategoryCode: '06',
      subCategoryName: 'その他オブジェ'
    };
  }
}

/**
 * テスト用商品を作成する関数
 */
async function createTestProduct(productData: any) {
  try {
    console.log(`テスト用商品「${productData.title}」を作成中...`);

    // 既存の商品を削除（強制的に新しく作成）
    await prisma.product.deleteMany({
      where: {
        shop: config.shop,
        sku: productData.sku
      }
    });

    // basicInfoからカテゴリ情報を取得
    const basicInfo = JSON.parse(productData.basicInfo);
    const categoryInfo = await getCategoryInfo(basicInfo.majorCategory, basicInfo.minorCategory);

    // basicInfoにカテゴリマスタ情報を追加
    const updatedBasicInfo = {
      ...basicInfo,
      // カテゴリマスタ連動情報
      categoryMasterCode: categoryInfo.categoryCode,
      categoryMasterName: categoryInfo.categoryName,
      subCategoryMasterCode: categoryInfo.subCategoryCode,
      subCategoryMasterName: categoryInfo.subCategoryName
    };

    // 商品データを作成
    const product = await prisma.product.create({
      data: {
        id: uuidv4(),
        shop: config.shop,
        shopifyId: productData.shopifyId,
        title: productData.title,
        sku: productData.sku,
        price: productData.price,
        status: productData.status,
        locationId: productData.locationId,
        basicInfo: JSON.stringify(updatedBasicInfo),
        pricing: productData.pricing,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('テスト用商品を作成しました:');
    console.log(`商品ID: ${product.id}`);
    console.log(`ShopifyID: ${product.shopifyId}`);
    console.log(`商品名: ${product.title}`);
    console.log(`SKU: ${product.sku}`);
    console.log(`カテゴリ: ${categoryInfo.categoryCode} - ${categoryInfo.categoryName}`);
    console.log(`サブカテゴリ: ${categoryInfo.subCategoryCode} - ${categoryInfo.subCategoryName}`);

    return product;
  } catch (error) {
    console.error('テスト用商品作成エラー:', error);
    throw error;
  }
}

/**
 * メイン関数
 */
async function main() {
  try {
    console.log('予約テスト用商品作成スクリプトを開始します...');

    const createdProducts = [];

    // テスト用商品を作成
    for (const productData of config.products) {
      const product = await createTestProduct(productData);
      createdProducts.push(product);
    }

    console.log('\n===== テスト用商品情報 =====');

    for (let i = 0; i < createdProducts.length; i++) {
      const product = createdProducts[i];

      console.log(`\n----- 商品${i + 1} -----`);
      console.log(`商品ID: ${product.id}`);
      console.log(`ShopifyID: ${product.shopifyId}`);
      console.log(`商品名: ${product.title}`);
      console.log(`SKU: ${product.sku}`);
      console.log(`価格: ${product.price}円`);
      console.log(`ステータス: ${product.status}`);
      console.log(`ロケーション: ${product.locationId}`);
    }

    console.log('\n以下のコマンドでテストを実行できます:');
    console.log(`npm run test:date-conflict ${createdProducts[0].shopifyId}`);
    console.log(`npm run test:holiday-pricing ${createdProducts[0].shopifyId}`);
    console.log(`npm run test:multi-product ${createdProducts[0].shopifyId} ${createdProducts[1].shopifyId}`);
  } catch (error) {
    console.error('スクリプト実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
main();
