# レンタル商品料金計算ロジック

## 概要

このドキュメントでは、レンタル商品ECシステムにおける料金計算ロジックについて詳細に説明します。Shopify上でカスタムアプリを使用して実装する際の具体的な計算方法、実装上の注意点、およびコード例を提供します。

## 料金体系

### 基本料金モデル

レンタル商品の料金は以下のシンプルな体系に基づいて計算されます：

1. **1日目**: 基本料金の100%
2. **2〜6日目**: 各日基本料金の20%
3. **7日目以降**: 各日基本料金の10%

この料金体系は、以下の利点があります：
- シンプルで理解しやすい
- 実装が容易
- 長期レンタルを促進する割引構造
- 商品の基本価格を1日目の料金に設定するだけで運用可能

### 計算例

例えば、基本料金が10,000円の商品の場合：

| レンタル日数 | 計算式 | 合計金額 |
|------------|-------|--------|
| 1日 | 10,000円 × 100% | 10,000円 |
| 3日 | 10,000円 + (10,000円 × 20% × 2日) | 14,000円 |
| 7日 | 10,000円 + (10,000円 × 20% × 5日) + (10,000円 × 10% × 1日) | 21,000円 |
| 10日 | 10,000円 + (10,000円 × 20% × 5日) + (10,000円 × 10% × 4日) | 25,000円 |

## 実装方法

### 料金計算関数

以下のJavaScript関数を使用して、レンタル料金を計算します：

```javascript
/**
 * レンタル料金を計算する関数
 * @param {number} basePrice - 基本料金（1日目の料金）
 * @param {number} days - レンタル日数
 * @param {Object} options - オプション設定
 * @returns {number} 合計レンタル料金
 */
function calculateRentalPrice(basePrice, days, options = {}) {
  // デフォルトオプション
  const defaultOptions = {
    day2To6Rate: 0.2,  // 2〜6日目の料率
    day7PlusRate: 0.1, // 7日目以降の料率
    excludeWeekends: false, // 週末を除外するかどうか
    excludeHolidays: false, // 祝日を除外するかどうか
    holidays: [],      // 除外する祝日のリスト
    roundToInteger: true, // 整数に丸めるかどうか
    seasonalRates: [], // 季節料金設定
    depositRate: 0.1,  // デポジット率
    minDepositAmount: 1000, // 最低デポジット金額
    maxDepositAmount: 50000 // 最高デポジット金額
  };

  // オプションをマージ
  const settings = { ...defaultOptions, ...options };

  // 基本料金（1日目）
  let totalPrice = basePrice;

  // 2〜6日目の料金を計算
  if (days > 1) {
    const day2to6Count = Math.min(days - 1, 5); // 最大5日間（2日目〜6日目）
    totalPrice += basePrice * settings.day2To6Rate * day2to6Count;
  }

  // 7日目以降の料金を計算
  if (days > 6) {
    const day7PlusCount = days - 6;
    totalPrice += basePrice * settings.day7PlusRate * day7PlusCount;
  }

  // 季節料金の適用
  if (settings.seasonalRates && settings.seasonalRates.length > 0) {
    // 季節料金の計算ロジックを実装
    // ...
  }

  // 整数に丸める（オプション）
  if (settings.roundToInteger) {
    totalPrice = Math.round(totalPrice);
  }

  return totalPrice;
}
```

### 日数計算関数

レンタル期間の日数を計算する関数：

```javascript
/**
 * 2つの日付間の日数を計算する関数
 * @param {Date} startDate - 開始日
 * @param {Date} endDate - 終了日
 * @param {Object} options - オプション設定
 * @returns {number} 日数
 */
function calculateDays(startDate, endDate, options = {}) {
  // デフォルトオプション
  const defaultOptions = {
    includeEndDate: true, // 終了日を含めるかどうか
    excludeWeekends: false, // 週末を除外するかどうか
    excludeHolidays: false, // 祝日を除外するかどうか
    holidays: []      // 除外する祝日のリスト
  };

  // オプションをマージ
  const settings = { ...defaultOptions, ...options };

  // 日数を計算
  const oneDay = 24 * 60 * 60 * 1000; // 1日のミリ秒数
  let diffDays = Math.round(Math.abs((startDate - endDate) / oneDay));

  // 終了日を含める場合は1日追加
  if (settings.includeEndDate) {
    diffDays += 1;
  }

  // 週末を除外する場合
  if (settings.excludeWeekends) {
    let currentDate = new Date(startDate);
    let weekendCount = 0;

    for (let i = 0; i < diffDays; i++) {
      const dayOfWeek = currentDate.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) { // 0=日曜日, 6=土曜日
        weekendCount++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    diffDays -= weekendCount;
  }

  // 祝日を除外する場合
  if (settings.excludeHolidays && settings.holidays.length > 0) {
    let holidayCount = 0;
    let currentDate = new Date(startDate);

    for (let i = 0; i < diffDays; i++) {
      const dateString = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD形式
      if (settings.holidays.includes(dateString)) {
        holidayCount++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    diffDays -= holidayCount;
  }

  return diffDays;
}
```

### フロントエンドでの実装

カレンダーUIと連携した料金計算の実装例：

```javascript
// カレンダーコンポーネントの例
function RentalCalendar({ product }) {
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [totalPrice, setTotalPrice] = useState(0);
  const [priceBreakdown, setPriceBreakdown] = useState([]);

  useEffect(() => {
    if (startDate && endDate) {
      // 日数を計算
      const days = calculateDays(startDate, endDate);

      // 料金を計算
      const price = calculateRentalPrice(product.price, days);
      setTotalPrice(price);

      // 料金内訳を計算
      const breakdown = calculatePriceBreakdown(product.price, days);
      setPriceBreakdown(breakdown);
    }
  }, [startDate, endDate, product]);

  // 料金内訳を計算する関数
  function calculatePriceBreakdown(basePrice, days) {
    const breakdown = [];

    // 1日目
    breakdown.push({
      description: '1日目',
      amount: basePrice,
      rate: '100%'
    });

    // 2〜6日目
    if (days > 1) {
      const day2to6Count = Math.min(days - 1, 5);
      const day2to6Amount = basePrice * 0.2 * day2to6Count;
      breakdown.push({
        description: `2〜${1 + day2to6Count}日目`,
        amount: day2to6Amount,
        rate: '20%',
        days: day2to6Count
      });
    }

    // 7日目以降
    if (days > 6) {
      const day7PlusCount = days - 6;
      const day7PlusAmount = basePrice * 0.1 * day7PlusCount;
      breakdown.push({
        description: `7〜${days}日目`,
        amount: day7PlusAmount,
        rate: '10%',
        days: day7PlusCount
      });
    }

    return breakdown;
  }

  // カレンダーUIのレンダリング
  return (
    <div className="rental-calendar">
      <div className="date-selection">
        <DatePicker
          selected={startDate}
          onChange={date => setStartDate(date)}
          selectsStart
          startDate={startDate}
          endDate={endDate}
          minDate={new Date()}
          placeholderText="レンタル開始日を選択"
        />
        <DatePicker
          selected={endDate}
          onChange={date => setEndDate(date)}
          selectsEnd
          startDate={startDate}
          endDate={endDate}
          minDate={startDate}
          placeholderText="レンタル終了日を選択"
        />
      </div>

      {startDate && endDate && (
        <div className="price-display">
          <h3>レンタル料金: {formatPrice(totalPrice)}</h3>

          <div className="price-breakdown">
            <h4>料金内訳:</h4>
            <ul>
              {priceBreakdown.map((item, index) => (
                <li key={index}>
                  {item.description}: {formatPrice(item.amount)}
                  {item.days && ` (${item.rate} × ${item.days}日)`}
                </li>
              ))}
            </ul>
          </div>

          <button
            onClick={() => addToCart(product, startDate, endDate, totalPrice)}
            className="add-to-cart-button"
          >
            カートに追加
          </button>
        </div>
      )}
    </div>
  );
}
```

## カートへの追加と表示

### カートへの追加処理

選択された日付と計算された料金をカートに追加する処理：

```javascript
/**
 * カートに商品を追加する関数
 * @param {Object} product - 商品情報
 * @param {Date} startDate - レンタル開始日
 * @param {Date} endDate - レンタル終了日
 * @param {number} calculatedPrice - 計算された料金
 */
async function addToCart(product, startDate, endDate, calculatedPrice) {
  // 日付をフォーマット
  const formattedStartDate = formatDate(startDate);
  const formattedEndDate = formatDate(endDate);

  // 日数を計算
  const days = calculateDays(startDate, endDate);

  // Line Item Propertiesとして日付情報を保存
  const properties = {
    'レンタル開始日': formattedStartDate,
    'レンタル終了日': formattedEndDate,
    'レンタル日数': days,
    '計算料金': formatPrice(calculatedPrice)
  };

  try {
    // カートに追加
    const response = await fetch('/cart/add.js', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        id: product.variant_id,
        quantity: 1,
        properties: properties
      })
    });

    if (!response.ok) {
      throw new Error('カートへの追加に失敗しました');
    }

    // カートページにリダイレクト
    window.location.href = '/cart';
  } catch (error) {
    console.error('エラー:', error);
    alert('カートへの追加中にエラーが発生しました。もう一度お試しください。');
  }
}
```

## チェックアウト時の注意点

### 価格表示と実際の請求額の差異

Shopify標準のCart APIを使用する場合、チェックアウト時には商品の基本価格が適用されます。これにより、カートページで表示される計算料金と、チェックアウト時に表示される金額に差異が生じる可能性があります。

### 対応策

1. **ユーザーへの明確な説明**:
   - カートページとチェックアウトページに注意書きを追加
   - 「表示価格は概算です。実際の請求額は注文確定後にメールでお知らせします」などの説明を表示

2. **注文後の価格調整**:
   - 注文確定後に、管理画面から価格を手動で調整
   - Shopify Admin APIを使用して自動的に価格を調整するスクリプトを実装

```javascript
/**
 * 注文の価格を調整する関数
 * @param {string} orderId - 注文ID
 * @param {number} adjustedPrice - 調整後の価格
 */
async function adjustOrderPrice(orderId, adjustedPrice) {
  // Shopify Admin APIを使用して注文を更新
  // 実際の実装はShopify Admin APIのドキュメントを参照

  // 例: GraphQL APIを使用した実装
  const mutation = `
    mutation orderEditBegin($id: ID!) {
      orderEditBegin(id: $id) {
        calculatedOrder {
          id
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  // 注文編集を開始
  // 価格を調整
  // 注文編集を確定

  // 調整後の請求書をユーザーに送信
}
```

## 実装済みの拡張機能

### 料金設定の拡張

基本料金体系を拡張し、以下の機能を実装しました：

1. **シーズン別料金**:
   - 特定期間に対して料金倍率を設定可能
   - 繁忙期（夏季、年末年始など）は割増料金
   - 閑散期は割引料金

2. **デポジット設定の柔軟化**:
   - 商品ごとにデポジット率を設定可能
   - 最低・最高デポジット金額の設定可能

3. **キャンセル料金の柔軟化**:
   - 商品やカテゴリごとにキャンセル料率を設定可能
   - 日数区分ごとの料率をカスタマイズ可能

### 実装例：シーズン別料金の設定

```json
{
  "seasonalRates": [
    {
      "startDate": "2024-07-20",
      "endDate": "2024-08-31",
      "name": "夏季",
      "rateMultiplier": 1.5
    },
    {
      "startDate": "2024-12-25",
      "endDate": "2025-01-05",
      "name": "年末年始",
      "rateMultiplier": 1.8
    },
    {
      "startDate": "2024-05-01",
      "endDate": "2024-05-10",
      "name": "GW",
      "rateMultiplier": 1.3
    }
  ]
}
```

## 将来の拡張計画

以下の機能を今後実装予定です：

1. **曜日別料金**:
   - 週末（金・土・日）は割増料金
   - 平日は通常料金または割引料金

2. **長期レンタル割引**:
   - 30日以上のレンタルでさらに割引
   - 定期レンタルプランの提供

3. **セット割引**:
   - 複数商品同時レンタルの割引
   - 組み合わせパッケージの料金

4. **顧客ランク別料金**:
   - リピーター割引
   - 会員ランク別の料金設定

## まとめ

このドキュメントでは、レンタル商品ECシステムにおける料金計算ロジックについて詳細に説明しました。シンプルな料金体系（1日目は基本料金、2〜6日目は各日20%、7日目以降は各日10%）を採用することで、実装の複雑さを軽減しつつ、柔軟なレンタルシステムを構築できます。

チェックアウト時の価格表示と実際の請求額の差異に注意し、適切な対応策を講じることで、ユーザーに透明性のある料金体系を提供することが重要です。

将来的な拡張性も考慮し、柔軟な設計を心がけることで、ビジネスの成長に合わせてシステムを進化させることができます。
