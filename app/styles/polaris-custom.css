/* Polaris カスタマイズ - 画面幅の拡張 */

/* Polaris Page コンポーネントの最大幅を1400pxに拡張 */
.Polaris-Page {
  margin: 0 auto;
  padding: 0;
  max-width: 1400px !important;
}

/* Polaris Layout の最大幅も調整 */
.Polaris-Layout {
  max-width: 1400px !important;
  margin: 0 auto;
}

/* Polaris Card の幅を最大限活用 */
.Polaris-Card {
  width: 100%;
}

/* DataTable の幅を最大限活用 */
.Polaris-DataTable__Table {
  width: 100%;
  min-width: 100%;
}

.Polaris-DataTable__ScrollContainer {
  width: 100%;
}

/* 大画面での余白調整 */
@media (min-width: 1200px) {
  .Polaris-Page {
    padding: 0 2rem;
  }
  
  .Polaris-Layout {
    padding: 0;
  }
  
  /* テーブルの列幅を最適化 */
  .Polaris-DataTable__Cell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  /* 検索フィールドの幅を拡張 */
  .Polaris-TextField__Input {
    min-width: 300px;
  }
}

/* 超大画面（1400px以上）での最適化 */
@media (min-width: 1400px) {
  .Polaris-Page {
    padding: 0 3rem;
  }
  
  /* より多くの情報を表示するためのテーブル最適化 */
  .Polaris-DataTable__Table {
    table-layout: auto;
  }
  
  .Polaris-DataTable__Cell {
    max-width: none;
    white-space: normal;
  }
  
  /* 検索セクションの幅を最大限活用 */
  .Polaris-InlineStack {
    width: 100%;
  }
}

/* レスポンシブ対応 - タブレット */
@media (min-width: 768px) and (max-width: 1199px) {
  .Polaris-Page {
    max-width: 100%;
    padding: 0 1rem;
  }
}

/* レスポンシブ対応 - モバイル */
@media (max-width: 767px) {
  .Polaris-Page {
    max-width: 100%;
    padding: 0 0.5rem;
  }
  
  .Polaris-DataTable__ScrollContainer {
    overflow-x: auto;
  }
}

/* 予約管理画面専用の最適化 */
.booking-management-page .Polaris-DataTable__Table {
  font-size: 0.875rem; /* 14px */
}

.booking-management-page .Polaris-DataTable__Cell {
  padding: 0.5rem 0.75rem;
}

/* 検索結果の表示密度を上げる */
.booking-management-page .Polaris-DataTable--condensed .Polaris-DataTable__Cell {
  padding: 0.375rem 0.5rem;
}

/* ステータスバッジの最適化 */
.Polaris-Badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* 長いテキストの表示改善 */
.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.text-wrap {
  white-space: normal;
  word-break: break-word;
}

/* カスタムユーティリティクラス */
.w-full-important {
  width: 100% !important;
}

.max-w-none-important {
  max-width: none !important;
}
