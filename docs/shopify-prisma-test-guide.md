# Shopify と Prisma 連携テストガイド

このドキュメントは、Shopify と Prisma の連携テストに関する情報をまとめたものです。テストスクリプトの使用方法、テスト環境の設定、テスト結果の解釈などを説明します。

## 目次

1. [テストスクリプトの概要](#テストスクリプトの概要)
2. [テスト環境の設定](#テスト環境の設定)
3. [テストの実行方法](#テストの実行方法)
4. [テスト結果の解釈](#テスト結果の解釈)
5. [トラブルシューティング](#トラブルシューティング)
6. [テストの拡張](#テストの拡張)

## テストスクリプトの概要

テストスクリプト `scripts/test-shopify-prisma-integration.ts` は、Shopify と Prisma の連携をテストするためのものです。主に以下の機能をテストします：

1. **商品同期**: Shopify の商品データを Prisma データベースに同期
2. **在庫カレンダー更新**: 商品の在庫カレンダーを更新
3. **予約作成**: 同期された商品データを使用して予約を作成
4. **注文連携**: 予約データと Shopify の注文データを関連付け
5. **商品バリエーション**: バリエーションを持つ商品の作成と予約
6. **エラーケース**: 無効なデータや例外的な状況での動作

テストスクリプトは、実際の Shopify API を使用するモードとローカルモード（モックデータを使用）の両方をサポートしています。

## テスト環境の設定

### 環境変数

テスト環境の設定は、`.env.test` ファイルで行うことができます。以下は設定例です：

```
# Shopify API 設定
SHOPIFY_API_KEY=your_api_key
SHOPIFY_API_SECRET=your_api_secret
SHOPIFY_SHOP=your-shop.myshopify.com
SHOPIFY_ADMIN_API_ACCESS_TOKEN=your_access_token
SHOPIFY_API_VERSION=2024-01

# テスト設定
TEST_PRODUCT_ID=123456789
TEST_BOOKING_DAYS=3
TEST_CUSTOMER_EMAIL=<EMAIL>
TEST_CUSTOMER_NAME=テスト顧客
USE_LOCAL_MODE=false
```

### 必要なパッケージ

テストスクリプトを実行するには、以下のパッケージが必要です：

```bash
npm install --save-dev uuid node-fetch @types/uuid @types/node-fetch
```

### データベース設定

テストは実際の Prisma データベースを使用します。テスト用のデータベースを用意することをお勧めします：

```bash
# テスト用のデータベースを作成
npx prisma db push --preview-feature

# テストデータをシードする（必要に応じて）
npx prisma db seed
```

## テストの実行方法

### 基本的な使い方

```bash
# 基本テスト
npm run test:shopify-prisma-integration

# または
npx tsx scripts/test-shopify-prisma-integration.ts
```

### オプション

テストスクリプトは、以下のコマンドライン引数をサポートしています：

- `--cleanup`: テスト終了後にテストデータをクリーンアップ
- `--error-cases`: エラーケースのみをテスト
- `--variants`: バリエーションのみをテスト
- `--local` または `--no-api`: Shopify API を使用せず、ローカルモードで実行
- `<product_id>`: 特定の商品 ID を指定

### 使用例

```bash
# クリーンアップあり
npm run test:shopify-prisma-integration -- --cleanup

# エラーケースのみ
npm run test:shopify-prisma-integration -- --error-cases

# バリエーションのみ
npm run test:shopify-prisma-integration -- --variants

# ローカルモード
npm run test:shopify-prisma-integration -- --local

# 特定の商品 ID を指定
npm run test:shopify-prisma-integration -- 123456789

# 複数のオプションを組み合わせ
npm run test:shopify-prisma-integration -- --variants --cleanup
```

## テスト結果の解釈

テスト結果は、コンソール出力とテスト結果レポートの両方で確認できます。

### コンソール出力

テスト実行中、以下の情報がコンソールに出力されます：

- テストの進行状況
- 商品情報
- 予約情報
- エラーメッセージ（発生した場合）
- テスト結果のサマリー

### テスト結果レポート

テスト終了後、`docs/shopify-prisma-integration-test-report.md` にテスト結果レポートが生成されます。レポートには以下の情報が含まれます：

- テスト概要
- テスト環境
- テスト結果の概要（成功/失敗）
- 各テストケースの詳細結果
- 実行環境情報
- 改善提案

### 成功基準

テストは以下の条件を満たす場合に成功とみなされます：

- 基本テスト: 商品同期、在庫カレンダー更新、予約作成、注文連携のすべてが成功
- バリエーションテスト: バリエーション商品の作成と予約が成功
- エラーケーステスト: すべてのエラーケースが期待通りに動作

## トラブルシューティング

### よくある問題と解決策

1. **Shopify API エラー**
   - API キーとアクセストークンが正しく設定されているか確認
   - メタフィールドの型が `json` になっているか確認
   - API レート制限に達していないか確認

2. **データベースエラー**
   - Prisma データベースの接続設定を確認
   - データベースのマイグレーションが最新か確認
   - 必要なテーブルとカラムが存在するか確認

3. **テストスクリプトエラー**
   - 必要なパッケージがインストールされているか確認
   - TypeScript のバージョンが互換性があるか確認
   - Node.js のバージョンが互換性があるか確認

### デバッグ方法

1. **詳細なログ出力**
   - `DEBUG=true` 環境変数を設定して、詳細なログを出力
   - `npx tsx --inspect scripts/test-shopify-prisma-integration.ts` でデバッガを有効化

2. **部分的なテスト実行**
   - 特定のテストケースのみを実行して、問題を切り分け
   - ローカルモードで実行して、API 関連の問題を切り分け

3. **手動検証**
   - Shopify 管理画面で商品データを確認
   - Prisma Studio で データベースの内容を確認
   - API リクエストを Postman などで手動で実行

## テストの拡張

### 新しいテストケースの追加

テストスクリプトに新しいテストケースを追加する方法：

1. 新しいテスト関数を作成
2. メイン関数に新しいテストケースを追加
3. テスト結果レポートに新しいテストケースの結果を追加

### テスト自動化

CI/CD パイプラインにテストを組み込む方法：

1. GitHub Actions や CircleCI などの CI/CD サービスを設定
2. テストスクリプトを CI/CD パイプラインに組み込む
3. テスト結果を自動的に収集して報告

### パフォーマンステスト

パフォーマンステストを追加する方法：

1. 大量のデータを使用したテストケースを作成
2. 実行時間を計測する機能を追加
3. パフォーマンスメトリクスを収集して報告
