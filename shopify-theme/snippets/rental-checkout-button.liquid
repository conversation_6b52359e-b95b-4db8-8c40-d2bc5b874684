{% comment %}
  レンタル商品用のドラフトオーダーチェックアウトボタン
{% endcomment %}

<div id="rental-checkout-container" class="cart__footer-actions" style="margin-top: 20px;">
  <button type="button" id="rental-checkout-button" class="btn btn--full btn--primary" style="width: 100%; padding: 15px; font-size: 16px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
    レンタル商品をチェックアウト
  </button>
  <div id="rental-checkout-loading" style="display: none; text-align: center; margin-top: 10px;">
    <span>処理中...</span>
  </div>
  <div id="rental-checkout-error" style="display: none; color: red; margin-top: 10px; text-align: center;">
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const checkoutButton = document.getElementById('rental-checkout-button');
    const loadingIndicator = document.getElementById('rental-checkout-loading');
    const errorMessage = document.getElementById('rental-checkout-error');
    
    if (checkoutButton) {
      checkoutButton.addEventListener('click', async function() {
        try {
          // ローディング表示
          checkoutButton.disabled = true;
          loadingIndicator.style.display = 'block';
          errorMessage.style.display = 'none';
          
          // カートの内容を取得
          const cartResponse = await fetch('/cart.js');
          const cart = await cartResponse.json();
          
          // レンタル商品かどうかをチェック
          const hasRentalItems = cart.items.some(item => 
            item.properties && 
            (item.properties['レンタル開始日'] || item.properties['rental_start_date'])
          );
          
          if (!hasRentalItems) {
            errorMessage.textContent = 'カートにレンタル商品がありません。';
            errorMessage.style.display = 'block';
            loadingIndicator.style.display = 'none';
            checkoutButton.disabled = false;
            return;
          }
          
          // ドラフトオーダーAPIのエンドポイント
          const apiUrl = '{{ shop.secure_url }}/apps/ease-next-temp/create-draft-order-from-cart';
          
          // ドラフトオーダーの作成リクエスト
          const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              items: cart.items.map(item => ({
                variantId: item.variant_id.toString(),
                productId: item.product_id.toString(),
                title: item.title,
                variantTitle: item.variant_title,
                startDate: item.properties['レンタル開始日'] || item.properties['rental_start_date'],
                endDate: item.properties['レンタル終了日'] || item.properties['rental_end_date'],
                bookingType: item.properties['予約タイプ'] || item.properties['booking_type'] || 'CONFIRMED',
                rentalDays: parseInt(item.properties['レンタル日数'] || item.properties['rental_days'] || '0'),
                price: item.price,
                image: item.image,
                quantity: item.quantity
              }))
            })
          });
          
          const data = await response.json();
          
          if (data.success && data.draftOrder && data.draftOrder.invoiceUrl) {
            // カートをクリア
            await fetch('/cart/clear.js', { method: 'POST' });
            
            // ドラフトオーダーの請求書ページにリダイレクト
            window.location.href = data.draftOrder.invoiceUrl;
          } else {
            throw new Error(data.error || 'ドラフトオーダーの作成に失敗しました');
          }
        } catch (error) {
          console.error('ドラフトオーダー作成エラー:', error);
          errorMessage.textContent = error.message || 'チェックアウト処理中にエラーが発生しました';
          errorMessage.style.display = 'block';
          loadingIndicator.style.display = 'none';
          checkoutButton.disabled = false;
        }
      });
    }
  });
</script>
