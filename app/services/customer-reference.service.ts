import { Session } from "@shopify/shopify-api";
import { GraphQLClient, gql } from 'graphql-request';

/**
 * キャッシュの有効期限（ミリ秒）
 * デフォルト: 5分
 */
const CACHE_EXPIRY = 5 * 60 * 1000;

/**
 * 顧客情報の型定義
 */
export interface CustomerInfo {
  id: string;
  shopifyId: string;
  firstName: string;
  lastName: string;
  name: string;
  email: string;
  phone: string;
  address?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * キャッシュアイテムの型定義
 */
interface CacheItem<T> {
  data: T;
  timestamp: number;
}

/**
 * 顧客参照サービス
 * Shopify APIから顧客情報を取得し、キャッシュする
 *
 * このサービスは、顧客情報の取得と検索に関する機能を提供します。
 * 顧客IDから顧客情報を取得する場合は、キャッシュを活用して、API呼び出しを最小化します。
 * 顧客検索は、Shopify APIを使用して行います。
 *
 * 使用例:
 * ```typescript
 * const customerService = new CustomerReferenceService(session);
 * const customerInfo = await customerService.getCustomerById(customerId);
 * const searchResults = await customerService.searchCustomers("山田");
 * ```
 */
export class CustomerReferenceService {
  private session: Session;
  private client: any;
  private directClient: GraphQLClient;
  private cache: Map<string, CacheItem<CustomerInfo>> = new Map();
  private shop: string;

  /**
   * コンストラクタ
   * @param session Shopifyセッション
   */
  constructor(session: Session) {
    this.session = session;
    this.shop = session.shop;

    // GraphQLクライアントの初期化
    this.client = {
      query: async ({ data }: { data: any }) => {
        // 直接GraphQLClientを使用
        const client = new GraphQLClient(
          `https://${session.shop}/admin/api/2025-01/graphql.json`,
          {
            headers: {
              'X-Shopify-Access-Token': session.accessToken,
              'Content-Type': 'application/json',
            },
          }
        );
        return await client.request(data.query, data.variables);
      }
    };

    // 直接GraphQLClientを使用する方法も追加
    this.directClient = new GraphQLClient(
      `https://${this.shop}/admin/api/2024-01/graphql.json`,
      {
        headers: {
          'X-Shopify-Access-Token': session.accessToken || '',
          'Content-Type': 'application/json',
        },
      }
    );

    // キャッシュの初期化
    this.initializeCache();
  }

  /**
   * キャッシュの初期化
   * ローカルストレージからキャッシュを復元
   */
  private initializeCache(): void {
    // サーバーサイドレンダリング時は何もしない
    if (typeof window === 'undefined') {
      console.log('サーバーサイドレンダリング中のため、キャッシュ初期化をスキップします');
      return;
    }

    try {
      // クライアントサイドでのみ実行
      if (typeof localStorage !== 'undefined') {
        const cachedData = localStorage.getItem(`customer-cache-${this.shop}`);
        if (cachedData) {
          const parsedData = JSON.parse(cachedData);

          // キャッシュの有効期限をチェック
          const now = Date.now();
          Object.entries(parsedData).forEach(([key, value]: [string, any]) => {
            if (now - value.timestamp < CACHE_EXPIRY) {
              this.cache.set(key, value as CacheItem<CustomerInfo>);
            }
          });
        }
      }
    } catch (error) {
      console.error('キャッシュの初期化エラー:', error);
    }
  }

  /**
   * キャッシュの保存
   * メモリキャッシュをローカルストレージに保存
   */
  private saveCache(): void {
    // サーバーサイドレンダリング時は何もしない
    if (typeof window === 'undefined') {
      console.log('サーバーサイドレンダリング中のため、キャッシュ保存をスキップします');
      return;
    }

    try {
      // クライアントサイドでのみ実行
      if (typeof localStorage !== 'undefined') {
        const cacheObject: Record<string, CacheItem<CustomerInfo>> = {};
        this.cache.forEach((value, key) => {
          cacheObject[key] = value;
        });
        localStorage.setItem(`customer-cache-${this.shop}`, JSON.stringify(cacheObject));
      }
    } catch (error) {
      console.error('キャッシュの保存エラー:', error);
    }
  }

  /**
   * キャッシュのクリア
   */
  public clearCache(): void {
    this.cache.clear();
    // サーバーサイドレンダリング時は何もしない
    if (typeof window === 'undefined') {
      console.log('サーバーサイドレンダリング中のため、キャッシュクリアをスキップします');
      return;
    }

    // クライアントサイドでのみ実行
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem(`customer-cache-${this.shop}`);
    }
  }

  /**
   * 顧客IDから顧客情報を取得
   * @param customerId Shopify顧客ID
   * @param forceRefresh キャッシュを無視して再取得するかどうか
   * @returns 顧客情報
   */
  async getCustomerById(customerId: string, forceRefresh = false): Promise<CustomerInfo | null> {
    // IDが空の場合はnullを返す
    if (!customerId) {
      console.log('顧客ID取得: IDが空です');
      return null;
    }

    console.log(`顧客ID取得: 元のID=${customerId}`);

    // GID形式でない場合はGID形式に変換
    const gid = customerId.startsWith('gid://')
      ? customerId
      : `gid://shopify/Customer/${customerId}`;

    console.log(`顧客ID取得: 変換後のGID=${gid}`);

    // キャッシュをチェック
    if (!forceRefresh) {
      const cachedCustomer = this.cache.get(gid);
      if (cachedCustomer && (Date.now() - cachedCustomer.timestamp < CACHE_EXPIRY)) {
        console.log(`顧客ID取得: キャッシュから取得 ID=${gid}`);
        return cachedCustomer.data;
      }
    }

    try {
      // サーバーサイドの場合は直接APIを呼び出す
      if (typeof window === 'undefined') {
        // GraphQLクエリ
        const GET_CUSTOMER_QUERY = gql`
          query getCustomer($id: ID!) {
            customer(id: $id) {
              id
              firstName
              lastName
              email
              phone
              createdAt
              updatedAt
              defaultAddress {
                address1
                address2
                city
                province
                zip
                country
              }
            }
          }
        `;

        console.log(`顧客ID取得: 直接APIリクエスト実行 ID=${gid}`);

        // 直接GraphQLClientを使用してAPIリクエスト
        const response = await this.directClient.request(GET_CUSTOMER_QUERY, {
          id: gid
        });

        // レスポンスから顧客情報を取得
        if (response && response.customer) {
          const customer = response.customer;
          console.log(`顧客ID取得: 顧客情報取得成功`, customer);

          const defaultAddress = customer.defaultAddress;

          // 住所を整形
          let address = '';
          if (defaultAddress) {
            address = [
              defaultAddress.address1,
              defaultAddress.address2,
              defaultAddress.city,
              defaultAddress.province,
              defaultAddress.zip,
              defaultAddress.country
            ].filter(Boolean).join(' ');
          }

          // 顧客情報を整形
          const customerInfo: CustomerInfo = {
            id: gid,
            shopifyId: gid.replace('gid://shopify/Customer/', ''),
            firstName: customer.firstName || '',
            lastName: customer.lastName || '',
            name: `${customer.lastName || ''} ${customer.firstName || ''}`.trim(), // 日本語表記に合わせて姓・名の順に変更
            email: customer.email || '',
            phone: customer.phone || '',
            address,
            createdAt: customer.createdAt,
            updatedAt: customer.updatedAt
          };

          console.log(`顧客ID取得: 顧客情報整形完了`, customerInfo);

          // キャッシュに保存
          this.cache.set(gid, {
            data: customerInfo,
            timestamp: Date.now()
          });
          this.saveCache();

          return customerInfo;
        }
      } else {
        // クライアントサイドの場合はサーバーサイドAPIを使用

        // 数値IDを使用
        const shopifyId = customerId.replace('gid://shopify/Customer/', '');

        // APIエンドポイントを呼び出し
        const response = await fetch(`/api/customer?id=${shopifyId}`);
        const data = await response.json();

        if (response.ok && data.customer) {
          // キャッシュに保存
          this.cache.set(gid, {
            data: data.customer,
            timestamp: Date.now()
          });
          this.saveCache();

          return data.customer;
        }
      }

      console.log(`顧客ID取得: 顧客情報なし ID=${gid}`);

      // IDでの検索に失敗した場合、顧客検索APIを使用して検索
      try {
        // 数値IDの場合は、そのIDで検索
        const searchResults = await this.searchCustomers(customerId, 1);

        if (searchResults.length > 0) {
          return searchResults[0];
        }
      } catch (searchError) {
        // エラーを無視
      }

      return null;
    } catch (error) {
      console.error('顧客情報取得エラー:', error);

      // 数値IDの場合、別の形式でも試してみる
      if (!customerId.startsWith('gid://')) {
        try {
          console.log(`顧客ID取得: 別の形式でリトライ ID=${customerId}`);

          // 顧客検索APIを使用して検索
          const searchResults = await this.searchCustomers(customerId, 1);

          if (searchResults.length > 0) {
            console.log(`顧客ID取得: 顧客検索APIで顧客が見つかりました ID=${customerId}`);
            return searchResults[0];
          }
        } catch (retryError) {
          console.error('顧客ID取得: 別の形式でのリトライ失敗', retryError);
        }
      }

      // エラーをスローするのではなく、nullを返す
      return null;
    }
  }

  /**
   * 顧客を検索
   * @param query 検索クエリ（名前、メールアドレス、電話番号など）
   * @param limit 取得する最大件数
   * @returns 顧客情報の配列
   */
  async searchCustomers(query: string, limit = 10): Promise<CustomerInfo[]> {
    try {
      console.log(`顧客検索: 元のクエリ="${query}", 上限=${limit}`);

      // 検索クエリの前処理
      let searchQuery = query.trim();

      // メールアドレスや電話番号の検索の場合は通常の検索を行う
      if (searchQuery.includes('@') || searchQuery.match(/^\+?[0-9\-\(\)]+$/)) {
        console.log(`顧客検索: メールアドレスまたは電話番号と判断`);
        return await this.performCustomerSearch(searchQuery, limit);
      }

      // 検索クエリがスペースを含む名前の場合（例: 「佐藤 花子」）
      const nameParts = searchQuery.match(/[\p{L}\p{N}]+/gu) || [];
      if (nameParts.length === 2 && !searchQuery.includes(':')) {
        // 通常の検索（元のクエリ）
        const results1 = await this.performCustomerSearch(searchQuery, limit);

        // 姓名の順序を入れ替えた検索
        const reversedQuery = `${nameParts[1]} ${nameParts[0]}`;
        console.log(`顧客検索: 姓名の順序を入れ替えて検索 "${reversedQuery}"`);
        const results2 = await this.performCustomerSearch(reversedQuery, limit);

        // 結果をマージ（重複を除去）
        const mergedResults = [...results1];
        for (const customer of results2) {
          if (!mergedResults.some(c => c.id === customer.id)) {
            mergedResults.push(customer);
          }
        }

        console.log(`顧客検索: マージ後の結果 ${mergedResults.length}件`);
        return mergedResults;
      }

      // 日本語名でスペースがない場合（例: 「佐藤花子」）
      // 日本語の名前は通常2-4文字なので、2-4文字の名前と仮定して分割パターンを試す
      if (searchQuery.match(/^[\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Han}]+$/u) &&
          searchQuery.length >= 2 && searchQuery.length <= 8 &&
          !searchQuery.includes(':')) {

        console.log(`顧客検索: スペースなしの日本語名と判断 "${searchQuery}"`);

        // 通常の検索（元のクエリ）
        const results1 = await this.performCustomerSearch(searchQuery, limit);
        let allResults = [...results1];

        // 可能な分割パターンを試す（2文字姓+残り、3文字姓+残り、など）
        for (let i = 1; i < searchQuery.length; i++) {
          const lastName = searchQuery.substring(0, i);
          const firstName = searchQuery.substring(i);

          // 姓名の間にスペースを入れたパターン
          const nameWithSpace = `${lastName} ${firstName}`;
          console.log(`顧客検索: 分割パターン試行 "${nameWithSpace}"`);
          const results2 = await this.performCustomerSearch(nameWithSpace, limit);

          // 姓名を逆にしたパターン
          const reversedName = `${firstName} ${lastName}`;
          console.log(`顧客検索: 分割パターン試行（逆順） "${reversedName}"`);
          const results3 = await this.performCustomerSearch(reversedName, limit);

          // 結果をマージ
          for (const customer of [...results2, ...results3]) {
            if (!allResults.some(c => c.id === customer.id)) {
              allResults.push(customer);
            }
          }

          // 十分な結果が得られたら終了
          if (allResults.length >= limit) {
            break;
          }
        }

        console.log(`顧客検索: 日本語名分割検索後の結果 ${allResults.length}件`);
        return allResults.slice(0, limit);
      }

      // 通常の検索
      return await this.performCustomerSearch(searchQuery, limit);
    } catch (error) {
      console.error('顧客検索エラー:', error);
      // エラーをスローするのではなく、空の配列を返す
      return [];
    }
  }

  /**
   * 顧客検索を実行する内部メソッド
   * @param query 検索クエリ
   * @param limit 取得する最大件数
   * @returns 顧客情報の配列
   */
  private async performCustomerSearch(query: string, limit: number): Promise<CustomerInfo[]> {
    try {
      console.log(`顧客検索実行: クエリ="${query}", 上限=${limit}`);

      // サーバーサイドの場合は直接APIを呼び出す
      if (typeof window === 'undefined') {
        // GraphQLクエリ
        const SEARCH_CUSTOMERS_QUERY = gql`
          query searchCustomers($query: String!, $first: Int!) {
            customers(first: $first, query: $query) {
              edges {
                node {
                  id
                  firstName
                  lastName
                  email
                  phone
                  createdAt
                  updatedAt
                  defaultAddress {
                    address1
                    address2
                    city
                    province
                    zip
                    country
                  }
                }
              }
            }
          }
        `;

        // 直接GraphQLClientを使用してAPIリクエスト
        const response = await this.directClient.request(SEARCH_CUSTOMERS_QUERY, {
          query,
          first: limit
        });

        // レスポンスから顧客情報を取得
        if (response && response.customers && response.customers.edges) {
          const customers = response.customers.edges.map((edge: any) => {
            const customer = edge.node;
            const defaultAddress = customer.defaultAddress;

            // 住所を整形
            let address = '';
            if (defaultAddress) {
              address = [
                defaultAddress.address1,
                defaultAddress.address2,
                defaultAddress.city,
                defaultAddress.province,
                defaultAddress.zip,
                defaultAddress.country
              ].filter(Boolean).join(' ');
            }

            // 顧客情報を整形
            const customerInfo: CustomerInfo = {
              id: customer.id,
              shopifyId: customer.id.replace('gid://shopify/Customer/', ''),
              firstName: customer.firstName || '',
              lastName: customer.lastName || '',
              name: `${customer.lastName || ''} ${customer.firstName || ''}`.trim(), // 日本語表記に合わせて姓・名の順に変更
              email: customer.email || '',
              phone: customer.phone || '',
              address,
              createdAt: customer.createdAt,
              updatedAt: customer.updatedAt
            };

            // キャッシュに保存
            this.cache.set(customer.id, {
              data: customerInfo,
              timestamp: Date.now()
            });

            return customerInfo;
          });

          // キャッシュを保存
          this.saveCache();

          console.log(`顧客検索実行: ${customers.length}件の顧客が見つかりました`);
          return customers;
        }
      } else {
        // クライアントサイドの場合はサーバーサイドAPIを使用
        console.log(`顧客検索実行: サーバーサイドAPIを使用 クエリ="${query}"`);

        // APIエンドポイントを呼び出し
        const response = await fetch('/api/customer', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query,
            limit
          })
        });

        const data = await response.json();

        if (response.ok && data.customers) {
          console.log(`顧客検索実行: サーバーサイドAPIから${data.customers.length}件の顧客が見つかりました`);

          // キャッシュに保存
          data.customers.forEach((customer: CustomerInfo) => {
            this.cache.set(customer.id, {
              data: customer,
              timestamp: Date.now()
            });
          });

          this.saveCache();

          return data.customers;
        }
      }

      console.log(`顧客検索実行: 顧客が見つかりませんでした`);
      return [];
    } catch (error) {
      console.error('顧客検索実行エラー:', error);
      return [];
    }
  }
}
