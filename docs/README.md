# docs フォルダ構成と文書管理ガイド

## 概要

このドキュメントは、プロジェクトのドキュメント管理の方針と構成を説明します。効率的にドキュメントを整理しつつ、プロジェクトの履歴も保持するための構造を定義しています。

## フォルダ構造

```
docs/
├── README.md（本ファイル - 目次と現行ドキュメント一覧）
├── active/（現行の有効なドキュメント）
│   ├── specifications/（仕様書）
│   ├── guides/（実装ガイド）
│   ├── plans/（実装計画）
│   └── tasks/（進行中のタスク）
└── archive/（過去のドキュメント）
    ├── date-based/（日付別アーカイブ）
    │   ├── 2024-05/
    │   └── 2024-06/
    └── completed/（完了したタスク）
```

## ドキュメント命名規則

1. **現行ドキュメント**: 簡潔で内容を表す名前
   - 例: `商品バリエーション方式実装計画_更新版.md`

2. **アーカイブドキュメント**: プレフィックスまたは日付を追加
   - 例: `ARCHIVED_2024-05-21_商品バリエーション方式実装計画.md`
   - 例: `COMPLETED_タスク進捗状況_旧バリエーション方式.md`

## 現行ドキュメント一覧

### 仕様書

| ドキュメント名 | 説明 | 最終更新日 |
|--------------|------|----------|
| [メタフィールド構造_最新版.md](./active/specifications/メタフィールド構造_最新版.md) | 最新のメタフィールド構造定義 | 2024-05-21 |
| [在庫管理ロジック_更新版.md](./active/specifications/在庫管理ロジック_更新版.md) | 在庫管理の詳細ロジック | 2024-05-21 |
| [料金計算ロジック_統合版.md](./active/specifications/料金計算ロジック_統合版.md) | 統合された料金計算ロジック | 2024-05-21 |
| [Shopify_Prisma同期設計.md](./active/specifications/Shopify_Prisma同期設計.md) | ShopifyとPrismaの同期設計 | 2024-05-21 |
| [メタフィールド設計.md](./active/specifications/メタフィールド設計.md) | メタフィールドの設計概要 | 2024-05-21 |
| [メンテナンス管理ロジック.md](./active/specifications/メンテナンス管理ロジック.md) | メンテナンス管理の詳細ロジック | 2024-05-21 |
| [SYS_001_メタフィールド設計改善.md](./active/specifications/SYS_001_メタフィールド設計改善.md) | メタフィールド設計の改善提案 | 2024-05-21 |

### 実装計画

| ドキュメント名 | 説明 | 最終更新日 |
|--------------|------|----------|
| [商品バリエーション方式実装計画_更新版.md](./active/plans/商品バリエーション方式実装計画_更新版.md) | 最新の実装計画 | 2024-05-21 |
| [商品バリエーション方式メタフィールド更新計画.md](./active/plans/商品バリエーション方式メタフィールド更新計画.md) | メタフィールド更新計画 | 2024-05-21 |

### 実装ガイド

| ドキュメント名 | 説明 | 最終更新日 |
|--------------|------|----------|
| [商品バリエーション方式と在庫ユニット管理の実装ガイド.md](./active/guides/商品バリエーション方式と在庫ユニット管理の実装ガイド.md) | 詳細な実装手順 | 2024-05-21 |
| [商品バリエーション方式実装手順.md](./active/guides/商品バリエーション方式実装手順.md) | 商品バリエーション実装手順 | 2024-05-21 |
| [商品バリエーション方式テスト手順.md](./active/guides/商品バリエーション方式テスト手順.md) | テスト手順の詳細 | 2024-05-21 |
| [商品バリエーション方式自動設定.md](./active/guides/商品バリエーション方式自動設定.md) | 自動設定の手順 | 2024-05-21 |
| [IMP_001_商品バリエーション方式Webhook実装テスト.md](./active/guides/IMP_001_商品バリエーション方式Webhook実装テスト.md) | Webhook実装テスト | 2024-05-21 |
| [IMP_002_手動Webhook登録手順.md](./active/guides/IMP_002_手動Webhook登録手順.md) | 手動Webhook登録の手順 | 2024-05-21 |
| [商品登録ガイド.md](./active/guides/商品登録ガイド.md) | 商品登録ガイド | 2024-05-21 |
| [商品登録テスト手順書.md](./active/guides/商品登録テスト手順書.md) | 商品登録のテスト手順 | 2024-05-21 |
| [IMP_001_商品登録ガイド.md](./active/guides/IMP_001_商品登録ガイド.md) | 実装用商品登録ガイド | 2024-05-21 |
| [メタフィールド管理ツール.md](./active/guides/メタフィールド管理ツール.md) | メタフィールド管理ツールの使用方法 | 2024-05-21 |
| [SYS_002_Shopify同期問題解決.md](./active/guides/SYS_002_Shopify同期問題解決.md) | Shopify同期問題の解決方法 | 2024-05-21 |
| [SYS_003_Shopify同期テスト手順.md](./active/guides/SYS_003_Shopify同期テスト手順.md) | Shopify同期のテスト手順 | 2024-05-21 |

### 進行中のタスク

| ドキュメント名 | 説明 | 最終更新日 |
|--------------|------|----------|
| [タスク進捗状況_商品バリエーション方式.md](./active/tasks/タスク進捗状況_商品バリエーション方式.md) | 現在のタスク進捗 | 2024-05-21 |

## アーカイブドキュメント

### 日付別アーカイブ（2024-05）

| ドキュメント名 | 説明 | アーカイブ日 |
|--------------|------|------------|
| [商品バリエーション方式実装計画.md](./archive/date-based/2024-05/商品バリエーション方式実装計画.md) | 初期実装計画 | 2024-05-21 |
| [商品バリエーション方式メタフィールド変更.md](./archive/date-based/2024-05/商品バリエーション方式メタフィールド変更.md) | 旧メタフィールド変更計画 | 2024-05-21 |
| [在庫管理ロジック_現状版.md](./archive/date-based/2024-05/在庫管理ロジック_現状版.md) | 旧在庫管理ロジック | 2024-05-21 |
| [在庫管理方法の検討.md](./archive/date-based/2024-05/在庫管理方法の検討.md) | 初期検討資料 | 2024-05-21 |
| [メタフィールド構造.md](./archive/date-based/2024-05/メタフィールド構造.md) | 旧メタフィールド構造 | 2024-05-21 |
| [料金計算ロジック_現状版.md](./archive/date-based/2024-05/料金計算ロジック_現状版.md) | 旧料金計算ロジック | 2024-05-21 |

### 完了したタスク

| ドキュメント名 | 説明 | 完了日 |
|--------------|------|-------|
| [タスク進捗状況.md](./archive/completed/タスク進捗状況.md) | 過去のタスク進捗 | 2024-05-21 |
| [タスク進捗状況_現在.md](./archive/completed/タスク進捗状況_現在.md) | 完了した現在のタスク | 2024-05-21 |

## ドキュメント管理のワークフロー

### 新規ドキュメントの作成

1. 適切なカテゴリフォルダに新規ドキュメントを作成
2. README.mdの一覧を更新
3. コミット時に詳細なコミットメッセージを記載

### ドキュメントの更新

1. 既存ドキュメントを更新
2. README.mdの「最終更新日」を更新
3. 大幅な変更の場合は古いバージョンをアーカイブ

### ドキュメントのアーカイブ

1. 完了または置き換えられたドキュメントを適切なアーカイブフォルダへ移動
2. プレフィックスを追加（ARCHIVED_、COMPLETED_など）
3. README.mdからアーカイブしたドキュメントを削除または「アーカイブドキュメント」セクションに移動

## Git リポジトリでの記録

すべてのドキュメント変更は Git によって記録されるため、ファイルを削除または移動しても履歴は保持されます。重要なマイルストーンでは Git タグを付けることで、特定の時点のドキュメント状態を保存できます。

例:
```bash
git tag -a v1.0-docs-milestone -m "商品バリエーション方式の実装計画完了"
```

## ドキュメント管理のベストプラクティス

1. **定期的な棚卸し**: 四半期ごとにドキュメントを確認し、整理
2. **一貫した命名規則**: すべてのドキュメントに統一された命名規則を適用
3. **クロスリファレンス**: 関連ドキュメント間のリンクを含める
4. **バージョン情報**: 各ドキュメントの冒頭にバージョン履歴を記載
5. **責任者の明記**: 各ドキュメントの管理責任者を明記

## 定期的なドキュメント整理の手順

1. 四半期ごとに全ドキュメントの棚卸しを実施
2. 完了したタスクや置き換えられた計画書をアーカイブ
3. README.mdを更新して最新の状態を反映
4. 必要に応じて古いアーカイブを圧縮または外部ストレージに移動