# テストデータ検証チェックリスト

## 概要

このチェックリストは、テストデータの作成後に、各機能が正しく動作するかを検証するためのものです。各項目を順番に確認し、問題がある場合は修正してください。

## 1. 商品関連の検証

### 1.1 商品一覧表示
- [ ] 商品一覧画面（`/app/products`）にアクセスできる
- [ ] 作成したすべてのテスト商品が表示される
- [ ] 商品のタイトル、SKU、価格、ステータスが正しく表示される
- [ ] 商品の在庫場所（NY/PR）が正しく表示される

### 1.2 商品詳細表示
- [ ] 商品詳細画面（`/app/products/:id`）にアクセスできる
- [ ] 商品の基本情報が正しく表示される
- [ ] 商品のメタフィールド情報が正しく表示される
- [ ] 商品の在庫状態が正しく表示される
- [ ] 商品の予約状況が正しく表示される

### 1.3 商品検索・フィルタリング
- [ ] 商品名での検索が機能する
- [ ] SKUでの検索が機能する
- [ ] ステータスでのフィルタリングが機能する
- [ ] 在庫場所でのフィルタリングが機能する
- [ ] カテゴリでのフィルタリングが機能する

### 1.4 商品ステータス変更
- [ ] 商品のステータスを変更できる（available → maintenance など）
- [ ] ステータス変更がデータベースに反映される
- [ ] ステータス変更がShopifyに反映される

### 1.5 商品バリエーション表示
- [ ] バリエーションを持つ商品の場合、すべてのバリエーションが表示される
- [ ] バリエーションごとの価格が正しく表示される
- [ ] バリエーションごとのSKUが正しく表示される

## 2. 予約関連の検証

### 2.1 予約一覧表示
- [ ] 予約一覧画面（`/app/bookings`）にアクセスできる
- [ ] 作成したすべてのテスト予約が表示される
- [ ] 予約の日付、顧客名、商品名、ステータスが正しく表示される
- [ ] 予約のステータスに応じた色分けが正しく表示される

### 2.2 予約詳細表示
- [ ] 予約詳細画面（`/app/bookings/:id`）にアクセスできる
- [ ] 予約の基本情報が正しく表示される
- [ ] 関連する商品情報が正しく表示される
- [ ] 顧客情報が正しく表示される
- [ ] 料金情報が正しく表示される
- [ ] 注文情報（Shopify連携）が正しく表示される

### 2.3 予約検索・フィルタリング
- [ ] 予約IDでの検索が機能する
- [ ] 顧客名での検索が機能する（日本語名対応）
- [ ] 商品名での検索が機能する
- [ ] 日付範囲での検索が機能する
- [ ] ステータスでのフィルタリングが機能する

### 2.4 予約ステータス変更
- [ ] 予約のステータスを変更できる（PROVISIONAL → CONFIRMED など）
- [ ] ステータス変更がデータベースに反映される
- [ ] ステータス変更に応じて在庫状態が更新される

### 2.5 予約カレンダー表示
- [ ] 予約カレンダー画面（`/app/bookings/calendar`）にアクセスできる
- [ ] すべての予約がカレンダーに正しく表示される
- [ ] 予約のステータスに応じた色分けが正しく表示される
- [ ] 日付範囲の変更が機能する
- [ ] 商品フィルタリングが機能する

### 2.6 予約の日付範囲検索
- [ ] 開始日での検索が機能する
- [ ] 終了日での検索が機能する
- [ ] 日付範囲での検索が機能する
- [ ] 日付形式が正しく処理される

## 3. 顧客関連の検証

### 3.1 顧客一覧表示
- [ ] 顧客一覧画面（`/app/customers`）にアクセスできる
- [ ] 作成したすべてのテスト顧客が表示される
- [ ] 顧客の名前、メールアドレス、電話番号が正しく表示される

### 3.2 顧客詳細表示
- [ ] 顧客詳細画面（`/app/customers/:id`）にアクセスできる
- [ ] 顧客の基本情報が正しく表示される
- [ ] 顧客の予約履歴が正しく表示される
- [ ] 顧客の注文履歴（Shopify連携）が正しく表示される

### 3.3 顧客検索
- [ ] 顧客名での検索が機能する（日本語名対応）
- [ ] 姓名の間にスペースがない日本語名でも検索できる（例：「佐藤花子」）
- [ ] メールアドレスでの検索が機能する
- [ ] 電話番号での検索が機能する

## 4. Shopify連携の検証

### 4.1 商品同期
- [ ] Shopifyで商品を更新すると、Prismaデータベースに反映される
- [ ] Prismaデータベースで商品を更新すると、Shopifyに反映される
- [ ] メタフィールドの情報が正しく同期される

### 4.2 注文連携
- [ ] Shopifyで注文を作成すると、予約情報が作成される
- [ ] 予約情報と注文情報が正しく関連付けられる
- [ ] 注文ステータスの変更が予約ステータスに反映される

### 4.3 顧客連携
- [ ] Shopifyの顧客情報がアプリで参照できる
- [ ] 顧客IDによる参照が正しく機能する

## 5. 在庫管理の検証

### 5.1 在庫状態の更新
- [ ] 予約が作成されると、在庫状態が更新される
- [ ] 予約がキャンセルされると、在庫状態が更新される
- [ ] 予約の日付が変更されると、在庫状態が更新される

### 5.2 在庫カレンダーの表示
- [ ] 在庫カレンダーが正しく表示される
- [ ] 予約済みの日付が正しく表示される
- [ ] 利用可能な日付が正しく表示される
- [ ] メンテナンス中の日付が正しく表示される

### 5.3 在庫の重複チェック
- [ ] 同じ商品の同じ日付に複数の予約ができないことを確認
- [ ] 重複チェックのエラーメッセージが正しく表示される

## 6. 管理画面全般の検証

### 6.1 ナビゲーション
- [ ] すべてのナビゲーションリンクが機能する
- [ ] アクティブなページが正しく強調表示される
- [ ] モバイル表示でもナビゲーションが正しく機能する

### 6.2 UI/UX
- [ ] すべての画面が正しくレンダリングされる
- [ ] フォームの入力と送信が正しく機能する
- [ ] エラーメッセージが適切に表示される
- [ ] 成功メッセージが適切に表示される
- [ ] ローディング状態が適切に表示される

### 6.3 パフォーマンス
- [ ] データの読み込みが適切な速度で行われる
- [ ] 大量のデータがある場合でもパフォーマンスが維持される
- [ ] ページネーションが正しく機能する

## 7. エラーケースの検証

### 7.1 入力バリデーション
- [ ] 必須フィールドが空の場合、適切なエラーメッセージが表示される
- [ ] 無効な日付形式の場合、適切なエラーメッセージが表示される
- [ ] 無効なメールアドレスの場合、適切なエラーメッセージが表示される

### 7.2 エラー処理
- [ ] APIエラーが発生した場合、適切なエラーメッセージが表示される
- [ ] データベースエラーが発生した場合、適切なエラーメッセージが表示される
- [ ] ネットワークエラーが発生した場合、適切なエラーメッセージが表示される

## 8. 追加検証項目

### 8.1 日本語対応
- [ ] 日本語の商品名が正しく表示される
- [ ] 日本語の顧客名が正しく表示される
- [ ] 日本語の検索が正しく機能する
- [ ] 日本語のエラーメッセージが正しく表示される

### 8.2 日付処理
- [ ] 日本のタイムゾーンで日付が正しく表示される
- [ ] 日付範囲の計算が正しく行われる
- [ ] 休日（日曜日、祝日、年末年始）の処理が正しく行われる
