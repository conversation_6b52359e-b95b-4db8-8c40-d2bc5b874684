import { PrismaClient } from '@prisma/client';
import { 
  buildImprovedSkuSearch, 
  extractBaseSku, 
  generateAllVariantSkus,
  demonstrateSkuSearch 
} from '../app/utils/sku-search-improved';

const prisma = new PrismaClient();

/**
 * 改善されたSKU検索のテスト
 */
async function testImprovedSkuSearch() {
  console.log('=== 改善されたSKU検索機能のテスト ===\n');

  // 1. ユーティリティ関数のテスト
  console.log('1. 基本SKU抽出テスト:');
  const testSkus = [
    '10101031-001-1D',
    '10101031-001-PROV',
    '201-07-107-3D',
    '10101031-001',
    '201-07-107'
  ];

  testSkus.forEach(sku => {
    const baseSku = extractBaseSku(sku);
    console.log(`  ${sku} → ${baseSku}`);
  });

  // 2. バリアント生成テスト
  console.log('\n2. バリアントSKU生成テスト:');
  const baseSku = '10101031-001';
  const variants = generateAllVariantSkus(baseSku);
  console.log(`  基本SKU: ${baseSku}`);
  console.log(`  バリアント: ${variants.join(', ')}`);

  // 3. 検索条件生成テスト
  console.log('\n3. 検索条件生成テスト:');
  const testQueries = [
    '10101031-001',
    '10101031-001-3D',
    '10101031',
    '201-07',
    'ソファ'
  ];

  testQueries.forEach(query => {
    const result = buildImprovedSkuSearch(query);
    console.log(`\n  検索: "${query}"`);
    console.log(`  基本SKU: ${result.baseSku || 'なし'}`);
    console.log(`  バリアント数: ${result.variants.length}`);
    console.log(`  検索条件数: ${result.searchConditions.length}`);
    
    if (result.searchConditions.length > 0) {
      result.searchConditions.forEach((condition, index) => {
        console.log(`    条件${index + 1}: ${JSON.stringify(condition)}`);
      });
    }
  });

  // 4. 実際のデータベース検索テスト
  console.log('\n\n4. データベース検索テスト:');
  
  const searchTests = [
    { query: '10101031-001', description: '基本SKUで検索' },
    { query: '10101031-001-1D', description: 'バリアント付きSKUで検索' },
    { query: '10101031', description: '商品コードで検索' }
  ];

  for (const test of searchTests) {
    console.log(`\n  テスト: ${test.description}`);
    console.log(`  検索クエリ: "${test.query}"`);
    
    const { searchConditions } = buildImprovedSkuSearch(test.query);
    
    try {
      const products = await prisma.product.findMany({
        where: {
          OR: searchConditions
        },
        select: {
          id: true,
          title: true,
          sku: true
        },
        take: 10
      });

      console.log(`  結果: ${products.length}件`);
      products.forEach(product => {
        console.log(`    - ${product.title} (${product.sku})`);
      });
    } catch (error) {
      console.log(`  エラー: ${error}`);
    }
  }

  // 5. パフォーマンステスト
  console.log('\n\n5. パフォーマンステスト:');
  
  const performanceTest = async (query: string) => {
    const startTime = Date.now();
    
    // 従来の検索方法
    const oldMethod = await prisma.product.findMany({
      where: {
        OR: [
          { sku: { contains: query, mode: 'insensitive' } }
        ]
      },
      select: { id: true }
    });
    
    const oldTime = Date.now() - startTime;
    
    // 改善された検索方法
    const startTime2 = Date.now();
    const { searchConditions } = buildImprovedSkuSearch(query);
    
    const newMethod = await prisma.product.findMany({
      where: {
        OR: searchConditions
      },
      select: { id: true }
    });
    
    const newTime = Date.now() - startTime2;
    
    console.log(`  クエリ: "${query}"`);
    console.log(`    従来の方法: ${oldMethod.length}件 (${oldTime}ms)`);
    console.log(`    改善版: ${newMethod.length}件 (${newTime}ms)`);
    console.log(`    改善率: ${((oldTime - newTime) / oldTime * 100).toFixed(1)}%`);
  };

  await performanceTest('10101031-001');
}

/**
 * 使用例のデモンストレーション
 */
async function runDemonstration() {
  console.log('\n\n=== 使用例のデモンストレーション ===\n');
  
  // ユーティリティ関数のデモ
  demonstrateSkuSearch();
  
  // 実際の使用例
  console.log('\n実際の使用例:');
  console.log('```typescript');
  console.log('// 商品検索APIでの使用');
  console.log('const { searchConditions } = buildImprovedSkuSearch(searchQuery);');
  console.log('const products = await prisma.product.findMany({');
  console.log('  where: {');
  console.log('    shop,');
  console.log('    OR: [');
  console.log('      { title: { contains: searchQuery } },');
  console.log('      ...searchConditions');
  console.log('    ]');
  console.log('  }');
  console.log('});');
  console.log('```');
}

// メイン実行
async function main() {
  try {
    await testImprovedSkuSearch();
    await runDemonstration();
  } catch (error) {
    console.error('テストエラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 実行
main();