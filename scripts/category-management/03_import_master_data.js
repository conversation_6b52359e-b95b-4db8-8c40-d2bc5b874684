#!/usr/bin/env node

/**
 * 統合カテゴリマスタをデータベースにインポートするスクリプト
 * 
 * 使用方法:
 * node scripts/category-management/03_import_master_data.js [shop]
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { PrismaClient } from '@prisma/client';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 入力ファイルのパス
const UNIFIED_CATEGORY_CSV = path.join(__dirname, '../../master-data-csv/generated/unified_category_master.csv');
const UNIFIED_SUBCATEGORY_CSV = path.join(__dirname, '../../master-data-csv/generated/unified_subcategory_master.csv');

const prisma = new PrismaClient();

/**
 * CSVファイルを読み込んでパースする
 */
function parseCSV(filePath) {
  if (!fs.existsSync(filePath)) {
    throw new Error(`ファイルが見つかりません: ${filePath}`);
  }
  
  const content = fs.readFileSync(filePath, 'utf-8');
  const lines = content.split('\n').filter(line => line.trim());
  const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
  
  return lines.slice(1).map(line => {
    const values = parseCSVLine(line);
    const obj = {};
    headers.forEach((header, index) => {
      obj[header] = values[index] || '';
    });
    return obj;
  });
}

/**
 * CSV行をパースする（クォート対応）
 */
function parseCSVLine(line) {
  const result = [];
  let current = '';
  let inQuotes = false;
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i];
    
    if (char === '"') {
      inQuotes = !inQuotes;
    } else if (char === ',' && !inQuotes) {
      result.push(current.trim());
      current = '';
    } else {
      current += char;
    }
  }
  
  result.push(current.trim());
  return result;
}

/**
 * カテゴリマスタをインポート
 */
async function importCategoryMaster(shop, categories) {
  console.log(`=== カテゴリマスタインポート開始 (${categories.length}件) ===`);
  
  let created = 0;
  let updated = 0;
  let errors = 0;
  
  for (const category of categories) {
    try {
      const data = {
        shop,
        code: category.code,
        name: category.name,
        parentCode: category.parentCode || null,
        level: parseInt(category.level) || 1,
        displayOrder: parseInt(category.displayOrder) || 0,
        isActive: category.isActive === 'true',
        description: category.description || null
      };
      
      // 既存レコードをチェック
      const existing = await prisma.categoryMaster.findUnique({
        where: { shop_code: { shop, code: category.code } }
      });
      
      if (existing) {
        // 更新
        await prisma.categoryMaster.update({
          where: { shop_code: { shop, code: category.code } },
          data
        });
        updated++;
      } else {
        // 新規作成
        await prisma.categoryMaster.create({ data });
        created++;
      }
      
      if ((created + updated) % 10 === 0) {
        console.log(`処理中: ${created + updated}/${categories.length}`);
      }
      
    } catch (error) {
      console.error(`カテゴリ ${category.code} のインポートエラー:`, error.message);
      errors++;
    }
  }
  
  console.log(`カテゴリマスタインポート完了: 作成=${created}, 更新=${updated}, エラー=${errors}`);
  return { created, updated, errors };
}

/**
 * サブカテゴリマスタをインポート
 */
async function importSubCategoryMaster(shop, subCategories) {
  console.log(`=== サブカテゴリマスタインポート開始 (${subCategories.length}件) ===`);
  
  let created = 0;
  let updated = 0;
  let errors = 0;
  
  for (const subCategory of subCategories) {
    try {
      const data = {
        shop,
        code: subCategory.code,
        name: subCategory.name,
        parentCategoryCode: subCategory.parentCategoryCode,
        isActive: subCategory.isActive === 'true',
        displayOrder: parseInt(subCategory.displayOrder) || 0,
        description: subCategory.description || null
      };
      
      // 親カテゴリの存在確認
      const parentCategory = await prisma.categoryMaster.findUnique({
        where: { shop_code: { shop, code: subCategory.parentCategoryCode } }
      });
      
      if (!parentCategory) {
        console.warn(`親カテゴリが見つかりません: ${subCategory.parentCategoryCode}`);
        errors++;
        continue;
      }
      
      // 既存レコードをチェック
      const existing = await prisma.subCategoryMaster.findUnique({
        where: {
          shop_code_parentCategoryCode: {
            shop,
            code: subCategory.code,
            parentCategoryCode: subCategory.parentCategoryCode
          }
        }
      });
      
      if (existing) {
        // 更新
        await prisma.subCategoryMaster.update({
          where: {
            shop_code_parentCategoryCode: {
              shop,
              code: subCategory.code,
              parentCategoryCode: subCategory.parentCategoryCode
            }
          },
          data
        });
        updated++;
      } else {
        // 新規作成
        await prisma.subCategoryMaster.create({ data });
        created++;
      }
      
      if ((created + updated) % 50 === 0) {
        console.log(`処理中: ${created + updated}/${subCategories.length}`);
      }
      
    } catch (error) {
      console.error(`サブカテゴリ ${subCategory.parentCategoryCode}-${subCategory.code} のインポートエラー:`, error.message);
      errors++;
    }
  }
  
  console.log(`サブカテゴリマスタインポート完了: 作成=${created}, 更新=${updated}, エラー=${errors}`);
  return { created, updated, errors };
}

/**
 * インポート結果を出力
 */
function outputImportResults(shop, categoryResults, subCategoryResults) {
  const results = [
    '# カテゴリマスタインポート結果',
    '',
    `実行日時: ${new Date().toLocaleString('ja-JP')}`,
    `対象ショップ: ${shop}`,
    '',
    '## カテゴリマスタ',
    `- 作成: ${categoryResults.created}件`,
    `- 更新: ${categoryResults.updated}件`,
    `- エラー: ${categoryResults.errors}件`,
    '',
    '## サブカテゴリマスタ',
    `- 作成: ${subCategoryResults.created}件`,
    `- 更新: ${subCategoryResults.updated}件`,
    `- エラー: ${subCategoryResults.errors}件`,
    '',
    '## 合計',
    `- 総作成件数: ${categoryResults.created + subCategoryResults.created}件`,
    `- 総更新件数: ${categoryResults.updated + subCategoryResults.updated}件`,
    `- 総エラー件数: ${categoryResults.errors + subCategoryResults.errors}件`
  ];
  
  const outputPath = path.join(__dirname, '../../master-data-csv/generated/import_results.md');
  fs.writeFileSync(outputPath, results.join('\n'), 'utf-8');
  console.log(`インポート結果を出力: ${outputPath}`);
}

/**
 * データベース接続テスト
 */
async function testDatabaseConnection() {
  try {
    await prisma.$connect();
    console.log('データベース接続成功');
    return true;
  } catch (error) {
    console.error('データベース接続エラー:', error.message);
    return false;
  }
}

/**
 * メイン処理
 */
async function main() {
  const shop = process.argv[2] || 'ease-next-temp.myshopify.com';
  
  try {
    console.log(`=== カテゴリマスタインポート開始 ===`);
    console.log(`対象ショップ: ${shop}`);
    
    // データベース接続テスト
    const connected = await testDatabaseConnection();
    if (!connected) {
      process.exit(1);
    }
    
    // 前提条件チェック
    if (!fs.existsSync(UNIFIED_CATEGORY_CSV)) {
      console.error('統合カテゴリマスタファイルが見つかりません。先に02_create_unified_master.jsを実行してください。');
      process.exit(1);
    }
    
    if (!fs.existsSync(UNIFIED_SUBCATEGORY_CSV)) {
      console.error('統合サブカテゴリマスタファイルが見つかりません。先に02_create_unified_master.jsを実行してください。');
      process.exit(1);
    }
    
    // CSVファイルを読み込み
    const categories = parseCSV(UNIFIED_CATEGORY_CSV);
    const subCategories = parseCSV(UNIFIED_SUBCATEGORY_CSV);
    
    console.log(`カテゴリマスタ: ${categories.length}件`);
    console.log(`サブカテゴリマスタ: ${subCategories.length}件`);
    
    // インポート実行
    const categoryResults = await importCategoryMaster(shop, categories);
    const subCategoryResults = await importSubCategoryMaster(shop, subCategories);
    
    // 結果出力
    outputImportResults(shop, categoryResults, subCategoryResults);
    
    console.log('\n=== インポート完了 ===');
    
  } catch (error) {
    console.error('エラーが発生しました:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
