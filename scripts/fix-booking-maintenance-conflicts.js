/**
 * 予約とメンテナンスの競合修正スクリプト
 */

import { PrismaClient } from '@prisma/client';
import { format } from 'date-fns';

const prisma = new PrismaClient();

async function fixBookingMaintenanceConflicts() {
  console.log('🔧 予約とメンテナンスの競合修正を開始...\n');

  try {
    // 競合している商品を検索
    const conflictProducts = await prisma.$queryRaw`
      SELECT DISTINCT p.id, p.title, p.sku, p.status
      FROM products p
      INNER JOIN bookings b ON p.id = b."productId"
      INNER JOIN maintenances m ON p.id = m."productId"
      WHERE b.status IN ('PROVISIONAL', 'CONFIRMED')
        AND b."endDate" >= CURRENT_DATE
        AND m.status = 'IN_PROGRESS'
        AND (
          (b."startDate" <= m."endDate" AND b."endDate" >= m."startDate")
          OR m."endDate" IS NULL
        )
    `;

    console.log(`🚨 競合している商品: ${conflictProducts.length}件\n`);

    if (conflictProducts.length === 0) {
      console.log('✅ 競合は見つかりませんでした');
      return;
    }

    for (const product of conflictProducts) {
      console.log(`📦 ${product.title} (${product.sku}) の競合を修正中...`);

      // 該当商品の予約とメンテナンスを取得
      const bookings = await prisma.booking.findMany({
        where: {
          productId: product.id,
          status: { in: ['PROVISIONAL', 'CONFIRMED'] },
          endDate: { gte: new Date() }
        },
        orderBy: { startDate: 'asc' }
      });

      const maintenances = await prisma.maintenance.findMany({
        where: {
          productId: product.id,
          status: 'IN_PROGRESS'
        },
        orderBy: { startDate: 'asc' }
      });

      console.log(`   予約: ${bookings.length}件, メンテナンス: ${maintenances.length}件`);

      // 各メンテナンスについて競合をチェック
      for (const maintenance of maintenances) {
        const maintenanceStart = new Date(maintenance.startDate);
        const maintenanceEnd = maintenance.endDate ? new Date(maintenance.endDate) : new Date('2099-12-31');

        console.log(`\n   🔧 メンテナンス: ${format(maintenanceStart, 'yyyy-MM-dd')} 〜 ${format(maintenanceEnd, 'yyyy-MM-dd')}`);

        // 競合する予約を検索
        const conflictingBookings = bookings.filter(booking => {
          const bookingStart = new Date(booking.startDate);
          const bookingEnd = new Date(booking.endDate);
          
          return (
            (bookingStart <= maintenanceEnd && bookingEnd >= maintenanceStart)
          );
        });

        if (conflictingBookings.length > 0) {
          console.log(`   ❌ 競合する予約: ${conflictingBookings.length}件`);
          
          conflictingBookings.forEach((booking, index) => {
            console.log(`     ${index + 1}. ${booking.bookingId} (${format(booking.startDate, 'yyyy-MM-dd')} 〜 ${format(booking.endDate, 'yyyy-MM-dd')})`);
            console.log(`        顧客: ${booking.customerName}, ステータス: ${booking.status}`);
          });

          // 修正オプションを提示
          console.log(`\n   🔄 修正オプション:`);
          console.log(`     1. メンテナンスを延期する（推奨）`);
          console.log(`     2. 予約をキャンセルする`);
          console.log(`     3. メンテナンスを完了にする`);

          // 自動修正: メンテナンスを延期
          const lastBookingEnd = new Date(Math.max(...conflictingBookings.map(b => b.endDate.getTime())));
          const newMaintenanceStart = new Date(lastBookingEnd);
          newMaintenanceStart.setDate(newMaintenanceStart.getDate() + 1);

          console.log(`\n   ✅ 自動修正: メンテナンスを ${format(newMaintenanceStart, 'yyyy-MM-dd')} に延期`);

          // メンテナンス期間を更新
          await prisma.maintenance.update({
            where: { id: maintenance.id },
            data: {
              startDate: newMaintenanceStart,
              notes: `${maintenance.notes || ''}\n[自動修正] 予約競合により延期 (元の開始日: ${format(maintenanceStart, 'yyyy-MM-dd')})`
            }
          });

          // 在庫カレンダーを更新
          // 古いメンテナンス期間の在庫カレンダーを削除
          await prisma.inventoryCalendar.deleteMany({
            where: {
              productId: product.id,
              maintenanceId: maintenance.id,
              unavailableReason: 'MAINTENANCE'
            }
          });

          // 新しいメンテナンス期間の在庫カレンダーを作成
          const newMaintenanceEnd = maintenance.endDate ? new Date(maintenance.endDate) : new Date(newMaintenanceStart);
          if (!maintenance.endDate) {
            newMaintenanceEnd.setDate(newMaintenanceStart.getDate() + 7); // デフォルト7日間
          }

          const dates = [];
          const currentDate = new Date(newMaintenanceStart);
          while (currentDate <= newMaintenanceEnd) {
            dates.push(new Date(currentDate));
            currentDate.setDate(currentDate.getDate() + 1);
          }

          for (const date of dates) {
            await prisma.inventoryCalendar.upsert({
              where: {
                shop_productId_date: {
                  shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
                  productId: product.id,
                  date: date
                }
              },
              update: {
                isAvailable: false,
                unavailableReason: 'MAINTENANCE',
                maintenanceId: maintenance.id,
                updatedAt: new Date()
              },
              create: {
                shop: process.env.SHOPIFY_SHOP || 'peaces-test-block.myshopify.com',
                productId: product.id,
                shopifyProductId: product.shopifyId || '0',
                date: date,
                isAvailable: false,
                unavailableReason: 'MAINTENANCE',
                maintenanceId: maintenance.id,
                createdAt: new Date(),
                updatedAt: new Date()
              }
            });
          }

          console.log(`   📅 在庫カレンダーを更新: ${dates.length}日間`);

        } else {
          console.log(`   ✅ 競合なし`);
        }
      }

      console.log(`\n✅ ${product.title} の競合修正完了\n`);
    }

    // 修正後の確認
    console.log('=== 修正後の確認 ===');
    const remainingConflicts = await prisma.$queryRaw`
      SELECT DISTINCT p.id, p.title, p.sku
      FROM products p
      INNER JOIN bookings b ON p.id = b."productId"
      INNER JOIN maintenances m ON p.id = m."productId"
      WHERE b.status IN ('PROVISIONAL', 'CONFIRMED')
        AND b."endDate" >= CURRENT_DATE
        AND m.status = 'IN_PROGRESS'
        AND (
          (b."startDate" <= m."endDate" AND b."endDate" >= m."startDate")
          OR m."endDate" IS NULL
        )
    `;

    if (remainingConflicts.length === 0) {
      console.log('✅ すべての競合が修正されました');
    } else {
      console.log(`❌ まだ${remainingConflicts.length}件の競合が残っています`);
    }

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプト実行
fixBookingMaintenanceConflicts();
