/**
 * Shopify商品バリエーション自動作成サービス
 *
 * 商品作成・更新時に必要なレンタル期間バリエーションを自動作成します
 */

import { GraphqlQueryError } from '@shopify/shopify-api';
import { UnifiedPricingService } from '../pricing/unified-pricing.service';

export interface VariantCreationConfig {
  basePrice: number;
  createDays: number[]; // 作成する日数のリスト（デフォルト: [1,2,3,4,5,6,7]）
  provisionalRate?: number; // 仮予約料率（デフォルト: 0.1）
  createProvisionalVariants?: boolean; // 仮予約バリエーションも作成するか
  productStatus?: string; // 商品ステータス（デフォルト: 'available'）
  location?: string; // 在庫場所（デフォルト: 'NY'）
  sku?: string; // 商品SKU
}

export interface VariantCreationResult {
  success: boolean;
  createdVariants: Array<{
    days: number;
    price: number;
    variantId?: string;
    title: string;
    isProvisional?: boolean;
  }>;
  skippedVariants: Array<{
    days: number;
    reason: string;
    isProvisional?: boolean;
  }>;
  errors: string[];
  metafieldsSet: boolean;
  inventoryUpdated: boolean;
}

export class VariantAutoCreatorService {
  private static instance: VariantAutoCreatorService;
  private pricingService: UnifiedPricingService;

  private constructor() {
    this.pricingService = UnifiedPricingService.getInstance();
  }

  public static getInstance(): VariantAutoCreatorService {
    if (!VariantAutoCreatorService.instance) {
      VariantAutoCreatorService.instance = new VariantAutoCreatorService();
    }
    return VariantAutoCreatorService.instance;
  }

  /**
   * 商品に必要なバリエーションを自動作成
   */
  async createMissingVariants(
    admin: any,
    shopifyProductId: string,
    config: VariantCreationConfig
  ): Promise<VariantCreationResult> {
    console.log(`=== 商品 ${shopifyProductId} のバリエーション自動作成開始 ===`);

    const result: VariantCreationResult = {
      success: false,
      createdVariants: [],
      skippedVariants: [],
      errors: [],
      metafieldsSet: false,
      inventoryUpdated: false
    };

    try {
      // 既存のバリエーションを取得
      const existingVariants = await this.getExistingVariants(admin, shopifyProductId);
      console.log(`既存バリエーション数: ${existingVariants.length}`);

      // 必要なバリエーションを計算
      const requiredVariants = this.calculateRequiredVariants(config);
      console.log(`必要バリエーション数: ${requiredVariants.length}`);

      // 不足しているバリエーションを特定
      const missingVariants = this.findMissingVariants(existingVariants, requiredVariants);
      console.log(`不足バリエーション数: ${missingVariants.length}`);

      if (missingVariants.length === 0) {
        console.log('✅ すべてのバリエーションが既に存在します');
        result.success = true;
        return result;
      }

      // 不足しているバリエーションを一括作成
      if (missingVariants.length > 0) {
        try {
          const createdVariants = await this.createVariantsBulk(admin, shopifyProductId, missingVariants);
          result.createdVariants.push(...createdVariants);

          createdVariants.forEach(variant => {
            console.log(`✅ ${variant.title}: ¥${variant.price.toLocaleString()}`);
          });
        } catch (error) {
          const errorMessage = `バリエーション一括作成失敗: ${error.message}`;
          result.errors.push(errorMessage);

          // 失敗した場合は個別にスキップとして記録
          missingVariants.forEach(variant => {
            result.skippedVariants.push({
              days: variant.days,
              reason: error.message,
              isProvisional: variant.isProvisional
            });
          });
          console.error(`❌ ${errorMessage}`);
        }
      }

      // メタフィールドを設定
      try {
        await this.setProductMetafields(admin, shopifyProductId, config);
        result.metafieldsSet = true;
        console.log('✅ メタフィールド設定完了');
      } catch (error) {
        result.errors.push(`メタフィールド設定エラー: ${error.message}`);
        console.error('❌ メタフィールド設定エラー:', error);
      }

      // 在庫設定（Admin API Access Token権限修正後に有効化）
      try {
        await this.updateProductInventory(admin, shopifyProductId, config);
        result.inventoryUpdated = true;
        console.log('✅ 在庫設定完了');
      } catch (error) {
        result.errors.push(`在庫設定エラー: ${error.message}`);
        console.error('❌ 在庫設定エラー:', error);
        console.log('💡 在庫設定に失敗しました。Admin API Access Tokenの権限を確認してください');
        result.inventoryUpdated = false;
      }

      result.success = result.errors.length === 0;
      console.log(`=== バリエーション自動作成完了: 成功=${result.createdVariants.length}, 失敗=${result.errors.length} ===`);

      return result;

    } catch (error) {
      result.errors.push(`バリエーション自動作成エラー: ${error.message}`);
      console.error('❌ バリエーション自動作成エラー:', error);
      return result;
    }
  }

  /**
   * 既存のバリエーションを取得
   */
  private async getExistingVariants(admin: any, shopifyProductId: string): Promise<Array<{
    id: string;
    title: string;
    price: string;
    days?: number;
    isProvisional?: boolean;
  }>> {
    try {
      const response = await admin.graphql(`
        query getProductVariants($id: ID!) {
          product(id: $id) {
            variants(first: 50) {
              edges {
                node {
                  id
                  title
                  price
                  sku
                }
              }
            }
          }
        }
      `, {
        id: `gid://shopify/Product/${shopifyProductId}`
      });

      const data = await response.json();

      if (data.errors) {
        throw new Error(`GraphQL エラー: ${JSON.stringify(data.errors)}`);
      }

      const variants = data.data.product.variants.edges.map((edge: any) => {
        const variant = edge.node;
        const title = variant.title || '';

        // タイトルから日数を抽出
        const dayMatch = title.match(/(\d+)日/);
        const days = dayMatch ? parseInt(dayMatch[1]) : undefined;

        // 仮予約かどうかを判定
        const isProvisional = title.includes('仮予約') || title.includes('provisional');

        return {
          id: variant.id,
          title: variant.title,
          price: variant.price,
          days,
          isProvisional
        };
      });

      return variants;

    } catch (error) {
      console.error('既存バリエーション取得エラー:', error);
      throw error;
    }
  }

  /**
   * 必要なバリエーションを計算
   */
  private calculateRequiredVariants(config: VariantCreationConfig): Array<{
    days: number;
    price: number;
    title: string;
    sku: string;
    isProvisional: boolean;
  }> {
    const variants: Array<{
      days: number;
      price: number;
      title: string;
      sku: string;
      isProvisional: boolean;
    }> = [];

    // 正規予約バリエーション
    for (const days of config.createDays) {
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(startDate.getDate() + days - 1);

      const pricingResult = this.pricingService.calculatePrice(startDate, endDate, config.basePrice);

      const baseSku = config.sku || '';
      const formattedSku = baseSku ? `${baseSku}-${days}D` : `${days}D`;

      variants.push({
        days,
        price: pricingResult.totalPrice,
        title: `${days}日レンタル`,
        sku: formattedSku,
        isProvisional: false
      });
    }

    // 仮予約バリエーション（オプション）
    if (config.createProvisionalVariants) {
      for (const days of config.createDays) {
        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(startDate.getDate() + days - 1);

        const pricingResult = this.pricingService.calculateProvisionalPrice(startDate, endDate, config.basePrice);

        const baseSku = config.sku || '';
        const formattedSku = baseSku ? `${baseSku}-${days}D-PROV` : `${days}D-PROV`;

        variants.push({
          days,
          price: pricingResult.totalPrice,
          title: `${days}日仮予約`,
          sku: formattedSku,
          isProvisional: true
        });
      }
    }

    // 8日以上用の1日あたり料金
    const additionalDayPrice = Math.round(config.basePrice * 0.1);
    const baseSku = config.sku || '';
    const formattedSku8Plus = baseSku ? `${baseSku}-8D+` : '8D+';

    variants.push({
      days: 8,
      price: additionalDayPrice,
      title: '8日以上レンタル',
      sku: formattedSku8Plus,
      isProvisional: false
    });

    return variants;
  }

  /**
   * 不足しているバリエーションを特定
   */
  private findMissingVariants(
    existing: Array<{ days?: number; isProvisional?: boolean; title: string }>,
    required: Array<{ days: number; isProvisional: boolean; title: string; price: number; sku: string }>
  ): Array<{ days: number; price: number; title: string; sku: string; isProvisional: boolean }> {
    return required.filter(req => {
      const exists = existing.some(ex =>
        ex.days === req.days &&
        (ex.isProvisional || false) === req.isProvisional
      );
      return !exists;
    });
  }

  /**
   * 複数バリエーションを一括作成
   */
  private async createVariantsBulk(
    admin: any,
    shopifyProductId: string,
    variants: Array<{ days: number; price: number; title: string; sku: string; isProvisional: boolean }>
  ): Promise<Array<{ days: number; price: number; variantId: string; title: string; isProvisional: boolean }>> {
    try {
      const variantInputs = variants.map(variant => ({
        price: variant.price.toString(),
        optionValues: [{
          optionName: 'Title',
          name: variant.title
        }],
        inventoryItem: {
          sku: variant.sku,
          tracked: true
        },
        inventoryPolicy: 'DENY'
        // inventoryQuantitiesは権限エラーのため一旦削除
      }));

      const response = await admin.graphql(`
        mutation productVariantsBulkCreate($productId: ID!, $variants: [ProductVariantsBulkInput!]!) {
          productVariantsBulkCreate(productId: $productId, variants: $variants) {
            productVariants {
              id
              title
              price
              inventoryItem {
                sku
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `, {
        productId: `gid://shopify/Product/${shopifyProductId}`,
        variants: variantInputs
      });

      const data = await response.json();

      if (data.errors) {
        throw new Error(`GraphQL エラー: ${JSON.stringify(data.errors)}`);
      }

      if (data.data.productVariantsBulkCreate.userErrors.length > 0) {
        throw new Error(`バリエーション一括作成エラー: ${JSON.stringify(data.data.productVariantsBulkCreate.userErrors)}`);
      }

      const createdVariants = data.data.productVariantsBulkCreate.productVariants;

      return createdVariants.map((createdVariant: any, index: number) => ({
        days: variants[index].days,
        price: variants[index].price,
        variantId: createdVariant.id,
        title: variants[index].title,
        isProvisional: variants[index].isProvisional
      }));

    } catch (error) {
      console.error('バリエーション一括作成エラー:', error);
      throw error;
    }
  }

  /**
   * 商品のメタフィールドを設定
   */
  private async setProductMetafields(
    admin: any,
    shopifyProductId: string,
    config: VariantCreationConfig
  ): Promise<void> {
    const productStatus = config.productStatus || 'available';
    const location = config.location || 'NY';
    const sku = config.sku || '';

    // basic_info メタフィールドを設定
    const basicInfo = {
      productCode: sku.split('-')[0] || sku,
      detailCode: sku.split('-')[1] || '001',
      kana: '', // 後で手動設定
      location: location,
      status: productStatus
    };

    // pricing メタフィールドを設定
    const pricing = {
      basePrice: config.basePrice,
      depositRate: 0.1,
      discountRules: {
        day2_6_rate: 0.2,
        day7_plus_rate: 0.1
      },
      minimumDays: 1,
      maximumDays: 30
    };

    try {
      // basic_info メタフィールドを設定
      await admin.graphql(`
        mutation productUpdate($input: ProductInput!) {
          productUpdate(input: $input) {
            product {
              id
            }
            userErrors {
              field
              message
            }
          }
        }
      `, {
        input: {
          id: `gid://shopify/Product/${shopifyProductId}`,
          metafields: [
            {
              namespace: 'rental',
              key: 'basic_info',
              value: JSON.stringify(basicInfo),
              type: 'json'
            },
            {
              namespace: 'rental',
              key: 'pricing',
              value: JSON.stringify(pricing),
              type: 'json'
            },
            {
              namespace: 'rental',
              key: 'status',
              value: productStatus,
              type: 'single_line_text_field'
            },
            {
              namespace: 'rental',
              key: 'location',
              value: location,
              type: 'single_line_text_field'
            }
          ]
        }
      });

      console.log(`メタフィールド設定: status=${productStatus}, location=${location}`);

    } catch (error) {
      console.error('メタフィールド設定エラー:', error);
      throw error;
    }
  }

  /**
   * 商品の在庫を更新
   */
  private async updateProductInventory(
    admin: any,
    shopifyProductId: string,
    config: VariantCreationConfig
  ): Promise<void> {
    const productStatus = config.productStatus || 'available';

    // availableの場合は在庫1、それ以外は在庫0
    const inventoryQuantity = productStatus === 'available' ? 1 : 0;

    try {
      // ロケーションを動的に取得
      const locationResponse = await admin.graphql(`
        query getLocations {
          locations(first: 10) {
            edges {
              node {
                id
                name
                address {
                  formatted
                }
              }
            }
          }
        }
      `);

      const locationData = await locationResponse.json();

      if (locationData.errors) {
        throw new Error(`ロケーション取得エラー: ${JSON.stringify(locationData.errors)}`);
      }

      const locations = locationData.data.locations.edges;
      const targetLocation = config.location || 'NY';

      // ロケーション名でマッチング（NYまたはPR）
      let defaultLocationId = 'gid://shopify/Location/69852102824'; // フォールバック

      for (const locationEdge of locations) {
        const location = locationEdge.node;
        const locationName = location.name.toLowerCase();
        const targetName = targetLocation.toLowerCase();

        if (locationName.includes(targetName) ||
            (targetName === 'ny' && locationName.includes('new york')) ||
            (targetName === 'pr' && locationName.includes('puerto rico'))) {
          defaultLocationId = location.id;
          console.log(`✅ ロケーション発見: ${location.name} (${location.id})`);
          break;
        }
      }

      console.log(`使用するロケーション: ${targetLocation} → ${defaultLocationId}`);

      // 商品のバリエーションを取得
      const response = await admin.graphql(`
        query getProductVariants($id: ID!) {
          product(id: $id) {
            variants(first: 50) {
              edges {
                node {
                  id
                  inventoryItem {
                    id
                  }
                }
              }
            }
          }
        }
      `, {
        id: `gid://shopify/Product/${shopifyProductId}`
      });

      const data = await response.json();

      if (data.errors) {
        throw new Error(`GraphQL エラー: ${JSON.stringify(data.errors)}`);
      }

      const variants = data.data.product.variants.edges;

      // 各バリエーションの在庫を設定
      for (const variantEdge of variants) {
        const variant = variantEdge.node;
        const inventoryItemId = variant.inventoryItem.id;

        // 在庫追跡を有効化
        await admin.graphql(`
          mutation inventoryItemUpdate($id: ID!, $input: InventoryItemInput!) {
            inventoryItemUpdate(id: $id, input: $input) {
              inventoryItem {
                id
                tracked
              }
              userErrors {
                field
                message
              }
            }
          }
        `, {
          id: inventoryItemId,
          input: {
            tracked: true
          }
        });

        console.log(`  在庫追跡を有効化: ${inventoryItemId}`);

        // 在庫アイテムをロケーションで有効化
        try {
          const activateResponse = await admin.graphql(`
            mutation inventoryActivate($inventoryItemId: ID!, $locationId: ID!) {
              inventoryActivate(inventoryItemId: $inventoryItemId, locationId: $locationId) {
                inventoryLevel {
                  id
                }
                userErrors {
                  field
                  message
                }
              }
            }
          `, {
            inventoryItemId: inventoryItemId,
            locationId: defaultLocationId
          });

          const activateData = await activateResponse.json();

          if (activateData.errors) {
            console.log(`  ⚠️ 在庫有効化GraphQLエラー: ${JSON.stringify(activateData.errors)}`);
          } else if (activateData.data.inventoryActivate.userErrors.length > 0) {
            console.log(`  ⚠️ 在庫有効化ユーザーエラー: ${JSON.stringify(activateData.data.inventoryActivate.userErrors)}`);
          } else {
            console.log(`  ✅ 在庫レベル有効化成功: ${inventoryItemId}`);
          }
        } catch (activateError) {
          console.log(`  ⚠️ 在庫有効化エラー（既に有効化済みの可能性）: ${activateError.message}`);
        }

        // 少し待機してAPI制限を回避
        await new Promise(resolve => setTimeout(resolve, 500));

        const inventoryResponse = await admin.graphql(`
          mutation inventorySetQuantities($input: InventorySetQuantitiesInput!) {
            inventorySetQuantities(input: $input) {
              inventoryAdjustmentGroup {
                id
                changes {
                  name
                  delta
                  quantityAfterChange
                }
              }
              userErrors {
                field
                message
              }
            }
          }
        `, {
          input: {
            name: 'available',
            reason: 'correction',
            referenceDocumentUri: `https://example.com/product-setup-${shopifyProductId}`,
            ignoreCompareQuantity: true,
            quantities: [{
              inventoryItemId: inventoryItemId,
              locationId: defaultLocationId,
              quantity: inventoryQuantity
            }]
          }
        });

        const inventoryResult = await inventoryResponse.json();

        if (inventoryResult.errors) {
          console.log(`  ⚠️ 在庫設定GraphQLエラー: ${JSON.stringify(inventoryResult.errors)}`);
        } else if (inventoryResult.data.inventorySetQuantities.userErrors.length > 0) {
          console.log(`  ⚠️ 在庫設定ユーザーエラー: ${JSON.stringify(inventoryResult.data.inventorySetQuantities.userErrors)}`);
        } else {
          console.log(`  ✅ 在庫設定成功: ${inventoryItemId} → ${inventoryQuantity}個`);
        }

        if (inventoryResult.data?.inventorySetQuantities?.inventoryAdjustmentGroup?.changes) {
          const changes = inventoryResult.data.inventorySetQuantities.inventoryAdjustmentGroup.changes;
          console.log(`  在庫設定詳細: ${JSON.stringify(changes)}`);
        }
      }

      console.log(`在庫設定: ${variants.length}個のバリエーションを在庫${inventoryQuantity}に設定`);

    } catch (error) {
      console.error('在庫設定エラー:', error);
      console.log('💡 在庫設定に失敗しました。手動でShopify管理画面から設定してください');
      // エラーを投げずに処理を続行
    }
  }

  /**
   * 商品の基本価格を取得
   */
  async getProductBasePrice(admin: any, shopifyProductId: string): Promise<number> {
    try {
      const response = await admin.graphql(`
        query getProduct($id: ID!) {
          product(id: $id) {
            variants(first: 1) {
              edges {
                node {
                  price
                }
              }
            }
          }
        }
      `, {
        id: `gid://shopify/Product/${shopifyProductId}`
      });

      const data = await response.json();

      if (data.errors) {
        throw new Error(`GraphQL エラー: ${JSON.stringify(data.errors)}`);
      }

      const variants = data.data.product.variants.edges;
      if (variants.length === 0) {
        throw new Error('商品にバリエーションが存在しません');
      }

      return parseFloat(variants[0].node.price);

    } catch (error) {
      console.error('基本価格取得エラー:', error);
      throw error;
    }
  }
}
